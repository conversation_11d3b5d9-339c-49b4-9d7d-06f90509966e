/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class R2099 {

    private String evtFechaEvPer_Id;
    private int sucesso;

    private String ideEvento_perApur;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;

    private String ideContri_tpInsc;
    private String ideContri_nrInsc;

    private String ideRespInf_nmResp;
    private String ideRespInf_cpfResp;
    private String ideRespInf_telefone;
    private String ideRespInf_email;

    private String infoFech_evtServTm;
    private String infoFech_evtServPr;
    private String infoFech_evtAssDespRec;
    private String infoFech_evtAssDespRep;
    private String infoFech_evtComProd;
    private String infoFech_evtCPRB;
    private String infoFech_evtPgtos;
    private String infoFech_compSemMovto;

    public String getEvtFechaEvPer_Id() {
        return evtFechaEvPer_Id;
    }

    public void setEvtFechaEvPer_Id(String evtFechaEvPer_Id) {
        this.evtFechaEvPer_Id = evtFechaEvPer_Id;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_perApur() {
        return ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeContri_tpInsc() {
        return ideContri_tpInsc;
    }

    public void setIdeContri_tpInsc(String ideContri_tpInsc) {
        this.ideContri_tpInsc = ideContri_tpInsc;
    }

    public String getIdeContri_nrInsc() {
        return ideContri_nrInsc;
    }

    public void setIdeContri_nrInsc(String ideContri_nrInsc) {
        this.ideContri_nrInsc = ideContri_nrInsc;
    }

    public String getIdeRespInf_nmResp() {
        return ideRespInf_nmResp;
    }

    public void setIdeRespInf_nmResp(String ideRespInf_nmResp) {
        this.ideRespInf_nmResp = ideRespInf_nmResp;
    }

    public String getIdeRespInf_cpfResp() {
        return ideRespInf_cpfResp;
    }

    public void setIdeRespInf_cpfResp(String ideRespInf_cpfResp) {
        this.ideRespInf_cpfResp = ideRespInf_cpfResp;
    }

    public String getIdeRespInf_telefone() {
        return ideRespInf_telefone;
    }

    public void setIdeRespInf_telefone(String ideRespInf_telefone) {
        this.ideRespInf_telefone = ideRespInf_telefone;
    }

    public String getIdeRespInf_email() {
        return ideRespInf_email;
    }

    public void setIdeRespInf_email(String ideRespInf_email) {
        this.ideRespInf_email = ideRespInf_email;
    }

    public String getInfoFech_evtServTm() {
        return infoFech_evtServTm;
    }

    public void setInfoFech_evtServTm(String infoFech_evtServTm) {
        this.infoFech_evtServTm = infoFech_evtServTm;
    }

    public String getInfoFech_evtServPr() {
        return infoFech_evtServPr;
    }

    public void setInfoFech_evtServPr(String infoFech_evtServPr) {
        this.infoFech_evtServPr = infoFech_evtServPr;
    }

    public String getInfoFech_evtAssDespRec() {
        return infoFech_evtAssDespRec;
    }

    public void setInfoFech_evtAssDespRec(String infoFech_evtAssDespRec) {
        this.infoFech_evtAssDespRec = infoFech_evtAssDespRec;
    }

    public String getInfoFech_evtAssDespRep() {
        return infoFech_evtAssDespRep;
    }

    public void setInfoFech_evtAssDespRep(String infoFech_evtAssDespRep) {
        this.infoFech_evtAssDespRep = infoFech_evtAssDespRep;
    }

    public String getInfoFech_evtComProd() {
        return infoFech_evtComProd;
    }

    public void setInfoFech_evtComProd(String infoFech_evtComProd) {
        this.infoFech_evtComProd = infoFech_evtComProd;
    }

    public String getInfoFech_evtCPRB() {
        return infoFech_evtCPRB;
    }

    public void setInfoFech_evtCPRB(String infoFech_evtCPRB) {
        this.infoFech_evtCPRB = infoFech_evtCPRB;
    }

    public String getInfoFech_evtPgtos() {
        return infoFech_evtPgtos;
    }

    public void setInfoFech_evtPgtos(String infoFech_evtPgtos) {
        this.infoFech_evtPgtos = infoFech_evtPgtos;
    }

    public String getInfoFech_compSemMovto() {
        return infoFech_compSemMovto;
    }

    public void setInfoFech_compSemMovto(String infoFech_compSemMovto) {
        this.infoFech_compSemMovto = infoFech_compSemMovto;
    }

}

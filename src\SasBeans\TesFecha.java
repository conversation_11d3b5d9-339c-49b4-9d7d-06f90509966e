package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TesFecha {

    private BigDecimal CodFil;
    private String CodCli;
    private LocalDate DtFecha;
    private String Operador;
    private LocalDate Dt_alter;
    private String Hr_Alter;

    public TesFecha() {
    }

    public TesFecha(TesFecha original) {
        this.CodFil = original.getCodFil();
        this.CodCli = original.getCodCli();
        this.DtFecha = original.getDtFecha();
        this.Operador = original.getOperador();
        this.Dt_alter = original.getDt_alter();
        this.Hr_Alter = original.getHr_Alter();
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public LocalDate getDtFecha() {
        return DtFecha;
    }

    public void setDtFecha(LocalDate DtFecha) {
        this.DtFecha = DtFecha;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(LocalDate Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 89 * hash + Objects.hashCode(this.CodFil);
        hash = 89 * hash + Objects.hashCode(this.CodCli);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TesFecha other = (TesFecha) obj;
        if (!Objects.equals(this.CodCli, other.CodCli)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "TesFecha{" + "CodFil=" + CodFil + ", CodCli=" + CodCli + ", DtFecha=" + DtFecha + '}';
    }
}

/*
 */
package SasBeansCompostas;

import SasBeans.Produtos;
import SasBeans.PropProd;
import SasBeans.PropServ;
import SasBeans.PropServItens;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProdutosPropProdServ {

    private Produtos produto;
    private PropProd propProd;
    private PropServ propServ;
    private List<PropServItens> propServItens;

    private BigDecimal id;

    public ProdutosPropProdServ() {
        produto = new Produtos();
        propProd = new PropProd();
        propServ = new PropServ();
        propServItens = new ArrayList<>();
    }

    public Produtos getProduto() {
        return produto;
    }

    public void setProduto(Produtos produto) {
        this.produto = produto;
    }

    public PropProd getPropProd() {
        return propProd;
    }

    public void setPropProd(PropProd propProd) {
        this.propProd = propProd;
    }

    public PropServ getPropServ() {
        return propServ;
    }

    public void setPropServ(PropServ propServ) {
        this.propServ = propServ;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public List<PropServItens> getPropServItens() {
        return propServItens;
    }

    public void setPropServItens(List<PropServItens> propServItens) {
        this.propServItens = propServItens;
    }
}

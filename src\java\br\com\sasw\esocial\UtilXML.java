/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.esocial;

/**
 *
 * <AUTHOR>
 */
public class UtilXML {

    public static String subStrIntoDelim(String texto, String delimitadorInicial, String delimitadorFinal) {
        StringBuilder textoValido = new StringBuilder();
        boolean encontrouPrimeiroDelimitador = false;
        try {
            for (int i = 0; i < texto.length(); i++) {
                if (encontrouPrimeiroDelimitador) {
                    if (!texto.substring(i).startsWith(delimitadorFinal)) {
                        textoValido.append(texto.subSequence(i, i + 1));
                    } else {
                        i = texto.length();
                    }
                } else {
                    if (texto.substring(i).startsWith(delimitadorInicial)) {
                        encontrouPrimeiroDelimitador = true;
                        i += delimitadorInicial.length() - 1;
                    }
                }
            }
            return textoValido.toString();
        } catch (NullPointerException npe) {
            return "";
        }
    }

}

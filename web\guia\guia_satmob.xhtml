<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:f="http://xmlns.jcp.org/jsf/core">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <title></title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                a:hover,
                a:focus {
                    color:#3479A3 !important;
                }
                
                body{
                    overflow: auto !important;
                    max-height: 100vh;
                    padding:0px 0px 0px 0px !important;
                    margin: 0px !important;
                    background: #FFF !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <ui:composition>
                <f:metadata>
                    <f:viewParam name="empresa" value="#{login.empresaBD}"/>
                    <f:viewParam name="filial" value="#{login.codFil}"/>
                    <f:viewAction action="#{login.conectarEmpresa()}"/>
                    <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                    <f:viewParam name="seqRota" value="#{valores.sequencia}" />
                    <f:viewParam name="parada" value="#{valores.parada}" />
                    <f:viewParam name="serie" value="#{valores.carregamentoSerie}" />
                    <f:viewParam name="guia" value="#{valores.carregamentoGuia}" />
                    <f:viewParam name="filial" value="#{valores.codfil}"/>
                    <f:viewAction action="#{valores.abrirGuiasPagina()}"/>
                </f:metadata>
                <div id="body">
                    <p:growl id="msgs"/>
                    <h:form id="impressao">
                        <p:panel class="guiaimpressa" styleClass="guiaimpressa" 
                                 style="padding:12px !important; border:thin solid #DDD !important; overflow:auto !important;max-height: 100% !important">
                            <h:outputText id="guiaimpressa" value="#{valores.html}" escape="false"/>
                        </p:panel>
                    </h:form>
                </div>
                
                <script>
                    $(document).ready(function(){
                       const Titulo = '#{localemsgs.Guia}: ' + ObterParamURL('guia')+ ' / #{localemsgs.Serie}: ' + ObterParamURL('serie') + ' / #{localemsgs.Parada}: ' + ObterParamURL('parada');
                       $('title').text(Titulo);
                    });
                </script>
            </ui:composition>
        </h:body>
    </f:view>
</html>
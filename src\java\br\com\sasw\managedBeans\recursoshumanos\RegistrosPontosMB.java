package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Controller.RegistrosPonto.RegistrosPontoSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.BatidaPonto;
import SasBeans.Filiais;
import SasBeans.FolhaPonto;
import SasBeans.SasPWFill;
import br.com.sasw.arquivos.PDF;
import br.com.sasw.lazydatamodels.FolhasPontoLazyList;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.utils.Mascaras.Data;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.faces.application.FacesMessage;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;

/**
 *
 * <AUTHOR>
 */
@Named(value = "registrospontos")
@ViewScoped
public class RegistrosPontosMB implements Serializable {

    private final ArquivoLog logerro;
    private final String banco, caminho;
    private final BigDecimal codPessoa;
    private String log;
    private Persistencia persistencia;
    private final RotasSatWeb rotassatweb;
    private RegistrosPontoSatMobWeb controller;
    private SasPWFill filial;
    private Filiais filiais;
    private final String codFil;
    private String dataInicio, dataFim;
    private Date dataObjetoInicio, dataObjetoFim;
    private FolhaPonto selecionado;
    private LazyDataModel<FolhaPonto> lazyFolhas;
    private int total;
    private final Map filters;
    private Boolean mostrandoTodasFiliais;
    private List<BatidaPonto> batidas;
    private StreamedContent pdfStream;
    private String chaveOrdem = "POSTO";
    private List<Date> datasSelecionadas;

    public RegistrosPontosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        // Busca
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog(this.getClass().getSimpleName());
        rotassatweb = new RotasSatWeb();

        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, 1);
        dataObjetoInicio = c.getTime();
        dataInicio = dataObjetoInicio.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataObjetoFim = c.getTime();
        dataFim = dataObjetoFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(dataObjetoInicio); // data inicial
        datasSelecionadas.add(dataObjetoFim); // data final
        
        filters = new HashMap();
        filters.put("codFil", codFil);
        filters.put("posto", "");
        filters.put("matricula", "");
        filters.put("nome", "");
        filters.put("dataInicio", dataInicio);
        filters.put("dataFim", dataFim);
        mostrandoTodasFiliais = false;
    }

    public void Persistencias(Persistencia persistencia) {
        try {
            this.persistencia = persistencia;
            if (null == persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            controller = new RegistrosPontoSatMobWeb(persistencia);
            filiais = rotassatweb.buscaInfoFilial(codFil, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../index.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    private LazyDataModel<FolhaPonto> getAllRegistros() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            lazyFolhas = new FolhasPontoLazyList(persistencia, replaceOrder(this.chaveOrdem));

            total = controller.contagemRegistro(filters);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }

        return lazyFolhas;
    }

    public void gerarLog(String texto) {
        logerro.GravaMetodos(texto, caminho);
    }

    private String replaceOrder(String Valor) {
        try {
            switch (this.chaveOrdem) {
                case "POSTO":
                    return "PstServ.Local, Funcion.Nome";
                case "NOME":
                    return "Funcion.Nome";
                case "MATRICULA":
                    return "Funcion.Matr, Funcion.Nome";
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return null;
    }

    public void selecionarData() {
        try {
            atualizaIntervalo();
            filters.replace("dataInicio", dataInicio);
            filters.replace("dataFim", dataFim);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            gerarLog("Listando guias");
            getAllRegistros();
            dt.setFirst(0);

            total = controller.contagemRegistro(filters);

            PrimeFaces.current().ajax().update("msgs", "main", "cabecalho");
            PrimeFaces.current().executeScript("PF('oCalendarios').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }
    
    public void selecionarDatas(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.dataObjetoInicio = this.datasSelecionadas.get(0);
                this.dataObjetoFim = this.datasSelecionadas.get(1);

                atualizaIntervalo();
                filters.replace("dataInicio", dataInicio);
                filters.replace("dataFim", dataFim);
                DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
                dt.setFilters(filters);
                gerarLog("Listando guias");
                getAllRegistros();
                dt.setFirst(0);

                total = controller.contagemRegistro(filters);

                PrimeFaces.current().ajax().update("msgs", "main", "cabecalho");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void mostrarFiliais() {
        filters.replace("codFil", mostrandoTodasFiliais ? "" : codFil);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(filters);
        getAllRegistros();
        dt.setFirst(0);
    }

    public void prePesquisa() {
        try {
            selecionado = new FolhaPonto();

            if (mostrandoTodasFiliais) {
                filial = new SasPWFill();
            } else {
                filial = controller.buscaFilial(codFil, codPessoa);
            }
            PrimeFaces.current().executeScript("PF('dlgPesquisar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    private String toLocalDateString(Date data) {
        return data
                .toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    private void setMes(Date date, Integer incremento) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, incremento);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        dataObjetoInicio = calendar.getTime();
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataObjetoFim = calendar.getTime();
        dataInicio = toLocalDateString(dataObjetoInicio);
        dataFim = toLocalDateString(dataObjetoFim);
    }

    private void atualizaIntervalo() throws Exception {
        if (dataObjetoInicio.after(dataObjetoFim)) {
            throw new Exception("IntervaloInvalido");
        }

        dataInicio = toLocalDateString(dataObjetoInicio);
        dataFim = toLocalDateString(dataObjetoFim);
    }

    private void atualizaCalendarioETabela() throws Exception {
        atualizaIntervalo();
        filters.replace("dataInicio", dataInicio);
        filters.replace("dataFim", dataFim);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance()
                .getViewRoot()
                .findComponent("main:tabela");
        dt.setFilters(filters);
        getAllRegistros();
        dt.setFirst(0);
    }

    private void logExceptionCalendario(Exception e) {
        FacesMessage mensagem = new FacesMessage(
                FacesMessage.SEVERITY_ERROR,
                Messages.getMessageS(e.getMessage()),
                null
        );
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
        log = this.getClass().getSimpleName() + "\r\n"
                + Thread.currentThread().getStackTrace()[1].getMethodName()
                + "\r\n" + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    public void vaDataAnterior() {
        try {
            Date date = new SimpleDateFormat("yyyyMMdd").parse(dataInicio);
            setMes(date, -1);
            atualizaCalendarioETabela();
        } catch (Exception e) {
            logExceptionCalendario(e);
        }
    }

    public void vaDataPosterior() {
        try {
            Date date = new SimpleDateFormat("yyyyMMdd").parse(dataFim);
            setMes(date, 1);
            atualizaCalendarioETabela();
        } catch (Exception e) {
            logExceptionCalendario(e);
        }
    }

    public void ordenar() {
        getAllRegistros();
    }

    public void pesquisar() {
        try {
            atualizaIntervalo();
            filters.replace("dataInicio", dataInicio);
            filters.replace("dataFim", dataFim);
            if (filial == null || filial.getCodfilAc() == null || filial.getCodfilAc().equals("0")) {
                filters.replace("codFil", null);
            } else {
                filters.replace("codFil", filial.getCodfilAc());
                mostrandoTodasFiliais = !codFil.equals(filial.getCodfilAc());
            }
            if (null != selecionado) {
                filters.replace("matricula", selecionado.getMatr() == null || selecionado.getMatr().equals("") ? "" : selecionado.getMatr());
                filters.replace("nome", selecionado.getNome() == null || selecionado.getNome().equals("") ? "" : selecionado.getNome());
                filters.replace("posto", selecionado.getPosto() == null || selecionado.getPosto().equals("") ? "" : selecionado.getPosto());
            }
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            getAllRegistros();
            dt.setFirst(0);

            total = controller.contagemRegistro(filters);

            PrimeFaces.current().ajax().update("msgs", "main", "cabecalho", "corporativo");
            PrimeFaces.current().executeScript("PF('dlgPesquisar').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void limpaFiltros() {
        mostrandoTodasFiliais = false;
        filters.replace("codFil", codFil);
        filters.replace("posto", "");
        filters.replace("matricula", "");
        filters.replace("nome", "");
        filters.replace("dataInicio", dataInicio);
        filters.replace("dataFim", dataFim);

        this.selecionado = null;

        getAllRegistros();
    }

    public void verBatidas() throws Exception {
        if (null == selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneContrato"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                filial = controller.buscaFilial(codFil, codPessoa);
                batidas = controller.listaAllBatidas(selecionado.getMatr(), dataInicio, dataFim);
                batidas = completeMissingDays(batidas);

                PrimeFaces.current().ajax().update("verBatidas:batidas");
                PrimeFaces.current().executeScript("PF('dlgVisualizar').show();");
            } catch (NumberFormatException e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                logerro.Grava(log, caminho);
            }
        }
    }

    private List<BatidaPonto> completeMissingDays(List<BatidaPonto> lista) {
    
    for(BatidaPonto batidaPonto: lista){
        batidaPonto.setData(Data(batidaPonto.getData()));
    }
            
            
    return lista ;

    // Código destivado após troca de referencia para Rh_Horas_XPE
    /*List<BatidaPonto> listaCompleta = new ArrayList();
        Calendar dayOfWeekCal = Calendar.getInstance();
        Calendar startCalendar = new GregorianCalendar();
        startCalendar.setTime(dataObjetoInicio);
        Calendar endCalendar = new GregorianCalendar();
        endCalendar.setTime(dataObjetoFim);
        endCalendar.add(Calendar.DATE, 1);

        while (startCalendar.before(endCalendar)) {
            Date result = startCalendar.getTime();
            String dataTxt = result.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            Optional<BatidaPonto> first = lista.stream()
                    .filter(o -> o.getData().equals(dataTxt))
                    .findFirst();

            BatidaPonto batida = null;
            if (!first.isPresent()) {
                batida = new BatidaPonto();
                batida.setData(dataTxt);

                dayOfWeekCal.setTime(result);
                batida.setDiaSemana(Integer.toString(dayOfWeekCal.get(Calendar.DAY_OF_WEEK)));
            }
            BatidaPonto bar = first.orElse(batida);
            listaCompleta.add(bar);

            startCalendar.add(Calendar.DATE, 1);
        }

        return listaCompleta;*/
}

public void gerarDownloadPDF() {
        /*if (null == selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneFuncion"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {*/
        String NomeArquivo = "Completo";
        String matricula = "";
        controller = new RegistrosPontoSatMobWeb(this.persistencia, replaceOrder(this.chaveOrdem));

        if (null != selecionado) {
            matricula = selecionado.getMatr();
            NomeArquivo = matricula;
        }

        final PDF pdf = new PDF("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + filial + "\\Folhadeponto\\", NomeArquivo + ".pdf");

        try {
            if (!matricula.equals("")) {
                // Único Colaborador
                pdf.CabecalhoPontoNew(FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator),
                        persistencia.getEmpresa(), filiais, selecionado);

                List<BatidaPonto> batidasSelecionado = controller.listaAllBatidas(matricula, dataInicio, dataFim);
                List<BatidaPonto> listaCompleta = completeMissingDays(batidasSelecionado);
                listaCompleta.forEach(batida -> pdf.ItemPonto(batida));

                pdf.FechaPontoNew(dataInicio, dataFim, selecionado.getNome());
                controller.geraLogFolhaDePonto(selecionado.getMatr(), dataInicio, dataFim, codFil, persistencia);
            } else {
                //Todos Colaboradores    
                int Pagina = 1;
                for (FolhaPonto folhaPontoItem : controller.listaFolhasPaginadoSemFiltro(0, 1000, filters)) {
                    pdf.CabecalhoPontoNew(FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator),
                            persistencia.getEmpresa(), filiais, folhaPontoItem, Pagina);

                    List<BatidaPonto> batidasSelecionado = controller.listaAllBatidas(folhaPontoItem.getMatr(), dataInicio, dataFim);
                    List<BatidaPonto> listaCompleta = completeMissingDays(batidasSelecionado);
                    listaCompleta.forEach(batida -> pdf.ItemPonto(batida));

                    pdf.FechaPontoNew(dataInicio, dataFim, folhaPontoItem.getNome());
                    controller.geraLogFolhaDePonto(folhaPontoItem.getMatr(), dataInicio, dataFim, codFil, persistencia);

                    Pagina++;
                }
            }

            // Prepare
            FacesContext facesContext = FacesContext.getCurrentInstance();
            ExternalContext externalContext = facesContext.getExternalContext();
            HttpServletResponse response = (HttpServletResponse) externalContext.getResponse();
            pdf.sendFileResponse(facesContext, response);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ArquivoNaoEncontrado"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        //}
    }

    private void imprimirUm(PDF pdf, FolhaPonto selecionado) throws Exception {
        try {

            String matricula = selecionado.getMatr();
            pdf.CabecalhoPontoNew(FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator),
                    persistencia.getEmpresa(), filiais, selecionado);

            List<BatidaPonto> batidasSelecionado = controller.listaAllBatidas(matricula, dataInicio, dataFim);
            batidasSelecionado.forEach(batida -> pdf.ItemPonto(batida));

            pdf.FechaPontoNew(dataInicio, dataFim, selecionado.getNome());
            controller.geraLogFolhaDePonto(selecionado.getMatr(), dataInicio, dataFim, codFil, persistencia);
        } catch (Exception e) {

        }
    }

    public void gerarDownloadPDFAll() {
        try {
            PDF pdf = new PDF("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + filial + "\\Folhadeponto\\", "__all__" + ".pdf"); // FIXME nome

            controller = new RegistrosPontoSatMobWeb(this.persistencia, replaceOrder(this.chaveOrdem));
            List<FolhaPonto> all = controller.listaFolhasPaginadoSemFiltro(0, 99, filters);
            all.forEach(folha -> {
                try {
                    imprimirUm(pdf, folha);
                } catch (Exception e) {

                }
            });

            controller.geraLogFolhaDePonto("__all__", dataInicio, dataFim, codFil, persistencia);
            pdf.FechaPdf();
            pdf.service();

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ArquivoNaoEncontrado"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<BatidaPonto> getBatidas() {
        return batidas;
    }

    public void setBatidas(List<BatidaPonto> batidas) {
        this.batidas = batidas;
    }

    public Date getDataObjetoInicio() {
        return dataObjetoInicio;
    }

    public void setDataObjetoInicio(Date dataObjetoInicio) {
        this.dataObjetoInicio = dataObjetoInicio;
    }

    public Date getDataObjetoFim() {
        return dataObjetoFim;
    }

    public void setDataObjetoFim(Date dataObjetoFim) {
        this.dataObjetoFim = dataObjetoFim;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFim() {
        return dataFim;
    }

    public void setDataFim(String dataFim) {
        this.dataFim = dataFim;
    }

    public FolhaPonto getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(FolhaPonto selecionado) {
        this.selecionado = selecionado;
    }

    public Boolean getMostrandoTodasFiliais() {
        return mostrandoTodasFiliais;
    }

    public void setMostrandoTodasFiliais(Boolean mostrandoTodasFiliais) {
        this.mostrandoTodasFiliais = mostrandoTodasFiliais;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public LazyDataModel<FolhaPonto> getLazyFolhas() {
        if (lazyFolhas == null) {
            getAllRegistros();
        }

        return lazyFolhas;
    }

    public StreamedContent getPdfStream() {
        return pdfStream;
    }

    public String getChaveOrdem() {
        return chaveOrdem;
    }

    public void setChaveOrdem(String chaveOrdem) {
        this.chaveOrdem = chaveOrdem;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

}

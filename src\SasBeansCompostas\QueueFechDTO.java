/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class QueueFechDTO {

    private String Comando_Ref;
    private BigDecimal Sequencia;
    private LocalDate DataHoraComando;
    private String HoraComando;
    private String Latitude;
    private String Longitude;
    private String Nome_Guer;
    private String Rota;
    private String Veiculo;
    private String local;
    private String mensagem;

    public QueueFechDTO() {
        Sequencia = null;
        DataHoraComando = LocalDate.now();
        HoraComando = "";
        Latitude = "";
        Longitude = "";
        Nome_Guer = "";
        Rota = "";
        Veiculo = "";
    }

    public String getComando_Ref() {
        return Comando_Ref;
    }

    public void setComando_Ref(String Comando_Ref) {
        this.Comando_Ref = Comando_Ref;
    }

    public LocalDate getDataHoraComando() {
        return DataHoraComando;
    }

    public void setDataHoraComando(LocalDate DataHoraComando) {
        this.DataHoraComando = DataHoraComando;
    }

    public String getHoraComando() {
        return HoraComando;
    }

    public void setHoraComando(String HoraComando) {
        this.HoraComando = HoraComando;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getNome_Guer() {
        return Nome_Guer;
    }

    public void setNome_Guer(String Nome_Guer) {
        this.Nome_Guer = Nome_Guer;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getVeiculo() {
        return Veiculo;
    }

    public void setVeiculo(String Veiculo) {
        this.Veiculo = Veiculo;
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }
}

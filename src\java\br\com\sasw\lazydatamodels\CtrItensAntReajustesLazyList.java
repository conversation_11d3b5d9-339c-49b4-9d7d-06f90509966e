/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.OS_Vig.OS_VigSatMobWeb;
import Dados.Persistencia;
import SasBeans.CtrItensAntReajustes;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class CtrItensAntReajustesLazyList extends LazyDataModel<CtrItensAntReajustes> {

    private static final long serialVersionUID = 1L;
    private List<CtrItensAntReajustes> itens = null;
    private final Persistencia persistencia;
    private final OS_VigSatMobWeb osVigController;
    private final String idContrato;

    public CtrItensAntReajustesLazyList(String idContrato, Persistencia pst) {
        this.idContrato = idContrato;
        this.persistencia = pst;
        osVigController = new OS_VigSatMobWeb();
    }

    @Override
    public List<CtrItensAntReajustes> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            itens = osVigController.listarItensAnterioresPaginada(first, pageSize, idContrato, filters, persistencia);

            // set the total of players
            setRowCount(osVigController.contagemItensAnteriores(idContrato, filters, persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return itens;
    }

    @Override
    public Object getRowKey(CtrItensAntReajustes itens) {
        if (null == itens.getCodFil()
                || null == itens.getContrato()
                || null == itens.getData()
                || null == itens.getTipoPosto()) {
            return null;
        }
        return itens.getCodFil().replace(".0", "")
                + ";" + itens.getContrato()
                + ";" + itens.getData()
                + ";" + itens.getTipoPosto();
    }

    @Override
    public CtrItensAntReajustes getRowData(String codFilContrato) {
        String[] codFilContratoArray = codFilContrato.split(";");
        if (codFilContratoArray.length != 4) {
            return null;
        }
        for (CtrItensAntReajustes contrato : itens) {
            if (codFilContratoArray[0].equals(contrato.getCodFil().replace(".0", ""))
                    && codFilContratoArray[1].equals(contrato.getContrato())
                    && codFilContratoArray[2].equals(contrato.getData())
                    && codFilContratoArray[3].equals(contrato.getTipoPosto())) {
                return contrato;
            }
        }
        return null;
    }
}

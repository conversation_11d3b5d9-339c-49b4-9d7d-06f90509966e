/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S2298 {

    private int sucesso;
    private String evtReintegr_Id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideVinculo_cpfTrab;
    private String ideVinculo_nisTrab;
    private String ideVinculo_matricula;
    private String infoReintegr_tpReint;
    private String infoReintegr_dtEfetRetorno;
    private String infoReintegr_dtEfeito;
    private String infoReintegr_indPagtoJuizo;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtReintegr_Id() {
        return evtReintegr_Id;
    }

    public void setEvtReintegr_Id(String evtReintegr_Id) {
        this.evtReintegr_Id = evtReintegr_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeVinculo_cpfTrab() {
        return ideVinculo_cpfTrab;
    }

    public void setIdeVinculo_cpfTrab(String ideVinculo_cpfTrab) {
        this.ideVinculo_cpfTrab = ideVinculo_cpfTrab;
    }

    public String getIdeVinculo_nisTrab() {
        return ideVinculo_nisTrab;
    }

    public void setIdeVinculo_nisTrab(String ideVinculo_nisTrab) {
        this.ideVinculo_nisTrab = ideVinculo_nisTrab;
    }

    public String getIdeVinculo_matricula() {
        return ideVinculo_matricula;
    }

    public void setIdeVinculo_matricula(String ideVinculo_matricula) {
        this.ideVinculo_matricula = ideVinculo_matricula;
    }

    public String getInfoReintegr_tpReint() {
        return infoReintegr_tpReint;
    }

    public void setInfoReintegr_tpReint(String infoReintegr_tpReint) {
        this.infoReintegr_tpReint = infoReintegr_tpReint;
    }

    public String getInfoReintegr_dtEfetRetorno() {
        return infoReintegr_dtEfetRetorno;
    }

    public void setInfoReintegr_dtEfetRetorno(String infoReintegr_dtEfetRetorno) {
        this.infoReintegr_dtEfetRetorno = infoReintegr_dtEfetRetorno;
    }

    public String getInfoReintegr_dtEfeito() {
        return infoReintegr_dtEfeito;
    }

    public void setInfoReintegr_dtEfeito(String infoReintegr_dtEfeito) {
        this.infoReintegr_dtEfeito = infoReintegr_dtEfeito;
    }

    public String getInfoReintegr_indPagtoJuizo() {
        return infoReintegr_indPagtoJuizo;
    }

    public void setInfoReintegr_indPagtoJuizo(String infoReintegr_indPagtoJuizo) {
        this.infoReintegr_indPagtoJuizo = infoReintegr_indPagtoJuizo;
    }

}

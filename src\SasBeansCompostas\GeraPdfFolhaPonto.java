/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Cargos;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.Rh_Ctrl;
import SasBeans.Sindicatos;

/**
 *
 * <AUTHOR>
 */
public class GeraPdfFolhaPonto {

    private Rh_Ctrl rh_Ctrl;
    private Funcion funcion;
    private PstServ pstServ;
    private Sindicatos sindicatos;
    private Filiais filiais;
    private Cargos cargos;

    /**
     * @return the rh_Ctrl
     */
    public Rh_Ctrl getRh_Ctrl() {
        return rh_Ctrl;
    }

    /**
     * @param rh_Ctrl the rh_Ctrl to set
     */
    public void setRh_Ctrl(Rh_Ctrl rh_Ctrl) {
        this.rh_Ctrl = rh_Ctrl;
    }

    /**
     * @return the funcion
     */
    public Funcion getFuncion() {
        return funcion;
    }

    /**
     * @param funcion the funcion to set
     */
    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    /**
     * @return the pstServ
     */
    public PstServ getPstServ() {
        return pstServ;
    }

    /**
     * @param pstServ the pstServ to set
     */
    public void setPstServ(PstServ pstServ) {
        this.pstServ = pstServ;
    }

    /**
     * @return the sindicatos
     */
    public Sindicatos getSindicatos() {
        return sindicatos;
    }

    /**
     * @param sindicatos the sindicatos to set
     */
    public void setSindicatos(Sindicatos sindicatos) {
        this.sindicatos = sindicatos;
    }

    /**
     * @return the filiais
     */
    public Filiais getFiliais() {
        return filiais;
    }

    /**
     * @param filiais the filiais to set
     */
    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    /**
     * @return the cargos
     */
    public Cargos getCargos() {
        return cargos;
    }

    /**
     * @param cargos the cargos to set
     */
    public void setCargos(Cargos cargos) {
        this.cargos = cargos;
    }

}

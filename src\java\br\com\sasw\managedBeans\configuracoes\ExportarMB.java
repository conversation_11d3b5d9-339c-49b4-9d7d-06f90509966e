/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.configuracoes;

import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.ParametDao;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import java.awt.Color;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import javax.faces.application.FacesMessage;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

/**
 *
 * <AUTHOR>
 */
@Named(value = "exportarMB")
@ViewScoped
public class ExportarMB implements Serializable {

    String logo, banco, operador, msgOperador, msgData, titulo;

    public ExportarMB() {
        operador = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("nome");
        banco = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("banco");
        msgOperador = Messages.getMessageS("operador") + ": " + FuncoesString.RecortaAteEspaço(operador, 0, 10);
        msgData = Messages.getMessageS("data") + ": " + DataAtual.getDataAtual("TELA");
        titulo = "titulo";
    }

    public void PdfPreProcessor(Object document) throws Exception {
        try {
            Document pdf = (Document) document;
            pdf.setPageSize(PageSize.A4.rotate());
            pdf.setMargins(-60, -60, 36, 16);

            PdfPTable table = new PdfPTable(1);

            PdfPCell cell = new PdfPCell(new Phrase(msgOperador));
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(msgData));
            table.addCell(cell);

            pdf.open();
            ExternalContext externalContext = FacesContext.getCurrentInstance().getExternalContext();
            logo = externalContext.getRealPath("") + File.separator + "assets" + File.separator + "logos" + File.separator + getLogo(banco);
            Image image = Image.getInstance(logo);
            image.setAlignment(Image.ALIGN_CENTER);
            pdf.add(image);
            if (!titulo.equals("titulo")) {
                Paragraph p = new Paragraph(titulo, new Font(Font.HELVETICA, 18, Font.BOLD, Color.BLACK));
                p.setAlignment(Element.ALIGN_CENTER);
                pdf.add(p);
                Paragraph p2 = new Paragraph("\n", new Font(Font.HELVETICA, 12, Font.BOLD, Color.BLACK));
                p2.setAlignment(Element.ALIGN_CENTER);
                pdf.add(p2);
            }
            pdf.addAuthor(operador);
            pdf.addCreationDate();
        } catch (IOException | DocumentException e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public String getLogo(String banco) throws Exception {
        return LoginMB.getLogoS(banco);
        /*
        String url;
        switch (banco) {
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                try {
                    FacesContext fc = FacesContext.getCurrentInstance();
                    String CodigoFilial = (String) fc.getExternalContext().getSessionMap().get("filial");
                    if (!CodigoFilial.equals("3001")) {
                        url = "logo_confederal.jpg";
                    } else {
                        url = "logo_confere.jpg";
                    }
                } catch (Exception ex) {
                    url = "logo_confederal.jpg";
                }
                
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
                url = "logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "LogoTVip.jpg";
                break;
            case "SATPRESERVE":
                url = "logo_preserve.jpg";
                break;
            case "VSG":
                url = "logo_VSG.jpg";
                break;
            case "SATTSEG":
                url = "logo_tecnoseg.jpg";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
                url = "logo_AGIL.jpg";
                break;
            case "SATLOYAL":
                url = "logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
                url = "logo_transvig.jpg";
                break;
            case "SATINVLMT":
            case "SATINVLRS":
                url = "logo_invioseg.jpg";
                break;
            case "SATGSI":
                url = "logo_GSI.jpg";
                break;
            case "SATTRANSEXCEL":
                url = "logoExcel.jpg";
                break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "LogoRDB.jpg";
                break;
            case "SATTAMEME":
                url = "logo_tameme.jpg";
                break;
            case "SATCOMETRA":
                url = "logo_GSI.jpg";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "LogoSASEX.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "eagle.jpg";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "logosas.jpg";
                break;
            case "SATCIT":
                url = "logo_citcompany.png";
                break;
            case "SATSHALOM":
                url = "logo_shalom.png";
                break;
            default:
                url = "";
        }
        return url;*/
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }
}

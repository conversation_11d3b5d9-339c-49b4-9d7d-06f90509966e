package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesEntDD;

/**
 *
 * <AUTHOR>
 */
public class TesEntDDDao {

    /**
     * Troca a serie da guia em TesEntrada
     *
     * @param persistencia - conexão ao banco de dados
     * @param tesentdd - Guia em tesentrada Obrigatório - guia, série
     * @param novaserie - série de destino da guia
     * @throws Exception
     */
    public void TrocaSerieGuia(Persistencia persistencia, TesEntDD tesentdd, String novaserie) throws Exception {
        try {
            String sql;
            sql = "update TesEntDD set Serie = ?"
                    + " where Guia = ?"
                    + " and Serie =  ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(novaserie);
            consulta.setString(tesentdd.getGuia().toPlainString());
            consulta.setString(tesentdd.getSerie());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao trocar serie da guia em TesEntDD - " + e.getMessage());
        }
    }
}

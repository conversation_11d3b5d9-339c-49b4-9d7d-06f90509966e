/*
 */
package br.com.sasw.conversores;

import static br.com.sasw.utils.Messages.getMessageS;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorTipoDeposito")
public class ConversorTipoDeposito implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            String tipoDeposito = value.toString();
            switch (tipoDeposito.toUpperCase()) {
                case "DINHEIRO":
                    return getMessageS("Deposito");
                case "COLETA":
                    return getMessageS("<PERSON><PERSON>");
                default:
                    return getMessageS(tipoDeposito);
            }
        } catch (Exception e) {
            return value.toString();
        }
    }

}

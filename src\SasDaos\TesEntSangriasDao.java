/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesEntSangrias;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesEntSangriasDao {

    public void atualizaValorDecl(String guia, String serie, String docto, String valorDecl,
            String operador, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE TesEntSangrias ValorDecl = ?, Operador = ?, "
                    + " Dt_Alter = ?, Hr_Alter = ? "
                    + " WHERE guia = ? AND serie = ? AND docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valorDecl);
            consulta.setString(operador);
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao iniciar sangria - " + e.getMessage());
        }
    }

    /**
     * Verifica a existencia de uma sangria ou não
     *
     * @param guia
     * @param serie
     * @param docto
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeSangria(String guia, String serie, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql = " select * from TesEntSangrias"
                    + " where guia = ? and serie = ? and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            List<String> sangrias = new ArrayList<>();
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.select();
            while (consulta.Proximo()) {
                sangrias.add(consulta.getString("guia"));
            }
            consulta.Close();
            return !sangrias.isEmpty();
        } catch (Exception e) {
            throw new Exception("Falha ao verificar existencia de sangria - " + e.getMessage());
        }
    }

    /**
     * Verifica a existencia de uma sangria ou não
     *
     * @param guia
     * @param serie
     * @param docto
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getValorDecl(String guia, String serie, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql = " select * from TesEntSangrias"
                    + " where guia = ? and serie = ? and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.select();
            String valorDecl = "0";
            while (consulta.Proximo()) {
                valorDecl = consulta.getString("ValorDecl");
            }
            consulta.Close();
            return valorDecl;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar existencia de sangria - " + e.getMessage());
        }
    }
    
    /**
     * Verifica a existencia de lançamento PDV
     *
     * @param guia
     * @param serie
     * @param docto
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getCodPdv(String guia, String serie, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql = " select * from TesEntSangrias"
                    + " where guia = ? and serie = ? and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.select();
            String codPdv = "";
            while (consulta.Proximo()) {
                codPdv = consulta.getString("PDV");
            }
            consulta.Close();
            return codPdv;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar existencia de sangria - " + e.getMessage());
        }
    }
    
    /**
     * Verifica a existencia de lançamento PDV
     *
     * @param guia
     * @param serie
     * @param docto
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getCodOperador(String guia, String serie, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql = " select * from TesEntSangrias"
                    + " where guia = ? and serie = ? and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.select();
            String codPdvOperador = "";
            while (consulta.Proximo()) {
                codPdvOperador = consulta.getString("PDVOperador");
            }
            consulta.Close();
            return codPdvOperador;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar existencia de sangria - " + e.getMessage());
        }
    }

    public int getQtdSangrias(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = " select count(*) sangrias "
                    + " from tesentsangrias "
                    + " where guia = ? and serie = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            int qtdSangrias = 0;
            while (consulta.Proximo()) {
                qtdSangrias = consulta.getInt("sangrias");
            }
            consulta.Close();
            return qtdSangrias;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar existencia de sangria - " + e.getMessage());
        }
    }

    /**
     * Inicia uma nova sangria
     *
     * @param guia
     * @param serie
     * @param docto
     * @param persistencia
     * @throws Exception
     */
    public void iniciaSangria(String guia, String serie, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql = "Insert into TesEntSangrias (Guia, Serie, Docto, Hr_Rec, Dt_Rec) "
                    + " Values (?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao iniciar sangria - " + e.getMessage());
        }
    }

    /**
     * Atualiza a sangria com os novos valores
     *
     * @param guia
     * @param serie
     * @param docto
     * @param situacao
     * @param operador
     * @param valordecl
     * @param dtmovimento
     * @param persistencia
     * @throws Exception
     */
    public void atualizaSangria(String guia, String serie, String docto, String situacao,
            String operador, String valordecl, String dtmovimento, String pdv, String codOperador, Persistencia persistencia) throws Exception {
        try {
            String sql = "update TesEntSangrias set Situacao = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ?, "
                    + "DtColeta = (select isnull(dtent,convert(date,getdate())) from cxfguias where cxfguias.guia = ? and cxfguias.serie = ?),"
                    + "ValorDecl = ?, dtmovimento = ?, valorapurado = 0, difmaior = 0, difmenor = 0, moedasvalor = 0, lacre = COALESCE(lacre,docto), PDV = ?, PDVOperador = ? "
                    + "where guia = ? and serie = ? and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(situacao);
            consulta.setString(operador);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(valordecl);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            
            consulta.setString(pdv);
            consulta.setString(codOperador);
            
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar sangria - " + e.getMessage());
        }
    }

    /**
     * Insere uma nova entrada na tabela TesEntSangrias
     *
     * @param guia
     * @param serie
     * @param docto
     * @param situacao
     * @param operador
     * @param valordecl
     * @param dtmovimento
     * @param persistencia
     * @throws Exception
     */
    public void InsereSangria(String guia, String serie, String docto, String situacao,
            String operador, String valordecl, String dtmovimento, Persistencia persistencia) throws Exception {
        try {
            String sql = "Insert into TesEntSangrias (Guia, Serie, Docto, Situacao, Operador, Dt_Alter, Hr_Alter, DtColeta,"
                    + "ValorDecl, dtmovimento, valorapurado, difmaior, difmenor, moedasvalor) "
                    + " Values (?,?,?,?,?,?,?, (select dtent from cxfguias where cxfguias.guia = ? and cxfguias.serie = ?),?,?,0,0,0,0)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.setString(situacao);
            consulta.setString(operador);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(valordecl);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir sangria - " + e.getMessage());
        }
    }

    /**
     * Atualiza os valores da sangria caso tenha ocorrido alguma mudança
     *
     * @param guia
     * @param serie
     * @param docto
     * @param persistencia
     * @throws Exception
     */
    public void AtualizaValoresSangria(String guia, String serie, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql = "update TesEntSangrias set valorapurado = ("
                    + "select isnull( sum (valor),0) from tesentdn "
                    + "where tesentdn.guia = tesentsangrias.guia "
                    + "  and tesentdn.serie = tesentsangrias.serie"
                    + "  and tesentdn.docto = tesentsangrias.docto) +"
                    + " (select isnull( sum (valor),0) from tesentdd "
                    + " where tesentdd.guia = tesentsangrias.guia "
                    + "   and tesentdd.serie = tesentsangrias.serie"
                    + "   and tesentdd.docto = tesentsangrias.docto) + "
                    + " (select isnull( sum (valor),0) from tesentmd "
                    + " where tesentmd.guia = tesentsangrias.guia "
                    + "  and tesentmd.serie = tesentsangrias.serie"
                    + "  and tesentmd.docto = tesentsangrias.docto),"
                    + " difmaior = (select isnull( sum (valor),0) from tesentdif"
                    + " where tesentdif.guia = tesentsangrias.guia"
                    + "  and tesentdif.serie = tesentsangrias.serie"
                    + "  and tesentdif.docto = tesentsangrias.docto"
                    + "   and tesentdif.tipo = '+'),"
                    + " difmenor = (select isnull( sum (valor),0) from tesentdif"
                    + " where tesentdif.guia = tesentsangrias.guia"
                    + "  and tesentdif.serie = tesentsangrias.serie"
                    + "  and tesentdif.docto = tesentsangrias.docto"
                    + "   and tesentdif.tipo = '-')"
                    /*+ " moedasvalor = (select isnull( sum (valor),0) from tesentmd "
                    + "where tesentmd.guia = tesentsangrias.guia "
                    + "  and tesentmd.serie = tesentsangrias.serie"
                    + "  and tesentmd.docto = tesentsangrias.docto)"*/
                    + " where guia = ? and serie = ? and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar sangria - " + e.getMessage());
        }
    }

    public List<TesEntSangrias> getAllSangrias(String guia, String serie, Persistencia persistencia) throws Exception {
        List<TesEntSangrias> retorno = new ArrayList<>();
        try {
            String sql = "SELECT docto, (valorapurado + moedasvalor) valorapurado"
                    + " from tesentsangrias "
                    + " WHERE guia = ? AND serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            TesEntSangrias tesEntSangrias;
            while (consulta.Proximo()) {
                tesEntSangrias = new TesEntSangrias();
                tesEntSangrias.setDocto(consulta.getString("docto"));
                tesEntSangrias.setValorApurado(consulta.getBigDecimal("valorApurado"));
                retorno.add(tesEntSangrias);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list sangrias - " + e.getMessage());
        }
    }
}

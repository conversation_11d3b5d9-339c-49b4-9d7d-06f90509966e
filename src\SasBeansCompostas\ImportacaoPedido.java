/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.PreOrder;
import SasBeans.PreOrderVol;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ImportacaoPedido {

    public ImportacaoPedido() {
        this.pedido = new PreOrder();
        this.composicoes = new ArrayList<>();
    }

    private PreOrder pedido;
    private List<PreOrderVol> composicoes;

    public PreOrder getPedido() {
        return pedido;
    }

    public void setPedido(PreOrder pedido) {
        this.pedido = pedido;
    }

    public List<PreOrderVol> getComposicoes() {
        return composicoes;
    }

    public void setComposicoes(List<PreOrderVol> composicoes) {
        this.composicoes = composicoes;
    }
}

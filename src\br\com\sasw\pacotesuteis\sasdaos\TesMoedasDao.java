/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesMoedas;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesMoedasDao {

    public List<TesMoedas> listarMoedas(Persistencia persistencia) throws Exception {
        try {
            List<TesMoedas> retorno = new ArrayList<>();
            String sql = "SELECT\n"
                    + "    *\n"
                    + "FROM\n"
                    + "    TesMoedas\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            TesMoedas tesMoedas;
            while (consulta.Proximo()) {
                tesMoedas = new TesMoedas();
                tesMoedas.setCodMoeda(consulta.getString("CodMoeda"));
                tesMoedas.setDescricao(consulta.getString("Descricao"));
                tesMoedas.setValor(consulta.getString("Valor"));
                tesMoedas.setDtCotacao(consulta.getString("DtCotacao"));
                tesMoedas.setOperador(consulta.getString("Operador"));
                tesMoedas.setDt_Alter(consulta.getString("Dt_Alter"));
                tesMoedas.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesMoedas);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesMoedasVlrDao.obterCotacaoDiaria - " + e.getMessage() + "\r\n"
                    + "SELECT\n"
                    + "    *\n"
                    + "FROM\n"
                    + "    TesMoedas");
        }
    }
}

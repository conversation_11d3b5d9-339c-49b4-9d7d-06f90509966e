<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets" style="overflow:hidden !important; max-height:100% !important;"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/> 
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/menu.css" rel="stylesheet" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/cofres.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/animate.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.3/Chart.min.js"></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiMob}" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            
            <style>
                .FotoTimeLine{
                    height:130px;
                    padding: 4px 0px 4px 0px !important;
                    margin-top:4px;
                }
                
                .FotoTimeLine:nth-child(even){
                    padding-left:10px !important;
                }
                
                .FotoTimeLine div{
                    border-top:2px solid #3C8DBC;
                    border-bottom:2px solid #3C8DBC;
                    width:100%;
                    height: 100%;
                    border-radius:6px;
                    background-size:cover;
                    background-position:center center;
                    box-shadow:2px 2px 3px #CCC;
                }
                
                label[ref="usuario"]{
                    width:100%;
                    margin:0px !important;
                }
                
                .ItemRel{
                    border-top:4px solid #3C8DBC;
                    background-color: #FFF;
                    box-shadow:2px 2px 4px #CCC;
                    border-radius:8px;
                }

                #divDados{
                    overflow-y:auto !important;
                    width:100% !important;
                }

                h4{
                    font-weight: 500 !important;
                    color: #666 !important;
                    font-size:12pt !important;
                    width:100%;
                    border-bottom: thin solid #DDD;
                    padding-bottom: 8px;
                }

                .ItemRelatorio{
                    height:80px;
                    width:100%;
                    padding:0px !important;
                    border-bottom:thin solid #EEE !important;
                }

                .ItemRelatorio:nth-child(even){
                    background-color:#F6F6F6 !important;
                }

                .FotoPrincipal{
                    width:60px;
                    height:60px;
                    background-color:#FFF;
                    background-position:center center;
                    background-size:cover;
                    border-radius:50%;
                }

                .DadosRelatorio{
                    width:100%;
                    height:100%;
                    border-spacing:0px !important;
                }

                .DadosRelatorio tr td{
                    vertical-align:middle !important;
                }

                .DadosRelatorio tr td:nth-child(1){
                    width:70px;
                    text-align:center;
                    padding-left: 8px;
                    vertical-align:top !important;
                    padding-top: 9px;
                }

                .DadosRelatorio tr td:last-child{
                    width:90px;
                    text-align:center;
                    padding-right: 6px;
                }

                .DadosRelatorio tr td:not(:first-child){

                }

                .badge{
                    width:100%;
                    text-align:center;
                    border-radius:4px;
                    background-color:forestgreen;
                    font-size:8pt !important;
                }
                
                .badge-success{
                    padding-bottom:6px !important;
                    margin-top:4px;
                }
                
                .badge i{
                    display:block;
                    margin-top: 4px;
                    margin-bottom: 4px;
                }

                label[ref="ocorrencia"]{
                    background-color:orangered;
                    color:#FFF;
                    border-radius:12px;
                    padding:1px 8px 1px 8px;
                    text-align:center;
                }
                
                .timeline{
                    position: relative;
                    padding-top: 3rem;
                    padding-bottom: 0px !important;
                }

                .timeline:before{
                    content: "";
                    width: 4px;
                    height: 100%;
                    background-color: #3C8DBC;
                    position: absolute;
                    top: 0;
                }

                .timeline__group{
                    position: relative;
                }

                .timeline__group:not(:first-of-type){
                    margin-top: 4rem;
                }

                .timeline__year{
                    padding: .5rem 1.5rem;
                    color: var(--uiTimelineSecondaryColor);
                    background-color: #FFF;
                    color:#3C8DBC;
                    border:thin solid #3C8DBC;
                    position: absolute;
                    left: -3px;
                    top: 0;
                    font-weight:bold;
                }

                .timeline__box{
                    position: relative;
                }

                .timeline__box:nth-child(odd) .timeline__post{
                    background-color:#EEE;
                }
                
                .timeline__box:not(:last-of-type){
                    margin-bottom: 30px;
                }

                .timeline__box:before{
                    content: "";
                    width: 100%;
                    height: 2px;
                    background-color: #3C8DBC;
                    position: absolute;
                    left: 0;
                    z-index: -1;
                }

                .timeline__date{
                    min-width: 65px;
                    position: absolute;
                    left: 0;

                    box-sizing: border-box;
                    padding: .5rem 1.5rem;
                    text-align: center;

                    background-color: #3C8DBC;
                    color: var(--uiTimelineSecondaryColor);
                    border-radius:20px;
                }

                .timeline__day{
                    font-size: 2rem;
                    font-weight: 700;
                    display: block;
                }

                .timeline__month{
                    display: block;
                    font-size: .8em;
                    text-transform: uppercase;
                }

                .timeline__post{
                    padding: 1.5rem 2rem;
                    border-radius: 2px;
                    border-left: 5px solid #3C8DBC !important;
                    border-right: thin solid #CCC;
                    border-top: thin solid #CCC;
                    border-bottom: thin solid #CCC;
                    box-shadow: 2px 2px 4px #DDD;
                    background-color: var(--uiTimelineSecondaryColor);
                    border-radius: 2px 4px 4px 2px;
                    color:#555 !important;
                    font-size:10pt !important;
                    padding-bottom:0px !important;
                }
                
                .timeline__content{
                    height: 100%;
                    display: block !important;
                }

                @media screen and (min-width: 641px){

                    .timeline:before{
                        left: 30px;
                    }

                    .timeline__group{
                        padding-top: 55px;
                    }

                    .timeline__box{
                        padding-left: 80px;
                    }

                    .timeline__box:before{
                        top: 50%;
                        transform: translateY(-50%);  
                    }  

                    .timeline__date{
                        top: 50%;
                        margin-top: -27px;
                    }
                }

                @media screen and (max-width: 640px){

                    .timeline:before{
                        left: 0;
                    }

                    .timeline__group{
                        padding-top: 40px;
                    }

                    .timeline__box{
                        padding-left: 20px;
                        padding-top: 70px;
                    }

                    .timeline__box:before{
                        top: 90px;
                    }    

                    .timeline__date{
                        top: 0;
                    }
                }

                .timeline{
                    font-size: 16px;
                }

                /*
                =====
                DEMO
                =====
                */

                @media (min-width: 768px){

                    html{
                        font-size: 62.5%;
                    }
                }

                @media (max-width: 767px){

                    html{
                        font-size: 55%;
                    }
                }

                body{
                    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Open Sans, Ubuntu, Fira Sans, Helvetica Neue, sans-serif;
                    font-size: 1.6rem;
                    color: #222;

                    background-color: #f0f0f0;
                    margin: 0;
                    -webkit-overflow-scrolling: touch;   
                    overflow-y: scroll;

                    display: flex;
                    flex-direction: column;
                }

                p{
                    margin-top: 0;
                    margin-bottom: 1.5rem;
                    line-height: 1.5;
                }

                p:last-child{
                    margin-bottom: 0;
                }

                .page{
                    max-width: 800px;
                    padding: 10rem 2rem 3rem;
                    margin-left: auto;
                    margin-right: auto;
                    order: 1;
                }

            </style>
        </h:head>
        <h:body id="h" style="overflow:hidden !important;min-height:100% !important;">            
            <f:metadata>
                <f:viewAction action="#{mobEW.Persistencia(login.pp, login.satellite)}" />
                <f:viewAction action="#{mobEW.carregarDadosRelatorios()}" />
            </f:metadata>

            <p:growl id="msgs" widgetVar="g"/>

            <div id="body" style="overflow:hidden !important;max-height:100% !important;">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_satmob_contratosG.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Relatorios}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{mobEW.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{mobEW.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{mobEW.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{mobEW.filiais.bairro}, #{mobEW.filiais.cidade}/#{mobEW.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:commandLink action="#{mobEW.dataAnterior}" update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />  
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{mobEW.dataTela}" locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                        <p:ajax event="dateSelect" listener="#{mobEW.selecionarData}" update="main cabecalho" />
                                    </p:calendar>

                                    <p:commandLink action="#{mobEW.dataPosterior}" update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />  
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>
                <h:form id="main" style="overflow:hidden !important; padding:10px 2px 10px 2px !important;">
                    <div id="divDados">
                        <div ref="fundoItem" class="col-md-7 col-sm-7 col-xs-12" style="padding:8px !important; float:left;">
                            <div class="col-md-12 col-sm-12 col-xs-12 ItemRel">
                                <h4 style="margin:10px 0px 0px 0px !important;">#{localemsgs.Relatorios}</h4>
                                <div class="col-md-12" style="height:calc(100% - 60px); overflow-y:auto; padding:0px;">
                                    <h:outputText value="#{mobEW.dadosRelatorioLine}" escape="false"></h:outputText>
                                   
                                </div>
                            </div>    
                        </div>


                        <div ref="fundoItem" class="col-md-5 col-sm-5 col-xs-12" style="padding:8px !important; height:100% !important; float:right; top:0px !important;">
                            <div class="col-md-12 col-sm-12 col-xs-12 ItemRel" ref="timeline" style="height:700px !important;">
                                <h4>#{localemsgs.Eventos}</h4>
                                <div class="col-md-12" style="height:calc(100% - 60px); overflow:auto !important; padding-bottom:0px !important">
                                    <div class="timeline" style="padding-bottom: 0px !important">
                                        <div class="timeline__group" style="padding-bottom: 0px !important">
                                            <h:outputText value="#{mobEW.dadosRelatorioTimeLine}" escape="false"></h:outputText>
                                        </div>
                                    </div>
                                </div>
                            </div>   
                        </div>

                        <div ref="fundoItem" class="col-md-7 col-sm-7 col-xs-12" style="padding:8px !important; float:left;">
                            <div class="col-md-12 col-sm-12 col-xs-12 ItemRel">
                                <h4>#{localemsgs.CheckIn}</h4>
                                <div class="col-md-12" style="height:calc(100% - 60px);">
                                     <div id="mapGoogle" style="height: 100% !important; width:100% !important;"></div>
                                </div>
                            </div>    
                        </div>
                    </div>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            
            <script>
                // <![CDATA[    
               var map;
               
                $(document).ready(function () {
                    AjustarTela();
                });

                function AjustarTela(){
                    $('.ItemRel:not([ref="timeline"])').css('height', (($(window).height() - 146) / 2) + 'px');
                    $('.ItemRel[ref="timeline"]').css('height', ($(window).height() - 130) + 'px');
                    
                    if ($(document).width() <= 700)
                        $('#divDados').css('max-height', ($('body').height() - 195) + 'px');
                    else{
                        $('#divDados').css('max-height', ($(window).height() - 111) + 'px');
                    }
                }

                function initMap(){
                    map = new google.maps.Map(document.getElementById('mapGoogle'), {
                        zoom: 6,
                        gestureHandling: 'cooperative'
                    });
                }

                $(window).resize(function(){                   
                    AjustarTela();
                })
                ;

                // ]]>
            </script>
        </h:body>
    </f:view>
</html>
/*
 */
package br.com.sasw.conversores;

import SasBeans.SasPWFill;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorFilial")
public class ConversorFilial implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        SasPWFill filial = new SasPWFill();
        String[] parts = value.split(" - ");
        filial.setCodfilAc(parts[0]);
        filial.setDescricao(value);
        return filial;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {

        try {
            return ((SasPWFill) value).getDescricao();
        } catch (Exception e) {
            return null;
        }
    }
}

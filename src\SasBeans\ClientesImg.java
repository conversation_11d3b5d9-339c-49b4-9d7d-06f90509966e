/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class ClientesImg {

    private String CodFil;
    private String Codigo;
    private String Sequencia;
    private String Imagem;
    private String FotoPdr;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public ClientesImg() {
        CodFil = "";
        Codigo = "";
        Sequencia = "";
        Imagem = "";
        FotoPdr = "";
        Operador = "";
        Dt_Alter = "";
        Hr_Alter = "";
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getImagem() {
        return Imagem;
    }

    public void setImagem(String Imagem) {
        this.Imagem = Imagem;
    }

    public String getFotoPdr() {
        return FotoPdr;
    }

    public void setFotoPdr(String FotoPdr) {
        this.FotoPdr = FotoPdr;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

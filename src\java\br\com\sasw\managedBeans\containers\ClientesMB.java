/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.containers;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Controller.Login.LoginSatMobWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.MovimentacaoContainer;
import SasBeans.Municipios;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaPortalSrv;
import SasBeans.RamosAtiv;
import SasBeans.Regiao;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeansCompostas.UsuarioSatMobWeb;
import br.com.sasw.lazydatamodels.containers.ClientesLazyList;
import br.com.sasw.pacotesuteis.utilidades.BuscarEndereco;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import br.com.sasw.pacotesuteis.utilidades.ValidadorCPF_CNPJ;
import br.com.sasw.utils.LocaleController;
import static br.com.sasw.utils.Mascaras.CEP;
import static br.com.sasw.utils.Mascaras.CNPJ;
import static br.com.sasw.utils.Mascaras.CPF;
import static br.com.sasw.utils.Mascaras.removeMascara;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.enterprise.context.SessionScoped;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.SelectEvent;
import org.primefaces.json.JSONObject;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "clientescontainer")
@SessionScoped
public class ClientesMB implements Serializable {

    private Persistencia persistencia, central;
    private Clientes novo, cliFat;
    private final ClientesSatMobWeb clientesSatMobWeb;
    private List<MovimentacaoContainer> historicoMovimentacao;
    private BigDecimal codPessoa;
    private String codfil, escolha, banco, operador, param, caminho,
            log, pesquisaNome, nomeAux, cpfcnpj, ierg, centroMapa, qtdeCacambas, qtdeOrcamento;
    private ArquivoLog logerro;
    private int flag, flagUsuario;
    private List<Municipios> cidades;
    private SasPWFill filial;
    private Filiais filiais;
    private final LoginSatMobWeb loginsatmobweb;
    private LazyDataModel<Clientes> clientes = null;
    private List<Clientes> cliFats;
    private Map filters, niveis;
    private List<UsuarioSatMobWeb> usuarios;
    private boolean cadastroNovaPessoa;
    private UsuarioSatMobWeb usuario, buscaUsuario;
    private List<Pessoa> listaPessoa;
    private Pessoa novaPessoa, pessoa;
    private List<SASGrupos> grupos;
    private PessoaPortalSrv servico;
    private PessoaCliAut pessoacliaut;
    private List<Regiao> regioes;
    private Regiao regiao;
    private List<RamosAtiv> ramosAtiv;

    public ClientesMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        novo = new Clientes();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        clientesSatMobWeb = new ClientesSatMobWeb();
        escolha = "cgc";
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
    }

    public void Persistencias(Persistencia pstLocal, Persistencia central) {
        try {
            this.persistencia = pstLocal;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.central = central;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.codfil = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("filial");

            this.filters = new HashMap();
            this.filters.put("clientes.codFil = ?", Arrays.asList(this.codfil));
            this.filters.put("clientes.situacao = ? ", Arrays.asList("A"));
            this.filters.put(" (clientes.nome like ? OR clientes.nred like ?) ", Arrays.asList());

            this.filiais = this.clientesSatMobWeb.buscaInfoFilial(this.codfil, this.persistencia);

            this.niveis = gerarNiveis();
            this.grupos = this.clientesSatMobWeb.listarGrupos(this.persistencia);
            this.ramosAtiv = this.clientesSatMobWeb.listarRamosAtiv(this.persistencia);
            this.regioes = this.clientesSatMobWeb.listarRegioes("1", this.persistencia);
            this.cliFats = this.clientesSatMobWeb.listaCliFat(this.codPessoa, this.persistencia);

            this.pesquisaNome = null;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    private Map gerarNiveis() {
        Map map = new HashMap<>();
        map.put(getMessageS("Administrador"), "9");
        map.put(getMessageS("Operacao"), "1");
        map.put(getMessageS("Manutencao"), "2");
        map.put(getMessageS("Gerencia"), "3");
        return map;
    }

    public void buscarEndereco() {
        try {
            String enderecoCompleto = new BuscarEndereco().BuscarPeloCEP(this.novo.getCEP());
            JSONObject obj = new JSONObject(enderecoCompleto);
            if (Integer.parseInt(obj.get("resultado").toString()) == 1) {
                this.novo.setBairro(obj.get("bairro").toString());
                this.novo.setEnde(obj.get("tipo_logradouro").toString() + " " + obj.get("logradouro").toString());
                if (obj.get("uf").toString().equals("DF") || obj.get("uf").toString().equals("df")) {
                    this.novo.setCidade("BRASILIA");
                    this.novo.setEstado("DF");
                } else {
                    this.cidades = this.clientesSatMobWeb.listarMunicipios(obj.get("uf").toString(), obj.get("cidade").toString(), this.persistencia);
                    this.novo.setCidade(this.cidades.get(0).getNome());
                    this.novo.setEstado(this.cidades.get(0).getUF().substring(0, 2));
                }
                this.novo.setCEP(CEP(this.novo.getCEP()));
                PrimeFaces.current().executeScript("PF('dlgOk').show()");
            } else {
                this.novo.setCEP(CEP(this.novo.getCEP()));
                throw new Exception(Messages.getMessageS("EnderecoNaoEncontrado"));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void mascaraCNPJCPF() {
        try {
            if (this.cpfcnpj != null) {
                this.cpfcnpj = removeMascara(this.cpfcnpj);

                if (this.cpfcnpj.length() == 11) {
                    this.escolha = "cpf";
                    this.cpfcnpj = CPF(this.cpfcnpj);
                } else {
                    this.escolha = "cgc";
                    if (this.cpfcnpj.length() == 14) {
                        this.cpfcnpj = CNPJ(this.cpfcnpj);
                    }
                }
            }
            System.out.println("mascaraCNPJCPF: " + this.cpfcnpj);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrarCliente() {
        try {
            this.novo.setCodFil("1");
            this.novo.setOper_Alt(RecortaAteEspaço(this.operador, 0, 10));
            this.novo.setHr_Alter(getDataAtual("HORA"));
            this.novo.setDt_Alter(LocalDate.now());

            if (this.novo.getNome() != null) {
                this.novo.setNome(this.novo.getNome().toUpperCase());
            }
            if (this.novo.getNRed() != null) {
                this.novo.setNRed(this.novo.getNRed().toUpperCase());
            }
            if (this.novo.getEnde() != null) {
                this.novo.setEnde(this.novo.getEnde().toUpperCase());
            }
            if (this.novo.getBairro() != null) {
                this.novo.setBairro(this.novo.getBairro().toUpperCase());
            }
            if (this.novo.getCidade() != null) {
                this.novo.setCidade(this.novo.getCidade().toUpperCase());
            }
            if (this.novo.getEstado() != null) {
                this.novo.setEstado(this.novo.getEstado().toUpperCase());
            }
            if (this.novo.getIE() != null) {
                this.novo.setIE(this.novo.getIE().toUpperCase());
            }
            this.novo.setSituacao("A");
            this.novo.setDiaFechaFat(1);
            this.novo.setDiaVencNF(1);
            if (this.regiao == null) {
                this.novo.setRegiao("999");
            } else {
                this.novo.setRegiao(this.regiao.getRegiao());
            }

            if (this.escolha != null) {
                if (this.escolha.equals("cpf")) {
                    this.novo.setRG(this.ierg);
                } else {
                    if (this.ierg != null) {
                        this.novo.setInsc_Munic(this.ierg.toUpperCase());
                    }
                }
            }

            this.novo.setFone1(removeMascara(this.novo.getFone1()));
            this.novo.setFone2(removeMascara(this.novo.getFone2()));

            Locale locale = LocaleController.getsCurrentLocale();
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!this.novo.getCPF().isEmpty()) {
                    this.novo.setCPF(removeMascara(this.novo.getCPF()));
                    if (!ValidadorCPF_CNPJ.ValidarCPF(this.novo.getCPF())) {
                        throw new Exception(Messages.getMessageS("CPFInvalido"));
                    }
                }
                if (!this.novo.getCGC().isEmpty()) {
                    this.novo.setCGC(removeMascara(this.novo.getCGC()));
                    if (!ValidadorCPF_CNPJ.ValidarCNPJ(removeMascara(this.novo.getCGC()))) {
                        throw new Exception(Messages.getMessageS("CNPJInvalido"));
                    }
                }
            }

            this.novo.setCEP(removeMascara(this.novo.getCEP()));
            this.novo = this.clientesSatMobWeb.inserirCliente(this.novo, this.cliFat, this.codPessoa, this.qtdeCacambas, this.persistencia);

            this.pessoacliaut = new PessoaCliAut();
            this.pessoacliaut.setCodigo(this.codPessoa.toString());
            this.pessoacliaut.setCodCli(this.novo.getCodigo());
            this.pessoacliaut.setCodFil(this.novo.getCodFil().toPlainString());
            this.pessoacliaut.setNomeCli(this.novo.getNRed());
            this.pessoacliaut.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.pessoacliaut.setDt_Alter(getDataAtual("SQL"));
            this.pessoacliaut.setHr_Alter(getDataAtual("HORA"));
            this.clientesSatMobWeb.inserirCliente(this.pessoacliaut, this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editarCliente() {
        try {
            this.novo.setOper_Alt(RecortaAteEspaço(this.operador, 0, 10));
            this.novo.setHr_Alter(getDataAtual("HORA"));
            this.novo.setDt_Alter(LocalDate.now());

            if (this.novo.getNome() != null) {
                this.novo.setNome(this.novo.getNome().toUpperCase());
            }
            if (this.novo.getNRed() != null) {
                this.novo.setNRed(this.novo.getNRed().toUpperCase());
            }
            if (this.novo.getEnde() != null) {
                this.novo.setEnde(this.novo.getEnde().toUpperCase());
            }
            if (this.novo.getBairro() != null) {
                this.novo.setBairro(this.novo.getBairro().toUpperCase());
            }
            if (this.novo.getCidade() != null) {
                this.novo.setCidade(this.novo.getCidade().toUpperCase());
            }
            if (this.novo.getEstado() != null) {
                this.novo.setEstado(this.novo.getEstado().toUpperCase());
            }
            if (this.novo.getIE() != null) {
                this.novo.setIE(this.novo.getIE().toUpperCase());
            }
            this.novo.setSituacao("A");
            this.novo.setDiaFechaFat(1);
            this.novo.setDiaVencNF(1);
            if (this.regiao == null) {
                this.novo.setRegiao("999");
            } else {
                this.novo.setRegiao(this.regiao.getRegiao());
            }

            if (this.escolha != null) {
                if (this.escolha.equals("cpf")) {
                    this.novo.setRG(this.ierg);
                } else {
                    if (this.ierg != null) {
                        this.novo.setInsc_Munic(this.ierg.toUpperCase());
                    }
                }
            }

            this.novo.setFone1(removeMascara(this.novo.getFone1()));
            this.novo.setFone2(removeMascara(this.novo.getFone2()));

            Locale locale = LocaleController.getsCurrentLocale();
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!this.novo.getCPF().isEmpty()) {
                    this.novo.setCPF(removeMascara(this.novo.getCPF()));
                    if (!ValidadorCPF_CNPJ.ValidarCPF(this.novo.getCPF())) {
                        throw new Exception(Messages.getMessageS("CPFInvalido"));
                    }
                }
                if (!this.novo.getCGC().isEmpty()) {
                    this.novo.setCGC(removeMascara(this.novo.getCGC()));
                    if (!ValidadorCPF_CNPJ.ValidarCNPJ(removeMascara(this.novo.getCGC()))) {
                        throw new Exception(Messages.getMessageS("CNPJInvalido"));
                    }
                }
            }

            this.novo.setCEP(removeMascara(this.novo.getCEP()));
//            this.novo = this.clientesSatMobWeb.inserirCliente(this.novo, this.codPessoa, this.qtdeCacambas, this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Endereco() {
        try {
            String enderecoCompleto = new BuscarEndereco().BuscarPeloCEP(this.novo.getCEP());
            JSONObject obj = new JSONObject(enderecoCompleto);
            if (Integer.parseInt(obj.get("resultado").toString()) == 1) {
                this.novo.setBairro(obj.get("bairro").toString());
                this.novo.setEnde(obj.get("tipo_logradouro").toString() + " " + obj.get("logradouro").toString());
                if (obj.get("uf").toString().equals("DF") || obj.get("uf").toString().equals("df")) {
                    this.novo.setCidade("BRASILIA");
                    this.novo.setEstado("DF");
                } else {
                    this.cidades = this.clientesSatMobWeb.listaMunicipios(obj.get("uf").toString(), obj.get("cidade").toString(), this.persistencia);
                    this.novo.setCidade(this.cidades.get(0).getNome());
                    this.novo.setEstado(this.cidades.get(0).getUF().substring(0, 2));
                }
                this.novo.setCEP(CEP(this.novo.getCEP()));
                PrimeFaces.current().executeScript("PF('dlgOk').show()");
            } else {
                this.novo.setCEP(CEP(this.novo.getCEP()));
                throw new Exception(getMessageS("EnderecoNaoEncontrado"));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
        }
    }

    public List<String> BuscarCidade(String query) {
        try {
            List<String> retorno = new ArrayList<>();
            this.cidades = this.clientesSatMobWeb.listaMunicipios(query, this.persistencia);
            for (Municipios cidade : this.cidades) {
                retorno.add(cidade.getNome() + ", " + cidade.getUF());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
        }
        return null;
    }

    public void SelecionarCidade(SelectEvent event) {
        String[] parts = event.getObject().toString().split(", ");
        this.novo.setCidade(parts[0]);
        this.novo.setEstado(parts[1]);
    }

    public void Cadastrar() {
        try {
            this.novo.setCodFil(this.filial.getCodfilAc());
            this.novo.setOper_Alt(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novo.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novo.setDt_Alter(LocalDate.now());

            this.novo.setNome(this.novo.getNome().toUpperCase());
            this.novo.setNRed(this.novo.getNRed().toUpperCase());
            this.novo.setEnde(this.novo.getEnde().toUpperCase());
            this.novo.setBairro(this.novo.getBairro().toUpperCase());
            this.novo.setCidade(this.novo.getCidade().toUpperCase());
            this.novo.setEstado(this.novo.getEstado().toUpperCase());
            this.novo.setInsc_Munic(this.novo.getInsc_Munic().toUpperCase());
            this.novo.setIE(this.novo.getIE().toUpperCase());
            this.novo.setSituacao("A");
            this.novo.setDiaFechaFat(1);
            this.novo.setDiaVencNF(1);
            this.novo.setRegiao("999");

            this.novo.setFone1(removeMascara(this.novo.getFone1()));
            this.novo.setFone2(removeMascara(this.novo.getFone2()));

            Locale locale = LocaleController.getsCurrentLocale();
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!this.novo.getCPF().isEmpty()) {
                    this.novo.setCPF(removeMascara(this.novo.getCPF()));
                    if (!ValidadorCPF_CNPJ.ValidarCPF(this.novo.getCPF())) {
                        throw new Exception(getMessageS("CPFInvalido"));
                    }
                }
                if (!this.novo.getCGC().isEmpty()) {
                    this.novo.setCGC(removeMascara(this.novo.getCGC()));
                    if (!ValidadorCPF_CNPJ.ValidarCNPJ(removeMascara(this.novo.getCGC()))) {
                        throw new Exception(getMessageS("CNPJInvalido"));
                    }
                }
            }

            this.novo.setCEP(removeMascara(this.novo.getCEP()));
            this.clientesSatMobWeb.gravaNovoCliente(this.novo, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
        }
    }

    public void buttonAction(ActionEvent actionEvent) {
        if (null == this.novo) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneCliente"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.novo = new Clientes(this.novo);
                this.novo.setCEP(CEP(removeMascara(this.novo.getCEP())));

                this.filial = this.loginsatmobweb.BuscaFilial(this.novo.getCodFil().toString(), this.codPessoa, this.persistencia);
                FacesContext.getCurrentInstance().getExternalContext().redirect("../clientes/new.xhtml?faces-redirect=true");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                this.log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(this.log, caminho);
            }

        }
    }

    public void editarCliente(Clientes cliente) {
        if (null == cliente) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneCliente"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.flag = 2;
                this.novo = new Clientes(cliente);
                this.novo.setCEP(CEP(removeMascara(this.novo.getCEP())));

                this.filial = this.loginsatmobweb.BuscaFilial(this.novo.getCodFil().toString(), this.codPessoa, this.persistencia);
                FacesContext.getCurrentInstance().getExternalContext().redirect("../clientes/new.xhtml?faces-redirect=true");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                this.log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(this.log, caminho);
            }

        }
    }

    public void Editar() {
        try {
            this.novo.setCodFil(this.filial.getCodfilAc());
            this.novo.setOper_Alt(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novo.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novo.setDt_Alter(LocalDate.now());

            Locale locale = LocaleController.getsCurrentLocale();
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!this.novo.getCPF().isEmpty()) {
                    this.novo.setCPF(removeMascara(this.novo.getCPF()));
                    if (!ValidadorCPF_CNPJ.ValidarCPF(this.novo.getCPF())) {
                        throw new Exception(getMessageS("CPFInvalido"));
                    }
                }
                if (!this.novo.getCGC().isEmpty()) {
                    this.novo.setCGC(removeMascara(this.novo.getCGC()));
                    if (!ValidadorCPF_CNPJ.ValidarCNPJ(removeMascara(this.novo.getCGC()))) {
                        throw new Exception(getMessageS("CNPJInvalido"));
                    }
                }
            }

            this.novo.setFone1(removeMascara(this.novo.getFone1()));
            this.novo.setFone2(removeMascara(this.novo.getFone2()));
            this.novo.setCEP(removeMascara(this.novo.getCEP()));

            this.novo.setNome(this.novo.getNome().toUpperCase());
            this.novo.setNRed(this.novo.getNRed().toUpperCase());
            this.novo.setEnde(this.novo.getEnde().toUpperCase());
            this.novo.setBairro(this.novo.getBairro().toUpperCase());
            this.novo.setCidade(this.novo.getCidade().toUpperCase());
            this.novo.setEstado(this.novo.getEstado().toUpperCase());
            this.novo.setInsc_Munic(this.novo.getInsc_Munic().toUpperCase());
            this.novo.setIE(this.novo.getIE().toUpperCase());

            this.clientesSatMobWeb.gravaCliente(this.novo, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
        }
        this.novo = new Clientes();
    }

    public void novoCliente() {
        this.novo = new Clientes();
        this.flag = 1;
        try {
            this.filial = this.loginsatmobweb.BuscaFilial(this.codfil, this.codPessoa, this.persistencia);
            this.novo.setCodFil(this.filial.getCodfilAc());
            FacesContext.getCurrentInstance().getExternalContext().redirect("../clientes/new.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void pesquisar() {
        if (!this.pesquisaNome.equals("")) {
            this.filters.replace(" (clientes.nome like ? OR clientes.nred like ?) ",
                    Arrays.asList("%" + this.pesquisaNome + "%", "%" + this.pesquisaNome + "%"));
        }
        getAllClientes();
    }

    public void limparPesquisa() {
        this.pesquisaNome = null;
        this.filters.replace(" (clientes.nome like ? OR clientes.nred like ?) ", Arrays.asList());
        getAllClientes();
    }

    public LazyDataModel<Clientes> getAllClientes() {
        if (this.clientes == null) {
            this.clientes = new ClientesLazyList(this.persistencia, this.codPessoa, this.filters);
        } else {
            ((ClientesLazyList) this.clientes).setFilters(this.filters);
        }
        return this.clientes;
    }

    public void abrirUsuarios(Clientes novo) {
        try {
            this.novo = novo;
            this.usuarios = this.clientesSatMobWeb.listagemUsuarios(this.novo.getCodigo(), this.persistencia, this.central);
            PrimeFaces.current().executeScript("PF('dlgListarUsuarios').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void novoUsuario() {
        try {
            this.pessoa = new Pessoa();

            this.listaPessoa = new ArrayList<>();
            this.listaPessoa.add(this.pessoa);

            this.novaPessoa = new Pessoa();
            this.novaPessoa.setCodigo("-1");
            this.novaPessoa.setNome(getMessageS("CadastrarNovaPessoa"));

            this.cadastroNovaPessoa = false;

            this.usuario = new UsuarioSatMobWeb();
            this.usuario.setPessoa(this.pessoa);

            this.flagUsuario = 1;

            PrimeFaces.current().resetInputs("formCadastrarUsuario:editar");
            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirUsuario(UsuarioSatMobWeb usuario) {
        try {
            this.usuario = usuario;

            this.cadastroNovaPessoa = false;

            this.flagUsuario = 2;

            PrimeFaces.current().resetInputs("formCadastrarUsuario:editar");
            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Pessoa> listarQueryValida(String query) {
        try {
            this.nomeAux = query;
            this.listaPessoa = this.clientesSatMobWeb.listaPessoaQuery(query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        this.listaPessoa.add(this.novaPessoa);
        return this.listaPessoa;
    }

    public void selecionarPessoa(SelectEvent event) {
        try {
            this.usuario = new UsuarioSatMobWeb();
            // Buscar pessoa:
            this.pessoa = ((Pessoa) event.getObject());

            // Pessoa existe?
            if (this.pessoa.getCodigo().toBigInteger().toString().equals("-1")) {
                // Pessoa não existe.
                this.cadastroNovaPessoa = true;
                this.usuario.getPessoa().setNome(this.nomeAux);
            } else {
                // Pessoa existe.
                this.cadastroNovaPessoa = false;
                this.usuario.setPessoa(this.pessoa);

                // Pessoa possui codPessoaWeb?
                if (null == this.usuario.getPessoa().getCodPessoaWEB()) {
                    // Pessoa não possui codPessoaWeb.
                    this.pessoa = this.clientesSatMobWeb.inserirCodPessoaWeb(this.pessoa, this.persistencia, this.central);
                    this.usuario.setPessoa(this.pessoa);
                }

                // Pessoa possui codPessoaWeb.
                // Buscar usuário:
                this.buscaUsuario = this.clientesSatMobWeb.buscarUsuario(this.usuario.getPessoa().getCodPessoaWEB(), this.persistencia, this.central);
                if (null != this.buscaUsuario) {
                    this.usuario = this.buscaUsuario;

                    this.flagUsuario = 2;
                }

                this.usuario.getSaspw().setNome(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getSaspw().setCodPessoa(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getSaspw().setCodPessoaWeb(this.usuario.getPessoa().getCodPessoaWEB().toPlainString());
                this.usuario.getSaspw().setNomeCompleto(this.usuario.getPessoa().getNome());
                this.usuario.getSaspw().setCodigo(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getPessoalogin().setCodigo(this.usuario.getPessoa().getCodPessoaWEB());
                this.usuario.getPessoalogin().setCodPessoaBD(this.usuario.getPessoa().getCodigo());

            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrar() {
        this.usuario.getSaspw().setOperador(RecortaAteEspaço(this.operador, 0, 10));
        this.usuario.getSaspw().setCodFil(this.novo.getCodFil().toString());

        switch (this.usuario.getSaspw().getNivelx()) {
            case "1":
                this.usuario.getSaspw().setNivelOP(getMessageS("Operacao"));
                break;
            case "2":
                this.usuario.getSaspw().setNivelOP(getMessageS("Manutencao"));
                break;
            case "3":
                this.usuario.getSaspw().setNivelOP(getMessageS("Gerencia"));
                break;
            case "4":
                this.usuario.getSaspw().setNivelOP(getMessageS("PortalRH"));
                break;
            case "5":
                this.usuario.getSaspw().setNivelOP(getMessageS("GTV"));
                break;
            case "6":
                this.usuario.getSaspw().setNivelOP(getMessageS("CofreInteligente"));
                break;
            case "7":
                this.usuario.getSaspw().setNivelOP(getMessageS("AssinarGTV"));
                break;
            case "8":
                this.usuario.getSaspw().setNivelOP(getMessageS("SatMobEW"));
                break;
            case "9":
                this.usuario.getSaspw().setNivelOP(getMessageS("Administrador"));
                break;
            case "10":
                this.usuario.getSaspw().setNivelOP(getMessageS("Chamados"));
                break;
            default:
                this.usuario.getSaspw().setNivelOP(getMessageS(""));
                break;
        }
        try {

            if (this.cadastroNovaPessoa) {
                this.novaPessoa = new Pessoa();
                this.novaPessoa.setNome(this.usuario.getPessoa().getNome().toUpperCase());
                this.novaPessoa.setEmail(this.usuario.getPessoa().getEmail());
                this.novaPessoa.setPWWeb(this.usuario.getPessoa().getPWWeb());
                this.novaPessoa.setCPF(removeMascara(this.usuario.getPessoa().getCPF()));
                this.novaPessoa.setRG(removeMascara(this.usuario.getPessoa().getRG()));
                this.novaPessoa.setRGOrgEmis(removeMascara(this.usuario.getPessoa().getRGOrgEmis()));
                this.novaPessoa.setSituacao(this.usuario.getPessoa().getSituacao());
                this.novaPessoa.setOperador(RecortaAteEspaço(this.operador, 0, 10));
                this.novaPessoa.setDt_Alter(getDataAtual("SQL"));
                this.novaPessoa.setHr_Alter(getDataAtual("HORA"));
                this.novaPessoa = this.clientesSatMobWeb.inserirNovaPessoa(this.novaPessoa, this.persistencia, this.central);

                this.usuario.setPessoa(this.novaPessoa);

                this.usuario.getSaspw().setNome(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getSaspw().setCodPessoa(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getSaspw().setCodPessoaWeb(this.usuario.getPessoa().getCodPessoaWEB().toPlainString());
                this.usuario.getSaspw().setNomeCompleto(this.usuario.getPessoa().getNome());
                this.usuario.getSaspw().setCodigo(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getPessoalogin().setCodigo(this.usuario.getPessoa().getCodPessoaWEB());
                this.usuario.getPessoalogin().setCodPessoaBD(this.usuario.getPessoa().getCodigo());
            }

            this.usuario.getSaspw().setNivelOP(this.usuario.getSaspw().getNivelOP().toUpperCase());
            this.usuario.getSaspw().setDescricao(this.usuario.getSaspw().getDescricao().toUpperCase());
            this.usuario.getSaspw().setMotivo(this.usuario.getSaspw().getMotivo().toUpperCase());
            this.usuario.getPessoalogin().setNivel(this.usuario.getSaspw().getNivelx());
            this.usuario.getPessoalogin().setBancoDados(this.persistencia.getEmpresa());
            this.usuario.getPessoalogin().setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.usuario.getPessoalogin().setDt_alter(getDataAtual("SQL"));
            this.usuario.getPessoalogin().setHr_Alter(getDataAtual("HORA"));
            this.usuario.getSaspw().setCodGrupo(Integer.valueOf(this.usuario.getGrupo().getCodigo()));

            this.clientesSatMobWeb.criarAcesso(this.usuario, this.persistencia, this.central);

            this.servico = new PessoaPortalSrv();
            this.servico.setCodigo(this.usuario.getPessoa().getCodPessoaWEB().toString());
            this.servico.setServico("401");
            this.servico.setOper_Incl(RecortaAteEspaço(this.operador, 0, 10));
            this.servico.setDt_Incl(getDataAtual("SQL"));
            this.servico.setHr_Incl(getDataAtual("HORA"));
            this.servico.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.servico.setDt_Alter(getDataAtual("SQL"));
            this.servico.setHr_Alter(getDataAtual("HORA"));
            this.clientesSatMobWeb.inserirServicoAutomatico(this.servico, this.central);

            this.filial = new SasPWFill();
            this.filial.setCodfilAc(this.usuario.getSaspw().getCodFil());
            this.filial.setCodFil(this.usuario.getSaspw().getCodFil());
            this.filial.setNome(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
            this.filial.setCodigo(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
            this.filial.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.clientesSatMobWeb.inserirFilial(this.filial, this.persistencia);

            this.pessoacliaut = new PessoaCliAut();
            this.pessoacliaut.setCodigo(this.usuario.getSaspw().getCodPessoa().toPlainString());
            this.pessoacliaut.setCodCli(this.novo.getCodigo());
            this.pessoacliaut.setCodFil(this.novo.getCodFil().toPlainString());
            this.pessoacliaut.setNomeCli(this.novo.getNRed());
            this.pessoacliaut.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.pessoacliaut.setDt_Alter(getDataAtual("SQL"));
            this.pessoacliaut.setHr_Alter(getDataAtual("HORA"));
            this.clientesSatMobWeb.inserirCliente(this.pessoacliaut, this.persistencia);

            this.usuarios = this.clientesSatMobWeb.listagemUsuarios(this.novo.getCodigo(), this.persistencia, this.central);

            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editar() {
        try {
            this.usuario.getSaspw().setOperador(RecortaAteEspaço(this.operador, 0, 10));
            switch (this.usuario.getSaspw().getNivelx()) {
                case "1":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Operacao"));
                    break;
                case "2":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Manutencao"));
                    break;
                case "3":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Gerencia"));
                    break;
                case "4":
                    this.usuario.getSaspw().setNivelOP(getMessageS("PortalRH"));
                    break;
                case "5":
                    this.usuario.getSaspw().setNivelOP(getMessageS("GTV"));
                    break;
                case "6":
                    this.usuario.getSaspw().setNivelOP(getMessageS("CofreInteligente"));
                    break;
                case "7":
                    this.usuario.getSaspw().setNivelOP(getMessageS("AssinarGTV"));
                    break;
                case "8":
                    this.usuario.getSaspw().setNivelOP(getMessageS("SatMobEW"));
                    break;
                case "9":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Administrador"));
                    break;
                case "10":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Chamados"));
                    break;
                default:
                    this.usuario.getSaspw().setNivelOP(getMessageS(""));
                    break;
            }
            this.usuario.getSaspw().setNivelOP(RecortaString(this.usuario.getSaspw().getNivelOP().toUpperCase(), 0, 15));
            this.usuario.getSaspw().setDescricao(this.usuario.getSaspw().getDescricao().toUpperCase());
            this.usuario.getSaspw().setMotivo(this.usuario.getSaspw().getMotivo().toUpperCase());
            this.usuario.getSaspw().setCodGrupo(Integer.valueOf(this.usuario.getGrupo().getCodigo()));
            this.usuario.getSaspw().setCodPessoa(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
            this.usuario.getSaspw().setCodPessoaWeb(this.usuario.getPessoa().getCodPessoaWEB().toPlainString());
            this.usuario.getSaspw().setNomeCompleto(this.usuario.getPessoa().getNome());
            this.usuario.getSaspw().setCodigo(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
            this.usuario.getPessoalogin().setCodigo(this.usuario.getPessoa().getCodPessoaWEB());
            this.usuario.getPessoalogin().setCodPessoaBD(this.usuario.getPessoa().getCodigo());
            this.usuario.getPessoalogin().setNivel(this.usuario.getSaspw().getNivelx());

            this.clientesSatMobWeb.criarAcesso(this.usuario, this.persistencia, this.central);

            this.pessoacliaut = new PessoaCliAut();
            this.pessoacliaut.setCodigo(this.usuario.getPessoa().getCodigo().toPlainString());
            this.pessoacliaut.setCodCli(this.novo.getCodigo());
            this.pessoacliaut.setCodFil(this.novo.getCodFil().toPlainString());
            this.pessoacliaut.setNomeCli(this.novo.getNRed());
            this.pessoacliaut.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.pessoacliaut.setDt_Alter(getDataAtual("SQL"));
            this.pessoacliaut.setHr_Alter(getDataAtual("HORA"));
            this.clientesSatMobWeb.inserirCliente(this.pessoacliaut, this.persistencia);

            this.usuarios = this.clientesSatMobWeb.listagemUsuarios(this.novo.getCodigo(), this.persistencia, this.central);

            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void excluir(UsuarioSatMobWeb usuario) {
        try {
            this.usuario = usuario;

            this.pessoacliaut = new PessoaCliAut();
            this.pessoacliaut.setCodigo(this.usuario.getPessoa().getCodigo().toPlainString());
            this.pessoacliaut.setCodCli(this.novo.getCodigo());
            this.pessoacliaut.setCodFil(this.novo.getCodFil().toPlainString());
            this.pessoacliaut.setNomeCli(this.novo.getNRed());
            this.pessoacliaut.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.pessoacliaut.setDt_Alter(getDataAtual("SQL"));
            this.pessoacliaut.setHr_Alter(getDataAtual("HORA"));
            this.pessoacliaut.setFlag_Excl("*");
            this.clientesSatMobWeb.inserirCliente(this.pessoacliaut, this.persistencia);

            this.usuarios = this.clientesSatMobWeb.listagemUsuarios(this.novo.getCodigo(), this.persistencia, this.central);

            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void preCadastroCliente() {
        try {
            this.novo.setCodFil("1");
            this.regiao = null;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        PrimeFaces.current().resetInputs("formCadastrar");
        PrimeFaces.current().ajax().update("formCadastrar:cadastrar");
        TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
        tabs.setActiveIndex(0);
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
    }

    public void abrirCliente(Clientes novo) {
        try {
            this.novo = novo;
            this.qtdeCacambas = this.novo.getObs();
            this.regiao = new Regiao();
            this.regiao.setCodFil("1");
            this.regiao.setRegiao(this.novo.getRegiao());
            PrimeFaces.current().resetInputs("formCadastrar");
            PrimeFaces.current().ajax().update("formCadastrar:cadastrar");
            TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
            tabs.setActiveIndex(1);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public List<Municipios> getCidades() {
        return cidades;
    }

    public void setCidades(List<Municipios> cidades) {
        this.cidades = cidades;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public boolean getObrigatorio() {
        Locale locale = LocaleController.getsCurrentLocale();
        return locale.getLanguage().toUpperCase().equals("PT");
    }

    public List<MovimentacaoContainer> getHistoricoMovimentacao() {
        return historicoMovimentacao;
    }

    public void setHistoricoMovimentacao(List<MovimentacaoContainer> historicoMovimentacao) {
        this.historicoMovimentacao = historicoMovimentacao;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public Clientes getNovo() {
        return novo;
    }

    public void setNovo(Clientes novo) {
        this.novo = novo;
    }

    public List<UsuarioSatMobWeb> getUsuarios() {
        return usuarios;
    }

    public void setUsuarios(List<UsuarioSatMobWeb> usuarios) {
        this.usuarios = usuarios;
    }

    public String getPesquisaNome() {
        return pesquisaNome;
    }

    public void setPesquisaNome(String pesquisaNome) {
        this.pesquisaNome = pesquisaNome;
    }

    public int getFlagUsuario() {
        return flagUsuario;
    }

    public void setFlagUsuario(int flagUsuario) {
        this.flagUsuario = flagUsuario;
    }

    public Map getNiveis() {
        return niveis;
    }

    public void setNiveis(Map niveis) {
        this.niveis = niveis;
    }

    public boolean isCadastroNovaPessoa() {
        return cadastroNovaPessoa;
    }

    public void setCadastroNovaPessoa(boolean cadastroNovaPessoa) {
        this.cadastroNovaPessoa = cadastroNovaPessoa;
    }

    public UsuarioSatMobWeb getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioSatMobWeb usuario) {
        this.usuario = usuario;
    }

    public List<Pessoa> getListaPessoa() {
        return listaPessoa;
    }

    public void setListaPessoa(List<Pessoa> listaPessoa) {
        this.listaPessoa = listaPessoa;
    }

    public Pessoa getNovaPessoa() {
        return novaPessoa;
    }

    public void setNovaPessoa(Pessoa novaPessoa) {
        this.novaPessoa = novaPessoa;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public List<SASGrupos> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<SASGrupos> grupos) {
        this.grupos = grupos;
    }

    public List<Regiao> getRegioes() {
        return regioes;
    }

    public void setRegioes(List<Regiao> regioes) {
        this.regioes = regioes;
    }

    public Regiao getRegiao() {
        return regiao;
    }

    public void setRegiao(Regiao regiao) {
        this.regiao = regiao;
    }

    public List<RamosAtiv> getRamosAtiv() {
        return ramosAtiv;
    }

    public void setRamosAtiv(List<RamosAtiv> ramosAtiv) {
        this.ramosAtiv = ramosAtiv;
    }

    public String getEscolha() {
        return escolha;
    }

    public void setEscolha(String escolha) {
        this.escolha = escolha;
    }

    public String getCpfcnpj() {
        return cpfcnpj;
    }

    public void setCpfcnpj(String cpfcnpj) {
        this.cpfcnpj = cpfcnpj;
    }

    public String getIerg() {
        return ierg;
    }

    public void setIerg(String ierg) {
        this.ierg = ierg;
    }

    public String getCentroMapa() {
        return centroMapa;
    }

    public void setCentroMapa(String centroMapa) {
        this.centroMapa = centroMapa;
    }

    public String getQtdeCacambas() {
        return qtdeCacambas;
    }

    public void setQtdeCacambas(String qtdeCacambas) {
        this.qtdeCacambas = qtdeCacambas;
    }

    public String getQtdeOrcamento() {
        return qtdeOrcamento;
    }

    public void setQtdeOrcamento(String qtdeOrcamento) {
        this.qtdeOrcamento = qtdeOrcamento;
    }

    public List<Clientes> getCliFats() {
        return cliFats;
    }

    public void setCliFats(List<Clientes> cliFats) {
        this.cliFats = cliFats;
    }

    public Clientes getCliFat() {
        return cliFat;
    }

    public void setCliFat(Clientes cliFat) {
        this.cliFat = cliFat;
    }
}

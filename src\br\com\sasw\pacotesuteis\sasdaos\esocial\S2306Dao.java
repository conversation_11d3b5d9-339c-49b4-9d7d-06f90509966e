/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2306;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2306Dao {

    public List<S2306> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select \n"
                    + "1 ideEmpregador_tpInsc, \n"
                    + "Filiais.CNPJ ideEmpregador_nrInsc,\n"
                    + "Funcion.CPF ideTrabSemVinculo_cpfTrab,\n"
                    + "Funcion.PIS ideTrabSemVinculo_nisTrab,\n"
                    + "Funcion.Matr ideTrabSemVinculo_matricula,\n"
                    + "Convert(date,FuncionCargos.Data) altContratual_dtAlteracao,\n"
                    + "Case when FuncionCargos.Motivo like '%CONVENCAO%' then Convert(date,FuncionCargos.Data) else NULL end altContratual_dtEf,\n"
                    + "FuncionCargos.Motivo altContratual_dscAlt, \n"
                    + "1 vinculo_tpRegPrev,\n"
                    + "1 infoCeletista_tpRegJor, \n"
                    + "1 infoCeletista_natAtividade,\n"
                    + "Fornec.CNPJ infoCeletista_cnpjSindCategProf,\n"
                    + " Cargos.Descricao infoContrato_codCargo, \n"
                    + " Case when Funcion.vinculo = 'D' then '721' "
                    + "      when Funcion.Vinculo = 'S' then '723' "
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + " end ideTrabSemVinculo_codCateg, "
                    + "Funcion.Salario remuneracao_vrSalFx, \n"
                    + "Funcion.FormaPgto remuneracao_undSalFixo,\n"
                    + "1 duracao_tpContr,\n"
                    + "1 localTrabGeral_tpInsc,\n"
                    + "Clientes.CGC localTrabGeral_nrInsc,\n"
                    + "RHHorario.tipocomp, RHHorario.chcomp, RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, \n"
                    + "RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, \n"
                    + "RHEscala.HrSabNot, RHEscala.HrDomDiu, RHEscala.HrDomNot, \n"
                    + "0 horContratual_tmpParc, Convert(bigint,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, \n"
                    + " RHHorario.D1, RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, \n"
                    + " FuncionAdic.NatEstagio infoEstagiario_natEstagio, FuncionAdic.NivelEstagio infoEstagiario_nivEstagio, "
                    + " FuncionAdic.DtFimEstagio infoEstagiario_dtPrevTerm, "
                    + " instEnsino.CNPJ instEnsino_cnpjInstEnsino, instEnsino.Empresa instEnsino_nmRazao, instEnsino.Endereco instEnsino_dscLograd, "
                    + " instEnsino.Bairro instEnsino_bairro, instEnsino.CEP instEnsino_cep, instEnsino.CodCidade instEnsino_codMunic, "
                    + " instEnsino.UF instEnsino_uf, "
                    + " AgenteIntegrador.CNPJ ageIntegracao_cnpjAgntInteg, AgenteIntegrador.Empresa ageIntegracao_nmRazao, AgenteIntegrador.Endereco ageIntegracao_dscLograd, "
                    + " AgenteIntegrador.Bairro ageIntegracao_bairro, AgenteIntegrador.CEP ageIntegracao_cep, AgenteIntegrador.CodCidade ageIntegracao_codMunic, "
                    + " AgenteIntegrador.UF ageIntegracao_uf, "
                    + " SupEstagio.CPF supervisorEstagio_cpfSupervisor, SupEstagio.Nome supervisorEstagio_nmSuperv, "
                    + "                     (select max(sucesso) from  ( \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso \n"
                    + "                             From XmleSocial z \n"
                    + "                             where z.Identificador = Funcion.Matr \n"
                    + "                                 and z.evento = 'S-2306' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%aguardando%' \n"
                    + "                                         or z.Xml_Retorno = ''\n"
                    + "                                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))\n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.Matr \n"
                    + "                                 and z.evento = 'S-2306' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%<ocorrencia>%' \n"
                    + "                                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) \n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.Matr \n"
                    + "                                 and z.evento = 'S-2306' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'\n"
                    + "                                      or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso \n"
                    + "from FuncionCargos\n"
                    + "Left join Filiais  on Filiais.CodFil = FuncionCargos.CodFil\n"
                    + "Left join Funcion  on Funcion.Matr = FuncionCargos.Matr\n"
                    + " Left Join Cargos  on Cargos.Codigo = Funcion.CodCargo "
                    + "Left join Sindicatos  on Sindicatos.Codigo = Funcion.Sindicato \n"
                    + "Left join Fornec on Fornec.Codigo = Sindicatos.CodForn\n"
                    + "Left Join PstServ  on Funcion.Secao  = PstServ.Secao \n"
                    + "                   and Funcion.CodFil = PstServ.CodFil \n"
                    + "Left Join Clientes  on Clientes.Codigo = PstServ.CodCli \n"
                    + "                    and Clientes.CodFil = PstServ.CodFil \n"
                    + "Left Join RHEscala  on RHEscala.Codigo = Funcion.Escala \n"
                    + "                    and RHEscala.CodFil = Funcion.CodFil \n"
                    + "Left Join RHHorario  on  RHHorario.Codigo    = Funcion.Horario \n"
                    + "                    and RHHorario.CodFil    = Funcion.CodFil \n"
                    + "Left Join FuncionAdic  on FuncionAdic.Matr = Funcion.Matr \n"
                    + "Left Join Fornec instEnsino  on instEnsino.Codigo = FuncionAdic.InstEnsino \n"
                    + "Left Join Fornec AgenteIntegrador  on AgenteIntegrador.Codigo = FuncionAdic.AgenteIntegrador \n"
                    + "Left Join Pessoa SupEstagio  on SupEstagio.Codigo = FuncionAdic.SupEstagio \n"
                    + "where FuncionCargos.CodFil = ? \n"
                    + "  and Substring(Replace(convert(Varchar,FuncionCargos.Data,111),'/','-'),1,7) = ?\n"
                    + "  and Funcion.Vinculo in ('D','E','S','A') "
                    + "  and FuncionCargos.Motivo <> 'ADMISSAO'\n"
                    + "  and FuncionCargos.Motivo <> 'INICIO DE CONTRATO'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(codFil);
            consulta.setString(compet);

            consulta.select();
            List<S2306> retorno = new ArrayList<>();
            S2306 s2306;
            while (consulta.Proximo()) {
                s2306 = new S2306();

                s2306.setSucesso(consulta.getInt("sucesso"));

                s2306.setIdeEvento_indRetif("1");
                s2306.setIdeEvento_procEmi("1");
                s2306.setIdeEvento_verProc("Satellite eSocial");

                s2306.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2306.setIdeEmpregador_nrInsc(FuncoesString.RecortaString(consulta.getString("ideEmpregador_nrInsc"), 0, 8));

                s2306.setIdeTrabSemVinculo_cpfTrab(consulta.getString("ideTrabSemVinculo_cpfTrab"));
                s2306.setIdeTrabSemVinculo_nisTrab(consulta.getString("ideTrabSemVinculo_nisTrab"));
                s2306.setIdeTrabSemVinculo_codCateg(consulta.getString("ideTrabSemVinculo_codCateg"));

                s2306.setInfoTSVAlteracao_dtAlteracao(consulta.getString("altContratual_dtAlteracao"));
                s2306.setInfoTSVAlteracao_natAtividade("1"); // 1 - Trab Urbano / 2 - Trab Rural

                s2306.setCargoFuncao_codCargo(consulta.getString("infoContrato_codCargo"));

                s2306.setRemuneracao_vrSalFx(consulta.getString("remuneracao_vrSalFx"));
                s2306.setRemuneracao_undSalFixo(consulta.getString("remuneracao_undSalFixo"));
                s2306.setRemuneracao_dscSalVar("");

                s2306.setInfoEstagiario_natEstagio(consulta.getString("infoEstagiario_natEstagio"));
                s2306.setInfoEstagiario_nivEstagio(consulta.getString("infoEstagiario_nivEstagio"));
                s2306.setInfoEstagiario_dtPrevTerm(consulta.getString("infoEstagiario_dtPrevTerm"));

                s2306.setInstEnsino_cnpjInstEnsino(consulta.getString("instEnsino_cnpjInstEnsino"));
                s2306.setInstEnsino_nmRazao(consulta.getString("instEnsino_nmRazao"));
                s2306.setInstEnsino_dscLograd(consulta.getString("instEnsino_dscLograd"));
                s2306.setInstEnsino_nrLograd("");
                s2306.setInstEnsino_bairro(consulta.getString("instEnsino_bairro"));
                s2306.setInstEnsino_cep(consulta.getString("instEnsino_cep"));
                s2306.setInstEnsino_codMunic(consulta.getString("instEnsino_codMunic"));
                s2306.setInstEnsino_uf(consulta.getString("instEnsino_uf"));

                s2306.setAgeIntegracao_cnpjAgntInteg(consulta.getString("ageIntegracao_cnpjAgntInteg"));
                s2306.setAgeIntegracao_nmRazao(consulta.getString("ageIntegracao_nmRazao"));
                s2306.setAgeIntegracao_dscLograd(consulta.getString("ageIntegracao_dscLograd"));
                s2306.setAgeIntegracao_nrLograd("S/N");
                s2306.setAgeIntegracao_bairro(consulta.getString("ageIntegracao_bairro"));
                s2306.setAgeIntegracao_cep(consulta.getString("ageIntegracao_cep"));
                s2306.setAgeIntegracao_codMunic(consulta.getString("ageIntegracao_codMunic"));
                s2306.setAgeIntegracao_uf(consulta.getString("ageIntegracao_uf"));

                s2306.setSupervisorEstagio_cpfSupervisor(consulta.getString("supervisorEstagio_cpfSupervisor"));
                s2306.setSupervisorEstagio_nmSuperv(consulta.getString("supervisorEstagio_nmSuperv"));

                retorno.add(s2306);

            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception();
        }
    }
}

/*
 */
package br.com.sasw.conversores;

import java.math.BigDecimal;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("kbp")
public class ConversorKanBanPessoa implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        if (null == value) {
            return "";
        }
        switch (value) {
            case "1":
                return new BigDecimal("1");
            case "2":
                return new BigDecimal("2");
            case "17":
                return new BigDecimal("17");
            case "25":
                return new BigDecimal("25");
            case "15":
                return new BigDecimal("15");
            case "32":
                return new BigDecimal("32");
            case "51":
                return new BigDecimal("51");
            case "280":
                return new BigDecimal("280");
            case "311":
                return new BigDecimal("311");
            default:
                return "";
        }
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            if (null == value) {
                return "";
            }
            switch (value.toString()) {
                case "1":
                    return "MARCOS ANTONIO";
                case "2":
                    return "ILDO GUILHERME";
                case "17":
                    return "PARIZOTTO";
                case "25":
                    return "MANOEL";
                case "15":
                    return "RICHARD";
                case "32":
                    return "MARCIO ROSEMBERG";
                case "51":
                    return "CLAUDIA";
                case "280":
                    return "MARCUS";
                case "310":
                    return "JULIANA";
                default:
                    return value.toString();
            }
        } catch (Exception e) {
            return value.toString();
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans.compostas;

import SasBeans.Clientes;
import SasBeans.Rotas;
import SasBeans.Rt_Perc;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Rt_PercRotasClientes {

    private Rt_Perc rt_Perc;
    private Rotas rotas;
    private Clientes clientes;

    public Rt_PercRotasClientes() {
        this.rt_Perc = new Rt_Perc();
        this.rotas = new Rotas();
        this.clientes = new Clientes();
    }

    public Rt_Perc getRt_Perc() {
        return rt_Perc;
    }

    public void setRt_Perc(Rt_Perc rt_Perc) {
        this.rt_Perc = rt_Perc;
    }

    public Rotas getRotas() {
        return rotas;
    }

    public void setRotas(Rotas rotas) {
        this.rotas = rotas;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.rt_Perc);
        hash = 97 * hash + Objects.hashCode(this.rotas);
        hash = 97 * hash + Objects.hashCode(this.clientes);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Rt_PercRotasClientes other = (Rt_PercRotasClientes) obj;
        if (!Objects.equals(this.rt_Perc, other.rt_Perc)) {
            return false;
        }
        if (!Objects.equals(this.rotas, other.rotas)) {
            return false;
        }
        if (!Objects.equals(this.clientes, other.clientes)) {
            return false;
        }
        return true;
    }
}

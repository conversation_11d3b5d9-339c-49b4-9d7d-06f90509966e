<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <header>
        <h:form id="cabecalho">
            <div class="ui-grid ui-grid-responsive">
                <div class="ui-grid-row cabecalho">
                    <div
                        id="divTopoTela"
                        class="col-md-4 col-sm-12 col-xs-12"
                        style="align-self: center; padding-left: 4px !important; padding-top:4px !important;"
                        >
                        <img src="../assets/img/icone_tesouraria_entradas.png" height="40" style="margin-top:-6px !important" />
                        <label class="TituloPagina">#{localemsgs.TesourariaEntradas}</label>
                        <label class="TituloDataHora">
                            <h:outputText value="#{localemsgs.Data}: "/>
                            <span>
                                <h:outputText
                                    id="dataDia"
                                    value="#{tesEntrada.dataTela}"
                                    converter="conversorDia"
                                    />
                            </span>
                        </label>
                    </div>

                    <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                        <label class="FilialNome">#{tesEntrada.filialTela.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                        <label class="FilialEndereco">#{tesEntrada.filialTela.endereco}</label>
                        <label class="FilialBairroCidade">#{tesEntrada.filialTela.bairro}, #{tesEntrada.filialTela.cidade}/#{tesEntrada.filialTela.UF}</label>
                    </div>

                    <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                        <p:commandLink action="#{tesEntrada.voltarDia()}" update="main cabecalho msgs">
                            <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                        </p:commandLink>

                        <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                    pattern="#{mascaras.padraoData}" value="#{tesEntrada.dataTela}" locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                            <p:ajax event="dateSelect" listener="#{tesEntrada.selecionarData}" update="main cabecalho msgs" />
                        </p:calendar>

                        <p:commandLink action="#{tesEntrada.avancarDia()}" update="main cabecalho msgs">
                            <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                        </p:commandLink>
                    </div>

                    <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                        <p:commandLink title="#{localemsgs.Voltar}"
                                       onclick="window.history.back();" action="#">
                            <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                        </p:commandLink>
                    </div>
                </div>
            </div>
        </h:form>
    </header>

</ui:composition>
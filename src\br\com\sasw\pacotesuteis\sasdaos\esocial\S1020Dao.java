/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1020;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1020Dao {

    /**
     * LOYAL
     *
     * @param codFil
     * @param compet
     * @param ambiente
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<S1020> get2(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<S1020> retorno = new ArrayList<>();
            String sql = " Select "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc,  "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, Clientes.Nome dadosLotacao_razaoSocial, "
                    + " Convert(varchar,Convert(BigInt,Filiais.CodFil*10000))+Convert(Varchar,Clientes.Codigo) ideLotacao_codLotacao, "                    
                    + " Case when Filiais.PorteEmp = 5 then '01' "
                    + "     when Filiais.PorteEmp = 3 then '04' "                    
                    + "    else '03' end dadosLotacao_tpLotacao,  "
                    + " Case when Len(Clientes.CPF) = 0 then '1' else '2' end dadosLotacao_tpInsc,  "
                    + " Case when Len(Clientes.CPF) = 0 then clientes.CGC else Clientes.CPF end dadosLotacao_nrInsc,  "
                    + " Filiais.FPAS fpasLotacao_fpas, Filiais.CodOutrasEnt fpasLotacao_codTercs,   "
                    + " (select max(sucesso) from  (  "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Convert(varchar,Convert(BigInt,Filiais.CodFil*10000))+Convert(Varchar,Clientes.Codigo) "
                    + "             and z.evento = 'S-1020'  "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%'  "
                    + "                     or z.Xml_Retorno = '' "
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = Convert(varchar,Convert(BigInt,Filiais.CodFil*10000))+Convert(Varchar,Clientes.Codigo) "
                    + "             and z.evento = 'S-1020'  "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%'  "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = Convert(varchar,Convert(BigInt,Filiais.CodFil*10000))+Convert(Varchar,Clientes.Codigo) "
                    + "             and z.evento = 'S-1020'  "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso  "
                    + " From Funcion "
                    + " left join Filiais    on Filiais.CodFil   = Funcion.CodFil "
                    + " left join PstServ    on  PstServ.Secao   = Funcion.Secao   "
                    + "                     and PstServ.CodFil  = Funcion.CodFil  "
                    + " left join OS_Vig     on  OS_Vig.OS       = PstServ.OS       "
                    + "                     and OS_Vig.CodFil   = PstServ.CodFil   "
                    + " left join Clientes   on  Clientes.Codigo = OS_Vig.CliFat    "
                    + "                     and Clientes.CodFil = OS_Vig.CodFil    "
                    + " where Funcion.CodFil = ? "
                    + "   and Funcion.Situacao <> 'D' "
                    + " Group by Filiais.TipoPessoa,Filiais.CNPJ,Filiais.CodFil, Filiais.PorteEmp, Clientes.Nome, "
                    + " Filiais.FPAS, Filiais.CodOutrasEnt, Clientes.Codigo, Clientes.CPF, Clientes.CGC  ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.select();
            S1020 s1020;
            while (consulta.Proximo()) {
                s1020 = new S1020();
                s1020.setSucesso(consulta.getInt("sucesso"));
                s1020.setIdeEvento_procEmi("1");
                s1020.setIdeEvento_verProc("Satellite eSocial");
                s1020.setIdeEvento_tpAmb(ambiente);
                s1020.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1020.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s1020.setIdeLotacao_codLotacao(consulta.getString("ideLotacao_codLotacao"));
                s1020.setIdeLotacao_iniValid(compet);
                s1020.setDadosLotacao_tpLotacao(consulta.getString("dadosLotacao_tpLotacao"));
                s1020.setDadosLotacao_tpInsc(consulta.getString("dadosLotacao_tpInsc"));
                s1020.setDadosLotacao_nrInsc(consulta.getString("dadosLotacao_nrInsc"));
                s1020.setDadosLotacao_razaoSocial(consulta.getString("dadosLotacao_razaoSocial"));
                s1020.setFpasLotacao_fpas(consulta.getString("fpasLotacao_fpas"));
                s1020.setFpasLotacao_codTercs(consulta.getString("fpasLotacao_codTercs"));
                retorno.add(s1020);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1020Dao.get - " + e.getMessage() + "\r\n"
                    + " Select Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, (Filiais.CodFil*10000)+Filiais.CodFil ideLotacao_codLotacao, "
                    + " Case when Filiais.PorteEmp = 5 then '01' "
                    + " when Filiais.PorteEmp = 3 then '04' "                    
                    + "  else '03' end dadosLotacao_tpLotacao,  "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end dadosLotacao_tpInsc, Filiais.CNPJ dadosLotacao_nrInsc, "
                    + " Filiais.FPAS fpasLotacao_fpas, Filiais.CodOutrasEnt fpasLotacao_codTercs,  "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Ambiente = " + ambiente
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Ambiente = " + ambiente
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Ambiente = " + ambiente
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Filiais "
                    + " where Filiais.CodFil = ? "
                    + " Group by Filiais.TipoPessoa,Filiais.CNPJ,Filiais.CodFil, Filiais.FPAS, Filiais.PorteEmp, Filiais.CodOutrasEnt ");
        }
    }

    public List<S1020> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<S1020> retorno = new ArrayList<>();
            String sql = " Select Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, (Filiais.CodFil*10000)+Filiais.CodFil ideLotacao_codLotacao, "
                    + " Case when Filiais.PorteEmp = 5 then '01' "
                    + " when Filiais.PorteEmp = 3 then '04' "
                    + "  else '03' end dadosLotacao_tpLotacao,  "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end dadosLotacao_tpInsc, Filiais.CNPJ dadosLotacao_nrInsc, "
                    + " Filiais.FPAS fpasLotacao_fpas, Filiais.CodOutrasEnt fpasLotacao_codTercs,  "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = (Filiais.CodFil*10000)+Filiais.CodFil "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = (Filiais.CodFil*10000)+Filiais.CodFil "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = (Filiais.CodFil*10000)+Filiais.CodFil "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Filiais "
                    + " where Filiais.CodFil = ? "
                    + " Group by Filiais.TipoPessoa,Filiais.CNPJ,Filiais.CodFil, Filiais.FPAS, Filiais.PorteEmp, Filiais.CodOutrasEnt ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.select();
            S1020 s1020;
            while (consulta.Proximo()) {
                s1020 = new S1020();
                s1020.setSucesso(consulta.getInt("sucesso"));
                s1020.setIdeEvento_procEmi("1");
                s1020.setIdeEvento_verProc("Satellite eSocial");
                s1020.setIdeEvento_tpAmb(ambiente);
                s1020.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1020.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s1020.setIdeLotacao_codLotacao(consulta.getString("ideLotacao_codLotacao"));
                s1020.setIdeLotacao_iniValid(compet);
                s1020.setDadosLotacao_tpLotacao(consulta.getString("dadosLotacao_tpLotacao"));
                s1020.setDadosLotacao_tpInsc(consulta.getString("dadosLotacao_tpInsc"));
                s1020.setDadosLotacao_nrInsc(consulta.getString("dadosLotacao_nrInsc"));
                s1020.setFpasLotacao_fpas(consulta.getString("fpasLotacao_fpas"));
                s1020.setFpasLotacao_codTercs(consulta.getString("fpasLotacao_codTercs"));
                retorno.add(s1020);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1020Dao.get - " + e.getMessage() + "\r\n"
                    + " Select Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, (Filiais.CodFil*10000)+Filiais.CodFil ideLotacao_codLotacao, "
                    + " Case when Filiais.PorteEmp = 5 then '01' "
                    + " when Filiais.PorteEmp = 3 then '04' "
                    + "  else '03' end dadosLotacao_tpLotacao,  "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end dadosLotacao_tpInsc, Filiais.CNPJ dadosLotacao_nrInsc, "
                    + " Filiais.FPAS fpasLotacao_fpas, Filiais.CodOutrasEnt fpasLotacao_codTercs,  "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Ambiente = " + ambiente
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Ambiente = " + ambiente
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1020' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Ambiente = " + ambiente
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Filiais "
                    + " where Filiais.CodFil = ? "
                    + " Group by Filiais.TipoPessoa,Filiais.CNPJ,Filiais.CodFil, Filiais.FPAS, Filiais.PorteEmp, Filiais.CodOutrasEnt ");
        }
    }
}

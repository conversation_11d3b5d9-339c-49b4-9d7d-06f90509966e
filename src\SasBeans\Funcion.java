package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Funcion {

    private BigDecimal Matr;
    private String Nome;
    private String Nome_Guer;
    private BigDecimal CodFil;
    private int Regional;
    private String Secao;
    private String CCusto;
    private String CodPonto;
    private String Dt_Admis;
    private String Cargo;
    private BigDecimal CodCargo;
    private String Apresen;
    private String Situacao;
    private String Dt_Situac;
    private String Escala;
    private int Horario;
    private int GrpEscala;
    private String Funcao;
    private String Dt_FormIni;
    private String Dt_FormFim;
    private String LocalForm;
    private String Certific;
    private String Reg_PF;
    private String Local_PF;
    private String Reg_PFUF;
    private String Reg_PFDt;
    private String CarNacVig;
    private String DtValCNV;
    private String DtEmissaoCNV;
    private String CadastroAFIS;
    private String Reg_MT;
    private String Dt_Recicl;
    private String Dt_VenCurs;
    private String Dt_ExameMe;
    private String Dt_Psico;
    private String ExtensaoTV;
    private String ExtSegPes;
    private String ExtEscolta;
    private String ExtGrdEventos;
    private String ExtArmasNLetais;
    private String GrupoSang;
    private String Instrucao;
    private String Raca;
    private String EstCivil;
    private String Endereco;
    private String Numero;
    private String Complemento;
    private String Bairro;
    private String Cidade;
    private String UF;
    private String CEP;
    private String Fone1;
    private String Fone2;
    private String Email;
    private String Dt_Nasc;
    private String Sexo;
    private String Naturalid;
    private String Pai;
    private String Mae;
    private String Conjuge;
    private String CNH;
    private String Dt_VenCNH;
    private String UF_CNH;
    private String Categoria;
    private String RG;
    private String OrgEmis;
    private String RgDtEmis;
    private String CPF;
    private String PIS;
    private String Reservista;
    private String ReservCat;
    private String CTPS_Nro;
    private String CTPS_Serie;
    private String CTPS_UF;
    private String CTPS_Emis;
    private String TitEleit;
    private String TitEZona;
    private String TitSecao;
    private String Ct_Banco;
    private String Ct_Agencia;
    private String Ct_Conta;
    private String Ct_CodOper;
    private String Obs;
    private BigDecimal Salario;
    private String Sindicato;
    private BigDecimal CHMes;
    private BigDecimal CHSeman;
    private BigDecimal He_Periodo;
    private String DepIR;
    private String DepSF;
    private String FGTSOpcao;
    private String FGTSBanco;
    private String FGTSAg;
    private String PgCtSin;
    private String AssMedic;
    private String DepAssMed;
    private String CestaBas;
    private String ValeRef;
    private String ConvFarma;
    private String SegVida;
    private String TipoAdm;
    private String DefFis;
    private String DefFisTipo;
    private String DefFisDesc;
    private String Nacionalid;
    private String AnoCheg;
    private String FolhaLivro;
    private String PgINSS;
    private String PgIR;
    private String SEFIPOcor;
    private String Conta_Ctb;
    private BigDecimal Altura;
    private BigDecimal Peso;
    private String Dt_Demis;
    private BigDecimal CodCidade;
    private BigDecimal CodNaturalid;
    private String ExpGESP;
    private String Vinculo;
    private String FormaPgto;
    private BigDecimal Jornada;
    private String SegDesemp;
    private String FPAdiant;
    private String CodAlimentacao;
    private String Chavebancaria;
    private BigDecimal CodPessoaWeb;
    private String InterfExt;
    private String Cod_ExameCNH;
    private String Dt_ExameCNH;
    private String CNPJ_LabExame;
    private String UF_ExameCNH;
    private String CRM_ExamCNH;
    private String TrabParcial;
    private String Teletrabalho;
    private String TrabIntermitente;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private BigDecimal CodPessoaReaproveitamento;
    private String Contrato;
    private String TIpo;
    private String Chave;

    private String ContratoRef;
    private String LocalRef;
    private String SecaoRef;
    
    private String CargoDescr;
    
    public Funcion() {
        CodPessoaReaproveitamento = BigDecimal.ZERO;
    }

    public Funcion(Funcion original) {
        Matr = original.getMatr();
        Nome = original.getNome();
        Nome_Guer = original.getNome_Guer();
        CodFil = original.getCodFil();
        Regional = original.getRegional();
        Secao = original.getSecao();
        CCusto = original.getCCusto();
        CodPonto = original.getCodPonto();
        Dt_Admis = original.getDt_Admis();
        Cargo = original.getCargo();
        CodCargo = original.getCodCargo();
        Apresen = original.getApresen();
        Situacao = original.getSituacao();
        Dt_Situac = original.getDt_Situac();
        Escala = original.getEscala();
        Horario = original.getHorario();
        GrpEscala = original.getGrpEscala();
        Funcao = original.getFuncao();
        Dt_FormIni = original.getDt_FormIni();
        Dt_FormFim = original.getDt_FormFim();
        LocalForm = original.getLocalForm();
        Certific = original.getCertific();
        Reg_PF = original.getReg_PF();
        Local_PF = original.getLocal_PF();
        Reg_PFUF = original.getReg_PFUF();
        Reg_PFDt = original.getReg_PFDt();
        CarNacVig = original.getCarNacVig();
        DtValCNV = original.getDtValCNV();
        Reg_MT = original.getReg_MT();
        Dt_Recicl = original.getDt_Recicl();
        Dt_VenCurs = original.getDt_VenCurs();
        Dt_ExameMe = original.getDt_ExameMe();
        Dt_Psico = original.getDt_Psico();
        ExtensaoTV = original.getExtensaoTV();
        ExtSegPes = original.getExtSegPes();
        ExtEscolta = original.getExtEscolta();
        GrupoSang = original.getGrupoSang();
        Instrucao = original.getInstrucao();
        Raca = original.getRaca();
        EstCivil = original.getEstCivil();
        Endereco = original.getEndereco();
        Numero = original.getNumero();
        Complemento = original.getComplemento();
        Bairro = original.getBairro();
        Cidade = original.getCidade();
        UF = original.getUF();
        CEP = original.getCEP();
        Fone1 = original.getFone1();
        Fone2 = original.getFone2();
        Email = original.getEmail();
        Dt_Nasc = original.getDt_Nasc();
        Sexo = original.getSexo();
        Naturalid = original.getNaturalid();
        Pai = original.getPai();
        Mae = original.getMae();
        Conjuge = original.getConjuge();
        CNH = original.getCNH();
        Dt_VenCNH = original.getDt_VenCNH();
        Categoria = original.getCategoria();
        RG = original.getRG();
        OrgEmis = original.getOrgEmis();
        RgDtEmis = original.getRgDtEmis();
        CPF = original.getCPF();
        PIS = original.getPIS();
        Reservista = original.getReservista();
        ReservCat = original.getReservCat();
        CTPS_Nro = original.getCTPS_Nro();
        CTPS_Serie = original.getCTPS_Serie();
        CTPS_UF = original.getCTPS_UF();
        CTPS_Emis = original.getCTPS_Emis();
        TitEleit = original.getTitEleit();
        TitEZona = original.getTitEZona();
        TitSecao = original.getTitSecao();
        Ct_Banco = original.getCt_Banco();
        Ct_Agencia = original.getCt_Agencia();
        Ct_Conta = original.getCt_Conta();
        Ct_CodOper = original.getCt_CodOper();
        Obs = original.getObs();
        Salario = original.getSalario();
        Sindicato = original.getSindicato();
        CHMes = original.getCHMes();
        CHSeman = original.getCHSeman();
        He_Periodo = original.getHe_Periodo();
        DepIR = original.getDepIR();
        DepSF = original.getDepSF();
        FGTSOpcao = original.getFGTSOpcao();
        FGTSBanco = original.getFGTSBanco();
        FGTSAg = original.getFGTSAg();
        PgCtSin = original.getPgCtSin();
        AssMedic = original.getAssMedic();
        DepAssMed = original.getDepAssMed();
        CestaBas = original.getCestaBas();
        ValeRef = original.getValeRef();
        ConvFarma = original.getConvFarma();
        SegVida = original.getSegVida();
        TipoAdm = original.getTipoAdm();
        DefFis = original.getDefFis();
        DefFisTipo = original.getDefFisTipo();
        DefFisDesc = original.getDefFisDesc();
        Nacionalid = original.getNacionalid();
        AnoCheg = original.getAnoCheg();
        FolhaLivro = original.getFolhaLivro();
        PgINSS = original.getPgINSS();
        PgIR = original.getPgIR();
        SEFIPOcor = original.getSEFIPOcor();
        Conta_Ctb = original.getConta_Ctb();
        Altura = original.getAltura();
        Peso = original.getPeso();
        Dt_Demis = original.getDt_Demis();
        CodCidade = original.getCodCidade();
        CodNaturalid = original.getCodNaturalid();
        ExpGESP = original.getExpGESP();
        Vinculo = original.getVinculo();
        FormaPgto = original.getFormaPgto();
        Jornada = original.getJornada();
        SegDesemp = original.getSegDesemp();
        Operador = original.getOperador();
        Dt_Alter = original.getDt_Alter();
        Hr_Alter = original.getHr_Alter();
        InterfExt = original.getInterfExt();
        CodPessoaWeb = original.getCodPessoaWeb();
        CodPessoaReaproveitamento = original.getCodPessoaReaproveitamento();
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public void setMatr(BigDecimal Matr) {
        try {
            this.Matr = Matr;
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getNome_Guer() {
        return Nome_Guer;
    }

    public void setNome_Guer(String Nome_Guer) {
        this.Nome_Guer = Nome_Guer;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public void setCodFil(BigDecimal CodFil) {
        try {
            this.CodFil = CodFil;
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public int getRegional() {
        return Regional;
    }

    public void setRegional(int Regional) {
        this.Regional = Regional;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public String getCodPonto() {
        return CodPonto;
    }

    public void setCodPonto(String CodPonto) {
        this.CodPonto = CodPonto;
    }

    public String getDt_Admis() {
        return Dt_Admis;
    }

    public void setDt_Admis(String Dt_Admis) {
        this.Dt_Admis = Dt_Admis;
    }

    public String getCargo() {
        return Cargo;
    }

    public void setCargo(String Cargo) {
        this.Cargo = Cargo;
    }

    public BigDecimal getCodCargo() {
        return CodCargo;
    }

    public void setCodCargo(String CodCargo) {
        try {
            this.CodCargo = new BigDecimal(CodCargo);
        } catch (Exception e) {
            this.CodCargo = new BigDecimal("0");
        }
    }

    public String getApresen() {
        return Apresen;
    }

    public void setApresen(String Apresen) {
        this.Apresen = Apresen;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getDt_Situac() {
        return Dt_Situac;
    }

    public void setDt_Situac(String Dt_Situac) {
        this.Dt_Situac = Dt_Situac;
    }

    public String getEscala() {
        return Escala;
    }

    public void setEscala(String Escala) {
        this.Escala = Escala;
    }

    public int getHorario() {
        return Horario;
    }

    public void setHorario(int Horario) {
        this.Horario = Horario;
    }

    public int getGrpEscala() {
        return GrpEscala;
    }

    public void setGrpEscala(int GrpEscala) {
        this.GrpEscala = GrpEscala;
    }

    public String getFuncao() {
        return Funcao;
    }

    public void setFuncao(String Funcao) {
        this.Funcao = Funcao;
    }

    public String getDt_FormIni() {
        return Dt_FormIni;
    }

    public void setDt_FormIni(String Dt_FormIni) {
        this.Dt_FormIni = Dt_FormIni;
    }

    public String getDt_FormFim() {
        return Dt_FormFim;
    }

    public void setDt_FormFim(String Dt_FormFim) {
        this.Dt_FormFim = Dt_FormFim;
    }

    public String getLocalForm() {
        return LocalForm;
    }

    public void setLocalForm(String LocalForm) {
        this.LocalForm = LocalForm;
    }

    public String getCertific() {
        return Certific;
    }

    public void setCertific(String Certific) {
        this.Certific = Certific;
    }

    public String getReg_PF() {
        return Reg_PF;
    }

    public void setReg_PF(String Reg_PF) {
        this.Reg_PF = Reg_PF;
    }

    public String getLocal_PF() {
        return Local_PF;
    }

    public void setLocal_PF(String Local_PF) {
        this.Local_PF = Local_PF;
    }

    public String getReg_PFUF() {
        return Reg_PFUF;
    }

    public void setReg_PFUF(String Reg_PFUF) {
        this.Reg_PFUF = Reg_PFUF;
    }

    public String getReg_PFDt() {
        return Reg_PFDt;
    }

    public void setReg_PFDt(String Reg_PFDt) {
        this.Reg_PFDt = Reg_PFDt;
    }

    public String getCarNacVig() {
        return CarNacVig;
    }

    public void setCarNacVig(String CarNacVig) {
        this.CarNacVig = CarNacVig;
    }

    public String getDtValCNV() {
        return DtValCNV;
    }

    public void setDtValCNV(String DtValCNV) {
        this.DtValCNV = DtValCNV;
    }

    public String getReg_MT() {
        return Reg_MT;
    }

    public void setReg_MT(String Reg_MT) {
        this.Reg_MT = Reg_MT;
    }

    public String getDt_Recicl() {
        return Dt_Recicl;
    }

    public void setDt_Recicl(String Dt_Recicl) {
        this.Dt_Recicl = Dt_Recicl;
    }

    public String getDt_VenCurs() {
        return Dt_VenCurs;
    }

    public void setDt_VenCurs(String Dt_VenCurs) {
        this.Dt_VenCurs = Dt_VenCurs;
    }

    public String getDt_ExameMe() {
        return Dt_ExameMe;
    }

    public void setDt_ExameMe(String Dt_ExameMe) {
        this.Dt_ExameMe = Dt_ExameMe;
    }

    public String getDt_Psico() {
        return Dt_Psico;
    }

    public void setDt_Psico(String Dt_Psico) {
        this.Dt_Psico = Dt_Psico;
    }

    public String getExtensaoTV() {
        return ExtensaoTV;
    }

    public void setExtensaoTV(String ExtensaoTV) {
        this.ExtensaoTV = ExtensaoTV;
    }

    public String getExtSegPes() {
        return ExtSegPes;
    }

    public void setExtSegPes(String ExtSegPes) {
        this.ExtSegPes = ExtSegPes;
    }

    public String getGrupoSang() {
        return GrupoSang;
    }

    public void setGrupoSang(String GrupoSang) {
        this.GrupoSang = GrupoSang;
    }

    public String getInstrucao() {
        return Instrucao;
    }

    public void setInstrucao(String Instrucao) {
        this.Instrucao = Instrucao;
    }

    public String getRaca() {
        return Raca;
    }

    public void setRaca(String Raca) {
        this.Raca = Raca;
    }

    public String getEstCivil() {
        return EstCivil;
    }

    public void setEstCivil(String EstCivil) {
        this.EstCivil = EstCivil;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getComplemento() {
        return Complemento;
    }

    public void setComplemento(String Complemento) {
        this.Complemento = Complemento;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getFone1() {
        return Fone1;
    }

    public void setFone1(String Fone1) {
        this.Fone1 = Fone1;
    }

    public String getFone2() {
        return Fone2;
    }

    public void setFone2(String Fone2) {
        this.Fone2 = Fone2;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getDt_Nasc() {
        return Dt_Nasc;
    }

    public void setDt_Nasc(String Dt_Nasc) {
        this.Dt_Nasc = Dt_Nasc;
    }

    public String getSexo() {
        return Sexo;
    }

    public void setSexo(String Sexo) {
        this.Sexo = Sexo;
    }

    public String getNaturalid() {
        return Naturalid;
    }

    public void setNaturalid(String Naturalid) {
        this.Naturalid = Naturalid;
    }

    public String getPai() {
        return Pai;
    }

    public void setPai(String Pai) {
        this.Pai = Pai;
    }

    public String getMae() {
        return Mae;
    }

    public void setMae(String Mae) {
        this.Mae = Mae;
    }

    public String getConjuge() {
        return Conjuge;
    }

    public void setConjuge(String Conjuge) {
        this.Conjuge = Conjuge;
    }

    public String getCNH() {
        return CNH;
    }

    public void setCNH(String CNH) {
        this.CNH = CNH;
    }

    public String getDt_VenCNH() {
        return Dt_VenCNH;
    }

    public void setDt_VenCNH(String Dt_VenCNH) {
        this.Dt_VenCNH = Dt_VenCNH;
    }

    public String getCategoria() {
        return Categoria;
    }

    public void setCategoria(String Categoria) {
        this.Categoria = Categoria;
    }

    public String getRG() {
        return RG;
    }

    public void setRG(String RG) {
        this.RG = RG;
    }

    public String getOrgEmis() {
        return OrgEmis;
    }

    public void setOrgEmis(String OrgEmis) {
        this.OrgEmis = OrgEmis;
    }

    public String getRgDtEmis() {
        return RgDtEmis;
    }

    public void setRgDtEmis(String RgDtEmis) {
        this.RgDtEmis = RgDtEmis;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getPIS() {
        return PIS;
    }

    public void setPIS(String PIS) {
        this.PIS = PIS;
    }

    public String getReservista() {
        return Reservista;
    }

    public void setReservista(String Reservista) {
        this.Reservista = Reservista;
    }

    public String getReservCat() {
        return ReservCat;
    }

    public void setReservCat(String ReservCat) {
        this.ReservCat = ReservCat;
    }

    public String getCTPS_Nro() {
        return CTPS_Nro;
    }

    public void setCTPS_Nro(String CTPS_Nro) {
        this.CTPS_Nro = CTPS_Nro;
    }

    public String getCTPS_Serie() {
        return CTPS_Serie;
    }

    public void setCTPS_Serie(String CTPS_Serie) {
        this.CTPS_Serie = CTPS_Serie;
    }

    public String getCTPS_UF() {
        return CTPS_UF;
    }

    public void setCTPS_UF(String CTPS_UF) {
        this.CTPS_UF = CTPS_UF;
    }

    public String getCTPS_Emis() {
        return CTPS_Emis;
    }

    public void setCTPS_Emis(String CTPS_Emis) {
        this.CTPS_Emis = CTPS_Emis;
    }

    public String getTitEleit() {
        return TitEleit;
    }

    public void setTitEleit(String TitEleit) {
        this.TitEleit = TitEleit;
    }

    public String getTitEZona() {
        return TitEZona;
    }

    public void setTitEZona(String TitEZona) {
        this.TitEZona = TitEZona;
    }

    public String getTitSecao() {
        return TitSecao;
    }

    public void setTitSecao(String TitSecao) {
        this.TitSecao = TitSecao;
    }

    public String getCt_Banco() {
        return Ct_Banco;
    }

    public void setCt_Banco(String Ct_Banco) {
        this.Ct_Banco = Ct_Banco;
    }

    public String getCt_Agencia() {
        return Ct_Agencia;
    }

    public void setCt_Agencia(String Ct_Agencia) {
        this.Ct_Agencia = Ct_Agencia;
    }

    public String getCt_Conta() {
        return Ct_Conta;
    }

    public void setCt_Conta(String Ct_Conta) {
        this.Ct_Conta = Ct_Conta;
    }

    public String getCt_CodOper() {
        return Ct_CodOper;
    }

    public void setCt_CodOper(String Ct_CodOper) {
        this.Ct_CodOper = Ct_CodOper;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public BigDecimal getSalario() {
        return Salario;
    }

    public void setSalario(String Salario) {
        try {
            this.Salario = new BigDecimal(Salario);
        } catch (Exception e) {
            this.Salario = new BigDecimal("0");
        }
    }

    public String getSindicato() {
        return Sindicato;
    }

    public void setSindicato(String Sindicato) {
        this.Sindicato = Sindicato;
    }

    public BigDecimal getCHMes() {
        return CHMes;
    }

    public void setCHMes(String CHMes) {
        try {
            this.CHMes = new BigDecimal(CHMes);
        } catch (Exception e) {
            this.CHMes = new BigDecimal("0");
        }
    }

    public BigDecimal getCHSeman() {
        return CHSeman;
    }

    public void setCHSeman(String CHSeman) {
        try {
            this.CHSeman = new BigDecimal(CHSeman);
        } catch (Exception e) {
            this.CHSeman = new BigDecimal("0");
        }
    }

    public BigDecimal getHe_Periodo() {
        return He_Periodo;
    }

    public void setHe_Periodo(String He_Periodo) {
        try {
            this.He_Periodo = new BigDecimal(He_Periodo);
        } catch (Exception e) {
            this.He_Periodo = new BigDecimal("0");
        }
    }

    public String getDepIR() {
        return DepIR;
    }

    public void setDepIR(String DepIR) {
        this.DepIR = DepIR;
    }

    public String getDepSF() {
        return DepSF;
    }

    public void setDepSF(String DepSF) {
        this.DepSF = DepSF;
    }

    public String getFGTSOpcao() {
        return FGTSOpcao;
    }

    public void setFGTSOpcao(String FGTSOpcao) {
        this.FGTSOpcao = FGTSOpcao;
    }

    public String getFGTSBanco() {
        return FGTSBanco;
    }

    public void setFGTSBanco(String FGTSBanco) {
        this.FGTSBanco = FGTSBanco;
    }

    public String getFGTSAg() {
        return FGTSAg;
    }

    public void setFGTSAg(String FGTSAg) {
        this.FGTSAg = FGTSAg;
    }

    public String getPgCtSin() {
        return PgCtSin;
    }

    public void setPgCtSin(String PgCtSin) {
        this.PgCtSin = PgCtSin;
    }

    public String getAssMedic() {
        return AssMedic;
    }

    public void setAssMedic(String AssMedic) {
        this.AssMedic = AssMedic;
    }

    public String getDepAssMed() {
        return DepAssMed;
    }

    public void setDepAssMed(String DepAssMed) {
        this.DepAssMed = DepAssMed;
    }

    public String getCestaBas() {
        return CestaBas;
    }

    public void setCestaBas(String CestaBas) {
        this.CestaBas = CestaBas;
    }

    public String getValeRef() {
        return ValeRef;
    }

    public void setValeRef(String ValeRef) {
        this.ValeRef = ValeRef;
    }

    public String getConvFarma() {
        return ConvFarma;
    }

    public void setConvFarma(String ConvFarma) {
        this.ConvFarma = ConvFarma;
    }

    public String getSegVida() {
        return SegVida;
    }

    public void setSegVida(String SegVida) {
        this.SegVida = SegVida;
    }

    public String getTipoAdm() {
        return TipoAdm;
    }

    public void setTipoAdm(String TipoAdm) {
        this.TipoAdm = TipoAdm;
    }

    public String getDefFis() {
        return DefFis;
    }

    public void setDefFis(String DefFis) {
        this.DefFis = DefFis;
    }

    public String getDefFisTipo() {
        return DefFisTipo;
    }

    public void setDefFisTipo(String DefFisTipo) {
        this.DefFisTipo = DefFisTipo;
    }

    public String getDefFisDesc() {
        return DefFisDesc;
    }

    public void setDefFisDesc(String DefFisDesc) {
        this.DefFisDesc = DefFisDesc;
    }

    public String getNacionalid() {
        return Nacionalid;
    }

    public void setNacionalid(String Nacionalid) {
        this.Nacionalid = Nacionalid;
    }

    public String getAnoCheg() {
        return AnoCheg;
    }

    public void setAnoCheg(String AnoCheg) {
        this.AnoCheg = AnoCheg;
    }

    public String getFolhaLivro() {
        return FolhaLivro;
    }

    public void setFolhaLivro(String FolhaLivro) {
        this.FolhaLivro = FolhaLivro;
    }

    public String getPgINSS() {
        return PgINSS;
    }

    public void setPgINSS(String PgINSS) {
        this.PgINSS = PgINSS;
    }

    public String getPgIR() {
        return PgIR;
    }

    public void setPgIR(String PgIR) {
        this.PgIR = PgIR;
    }

    public String getSEFIPOcor() {
        return SEFIPOcor;
    }

    public void setSEFIPOcor(String SEFIPOcor) {
        this.SEFIPOcor = SEFIPOcor;
    }

    public String getConta_Ctb() {
        return Conta_Ctb;
    }

    public void setConta_Ctb(String Conta_Ctb) {
        this.Conta_Ctb = Conta_Ctb;
    }

    public BigDecimal getAltura() {
        return Altura;
    }

    public void setAltura(String Altura) {
        try {
            this.Altura = new BigDecimal(Altura);
        } catch (Exception e) {
            this.Altura = new BigDecimal("0");
        }
    }

    public BigDecimal getPeso() {
        return Peso;
    }

    public void setPeso(String Peso) {
        try {
            this.Peso = new BigDecimal(Peso);
        } catch (Exception e) {
            this.Peso = new BigDecimal("0");
        }
    }

    public String getDt_Demis() {
        return Dt_Demis;
    }

    public void setDt_Demis(String Dt_Demis) {
        this.Dt_Demis = Dt_Demis;
    }

    public BigDecimal getCodCidade() {
        return CodCidade;
    }

    public void setCodCidade(String CodCidade) {
        try {
            this.CodCidade = new BigDecimal(CodCidade);
        } catch (Exception e) {
            this.CodCidade = new BigDecimal("0");
        }
    }

    public BigDecimal getCodNaturalid() {
        return CodNaturalid;
    }

    public void setCodNaturalid(String CodNaturalid) {
        try {
            this.CodNaturalid = new BigDecimal(CodNaturalid);
        } catch (Exception e) {
            this.CodNaturalid = new BigDecimal("0");
        }
    }

    public String getExpGESP() {
        return ExpGESP;
    }

    public void setExpGESP(String ExpGESP) {
        this.ExpGESP = ExpGESP;
    }

    public String getVinculo() {
        return Vinculo;
    }

    public void setVinculo(String Vinculo) {
        this.Vinculo = Vinculo;
    }

    public String getFormaPgto() {
        return FormaPgto;
    }

    public void setFormaPgto(String FormaPgto) {
        this.FormaPgto = FormaPgto;
    }

    public BigDecimal getJornada() {
        return Jornada;
    }

    public void setJornada(String Jornada) {
        try {
            this.Jornada = new BigDecimal(Jornada);
        } catch (Exception e) {
            this.Jornada = new BigDecimal("0");
        }
    }

    public String getSegDesemp() {
        return SegDesemp;
    }

    public void setSegDesemp(String SegDesemp) {
        this.SegDesemp = SegDesemp;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    /**
     * @return the ExtEscolta
     */
    public String getExtEscolta() {
        return ExtEscolta;
    }

    /**
     * @param ExtEscolta the ExtEscolta to set
     */
    public void setExtEscolta(String ExtEscolta) {
        this.ExtEscolta = ExtEscolta;
    }

    public String getInterfExt() {
        return InterfExt;
    }

    public void setInterfExt(String InterfExt) {
        this.InterfExt = InterfExt;
    }

    public BigDecimal getCodPessoaWeb() {
        return CodPessoaWeb;
    }

    public void setCodPessoaWeb(BigDecimal CodPessoaWeb) {
        this.CodPessoaWeb = CodPessoaWeb;
    }

    public BigDecimal getCodPessoaReaproveitamento() {
        return CodPessoaReaproveitamento;
    }

    public void setCodPessoaReaproveitamento(BigDecimal CodPessoaReaproveitamento) {
        this.CodPessoaReaproveitamento = CodPessoaReaproveitamento;
    }

    public String getDtEmissaoCNV() {
        return DtEmissaoCNV;
    }

    public void setDtEmissaoCNV(String DtEmissaoCNV) {
        this.DtEmissaoCNV = DtEmissaoCNV;
    }

    public String getCadastroAFIS() {
        return CadastroAFIS;
    }

    public void setCadastroAFIS(String CadastroAFIS) {
        this.CadastroAFIS = CadastroAFIS;
    }

    public String getExtGrdEventos() {
        return ExtGrdEventos;
    }

    public void setExtGrdEventos(String ExtGrdEventos) {
        this.ExtGrdEventos = ExtGrdEventos;
    }

    public String getExtArmasNLetais() {
        return ExtArmasNLetais;
    }

    public void setExtArmasNLetais(String ExtArmasNLetais) {
        this.ExtArmasNLetais = ExtArmasNLetais;
    }

    public String getUF_CNH() {
        return UF_CNH;
    }

    public void setUF_CNH(String UF_CNH) {
        this.UF_CNH = UF_CNH;
    }

    public String getFPAdiant() {
        return FPAdiant;
    }

    public void setFPAdiant(String FPAdiant) {
        this.FPAdiant = FPAdiant;
    }

    public String getCodAlimentacao() {
        return CodAlimentacao;
    }

    public void setCodAlimentacao(String CodAlimentacao) {
        this.CodAlimentacao = CodAlimentacao;
    }

    public String getChavebancaria() {
        return Chavebancaria;
    }

    public void setChavebancaria(String Chavebancaria) {
        this.Chavebancaria = Chavebancaria;
    }

    public String getCod_ExameCNH() {
        return Cod_ExameCNH;
    }

    public void setCod_ExameCNH(String Cod_ExameCNH) {
        this.Cod_ExameCNH = Cod_ExameCNH;
    }

    public String getDt_ExameCNH() {
        return Dt_ExameCNH;
    }

    public void setDt_ExameCNH(String Dt_ExameCNH) {
        this.Dt_ExameCNH = Dt_ExameCNH;
    }

    public String getCNPJ_LabExame() {
        return CNPJ_LabExame;
    }

    public void setCNPJ_LabExame(String CNPJ_LabExame) {
        this.CNPJ_LabExame = CNPJ_LabExame;
    }

    public String getUF_ExameCNH() {
        return UF_ExameCNH;
    }

    public void setUF_ExameCNH(String UF_ExameCNH) {
        this.UF_ExameCNH = UF_ExameCNH;
    }

    public String getCRM_ExamCNH() {
        return CRM_ExamCNH;
    }

    public void setCRM_ExamCNH(String CRM_ExamCNH) {
        this.CRM_ExamCNH = CRM_ExamCNH;
    }

    public String getTrabParcial() {
        return TrabParcial;
    }

    public void setTrabParcial(String TrabParcial) {
        this.TrabParcial = TrabParcial;
    }

    public String getTeletrabalho() {
        return Teletrabalho;
    }

    public void setTeletrabalho(String Teletrabalho) {
        this.Teletrabalho = Teletrabalho;
    }

    public String getTrabIntermitente() {
        return TrabIntermitente;
    }

    public void setTrabIntermitente(String TrabIntermitente) {
        this.TrabIntermitente = TrabIntermitente;
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getTIpo() {
        return TIpo;
    }

    public void setTIpo(String TIpo) {
        this.TIpo = TIpo;
    }

    public String getChave() {
        return Chave;
    }

    public void setChave(String Chave) {
        this.Chave = Chave;
    }

    public String getContratoRef() {
        return ContratoRef;
    }

    public void setContratoRef(String ContratoRef) {
        this.ContratoRef = ContratoRef;
    }

    public String getLocalRef() {
        return LocalRef;
    }

    public void setLocalRef(String LocalRef) {
        this.LocalRef = LocalRef;
    }

    public String getSecaoRef() {
        return SecaoRef;
    }

    public void setSecaoRef(String SecaoRef) {
        this.SecaoRef = SecaoRef;
    }    

    public String getCargoDescr() {
        return CargoDescr;
    }

    public void setCargoDescr(String CargoDescr) {
        this.CargoDescr = CargoDescr;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 23 * hash + Objects.hashCode(this.Matr);
        return hash;
    }

    @Override
    public String toString() {
        return "Funcion{" + "Matr=" + Matr + ", Nome=" + Nome + ", Nome_Guer=" + Nome_Guer + ", CodFil=" + CodFil + ", Regional=" + Regional + ", Secao=" + Secao + ", CCusto=" + CCusto + ", CodPonto=" + CodPonto + ", Dt_Admis=" + Dt_Admis + ", Cargo=" + Cargo + ", CodCargo=" + CodCargo + ", Apresen=" + Apresen + ", Situacao=" + Situacao + ", Dt_Situac=" + Dt_Situac + ", Escala=" + Escala + ", Horario=" + Horario + ", GrpEscala=" + GrpEscala + ", Funcao=" + Funcao + ", Dt_FormIni=" + Dt_FormIni + ", Dt_FormFim=" + Dt_FormFim + ", LocalForm=" + LocalForm + ", Certific=" + Certific + ", Reg_PF=" + Reg_PF + ", Local_PF=" + Local_PF + ", Reg_PFUF=" + Reg_PFUF + ", Reg_PFDt=" + Reg_PFDt + ", CarNacVig=" + CarNacVig + ", DtValCNV=" + DtValCNV + ", DtEmissaoCNV=" + DtEmissaoCNV + ", CadastroAFIS=" + CadastroAFIS + ", Reg_MT=" + Reg_MT + ", Dt_Recicl=" + Dt_Recicl + ", Dt_VenCurs=" + Dt_VenCurs + ", Dt_ExameMe=" + Dt_ExameMe + ", Dt_Psico=" + Dt_Psico + ", ExtensaoTV=" + ExtensaoTV + ", ExtSegPes=" + ExtSegPes + ", ExtEscolta=" + ExtEscolta + ", ExtGrdEventos=" + ExtGrdEventos + ", ExtArmasNLetais=" + ExtArmasNLetais + ", GrupoSang=" + GrupoSang + ", Instrucao=" + Instrucao + ", Raca=" + Raca + ", EstCivil=" + EstCivil + ", Endereco=" + Endereco + ", Numero=" + Numero + ", Complemento=" + Complemento + ", Bairro=" + Bairro + ", Cidade=" + Cidade + ", UF=" + UF + ", CEP=" + CEP + ", Fone1=" + Fone1 + ", Fone2=" + Fone2 + ", Email=" + Email + ", Dt_Nasc=" + Dt_Nasc + ", Sexo=" + Sexo + ", Naturalid=" + Naturalid + ", Pai=" + Pai + ", Mae=" + Mae + ", Conjuge=" + Conjuge + ", CNH=" + CNH + ", Dt_VenCNH=" + Dt_VenCNH + ", UF_CNH=" + UF_CNH + ", Categoria=" + Categoria + ", RG=" + RG + ", OrgEmis=" + OrgEmis + ", RgDtEmis=" + RgDtEmis + ", CPF=" + CPF + ", PIS=" + PIS + ", Reservista=" + Reservista + ", ReservCat=" + ReservCat + ", CTPS_Nro=" + CTPS_Nro + ", CTPS_Serie=" + CTPS_Serie + ", CTPS_UF=" + CTPS_UF + ", CTPS_Emis=" + CTPS_Emis + ", TitEleit=" + TitEleit + ", TitEZona=" + TitEZona + ", TitSecao=" + TitSecao + ", Ct_Banco=" + Ct_Banco + ", Ct_Agencia=" + Ct_Agencia + ", Ct_Conta=" + Ct_Conta + ", Ct_CodOper=" + Ct_CodOper + ", Obs=" + Obs + ", Salario=" + Salario + ", Sindicato=" + Sindicato + ", CHMes=" + CHMes + ", CHSeman=" + CHSeman + ", He_Periodo=" + He_Periodo + ", DepIR=" + DepIR + ", DepSF=" + DepSF + ", FGTSOpcao=" + FGTSOpcao + ", FGTSBanco=" + FGTSBanco + ", FGTSAg=" + FGTSAg + ", PgCtSin=" + PgCtSin + ", AssMedic=" + AssMedic + ", DepAssMed=" + DepAssMed + ", CestaBas=" + CestaBas + ", ValeRef=" + ValeRef + ", ConvFarma=" + ConvFarma + ", SegVida=" + SegVida + ", TipoAdm=" + TipoAdm + ", DefFis=" + DefFis + ", DefFisTipo=" + DefFisTipo + ", DefFisDesc=" + DefFisDesc + ", Nacionalid=" + Nacionalid + ", AnoCheg=" + AnoCheg + ", FolhaLivro=" + FolhaLivro + ", PgINSS=" + PgINSS + ", PgIR=" + PgIR + ", SEFIPOcor=" + SEFIPOcor + ", Conta_Ctb=" + Conta_Ctb + ", Altura=" + Altura + ", Peso=" + Peso + ", Dt_Demis=" + Dt_Demis + ", CodCidade=" + CodCidade + ", CodNaturalid=" + CodNaturalid + ", ExpGESP=" + ExpGESP + ", Vinculo=" + Vinculo + ", FormaPgto=" + FormaPgto + ", Jornada=" + Jornada + ", SegDesemp=" + SegDesemp + ", FPAdiant=" + FPAdiant + ", CodAlimentacao=" + CodAlimentacao + ", Chavebancaria=" + Chavebancaria + ", CodPessoaWeb=" + CodPessoaWeb + ", InterfExt=" + InterfExt + ", Cod_ExameCNH=" + Cod_ExameCNH + ", Dt_ExameCNH=" + Dt_ExameCNH + ", CNPJ_LabExame=" + CNPJ_LabExame + ", UF_ExameCNH=" + UF_ExameCNH + ", CRM_ExamCNH=" + CRM_ExamCNH + ", TrabParcial=" + TrabParcial + ", Teletrabalho=" + Teletrabalho + ", TrabIntermitente=" + TrabIntermitente + ", Operador=" + Operador + ", Dt_Alter=" + Dt_Alter + ", Hr_Alter=" + Hr_Alter + ", CodPessoaReaproveitamento=" + CodPessoaReaproveitamento + ", Contrato=" + Contrato + '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Funcion other = (Funcion) obj;
        if (!Objects.equals(this.Matr, other.Matr)) {
            return false;
        }
        return true;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2206 {

    private int sucesso;
    private String evtAltContratual_Id;
    private IdeEvento ideEvento;
    private IdeEmpregador ideEmpregador;
    private IdeVinculo ideVinculo;
    private AltContratual altContratual;

    public static class IdeEvento {

        private String ideEvento_indRetif;
        private String ideEvento_nrRecibo;
        private String ideEvento_tpAmb;
        private String ideEvento_procEmi;
        private String ideEvento_verProc;

        public String getIdeEvento_indRetif() {
            return ideEvento_indRetif;
        }

        public void setIdeEvento_indRetif(String ideEvento_indRetif) {
            this.ideEvento_indRetif = ideEvento_indRetif;
        }

        public String getIdeEvento_nrRecibo() {
            return ideEvento_nrRecibo;
        }

        public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
            this.ideEvento_nrRecibo = ideEvento_nrRecibo;
        }

        public String getIdeEvento_tpAmb() {
            return ideEvento_tpAmb;
        }

        public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
            this.ideEvento_tpAmb = ideEvento_tpAmb;
        }

        public String getIdeEvento_procEmi() {
            return ideEvento_procEmi;
        }

        public void setIdeEvento_procEmi(String ideEvento_procEmi) {
            this.ideEvento_procEmi = ideEvento_procEmi;
        }

        public String getIdeEvento_verProc() {
            return ideEvento_verProc;
        }

        public void setIdeEvento_verProc(String ideEvento_verProc) {
            this.ideEvento_verProc = ideEvento_verProc;
        }
    }

    public static class IdeEmpregador {

        private String ideEmpregador_tpInsc;
        private String ideEmpregador_nrInsc;

        public String getIdeEmpregador_tpInsc() {
            return ideEmpregador_tpInsc;
        }

        public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
            this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
        }

        public String getIdeEmpregador_nrInsc() {
            return ideEmpregador_nrInsc;
        }

        public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
            this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
        }
    }

    public static class IdeVinculo {

        private String ideVinculo_cpfTrab;
        private String ideVinculo_nisTrab;
        private String ideVinculo_matricula;

        public String getIdeVinculo_cpfTrab() {
            return ideVinculo_cpfTrab;
        }

        public void setIdeVinculo_cpfTrab(String ideVinculo_cpfTrab) {
            this.ideVinculo_cpfTrab = ideVinculo_cpfTrab;
        }

        public String getIdeVinculo_nisTrab() {
            return ideVinculo_nisTrab;
        }

        public void setIdeVinculo_nisTrab(String ideVinculo_nisTrab) {
            this.ideVinculo_nisTrab = ideVinculo_nisTrab;
        }

        public String getIdeVinculo_matricula() {
            return ideVinculo_matricula;
        }

        public void setIdeVinculo_matricula(String ideVinculo_matricula) {
            this.ideVinculo_matricula = ideVinculo_matricula;
        }
    }

    public static class AltContratual {

        private String altContratual_dtAlteracao;
        private String altContratual_dtEf;
        private String altContratual_dscAlt;
        private Vinculo altContratual_vinculo;
        private InfoRegimeTrab altContratual_infoRegimeTrab;
        private InfoContrato altContratual_infoContrato;

        public static class Vinculo {

            private String vinculo_tpRegPrev;

            public String getVinculo_tpRegPrev() {
                return vinculo_tpRegPrev;
            }

            public void setVinculo_tpRegPrev(String vinculo_tpRegPrev) {
                this.vinculo_tpRegPrev = vinculo_tpRegPrev;
            }
        }

        public static class InfoRegimeTrab {

            private InfoCeletista infoRegimeTrab_infoCeletista;
            private InfoEstatutario infoRegimeTrab_infoEstatuario;

            public static class InfoCeletista {

                private String infoCeletista_tpRegJor;
                private String infoCeletista_natAtividade;
                private String infoCeletista_dtBase;
                private String infoCeletista_cnpjSindCategProf;
                private TrabTemp infoCeletista_trabTempo;
                private Aprend infoCeletista_aprend;

                public static class TrabTemp {

                    private String trabTemp_justProrr;

                    public String getTrabTemp_justProrr() {
                        return trabTemp_justProrr;
                    }

                    public void setTrabTemp_justProrr(String trabTemp_justProrr) {
                        this.trabTemp_justProrr = trabTemp_justProrr;
                    }
                }

                public static class Aprend {

                    private String aprend_tpInsc;
                    private String aprend_nrInsc;

                    public String getAprend_tpInsc() {
                        return aprend_tpInsc;
                    }

                    public void setAprend_tpInsc(String aprend_tpInsc) {
                        this.aprend_tpInsc = aprend_tpInsc;
                    }

                    public String getAprend_nrInsc() {
                        return aprend_nrInsc;
                    }

                    public void setAprend_nrInsc(String aprend_nrInsc) {
                        this.aprend_nrInsc = aprend_nrInsc;
                    }
                }

                public String getInfoCeletista_tpRegJor() {
                    return infoCeletista_tpRegJor;
                }

                public void setInfoCeletista_tpRegJor(String infoCeletista_tpRegJor) {
                    this.infoCeletista_tpRegJor = infoCeletista_tpRegJor;
                }

                public String getInfoCeletista_natAtividade() {
                    return infoCeletista_natAtividade;
                }

                public void setInfoCeletista_natAtividade(String infoCeletista_natAtividade) {
                    this.infoCeletista_natAtividade = infoCeletista_natAtividade;
                }

                public String getInfoCeletista_dtBase() {
                    return infoCeletista_dtBase;
                }

                public void setInfoCeletista_dtBase(String infoCeletista_dtBase) {
                    this.infoCeletista_dtBase = infoCeletista_dtBase;
                }

                public String getInfoCeletista_cnpjSindCategProf() {
                    return infoCeletista_cnpjSindCategProf;
                }

                public void setInfoCeletista_cnpjSindCategProf(String infoCeletista_cnpjSindCategProf) {
                    this.infoCeletista_cnpjSindCategProf = infoCeletista_cnpjSindCategProf;
                }

                public TrabTemp getInfoCeletista_trabTempo() {
                    return infoCeletista_trabTempo == null ? new TrabTemp() : infoCeletista_trabTempo;
                }

                public void setInfoCeletista_trabTempo(TrabTemp infoCeletista_trabTempo) {
                    this.infoCeletista_trabTempo = infoCeletista_trabTempo;
                }

                public Aprend getInfoCeletista_aprend() {
                    return infoCeletista_aprend == null ? new Aprend() : infoCeletista_aprend;
                }

                public void setInfoCeletista_aprend(Aprend infoCeletista_aprend) {
                    this.infoCeletista_aprend = infoCeletista_aprend;
                }
            }

            public static class InfoEstatutario {

                private String infoEstatutario_tpPlanRP;

                public String getInfoEstatutario_tpPlanRP() {
                    return infoEstatutario_tpPlanRP;
                }

                public void setInfoEstatutario_tpPlanRP(String infoEstatutario_tpPlanRP) {
                    this.infoEstatutario_tpPlanRP = infoEstatutario_tpPlanRP;
                }
            }

            public InfoCeletista getInfoRegimeTrab_infoCeletista() {
                return infoRegimeTrab_infoCeletista == null ? new InfoCeletista() : infoRegimeTrab_infoCeletista;
            }

            public void setInfoRegimeTrab_infoCeletista(InfoCeletista infoRegimeTrab_infoCeletista) {
                this.infoRegimeTrab_infoCeletista = infoRegimeTrab_infoCeletista;
            }

            public InfoEstatutario getInfoRegimeTrab_infoEstatuario() {
                return infoRegimeTrab_infoEstatuario == null ? new InfoEstatutario() : infoRegimeTrab_infoEstatuario;
            }

            public void setInfoRegimeTrab_infoEstatuario(InfoEstatutario infoRegimeTrab_infoEstatuario) {
                this.infoRegimeTrab_infoEstatuario = infoRegimeTrab_infoEstatuario;
            }
        }

        public static class InfoContrato {

            private String infoContrato_nmCargo;
            private String infoContrato_codFuncao;
            private String infoContrato_codCateg;
            private String infoContrato_codCarreira;
            private String infoContrato_dtIngrCarr;
            private String CBO;
            private Remuneracao infoContrato_remuneracao;
            private Duracao infoContrato_duracao;
            private LocalTrabalho infoContrato_localTrabalho;
            private HorContratual infoContrato_horContratual;
            private FiliacaoSindical infoContrato_filiacaoSindical;
            private AlvaraJudicial infoContrato_alvaraJudicial;
            private List<Observacoes> infoContrato_observacoes;
            private ServPubl infoContrato_servPubl;

            public static class Remuneracao {

                private String remuneracao_vrSalFx;
                private String remuneracao_undSalFixo;
                private String remuneracao_dscSalVar;

                public String getRemuneracao_vrSalFx() {
                    return remuneracao_vrSalFx;
                }

                public void setRemuneracao_vrSalFx(String remuneracao_vrSalFx) {
                    this.remuneracao_vrSalFx = remuneracao_vrSalFx;
                }

                public String getRemuneracao_undSalFixo() {
                    return remuneracao_undSalFixo;
                }

                public void setRemuneracao_undSalFixo(String remuneracao_undSalFixo) {
                    this.remuneracao_undSalFixo = remuneracao_undSalFixo;
                }

                public String getRemuneracao_dscSalVar() {
                    return remuneracao_dscSalVar;
                }

                public void setRemuneracao_dscSalVar(String remuneracao_dscSalVar) {
                    this.remuneracao_dscSalVar = remuneracao_dscSalVar;
                }
            }

            public static class Duracao {

                private String duracao_tpContr;
                private String duracao_dtTerm;

                public String getDuracao_tpContr() {
                    return duracao_tpContr;
                }

                public void setDuracao_tpContr(String duracao_tpContr) {
                    this.duracao_tpContr = duracao_tpContr;
                }

                public String getDuracao_dtTerm() {
                    return duracao_dtTerm;
                }

                public void setDuracao_dtTerm(String duracao_dtTerm) {
                    this.duracao_dtTerm = duracao_dtTerm;
                }
            }

            public static class LocalTrabalho {

                private LocalTrabGeral localTrabalho_localTrabGeral;
                private LocalTrabDom localTrabalho_localTrabDom;

                public static class LocalTrabGeral {

                    private String localTrabGeral_tpInsc;
                    private String localTrabGeral_nrInsc;
                    private String localTrabGeral_descComp;

                    public String getLocalTrabGeral_tpInsc() {
                        return localTrabGeral_tpInsc;
                    }

                    public void setLocalTrabGeral_tpInsc(String localTrabGeral_tpInsc) {
                        this.localTrabGeral_tpInsc = localTrabGeral_tpInsc;
                    }

                    public String getLocalTrabGeral_nrInsc() {
                        return localTrabGeral_nrInsc;
                    }

                    public void setLocalTrabGeral_nrInsc(String localTrabGeral_nrInsc) {
                        this.localTrabGeral_nrInsc = localTrabGeral_nrInsc;
                    }

                    public String getLocalTrabGeral_descComp() {
                        return localTrabGeral_descComp;
                    }

                    public void setLocalTrabGeral_descComp(String localTrabGeral_descComp) {
                        this.localTrabGeral_descComp = localTrabGeral_descComp;
                    }
                }

                public static class LocalTrabDom {

                    private String localTrabDom_tpLograd;
                    private String localTrabDom_dscLograd;
                    private String localTrabDom_nrLograd;
                    private String localTrabDom_complemento;
                    private String localTrabDom_bairro;
                    private String localTrabDom_cep;
                    private String localTrabDom_codMunic;
                    private String localTrabDom_uf;

                    public String getLocalTrabDom_tpLograd() {
                        return localTrabDom_tpLograd;
                    }

                    public void setLocalTrabDom_tpLograd(String localTrabDom_tpLograd) {
                        this.localTrabDom_tpLograd = localTrabDom_tpLograd;
                    }

                    public String getLocalTrabDom_dscLograd() {
                        return localTrabDom_dscLograd;
                    }

                    public void setLocalTrabDom_dscLograd(String localTrabDom_dscLograd) {
                        this.localTrabDom_dscLograd = localTrabDom_dscLograd;
                    }

                    public String getLocalTrabDom_nrLograd() {
                        return localTrabDom_nrLograd;
                    }

                    public void setLocalTrabDom_nrLograd(String localTrabDom_nrLograd) {
                        this.localTrabDom_nrLograd = localTrabDom_nrLograd;
                    }

                    public String getLocalTrabDom_complemento() {
                        return localTrabDom_complemento;
                    }

                    public void setLocalTrabDom_complemento(String localTrabDom_complemento) {
                        this.localTrabDom_complemento = localTrabDom_complemento;
                    }

                    public String getLocalTrabDom_bairro() {
                        return localTrabDom_bairro;
                    }

                    public void setLocalTrabDom_bairro(String localTrabDom_bairro) {
                        this.localTrabDom_bairro = localTrabDom_bairro;
                    }

                    public String getLocalTrabDom_cep() {
                        return localTrabDom_cep;
                    }

                    public void setLocalTrabDom_cep(String localTrabDom_cep) {
                        this.localTrabDom_cep = localTrabDom_cep;
                    }

                    public String getLocalTrabDom_codMunic() {
                        return localTrabDom_codMunic;
                    }

                    public void setLocalTrabDom_codMunic(String localTrabDom_codMunic) {
                        this.localTrabDom_codMunic = localTrabDom_codMunic;
                    }

                    public String getLocalTrabDom_uf() {
                        return localTrabDom_uf;
                    }

                    public void setLocalTrabDom_uf(String localTrabDom_uf) {
                        this.localTrabDom_uf = localTrabDom_uf;
                    }
                }

                public LocalTrabGeral getLocalTrabalho_localTrabGeral() {
                    return localTrabalho_localTrabGeral == null ? new LocalTrabGeral() : localTrabalho_localTrabGeral;
                }

                public void setLocalTrabalho_localTrabGeral(LocalTrabGeral localTrabalho_localTrabGeral) {
                    this.localTrabalho_localTrabGeral = localTrabalho_localTrabGeral;
                }

                public LocalTrabDom getLocalTrabalho_localTrabDom() {
                    return localTrabalho_localTrabDom == null ? new LocalTrabDom() : localTrabalho_localTrabDom;
                }

                public void setLocalTrabalho_localTrabDom(LocalTrabDom localTrabalho_localTrabDom) {
                    this.localTrabalho_localTrabDom = localTrabalho_localTrabDom;
                }
            }

            public static class HorContratual {

                private String horContratual_qtdHrsSem;
                private String horContratual_tpJornada;
                private String horContratual_dscTpJorn;
                private String horContratual_dscJorn;
                private String horContratual_tmpParc;
                private List<Horario> horContratual_horario;

                public static class Horario {

                    private String horario_dia;
                    private String horario_codHorContrat;

                    public String getHorario_dia() {
                        return horario_dia;
                    }

                    public void setHorario_dia(String horario_dia) {
                        this.horario_dia = horario_dia;
                    }

                    public String getHorario_codHorContrat() {
                        return horario_codHorContrat;
                    }

                    public void setHorario_codHorContrat(String horario_codHorContrat) {
                        this.horario_codHorContrat = horario_codHorContrat;
                    }
                }

                public String getHorContratual_dscJorn() {
                    return horContratual_dscJorn;
                }

                public void setHorContratual_dscJorn(String horContratual_dscJorn) {
                    this.horContratual_dscJorn = horContratual_dscJorn;
                }                
                
                public String getHorContratual_qtdHrsSem() {
                    return horContratual_qtdHrsSem;
                }

                public void setHorContratual_qtdHrsSem(String horContratual_qtdHrsSem) {
                    this.horContratual_qtdHrsSem = horContratual_qtdHrsSem;
                }

                public String getHorContratual_tpJornada() {
                    return horContratual_tpJornada;
                }

                public void setHorContratual_tpJornada(String horContratual_tpJornada) {
                    this.horContratual_tpJornada = horContratual_tpJornada;
                }

                public String getHorContratual_dscTpJorn() {
                    return horContratual_dscTpJorn;
                }

                public void setHorContratual_dscTpJorn(String horContratual_dscTpJorn) {
                    this.horContratual_dscTpJorn = horContratual_dscTpJorn;
                }

                public String getHorContratual_tmpParc() {
                    return horContratual_tmpParc;
                }

                public void setHorContratual_tmpParc(String horContratual_tmpParc) {
                    this.horContratual_tmpParc = horContratual_tmpParc;
                }

                public List<Horario> getHorContratual_horario() {
                    return horContratual_horario == null ? new ArrayList<>() : horContratual_horario;
                }

                public void setHorContratual_horario(List<Horario> horContratual_horario) {
                    this.horContratual_horario = horContratual_horario;
                }
            }

            public static class FiliacaoSindical {

                private String filiacaoSindical_cnpjSindTrab;

                public String getFiliacaoSindical_cnpjSindTrab() {
                    return filiacaoSindical_cnpjSindTrab;
                }

                public void setFiliacaoSindical_cnpjSindTrab(String filiacaoSindical_cnpjSindTrab) {
                    this.filiacaoSindical_cnpjSindTrab = filiacaoSindical_cnpjSindTrab;
                }
            }

            public static class AlvaraJudicial {

                private String alvaraJudicial_nrProcJud;

                public String getAlvaraJudicial_nrProcJud() {
                    return alvaraJudicial_nrProcJud;
                }

                public void setAlvaraJudicial_nrProcJud(String alvaraJudicial_nrProcJud) {
                    this.alvaraJudicial_nrProcJud = alvaraJudicial_nrProcJud;
                }
            }

            public static class Observacoes {

                private String observacoes_observacao;

                public String getObservacoes_observacao() {
                    return observacoes_observacao;
                }

                public void setObservacoes_observacao(String observacoes_observacao) {
                    this.observacoes_observacao = observacoes_observacao;
                }
            }

            public static class ServPubl {

                private String servPubl_mtvAlter;

                public String getServPubl_mtvAlter() {
                    return servPubl_mtvAlter;
                }

                public void setServPubl_mtvAlter(String servPubl_mtvAlter) {
                    this.servPubl_mtvAlter = servPubl_mtvAlter;
                }
            }

            public String getInfoContrato_nmCargo() {
                return infoContrato_nmCargo;
            }

            public void setInfoContrato_nmCargo(String infoContrato_nmCargo) {
                this.infoContrato_nmCargo = infoContrato_nmCargo;
            }

            public String getInfoContrato_CBO() {
                return CBO;
            }

            public void setInfoContrato_CBO(String CBO) {
                this.CBO= CBO;
            }            
            
            public String getInfoContrato_codFuncao() {
                return infoContrato_codFuncao;
            }

            public void setInfoContrato_codFuncao(String infoContrato_codFuncao) {
                this.infoContrato_codFuncao = infoContrato_codFuncao;
            }

            public String getInfoContrato_codCateg() {
                return infoContrato_codCateg;
            }

            public void setInfoContrato_codCateg(String infoContrato_codCateg) {
                this.infoContrato_codCateg = infoContrato_codCateg;
            }

            public String getInfoContrato_codCarreira() {
                return infoContrato_codCarreira;
            }

            public void setInfoContrato_codCarreira(String infoContrato_codCarreira) {
                this.infoContrato_codCarreira = infoContrato_codCarreira;
            }

            public String getInfoContrato_dtIngrCarr() {
                return infoContrato_dtIngrCarr;
            }

            public void setInfoContrato_dtIngrCarr(String infoContrato_dtIngrCarr) {
                this.infoContrato_dtIngrCarr = infoContrato_dtIngrCarr;
            }

            public Remuneracao getInfoContrato_remuneracao() {
                return infoContrato_remuneracao == null ? new Remuneracao() : infoContrato_remuneracao;
            }

            public void setInfoContrato_remuneracao(Remuneracao infoContrato_remuneracao) {
                this.infoContrato_remuneracao = infoContrato_remuneracao;
            }

            public Duracao getInfoContrato_duracao() {
                return infoContrato_duracao == null ? new Duracao() : infoContrato_duracao;
            }

            public void setInfoContrato_duracao(Duracao infoContrato_duracao) {
                this.infoContrato_duracao = infoContrato_duracao;
            }

            public LocalTrabalho getInfoContrato_localTrabalho() {
                return infoContrato_localTrabalho == null ? new LocalTrabalho() : infoContrato_localTrabalho;
            }

            public void setInfoContrato_localTrabalho(LocalTrabalho infoContrato_localTrabalho) {
                this.infoContrato_localTrabalho = infoContrato_localTrabalho;
            }

            public HorContratual getInfoContrato_horContratual() {
                return infoContrato_horContratual == null ? new HorContratual() : infoContrato_horContratual;
            }

            public void setInfoContrato_horContratual(HorContratual infoContrato_horContratual) {
                this.infoContrato_horContratual = infoContrato_horContratual;
            }

            public FiliacaoSindical getInfoContrato_filiacaoSindical() {
                return infoContrato_filiacaoSindical == null ? new FiliacaoSindical() : infoContrato_filiacaoSindical;
            }

            public void setInfoContrato_filiacaoSindical(FiliacaoSindical infoContrato_filiacaoSindical) {
                this.infoContrato_filiacaoSindical = infoContrato_filiacaoSindical;
            }

            public AlvaraJudicial getInfoContrato_alvaraJudicial() {
                return infoContrato_alvaraJudicial == null ? new AlvaraJudicial() : infoContrato_alvaraJudicial;
            }

            public void setInfoContrato_alvaraJudicial(AlvaraJudicial infoContrato_alvaraJudicial) {
                this.infoContrato_alvaraJudicial = infoContrato_alvaraJudicial;
            }

            public List<Observacoes> getInfoContrato_observacoes() {
                return infoContrato_observacoes == null ? new ArrayList<>() : infoContrato_observacoes;
            }

            public void setInfoContrato_observacoes(List<Observacoes> infoContrato_observacoes) {
                this.infoContrato_observacoes = infoContrato_observacoes;
            }

            public ServPubl getInfoContrato_servPubl() {
                return infoContrato_servPubl == null ? new ServPubl() : infoContrato_servPubl;
            }

            public void setInfoContrato_servPubl(ServPubl infoContrato_servPubl) {
                this.infoContrato_servPubl = infoContrato_servPubl;
            }
        }

        public String getAltContratual_dtAlteracao() {
            return altContratual_dtAlteracao;
        }

        public void setAltContratual_dtAlteracao(String altContratual_dtAlteracao) {
            this.altContratual_dtAlteracao = altContratual_dtAlteracao;
        }

        public String getAltContratual_dtEf() {
            return altContratual_dtEf;
        }

        public void setAltContratual_dtEf(String altContratual_dtEf) {
            this.altContratual_dtEf = altContratual_dtEf;
        }

        public String getAltContratual_dscAlt() {
            return altContratual_dscAlt;
        }

        public void setAltContratual_dscAlt(String altContratual_dscAlt) {
            this.altContratual_dscAlt = altContratual_dscAlt;
        }

        public Vinculo getAltContratual_vinculo() {
            return altContratual_vinculo == null ? new Vinculo() : altContratual_vinculo;
        }

        public void setAltContratual_vinculo(Vinculo altContratual_vinculo) {
            this.altContratual_vinculo = altContratual_vinculo;
        }

        public InfoRegimeTrab getAltContratual_infoRegimeTrab() {
            return altContratual_infoRegimeTrab == null ? new InfoRegimeTrab() : altContratual_infoRegimeTrab;
        }

        public void setAltContratual_infoRegimeTrab(InfoRegimeTrab altContratual_infoRegimeTrab) {
            this.altContratual_infoRegimeTrab = altContratual_infoRegimeTrab;
        }

        public InfoContrato getAltContratual_infoContrato() {
            return altContratual_infoContrato == null ? new InfoContrato() : altContratual_infoContrato;
        }

        public void setAltContratual_infoContrato(InfoContrato altContratual_infoContrato) {
            this.altContratual_infoContrato = altContratual_infoContrato;
        }
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtAltContratual_Id() {
        return evtAltContratual_Id;
    }

    public void setEvtAltContratual_Id(String evtAltContratual_Id) {
        this.evtAltContratual_Id = evtAltContratual_Id;
    }

    public IdeEvento getIdeEvento() {
        return ideEvento == null ? new IdeEvento() : ideEvento;
    }

    public void setIdeEvento(IdeEvento ideEvento) {
        this.ideEvento = ideEvento;
    }

    public IdeEmpregador getIdeEmpregador() {
        return ideEmpregador == null ? new IdeEmpregador() : ideEmpregador;
    }

    public void setIdeEmpregador(IdeEmpregador ideEmpregador) {
        this.ideEmpregador = ideEmpregador;
    }

    public IdeVinculo getIdeVinculo() {
        return ideVinculo == null ? new IdeVinculo() : ideVinculo;
    }

    public void setIdeVinculo(IdeVinculo ideVinculo) {
        this.ideVinculo = ideVinculo;
    }

    public AltContratual getAltContratual() {
        return altContratual == null ? new AltContratual() : altContratual;
    }

    public void setAltContratual(AltContratual altContratual) {
        this.altContratual = altContratual;
    }
}

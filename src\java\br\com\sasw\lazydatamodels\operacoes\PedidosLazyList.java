/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels.operacoes;

import Controller.Pedidos.PedidosSatMobWeb;
import Dados.Persistencia;
import SasBeans.CxForte;
import SasBeans.Pedido;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PedidosLazyList extends LazyDataModel<Pedido> {

    private static final long serialVersionUID = 1L;
    private List<Pedido> pedidos;
    private final PedidosSatMobWeb pedidosSatMobWeb;
    private final Persistencia persistencia;
    private Map filters;
    private final List<CxForte> cxForteList;

    public PedidosLazyList(Persistencia pst, Map filters, List<CxForte> cxForteList) {
        this.persistencia = pst;
        this.pedidosSatMobWeb = new PedidosSatMobWeb();
        this.filters = filters;
        this.cxForteList = cxForteList;
    }

    @Override
    public List<Pedido> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.pedidos = this.pedidosSatMobWeb.listagemPaginada(first, pageSize, this.filters, this.cxForteList, this.persistencia);

            //set the  total of players
            setRowCount(this.pedidosSatMobWeb.totalPedidos(this.filters, this.persistencia));
            //set the page size
            setPageSize(pageSize);

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.pedidos;
    }

    @Override
    public Object getRowKey(Pedido pedido) {
        try {
            return pedido.getCodFil().toBigInteger() + ";" + pedido.getNumero().toBigInteger();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public Pedido getRowData(String pedido) {
        try {
            for (Pedido p : this.pedidos) {
                if (p.getCodFil().toBigInteger().toString().equals(pedido.split(";")[0])
                        && p.getNumero().toBigInteger().toString().equals(pedido.split(";")[1])) {
                    return p;
                }
            }
            return null;
        } catch (Exception e) {
            System.out.println("Pedido: " + pedido + "\r\nERRO: " + e.getMessage());
            return null;
        }
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

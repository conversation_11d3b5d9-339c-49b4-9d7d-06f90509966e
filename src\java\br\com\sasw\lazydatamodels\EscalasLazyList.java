/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Escala.EscalaSatMobWeb;
import Dados.Persistencia;
import SasBeansCompostas.CarregaEscala;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class EscalasLazyList extends LazyDataModel<CarregaEscala> {

    private static final long serialVersionUID = 1L;
    private List<CarregaEscala> escalas;
    private final EscalaSatMobWeb escalamobweb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public EscalasLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.escalamobweb = new EscalaSatMobWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<CarregaEscala> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.escalas = this.escalamobweb.ListagemPaginada(first, pageSize, filters, this.codPessoa, this.persistencia);

            // set the total of players
            setRowCount(this.escalamobweb.Contagem(filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.escalas;
    }

    @Override
    public Object getRowKey(CarregaEscala escala) {
        return escala.getEscala().getRota();
    }

    @Override
    public CarregaEscala getRowData(String rota) {
        for (CarregaEscala escala : this.escalas) {
            if (rota.equals(escala.getEscala().getRota())) {
                return escala;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

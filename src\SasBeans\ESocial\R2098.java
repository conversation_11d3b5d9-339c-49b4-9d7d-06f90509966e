/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class R2098 {

    private String evtReabreEvPer_Id;
    private int sucesso;

    private String ideEvento_perApur;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;

    private String ideContri_tpInsc;
    private String ideContri_nrInsc;

    public String getEvtReabreEvPer_Id() {
        return evtReabreEvPer_Id;
    }

    public void setEvtReabreEvPer_Id(String evtReabreEvPer_Id) {
        this.evtReabreEvPer_Id = evtReabreEvPer_Id;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_perApur() {
        return ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeContri_tpInsc() {
        return ideContri_tpInsc;
    }

    public void setIdeContri_tpInsc(String ideContri_tpInsc) {
        this.ideContri_tpInsc = ideContri_tpInsc;
    }

    public String getIdeContri_nrInsc() {
        return ideContri_nrInsc;
    }

    public void setIdeContri_nrInsc(String ideContri_nrInsc) {
        this.ideContri_nrInsc = ideContri_nrInsc;
    }

}

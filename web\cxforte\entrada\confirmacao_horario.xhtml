<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <p:dialog header="#{localemsgs.Parada}"
              widgetVar="dlgParadaHorario"
              minHeight="40"
              resizable="false"
              modal="true"
              >
        <h:form id="confirmarParadaHorario">
            <f:facet name="header">
                <h:outputText value="#{localemsgs.Confirmacao}" style="color:black" />
            </f:facet>

            <p:panel
                id="panel"
                class="modalSolicitacao"
                >

                <div class="row">
                    <div class="col-xs-6">
                        <strong>#{localemsgs.Parada}</strong>
                    </div>
                    <div class="col-xs-6">
                        <span>#{cxForteEntrada.listaHorasRtPercIndex}/#{cxForteEntrada.listaHorasRtPerc.size()}. #{localemsgs.Horario}: #{cxForteEntrada.listaHorasRtPercAtual.hora1}</span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-12">
                        <strong>#{localemsgs.DesejaUtilizarParada}</strong>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-12">
                        <p:commandButton
                            action="#{cxForteEntrada.utilizarHoraParada()}"
                            update="msgs @form"
                            styleClass="botao"
                            value="#{localemsgs.Sim}"
                            />
                        <p:commandButton
                            action="#{cxForteEntrada.naoUtilizarHoraParada()}"
                            update="msgs @form"
                            styleClass="botao"
                            value="#{localemsgs.Nao}"
                            />
                    </div>
                </div>
            </p:panel>
        </h:form>
    </p:dialog>

</ui:composition>
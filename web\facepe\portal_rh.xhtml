<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiMob}" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <link href="../assets/css/animate.css" rel="stylesheet" type="text/css"/>
            <link type="text/css" href="../assets/css/webPonto.css" rel="stylesheet" />
            <meta name="theme-color" content="#002172" />
            <meta name="msapplication-navbutton-color" content="#002172" />
            <meta name="apple-mobile-web-app-status-bar-style" content="#002172" />
            <style>
                html, body, form{
                    overflow: hidden !important;
                }
            </style>
        </h:head> 
        <h:body id="bodyWebPonto" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{login.carregarDadosWebPonto}" />
                <f:viewAction action="#{mobEW.PersistenciaWebPonto(login.pp)}" />
            </f:metadata>

            <p:growl id="msgs" />

            <h:form id="frmCabecalho" style="height: 60px; width: 100%; padding: 0px; margin: 0px; background-color: #FFF;">
                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 60px; width: 100%; padding: 0px 0px 0px 65px; margin: 0px; background-color: #002172; text-align: left;">
                    <img src="../assets/logos/SatMobEw.png" height="42" style="border-radius: 50%; left: 10px; top: 7px; position: absolute" />
                    <i class="fa fa-bars" ref="BarsMenu" style="position: absolute; font-size: 16pt; color: #FFF; right: 27px; top: 1px; cursor: pointer; padding: 14px 8px 14px 8px !important; width: 50px; text-align: center"></i>
                    <label class="DescricaoPonto" style="max-width: calc(100% - 100px) !important; font-size: 13pt !important; height: 21px !important; padding: 0px 5px 0px 0px !important; margin-top: 5px !important;">#{login.webPontoNomeGuer}</label>
                    <label class="DescricaoPonto">#{login.webPontoMatr} - #{login.webPontoFuncao}</label>
                    <label class="DescricaoPonto">#{login.webPontoEscala}</label>
                </div>
            </h:form>

            <h:form id="frmPortal" style="height: calc(100% - 60px); width: 100%; padding: 0px; margin: 0px; background-color: #EEE;">
                <iframe id="ifrPortal" style="height: 100%; width: 100%; border: none; margin: 0px; padding: 0px"></iframe>


                <script type="text/javascript">
                    // <![CDATA[

                    $(document).ready(function () {
                        $('#ifrPortal').attr('src','https://mobile.sasw.com.br/SatMobWeb/index.xhtml?Login=Portal&Email=${login.pp.empresa}@${login.webPontoMatr}&Senha=${login.webPontoPw}');
                    })
                    // ]]>
                </script>
            </h:form>
        </h:body>
    </f:view>
</html>

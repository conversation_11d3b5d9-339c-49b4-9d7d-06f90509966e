<!DOCTYPE html>
<!--
//SASW Tecnologia
//SATMOB
//FrmPontoBatidas - HTML
//Autor: Carlos
//Data: 02/03/2022
//
//Atualizacoes de versao: 
//
//-->
<html lang="pt-BR">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">

        <title>SatMob Batidas de Ponto EW</title>

        <script defer src="frmpontobatidas.js"></script>
        <link href="frmpontobatidas.css" rel="stylesheet"/>
		





    </head>

    <body>
        <nav class="navbar navbar" id="nav-home" style="display: flex; align-items: center; justify-content: center">
		    <div id="navbar-esq" style="width: 30%">
			   <img style="border-radius: 100%; width: 9%;margin-left: 2%"src="layout-imgs/SatMobEw.png" alt="SatMOB" class="d-inline-block""/>
			   <label style="width: 100%; font-size: 25px; font-weight: 500; color:#fff; margin-left: 2%; font-family: tahoma;">SatMob EW<label/>
			</div>
            <div id="container-top" style="width: 60%">
                <a style="display: flex; align-items: center;">
                    <img src="https://mobile.sasw.com.br:9091/satmobile/logos/logo_satsasex.png" alt="SatMOB" class="d-inline-block align-text-top"
                         style="width: 100%; "/>
                </a>
            </div>
			<div id="navbar-dir" style="width: 30%">
			</div>
        </nav>
		<div id="greeting"></div>

        <div id="painelMensagens" style="display: none; justify-content: center; align-items: center; flex-direction: column; margin-top: 5%;">
		   <input type="text" id="editMensagem" style="padding: 3px 5px; margin: 1%; font-size: medium; border-radius: 10px; text-align:center;" size="40px" readonly/>
		</div>

        <div id="painelLogin">         
		   <div style="display: flex; justify-content: center; align-items: center; flex-direction: column; margin-top: 5%;"> 
               <div>		   
                  <input placeholder="Matrícula" type="text" id="editMatr" style="padding: 1% 1%; margin: 1%; margin-left: -48px; font-size: medium; border-radius: 10px;" />            
			   </div>
			   <div >
                  <input placeholder="Senha" type ="password" id="editSenha"  Style="font-size: medium; border-radius: 10px; float: left" >			 
                  <button id="btnbio" style="margin-left :5px;" onclick="autenticaBio()"><div><img src="layout-imgs/imgbioface.png" height="20px"></div></button>
			   </div>
		   </div>
	   </div>
	   
		   
            <!--<div class="vimglbenviar"></div><div class="lbenviar">Enviar</div>-->            
        
		
        <!--button hidden class="btn" onclick="openModal('dv-modal')">Abrir modal</button-->

        <div id="dv-modal" class="modal">
           <div class="modal-content">
              <div class="modal-header">
                     <h4>Informação</h4>
              </div>
              <div class="modal-body" id="modal-textoCentral">

              </div>
              <div class="modal-footer">
                 <br> 
                 <button class="btn" onclick="closeModal('dv-modal')">Fechar</button>
                 <br>
              </div>
           </div>
        </div>

        <!--a href="#abrirModal">Open Modal</a>

        <div id="abrirModal" class="modal">
          <a href="#fechar" title="Fechar" class="fechar">x</a>
          <h2>Janela Modal</h2>
          <p>Esta é uma simples janela de modal.</p>
          <p>Você pode fazer qualquer coisa aqui, página de Login, pop-ups, ou formulários</p>
          <!--iframe hidden src="https://www.youtube.com/embed/jSHZakRmHiE" width="680" height="480" allowfullscreen></iframe-->
        </div-->	

		
       
        <div id="painelFuncion" style="display: none; justify-content: center; align-items: center;  margin-top: 2%; flex-direction: column;">            
            <input  type="text" id="editMatrF" style="padding: 1% 1%; margin: 1%; font-size: lower; border-radius: 10px; background-color: #B0C4DE; text-align:center; color: #000000;" size="15px" readonly/>

            <input  type="text" id="editNomeF" style="padding: 1% 1%; margin: 1%; font-size: lower; border-radius: 10px; background-color: #B0C4DE; text-align:center; color: #000000;" size="35px" readonly/>			

        </div>

        <div id="divCartaoFuncion" style="display: flex; justify-content: center; align-items: center;">
           <section id="sectionFoto" style="display: none" >
              <img style="border-radius: 75px; margin-top: 1%" src="layout-imgs/FotoND.jpg" id="fotoBiometria" width="162" height="176" /> <!-- 294 x 319 -->       
           
              <div style="align-items: left; justify-content: none">
                  <ul>
                  <h4><spam id="lbNomeF"></spam><br>
                   <spam id="lbCargoF" style="font-weight: normal">Cargo não informado</spam></h4>
                    <b>Matrícula:</b> &#09 <spam id="lbMatrF"></spam><br>
                    <b>Filial:</b> &#09 <spam id="lbFilialF">Filial não informada</spam><br>
                    <b>Posto:</b> &#09 <spam id="lbPostoF">Posto não informado</spam><br>
                    <b>Telefone:</b> &#09 <spam id="lbFoneF">(00) 00000-0000</spam><br>
                    <b>email:</b> &#09 <spam id="lbemailF">email não informado</spam><br>
                  </ul>
                 <br clear="all">
              </div>
           </section>	
	</div>

        <!-- Grid com horarios de ponto -->
        <div id="painelInfBatida" style="display: none; justify-content: center; align-items: Center;">
            <div class="painelGridBatidas" id='painelGridBatidasID'>
                <div class="divGridBatidas-Cab">
                    <h4>Registros de Ponto</h4>
                </div>
                <div class="scrollBoxBatidas">
                    <div class="divGridBatidas-Corpo">
                        <table>
                            <thead>
                                <tr class="divGridBatidas-tit">
                                    <th width="90"> Data </th>
                                    <th> Hora 1 </th>
                                    <th> Hora 2 </th>
                                    <th> Hora 3 </th>
                                    <th> Hora 4 </th>
                                </tr>
                            </thead>
                            <tbody id="gridBatidasLn"></tbody>
                        </table>
                    </div>    
                </div>
            </div>
        </div>
		
	    <div style="display: flex; justify-content: center; align-items: center;  margin-top: 2%;">
     	      <button style=" margin-right:2%;" type="button" id="btnEnviar" onclick="autenticar()">
			     <div><img src="layout-imgs/btnconfirmar.png" height="40px"></div>
		         <div>Enviar</div>
			  </button>
	          <button type="button" id="btnSair" onclick="atualizar()">
			     <div><img src="layout-imgs/btncancelar.png" height="40px" ></div>
		         <div>Sair</div>
		    </button>
        </div>
    </body>

</html>

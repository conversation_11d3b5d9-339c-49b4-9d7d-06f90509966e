<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../../assets/css/stylePage.css" rel="stylesheet" />
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    .tabela .ui-datatable-scrollable-body{
                        height: calc(100% - 6.5em);
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td {
                        min-width: 100px;
                        max-width: 100px;
                        white-space: normal !important;
                        overflow-wrap: break-word !important;
                        word-wrap: break-word !important;
                        -webkit-hyphens: auto !important;
                        -ms-hyphens: auto !important;
                        hyphens: auto !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                .tabela .ui-datatable-scrollable-body{
                    height: calc(100% - 9em);
                }

                .botoesDataTable {
                    width: 40px;
                    margin-top: 8px;
                    position: absolute;
                    right: -14px; top: 50%;
                    transform: translateY(-50%);
                }

                .veiculoCard .veiculoCardInner{
                    padding:0px !important;
                    width:100% !important;
                    border:1px solid #CCC !important;
                    background:linear-gradient(to bottom, #FAFAFA, #F6F6F6);
                    margin-bottom:6px;
                    margin-top:4px !important;
                    border-radius:6px !important;
                    box-shadow:2px 2px 3px #DDD;
                    border-top:5px solid #3c8dbc !important;
                }

            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{veiculos.Persistencia(login.pp)}" />
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12">
                                    <img src="../../assets/img/icone_satmob_veiculos.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Veiculos}</label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12">
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{veiculos.filiais.descricao}
                                            <label id="btTrocarFilial"
                                                   onclick="top.location.href = '../param.xhtml'"
                                                   >
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco">
                                            #{veiculos.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{veiculos.filiais.bairro}, #{veiculos.filiais.cidade}/#{veiculos.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3">
                                    <p:commandLink title="#{localemsgs.Voltar}" onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <div class="ui-grid ui-grid-responsive FundoPagina"
                         style="overflow:hidden !important; overflow-y:auto !important; padding-right:12px !important; border-top-color:#d2d6de !important;">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel id="cadastrar" style="display: inline;">
                                    <p:dataGrid
                                        id="tabela"
                                        value="#{veiculos.allVeiculos}"
                                        paginator="true" rows="50" lazy="true"
                                        rowsPerPageTemplate="5,10,15,20,25,50"
                                        currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Veiculos}"
                                        paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport}
                                        {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                        var="veiculo" columns="1"
                                        emptyMessage="#{localemsgs.SemRegistros}">
                                        <div class="veiculoCard" style=" max-width: 1024px; margin: 0 auto;">
                                            <p:panel header="#{veiculo.placa} #{veiculo.descricaoModelo}" class="veiculoCardInner">
                                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                             layout="grid" styleClass="ui-panelgrid-blank">
                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.Numero}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.numero}">
                                                                <f:convertNumber pattern="0000"/>
                                                            </h:outputText>
                                                        </div>
                                                    </p:column>
                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.CodFil}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.codFil}">
                                                                <f:convertNumber pattern="0000"/>
                                                            </h:outputText>
                                                        </div>
                                                    </p:column>

                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.Tipo}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.tipo}" converter="conversorTipoVeiculo"/>
                                                        </div>
                                                    </p:column>

                                                    <p:column style="padding:0px !important; margin:0px !important;">
                                                        <p:commandLink title="#{localemsgs.Editar}" update="msgs formVeiculo:cadastrar cabecalho"
                                                                       actionListener="#{veiculos.editarVeiculo(veiculo)}">
                                                            <p:graphicImage url="../../assets/img/icone_redondo_editar.png" height="40"/>
                                                        </p:commandLink>
                                                    </p:column>
                                                </p:panelGrid>

                                            </p:panel>

                                            <script>
                                                // <![CDATA[
                                                $(document).ready(function () {
                                                    if ($(document).width() <= 700)
                                                        $('.FundoPagina').css('max-height', ($('body').height() - 200) + 'px');
                                                    else
                                                        $('.FundoPagina').css('max-height', ($('body').height() - 121) + 'px');
                                                });

                                                $(window).resize(function () {
                                                    if ($(document).width() <= 700)
                                                        $('.FundoPagina').css('max-height', ($('body').height() - 200) + 'px');
                                                    else
                                                        $('.FundoPagina').css('max-height', ($('body').height() - 121) + 'px');
                                                });
                                                // ]]>
                                            </script>
                                        </div>
                                    </p:dataGrid>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 0px; bottom: 180px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}"
                                           update="formVeiculo:dlgCadastrar cabecalho" actionListener="#{veiculos.novoVeiculo()}">
                                <p:graphicImage url="../../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <h:form id="formVeiculo">
                    <p:dialog widgetVar="dlgCadastrar" id="dlgCadastrar"
                              positionType="absolute"
                              draggable="false" modal="true" closable="true"
                              resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              styleClass="painelCadastro"
                              style="max-height:75% !important; max-width: 800px !important;
                              border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;
                              padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important;
                              font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formVeiculo\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                        </p:confirmDialog>

                        <f:facet name="header">
                            <img src="../../assets/img/icone_satmob_veiculos.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText
                                value="#{veiculos.flag eq 1 ? localemsgs.CadastrarVeiculo : localemsgs.EditarVeiculo}"
                                style="color:#022a48" />
                        </f:facet>

                        <p:panel id="cadastrar" styleClass="painelCadastro" style="background-color:#EEE !important;">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="filial" value="#{localemsgs.Filial}:"/>
                                <p:selectOneMenu id="filial" value="#{veiculos.filial}" converter="omnifaces.SelectItemsConverter"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%" disabled="#{veiculos.flag eq 2}">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="numero" value="#{localemsgs.Numero}:" />
                                <p:inputText value="#{veiculos.veiculo.numero}"
                                             id="numero" style="width: 100%">
                                    <p:watermark for="numero" value="#{localemsgs.Numero}"/>
                                </p:inputText>

                                <p:outputLabel for="placa" value="#{localemsgs.Placa}:" />
                                <p:inputText value="#{veiculos.veiculo.placa}" maxlength="7"
                                             id="placa" style="width: 100%">
                                    <p:watermark for="placa" value="#{localemsgs.Placa}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="situacao" value="#{localemsgs.Tipo}:"/>
                                <p:selectOneMenu value="#{veiculos.veiculo.tipo}" style="width: 100%" required="true" id="situacao">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.CarroForte}" itemValue="F"/>
                                    <f:selectItem itemLabel="#{localemsgs.CarroLeve}" itemValue="L"/>
                                    <f:selectItem itemLabel="#{localemsgs.Moto}" itemValue="M"/>
                                    <f:selectItem itemLabel="#{localemsgs.Pesado}" itemValue="P"/>
                                    <f:selectItem itemLabel="#{localemsgs.Blindado}" itemValue="B"/>
                                    <f:selectItem itemLabel="#{localemsgs.Particular}" itemValue="R"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank" id="panelModelo">
                                <p:outputLabel for="modelo" value="#{localemsgs.Modelo}:" rendered="#{!veiculos.cadastroNovaModelo}"/>
                                <p:autoComplete id="modelo" value="#{veiculos.modelo}" completeMethod="#{veiculos.buscarModelos}"
                                                required="true" label="#{localemsgs.Modelo}" rendered="#{!veiculos.cadastroNovaModelo}"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Modelo}"  scrollHeight="200"
                                                inputStyle="width: 100%" placeholder="#{localemsgs.Modelo}" forceSelection="true"
                                                var="modelo" itemLabel="#{modelo.descricao}" itemValue="#{modelo}">
                                    <p:column>
                                        <i class="fas fa-plus" style="#{modelo.codigo eq '-1' ? 'display: inline; font-size: 10px;' : 'display: none'}"></i>
                                        <h:outputText value=" #{modelo.descricao}" style="#{modelo.codigo eq '-1' ? 'font-weight: bold' : ''}"/>
                                    </p:column>
                                    <o:converter converterId="omnifaces.ListIndexConverter" list="#{veiculos.buscaModelos}" />
                                    <p:ajax event="itemSelect" listener="#{veiculos.selecionarModelo}" update="msgs formVeiculo:panelModelo"/>
                                </p:autoComplete>

                                <p:outputLabel for="novoModelo" value="#{localemsgs.Modelo}:" rendered="#{veiculos.cadastroNovaModelo}"/>
                                <p:inputText value="#{veiculos.modelo.descricao}" rendered="#{veiculos.cadastroNovaModelo}" id="novoModelo" style="width: 100%">
                                    <p:watermark for="novoModelo" value="#{localemsgs.Modelo}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:commandLink action="#{veiculos.cadastrar}" update="msgs main" styleClass="botao"
                                           title="#{localemsgs.Cadastrar}" rendered="#{veiculos.flag eq 1}">
                                <p:graphicImage url="../../assets/img/icone_redondo_adicionar.png" width="40" height="40" />
                            </p:commandLink>

                            <p:commandLink action="#{veiculos.editar}" update="msgs main" styleClass="botao"
                                           title="#{localemsgs.Editar}" rendered="#{veiculos.flag eq 2}">
                                <p:graphicImage url="../../assets/img/icone_redondo_editar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <!-- TODO -->
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="../#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

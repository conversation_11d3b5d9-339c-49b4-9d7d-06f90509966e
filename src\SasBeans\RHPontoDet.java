/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class RHPontoDet {

    private String Matr;
    private String DtCompet;
    private String Batida;
    private String Secao;
    private String Local;
    
    private String Nome;
    private String Atraso;
    private String HoraRegistro;
    private String HoraPrevista;
    
    private String Hora1;
    private String Hora2;
    private String Hora3;
    private String Hora4;

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public String getDtCompet() {
        return DtCompet;
    }

    public void setDtCompet(String DtCompet) {
        this.DtCompet = DtCompet;
    }

    public String getBatida() {
        return Batida;
    }

    public void setBatida(String Batida) {
        this.Batida = Batida;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getLocal() {
        return Local;
    }

    public void setLocal(String Local) {
        this.Local = Local;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getAtraso() {
        return Atraso;
    }

    public void setAtraso(String Atraso) {
        this.Atraso = Atraso;
    }

    public String getHoraRegistro() {
        return HoraRegistro;
    }

    public void setHoraRegistro(String HoraRegistro) {
        this.HoraRegistro = HoraRegistro;
    }

    public String getHoraPrevista() {
        return HoraPrevista;
    }

    public void setHoraPrevista(String HoraPrevista) {
        this.HoraPrevista = HoraPrevista;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getHora2() {
        return Hora2;
    }

    public void setHora2(String Hora2) {
        this.Hora2 = Hora2;
    }

    public String getHora3() {
        return Hora3;
    }

    public void setHora3(String Hora3) {
        this.Hora3 = Hora3;
    }

    public String getHora4() {
        return Hora4;
    }

    public void setHora4(String Hora4) {
        this.Hora4 = Hora4;
    }
}

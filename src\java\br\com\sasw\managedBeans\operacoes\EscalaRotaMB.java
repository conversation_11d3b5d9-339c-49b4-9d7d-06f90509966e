package br.com.sasw.managedBeans.operacoes;

import SasBeansCompostas.EscalaPessoaDTO;
import br.com.sasw.lazydatamodels.EscalaPessoaLazyList;
import br.com.sasw.managedBeans.BaseBeanMB;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named
@ViewScoped
public class EscalaRotaMB extends BaseBeanMB {

    private LazyDataModel<EscalaPessoaDTO> escalasPaginadas;
    private EscalaPessoaDTO escalaSelecionada, escalaEdicao;
    private final Map filters = new HashMap();

    public EscalaRotaMB() throws Exception {
        super();
        filters.put("codFil", codFil);
        filters.put("data", dataTela);
    }

    @PostConstruct
    public void init() {
        carregarEscalas();
    }

    public void carregarEscalas() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabelaEscalas");
        dt.setFilters(filters);
        escalasPaginadas = new EscalaPessoaLazyList(persistencia);
    }

    public void ativarModalCadastro() {
        escalaSelecionada = null;
        PrimeFaces.current().resetInputs("cadastroEscala:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        prepararCadastro();
    }

    public void ativarModalEdicao() {
        if (escalaSelecionada == null) {
            displayInfo("SelecioneEscala");
            return;
        }
        PrimeFaces.current().resetInputs("cadastroEscala:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        prepararCadastro();
    }

    public void ativarModalPesquisa() {
        PrimeFaces.current().resetInputs("formPesquisa:pesquisar");
        PrimeFaces.current().executeScript("PF('dlgPesquisa').show();");
    }

    ////////////////////////////////
    // Cadastro:
    public void prepararCadastro() {
        if (escalaSelecionada == null) {
        } else {
            escalaEdicao = new EscalaPessoaDTO(escalaSelecionada);
            // TODO...
        }
    }

    public LazyDataModel<EscalaPessoaDTO> getEscalasPaginadas() {
        return escalasPaginadas;
    }

    public void setEscalasPaginadas(LazyDataModel<EscalaPessoaDTO> escalasPaginadas) {
        this.escalasPaginadas = escalasPaginadas;
    }

    public EscalaPessoaDTO getEscalaSelecionada() {
        return escalaSelecionada;
    }

    public void setEscalaSelecionada(EscalaPessoaDTO escalaSelecionada) {
        this.escalaSelecionada = escalaSelecionada;
    }

    public EscalaPessoaDTO getEscalaEdicao() {
        return escalaEdicao;
    }
}

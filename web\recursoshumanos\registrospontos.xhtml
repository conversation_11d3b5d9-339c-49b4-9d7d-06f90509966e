<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:components="http://xmlns.jcp.org/jsf/composite/components"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/common-layout.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>

            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    .tabela .ui-datatable-scrollable-body{
                        flex-grow: 1;

                    }
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid tbody tr td:nth-child(1){
                        text-align: center;
                        min-width: 50px !important;
                        width:50px !important;
                    }

                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2){
                        text-align: center;
                        min-width: 80px !important;
                        width:80px !important;
                    }

                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid tbody tr td:nth-child(6),
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7){
                        text-align: center;
                    }

                    .ui-expanded-row-content .ui-datatable-scrollable-body{
                        height:auto !important;
                    }
                    
                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid tbody tr td:nth-child(1),
                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2){
                        min-width: 120px !important;
                        width: 120px !important;
                        max-width: 120px !important;
                        text-align: center !important;
                    }
                    
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7),
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid tbody tr td:nth-child(8){
                        min-width: 180px !important;
                        width: 180px !important;
                        max-width: 180px !important;
                        text-align: center !important;
                    }
                    
                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid tbody tr td:nth-child(3),
                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid tbody tr td:nth-child(4){
                        text-align: left !important;
                    }
                    
                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid tbody tr td:nth-child(5){
                        min-width: 200px !important;
                        width: 200px !important;
                        max-width: 200px !important;
                        text-align: center !important;
                    }
                    
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid tbody tr td:nth-child(6){
                        text-align: center !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }
                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }


                #body {
                    height: calc(100% - 40px);
                    position: relative;
                    display: flex;
                    flex-direction: column;
                }

                #main {
                    flex-grow: 1;
                }

                #formPesquisar .ui-radiobutton {
                    background: transparent !important;
                }

                .ui-datatable-scrollable-body,
                .FundoPagina > .ui-panel-content {
                    height: 100% !important;
                }

                .FundoPagina {
                    border: thin solid #CCC !important;
                    border-top:4px solid #3C8DBC !important;
                }

                [id*="tabGeral"],
                [id*="tabDocumentos"]{
                    background-color:#FFF !important;
                    padding-right: 20px!important;
                }
                
                [id*="formOrdenacaoRapida"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }
                
                .calendario input{
                    width: 200px !important;
                }
                
                [id*="verBatidas"] .ui-dialog.ui-widget-content .ui-dialog-titlebar,
                [id*="formPesquisar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar,
                [id*="formOrdenacaoRapida"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color: #FFF !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="verBatidas"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content,
                [id*="formPesquisar"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content,
                [id*="formOrdenacaoRapida"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color: #EEE !important;
                }
            </style>
        </h:head>

        <h:body id="h" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{registrospontos.Persistencias(login.pp)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_satmob_contratosG.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.RegistroPresenca}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText value="#{registrospontos.dataInicio}" converter="conversorData" />
                                            <h:outputText value=" - "/>
                                            <h:outputText value="#{registrospontos.dataFim}" converter="conversorData"/>
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{registrospontos.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{registrospontos.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{registrospontos.filiais.bairro}, #{registrospontos.filiais.cidade}/#{registrospontos.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" >

                                    <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                                value="#{registrospontos.datasSelecionadas}"
                                                monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{registrospontos.selecionarDatas}" update="main msgs" />
                                  </p:datePicker>

                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:dataTable
                            id="tabela"
                            value="#{registrospontos.lazyFolhas}"
                            selection="#{registrospontos.selecionado}"
                            rowKey="#{lista.matr}"
                            paginator="true"
                            rows="15"
                            lazy="true"
                            reflow="true"
                            rowsPerPageTemplate="5,10,15, 20, 25"
                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.RegistroPresenca}"
                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                            var="lista"
                            styleClass="tabela"
                            selectionMode="single"
                            emptyMessage="#{localemsgs.SemRegistros}"
                            scrollable="true"
                            class="DataGrid"
                            scrollWidth="100%"
                            style="display: flex; flex-direction: column; font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                            >
                            <p:ajax event="rowSelect" listener="#{registrospontos.verBatidas}"
                                    oncomplete="PF('dlgVisualizar').show();"
                                    update="msgs verBatidas main:botoes"/>
                            <p:column headerText="#{localemsgs.CodFil}" class="celula-right">
                                <h:outputText value="#{lista.codFil}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Matr}">
                                <h:outputText value="#{lista.matr}">
                                    <f:convertNumber maxIntegerDigits="10" maxFractionDigits="1" pattern="#########0.0" />
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Nome}">
                                <h:outputText value="#{lista.nome}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Posto}">
                                <h:outputText value="#{lista.posto}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.secao}" class="text-right">
                                <h:outputText value="#{lista.secao}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Horario}" class="text-right">
                                <h:outputText value="#{lista.horario}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.CTPS_Nro}">
                                <h:outputText value="#{lista.CTPS_Nro}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.CTPS_Serie}">
                                <h:outputText value="#{lista.CTPS_Serie}"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 80px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Imprimir}"
                                           actionListener="#{registrospontos.gerarDownloadPDF}"
                                           ajax="false"
                                           target="_blank"
                                           update="msgs">
                                <p:graphicImage url="../assets/img/icone_pdf.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px; display: none">
                            <p:commandLink title="#{localemsgs.Imprimir} +"
                                           actionListener="#{registrospontos.gerarDownloadPDFAll}"
                                           ajax="false"
                                           rendered="false"
                                           update="msgs">
                                <p:graphicImage url="../assets/img/icone_pdf.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" action="#{registrospontos.prePesquisa}"
                                           update="formPesquisar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Ordenacao}" process="@this"
                                           oncomplete="PF('dlgOrdenacaoRapida').show()" update="formOrdenacaoRapida">
                                <p:graphicImage url="../assets/img/icone_redondo_ordenar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{registrospontos.limpaFiltros}"
                                           update=":main:tabela :msgs :cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <!-- Ver batidas-->
                <h:form id="verBatidas">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgVisualizar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="80%"
                               style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;"
                               >
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.FolhaDePonto}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <div class="ui-grid-row" style="padding-bottom: 16px;">
                            <p:outputLabel for="nome" value="#{localemsgs.Nome}:" style="padding-right: 10px;"/>
                            <h:outputText id="nome" value="#{registrospontos.selecionado.nome}" />
                        </div>

                        <div style="position:relative; overflow: auto; -webkit-overflow-scrolling: touch; height: 50vh;">
                            <p:dataTable
                                id="batidas"
                                value="#{registrospontos.batidas}"
                                var="batida"
                                paginator="true"
                                rows="50"
                                rowsPerPageTemplate="10,20,25,50,100"
                                currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Batidas}"
                                paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                emptyMessage="#{localemsgs.SemRegistros}"
                                reflow="true"
                                styleClass="tabelaReformulada"
                                class="grelha tabelaReformulada"
                                scrollable="true"
                                scrollWidth="100%"
                                >
                                <p:column headerText="#{localemsgs.Data}"  class="text-right">
                                    <h:outputText value="#{batida.data}" converter="conversorData"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DiaSemana}"  class="text-right">
                                    <h:outputText value="#{batida.diaSemana}" converter="conversorDiaSemana"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora1}"  class="text-right">
                                    <h:outputText value="#{batida.batida01}" converter="conversorHora"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora2}"  class="text-right">
                                    <h:outputText value="#{batida.batida02}" converter="conversorHora"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora3}"  class="text-right">
                                    <h:outputText value="#{batida.batida03}" converter="conversorHora"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora4}"  class="text-right">
                                    <h:outputText value="#{batida.batida04}" converter="conversorHora"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.PostoRegistro}" >
                                    <h:outputText value="#{batida.postoRegistro}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.OBS}">
                                    <h:outputText value="#{batida.OBS}"/>
                                </p:column>
                            </p:dataTable>
                        </div>

                        <p:commandLink title="#{localemsgs.Imprimir}"
                                       actionListener="#{registrospontos.gerarDownloadPDF}"
                                       ajax="false"
                                       target="_blank"
                                       update="msgs">
                            <p:graphicImage url="../assets/img/icone_pdf.png" height="40"/>
                        </p:commandLink>
                    </p:dialog>
                </h:form>

                <!--Pesquisar -->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true" focus="nome"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.RegistroPresenca}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel id="cal1" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}"/>
                                </div>
                                <div style="width: 25%; float: left">
                                    <p:calendar id="calendario1" styleClass="calendario"
                                                value="#{registrospontos.dataObjetoInicio}" mask="true"
                                                title="#{localemsgs.DataInicial}" label="#{localemsgs.DataInicial}"
                                                pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left;">
                                    <p:outputLabel id="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}"/>
                                </div>
                                <div style="width: 25%; float: left">
                                    <p:calendar id="calendario2" styleClass="calendario"
                                                value="#{registrospontos.dataObjetoFim}" mask="true"
                                                title="#{localemsgs.DataFinal}" label="#{localemsgs.DataFinal}"
                                                pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="filial" value="#{localemsgs.Filial}:"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="filial" value="#{registrospontos.filial}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains" style="width: 100%" label="">
                                        <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                                       itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nome" value="#{localemsgs.Nome}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome" value="#{registrospontos.selecionado.nome}" label="#{localemsgs.Nome}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="matricula" value="#{localemsgs.Matr}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="matricula" value="#{registrospontos.selecionado.matr}" label="#{localemsgs.Matr}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="matricula" value="#{localemsgs.Matr}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="posto" value="#{localemsgs.Posto}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="posto" value="#{registrospontos.selecionado.posto}" label="#{localemsgs.Posto}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="posto" value="#{localemsgs.Posto}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{registrospontos.pesquisar}"
                                               update=":msgs :cabecalho corporativo"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
                
                <!--Ordenar -->
                <h:form id="formOrdenacaoRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgOrdenacaoRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgOrdenacaoRapida"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Ordenacao}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelOrdenacaoRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoesOrdenacao" value="#{localemsgs.OrdenarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoesOrdenacao"
                                        value="#{registrospontos.chaveOrdem}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.PstServ}" itemValue="POSTO" />
                                        <f:selectItem itemLabel="#{localemsgs.Matr}" itemValue="MATRICULA" />
                                    </p:selectOneRadio>
                                </div>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-right: 0px !important">
                                <p:commandLink id="botaoOrdenacaoRapida"
                                               action="#{registrospontos.ordenar()}"
                                               update=":msgs :cabecalho corporativo main"
                                               oncomplete="PF('dlgOrdenacaoRapida').hide()"
                                               title="#{localemsgs.Ordenar}" styleClass="btn btn-primary">
                                    <i class="fa fa-sort" style="margin-right:8px !important"></i>#{localemsgs.Ordenar}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelOrdenacaoRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoOrdenacaoRapida').click();
                        }
                    });
                </script>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <h:outputText value="#{localemsgs.Corporativo}: " />
                                <p:selectBooleanCheckbox value="#{registrospontos.mostrandoTodasFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{registrospontos.mostrarFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

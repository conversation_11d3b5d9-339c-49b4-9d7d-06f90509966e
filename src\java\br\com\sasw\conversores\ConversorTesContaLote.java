package br.com.sasw.conversores;

import SasBeansCompostas.TesConta;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;
import java.util.Iterator;
import java.util.List;

@FacesConverter("conversorTesContaLote")
public class ConversorTesContaLote implements Converter {
    private List<TesConta> list;

    public ConversorTesContaLote() {
    }

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        Iterator iterator = this.list.iterator();

        TesConta lote;
        do {
            if (!iterator.hasNext()) {
                return null;
            }

            lote = (TesConta) iterator.next();
        } while (!value.equals(lote.getTipoSrvCodigo()));

        return lote;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        return value == null ? null : value.toString();
    }

    public void setList(List<TesConta> list) {
        this.list = list;
    }
}

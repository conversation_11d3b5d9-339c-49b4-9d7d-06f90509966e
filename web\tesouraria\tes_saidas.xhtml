<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css"  href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                [id*="tabelaLacre"] th:nth-child(2),
                [id*="tabelaLacre"] td:nth-child(2){
                    min-width: 28px !important;
                    width: 28px !important;
                    max-width: 28px !important;
                }

                .Desabilitado{
                    border: none !important;
                    padding: 3px !important;
                    background: transparent !important;
                    color: #666 !important;                    
                    margin-bottom: 8px !important;
                    box-shadow: none !important;
                    border-bottom: thin solid #888 !important;
                    height: 31px !important;
                }

                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formLacre"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="formCadastrar"] label{
                    margin-bottom: 2px !important;
                    font-weight: bold !important;
                    text-shadow: 1px 1px #FFF !important;
                }

                .FundoPagina{
                    min-height: calc(100% - 10px) !important;
                    height: calc(100% - 10px) !important;
                    max-height: calc(100% - 10px) !important;
                }

                .ui-inputtext{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                [id*="range_input"]{
                    min-width: 100% !Important;
                    width: 100% !Important;
                    max-width: 100% !Important;
                }

                [id*="divFiltros"]{
                    padding: 15px 10px 10px 10px !important;
                }

                [id*="divFiltros"] label{
                    width: 100% !important;
                    font-size: 10pt !important;
                    color: #333 !important;
                    text-shadow: 1px 1px #FFF;
                    font-weight: bold;
                }

                [id*="tabela"] .ui-datatable-tablewrapper{
                    height: calc(100% - 152px) !important;
                }

                [id*="tabela"] .ui-datatable-data {
                    height: calc(100% - 200px) !important;
                }

                [id*="tabela"] td,
                [id*="tabela"] th{
                    min-width: 150px !important;
                    width: 150px !important;
                    max-width: 150px !important;
                }

                [id*="tabela"] td:nth-child(6),
                [id*="tabela"] th:nth-child(6){
                    min-width: 250px !important;
                    width: 250px !important;
                    max-width: 250px !important;
                }

                [id*="tabela"] td:nth-child(4),
                [id*="tabela"] th:nth-child(4){
                    min-width: 80px !important;
                    width: 80px !important;
                    max-width: 80px !important;
                }

                .ui-datatable-scrollable .ui-datatable-scrollable-header{
                    margin: 0px !important;
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px){
                    .DataGrid [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }


                }

                [id*="Central"]{
                    height: calc(100% - 0px) !important;
                }

                #lblGrid{
                    width: 100% !important;
                    text-align: center;
                    color: #AAA !important;
                    font-size: 18pt;
                    font-weight: 500;
                    position: absolute;
                    z-index: 999;
                    height: 140px;
                    top:0;
                    right:0;
                    bottom:50px;
                    left:0;
                    margin: auto !important;
                }

                #lblGrid i{
                    color: #AAA !important; 
                    font-size: 26pt;
                } 

                [id*="btPesquisar"]{
                    height: 33px !important;
                    padding-top: 5px;
                    margin-top: 22px;
                }

                [id*="tabel_paginator_bottom"]{
                    display: none;
                }

                body .ui-calendar button.ui-datepicker-trigger>span.ui-icon-calendar:before {
                    top: -1px !important;
                }

                [id*="tabela_paginator_bottom"]{
                    display: none !important;
                }

                .DestaqueCxForte td{
                    color: forestgreen;
                }

                .DestaquePendente td{
                    color: red;
                }

                .ui-state-highlight td{
                    color: #FFF;
                }

                #tblProcessados{
                    width: 100%;
                    border: thin solid #DDD;
                    border-collapse: separate;
                    border-spacing: 1px !important;
                }

                #tblProcessados thead tr th{
                    background: linear-gradient(to bottom, #333, #101010) !important;
                    color: #FFF !important;
                    font-weight: bold !important;
                    text-align: center !important;
                    padding: 8px !important;
                }

                #tblProcessados tbody tr td{
                    text-align: center !important;
                    border: thin solid #DDD;
                    color: #666 !important;
                    padding: 4px !important;
                }

                .jconfirm-title{
                    color:#3498db !important;
                }
                
                .btn-warning{
                    background-color: #de9013 !important
                }
                
                .btn-warning:hover{
                    background-color: #df8e0c !important
                }
            </style>
        </h:head>

        <h:body id="h" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{tesSaida.carregarListaTesouraria()}" />
            </f:metadata>

            <p:growl id="msgs" />

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_tesouraria_saidas.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.SaidaTesouraria}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span>
                                            <h:outputText value="#{tesSaida.dataSelecionada1}" converter="conversorDateDia" />
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">
                                        #{tesSaida.filialTela.descricao}
                                        <label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label>
                                    </label>
                                    <label class="FilialEndereco">#{tesSaida.filialTela.endereco}</label>
                                    <label class="FilialBairroCidade">#{tesSaida.filialTela.bairro}, #{tesSaida.filialTela.cidade}/#{tesSaida.filialTela.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: center !important; padding-top: 0px !important; position: relative">
                                    <p:commandLink action="#{tesSaida.carregarGrideGuias}" 
                                                   update="msgs main" class="btn btn-warning"
                                                   id="btProcessarGuias"
                                                   onclick="rcProcessarGuias();">
                                        <h:outputText value="#{localemsgs.ExistemGuiaPendentesSaida1} #{tesSaida.quantidadeGuias} #{localemsgs.ExistemGuiaPendentesSaida2}" converter="conversorDateDia" />
                                    </p:commandLink>

                                    <p:remoteCommand name="rcProcessarGuias" partialSubmit="true" 
                                                     process="@this" 
                                                     update="msgs main cabecalho:btProcessarGuias" 
                                                     actionListener="#{tesSaida.processarGuias()}" />   
                                    <label id="lblTextoFixo" style="bottom: 2px;left: 0; right: 0; margin: 0 auto !important; width: 110px; border-radius: 0px 0px 3px 3px; position: absolute; background-color: darkred; color: #FFF; text-align: center; font-size: 9pt; padding: 0px !important"><i>#{localemsgs.SomenteGuiasLacre}</i></label>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:panel id="divFiltros" class="col-md-12 col-sm-12 col-xs-12" style="height: 90px; background-color: #EEE; border: thin solid #CCC; box-shadow: 2px 2px 4px #DDD; margin-bottom: 20px; border-radius: 4px; border-left: 4px solid #888 !important;border-right: 4px solid #888 !important; padding-right: 26px !important">
                            <div class="col-md-3 col-sm-3 col-xs-6" style="padding-right: 0px !important;">
                                <label>#{localemsgs.Filial}</label>
                                <p:selectOneMenu
                                    value="#{tesSaida.codFil}"
                                    converter="omnifaces.SelectItemsConverter"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                    styleClass="filiais"
                                    style="width: 100%; font-size: 12px; max-width: 100%;"
                                    >
                                    <f:selectItems value="#{tesSaida.filiais}"
                                                   var="item"
                                                   itemValue="#{item.codfilAc}"
                                                   itemLabel="#{item.descricao}"
                                                   noSelectionValue=""/>
                                    <p:ajax event="itemSelect"
                                            update="msgs tesourarias main:Central main:botoes"
                                            process="@this"
                                            partialSubmit="true"
                                            listener="#{tesSaida.carregarListaTesouraria}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6" style="padding-right: 0px !important;">
                                <label>#{localemsgs.Tesouraria}</label>
                                <p:selectOneMenu value="#{tesSaida.codCli1}" style="width: 100%" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tesouraria}" required="true" id="tesourarias">
                                    <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Selecione}" itemValue=""></f:selectItem>

                                    <f:selectItems value="#{tesSaida.listaTesouraria}"
                                                   var="itemTes"
                                                   itemValue="#{itemTes.codigo};#{itemTes.NRed}"
                                                   itemLabel="#{itemTes.NRed}"
                                                   noSelectionValue=""/>
                                    <p:ajax event="itemSelect"
                                            update="msgs main:Central main:botoes"
                                            process="@this"
                                            partialSubmit="true"
                                            listener="#{tesSaida.limpaEfetuada}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6" style="padding-right: 0px !important;">
                                <label>#{localemsgs.TipoMovimentacao}</label>
                                <p:selectOneMenu value="#{tesSaida.tipoMov}" style="width: 100%" required="true" id="tipoMov">
                                    <f:selectItem itemLabel="0 - #{localemsgs.Geral}" itemValue="0" />
                                    <f:selectItem itemLabel="1 - #{localemsgs.Diurno}" itemValue="1" />
                                    <f:selectItem itemLabel="2 - #{localemsgs.Noturno}" itemValue="2" />
                                    <f:selectItem itemLabel="3 - #{localemsgs.Vespertino}" itemValue="3" />

                                    <p:ajax event="itemSelect"
                                            update="msgs main:Central main:botoes"
                                            process="@this"
                                            partialSubmit="true"
                                            listener="#{tesSaida.limpaEfetuada}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-6" style="padding-right: 0px !important;">
                                <table style="width:100% !important; padding: 0px;">
                                    <tr>
                                        <td style="padding-right: 16px !important; padding-top: 2px;">
                                            <center><label>#{localemsgs.Periodo}</label>
                                                <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                                              value="#{tesSaida.datasSelecionadas}"
                                                              monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                              pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                              converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                                    <p:ajax event="dateSelect" listener="#{tesSaida.selecionarDatas}" update="msgs main:Central main:botoes" />
                                                </p:datePicker>
                                            </center>
                                        </td>
                                        <td style="width: 130px">
                                            <p:commandLink action="#{tesSaida.carregarGrideGuias}" 
                                                           update="msgs main cabecalho:btProcessarGuias" class="btn btn-success"
                                                           id="btPesquisar"
                                                           style="min-width: 130px !important">
                                                <i class="fa fa-search"></i>&nbsp;&nbsp;#{localemsgs.Pesquisar}
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </p:panel>

                        <p:panel id="Central" class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                            <label id="lblGrid" style="display: #{tesSaida.pesquisaEfetuada?'none':''}"><i class="fa fa-exclamation-triangle"></i>&nbsp;&nbsp;#{localemsgs.CliquePesquisar}</label>
                            <p:dataTable id="tabela" 
                                         rendered="#{tesSaida.pesquisaEfetuada}"
                                         value="#{tesSaida.lazyTesSaidas}" 
                                         selection="#{tesSaida.guiaSelecionada}" 
                                         paginator="true" 
                                         rows="15" 
                                         lazy="true"
                                         reflow="true" 
                                         rowsPerPageTemplate="5, 10, 15, 20, 25"
                                         currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Guias}"
                                         paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                         var="lista" 
                                         styleClass="tabela" 
                                         selectionMode="single" 
                                         emptyMessage="#{localemsgs.SemRegistros}"
                                         class="tabela" 
                                         style="background: white;padding:0px !important; margin:0px !important; height:calc(100% - 115px) !important;max-height: calc(100% - 115px) !important;"
                                         rowStyleClass="#{lista.situacao ne 'OK'? 'DestaquePendente': lista.serie ne '53'?'':'DestaqueCxForte'}">
                                <p:ajax event="rowDblselect" listener="#{tesSaida.dblSelect}" update="formCadastrar msgs" />
                                <p:column headerText="#{localemsgs.Data}" class="text-center">
                                    <h:outputText value="#{lista.data}" title="#{lista.data}" converter="conversorData" class="text-center" />
                                </p:column>
                                <p:column headerText="#{localemsgs.TipoMovimentacao}" class="text-center">
                                    <h:outputText value="#{lista.tipoMov}" title="#{lista.tipoMov}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Guia}" class="text-center">
                                    <h:outputText value="#{lista.guia}" title="#{lista.guia}" class="text-center" converter="conversor0" />
                                </p:column>
                                <p:column headerText="#{localemsgs.Serie}" class="text-center">
                                    <h:outputText value="#{lista.serie}" title="#{lista.serie}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Agencia}" class="text-center">
                                    <h:outputText value="#{lista.agencia}" title="#{lista.agencia}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Destino}" class="text-center">
                                    <h:outputText value="#{lista.destino}" title="#{lista.destino}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TotalGeral}" class="text-center">
                                    <h:outputText value="#{lista.totalGeral}" title="#{lista.totalGeral}" class="text-center" converter="conversormoedasemsimbolo"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Lacre}" class="text-center">
                                    <h:outputText value="#{lista.lacre}" title="#{lista.lacre}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.CodCli2}" class="text-center">
                                    <h:outputText value="#{lista.codCli2}" title="#{lista.codCli2}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.CodCli3}" class="text-center">
                                    <h:outputText value="#{lista.codCli3}" title="#{lista.codCli3}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TotalDN}" class="text-center">
                                    <h:outputText value="#{lista.totalDN}" title="#{lista.totalDN}" class="text-center" converter="conversormoedasemsimbolo"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TotalDD}" class="text-center">
                                    <h:outputText value="#{lista.totalDD}" title="#{lista.totalDD}" class="text-center" converter="conversormoedasemsimbolo"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.MarcaATM}" class="text-center">
                                    <h:outputText value="#{lista.marcaAtm}" title="#{lista.marcaAtm}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TotalMoeda}" class="text-center">
                                    <h:outputText value="#{lista.totalMoeda}" title="#{lista.totalMoeda}" class="text-center" converter="conversormoedasemsimbolo"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ChequesQtde}" class="text-center">
                                    <h:outputText value="#{lista.chequesQtde}" title="#{lista.chequesQtde}" class="text-center" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ChequesValor}" class="text-center">
                                    <h:outputText value="#{lista.chequesValor}" title="#{lista.chequesValor}" class="text-center" converter="conversormoedasemsimbolo"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TicketsQtde}" class="text-center">
                                    <h:outputText value="#{lista.ticketsQtde}" title="#{lista.ticketsQtde}" class="text-center" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TicketsValor}" class="text-center">
                                    <h:outputText value="#{lista.ticketsValor}" title="#{lista.ticketsValor}" class="text-center" converter="conversormoedasemsimbolo"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.MatrConf}" class="text-center">
                                    <h:outputText value="#{lista.matrConf}" title="#{lista.matrConf}" class="text-center" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HoraInicio}" class="text-center">
                                    <h:outputText value="#{lista.hrInicio}" converter="conversorHora" title="#{lista.hrInicio}" class="text-center" />
                                </p:column>
                                <p:column headerText="#{localemsgs.Camera}" class="text-center">
                                    <h:outputText value="#{lista.camera}" title="#{lista.camera}" class="text-center" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Diferenca}" class="text-center">
                                    <h:outputText value="#{lista.diferenca}" title="#{lista.diferenca}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ContaTes}" class="text-center">
                                    <h:outputText value="#{lista.contaTes}" title="#{lista.contaTes}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.tipoServ}" class="text-center">
                                    <h:outputText value="#{lista.tipoSrv}" title="#{lista.tipoSrv}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.CodSrv}" class="text-center">
                                    <h:outputText value="#{lista.codSrv}" title="#{lista.codSrv}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.OBS}" class="text-center">
                                    <h:outputText value="#{lista.obs}" title="#{lista.obs}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Situacao}" class="text-center">
                                    <h:outputText value="#{lista.situacao}" title="#{lista.situacao}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Pedido}" class="text-center">
                                    <h:outputText value="#{lista.pedido}" title="#{lista.pedido}" class="text-center" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora1}" class="text-center">
                                    <h:outputText value="#{lista.hora1}" converter="conversorHora" title="#{lista.hora1}" class="text-center" />
                                </p:column>
                                <p:column headerText="#{localemsgs.Rota}" class="text-center">
                                    <h:outputText value="#{lista.rota}" title="#{lista.rota}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TipoSrvRota}" class="text-center">
                                    <h:outputText value="#{lista.tipoSrvRota}" title="#{lista.tipoSrvRota}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Remessa}" class="text-center">
                                    <h:outputText value="#{lista.remessa}" title="#{lista.remessa}" class="text-center" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DataPedido}" class="text-center">
                                    <h:outputText value="#{lista.dt_Pedido}" title="#{lista.dt_Pedido}" converter="conversorData" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TpCliBrad}" class="text-center">
                                    <h:outputText value="#{lista.tpCliBrad}" title="#{lista.tpCliBrad}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                    <h:outputText value="#{lista.operador}" title="#{lista.operador}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                    <h:outputText value="#{lista.dt_Alter}" title="#{lista.dt_Alter}" converter="conversorData" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                    <h:outputText value="#{lista.hr_Alter}" title="#{lista.hr_Alter}" converter="conversorHora" class="text-center"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>    

                        <p:panel style="position: fixed; z-index: 1; right: 5px; bottom: 50px !important; background: transparent; height:50px !important;" id="botoes">
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.Editar}"
                                               rendered="#{tesSaida.pesquisaEfetuada}"
                                               update="formCadastrar msgs"
                                               process="@this,tabela"
                                               actionListener="#{tesSaida.preEdicao}">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="55"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:panel>
                </h:form>

                <h:form id="formLacre" class="form-inline" style="max-width: 600px !important">
                    <p:dialog widgetVar="dlgLacre"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important; max-width: 500px !important">

                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.Lacre}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrarLacre" style="background-color: transparent; max-width: 100% !important; margin-top:-10px !important; z-index:999 !important; height:calc(100% - 20px) !important; padding-left: 0px !important; padding-right: 0px !important;" class="cadastrar">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label>#{localemsgs.numero}</label>
                                <h:inputText class="form-control" id="txtNovoLacre" value="#{tesSaida.lacreSelecionado.lacre}" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.numero}" maxlength="20"></h:inputText>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 10px !important; padding-bottom: 0px !important; text-align: right">
                                <p:commandLink action="#{tesSaida.salvarLacre}" 
                                               update="msgs formCadastrar:tabelaLacre" class="btn btn-primary" process="@this txtNovoLacre"
                                               id="btSalvar">
                                    <i class="fa fa-save"></i>&nbsp;&nbsp;#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formCadastrar" class="form-inline" style="max-width: 600px !important">
                    <p:dialog widgetVar="dlgCadastrar"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important; max-width: 500px !important">

                        <f:facet name="header">
                            <img src="../assets/img/icone_tesouraria_saidas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SaidaNumerario}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; margin-top:-10px !important; z-index:999 !important; height:calc(100% - 20px) !important; padding-left: 0px !important; padding-right: 0px !important;" class="cadastrar">
                            <div class="col-md-9 col-sm-9 col-xs-9">
                                <label>#{localemsgs.Guia}</label>
                                <h:outputText class="form-control Desabilitado" id="txtGuia" value="#{tesSaida.guiaSelecionada.guia}"></h:outputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3">
                                <label>#{localemsgs.Serie}</label>
                                <h:outputText class="form-control Desabilitado" id="txtSerie" value="#{tesSaida.guiaSelecionada.serie}"></h:outputText>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label>#{localemsgs.Origem}</label>
                                <h:outputText class="form-control Desabilitado" id="txtOrigem" value="#{tesSaida.codCli1.split(';')[1]}"></h:outputText>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label>#{localemsgs.Destino}</label>
                                <h:outputText class="form-control Desabilitado" id="txtDestino" value="#{tesSaida.guiaSelecionada.destino}"></h:outputText>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-6">
                                <label>#{localemsgs.Valor}</label>
                                <h:outputText class="form-control Desabilitado" id="txtvalor" value="#{tesSaida.guiaSelecionada.totalDN}" converter="conversormoedasemsimbolo"></h:outputText>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-6">
                                <label>#{localemsgs.Moeda}</label>
                                <h:outputText class="form-control Desabilitado" id="txtMoeda" value="#{tesSaida.guiaSelecionada.totalMoeda}" converter="conversormoedasemsimbolo"></h:outputText>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label>#{localemsgs.Conferente}&nbsp;<font color="red">(*)</font></label>
                                <p:selectOneMenu id="filtroFuncionario" value="#{tesSaida.guiaSelecionada.matrConf}" converter="omnifaces.SelectItemsConverter"
                                                 styleClass="filial"
                                                 filter="true" filterMatchMode="contains"
                                                 required="true"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Conferente}" 
                                                 style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                    <f:selectItems value="#{tesSaida.funcionarios}" var="funcionarios" itemValue="#{funcionarios.matr}"
                                                   itemLabel="#{funcionarios.nome}" noSelectionValue=""/>
                                    <p:watermark for="filtroFuncionario" value="#{localemsgs.Funcionarios}"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="max-width: calc(100% - 30px) !important; margin-left: 15px; padding: 4px 8px 8px 8px !important; margin-top: 10px !important; background: lightyellow; border: thin solid orangered; min-height: 30px;">
                                <label style="width: 100%; color: orangered !important;">#{localemsgs.Composicoes}</label>
                                <label style="width: 100%; color: #000 !important; font-weight: 500 !important;"><strong>#{localemsgs.Cedulas}: </strong>#{tesSaida.dadosComposicaoDN}</label>
                                <label style="width: 100%; color: #000 !important; font-weight: 500 !important;"><strong>#{localemsgs.Moedas}: </strong>#{tesSaida.dadosComposicaoMD}</label>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 8px !important; padding-bottom: 0px !important">
                                <label>#{localemsgs.Lacres}</label>
                            </div>
                            <div class="col-md col-sm-12 col-xs-12" style="max-width: calc(100% - 30px) !important; margin-left: 15px; padding: 0px !important; margin-top: 0px !important; background: #FFF; border: thin solid #CCC; min-height: 100px;height: 100px;max-height: 130px;">
                                <div style="width: calc(100% - 50px) !important; float: left;min-height: 98px;height: 98px;max-height: 98px; padding: 0px !important">
                                    <p:dataTable id="tabelaLacre"
                                                 value="#{tesSaida.listaLacres}"
                                                 var="gridLacres"
                                                 selectionMode="single"
                                                 selection="#{tesSaida.lacreSelecionado}"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true"
                                                 class="tabela DataGrid"

                                                 style="font-size: 12px; margin: 0px !important; min-height: 98px !important;height: 98px !important;max-height: 98px !important;"
                                                 rowKey="#{gridLacres.lacre}">
                                        <p:ajax event="rowDblselect" listener="#{tesSaida.dblSelectLacre}" update="msgs formLacre tabelaLacre" />

                                        <p:column headerText="#{localemsgs.Numero}" class="text-left" style="text-align: left !important">
                                            <h:outputText value="#{gridLacres.lacre}" title="#{gridLacres.lacre}" class="text-left" style="color: #000 !important" />
                                        </p:column>
                                    </p:dataTable>
                                </div>
                                <div style="width: 50px !important; float: left;min-height: 98px;height: 98px;max-height: 98px; text-align: center !important; border-left: thin solid #DDD; background-color: whitesmoke; padding-top: 2px !important; ">
                                    <p:commandLink title="#{localemsgs.Novo}"
                                                   update="msgs formLacre tabelaLacre"
                                                   process="@this tabelaLacre"
                                                   oncomplete="PF('dlgLacre').show();"
                                                   actionListener="#{tesSaida.novoLacre}" style="display: block">
                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30"/>
                                    </p:commandLink>
                                    <p:commandLink title="#{localemsgs.Editar}"
                                                   update="msgs"
                                                   process="@this tabelaLacre"
                                                   actionListener="#{tesSaida.editarLacre}" style="display: block; margin-top: 1px !important;">
                                        <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                    </p:commandLink>
                                    <p:commandLink title="#{localemsgs.Excluir}"
                                                   update="msgs tabelaLacre"
                                                   process="@this tabelaLacre"
                                                   actionListener="#{tesSaida.excluirLacre}" style="display: block; margin-top: 1px !important;">
                                        <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="30"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 10px !important; padding-bottom: 0px !important; text-align: right">
                                <p:commandLink action="#{tesSaida.salvarListaLacres}" 
                                               update="msgs main cabecalho:btProcessarGuias" class="btn btn-primary"
                                               id="btSalvar">
                                    <i class="fa fa-save"></i>&nbsp;&nbsp;#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <script>
                    $(document).ready(function () {
                        //$('#lblTextoFixo').width(($('[id*="btProcessarGuias"]').width() + 26) + 'px');

                        setTimeout(function () {
                            $('#lblTextoFixo').css({
                                'min-width': $('a[id*="cabecalho:btProcessarGuias"]:first-child').css('width').toString(),
                                'width': $('a[id*="cabecalho:btProcessarGuias"]:first-child').css('width').toString(),
                                'max-width': $('a[id*="cabecalho:btProcessarGuias"]:first-child').css('width').toString()
                            });
                        }, 100);
                    });

                    function MostrarListaProcessadas(HtmlBody) {
                        var HTML = '';

                        HTML += '<table id="tblProcessados">';
                        HTML += '  <thead>';
                        HTML += '    <tr>';
                        HTML += '      <th>#{localemsgs.Tesouraria}</th>';
                        HTML += '      <th>#{localemsgs.Data}</th>';
                        HTML += '      <th>#{localemsgs.Guia}</th>';
                        HTML += '      <th>#{localemsgs.Serie}</th>';
                        HTML += '    </tr>';
                        HTML += '  </thead>';
                        HTML += '  <tbody>';
                        HTML += HtmlBody;
                        HTML += '  </tbody>';
                        HTML += '</table>';

                        $.ModalDialogCallBack('large',
                                'fa fa-check-square-o fa-lg',
                                '#{localemsgs.ProcessamentoEfetuado}',
                                HTML.toString(),
                                'blue',
                                'fa fa-times',
                                '#{localemsgs.Fechar}',
                                '',
                                '',
                                '',
                                '#{localeController.number}');
                    }
                </script>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <!--Rodapé-->
            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:0px; max-height:10px !important;">
                        <h:form id="corporativo" style="background-color: whitesmoke; padding: 3px 8px 3px 8px !important; border: thin solid #CCC;">
                            <label style="width: 100%; margin: 0px 0px 2px 0px !important; font-size: 8pt !important; font-weight: bold; color: forestgreen !important; position: relative;"><label style="position: absolute;width: 10px; height: 10px; background-color: forestgreen"></label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#{localemsgs.GuiaEmitidaCxForte}</label>
                            <label style="width: 100%; margin: 0px 0px 2px 0px !important; font-size: 8pt !important; font-weight: bold; color: red !important; position: relative;"><label style="position: absolute;width: 10px; height: 10px; background-color: red"></label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#{localemsgs.GuiaPendente}</label>
                            <label style="width: 100%; margin: 0px !important; font-size: 8pt !important; font-weight: bold; color: steelblue !important; position: relative"><label style="position: absolute;width: 10px; height: 10px; background-color: steelblue"></label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#{localemsgs.GuiaEmitidaNaoCxForte}</label>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-4 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>      
</html>
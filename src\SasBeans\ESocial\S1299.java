/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1299 {

    private String sucesso;

    private String evtFechaEvPer_Id;

    private String ideEvento_indApuracao;
    private String ideEvento_perApur;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;

    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;

    private String ideRespInf_nmResp;
    private String ideRespInf_cpfResp;
    private String ideRespInf_telefone;
    private String ideRespInf_email;

    private String infoFech_evtRemun;
    private String infoFech_evtPgtos;
    private String infoFech_evtAqProd;
    private String infoFech_evtComProd;
    private String infoFech_evtContratAvNP;
    private String infoFech_evtInfoComplPer;
    private String infoFech_compSemMovto;

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtFechaEvPer_Id() {
        return null == evtFechaEvPer_Id ? "" : evtFechaEvPer_Id;
    }

    public void setEvtFechaEvPer_Id(String evtFechaEvPer_Id) {
        this.evtFechaEvPer_Id = evtFechaEvPer_Id;
    }

    public String getIdeEvento_indApuracao() {
        return null == ideEvento_indApuracao ? "" : ideEvento_indApuracao;
    }

    public void setIdeEvento_indApuracao(String ideEvento_indApuracao) {
        this.ideEvento_indApuracao = ideEvento_indApuracao;
    }

    public String getIdeEvento_perApur() {
        return null == ideEvento_perApur ? "" : ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeRespInf_nmResp() {
        return null == ideRespInf_nmResp ? "" : ideRespInf_nmResp;
    }

    public void setIdeRespInf_nmResp(String ideRespInf_nmResp) {
        this.ideRespInf_nmResp = ideRespInf_nmResp;
    }

    public String getIdeRespInf_cpfResp() {
        return null == ideRespInf_cpfResp ? "" : ideRespInf_cpfResp;
    }

    public void setIdeRespInf_cpfResp(String ideRespInf_cpfResp) {
        this.ideRespInf_cpfResp = ideRespInf_cpfResp;
    }

    public String getIdeRespInf_telefone() {
        return null == ideRespInf_telefone ? "" : ideRespInf_telefone;
    }

    public void setIdeRespInf_telefone(String ideRespInf_telefone) {
        this.ideRespInf_telefone = ideRespInf_telefone;
    }

    public String getIdeRespInf_email() {
        return null == ideRespInf_email ? "" : ideRespInf_email;
    }

    public void setIdeRespInf_email(String ideRespInf_email) {
        this.ideRespInf_email = ideRespInf_email;
    }

    public String getInfoFech_evtRemun() {
        return null == infoFech_evtRemun ? "" : infoFech_evtRemun;
    }

    public void setInfoFech_evtRemun(String infoFech_evtRemun) {
        this.infoFech_evtRemun = infoFech_evtRemun;
    }

    public String getInfoFech_evtPgtos() {
        return null == infoFech_evtPgtos ? "" : infoFech_evtPgtos;
    }

    public void setInfoFech_evtPgtos(String infoFech_evtPgtos) {
        this.infoFech_evtPgtos = infoFech_evtPgtos;
    }

    public String getInfoFech_evtAqProd() {
        return null == infoFech_evtAqProd ? "" : infoFech_evtAqProd;
    }

    public void setInfoFech_evtAqProd(String infoFech_evtAqProd) {
        this.infoFech_evtAqProd = infoFech_evtAqProd;
    }

    public String getInfoFech_evtComProd() {
        return null == infoFech_evtComProd ? "" : infoFech_evtComProd;
    }

    public void setInfoFech_evtComProd(String infoFech_evtComProd) {
        this.infoFech_evtComProd = infoFech_evtComProd;
    }

    public String getInfoFech_evtContratAvNP() {
        return null == infoFech_evtContratAvNP ? "" : infoFech_evtContratAvNP;
    }

    public void setInfoFech_evtContratAvNP(String infoFech_evtContratAvNP) {
        this.infoFech_evtContratAvNP = infoFech_evtContratAvNP;
    }

    public String getInfoFech_evtInfoComplPer() {
        return null == infoFech_evtInfoComplPer ? "" : infoFech_evtInfoComplPer;
    }

    public void setInfoFech_evtInfoComplPer(String infoFech_evtInfoComplPer) {
        this.infoFech_evtInfoComplPer = infoFech_evtInfoComplPer;
    }

    public String getInfoFech_compSemMovto() {
        return null == infoFech_compSemMovto ? "" : infoFech_compSemMovto;
    }

    public void setInfoFech_compSemMovto(String infoFech_compSemMovto) {
        this.infoFech_compSemMovto = infoFech_compSemMovto;
    }
}

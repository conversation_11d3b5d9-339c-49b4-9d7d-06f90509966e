/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.RegistrosPonto.RegistrosPontoSatMobWeb;
import Dados.Persistencia;
import SasBeans.FolhaPonto;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class FolhasPontoLazyList extends LazyDataModel<FolhaPonto> {

    private static final long serialVersionUID = 1L;
    private List<FolhaPonto> folhas;
    private final RegistrosPontoSatMobWeb controller;

    public FolhasPontoLazyList(Persistencia persistencia) {
        this.controller = new RegistrosPontoSatMobWeb(persistencia);
    }
    
    public FolhasPontoLazyList(Persistencia persistencia, String recebeOrderBy) {
        this.controller = new RegistrosPontoSatMobWeb(persistencia, recebeOrderBy);
    }

    @Override
    public List<FolhaPonto> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            folhas = controller.listaFolhasPaginadoSemFiltro(first, pageSize, filters);

            // set the total of players
            setRowCount(controller.contagemRegistro(filters));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }

        return folhas;
    }

    @Override
    public Object getRowKey(FolhaPonto folha) {
        return folha.getMatr();
    }

    @Override
    public FolhaPonto getRowData(String matricula) {
        try {
            for (FolhaPonto folha : this.folhas) {
                if (matricula.equals(folha.getMatr())) {
                    return folha;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class FatISSGrp {

    private String Codigo;
    private String Tipo;
    private String Descricao;
    private String CodMunic;
    private String ISS;
    private String AliqICMS;
    private String ICMSRet;
    private String ISSRet;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String Municipio;
    private String AliqISS;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getCodMunic() {
        return CodMunic;
    }

    public void setCodMunic(String CodMunic) {
        this.CodMunic = CodMunic;
    }

    public String getISS() {
        return ISS;
    }

    public void setISS(String ISS) {
        this.ISS = ISS;
    }

    public String getAliqICMS() {
        return AliqICMS;
    }

    public void setAliqICMS(String AliqICMS) {
        this.AliqICMS = AliqICMS;
    }

    public String getICMSRet() {
        return ICMSRet;
    }

    public void setICMSRet(String ICMSRet) {
        this.ICMSRet = ICMSRet;
    }

    public String getISSRet() {
        return ISSRet;
    }

    public void setISSRet(String ISSRet) {
        this.ISSRet = ISSRet;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getMunicipio() {
        return Municipio;
    }

    public void setMunicipio(String Municipio) {
        this.Municipio = Municipio;
    }

    public String getAliqISS() {
        return AliqISS;
    }

    public void setAliqISS(String AliqISS) {
        this.AliqISS = AliqISS;
    }
}

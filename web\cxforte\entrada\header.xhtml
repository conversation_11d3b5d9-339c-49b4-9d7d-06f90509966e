<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:c="http://java.sun.com/jsp/jstl/core"
    >
    <style>
        button.ui-datepicker-trigger>span.ui-icon-calendar:before{
            top: 6px !important;
        }
    </style>
    <header class="fixed-header">
        <h:form id="cabecalho">
            <div class="cabecalho equal" style="padding-top: 4px;">
                <div id="divTopoTela2" class="col-md-3 col-xs-5">
                    <img src="../assets/img/icon_caixa_forte.png" height="40"/>

                    <div>
                        <label class="tituloPagina">#{localemsgs.EntradaCxForte}</label>
                        <label class="tituloDataHora">
                            <h:outputText value="#{localemsgs.Data}: "/>
                            <span><h:outputText id="dataDia" value="#{cxForteEntrada.dataTela}" converter="conversorDia"/></span>
                        </label>
                    </div>
                </div>

                <div id="divDadosFilial"
                     class="col-md-3 col-md-2 col-xs-4 boxSelecaoFilial">
                    <p:selectOneMenu
                        value="#{cxForteEntrada.codFil}"
                        converter="omnifaces.SelectItemsConverter"
                        required="true"
                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                        styleClass="filial"
                        style="width: 100%; font-size: 12px; max-width: 250px;"
                        >
                        <f:selectItems value="#{cxForteEntrada.filiais}"
                                       var="item"
                                       itemValue="#{item.codfilAc}"
                                       itemLabel="#{item.descricao}"
                                       noSelectionValue=""/>
                        <p:ajax event="itemSelect"
                                update="msgs main:tabela main:rota cabecalho"
                                process="@this"
                                partialSubmit="true"
                                listener="#{cxForteEntrada.onChangeCodFil()}"/>
                    </p:selectOneMenu>
                    <label class="FilialEndereco">#{cxForteEntrada.filialTela.endereco}</label>
                    <label class="FilialBairroCidade">#{cxForteEntrada.filialTela.bairro}, #{cxForteEntrada.filialTela.cidade}/#{cxForteEntrada.filialTela.UF}</label>
                </div>

                <div id="cxforte"
                     class="col-md-3 col-xs-6 boxSelecaoCaixaForte">
                    <div style="flex-shrink: 0;">
                        <p:outputLabel
                            value="#{localemsgs.CxForte}:&#160;"
                            />
                    </div>

                    <div style="flex-shrink: 0;">
                        <c:choose>
                            <c:when test="#{cxForteEntrada.caixasFortes.size() eq 0}">
                                <h:outputText value="#{localemsgs.CxForteNaoEncontrado}"/>
                            </c:when>
                            <c:when test="#{cxForteEntrada.caixasFortes.size() eq 1}">
                                <p:inputText
                                    value="#{cxForteEntrada.caixaForteSelecionado.codCli}"
                                    disabled="true"
                                    class="primefacesInputFix"
                                    />
                            </c:when>
                            <c:otherwise>
                                <p:selectOneMenu
                                    value="#{cxForteEntrada.caixaForteSelecionado}"
                                    converter="omnifaces.SelectItemsConverter"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                    styleClass="filial"
                                    style="width: 100%;"
                                    >
                                    <f:selectItems value="#{cxForteEntrada.caixasFortes}"
                                                   var="item"
                                                   itemValue="#{item}"
                                                   itemLabel="#{item.codCli}"
                                                   noSelectionValue=""/>
                                    <p:ajax event="itemSelect"
                                            update="msgs main:tabela main:rota cabecalho"
                                            process="@this"
                                            partialSubmit="true"
                                            listener="#{cxForteEntrada.onCaixaForteUpdate()}"/>
                                </p:selectOneMenu>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>

                <div id="divCalendario" class="col-md-3 col-xs-6"
                     style="display: flex; justify-content: center; padding-right: 15px;">
                    <p:commandLink action="#{cxForteEntrada.mudarDia(-1)}" update="main:tabela main:rota cabecalho msgs">
                        <p:graphicImage url="../assets/img/botao_anterior.png"/>
                    </p:commandLink>

                    <p:calendar id="calendario"
                                styleClass="calendario" showOn="button" navigator="true"
                                converter="conversorData"
                                pattern="#{mascaras.padraoData}"
                                value="#{cxForteEntrada.dataTela}"
                                locale="#{localeController.getCurrentLocale()}">
                        <p:ajax event="dateSelect" listener="#{cxForteEntrada.selecionarData}" update="main:tabela main:rota cabecalho msgs" />
                    </p:calendar>

                    <p:commandLink action="#{cxForteEntrada.mudarDia(1)}" update="main:tabela main:rota cabecalho msgs">
                        <p:graphicImage url="../assets/img/botao_proximo.png"/>
                    </p:commandLink>
                </div>
                
                 <div id="divBotaoVoltar" class="col-md-1 col-xs-3"
                     style="display: flex; justify-content: flex-end; padding: 2px;">
                    <p:commandLink title="Home"
                                   ajax="false"
                                   action="/menu.xhtml?faces-redirect=true">
                        <p:graphicImage url="../assets/img/icone_home_branco.png" height="40"/>
                    </p:commandLink>

                    <p:commandLink title="#{localemsgs.Voltar}"
                                   ajax="false"
                                   onclick="window.history.back();" action="#">
                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                    </p:commandLink>
                </div>
            </div>
        </h:form>
    </header>

</ui:composition>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels.comercial;

import Controller.OS_Vig.OS_VigSatMobWeb;
import Dados.Persistencia;
import SasBeans.OS_Vig;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class OS_VigLazyList extends LazyDataModel<OS_Vig> {

    private static final long serialVersionUID = 1L;
    private final OS_VigSatMobWeb os_VigSatMobWeb;
    private List<OS_Vig> os_vigs;
    private Map filters;
    private final Persistencia persistencia;

    public OS_VigLazyList(Persistencia pst, Map filters) {
        this.persistencia = pst;
        this.os_VigSatMobWeb = new OS_VigSatMobWeb();
        this.filters = filters;
    }

    @Override
    public List<OS_Vig> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.os_vigs = this.os_VigSatMobWeb.listaPaginada(first, pageSize, this.filters, this.persistencia);

            // set the total of players
            setRowCount(this.os_VigSatMobWeb.totalListaPaginada(this.filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.os_vigs;
    }

    @Override
    public Object getRowKey(OS_Vig os_vig) {
        if (null == os_vig.getCodFil() || null == os_vig.getOS()) {
            return null;
        }
        return os_vig.getCodFil().replace(".0", "") + ";" + os_vig.getOS().replace(".0", "");
    }

    private int indice;
    private OS_Vig os_vig;

    @Override
    public OS_Vig getRowData(String codFilOS) {
        String[] codFilOSArray = codFilOS.split(";");
        if (codFilOSArray.length != 2) {
            return null;
        }

        this.os_vig = new OS_Vig();
        this.os_vig.setCodFil(codFilOSArray[0].replace(".0", ""));
        this.os_vig.setOS(codFilOSArray[1].replace(".0", ""));

        this.indice = this.os_vigs.indexOf(this.os_vig);
        if (this.indice == -1) {
            return null;
        } else {
            return this.os_vigs.get(this.indice);
        }
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

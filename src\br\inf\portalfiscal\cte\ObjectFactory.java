//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.5-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2020.08.24 at 06:12:43 PM BRT 
//


package br.inf.portalfiscal.cte;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the br.inf.portalfiscal.cte package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: br.inf.portalfiscal.cte
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link TImp }
     * 
     */
    public TImp createTImp() {
        return new TImp();
    }

    /**
     * Create an instance of {@link TProtCTe }
     * 
     */
    public TProtCTe createTProtCTe() {
        return new TProtCTe();
    }

    /**
     * Create an instance of {@link TUnidadeTransp }
     * 
     */
    public TUnidadeTransp createTUnidadeTransp() {
        return new TUnidadeTransp();
    }

    /**
     * Create an instance of {@link TProtCTeOS }
     * 
     */
    public TProtCTeOS createTProtCTeOS() {
        return new TProtCTeOS();
    }

    /**
     * Create an instance of {@link TProtGTVe }
     * 
     */
    public TProtGTVe createTProtGTVe() {
        return new TProtGTVe();
    }

    /**
     * Create an instance of {@link TCTe }
     * 
     */
    public TCTe createTCTe() {
        return new TCTe();
    }

    /**
     * Create an instance of {@link TCTe.InfCte }
     * 
     */
    public TCTe.InfCte createTCTeInfCte() {
        return new TCTe.InfCte();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm }
     * 
     */
    public TCTe.InfCte.InfCTeNorm createTCTeInfCteInfCTeNorm() {
        return new TCTe.InfCte.InfCTeNorm();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfServVinc }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfServVinc createTCTeInfCteInfCTeNormInfServVinc() {
        return new TCTe.InfCte.InfCTeNorm.InfServVinc();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfCteSub }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfCteSub createTCTeInfCteInfCTeNormInfCteSub() {
        return new TCTe.InfCte.InfCTeNorm.InfCteSub();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfCteSub.TomaICMS }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfCteSub.TomaICMS createTCTeInfCteInfCTeNormInfCteSubTomaICMS() {
        return new TCTe.InfCte.InfCTeNorm.InfCteSub.TomaICMS();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.Cobr }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.Cobr createTCTeInfCteInfCTeNormCobr() {
        return new TCTe.InfCte.InfCTeNorm.Cobr();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.DocAnt }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.DocAnt createTCTeInfCteInfCTeNormDocAnt() {
        return new TCTe.InfCte.InfCTeNorm.DocAnt();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt createTCTeInfCteInfCTeNormDocAntEmiDocAnt() {
        return new TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt createTCTeInfCteInfCTeNormDocAntEmiDocAntIdDocAnt() {
        return new TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfDoc }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfDoc createTCTeInfCteInfCTeNormInfDoc() {
        return new TCTe.InfCte.InfCTeNorm.InfDoc();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfCarga }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfCarga createTCTeInfCteInfCTeNormInfCarga() {
        return new TCTe.InfCte.InfCTeNorm.InfCarga();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Imp }
     * 
     */
    public TCTe.InfCte.Imp createTCTeInfCteImp() {
        return new TCTe.InfCte.Imp();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.VPrest }
     * 
     */
    public TCTe.InfCte.VPrest createTCTeInfCteVPrest() {
        return new TCTe.InfCte.VPrest();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl }
     * 
     */
    public TCTe.InfCte.Compl createTCTeInfCteCompl() {
        return new TCTe.InfCte.Compl();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Entrega }
     * 
     */
    public TCTe.InfCte.Compl.Entrega createTCTeInfCteComplEntrega() {
        return new TCTe.InfCte.Compl.Entrega();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Fluxo }
     * 
     */
    public TCTe.InfCte.Compl.Fluxo createTCTeInfCteComplFluxo() {
        return new TCTe.InfCte.Compl.Fluxo();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Ide }
     * 
     */
    public TCTe.InfCte.Ide createTCTeInfCteIde() {
        return new TCTe.InfCte.Ide();
    }

    /**
     * Create an instance of {@link TImpOS }
     * 
     */
    public TImpOS createTImpOS() {
        return new TImpOS();
    }

    /**
     * Create an instance of {@link TUnidCarga }
     * 
     */
    public TUnidCarga createTUnidCarga() {
        return new TUnidCarga();
    }

    /**
     * Create an instance of {@link TGTVe }
     * 
     */
    public TGTVe createTGTVe() {
        return new TGTVe();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte }
     * 
     */
    public TGTVe.InfCte createTGTVeInfCte() {
        return new TGTVe.InfCte();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.DetGTV }
     * 
     */
    public TGTVe.InfCte.DetGTV createTGTVeInfCteDetGTV() {
        return new TGTVe.InfCte.DetGTV();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Compl }
     * 
     */
    public TGTVe.InfCte.Compl createTGTVeInfCteCompl() {
        return new TGTVe.InfCte.Compl();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Ide }
     * 
     */
    public TGTVe.InfCte.Ide createTGTVeInfCteIde() {
        return new TGTVe.InfCte.Ide();
    }

    /**
     * Create an instance of {@link TCTeOS }
     * 
     */
    public TCTeOS createTCTeOS() {
        return new TCTeOS();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte }
     * 
     */
    public TCTeOS.InfCte createTCTeOSInfCte() {
        return new TCTeOS.InfCte();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm createTCTeOSInfCteInfCTeNorm() {
        return new TCTeOS.InfCte.InfCTeNorm();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfGTVe }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfGTVe createTCTeOSInfCteInfCTeNormInfGTVe() {
        return new TCTeOS.InfCte.InfCTeNorm.InfGTVe();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.Cobr }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.Cobr createTCTeOSInfCteInfCTeNormCobr() {
        return new TCTeOS.InfCte.InfCTeNorm.Cobr();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfCteSub }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfCteSub createTCTeOSInfCteInfCTeNormInfCteSub() {
        return new TCTeOS.InfCte.InfCTeNorm.InfCteSub();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfCteSub.TomaICMS }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfCteSub.TomaICMS createTCTeOSInfCteInfCTeNormInfCteSubTomaICMS() {
        return new TCTeOS.InfCte.InfCTeNorm.InfCteSub.TomaICMS();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfServico }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfServico createTCTeOSInfCteInfCTeNormInfServico() {
        return new TCTeOS.InfCte.InfCTeNorm.InfServico();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Imp }
     * 
     */
    public TCTeOS.InfCte.Imp createTCTeOSInfCteImp() {
        return new TCTeOS.InfCte.Imp();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.VPrest }
     * 
     */
    public TCTeOS.InfCte.VPrest createTCTeOSInfCteVPrest() {
        return new TCTeOS.InfCte.VPrest();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Compl }
     * 
     */
    public TCTeOS.InfCte.Compl createTCTeOSInfCteCompl() {
        return new TCTeOS.InfCte.Compl();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Ide }
     * 
     */
    public TCTeOS.InfCte.Ide createTCTeOSInfCteIde() {
        return new TCTeOS.InfCte.Ide();
    }

    /**
     * Create an instance of {@link TRetEnviCTe }
     * 
     */
    public TRetEnviCTe createTRetEnviCTe() {
        return new TRetEnviCTe();
    }

    /**
     * Create an instance of {@link TEndereco }
     * 
     */
    public TEndereco createTEndereco() {
        return new TEndereco();
    }

    /**
     * Create an instance of {@link TRetCTeOS }
     * 
     */
    public TRetCTeOS createTRetCTeOS() {
        return new TRetCTeOS();
    }

    /**
     * Create an instance of {@link TRetCTe }
     * 
     */
    public TRetCTe createTRetCTe() {
        return new TRetCTe();
    }

    /**
     * Create an instance of {@link TEndReEnt }
     * 
     */
    public TEndReEnt createTEndReEnt() {
        return new TEndReEnt();
    }

    /**
     * Create an instance of {@link TRespTec }
     * 
     */
    public TRespTec createTRespTec() {
        return new TRespTec();
    }

    /**
     * Create an instance of {@link TEndernac }
     * 
     */
    public TEndernac createTEndernac() {
        return new TEndernac();
    }

    /**
     * Create an instance of {@link TLocal }
     * 
     */
    public TLocal createTLocal() {
        return new TLocal();
    }

    /**
     * Create an instance of {@link TEnviCTe }
     * 
     */
    public TEnviCTe createTEnviCTe() {
        return new TEnviCTe();
    }

    /**
     * Create an instance of {@link TRetGTVe }
     * 
     */
    public TRetGTVe createTRetGTVe() {
        return new TRetGTVe();
    }

    /**
     * Create an instance of {@link TEndeEmi }
     * 
     */
    public TEndeEmi createTEndeEmi() {
        return new TEndeEmi();
    }

    /**
     * Create an instance of {@link TEndOrg }
     * 
     */
    public TEndOrg createTEndOrg() {
        return new TEndOrg();
    }

    /**
     * Create an instance of {@link TImp.ICMS00 }
     * 
     */
    public TImp.ICMS00 createTImpICMS00() {
        return new TImp.ICMS00();
    }

    /**
     * Create an instance of {@link TImp.ICMS20 }
     * 
     */
    public TImp.ICMS20 createTImpICMS20() {
        return new TImp.ICMS20();
    }

    /**
     * Create an instance of {@link TImp.ICMS45 }
     * 
     */
    public TImp.ICMS45 createTImpICMS45() {
        return new TImp.ICMS45();
    }

    /**
     * Create an instance of {@link TImp.ICMS60 }
     * 
     */
    public TImp.ICMS60 createTImpICMS60() {
        return new TImp.ICMS60();
    }

    /**
     * Create an instance of {@link TImp.ICMS90 }
     * 
     */
    public TImp.ICMS90 createTImpICMS90() {
        return new TImp.ICMS90();
    }

    /**
     * Create an instance of {@link TImp.ICMSOutraUF }
     * 
     */
    public TImp.ICMSOutraUF createTImpICMSOutraUF() {
        return new TImp.ICMSOutraUF();
    }

    /**
     * Create an instance of {@link TImp.ICMSSN }
     * 
     */
    public TImp.ICMSSN createTImpICMSSN() {
        return new TImp.ICMSSN();
    }

    /**
     * Create an instance of {@link TProtCTe.InfProt }
     * 
     */
    public TProtCTe.InfProt createTProtCTeInfProt() {
        return new TProtCTe.InfProt();
    }

    /**
     * Create an instance of {@link TProtCTe.InfFisco }
     * 
     */
    public TProtCTe.InfFisco createTProtCTeInfFisco() {
        return new TProtCTe.InfFisco();
    }

    /**
     * Create an instance of {@link TUnidadeTransp.LacUnidTransp }
     * 
     */
    public TUnidadeTransp.LacUnidTransp createTUnidadeTranspLacUnidTransp() {
        return new TUnidadeTransp.LacUnidTransp();
    }

    /**
     * Create an instance of {@link TProtCTeOS.InfProt }
     * 
     */
    public TProtCTeOS.InfProt createTProtCTeOSInfProt() {
        return new TProtCTeOS.InfProt();
    }

    /**
     * Create an instance of {@link TProtCTeOS.InfFisco }
     * 
     */
    public TProtCTeOS.InfFisco createTProtCTeOSInfFisco() {
        return new TProtCTeOS.InfFisco();
    }

    /**
     * Create an instance of {@link TProtGTVe.InfProt }
     * 
     */
    public TProtGTVe.InfProt createTProtGTVeInfProt() {
        return new TProtGTVe.InfProt();
    }

    /**
     * Create an instance of {@link TProtGTVe.InfFisco }
     * 
     */
    public TProtGTVe.InfFisco createTProtGTVeInfFisco() {
        return new TProtGTVe.InfFisco();
    }

    /**
     * Create an instance of {@link TCTe.InfCTeSupl }
     * 
     */
    public TCTe.InfCTeSupl createTCTeInfCTeSupl() {
        return new TCTe.InfCTeSupl();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Emit }
     * 
     */
    public TCTe.InfCte.Emit createTCTeInfCteEmit() {
        return new TCTe.InfCte.Emit();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Rem }
     * 
     */
    public TCTe.InfCte.Rem createTCTeInfCteRem() {
        return new TCTe.InfCte.Rem();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Exped }
     * 
     */
    public TCTe.InfCte.Exped createTCTeInfCteExped() {
        return new TCTe.InfCte.Exped();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Receb }
     * 
     */
    public TCTe.InfCte.Receb createTCTeInfCteReceb() {
        return new TCTe.InfCte.Receb();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Dest }
     * 
     */
    public TCTe.InfCte.Dest createTCTeInfCteDest() {
        return new TCTe.InfCte.Dest();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCteComp }
     * 
     */
    public TCTe.InfCte.InfCteComp createTCTeInfCteInfCteComp() {
        return new TCTe.InfCte.InfCteComp();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCteAnu }
     * 
     */
    public TCTe.InfCte.InfCteAnu createTCTeInfCteInfCteAnu() {
        return new TCTe.InfCte.InfCteAnu();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.AutXML }
     * 
     */
    public TCTe.InfCte.AutXML createTCTeInfCteAutXML() {
        return new TCTe.InfCte.AutXML();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfSolicNFF }
     * 
     */
    public TCTe.InfCte.InfSolicNFF createTCTeInfCteInfSolicNFF() {
        return new TCTe.InfCte.InfSolicNFF();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfModal }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfModal createTCTeInfCteInfCTeNormInfModal() {
        return new TCTe.InfCte.InfCTeNorm.InfModal();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.VeicNovos }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.VeicNovos createTCTeInfCteInfCTeNormVeicNovos() {
        return new TCTe.InfCte.InfCTeNorm.VeicNovos();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfGlobalizado }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfGlobalizado createTCTeInfCteInfCTeNormInfGlobalizado() {
        return new TCTe.InfCte.InfCTeNorm.InfGlobalizado();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfServVinc.InfCTeMultimodal }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfServVinc.InfCTeMultimodal createTCTeInfCteInfCTeNormInfServVincInfCTeMultimodal() {
        return new TCTe.InfCte.InfCTeNorm.InfServVinc.InfCTeMultimodal();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfCteSub.TomaICMS.RefNF }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfCteSub.TomaICMS.RefNF createTCTeInfCteInfCTeNormInfCteSubTomaICMSRefNF() {
        return new TCTe.InfCte.InfCTeNorm.InfCteSub.TomaICMS.RefNF();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.Cobr.Fat }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.Cobr.Fat createTCTeInfCteInfCTeNormCobrFat() {
        return new TCTe.InfCte.InfCTeNorm.Cobr.Fat();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.Cobr.Dup }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.Cobr.Dup createTCTeInfCteInfCTeNormCobrDup() {
        return new TCTe.InfCte.InfCTeNorm.Cobr.Dup();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt.IdDocAntPap }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt.IdDocAntPap createTCTeInfCteInfCTeNormDocAntEmiDocAntIdDocAntIdDocAntPap() {
        return new TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt.IdDocAntPap();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt.IdDocAntEle }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt.IdDocAntEle createTCTeInfCteInfCTeNormDocAntEmiDocAntIdDocAntIdDocAntEle() {
        return new TCTe.InfCte.InfCTeNorm.DocAnt.EmiDocAnt.IdDocAnt.IdDocAntEle();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfDoc.InfNF }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfDoc.InfNF createTCTeInfCteInfCTeNormInfDocInfNF() {
        return new TCTe.InfCte.InfCTeNorm.InfDoc.InfNF();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfDoc.InfNFe }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfDoc.InfNFe createTCTeInfCteInfCTeNormInfDocInfNFe() {
        return new TCTe.InfCte.InfCTeNorm.InfDoc.InfNFe();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfDoc.InfOutros }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfDoc.InfOutros createTCTeInfCteInfCTeNormInfDocInfOutros() {
        return new TCTe.InfCte.InfCTeNorm.InfDoc.InfOutros();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.InfCTeNorm.InfCarga.InfQ }
     * 
     */
    public TCTe.InfCte.InfCTeNorm.InfCarga.InfQ createTCTeInfCteInfCTeNormInfCargaInfQ() {
        return new TCTe.InfCte.InfCTeNorm.InfCarga.InfQ();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Imp.ICMSUFFim }
     * 
     */
    public TCTe.InfCte.Imp.ICMSUFFim createTCTeInfCteImpICMSUFFim() {
        return new TCTe.InfCte.Imp.ICMSUFFim();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.VPrest.Comp }
     * 
     */
    public TCTe.InfCte.VPrest.Comp createTCTeInfCteVPrestComp() {
        return new TCTe.InfCte.VPrest.Comp();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.ObsCont }
     * 
     */
    public TCTe.InfCte.Compl.ObsCont createTCTeInfCteComplObsCont() {
        return new TCTe.InfCte.Compl.ObsCont();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.ObsFisco }
     * 
     */
    public TCTe.InfCte.Compl.ObsFisco createTCTeInfCteComplObsFisco() {
        return new TCTe.InfCte.Compl.ObsFisco();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Entrega.SemData }
     * 
     */
    public TCTe.InfCte.Compl.Entrega.SemData createTCTeInfCteComplEntregaSemData() {
        return new TCTe.InfCte.Compl.Entrega.SemData();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Entrega.ComData }
     * 
     */
    public TCTe.InfCte.Compl.Entrega.ComData createTCTeInfCteComplEntregaComData() {
        return new TCTe.InfCte.Compl.Entrega.ComData();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Entrega.NoPeriodo }
     * 
     */
    public TCTe.InfCte.Compl.Entrega.NoPeriodo createTCTeInfCteComplEntregaNoPeriodo() {
        return new TCTe.InfCte.Compl.Entrega.NoPeriodo();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Entrega.SemHora }
     * 
     */
    public TCTe.InfCte.Compl.Entrega.SemHora createTCTeInfCteComplEntregaSemHora() {
        return new TCTe.InfCte.Compl.Entrega.SemHora();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Entrega.ComHora }
     * 
     */
    public TCTe.InfCte.Compl.Entrega.ComHora createTCTeInfCteComplEntregaComHora() {
        return new TCTe.InfCte.Compl.Entrega.ComHora();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Entrega.NoInter }
     * 
     */
    public TCTe.InfCte.Compl.Entrega.NoInter createTCTeInfCteComplEntregaNoInter() {
        return new TCTe.InfCte.Compl.Entrega.NoInter();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Compl.Fluxo.Pass }
     * 
     */
    public TCTe.InfCte.Compl.Fluxo.Pass createTCTeInfCteComplFluxoPass() {
        return new TCTe.InfCte.Compl.Fluxo.Pass();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Ide.Toma3 }
     * 
     */
    public TCTe.InfCte.Ide.Toma3 createTCTeInfCteIdeToma3() {
        return new TCTe.InfCte.Ide.Toma3();
    }

    /**
     * Create an instance of {@link TCTe.InfCte.Ide.Toma4 }
     * 
     */
    public TCTe.InfCte.Ide.Toma4 createTCTeInfCteIdeToma4() {
        return new TCTe.InfCte.Ide.Toma4();
    }

    /**
     * Create an instance of {@link TImpOS.ICMS00 }
     * 
     */
    public TImpOS.ICMS00 createTImpOSICMS00() {
        return new TImpOS.ICMS00();
    }

    /**
     * Create an instance of {@link TImpOS.ICMS20 }
     * 
     */
    public TImpOS.ICMS20 createTImpOSICMS20() {
        return new TImpOS.ICMS20();
    }

    /**
     * Create an instance of {@link TImpOS.ICMS45 }
     * 
     */
    public TImpOS.ICMS45 createTImpOSICMS45() {
        return new TImpOS.ICMS45();
    }

    /**
     * Create an instance of {@link TImpOS.ICMS90 }
     * 
     */
    public TImpOS.ICMS90 createTImpOSICMS90() {
        return new TImpOS.ICMS90();
    }

    /**
     * Create an instance of {@link TImpOS.ICMSOutraUF }
     * 
     */
    public TImpOS.ICMSOutraUF createTImpOSICMSOutraUF() {
        return new TImpOS.ICMSOutraUF();
    }

    /**
     * Create an instance of {@link TImpOS.ICMSSN }
     * 
     */
    public TImpOS.ICMSSN createTImpOSICMSSN() {
        return new TImpOS.ICMSSN();
    }

    /**
     * Create an instance of {@link TUnidCarga.LacUnidCarga }
     * 
     */
    public TUnidCarga.LacUnidCarga createTUnidCargaLacUnidCarga() {
        return new TUnidCarga.LacUnidCarga();
    }

    /**
     * Create an instance of {@link TGTVe.InfCTeSupl }
     * 
     */
    public TGTVe.InfCTeSupl createTGTVeInfCTeSupl() {
        return new TGTVe.InfCTeSupl();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Emit }
     * 
     */
    public TGTVe.InfCte.Emit createTGTVeInfCteEmit() {
        return new TGTVe.InfCte.Emit();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Rem }
     * 
     */
    public TGTVe.InfCte.Rem createTGTVeInfCteRem() {
        return new TGTVe.InfCte.Rem();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Dest }
     * 
     */
    public TGTVe.InfCte.Dest createTGTVeInfCteDest() {
        return new TGTVe.InfCte.Dest();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.AutXML }
     * 
     */
    public TGTVe.InfCte.AutXML createTGTVeInfCteAutXML() {
        return new TGTVe.InfCte.AutXML();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.DetGTV.InfEspecie }
     * 
     */
    public TGTVe.InfCte.DetGTV.InfEspecie createTGTVeInfCteDetGTVInfEspecie() {
        return new TGTVe.InfCte.DetGTV.InfEspecie();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.DetGTV.InfVeiculo }
     * 
     */
    public TGTVe.InfCte.DetGTV.InfVeiculo createTGTVeInfCteDetGTVInfVeiculo() {
        return new TGTVe.InfCte.DetGTV.InfVeiculo();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Compl.ObsCont }
     * 
     */
    public TGTVe.InfCte.Compl.ObsCont createTGTVeInfCteComplObsCont() {
        return new TGTVe.InfCte.Compl.ObsCont();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Compl.ObsFisco }
     * 
     */
    public TGTVe.InfCte.Compl.ObsFisco createTGTVeInfCteComplObsFisco() {
        return new TGTVe.InfCte.Compl.ObsFisco();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Ide.Toma }
     * 
     */
    public TGTVe.InfCte.Ide.Toma createTGTVeInfCteIdeToma() {
        return new TGTVe.InfCte.Ide.Toma();
    }

    /**
     * Create an instance of {@link TGTVe.InfCte.Ide.TomaTerceiro }
     * 
     */
    public TGTVe.InfCte.Ide.TomaTerceiro createTGTVeInfCteIdeTomaTerceiro() {
        return new TGTVe.InfCte.Ide.TomaTerceiro();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCTeSupl }
     * 
     */
    public TCTeOS.InfCTeSupl createTCTeOSInfCTeSupl() {
        return new TCTeOS.InfCTeSupl();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Emit }
     * 
     */
    public TCTeOS.InfCte.Emit createTCTeOSInfCteEmit() {
        return new TCTeOS.InfCte.Emit();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Toma }
     * 
     */
    public TCTeOS.InfCte.Toma createTCTeOSInfCteToma() {
        return new TCTeOS.InfCte.Toma();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCteComp }
     * 
     */
    public TCTeOS.InfCte.InfCteComp createTCTeOSInfCteInfCteComp() {
        return new TCTeOS.InfCte.InfCteComp();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCteAnu }
     * 
     */
    public TCTeOS.InfCte.InfCteAnu createTCTeOSInfCteInfCteAnu() {
        return new TCTeOS.InfCte.InfCteAnu();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.AutXML }
     * 
     */
    public TCTeOS.InfCte.AutXML createTCTeOSInfCteAutXML() {
        return new TCTeOS.InfCte.AutXML();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfDocRef }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfDocRef createTCTeOSInfCteInfCTeNormInfDocRef() {
        return new TCTeOS.InfCte.InfCTeNorm.InfDocRef();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.Seg }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.Seg createTCTeOSInfCteInfCTeNormSeg() {
        return new TCTeOS.InfCte.InfCTeNorm.Seg();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfModal }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfModal createTCTeOSInfCteInfCTeNormInfModal() {
        return new TCTeOS.InfCte.InfCTeNorm.InfModal();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfGTVe.Comp }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfGTVe.Comp createTCTeOSInfCteInfCTeNormInfGTVeComp() {
        return new TCTeOS.InfCte.InfCTeNorm.InfGTVe.Comp();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.Cobr.Fat }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.Cobr.Fat createTCTeOSInfCteInfCTeNormCobrFat() {
        return new TCTeOS.InfCte.InfCTeNorm.Cobr.Fat();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.Cobr.Dup }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.Cobr.Dup createTCTeOSInfCteInfCTeNormCobrDup() {
        return new TCTeOS.InfCte.InfCTeNorm.Cobr.Dup();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfCteSub.TomaICMS.RefNF }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfCteSub.TomaICMS.RefNF createTCTeOSInfCteInfCTeNormInfCteSubTomaICMSRefNF() {
        return new TCTeOS.InfCte.InfCTeNorm.InfCteSub.TomaICMS.RefNF();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.InfCTeNorm.InfServico.InfQ }
     * 
     */
    public TCTeOS.InfCte.InfCTeNorm.InfServico.InfQ createTCTeOSInfCteInfCTeNormInfServicoInfQ() {
        return new TCTeOS.InfCte.InfCTeNorm.InfServico.InfQ();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Imp.ICMSUFFim }
     * 
     */
    public TCTeOS.InfCte.Imp.ICMSUFFim createTCTeOSInfCteImpICMSUFFim() {
        return new TCTeOS.InfCte.Imp.ICMSUFFim();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Imp.InfTribFed }
     * 
     */
    public TCTeOS.InfCte.Imp.InfTribFed createTCTeOSInfCteImpInfTribFed() {
        return new TCTeOS.InfCte.Imp.InfTribFed();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.VPrest.Comp }
     * 
     */
    public TCTeOS.InfCte.VPrest.Comp createTCTeOSInfCteVPrestComp() {
        return new TCTeOS.InfCte.VPrest.Comp();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Compl.ObsCont }
     * 
     */
    public TCTeOS.InfCte.Compl.ObsCont createTCTeOSInfCteComplObsCont() {
        return new TCTeOS.InfCte.Compl.ObsCont();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Compl.ObsFisco }
     * 
     */
    public TCTeOS.InfCte.Compl.ObsFisco createTCTeOSInfCteComplObsFisco() {
        return new TCTeOS.InfCte.Compl.ObsFisco();
    }

    /**
     * Create an instance of {@link TCTeOS.InfCte.Ide.InfPercurso }
     * 
     */
    public TCTeOS.InfCte.Ide.InfPercurso createTCTeOSInfCteIdeInfPercurso() {
        return new TCTeOS.InfCte.Ide.InfPercurso();
    }

    /**
     * Create an instance of {@link TRetEnviCTe.InfRec }
     * 
     */
    public TRetEnviCTe.InfRec createTRetEnviCTeInfRec() {
        return new TRetEnviCTe.InfRec();
    }

}

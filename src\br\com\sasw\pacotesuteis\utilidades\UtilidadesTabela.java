package br.com.sasw.pacotesuteis.utilidades;

import java.awt.Component;
import javax.swing.JTable;
import javax.swing.table.DefaultTableColumnModel;
import javax.swing.table.TableCellRenderer;
import javax.swing.table.TableColumn;

/**
 *
 * <AUTHOR>
 */
public class UtilidadesTabela {

    /**
     * Modela a largura de uma coluna de Tabela jTable(grid) para o maior
     * conteudo entre celula e cabecalho - Usar dentro de um for
     *
     * @param table - jTable que se deseja formatar
     * @param vColIndex - indice da coluna (comecando por 0)
     */
    public static void Redimensionar(JTable table, int vColIndex) throws Exception {
        int margin = 1; //parametro na versão original
        try {
            DefaultTableColumnModel colModel = (DefaultTableColumnModel) table.getColumnModel();
            TableColumn col = colModel.getColumn(vColIndex);
            int width;            // Obtém a largura do cabeçalho da coluna
            TableCellRenderer renderer = col.getHeaderRenderer();
            if (renderer == null) {
                renderer = table.getTableHeader().getDefaultRenderer();
            }
            Component comp = renderer.getTableCellRendererComponent(
                    table, col.getHeaderValue(), false, false, 0, 0);
            width = comp.getPreferredSize().width;            // Obtém a largura maxima da coluna de dados
            for (int r = 0; r < table.getRowCount(); r++) {
                renderer = table.getCellRenderer(r, vColIndex);
                comp = renderer.getTableCellRendererComponent(
                        table, table.getValueAt(r, vColIndex), false, false, r, vColIndex);
                width = Math.max(width, comp.getPreferredSize().width);
            }
            width += 2 * margin;            // Configura a largura
            col.setPreferredWidth(width);
        } catch (Exception e) {
            throw new Exception("Falha- " + e.getMessage());
        }
    }
}

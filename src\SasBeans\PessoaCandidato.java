/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class PessoaCandidato {

    private String SindicatoProp;
    private String Vicios;
    private String PassaTempos;
    private String AcaoCriminal;
    private String MovAcaoCrim;
    private String MovAcaoTrab;
    private String SPC;
    private String CheqDevolvido; //um caractere S ou N
    private String VlrDivida;
    private String ArmaFogo;
    private String RegistroArma;
    private String AceitaTrocaHr; //um caractere S ou N
    private String AceitaTrocaLocal; //um caractere S ou N
    private String OutrosCursos;
    private String Recrutamento;
    private String AmigosEmpresa; //um caractere S ou N
    private String FamiliaresEmpresa; //um caractere S ou N
    private String QtdeFilhos;
    private String IdadeFilhos;
    private String BensProprios;

    public String getSindicatoProp() {
        return SindicatoProp;
    }

    public void setSindicatoProp(String SindicatoProp) {
        this.SindicatoProp = SindicatoProp;
    }

    public String getVicios() {
        return Vicios;
    }

    public void setVicios(String Vicios) {
        this.Vicios = Vicios;
    }

    public String getPassaTempos() {
        return PassaTempos;
    }

    public void setPassaTempos(String PassaTempos) {
        this.PassaTempos = PassaTempos;
    }

    public String getAcaoCriminal() {
        return AcaoCriminal;
    }

    public void setAcaoCriminal(String AcaoCriminal) {
        this.AcaoCriminal = AcaoCriminal;
    }

    public String getMovAcaoCrim() {
        return MovAcaoCrim;
    }

    public void setMovAcaoCrim(String MovAcaoCrim) {
        this.MovAcaoCrim = MovAcaoCrim;
    }

    public String getMovAcaoTrab() {
        return MovAcaoTrab;
    }

    public void setMovAcaoTrab(String MovAcaoTrab) {
        this.MovAcaoTrab = MovAcaoTrab;
    }

    public String getSPC() {
        return SPC;
    }

    public void setSPC(String SPC) {
        this.SPC = SPC;
    }

    public String getCheqDevolvido() {
        return CheqDevolvido;
    }

    public void setCheqDevolvido(String CheqDevolvido) {
        this.CheqDevolvido = CheqDevolvido;
    }

    public String getVlrDivida() {
        return VlrDivida;
    }

    public void setVlrDivida(String VlrDivida) {
        this.VlrDivida = VlrDivida;
    }

    public String getArmaFogo() {
        return ArmaFogo;
    }

    public void setArmaFogo(String ArmaFogo) {
        this.ArmaFogo = ArmaFogo;
    }

    public String getRegistroArma() {
        return RegistroArma;
    }

    public void setRegistroArma(String RegistroArma) {
        this.RegistroArma = RegistroArma;
    }

    public String getAceitaTrocaHr() {
        return AceitaTrocaHr;
    }

    public void setAceitaTrocaHr(String AceitaTrocaHr) {
        this.AceitaTrocaHr = AceitaTrocaHr;
    }

    public String getAceitaTrocaLocal() {
        return AceitaTrocaLocal;
    }

    public void setAceitaTrocaLocal(String AceitaTrocaLocal) {
        this.AceitaTrocaLocal = AceitaTrocaLocal;
    }

    public String getOutrosCursos() {
        return OutrosCursos;
    }

    public void setOutrosCursos(String OutrosCursos) {
        this.OutrosCursos = OutrosCursos;
    }

    public String getRecrutamento() {
        return Recrutamento;
    }

    public void setRecrutamento(String Recrutamento) {
        this.Recrutamento = Recrutamento;
    }

    public String getAmigosEmpresa() {
        return AmigosEmpresa;
    }

    public void setAmigosEmpresa(String AmigosEmpresa) {
        this.AmigosEmpresa = AmigosEmpresa;
    }

    public String getFamiliaresEmpresa() {
        return FamiliaresEmpresa;
    }

    public void setFamiliaresEmpresa(String FamiliaresEmpresa) {
        this.FamiliaresEmpresa = FamiliaresEmpresa;
    }

    public String getQtdeFilhos() {
        return QtdeFilhos;
    }

    public void setQtdeFilhos(String QtdeFilhos) {
        this.QtdeFilhos = QtdeFilhos;
    }

    public String getIdadeFilhos() {
        return IdadeFilhos;
    }

    public void setIdadeFilhos(String IdadeFilhos) {
        this.IdadeFilhos = IdadeFilhos;
    }

    public String getBensProprios() {
        return BensProprios;
    }

    public void setBensProprios(String BensProprios) {
        this.BensProprios = BensProprios;
    }
}

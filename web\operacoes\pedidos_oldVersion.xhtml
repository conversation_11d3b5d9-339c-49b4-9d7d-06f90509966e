<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css"  href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css"  href="../assets/css/pedidos.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .ui-inputtext{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formPesquisar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formCadastroComposicao"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formPedidosImportados"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formPreOrders"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formImportarPedidos"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px){
                    .DataGrid [role="columnheader"] > span {
                        top: -1px !important;
                        position: relative !important;
                    }
                }

            </style>
        </h:head>

        <h:body id="h" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{pedido.persistencia(login.pp)}" />

            </f:metadata>

            <p:growl id="msgs" />

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_satmob_contratosG.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Pedidos}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span>
                                            <h:outputText value="#{pedido.dataSelecionada1}" converter="conversorDateDia" />
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">
                                        #{pedido.nomeFilial}
                                        <label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label>
                                    </label>
                                    <label class="FilialEndereco">#{pedido.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{pedido.filiais.bairro}, #{pedido.filiais.cidade}/#{pedido.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-12 col-xs-12">

                                    <!--Botão voltar-->
                                    <p:commandLink action="#{pedido.dataAnterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png"/>
                                    </p:commandLink>

                                    <p:datePicker id="calendario" value="#{pedido.dataSelecionada1}" readonlyInput="true"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                  monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{pedido.selecionarData}" update="main cabecalho" />
                                    </p:datePicker>

                                    <!--Botão avançar-->
                                    <p:commandLink action="#{pedido.dataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png"/>
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <!--Centro-->
                <h:form id="main">
                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:dataTable id="tabela" 
                                     value="#{pedido.pedidos}" 
                                     selection="#{pedido.pedidoSelecionado}" 
                                     paginator="true" 
                                     rows="15" 
                                     lazy="true"
                                     reflow="true" 
                                     rowsPerPageTemplate="5, 10, 15, 20, 25"
                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Pedidos}"
                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                     var="lista" styleClass="tabela" selectionMode="single" emptyMessage="#{localemsgs.SemRegistros}"
                                     class="tabela" scrollWidth="100%" style="background: white;
                                     padding:0px !important; margin:0px !important; height:100% !important;
                                     max-height:100% !important;"
                                     rowStyleClass="#{lista.flag_Excl eq '*' ? 'ciano'
                                                      : lista.azul ? ' azul'
                                                      : lista.situacao eq 'PD' ? 'inativo'
                                                      : lista.situacao eq 'OK' ? 'verde'
                                                      : null}">
                            <p:ajax event="rowDblselect" listener="#{pedido.dblSelect}" update="formCadastrar msgs" />
                            <p:column headerText="#{localemsgs.Numero}" style="min-width: 80px !important; width: 80px !important;" class="text-center">
                                <h:outputText value="#{lista.numero}" title="#{lista.numero}" class="text-center" >
                                    <f:convertNumber pattern="0000"  />
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.CodFil}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.codFil}" title="#{lista.codFil}" class="text-center">
                                    <f:convertNumber pattern="0000" />
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.data}" title="#{lista.data}" converter="conversorData" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Tipo}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.tipo}" title="#{lista.tipo}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.classificacao}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.classifSrv}" title="#{lista.classifSrv}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Origem}" style="min-width: 300px !important;width: 300px !important; max-width: 300px !important;" class="text-center">
                                <h:outputText value="#{lista.NRed1}" title="#{lista.NRed1}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora1O}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.hora1O}" converter="conversorHora" title="#{lista.hora1O}" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora2O}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.hora2O}" converter="conversorHora" title="#{lista.hora2O}" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Regiao1}" style="min-width: 150px !important; width: 150px !important;" class="text-center">
                                <h:outputText value="#{lista.regiao1}" title="#{lista.regiao1}" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Destino}" style="min-width: 300px !important; width: 300px !important;" class="text-center">
                                <h:outputText value="#{lista.NRed2}" title="#{lista.NRed2}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Regiao2}" style="min-width: 150px !important; width: 150px !important;" class="text-center">
                                <h:outputText value="#{lista.regiao2}" title="#{lista.regiao2}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora1D}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.hora1D}" converter="conversorHora" title="#{lista.hora1D}" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora2D}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.hora2D}" converter="conversorHora" title="#{lista.hora2D}" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Solicitante}" style="min-width: 200px !important; width: 200px !important;" class="text-center">
                                <h:outputText value="#{lista.solicitante}" title="#{lista.solicitante}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.PedidoCliente}" style="min-width: 150px !important; width: 150px !important;" class="text-center">
                                <h:outputText value="#{lista.pedidoCliente}" title="#{lista.pedidoCliente}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.TipoMoeda}" style="min-width: 150px !important; width: 150px !important;" class="text-center">
                                <h:outputText value="#{lista.tipoMoeda}" title="#{lista.tipoMoeda}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Valor}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.valor}" title="#{lista.valor}" converter="conversormoedasemsimbolo" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Obs}" style="min-width: 300px !important; width: 300px !important;" class="text-center">
                                <h:outputText value="#{lista.obs}" title="#{lista.obs}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.OS}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.OS}" title="#{lista.OS}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Guia}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.OS}" title="#{lista.OS}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.QtdeGTV}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.OS}" title="#{lista.OS}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.OperIncl}" style="min-width: 150px !important; width: 150px !important;" class="text-center">
                                <h:outputText value="#{lista.operIncl}" title="#{lista.operIncl}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Incl}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.dt_Incl}" title="#{lista.dt_Incl}" converter="conversorData" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Incl}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.hr_Incl}" title="#{lista.dt_Incl}" converter="conversorHora" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Operador}" style="min-width: 150px !important; width: 150px !important;" class="text-center">
                                <h:outputText value="#{lista.operador}" title="#{lista.operador}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Alter}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.dt_Alter}" title="#{lista.dt_Alter}" converter="conversorData" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Alter}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.hr_Alter}" title="#{lista.hr_Alter}" converter="conversorHora" class="text-center" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Situacao}" style="min-width: 150px !important; width: 150px !important;" class="text-center">
                                <h:outputText value="#{lista.situacao}" title="#{lista.situacao}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SeqRota}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.seqRota}" title="#{lista.seqRota}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Parada}" style="min-width: 100px !important; width: 100px !important;" class="text-center">
                                <h:outputText value="#{lista.parada}" title="#{lista.parada}" class="text-center"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 240px !important; background: transparent; height:200px !important;" id="botoes">
                        <p:remoteCommand name="rcExcluirPedido" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs main" 
                                         actionListener="#{pedido.excluirPedido()}" />                            

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Novo}"
                                           actionListener="#{pedido.preCadastro}"
                                           update="formCadastrar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}"
                                           update="formCadastrar msgs"
                                           actionListener="#{pedido.preEdicao}">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Excluir}"
                                           update="formCadastrar msgs"
                                           actionListener="#{pedido.preExcluir}">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                           oncomplete="PF('dlgPesquisar').show()"
                                           update="formPesquisar cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40" />
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.UploadPreOrder}" rendered="#{login.pp.empresa.contains('CONFEDERAL')}"
                                           action="#{pedido.verificaClienteOrigemPedido}"
                                           update="formUpload msgs">
                                <p:graphicImage url="../assets/img/icone_upload.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink title="#{localemsgs.UploadPedido}"
                                           rendered="#{!login.pp.empresa.contains('CONFEDERAL')}"
                                           action="#{pedido.preImportacaoPedido}"
                                           update="formImportarPedidos msgs">
                                <p:graphicImage url="../assets/img/icone_upload.png" height="40"/>
                            </p:commandLink>
                        </div>  

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.GeracaoSuprimentos}"
                                           action="#{pedido.preGeracaoGTV}"
                                           update="msgs">
                                <p:graphicImage url="../assets/img/icone_satmob_suprimento.png" height="40" />
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}"
                                           action="#{pedido.limpaFiltros}"
                                           update=":main:tabela :msgs :cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>       

                        <div style="padding-bottom: 10px">    
                            <p:commandLink title="#{localemsgs.Pedidos}/#{localemsgs.Refeicoes}" rendered="#{login.pp.empresa eq 'SATMAXIMA'}" 
                                           action="../refeicoes/pedidos_refeicao.xhtml?faces-redirect=true">
                                <p:graphicImage url="../assets/img/icone_cadastros.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <h:form id="formCadastrar" class="form-inline">
                    <p:dialog widgetVar="dlgCadastrar"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important">

                        <f:facet name="header">
                            <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Pedido}" style="color:#022a48"/>
                            <h:outputText value=" #{pedido.pedidoSelecionado.numero.toBigInteger()}" style="color:#022a48" rendered="#{pedido.flag eq 2}"/>
                            <h:outputText value=" - " style="color:#022a48"/>
                            <h:outputText value="#{pedido.pedidoSelecionado.situacao}" class="#{pedido.pedidoSelecionado.situacao eq 'OK' ? 'verde' : 'inativo'}"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; margin-top:-10px !important; z-index:999 !important; height:calc(100% - 20px) !important" class="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                            </p:confirmDialog>

                            <div class="col-md-3">
                                <p:outputLabel for="filial" value="#{localemsgs.Filial}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <p:selectOneMenu id="filial" value="#{pedido.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}"/>
                                    <p:ajax event="itemSelect" listener="#{pedido.selecionarFilial}" update="msgs cadastrar"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-2">
                                <p:outputLabel for="tipo" value="#{localemsgs.Tipo}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <p:selectOneMenu id="tipo" value="#{pedido.pedidoSelecionado.tipo}" disabled="#{null ne pedido.pedidoSelecionado.numero}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}" 
                                                 filter="true" filterMatchMode="contains" style="width: 100%">
                                    <f:selectItem itemValue="T" itemLabel="#{localemsgs.Transporte}"/>
                                    <f:selectItem itemValue="C" itemLabel="#{localemsgs.Cancelamento}"/>
                                    <f:selectItem itemValue="M" itemLabel="#{localemsgs.Material}"/>
                                    <f:selectItem itemValue="O" itemLabel="#{localemsgs.Ocorrencia}"/>
                                    <p:ajax event="itemSelect" oncomplete="PF('dlgCadastrar').initPosition()"
                                            update="panelOrigem panelDestino panelValor panelOS tabs:panelComposicoes msgs"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-2">
                                <p:outputLabel for="classifSrv" value="#{localemsgs.ClassifSrv}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <p:selectOneMenu id="classifSrv" value="#{pedido.pedidoSelecionado.classifSrv}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.ClassifSrv}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%">
                                    <f:selectItem itemValue="R" itemLabel="#{localemsgs.Rotineiro}"/>
                                    <f:selectItem itemValue="V" itemLabel="#{localemsgs.Eventual}"/>
                                    <f:selectItem itemValue="E" itemLabel="#{localemsgs.Especial}"/>
                                    <f:selectItem itemValue="A" itemLabel="#{localemsgs.AssitTecnica}"/>
                                    <f:selectItem itemValue="P" itemLabel="#{localemsgs.Preliminar}"/>
                                    <f:selectItem itemValue="I" itemLabel="#{localemsgs.Intermediaria}"/>
                                    <f:selectItem itemValue="J" itemLabel="#{localemsgs.ProjetoEspecial}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-5" style="padding:0px !important">
                                <div class="col-md-12" style="padding:5px 0px 0px 5px !important">
                                    <p:outputLabel for="data" value="#{localemsgs.Data}">
                                        <font style="color:red; font-weight:bold">(*)</font>
                                    </p:outputLabel>
                                </div>
                                <div class="col-md-6" style="padding-top:0px !Important">
                                    <p:datePicker id="data" value="#{pedido.pedidoSelecionado.data}"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario2" showIcon="true"
                                                  required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  style="width: 100%" converter="conversorData" >
                                        <p:ajax event="dateSelect" update="dataDia classifSrv" listener="#{pedido.selecionarDadaPedido}"/>
                                    </p:datePicker>   

                                </div>
                                <div class="col-md-6" style="padding-top:0px !Important">
                                    <p:inputText id="dataDia" value="#{pedido.pedidoSelecionado.data}" disabled="true"
                                                 converter="conversorDiaSemana2" style="width: 100%;"/>
                                </div>
                            </div>

                            <!-- ITENS PARA REAPROVEITAMENTO DA TELA DE CLIENTES -->
                            <!-- Início -->
                            <h:inputHidden value="#{pedido.pedidoSelecionado.codCliReaproveitaOrigem}" id="txtReaproveitaOri" />
                            <h:inputHidden value="#{pedido.pedidoSelecionado.codCliReaproveitaDestino}" id="txtReaproveitaDst" />
                            <p:remoteCommand name="rc" partialSubmit="true" 
                                             process="@this,txtReaproveitaOri,txtReaproveitaDst" 
                                             update="msgs origem origemCod origemEnde origemRegiao origemRegiaoDesc origemBairro origemCidade os" 
                                             actionListener="#{pedido.reaproveitaClienteOrigem()}" />                            
                            <p:remoteCommand name="rc1" partialSubmit="true" 
                                             process="@this,txtReaproveitaOri,txtReaproveitaDst" 
                                             update="msgs destino destinoCod destinoEnde destinoRegiao destinoRegiaoDesc destinoBairro destinoCidade os NRed NRedFat NRedDst descricao" 
                                             actionListener="#{pedido.reaproveitaClienteDestino()}" />
                            <!-- FIM -->


                            <p:panel id="panelOrigem">
                                <div class="col-md-12" style="padding: 0px 0px 0px 0px !important;">
                                    <div class="col-md-7" style="padding-bottom:0px !important;">
                                        <p:outputLabel for="origem" value="#{localemsgs.Origem}">
                                            <font style="color:red; font-weight:bold">(*)</font>
                                        </p:outputLabel>

                                        <label id="btPesquisarClienteOrigem" style="display:#{pedido.pedidoSelecionado.situacao.equals('PD')  ?'':'none'}; margin-top: -2px; position:absolute !important; font-size: 8pt; padding:1px 8px 1px 8px !important; text-align:center; border-radius:30px; background-color:steelblue; color:#FFF; cursor:pointer; margin-left: 12px; width: 80px; height:18px"><i class="fa fa-search"></i>&nbsp;&nbsp;#{localemsgs.Pesquisar}</label>
                                    </div>
                                    <div class="col-md-5" style="padding-bottom:0px !important;">
                                        <p:outputLabel for="hora1O" value="#{localemsgs.ColetarEntre}">
                                            <font style="color:red; font-weight:bold">(*)</font>
                                        </p:outputLabel>
                                    </div>
                                </div>
                                <div class="col-md-2" style="padding-top:0px !important;">
                                    <p:inputText id="origemCod" value="#{pedido.clienteOrigem.codigo}" disabled="true" 
                                                 style="width: 100%; background-color:#EEE; text-align:center !important" />
                                </div>
                                <div class="col-md-5" style="padding-top:0px !important;">
                                    <p:autoComplete id="origem" value="#{pedido.clienteOrigem}" completeMethod="#{pedido.listarCliOri}" autocomplete="off"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Origem}"
                                                    styleClass="origem" label="#{localemsgs.Origem}" forceSelection="true" minQueryLength="3"
                                                    queryDelay="500" scrollHeight="200" var="cont" itemValue="#{cont}" itemLabel="#{cont.NRed}"
                                                    inputStyle="font-weight: bold; color:#000 !important;border-color:orangered !important; background-color:lightyellow"
                                                    style="min-width:100% !important; width:100% !important; max-width:100% !important"
                                                    rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                    required="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                    emptyMessage="#{localemsgs.SemRegistros}">
                                        <p:ajax event="itemSelect" listener="#{pedido.selecionarOrigem}" 
                                                update="msgs origemCod origemEnde origemRegiao
                                                origemRegiaoDesc origemBairro origemCidade os"/>
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{pedido.clienteOrigemList}" />
                                    </p:autoComplete>
                                    <p:autoComplete id="cacenlamento" value="#{pedido.clienteCancelamento}" completeMethod="#{pedido.listarCliCancelamento}" autocomplete="off"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Origem}"
                                                    styleClass="origem" label="#{localemsgs.Origem}" forceSelection="true" minQueryLength="3"
                                                    queryDelay="500" scrollHeight="200" var="cont" itemValue="#{cont}" 
                                                    itemLabel="#{cont.clientes.NRed}"
                                                    inputStyle="font-weight: bold; color:#000 !important;border-color:orangered !important; background-color:lightyellow"
                                                    style="min-width:100% !important; width:100% !important; max-width:100% !important"
                                                    rendered="#{pedido.pedidoSelecionado.tipo eq 'C'}"
                                                    required="#{pedido.pedidoSelecionado.tipo eq 'C'}"
                                                    emptyMessage="#{localemsgs.SemRegistros}">
                                        <f:facet name="itemtip">
                                            <h:panelGrid columns="2" cellpadding="7">
                                                <f:facet name="header">
                                                    <h:outputText value="#{localemsgs.Cliente}: " />
                                                    <h:outputText value="#{cont.clientes.NRed}" />
                                                </f:facet>

                                                <h:outputText value="#{localemsgs.Rota}: " />
                                                <h:outputText value="#{cont.rotas.rota}" />

                                                <h:outputText value="#{localemsgs.Parada}: " />
                                                <h:outputText value="#{cont.rt_Perc.parada}" />

                                                <h:outputText value="#{localemsgs.Horario}: " />
                                                <h:outputText value="#{cont.rt_Perc.hora1}" converter="conversorHora"/>
                                            </h:panelGrid>
                                        </f:facet>
                                        <p:ajax event="itemSelect" listener="#{pedido.selecionarCliCancelamento}" 
                                                update="msgs origemCod origemEnde origemRegiao
                                                origemRegiaoDesc origemBairro origemCidade os
                                                hora1O hora2O"/>
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{pedido.clienteCancelamentoList}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-5" style="padding:0px !important;">
                                    <div class="col-md-6" style="padding-top: 0px !important;">
                                        <p:selectOneMenu id="hora1O" value="#{pedido.pedidoSelecionado.hora1O}" editable="true" 
                                                         converter="conversorHora" validator="ValidadorHora"
                                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                                         filter="true" filterMatchMode="contains" style="width: 100%">
                                            <f:selectItems value="#{horas.obterHorario()}" />
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-6" style="padding-top: 0px !important;">
                                        <p:selectOneMenu id="hora2O" value="#{pedido.pedidoSelecionado.hora2O}" editable="true" 
                                                         converter="conversorHora" validator="ValidadorHora"
                                                         required="#{pedido.pedidoSelecionado.tipo ne 'C'}" 
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                                         filter="true" filterMatchMode="contains" style="width: 100%">
                                            <f:selectItems value="#{horas.obterHorario()}" />
                                        </p:selectOneMenu>
                                    </div>
                                </div>
                                <div class="col-md-2" style="padding-top: 0px !important;"></div>
                                <div class="col-md-10" style="padding: 0px !important;">
                                    <div class="col-md-6" style="padding-top: 0px !important;">
                                        <p:inputText id="origemEnde" value="#{pedido.clienteOrigem.ende}" disabled="true"
                                                     style="width: 100%;background-color:#EEE;"/>
                                    </div>
                                    <div class="col-md-1" style="padding-top: 0px !important;">
                                        <p:inputText id="origemRegiao" value="#{pedido.clienteOrigem.regiao}" disabled="true" 
                                                     style="width: 100%;background-color:#EEE;"/>
                                    </div>
                                    <div class="col-md-5" style="padding-top: 0px !important;">
                                        <p:inputText id="origemRegiaoDesc" value="#{pedido.clienteOrigem.interfExt}" disabled="true" 
                                                     style="width: 100%;background-color:#EEE;"/>
                                    </div>
                                </div>
                                <div class="col-md-2" style="padding-top: 0px !important;"></div>
                                <div class="col-md-5" style="padding: 0px 5px 0px 5px !important;">
                                    <p:inputText id="origemBairro" value="#{pedido.clienteOrigem.bairro}" disabled="true"
                                                 style="width: 100%;background-color:#EEE;"/>
                                </div>
                                <div class="col-md-5" style="padding: 0px 5px 0px 5px !important;">
                                    <p:inputText id="origemCidade" value="#{pedido.clienteOrigem.cidade}" disabled="true"
                                                 style="width: 100%;background-color:#EEE;"/>
                                </div>
                            </p:panel>

                            <p:panel id="panelDestino">
                                <div class="col-md-12" style="padding: 0px 0px 0px 0px !important;">
                                    <div class="col-md-7" style="padding-bottom:0px !important;">
                                        <p:outputLabel for="destino" value="#{localemsgs.Destino}" rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}">
                                            <font style="color:red; font-weight:bold">(*)</font>
                                        </p:outputLabel>
                                        <label id="btPesquisarClienteDestino" style="display:#{pedido.pedidoSelecionado.situacao.equals('PD') and pedido.pedidoSelecionado.tipo ne 'C'  ?'':'none'}; margin-top: -2px; position:absolute !important; font-size: 8pt; padding:1px 8px 1px 8px !important; text-align:center; border-radius:30px; background-color:steelblue; color:#FFF; cursor:pointer; margin-left: 12px; width: 80px; height:18px"><i class="fa fa-search"></i>&nbsp;&nbsp;#{localemsgs.Pesquisar}</label>
                                    </div>
                                    <div class="col-md-5" style="padding-bottom:0px !important;">
                                        <p:outputLabel for="hora1D" value="#{localemsgs.EntregarEntre}" rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}">
                                            <font style="color:red; font-weight:bold">(*)</font>
                                        </p:outputLabel>
                                    </div>
                                </div>
                                <div class="col-md-2" style="padding-top:0px !important;">
                                    <p:inputText id="destinoCod" value="#{pedido.clienteDestino.codigo}" disabled="true"
                                                 rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                 style="width: 100%; background-color:#EEE; text-align:center !important"/>
                                </div>
                                <div class="col-md-5" style="padding-top:0px !important;">
                                    <p:autoComplete id="destino" value="#{pedido.clienteDestino}" completeMethod="#{pedido.listarCliDst}" autocomplete="off"
                                                    rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                    required="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Destino}"
                                                    styleClass="destino" label="#{localemsgs.destino}"
                                                    inputStyle="font-weight: bold; color:#000 !important;border-color:orangered !important; background-color:lightyellow"
                                                    style="min-width:100% !important; width:100% !important; max-width:100% !important"
                                                    forceSelection="true" minQueryLength="3"
                                                    queryDelay="500" scrollHeight="200" var="cont" itemValue="#{cont}" itemLabel="#{cont.NRed}"
                                                    emptyMessage="#{localemsgs.SemRegistros}">
                                        <p:ajax event="itemSelect" listener="#{pedido.selecionarDestino}"
                                                update="msgs destinoCod destinoEnde destinoRegiao destinoRegiaoDesc destinoBairro destinoCidade
                                                os NRed NRedFat NRedDst descricao"/>
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{pedido.clienteDestinoList}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-5" style="padding:0px !important;">
                                    <div class="col-md-6" style="padding-top: 0px !important;">
                                        <p:selectOneMenu id="hora1D" value="#{pedido.pedidoSelecionado.hora1D}" editable="true" 
                                                         rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                         converter="conversorHora" validator="ValidadorHora"
                                                         required="#{pedido.pedidoSelecionado.tipo ne 'C'}" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.HorarioEntrega}"
                                                         filter="true" filterMatchMode="contains" style="width: 100%">
                                            <f:selectItems value="#{horas.obterHorario()}" />
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-6" style="padding-top: 0px !important;">
                                        <p:selectOneMenu id="hora2D" value="#{pedido.pedidoSelecionado.hora2D}" editable="true" 
                                                         converter="conversorHora" validator="ValidadorHora"
                                                         rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                         required="#{pedido.pedidoSelecionado.tipo ne 'C'}" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.HorarioEntrega}"
                                                         filter="true" filterMatchMode="contains" style="width: 100%">
                                            <f:selectItems value="#{horas.obterHorario()}" />
                                        </p:selectOneMenu>
                                    </div>
                                </div>
                                <div class="col-md-2" style="padding-top: 0px !important;"></div>
                                <div class="col-md-10" style="padding: 0px !important;">
                                    <div class="col-md-6" style="padding-top: 0px !important;">
                                        <p:inputText id="destinoEnde" value="#{pedido.clienteDestino.ende}" disabled="true"
                                                     rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                     style="width: 100%;"/>
                                    </div>
                                    <div class="col-md-1" style="padding-top: 0px !important;">
                                        <p:inputText id="destinoRegiao" value="#{pedido.clienteDestino.regiao}" disabled="true"
                                                     rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                     style="width: 100%;"/>
                                    </div>
                                    <div class="col-md-5" style="padding-top: 0px !important;">
                                        <p:inputText id="destinoRegiaoDesc" value="#{pedido.clienteDestino.interfExt}" disabled="true" 
                                                     rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                     style="width: 100%;"/>
                                    </div>
                                </div>
                                <div class="col-md-2" style="padding: 0px 0px 7px 0px !important;"></div>
                                <div class="col-md-5" style="padding: 0px 5px 7px 5px !important;">
                                    <p:inputText id="destinoBairro" value="#{pedido.clienteDestino.bairro}" disabled="true"
                                                 rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                 style="width: 100%;"/>
                                </div>
                                <div class="col-md-5" style="padding: 0px 5px 7px 5px !important;">
                                    <p:inputText id="destinoCidade" value="#{pedido.clienteDestino.cidade}" disabled="true" 
                                                 rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                 style="width: 100%;"/>
                                </div>
                            </p:panel>

                            <p:panel id="panelValor" class="col-md-2" style="padding: 0px 5px 7px 0px !important; background-color:transparent">
                                <p:outputLabel for="valor" value="#{localemsgs.Valor}"
                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"/>
                                <p:inputNumber id="valor" value="#{pedido.pedidoSelecionado.valor}" style="width: 50%" disabled="true"
                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}" />
                            </p:panel>
                            <div class="col-md-5" style="padding: 0px 5px 7px 5px !important;">
                                <p:outputLabel for="solicitante" value="#{localemsgs.Solicitante}"/>
                                <p:inputText id="solicitante" value="#{pedido.pedidoSelecionado.solicitante}" style="width: 100%">
                                    <p:watermark for="solicitante" value="#{localemsgs.Solicitante}"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-5" style="padding: 0px 5px 7px 5px !important;">
                                <p:outputLabel for="pedidoCliente" value="#{localemsgs.Pedido} #{localemsgs.Cliente}"/>
                                <p:inputText id="pedidoCliente" value="#{pedido.pedidoSelecionado.pedidoCliente}"  style="width: 100%">
                                    <p:watermark for="pedidoCliente" value="#{localemsgs.Pedido} #{localemsgs.Cliente}"/>
                                </p:inputText>
                            </div>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2, ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank" 
                                         style="padding-bottom:0px !important;bottom:0px !important;margin-bottom:0px !important;">
                                <p:outputLabel for="obs" value="#{localemsgs.Obs}"/>
                                <p:inputTextarea id="obs" value="#{pedido.pedidoSelecionado.obs}" label="#{localemsgs.Obs}"
                                                 style="width: 100%" rows="2" maxlength="80"/>
                                <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                            </p:panelGrid>

                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-2,ui-grid-col-3"
                                         layout="grid" styleClass="ui-panelgrid-blank" id="panelOS"
                                         style="padding-top:0px !important;top:0px !important;margin-top:0px !important;">
                                <p:outputLabel for="os" value="#{localemsgs.OS}"
                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <p:column>
                                    <p:inputText id="os" value="#{pedido.pedidoSelecionado.OS}" 
                                                 rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                 style="width: calc(100% - 30px) !important; min-width: auto !important; text-align: right"/>  
                                    <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{pedido.pesquisarOS}" update="msgs" process="@this os"
                                                   rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                    </p:commandLink>    
                                </p:column>

                                <p:outputLabel for="NRed" value="#{localemsgs.Trajeto}" style="float:right"
                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"/>
                                <p:inputText id="NRed" value="#{pedido.osSelecionada.NRed}" disabled="true" style="width: 100%;"
                                             rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"/>
                                <p:inputText id="NRedDst" value="#{pedido.osSelecionada.NRedDst}" disabled="true" style="width: 100%;"
                                             rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"/>



                                <p:outputLabel for="tipoMoeda" value="#{localemsgs.TipoMoeda}" 
                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <p:selectOneMenu id="tipoMoeda" value="#{pedido.pedidoSelecionado.tipoMoeda}"
                                                 filter="false" filterMatchMode="contains"
                                                 rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                 required="#{pedido.pedidoSelecionado.tipo ne 'C'}" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoMoeda}"
                                                 style="width: 100%; background-color: honeydew !important; background: honeydew !important; border: thin solid forestgreen; color: #000 !important">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                    <f:selectItem itemLabel="(EUR) #{localemsgs.MoedaEuro}" itemValue="EUR" />
                                    <f:selectItem itemLabel="(USD) #{localemsgs.MoedaDolar}" itemValue="USD" />
                                    <f:selectItem itemLabel="(GBP) #{localemsgs.MoedaBritaniva}" itemValue="GBP" />
                                    <f:selectItem itemLabel="(CLP) #{localemsgs.MoedaPesoChileno}" itemValue="CLP" />
                                    <f:selectItem itemLabel="(COP) #{localemsgs.MoedaPesoColombiano}" itemValue="COP" />
                                    <f:selectItem itemLabel="(MXN) #{localemsgs.MoedaMexico}" itemValue="MXN" />
                                    <f:selectItem itemLabel="(R$) #{localemsgs.MoedaReal}" itemValue="R$" />
                                </p:selectOneMenu>




                                <p:outputLabel for="NRedFat" value="#{localemsgs.Faturar}" style="float:right"
                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"/>
                                <p:inputText id="NRedFat" value="#{pedido.osSelecionada.NRedFat}" disabled="true" style="width: 100%;"
                                             rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"/>
                                <p:inputText id="descricao" value="#{pedido.osSelecionada.descricao}" disabled="true" style="width: 100%;"
                                             rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"/>

                            </p:panelGrid>

                            <p:tabView id="tabs" activeIndex="0" dynamic="true" cache="true" orientation="top" style="padding:0px !important; background-color:#EEE;">
                                <p:tab title="#{localemsgs.Composicoes}">
                                    <p:panel id="panelComposicoes" style="background-color:transparent !important;">
                                        <div style="display: flex;">
                                            <div class="col-md-6" style="display:inline; border:thin solid #DDD; padding:2px 2px 0px 2px !important; 
                                                 max-height:200px !important; margin-top:8px !important; background-color:#FFF !important;">
                                                <p:panel rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}" style="background-color:transparent !important">
                                                    <div class="col-md-12" style="display:inline; border:thin solid #DDD; background-color:transparent !important; padding:2px 2px 0px 2px !important;
                                                         height:195px !important; margin-top:0px !important">
                                                        <h:outputText style="position:absolute; z-index:2; top:2px; left:2px; width:calc(100% - 4px);
                                                                      background:linear-gradient(to bottom, #505050, #202020); text-align: center; font-weight:bold; 
                                                                      font-size:9pt; padding:5px 6px 7px 6px; color:#FFF" value="#{localemsgs.ComposicoesCedulas}"
                                                                      rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}"/>
                                                        <div class="col-md-1#{pedido.pedidoSelecionado.situacao.equals('PD') ?'1':'2'}" style="display:inline; padding:0px !important;margin-top:31px; height:163px; background-color:transparent !important;
                                                             width:calc(100% - #{pedido.pedidoSelecionado.situacao.equals('PD') ?'60':'0'}px) !important;">
                                                            <p:panel style="overflow:hidden !important; padding:0px !important; background-color:transparent !important" 
                                                                     rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}">
                                                                <p:dataTable rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                                             id="tabelaCedulaMoeda"
                                                                             value="#{pedido.listaPedidoCedula}"
                                                                             rowKey="#{lista.codigo}"
                                                                             paginator="false"
                                                                             paginatorTemplate="false"
                                                                             lazy="true"
                                                                             reflow="true"
                                                                             var="lista"
                                                                             selection="#{pedido.pedidoCedulaMoeda}"
                                                                             styleClass="tabela"
                                                                             selectionMode="single"
                                                                             emptyMessage="#{localemsgs.SemRegistros}"
                                                                             scrollable="true"
                                                                             class="tabela DataGrid"
                                                                             scrollWidth="100%"
                                                                             style="display: flex; flex-direction: column; font-size: 12px; background: white; 
                                                                             padding:0px !important; margin:0px !important;min-height:100% !important; 
                                                                             max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important; 
                                                                             background-color:#FFF !important;margin-top:-10px !important"
                                                                             >
                                                                    <p:ajax listener="#{pedido.onRowComposicaoSelect}" event="rowSelect" />
                                                                    <p:ajax listener="#{pedido.dblSelectComposicao}" event="rowDblselect" update="formCadastroComposicao:cadastrar msgs" />

                                                                    <p:column headerText="#{localemsgs.Tipo}" class="text-center">
                                                                        <h:outputText value="#{lista.tipo eq 'C'?localemsgs.Cedula: localemsgs.Moeda}" class="text-center" />
                                                                    </p:column>
                                                                    <p:column headerText="#{localemsgs.Cedula}" class="text-center">
                                                                        <h:outputText value="#{lista.codigo}" converter="conversormoedasemsimbolo" class="text-center" />
                                                                    </p:column>
                                                                    <p:column headerText="#{localemsgs.Qtde}"  class="text-center">
                                                                        <h:outputText value="#{lista.qtde}"  class="text-center"/>
                                                                    </p:column>
                                                                    <p:column headerText="#{localemsgs.Valor}" class="text-center">
                                                                        <h:outputText value="#{lista.valor}" converter="conversormoedasemsimbolo" class="text-center"/>
                                                                    </p:column>
                                                                </p:dataTable>
                                                            </p:panel>
                                                        </div>
                                                        <div class="col-md-1" style="display:inline; padding:0px !important;margin-top:31px; height:158px; width:58px !important; margin-left:2px; border:thin solid #DDD;background-color:#EEE;display:#{pedido.pedidoSelecionado.situacao.equals('PD') ?'':'none'}">
                                                            <p:panel id="botoesCadastro" style="width:100% !important; width:100%; text-align:center; margin-top:10px !important; background: transparent"
                                                                     rendered = "#{pedido.pedidoSelecionado.situacao.equals('PD')}">
                                                                <p:commandLink title="#{localemsgs.Adicionar}"
                                                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}"
                                                                               partialSubmit="true" process="@this"
                                                                               update="formCadastroComposicao:cadastrar tabelaCedulaMoeda tabelaMoeda msgs"
                                                                               actionListener="#{pedido.buttonActionComposicaoCedulas}" >
                                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40" />
                                                                </p:commandLink>

                                                                <p:commandLink title="#{localemsgs.Editar}" actionListener="#{pedido.buttonActionComposicao}"
                                                                               partialSubmit="true" process="@this"
                                                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}"
                                                                               update="formCadastroComposicao:cadastrar msgs">
                                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="40" height="40" style="margin-top:10px;" />
                                                                </p:commandLink>

                                                                <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{pedido.excluirItemComposicao}"
                                                                               partialSubmit="true" process="@form"
                                                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}"
                                                                               update="tabelaCedulaMoeda msgs">
                                                                    <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="40" height="40" style="margin-top:10px;" />
                                                                </p:commandLink>
                                                            </p:panel>
                                                        </div>
                                                    </div>
                                                </p:panel>
                                            </div>

                                            <div class="col-md-6" style="display:inline; border:thin solid #DDD; padding:2px 2px 0px 2px !important; 
                                                 max-height:200px !important; margin-top:8px !important; background-color:#FFF !important;">
                                                <p:panel  rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}">                                    
                                                    <div class="col-md-12" style="display:inline; border:thin solid #DDD; padding:2px 2px 0px 2px !important; 
                                                         height:195px !important; margin-top:0px !important">
                                                        <h:outputText style="position:absolute; z-index:2; top:2px; left:2px; width:calc(100% - 4px);
                                                                      background:linear-gradient(to bottom, #505050, #202020); text-align: center; font-weight:bold; 
                                                                      font-size:9pt; padding:5px 6px 7px 6px; color:#FFF" value="#{localemsgs.ComposicoesMoedas}"
                                                                      rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}"/>
                                                        <div class="col-md-1#{pedido.pedidoSelecionado.situacao.equals('PD') ?'1':'2'}" style="display:inline; padding:0px !important;margin-top:31px; height:163px;
                                                             width:calc(100% - #{pedido.pedidoSelecionado.situacao.equals('PD') ?'60':'0'}px) !important;">
                                                            <p:panel style="overflow:hidden !important; padding:0px !important;" 
                                                                     rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}">
                                                                <p:dataTable rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                                             id="tabelaMoeda"
                                                                             value="#{pedido.listaPedidoMoeda}"                                                             
                                                                             rowKey="#{lista.codigo}"
                                                                             paginator="false"
                                                                             paginatorTemplate="false"
                                                                             lazy="true"
                                                                             reflow="true"
                                                                             var="lista"
                                                                             selection="#{pedido.pedidoCedulaMoeda}"
                                                                             styleClass="tabela"
                                                                             selectionMode="single"
                                                                             emptyMessage="#{localemsgs.SemRegistros}"
                                                                             scrollable="true"
                                                                             class="tabela DataGrid"
                                                                             scrollWidth="100%"
                                                                             style="display: flex; flex-direction: column; font-size: 12px; background: white; 
                                                                             padding:0px !important; margin:0px !important;min-height:100% !important; 
                                                                             max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important; 
                                                                             background-color:#FFF !important;margin-top:-10px !important"
                                                                             >
                                                                    <p:ajax listener="#{pedido.onRowComposicaoSelect}" event="rowSelect" />
                                                                    <p:ajax listener="#{pedido.dblSelectComposicao}" event="rowDblselect" update="formCadastroComposicao:cadastrar msgs" />

                                                                    <p:column headerText="#{localemsgs.Tipo}" class="text-center">
                                                                        <h:outputText value="#{lista.tipo eq 'C'?localemsgs.Cedula: localemsgs.Moeda}" class="text-center" />
                                                                    </p:column>
                                                                    <p:column headerText="#{localemsgs.Moeda}" class="text-center">
                                                                        <h:outputText value="#{lista.codigo}" converter="conversormoeda" class="text-center" />
                                                                    </p:column>
                                                                    <p:column headerText="#{localemsgs.Qtde}"  class="text-center">
                                                                        <h:outputText value="#{lista.qtde}"  class="text-center"/>
                                                                    </p:column>
                                                                    <p:column headerText="#{localemsgs.Valor}" class="text-center">
                                                                        <h:outputText value="#{lista.valor}" converter="conversormoeda" class="text-center"/>
                                                                    </p:column>
                                                                </p:dataTable>
                                                            </p:panel>
                                                        </div>
                                                        <div class="col-md-1" style="display:inline; padding:0px !important;margin-top:31px; height:158px; width:58px !important; margin-left:2px; border:thin solid #DDD;background-color:#EEE; display:#{pedido.pedidoSelecionado.situacao.equals('PD') ?'':'none'}">
                                                            <p:panel id="botoesCadastroMoeda" style="width:100% !important; width:100%; text-align:center; margin-top:10px !important; background: transparent"
                                                                     rendered="#{pedido.pedidoSelecionado.situacao.equals('PD')}">
                                                                <p:commandLink title="#{localemsgs.Adicionar}"
                                                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}"
                                                                               partialSubmit="true" process="@this"
                                                                               update="formCadastroComposicao:cadastrar tabelaCedulaMoeda msgs"
                                                                               actionListener="#{pedido.buttonActionComposicaoMoedas}" >
                                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40" />
                                                                </p:commandLink>

                                                                <p:commandLink title="#{localemsgs.Editar}" actionListener="#{pedido.buttonActionComposicao}"
                                                                               partialSubmit="true" process="@this"
                                                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}"
                                                                               update="formCadastroComposicao:cadastrar msgs">
                                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="40" height="40" style="margin-top:10px;" />
                                                                </p:commandLink>

                                                                <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{pedido.excluirItemComposicao}"
                                                                               partialSubmit="true" process="@form"
                                                                               rendered="#{pedido.pedidoSelecionado.tipo ne 'C' and pedido.pedidoSelecionado.tipo ne 'M'}"
                                                                               update="tabelaCedulaMoeda msgs">
                                                                    <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="40" height="40" style="margin-top:10px;" />
                                                                </p:commandLink>
                                                            </p:panel>
                                                        </div>
                                                    </div>
                                                </p:panel>  
                                            </div>
                                        </div>
                                    </p:panel>
                                </p:tab>

                                <p:tab title="#{localemsgs.Guias}">
                                    <p:panel>
                                        <p:dataTable rendered="#{pedido.pedidoSelecionado.tipo ne 'C'}"
                                                     id="tabelaGuias"
                                                     value="#{pedido.listaGuias}"
                                                     rowKey="#{lista.toString()}"
                                                     paginator="false"
                                                     paginatorTemplate="false"
                                                     lazy="true"
                                                     reflow="true"
                                                     var="lista"
                                                     selection="#{pedido.guiaSelecionada}"
                                                     styleClass="tabela"
                                                     selectionMode="single"
                                                     emptyMessage="#{localemsgs.SemRegistros}"
                                                     scrollable="true"
                                                     class="tabela DataGrid"
                                                     scrollWidth="100%"
                                                     style="display: flex; flex-direction: column; font-size: 12px; background: white; 
                                                     padding:0px !important; margin:0px !important;min-height:100% !important; 
                                                     max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important; 
                                                     background-color:#FFF !important;margin-top:-10px !important"
                                                     >
                                            <p:ajax listener="#{pedido.selectGuia}"
                                                    event="rowDblselect"
                                                    update="formCadastroComposicao:cadastrar msgs"
                                                    />

                                            <p:column headerText="#{localemsgs.Origem}" class="text-center">
                                                <h:outputText value="#{lista.cliente.NRed}" class="text-center"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Guia}" class="text-center">
                                                <h:outputText value="#{lista.gtv.guia}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Serie}" class="text-center">
                                                <h:outputText value="#{lista.gtv.serie}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TotalGeral}" class="text-center">
                                                <h:outputText value="#{lista.totalGeral}" class="text-center" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TotalDN}" class="text-center">
                                                <h:outputText value="#{lista.totalDN}" class="text-center" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TotalMoeda}" class="text-center">
                                                <h:outputText value="#{lista.totalMoeda}" class="text-center" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ChequesQtde}" class="text-center">
                                                <h:outputText value="#{lista.pedido.chequesQtde}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ChequesValor}" class="text-center">
                                                <h:outputText value="#{lista.pedido.chequesValor}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.QtdTickets}" class="text-center">
                                                <h:outputText value="#{lista.pedido.ticketsQtde}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TicketsValor}" class="text-center">
                                                <h:outputText value="#{lista.pedido.ticketsValor}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.KitTrocoQtde}" class="text-center">
                                                <h:outputText value="#{lista.tesSaida.kitTrocoQtde}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Diferenca}" class="text-center">
                                                <h:outputText value="#{lista.tesSaida.diferenca}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TotalDD}" class="text-center">
                                                <h:outputText value="#{lista.tesSaida.totalDD}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ContaTes}" class="text-center">
                                                <h:outputText value="#{lista.tesSaida.contaTes}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TipoSrv}" class="text-center">
                                                <h:outputText value="#{lista.tesSaida.tipoSrv}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.CodSrv}" class="text-center">
                                                <h:outputText value="#{lista.osvig.codSrv}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Obs}" class="text-center">
                                                <h:outputText value="#{lista.pedido.obs}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Situacao}" class="text-center">
                                                <h:outputText value="#{lista.pedido.situacao}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Pedido}" class="text-center">
                                                <h:outputText value="#{lista.pedido.pedidoCliente}" class="text-center" converter="conversor0"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Retorno}" class="text-center">
                                                <h:outputText value="#{lista.cliente.retorno}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.DataPedido}" class="text-center">
                                                <h:outputText value="#{lista.dataPedido}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                                <h:outputText value="#{lista.gtv.operador}" class="text-center" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                                <h:outputText value="#{lista.gtv.dt_alter}" class="text-center" converter="conversorLocalDate"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                                <h:outputText value="#{lista.gtv.hr_Alter}" class="text-center" />
                                            </p:column>

                                        </p:dataTable>

                                        <div style="margin-left: 8px;">
                                            <span>#{localemsgs.Quantidade}:</span>
                                            <p:spacer width="20"/>
                                            <h:outputText value="#{pedido.listaGuias.size()}"
                                                          converter="conversorCodFil"/>
                                            <p:spacer width="20"/>
                                            <span>#{localemsgs.ValorTotal}:</span>
                                            <p:spacer width="20"/>
                                            <h:outputText value="#{pedido.valorTotalListaGuias}"
                                                          converter="conversormoeda"
                                                          style="color: green;"/>
                                        </div>
                                    </p:panel>
                                </p:tab>
                            </p:tabView>

                            <p:panel class="col-md-12" style="background-color:transparent !important; text-align:right; padding:12px 0px 0px 0px !important; margin:0px !important">
                                <p:commandLink id="cadastro" action="#{pedido.cadastrarPedido(true)}" 
                                               rendered="#{pedido.flag eq 1 or pedido.pedidoSelecionado.situacao.equals('PD')}"
                                               title="#{localemsgs.Cadastrar}" update="main cadastrar msgs" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </p:panel>
                        </p:panel>
                    </p:dialog>

                    <p:confirmDialog widgetVar="dlgInformarComposicoes" header="#{localemsgs.Atencao}"
                                     message="#{localemsgs.InformarComposicoes}" styleClass="dialogoPequeno">                                
                        <p:commandButton value="#{localemsgs.Nao}" onclick="PF('dlgInformarComposicoes').hide();"/>                    
                        <p:commandButton value="#{localemsgs.Sim}" oncomplete="PF('dlgInformarComposicoes').hide();" 
                                         update="main cadastrar msgs"
                                         action="#{pedido.cadastrarPedido(false)}" />

                    </p:confirmDialog>
                </h:form>

                <!-- Tela de Pesquisa -->
                <h:form id="formPesquisar">
                    <p:dialog widgetVar="dlgPesquisar" draggable="false" fitViewport="true" positionType="absolute" responsive="true"
                              modal="true" closable="true" resizable="false" dynamic="true" width="440" class="dialogoPequeno"
                              showEffect="drop" hideEffect="drop" closeOnEscape="true">
                        <f:facet name="header">
                            <img src="../assets/img/icone_pedidos.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Pesquisar}" style="color:#022a48"/>
                        </f:facet>
                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left;">
                                    <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"/>
                                </div>
                                <div style="width: 75%; float:left">
                                    <p:selectOneMenu id="codfil" value="#{pedido.pedidoPesquisa.codFilPesquisa}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial" filter="true" filterMatchMode="contains" style="width: 100%" >
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}" 
                                                       itemLabel="#{filial.descricao}" noSelectionValue="" />
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                            <div class="ui-grid-row" style="padding-bottom: 3px">
                                <div style="width: 25%; float: left;">
                                    <p:outputLabel for="codigo" value="#{localemsgs.Numero}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="codigo" value="#{pedido.pedidoPesquisa.numeroPesquisa}"
                                                 maxlength="50" style="width: 100%">
                                        <p:watermark for="codigo" value="#{localemsgs.Numero}"/>
                                    </p:inputText>
                                </div>
                            </div>
                            <div class="ui-grid-row" style="padding-bottom: 3px">
                                <div style="width: 25%; float: left;">
                                    <p:outputLabel for="origem" value="#{localemsgs.Origem}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="origem" value="#{pedido.pedidoPesquisa.NRed1}"
                                                 maxlength="50"
                                                 style="width: 100%">
                                        <p:watermark for="origem" value="#{localemsgs.Origem}"/>
                                    </p:inputText>
                                </div>
                            </div>
                            <div class="ui-grid-row" style="padding-bottom: 3px">
                                <div style="width: 25%; float: left;">
                                    <p:outputLabel for="destino" value="#{localemsgs.Destino}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="destino" value="#{pedido.pedidoPesquisa.NRed2}"
                                                 maxlength="50"
                                                 style="width: 100%">
                                        <p:watermark for="destino" value="#{localemsgs.Destino}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{pedido.pesquisaPaginada()}"
                                               oncomplete="PF('dlgPesquisar').hide()"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>

                    </p:dialog>

                </h:form>

                <!-- Form cadastro de composições -->
                <h:form class="form-inline" id="formCadastroComposicao">
                    <p:dialog widgetVar="dlgCadastroComposicao" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastroComposicao" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; min-width:250px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.Composicoes}" style="color:#022a48"/>
                        </f:facet>
                        <p:panelGrid id="cadastrar" columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="tipoComposicao" value="#{localemsgs.Tipo}"><font color="red"> (*)</font></p:outputLabel>
                            <p:selectOneMenu value="#{pedido.pedidoCedulaMoedaNovo.tipo}" style="width: 100%" required="true"
                                             id="tipoComposicao" disabled="true">
                                <p:ajax event="itemSelect" listener="#{pedido.selecionarTipoMoedaCedula}" update="formCadastroComposicao:cadastrar"/>
                                <f:selectItem itemLabel="#{localemsgs.Cedula}" itemValue="C" />
                                <f:selectItem itemLabel="#{localemsgs.Moeda}" itemValue="M" />
                            </p:selectOneMenu>


                            <p:outputLabel for="cedulaMoeda" value="#{pedido.pedidoCedulaMoedaNovo.tipo eq 'C'? localemsgs.Cedula: localemsgs.Moeda}"><font color="red"> (*)</font></p:outputLabel>
                            <p:selectOneMenu value="#{pedido.pedidoCedulaMoedaNovo.codigo}" style="width: 100%" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CedulaMoeda}"
                                             id="cedulaMoeda">
                                <f:selectItem itemLabel="#{localemsgs.Selecione}" />
                                <f:selectItems value="#{pedido.tesMoedasSelecao}" var="moedas" itemValue="#{moedas.codigo}"
                                               itemLabel="#{moedas.codigo}"  noSelectionValue="Selecione"/>
                            </p:selectOneMenu>

                            <p:outputLabel for="qtde" value="#{localemsgs.Qtde}"><font color="red"> (*)</font></p:outputLabel>
                            <p:inputNumber id="qtde" value="#{pedido.pedidoCedulaMoedaNovo.qtde}" decimalPlaces="0" style="text-align:center !important;" maxlength="3"></p:inputNumber>

                            <p:outputLabel for="valorCedulaCadastro" value="#{localemsgs.Valor}" rendered="false" />
                            <p:inputNumber id="valorCedulaCadastro" value="#{pedido.pedidoCedulaMoedaNovo.valor}" style="text-align:center !important;" rendered="false"></p:inputNumber>

                        </p:panelGrid>
                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="margin-top:8px">
                            <p:commandLink title="#{localemsgs.Salve}" id="cadastrarDieta" action="#{pedido.salvarDadosComposicao}"
                                           update="formCadastrar:tabs:tabelaCedulaMoeda formCadastrar:tabs:tabelaMoeda msgs"
                                           style="width:100%">
                                <label class="btn btn-lg btn-success" style="width:100% !important;margin-left: 0px;"><i class="fa fa-save"></i>&nbsp;#{localemsgs.Salve}</label>
                            </p:commandLink>
                        </p:panelGrid>
                        <script>
                            $(document)
                                    .on('keyup', '.ui-inputfield[id*="qtde"]', function () {
                                        ;
                                        if ($(this).val().trim() != '')
                                            $('.ui-inputfield[id*="valorCedulaCadastro"]').val('').attr('disabled', 'disabled');
                                        else
                                            $('.ui-inputfield[id*="valorCedulaCadastro"]').val('').removeAttr('disabled');
                                    })
                                    .on('keyup', '.ui-inputfield[id*="valorCedulaCadastro"]', function () {
                                        ;
                                        if ($(this).val().trim() != '')
                                            $('.ui-inputfield[id*="qtde"]').val('').attr('disabled', 'disabled');
                                        else
                                            $('.ui-inputfield[id*="qtde"]').val('').removeAttr('disabled');
                                    });
                        </script>
                    </p:dialog>
                </h:form>

                <p:dialog widgetVar="dlgEdicaoGuia" positionType="absolute"
                          responsive="true" draggable="false"
                          styleClass="dialogo" modal="true" closable="true"
                          resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop"
                          closeOnEscape="true" class="dialogoPagina"
                          style="padding-bottom: 0px !important">
                    <f:facet name="header">
                        <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.SaidaNumerario}"/>
                    </f:facet>

                    <h:form id="formEdicaoGuia">
                        <p:panel
                            id="panel"
                            class="modalSolicitacao"
                            >
                            <div class="col-md-3">
                                <p:outputLabel for="filial" value="#{localemsgs.Filial}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <p:selectOneMenu id="filial" value="#{pedido.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 filter="true" filterMatchMode="contains"
                                                 disabled="true"
                                                 style="width: 100%">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-2">
                                <p:outputLabel for="tesouraria" value="#{localemsgs.Tesouraria}"/>

                                <h:outputText value="#{pedido.guiaEdicao.osvig.cliDst} #{pedido.guiaEdicao.origem}"
                                              id="tesouraria"/>
                            </div>

                            <div class="col-md-5" style="padding:0px !important">
                                <div class="col-md-12" style="padding:5px 0px 0px 5px !important">
                                    <p:outputLabel for="data" value="#{localemsgs.Data}"/>
                                </div>

                                <div class="col-md-6" style="padding-top:0px !Important">
                                    <p:inputText id="data" value="#{pedido.pedidoSelecionado.data}" disabled="true"
                                                 converter="conversorDia" style="width: 100%;"/>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <p:outputLabel for="tipoMov" value="#{localemsgs.Filial}"/>

                                <p:selectOneMenu id="tipoMov" value="#{pedido.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%">
                                    <f:selectItem itemValue="0" itemLabel="0 - #{localemsgs.Geral}"/>
                                    <f:selectItem itemValue="1" itemLabel="1 - #{localemsgs.Diurno}"/>
                                    <f:selectItem itemValue="2" itemLabel="2 - #{localemsgs.Noturno}"/>
                                    <f:selectItem itemValue="3" itemLabel="3 - #{localemsgs.Vespertino}"/>

                                    <!--<p:ajax event="itemSelect" listener="#{pedido.selecionarFilial}" update="msgs @this"/>-->
                                </p:selectOneMenu>
                            </div>


                            <div class="col-md-2">
                                <p:outputLabel for="guia" value="#{localemsgs.Guia}"/>

                                <p:inputText value="#{pedido.guiaEdicao.gtv.guia}"
                                             id="guia"/>

                                <p:inputText value="#{pedido.guiaEdicao.gtv.serie}"
                                             id="serie"/>
                            </div>

                            <div class="col-md-2">
                                <p:outputLabel for="tipoSrv" value="#{localemsgs.TipoSrv}"/>

                                <p:inputText value="#{pedido.guiaEdicao.tesSaida.tipoSrv}"
                                             id="tipoSrv"/>
                            </div>



                            <p:panel class="col-md-12"
                                     style="background-color:transparent !important; text-align:right; padding:12px 0px 0px 0px !important; margin:0px !important">
                                <p:commandLink id="cadastro" action="#{pedido.cadastrarPedido(true)}"
                                               title="#{localemsgs.Cadastrar}" update="main @this msgs" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </p:panel>
                        </p:panel>
                    </h:form>
                </p:dialog>

                <p:dialog widgetVar="dlgTarefaGeracaoGTV" positionType="absolute"
                          responsive="true" draggable="false"
                          styleClass="dialogo" modal="true" closable="true"
                          resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop"
                          closeOnEscape="true" class="dialogoPagina"
                          style="padding-bottom: 0px !important">
                    <f:facet name="header">
                        <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.GeracaoSuprimentos}"/>
                    </f:facet>

                    <h:form id="formTarefaGeracaoGTV">
                        <p:panel
                            id="panel"
                            class="modalSolicitacao"
                            >
                            <div class="col-md-6">
                                <p:outputLabel for="filial" value="#{localemsgs.TipoGuia}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>

                                <p:selectOneMenu id="filial"
                                                 value="#{pedido.gtvSeqSelecionado}"
                                                 converter="omnifaces.SelectItemsConverter"
                                                 required="true"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoGuia}"
                                                 filter="true"
                                                 filterMatchMode="contains"
                                                 style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>

                                    <f:selectItems value="#{pedido.listaGTVSeq}"
                                                   var="item"
                                                   itemValue="#{item}"
                                                   itemLabel="#{item.descricao}"/>

                                    <p:ajax event="itemSelect"
                                            listener="#{pedido.selecionarSequenciaGTV}"
                                            partialSubmit="true"
                                            update="msgs sequencia"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-6">
                                <p:outputLabel value="#{localemsgs.Sequencia}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>

                                <p:inputNumber id="sequencia"
                                               value="#{pedido.numGuiaSelecionado}"
                                               disabled="#{pedido.gtvSeqSelecionado.serie.equals('53')}"
                                               decimalPlaces="0"
                                               converter="conversor0"/>
                            </div>

                            <p:panel>
                                <p:dataTable id="tabelaTesFecha"
                                             value="#{pedido.listaTesFecha}"
                                             rowKey="#{lista.tesFecha.codCli}"
                                             selection="#{pedido.selectedTesFecha}"
                                             var="lista"
                                             paginator="false"
                                             paginatorTemplate="false"
                                             reflow="true"
                                             styleClass="tabela"
                                             emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true"
                                             class="tabela DataGrid"
                                             scrollWidth="100%"
                                             style="display: flex; flex-direction: column; font-size: 12px; background: white; 
                                             padding:0px !important; margin:0px !important;min-height:100% !important; 
                                             max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important; 
                                             background-color:#FFF !important;margin-top:-10px !important"
                                             >
                                    <p:column headerText="#{localemsgs.Tesouraria}" class="text-center">
                                        <h:outputText value="#{lista.clientes.NRed}: #{lista.tesFecha.codCli}" class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Qtde}" class="text-center">
                                        <h:outputText value="#{lista.qtde}" class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Valor}" class="text-center">
                                        <h:outputText value="#{lista.valor}" class="text-center" converter="conversormoeda"/>
                                    </p:column>

                                    <p:column selectionMode="multiple" style="width:16px;text-align:center"/>
                                </p:dataTable>
                            </p:panel>


                            <p:panel class="col-md-12"
                                     style="background-color:transparent !important; text-align:right; padding:12px 0px 0px 0px !important; margin:0px !important">
                                <p:commandLink id="cadastro"
                                               action="#{pedido.gerarSaidasTesouraria}"
                                               title="#{localemsgs.Cadastrar}"
                                               update="main @form msgs formSuprimentosSucesso"
                                               styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </p:panel>
                        </p:panel>
                    </h:form>
                </p:dialog>

                <h:form id="formSuprimentosSucesso">
                    <p:dialog header="Basic Dialog"
                              widgetVar="dlgSuprimentosSucesso"
                              positionType="absolute"
                              responsive="true"
                              styleClass="dialogo"
                              modal="true"
                              closable="true"
                              resizable="false"
                              dynamic="true"
                              showEffect="drop"
                              hideEffect="drop"
                              style="padding-bottom: 0px !important"
                              minHeight="40">
                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.OperacaoSucesso}" style="color:#022a48"/>
                        </f:facet>
                        <p:panelGrid id="cadastrar" columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank">

                            <p:dataList value="#{pedido.selectedTesFecha}"
                                        var="item">
                                <f:facet name="header">
                                    #{localemsgs.GuiasGeradas}
                                </f:facet>
                                <h:outputText value="#{item.clientes.NRed} - #{item.tesFecha.codCli}:"/>
                                <p:spacer width="12"/>
                                <h:outputText
                                    value="#{item.qtde} #{localemsgs.Guias}"
                                    style="font-weight:bold"/>
                            </p:dataList>
                        </p:panelGrid>

                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="margin-top:8px">
                            <p:commandLink title="#{localemsgs.Fechar}"
                                           onclick="PF('dlgSuprimentosSucesso').hide(); PF('dlgTarefaGeracaoGTV').hide();"
                                           update="msgs @form"
                                           style="width:100%">
                                <label class="btn btn-lg btn-primary" style="width:100% !important;margin-left: 0px;">#{localemsgs.Fechar}</label>
                            </p:commandLink>
                        </p:panelGrid>
                    </p:dialog> 
                </h:form>

                <!-- Form importação pedidos -->
                <h:form id="formImportarPedidos">
                    <p:dialog widgetVar="dlgImportarPedidos" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"

                              class="dialogoGrande">
                        <f:facet name="header">
                            <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.UploadPedido}" style="color:#022a48;" /> 
                        </f:facet>
                        <p:panel id="panelUpload" style="background-color: transparent" styleClass="cadastrar">

                            <p:fileUpload id="espacoUpload" auto="true" skinSimple="true" label="#{localemsgs.SelecioneArquivo}"
                                          update="formImportarPedidos:panelUpload formImportarPedidos:arquivos msgs formPreOrders" 
                                          onstart="PF('pfBlock').show();" oncomplete="PF('dlgImportarPedidos').initPosition();PF('pfBlock').hide();"
                                          class="upload" multiple="true" dragDropSupport="true"
                                          fileUploadListener="#{pedido.realizarUploadPedido}" mode="advanced" 
                                          allowTypes="/(\.|\/)(txt)$/" previewWidth="10" invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                          style="height:40px; text-align: center;">
                            </p:fileUpload>
                            <div style="height:170px !important; overflow-y:auto !important; width:100%; position:relative;">
                                <p:dataTable value="#{pedido.arquivosPedidos}" scrollHeight="100%" scrollWidth="100%" 
                                             scrollable="true" rendered="#{!pedido.arquivosPedidos.isEmpty()}"
                                             var="listaDocumentos" 
                                             id="arquivos" 
                                             styleClass="tabelaArquivos DataGrid"
                                             style="font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                    <f:facet name="header">
                                        <h:outputText value="#{localemsgs.ArquivosRecentes}:"/>
                                    </f:facet>
                                    <p:column headerText="#{localemsgs.Nome}" style="width: 80%">
                                        <h:outputText value="#{listaDocumentos.name}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Tamanho}" style="width: 20%; text-align: center">
                                        <h:outputText value="#{listaDocumentos.length()}" converter="conversorKB"/>
                                    </p:column>
                                </p:dataTable>
                            </div>

                            <p:commandLink oncomplete="PF('dlgImportarPedidos').hide();" update="main"
                                           title="#{localemsgs.Enviar}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formPedidosImportados">
                    <p:dialog widgetVar="dlgPedidosImportados" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                              class="dialogoPagina">
                        <f:facet name="header">
                            <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.UploadPedido}" style="color:#022a48;" /> 
                        </f:facet>
                        <p:panel id="panelPreOrders" style="background-color: transparent" styleClass="cadastrar">

                            <h:outputText value="#{pedido.mensagemImportacao}"/>

                            <p:dataTable value="#{pedido.pedidosImportados}" scrollHeight="400" 
                                         scrollable="true" resizableColumns="true"
                                         var="listaPedidos" id="listaPedidos" styleClass="tabela DataGrid"
                                         style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;
                                         min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; 
                                         width:100% !important;">
                                <p:ajax event="rowToggle" oncomplete="PF('dlgPedidosImportados').initPosition();"/>
                                <p:column style="width:16px">
                                    <p:rowToggler rendered="#{listaPedidos.composicoes.size() gt 0}"  />
                                </p:column>
                                <p:column headerText="#{localemsgs.Data}">
                                    <h:outputText value="#{listaPedidos.pedido.dtColeta}" converter="conversorData"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Horario}">
                                    <h:outputText value="#{listaPedidos.pedido.hora1D}" converter="conversorHora"/>
                                    <h:outputText value=" - "/>
                                    <h:outputText value="#{listaPedidos.pedido.hora2D}" converter="conversorHora"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Valor}">
                                    <h:outputText value="#{listaPedidos.pedido.valor}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Origem}">
                                    <h:outputText value="#{listaPedidos.pedido.NRed1}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Destino}">
                                    <h:outputText value="#{listaPedidos.pedido.NRed2}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Obs}">
                                    <h:outputText value="#{listaPedidos.pedido.obs}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Composicoes}">
                                    <h:outputText value="#{listaPedidos.composicoes.size()}"/>
                                </p:column>

                                <p:rowExpansion>
                                    <p:dataTable value="#{listaPedidos.composicoes}"
                                                 resizableColumns="true"
                                                 var="listaLacres" id="listaLacres" styleClass="tabela DataGrid"
                                                 style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                        <p:column headerText="#{localemsgs.TipoCedula}" style="width: 75px">
                                            <h:outputText value="#{listaLacres.lacre}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Qtde}" style="width: 75px">
                                            <h:outputText value="#{listaLacres.qtde}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Valor}" style="width: 130px">
                                            <h:outputText value="#{listaLacres.valor}" converter="conversormoeda"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:rowExpansion>
                            </p:dataTable>

                            <p:commandLink actionListener="#{pedido.inserirPedidos}" 
                                           update="formImportarPedidos:panelUpload formImportarPedidos:arquivos msgs"
                                           title="#{localemsgs.Prosseguir}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                            <p:commandLink oncomplete="PF('dlgPedidosImportados').hide();"
                                           title="#{localemsgs.Cancelar}">
                                <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formPreOrders">
                    <p:dialog widgetVar="dlgPreOrders" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                              class="dialogoGrande">
                        <f:facet name="header">
                            <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.UploadPreOrder}" style="color:#022a48;" /> 
                        </f:facet>
                        <p:panel id="panelPreOrders" style="background-color: transparent" styleClass="cadastrar">

                            <h:outputText value="#{pedido.mensagemImportacao}"/>

                            <p:dataTable value="#{pedido.listaAgencias}" scrollHeight="400" 
                                         scrollable="true" resizableColumns="true"
                                         var="listaPedidos" id="listaPedidos" styleClass="tabela DataGrid"
                                         style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                <p:column style="width:16px">
                                    <p:rowToggler />
                                </p:column>
                                <p:column headerText="#{localemsgs.Agencia}">
                                    <h:outputText value="#{listaPedidos.agencia}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.SubAgencia}">
                                    <h:outputText value="#{listaPedidos.subAgencia}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DtColeta}">
                                    <h:outputText value="#{listaPedidos.dtColeta}" converter="conversorData"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DtEntrega}">
                                    <h:outputText value="#{listaPedidos.dtEntrega}" converter="conversorData"/>
                                </p:column>

                                <p:rowExpansion>
                                    <p:dataTable value="#{listaPedidos.listaMalotes}"
                                                 resizableColumns="true"
                                                 var="listaLacres" id="listaLacres" styleClass="tabela DataGrid"
                                                 style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                        <p:column headerText="#{localemsgs.Pedido}" style="width: 75px">
                                            <h:outputText value="#{listaLacres.pedidoCliente}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Lacre}" style="width: 75px">
                                            <h:outputText value="#{listaLacres.lacre}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Horario}" style="width: 70px">
                                            <h:outputText value="#{listaLacres.horario}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Valor}" style="width: 130px">
                                            <h:outputText value="#{listaLacres.valor}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Obs}" style="white-space: normal">
                                            <h:outputText value="#{listaLacres.obs}"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:rowExpansion>
                            </p:dataTable>

                            <p:commandLink actionListener="#{pedido.verificarExistenciaPreOrder}" 
                                           update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs panelConfirmacaoPreOrders"
                                           title="#{localemsgs.Prosseguir}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                            <p:commandLink oncomplete="PF('dlgPreOrders').hide();"
                                           title="#{localemsgs.Cancelar}">
                                <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panel>
                    </p:dialog>

                    <p:dialog positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;"
                              header="#{localemsgs.Opcoes}" widgetVar="dlgConfirmacao">
                        <f:facet name="header">
                            <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.UploadPreOrder}" style="color:#022a48;" /> 
                        </f:facet>
                        <p:panel id="panelConfirmacaoPreOrders" style="background-color: transparent; text-align: center" styleClass="editar">

                            <div class="form-inline">
                                <h:outputText value="#{pedido.mensagemImportacao}" style="text-align: center"/>
                                <p:spacer height="20px"/>
                            </div>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4," 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:commandButton action="#{pedido.inserirNovoLotePreOrders}" 
                                                 update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs"
                                                 title="#{localemsgs.NovaImportacao}" value="#{localemsgs.NovaImportacao}" />

                                <p:commandButton action="#{pedido.inserirPreOrders}" 
                                                 update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs"
                                                 title="#{localemsgs.Atualizar}" value="#{localemsgs.Atualizar}"/>

                                <p:commandButton oncomplete="PF('dlgConfirmacao').hide();PF('dlgPreOrders').hide();"
                                                 title="#{localemsgs.Cancelar}" value="#{localemsgs.Cancelar}"/>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>
                </h:form>                
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <script type="text/javascript">
                // <![CDATA[
                function ConfirmarExclusaoPedido(NumeroPedido) {
                    $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}',
                            '#{localemsgs.PerguntaPedido} <b>' + NumeroPedido + '</b>?',
                            '#{localemsgs.Sim}',
                            '#{localemsgs.Nao}',
                            function () {
                                rcExcluirPedido();
                            },
                            null);
                }

                function CarregarClienteDestino(inCodigoCliente) {
                    $JanelaFormClientes.close();

                    $('[id*="txtReaproveitaDst"]').val(inCodigoCliente);
                    rc1();
                }

                function CarregarClienteOrigem(inCodigoCliente) {
                    $JanelaFormClientes.close();

                    $('[id*="txtReaproveitaOri"]').val(inCodigoCliente);
                    rc();
                }

                $(document)
                        .on('mousedown', 'label[id*="btPesquisarCliente"]', function () {
                            let Tipo = '';
                            let Altura = $('body').height() - 250;

                            if ($(this).attr('id') === 'btPesquisarClienteDestino')
                                Tipo = 'D';
                            else if ($(this).attr('id') === 'btPesquisarClienteOrigem')
                                Tipo = 'O';

                            let HTML = '<iframe id="ifrClientes" src="../comercial/clientes.xhtml?selecao=S&tipo=' + Tipo + '" style="border:thin solid #CCC !important; margin:0px !important; padding:0px !important; width: 100% !important; height: ' + Altura.toString() + 'px !important;"></iframe>';

                            $JanelaFormClientes = $.alert({
                                icon: 'fa fa-search',
                                type: 'blue',
                                title: '<font color="#000">#{localemsgs.SelecioneCliente}</font>',
                                content: HTML,
                                boxWidth: '96%',
                                closeIcon: true,
                                useBootstrap: false,
                                buttons: {
                                    cancel: {
                                        btnClass: 'btn-red',
                                        text: '<i class="fa fa-ban"></i>&nbsp;#{localemsgs.Cancelar}'
                                    }
                                }
                            });
                        })
                        ;
                // ]]>
            </script>

            <!--Rodapé-->
            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{pedido.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{pedido.mostrarFiliais()}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText styleClass="corporativo-label" value="#{localemsgs.ExibirExcluidos}: " /></label>
                                <p:selectBooleanCheckbox value="#{pedido.exibirExcluidos}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{pedido.exibirExcluidos()}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

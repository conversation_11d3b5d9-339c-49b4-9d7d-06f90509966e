/*
 */
package SasBeansCompostas;

import SasBeans.Saspwac;
import SasBeans.Sysdef;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class SaspwacSysdef {

    private Saspwac saspwac;
    private Sysdef sysdef;

    public Saspwac getSaspwac() {
        return saspwac;
    }

    public void setSaspwac(Saspwac saspwac) {
        this.saspwac = saspwac;
    }

    public Sysdef getSysdef() {
        return sysdef;
    }

    public void setSysdef(Sysdef sysdef) {
        this.sysdef = sysdef;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 79 * hash + Objects.hashCode(this.saspwac);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SaspwacSysdef other = (SaspwacSysdef) obj;
        if (!Objects.equals(this.sysdef, other.sysdef)) {
            return false;
        }
        return true;
    }
}

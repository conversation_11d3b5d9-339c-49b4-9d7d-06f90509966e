<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/os_vig.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .ui-inputfield,
                input{
                    min-width: 100% !important;
                    width: 100% !important;
                    max-width: 100% !important;
                    margin-top: 2px !important;
                }

                [id*="gridDefault"] div{
                    padding: 4px !important;
                }

                [id$="pnlDados"] > div{
                    height: 100% !important
                }

                .ui-selectonemenu label.ui-selectonemenu-label {
                    cursor: pointer;
                    padding-top: 0px !important;
                    margin-top: 0px !important;
                }

                @media only screen and (max-width: 3000px) and (min-width: 641px) {
                    [id*="grdResumoDet"] thead tr th:nth-child(1),
                    [id*="grdResumoDet"] tbody tr td:nth-child(1){
                        min-width: 80px !important;
                        width: 80px !important;
                        max-width: 80px !important;
                    }

                    [id*="grdResumoDet"] thead tr th:nth-child(3),
                    [id*="grdResumoDet"] tbody tr td:nth-child(3){
                        min-width: 100px !important;
                        width: 100px !important;
                        max-width: 100px !important;
                    }

                    [id*="grdResumoDet"] thead tr th:nth-child(5),
                    [id*="grdResumoDet"] tbody tr td:nth-child(5){
                        min-width: 150px !important;
                        width: 150px !important;
                        max-width: 150px !important;
                    }

                    #divBtPesquisar{
                        padding-top: 28px !important;
                    }

                    .DestaqueValor{
                        font-size: 22pt !important;
                    }
                }

                @media only screen and (max-width: 641px) and (min-width: 0px) {
                    .ui-datatable-scrollable span{
                        font-weight: 500 !important;
                    }

                    .ui-datatable-scrollable tbody tr td span:first-child{
                        min-width: 130px !important;
                        width: 130px !important;
                        max-width: 130px !important;
                    }

                    .ui-datatable-scrollable tbody tr td:first-child span:last-child{
                        font-weight: 600 !important;
                        font-size: 12pt !important;
                    }

                    [id$="main"] {
                        height: calc(100% - 118px) !important;
                        top: 116px !important;
                    }
                }

                .ui-datatable-scrollable-header-box,
                .ui-datatable-scrollable-header,
                .ui-widget-header,
                .ui-datatable-scrollable .ui-datatable-scrollable-header{
                    padding: 0px !important;
                    margin: 0px !important;
                }

                .ui-datatable-scrollable-body,
                .ui-datatable-scrollable-body,
                [id$="grdResumo"] div,
                [id$="grdResumoDet"] div,
                [id$="grdTransacoes"] div{
                    padding-top: 0px !important;
                }

                .ui-dialog-title{
                    margin-left: 12px !important;
                }
                
                [id*="grdParadas"] a{
                    text-decoration: none !important;
                    font-size: 8pt !important;
                }
                
                [id$="grdParadas"]{
                    height: 400px !important;
                }
            </style>
        </h:head>
        <h:body id="h" style="max-height: 100% !important; height: 100% !important;">
            <f:metadata>
                <f:viewAction action="#{creceber.Persistencias(login.pp)}"/>
                <f:viewAction action="#{creceber.selecionaDia}"/>
                <f:viewAction action="#{creceber.consultaResumoCaixa}"/>
                <f:viewAction action="#{creceber.consultaResumoCaixaDetalhes}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_contas_receber.png" height="40" style="margin-top:-6px !important; margin-right: 6px !important" />
                                    <label class="TituloPagina">#{localemsgs.ResumoCaixa}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText value="#{creceber.dataInicio}" converter="conversorDate" />
                                            <h:outputText value=" - "/>
                                            <h:outputText value="#{creceber.dataFim}" converter="conversorDate"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{creceber.filiais.descricao}
                                            <label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco" style="line-height: 9px !important">
                                            #{creceber.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{creceber.filiais.bairro}, #{creceber.filiais.cidade}/#{creceber.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divCalendario" class="ui-grid-col-3" style="align-self: center; text-align: center; margin-top: -7px !important">

                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3"
                                     style="padding:0px 10px 0px 0px !important; text-align: right !important">
                                    <p:commandLink title="#{localemsgs.Voltar}" oncomplete="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main" style="padding-top: 19px !important; background-color: #ECF0F5 !important">
                    <p:dialog header="#{localemsgs.Paradas}" styleClass="box-primary modalBox"
                              widgetVar="dlgVinculoTransacao" minHeight="500" width="700" modal="true" appendTo="@(body)"
                              responsive="true" dynamic="true" resizable="false">
                        <p:panel>
                            <p:dataTable id="grdParadas" value="#{creceber.listaResumoForVinculo}"
                                         style="font-size: 12px;" var="resumoParadas" rowKey="R#{resumoParadas.rota}_#{resumoParadas.contaFin.toString()}"
                                         resizableColumns="false" selectionMode="single"
                                         emptyMessage="#{localemsgs.SemRegistrosTransacao}"
                                         selection="#{creceber.paradaSelecionada}" 
                                         scrollHeight="100%" styleClass="tabela" class="tabela" scrollable="true">
                                <p:column headerText="#{localemsgs.Data}" class="text-center">
                                    <h:outputText value="#{resumoParadas.dataNF}"  class="text-center" converter="conversorData"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Rota}" class="text-center">
                                    <h:outputText value="#{resumoParadas.rota}"  class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Parada}" class="text-center">
                                    <h:outputText value="#{resumoParadas.praca}"  class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ER}" class="text-center">
                                    <h:outputText value="#{resumoParadas.CMC7}"  class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.NRed}" class="text-center">
                                    <h:outputText value="#{resumoParadas.cliente}"  class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.FormaPagamento}" class="text-center">
                                    <h:outputText value="#{resumoParadas.formaPgtoDescr}"  class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorTotal}" class="text-center">
                                    <h:outputText value="#{resumoParadas.valor}"  class="text-center" converter="conversormoeda"/>
                                </p:column>
                                
                                <p:column headerText="#{localemsgs.Vincular}" class="text-center">
                                    <p:commandLink id="salvar" action="#{creceber.salvarVinculoTransacao(resumoParadas)}" update="msgs main:pnlDados main:botoes"
                                                   styleClass="btn btn-primary" style="color:#FFF !important" process="@this">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                                </p:column>
                                <p:summaryRow>
                                    <p:column colspan="4" style="text-align: right; background: #517692;">
                                        <h:outputText value="#{localemsgs.ValorTotal}: #{localemsgs.Rota} #{resumoCaixaDet.rota}" style="color:#FFF !important"/>
                                    </p:column>
                                    <p:column  style="text-align: center; background: #517692;">
                                        <h:outputText value="#{creceber.calcularTotalResumoCaixaDet(resumoCaixaDet.rota)}" converter="conversormoeda" style="color:#FFF !important"/>
                                    </p:column>
                                </p:summaryRow>
                            </p:dataTable>
                        </p:panel>\
                    </p:dialog>



                    <p:panel id="gridDefault" class="ui-grid ui-grid-responsive FundoPagina"
                             style="overflow:hidden !important; overflow-y:auto !important; padding-right:12px !important; border-top: 4px solid #3C8DBC !important">

                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>#{localemsgs.Periodo}</label><br />
                            <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                          value="#{creceber.datasSelecionadas}"
                                          monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                          pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                          converter="conversorDate" locale="#{localeController.getCurrentLocale()}" style="width: 100% !important; max-width: 200px !important"
                                          >
                                <p:ajax event="dateSelect" listener="#{creceber.selecionarDatasResumoCaixa}" update="msgs cabecalho cboRotas cboFormas" />
                            </p:datePicker>
                        </div> 
                        <div class="col-md-3 col-sm-3 col-xs-6">
                            <label>#{localemsgs.Rota}</label>
                            <p:selectOneMenu converter="omnifaces.SelectItemsConverter" id="cboRotas"
                                             style="width: 100%" value="#{creceber.rotaSelecionada}">
                                <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}" itemValue="" />
                                <f:selectItems value="#{creceber.rotasSelecao}"
                                               var="rotaSelecaoLista"
                                               itemValue="#{rotaSelecaoLista.rota}" 
                                               itemLabel="#{localemsgs.Rota} #{rotaSelecaoLista.rota}" noSelectionValue=""
                                               />
                            </p:selectOneMenu>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-6">
                            <label>#{localemsgs.FormaPagamento}</label>
                            <p:selectOneMenu converter="omnifaces.SelectItemsConverter" id="cboFormas"
                                             style="width: 100%" value="#{creceber.formaPagtoSelecionada}">
                                <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}" itemValue="" />
                                <f:selectItems value="#{creceber.formasPagto}"
                                               var="formaPagtoSelecaoLista"
                                               itemValue="#{formaPagtoSelecaoLista.formaPgto}" 
                                               itemLabel="#{formaPagtoSelecaoLista.formaPgtoDescr}" noSelectionValue=""
                                               />
                            </p:selectOneMenu>
                        </div>

                        <div class="col-md-3 col-sm-3 col-xs-12" id="divBtPesquisar">
                            <p:commandLink id="btAcaoPesquisa" class="btn btn-primary" style="width: 100%" actionListener="#{creceber.pesquisarResumoCaixa}" update="msgs main:pnlDados main:botoes">
                                #{localemsgs.Pesquisar}
                            </p:commandLink>
                        </div>

                        <p:panel class="col-md-12 col-sm-12 col-xs-12" id="pnlDados" style="height: calc(100% - 70px) !important; padding-right: 0px !important; padding-left: 0px !important;">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 10px">
                                <h3 style="font-weight: 500 !important; color: #3C8DBC"><i class="fa fa-bar-chart-o" style="margin-right: 10px"></i>#{localemsgs.Total}</h3>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-6">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 100px; border: 2px solid #CCC; box-shadow: 2px 2px 3px #DDD; background-color: #ECF0F5; border-radius: 4px; padding: 24px !important; text-align: center">
                                    <label class="DestaqueValor" style="font-size: 12pt">#{localemsgs.Quantidade} #{localemsgs.Total}: <b>#{creceber.resumoCaixaTotalQtde}</b></label>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-6">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 100px; border: 2px solid #CCC; box-shadow: 2px 2px 3px #DDD; background-color: #ECF0F5; border-radius: 4px; padding: 24px !important; text-align: center">
                                    <label class="DestaqueValor" style="font-size: 12pt">#{localemsgs.ValorTotal}: <b><h:outputText value="#{creceber.resumoCaixaTotalValor}" converter="conversormoeda"></h:outputText></b></label>
                                </div>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 20px">
                                <h3 style="font-weight: 500 !important; color: #3C8DBC"><i class="fa fa-file-text-o" style="margin-right: 10px"></i>#{localemsgs.ResumoCaixa}</h3>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12" style="height: calc(100% - 192px) !important;padding-left: 0px !important;">
                                <p:dataTable id="grdResumoDet" value="#{creceber.resumoCaixaDet}"
                                             style="font-size: 12px;" var="resumoCaixaDet" rowKey="#{resumoCaixaDet.cliente}_#{resumoCaixaDet.formaPgto}"
                                             resizableColumns="false" selectionMode="single"
                                             emptyMessage="#{localemsgs.SemRegistros}"
                                             sortBy="#{resumoCaixaDet.rota}"
                                             scrollHeight="100%" styleClass="tabela" class="tabela" scrollable="true"  disabledSelection="true">
                                    <p:column headerText="#{localemsgs.Rota}" class="text-center">
                                        <h:outputText value="#{resumoCaixaDet.rota}"  class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cliente}" class="text-center">
                                        <h:outputText value="#{resumoCaixaDet.cliente}"  class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Quantidade}" class="text-center">
                                        <h:outputText value="#{resumoCaixaDet.qtde}"  class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.FormaPagamento}" class="text-center">
                                        <h:outputText value="#{resumoCaixaDet.formaPgtoDescr}"  class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.ValorTotal}" class="text-center">
                                        <h:outputText value="#{resumoCaixaDet.valor}"  class="text-center" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.TotalOnePay}" class="text-center">
                                        <h:outputText value="#{resumoCaixaDet.onePayTotal}"  class="text-center" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.TaxaOnePay}" class="text-center">
                                        <h:outputText value="#{resumoCaixaDet.onePayTaxa}"  class="text-center" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.LiquidoOnePay}" class="text-center">
                                        <h:outputText value="#{resumoCaixaDet.onePayLiquido}"  class="text-center" converter="conversormoeda"/>
                                    </p:column>
                                    <p:summaryRow>
                                        <p:column colspan="4" style="text-align: right; background: #517692;">
                                            <h:outputText value="#{localemsgs.ValorTotal}: #{localemsgs.Rota} #{resumoCaixaDet.rota}" style="color:#FFF !important"/>
                                        </p:column>
                                        <p:column  style="text-align: center; background: #517692;">
                                            <h:outputText value="#{creceber.calcularTotalResumoCaixaDet(resumoCaixaDet.rota)}" converter="conversormoeda" style="color:#FFF !important"/>
                                        </p:column>
                                        <p:column  style="text-align: center; background: #517692;">
                                            <h:outputText value="#{creceber.calcularTotalResumoCaixaDetOneTotal(resumoCaixaDet.rota)}" converter="conversormoeda" style="color:#FFF !important"/>
                                        </p:column>
                                        <p:column  style="text-align: center; background: #517692;">
                                            <h:outputText value="#{creceber.calcularTotalResumoCaixaDetOneTaxa(resumoCaixaDet.rota)}" converter="conversormoeda" style="color:#FFF !important"/>
                                        </p:column>
                                        <p:column  style="text-align: center; background: #517692;">
                                            <h:outputText value="#{creceber.calcularTotalResumoCaixaDetOneLiquido(resumoCaixaDet.rota)}" converter="conversormoeda" style="color:#FFF !important"/>
                                        </p:column>
                                    </p:summaryRow>
                                </p:dataTable>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-12" style="height: calc(100% - 192px) !important;padding-right: 0px !important;">
                                <p:dataTable id="grdResumo" value="#{creceber.resumoCaixa}"
                                             style="font-size: 12px;" var="resumoCaixa" rowKey="#{resumoCaixa.formaPgto}_#{resumoCaixa.qtde}"
                                             resizableColumns="false" selectionMode="single" scrollable="true"
                                             emptyMessage="#{localemsgs.SemRegistros}" 
                                             sortBy="#{resumoCaixa.CCusto}"
                                             scrollHeight="100%" styleClass="tabela" class="tabela" disabledSelection="true">
                                    <p:column headerText="#{localemsgs.FormaPagamento}" class="text-center">
                                        <h:outputText value="#{resumoCaixa.formaPgtoDescr}"  class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Quantidade}" class="text-center">
                                        <h:outputText value="#{resumoCaixa.qtde}"  class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.ValorTotal}" class="text-center">
                                        <h:outputText value="#{resumoCaixa.valor}"  class="text-center" converter="conversormoeda"/>
                                    </p:column>
                                    <p:summaryRow>
                                        <p:column colspan="2" style="text-align: center; background:#517692; color:#FFF">
                                            <h:outputText value="#{localemsgs.Total}" style="color:#FFF !important"/>
                                        </p:column>
                                        <p:column  style="text-align: center; background:#517692; color:#FFF">
                                            <h:outputText value="#{creceber.calcularTotalResumoCaixa()}" converter="conversormoeda" style="color:#FFF !important"/>
                                        </p:column>
                                    </p:summaryRow>
                                </p:dataTable>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-12" style="height: calc(100% - 192px) !important;padding-right: 0px !important; padding-bottom: 0px !important;">
                                <h4 style="background: linear-gradient(to bottom, #597d98, #4b708d) !important; color: #FFF; border: thin solid #7397b1; text-align: center; padding: 8px; width: calc(100% - 18px); margin-left: 8px; margin-top: 4px; margin-bottom: 0px;">Transações sem Vínculo</h4>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100% - 50px) !important;padding: 0px !important;">
                                    <p:dataTable id="grdTransacoes" value="#{creceber.listaTransacoes}"
                                                 style="font-size: 12px;" var="resumoTransacoes" rowKey="#{resumoTransacoes.sequencia}"
                                                 resizableColumns="false" selectionMode="single" scrollable="true"
                                                 emptyMessage="#{localemsgs.SemRegistros}" 
                                                 selection="#{creceber.transacao}" 
                                                 scrollHeight="100%" styleClass="tabela" class="tabela">
                                        <p:ajax event="rowDblselect" listener="#{creceber.carregarTransacoesForVinculo}" update="msgs main"/>
                                        <p:column headerText="#{localemsgs.Data}" class="text-center">
                                            <h:outputText value="#{resumoTransacoes.created}"  class="text-center" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora}" class="text-center">
                                            <h:outputText value="#{resumoTransacoes.created.split(' ')[1].substring(0,5)}"  class="text-center" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nome}" class="text-center">
                                            <h:outputText value="#{resumoTransacoes.nome}"  class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorTotal}" class="text-center">
                                            <h:outputText value="#{resumoTransacoes.total}"  class="text-center" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Tipo}" class="text-center">
                                            <h:outputText value="#{resumoTransacoes.tipo_pagamento_tit}"  class="text-center" />
                                        </p:column>
                                        <p:summaryRow>
                                            <p:column colspan="2" style="text-align: center; background:#517692; color:#FFF">
                                                <h:outputText value="#{localemsgs.Total}" style="color:#FFF !important"/>
                                            </p:column>
                                            <p:column  style="text-align: center; background:#517692; color:#FFF">
                                                <h:outputText value="#{creceber.calcularTotalResumoCaixa()}" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>
                                        </p:summaryRow>
                                    </p:dataTable>
                                </div>
                            </div>

                        </p:panel>
                    </p:panel>

                    <p:panel style="position: fixed; z-index: 1; right: 5px; bottom: 60px !important; background: transparent; height: 50px" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink id="btImprimirPontos" title="#{localemsgs.Imprimir}" action="return false" rendered="#{creceber.resumoCaixa.size() > 0}">
                                <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>

                    <script>
                        // <![CDATA[    
                        $(document).on('click', '[id*="btImprimirPontos"]', function () {
                            ImprimirResumoCaixa(CriarCabecalho('#{login.getLogo(login.empresa.bancoDados)}',
                                    '#{creceber.filiais.descricao}',
                                    '#{creceber.filiais.endereco} - #{creceber.filiais.bairro}, #{creceber.filiais.cidade}/#{creceber.filiais.UF} - #{creceber.filiais.CEP} ',
                                    'CNPJ: #{creceber.filiais.CNPJ}',
                                    '#{localemsgs.RELATÓRIO} - #{localemsgs.ResumoCaixa} - #{localemsgs.Periodo}: ' + $('[id*="range_input"]').val()),
                                    $('[id$="grdResumoDet"]'), $('[id$="grdResumo"]'));
                        });

                        // ]]>
                    </script>
                </h:form>

                <ui:insert name="loading" >
                    <ui:include src="../assets/template/loading.xhtml" />
                </ui:insert>

                <footer>
                    <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                        <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important; display: #{login.pp.empresa ne 'SATMAXIMA'?'none':''}">
                            <h:form id="corporativo">
                                <div style="margin-top: 12px !important">
                                    <label ref="lblCheck"><h:outputText value="#{localemsgs.Excluido}: " /></label>
                                    <p:selectBooleanCheckbox value="#{pessoa.mostraExcluidos}">
                                        <p:ajax />
                                    </p:selectBooleanCheckbox>
                                </div>
                            </h:form>
                        </div>
                        <div class="container" style="min-height:10px !important;max-height:40px !important;">
                            <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                                <table class="footer-time" style="min-height:10px !important">
                                    <tr>
                                        <td>
                                            <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                                <table class="footer-user" style="min-height:10px !important">
                                    <tr>
                                        <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                        <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                    <tr>
                                        <td><img src="../assets/img/logo_satweb.png" /></td>
                                        <td>
                                            <h:form>
                                                <h:commandLink actionListener="#{localeController.increment}"
                                                               action="#{localeController.getLocales}" >
                                                    <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                                </h:commandLink>
                                            </h:form>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>

        </h:body>
    </f:view>

</html>

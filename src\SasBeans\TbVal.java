package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TbVal {

    private Integer Tabela;
    private Integer Codigo;
    private String Descricao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public Integer getTabela() {
        return Tabela;
    }

    public void setTabela(Integer Tabela) {
        this.Tabela = Tabela;
    }

    public Integer getCodigo() {
        return Codigo;
    }

    public void setCodigo(Integer Codigo) {
        this.Codigo = Codigo;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 61 * hash + Objects.hashCode(this.Tabela);
        hash = 61 * hash + Objects.hashCode(this.Codigo);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TbVal other = (TbVal) obj;
        if (!Objects.equals(this.Tabela, other.Tabela)) {
            return false;
        }
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }

}

package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ChavesMov {

    private BigDecimal Sequencia;
    private BigDecimal CodFil;
    private BigDecimal NroChave;
    private Integer Ordem;
    private LocalDate Data;
    private String Rota;
    private BigDecimal Remessa;
    private String CodRemessa;
    private LocalDate DtSai;
    private String HrSai;
    private String OperSai;
    private String LacreSai;
    private String RotaEnt;
    private LocalDate DtEnt;
    private String HrEnt;
    private String OperEnt;
    private String LacreEnt;
    private String LacreAnt;
    private LocalDate DtRecMob;
    private String HrRecMob;

    private String filial;
    private BigDecimal quantidade;
    private String nred;
    private String email;

    public ChavesMov() {
        this.Sequencia = new BigDecimal("0");
        this.CodFil = new BigDecimal("0");
        this.NroChave = new BigDecimal("0");
        this.Ordem = 0;
        this.Data = LocalDate.now();
        this.Rota = "";
        this.Remessa = new BigDecimal("0");
        this.CodRemessa = "";
        this.DtSai = LocalDate.now();
        this.HrSai = "";
        this.OperSai = "";
        this.LacreSai = "";
        this.RotaEnt = "";
        this.DtEnt = LocalDate.now();
        this.HrEnt = "";
        this.OperEnt = "";
        this.LacreEnt = "";
        this.LacreAnt = "";
        this.DtRecMob = LocalDate.now();
        this.HrRecMob = "";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public BigDecimal getNroChave() {
        return NroChave;
    }

    public void setNroChave(String NroChave) {
        try {
            this.NroChave = new BigDecimal(NroChave);
        } catch (Exception e) {
            this.NroChave = new BigDecimal("0");
        }
    }

    public Integer getOrdem() {
        return Ordem;
    }

    public void setOrdem(Integer Ordem) {
        this.Ordem = Ordem;
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public BigDecimal getRemessa() {
        return Remessa;
    }

    public void setRemessa(String Remessa) {
        try {
            this.Remessa = new BigDecimal(Remessa);
        } catch (Exception e) {
            this.Remessa = new BigDecimal("0");
        }
    }

    public String getCodRemessa() {
        return CodRemessa;
    }

    public void setCodRemessa(String CodRemessa) {
        this.CodRemessa = CodRemessa;
    }

    public LocalDate getDtSai() {
        return DtSai;
    }

    public void setDtSai(LocalDate DtSai) {
        this.DtSai = DtSai;
    }

    public String getHrSai() {
        return HrSai;
    }

    public void setHrSai(String HrSai) {
        this.HrSai = HrSai;
    }

    public String getOperSai() {
        return OperSai;
    }

    public void setOperSai(String OperSai) {
        this.OperSai = OperSai;
    }

    public String getLacreSai() {
        return LacreSai;
    }

    public void setLacreSai(String LacreSai) {
        this.LacreSai = LacreSai;
    }

    public String getRotaEnt() {
        return RotaEnt;
    }

    public void setRotaEnt(String RotaEnt) {
        this.RotaEnt = RotaEnt;
    }

    public LocalDate getDtEnt() {
        return DtEnt;
    }

    public void setDtEnt(LocalDate DtEnt) {
        this.DtEnt = DtEnt;
    }

    public String getHrEnt() {
        return HrEnt;
    }

    public void setHrEnt(String HrEnt) {
        this.HrEnt = HrEnt;
    }

    public String getOperEnt() {
        return OperEnt;
    }

    public void setOperEnt(String OperEnt) {
        this.OperEnt = OperEnt;
    }

    public String getLacreEnt() {
        return LacreEnt;
    }

    public void setLacreEnt(String LacreEnt) {
        this.LacreEnt = LacreEnt;
    }

    public String getLacreAnt() {
        return LacreAnt;
    }

    public void setLacreAnt(String LacreAnt) {
        this.LacreAnt = LacreAnt;
    }

    public LocalDate getDtRecMob() {
        return DtRecMob;
    }

    public void setDtRecMob(LocalDate DtRecMob) {
        this.DtRecMob = DtRecMob;
    }

    public String getHrRecMob() {
        return HrRecMob;
    }

    public void setHrRecMob(String HrRecMob) {
        this.HrRecMob = HrRecMob;
    }

    public String getFilial() {
        return filial;
    }

    public void setFilial(String filial) {
        this.filial = filial;
    }

    public BigDecimal getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(BigDecimal quantidade) {
        this.quantidade = quantidade;
    }

    public String getNred() {
        return nred;
    }

    public void setNred(String nred) {
        this.nred = nred;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}

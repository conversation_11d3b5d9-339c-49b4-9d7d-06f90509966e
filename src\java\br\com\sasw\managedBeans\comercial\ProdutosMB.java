/*
 */
package br.com.sasw.managedBeans.comercial;

import Arquivo.ArquivoLog;
import Controller.Produtos.ProdutosSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.Produtos;
import br.com.sasw.lazydatamodels.ProdutosLazyList;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "produtos")
@ViewScoped
public class ProdutosMB implements Serializable {

    private Persistencia persistencia;
    private BigDecimal codPessoa;
    private String codFil, operador, banco, nomeFilial, caminho, log, dataTela;
    private int total, flag;
    private ArquivoLog logerro;
    private Map filters;
    private ProdutosSatMobWeb produtossatmobweb;
    private LazyDataModel<Produtos> produtos = null;
    private Produtos novo;
    private Boolean limparFiltros;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;

    public ProdutosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");

        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        produtossatmobweb = new ProdutosSatMobWeb();
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");
    }

    public void Persistencias(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + this.banco);
            }
            this.filters = new HashMap<>();
            this.filters.put(" descricao like ? ", "");
            this.filters.put(" aplicacao like ? ", "");
            this.total = this.produtossatmobweb.contagem(this.filters, this.persistencia);
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<Produtos> getAllProdutos() {
        if (this.produtos == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");

            dt.setFilters(this.filters);
            this.produtos = new ProdutosLazyList(this.persistencia);
        }
        try {
            this.total = this.produtossatmobweb.contagem(this.filters, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.produtos;
    }

    public void PreEditar() {
        if (null == this.novo) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SelecioneProduto"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.flag = 2;
                PrimeFaces.current().resetInputs("formCadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void Editar() {
        try {
            this.novo.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.novo.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novo.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novo = (Produtos) FuncoesString.removeAcentoObjeto(this.novo);
            if (null != this.novo.getAplicacao()) {
                this.novo.setAplicacao(this.novo.getAplicacao().toUpperCase());
            }
            if (null != this.novo.getDescricao()) {
                this.novo.setDescricao(this.novo.getDescricao().toUpperCase());
            }
            if (null != this.novo.getObs()) {
                this.novo.setObs(this.novo.getObs().toUpperCase());
            }
            this.produtossatmobweb.atualizar(this.novo, persistencia);
            this.total = this.produtossatmobweb.contagem(this.filters, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void PreCadastrar() {
        this.novo = new Produtos();
        this.flag = 1;
    }

    public void Cadastrar() {
        try {
            this.novo.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.novo.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novo.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novo = (Produtos) FuncoesString.removeAcentoObjeto(this.novo);
            if (null != this.novo.getAplicacao()) {
                this.novo.setAplicacao(this.novo.getAplicacao().toUpperCase());
            }
            if (null != this.novo.getDescricao()) {
                this.novo.setDescricao(this.novo.getDescricao().toUpperCase());
            }
            if (null != this.novo.getObs()) {
                this.novo.setObs(this.novo.getObs().toUpperCase());
            }
            this.produtossatmobweb.inserir(this.novo, persistencia);
            this.total = this.produtossatmobweb.contagem(this.filters, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void PesquisaPaginada() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");

        if (this.novo.getDescricao().equals("")) {
            this.filters.replace(" descricao like ? ", "");
        } else {
            this.filters.replace(" descricao like ? ", "%" + this.novo.getDescricao() + "%");
        }

        if (this.novo.getAplicacao().equals("")) {
            this.filters.replace(" aplicacao like ? ", "");
        } else {
            this.filters.replace(" aplicacao like ? ", "%" + this.novo.getAplicacao() + "%");
        }

        dt.setFilters(this.filters);
        getAllProdutos();
        dt.setFirst(0);
    }

    public void LimparFiltros() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        this.limparFiltros = false;
        this.filters.replace(" descricao like ? ", "");
        this.filters.replace(" aplicacao like ? ", "");
        dt.setFilters(this.filters);
        getAllProdutos();
        dt.setFirst(0);
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public LazyDataModel<Produtos> getProdutos() {
        return produtos;
    }

    public void setProdutos(LazyDataModel<Produtos> produtos) {
        this.produtos = produtos;
    }

    public Produtos getNovo() {
        return novo;
    }

    public void setNovo(Produtos novo) {
        this.novo = novo;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.PessoaDet;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PessoaDetDao {

    public boolean inserirPessoaDet(PessoaDet pessoaDet, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE PessoaDet SET Codigo = ?, FaceID = ?, Operador = ?, Dt_alter = ?, Hr_Alter = ? WHERE Codigo = ?\n"
                    + "INSERT INTO PessoaDet (Codigo, FaceID, Operador, Dt_alter, Hr_Alter)\n"
                    + "SELECT ?, ?, ?, ?, ?\n"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado FROM PessoaDet WHERE Codigo = ?) AS A WHERE A.qtde_cadastrado = 0;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoaDet.getCodigo());
            consulta.setString(pessoaDet.getFaceID());
            consulta.setString(pessoaDet.getOperador());
            consulta.setString(pessoaDet.getDt_alter());
            consulta.setString(pessoaDet.getHr_Alter());

            consulta.setString(pessoaDet.getCodigo());

            consulta.setString(pessoaDet.getCodigo());
            consulta.setString(pessoaDet.getFaceID());
            consulta.setString(pessoaDet.getOperador());
            consulta.setString(pessoaDet.getDt_alter());
            consulta.setString(pessoaDet.getHr_Alter());

            consulta.setString(pessoaDet.getCodigo());
            int retorno = consulta.insert();
            consulta.close();
            return retorno > 0;
        } catch (Exception e) {
            throw new Exception("PessoaDetDao.inserirPessoaDet - " + e.getMessage() + "\r\n"
                    + "UPDATE PessoaDet SET Codigo = ?, FaceID = ?, Operador = ?, Dt_alter = ?, Hr_Alter = ? WHERE Codigo = ?"
                    + "INSERT INTO PessoaDet (Codigo, FaceID, Operador, Dt_alter, Hr_Alter) "
                    + "SELECT" + pessoaDet.getCodigo() + ", " + pessoaDet.getFaceID() + ", " + pessoaDet.getOperador() + ", "
                    + pessoaDet.getDt_alter() + ", " + pessoaDet.getHr_Alter() + ")"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado FROM PessoaDet WHERE Codigo = " + pessoaDet.getCodigo() + ") AS A WHERE A.qtde_cadastrado = 0;");
        }
    }

    /**
     * Lista as pessoas Ordena a busca para trazer os itens editados antes
     * daqueles alterados a mais tempo
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PessoaDet> listarPessoaDet(Persistencia persistencia) throws Exception {
        try {
            List<PessoaDet> retorno = new ArrayList<>();
            String sql = " SELECT PessoaDet.*, Pessoa.PWWeb, Pessoa.Nome, Pessoa.Matr FROM PessoaDet \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaDet.Codigo \n"
                    + "ORDER BY PessoaDet.Dt_alter DESC, PessoaDet.Hr_Alter DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            PessoaDet pessoaDet;
            while (consulta.Proximo()) {
                pessoaDet = new PessoaDet();
                pessoaDet.setCodigo(consulta.getString("Codigo"));
                pessoaDet.setFaceID(consulta.getString("FaceID"));
                pessoaDet.setOperador(consulta.getString("Operador"));
                pessoaDet.setDt_alter(consulta.getString("Dt_alter"));
                pessoaDet.setHr_Alter(consulta.getString("Hr_Alter"));

                pessoaDet.setPWWeb(consulta.getString("PWWeb"));
                pessoaDet.setNome(consulta.getString("Nome"));
                pessoaDet.setMatr(consulta.getString("Matr"));
                retorno.add(pessoaDet);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDetDao.listarPessoaDet - " + e.getMessage() + "\r\n"
                    + " SELECT PessoaDet.*, Pessoa.PWWeb, Pessoa.Nome, Pessoa.Matr FROM PessoaDet \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaDet.Codigo \n"
                    + "ORDER BY PessoaDet.Dt_alter DESC, PessoaDet.Hr_Alter DESC ");
        }
    }

    /**
     * Limita a busca por CodFil do funcionário Ordena a busca para trazer os
     * itens editados antes daqueles alterados a mais tempo
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PessoaDet> listarPessoaDet(String codFil, Persistencia persistencia) throws Exception {
        try {
            List<PessoaDet> retorno = new ArrayList<>();
            String sql = " SELECT PessoaDet.*, Pessoa.PWWeb, Pessoa.Nome, Pessoa.Matr \n"
                    + "FROM PessoaDet \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaDet.Codigo\n"
                    + "LEFT JOIN Funcion ON Funcion.Matr = Pessoa.Matr\n"
                    + "WHERE Funcion.CodFil = ? \n"
                    + "ORDER BY PessoaDet.Dt_alter DESC, PessoaDet.Hr_Alter DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();

            PessoaDet pessoaDet;
            while (consulta.Proximo()) {
                pessoaDet = new PessoaDet();
                pessoaDet.setCodigo(consulta.getString("Codigo"));
                pessoaDet.setFaceID(consulta.getString("FaceID"));
                pessoaDet.setOperador(consulta.getString("Operador"));
                pessoaDet.setDt_alter(consulta.getString("Dt_alter"));
                pessoaDet.setHr_Alter(consulta.getString("Hr_Alter"));

                pessoaDet.setPWWeb(consulta.getString("PWWeb"));
                pessoaDet.setNome(consulta.getString("Nome"));
                pessoaDet.setMatr(consulta.getString("Matr"));
                retorno.add(pessoaDet);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDetDao.listarPessoaDet - " + e.getMessage() + "\r\n"
                    + " SELECT PessoaDet.*, Pessoa.PWWeb, Pessoa.Nome, Pessoa.Matr FROM PessoaDet \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaDet.Codigo\n"
                    + "LEFT JOIN Funcion ON Funcion.Matr = Pessoa.Matr\n"
                    + "WHERE Funcion.CodFil = " + codFil + "\n"
                    + "ORDER BY PessoaDet.Dt_alter DESC, PessoaDet.Hr_Alter DESC ");
        }
    }

    /**
     * Busca uma pessoa cadastrada
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public PessoaDet buscarPessoaDet(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT PessoaDet.*, Pessoa.PWWeb, Pessoa.Nome, Pessoa.Matr \n"
                    + "FROM PessoaDet \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaDet.Codigo\n"
                    + "WHERE PessoaDet.Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();

            PessoaDet pessoaDet = null;
            while (consulta.Proximo()) {
                pessoaDet = new PessoaDet();
                pessoaDet.setCodigo(consulta.getString("Codigo"));
                pessoaDet.setFaceID(consulta.getString("FaceID"));
                pessoaDet.setOperador(consulta.getString("Operador"));
                pessoaDet.setDt_alter(consulta.getString("Dt_alter"));
                pessoaDet.setHr_Alter(consulta.getString("Hr_Alter"));

                pessoaDet.setPWWeb(consulta.getString("PWWeb"));
                pessoaDet.setNome(consulta.getString("Nome"));
                pessoaDet.setMatr(consulta.getString("Matr"));
            }
            consulta.Close();
            return pessoaDet;
        } catch (Exception e) {
            throw new Exception("PessoaDetDao.listarPessoaDet - " + e.getMessage() + "\r\n"
                    + " SELECT PessoaDet.*, Pessoa.PWWeb, Pessoa.Nome, Pessoa.Matr FROM PessoaDet \n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaDet.Codigo\n"
                    + "WHERE PessoaDet.Codigo = " + codigo);
        }
    }
}

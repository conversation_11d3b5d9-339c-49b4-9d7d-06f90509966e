package Controller.Filiais;

import Dados.Persistencia;
import SasBeans.Filiais;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class FiliaisSatMobWeb {

    /**
     * Listagem de filiais
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Filiais> ListaFiliais(Persistencia persistencia) throws Exception {
        try {
            FiliaisDao fdao = new FiliaisDao();
            return fdao.listagemFiliais(persistencia);
        } catch (Exception e) {
            throw new Exception("filiais.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cadastro de Filiais
     *
     * @param filiais - Objeto filial Dados - codfil, descricao, razaosocial,
     * operador
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void InserirFilial(<PERSON>lia<PERSON> filiais, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao fdao = new FiliaisDao();
            filiais = (Filiais) FuncoesString.removeAcentoObjeto(filiais);
            fdao.InserirFiliais(filiais, persistencia);
        } catch (Exception e) {
            throw new Exception("filiais.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Grava alteração de filial
     *
     * @param filiais - objeto filial Dados - codfil, descricao, razaosocial,
     * operador
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void GravarFilial(Filiais filiais, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao fdao = new FiliaisDao();
            filiais = (Filiais) FuncoesString.removeAcentoObjeto(filiais);
            fdao.GravarFilial(filiais, persistencia);
        } catch (Exception e) {
            throw new Exception("filiais.falhageral<message>" + e.getMessage());
        }
    }

    public List<Filiais> PesquisarFilial(Filiais filial, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao fdao = new FiliaisDao();
            return fdao.PesquisarFilial(filial, persistencia);
        } catch (Exception e) {
            throw new Exception("filiais.falhageral<message>" + e.getMessage());
        }
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de pessoas
     *
     * @param filtros - filtros para pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            FiliaisDao fdao = new FiliaisDao();
            retorno = fdao.TotalFiliaisMobWeb(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de pessoas
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Filiais> ListagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {

        try {
            List<Filiais> retorno;
            FiliaisDao fdao = new FiliaisDao();
            retorno = fdao.ListaPaginada(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }
}

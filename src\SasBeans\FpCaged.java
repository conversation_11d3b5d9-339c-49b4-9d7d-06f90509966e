/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FpCaged {

    private BigDecimal CodFil;
    private String Compet;
    private BigDecimal Matr;
    private String TipoMov;
    private LocalDate DtMovto;
    private String Atualizacao;
    private String CompetAcerto;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public String getCompet() {
        return Compet;
    }

    public void setCompet(String Compet) {
        this.Compet = Compet;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public String getTipoMov() {
        return TipoMov;
    }

    public void setTipoMov(String TipoMov) {
        this.TipoMov = TipoMov;
    }

    public LocalDate getDtMovto() {
        return DtMovto;
    }

    public void setDtMovto(LocalDate DtMovto) {
        this.DtMovto = DtMovto;
    }

    public String getAtualizacao() {
        return Atualizacao;
    }

    public void setAtualizacao(String Atualizacao) {
        this.Atualizacao = Atualizacao;
    }

    public String getCompetAcerto() {
        return CompetAcerto;
    }

    public void setCompetAcerto(String CompetAcerto) {
        this.CompetAcerto = CompetAcerto;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

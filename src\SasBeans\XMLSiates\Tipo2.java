/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.XMLSiates;

/**
 *
 * <AUTHOR>
 */
public class Tipo2 {

    public Tipo2(ArquivoXml arquivoxml) {
        this.arquivoxml = arquivoxml;
    }

    private ArquivoXml arquivoxml;

    public ArquivoXml getArquivoxml() {
        return arquivoxml;
    }

    public void setArquivoxml(ArquivoXml arquivoxml) {
        this.arquivoxml = arquivoxml;
    }

    public static class ArquivoXml {

        public ArquivoXml(Info_arquivo info_arquivo, Info_fornecedor info_fornecedor, Retorno retorno) {
            this.info_arquivo = info_arquivo;
            this.info_fornecedor = info_fornecedor;
            this.retorno = retorno;
        }

        private Info_arquivo info_arquivo;
        private Info_fornecedor info_fornecedor;
        private Retorno retorno;

        public Info_arquivo getInfo_arquivo() {
            return info_arquivo;
        }

        public void setInfo_arquivo(Info_arquivo info_arquivo) {
            this.info_arquivo = info_arquivo;
        }

        public Info_fornecedor getInfo_fornecedor() {
            return info_fornecedor;
        }

        public void setInfo_fornecedor(Info_fornecedor info_fornecedor) {
            this.info_fornecedor = info_fornecedor;
        }

        public Retorno getRetorno() {
            return retorno;
        }

        public void setRetorno(Retorno retorno) {
            this.retorno = retorno;
        }
    }

    public static class Info_arquivo {

        public Info_arquivo(String tipoarquivo, String idarquivo, String datahorageracaoarquivo, String comunicacao) {
            this.tipoarquivo = tipoarquivo;
            this.idarquivo = idarquivo;
            this.datahorageracaoarquivo = datahorageracaoarquivo;
            this.comunicacao = comunicacao;
        }

        private String tipoarquivo;
        private String idarquivo;
        private String datahorageracaoarquivo;
        private String comunicacao;

        public String getTipoarquivo() {
            return tipoarquivo;
        }

        public void setTipoarquivo(String tipoarquivo) {
            this.tipoarquivo = tipoarquivo;
        }

        public String getIdarquivo() {
            return idarquivo;
        }

        public void setIdarquivo(String idarquivo) {
            this.idarquivo = idarquivo;
        }

        public String getDatahorageracaoarquivo() {
            return datahorageracaoarquivo;
        }

        public void setDatahorageracaoarquivo(String datahorageracaoarquivo) {
            this.datahorageracaoarquivo = datahorageracaoarquivo;
        }

        public String getComunicacao() {
            return comunicacao;
        }

        public void setComunicacao(String comunicacao) {
            this.comunicacao = comunicacao;
        }

    }

    public static class Info_fornecedor {

        public Info_fornecedor(String idfornecedor, String nomefornecedor) {
            this.idfornecedor = idfornecedor;
            this.nomefornecedor = nomefornecedor;
        }

        private String idfornecedor;
        private String nomefornecedor;

        public String getIdfornecedor() {
            return idfornecedor;
        }

        public void setIdfornecedor(String idfornecedor) {
            this.idfornecedor = idfornecedor;
        }

        public String getNomefornecedor() {
            return nomefornecedor;
        }

        public void setNomefornecedor(String nomefornecedor) {
            this.nomefornecedor = nomefornecedor;
        }
    }

    public static class Retorno {

        public Retorno(String codigodobanco, Chamado_caixa chamado_caixa, String tipo_retorno, String chamado_fornecedor,
                String previsaoatendimento, String responsavelatendimento, String descricao) {
            this.codigodobanco = codigodobanco;
            this.chamado_caixa = chamado_caixa;
            this.tipo_retorno = tipo_retorno;
            this.chamado_fornecedor = chamado_fornecedor;
            this.previsaoatendimento = previsaoatendimento;
            this.responsavelatendimento = responsavelatendimento;
            this.descricao = descricao;
        }

        private String codigodobanco;
        private Chamado_caixa chamado_caixa;
        private String tipo_retorno;
        private String chamado_fornecedor;
        private String previsaoatendimento;
        private String responsavelatendimento;
        private String descricao;

        public String getCodigodobanco() {
            return codigodobanco;
        }

        public void setCodigodobanco(String codigodobanco) {
            this.codigodobanco = codigodobanco;
        }

        public Chamado_caixa getChamado_caixa() {
            return chamado_caixa;
        }

        public void setChamado_caixa(Chamado_caixa chamado_caixa) {
            this.chamado_caixa = chamado_caixa;
        }

        public String getTipo_retorno() {
            return tipo_retorno;
        }

        public void setTipo_retorno(String tipo_retorno) {
            this.tipo_retorno = tipo_retorno;
        }

        public String getChamado_fornecedor() {
            return chamado_fornecedor;
        }

        public void setChamado_fornecedor(String chamado_fornecedor) {
            this.chamado_fornecedor = chamado_fornecedor;
        }

        public String getPrevisaoatendimento() {
            return previsaoatendimento;
        }

        public void setPrevisaoatendimento(String previsaoatendimento) {
            this.previsaoatendimento = previsaoatendimento;
        }

        public String getResponsavelatendimento() {
            return responsavelatendimento;
        }

        public void setResponsavelatendimento(String responsavelatendimento) {
            this.responsavelatendimento = responsavelatendimento;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }
    }

    public static class Chamado_caixa {

        public Chamado_caixa(String no_req, String no_wo, String no_inc, String no_crq) {
            this.no_req = no_req;
            this.no_wo = no_wo;
            this.no_inc = no_inc;
            this.no_crq = no_crq;
        }
        private String no_req;
        private String no_wo;
        private String no_inc;
        private String no_crq;

        public String getNo_req() {
            return no_req;
        }

        public void setNo_req(String no_req) {
            this.no_req = no_req;
        }

        public String getNo_wo() {
            return no_wo;
        }

        public void setNo_wo(String no_wo) {
            this.no_wo = no_wo;
        }

        public String getNo_inc() {
            return no_inc;
        }

        public void setNo_inc(String no_inc) {
            this.no_inc = no_inc;
        }

        public String getNo_crq() {
            return no_crq;
        }

        public void setNo_crq(String no_crq) {
            this.no_crq = no_crq;
        }
    }
}

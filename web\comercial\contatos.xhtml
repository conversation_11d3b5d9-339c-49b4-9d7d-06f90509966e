<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid thead tr td:nth-child(1){
                        min-width: 49px;
                    }
                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid thead tr td:nth-child(2){
                        min-width: 49px;
                    }
                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid thead tr td:nth-child(3){
                        min-width: 290px;
                    }
                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid thead tr td:nth-child(4){
                        min-width: 290px;
                    }
                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid thead tr td:nth-child(5){
                        min-width: 200px;
                    }
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid thead tr td:nth-child(6){
                        min-width: 180px;
                    }
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid thead tr td:nth-child(7){
                        min-width: 290px;
                    }
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid thead tr td:nth-child(8){
                        min-width: 122px;
                    }
                    .DataGrid thead tr th:nth-child(9),
                    .DataGrid thead tr td:nth-child(9){
                        min-width: 122px;
                    }
                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid thead tr td:nth-child(10) {
                        min-width: 20px;
                    }
                    .DataGrid thead tr th:nth-child(11),
                    .DataGrid thead tr td:nth-child(11){
                        min-width: 90px;
                    }
                    .DataGrid thead tr th:nth-child(12),
                    .DataGrid thead tr td:nth-child(12){
                        min-width: 100px;
                    }
                    .DataGrid thead tr th:nth-child(13),
                    .DataGrid thead tr td:nth-child(13){
                        min-width: 100px;
                    }
                    .DataGrid thead tr th:nth-child(14),
                    .DataGrid thead tr td:nth-child(14){
                        min-width: 280px;
                    }
                    .DataGrid thead tr th:nth-child(15),
                    .DataGrid thead tr td:nth-child(15){
                        min-width: 300px;
                    }
                    .DataGrid thead tr th:nth-child(16),
                    .DataGrid thead tr td:nth-child(16){
                        min-width: 140px;
                    }
                    .DataGrid thead tr th:nth-child(17),
                    .DataGrid thead tr td:nth-child(17){
                        min-width: 193px;
                    }
                    .DataGrid thead tr th:nth-child(18),
                    .DataGrid thead tr td:nth-child(18){
                        min-width: 122px;
                    }
                    .DataGrid thead tr th:nth-child(19),
                    .DataGrid thead tr td:nth-child(19){
                        min-width: 94px;
                    }
                    .DataGrid thead tr th:nth-child(20),
                    .DataGrid thead tr td:nth-child(20){
                        min-width: 122px;
                    }
                    .DataGrid thead tr th:nth-child(21),
                    .DataGrid thead tr td:nth-child(21){
                        min-width: 122px;
                    }
                    .DataGrid thead tr th:nth-child(22),
                    .DataGrid thead tr td:nth-child(22){
                        min-width: 49px;
                    }
                    .DataGrid thead tr th:nth-child(23),
                    .DataGrid thead tr td:nth-child(23){
                        min-width: 84px;
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td{
                        text-align: center !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                .ui-radiobutton {
                    background: inherit !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }
            </style>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{contatos.Persistencias(login.pp)}"/>
            </f:metadata>
            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;"
                                     >
                                    <img src="../assets/img/icone_satmob_contatos_G.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Contatos}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span>
                                            <h:outputText
                                                id="dataDia"
                                                value="#{contatos.dataTela}"
                                                converter="conversorDia"
                                                />
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="text-align: center !important;"
                                     >
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{contatos.filiais.descricao}
                                            <label id="btTrocarFilial"
                                                   onclick="top.location.href = '../param.xhtml'"
                                                   >
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco">
                                            #{contatos.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{contatos.filiais.bairro}, #{contatos.filiais.cidade}/#{contatos.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar"
                                     class="col-md-2 col-sm-2 col-xs-3"
                                     style="padding:0px 10px 0px 0px !important; text-align: right !important"
                                     >
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();"
                                                   action="#"
                                                   >
                                        <p:graphicImage
                                            url="../assets/img/icone_voltar_branco.png"
                                            height="40"
                                            />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <!--Tabela Contatos-->
                <h:form id="main">
                    <p:hotkey bind="a" actionListener="#{contatos.PreCadastro}" update="msgs formCadastrar"/>
                    <p:hotkey bind="p" actionListener="#{contatos.PrePesquisa}" update="formPesquisaRapida msgs" oncomplete="PF('dlgPesquisaRapida').show()"/>
                    <p:hotkey bind="e" actionListener="#{contatos.PreEdicao}" update="msgs formCadastrar"/>
                    <p:hotkey bind="shift+x" action="#{exportarMB.setTitulo(localemsgs.Contatos)}" oncomplete="PF('dlgExportar').show();" rendered="false"/>

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline;">
                                    <p:dataTable
                                        id="tabela"
                                        value="#{contatos.allContatos}"
                                        paginator="true"
                                        rows="15"
                                        lazy="true"
                                        reflow="true"
                                        rowsPerPageTemplate="5,10,15, 20, 25"
                                        currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Pessoas}"
                                        paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                        var="lista"
                                        selectionMode="single"
                                        styleClass="tabela"
                                        selection="#{contatos.contatoSelecionado}"
                                        rowKey="#{lista.codigo}"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        scrollable="true"
                                        class="tabela DataGrid"
                                        scrollHeight="100%"
                                        style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                        >
                                        <p:ajax event="rowDblselect" listener="#{contatos.PreEdicao}" update="formCadastrar msgs"/>
                                        <p:column headerText="#{localemsgs.CodFil}">
                                            <h:outputText value="#{lista.codFil}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Codigo}" class="celula-right">
                                            <h:outputText value="#{lista.codigo}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nome}">
                                            <h:outputText value="#{lista.nome}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NomeFantasia}">
                                            <h:outputText value="#{lista.fantasia}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Situacao}">
                                            <h:outputText value="#{contatos.mapSituacao.get(lista.situacao.toBigInteger().toString())}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Contato}">
                                            <h:outputText value="#{lista.contato}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Endereco}">
                                            <h:outputText value="#{lista.endereco}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Bairro}">
                                            <h:outputText value="#{lista.bairro}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Cidade}">
                                            <h:outputText value="#{lista.cidade}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.UF}">
                                            <h:outputText value="#{lista.UF}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CEP}">
                                            <h:outputText value="#{lista.CEP}" converter="conversorCEP"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Fone1}">
                                            <h:outputText value="#{lista.fone1}" converter="conversorFone"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Fone2}">
                                            <h:outputText value="#{lista.fone2}" converter="conversorFone"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Email}">
                                            <h:outputText value="#{lista.email}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Obs}">
                                            <h:outputText value="#{lista.obs}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CGC}">
                                            <h:outputText value="#{lista.CNPJ}" converter="conversorCNPJ"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.IE}">
                                            <h:outputText value="#{lista.IE}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Insc_Munic}">
                                            <h:outputText value="#{lista.IM}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CPF}">
                                            <h:outputText value="#{lista.CPF}" converter="conversorCPF"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.RG}">
                                            <h:outputText value="#{lista.RG}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}">
                                            <h:outputText value="#{lista.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}">
                                            <h:outputText value="#{lista.hr_Alter}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}">
                                            <h:outputText value="#{lista.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 80px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{contatos.PreCadastro}"
                                           update="msgs formCadastrar" >
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{contatos.PreEdicao}"
                                           update="msgs formCadastrar">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{contatos.PrePesquisa}"
                                           update="formPesquisar msgs" oncomplete="PF('dlgPesquisar').show()" >
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{contatos.LimparFiltros}"
                                           update=":main:tabela :msgs :cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px; display: none;">
                            <p:commandLink title="#{localemsgs.Exportar}" action="#{exportarMB.setTitulo(localemsgs.Contatos)}"
                                           oncomplete="PF('dlgExportar').show();">
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <!-- Cadastrar novo -->
                <h:form id="formCadastrar" class="form-inline">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" focus="nome" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formCadastrar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_contatos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarContato}"
                                          style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"
                                          />
                        </f:facet>
                        <p:panel id="cadastrar"
                                 style="background-color: transparent;  max-width: 100% !important;"
                                 styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="codfil" value="#{localemsgs.CodFil}: "  />
                                <p:selectOneMenu id="codfil" value="#{contatos.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains" disabled="#{contatos.flag eq 2}">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="nome" value="#{localemsgs.Nome}: " />
                                <p:inputText id="nome" value="#{contatos.novoContato.nome}"
                                             required="true" label="#{localemsgs.Nome}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                             maxlength="60">
                                    <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                </p:inputText>

                                <p:outputLabel for="situacao" value="#{localemsgs.Situacao}: " />
                                <p:selectOneMenu id="situacao" value="#{contatos.situacao}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItems value="#{contatos.sit}" var="situacoes" itemValue="#{situacoes.codigo}"
                                                   itemLabel="#{situacoes.descricao}"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="fantasia" value="#{localemsgs.NomeFantasia}: " style="display:flex;justify-content:center;align-items:center;"/>
                                <p:inputText id="fantasia" value="#{contatos.novoContato.fantasia}"
                                             required="true" label="#{localemsgs.NomeFantasia}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NomeFantasia}"
                                             maxlength="60">
                                    <p:watermark for="fantasia" value="#{localemsgs.NomeFantasia}"/>
                                </p:inputText>

                                <p:outputLabel for="tpcli" value="#{localemsgs.TpCli}: " style="display:flex;justify-content:center;align-items:center;"/>
                                <p:selectOneMenu id="tpcli" value="#{contatos.novoContato.tpCli}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TpCli}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Agencia}" itemValue="0"/>
                                    <f:selectItem itemLabel="#{localemsgs.PAB}" itemValue="3"/>
                                    <f:selectItem itemLabel="#{localemsgs.TA}" itemValue="4"/>
                                    <f:selectItem itemLabel="#{localemsgs.TAC}" itemValue="5"/>
                                    <f:selectItem itemLabel="#{localemsgs.Cliente}" itemValue="6"/>
                                    <f:selectItem itemLabel="#{localemsgs.Tesouraria}" itemValue="7"/>
                                    <f:selectItem itemLabel="#{localemsgs.OutrasTransp}" itemValue="8"/>
                                    <f:selectItem itemLabel="#{localemsgs.ATM}" itemValue="9"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-1,ui-grid-col-2,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="cep" value="#{localemsgs.CEP}: "/>
                                <p:inputText id="cep" value="#{contatos.novoContato.CEP}"
                                             maxlength="8" style="width: 100%">
                                    <p:watermark for="cep" value="#{localemsgs.CEP}"/>
                                </p:inputText>

                                <p:commandLink title="#{localemsgs.Pesquisar}"
                                               partialSubmit="true" process="@this formCadastrar:cep" id="cep_pesquisa"
                                               update="formCadastrar:cep formCadastrar:ende formCadastrar:bairro formCadastrar:cidade formCadastrar:estado msgs"
                                               actionListener="#{contatos.Endereco}">
                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.AtualizarCEP}" icon="ui-icon-alert" />
                                    <p:dialog header="#{localemsgs.Aviso}" widgetVar="dlgOk" resizable="false"
                                              draggable="false" closable="true" width="300">
                                        <div class="form-inline">
                                            <h:outputText value="#{localemsgs.CompletarEndereco}" style="text-align: center"/>
                                            <p:spacer height="20px"/>
                                        </div>
                                        <p:commandButton value="#{localemsgs.OK}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                         onclick="PF('dlgOk').hide();" />
                                    </p:dialog>
                                </p:commandLink>

                                <p:outputLabel for="bairro" value="#{localemsgs.Bairro}: "/>
                                <p:inputText id="bairro" value="#{contatos.novoContato.bairro}"
                                             required="true" label="#{localemsgs.Bairro}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Bairro}"
                                             maxlength="25">
                                    <f:validateLength maximum="25"/>
                                    <p:watermark for="bairro" value="#{localemsgs.Bairro}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="ende" value="#{localemsgs.Endereco}: "/>
                                <p:inputText id="ende" value="#{contatos.novoContato.endereco}"
                                             required="true" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Endereco}"
                                             maxlength="60">
                                    <p:watermark for="ende" value="#{localemsgs.Endereco}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="cidade" value="#{localemsgs.Cidade}: "/>
                                <p:autoComplete id="cidade" value="#{contatos.novoContato.cidade}" styleClass="cidade"
                                                required="true" label="#{localemsgs.Cidade}" completeMethod="#{contatos.BuscarCidade}"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}" scrollHeight="200"
                                                maxlength="25" forceSelection="true" style="width: 100%">
                                    <p:ajax event="itemSelect" listener="#{contatos.SelecionarCidade}"
                                            update="formCadastrar:cidade formCadastrar:estado"/>
                                    <p:watermark for="cidade" value="#{localemsgs.Cidade}"/>
                                </p:autoComplete>

                                <p:outputLabel for="estado" value="#{localemsgs.UF}: "/>
                                <p:inputText id="estado" value="#{contatos.novoContato.UF}" style="width: 100%"
                                             required="true" label="#{localemsgs.UF}" disabled="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.UF}" maxlength="2">
                                    <p:watermark for="estado" value="#{localemsgs.UF}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="fone1" value="#{localemsgs.Fone1}: "/>
                                <p:inputMask id="fone1" value="#{contatos.novoContato.fone1}" style="width: 100%"
                                             label="#{localemsgs.Fone1}" maxlength="11" mask="#{mascaras.mascaraFone}">
                                    <p:watermark for="fone1" value="#{localemsgs.Fone1}"/>
                                </p:inputMask>


                                <p:outputLabel for="fone2" value="#{localemsgs.Fone2}: "/>
                                <p:inputMask id="fone2" value="#{contatos.novoContato.fone2}" style="width: 100%"
                                             label="#{localemsgs.Fone2}" maxlength="11" mask="#{mascaras.mascaraFone}">
                                    <p:watermark for="fone2" value="#{localemsgs.Fone2}"/>
                                </p:inputMask>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="contato" value="#{localemsgs.Contato}: "/>
                                <p:inputText id="contato" value="#{contatos.novoContato.contato}" style="width: 100%"
                                             label="#{localemsgs.Contato}" maxlength="80" required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Contato}">
                                    <p:watermark for="contato" value="#{localemsgs.Contato}"/>
                                </p:inputText>

                                <p:outputLabel for="email" value="#{localemsgs.Email}: "/>
                                <p:inputText id="email" value="#{contatos.novoContato.email}" style="width: 100%"
                                             label="#{localemsgs.Email}" maxlength="80" required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}">
                                    <p:watermark for="email" value="#{localemsgs.Email}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="obs" value="#{localemsgs.Obs}: "/>
                                <p:inputText id="obs" value="#{contatos.novoContato.obs}" style="width: 100%"
                                             label="#{localemsgs.Obs}" maxlength="80">
                                    <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-3,ui-grid-col-3"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="codorigem" value="#{localemsgs.Origem}: " />
                                <p:selectOneMenu id="codorigem" value="#{contatos.novoContato.codOrigem}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Origem}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItems value="#{contatos.origem}" var="origens" itemValue="#{origens.codigo}"
                                                   itemLabel="#{origens.descricao}"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="escolha" value="#{localemsgs.SelecionarCliente}: " />
                                <p:selectOneButton value="#{contatos.escolha}" id="escolha" style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.PessoaJuridica}" itemValue="cgc"/>
                                    <f:selectItem itemLabel="#{localemsgs.PessoaFisica}" itemValue="cpf" />
                                    <p:ajax update="pessoa" oncomplete="PF('dlgCadastrar').initPosition()"/>
                                </p:selectOneButton>
                            </p:panelGrid>

                            <p:panel id="pessoa" style=" background-color: transparent">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="cgc" value="#{localemsgs.CGC}: " rendered="#{contatos.escolha == 'cgc'}"/>
                                    <p:inputMask id="cgc" value="#{contatos.novoContato.CNPJ}"
                                                 label="#{localemsgs.CGC}" style="width: 100%"
                                                 rendered="#{contatos.escolha == 'cgc'}" size="18" maxlength="14"
                                                 mask="#{mascaras.mascaraCNPJ}"/>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="cpf" value="#{localemsgs.CPF}: " rendered="#{contatos.escolha == 'cpf'}" />
                                    <p:inputMask id="cpf" value="#{contatos.novoContato.CPF}"
                                                 label="#{localemsgs.CPF}" style="width: 100%"
                                                 rendered="#{contatos.escolha == 'cpf'}" maxlength="11"
                                                 mask="#{mascaras.mascaraCPF}">
                                    </p:inputMask>

                                    <p:outputLabel for="rg" value="#{localemsgs.RG}: " rendered="#{contatos.escolha == 'cpf'}"/>
                                    <p:inputText id="rg" value="#{contatos.novoContato.RG}"
                                                 label="#{localemsgs.RG}" style="width: 100%"
                                                 rendered="#{contatos.escolha == 'cpf'}" size="15" maxlength="15">
                                        <p:watermark for="rg" value="#{localemsgs.RG}"/>
                                    </p:inputText>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="ie" value="#{localemsgs.IE}: " rendered="#{contatos.escolha == 'cgc'}"/>
                                    <p:inputText id="ie" value="#{contatos.novoContato.IE}"
                                                 label="#{localemsgs.IE}" style="width: 100%"
                                                 rendered="#{contatos.escolha == 'cgc'}" size="24" maxlength="24">
                                        <p:watermark for="ie" value="#{localemsgs.IE}"/>
                                    </p:inputText>

                                    <p:outputLabel for="im" value="#{localemsgs.Insc_Munic}: " rendered="#{contatos.escolha == 'cgc'}"/>
                                    <p:inputText id="im" value="#{contatos.novoContato.IM}"
                                                 label="#{localemsgs.Insc_Munic}" style="width: 100%"
                                                 rendered="#{contatos.escolha == 'cgc'}" size="24" maxlength="15" >
                                        <p:watermark for="im" value="#{localemsgs.Insc_Munic}"/>
                                    </p:inputText>
                                </p:panelGrid>
                            </p:panel>

                            <div class="form-inline">
                                <p:commandLink rendered="#{contatos.flag eq 1}" id="cadastro" action="#{contatos.Cadastrar}" update=" :main:tabela :msgs :cabecalho"
                                               title="#{localemsgs.Cadastrar}" >
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink rendered="#{contatos.flag eq 2}" id="edit" action="#{contatos.Editar}" update=":msgs :main:tabela :cabecalho"
                                               title="#{localemsgs.Editar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:blockUI block="formCadastrar:cadastrar" trigger="formCadastrar:cadastro"/>
                                <p:blockUI block="formCadastrar:cadastrar" trigger="formCadastrar:edit"/>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisar contatos-->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_contatos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarContato}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil" value="#{contatos.contatoPesquisa.codFil}"
                                                     converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nome" value="#{localemsgs.Nome}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome" value="#{contatos.contatoPesquisa.nome}" label="#{localemsgs.Nome}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nred" value="#{localemsgs.NomeFantasia}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nred" value="#{contatos.contatoPesquisa.fantasia}" label="#{localemsgs.NomeFantasia}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nred" value="#{localemsgs.NomeFantasia}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="tpcli" value="#{localemsgs.TpCli}: " style="display:flex;justify-content:center;align-items:center;"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="tpcli" value="#{contatos.contatoPesquisa.tpCli}"
                                                     styleClass="filial" style="width: 100%"
                                                     filter="true" filterMatchMode="contains">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Agencia}" itemValue="0"/>
                                        <f:selectItem itemLabel="#{localemsgs.PAB}" itemValue="3"/>
                                        <f:selectItem itemLabel="#{localemsgs.TA}" itemValue="4"/>
                                        <f:selectItem itemLabel="#{localemsgs.TAC}" itemValue="5"/>
                                        <f:selectItem itemLabel="#{localemsgs.Cliente}" itemValue="6"/>
                                        <f:selectItem itemLabel="#{localemsgs.Tesouraria}" itemValue="7"/>
                                        <f:selectItem itemLabel="#{localemsgs.OutrasTransp}" itemValue="8"/>
                                        <f:selectItem itemLabel="#{localemsgs.ATM}" itemValue="9"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="situacao" value="#{localemsgs.Situacao}: " />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="situacao" value="#{contatos.contatoPesquisa.situacao}"
                                                     styleClass="filial" style="width: 100%"
                                                     filter="true" filterMatchMode="contains">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItems value="#{contatos.sit}" var="situacoes" itemValue="#{situacoes.codigo}"
                                                       itemLabel="#{situacoes.descricao}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{contatos.PesquisaPaginada}" oncomplete="PF('dlgPesquisar').hide()"
                                               update=" :main:tabela :msgs :cabecalho"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Pesquisa Rápida Posto de Serviço -->
                <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_postosdeservico.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarPstServ}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="panelPesquisaRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{contatos.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Codigo}" itemValue="CODFIL" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.Fantasia}" itemValue="FANTASIA" />
                                        <f:selectItem itemLabel="#{localemsgs.CodCli}" itemValue="CODCLI" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{contatos.chavePesquisa eq 'CODFIL'}" value="#{localemsgs.Codigo}: "/>
                                        <p:outputLabel for="opcao" rendered="#{contatos.chavePesquisa eq 'NOME'}" value="#{localemsgs.Nome}: "/>
                                        <p:outputLabel for="opcao" rendered="#{contatos.chavePesquisa eq 'FANTASIA'}" value="#{localemsgs.Fantasia}: "/>
                                        <p:outputLabel for="opcao" rendered="#{contatos.chavePesquisa eq 'CODCLI'}" value="#{localemsgs.CodCli}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{contatos.chavePesquisa eq 'CODFIL' ? contatos.contatoPesquisa.codFil
                                                     : contatos.chavePesquisa eq 'NOME' ? contatos.contatoPesquisa.nome
                                                     : contatos.chavePesquisa eq 'FANTASIA' ? contatos.contatoPesquisa.fantasia
                                                     : contatos.contatoPesquisa.codCli
                                            }"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="botaoPesquisaRapida"
                                               action="#{contatos.pesquisarUnico()}"
                                               update=" :main:tabela :msgs cabecalho"
                                               oncomplete="PF('dlgPesquisaRapida').hide()"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                </script>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

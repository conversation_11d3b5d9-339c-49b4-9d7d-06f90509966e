/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Inspecoes.InspecoesSatWeb;
import Dados.Persistencia;
import SasBeans.PstInspecao;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PstInspecaoLazyList extends LazyDataModel<PstInspecao> {

    private static final long serialVersionUID = 1L;
    private List<PstInspecao> pstInspecaoList;
    private final InspecoesSatWeb inspecoesSatWeb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public PstInspecaoLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.inspecoesSatWeb = new InspecoesSatWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<PstInspecao> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.pstInspecaoList = this.inspecoesSatWeb.getPstInspecoes(first, pageSize, filters, this.codPessoa, this.persistencia);

            // set the total of players
            setRowCount(this.inspecoesSatWeb.qtdPstInspecoes(filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.pstInspecaoList;
    }

    @Override
    public Object getRowKey(PstInspecao pstInspecao) {
        return pstInspecao.getSequencia();
    }

    @Override
    public PstInspecao getRowData(String rowKey) {
        for (PstInspecao pstInspecao : this.pstInspecaoList) {
            if (rowKey.equals(pstInspecao.getChavePrimaria())) {
                return pstInspecao;
            }
        }
        getWrappedData();
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public BigDecimal getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(BigDecimal codPessoa) {
        this.codPessoa = codPessoa;
    }
}

package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Saspwac {

    private String Nome;
    private BigDecimal Sistema;
    private BigDecimal CodFil;
    private BigDecimal Codigo;
    private int Inclusao;
    private int Alteracao;
    private int Exclusao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private Boolean InclusaoB;
    private Boolean AlteracaoB;
    private Boolean ExclusaoB;

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public BigDecimal getSistema() {
        return Sistema;
    }

    public void setSistema(String Sistema) {
        try {
            this.Sistema = new BigDecimal(Sistema);
        } catch (Exception e) {
            this.Sistema = new BigDecimal("0");
        }
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    public int getInclusao() {
        return Inclusao;
    }

    public void setInclusao(int Inclusao) {
        this.Inclusao = Inclusao;
        this.InclusaoB = this.Inclusao == 1;
    }

    public int getAlteracao() {
        return Alteracao;
    }

    public void setAlteracao(int Alteracao) {
        this.Alteracao = Alteracao;
        this.AlteracaoB = this.Alteracao == 1;
    }

    public int getExclusao() {
        return Exclusao;
    }

    public void setExclusao(int Exclusao) {
        this.Exclusao = Exclusao;
        this.ExclusaoB = this.Exclusao == 1;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public Boolean getInclusaoB() {
        return InclusaoB;
    }

    public void setInclusaoB(Boolean InclusaoB) {
        this.InclusaoB = InclusaoB;
    }

    public Boolean getAlteracaoB() {
        return AlteracaoB;
    }

    public void setAlteracaoB(Boolean AlteracaoB) {
        this.AlteracaoB = AlteracaoB;
    }

    public Boolean getExclusaoB() {
        return ExclusaoB;
    }

    public void setExclusaoB(Boolean ExclusaoB) {
        this.ExclusaoB = ExclusaoB;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 23 * hash + Objects.hashCode(this.Sistema);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Saspwac other = (Saspwac) obj;
        if (!Objects.equals(this.Sistema, other.Sistema)) {
            return false;
        }
        return true;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.Rt_GuiasMoeda;

/**
 *
 * <AUTHOR>
 */
public class Rt_GuiasMoedaDao {

    public void inserirRt_GuiasMoedasDao(Rt_GuiasMoeda rt_guiasMoeda, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO Rt_GuiasMoeda (Sequencia, Parada, Guia, Serie, Moeda, Operador, Dt_Alter, Hr_Alter) \n"
                    + "SELECT ?, ?, ?, ?, ?, ?, ?, ? \n"
                    + "FROM (SELECT\n"
                    + "    COUNT(*) AS qtde_cadastrado \n"
                    + "    FROM Rt_GuiasMoeda\n"
                    + "    WHERE Sequencia = ? AND Parada = ? AND Guia = ? AND Serie = ? AND Moeda = ? ) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0";
            Consulta consulta = new Consulta(sql, persistencia);
            // Select
            consulta.setString(rt_guiasMoeda.getSequencia());
            consulta.setString(rt_guiasMoeda.getParada());
            consulta.setString(rt_guiasMoeda.getGuia());
            consulta.setString(rt_guiasMoeda.getSerie());
            consulta.setString(rt_guiasMoeda.getMoeda());
            consulta.setString(rt_guiasMoeda.getOperador());
            consulta.setString(rt_guiasMoeda.getDt_Alter());
            consulta.setString(rt_guiasMoeda.getHr_Alter());

            // Where
            consulta.setString(rt_guiasMoeda.getSequencia());
            consulta.setString(rt_guiasMoeda.getParada());
            consulta.setString(rt_guiasMoeda.getGuia());
            consulta.setString(rt_guiasMoeda.getSerie());
            consulta.setString(rt_guiasMoeda.getMoeda());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasMoedaDao.inserirRt_GuiasMoedasDao - " + e.getMessage() + "\r\n"
                    + "INSERT INTO Rt_GuiasMoeda (Sequencia, Parada, Guia, Serie, Moeda, Operador, Dt_Alter, Hr_Alter) \n"
                    + "SELECT ?, ?, ?, ?, ?, ?, ?, ? \n"
                    + "FROM (SELECT\n"
                    + "    COUNT(*) AS qtde_cadastrado \n"
                    + "    FROM Rt_GuiasMoeda\n"
                    + "    WHERE Sequencia = " + rt_guiasMoeda.getSequencia() + " AND Parada = " + rt_guiasMoeda.getParada()
                    + " AND Guia = " + rt_guiasMoeda.getGuia() + " AND Serie = " + rt_guiasMoeda.getSerie() + " AND Moeda = " + rt_guiasMoeda.getMoeda()
                    + " ) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0"
            );
        }
    }
}

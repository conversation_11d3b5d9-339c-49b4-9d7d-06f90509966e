package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.OS_Vig;
import SasBeans.TesEntrada;
import SasBeansCompostas.TesConta;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesEntradaDao {

    private Persistencia persistencia;

    /**
     *
     */
    public TesEntradaDao() {
    }

    /**
     *
     * @param persistencia
     */
    public TesEntradaDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    /**
     *
     * @param tesEntrada
     * @param persistencia
     * @throws Exception
     */
    public void atualizaValorRecTesEntrada(TesEntrada tesEntrada, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE TesEntrada SET ValorRec = Valor \n"
                    + " WHERE Guia = ? AND Serie = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(tesEntrada.getGuia());
            consulta.setString(tesEntrada.getSerie());
            consulta.setBigDecimal(tesEntrada.getCodFil());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.atualizaValorRecTesEntrada - " + e.getMessage() + "\r\n"
                    + "  UPDATE TesEntrada SET ValorRec = Valor \n"
                    + " WHERE Guia = " + tesEntrada.getGuia() + " AND Serie = " + tesEntrada.getSerie() + " AND CodFil = " + tesEntrada.getCodFil());
        }
    }

    /**
     *
     * @param tesEntrada
     * @param persistencia
     * @throws Exception
     */
    public void inserirTesEntrada(TesEntrada tesEntrada, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO TesEntrada (CodFil, Guia, Serie, Data, CodCli1, CodCli2, TipoMov, Lote, ContaTes, \n"
                    + "Valor, ValorRec, TotalDN, Situacao, MatrConf, HrInicio, HrFinal, Tempo, CodSrv, NroMaqConf,\n"
                    + "OperRec, Operador, Dt_Alter, Hr_Alter, Dt_Incl, hr_Incl) \n"
                    + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(tesEntrada.getCodFil());
            consulta.setBigDecimal(tesEntrada.getGuia());
            consulta.setString(tesEntrada.getSerie());
            consulta.setString(tesEntrada.getData());
            consulta.setString(tesEntrada.getCodCli1());
            consulta.setString(tesEntrada.getCodCli2());
            consulta.setString(tesEntrada.getTipoMov());
            consulta.setString(tesEntrada.getLote());
            consulta.setBigDecimal(tesEntrada.getContaTes());
            consulta.setBigDecimal(tesEntrada.getValor());
            consulta.setBigDecimal(tesEntrada.getValorRec());
            consulta.setBigDecimal(tesEntrada.getTotalDN());
            consulta.setString(tesEntrada.getSituacao());
            consulta.setBigDecimal(tesEntrada.getMatrConf());
            consulta.setString(tesEntrada.getHrInicio());
            consulta.setString(tesEntrada.getHrFinal());
            consulta.setBigDecimal(tesEntrada.getTempo());
            consulta.setString(tesEntrada.getCodSrv());
            consulta.setBigDecimal(tesEntrada.getNroMaqConf());
            consulta.setString(tesEntrada.getOperRec());
            consulta.setString(tesEntrada.getOperador());
            consulta.setString(tesEntrada.getDt_Alter());
            consulta.setString(tesEntrada.getHr_Alter());
            consulta.setString(tesEntrada.getDt_Incl());
            consulta.setString(tesEntrada.getHr_Incl());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.inserirTesEntrada - " + e.getMessage() + "\r\n"
                    + " INSERT INTO TesEntrada (CodFil, Guia, Serie, Data, CodCli1, CodCli2, TipoMov, Lote, ContaTes, \n"
                    + "Valor, ValorRec, TotalDN, Situacao, MatrConf, HrInicio, HrFinal, Tempo, CodSrv, NroMaqConf,\n"
                    + "OperRec, Operador, Dt_Alter, Hr_Alter) \n"
                    + "VALUES (" + tesEntrada.getCodFil() + ", " + tesEntrada.getGuia() + ", " + tesEntrada.getSerie() + ", " + tesEntrada.getData() + ", "
                    + tesEntrada.getCodCli1() + ", " + tesEntrada.getCodCli2() + ", " + tesEntrada.getTipoMov() + ", " + tesEntrada.getLote() + ", "
                    + tesEntrada.getContaTes() + ", " + tesEntrada.getValor() + ", " + tesEntrada.getValorRec() + ", " + tesEntrada.getTotalDN() + ", "
                    + tesEntrada.getSituacao() + ", " + tesEntrada.getMatrConf() + ", " + tesEntrada.getHrInicio() + ", " + tesEntrada.getHrFinal() + ", "
                    + tesEntrada.getTempo() + ", " + tesEntrada.getCodSrv() + ", " + tesEntrada.getNroMaqConf() + ",\n"
                    + tesEntrada.getOperRec() + ", " + tesEntrada.getOperador() + ", " + tesEntrada.getDt_Alter() + "," + tesEntrada.getHr_Alter() + ", "
                    + tesEntrada.getDt_Incl() + ", " + tesEntrada.getHr_Incl() + ")");
        }
    }

    /**
     * Recupera o nome do cliente
     *
     * @param guia numero da guia
     * @param serie serie da guia
     * @param persistencia conexão com o banco de dados
     * @return nome do cliente
     * @throws Exception
     */
    public String obterCliente(BigDecimal guia, String serie, Persistencia persistencia) throws Exception {
        String nome = "";
        try {
            String sql = "SELECT Clientes.Nome nome FROM TesEntrada JOIN Clientes"
                    + "    on TesEntrada.codfil = Clientes.codfil AND "
                    + "        TesEntrada.codcli1 = Clientes.codigo"
                    + "    WHERE TesEntrada.guia = ? AND TesEntrada.serie = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(guia);
            consulta.setString(serie);
            consulta.select();

            while (consulta.Proximo()) {
                nome = consulta.getString("nome");
            }
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return nome;
    }

    /**
     * Troca a serie da guia em TesEntrada
     *
     * @param persistencia - conexão ao banco de dados
     * @param tesentrada - Guia em tesentrada Obrigatório - guia, série e codfil
     * @param novaserie - série de destino da guia
     * @throws Exception
     */
    public void TrocaSerieGuia(Persistencia persistencia, TesEntrada tesentrada, String novaserie) throws Exception {
        try {
            String sql;
            sql = "update TesEntrada set Serie = ?"
                    + " where Guia = ?"
                    + " and Serie =  ?"
                    + " and CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(novaserie);
            consulta.setString(tesentrada.getGuia().toPlainString());
            consulta.setString(tesentrada.getSerie());
            consulta.setString(tesentrada.getCodFil().toPlainString());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao trocar serie da guia em TesEntrada - " + e.getMessage());
        }
    }

    /**
     * Lista entradas em TesEntrada
     *
     * @param persistencia - conexão ao banco de dados
     * @param tesentrada - Guia em tesentrada Obrigatório - guia, série,
     * codcli2, situacao e data
     * @return
     * @throws Exception
     */
    public List<TesEntrada> listaEntradasSatMob(TesEntrada tesentrada, Persistencia persistencia) throws Exception {
        List<TesEntrada> retorno;
        try {
            String sql;
            sql = "Select TesEntrada.Data, Count(TesEntSangrias.Docto) Sangrias, TesEntrada.Guia, TesEntrada.Serie, "
                    + " isnull(Sum(TesEntSangrias.ValorApurado + TesEntSangrias.moedasvalor),0) ValorApurado, TesEntrada.Valor, "
                    + " TesEntrada.CodCli2, Clientes1.NRed NRedOri, clientes1.Ende Ende1, "
                    + " Clientes2.NRed NRedDst, TesEntrada.Situacao "
                    + " from TesEntrada "
                    + " left join  Clientes Clientes1 on  TesEntrada.CodCli1 = Clientes1.Codigo "
                    + "                               and TesEntrada.CodFil  = Clientes1.CodFil "
                    + " left join  Clientes Clientes2 on  TesEntrada.CodCli2 = Clientes2.Codigo "
                    + "                               and TesEntrada.CodFil  = Clientes2.CodFil "
                    + " left join TesEntSangrias on TesEntSangrias.Guia = TesEntrada.Guia"
                    + "                        and TesEntSangrias.Serie = TesEntrada.Serie"
                    + " Where TesEntrada.Guia = ? "
                    + "   and (TesEntrada.Serie = ? OR TesEntrada.Serie = REPLICATE('0', 3 - LEN(?)) + RTrim(?))"
                    //+ "   and TesEntrada.Situacao = ? "
                    //+ "   and TesEntrada.Data = ? "
                    + "   and TesEntrada.CodFil = ? "
                    + " Group By TesEntrada.Data, TesEntrada.Guia, TesEntrada.Serie, TesEntrada.Valor, TesEntrada.CodCli2, "
                    + "     Clientes1.NRed, Clientes1.Ende, Clientes2.NRed, TesEntrada.Situacao";
            retorno = new ArrayList<>();
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(tesentrada.getGuia());
            consult.setString(tesentrada.getSerie());
            consult.setString(tesentrada.getSerie());
            consult.setString(tesentrada.getSerie());
            //consult.setString(tesentrada.getSituacao());
            //consult.setString(tesentrada.getData());
            consult.setBigDecimal(tesentrada.getCodFil());
            consult.select();
            TesEntrada entrada;
            while (consult.Proximo()) {
                entrada = new TesEntrada();
                entrada.setSituacao(consult.getString("Situacao"));
                entrada.setCodCli2(consult.getString("codCli2"));
                entrada.setGuia(consult.getString("Guia"));
                entrada.setSerie(consult.getString("Serie"));
                entrada.setValor(consult.getString("Valor"));
                entrada.setEnde1(consult.getString("Ende1"));
                entrada.setNRed1(consult.getString("NRedOri"));
                entrada.setNRed2(consult.getString("NRedDst"));
                entrada.setQtde(consult.getString("Sangrias"));
                entrada.setValorRec(consult.getString("ValorApurado"));
                entrada.setData(consult.getString("Data"));
                retorno.add(entrada);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list guias em TesEntrada - " + e.getMessage());
        }
        return retorno;
    }

    /**
     * Lista entradas em TesEntrada
     *
     * @param codCli1
     * @param codFil
     * @param serie
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TesEntrada> listaTesEntrada(String codCli1, String codFil, String serie, Persistencia persistencia) throws Exception {
        try {
            List<TesEntrada> retorno = new ArrayList<>();
            String sql = " SELECT TOP 2000 *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM TesEntrada WHERE CodCli1 = ? AND CodFil = ? AND Serie = ? \n"
                    + " ORDER BY Guia DESC";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli1);
            consulta.setString(codFil);
            consulta.setString(serie);
            consulta.select();
            TesEntrada tesEntrada;
            while (consulta.Proximo()) {
                tesEntrada = new TesEntrada();
                tesEntrada.setCodFil(consulta.getString("CodFil"));
                tesEntrada.setGuia(consulta.getString("Guia"));
                tesEntrada.setSerie(consulta.getString("Serie"));
                tesEntrada.setData(consulta.getString("DataC"));
                tesEntrada.setCodCli1(consulta.getString("CodCli1"));
                tesEntrada.setCodCli2(consulta.getString("CodCli2"));
                tesEntrada.setTipoMov(consulta.getString("TipoMov"));
                tesEntrada.setLote(consulta.getString("Lote"));
                tesEntrada.setSituacao(consulta.getString("Situacao"));
                tesEntrada.setContaTes(consulta.getString("ContaTes"));
                tesEntrada.setValor(consulta.getString("Valor"));
                tesEntrada.setValorRec(consulta.getString("ValorRec"));
                tesEntrada.setTotalDN(consulta.getString("TotalDN"));
                tesEntrada.setMatrConf(consulta.getString("MatrConf"));
                tesEntrada.setHrInicio(consulta.getString("HrInicio"));
                tesEntrada.setHrFinal(consulta.getString("HrFinal"));
                tesEntrada.setTempo(consulta.getString("Tempo"));
                tesEntrada.setCodSrv(consulta.getString("CodSrv"));
                tesEntrada.setNroMaqConf(consulta.getString("NroMaqConf"));
                tesEntrada.setOperRec(consulta.getString("OperRec"));
                tesEntrada.setOperador(consulta.getString("Operador"));
                tesEntrada.setDt_Alter(consulta.getString("Dt_AlterC"));
                tesEntrada.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesEntrada);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.listaTesEntrada - " + e.getMessage() + "\r\n"
                    + " SELECT *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM TesEntrada WHERE CodCli1 = " + codCli1 + " AND CodFil = " + codFil + " AND Serie = " + serie);
        }
    }

    /**
     * Lista entradas em TesEntrada
     *
     * @param codCli1
     * @param codFil
     * @param serie
     * @param data
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TesEntrada> listaTesEntrada(String codCli1, String codFil, String serie, String data, Persistencia persistencia) throws Exception {
        try {
            List<TesEntrada> retorno = new ArrayList<>();
            String sql = " SELECT Pessoa.Nome, TesEntrada.*, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, TesEntrada.Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM TesEntrada \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = TesEntrada.MatrConf "
                    + "WHERE CodCli1 = ? AND CodFil = ? AND Serie = ? AND data = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli1);
            consulta.setString(codFil);
            consulta.setString(serie);
            consulta.setString(data);
            consulta.select();
            TesEntrada tesEntrada;
            while (consulta.Proximo()) {
                tesEntrada = new TesEntrada();
                tesEntrada.setCodFil(consulta.getString("CodFil"));
                tesEntrada.setGuia(consulta.getString("Guia"));
                tesEntrada.setSerie(consulta.getString("Serie"));
                tesEntrada.setData(consulta.getString("DataC"));
                tesEntrada.setCodCli1(consulta.getString("CodCli1"));
                tesEntrada.setCodCli2(consulta.getString("CodCli2"));
                tesEntrada.setTipoMov(consulta.getString("TipoMov"));
                tesEntrada.setLote(consulta.getString("Lote"));
                tesEntrada.setSituacao(consulta.getString("Situacao"));
                tesEntrada.setContaTes(consulta.getString("ContaTes"));
                tesEntrada.setValor(consulta.getString("Valor"));
                tesEntrada.setValorRec(consulta.getString("ValorRec"));
                tesEntrada.setTotalDN(consulta.getString("TotalDN"));
                tesEntrada.setMatrConf(consulta.getString("MatrConf"));
                tesEntrada.setHrInicio(consulta.getString("HrInicio"));
                tesEntrada.setHrFinal(consulta.getString("HrFinal"));
                tesEntrada.setTempo(consulta.getString("Tempo"));
                tesEntrada.setCodSrv(consulta.getString("CodSrv"));
                tesEntrada.setNroMaqConf(consulta.getString("NroMaqConf"));
                tesEntrada.setOperRec(consulta.getString("OperRec"));
                tesEntrada.setOperador(consulta.getString("Nome"));
                tesEntrada.setDt_Alter(consulta.getString("Dt_AlterC"));
                tesEntrada.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesEntrada);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.listaTesEntrada - " + e.getMessage() + "\r\n"
                    + " SELECT *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM TesEntrada WHERE CodCli1 = " + codCli1 + " AND CodFil = " + codFil + " AND Serie = " + serie + " AND Data = " + data);
        }
    }

    /**
     * Verifica a existencia de entrada nas tabelas
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String Sangria(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "SELECT  max(docto) docto from tesentdn "
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto like 'aut%'";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            String sangriadn = "AUT0000";
            try {
                while (consult.Proximo()) {
                    sangriadn = consult.getString("docto");
                }
            } catch (Exception e2) {
                sangriadn = "AUT0001";
            }
            if (!sangriadn.contains("AUT")) {
                sangriadn = "AUT0000";
            }
            sql = "SELECT  max(docto) docto from tesentmd "
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto like 'aut%'";
            Consulta consult2 = new Consulta(sql, persistencia);
            consult2.setString(guia);
            consult2.setString(serie);
            consult2.select();
            String sangriamd = "AUT0001";
            try {
                while (consult2.Proximo()) {
                    sangriamd = consult2.getString("docto");
                }
            } catch (Exception e2) {
                sangriamd = "AUT0000";
            }
            consult2.Close();
            if (!sangriamd.contains("AUT")) {
                sangriamd = "AUT0000";
            }
            sql = "SELECT  max(docto) docto from TesEntSangrias "
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto like 'aut%'";
            Consulta consult3 = new Consulta(sql, persistencia);
            consult3.setString(guia);
            consult3.setString(serie);
            consult3.select();
            String sangriaes = "AUT0001";
            try {
                while (consult3.Proximo()) {
                    sangriaes = consult3.getString("docto");
                }
            } catch (Exception e2) {
                sangriaes = "AUT0000";
            }
            consult3.Close();
            if (!sangriaes.contains("AUT")) {
                sangriaes = "AUT0000";
            }
            int dn = Integer.parseInt(sangriadn.substring(4));
            int md = Integer.parseInt(sangriamd.substring(4));
            int es = Integer.parseInt(sangriaes.substring(4));

            int res = dn > md ? dn : md;
            res = res > es ? res : es;

            String docto = "AUT" + FuncoesString.PreencheEsquerda(String.valueOf(res + 1), 4, "0");

            return docto;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar sangria - " + e.getMessage());
        }
    }

    /**
     * Retorna o valor corrente da recontagem
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String valorRecontagem(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT  valorrec from tesentrada "
                    + " Where Guia = ?"
                    + "  and Serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            String retorno = "";
            try {
                while (consult.Proximo()) {
                    retorno = consult.getString("valorrec");
                }
            } catch (Exception e2) {
                retorno = "0";
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar valorrec tesentrada - " + e.getMessage());
        }
    }

    /**
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @throws Exception
     */
    public void atualizaHr_Rec(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = " update tesentrada "
                    + " set hr_rec = case when (hr_rec is null) then substring (convert (varchar,getdate(),114),1,5) else hr_rec end "
                    + " where guia = ? and serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar hr_rec tesentrada - " + e.getMessage());
        }
    }

    /**
     * Finaliza a sangria em TesEntrada
     *
     * @param valorRec
     * @param operador
     * @param guia
     * @param serie
     * @param codfil
     * @param persistencia
     * @throws Exception
     */
    public void finalizaSangria(BigDecimal valorRec, String operador, String guia, String serie, String codfil, Persistencia persistencia) throws Exception {
        try {
            String tesentsangrias = "select difmaior, difmenor from TesEntSangrias where guia = ? and serie = ? ";
            Consulta consulta = new Consulta(tesentsangrias, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            BigDecimal difmaior = BigDecimal.ZERO, difmenor = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                difmaior = difmaior.add(consulta.getBigDecimal("difmaior") == null ? BigDecimal.ZERO : consulta.getBigDecimal("difmaior"));
                difmenor = difmenor.add(consulta.getBigDecimal("difmenor") == null ? BigDecimal.ZERO : consulta.getBigDecimal("difmenor"));
                System.out.println(difmenor);
            }
            consulta.Close();

            consulta = new Consulta("select valor from tesentrada where guia = ? and serie = ? and codfil = ?", persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codfil);
            consulta.select();
            BigDecimal valor = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                valor = consulta.getBigDecimal("valor");
            }
            consulta.Close();

            String sql;
            try {
                if (valorRec.add(difmenor).subtract(difmaior).compareTo(valor) == 0) {
                    sql = "update TesEntrada set valorrec = ?, situacao = 'S', ChequesQtde = 0, "
                            + " ChequesValor = 0, TicketsQtde = 0, TicketsValor = 0, OCTQtde = 0, "
                            + " OCTValor = 0, DifMaior = case when (DifMaior is null) then 0 else DifMaior end, "
                            + " DifMenor = case when (DifMenor is null) then 0 else DifMenor end, "
                            + " DivMaior = case when (DivMaior is null) then 0 else DivMaior end, "
                            + " DivMenor = case when (DivMenor is null) then 0 else DivMenor end, "
                            + " CedFalsaQtde = 0, CedFalsaValor = 0, MoedasValor = 0, TotalDD = 0, "
                            + " TotalDN = (select sum(valor) from tesentdn where guia = ? and serie = ?), "
                            + " TotalMoeda = (select sum(valor) from tesentmd where guia = ? and serie = ?), "
                            + " OperRec = ?, Dt_Rec = ? "
                            + " where Guia = ? "
                            + " and Serie =  ? "
                            + " and codfil = ? ";
                } else {
                    sql = "update TesEntrada set valorrec = ?, ChequesQtde = 0, "
                            + " ChequesValor = 0, TicketsQtde = 0, TicketsValor = 0, OCTQtde = 0, "
                            + " OCTValor = 0, DifMaior = case when (DifMaior is null) then 0 else DifMaior end, "
                            + " DifMenor = case when (DifMenor is null) then 0 else DifMenor end, "
                            + " DivMaior = case when (DivMaior is null) then 0 else DivMaior end, "
                            + " DivMenor = case when (DivMenor is null) then 0 else DivMenor end, "
                            + " CedFalsaQtde = 0, CedFalsaValor = 0, MoedasValor = 0, TotalDD = 0, "
                            + " TotalDN = (select sum(valor) from tesentdn where guia = ? and serie = ?), "
                            + " TotalMoeda = (select sum(valor) from tesentmd where guia = ? and serie = ?), "
                            + " OperRec = ?, Dt_Rec = ? "
                            + " where Guia = ? "
                            + " and Serie =  ? "
                            + " and codfil = ? ";
                }
            } catch (Exception e2) {
                sql = "update TesEntrada set valorrec = ?, ChequesQtde = 0, "
                        + " ChequesValor = 0, TicketsQtde = 0, TicketsValor = 0, OCTQtde = 0, "
                        + " OCTValor = 0, DifMaior = case when (DifMaior is null) then 0 else DifMaior end, "
                        + " DifMenor = case when (DifMenor is null) then 0 else DifMenor end, "
                        + " DivMaior = case when (DivMaior is null) then 0 else DivMaior end, "
                        + " DivMenor = case when (DivMenor is null) then 0 else DivMenor end, "
                        + " CedFalsaQtde = 0, CedFalsaValor = 0, MoedasValor = 0, "
                        + " TotalDN = (select sum(valor) from tesentdn where guia = ? and serie = ?), "
                        + " TotalMoeda = (select sum(valor) from tesentmd where guia = ? and serie = ?), "
                        + " OperRec = ?, Dt_Rec = ? "
                        + " where Guia = ? "
                        + " and Serie =  ? "
                        + " and codfil = ? ";
            }
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(valorRec);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(guia);
            consulta.setString(serie);

            String operadorTrat = "MOB-" + operador;

            if (operadorTrat.length() > 10) {
                operadorTrat = operadorTrat.substring(0, 10);
            }

            consulta.setString(operadorTrat);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codfil);
            consulta.update();

            // Atualizar dados de tempo
            sql = "";
            sql = "UPDATE TesEntrada\n"
                    + "SET TesEntrada.MatrConf = TesEntStat.Matr,\n"
                    + "    TesEntrada.HrInicio = TesEntStat.HrInicio,\n"
                    + "    TesEntrada.HrFinal  = TesEntStat.HrFinal,\n"
                    + "    TesEntrada.Tempo    = TesEntStat.Tempo,\n"
                    + "    TesEntrada.Operador = ?\n"
                    + "FROM TesEntrada\n"
                    + "JOIN (SELECT TOP 1 *\n"
                    + "      FROM TesEntStat AS X\n"
                    + "      WHERE X.Guia  = ?\n"
                    + "      AND   X.Serie = ?\n"
                    + "      ORDER BY X.Dt_alter DESC, X.Hr_Alter DESC) TesEntStat\n"
                    + "  ON TesEntStat.Guia  = TesEntrada.Guia\n"
                    + " AND TesEntStat.Serie = TesEntrada.Serie\n"
                    + "WHERE TesEntStat.Guia   = ?\n"
                    + "AND   TesEntStat.Serie  = ?\n"
                    + "AND   TesEntrada.CodFil = ?";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(operadorTrat);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codfil);
            consulta.update();

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao finalizar sangria da guia em TesEntrada - " + e.getMessage());
        }
    }

    /**
     * Atualiza o valor da diferença de acordo com o tipo da diferença
     *
     * @param tipo
     * @param diferenca
     * @param guia
     * @param serie
     * @param codfil
     * @param persistencia
     * @throws Exception
     */
    public void atualizaDiferenca(String tipo, BigDecimal diferenca, String guia, String serie, String codfil, Persistencia persistencia) throws Exception {
        try {
            String sql = "update TesEntrada set ";
            switch (tipo.toUpperCase()) {
                case "+":
                    sql += "difmaior = ? ";
                    break;
                case "-":
                    sql += "difmenor = ? ";
                    break;
                case "V+":
                    sql += "divmaior = ? ";
                    break;
                case "V-":
                    sql += "divmenor = ? ";
                    break;
                default:
                    break;
            }
            sql += " where Guia = ? "
                    + " and Serie =  ? "
                    + " and codfil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(diferenca);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codfil);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar diferenca em TesEntrada - " + e.getMessage());
        }
    }

    /**
     * Retorna o valor corrente de difereças
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String valorDiferenca(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = " select (difmaior + divmaior - difmenor - divmenor) dif "
                    + " from tesentrada "
                    + " where guia = ? and serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            String retorno = "";
            try {
                while (consult.Proximo()) {
                    retorno = consult.getString("dif");
                }
                if ("".equals(retorno)) {
                    throw new Exception("valor vazio");
                }
            } catch (Exception e2) {
                retorno = "0";
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar valor difs tesentrada - " + e.getMessage());
        }
    }

    /**
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public TesEntrada diferencas(String guia, String serie, Persistencia persistencia) throws Exception {
        TesEntrada retorno = new TesEntrada();
        try {
            String sql = "select difmaior, divmaior, difmenor, divmenor "
                    + " from tesentrada "
                    + " where guia = ? and serie = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            while (consulta.Proximo()) {
                retorno.setDifMaior(consulta.getString("difmaior"));
                retorno.setDifMenor(consulta.getString("difmenor"));
                retorno.setDivMaior(consulta.getString("divmaior"));
                retorno.setDivMenor(consulta.getString("divmenor"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Falha ao buscar diferenças - " + e.getMessage());
        }
    }

    /**
     *
     * @param first
     * @param pageSize
     * @param codFil
     * @param data
     * @param somentePendentes
     * @param guia
     * @param cliente1
     * @param cliente2
     * @return
     * @throws Exception
     */
    public List<TesEntrada> allTesEntradasPaginada(
            int first,
            int pageSize,
            BigDecimal codFil,
            String data,
            boolean somentePendentes,
            String guia,
            String cliente1,
            String cliente2
    ) throws Exception {
        String sql = "SELECT * FROM(\n"
                + "SELECT ROW_NUMBER() OVER ( ORDER BY Data, TesEntrada.Dt_Alter, TesEntrada.Hr_Alter) AS RowNum,"
                + "    TesEntrada.CodFil,\n"
                + "    TesEntrada.Guia,\n"
                + "    TesEntrada.Serie,\n"
                + "    TesEntrada.Data,\n"
                + "    TesEntrada.CodCli1,\n"
                + "    TesEntrada.CodCli2,\n"
                + "    TesEntrada.TipoMov,\n"
                + "    TesEntrada.CodSrv,\n"
                + "    TesEntrada.Lote,\n"
                + "    TesEntrada.ContaTes,\n"
                + "    TesEntrada.OS,\n"
                + "    TesEntrada.Valor,\n"
                + "    TesEntrada.Qtde,\n"
                + "    TesEntrada.ChequesQtde,\n"
                + "    TesEntrada.ChequesValor,\n"
                + "    TesEntrada.TicketsQtde,\n"
                + "    TesEntrada.TicketsValor,\n"
                + "    TesEntrada.MoedasValor,\n"
                + "    TesEntrada.OCTQtde,\n"
                + "    TesEntrada.OCTValor,\n"
                + "    TesEntrada.ValorRec,\n"
                + "    TesEntrada.DifMaior,\n"
                + "    TesEntrada.DifMenor,\n"
                + "    TesEntrada.DivMaior,\n"
                + "    TesEntrada.DivMenor,\n"
                + "    TesEntrada.CedFalsaQtde,\n"
                + "    TesEntrada.CedFalsaValor,\n"
                + "    TesEntrada.MatrConf,\n"
                + "    TesEntrada.HrInicio,\n"
                + "    TesEntrada.HrFinal,\n"
                + "    TesEntrada.Tempo,\n"
                + "    TesEntrada.TotalDN,\n"
                + "    TesEntrada.TotalDD,\n"
                + "    TesEntrada.TotalMoeda,\n"
                + "    TesEntrada.QtdeCed,\n"
                + "    TesEntrada.Obs,\n"
                + "    TesEntrada.Situacao,\n"
                + "    TesEntrada.Faturar,\n"
                + "    TesEntrada.NroMaqConf,\n"
                + "    TesEntrada.OperIncl,\n"
                + "    TesEntrada.Dt_Incl,\n"
                + "    TesEntrada.Hr_Incl,\n"
                + "    TesEntrada.Operador,\n"
                + "    TesEntrada.Dt_Alter,\n"
                + "    TesEntrada.Hr_Alter,\n"
                + "    TesEntrada.OperRec,\n"
                + "    TesEntrada.Dt_Rec,\n"
                + "    TesEntrada.Hr_Rec,\n"
                + "    Clientes1.NRed NRedOri,\n"
                + "    Clientes2.NRed NRedDst,\n"
                + "    TesContas.Descricao ContaDesc,\n"
                + "    CxfGuias.Remessa,\n"
                + "    CxFGuias.OS CxFGuiasOS,\n"
                + "    (SELECT COUNT(*) Qtde FROM CxFGuiasVol\n"
                + "    WHERE CxfGuiasVol.Guia = TesEntrada.Guia\n"
                + "    AND CxfGuiasVol.Serie = Tesentrada.Serie) QtdeVol\n"
                + "FROM TesEntrada\n"
                + "LEFT JOIN Clientes Clientes1 ON (TesEntrada.CodCli1 = Clientes1.Codigo)\n"
                + "AND (Clientes1.CodFil = TesEntrada.CodFil)\n"
                + "LEFT JOIN  Clientes Clientes2 ON (TesEntrada.CodCli2 = Clientes2.Codigo)\n"
                + "    AND (Clientes2.CodFil = TesEntrada.CodFil)\n"
                + "LEFT JOIN TesContas ON TesContas.Codigo = TesEntrada.ContaTes\n"
                + "LEFT JOIN CxfGuias ON CxfGuias.Guia = TesEntrada.Guia\n"
                + "    AND CxfGuias.Serie = TEsEntrada.Serie\n"
                + "WHERE TesEntrada.Data = ?\n"
                + "    AND TesEntrada.CodFil = ?\n"
                + "    AND TesEntrada.Serie <> 'ZGE'\n";

        if (guia != null && !guia.equals("")) {
            sql += "    AND TesEntrada.Guia = ?\n";
        }
        if (cliente1 != null && !cliente1.equals("")) {
            sql += "    AND Clientes1.NRed LIKE ?\n";
        }
        if (cliente2 != null && !cliente2.equals("")) {
            sql += "    AND Clientes2.NRed LIKE ?\n";
        }
        if (somentePendentes) {
            sql += "    AND TesEntrada.Situacao <> 'S'\n";
        }

        sql += ") AS RowConstrainedResult\n"
                + "WHERE   RowNum >= ?\n"
                + "    AND RowNum <= ?\n"
                + "ORDER BY RowNum;\n";

        List<TesEntrada> lista = new ArrayList<>();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setBigDecimal(codFil);

            if (guia != null && !guia.equals("")) {
                consulta.setBigDecimal(guia);
            }
            if (cliente1 != null && !cliente1.equals("")) {
                consulta.setString("%" + cliente1 + "%");
            }
            if (cliente2 != null && !cliente2.equals("")) {
                consulta.setString("%" + cliente2 + "%");
            }

            consulta.setInt(first);
            consulta.setInt(first + pageSize);
            consulta.select();

            while (consulta.Proximo()) {
                TesEntrada tesEntrada = new TesEntrada();

                tesEntrada.setCodFil(consulta.getString("CodFil"));
                tesEntrada.setGuia(consulta.getString("Guia"));
                tesEntrada.setSerie(consulta.getString("Serie"));
                tesEntrada.setData(consulta.getString("Data"));
                tesEntrada.setCodCli1(consulta.getString("CodCli1"));
                tesEntrada.setCodCli2(consulta.getString("CodCli2"));
                tesEntrada.setTipoMov(consulta.getString("TipoMov"));
                tesEntrada.setCodSrv(consulta.getString("CodSrv"));
                tesEntrada.setLote(consulta.getString("Lote"));
                tesEntrada.setContaTes(consulta.getString("ContaTes"));
                tesEntrada.setOS(consulta.getString("CxFGuiasOS"));
                tesEntrada.setValor(consulta.getString("Valor"));
                tesEntrada.setQtde(consulta.getString("Qtde"));
                tesEntrada.setChequesQtde(consulta.getString("ChequesQtde"));
                tesEntrada.setChequesValor(consulta.getString("ChequesValor"));
                tesEntrada.setTicketsQtde(consulta.getString("TicketsQtde"));
                tesEntrada.setTicketsValor(consulta.getString("TicketsValor"));
                tesEntrada.setMoedasValor(consulta.getString("MoedasValor"));
                tesEntrada.setOCTQtde(consulta.getString("OCTQtde"));
                tesEntrada.setOCTValor(consulta.getString("OCTValor"));
                tesEntrada.setValorRec(consulta.getString("ValorRec"));
                tesEntrada.setDifMaior(consulta.getString("DifMaior"));
                tesEntrada.setDifMenor(consulta.getString("DifMenor"));
                tesEntrada.setDivMaior(consulta.getString("DivMaior"));
                tesEntrada.setDivMenor(consulta.getString("DivMenor"));
                tesEntrada.setCedFalsaQtde(consulta.getString("CedFalsaQtde"));
                tesEntrada.setCedFalsaValor(consulta.getString("CedFalsaValor"));
                tesEntrada.setMatrConf(consulta.getString("MatrConf"));
                tesEntrada.setHrInicio(consulta.getString("HrInicio"));
                tesEntrada.setHrFinal(consulta.getString("HrFinal"));
                tesEntrada.setTempo(consulta.getString("Tempo"));
                tesEntrada.setTotalDN(consulta.getString("TotalDN"));
                tesEntrada.setTotalDD(consulta.getString("TotalDD"));
                tesEntrada.setTotalMoeda(consulta.getString("TotalMoeda"));
                tesEntrada.setQtdeCed(consulta.getString("QtdeCed"));
                tesEntrada.setObs(consulta.getString("Obs"));
                tesEntrada.setSituacao(consulta.getString("Situacao"));
                tesEntrada.setFaturar(consulta.getString("Faturar"));
                tesEntrada.setNroMaqConf(consulta.getString("NroMaqConf"));
                tesEntrada.setOperIncl(consulta.getString("OperIncl"));
                tesEntrada.setDt_Incl(consulta.getString("Dt_Incl"));
                tesEntrada.setHr_Incl(consulta.getString("Hr_Incl"));
                tesEntrada.setOperador(consulta.getString("Operador"));
                tesEntrada.setDt_Alter(consulta.getString("Dt_Alter"));
                tesEntrada.setHr_Alter(consulta.getString("Hr_Alter"));
                tesEntrada.setOperRec(consulta.getString("OperRec"));
                tesEntrada.setDt_Rec(consulta.getString("Dt_Rec"));
                tesEntrada.setHr_Rec(consulta.getString("Hr_Rec"));
                tesEntrada.setNRed1(consulta.getString("NRedOri"));
                tesEntrada.setNRed2(consulta.getString("NRedDst"));
                tesEntrada.setContaDesc(consulta.getString("ContaDesc"));
                tesEntrada.setRemessa(consulta.getBigDecimal("Remessa"));
                tesEntrada.setQtdeVol(consulta.getString("QtdeVol"));

                lista.add(tesEntrada);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return lista;
    }

    /**
     *
     * @param codFil
     * @param data
     * @param somentePendentes
     * @param guia
     * @param cliente1
     * @param cliente2
     * @return
     * @throws Exception
     */
    public int contagemTesEntradas(
            BigDecimal codFil,
            String data,
            boolean somentePendentes,
            String guia,
            String cliente1,
            String cliente2
    ) throws Exception {
        int total = 0;
        String sql = "SELECT COUNT(*) AS total\n"
                + "FROM TesEntrada\n"
                + "LEFT JOIN Clientes Clientes1 ON (TesEntrada.CodCli1 = Clientes1.Codigo)\n"
                + "AND (Clientes1.CodFil = TesEntrada.CodFil)\n"
                + "LEFT JOIN  Clientes Clientes2 ON (TesEntrada.CodCli2 = Clientes2.Codigo)\n"
                + "    AND (Clientes2.CodFil = TesEntrada.CodFil)\n"
                + "LEFT JOIN TesContas ON TesContas.Codigo = TesEntrada.ContaTes\n"
                + "LEFT JOIN CxfGuias ON CxfGuias.Guia = TesEntrada.Guia\n"
                + "    AND CxfGuias.Serie = TEsEntrada.Serie\n"
                + "WHERE TesEntrada.Data = ?\n"
                + "    AND TesEntrada.CodFil = ?\n"
                + "    AND TesEntrada.Serie <> 'ZGE'\n";

        if (guia != null && !guia.equals("")) {
            sql += "    AND TesEntrada.Guia = ?\n";
        }
        if (cliente1 != null && !cliente1.equals("")) {
            sql += "    AND Clientes1.NRed LIKE ?\n";
        }
        if (cliente2 != null && !cliente2.equals("")) {
            sql += "    AND Clientes2.NRed LIKE ?\n";
        }
        if (somentePendentes) {
            sql += "    AND TesEntrada.Situacao <> 'S'\n";
        }

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setBigDecimal(codFil);

            if (guia != null && !guia.equals("")) {
                consulta.setBigDecimal(guia);
            }
            if (cliente1 != null && !cliente1.equals("")) {
                consulta.setString("%" + cliente1 + "%");
            }
            if (cliente2 != null && !cliente2.equals("")) {
                consulta.setString("%" + cliente2 + "%");
            }

            consulta.select();

            while (consulta.Proximo()) {
                total = consulta.getInt("total");
                break;
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return total;
    }

    /**
     * Busca uma TesEntrada no banco com este id
     *
     * @param codFil
     * @param guia
     * @param serie
     * @param codPessoa
     * @param banco
     * @return TesEntrada encontrada, ou null
     * @throws Exception
     */
    public TesEntrada getTesEntradasById(
            String codFil,
            String guia,
            String serie,
            String codPessoa,
            String banco
    ) throws Exception {
        String sql = ";SELECT TOP 1\n"
                + "    TesEntrada.CodFil 'TesEntrada.CodFil',\n"
                + "    TesEntrada.Guia 'TesEntrada.Guia',\n"
                + "    TesEntrada.Serie 'TesEntrada.Serie',\n"
                + "    TesEntrada.Data 'TesEntrada.Data',\n"
                + "    TesEntrada.CodCli1 'TesEntrada.CodCli1',\n"
                + "    TesEntrada.CodCli2 'TesEntrada.CodCli2',\n"
                + "    TesEntrada.TipoMov 'TesEntrada.TipoMov',\n"
                + "    TesEntrada.CodSrv 'TesEntrada.CodSrv',\n"
                + "    TesEntrada.Lote 'TesEntrada.Lote',\n"
                + "    TesEntrada.ContaTes 'TesEntrada.ContaTes',\n"
                + "    TesEntrada.OS 'TesEntrada.OS',\n"
                + "    TesEntrada.Valor 'TesEntrada.Valor',\n"
                + "    TesEntrada.Qtde 'TesEntrada.Qtde',\n"
                + "    TesEntrada.ChequesQtde 'TesEntrada.ChequesQtde',\n"
                + "    TesEntrada.ChequesValor 'TesEntrada.ChequesValor',\n"
                + "    TesEntrada.TicketsQtde 'TesEntrada.TicketsQtde',\n"
                + "    TesEntrada.TicketsValor 'TesEntrada.TicketsValor',\n"
                + "    TesEntrada.MoedasValor 'TesEntrada.MoedasValor',\n"
                + "    TesEntrada.OCTQtde 'TesEntrada.OCTQtde',\n"
                + "    TesEntrada.OCTValor 'TesEntrada.OCTValor',\n"
                + "    TesEntrada.ValorRec 'TesEntrada.ValorRec',\n"
                + "    TesEntrada.DifMaior 'TesEntrada.DifMaior',\n"
                + "    TesEntrada.DifMenor 'TesEntrada.DifMenor',\n"
                + "    TesEntrada.DivMaior 'TesEntrada.DivMaior',\n"
                + "    TesEntrada.DivMenor 'TesEntrada.DivMenor',\n"
                + "    TesEntrada.CedFalsaQtde 'TesEntrada.CedFalsaQtde',\n"
                + "    TesEntrada.CedFalsaValor 'TesEntrada.CedFalsaValor',\n"
                + "    TesEntrada.MatrConf 'TesEntrada.MatrConf',\n"
                + "    TesEntrada.HrInicio 'TesEntrada.HrInicio',\n"
                + "    TesEntrada.HrFinal 'TesEntrada.HrFinal',\n"
                + "    TesEntrada.Tempo 'TesEntrada.Tempo',\n"
                + "    TesEntrada.TotalDN 'TesEntrada.TotalDN',\n"
                + "    TesEntrada.TotalDD 'TesEntrada.TotalDD',\n"
                + "    TesEntrada.TotalMoeda 'TesEntrada.TotalMoeda',\n"
                + "    TesEntrada.QtdeCed 'TesEntrada.QtdeCed',\n"
                + "    TesEntrada.Obs 'TesEntrada.Obs',\n"
                + "    TesEntrada.Situacao 'TesEntrada.Situacao',\n"
                + "    TesEntrada.Faturar 'TesEntrada.Faturar',\n"
                + "    TesEntrada.NroMaqConf 'TesEntrada.NroMaqConf',\n"
                + "    TesEntrada.OperIncl 'TesEntrada.OperIncl',\n"
                + "    TesEntrada.Dt_Incl 'TesEntrada.Dt_Incl',\n"
                + "    TesEntrada.Hr_Incl 'TesEntrada.Hr_Incl',\n"
                + "    TesEntrada.Operador 'TesEntrada.Operador',\n"
                + "    TesEntrada.Dt_Alter 'TesEntrada.Dt_Alter',\n"
                + "    TesEntrada.Hr_Alter 'TesEntrada.Hr_Alter',\n"
                + "    TesEntrada.OperRec 'TesEntrada.OperRec',\n"
                + "    TesEntrada.Dt_Rec 'TesEntrada.Dt_Rec',\n"
                + "    TesEntrada.Hr_Rec 'TesEntrada.Hr_Rec',\n"
                + "    Clientes1.NRed 'Clientes1.NRed',\n"
                + "    Clientes2.NRed 'Clientes2.NRed',\n"
                + "    TesContas.Descricao 'TesContas.Descricao',\n"
                + "    CxfGuias.Remessa 'CxfGuias.Remessa',\n"
                + "    CxFGuias.OS 'CxFGuias.OS',\n"
                + "    OS_Vig.Descricao 'OS_Vig.Descricao',\n"
                + "    OS_Vig.NRed 'OS_Vig.NRed',\n"
                + "    OS_Vig.NRedDst 'OS_Vig.NRedDst',\n"
                + "    OS_Vig.NRedFat 'OS_Vig.NRedFat',\n"
                + "    TesContas.Codigo 'TesContas.Codigo',\n"
                + "    TesContas.Descricao 'TesContas.Descricao',\n"
                + "    TipoSrvCli.Codigo 'TipoSrvCli.Codigo',\n"
                + "    TipoSrvCli.Descricao 'TipoSrvCli.Descricao',\n"
                + "    (\n"
                + "        SELECT COUNT ( * ) Qtde\n"
                + "        FROM CxFGuiasVol\n"
                + "        WHERE CxfGuiasVol.Guia = TesEntrada.Guia\n"
                + "            AND CxfGuiasVol.Serie = Tesentrada.Serie\n"
                + "    ) QtdeVol\n"
                + "    FROM\n"
                + "    TesEntrada\n"
                + "    LEFT JOIN Clientes Clientes1 ON ( TesEntrada.CodCli1 = Clientes1.Codigo )\n"
                + "    AND ( Clientes1.CodFil = TesEntrada.CodFil )\n"
                + "    LEFT JOIN Clientes Clientes2 ON ( TesEntrada.CodCli2 = Clientes2.Codigo )\n"
                + "    AND ( Clientes2.CodFil = TesEntrada.CodFil )\n"
                + "    LEFT JOIN TesContas ON TesContas.Codigo = TesEntrada.ContaTes\n"
                + "    LEFT JOIN CxfGuias ON CxfGuias.Guia = TesEntrada.Guia\n"
                + "    AND CxfGuias.Serie = TEsEntrada.Serie\n"
                + "    LEFT JOIN OS_Vig ON OS_Vig.OS = CxFGuias.OS\n"
                + "    AND OS_Vig.CodFil = TesEntrada.CodFil\n"
                + "    LEFT JOIN TipoSrvCli ON TipoSrvCli.Codigo = TesEntrada.Lote\n"
                + "    WHERE\n"
                + "    TesEntrada.CodFil = ? \n"
                + "    AND TesEntrada.Serie <> 'ZGE'\n"
                + "    AND TesEntrada.Guia = ? \n"
                + "    AND TesEntrada.Serie = ? \n"
                + "    AND OS_Vig.OS IS NOT NULL\n";
        /*+ "    AND OS_Vig.codfil IN (\n"
                + "        SELECT filiais.codfil\n"
                + "        FROM saspw\n"
                + "        INNER JOIN saspwfil ON saspwfil.nome = saspw.nome\n"
                + "        INNER JOIN filiais ON filiais.codfil = saspwfil.codfilac\n"
                //+ "        INNER JOIN paramet ON paramet.filial_pdr = filiais.codfil\n"
                + "        WHERE saspw.codpessoa = ? \n"
                //+ "            AND paramet.path = ? \n"
                + "    );";*/

        Consulta consulta = null;
        TesEntrada tesEntrada = null;
        try {
            consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(codFil);
            consulta.setBigDecimal(guia);
            consulta.setString(serie);
            //consulta.setBigDecimal(codPessoa);
            //consulta.setString(banco);
            consulta.select();

            if (consulta.Proximo()) {
                tesEntrada = new TesEntrada();

                tesEntrada.setCodFil(consulta.getString("TesEntrada.CodFil"));
                tesEntrada.setGuia(consulta.getString("TesEntrada.Guia"));
                tesEntrada.setSerie(consulta.getString("TesEntrada.Serie"));
                tesEntrada.setData(consulta.getString("TesEntrada.Data"));
                tesEntrada.setCodCli1(consulta.getString("TesEntrada.CodCli1"));
                tesEntrada.setCodCli2(consulta.getString("TesEntrada.CodCli2"));
                tesEntrada.setTipoMov(consulta.getString("TesEntrada.TipoMov"));
                tesEntrada.setCodSrv(consulta.getString("TesEntrada.CodSrv"));
                tesEntrada.setLote(consulta.getString("TesEntrada.Lote"));
                tesEntrada.setContaTes(consulta.getString("TesEntrada.ContaTes"));
                tesEntrada.setOS(consulta.getString("CxfGuias.OS"));
                tesEntrada.setValor(consulta.getString("TesEntrada.Valor"));
                tesEntrada.setQtde(consulta.getString("TesEntrada.Qtde"));
                tesEntrada.setChequesQtde(consulta.getString("TesEntrada.ChequesQtde"));
                tesEntrada.setChequesValor(consulta.getString("TesEntrada.ChequesValor"));
                tesEntrada.setTicketsQtde(consulta.getString("TesEntrada.TicketsQtde"));
                tesEntrada.setTicketsValor(consulta.getString("TesEntrada.TicketsValor"));
                tesEntrada.setMoedasValor(consulta.getString("TesEntrada.MoedasValor"));
                tesEntrada.setOCTQtde(consulta.getString("TesEntrada.OCTQtde"));
                tesEntrada.setOCTValor(consulta.getString("TesEntrada.OCTValor"));
                tesEntrada.setValorRec(consulta.getString("TesEntrada.ValorRec"));
                tesEntrada.setDifMaior(consulta.getString("TesEntrada.DifMaior"));
                tesEntrada.setDifMenor(consulta.getString("TesEntrada.DifMenor"));
                tesEntrada.setDivMaior(consulta.getString("TesEntrada.DivMaior"));
                tesEntrada.setDivMenor(consulta.getString("TesEntrada.DivMenor"));
                tesEntrada.setCedFalsaQtde(consulta.getString("TesEntrada.CedFalsaQtde"));
                tesEntrada.setCedFalsaValor(consulta.getString("TesEntrada.CedFalsaValor"));
                tesEntrada.setMatrConf(consulta.getString("TesEntrada.MatrConf"));
                tesEntrada.setHrInicio(consulta.getString("TesEntrada.HrInicio"));
                tesEntrada.setHrFinal(consulta.getString("TesEntrada.HrFinal"));
                tesEntrada.setTempo(consulta.getString("TesEntrada.Tempo"));
                tesEntrada.setTotalDN(consulta.getString("TesEntrada.TotalDN"));
                tesEntrada.setTotalDD(consulta.getString("TesEntrada.TotalDD"));
                tesEntrada.setTotalMoeda(consulta.getString("TesEntrada.TotalMoeda"));
                tesEntrada.setQtdeCed(consulta.getString("TesEntrada.QtdeCed"));
                tesEntrada.setObs(consulta.getString("TesEntrada.Obs"));
                tesEntrada.setSituacao(consulta.getString("TesEntrada.Situacao"));
                tesEntrada.setFaturar(consulta.getString("TesEntrada.Faturar"));
                tesEntrada.setNroMaqConf(consulta.getString("TesEntrada.NroMaqConf"));
                tesEntrada.setOperIncl(consulta.getString("TesEntrada.OperIncl"));
                tesEntrada.setDt_Incl(consulta.getString("TesEntrada.Dt_Incl"));
                tesEntrada.setHr_Incl(consulta.getString("TesEntrada.Hr_Incl"));
                tesEntrada.setOperador(consulta.getString("TesEntrada.Operador"));
                tesEntrada.setDt_Alter(consulta.getString("TesEntrada.Dt_Alter"));
                tesEntrada.setHr_Alter(consulta.getString("TesEntrada.Hr_Alter"));
                tesEntrada.setOperRec(consulta.getString("TesEntrada.OperRec"));
                tesEntrada.setDt_Rec(consulta.getString("TesEntrada.Dt_Rec"));
                tesEntrada.setHr_Rec(consulta.getString("TesEntrada.Hr_Rec"));
                tesEntrada.setNRed1(consulta.getString("Clientes1.NRed"));
                tesEntrada.setNRed2(consulta.getString("Clientes2.NRed"));
                tesEntrada.setContaDesc(consulta.getString("TesContas.Descricao"));
                tesEntrada.setRemessa(consulta.getBigDecimal("CxfGuias.Remessa"));
                tesEntrada.setQtdeVol(consulta.getString("QtdeVol"));

                // Clientes
                Clientes cliente1 = new Clientes(), cliente2 = new Clientes();
                cliente1.setNRed(consulta.getString("Clientes1.NRed"));
                cliente2.setNRed(consulta.getString("Clientes2.NRed"));
                tesEntrada.setClienteOrigem(cliente1);
                tesEntrada.setClienteDestino(cliente2);

                // OS
                OS_Vig os_vig = new OS_Vig();
                os_vig.setOS(consulta.getString("CxFGuias.OS"));
                os_vig.setDescricao(consulta.getString("OS_Vig.Descricao"));
                os_vig.setNRed(consulta.getString("OS_Vig.NRed"));
                os_vig.setNRedDst(consulta.getString("OS_Vig.NRedDst"));
                os_vig.setNRedFat(consulta.getString("OS_Vig.NRedFat"));
                tesEntrada.setOs_vig(os_vig);

                // Conta
                TesConta conta = new TesConta();
                conta.setCodigo(consulta.getString("TesContas.Codigo"));
                conta.setDescricao(consulta.getString("TesContas.Descricao"));
                tesEntrada.setConta(conta);

                // Lote
                TesConta lote = new TesConta();
                lote.setTipoSrvCodigo(consulta.getString("TipoSrvCli.Codigo"));
                lote.setTipoSrvDesc(consulta.getString("TipoSrvCli.Descricao"));
                tesEntrada.setTesLote(lote);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return tesEntrada;
    }

    /**
     *
     * @param tesEntrada
     * @throws Exception
     */
    public void gravarTesEntrada(TesEntrada tesEntrada) throws Exception {
        boolean update = false;

        if (tesEntrada.getCodFil() != null
                && tesEntrada.getGuia() != null
                && tesEntrada.getSerie() != null) {
            TesEntrada record = getTesEntradasById(
                    tesEntrada.getCodFil().toString(),
                    tesEntrada.getGuia().toString(),
                    tesEntrada.getSerie(),
                    "",
                    ""
            );
            if (record.getCodFil() != null && record.getGuia() != null && record.getSerie() != null) {
                update = true;
            }
        }

        String sqlUpdate = "UPDATE TesEntrada SET\n"
                + "    Data = ?, CodCli1 = ?,\n"
                + "    CodCli2 = ?, TipoMov = ?, CodSrv = ?, \n"
                + "    Lote = ?, ContaTes = ?, OS = ?, Valor = ?, \n"
                + "    Qtde = ?, ChequesQtde = ?, ChequesValor = ?, TicketsQtde = ?, \n"
                + "    TicketsValor = ?, MoedasValor = ?, OCTQtde = ?, OCTValor = ?, \n"
                + "    ValorRec = ?, DifMaior = ?, DifMenor = ?, DivMaior = ?, \n"
                + "    DivMenor = ?, CedFalsaQtde = ?, CedFalsaValor = ?, MatrConf = ?, \n"
                + "    HrInicio = ?, HrFinal = ?, Tempo = ?, TotalDN = ?, \n"
                + "    TotalDD = ?, TotalMoeda = ?, QtdeCed = ?, Obs = ?, \n"
                + "    Situacao = ?, Faturar = ?, NroMaqConf = ?, OperIncl = ?, \n"
                + "    Dt_Incl = ?, Hr_Incl = ?, Operador = ?, Dt_Alter = ?, \n"
                + "    Hr_Alter = ?, OperRec = ?, Dt_Rec = ?, Hr_Rec = ?\n"
                + "WHERE CodFil = ? AND Guia = ? AND Serie = ? ;";

        String sqlInsert = "INSERT INTO TesEntrada (\n"
                + "Data, CodCli1, CodCli2,\n"
                + "TipoMov, CodSrv, Lote, ContaTes,\n"
                + "OS, Valor, Qtde, ChequesQtde,\n"
                + "ChequesValor, TicketsQtde, TicketsValor, MoedasValor,\n"
                + "OCTQtde, OCTValor, ValorRec, DifMaior,\n"
                + "DifMenor, DivMaior, DivMenor, CedFalsaQtde,\n"
                + "CedFalsaValor, MatrConf, HrInicio, HrFinal,\n"
                + "Tempo, TotalDN, TotalDD, TotalMoeda, QtdeCed,\n"
                + "Obs, Situacao, Faturar, NroMaqConf,\n"
                + "OperIncl, Dt_Incl, Hr_Incl, Operador,\n"
                + "Dt_Alter, Hr_Alter, OperRec, Dt_Rec, Hr_Rec,\n"
                + "CodFil, Guia, Serie ) VALUES ("
                + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,\n"
                + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\n"
                + ");";

        Consulta consulta = null;

        try {
            consulta = new Consulta(update ? sqlUpdate : sqlInsert, persistencia);

            consulta.setString(tesEntrada.getData());
            consulta.setString(tesEntrada.getCodCli1());
            consulta.setString(tesEntrada.getCodCli2());
            consulta.setString(tesEntrada.getTipoMov());
            consulta.setString(tesEntrada.getCodSrv());
            consulta.setString(tesEntrada.getLote());
            consulta.setBigDecimal(tesEntrada.getContaTes());
            consulta.setBigDecimal(tesEntrada.getOS());
            consulta.setBigDecimal(tesEntrada.getValor());
            consulta.setBigDecimal(tesEntrada.getQtde());
            consulta.setBigDecimal(tesEntrada.getChequesQtde());
            consulta.setBigDecimal(tesEntrada.getChequesValor());
            consulta.setBigDecimal(tesEntrada.getTicketsQtde());
            consulta.setBigDecimal(tesEntrada.getTicketsValor());
            consulta.setBigDecimal(tesEntrada.getMoedasValor());
            consulta.setBigDecimal(tesEntrada.getOCTQtde());
            consulta.setBigDecimal(tesEntrada.getOCTValor());
            consulta.setBigDecimal(tesEntrada.getValorRec());
            consulta.setBigDecimal(tesEntrada.getDifMaior());
            consulta.setBigDecimal(tesEntrada.getDifMenor());
            consulta.setBigDecimal(tesEntrada.getDivMaior());
            consulta.setBigDecimal(tesEntrada.getDivMenor());
            consulta.setBigDecimal(tesEntrada.getCedFalsaQtde());
            consulta.setBigDecimal(tesEntrada.getCedFalsaValor());
            consulta.setBigDecimal(tesEntrada.getMatrConf());
            consulta.setString(tesEntrada.getHrInicio());
            consulta.setString(tesEntrada.getHrFinal());
            consulta.setBigDecimal(tesEntrada.getTempo());
            consulta.setBigDecimal(tesEntrada.getTotalDN());
            consulta.setBigDecimal(tesEntrada.getTotalDD());
            consulta.setBigDecimal(tesEntrada.getTotalMoeda());
            consulta.setBigDecimal(tesEntrada.getQtdeCed());
            consulta.setString(tesEntrada.getObs());
            consulta.setString(tesEntrada.getSituacao());
            consulta.setString(tesEntrada.getFaturar());
            consulta.setBigDecimal(tesEntrada.getNroMaqConf());
            consulta.setString(tesEntrada.getOperIncl());
            consulta.setString(tesEntrada.getDt_Incl());
            consulta.setString(tesEntrada.getHr_Incl());
            consulta.setString(tesEntrada.getOperador());
            consulta.setString(tesEntrada.getDt_Alter());
            consulta.setString(tesEntrada.getHr_Alter());
            consulta.setString(tesEntrada.getOperRec());
            consulta.setString(tesEntrada.getDt_Rec());
            consulta.setString(tesEntrada.getHr_Rec());
            // deve ficar por último:
            consulta.setBigDecimal(tesEntrada.getCodFil());
            consulta.setBigDecimal(tesEntrada.getGuia());
            consulta.setString(tesEntrada.getSerie());

            if (update) {
                consulta.update();
            } else {
                consulta.insert();
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    /**
     *
     * @param first
     * @param pageSize
     * @param Codigo
     * @param descricao
     * @return
     * @throws Exception
     */
    public List<TesConta> allTesContaPaginada(
            int first,
            int pageSize,
            String Codigo,
            String descricao
    ) throws Exception {
        String sql = "SELECT * FROM(\n"
                + "SELECT ROW_NUMBER() OVER ( ORDER BY tescontas.Codigo) AS RowNum,\n"
                + "tescontas.Codigo, tescontas.CodFil, tescontas.Tipo,\n"
                + "tescontas.Descricao, tescontas.CodSrv, tescontas.Operador,\n"
                + "tescontas.Dt_Alter, tescontas.Hr_Alter,\n"
                + "tiposrvcli.Codigo AS TipoSrvCodigo,\n"
                + "tiposrvcli.Descricao AS TipoSrvDesc,\n"
                + "tiposrvcli.Banco, tiposrvcli.TAtend, tiposrvcli.TCob,\n"
                + "tiposrvcli.TCar, tiposrvcli.TipoSrv, tiposrvcli.ER,\n"
                + "tiposrvcli.CodInterf, tiposrvcli.Aditivo,\n"
                + "tiposrvcli.Exportar, tiposrvcli.SubCentro,\n"
                + "tiposrvcli.Operador AS TipoSrvOperador,\n"
                + "tiposrvcli.Dt_Alter AS TipoSrvDt_Alter,\n"
                + "tiposrvcli.Hr_Alter AS TipoSrvHr_Alter\n"
                + "FROM tescontas\n"
                + "LEFT JOIN tiposrvcli ON tiposrvcli.codigo = tescontas.tipo\n"
                + "WHERE tescontas.Codigo IS NOT NULL\n";

        if (Codigo != null && !Codigo.equals("")) {
            sql += "AND tescontas.Codigo = ?\n";
        }
        if (descricao != null && !descricao.equals("")) {
            sql += "AND tescontas.Descricao LIKE ?\n";
        }

        sql += ") AS RowConstrainedResult\n"
                + "WHERE   RowNum >= ?\n"
                + "    AND RowNum <= ?\n"
                + "ORDER BY RowNum;\n";

        List<TesConta> lista = new ArrayList<>();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);

            if (Codigo != null && !Codigo.equals("")) {
                consulta.setBigDecimal(Codigo);
            }
            if (descricao != null && !descricao.equals("")) {
                consulta.setString(descricao);
            }

            consulta.setInt(first);
            consulta.setInt(first + pageSize);
            consulta.select();

            while (consulta.Proximo()) {
                TesConta tesConta = new TesConta();

                tesConta.setCodigo(consulta.getString("Codigo"));
                tesConta.setCodFil(consulta.getString("CodFil"));
                tesConta.setTipo(consulta.getString("Tipo"));
                tesConta.setCodSrv(consulta.getString("CodSrv"));
                tesConta.setDescricao(consulta.getString("Descricao"));
                tesConta.setTipoSrvDesc(consulta.getString("TipoSrvDesc"));
                tesConta.setExportar(consulta.getString("Exportar"));
                tesConta.setOperador(consulta.getString("Operador"));
                tesConta.setDt_Alter(consulta.getString("Dt_Alter"));
                tesConta.setHr_Alter(consulta.getString("Hr_Alter"));
                tesConta.setTipoSrvCodigo(consulta.getString("TipoSrvCodigo"));
                tesConta.setBanco(consulta.getString("Banco"));
                tesConta.setTAtend(consulta.getString("TAtend"));
                tesConta.setTCob(consulta.getString("TCob"));
                tesConta.setTCar(consulta.getString("TCar"));
                tesConta.setTipoSrv(consulta.getString("TipoSrv"));
                tesConta.setER(consulta.getString("ER"));
                tesConta.setCodInterf(consulta.getString("CodInterf"));
                tesConta.setAditivo(consulta.getString("Aditivo"));
                tesConta.setSubCentro(consulta.getString("SubCentro"));
                tesConta.setTipoSrvOperador(consulta.getString("TipoSrvOperador"));
                tesConta.setTipoSrvDt_Alter(consulta.getString("TipoSrvDt_Alter"));
                tesConta.setTipoSrvHr_Alter(consulta.getString("TipoSrvHr_Alter"));

                lista.add(tesConta);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return lista;
    }

    /**
     * Obtém Conta pelo id
     *
     * @param Codigo
     * @return Conta ou null
     * @throws Exception
     */
    public TesConta getTesContaById(String Codigo) throws Exception {
        String sql = "SELECT TOP 1\n"
                + "tescontas.Codigo,\n"
                + "tescontas.CodFil,\n"
                + "tescontas.Tipo,\n"
                + "tescontas.Descricao,\n"
                + "tescontas.CodSrv,\n"
                + "tescontas.Operador,\n"
                + "tescontas.Dt_Alter,\n"
                + "tescontas.Hr_Alter,\n"
                + "tiposrvcli.Descricao AS TipoSrvDesc,\n"
                + "tiposrvcli.Exportar\n"
                + "FROM tescontas\n"
                + "LEFT JOIN tiposrvcli ON tiposrvcli.codigo = tescontas.tipo\n"
                + "WHERE tescontas.Codigo = ? ;";

        TesConta tesConta = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(Codigo);
            consulta.select();

            if (consulta.Proximo()) {
                tesConta = new TesConta();

                tesConta.setCodigo(consulta.getString("Codigo"));
                tesConta.setCodFil(consulta.getString("CodFil"));
                tesConta.setTipo(consulta.getString("Tipo"));
                tesConta.setCodSrv(consulta.getString("CodSrv"));
                tesConta.setDescricao(consulta.getString("Descricao"));
                tesConta.setTipoSrvDesc(consulta.getString("TipoSrvDesc"));
                tesConta.setExportar(consulta.getString("Exportar"));
                tesConta.setOperador(consulta.getString("Operador"));
                tesConta.setDt_Alter(consulta.getString("Dt_Alter"));
                tesConta.setHr_Alter(consulta.getString("Hr_Alter"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return tesConta;
    }

    public List<TesConta> getTesConta() throws Exception {
        List<TesConta> Retorno = new ArrayList<>();

        String sql = "SELECT TOP 1\n"
                + "tescontas.Codigo,\n"
                + "tescontas.CodFil,\n"
                + "tescontas.Tipo,\n"
                + "tescontas.Descricao,\n"
                + "tescontas.CodSrv,\n"
                + "tescontas.Operador,\n"
                + "tescontas.Dt_Alter,\n"
                + "tescontas.Hr_Alter,\n"
                + "tiposrvcli.Descricao AS TipoSrvDesc,\n"
                + "tiposrvcli.Exportar\n"
                + "FROM tescontas\n"
                + "LEFT JOIN tiposrvcli ON tiposrvcli.codigo = tescontas.tipo\n"
                + "ORDER BY Descricao";

        TesConta tesConta = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.select();

            if (consulta.Proximo()) {
                tesConta = new TesConta();

                tesConta.setCodigo(consulta.getString("Codigo"));
                tesConta.setCodFil(consulta.getString("CodFil"));
                tesConta.setTipo(consulta.getString("Tipo"));
                tesConta.setCodSrv(consulta.getString("CodSrv"));
                tesConta.setDescricao(consulta.getString("Descricao"));
                tesConta.setTipoSrvDesc(consulta.getString("TipoSrvDesc"));
                tesConta.setExportar(consulta.getString("Exportar"));
                tesConta.setOperador(consulta.getString("Operador"));
                tesConta.setDt_Alter(consulta.getString("Dt_Alter"));
                tesConta.setHr_Alter(consulta.getString("Hr_Alter"));

                Retorno.add(tesConta);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return Retorno;
    }

    /**
     * Obtém Conta interbancária da tesouraria
     *
     * @return Conta ou null
     * @throws Exception
     */
    public TesConta getTesContaDeTesouraria() throws Exception {
        final String clause = "WHERE tescontas.Descricao LIKE '%INTERBANC%'";

        String sql = "SELECT TOP 1\n"
                + "tescontas.Codigo,\n"
                + "tescontas.CodFil,\n"
                + "tescontas.Tipo,\n"
                + "tescontas.Descricao,\n"
                + "tescontas.CodSrv,\n"
                + "tescontas.Operador,\n"
                + "tescontas.Dt_Alter,\n"
                + "tescontas.Hr_Alter,\n"
                + "tiposrvcli.Descricao AS TipoSrvDesc,\n"
                + "tiposrvcli.Exportar\n"
                + "FROM tescontas\n"
                + "LEFT JOIN tiposrvcli ON tiposrvcli.codigo = tescontas.tipo\n"
                + clause;

        TesConta tesConta = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.select();

            if (consulta.Proximo()) {
                tesConta = new TesConta();

                tesConta.setCodigo(consulta.getString("Codigo"));
                tesConta.setCodFil(consulta.getString("CodFil"));
                tesConta.setTipo(consulta.getString("Tipo"));
                tesConta.setCodSrv(consulta.getString("CodSrv"));
                tesConta.setDescricao(consulta.getString("Descricao"));
                tesConta.setTipoSrvDesc(consulta.getString("TipoSrvDesc"));
                tesConta.setExportar(consulta.getString("Exportar"));
                tesConta.setOperador(consulta.getString("Operador"));
                tesConta.setDt_Alter(consulta.getString("Dt_Alter"));
                tesConta.setHr_Alter(consulta.getString("Hr_Alter"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return tesConta;
    }

    /**
     *
     * @param first
     * @param pageSize
     * @param codigo
     * @param descricao
     * @param banco
     * @return
     * @throws Exception
     */
    public List<TesConta> allTiposrvcliPaginada(
            int first,
            int pageSize,
            String codigo,
            String descricao,
            String banco
    ) throws Exception {
        String sql = "SELECT * FROM(\n"
                + "SELECT ROW_NUMBER() OVER ( ORDER BY Codigo) AS RowNum,\n"
                + "Codigo AS TipoSrvCodigo,\n"
                + "Descricao AS TipoSrvDesc,\n"
                + "Banco, TAtend, TCob, TCar, TipoSrv, ER, CodInterf, Aditivo, Exportar, SubCentro,\n"
                + "Operador AS TipoSrvOperador,\n"
                + "Dt_Alter AS TipoSrvDt_Alter,\n"
                + "Hr_Alter AS TipoSrvHr_Alter\n"
                + "FROM tiposrvcli\n";

        if (codigo != null && !codigo.equals("")) {
            sql += "AND Codigo = ?\n";
        }
        if (descricao != null && !descricao.equals("")) {
            sql += "AND Descricao LIKE ?\n";
        }
        if (banco != null && !banco.equals("")) {
            sql += "AND Banco = ?\n";
        }

        sql += ") AS RowConstrainedResult\n"
                + "WHERE   RowNum >= ?\n"
                + "    AND RowNum <= ?\n"
                + "ORDER BY RowNum;\n";

        List<TesConta> lista = new ArrayList<>();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);

            if (codigo != null && !codigo.equals("")) {
                consulta.setBigDecimal(codigo);
            }
            if (descricao != null && !descricao.equals("")) {
                consulta.setString(descricao);
            }
            if (banco != null && !banco.equals("")) {
                consulta.setString(banco);
            }

            consulta.setInt(first);
            consulta.setInt(first + pageSize);
            consulta.select();

            while (consulta.Proximo()) {
                TesConta tesConta = new TesConta();

                tesConta.setTipoSrvCodigo(consulta.getString("TipoSrvCodigo"));
                tesConta.setBanco(consulta.getString("Banco"));
                tesConta.setTAtend(consulta.getString("TAtend"));
                tesConta.setTCob(consulta.getString("TCob"));
                tesConta.setTCar(consulta.getString("TCar"));
                tesConta.setTipoSrv(consulta.getString("TipoSrv"));
                tesConta.setER(consulta.getString("ER"));
                tesConta.setCodInterf(consulta.getString("CodInterf"));
                tesConta.setAditivo(consulta.getString("Aditivo"));
                tesConta.setSubCentro(consulta.getString("SubCentro"));
                tesConta.setTipoSrvOperador(consulta.getString("TipoSrvOperador"));
                tesConta.setTipoSrvDt_Alter(consulta.getString("TipoSrvDt_Alter"));
                tesConta.setTipoSrvHr_Alter(consulta.getString("TipoSrvHr_Alter"));
                tesConta.setTipoSrvDesc(consulta.getString("TipoSrvDesc"));
                tesConta.setExportar(consulta.getString("Exportar"));

                lista.add(tesConta);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return lista;
    }

    /**
     *
     * @param first
     * @param pageSize
     * @param codigo
     * @param descricao
     * @param banco
     * @return
     * @throws Exception
     */
    public List<TesConta> allTiposrvcli() throws Exception {
        String sql = "SELECT\n"
                + " Codigo AS TipoSrvCodigo,\n"
                + " Descricao AS TipoSrvDesc,Descricao,\n"
                + " Banco, TAtend, TCob, TCar, TipoSrv, ER, CodInterf, Aditivo, Exportar, SubCentro,\n"
                + " Operador AS TipoSrvOperador,\n"
                + " Dt_Alter AS TipoSrvDt_Alter,\n"
                + " Hr_Alter AS TipoSrvHr_Alter\n"
                + "FROM tiposrvcli\n";
        sql += "ORDER BY Descricao;\n";

        List<TesConta> lista = new ArrayList<>();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);

            consulta.select();

            while (consulta.Proximo()) {
                TesConta tesConta = new TesConta();

                tesConta.setTipoSrvCodigo(consulta.getString("TipoSrvCodigo"));
                tesConta.setCodigo(consulta.getString("TipoSrvCodigo"));
                tesConta.setBanco(consulta.getString("Banco"));
                tesConta.setTAtend(consulta.getString("TAtend"));
                tesConta.setDescricao(consulta.getString("Descricao"));
                tesConta.setTCob(consulta.getString("TCob"));
                tesConta.setTCar(consulta.getString("TCar"));
                tesConta.setTipoSrv(consulta.getString("TipoSrv"));
                tesConta.setER(consulta.getString("ER"));
                tesConta.setCodInterf(consulta.getString("CodInterf"));
                tesConta.setAditivo(consulta.getString("Aditivo"));
                tesConta.setSubCentro(consulta.getString("SubCentro"));
                tesConta.setTipoSrvOperador(consulta.getString("TipoSrvOperador"));
                tesConta.setTipoSrvDt_Alter(consulta.getString("TipoSrvDt_Alter"));
                tesConta.setTipoSrvHr_Alter(consulta.getString("TipoSrvHr_Alter"));
                tesConta.setTipoSrvDesc(consulta.getString("TipoSrvDesc"));
                tesConta.setExportar(consulta.getString("Exportar"));

                lista.add(tesConta);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return lista;
    }

    /**
     * Obtém Lote pelo id
     *
     * @param codigo
     * @return Lote encontrado ou null
     * @throws Exception
     */
    public TesConta getTiposrvcliById(String codigo) throws Exception {
        String sql = "SELECT TOP 1\n"
                + "Codigo AS TipoSrvCodigo,\n"
                + "Descricao AS TipoSrvDesc,\n"
                + "Banco, TAtend, TCob, TCar, TipoSrv, ER, CodInterf, Aditivo, Exportar, SubCentro,\n"
                + "Operador AS TipoSrvOperador,\n"
                + "Dt_Alter AS TipoSrvDt_Alter,\n"
                + "Hr_Alter AS TipoSrvHr_Alter\n"
                + "FROM tiposrvcli\n"
                + "WHERE Codigo = ? ;";

        TesConta tesConta = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codigo);
            consulta.select();

            if (consulta.Proximo()) {
                tesConta = new TesConta();

                tesConta.setTipoSrvCodigo(consulta.getString("TipoSrvCodigo"));
                tesConta.setBanco(consulta.getString("Banco"));
                tesConta.setTAtend(consulta.getString("TAtend"));
                tesConta.setTCob(consulta.getString("TCob"));
                tesConta.setTCar(consulta.getString("TCar"));
                tesConta.setTipoSrv(consulta.getString("TipoSrv"));
                tesConta.setER(consulta.getString("ER"));
                tesConta.setCodInterf(consulta.getString("CodInterf"));
                tesConta.setAditivo(consulta.getString("Aditivo"));
                tesConta.setSubCentro(consulta.getString("SubCentro"));
                tesConta.setTipoSrvOperador(consulta.getString("TipoSrvOperador"));
                tesConta.setTipoSrvDt_Alter(consulta.getString("TipoSrvDt_Alter"));
                tesConta.setTipoSrvHr_Alter(consulta.getString("TipoSrvHr_Alter"));
                tesConta.setTipoSrvDesc(consulta.getString("TipoSrvDesc"));
                tesConta.setExportar(consulta.getString("Exportar"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return tesConta;
    }

    public List<Filiais> listaFiliais(Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<Filiais> retorno = new ArrayList<>();
            Filiais filial;

            sql = "SELECT *\n"
                    + "FROM Filiais\n"
                    + "ORDER BY Descricao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                filial = new Filiais();
                filial.setCodFil(consulta.getBigDecimal("codFil").toPlainString().replace(".0", ""));
                filial.setDescricao(consulta.getString("Descricao"));
                retorno.add(filial);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.listaFiliais - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Clientes> listaTesourarias(String codFil, Persistencia persistencia) throws Exception {
        String sql = "";

        if (null == codFil || codFil.equals("0")) {
            codFil = "";
        }

        try {
            List<Clientes> retorno = new ArrayList<>();
            Clientes cliente;

            sql = "select Clientes.* \n"
                    + "from TesFecha\n"
                    + "JOIN Clientes\n"
                    + "  ON Clientes.Codigo = TesFecha.CodCli\n"
                    + " AND Clientes.CodFil = TesFecha.CodFil\n";

            if (!codFil.equals("")) {
                sql += " WHERE TesFecha.codFil = ?\n";
            }
            sql += "ORDER BY NRed";

            Consulta consulta = new Consulta(sql, persistencia);

            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            consulta.select();

            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consulta.getBigDecimal("codFil"));
                cliente.setNRed(consulta.getString("Nred"));
                cliente.setNome(consulta.getString("Nome"));
                cliente.setBanco(consulta.getString("Banco"));
                cliente.setTpCli(consulta.getString("TpCli"));
                cliente.setCodCli(consulta.getString("CodCli"));
                cliente.setCodigo(consulta.getString("Codigo"));
                retorno.add(cliente);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.listaTesourarias - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Funcion> listaFuncionarios(String codFil, String codTesouraria, String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        String sql = "";

        if (null == codFil || codFil.equals("0")) {
            codFil = "";
        }

        if (null == codTesouraria) {
            codTesouraria = "";
        }

        try {
            List<Funcion> retorno = new ArrayList<>();
            Funcion funcion;

            sql = "SELECT \n"
                    + "  Funcion.Matr, \n"
                    + "  Funcion.Nome, \n"
                    + "  Funcion.CodFil,\n"
                    + "  Funcion.Secao\n"
                    + "  FROM TesEntrada\n"
                    + "  JOIN Funcion\n"
                    + "    ON TesEntrada.MatrConf = Funcion.Matr\n"
                    + "   AND TesEntrada.CodFil = Funcion.CodFil\n"
                    + "  WHERE TesEntrada.Data BETWEEN ? AND ?\n";
            if (!codFil.equals("")) {
                sql += "  AND   TesEntrada.CodFil = ?\n";
            }

            if (!codTesouraria.equals("")) {
                sql += "  AND   TesEntrada.CodCli2  = ?\n";
            }

            sql += "  GROUP BY Funcion.Matr, Funcion.Nome, Funcion.CodFil, Funcion.Secao\n"
                    + "  ORDER BY Funcion.Nome\n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataInicio);
            consulta.setString(dataFim);

            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!codTesouraria.equals("")) {
                consulta.setString(codTesouraria);
            }

            consulta.select();

            while (consulta.Proximo()) {
                funcion = new Funcion();
                funcion.setMatr(consulta.getBigDecimal("Matr").toPlainString().replace(".0", ""));
                funcion.setNome(consulta.getString("Nome"));
                funcion.setCodFil(consulta.getBigDecimal("CodFil").toPlainString().replace(".0", ""));
                funcion.setSecao(consulta.getString("Secao"));
                retorno.add(funcion);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.listaFuncionarios - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesEntrada> analiseQtdeValor(String codFil, String tesouraria, String matr, String dataInicio, String dataFinal, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            if (null == codFil) {
                codFil = "";
            }

            if (null == tesouraria) {
                tesouraria = "";
            }

            if (null == matr) {
                matr = "";
            }

            List<TesEntrada> Retorno = new ArrayList<>();
            TesEntrada tesEntrada;

            sql = " Select \n"
                    + " Funcion.Nome,\n"
                    + " TesEntrada.MatrConf Matr, \n"
                    + " TesEntrada.CodFil, \n"
                    + " Filiais.Descricao,\n"
                    + " Sum(TesEntStat.QtdeDH) QtdeDH, \n"
                    + " Sum(TesEntStat.QtdeMD) QtdeMD, \n"
                    + " (Sum(TesEntStat.QtdeDH+TesEntStat.QtdeMD)) QtdeDH_MD,\n"
                    + " REPLACE(FORMAT(((Sum(TesEntStat.QtdeDH+TesEntStat.QtdeMD)) / Sum(TesEntStat.Tempo) * 60),'N','en-us'), ',','') QtdeHora,\n"
                    + " REPLACE(FORMAT(((Sum(TesEntrada.ValorRec) / Sum(TesEntStat.Tempo)) * 60),'N','en-us'), ',','') ValorHora,\n"
                    + " Sum(TesEntrada.ValorRec) Valor\n"
                    + " from TesEntrada \n"
                    + " left Join (Select \n"
                    + "            Guia, \n"
                    + "            Serie, \n"
                    + "            Isnull(Sum(TesEntStat.Tempo),0) Tempo, \n"
                    + "            Isnull(Sum(TesEntStat.QtdeDH),0) QtdeDH, \n"
                    + "            Isnull(Sum(TesEntStat.QtdeMD),0) QtdeMD \n"
                    + "            From TesEntStat \n"
                    + "            Group by Guia, Serie) TesEntStat \n"
                    + "  on  TesEntrada.Guia  = TesEntStat.Guia \n"
                    + " and TesEntrada.Serie  = TesEntStat.Serie \n"
                    + " Left Join OS_Vig  \n"
                    + "   on  OS_Vig.Cliente  = TesEntrada.Codcli1 \n"
                    + "  and OS_Vig.CliDSt    = TesEntrada.CodCli2 \n"
                    + "  and Os_Vig.CodFil    = TesEntrada.CodFil \n"
                    + "  and OS_Vig.Situacao  = 'A'\n"
                    + " Left Join OS_VTes    \n"
                    + "   on OS_VTes.OS         = OS_Vig.OS \n"
                    + "  and OS_vTes.CodFil     = OS_Vig.CodFil \n"
                    + " Left Join TesTipoNum \n"
                    + "   on TesTipoNum.Codigo  = OS_VTes.TipoNum \n"
                    + " JOIN Funcion\n"
                    + "   ON TesEntrada.MatrConf = Funcion.Matr\n"
                    + "  AND TesEntrada.CodFil   = Funcion.CodFil\n"
                    + " JOIN Filiais\n"
                    + "   ON TesEntrada.CodFil   = Filiais.CodFil\n"
                    + " where TesEntrada.Data >= ?\n"
                    + "   and TesEntrada.Data <= ?\n"
                    + "   and OS_Vig.OS > 0\n";

            if (!codFil.equals("")) {
                sql += "   and TesEntrada.CodFil = ?\n";
            }

            if (!tesouraria.equals("")) {
                sql += "   and TesEntrada.Codcli2 = ?\n";
            }

            if (!matr.equals("")) {
                sql += "   and Funcion.matr = ?\n";
            }

            sql += " Group by Filiais.Descricao, Funcion.Nome, TesEntrada.MatrConf, TesEntrada.CodFil\n"
                    + " Order by TesEntrada.codFil, Funcion.Nome, TesEntrada.MatrConf";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFinal);

            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!tesouraria.equals("")) {
                consulta.setString(tesouraria);
            }

            if (!matr.equals("")) {
                consulta.setString(matr);
            }

            consulta.select();

            while (consulta.Proximo()) {
                tesEntrada = new TesEntrada();

                tesEntrada.setCodFil(consulta.getString("CodFil"));
                tesEntrada.setValor(consulta.getBigDecimal("Valor"));
                tesEntrada.setQtde(consulta.getBigDecimal("QtdeDH_MD"));
                tesEntrada.setFilialDescricao(consulta.getString("Descricao"));
                tesEntrada.setNomeConferente(consulta.getString("Nome"));
                tesEntrada.setMatrConf(consulta.getString("Matr"));
                tesEntrada.setQtdeHora(consulta.getBigDecimal("QtdeHora"));
                tesEntrada.setValorHora(consulta.getBigDecimal("ValorHora"));

                Retorno.add(tesEntrada);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.analiseQtdeValor - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public TesEntrada analiseTempoTotal(String codFil, String tesouraria, String matr, String dataInicio, String dataFinal, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            if (null == codFil) {
                codFil = "";
            }

            if (null == tesouraria) {
                tesouraria = "";
            }

            if (null == matr) {
                matr = "";
            }

            TesEntrada tesEntrada = new TesEntrada();
            tesEntrada.setTempoHora("00:00");

            sql = " Select \n"
                    + " dbo.fun_MinutosParaHoras(Sum(TesEntStat.Tempo)) TempoHora\n"
                    + " from TesEntrada \n"
                    + " left Join (Select \n"
                    + "            Guia, \n"
                    + "            Serie, \n"
                    + "            Isnull(Sum(TesEntStat.Tempo),0) Tempo, \n"
                    + "            Isnull(Sum(TesEntStat.QtdeDH),0) QtdeDH, \n"
                    + "            Isnull(Sum(TesEntStat.QtdeMD),0) QtdeMD \n"
                    + "            From TesEntStat \n"
                    + "            Group by Guia, Serie) TesEntStat \n"
                    + "  on  TesEntrada.Guia  = TesEntStat.Guia \n"
                    + " and TesEntrada.Serie  = TesEntStat.Serie \n"
                    + " Left Join OS_Vig  \n"
                    + "   on  OS_Vig.Cliente  = TesEntrada.Codcli1 \n"
                    + "  and OS_Vig.CliDSt    = TesEntrada.CodCli2 \n"
                    + "  and Os_Vig.CodFil    = TesEntrada.CodFil \n"
                    + "  and OS_Vig.Situacao  = 'A'\n"
                    + " Left Join OS_VTes    \n"
                    + "   on OS_VTes.OS         = OS_Vig.OS \n"
                    + "  and OS_vTes.CodFil     = OS_Vig.CodFil \n"
                    + " Left Join TesTipoNum \n"
                    + "   on TesTipoNum.Codigo  = OS_VTes.TipoNum \n"
                    + " JOIN Funcion\n"
                    + "   ON TesEntrada.MatrConf = Funcion.Matr\n"
                    + "  AND TesEntrada.CodFil   = Funcion.CodFil\n"
                    + " JOIN Filiais\n"
                    + "   ON TesEntrada.CodFil   = Filiais.CodFil\n"
                    + " where TesEntrada.Data >= ?\n"
                    + "   and TesEntrada.Data <= ?\n"
                    + "   and OS_Vig.OS > 0\n";

            if (!codFil.equals("")) {
                sql += "   and TesEntrada.CodFil = ?\n";
            }

            if (!tesouraria.equals("")) {
                sql += "   and TesEntrada.Codcli2 = ?\n";
            }

            if (!matr.equals("")) {
                sql += "   and Funcion.matr = ?\n";
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFinal);

            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!tesouraria.equals("")) {
                consulta.setString(tesouraria);
            }

            if (!matr.equals("")) {
                consulta.setString(matr);
            }

            consulta.select();

            while (consulta.Proximo()) {
                tesEntrada = new TesEntrada();

                tesEntrada.setTempoHora(consulta.getString("TempoHora"));
            }

            consulta.close();

            return tesEntrada;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.analiseTempoTotal - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesEntrada> analisePorDia(String codFil, String tesouraria, String matr, String dataInicio, String dataFinal, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            if (null == codFil) {
                codFil = "";
            }

            if (null == tesouraria) {
                tesouraria = "";
            }

            if (null == matr) {
                matr = "";
            }

            List<TesEntrada> Retorno = new ArrayList<>();
            TesEntrada tesEntrada;

            sql = " Select \n"
                    + " FORMAT(TesEntrada.Data,'dd/MM') data,\n"
                    + " (Sum(TesEntStat.QtdeDH+TesEntStat.QtdeMD)) QtdeDH_MD,\n"
                    + " REPLACE(FORMAT(Sum(TesEntrada.ValorRec),'N','en-us'), ',','') ValorRec\n"
                    + " from TesEntrada \n"
                    + " left Join (Select \n"
                    + "            Guia, \n"
                    + "            Serie, \n"
                    + "            Isnull(Sum(TesEntStat.Tempo),0) Tempo, \n"
                    + "            Isnull(Sum(TesEntStat.QtdeDH),0) QtdeDH, \n"
                    + "            Isnull(Sum(TesEntStat.QtdeMD),0) QtdeMD \n"
                    + "            From TesEntStat \n"
                    + "            Group by Guia, Serie) TesEntStat \n"
                    + "  on  TesEntrada.Guia  = TesEntStat.Guia \n"
                    + " and TesEntrada.Serie  = TesEntStat.Serie \n"
                    + " Left Join OS_Vig  \n"
                    + "   on  OS_Vig.Cliente  = TesEntrada.Codcli1 \n"
                    + "  and OS_Vig.CliDSt    = TesEntrada.CodCli2 \n"
                    + "  and Os_Vig.CodFil    = TesEntrada.CodFil \n"
                    + "  and OS_Vig.Situacao  = 'A'\n"
                    + " Left Join OS_VTes    \n"
                    + "   on OS_VTes.OS         = OS_Vig.OS \n"
                    + "  and OS_vTes.CodFil     = OS_Vig.CodFil \n"
                    + " Left Join TesTipoNum \n"
                    + "   on TesTipoNum.Codigo  = OS_VTes.TipoNum \n"
                    + " JOIN Funcion\n"
                    + "   ON TesEntrada.MatrConf = Funcion.Matr\n"
                    + "  AND TesEntrada.CodFil   = Funcion.CodFil\n"
                    + " JOIN Filiais\n"
                    + "   ON TesEntrada.CodFil   = Filiais.CodFil\n"
                    + " where TesEntrada.Data >= ?\n"
                    + "   and TesEntrada.Data <= ?\n"
                    + "   and OS_Vig.OS > 0\n";

            if (!codFil.equals("")) {
                sql += "   and TesEntrada.CodFil = ?\n";
            }

            if (!tesouraria.equals("")) {
                sql += "   and TesEntrada.Codcli2 = ?\n";
            }

            if (!matr.equals("")) {
                sql += "   and Funcion.matr = ?\n";
            }

            sql += "Group by TesEntrada.Data\n"
                    + " ORDER by TesEntrada.Data";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFinal);

            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!tesouraria.equals("")) {
                consulta.setString(tesouraria);
            }

            if (!matr.equals("")) {
                consulta.setString(matr);
            }

            consulta.select();

            while (consulta.Proximo()) {
                tesEntrada = new TesEntrada();

                tesEntrada.setData(consulta.getString("data"));
                tesEntrada.setQtde(consulta.getBigDecimal("QtdeDH_MD"));
                tesEntrada.setValorRec(consulta.getBigDecimal("ValorRec"));

                Retorno.add(tesEntrada);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.analisePorDia - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesEntrada> analiseDetalhadoGride(String codFil, String tesouraria, String matr, String dataInicio, String dataFinal, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            if (null == codFil) {
                codFil = "";
            }

            if (null == tesouraria) {
                tesouraria = "";
            }

            if (null == matr) {
                matr = "";
            }

            List<TesEntrada> Retorno = new ArrayList<>();
            TesEntrada tesEntrada;

            sql = " Select \n"
                    + " Filiais.Descricao,\n"
                    + " Funcion.Nome,\n"
                    + " Sum(TesEntStat.QtdeDH) QtdeDH, \n"
                    + " Sum(TesEntStat.QtdeMD) QtdeMD, \n"
                    + " (Sum(TesEntStat.QtdeDH+TesEntStat.QtdeMD)) QtdeTotal, \n"
                    + " dbo.fun_MinutosParaHoras(Sum(TesEntStat.Tempo)) TempoHora,\n"
                    + " REPLACE(FORMAT((case when Sum(TesEntStat.QtdeDH) = 0 or Sum(TesEntStat.Tempo) = 0  then  0 \n"
                    + "       else Sum(TesEntStat.QtdeDH)/Sum(TesEntStat.Tempo) * 60 end),'N','en-us'), ',','') ProdDH_hr, \n"
                    + " REPLACE(FORMAT((case when Sum(TesEntStat.QtdeMD) = 0 or Sum(TesEntStat.Tempo) = 0  then  0 \n"
                    + "       else Sum(TesEntStat.QtdeMD)/Sum(TesEntStat.Tempo) * 60 end),'N','en-us'), ',','') ProdMD_hr, \n"
                    + " REPLACE(FORMAT(Sum(TesEntrada.ValorRec),'N','en-us'), ',','') ValorRec\n"
                    + " from TesEntrada \n"
                    + " left Join (Select \n"
                    + "            Guia, \n"
                    + "            Serie, \n"
                    + "            Isnull(Sum(TesEntStat.Tempo),0) Tempo, \n"
                    + "            Isnull(Sum(TesEntStat.QtdeDH),0) QtdeDH, \n"
                    + "            Isnull(Sum(TesEntStat.QtdeMD),0) QtdeMD \n"
                    + "            From TesEntStat \n"
                    + "            Group by Guia, Serie) TesEntStat \n"
                    + "  on  TesEntrada.Guia  = TesEntStat.Guia \n"
                    + " and TesEntrada.Serie  = TesEntStat.Serie \n"
                    + " Left Join OS_Vig  \n"
                    + "   on  OS_Vig.Cliente  = TesEntrada.Codcli1 \n"
                    + "  and OS_Vig.CliDSt    = TesEntrada.CodCli2 \n"
                    + "  and Os_Vig.CodFil    = TesEntrada.CodFil \n"
                    + "  and OS_Vig.Situacao  = 'A'\n"
                    + " Left Join OS_VTes    \n"
                    + "   on OS_VTes.OS         = OS_Vig.OS \n"
                    + "  and OS_vTes.CodFil     = OS_Vig.CodFil \n"
                    + " Left Join TesTipoNum \n"
                    + "   on TesTipoNum.Codigo  = OS_VTes.TipoNum \n"
                    + " JOIN Funcion\n"
                    + "   ON TesEntrada.MatrConf = Funcion.Matr\n"
                    + "  AND TesEntrada.CodFil   = Funcion.CodFil\n"
                    + " JOIN Filiais\n"
                    + "   ON TesEntrada.CodFil   = Filiais.CodFil\n"
                    + " where TesEntrada.Data >= ?\n"
                    + "   and TesEntrada.Data <= ?\n"
                    + "   and OS_Vig.OS > 0\n";

            if (!codFil.equals("")) {
                sql += "   and TesEntrada.CodFil = ?\n";
            }

            if (!tesouraria.equals("")) {
                sql += "   and TesEntrada.Codcli2 = ?\n";
            }

            if (!matr.equals("")) {
                sql += "   and Funcion.matr = ?\n";
            }

            sql += " Group by Filiais.Descricao, Funcion.Nome, TesEntrada.MatrConf, TesEntrada.CodFil, ISNULL(OS_VTes.TipoNum,0), TesTipoNum.Peso\n"
                    + " Order by Filiais.Descricao, Funcion.Nome, TesEntrada.MatrConf, ISNULL(OS_VTes.TipoNum,0), TesTipoNum.Peso";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFinal);

            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }

            if (!tesouraria.equals("")) {
                consulta.setString(tesouraria);
            }

            if (!matr.equals("")) {
                consulta.setString(matr);
            }

            consulta.select();

            while (consulta.Proximo()) {
                tesEntrada = new TesEntrada();

                tesEntrada.setFilialDescricao(consulta.getString("Descricao"));
                tesEntrada.setNomeConferente(consulta.getString("Nome"));
                tesEntrada.setQtdeCed(consulta.getBigDecimal("QtdeDH"));
                tesEntrada.setMoedasValor(consulta.getBigDecimal("QtdeMD"));
                tesEntrada.setQtde(consulta.getBigDecimal("QtdeTotal"));
                tesEntrada.setTempoHora(consulta.getString("TempoHora"));
                tesEntrada.setQtdeHora(consulta.getBigDecimal("ProdDH_hr"));
                tesEntrada.setTotalMoeda(consulta.getBigDecimal("ProdMD_hr"));
                tesEntrada.setValorRec(consulta.getBigDecimal("ValorRec"));

                Retorno.add(tesEntrada);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.analiseDetalhadoGride - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesEntrada> relatorioProdutividade(String codFil, String dataInicio, String dataFinal, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            if (null == codFil || codFil.equals("")) {
                codFil = "0";
            }

            List<TesEntrada> Retorno = new ArrayList<>();
            TesEntrada tesEntrada;

            sql = "Select Filiais.descricao filDescr, TesEntrada.Data, TesEntrada.Guia, TesEntrada.Serie, Clientes.NRed, TesEntrada.Valor, MatrConf, Funcion.Nome_Guer, HrInicio, HrFinal, Tempo,\n"
                    + "(Select top 10 Sum(QtdeDH) from TesEntStat where TesEntStat.Guia = Tesentrada.Guia and TesEntStat.Serie = TesEntrada.Serie) QtdeCed,\n"
                    + "Case when Tempo > 0 then\n"
                    + "Round(((Select top 10 Sum(QtdeDH) from TesEntStat where TesEntStat.Guia = Tesentrada.Guia and TesEntStat.Serie = TesEntrada.Serie)/1000) /(Tempo/60),2) else null end MilheirosHr \n"
                    + "from TesEntrada\n"
                    + "left join Clientes on Clientes.Codigo = TesEntrada.CodCli1\n"
                    + "                   and Clientes.CodFil = TesEntrada.CodFIl\n"
                    + "join Funcion on Funcion.Matr = TesEntrada.MatrConf\n"
                    + "JOIN Filiais ON TesEntrada.codfil = Filiais.codfil\n"
                    + " WHERE TesEntrada.Data BETWEEN ? AND ?\n"
                    + "   AND TesEntrada.Serie <> 'ZGE'\n"
                    + "   AND TesEntrada.Serie <> 'CST'\n";

            if (!codFil.equals("0")) {
                sql += "  AND TesEntrada.CodFil = ?\n";
            }

            sql += "order by TesEntrada.Data DESC, Filiais.descricao, Funcion.NOme_Guer, HrInicio";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataInicio);
            consulta.setString(dataFinal);

            if (!codFil.equals("0")) {
                consulta.setString(codFil);
            }

            consulta.select();

            while (consulta.Proximo()) {
                tesEntrada = new TesEntrada();

                tesEntrada.setData(consulta.getString("Data"));
                tesEntrada.setGuia(consulta.getString("Guia"));
                tesEntrada.setSerie(consulta.getString("Serie"));
                tesEntrada.setNRed1(consulta.getString("Nred"));
                tesEntrada.setValor(consulta.getString("Valor"));
                tesEntrada.setMatrConf(consulta.getString("MatrConf"));
                tesEntrada.setNomeConferente(consulta.getString("Nome_Guer"));
                tesEntrada.setHrInicio(consulta.getString("HrInicio"));
                tesEntrada.setHrFinal(consulta.getString("HrFinal"));
                tesEntrada.setTempo(consulta.getString("Tempo"));
                tesEntrada.setQtdeCed(consulta.getString("QtdeCed"));
                tesEntrada.setQtdeVol(consulta.getString("MilheirosHr"));
                tesEntrada.setFilialDescricao(consulta.getString("filDescr"));

                Retorno.add(tesEntrada);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesEntradaDao.relatorioProdutividade - " + e.getMessage() + "\r\n" + sql);
        }
    }
}

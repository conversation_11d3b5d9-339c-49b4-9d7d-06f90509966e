<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/funcion.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .ui-inputtext{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                .calendario .ui-inputfield{
                    margin-top: 3px !important
                }

                [id*="formPesquisaRapida"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="pnlDados"]{
                    height: calc(100vh - 410px) !important;
                }

                [id*="cadastrar"] div[class*="col-md"]{
                    padding: 5px !important;
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -3px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .FundoPagina{
                        margin-top: 130px !important   
                    }

                    [id*="pnlDados"]{
                        height: calc(100vh - 530px) !important;
                    }
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid tbody tr td{
                        white-space:nowrap;
                        overflow:hidden;
                        text-overflow:ellipsis;
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid tbody tr td:nth-child(1),
                    .DataGrid thead tr th:nth-child(12),
                    .DataGrid tbody tr td:nth-child(12) {
                        min-width:50px !important;
                    }

                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2){
                        min-width:150px !important;
                    }

                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid tbody tr td:nth-child(5),
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7),
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid tbody tr td:nth-child(8),
                    .DataGrid thead tr th:nth-child(9),
                    .DataGrid tbody tr td:nth-child(9),
                    .DataGrid thead tr th:nth-child(13),
                    .DataGrid tbody tr td:nth-child(13),
                    .DataGrid thead tr th:nth-child(14),
                    .DataGrid tbody tr td:nth-child(14),
                    .DataGrid thead tr th:nth-child(15),
                    .DataGrid tbody tr td:nth-child(15),
                    .DataGrid thead tr th:nth-child(16),
                    .DataGrid tbody tr td:nth-child(16),
                    .DataGrid thead tr th:nth-child(18),
                    .DataGrid tbody tr td:nth-child(18),
                    .DataGrid thead tr th:nth-child(19),
                    .DataGrid tbody tr td:nth-child(19){
                        min-width:130px !important;
                    }

                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid tbody tr td:nth-child(4),
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid tbody tr td:nth-child(6),
                    .DataGrid thead tr th:nth-child(11),
                    .DataGrid tbody tr td:nth-child(11) {
                        min-width:300px !important;
                    }

                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid tbody tr td:nth-child(3),
                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid tbody tr td:nth-child(10),
                    .DataGrid thead tr th:nth-child(17),
                    .DataGrid tbody tr td:nth-child(17){
                        min-width:200px !important;
                    }
                }

                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }

                #formPesquisar .ui-radiobutton {
                    background: transparent !important;
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                #formPesquisaRapida .ui-radiobutton {
                    background: transparent !important;
                }

                [id*="formCadastrar"] label{
                    margin-bottom: 0px !important;
                }






                .fundoPasta.select{
                    background-color: #FFF;
                    border: thin solid #DDD;
                }

                .fundoPasta[hash="FICHA_FUNCIONAL"].select{

                }

                .fundoPasta, .btAdicionarArquivo, .fundoArquivo{
                    float: left; 
                    width: calc(14% - 5px); 
                    text-align: center;
                    height: 92px;
                    border-radius: 5px;
                    margin-top: 4px;
                    padding-top: 8px;
                    margin-left: 4px;
                    display: inline-block !important;
                    border: thin solid #EEE;
                    cursor: pointer;
                }

                .fundoArquivo{
                    border: none;
                }

                .fundoPasta i, .btAdicionarArquivo i, .fundoArquivo i{
                    font-size: 40pt;
                    color: #145B9B;
                    display: block;
                    cursor: pointer;
                }

                .fundoPasta label, .btAdicionarArquivo label, .fundoArquivo label{
                    font-size: 8pt;
                    cursor: pointer;
                    color: #000;
                }

                .fundoArquivo label{
                    background-color: #145B9B;
                    color: #FFF;
                    padding: 2px 6px 2px 6px;
                    border-radius: 10px;
                }

                .fundoPasta[hash="FICHA_FUNCIONAL"] i,
                .fundoPasta[hash="FICHA_FUNCIONAL"] label{
                    color: orangered;
                }


                #fundoPastaPai{
                    padding: 0px 0px 3px 0px;
                }

                .btAdicionarArquivo{
                    padding-top: 6px;
                }

                .btAdicionarArquivo{
                    border:3px solid #BBB;
                    background-color:#DDD;
                    border-radius:4px;
                    color:#BBB;
                    box-shadow:2px 2px 3px #CCC;
                }

                .btAdicionarArquivo i,
                .btAdicionarArquivo label{
                    color: #999;
                }

                .btAdicionarArquivo, .fundoArquivo{
                    margin-right: 4px;
                    margin-left: 0px;
                }

                .fundoArquivo{
                    width: auto !important;
                    padding-right: 30px !important;
                    padding-left: 30px !important;
                }

                @media only screen and (max-width: 5000px) and (min-width: 1600px) {
                    .fundoPasta{
                        width: calc(14.1% - 1.5px); 
                    }

                    #fundoPastaPai{
                        padding: 0px !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 0px) {
                    .fundoPasta, .btAdicionarArquivo, .fundoArquivo{
                        width: calc(50% - 5px) !important; 
                    }

                    #main{
                        top: 0px !Important;
                        height: 100vh !important
                    }

                    #PaiApresentacao{
                        top: 60px !important
                    }

                    #PaiBotoes{
                        top: -100px !important
                    }

                    #imgSatMob{
                        bottom: -80px !important;
                        height: 200px !important;    
                    }

                    .Cliente .ItemCliente{
                        height: 100% !important
                    }

                    .Cliente .ui-panel-titlebar span{
                        white-space: nowrap;
                        width: 220px !important;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .lblNred{
                        margin-top: -50px !important;
                    }
                }

                @media only screen and (max-width: 509px) and (min-width: 0px) {
                    #PaiBotoes{
                        max-width: 351px !important;
                        top: -80px !important
                    }

                }

                .TituloPasta{
                    margin-top: 20px !important;
                    font-size: 12pt;
                    color: #3C8DBC;
                    width: 100%;
                    text-align: left;
                    padding-top: 8px;
                    padding-bottom: 8px;
                    border-bottom: thin solid #3C8DBC;
                    border-top: thin solid #3C8DBC;
                    margin-bottom: 10px;
                    background-color: azure;
                }

                .jconfirm-closeIcon{
                    color: #000 !important;
                }


                .ItemCliente, .ItemClienteSemLat{
                    position: relative;
                }

                .ComFoto{
                    height: 420px;
                }

                .SemFoto{
                    height: 280px;
                }

                .ItemCliente .ui-panel-title{
                    margin-top: 0px !important;
                    font-size: 16pt !important;
                    font-weight: bold !important;
                    color: #3c8dbc !important;
                    margin-left: #{localeController.number.toString() eq '1'? '60': localeController.number.toString() eq '2'? '75': '84'}px !important;
                }

                .lblNred{
                    position: absolute;
                    margin-top: -40px;
                    margin-left: 10px;
                    padding: 1px 10px 2px 10px;
                    background-color: orangered;
                    color: #FFF;
                    font-weight: 600 !important;
                    border-radius: 30px;
                    font-size: 9pt !important;
                }

                .ui-panel.ui-widget .ui-panel-titlebar.ui-corner-all{
                    background-color: #FFF !important;
                    border-bottom: thin solid #CCC !important
                }

                body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                .jconfirm-bg {
                    opacity: 0.9 !important;
                }
            </style>
            <script type="text/javascript">
                // <![CDATA[
                let ArrayDoctos = new Array();

                $(document).ready(function () {
                    $(window).resize();
                })
                        .on('click', '.fundoPasta', function () {
                            $('.fundoPasta.select').removeClass('select');
                            $(this).addClass('select');
                            $('[id*="txtTipoArq"]').val($(this).attr('hash'));
                            CarregarArquivos();
                        })
                        .on('click', '.btAdicionarArquivo', function () {
                            $('[id*="uploadFotosRelatorio_input"]').click();
                        })
                        .on('click', '.fundoArquivo i', function () {
                            $this = $(this).parent('.fundoArquivo');

                            if (!$this.attr('carregando') ||
                                    $this.attr('carregando') == 'N') {
                                $this.attr('carregando', 'S').css('text-align', 'center').find('i:eq(0)').attr('class', 'fa fa-refresh fa-spin fa-fw').css('text-align', 'center');

                                const linkFoto = 'https://mobile.sasw.com.br:9091/satellite/documentos/#{login.pp.empresa}/Pe_Doctos/' + parseInt($this.attr('cod-pessoa')).toString() + '/' + $this.attr('link-data');

                                setTimeout(function () {
                                    try {
                                        AbrirFoto(linkFoto, function () {
                                            $('.fundoArquivo').removeAttr('carregando').find('i:eq(0)').attr('class', 'fa fa-file-image-o');
                                        });
                                    } catch (e) {
                                        $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.FotoNaoEncontrada}');
                                        $('.fundoArquivo').removeAttr('carregando').find('i:eq(0)').attr('class', 'fa fa-file-image-o');
                                    }
                                }, 800);
                            }
                        })
                        .on('click', '[ref="excluirArquivo"]', function () {
                            $thisExcl = $(this);

                            $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}', '#{localemsgs.ExcluirDocumento}', '#{localemsgs.Sim}', '#{localemsgs.Nao}', function () {
                                rcExclusao([{name: 'codPessoa', value: parseInt($thisExcl.attr('cod-pessoa'))}, {name: 'ordem', value: parseInt($thisExcl.attr('ordem'))}]);
                            });
                        })
                        .on('click', '.btApresentacaoEntendi', function () {
                            Apresentacao(parseInt($(this).attr('ref')));
                        });
                ;

                $(window).resize(function () {
                    if ($('div[south-type="js-confirm-content"]').length > 0) {
                        $('div[south-type="js-confirm-content"]').each(function () {
                            $(this).css('height', eval(($('body').height() - eval($(this).attr('ref-size')))) + 'px');
                        });
                    }

                    if ($('body').height() <= 800 &&
                            $('body').width() <= 500) {
                        $('#PaiBotoes, #imgSatMob').css('zoom', '0.7');
                    }
                });

                function RecarregarArray(Dados) {
                    ArrayDoctos = new Array();

                    if (null != Dados && Dados != '') {
                        Dados.split('|**|').forEach(function (Doc) {
                            ArrayDoctos.push({
                                Codigo: Doc.split('|__|')[0],
                                Ordem: Doc.split('|__|')[1],
                                Descricao: Doc.split('|__|')[2],
                                Tipo: Doc.split('|__|')[2].split('-')[0],
                                Minutos: Doc.split('|__|')[3]
                            });
                        });
                    }
                }

                function MascarasJS() {
                    /*setTimeout(function () {
                     $('[id*="divCPF"]').find('input').attr('south-type', 'cpf');
                     $('[id*="fone1"]').attr('south-type', 'telefone');
                     $('[id*="fone2"]').attr('south-type', 'telefone');
                     $('[id*="cep"]').attr('south-type', 'cep');
                     
                     CriarAtributos('#{localeController.number}');
                     }, 600);*/
                }

                function CarregarArquivos(PosUpload = false) {
                    if (PosUpload) {
                        $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.FotoCarregadaSucesso}');
                    }

                    const TipoArq = $('.fundoPasta.select').attr('hash');
                    let ArrayArqs = new Array();

                    for (I = 0; I < ArrayDoctos.length; I++) {
                        if (ArrayDoctos[I].Tipo == TipoArq) {
                            ArrayArqs.push({
                                Codigo: ArrayDoctos[I].Codigo,
                                Ordem: ArrayDoctos[I].Ordem,
                                Descricao: ArrayDoctos[I].Descricao,
                                Tipo: ArrayDoctos[I].Tipo,
                                Minutos: ArrayDoctos[I].Minutos
                            });
                        }
                    }

                    let HTML = ' ';

                    if (ArrayArqs.length > 0) {
                        if (ArrayArqs.length > 1)
                            HTML += '     <h3 class="TituloPasta">[&nbsp;&nbsp;<font ref="QtdeArquivos">' + ArrayArqs.length + '</font>&nbsp;&nbsp;] #{localemsgs.ArquivosPasta}</h3>';
                        else
                            HTML += '     <h3 class="TituloPasta">[&nbsp;&nbsp;<font ref="QtdeArquivos">' + ArrayArqs.length + '</font>&nbsp;&nbsp;] #{localemsgs.ArquivoPasta}</h3>';

                        for (I = 0; I < ArrayArqs.length; I++) {
                            HTML += '     <div class="fundoArquivo" cod-pessoa="' + ArrayArqs[I].Codigo + '" link-data="PES' + ArrayArqs[I].Codigo + '-' + ArrayArqs[I].Ordem + ArrayArqs[I].Descricao.split('-')[1] + '" style="text-align: center !important">';
                            HTML += '       <i class="fa fa-file-image-o"></i>';
                            HTML += '       <label>' + ArrayArqs[I].Descricao.split('-')[0] + '-' + ArrayArqs[I].Ordem + ArrayArqs[I].Descricao.split('-')[1] + '</label>';
                            HTML += '       <a href="javascript:void(0);" ref="excluirArquivo" cod-pessoa="' + ArrayArqs[I].Codigo + '" ordem="' + ArrayArqs[I].Ordem + '" class="btn btn-danger" style="display: block; white-space: nowrap !important;"><i class="fa fa-trash" style="font-size: 11pt !important; color: #FFF !important"></i>&nbsp;&nbsp;Excluir</a>';
                            HTML += '     </div>';
                        }
                    } else {
                        HTML += '     <h3 class="TituloPasta">#{localemsgs.NenhumArquivoEncontrado}</h3>';
                    }

                    if (TipoArq == 'FICHA_FUNCIONAL') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }
                    if (TipoArq == 'CNH') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'RG') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'COMPROVANTE_RESIDENCIA') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'CTPS') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'CERTIDAO_NASCIMENTO') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'CERTIDAO_CASAMENTO') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'RESERVISTA') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'PIS') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'CPF') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'PASSAPORTE') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'ATESTADO') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    if (TipoArq == 'CARTEIRA_VIGILANTE') {
                        HTML += '     <div class="btAdicionarArquivo">';
                        HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                        HTML += '       <label>#{localemsgs.Adicionar}</label>';
                        HTML += '     </div>';
                    }

                    $('#divArquivos').html(HTML);
                }

                function AbrirDocumentacao() {
                    let HTML = '';
                    HTML += '<div south-type="js-confirm-content" ref-size="148" style="height:' + eval(($('body').height() - 148)) + 'px">';
                    HTML += '   <div id="fundoPastaPai" class="col-md-12 col-sm-12 col-xs-12" style="background-color: #EEE; border: thin solid #DDD; height: 200px; border-radius: 5px; white-space: nowrap; overflow: auto">';
                    HTML += '     <div class="fundoPasta select" hash="FICHA_FUNCIONAL">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.FichaFuncional}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="RG">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.RG}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CNH">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CNH}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="COMPROVANTE_RESIDENCIA">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.ComprovanteResidencia}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CTPS">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CTPS}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CERTIDAO_NASCIMENTO">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CertidaoNascimento}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CERTIDAO_CASAMENTO">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CertidaoCasamento}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="RESERVISTA">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.Reservista}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="PIS">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.PIS}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CPF">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CPF}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="PASSAPORTE">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.Passaporte}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="ATESTADO">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.Atestado}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CARTEIRA_VIGILANTE">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CarteiraVigilante}</label>';
                    HTML += '     </div>';
                    HTML += '   </div>';
                    HTML += '   <div id="divArquivos" class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important; height: calc(100% - 200px)">';

                    HTML += '   </div>';
                    HTML += '</div>';

                    $JanelaForm = $.confirm({
                        icon: 'fa fa-files-o',
                        type: 'blue',
                        closeIcon: true,
                        columnClass: 'xlarge',
                        containerFluid: true,
                        title: '<font color="#5eaee3">#{localemsgs.Documentacao}</font>',
                        boxWidth: '98%',
                        content: HTML,
                        useBootstrap: true,
                        offsetTop: 10,
                        offsetBottom: 10,
                        scrollToPreviousElement: true,
                        alignMiddle: false,
                        buttons: {
                            cancel: {
                                text: '#{localemsgs.Fechar}',
                                isHidden: false
                            }
                        },
                        onContentReady: function () {
                            $('.fundoPasta.select').click();
                        }
                    });
                }

                // ]]>
            </script>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{funcionario.Persistencias(login.pp, login.satellite)}" />
                <f:viewAction action="#{mobEW.Persistencia(login.pp, login.satellite)}" />
            </f:metadata>

            <p:growl id="msgs" />

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_funcionarios.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Funcion}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{funcionario.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">#{funcionario.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                        <label class="FilialEndereco">#{funcionario.filiais.endereco}</label>
                                        <label class="FilialBairroCidade">#{funcionario.filiais.bairro}, #{funcionario.filiais.cidade}/#{funcionario.filiais.UF}</label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3" style="padding:0px 10px 0px 0px !important; text-align: right !important">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="a" update=" msgs formCadastrar" actionListener="#{funcionario.NovoFuncionario}" oncomplete="PF('dlgCadastrar').show();"/>
                    <p:hotkey bind="p" actionListener="#{funcionario.PrePesquisar}" oncomplete="PF('dlgPesquisaRapida').show();" update="formPesquisaRapida"/>
                    <p:hotkey bind="e" update="formCadastrar msgs" actionListener="#{funcionario.buttonAction}"/>
                    <p:hotkey bind="shift+x" actionListener="#{exportarMB.setTitulo(localemsgs.Funcion)}" oncomplete="PF('dlgExportar').show();"/>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 40px !important; background: transparent; height:200px !important;" id="botoes">

                        <p:remoteCommand name="rcExclusao" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs" 
                                         actionListener="#{funcionario.excluirDocumento}" />  

                        <p:remoteCommand name="rcGerarMatrAuto" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs formCadastrar" 
                                         oncomplete="PF('dlgCadastrar').show();"
                                         actionListener="#{funcionario.novoFuncionarioMatr}" />  

                        <p:remoteCommand name="rcNaoGerarMatrAuto" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs formCadastrar" 
                                         oncomplete="PF('dlgCadastrar').show();"
                                         actionListener="#{funcionario.novoFuncionarioSemMatr}" />                            

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Cadastrar}" id="btAddFuncion" onclick="GerarMatriculaAtutomatica()">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Configurar}" actionListener="#{funcionario.preConfiguracaoFuncionEW}"
                                           oncomplete="PF('dlgConfiguracaoEW').show();" update="formConfiguracaoEW">
                                <p:graphicImage url="../assets/img/icone_ew.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{funcionario.PrePesquisar}"
                                           oncomplete="PF('dlgPesquisaRapida').show();" update="formPesquisaRapida">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{funcionario.limparFiltros()}"
                                           update="msgs main:tabela corporativo">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px; display: none">
                            <p:commandLink title="#{localemsgs.Exportar}" actionListener="#{exportarMB.setTitulo(localemsgs.Funcion)}" rendered="false"
                                           oncomplete="PF('dlgExportar').show();" >
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important; overflow-y: auto !important; padding-bottom: 10px !Important">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline">
                                    <p:dataGrid  id="tabela" value="#{funcionario.allFuncion}" paginator="true" rows="100" lazy="true"
                                                 rowsPerPageTemplate="5,10,15,20,25,50,100"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Funcionarios}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport}
                                                 {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista" columns="1" class="tabelaFuncion"
                                                 emptyMessage="#{localemsgs.SemRegistros}">

                                        <div class="Cliente" style="padding-top: 0px !important;">
                                            <p:panel header="#{lista.funcion.nome}" class="ItemCliente #{lista.funcion.chave ne null and lista.funcion.chave ne '' and lista.funcion.chave.split(';')[0] ne null and lista.funcion.chave.split(';')[0] ne ''? 'ComFoto': 'SemFoto'}" style="border-top-color: #3c8dbc !important">
                                                <label class="lblNred">
                                                    #{localemsgs.Nome}
                                                </label>

                                                <div class="col-md-2" style="width: 120px; padding-top: 25px; text-align: center; padding-left: 20px !important; display: #{lista.funcion.chave ne null and lista.funcion.chave ne '' and lista.funcion.chave.split(';')[0] ne null and lista.funcion.chave.split(';')[0] ne ''? '': 'none'}">
                                                    <div title="#{localemsgs.CliqueVisualizarFoto}" style="float: left; background-color: #EEE; border: 2px solid #DDD; width: 100px; height: 100px; /*transform: rotate(90deg);*/ border-radius: 50%; background-image: url(#{mobEW.getFotoGride(lista.funcion.TIpo, lista.funcion.chave, lista.funcion.chave.split(';')[2])}); background-size: cover; background-position: center center; cursor: pointer;" onclick="AbrirFoto('#{mobEW.getFotoGride(lista.funcion.TIpo, lista.funcion.chave, lista.funcion.chave.split(';')[2])}');"></div>
                                                </div>

                                                <div class="col-md-10" style="width: calc(100% - 0px); padding-left: 0px !important;">
                                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                                 layout="grid" styleClass="ui-panelgrid-blank" style="margin-top: 8px !important">                                                    
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.CodFil}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.codFil}">
                                                                    <f:convertNumber pattern="0000"/>
                                                                </h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Nome_Guer}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.nome_Guer}"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Matricula}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.matr}" converter="conversor0"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.CPF}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.CPF}" converter="conversorCPF"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Email}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.email}"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.RG}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.RG}"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.RGOrg}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.orgEmis}"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Dt_Nasc}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.dt_Nasc}" converter="conversorData"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Secao}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.secao}">
                                                                    <f:convertNumber pattern="0000"/>
                                                                </h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Posto}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.pstserv.local}"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Situacao}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.situacao}"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Dt_Situac}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.dt_Situac}" converter="conversorData"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Dt_Admis}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.dt_Admis}" converter="conversorData"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.InterfExt}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.pstserv.interfExt}"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.CodPessoaWeb}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.codPessoaWeb}" converter="conversor0"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Operador}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.operador}"></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Dt_Alter}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.dt_Alter}" converter="conversorData" ></h:outputText>
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Hr_Alter}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.funcion.hr_Alter}" converter="conversorHora" ></h:outputText>
                                                            </div>
                                                        </p:column>
                                                    </p:panelGrid>
                                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                                        <p:column style="padding:0px !important; margin:0px !important;">
                                                            <div class="BotoesGrid" style="width:100px; text-align: right; padding:0px !important;">
                                                                <p:commandLink title="#{localemsgs.Editar}" update="formCadastrar msgs"
                                                                               actionListener="#{funcionario.buttonActionEditar(lista)}">
                                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                                                </p:commandLink>   
                                                            </div>
                                                        </p:column>
                                                        <p:column style="padding:0px !important; margin:0px !important;">
                                                            <div class="BotoesGrid" style="width:100px; text-align: right; padding:0px !important;">
                                                                <p:commandLink title="#{localemsgs.Documentacao}" update="msgs"
                                                                               actionListener="#{funcionario.buttonActionDocumentacaoSel(lista)}">
                                                                    <p:graphicImage url="../assets/img/icone_notas_fiscais.png" height="40"/>
                                                                </p:commandLink>
                                                            </div>
                                                        </p:column>

                                                    </p:panelGrid>
                                                </div>
                                            </p:panel>
                                        </div>


                                    </p:dataGrid>

                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-datagrid-content').height(($('body').height() - 265) + 'px');
                                            else {
                                                $('.ui-datagrid-content').height(($('body').height() - 175) + 'px');

                                            }
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-datagrid-content').height(($('body').height() - 265) + 'px');
                                            else {
                                                $('.ui-datagrid-content').height(($('body').height() - 175) + 'px');

                                            }
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>
                    
                    <p:panel id="pnlUpload" style="display: none">
                        <p:remoteCommand name="rc" 
                                         process="txtTipoArq" />

                        <p:inputText id="txtTipoArq" value="#{funcionario.tipoArq}"></p:inputText>

                        <p:fileUpload id="uploadFotosRelatorio"
                                      fileUploadListener="#{funcionario.HandleFileUpload}"
                                      allowTypes="/(\.|\/)(png|jpe?g|gif|bmp|PNG|JPE?G|GIF|BMP)$/"
                                      auto="true" multiple="false"
                                      process="@this txtTipoArq"
                                      invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                      dragDropSupport="false" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                      update="msgs" skinSimple="true" previewWidth="10"
                                      style="width:10px; height:10px;"></p:fileUpload>
                    </p:panel>
                </h:form>

                <h:form class="form-inline" id="formCadastrar">
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                              style="min-height:95% !important;height:95% !important;max-height:95% !important;max-width:95% !important;min-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <script>
                            /* $(document).ready(function () {
                             //first unbind the original click event
                             PF('dlgCadastrar').closeIcon.unbind('click');
                             
                             //register your own
                             PF('dlgCadastrar').closeIcon.click(function (e) {
                             $("#formCadastrar\\:botaoFechar").click();
                             //should be always called
                             e.preventDefault();
                             });
                             })*/
                        </script>
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarFuncion}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="cadastrar" class="cadastrar" style="background-color:#EEE; overflow:hidden !important; padding:0px !important;">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 0px !important; margin: 0px !important;display:#{funcionario.flag eq 2?'none':''}">
                                <a href="javascript:;" id="btnAtalhoPessoa" class="btn btn-success" style="border-radius: 20px"><i class="fa fa-search"></i>&nbsp;#{localemsgs.PesquisarPessoa}</a>
                            </div>

                            <!-- ITENS PARA REAPROVEITAMENTO DA TELA DE PESSOAS -->
                            <!-- Início -->
                            <h:inputHidden value="#{funcionario.codPessoaAproveitamento}" id="txtReaproveitaPessoal" />
                            <p:remoteCommand name="rcPessoa" partialSubmit="true" 
                                             process="@this,txtReaproveitaPessoal" 
                                             update="msgs pnlDados pnlFixoDados" 
                                             actionListener="#{funcionario.carregarDadosPessoaAproveitamento()}" />                            
                            <!-- FIM -->
                            <div>
                                <p:panel class="col-md-12 col-sm-12 col-xs-12" id="pnlFixoDados" style="background-color:transparent; padding: 10px 0px 0px 0px !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;">
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <label><p:outputLabel for="matr" value="#{localemsgs.Matr}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:inputText id="matr" value="#{funcionario.pessoaMB.novaPessoa.matr}"
                                                     required="true" style="width: 100%"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Matr}"
                                                     disabled="#{funcionario.flag eq 2 or funcionario.novo.matr.equals('0') }"
                                                     label="#{localemsgs.Matr}" maxlength="8">
                                            <f:convertNumber pattern="000000"/>
                                        </p:inputText>    
                                    </div>
                                    <div class="col-md-#{funcionario.flag eq 1?'3':'6'} col-sm-#{funcionario.flag eq 1?'3':'6'} col-xs-6">
                                        <label><p:outputLabel for="situacao2" value="#{localemsgs.Situacao}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:selectOneMenu id="situacao2" required="true"
                                                         value="#{funcionario.novo.situacao}"
                                                         disabled="#{funcionario.flag eq 1}"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}"
                                                         style="width: 100%;">
                                            <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                            <f:selectItem itemLabel="#{localemsgs.Demitido}" itemValue="D" />
                                            <p:ajax update="dataSituacao" process="@this" ></p:ajax>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position: relative">
                                        <p:outputLabel for="dataSituacao" value="#{localemsgs.DataSituacao}" />
                                        <p:calendar id="dataSituacao" value="#{funcionario.novo.dt_Situac}" 
                                                    converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}" styleClass="calendario" disabled="#{funcionario.novo.situacao ne 'D'}"
                                                    style="width: 100%;" required="#{funcionario.novo.situacao ne 'D'?'false':'true'}" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.dt_Situac}" />
                                        <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12" style="display:#{funcionario.flag eq 1?'':'none'}">
                                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding:7px 5px 3px 5px !important; background-color:#FFF; border: thin solid #DDD; border-radius: 3px; margin-top:21px; text-align: center !important">
                                            <p:selectBooleanCheckbox id="matrAut" style="float: right; margin-right: 6px !important;" value="#{funcionario.matriculaAutomatica}">
                                                <p:ajax update="matr" process="@this" listener="#{funcionario.buscarMatriculaAutomatica()}"/>
                                            </p:selectBooleanCheckbox>
                                            <p:outputLabel for="matrAut" value="#{localemsgs.MatrAut}" style="font-size:8pt !important" />
                                        </div>
                                    </div>

                                    <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding:0px !important; margin:0px !important">
                                        <div class="col-md-3 col-sm-3 col-xs-12">
                                            <label><p:outputLabel for="nome_guer" value="#{localemsgs.Nome_Guer}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:inputText id="nome_guer" value="#{funcionario.novo.nome_Guer}"
                                                         label="#{localemsgs.Nome_Guer}" disabled="#{funcionario.flag eq 2}"
                                                         maxlength="10"
                                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome_Guer}"
                                                         style="width: 100%">
                                                <p:ajax event="blur" listener="#{funcionario.validarNomeGuer}"
                                                        update="msgs formCadastrar:nome_guer"/>
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-9 col-sm-9 col-xs-12">
                                            <label><p:outputLabel for="nome" value="#{localemsgs.Nome}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:inputText id="nome" value="#{funcionario.novo.nome}" style="width: 100%"
                                                         required="true" label="#{localemsgs.Nome}"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                                         maxlength="50">
                                            </p:inputText>
                                        </div>
                                    </div>   
                                </p:panel>
                            </div>

                            <p:panel class="col-md-12 col-sm-12 col-xs-12" id="pnlDados" style="background-color:transparent; padding: 10px 0px 0px 0px !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px 10px 10px 10px !Important; background-color: #FFF !important; border: thin solid #CCC !important; border-radius: 4px; margin-bottom: 6px; box-shadow: 2px 2px 3px #DDD">
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <label><p:outputLabel for="subFil" value="#{localemsgs.Filial}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:selectOneMenu id="subFil" value="#{funcionario.filialFuncion}" converter="omnifaces.SelectItemsConverter"
                                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                         filter="true" filterMatchMode="contains"
                                                         style="width: 100%">
                                            <p:ajax event="itemSelect" listener="#{funcionario.atualizaHorarios}" update="formCadastrar:horario formCadastrar:pstserv"/>
                                            <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                           itemLabel="#{filial.descricao}"/>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="interfext" value="#{localemsgs.InterfExt}"/>
                                        <p:inputText id="interfext" value="#{funcionario.novo.interfExt}"
                                                     label="#{localemsgs.InterfExt}"  style="width: 100%">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-12">
                                        <label><p:outputLabel for="pstserv" value="#{localemsgs.PstServ}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:selectOneMenu id="pstserv" value="#{funcionario.novo.secao}" converter="omnifaces.SelectItemsConverter"
                                                         required="true" 
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PstServ}"
                                                         filter="true" filterMatchMode="contains"
                                                         style="width: 100%">
                                            <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Selecione}" />
                                            <f:selectItems value="#{funcionario.listaPostos}" var="pstserv" itemValue="#{pstserv.secao}"
                                                           itemLabel="#{pstserv.secao}, #{pstserv.local}: #{pstserv.descContrato}" noSelectionValue=""/>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-12">
                                        <label><p:outputLabel for="cargo" value="#{localemsgs.Cargo}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:selectOneMenu id="cargo" value="#{funcionario.cargo}" converter="omnifaces.SelectItemsConverter"
                                                         required="true" 
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cargo}"
                                                         filter="true" filterMatchMode="contains"
                                                         style="width: 100%">
                                            <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Selecione}" />
                                            <f:selectItems value="#{funcionario.listaCargos}" var="cargos" itemValue="#{cargos.cargo}"
                                                           itemLabel="#{cargos.descricao}" noSelectionValue=""/>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-12">
                                        <label><p:outputLabel for="horario" value="#{localemsgs.Horario}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:selectOneMenu id="horario" value="#{funcionario.rhHorario}" converter="omnifaces.SelectItemsConverter"
                                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Horario}"
                                                         filter="true" filterMatchMode="contains"
                                                         style="width: 100%">
                                            <f:selectItems value="#{funcionario.rhHorarios}" var="rhHorario" itemValue="#{rhHorario}"
                                                           itemLabel="#{rhHorario.descricao}"/>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <label><p:outputLabel for="turnoEscala" value="#{localemsgs.Escalas}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:selectOneMenu id="turnoEscala" value="#{funcionario.rhEscala}" converter="omnifaces.SelectItemsConverter"
                                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Escala}"
                                                         filter="true" filterMatchMode="contains"
                                                         style="width: 100%">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="" noSelectionOption="true" />
                                            <f:selectItems value="#{funcionario.rhEscalas}" var="rhEscala" itemValue="#{rhEscala}"
                                                           itemLabel="#{rhEscala.codigo} - #{rhEscala.descricao}"/>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12" style="position: relative">
                                        <label><p:outputLabel for="dt_admis" value="#{localemsgs.Dt_Admis}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:calendar id="dt_admis" value="#{funcionario.novo.dt_Admis}" 
                                                    converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Dt_Admis}" style="width: 100%; text-align:left !important;">
                                        </p:calendar>
                                        <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                    </div>

                                    <div class="col-md-9 col-sm-9 col-xs-12">
                                        <label><p:outputLabel for="funcao" value="#{localemsgs.Funcao}"  />  <font style="font-weight: bold; color: red">(*)</font></label>
                                        <p:selectOneMenu
                                            value="#{funcionario.novo.funcao}"
                                            id="funcao"
                                            required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Funcao}"
                                            style="width: 100%;">
                                            <f:selectItem itemLabel="#{localemsgs.Motorista}" itemValue="M"/>
                                            <f:selectItem itemLabel="#{localemsgs.ChefeEquipe}" itemValue="C"/>
                                            <f:selectItem itemLabel="#{localemsgs.Vigilante}" itemValue="V"/>
                                            <f:selectItem itemLabel="#{localemsgs.Patrimonial}" itemValue="P"/>
                                            <f:selectItem itemLabel="#{localemsgs.Todos}" itemValue="T"/>
                                            <f:selectItem itemLabel="#{localemsgs.Outros}" itemValue="O"/>
                                        </p:selectOneMenu>
                                    </div>
                                </div>






                                <div class="col-md-3 col-sm-3 col-xs-12" style="display:#{localeController.number eq 1?'':'none'}">
                                    <p:outputLabel for="cpf" value="#{localemsgs.CPF}"/>
                                    <p:inputMask id="cpf" value="#{funcionario.novo.CPF}" disabled="#{funcionario.flag eq 2}"
                                                 maxlength="11" style="width: 100%" mask="#{mascaras.mascaraCPF}" rendered="#{localeController.number eq 1}">
                                        <p:ajax event="blur" listener="#{funcionario.BuscarPessoa}"
                                                update="formCadastrar:cep_pesquisa msgs"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="rgorg" value="#{localemsgs.RGOrg}"/>
                                    <p:inputText id="rgorg" value="#{funcionario.pessoaMB.novaPessoa.RGOrgEmis}" 
                                                 label="#{localemsgs.RGOrg}" style="width: 100%"
                                                 maxlength="15">
                                        <p:ajax event="change" process="@this" listener="#{funcionario.GerarRG}" update="rg"></p:ajax>
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="rg" value="#{localemsgs.RG}"/>
                                    <p:inputText id="rg" value="#{funcionario.novo.RG}"
                                                 label="#{localemsgs.RG}" style="width: 100%"
                                                 maxlength="20">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="fone1" value="#{localemsgs.Fone1}" />
                                    <p:inputMask id="fone1" mask="#{mascaras.mascaraFone}" value="#{funcionario.pessoaMB.novaPessoa.fone1}"
                                                 style="width: 100%" maxlength="11">
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="fone2" value="#{localemsgs.Fone2}"/>
                                    <p:inputMask id="fone2" mask="#{mascaras.mascaraFone}" value="#{funcionario.pessoaMB.novaPessoa.fone2}" 
                                                 maxlength="11" style="width: 100%">
                                    </p:inputMask>
                                </div>
                                <div class="col-md-#{localeController.number eq 1?'9':'12'} col-sm-#{localeController.number eq 1?'9':'12'} col-xs-12">
                                    <p:outputLabel for="email" value="#{localemsgs.Email}" />
                                    <p:inputText id="email" value="#{funcionario.novo.email}"
                                                 label="#{localemsgs.Email}" style="width: 100%"
                                                 maxlength="50" >
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="cep" value="#{localemsgs.CEP}"/>
                                    <p:inputText id="cep" value="#{funcionario.novo.CEP}" 
                                                 style="width: 100%"
                                                 label="#{localemsgs.CEP}" maxlength="8">
                                    </p:inputText>

                                    <p:commandLink title="#{localemsgs.Pesquisar}"
                                                   partialSubmit="true" process="@this formCadastrar:cep" id="cep_pesquisa" 
                                                   update="formCadastrar:cep formCadastrar:ende formCadastrar:bairro formCadastrar:cidade formCadastrar:uf msgs"
                                                   actionListener="#{funcionario.Endereco}" rendered="#{localeController.number eq 1}">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="24" style="position: absolute; right: 12px; margin-top: 4px;"/>
                                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.AtualizarCEP}" icon="ui-icon-alert" />
                                        <p:confirmDialog global="true" >
                                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                        </p:confirmDialog>
                                        <p:dialog header="#{localemsgs.Aviso}" widgetVar="dlgOk" resizable="false"
                                                  draggable="false" closable="true" width="300">
                                            <div class="form-inline">
                                                <h:outputText value="#{localemsgs.CompletarEndereco}" style="text-align: center"/>
                                                <p:spacer height="20px"/> 
                                            </div>
                                            <p:commandButton value="#{localemsgs.OK}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                             onclick="PF('dlgOk').hide();" />
                                        </p:dialog>
                                    </p:commandLink>
                                </div>
                                <div class="col-md-9 col-sm-9 col-xs-12">
                                    <label><p:outputLabel for="bairro" value="#{localemsgs.Bairro}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                    <p:inputText id="bairro" value="#{funcionario.novo.bairro}" 
                                                 style="width: 100%"
                                                 required="true" label="#{localemsgs.Bairro}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Bairro}"
                                                 maxlength="20" />
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <label><p:outputLabel for="ende" value="#{localemsgs.Endereco}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                    <p:inputText id="ende" value="#{funcionario.novo.endereco}" 
                                                 required="true" label="#{localemsgs.Endereco}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Endereco}"
                                                 style="width: 100%"
                                                 maxlength="50">
                                    </p:inputText>
                                </div>
                                <div class="col-md-9 col-sm-9 col-xs-12">
                                    <label><p:outputLabel for="cidade" value="#{localemsgs.Cidade}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                    <p:autoComplete id="cidade" value="#{funcionario.novo.cidade}" styleClass="cidade" 
                                                    required="true" placeholder="#{localemsgs.Cidade}" completeMethod="#{funcionario.pessoaMB.BuscarCidade}"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}" scrollHeight="200"
                                                    style="width: 100%" maxlength="25" forceSelection="true" >
                                        <p:ajax event="itemSelect" listener="#{funcionario.selecionarCidade}"
                                                update="formCadastrar:cidade formCadastrar:uf"/>
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <label><p:outputLabel for="uf" value="#{localemsgs.UF}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                    <p:inputText id="uf" value="#{funcionario.novo.UF}" disabled="true"
                                                 required="true" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.UF}"
                                                 label="#{localemsgs.UF}" maxlength="2">
                                    </p:inputText>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <p:outputLabel for="obs" value="#{localemsgs.Obs}" />
                                    <p:inputText id="obs" value="#{funcionario.pessoaMB.novaPessoa.obs}" 
                                                 label="#{localemsgs.Obs}" style="width: 100%"
                                                 maxlength="60">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="sexo" value="#{localemsgs.Sexo}"/>
                                    <p:selectOneMenu value="#{funcionario.novo.sexo}"
                                                     id="sexo"

                                                     style="width: 100%;">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Masculino}" itemValue="M"/>
                                        <f:selectItem itemLabel="#{localemsgs.Feminino}" itemValue="F"/>
                                        <f:selectItem itemLabel="#{localemsgs.Outros}" itemValue="O"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="altura" value="#{localemsgs.Altura}" />
                                    <p:inputText id="altura" value="#{funcionario.pessoaMB.novaPessoa.altura}" 
                                                 style="width: 100%"
                                                 label="#{localemsgs.Altura}" maxlength="4">
                                        <f:convertNumber pattern="000"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="peso" value="#{localemsgs.Peso}"/>
                                    <p:inputText id="peso" value="#{funcionario.pessoaMB.novaPessoa.peso}" 
                                                 style="width: 100%"
                                                 label="#{localemsgs.Peso}" maxlength="3">
                                        <f:convertNumber pattern="000"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="dt_nasc" value="#{localemsgs.Dt_Nasc}" />  <font style="font-weight: bold; color: red">(*)</font></label>

                                    <p:calendar id="dt_nasc" value="#{funcionario.novo.dt_Nasc}" yearRange="1900:2021"
                                                converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Dt_Nasc}" />
                                    <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                </div>





                                <div class="row" style="margin: 0px !important; padding: 0px !important;">

                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="nacionalidade" value="#{localemsgs.Nacionalidade}"/>
                                        <p:selectOneMenu value="#{funcionario.novo.nacionalid}"
                                                         id="nacionalidade"
                                                         style="width: 100%;">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.Brasileiro}" itemValue="10"/>
                                            <f:selectItem itemLabel="#{localemsgs.Naturalizado}" itemValue="20"/>
                                            <f:selectItem itemLabel="#{localemsgs.Argentino}" itemValue="21"/>
                                            <f:selectItem itemLabel="#{localemsgs.Boliviano}" itemValue="22"/>
                                            <f:selectItem itemLabel="#{localemsgs.Suico}" itemValue="38"/>
                                            <f:selectItem itemLabel="#{localemsgs.Italiano}" itemValue="39"/>
                                            <f:selectItem itemLabel="#{localemsgs.Hatiano}" itemValue="40"/>
                                            <f:selectItem itemLabel="#{localemsgs.Japones}" itemValue="41"/>
                                            <f:selectItem itemLabel="#{localemsgs.Chileno}" itemValue="23"/>
                                            <f:selectItem itemLabel="#{localemsgs.Chines}" itemValue="42"/>
                                            <f:selectItem itemLabel="#{localemsgs.Paraguaio}" itemValue="24"/>
                                            <f:selectItem itemLabel="#{localemsgs.Coeano}" itemValue="43"/>
                                            <f:selectItem itemLabel="#{localemsgs.Uruguaio}" itemValue="25"/>
                                            <f:selectItem itemLabel="#{localemsgs.Russo}" itemValue="44"/>
                                            <f:selectItem itemLabel="#{localemsgs.Venezuelano}" itemValue="26"/>
                                            <f:selectItem itemLabel="#{localemsgs.Portugues}" itemValue="45"/>
                                            <f:selectItem itemLabel="#{localemsgs.Colombiano}" itemValue="27"/>
                                            <f:selectItem itemLabel="#{localemsgs.Paquistanes}" itemValue="46"/>
                                            <f:selectItem itemLabel="#{localemsgs.Peruano}" itemValue="28"/>
                                            <f:selectItem itemLabel="#{localemsgs.Indiano}" itemValue="47"/>
                                            <f:selectItem itemLabel="#{localemsgs.Equatoriano}" itemValue="29"/>
                                            <f:selectItem itemLabel="#{localemsgs.OutrosLatino}" itemValue="48"/>
                                            <f:selectItem itemLabel="#{localemsgs.Alemao}" itemValue="30"/>
                                            <f:selectItem itemLabel="#{localemsgs.OutrosAsiatico}" itemValue="49"/>
                                            <f:selectItem itemLabel="#{localemsgs.Belga}" itemValue="31"/>
                                            <f:selectItem itemLabel="#{localemsgs.OutrosEuropeu}" itemValue="51"/>
                                            <f:selectItem itemLabel="#{localemsgs.Britanico}" itemValue="32"/>
                                            <f:selectItem itemLabel="#{localemsgs.Angolano}" itemValue="60"/>
                                            <f:selectItem itemLabel="#{localemsgs.Canadense}" itemValue="34"/>
                                            <f:selectItem itemLabel="#{localemsgs.Congoles}" itemValue="61"/>
                                            <f:selectItem itemLabel="#{localemsgs.Espanhol}" itemValue="35"/>
                                            <f:selectItem itemLabel="#{localemsgs.SulAfricano}" itemValue="62"/>
                                            <f:selectItem itemLabel="#{localemsgs.NorteAmericano}" itemValue="36"/>
                                            <f:selectItem itemLabel="#{localemsgs.OutrosAfricano}" itemValue="70"/>
                                            <f:selectItem itemLabel="#{localemsgs.Frances}" itemValue="37"/>
                                            <f:selectItem itemLabel="#{localemsgs.OutrosDesc}" itemValue="80"/>
                                            <p:ajax event="itemSelect" update="formCadastrar:anoCheg formCadastrar:ingresso"></p:ajax>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="anoCheg" value="#{localemsgs.AnoCheg}" /></label>

                                        <p:inputText id="anoCheg" value="#{funcionario.novo.anoCheg}" 
                                                     style="width: 100%" maxlength="4" disabled="#{funcionario.novo.nacionalid eq null or funcionario.novo.nacionalid eq '' or funcionario.novo.nacionalid eq '10'}">
                                            <f:convertNumber pattern="000"/>
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="ingresso" value="#{localemsgs.Ingresso}"/>
                                        <p:selectOneMenu value="#{funcionario.novo.interfExt}"
                                                         id="ingresso"
                                                         style="width: 100%;"  disabled="#{funcionario.novo.nacionalid eq null or funcionario.novo.nacionalid eq '' or funcionario.novo.nacionalid eq '10'}">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.VistoPermanente}" itemValue="1"/>
                                            <f:selectItem itemLabel="#{localemsgs.VistoTemporario}" itemValue="2"/>
                                            <f:selectItem itemLabel="#{localemsgs.Asilado}" itemValue="3"/>
                                            <f:selectItem itemLabel="#{localemsgs.Refugiado}" itemValue="4"/>
                                            <f:selectItem itemLabel="#{localemsgs.SolicitanteRefugio}" itemValue="5"/>
                                            <f:selectItem itemLabel="#{localemsgs.ResidForaBrasil}" itemValue="6"/>
                                            <f:selectItem itemLabel="#{localemsgs.DefFisico}" itemValue="7"/>
                                            <f:selectItem itemLabel="#{localemsgs.ResidProv}" itemValue="8"/>
                                            <f:selectItem itemLabel="#{localemsgs.PermanBrasil}" itemValue="9"/>
                                            <f:selectItem itemLabel="#{localemsgs.BenefAcordo}" itemValue="10"/>
                                            <f:selectItem itemLabel="#{localemsgs.DepAgente}" itemValue="11"/>
                                            <f:selectItem itemLabel="#{localemsgs.BenefTrat}" itemValue="12"/>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="naturalidade" value="#{localemsgs.Naturalidade}"/>
                                        <p:autoComplete id="naturalidade" value="#{funcionario.novo.naturalid}" styleClass="cidade" 
                                                        completeMethod="#{funcionario.pessoaMB.BuscarCidade}"
                                                        scrollHeight="200"
                                                        style="width: 100% !important" maxlength="25" forceSelection="true" >
                                            <p:ajax event="itemSelect" listener="#{funcionario.selecionarCidade}" />
                                        </p:autoComplete>
                                    </div>
                                </div>
                                <div class="row" style="margin: 0px !important; padding: 0px !important;">
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="raca" value="#{localemsgs.Raca}"/>
                                        <p:selectOneMenu value="#{funcionario.novo.raca}"
                                                         id="raca"
                                                         style="width: 100%;">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.Branca}" itemValue="2"/>
                                            <f:selectItem itemLabel="#{localemsgs.Negra}" itemValue="4"/>
                                            <f:selectItem itemLabel="#{localemsgs.Amarela}" itemValue="6"/>
                                            <f:selectItem itemLabel="#{localemsgs.Parda}" itemValue="8"/>
                                            <f:selectItem itemLabel="#{localemsgs.Indigena}" itemValue="0"/>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-12">
                                        <p:outputLabel for="instrucao" value="#{localemsgs.Instrucao}"/>
                                        <p:selectOneMenu value="#{funcionario.novo.instrucao}"
                                                         id="instrucao"
                                                         style="width: 100%;">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItems value="#{funcionario.listTbVal}" var="listaInstrucao" itemValue="#{listaInstrucao.codigo}" itemLabel="#{listaInstrucao.descricao}"/>
                                        </p:selectOneMenu>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="estadoCivil" value="#{localemsgs.EstadoCivil}"/>
                                        <p:selectOneMenu value="#{funcionario.novo.estCivil}"
                                                         id="estadoCivil"
                                                         style="width: 100%;">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.Solteiro}" itemValue="S"/>
                                            <f:selectItem itemLabel="#{localemsgs.Casado}" itemValue="C"/>
                                            <f:selectItem itemLabel="#{localemsgs.Desquitado}" itemValue="Q"/>
                                            <f:selectItem itemLabel="#{localemsgs.Divorciado}" itemValue="D"/>
                                            <f:selectItem itemLabel="#{localemsgs.Viuvo}" itemValue="V"/>
                                            <f:selectItem itemLabel="#{localemsgs.Marital}" itemValue="M"/>
                                        </p:selectOneMenu>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="conjuge" value="#{localemsgs.Conjuge}" /></label>

                                    <p:inputText id="conjuge" value="#{funcionario.novo.conjuge}" 
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="pai" value="#{localemsgs.Pai}" /></label>

                                    <p:inputText id="pai" value="#{funcionario.novo.pai}" 
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="mae" value="#{localemsgs.Mae}" /></label>

                                    <p:inputText id="mae" value="#{funcionario.novo.mae}" 
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="pis" value="#{localemsgs.PIS}" /></label>

                                    <p:inputText id="pis" value="#{funcionario.novo.PIS}" 
                                                 style="width: 100%" disabled="#{funcionario.novo.PIS ne null and funcionario.novo.PIS ne ''}" >
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="ctpsNro" value="#{localemsgs.CTPS_Nro}" /></label>

                                    <p:inputText id="ctpsNro" value="#{funcionario.novo.CTPS_Nro}"
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="row" style="padding: 0px; margin: 0px">
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="ctpsSerie" value="#{localemsgs.CTPS_Serie}" /></label>

                                        <p:inputText id="ctpsSerie" value="#{funcionario.novo.CTPS_Serie}"
                                                     style="width: 100%">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="ctpsUF" value="#{localemsgs.CTPS_UF}" /></label>

                                        <p:inputText id="ctpsUF" value="#{funcionario.novo.CTPS_UF}"
                                                     style="width: 100%" maxlength="2">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="ctpsEmiss" value="#{localemsgs.Emissao}" /></label>

                                        <p:calendar id="ctpsEmiss" value="#{funcionario.novo.CTPS_Emis}" yearRange="1950:2021"
                                                    converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                    style="width: 100%;"  />
                                        <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                    </div>

                                    <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="cnh" value="#{localemsgs.CNH}" /></label>

                                        <p:inputText id="cnh" value="#{funcionario.novo.CNH}"
                                                     style="width: 100%">
                                        </p:inputText>
                                    </div>
                                </div>
                                <div class="row" style="padding: 0px; margin: 0px">
                                    <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="cnhVenc" value="#{localemsgs.Vencimento}" /></label>

                                        <p:calendar id="cnhVenc" value="#{funcionario.novo.dt_VenCNH}" yearRange="1950:2026"
                                                    converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                    style="width: 100%;"  />
                                        <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="cnhCat" value="#{localemsgs.Categoria}" /></label>

                                        <p:inputText id="cnhCat" value="#{funcionario.novo.categoria}"
                                                     style="width: 100%" maxlength="2">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="cnhUF" value="#{localemsgs.UF}" /></label>

                                        <p:inputText id="cnhUF" value="#{funcionario.novo.UF_CNH}"
                                                     style="width: 100%" maxlength="2">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="reservista" value="#{localemsgs.Reservista}" /></label>

                                        <p:inputText id="reservista" value="#{funcionario.novo.reservista}"
                                                     style="width: 100%">
                                        </p:inputText>
                                    </div>
                                </div>
                                <div class="row" style="padding: 0px; margin: 0px">
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="reservistaCat" value="#{localemsgs.Categoria}" /></label>

                                        <p:inputText id="reservistaCat" value="#{funcionario.novo.reservCat}"
                                                     style="width: 100%">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="tituloEleitor" value="#{localemsgs.TituloEleitor}" /></label>

                                        <p:inputText id="tituloEleitor" value="#{funcionario.novo.titEleit}"
                                                     style="width: 100%">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="tituloEleitorZona" value="#{localemsgs.Zona}" /></label>

                                        <p:inputText id="tituloEleitorZona" value="#{funcionario.novo.titEZona}"
                                                     style="width: 100%">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="tituloEleitorSecao" value="#{localemsgs.secao}" /></label>

                                        <p:inputText id="tituloEleitorSecao" value="#{funcionario.novo.titSecao}"
                                                     style="width: 100%">
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-12 col-sm-12 col-xs-12" style="position:relative;">
                                        <label style="margin:0px !important"><p:outputLabel for="grupoSang" value="#{localemsgs.GrupoSanguineo}" /></label>

                                        <p:selectOneMenu value="#{funcionario.novo.grupoSang}"
                                                         id="grupoSang"
                                                         style="width: 100%;">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="A+" itemValue="A+"/>
                                            <f:selectItem itemLabel="A-" itemValue="A-"/>
                                            <f:selectItem itemLabel="B+" itemValue="B+"/>
                                            <f:selectItem itemLabel="B-" itemValue="B-"/>
                                            <f:selectItem itemLabel="AB+" itemValue="AB+"/>
                                            <f:selectItem itemLabel="AB-" itemValue="AB-"/>
                                            <f:selectItem itemLabel="O-" itemValue="O+"/>
                                            <f:selectItem itemLabel="O+" itemValue="O-"/>
                                        </p:selectOneMenu>
                                    </div>
                                </div>
                            </p:panel> 
                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right">
                                <p:commandLink rendered="#{funcionario.flag eq 2}" id="documentacao" actionListener="#{funcionario.buttonActionDocumentacao2}"
                                               process="@this"
                                               update=":msgs" style="width: 150px !Important; margin-right: 15px; margin-top: 5px"
                                               title="#{localemsgs.Documentacao}" styleClass="btn btn-success">
                                    <i class="fa fa-file-text" style="margin-right:8px !important; display: block; margin-top: 4px;"></i>#{localemsgs.Documentacao}
                                </p:commandLink>

                                <p:commandLink rendered="#{funcionario.flag eq 1}" id="cadastro" action="#{funcionario.Cadastrar}"
                                               update=":msgs :main:tabela cabecalho formCadastrar:cadastrar"
                                               title="#{localemsgs.Cadastrar}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important;"></i>#{localemsgs.Salve}
                                </p:commandLink>
                                <p:commandLink rendered="#{funcionario.flag eq 2}" id="editar" action="#{funcionario.Cadastrar}"
                                               update=":msgs :main:tabela cabecalho formCadastrar:cadastrar" style="width: 150px !Important;margin-top: 5px"
                                               title="#{localemsgs.Editar}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important;  display: block; margin-top: 4px;"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Geração do link de configuração do EW-->
                <h:form id="formConfiguracaoEW">
                    <p:hotkey bind="esc" oncomplete="PF('dlgConfiguracaoEW').hide()"/>

                    <p:remoteCommand name="preConfiguracaoFuncionEW" partialSubmit="true" 
                                     process="@this" 
                                     update="msgs formConfiguracaoEW" 
                                     oncomplete="PF('dlgConfiguracaoEW').show();"
                                     actionListener="#{funcionario.preConfiguracaoFuncionEW}" />  

                    <p:remoteCommand name="enviarConfiguracaoFuncionEW" partialSubmit="true" 
                                     process="@this" 
                                     update="msgs formConfiguracaoEW" 
                                     actionListener="#{funcionario.enviarConfiguracaoFuncionEW}" />    
                    <p:dialog
                        widgetVar="dlgConfiguracaoEW" positionType="absolute" responsive="true" focus="opcao" draggable="false" modal="true"
                        closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; 
                        box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;
                        border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; 
                        padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarFuncion}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="panelConfiguracoesEW" style="background: transparent">                            
                            <p:outputLabel for="funcion" value="#{localemsgs.Funcion}"/>
                            <p:autoComplete id="funcion" value="#{funcionario.configuracaoEWFuncion}" styleClass="pstserv"
                                            label="#{localemsgs.Funcion}" placeholder="#{localemsgs.Funcion}" completeMethod="#{funcionario.buscarFuncion}" 
                                            style="width: 100%;" minQueryLength="3" scrollHeight="200" forceSelection="true"
                                            var="func" itemValue="#{func}" itemLabel="#{func.nome}" disabled="#{funcionario.configuracaoEWLiberarFuncion}">
                                <p:ajax event="itemSelect" listener="#{funcionario.selectionarFuncion}" update="funcion filial enviar cliente msgs" />
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{funcionario.configuracaoEWFuncionList}" />
                                <p:watermark for="funcion" value="#{localemsgs.Funcion}"/>
                            </p:autoComplete>

                            <p:outputLabel for="filial" value="#{localemsgs.Filial}"/>
                            <p:inputText id="filial" value="#{funcionario.configuracaoEWFilial.descricao}" styleClass="pstserv" disabled="true"
                                         label="#{localemsgs.Filial}"
                                         style="width: 100%;">
                                <p:watermark for="filial" value="#{localemsgs.Filial}"/>
                            </p:inputText>

                            <p:outputLabel for="cliente" value="#{localemsgs.Cliente}"/>
                            <p:autoComplete id="cliente" value="#{funcionario.configuracaoEWCliente}" styleClass="pstserv"
                                            label="#{localemsgs.Cliente}" placeholder="#{localemsgs.Cliente}" disabled="#{funcionario.configuracaoEWLiberarCliente}"
                                            completeMethod="#{funcionario.buscarCliente}" scrollHeight="200"
                                            style="width: 100%;" forceSelection="true" minQueryLength="3" 
                                            var="cli" itemValue="#{cli}" itemLabel="#{cli.codExt}">
                                <p:ajax event="itemSelect" listener="#{funcionario.selectionarCliente}" update="cliente pstserv enviar msgs" />
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{funcionario.configuracaoEWClienteList}" />
                            </p:autoComplete>

                            <p:outputLabel for="pstserv" value="#{localemsgs.PstServ}"/>
                            <p:selectOneMenu id="pstserv" value="#{funcionario.configuracaoEWPstServ}" converter="omnifaces.SelectItemsConverter" 
                                             styleClass="pstserv" required="true"  disabled="#{funcionario.configuracaoEWLiberarPosto}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PstServ}" label="#{localemsgs.PstServ}"
                                             filter="true" filterMatchMode="contains" placeholder="#{localemsgs.PstServ}">
                                <p:ajax listener="#{funcionario.selectionarPstServ}" update="cliente pstserv enviar cancelar panelConfiguracoesEW msgs" />
                                <f:selectItems value="#{funcionario.configuracaoEWPstServList}" var="pst" itemValue="#{pst}"
                                               itemLabel="#{pst.descricao}"  noSelectionValue="Selecione"/>
                                <p:watermark for="pstserv" value="#{localemsgs.PstServ}"/>
                            </p:selectOneMenu>


                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-top: 10px !important; padding-right:0px !important">

                                <p:commandLink id="cancelar" actionListener="#{funcionario.preConfiguracaoFuncionEW}"
                                               oncomplete="PF('dlgConfiguracaoEW').show();"
                                               rendered="#{funcionario.configuracaoEWLiberarPosto 
                                                           and funcionario.configuracaoEWLiberarCliente 
                                                           and funcionario.configuracaoEWLiberarFuncion}"
                                               update=":msgs formConfiguracaoEW" style="margin: 10px;"
                                               title="#{localemsgs.Cadastrar}" styleClass="btn btn-danger">
                                    <i class="fa fa-times" style="margin-right:8px !important"></i>#{localemsgs.Cancelar}
                                </p:commandLink>

                                <p:commandLink id="enviar" action="#{funcionario.alertaWhatsApp}" 
                                               rendered="#{funcionario.configuracaoEWLiberarPosto 
                                                           and funcionario.configuracaoEWLiberarCliente 
                                                           and funcionario.configuracaoEWLiberarFuncion}"
                                               update=":msgs formConfiguracaoEW" style="margin: 10px;"
                                               title="#{localemsgs.Enviar}" styleClass="btn btn-primary">
                                    <i class="fa fa-check" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisa rápida funcionário-->
                <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarFuncion}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelPesquisaRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{funcionario.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Matr}" itemValue="MATR" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome_Guer}" itemValue="NOME_GUER" />
                                        <f:selectItem itemLabel="#{localemsgs.PstServ}" itemValue="LOCAL" />
                                        <f:selectItem itemLabel="#{localemsgs.PIS}" itemValue="PIS" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'MATR'}" value="#{localemsgs.Matr}: "/>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'NOME'}" value="#{localemsgs.Nome}: "/>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'NOME_GUER'}" value="#{localemsgs.Nome_Guer}: "/>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'LOCAL'}" value="#{localemsgs.PstServ}: "/>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'PIS'}" value="#{localemsgs.PIS}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{funcionario.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-right:0px !important">

                                <p:commandLink id="botaoPesquisaRapida" action="#{funcionario.pesquisarUnico}" update="msgs" oncomplete="$(window).resize();"
                                               styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-search" style="margin-right:8px !important"></i>#{localemsgs.Pesquisar}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                </script>

                <!--Pesquisar funcionário-->
                <h:form id="formPesquisar">
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarFuncion}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel value="#{localemsgs.CodFil}: " for="codfil"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil"
                                                     value="#{funcionario.selecionado.funcion.codFil}"
                                                     converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true"
                                                     filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel value="#{localemsgs.Nome}: " for="nome"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome" value="#{funcionario.selecionado.funcion.nome}" label="#{localemsgs.Nome}"
                                                 maxlength="50" style="width:100%">
                                        <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nome_guer" value="#{localemsgs.Nome_Guer}:"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome_guer" value="#{funcionario.selecionado.funcion.nome_Guer}"
                                                 label="#{localemsgs.Nome_Guer}"
                                                 style="width:100%">

                                        <p:watermark for="nome_guer" value="#{localemsgs.Nome_Guer}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel value="#{localemsgs.Matr}: " for="matr"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="matr" value="#{funcionario.selecionado.funcion.matr}" label="#{localemsgs.Matr}"
                                                 maxlength="50" style="width:100%">
                                        <p:watermark for="matr" value="#{localemsgs.Matr}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left;">
                                    <p:outputLabel for="situacao2" value="#{localemsgs.Situacao}:" />
                                </div>
                                <div style="width: 75%; float: left;">
                                    <p:selectOneRadio id="situacao2"
                                                      value="#{funcionario.selecionado.funcion.situacao}"
                                                      style="width: 100%;">
                                        <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                        <f:selectItem itemLabel="#{localemsgs.Demitido}" itemValue="D" />
                                    </p:selectOneRadio>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="pstserv" value="#{localemsgs.PstServ}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:autoComplete id="pstserv" value="#{funcionario.selecionado.pstserv.local}" styleClass="pstserv"
                                                    label="#{localemsgs.cidade}" completeMethod="#{funcionario.BuscarPstServ}" scrollHeight="200"
                                                    style="width: 100%;">
                                        <p:watermark for="pstserv" value="#{localemsgs.PstServ}"/>
                                    </p:autoComplete>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{funcionario.PesquisaPaginada}" update=" :main:tabela :msgs"
                                               title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisar').hide()">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Exportar-->
                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" width="400"
                          style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                    </f:facet>
                    <h:form class="form-inline">
                        <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                        <p:separator />
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="filial" value="#{funcionario.eFilial}">
                                    <p:ajax update="labelFilial"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{funcionario.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="matr" value="#{funcionario.eMatr}">
                                    <p:ajax update="labelMatr"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelMatr" value="#{localemsgs.Matr}" style="#{funcionario.eMatr eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="nome" value="#{funcionario.eNome}">
                                    <p:ajax update="labelNome"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelNome" value="#{localemsgs.Nome}" style="#{funcionario.eNome eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="email" value="#{funcionario.eEmail}">
                                    <p:ajax update="labelEmail"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelEmail" value="#{localemsgs.Email}" style="#{funcionario.eEmail eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cpf" value="#{funcionario.eCPF}">
                                    <p:ajax update="labelCPF"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCPF" value="#{localemsgs.CPF}" style="#{funcionario.eCPF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="rg" value="#{funcionario.eRG}">
                                    <p:ajax update="labelRG"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelRG" value="#{localemsgs.RG}" style="#{funcionario.eRG ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="orgemis" value="#{funcionario.eOrgEmis}">
                                    <p:ajax update="labelOrgEmis"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelOrgEmis" value="#{localemsgs.RGOrg}" style="#{funcionario.eOrgEmis eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="NomeGuer" value="#{funcionario.eNomeGuer}">
                                    <p:ajax update="eNomeGuer"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eNomeGuer" value="#{localemsgs.Nome_Guer}" style="#{funcionario.eNomeGuer eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="nasc" value="#{funcionario.eDtNasc}">
                                    <p:ajax update="labelNasc"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelNasc" value="#{localemsgs.Dt_Nasc}" style="#{funcionario.eDtNasc eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="sit" value="#{funcionario.eSituacao}">
                                    <p:ajax update="labelSit"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSit" value="#{localemsgs.Situacao}" style="#{funcionario.eSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="eDtSituacao" value="#{funcionario.eDtSituacao}">
                                    <p:ajax update="labelSituacao"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSituacao" value="#{localemsgs.Dt_Situac}" style="#{funcionario.eDtSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="eDtAdmis" value="#{funcionario.eDtAdmis}">
                                    <p:ajax update="labelDtAdmis"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDtAdmis" value="#{localemsgs.Dt_Admis}" style="#{funcionario.eDtAdmis eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="posto" value="#{funcionario.ePosto}">
                                    <p:ajax update="labelPosto"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelPosto"  value="#{localemsgs.Posto}" style="#{funcionario.ePosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="CodPosto" value="#{funcionario.eCodPosto}">
                                    <p:ajax update="eCodPosto"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eCodPosto"  value="#{localemsgs.Secao}" style="#{funcionario.eCodPosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="CodPessoaWeb" value="#{funcionario.eCodPessoaWeb}">
                                    <p:ajax update="eCodPessoaWeb"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eCodPessoaWeb"  value="#{localemsgs.CodPessoa}" style="#{funcionario.eCodPessoaWeb eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="InterfExt" value="#{funcionario.eInterfExt}">
                                    <p:ajax update="eInterfExt"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eInterfExt" value="#{localemsgs.InterfExt}" style="#{funcionario.eInterfExt eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="hralter" value="#{funcionario.eHrAlter}">
                                    <p:ajax update="labelHrAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{funcionario.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="dtalter" value="#{funcionario.eDtAlter}">
                                    <p:ajax update="labelDtAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{funcionario.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="operador" value="#{funcionario.eOperador}">
                                    <p:ajax update="labelOperador"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{funcionario.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <p:separator />
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:panel style="text-align: center;background-color:#EEE;">
                                <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                                <h:commandLink id="pdf" actionListener="#{funcionario.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_pdf.png" style="height:32px;"/>
                                    <p:dataExporter target="main:tabela" type="pdf" fileName="#{localemsgs.Funcionarios}"
                                                    preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                                </h:commandLink>
                            </p:panel>

                            <p:panel style="text-align: center;background-color:#EEE;">
                                <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                                <h:commandLink id="xlsx" actionListener="#{funcionario.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_xls.png" style="height:32px;"/>
                                    <p:dataExporter target="main:tabela" type="xlsx" fileName="#{localemsgs.Funcionarios}"/>
                                </h:commandLink>
                            </p:panel>
                        </p:panelGrid>
                    </h:form>
                </p:dialog>

            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <script type="text/javascript">
                // <![CDATA[
                function CarregarDadosAproveitamento(inCodigoPessoa) {
                    $JanelaFormClientes.close();
                    $('[id*="txtReaproveitaPessoal"]').val(inCodigoPessoa);
                    rcPessoa();
                }

                function GerarMatriculaAtutomatica() {
                    $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}',
                            '#{localemsgs.DesejaGerarMatrAuto}',
                            '#{localemsgs.Sim}',
                            '#{localemsgs.Nao}',
                            function () {
                                rcGerarMatrAuto();
                            },
                            function () {
                                rcNaoGerarMatrAuto();
                            });
                }

                function AlertaEnvioWhatsapp(mensagem) {
                    console.log(mensagem);
                    $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}',
                            mensagem,
                            '#{localemsgs.Sim}',
                            '#{localemsgs.Nao}',
                            function () {
                                enviarConfiguracaoFuncionEW();
                            },
                            function () {
                                preConfiguracaoFuncionEW();
                            });
                }

                $(document)
                        .on('mousedown', '#btnAtalhoPessoa', function () {
                            let Altura = $('body').height() - 250;

                            let HTML = '<iframe id="ifrPessoas" src="pessoas.xhtml?selecao=S" style="border:thin solid #CCC !important; margin:0px !important; padding:0px !important; width: 100% !important; height: ' + Altura.toString() + 'px !important;"></iframe>';

                            $JanelaFormClientes = $.alert({
                                icon: 'fa fa-search',
                                type: 'blue',
                                title: '<font color="#000">#{localemsgs.SelecionePessoa}</font>',
                                content: HTML,
                                boxWidth: '96%',
                                closeIcon: true,
                                useBootstrap: false,
                                buttons: {
                                    cancel: {
                                        btnClass: 'btn-red',
                                        text: '<i class="fa fa-ban"></i>&nbsp;#{localemsgs.Cancelar}'
                                    }
                                }
                            });
                        })
                        ;
                // ]]>
            </script>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{funcionario.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela" listener="#{funcionario.MostrarFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.SomenteAtivos}: " /></label>
                                <p:selectBooleanCheckbox value="#{funcionario.somenteAtivos}">
                                    <p:ajax update="msgs main:tabela" listener="#{funcionario.SomenteAtivos}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>
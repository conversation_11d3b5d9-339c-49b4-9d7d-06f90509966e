/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2200;
import SasBeans.ESocial.S2200.Dependentes;
import SasBeans.ESocial.S2200.Horario;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2200Dao {

    public List<S2200> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select (Select top 1 substring(xml_retorno,\n"
                    + "                        charindex('<nrRecibo>',xml_retorno)+10, \n"
                    + "                        charindex('</nrRecibo>',xml_retorno) - charindex('<nrRecibo>',xml_retorno)-10) "
                    + "                             vinculo_nrRecInfPrelim \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Convert(BigInt,Funcion.CPF) \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = ? \n"
                    + "                                 and z.Compet = ? \n"
                    + "                                 and z.Ambiente = ? \n"
                    + "                                 and len(Xml_Retorno) > 0 \n"
                    + "                                 and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') "
                    + " Order by Sequencia desc) vinculo_nrRecInfPrelim, Funcion.Nacionalid, "
                    + " Funcion.PgCtSin ,RHHorario.tipocomp, RHHorario.chcomp, Funcion.CPF trabalhador_cpfTrab, "
                    + " Funcion.PIS trabalhador_nisTrab, Funcion.Nome trabalhador_nmTrab, "
                    + " Funcion.Sexo trabalhador_sexo, Funcion.Raca trabalhador_racaCor,  Funcion.EstCivil trabalhador_estCiv, "
                    + " Funcion.Instrucao trabalhador_grauInstr, Funcion.TipoAdm trabalhador_indPriEmpr, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto, "
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie CTPS_serieCtps, "
                    + " Funcion.CTPS_UF CTPS_ufCtps, Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor, "
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped, "
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid, "
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd, "
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, Municipios.CodIBGE brasil_codMunic, "
                    + " Funcion.UF brasil_uf, Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, Funcion.Email contato_emailPrinc, "
                    + " Convert(BigInt, Funcion.Matr) vinculo_matricula, substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoCeletista_dtAdm, "
                    + " Fornec.CNPJ infoCeletista_cnpjSindCategProf, Funcion.Salario remuneracao_vrSalFx,  Funcion.FormaPgto remuneracao_undSalFixo, "
                    + " Clientes.CGC localTrabGeral_nrInsc, Cargos.Descricao infoContrato_nmCargo,  Cargos.CBO,  RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, "
                    + " RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, RHEscala.HrSabNot, RHEscala.HrDomDiu, "
                    + " RHEscala.HrDomNot, Convert(bigint,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, RHHorario.D1, "
                    + " RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, Funcion.Situacao, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Demis,111),'/','-'),0,11) desligamento_dtDesligamento, "
                    + " Case when Funcion.TipoADM = 70 then 2 else 1 end infoCeletista_tpAdmissao,  "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc, Funcion.DefFis InfoDeficiencia_defFisica, FuncionAdic.DiasExperiencia, "
                    + " Funcion.DefFisTipo, FuncionAdic.EstClassTrab, FuncionAdic.EstCasadoBr, FuncionAdic.EstFilhosBr, substring(replace(convert(varchar,FuncionAdic.EstDtChegada,111),'/','-'),0,11) EstDtChegada, "
                    + " substring(replace(convert(varchar,FuncionAdic.Dt_TerminoExp1,111),'/','-'),0,11) duracao_dtTerm, "                    
                    + " Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                       (Select Max(CodIBGE) From Municipios where Nome = Substring(Naturalid,1,(CharIndex('/',Naturalid)-1)) "
                    + "                                                         and UF = Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) and Len(Naturalid) > 0) "
                    + "                    else '0' end nascimento_codMunic, "
                    + "                    Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                    (Select Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) UF from Funcion where Matr = FPMensal.Matr) "
                    + " else '0' end nascimento_uf, "
                    + " Case when Funcion.TrabIntermitente = 'S' then '111' "
                    + "      when Funcion.vinculo in ('E','J','M') then '103' "
                    + "      when Funcion.TipoADM = 25 then 105 "
                    + "      when Funcion.Vinculo in ('A') then '701' "
                    + "      else '101' end infoContrato_codCateg, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso "
                    + " from FPMensal "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr "
                    + " Left Join Cargos  on Cargos.Cargo = Funcion.Cargo "
                    + " Left Join Sindicatos  on Sindicatos.Codigo = FPMensal.Sindicato "
                    + " Left Join PstServ  on PstServ.Secao  = FPMensal.Secao "
                    + "                   and PstServ.CodFil = FPMensal.CodFil "
                    + " Left Join Clientes  on Clientes.Codigo = PstServ.CodCli "
                    + "                    and Clientes.CodFil = PstServ.CodFil "
                    + " Left Join RHEscala  on RHEscala.Codigo = FPMEnsal.Escala "
                    + "                    and RHEscala.CodFil = FPMEnsal.CodFil "
                    + " Left Join RHHorario  on  RHHorario.Codigo    = FPMensal.Horario "
                    + "                      and RHHorario.CodFil    = FPMensal.CodFil "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade "
                    + "                     and Municipios.UF   = Funcion.UF"
                    + " Left Join Fornec on Fornec.Codigo = Sindicatos.CodForn"
                    + " Left join FuncionAdic  on FuncionAdic.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' "
                    + " and Funcion.codfil = ? "
                    + " and FPMensal.situacao <> 'D' "
                    + " and Funcion.Vinculo not in ('D','E','S','A') "
                    //                    + " and Funcion.Matr = 529 "
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    //                    + "                             from FPPeriodos where DtFecha <= Convert(Date,Getdate())) "
                    + "                             from FPPeriodos where DtInicio = ?) "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet + "-01");
            consulta.select();
            List<S2200> listaS2200 = new ArrayList<>();
            S2200 s2200;
            while (consulta.Proximo()) {
                s2200 = new S2200();
                s2200.setIdeEvento_indRetif("1");
                s2200.setIdeEvento_procEmi("1");
                s2200.setIdeEvento_verProc("Satellite eSocial");
                s2200.setSucesso(consulta.getInt("sucesso"));
                s2200.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2200.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2200.setTrabalhador_cpfTrab(consulta.getString("trabalhador_cpfTrab"));
                s2200.setTrabalhador_nisTrab(consulta.getString("trabalhador_nisTrab"));
                s2200.setTrabalhador_nmTrab(consulta.getString("trabalhador_nmTrab"));
                s2200.setTrabalhador_sexo(consulta.getString("trabalhador_sexo"));
                s2200.setTrabalhador_racaCor(consulta.getString("trabalhador_racaCor"));
                s2200.setTrabalhador_estCiv(consulta.getString("trabalhador_estCiv"));
                s2200.setTrabalhador_grauInstr(consulta.getString("trabalhador_grauInstr"));
                s2200.setTrabalhador_indPriEmpr(consulta.getString("trabalhador_indPriEmpr"));
                s2200.setNascimento_dtNascto(consulta.getString("nascimento_dtNascto"));
                s2200.setNascimento_codMunic(consulta.getString("nascimento_codMunic"));
                s2200.setNascimento_uf(consulta.getString("nascimento_uf"));
                s2200.setNascimento_paisNascto(consulta.getString("Nacionalid"));
                s2200.setNascimento_paisNac("105");
                s2200.setNascimento_nmMae(consulta.getString("nascimento_nmMae"));
                s2200.setNascimento_nmPai(consulta.getString("nascimento_nmPai"));
                s2200.setCTPS_nrCtps(consulta.getString("CTPS_nrCtps"));
                s2200.setCTPS_serieCtps(consulta.getString("CTPS_serieCtps"));
                s2200.setCTPS_ufCtps(consulta.getString("CTPS_ufCtps"));
                s2200.setRG_nrRg(consulta.getString("RG_nrRg"));
                s2200.setRG_orgaoEmissor(consulta.getString("RG_orgaoEmissor"));
                s2200.setRG_dtExped(consulta.getString("RG_dtExped"));
                s2200.setCNH_nrRegCnh(consulta.getString("CNH_nrRegCnh"));
                s2200.setCNH_ufCnh(consulta.getString("CNH_ufCnh"));
                s2200.setCNH_dtValid(consulta.getString("CNH_dtValid"));
                s2200.setCNH_categoriaCnh(consulta.getString("CNH_categoriaCnh"));
                s2200.setBrasil_tpLograd("");
                s2200.setBrasil_dscLograd(consulta.getString("brasil_dscLograd"));
                s2200.setBrasil_nrLograd(consulta.getString("brasil_nrLograd"));
                s2200.setBrasil_complemento(consulta.getString("brasil_complemento"));
                s2200.setBrasil_bairro(consulta.getString("brasil_bairro"));
                s2200.setBrasil_cep(consulta.getString("brasil_cep"));
                s2200.setBrasil_codMunic(consulta.getString("Brasil_codMunic"));
                s2200.setBrasil_uf(consulta.getString("brasil_uf"));
                
                s2200.setInfoDeficiencia_defFisica("N");
                s2200.setInfoDeficiencia_defVisual("N");
                s2200.setInfoDeficiencia_defAuditiva("N");
                s2200.setInfoDeficiencia_defMental("N");
                s2200.setInfoDeficiencia_defIntelectual("N");
                s2200.setInfoDeficiencia_reabReadap("N");
                s2200.setInfoDeficiencia_infoCota("N");
                s2200.setInfoDeficiencia_observacao("N");                                
                
                if (consulta.getString("InfoDeficiencia_defFisica").equals("S")) {
                    s2200.setInfoDeficiencia_defFisica(consulta.getString("InfoDeficiencia_defFisica"));
                    if (consulta.getString("DefFisTipo").equals("1")) {
                        s2200.setInfoDeficiencia_defFisica("S");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("S");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("2")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("S");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("3")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("S");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("S");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("4")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("S");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("5")) {
                        s2200.setInfoDeficiencia_defFisica("S");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("6")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("S");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("7")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("S");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    }
                } else {
                    s2200.setInfoDeficiencia_defFisica("N");
                    s2200.setInfoDeficiencia_defVisual("N");;
                    s2200.setInfoDeficiencia_defAuditiva("N");
                    s2200.setInfoDeficiencia_defMental("N");
                    s2200.setInfoDeficiencia_defIntelectual("N");
                    s2200.setInfoDeficiencia_reabReadap("N");
                    s2200.setInfoDeficiencia_infoCota("N");
                    s2200.setInfoDeficiencia_observacao("N");
                }

                s2200.setContato_fonePrinc(consulta.getString("contato_fonePrinc"));
                s2200.setContato_foneAlternat(consulta.getString("contato_foneAlternat"));
                s2200.setContato_emailPrinc(consulta.getString("contato_emailPrinc"));
                s2200.setVinculo_matricula(consulta.getString("vinculo_matricula"));
                s2200.setVinculo_tpRegTrab("1");
                s2200.setVinculo_tpRegPrev("1");
                s2200.setVinculo_nrRecInfPrelim(consulta.getString("vinculo_nrRecInfPrelim"));
                s2200.setVinculo_cadIni("");
                s2200.setInfoCeletista_dtAdm(consulta.getString("infoCeletista_dtAdm"));
                s2200.setInfoCeletista_tpAdmissao(consulta.getString("infoCeletista_tpAdmissao"));
                s2200.setInfoCeletista_indAdmissao("1");
                s2200.setInfoCeletista_tpRegJor("1");
                s2200.setInfoCeletista_natAtividade("1");
                s2200.setInfoCeletista_cnpjSindCategProf(consulta.getString("infoCeletista_cnpjSindCategProf"));
                s2200.setFGTS_opcFGTS("1");
                s2200.setCBO(consulta.getString("CBO"));
                s2200.setFGTS_dtOpcFGTS(consulta.getString("infoCeletista_dtAdm")); // FGTS_dtOpcFGTS
                s2200.setInfoContrato_nmCargo(consulta.getString("infoContrato_nmCargo"));
                s2200.setInfoContrato_codCateg(consulta.getString("infoContrato_codCateg"));
                s2200.setRemuneracao_vrSalFx(consulta.getString("remuneracao_vrSalFx"));
                s2200.setRemuneracao_undSalFixo(consulta.getString("remuneracao_undSalFixo"));

                //s2200.setDuracao_tpContr("duracao_dtTerm");
                s2200.setLocalTrabGeral_tpInsc("1");
                s2200.setLocalTrabGeral_nrInsc(consulta.getString("localTrabGeral_nrInsc"));

                s2200.setTrabEstrangeiro_casadoBr(consulta.getString("EstCasadoBr"));
                s2200.setTrabEstrangeiro_classTrabEstrang(consulta.getString("EstClassTrab"));
                s2200.setTrabEstrangeiro_dtChegada(consulta.getString("EstDtChegada"));
                s2200.setTrabEstrangeiro_filhosBr(consulta.getString("EstFilhosBr"));

                s2200.setHorarios(new ArrayList<>());
                Horario horario;
                if (consulta.getString("D1").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("1");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D2").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("2");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D3").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("3");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D4").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("4");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D5").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("5");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D6").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("6");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D7").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("7");
                    s2200.getHorarios().add(horario);
                }

                s2200.setDependentes(new ArrayList<>());
                s2200.setHorContratual_qtdHrsSem(consulta.getString("tipocomp").equals("S") ? consulta.getString("chcomp")
                        : (new BigDecimal(consulta.getString("chcomp")).compareTo(new BigDecimal("180")) == 1 ? "44"
                        : (new BigDecimal(consulta.getString("chcomp")).compareTo(new BigDecimal("180")) == 0 ? "30"
                        : (new BigDecimal(consulta.getString("chcomp")).divide(new BigDecimal("5")).toPlainString()))));
                s2200.setHorContratual_tpJornada(
                        ((consulta.getString("DiasTrbDiu").equals("1")
                        || consulta.getString("DiasTrbNot").equals("1"))
                        && consulta.getString("DiasFolga").equals("1")) ? "2" : "3");
                s2200.setHorContratual_tmpParc("0");
                s2200.setFiliacaoSindical_cnpjSindTrab(consulta.getString("PgCtSin"));
                s2200.setDesligamento_dtDesligamento(consulta.getString("situacao").equals("D") ? consulta.getString("desligamento_dtDesligamento") : "");
                listaS2200.add(s2200);
            }
            consulta.Close();
            return listaS2200;
        } catch (Exception e) {
            throw new Exception("S2200Dao.get - " + e.getMessage() + "\r\n"
                    + " Select funcion.PgCtSin ,RHHorario.tipocomp, RHHorario.chcomp, Funcion.CPF trabalhador_cpfTrab, "
                    + " Funcion.PIS trabalhador_nisTrab, Funcion.Nome trabalhador_nmTrab, "
                    + " Funcion.Sexo trabalhador_sexo, Funcion.Raca trabalhador_racaCor,  Funcion.EstCivil trabalhador_estCiv, "
                    + " Funcion.Instrucao trabalhador_grauInstr, Funcion.TipoAdm trabalhador_indPriEmpr, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto, "
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie CTPS_serieCtps, "
                    + " Funcion.CTPS_UF CTPS_ufCtps, Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor, "
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped, "
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid, "
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd, "
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, Municipios.CodIBGE brasil_codMunic, "
                    + " Funcion.UF brasil_uf, Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, Funcion.Email contato_emailPrinc, "
                    + " Convert(BigInt, Funcion.Matr) vinculo_matricula, substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoCeletista_dtAdm, "
                    + " Fornec.CNPJ infoCeletista_cnpjSindCategProf, Funcion.Salario remuneracao_vrSalFx,  Funcion.FormaPgto remuneracao_undSalFixo, "
                    + " Filiais.CNPJ localTrabGeral_nrInsc, convert(bigint, Funcion.Cargo) infoContrato_nmCargo,  RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, "
                    + " RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, RHEscala.HrSabNot, RHEscala.HrDomDiu, "
                    + " RHEscala.HrDomNot, Convert(varchar,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, RHHorario.D1, "
                    + " RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, Funcion.Situacao, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Demis,111),'/','-'),0,11) desligamento_dtDesligamento, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc,"
                    + " Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                       (Select Max(CodIBGE) From Municipios where Nome = Substring(Naturalid,1,(CharIndex('/',Naturalid)-1)) "
                    + "                                                         and UF = Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) and Len(Naturalid) > 0) "
                    + "                    else '0' end nascimento_codMunic, "
                    + "                    Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                    (Select Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) UF from Funcion where Matr = FPMensal.Matr) "
                    + " else '0' end nascimento_uf, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "         and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso "
                    + " from FPMensal "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr "
                    + " Left Join Cargos  on Cargos.Codigo = Funcion.CodCargo "
                    + " Left Join Sindicatos  on Sindicatos.Codigo = FPMensal.Sindicato "
                    + " Left Join PstServ  on PstServ.Secao  = FPMensal.Secao "
                    + "                   and PstServ.CodFil = FPMensal.CodFil "
                    + " Left Join Clientes  on Clientes.Codigo = PstServ.CodCli "
                    + "                    and Clientes.CodFil = PstServ.CodFil "
                    + " Left Join RHEscala  on RHEscala.Codigo = FPMEnsal.Escala "
                    + "                    and RHEscala.CodFil = FPMEnsal.CodFil "
                    + " Left Join RHHorario  on  RHHorario.Codigo    = FPMensal.Horario "
                    + "                      and RHHorario.CodFil    = FPMensal.CodFil "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade "
                    + "                     and Municipios.UF   = Funcion.UF"
                    + " Left Join Fornec on Fornec.Codigo = Sindicatos.CodForn"
                    + " where TipoFP = 'MEN' "
                    + " and Funcion.codfil = " + codFil
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtInicio = " + compet + "-01) "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ");
        }
    }

    public List<S2200> getSimples(String codFil, String compet, String ambiente, String cadIni, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select CONVERT(BigInt, Funcion.Matr) vinculo_matricula, Funcion.Nome trabalhador_nmTrab, Funcion.CPF trabalhador_cpfTrab, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso "
                    + " from FPMensal "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " where TipoFP = 'MEN' "
                    + " and Funcion.codfil = ? "
                    + " and FPMensal.situacao <> 'D' "
                    //+ " and '20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPMensal.CodMovFP),3,2) = ? ";
                    + " AND Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','')";

            //+ " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
            //+ "                             from FPPeriodos where DtFecha <= Convert(Date,Getdate())) "
            //+ "                             from FPPeriodos where DtInicio = ?) ";
            if (cadIni.equals("S")) {
                sql += " and Substring(Replace(Convert(Varchar, Funcion.Dt_Admis, 111),'/','-'),1,7) <= ?";
            } else {
                sql += " and ((Substring(Replace(Convert(Varchar, Funcion.Dt_Admis, 111),'/','-'),1,7) = ?) "
                        + "       or (funcion.Matr > (Select Max(z.Matr) from Funcion z where Substring(Replace(Convert(Varchar, z.Dt_Admis, 111),'/','-'),1,7) = Substring(Replace(Convert(Varchar, DateAdd(Day, -1, Convert(Date,?)), 111),'/','-'),1,7) and z.CodFil = ? and z.TipoADM <> '70') )) ";
            }

            sql += " ORDER BY sucesso asc, Funcion.Matr asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            //consulta.setString(compet+"-01");
            consulta.setString(compet);
            if (cadIni.equals("N")) {
                consulta.setString(compet + "-01");
            }
            if (cadIni.equals("N")) {
                consulta.setString(codFil);
            }
            consulta.select();
            List<S2200> listaS2200 = new ArrayList<>();
            S2200 s2200;
            while (consulta.Proximo()) {
                s2200 = new S2200();
                s2200.setSucesso(consulta.getInt("sucesso"));
                s2200.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2200.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2200.setTrabalhador_cpfTrab(consulta.getString("trabalhador_cpfTrab"));
                s2200.setTrabalhador_nmTrab(consulta.getString("trabalhador_nmTrab"));
                s2200.setVinculo_matricula(consulta.getString("vinculo_matricula"));
                listaS2200.add(s2200);
            }
            consulta.Close();
            return listaS2200;
        } catch (Exception e) {
            throw new Exception("S2200Dao.getSimples - " + e.getMessage() + "\r\n"
                    + " Select Convert(BigInt,Funcion.Matr) vinculo_matricula, Funcion.Nome trabalhador_nmTrab, Funcion.CPF trabalhador_cpfTrab, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "         and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso "
                    + " from FPMensal "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " where TipoFP = 'MEN' "
                    + " and Funcion.codfil = " + codFil
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtInicio = " + compet + "-01) "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ");
        }
    }

    public List<S2200> getCompleta(List<S2200> funcions, String codFil, String compet, String ambiente, String tipo, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select (Select top 1 substring(xml_retorno,\n"
                    + "                        charindex('<nrRecibo>',xml_retorno)+10, \n"
                    + "                        charindex('</nrRecibo>',xml_retorno) - charindex('<nrRecibo>',xml_retorno)-10) "
                    + "                             vinculo_nrRecInfPrelim \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Convert(BigInt,Funcion.CPF) \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = ? \n"
                    + "                                 and z.Compet = ? \n"
                    + "                                 and z.Ambiente = ? \n"
                    + "                                 and len(Xml_Retorno) > 0 \n"
                    + "                                 and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') "
                    + " Order by Sequencia desc) vinculo_nrRecInfPrelim,"
                    + " (Select top 01 substring(xml_retorno,  charindex('<nrRecibo>',xml_retorno)+10, "
                    + "    charindex('</nrRecibo>',xml_retorno) - charindex('<nrRecibo>',xml_retorno)-10) ideEvento_nrRecibo "
                    + "    From XmleSocial z "
                    + "    where z.Identificador = Funcion.matr "
                    + "                     and z.evento = 'S-2200'"
                    + "                     and z.CodFil = ? "
                    //+ "                     and z.Compet = ? "
                    + "                     and z.Ambiente = ? "
                    + "                     and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') "
                    + " Order by Sequencia Desc) ideEvento_nrRecibo, "
                    + " funcion.PgCtSin ,RHHorario.tipocomp, RHHorario.chcomp, Funcion.CPF trabalhador_cpfTrab, "
                    + " Funcion.PIS trabalhador_nisTrab, Funcion.Nome trabalhador_nmTrab, Funcion.Nacionalid,  "
                    + " Funcion.Sexo trabalhador_sexo, Funcion.Raca trabalhador_racaCor,  Funcion.EstCivil trabalhador_estCiv, "
                    + " Funcion.Instrucao trabalhador_grauInstr, Funcion.TipoAdm trabalhador_indPriEmpr, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto, "
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie CTPS_serieCtps, "
                    + " Funcion.CTPS_UF CTPS_ufCtps, Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor, "
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped, "
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid, "
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd, "
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, Municipios.CodIBGE brasil_codMunic, "
                    + " Funcion.UF brasil_uf, Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, Funcion.Email contato_emailPrinc, Funcion.DefFis InfoDeficiencia_defFisica, " 
                    + " Funcion.DefFisTipo, Month(Sindicatos.Dt_CCT) infoCeletista_dtBase, "
                    + " RHHorario.Descricao + ' ' + "
                    + " Case when RHHorario.D1 >0 then 'DOM '+RHHorario.Hora101+' '+RHHorario.Hora102+' '+RHHorario.Hora103+' '+RHHorario.Hora104+' ' else '' end +"
                    + " Case when RHHorario.D2 >0 then 'SEG '+RHHorario.Hora201+' '+RHHorario.Hora202+' '+RHHorario.Hora203+' '+RHHorario.Hora204+' ' else '' end +"
                    + " Case when RHHorario.D3 >0 then 'TER '+RHHorario.Hora301+' '+RHHorario.Hora302+' '+RHHorario.Hora303+' '+RHHorario.Hora304+' ' else '' end +"
                    + " Case when RHHorario.D4 >0 then 'QUA '+RHHorario.Hora401+' '+RHHorario.Hora402+' '+RHHorario.Hora403+' '+RHHorario.Hora404+' ' else '' end +"
                    + " Case when RHHorario.D5 >0 then 'QUI '+RHHorario.Hora501+' '+RHHorario.Hora502+' '+RHHorario.Hora503+' '+RHHorario.Hora504+' ' else '' end +"
                    + " Case when RHHorario.D6 >0 then 'SEX '+RHHorario.Hora601+' '+RHHorario.Hora602+' '+RHHorario.Hora603+' '+RHHorario.Hora604+' ' else '' end +"
                    + " Case when RHHorario.D7 >0 then 'SAB '+RHHorario.Hora701+' '+RHHorario.Hora702+' '+RHHorario.Hora703+' '+RHHorario.Hora704+' ' else '' end dsrJorn, "
                    + " Convert(BigInt, Funcion.Matr) vinculo_matricula, substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoCeletista_dtAdm, "
                    + " Fornec.CNPJ infoCeletista_cnpjSindCategProf, Funcion.Salario remuneracao_vrSalFx,  Funcion.FormaPgto remuneracao_undSalFixo, "
                    + " Filiais.CNPJ localTrabGeral_nrInsc, Cargos.Descricao infoContrato_nmCargo,  Cargos.CBO, RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, "
                    + " RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, RHEscala.HrSabNot, RHEscala.HrDomDiu, "
                    + " RHEscala.HrDomNot, Convert(bigint,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, RHHorario.D1, "
                    + " RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, Funcion.Situacao, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Demis,111),'/','-'),0,11) desligamento_dtDesligamento, "
                    + " Case when Funcion.TipoADM = 70 then 2 else 1 end infoCeletista_tpAdmissao,  "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc, FuncionAdic.DiasExperiencia, "
                    + " FuncionAdic.EstClassTrab, FuncionAdic.EstCasadoBr, FuncionAdic.EstFilhosBr, substring(replace(convert(varchar,FuncionAdic.EstDtChegada,111),'/','-'),0,11) EstDtChegada, "
                    + " substring(replace(convert(varchar,FuncionAdic.Dt_TerminoExp1,111),'/','-'),0,11) duracao_dtTerm, FuncionAdic.DiasExperiencia,  "
                    + " Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                       (Select Max(CodIBGE) From Municipios where Nome = Substring(Naturalid,1,(CharIndex('/',Naturalid)-1)) "
                    + "                                                         and UF = Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) and Len(Naturalid) > 0) "
                    + "                    else '0' end nascimento_codMunic, "
                    + "                    Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                    (Select Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) UF from Funcion where Matr = FPMensal.Matr) "
                    + " else '0' end nascimento_uf, "
                    + " Case when Funcion.vinculo in ('J','M') then '103' "
                    + "      when Funcion.vinculo = 'D' then '721' "
                    + "      when Funcion.Vinculo = 'S' then '723' "
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + "      when Funcion.TipoADM = 25 then 105 "
                    + "      when Funcion.TrabIntermitente = 'S' then '111' "
                    + "      else '101' end infoContrato_codCateg, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso, FuncionAdic.DiasExperiencia,  "
                    + " substring(replace(convert(varchar,FuncionAdic.Dt_TerminoExp1,111),'/','-'),0,11) duracao_dtTerm "
                    + " from FPMensal "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr "
                    + " Left Join Cargos  on Cargos.Codigo = Funcion.CodCargo "
                    + " Left Join Sindicatos  on Sindicatos.Codigo = FPMensal.Sindicato "
                    + " Left Join PstServ  on PstServ.Secao  = FPMensal.Secao "
                    + "                   and PstServ.CodFil = FPMensal.CodFil "
                    + " Left Join Clientes  on Clientes.Codigo = PstServ.CodCli "
                    + "                    and Clientes.CodFil = PstServ.CodFil "
                    + " Left Join RHEscala  on RHEscala.Codigo = FPMEnsal.Escala "
                    + "                    and RHEscala.CodFil = FPMEnsal.CodFil "
                    + " Left Join RHHorario  on  RHHorario.Codigo    = FPMensal.Horario "
                    + "                      and RHHorario.CodFil    = FPMensal.CodFil "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade "
                    + "                     and Municipios.UF   = Funcion.UF"
                    + " Left Join Fornec on Fornec.Codigo = Sindicatos.CodForn"
                    + " Left Join FuncionAdic  on FuncionAdic.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' "
                    + " and Funcion.codfil = ? ";
            if (funcions.size() == 1) {
                sql += " and Funcion.matr = ? ";
            } else {
                sql += " and (Funcion.matr = ? ";
                for (int i = 1; i < funcions.size(); i++) {
                    sql += " OR Funcion.matr = ? ";
                }
                sql += ")";
            }
            sql += " and FPMensal.situacao <> 'D' "
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtInicio = ?) "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            //consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            for (S2200 funcion : funcions) {
                consulta.setString(funcion.getVinculo_matricula());
            }
            consulta.setString(compet + "-01");
            consulta.select();
            List<S2200> listaS2200 = new ArrayList<>();
            S2200 s2200;
            while (consulta.Proximo()) {
                s2200 = new S2200();
                s2200.setSucesso(consulta.getInt("sucesso"));
                s2200.setIdeEvento_indRetif(tipo);
                s2200.setIdeEvento_nrRecibo(tipo.equals("2") ? consulta.getString("ideEvento_nrRecibo") : "");
                s2200.setIdeEvento_procEmi("1");
                s2200.setIdeEvento_verProc("Satellite eSocial");
                s2200.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2200.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2200.setTrabalhador_cpfTrab(consulta.getString("trabalhador_cpfTrab"));
                s2200.setTrabalhador_nisTrab(consulta.getString("trabalhador_nisTrab"));
                s2200.setTrabalhador_nmTrab(consulta.getString("trabalhador_nmTrab"));
                s2200.setTrabalhador_sexo(consulta.getString("trabalhador_sexo"));
                s2200.setTrabalhador_racaCor(consulta.getString("trabalhador_racaCor"));
                s2200.setTrabalhador_estCiv(consulta.getString("trabalhador_estCiv"));
                s2200.setTrabalhador_grauInstr(consulta.getString("trabalhador_grauInstr"));
                s2200.setTrabalhador_indPriEmpr(consulta.getString("trabalhador_indPriEmpr"));
                s2200.setNascimento_dtNascto(consulta.getString("nascimento_dtNascto"));
                s2200.setNascimento_codMunic(consulta.getString("nascimento_codMunic"));
                s2200.setNascimento_uf(consulta.getString("nascimento_uf"));
                s2200.setNascimento_paisNascto(consulta.getString("Nacionalid"));
                s2200.setNascimento_paisNac("105");
                s2200.setNascimento_nmMae(consulta.getString("nascimento_nmMae"));
                s2200.setNascimento_nmPai(consulta.getString("nascimento_nmPai"));
                s2200.setCTPS_nrCtps(consulta.getString("CTPS_nrCtps"));
                s2200.setCTPS_serieCtps(consulta.getString("CTPS_serieCtps"));
                s2200.setCTPS_ufCtps(consulta.getString("CTPS_ufCtps"));
                s2200.setRG_nrRg(consulta.getString("RG_nrRg"));
                s2200.setRG_orgaoEmissor(consulta.getString("RG_orgaoEmissor"));
                s2200.setRG_dtExped(consulta.getString("RG_dtExped"));
                s2200.setCNH_nrRegCnh(consulta.getString("CNH_nrRegCnh"));
                s2200.setCNH_ufCnh(consulta.getString("CNH_ufCnh"));
                s2200.setCNH_dtValid(consulta.getString("CNH_dtValid"));
                s2200.setCNH_categoriaCnh(consulta.getString("CNH_categoriaCnh"));
                s2200.setBrasil_tpLograd("");
                s2200.setBrasil_dscLograd(consulta.getString("brasil_dscLograd"));
                s2200.setBrasil_nrLograd(consulta.getString("brasil_nrLograd"));
                s2200.setBrasil_complemento(consulta.getString("brasil_complemento"));
                s2200.setBrasil_bairro(consulta.getString("brasil_bairro"));
                s2200.setBrasil_cep(consulta.getString("brasil_cep"));
                s2200.setBrasil_codMunic(consulta.getString("Brasil_codMunic"));
                s2200.setBrasil_uf(consulta.getString("brasil_uf"));

// trata defciente Carlos 11/07/2022
                s2200.setInfoDeficiencia_defFisica("N");
                s2200.setInfoDeficiencia_defVisual("N");
                s2200.setInfoDeficiencia_defAuditiva("N");
                s2200.setInfoDeficiencia_defMental("N");
                s2200.setInfoDeficiencia_defIntelectual("N");
                s2200.setInfoDeficiencia_reabReadap("N");
                s2200.setInfoDeficiencia_infoCota("N");
                s2200.setInfoDeficiencia_observacao("N");                                
                
                if (consulta.getString("InfoDeficiencia_defFisica").equals("S")) {
                    s2200.setInfoDeficiencia_defFisica(consulta.getString("InfoDeficiencia_defFisica"));
                    if (consulta.getString("DefFisTipo").equals("1")) {
                        s2200.setInfoDeficiencia_defFisica("S");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("2")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("S");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("3")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("S");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("4")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("S");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("5")) {
                        s2200.setInfoDeficiencia_defFisica("S");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("6")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("N");
                        s2200.setInfoDeficiencia_reabReadap("S");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    } else if (consulta.getString("InfoDeficiencia_defFisica").equals("7")) {
                        s2200.setInfoDeficiencia_defFisica("N");
                        s2200.setInfoDeficiencia_defVisual("N");
                        s2200.setInfoDeficiencia_defAuditiva("N");
                        s2200.setInfoDeficiencia_defMental("N");
                        s2200.setInfoDeficiencia_defIntelectual("S");
                        s2200.setInfoDeficiencia_reabReadap("N");
                        s2200.setInfoDeficiencia_infoCota("N");
                        s2200.setInfoDeficiencia_observacao("N");
                    }
                } else {
                    s2200.setInfoDeficiencia_defFisica("N");
                    s2200.setInfoDeficiencia_defVisual("N");;
                    s2200.setInfoDeficiencia_defAuditiva("N");
                    s2200.setInfoDeficiencia_defMental("N");
                    s2200.setInfoDeficiencia_defIntelectual("N");
                    s2200.setInfoDeficiencia_reabReadap("N");
                    s2200.setInfoDeficiencia_infoCota("N");
                    s2200.setInfoDeficiencia_observacao("N");
                }
                


                s2200.setContato_fonePrinc(consulta.getString("contato_fonePrinc"));
                s2200.setContato_foneAlternat(consulta.getString("contato_foneAlternat"));
                s2200.setContato_emailPrinc(consulta.getString("contato_emailPrinc"));
                s2200.setVinculo_matricula(consulta.getString("vinculo_matricula"));
                s2200.setVinculo_tpRegTrab("1");
                s2200.setVinculo_tpRegPrev("1");
                s2200.setVinculo_nrRecInfPrelim(consulta.getString("vinculo_nrRecInfPrelim"));
                s2200.setVinculo_cadIni("");
                s2200.setInfoCeletista_dtAdm(consulta.getString("infoCeletista_dtAdm"));
                s2200.setInfoCeletista_tpAdmissao(consulta.getString("infoCeletista_tpAdmissao"));
                s2200.setInfoCeletista_dtBase(consulta.getString("infoCeletista_dtBase"));
                s2200.setInfoCeletista_indAdmissao("1");
                s2200.setInfoCeletista_tpRegJor("1");
                s2200.setInfoCeletista_natAtividade("1");
                s2200.setInfoCeletista_cnpjSindCategProf(consulta.getString("infoCeletista_cnpjSindCategProf"));
                s2200.setFGTS_opcFGTS("1");
                s2200.setCBO(consulta.getString("CBO"));
                s2200.setFGTS_dtOpcFGTS(consulta.getString("infoCeletista_dtAdm")); // FGTS_dtOpcFGTS
                s2200.setInfoContrato_nmCargo(consulta.getString("infoContrato_nmCargo"));
                s2200.setInfoContrato_codCateg(consulta.getString("infoContrato_codCateg"));
                s2200.setRemuneracao_vrSalFx(consulta.getString("remuneracao_vrSalFx"));
                s2200.setRemuneracao_undSalFixo(consulta.getString("remuneracao_undSalFixo"));
                if ((!consulta.getString("DiasExperiencia").toString().equals("45")) && 
                    (consulta.getString("DiasExperiencia").toString().equals("00")) &&
                    (!consulta.getString("DiasExperiencia").toString().equals("90")) &&
                    (!consulta.getString("DiasExperiencia").toString().equals("30"))){
                   s2200.setDuracao_tpContr("1");
                   s2200.setDuracao_dtTerm("");
                }else{
                   s2200.setDuracao_tpContr("2");                    
                   s2200.setDuracao_dtTerm(consulta.getString("duracao_dtTerm"));
                }
                s2200.setLocalTrabGeral_tpInsc("1");
                s2200.setLocalTrabGeral_nrInsc(consulta.getString("localTrabGeral_nrInsc"));

                s2200.setTrabEstrangeiro_casadoBr(consulta.getString("EstCasadoBr"));
                s2200.setTrabEstrangeiro_classTrabEstrang(consulta.getString("EstClassTrab"));
                s2200.setTrabEstrangeiro_dtChegada(consulta.getString("EstDtChegada"));
                s2200.setTrabEstrangeiro_filhosBr(consulta.getString("EstFilhosBr"));

//                s2200.setHorarios(new ArrayList<>());
//                Horario horario;
//                if (consulta.getString("D1").equals("1")) {
//                    horario = new Horario();
//                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
//                    horario.setHorario_dia("1");
//                    s2200.getHorarios().add(horario);
//                }
//                if (consulta.getString("D2").equals("1")) {
//                    horario = new Horario();
//                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
//                    horario.setHorario_dia("2");
//                    s2200.getHorarios().add(horario);
//                }
//                if (consulta.getString("D3").equals("1")) {
//                    horario = new Horario();
//                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
//                    horario.setHorario_dia("3");
//                    s2200.getHorarios().add(horario);
//                }
//                if (consulta.getString("D4").equals("1")) {
//                    horario = new Horario();
//                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
//                    horario.setHorario_dia("4");
//                    s2200.getHorarios().add(horario);
//                }
//                if (consulta.getString("D5").equals("1")) {
//                    horario = new Horario();
//                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
//                    horario.setHorario_dia("5");
//                    s2200.getHorarios().add(horario);
//                }
//                if (consulta.getString("D6").equals("1")) {
//                    horario = new Horario();
//                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
//                    horario.setHorario_dia("6");
//                    s2200.getHorarios().add(horario);
//                }
//                if (consulta.getString("D7").equals("1")) {
//                    horario = new Horario();
//                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
//                    horario.setHorario_dia("7");
//                    s2200.getHorarios().add(horario);
//                }

                s2200.setDependentes(new ArrayList<>());

                String valorTipoComp = consulta.getString("tipocomp");

                s2200.setHorContratual_qtdHrsSem(consulta.getString("tipocomp").equals("S") ? consulta.getString("chcomp")
                        : new BigDecimal(consulta.getString("chcomp")).compareTo(new BigDecimal("180")) == 1 ? "44"
                        : new BigDecimal(consulta.getString("chcomp")).compareTo(new BigDecimal("180")) == 0 ? "30"
                        : new BigDecimal(consulta.getString("chcomp")).divide(new BigDecimal("5")).toPlainString());
                s2200.setHorContratual_tpJornada(
                        ((consulta.getString("DiasTrbDiu").equals("1")
                        || consulta.getString("DiasTrbNot").equals("1"))
                        && consulta.getString("DiasFolga").equals("1")) ? "2" : "3");
                s2200.setHorContratual_tmpParc("0");
                s2200.setHorContratual_dscJorn(consulta.getString("dsrJorn"));
                s2200.setFiliacaoSindical_cnpjSindTrab(consulta.getString("PgCtSin"));
                s2200.setDesligamento_dtDesligamento(consulta.getString("situacao").equals("D") ? consulta.getString("desligamento_dtDesligamento") : "");
                listaS2200.add(s2200);
            }
            consulta.Close();
            return listaS2200;
        } catch (Exception e) {
            throw new Exception("S2200Dao.get - " + e.getMessage() + "\r\n"
                    + " Select funcion.PgCtSin ,RHHorario.tipocomp, RHHorario.chcomp, Funcion.CPF trabalhador_cpfTrab, "
                    + " Funcion.PIS trabalhador_nisTrab, Funcion.Nome trabalhador_nmTrab, "
                    + " Funcion.Sexo trabalhador_sexo, Funcion.Raca trabalhador_racaCor,  Funcion.EstCivil trabalhador_estCiv, "
                    + " Funcion.Instrucao trabalhador_grauInstr, Funcion.TipoAdm trabalhador_indPriEmpr, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto, "
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie CTPS_serieCtps, "
                    + " Funcion.CTPS_UF CTPS_ufCtps, Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor, "
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped, "
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid, "
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd, "
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, Municipios.CodIBGE brasil_codMunic, "
                    + " Funcion.UF brasil_uf, Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, Funcion.Email contato_emailPrinc, "
                    + " Convert(BigInt,Funcion.Matr) vinculo_matricula, substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoCeletista_dtAdm, "
                    + " Fornec.CNPJ infoCeletista_cnpjSindCategProf, Funcion.Salario remuneracao_vrSalFx,  Funcion.FormaPgto remuneracao_undSalFixo, "
                    + " Filiais.CNPJ localTrabGeral_nrInsc, Cargos.Descricao infoContrato_nmCargo, Cargos.CBO,  RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, "
                    + " RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, RHEscala.HrSabNot, RHEscala.HrDomDiu, "
                    + " RHEscala.HrDomNot, Convert(varchar,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, RHHorario.D1, "
                    + " RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, Funcion.Situacao, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Demis,111),'/','-'),0,11) desligamento_dtDesligamento, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc,"
                    + " Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                       (Select Max(CodIBGE) From Municipios where Nome = Substring(Naturalid,1,(CharIndex('/',Naturalid)-1)) "
                    + "                                                         and UF = Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) and Len(Naturalid) > 0) "
                    + "                    else '0' end nascimento_codMunic, "
                    + "                    Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                    (Select Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) UF from Funcion where Matr = FPMensal.Matr) "
                    + " else '0' end nascimento_uf, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "         and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso "
                    + " from FPMensal "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr "
                    + " Left Join Cargos  on Cargos.Codigo = Funcion.CodCargo "
                    + " Left Join Sindicatos  on Sindicatos.Codigo = FPMensal.Sindicato "
                    + " Left Join PstServ  on PstServ.Secao  = FPMensal.Secao "
                    + "                   and PstServ.CodFil = FPMensal.CodFil "
                    + " Left Join Clientes  on Clientes.Codigo = PstServ.CodCli "
                    + "                    and Clientes.CodFil = PstServ.CodFil "
                    + " Left Join RHEscala  on RHEscala.Codigo = FPMEnsal.Escala "
                    + "                    and RHEscala.CodFil = FPMEnsal.CodFil "
                    + " Left Join RHHorario  on  RHHorario.Codigo    = FPMensal.Horario "
                    + "                      and RHHorario.CodFil    = FPMensal.CodFil "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade "
                    + "                     and Municipios.UF   = Funcion.UF"
                    + " Left Join Fornec on Fornec.Codigo = Sindicatos.CodForn"
                    + " where TipoFP = 'MEN' "
                    + " and Funcion.codfil = " + codFil
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtInicio = " + compet + "-01) "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ");
        }
    }

    public List<S2200> getCompleta2(List<S2200> funcions, String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select (Select top 1 substring(xml_retorno,\n"
                    + "                        charindex('<nrRecibo>',xml_retorno)+10, \n"
                    + "                        charindex('</nrRecibo>',xml_retorno) - charindex('<nrRecibo>',xml_retorno)-10) "
                    + "                             vinculo_nrRecInfPrelim \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Convert(BigInt,Funcion.CPF) \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = ? \n"
                    + "                                 and z.Compet = ? \n"
                    + "                                 and z.Ambiente = ? \n"
                    + "                                 and len(Xml_Retorno) > 0 \n"
                    + "                                 and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') "
                    + " Order by Sequencia desc) vinculo_nrRecInfPrelim,"
                    + " funcion.PgCtSin ,RHHorario.tipocomp, RHHorario.chcomp, Funcion.CPF trabalhador_cpfTrab,  "
                    + " Funcion.PIS trabalhador_nisTrab, Funcion.Nome trabalhador_nmTrab,Funcion.Nacionalid,  "
                    + " Funcion.Sexo trabalhador_sexo, Funcion.Raca trabalhador_racaCor,  Funcion.EstCivil trabalhador_estCiv,  "
                    + " Funcion.Instrucao trabalhador_grauInstr, Funcion.TipoAdm trabalhador_indPriEmpr,  "
                    + " substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto,  "
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie CTPS_serieCtps,  "
                    + " Funcion.CTPS_UF CTPS_ufCtps, Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor,  "
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped,  "
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid,  "
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd,  "
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, Municipios.CodIBGE brasil_codMunic,  "
                    + " Funcion.UF brasil_uf, Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, Funcion.Email contato_emailPrinc,  "
                    + " Convert(BigInt, Funcion.Matr) vinculo_matricula, substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoCeletista_dtAdm,  "
                    + " Fornec.CNPJ infoCeletista_cnpjSindCategProf, Funcion.Salario remuneracao_vrSalFx,  Funcion.FormaPgto remuneracao_undSalFixo,  "
                    + " Clientes.CGC localTrabGeral_nrInsc, "
                    + " Cargos.Descricao infoContrato_nmCargo, Cargos.CBO, RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot,  "
                    + " RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, RHEscala.HrSabNot, RHEscala.HrDomDiu,  "
                    + " RHEscala.HrDomNot, Convert(varchar,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, RHHorario.D1,  "
                    + " RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, Funcion.Situacao,  "
                    + " substring(replace(convert(varchar,Funcion.Dt_Demis,111),'/','-'),0,11) desligamento_dtDesligamento,  "
                    + " Case when Funcion.TipoADM = 70 then 2 else 1 end infoCeletista_tpAdmissao,  "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc, FuncionAdic.DiasExperiencia, "
                    + " substring(replace(convert(varchar,FuncionAdic.Dt_TerminoExp1,111),'/','-'),0,11) duracao_dtTerm, "                    
                    + " FuncionAdic.CNPJAnt sucessaoVinc_cnpjEmpregAnt, FuncionAdic.MatrAnt sucessaoVinc_matricAnt, FuncionAdic.Dt_Transf sucessaoVinc_dtTransf, "
                    + " substring(replace(convert(varchar,FuncionAdic.Dt_TerminoExp1,111),'/','-'),0,11) duracao_dtTerm, "                    
                    + " FuncionAdic.EstClassTrab, FuncionAdic.EstCasadoBr, FuncionAdic.EstFilhosBr, substring(replace(convert(varchar,FuncionAdic.EstDtChegada,111),'/','-'),0,11) EstDtChegada, "
                    + " substring(replace(convert(varchar,FuncionAdic.Dt_TerminoExp1,111),'/','-'),0,11) duracao_dtTerm, "                    
                    + " Case when (CharIndex('/',Naturalid) > 1) then "
                    + "                     (Select Max(CodIBGE) From Municipios where Nome = Substring(Naturalid,1,(CharIndex('/',Naturalid)-1))  "
                    + "                                                         and UF = Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) and Len(Naturalid) > 0)  "
                    + "                 else '0' end nascimento_codMunic,  "
                    + "                 Case when (CharIndex('/',Naturalid) > 1) then "
                    + "                 (Select Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) UF from Funcion where Matr = FPMensal.Matr)  "
                    + " else '0' end nascimento_uf,  "
                    + " Case when Funcion.vinculo in ('J','M') then '103' "
                    + "      when Funcion.vinculo = 'D' then '721' "
                    + "      when Funcion.Vinculo = 'S' then '723' "
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + "      when Funcion.TipoADM = 25 then 105 "
                    + "      when Funcion.TrabIntermitente = 'S' then '111' "
                    + "      else '101' end infoContrato_codCateg, "
                    + " (select max(sucesso) from  (  "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr  "
                    + "             and z.evento = 'S-2200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%'  "
                    + "                     or z.Xml_Retorno = '' "
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = Funcion.Matr  "
                    + "             and z.evento = 'S-2200'  "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%'  "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = Funcion.Matr  "
                    + "             and z.evento = 'S-2200'  "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso  "
                    + " from FPMensal  "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr  "
                    + " Left Join Cargos  on Cargos.Codigo = Funcion.CodCargo "
                    + " Left Join Sindicatos  on Sindicatos.Codigo = FPMensal.Sindicato  "
                    + " Left Join PstServ  on PstServ.Secao  = FPMensal.Secao  "
                    + "                 and PstServ.CodFil = FPMensal.CodFil  "
                    + " Left Join Clientes  on Clientes.Codigo = PstServ.CodCli  "
                    + "                 and Clientes.CodFil = PstServ.CodFil  "
                    + " Left Join RHEscala  on RHEscala.Codigo = FPMEnsal.Escala  "
                    + "                 and RHEscala.CodFil = FPMEnsal.CodFil  "
                    + " Left Join RHHorario  on  RHHorario.Codigo    = FPMensal.Horario  "
                    + "                     and RHHorario.CodFil    = FPMensal.CodFil  "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil "
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade  "
                    + "                     and Municipios.UF   = Funcion.UF "
                    + " Left Join Fornec on Fornec.Codigo = Sindicatos.CodForn "
                    + " Left Join FuncionAdic  on FuncionAdic.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' ";

            if (funcions.size() == 1) {
                sql += " and Funcion.matr = ? ";
            } else {
                sql += " and (Funcion.matr = ? ";
                for (int i = 1; i < funcions.size(); i++) {
                    sql += " OR Funcion.matr = ? ";
                }
                sql += ")";
            }
            sql += " and FPMensal.situacao <> 'D' "
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtInicio = ?) "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            for (S2200 funcion : funcions) {
                consulta.setString(funcion.getVinculo_matricula());
            }
            consulta.setString(compet + "-01");
            consulta.select();
            List<S2200> listaS2200 = new ArrayList<>();
            S2200 s2200;
            while (consulta.Proximo()) {
                s2200 = new S2200();
                s2200.setIdeEvento_indRetif("1");
                s2200.setIdeEvento_procEmi("1");
                s2200.setIdeEvento_verProc("Satellite eSocial");
                s2200.setSucesso(consulta.getInt("sucesso"));
                s2200.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2200.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2200.setTrabalhador_cpfTrab(consulta.getString("trabalhador_cpfTrab"));
                s2200.setTrabalhador_nisTrab(consulta.getString("trabalhador_nisTrab"));
                s2200.setTrabalhador_nmTrab(consulta.getString("trabalhador_nmTrab"));
                s2200.setTrabalhador_sexo(consulta.getString("trabalhador_sexo"));
                s2200.setTrabalhador_racaCor(consulta.getString("trabalhador_racaCor"));
                s2200.setTrabalhador_estCiv(consulta.getString("trabalhador_estCiv"));
                s2200.setTrabalhador_grauInstr(consulta.getString("trabalhador_grauInstr"));
                s2200.setTrabalhador_indPriEmpr(consulta.getString("trabalhador_indPriEmpr"));
                s2200.setNascimento_dtNascto(consulta.getString("nascimento_dtNascto"));
                s2200.setNascimento_codMunic(consulta.getString("nascimento_codMunic"));
                s2200.setNascimento_uf(consulta.getString("nascimento_uf"));
                s2200.setNascimento_paisNascto(consulta.getString("Nacionalid"));
                s2200.setNascimento_paisNac("105");
                s2200.setNascimento_nmMae(consulta.getString("nascimento_nmMae"));
                s2200.setNascimento_nmPai(consulta.getString("nascimento_nmPai"));
                s2200.setCTPS_nrCtps(consulta.getString("CTPS_nrCtps"));
                s2200.setCTPS_serieCtps(consulta.getString("CTPS_serieCtps"));
                s2200.setCTPS_ufCtps(consulta.getString("CTPS_ufCtps"));
                s2200.setRG_nrRg(consulta.getString("RG_nrRg"));
                s2200.setRG_orgaoEmissor(consulta.getString("RG_orgaoEmissor"));
                s2200.setRG_dtExped(consulta.getString("RG_dtExped"));
                s2200.setCNH_nrRegCnh(consulta.getString("CNH_nrRegCnh"));
                s2200.setCNH_ufCnh(consulta.getString("CNH_ufCnh"));
                s2200.setCNH_dtValid(consulta.getString("CNH_dtValid"));
                s2200.setCNH_categoriaCnh(consulta.getString("CNH_categoriaCnh"));
                s2200.setBrasil_tpLograd("");
                s2200.setBrasil_dscLograd(consulta.getString("brasil_dscLograd"));
                s2200.setBrasil_nrLograd(consulta.getString("brasil_nrLograd"));
                s2200.setBrasil_complemento(consulta.getString("brasil_complemento"));
                s2200.setBrasil_bairro(consulta.getString("brasil_bairro"));
                s2200.setBrasil_cep(consulta.getString("brasil_cep"));
                s2200.setBrasil_codMunic(consulta.getString("Brasil_codMunic"));
                s2200.setBrasil_uf(consulta.getString("brasil_uf"));

                s2200.setContato_fonePrinc(consulta.getString("contato_fonePrinc"));
                s2200.setContato_foneAlternat(consulta.getString("contato_foneAlternat"));
                s2200.setContato_emailPrinc(consulta.getString("contato_emailPrinc"));
                s2200.setVinculo_matricula(consulta.getString("vinculo_matricula"));
                s2200.setVinculo_tpRegTrab("1");
                s2200.setVinculo_tpRegPrev("1");
                s2200.setVinculo_nrRecInfPrelim(consulta.getString("vinculo_nrRecInfPrelim"));
                s2200.setVinculo_cadIni("");
                s2200.setInfoCeletista_dtAdm(consulta.getString("infoCeletista_dtAdm"));
                s2200.setInfoCeletista_tpAdmissao(consulta.getString("infoCeletista_tpAdmissao"));
                s2200.setInfoCeletista_indAdmissao("1");
                s2200.setInfoCeletista_tpRegJor("1");
                s2200.setInfoCeletista_natAtividade("1");
                s2200.setInfoCeletista_cnpjSindCategProf(consulta.getString("infoCeletista_cnpjSindCategProf"));
                s2200.setFGTS_opcFGTS("1");
                s2200.setCBO(consulta.getString("CBO"));
                s2200.setFGTS_dtOpcFGTS(consulta.getString("infoCeletista_dtAdm")); // FGTS_dtOpcFGTS
                s2200.setInfoContrato_nmCargo(consulta.getString("infoContrato_nmCargo"));
                s2200.setInfoContrato_codCateg(consulta.getString("infoContrato_codCateg"));
                s2200.setRemuneracao_vrSalFx(consulta.getString("remuneracao_vrSalFx"));
                s2200.setRemuneracao_undSalFixo(consulta.getString("remuneracao_undSalFixo"));
                if (!consulta.getString("DiasExperiencia").toString().equals("45") && !consulta.getString("DiasExperiencia").toString().equals("90") &&
                        consulta.getString("DiasExperiencia").toString().equals("00")){
                   s2200.setDuracao_tpContr("1");
                   s2200.setDuracao_dtTerm("");
                }else{
                   s2200.setDuracao_tpContr("2");
                   s2200.setDuracao_dtTerm(consulta.getString("duracao_dtTerm"));
                }
                s2200.setLocalTrabGeral_tpInsc("1");
                s2200.setLocalTrabGeral_nrInsc(consulta.getString("localTrabGeral_nrInsc"));
                s2200.setSucessaoVinc_cnpjEmpregAnt(consulta.getString("sucessaoVinc_cnpjEmpregAnt"));
                s2200.setSucessaoVinc_dtTransf(consulta.getString("sucessaoVinc_dtTransf"));
                s2200.setSucessaoVinc_matricAnt(consulta.getString("sucessaoVinc_matricAnt"));

                s2200.setTrabEstrangeiro_casadoBr(consulta.getString("EstCasadoBr"));
                s2200.setTrabEstrangeiro_classTrabEstrang(consulta.getString("EstClassTrab"));
                s2200.setTrabEstrangeiro_dtChegada(consulta.getString("EstDtChegada"));
                s2200.setTrabEstrangeiro_filhosBr(consulta.getString("EstFilhosBr"));

                s2200.setHorarios(new ArrayList<>());
                S2200.Horario horario;
                if (consulta.getString("D1").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("1");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D2").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("2");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D3").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("3");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D4").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("4");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D5").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("5");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D6").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("6");
                    s2200.getHorarios().add(horario);
                }
                if (consulta.getString("D7").equals("1")) {
                    horario = new Horario();
                    horario.setHorario_codHorContrat(consulta.getString("horario_codHorContrat"));
                    horario.setHorario_dia("7");
                    s2200.getHorarios().add(horario);
                }

                s2200.setDependentes(new ArrayList<>());
                s2200.setHorContratual_qtdHrsSem(consulta.getString("tipocomp").equals("S") ? consulta.getString("chcomp")
                        : (new BigDecimal(consulta.getString("chcomp")).compareTo(new BigDecimal("180")) == 1 ? "44"
                        : (new BigDecimal(consulta.getString("chcomp")).compareTo(new BigDecimal("180")) == 0 ? "30"
                        : (new BigDecimal(consulta.getString("chcomp")).divide(new BigDecimal("5")).toPlainString()))));
                s2200.setHorContratual_tpJornada(
                        ((consulta.getString("DiasTrbDiu").equals("1")
                        || consulta.getString("DiasTrbNot").equals("1"))
                        && consulta.getString("DiasFolga").equals("1")) ? "2" : "3");
                s2200.setHorContratual_tmpParc("0");
                s2200.setFiliacaoSindical_cnpjSindTrab(consulta.getString("PgCtSin"));
                s2200.setDesligamento_dtDesligamento(consulta.getString("situacao").equals("D") ? consulta.getString("desligamento_dtDesligamento") : "");
                listaS2200.add(s2200);
            }
            consulta.Close();
            return listaS2200;
        } catch (Exception e) {
            throw new Exception("S2200Dao.get - " + e.getMessage() + "\r\n"
                    + " Select funcion.PgCtSin ,RHHorario.tipocomp, RHHorario.chcomp, Funcion.CPF trabalhador_cpfTrab, "
                    + " Funcion.PIS trabalhador_nisTrab, Funcion.Nome trabalhador_nmTrab, "
                    + " Funcion.Sexo trabalhador_sexo, Funcion.Raca trabalhador_racaCor,  Funcion.EstCivil trabalhador_estCiv, "
                    + " Funcion.Instrucao trabalhador_grauInstr, Funcion.TipoAdm trabalhador_indPriEmpr, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto, "
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie CTPS_serieCtps, "
                    + " Funcion.CTPS_UF CTPS_ufCtps, Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor, "
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped, "
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid, "
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd, "
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, Municipios.CodIBGE brasil_codMunic, "
                    + " Funcion.UF brasil_uf, Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, Funcion.Email contato_emailPrinc, "
                    + " Convert(BigInt, Funcion.Matr) vinculo_matricula, substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoCeletista_dtAdm, "
                    + " Fornec.CNPJ infoCeletista_cnpjSindCategProf, Funcion.Salario remuneracao_vrSalFx,  Funcion.FormaPgto remuneracao_undSalFixo, "
                    + " Filiais.CNPJ localTrabGeral_nrInsc, Cargos.Descricao infoContrato_nmCargo, Cargos.CBO, RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, "
                    + " RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, RHEscala.HrSabNot, RHEscala.HrDomDiu, "
                    + " RHEscala.HrDomNot, Convert(varchar,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, RHHorario.D1, "
                    + " RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, Funcion.Situacao, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Demis,111),'/','-'),0,11) desligamento_dtDesligamento, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc,"
                    + " Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                       (Select Max(CodIBGE) From Municipios where Nome = Substring(Naturalid,1,(CharIndex('/',Naturalid)-1)) "
                    + "                                                         and UF = Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) and Len(Naturalid) > 0) "
                    + "                    else '0' end nascimento_codMunic, "
                    + "                    Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                    (Select Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) UF from Funcion where Matr = FPMensal.Matr) "
                    + " else '0' end nascimento_uf, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "         and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.Matr "
                    + "         and z.evento = 'S-2200' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso "
                    + " from FPMensal "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr "
                    + " Left Join Cargos  on Cargos.Codigo = Funcion.CodCargo "
                    + " Left Join Sindicatos  on Sindicatos.Codigo = FPMensal.Sindicato "
                    + " Left Join PstServ  on PstServ.Secao  = FPMensal.Secao "
                    + "                   and PstServ.CodFil = FPMensal.CodFil "
                    + " Left Join Clientes  on Clientes.Codigo = PstServ.CodCli "
                    + "                    and Clientes.CodFil = PstServ.CodFil "
                    + " Left Join RHEscala  on RHEscala.Codigo = FPMEnsal.Escala "
                    + "                    and RHEscala.CodFil = FPMEnsal.CodFil "
                    + " Left Join RHHorario  on  RHHorario.Codigo    = FPMensal.Horario "
                    + "                      and RHHorario.CodFil    = FPMensal.CodFil "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade "
                    + "                     and Municipios.UF   = Funcion.UF"
                    + " Left Join Fornec on Fornec.Codigo = Sindicatos.CodForn"
                    + " where TipoFP = 'MEN' "
                    + " and Funcion.codfil = " + codFil
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtInicio = " + compet + "-01) "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ");
        }
    }

    public List<S2200> getDependentes(List<S2200> s2200, String codfil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Convert(BigInt, Funcion.Matr) Vinculo_matricula, F5_Dep.Tipo dependente_tpDep, F5_Dep.Nome dependente_nmDep, "
                    + " substring(replace(convert(varchar,F5_Dep.Dt_Nasc,111),'/','-'),0,11) dependente_dtNascto, "
                    + " F5_Dep.DepIR dependente_depIRRF, F5_Dep.DepSF dependente_depSF, F5_Dep.CPF dependente_cpfDep,"
                    + " Funcion.cpf Trabalhador_indPriEmpr "
                    + " from FPMensal "
                    + " Left Join Funcion on Funcion.Matr = FPMensal.Matr "
                    + " Inner Join F5_Dep  on F5_Dep.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' and Funcion.CodFil = ?  and F5_Dep.Tipo not in ('T','S') "// and F5_Dep.Tipo <> 'S' "
                    + "         and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtFecha <= Convert(Date,Getdate()))";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.select();
            S2200 s2200_dependente = new S2200();
            Dependentes dependente;
            int indice;
            while (consulta.Proximo()) {
                dependente = new Dependentes();
                dependente.setDependente_tpDep(consulta.getString("dependente_tpDep"));
                dependente.setDependente_nmDep(consulta.getString("dependente_nmDep"));
                dependente.setDependente_dtNascto(consulta.getString("dependente_dtNascto"));
                dependente.setDependente_depIRRF(consulta.getString("dependente_depIRRF"));
                dependente.setDependente_depSF(consulta.getString("dependente_depSF"));
                dependente.setDependente_cpfDep(consulta.getString("dependente_cpfDep"));
                dependente.setDependente_incTrab("N");

                s2200_dependente.setTrabalhador_cpfTrab(consulta.getString("Trabalhador_indPriEmpr"));

                indice = s2200.indexOf(s2200_dependente);
                if (indice >= 0) {
                    s2200.get(indice).getDependentes().add(dependente);
                }
//                for(S2200 s : s2200){
//                    if(s.getVinculo_matricula().equals(consulta.getString("Vinculo_matricula"))){
//                        s.getDependentes().add(dependente);
//                        break;
//                    }
//                }
            }
            consulta.Close();
            return s2200;
        } catch (Exception e) {
            throw new Exception("S2200Dao.getDependentes - " + e.getMessage() + "\r\n"
                    + "Select Convert(BigInt, Funcion.Matr) Matr, F5_Dep.Tipo dependente_tpDep, F5_Dep.Nome dependente_nmDep, "
                    + " substring(replace(convert(varchar,F5_Dep.Dt_Nasc,111),'/','-'),0,11) dependente_dtNascto, F5_Dep.DepIR dependente_depIRRF, "
                    + " F5_Dep.DepSF dependente_depSF "
                    + " from FPMensal "
                    + " Left Join Funcion on Funcion.Matr = FPMensal.Matr "
                    + " Inner Join F5_Dep  on F5_Dep.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' and Funcion.CodFil = " + codfil + " and F5_Dep.Tipo not in ('T','S') "
                    + "         and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtFecha <= Convert(Date,Getdate()))");
        }
    }

    public List<S2200> getTransferencias(List<S2200> s2200, String codfil, String compet,
            Persistencia persistencia, Persistencia persistenciaAgil2) throws Exception {
        try {
            // Buscando funcionários transferidos para na empresa atual
            String sql = " Select Funcion.CPF trabalhador_cpfTrab "
                    + " from FPMensal  "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr  "
                    + " where TipoFP = 'MEN'  "
                    + " and Funcion.codfil = ? "
                    + " and Funcion.TipoAdm = '70' "
                    + " and FPMensal.situacao <> 'D'  "
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4)  "
                    + "                             from FPPeriodos where DtInicio = ?) "
                    //+ " and funcion.Matr > (Select Min(z.Matr) from Funcion z where z.Dt_Admis >= ?) "
                    + " ORDER BY Funcion.Matr asc ; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(compet + "-01");
            //consulta.setString(compet+"-01");
            consulta.select();
            StringBuilder cpfs = new StringBuilder();
            while (consulta.Proximo()) {
                cpfs = cpfs.append("'").append(consulta.getString("trabalhador_cpfTrab")).append("', ");
            }
            cpfs = cpfs.append("' '");

            // Buscando infos na empresa anterior
            sql = " select filiais.cnpj sucessaoVinc_cnpjEmpregAnt, Convert(BigInt,funcion.matr) sucessaoVinc_matricAnt, "
                    + " Convert(Date,funcion.Dt_Situac) sucessaoVinc_dtTransf, funcion.cpf trabalhador_cpfTrab "
                    + " from funcion "
                    + " left join filiais on filiais.codfil = funcion.codfil "
                    + " where funcion.cpf in (" + cpfs + ")"
                    + "   and funcion.Situacao = 'D'";
            if (persistenciaAgil2 == null) {
                consulta = new Consulta(sql, persistencia);
            } else {
                consulta = new Consulta(sql, persistenciaAgil2);
            }
            consulta.select();
            S2200 s2200_trans;
            int indice;
            while (consulta.Proximo()) {
                s2200_trans = new S2200();
                s2200_trans.setTrabalhador_cpfTrab(consulta.getString("trabalhador_cpfTrab"));

                indice = s2200.indexOf(s2200_trans);

                if (indice >= 0) {
                    s2200.get(indice).setSucessaoVinc_cnpjEmpregAnt(consulta.getString("sucessaoVinc_cnpjEmpregAnt"));
                    s2200.get(indice).setSucessaoVinc_matricAnt(consulta.getString("sucessaoVinc_matricAnt"));
                    s2200.get(indice).setSucessaoVinc_dtTransf(consulta.getString("sucessaoVinc_dtTransf"));
                }
            }
            consulta.Close();
            return s2200;
        } catch (Exception e) {
            throw new Exception("S2200Dao.getTransferencias - " + e.getMessage() + "\r\n"
                    + "Select Convert(BigInt, Funcion.Matr) Matr, F5_Dep.Tipo dependente_tpDep, F5_Dep.Nome dependente_nmDep, "
                    + " substring(replace(convert(varchar,F5_Dep.Dt_Nasc,111),'/','-'),0,11) dependente_dtNascto, F5_Dep.DepIR dependente_depIRRF, "
                    + " F5_Dep.DepSF dependente_depSF "
                    + " from FPMensal "
                    + " Left Join Funcion on Funcion.Matr = FPMensal.Matr "
                    + " Inner Join F5_Dep  on F5_Dep.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' and Funcion.CodFil = " + codfil + " and F5_Dep.Tipo <> 'T' "
                    + "         and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtFecha <= Convert(Date,Getdate()))");
        }
    }
}

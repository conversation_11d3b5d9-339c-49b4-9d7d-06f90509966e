/*
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.Mascaras;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorDia")
public class ConversorDia implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            Date date = null;
            if (value.toString().length() == 8) {
                date = new SimpleDateFormat("yyyyMMdd").parse(value.toString());
            } else if (value.toString().length() == 10) {
                date = new SimpleDateFormat("yyyy-MM-dd").parse(value.toString());
            }
            Mascaras mascaras = new Mascaras();
            return new SimpleDateFormat(mascaras.getPadraoData() + " - EEEE", FacesContext.getCurrentInstance().getViewRoot().getLocale()).format(date);
        } catch (Exception e) {
            return value.toString();
        }
    }

}

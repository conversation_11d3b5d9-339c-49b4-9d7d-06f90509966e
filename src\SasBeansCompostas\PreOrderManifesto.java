/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

/**
 *
 * <AUTHOR>
 */
public class PreOrderManifesto {

    private String AgenciaDestino;
    private String SubAgenciaDestino;
    private String Destino;
    private String Guia;
    private String Serie;
    private String Lacre;
    private String Valor;
    private String RazaoSocial;
    private String Endereco;
    private String Cidade;
    private String UF;
    private String CNPJ;
    private String Fone;
    private String Nome;
    private String CodPessoaAut;
    private String Rota;
    private String Obs;

    public String getAgenciaDestino() {
        return AgenciaDestino;
    }

    public void setAgenciaDestino(String AgenciaDestino) {
        this.AgenciaDestino = AgenciaDestino;
    }

    public String getSubAgenciaDestino() {
        return SubAgenciaDestino;
    }

    public void setSubAgenciaDestino(String SubAgenciaDestino) {
        this.SubAgenciaDestino = SubAgenciaDestino;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getCodPessoaAut() {
        return CodPessoaAut;
    }

    public void setCodPessoaAut(String CodPessoaAut) {
        this.CodPessoaAut = CodPessoaAut;
    }

    public String getRazaoSocial() {
        return RazaoSocial;
    }

    public void setRazaoSocial(String RazaoSocial) {
        this.RazaoSocial = RazaoSocial;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getFone() {
        return Fone;
    }

    public void setFone(String Fone) {
        this.Fone = Fone;
    }

    public String getDestino() {
        return Destino;
    }

    public void setDestino(String Destino) {
        this.Destino = Destino;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }
}

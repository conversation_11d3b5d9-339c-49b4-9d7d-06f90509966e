/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.R1070;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class R1070Dao {

    public List<R1070> get(String codFil, String compet, String ambiente,
            Persistencia persistencia) throws Exception {
        try {
            List<R1070> retorno = new ArrayList<>();
            String sql = " Select Filiais.TipoPessoa ideContri_tpInsc, Filiais.CNPJ ideContri_nrInsc, Filiais.RazaoSocial infoCadastro_nmRazao, "
                    + " Filiais.PorteEmp infoCadastro_classTrib, Rais.NatJur infoCadastro_natJurid, Pessoa.Nome contato_nmCtt, "
                    + " Dirf.CPFRInf contato_cpfCtt, DIRF.DDDRInf+DIRF.FoneRInf contato_foneFixo, DIRF.emailRInf contato_email, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1070' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1070' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1070' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<cdRetorno>0%')) a) sucesso "
                    + " From Filiais "
                    + " Left join Rais  on Rais.CodFil = Filiais.CodFil "
                    + " Left Join Dirf  on Dirf.CodFil = Filiais.CodFil "
                    + " Left join Pessoa  on Pessoa.Codigo = Dirf.CodPessoaRIn "
                    + " Where Filiais.CodFil = ? "
                    + " ORDER BY sucesso asc, substring(Filiais.CNPJ,1,8) asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.select();
            R1070 r1070;
            while (consulta.Proximo()) {
                r1070 = new R1070();
                r1070.setSucesso(consulta.getInt("sucesso"));
                r1070.setIdeEvento_procEmi("1");
                r1070.setIdeEvento_verProc("Satellite Reinf");

                r1070.setIdeEmpregador_tpInsc(consulta.getString("ideContri_tpInsc"));
                r1070.setIdeEmpregador_nrInsc(consulta.getString("ideContri_nrInsc"));
                r1070.setInfoCadastro_classTrib(consulta.getString("infoCadastro_classTrib"));

                r1070.setInfoCadastro_nmRazao(consulta.getString("infoCadastro_nmRazao"));

//                r1070.setIdePeriodo_iniValid(compet);
//                r1070.setIdeEvento_tpAmb(ambiente);
//                r1070.setIdeProcesso_tpProc(tpProc);
//                r1070.setIdeProcesso_nrProc(nrProc);
//                r1070.setIdeProcesso_iniValid(iniValid);
//                r1070.setIdeProcesso_indAutoria(indAutoria);  
//                r1070.setInfoSusp_codSusp(codSusp);
//                r1070.setInfoSusp_indSusp(indSusp);
//                r1070.setInfoSusp_dtDescisao(dtDescisao);
//                r1070.setInfoSusp_indDeposito(indDeposito);
//                r1070.setDadosProcJud_ufVara(ufVara);
//                r1070.setDadosProcJud_codMunic(codMunic);
//                r1070.setDadosProcJud_idVara(idVara);
                r1070.setId((r1070.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2") + r1070.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001");
                r1070.setIdeEmpregador_tpInsc(r1070.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                r1070.setIdeEmpregador_nrInsc(r1070.getIdeEmpregador_nrInsc().substring(0, 8));

                retorno.add(r1070);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("R1000Dao.get - " + e.getMessage() + "\r\n"
                    + "  Select Filiais.TipoPessoa ideContri_tpInsc, Filiais.CNPJ ideContri_nrInsc, Filiais.RazaoSocial infoCadastro_nmRazao, "
                    + " Filiais.PorteEmp infoCadastro_classTrib, Rais.NatJur infoCadastro_natJurid, Pessoa.Nome contato_nmCtt, "
                    + " Dirf.CPFRInf contato_cpfCtt, DIRF.DDDRInf+DIRF.FoneRInf contato_foneFixo, DIRF.emailRInf contato_email, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1070' "
                    + "             and z.Compet = " + compet
                    + "             and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1070' "
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1070' "
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<cdRetorno>0%')) a) sucesso "
                    + " From Filiais "
                    + " Left join Rais  on Rais.CodFil = Filiais.CodFil "
                    + " Left Join Dirf  on Dirf.CodFil = Filiais.CodFil "
                    + " Left join Pessoa  on Pessoa.Codigo = Dirf.CodPessoaRIn "
                    + " Where Filiais.CodFil = " + codFil
                    + " ORDER BY sucesso asc, substring(Filiais.CNPJ,1,8) asc ");
        }
    }
}

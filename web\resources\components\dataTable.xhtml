<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:composite="http://java.sun.com/jsf/composite">
    <composite:interface>
        <composite:attribute name="idTabela" required="true"/>
        <composite:attribute name="rowKey" default="---------"/>
        <composite:attribute name="selection" required="true"/>
        <composite:attribute name="type" required="true"/>
        <composite:attribute name="value" required="true"/>
        <composite:attribute name="var" required="true"/>

        <composite:clientBehavior name="clientEvent" targets="inputId" event="rowDblselect" />
    </composite:interface>

    <composite:implementation>
        <p:panel
            class="ui-grid ui-grid-responsive FundoPagina"
            style="overflow:hidden !important; padding-right:12px !important;"
            >
            <p:dataTable
                id="#{cc.attrs.idTabela}"
                currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{cc.attrs.type}"
                emptyMessage="#{localemsgs.SemRegistros}"
                rowKey="#{cc.attrs.rowKey}"
                selection="#{cc.attrs.selection}"
                value="#{cc.attrs.value}"
                var="#{cc.attrs.var}"
                class="tabela DataGrid"
                lazy="true"
                paginator="true"
                paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                reflow="true"
                rows="15"
                rowsPerPageTemplate="5, 10, 15, 20, 25" 
                scrollWidth="100%"
                scrollable="true"
                selectionMode="single"
                style="display: flex; flex-direction: column; font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                styleClass="tabela">
                <c:set target="#{component}"
                       property="var"
                       value="#{cc.attrs.var}"/>
                <composite:insertChildren />
            </p:dataTable>
        </p:panel>
    </composite:implementation>
</html>

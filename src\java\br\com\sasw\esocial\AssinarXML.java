/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.esocial;

import static br.com.sasw.esocial.UtilXML.subStrIntoDelim;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.Key;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;
import javax.xml.crypto.dsig.CanonicalizationMethod;
import javax.xml.crypto.dsig.DigestMethod;
import javax.xml.crypto.dsig.Reference;
import javax.xml.crypto.dsig.SignedInfo;
import javax.xml.crypto.dsig.Transform;
import javax.xml.crypto.dsig.XMLSignature;
import javax.xml.crypto.dsig.XMLSignatureFactory;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.KeyValue;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

/**
 *
 * <AUTHOR>
 */
public class AssinarXML {

    public static String assinarXML0(KeyStore ks, String certificado, String senha, String xml, String grupo,
            String tpInsc, String nrInsc, String tagAssinatura) throws Exception {
        try {

            PrivateKey signatureKey_ = null;
            PublicKey pubKey_ = null;
            X509Certificate signingCertificate_ = null;
            Boolean prik = false;
            Boolean pubk = false;
            Enumeration aliases = ks.aliases();
            while (aliases.hasMoreElements()) {
                String keyAlias = aliases.nextElement().toString();
                Key key = ks.getKey(keyAlias, senha.toCharArray());
                if (key instanceof java.security.interfaces.RSAPrivateKey) {
                    Certificate[] certificateChain = ks.getCertificateChain(keyAlias);
                    X509Certificate signerCertificate = (X509Certificate) certificateChain[0];
                    boolean[] keyUsage = signerCertificate.getKeyUsage();
                    // check for digital signature or non-repudiation,
                    // but also accept if none is set
                    if ((keyUsage == null) || keyUsage[0] || keyUsage[1]) {
                        signatureKey_ = (PrivateKey) key;
                        signingCertificate_ = signerCertificate;
                        prik = true;
                        pubKey_ = signerCertificate.getPublicKey();
                        break;
                    }
                }
            }

            if (signatureKey_ == null) {
                throw new GeneralSecurityException("Found no signature key. Ensure that a valid card is inserted.");
            }
            XMLSignatureFactory xmlSigFactory = XMLSignatureFactory.getInstance("DOM");
            Reference ref = null;
            SignedInfo signedInfo = null;
            try {
                List<Transform> transform = new ArrayList<>();
                transform.add(xmlSigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null));
                transform.add(xmlSigFactory.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null));

                ref = xmlSigFactory.newReference("", xmlSigFactory.newDigestMethod(DigestMethod.SHA256, null), transform, null, null);
                signedInfo = xmlSigFactory.newSignedInfo(
                        xmlSigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE,
                                (C14NMethodParameterSpec) null),
                        xmlSigFactory.newSignatureMethod("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", null),
                        Collections.singletonList(ref));
            } catch (NoSuchAlgorithmException ex) {
                throw new Exception(ex.getMessage());
            }
            KeyInfoFactory kif = xmlSigFactory.getKeyInfoFactory();
            X509Data x509data = kif.newX509Data(Collections.nCopies(1, signingCertificate_));
            KeyValue kval = kif.newKeyValue(pubKey_);
            List keyInfoItems = new ArrayList();
            //keyInfoItems.add(kval);
            keyInfoItems.add(x509data);

            KeyInfo keyInfo = kif.newKeyInfo(keyInfoItems);
            //Create a new XML Signature
            XMLSignature xmlSignature = xmlSigFactory.newXMLSignature(signedInfo, keyInfo);

            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            DocumentBuilder builder = dbf.newDocumentBuilder();

            String[] eventos = xml.split("Evento.Separador");

            StringBuilder sb = new StringBuilder();
            sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
            sb.append("<eSocial xmlns=\"http://www.esocial.gov.br/schema/lote/eventos/envio/v1_1_1\">");
            sb.append("<envioLoteEventos grupo=\"").append(grupo).append("\">"); // TABELA = 1, NAO-PERIODICO = 2, PERIODICO = 3
            sb.append("<ideEmpregador>");
            sb.append("<tpInsc>").append(tpInsc.equals("J") ? "1" : "2").append("</tpInsc>");
            sb.append("<nrInsc>").append(nrInsc.substring(0, 8)).append("</nrInsc>");
            sb.append("</ideEmpregador>");
            sb.append("<ideTransmissor>");
            sb.append("<tpInsc>").append(tpInsc.equals("J") ? "1" : "2").append("</tpInsc>");
            sb.append("<nrInsc>").append(nrInsc).append("</nrInsc>");
            sb.append("</ideTransmissor>");
            sb.append("<eventos>");
            for (String evento : eventos) {
                Document doc = builder.parse(new ByteArrayInputStream(evento.getBytes(StandardCharsets.UTF_8.name())));

                //DOMSignContext domSignCtx = new DOMSignContext((Key) signatureKey_, doc.getElementsByTagName("eSocial").item(0));
                DOMSignContext domSignCtx = new DOMSignContext((Key) signatureKey_, doc.getElementsByTagName(tagAssinatura).item(0));
                xmlSignature.sign(domSignCtx);

                StringWriter writer = new StringWriter();
                TransformerFactory tf = TransformerFactory.newInstance();
                Transformer transformer = tf.newTransformer();
                transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
                transformer.transform(new DOMSource(doc), new StreamResult(writer));

                String id = subStrIntoDelim(evento, "Id=\"", "\"");

                sb.append("<evento Id=\"").append(id).append("\">");
                sb.append(writer.getBuffer()).append("");
                sb.append("</evento>");
            }

            sb.append("</eventos>");
            sb.append("</envioLoteEventos>");
            sb.append("</eSocial>");
            //sb.append("</REINF>");

            return sb.toString();
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public static String assinarXML(KeyStore ks, String senha, String xml, String grupo,
            String tpInsc, String nrInsc, String tpInscTrans, String nrInscTrans, String tagAssinatura) throws Exception {
        try {

            PrivateKey signatureKey_ = null;
            X509Certificate signingCertificate_ = null;
            Enumeration aliases = ks.aliases();
            while (aliases.hasMoreElements()) {
                String keyAlias = aliases.nextElement().toString();
                Key key = ks.getKey(keyAlias, senha.toCharArray());
                if (key instanceof java.security.interfaces.RSAPrivateKey) {
                    Certificate[] certificateChain = ks.getCertificateChain(keyAlias);
                    X509Certificate signerCertificate = (X509Certificate) certificateChain[0];
                    boolean[] keyUsage = signerCertificate.getKeyUsage();
                    // check for digital signature or non-repudiation,
                    // but also accept if none is set
                    if ((keyUsage == null) || keyUsage[0] || keyUsage[1]) {
                        signatureKey_ = (PrivateKey) key;
                        signingCertificate_ = signerCertificate;
                        break;
                    }
                }
            }

            if (signatureKey_ == null) {
                throw new GeneralSecurityException("Found no signature key. Ensure that a valid card is inserted.");
            }

            StringBuilder sb = new StringBuilder();

            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            DocumentBuilder builder = dbf.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8.name())));

            XMLSignatureFactory xmlSigFactory = XMLSignatureFactory.getInstance("DOM");
            Reference ref = null;
            SignedInfo signedInfo = null;
            Node node = null;

            try {
                List<Transform> transform = new ArrayList<>();
                transform.add(xmlSigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null));
                transform.add(xmlSigFactory.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null));
                switch (tagAssinatura) {
                    case "Reinf":
                        NodeList nodeList = doc.getElementsByTagName("*");
                        Element element = (Element) nodeList.item(1);
                        element.setIdAttribute("id", true);
                        node = element.getParentNode();
//                        ref = xmlSigFactory.newReference("#" + element.
//                                getAttribute("id"), xmlSigFactory.newDigestMethod(
//                                        DigestMethod.SHA256, null), transform, 
//                                        null, null);
                        ref = xmlSigFactory.newReference("", 
                                xmlSigFactory.newDigestMethod(
                                        DigestMethod.SHA256, null), transform, 
                                        null, null);
                        break;
                    case "eSocial":
                        node = doc.getElementsByTagName("eSocial").item(0);
                        ref = xmlSigFactory.newReference("", xmlSigFactory.newDigestMethod(DigestMethod.SHA256, null), transform, null, null);
                        break;
                }

                signedInfo = xmlSigFactory.newSignedInfo(
                        xmlSigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE,
                                (C14NMethodParameterSpec) null),
                        xmlSigFactory.newSignatureMethod("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", null),
                        Collections.singletonList(ref));
            } catch (NoSuchAlgorithmException ex) {
                throw new Exception(ex.getMessage());
            }
            KeyInfoFactory kif = xmlSigFactory.getKeyInfoFactory();
            X509Data x509data = kif.newX509Data(Collections.nCopies(1, signingCertificate_));
            List keyInfoItems = new ArrayList();
            keyInfoItems.add(x509data);

            KeyInfo keyInfo = kif.newKeyInfo(keyInfoItems);
            //Create a new XML Signature
            XMLSignature xmlSignature = xmlSigFactory.newXMLSignature(signedInfo, keyInfo);

            DOMSignContext domSignCtx = new DOMSignContext((Key) signatureKey_, node);

            xmlSignature.sign(domSignCtx);

            StringWriter writer = new StringWriter();
            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            
            transformer.transform(new DOMSource(doc), new StreamResult(writer));            
            switch (tagAssinatura) {
                case "Reinf":                    
                    sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
                    sb.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">\n");
                    sb.append(" <envioLoteEventos>\n");
                    sb.append("         <ideContribuinte>\n");
                    sb.append("             <tpInsc>").append(tpInsc.equals("J") ? "1" : "2").append("</tpInsc>\n");
                    sb.append("             <nrInsc>").append(nrInsc.substring(0, 8)).append("</nrInsc>\n");
                    sb.append("         </ideContribuinte>\n");
                    sb.append("   <eventos>\n");
                    sb.append("     <evento Id=\"").append(subStrIntoDelim(xml, "id=\"", "\"")).append("\">\n");
                    sb.append(writer.getBuffer()).append("");
                    sb.append("     </evento>\n");
                    sb.append("   </eventos>\n");
                    sb.append(" </envioLoteEventos>\n");
                    sb.append("</Reinf>");
                    break;
                case "eSocial":
                    sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
                    sb.append("<eSocial xmlns=\"http://www.esocial.gov.br/schema/lote/eventos/envio/v1_1_1\">\n");
                    sb.append("     <envioLoteEventos grupo=\"").append(grupo).append("\">\n"); // TABELA = 1, NÃO-PERIODICO = 2, PERIODICO = 3
                    sb.append("         <ideEmpregador>\n");
                    sb.append("             <tpInsc>").append(tpInsc.equals("J") ? "1" : "2").append("</tpInsc>\n");
                    sb.append("             <nrInsc>").append(nrInsc.substring(0, 8)).append("</nrInsc>\n");
                    sb.append("         </ideEmpregador>\n");
                    sb.append("         <ideTransmissor>\n");
                    sb.append("             <tpInsc>").append(tpInscTrans.equals("J") ? "1" : "2").append("</tpInsc>\n");
                    sb.append("             <nrInsc>").append(nrInscTrans).append("</nrInsc>\n");
                    sb.append("         </ideTransmissor>\n");
                    sb.append("         <eventos>\n");
                    sb.append("             <evento Id=\"").append(subStrIntoDelim(xml, "Id=\"", "\"")).append("\">\n");
                    sb.append(writer.getBuffer()).append("\n");
                    sb.append("             </evento>\n");
                    sb.append("         </eventos>\n");
                    sb.append("     </envioLoteEventos>");
                    sb.append("</eSocial>");
                    break;

            }
            return sb.toString();
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public static String assinarXMLReinf(KeyStore ks, String senha, String xml, String tagSignature) {
        try {

            PrivateKey privateKey = null;
            PublicKey pubKey_ = null;
            X509Certificate certificado = null;
            Boolean prik = false;
            Boolean pubk = false;
            Enumeration aliases = ks.aliases();
            while (aliases.hasMoreElements()) {
                String keyAlias = aliases.nextElement().toString();
                Key key = ks.getKey(keyAlias, senha.toCharArray());
                if (key instanceof java.security.interfaces.RSAPrivateKey) {
                    Certificate[] certificateChain = ks.getCertificateChain(keyAlias);
                    X509Certificate signerCertificate = (X509Certificate) certificateChain[0];
                    boolean[] keyUsage = signerCertificate.getKeyUsage();
                    // check for digital signature or non-repudiation,
                    // but also accept if none is set
                    if ((keyUsage == null) || keyUsage[0] || keyUsage[1]) {
                        privateKey = (PrivateKey) key;
                        certificado = signerCertificate;
                        prik = true;
                        pubKey_ = signerCertificate.getPublicKey();
                        break;
                    }
                }
            }

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(xml.getBytes("UTF-8")));

            NodeList list = doc.getElementsByTagName("Reinf");
//            NodeList list = doc.getElementsByTagName(tagSignature);    
            Element el = (Element) list.item(0);
            String referenceURI;
            referenceURI = el.getAttribute("id");
            el.setIdAttribute("id", true);

            XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

            List<Transform> transformList = new ArrayList<>();
            transformList.add(fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null));
            transformList.add(fac.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE,
                    (C14NMethodParameterSpec) null));

            Reference ref = fac.newReference("#" + referenceURI, fac.newDigestMethod(DigestMethod.SHA256, null),
                    transformList, null, null);

            SignedInfo si = fac.newSignedInfo(
                    fac.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
                    fac.newSignatureMethod("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", null),
                    Collections.singletonList(ref));

            KeyInfoFactory kif = fac.getKeyInfoFactory();
            X509Data x509Data = kif.newX509Data(Collections.singletonList(certificado));
            KeyInfo ki = kif.newKeyInfo(Collections.singletonList(x509Data));

            DOMSignContext dsc = new DOMSignContext(privateKey, el.getParentNode());

            XMLSignature signature = fac.newXMLSignature(si, ki);
            signature.sign(dsc);

            StreamResult streamResult = new StreamResult(new StringWriter());
            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer trans = tf.newTransformer();
            trans.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            trans.setOutputProperty(OutputKeys.INDENT, "no");
            trans.transform(new DOMSource(doc), streamResult);

            StringBuilder sb = new StringBuilder();
            sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
            sb.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventos/v1_03_02\">");
            sb.append("<loteEventos>\n");
            sb.append("<evento id=\"").append(subStrIntoDelim(xml, "id=\"", "\"")).append("\">");
            sb.append(streamResult.getWriter().toString());
            sb.append("</evento>");
            sb.append("</loteEventos>");
            sb.append("</Reinf>");
            return sb.toString();

        } catch (Exception e) {
            return e.getMessage();
        }

    }

}

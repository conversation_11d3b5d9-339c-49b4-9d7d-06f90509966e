package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Saspw {

    private String Nome;
    private String Situacao;
    private String Motivo;
    private int Acessos;
    private String CodFil;
    private String NomeCodFil;
    private BigDecimal Codigo;
    private String NomeCompleto;
    private String Descricao;
    private int CodGrupo;
    private String Nivelx;
    private String Nivel;
    private String NivelOP;
    private String PW;
    private BigDecimal CodPessoa;
    private BigDecimal CodPessoaWeb;
    private BigDecimal CodModulo;
    private String Modulo;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String cliente;
    private String Agencia;
    private String SubAgencia;
    private String email;

    public Saspw() {
        this.Nome = "";
        this.Situacao = "";
        this.Motivo = "";
        this.Acessos = 0;
        this.CodFil = "0";
        this.NomeCodFil = "";
        this.Codigo = new BigDecimal("0");
        this.NomeCompleto = "";
        this.Descricao = "";
        this.CodGrupo = 0;
        this.Nivelx = "";
        this.Nivel = "";
        this.NivelOP = "";
        this.PW = "";
        this.CodPessoa = new BigDecimal("0");
        this.CodPessoaWeb = new BigDecimal("0");
        this.CodModulo = new BigDecimal("0");
        this.Modulo = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
    }

    public String getNomeCodFil() {
        return NomeCodFil;
    }

    public void setNomeCodFil(String NomeCodFil) {
        this.NomeCodFil = NomeCodFil;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getMotivo() {
        return Motivo;
    }

    public void setMotivo(String Motivo) {
        this.Motivo = Motivo;
    }

    public int getAcessos() {
        return Acessos;
    }

    public void setAcessos(int Acessos) {
        this.Acessos = Acessos;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    public String getNomeCompleto() {
        return NomeCompleto;
    }

    public void setNomeCompleto(String NomeCompleto) {
        this.NomeCompleto = NomeCompleto;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public int getCodGrupo() {
        return CodGrupo;
    }

    public void setCodGrupo(int CodGrupo) {
        this.CodGrupo = CodGrupo;
    }

    public String getNivelx() {
        return Nivelx;
    }

    public void setNivelx(String Nivelx) {
        this.Nivelx = Nivelx;
    }

    public String getNivel() {
        return Nivel;
    }

    public void setNivel(String Nivel) {
        this.Nivel = Nivel;
    }

    public String getNivelOP() {
        return NivelOP;
    }

    public void setNivelOP(String NivelOP) {
        this.NivelOP = NivelOP;
    }

    public String getPW() {
        return PW;
    }

    public void setPW(String PW) {
        this.PW = PW;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("0");
        }
    }

    public BigDecimal getCodPessoaWeb() {
        return CodPessoaWeb;
    }

    public void setCodPessoaWeb(String CodPessoaWeb) {
        try {
            this.CodPessoaWeb = new BigDecimal(CodPessoaWeb);
        } catch (Exception e) {
            this.CodPessoaWeb = new BigDecimal("0");
        }
    }

    public BigDecimal getCodModulo() {
        return CodModulo;
    }

    public void setCodModulo(String CodModulo) {
        try {
            this.CodModulo = new BigDecimal(CodModulo);
        } catch (Exception e) {
            this.CodModulo = new BigDecimal("0");
        }
    }

    public String getModulo() {
        return Modulo;
    }

    public void setModulo(String Modulo) {
        this.Modulo = Modulo;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getSubAgencia() {
        return SubAgencia;
    }

    public void setSubAgencia(String SubAgencia) {
        this.SubAgencia = SubAgencia;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}

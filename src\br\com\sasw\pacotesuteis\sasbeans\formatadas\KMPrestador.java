/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans.formatadas;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class KMPrestador {

    private String Data;
    private String Origem;
    private String Destino;
    private String KmPercorrido;

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getOrigem() {
        return Origem;
    }

    public void setOrigem(String Origem) {
        this.Origem = Origem;
    }

    public String getDestino() {
        return Destino;
    }

    public void setDestino(String Destino) {
        this.Destino = Destino;
    }

    public String getKmPercorrido() {
        return KmPercorrido;
    }

    public void setKmPercorrido(String KmPercorrido) {
        this.KmPercorrido = KmPercorrido;
    }

    public void setKmPercorrido(BigDecimal KmPercorrido) {
        this.KmPercorrido = KmPercorrido.toString();
    }

}

package Propriedades;

import java.io.FileInputStream;
import java.util.Locale;
import java.util.Properties;
import java.util.ResourceBundle;

/**
 *
 * <AUTHOR>
 */
public class Propriedades {

    /**
     * carrega as propriedes de um arquivo propertie de configuração
     *
     * @param Caminho - caminho do arquivo
     * @return - devolve um objeto Properties
     * @throws Exception
     */
    public Properties carregaPropriedade(String Caminho) throws Exception {
        try {
            Properties props = new Properties();
            FileInputStream file = new FileInputStream(Caminho);
            //props.load(getClass().getResourceAsStream(Caminho));
            props.load(file);
            return props;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar propriedade\r\n"
                    + e.getMessage());
        }
    }

    /**
     * Devolve o local e idioma atual do jvm
     *
     * @return
     */
    public Locale getLocalAtual() {
        Locale latual = Locale.getDefault();
        return latual;
    }

    /**
     * Retorna objeto contendo as tags para uso de strings internacionalizadas
     *
     * @param Caminho - Caminho do arquivo propertie de idioma
     * @param local - objeto Locale
     * @return
     */
    public ResourceBundle getIdioma(String Caminho, Locale local) {
        ResourceBundle bundle = ResourceBundle.getBundle(Caminho, local);
        return bundle;
    }
}

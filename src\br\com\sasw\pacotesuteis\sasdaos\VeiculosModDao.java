/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.VeiculosMod;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class VeiculosModDao {

    public VeiculosMod buscarModeloCodigo(int codigo, Persistencia persistencia) throws Exception {
        try {
            VeiculosMod veiculosMod = null;
            String sql = " SELECT * FROM VeiculosMod WHERE Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(codigo);
            consulta.select();
            if (consulta.Proximo()) {
                veiculosMod = new VeiculosMod();
                veiculosMod.setCodigo(consulta.getInt("Codigo"));
                veiculosMod.setDescricao(consulta.getString("Descricao"));
                veiculosMod.setKMMedioMin(consulta.getBigDecimal("KMMedioMin"));
                veiculosMod.setKMMedioMax(consulta.getBigDecimal("KMMedioMax"));
                veiculosMod.setTanque(consulta.getBigDecimal("Tanque"));
                veiculosMod.setObs(consulta.getString("Obs"));
                veiculosMod.setFabricante(consulta.getInt("Fabricante"));
                veiculosMod.setOperador(consulta.getString("Operador"));
                veiculosMod.setDt_alter(consulta.getLocalDate("Dt_alter"));
                veiculosMod.setHr_alter(consulta.getString("Hr_alter"));
            }
            consulta.Close();
            return veiculosMod;
        } catch (Exception e) {
            throw new Exception("VeiculosModDao.buscarModeloCodigo - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM VeiculosMod WHERE codigo = " + codigo);
        }
    }

    public VeiculosMod buscarModeloDescricao(String descricao, Persistencia persistencia) throws Exception {
        try {
            VeiculosMod veiculosMod = null;
            String sql = " SELECT * FROM VeiculosMod WHERE Descricao = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(descricao);
            consulta.select();
            if (consulta.Proximo()) {
                veiculosMod = new VeiculosMod();
                veiculosMod.setCodigo(consulta.getInt("Codigo"));
                veiculosMod.setDescricao(consulta.getString("Descricao"));
                veiculosMod.setKMMedioMin(consulta.getBigDecimal("KMMedioMin"));
                veiculosMod.setKMMedioMax(consulta.getBigDecimal("KMMedioMax"));
                veiculosMod.setTanque(consulta.getBigDecimal("Tanque"));
                veiculosMod.setObs(consulta.getString("Obs"));
                veiculosMod.setFabricante(consulta.getInt("Fabricante"));
                veiculosMod.setOperador(consulta.getString("Operador"));
                veiculosMod.setDt_alter(consulta.getLocalDate("Dt_alter"));
                veiculosMod.setHr_alter(consulta.getString("Hr_alter"));
            }
            consulta.Close();
            return veiculosMod;
        } catch (Exception e) {
            throw new Exception("VeiculosModDao.buscarModeloDescricao - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM VeiculosMod WHERE Descricao = " + descricao);
        }
    }

    public void cadastrarModelo(VeiculosMod veiculosMod, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO VeiculosMod (Codigo, Descricao, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES ((SELECT ISNULL(MAX(Codigo), 0) + 1 Codigo FROM VeiculosMod), ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(veiculosMod.getDescricao().toUpperCase());
            consulta.setString(veiculosMod.getOperador());
            consulta.setString(veiculosMod.getDt_alter().toString());
            consulta.setString(veiculosMod.getHr_alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("VeiculosModDao.cadastrarModelo - " + e.getMessage() + "\r\n"
                    + " INSERT INTO VeiculosMod (Codigo, Descricao, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES ((SELECT ISNULL(MAX(Codigo), 0) + 1 Codigo FROM VeiculosMod), " + veiculosMod.getDescricao() + ", "
                    + "" + veiculosMod.getOperador() + ", " + veiculosMod.getDt_alter().toString() + ", " + veiculosMod.getHr_alter() + ") ");
        }
    }

    public List<VeiculosMod> listarModelos(Persistencia persistencia) throws Exception {
        try {
            List<VeiculosMod> retorno = new ArrayList<>();
            String sql = " SELECT * FROM VeiculosMod ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            VeiculosMod veiculosMod;
            while (consulta.Proximo()) {
                veiculosMod = new VeiculosMod();
                veiculosMod.setCodigo(consulta.getInt("Codigo"));
                veiculosMod.setDescricao(consulta.getString("Descricao"));
                veiculosMod.setKMMedioMin(consulta.getBigDecimal("KMMedioMin"));
                veiculosMod.setKMMedioMax(consulta.getBigDecimal("KMMedioMax"));
                veiculosMod.setTanque(consulta.getBigDecimal("Tanque"));
                veiculosMod.setObs(consulta.getString("Obs"));
                veiculosMod.setFabricante(consulta.getInt("Fabricante"));
                veiculosMod.setOperador(consulta.getString("Operador"));
                veiculosMod.setDt_alter(consulta.getLocalDate("Dt_alter"));
                veiculosMod.setHr_alter(consulta.getString("Hr_alter"));
                retorno.add(veiculosMod);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("VeiculosModDao.listarModelos - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM VeiculosMod ");
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1040;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1040Dao {

    public List<S1040> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql1 = "Select Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc "
                    + " From Filiais "
                    + " Where Filiais.CodFil = ? ";
            Consulta consulta = new Consulta(sql1, persistencia);
            consulta.setString(codFil);
            consulta.select();
            String ideEmpregador_tpInsc = "", ideEmpregador_nrInsc = "";
            while (consulta.Proximo()) {
                ideEmpregador_tpInsc = consulta.getString("ideEmpregador_tpInsc");
                ideEmpregador_nrInsc = consulta.getString("ideEmpregador_nrInsc");
            }
            String sql = " Select Codigo ideFuncao_codFuncao, Descricao dadosFuncao_dscFuncao, CBO dadosFuncao_codCBO, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1040' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1040' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1040' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Cargos "
                    + " WHERE cargo in (Select cargo from funcion where situacao <> 'D') "
                    + " ORDER BY sucesso asc, Cargos.Codigo asc ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.select();
            List<S1040> retorno = new ArrayList<>();
            S1040 s1040;
            while (consulta.Proximo()) {
                s1040 = new S1040();
                s1040.setSucesso(consulta.getInt("sucesso"));
                s1040.setIdeEvento_procEmi("1");
                s1040.setIdeEvento_verProc("Satellite eSocial");
                s1040.setIdeEmpregador_tpInsc(ideEmpregador_tpInsc);
                s1040.setIdeEmpregador_nrInsc(ideEmpregador_nrInsc);
                s1040.setIdeFuncao_codFuncao(consulta.getString("ideFuncao_codFuncao"));
                s1040.setDadosFuncao_dscFuncao(consulta.getString("dadosFuncao_dscFuncao"));
                s1040.setDadosFuncao_codCBO(consulta.getString("dadosFuncao_codCBO"));
                retorno.add(s1040);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1040Dao.get - " + e.getMessage() + "\r\n"
                    + " Select Codigo ideFuncao_codFuncao, Descricao dadosFuncao_dscFuncao, CBO dadosFuncao_codCBO, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "         and z.evento = 'S-1040' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "         and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "         and z.evento = 'S-1040' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "         and z.evento = 'S-1040' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Cargos "
                    + " WHERE cargo in (Select cargo from funcion where situacao <> 'D') "
                    + " ORDER BY sucesso asc, Cargos.Codigo asc ");
        }
    }
}

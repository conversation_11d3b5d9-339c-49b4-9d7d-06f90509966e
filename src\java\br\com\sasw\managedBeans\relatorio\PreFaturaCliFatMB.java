/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.relatorio;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.Rt_Perc;
import SasBeans.SasPWFill;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;

/**
 *
 * <AUTHOR>
 */
@Named(value = "prefaturaCliFat")
@ViewScoped
public class PreFaturaCliFatMB implements Serializable {

    private Persistencia persistencia;
    private List<Rt_Perc> trajetos;
    private BigDecimal codpessoa;
    private final String banco;
    private String codFil, dataTela, caminho, log, portal, data1, data2;
    private ArquivoLog logerro;
    private Date ultimoDia, dataSelecionada1, dataSelecionada2;

    private SasPWFill filial;
    private final RotasSatWeb rotassatweb;
    private Filiais filiais;

    public PreFaturaCliFatMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");

        dataTela = getDataAtual("SQL");

        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        dataSelecionada2 = c.getTime();
        data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        c.add(Calendar.DAY_OF_YEAR, -1);
        dataSelecionada1 = c.getTime();
        data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        ultimoDiadoMes();
        rotassatweb = new RotasSatWeb();

        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();

        portal = "N";
    }

    /**
     * Procura o último dia do mês atual
     */
    public void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            this.ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.codFil = (FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("filial")).toString();

            this.filiais = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);

            preFatura();

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void detalhesRotasData() {
        try {
            this.trajetos = this.rotassatweb.detalhesRotasData(this.dataTela, this.codFil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preFatura() {
        try {
            this.trajetos = this.rotassatweb.preFaturaCliFat(this.data1, this.data2, this.codFil, this.codpessoa, this.portal, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarData() {
        try {
            this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (this.dataSelecionada1.after(this.dataSelecionada2)) {
                throw new Exception("IntervaloInvalido");
            }
            preFatura();
            PrimeFaces.current().ajax().update("msgs", "main", "cabecalho", "panelCals", "totais");
            PrimeFaces.current().executeScript("PF('oCalendarios').hide()");
        } catch (Exception e) {

        }

    }

    public void dataAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            preFatura();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void impressaoAssinatura() {

    }

    public void dataPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            preFatura();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Rt_Perc> getTrajetos() {
        return trajetos;
    }

    public void setTrajetos(List<Rt_Perc> trajetos) {
        this.trajetos = trajetos;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public String getPortal() {
        return portal;
    }

    public void setPortal(String portal) {
        this.portal = portal;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataSelecionada2() {
        return dataSelecionada2;
    }

    public void setDataSelecionada2(Date dataSelecionada2) {
        this.dataSelecionada2 = dataSelecionada2;
    }
}

package Controller.TbVal;

import Dados.Persistencia;
import SasBeans.TbVal;
import SasDaos.TbValDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ControlerTbVal {

    /**
     * Listagem de Questões de supervisão
     *
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TbVal> Listagem312(Persistencia persistencia) throws Exception {
        try {
            TbValDao tbvaldao = new TbValDao();
            List<TbVal> retorno = new ArrayList();
            retorno = tbvaldao.getTbVal312(persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("questoes.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Inserir nova pergunta no questionário
     *
     * @param tbval - objeto tbval dados importantes - descrição e operador
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void Inserir312(TbVal tbval, Persistencia persistencia) throws Exception {
        try {
            TbValDao tbvaldao = new TbValDao();
            TbVal val = tbval;
            Integer seq;
            val.setTabela(312);
            seq = tbvaldao.getCodTBVal("312", persistencia);
            val.setDt_Alter(DataAtual.getDataAtual("SQL"));
            val.setHr_Alter(DataAtual.getDataAtual("HORA"));
            val.setCodigo(seq);
            val = (TbVal) FuncoesString.removeAcentoObjeto(val);
            tbvaldao.gravaTbVal(val, persistencia);
        } catch (Exception e) {
            throw new Exception("questoes.falhagera<message>" + e.getMessage());
        }
    }

    public void Atualizar312(TbVal tbval, Persistencia persistencia) throws Exception {
        try {
            TbValDao tbvaldao = new TbValDao();
            TbVal val = tbval;
            val.setTabela(312);
            val.setDt_Alter(DataAtual.getDataAtual("SQL"));
            val.setHr_Alter(DataAtual.getDataAtual("HORA"));
            val = (TbVal) FuncoesString.removeAcentoObjeto(val);
            tbvaldao.atualizaTbVal(val, persistencia);
        } catch (Exception e) {
            throw new Exception("questoes.falhagera<message>" + e.getMessage());
        }
    }

    public void Excluir312(TbVal tbval, Persistencia persistencia) throws Exception {
        try {
            TbValDao tbvaldao = new TbValDao();
            TbVal val = tbval;
            val.setTabela(312);
            tbvaldao.excluirTbVal(val, persistencia);
        } catch (Exception e) {
            throw new Exception("questoes.falhagera<message>" + e.getMessage());
        }
    }

    /**
     * Listagem de uma tabela
     *
     * @param tabela
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TbVal> Listagem(int tabela, Persistencia persistencia) throws Exception {
        try {
            TbValDao tbvaldao = new TbValDao();
            List<TbVal> retorno = tbvaldao.retornaLista(tabela, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("questoes.falhageral<message>" + e.getMessage());
        }
    }

    public int getMaxCodigoDeTabela(int tabela, Persistencia persistencia) throws Exception {
        try {
            TbValDao tbvaldao = new TbValDao();
            return tbvaldao.getCodTBVal(tabela, persistencia);
        } catch (Exception e) {
            throw new Exception("ControlerTbVal.getMaxCodigoDeTabela<message>" + e.getMessage());
        }
    }

    /**
     * Inserir nova pergunta no questionário
     *
     * @param tbval - objeto tbval dados importantes - descrição e operador
     * @param persistencia - conexão ao banco de dados
     * @return successo da operação
     * @throws Exception
     */
    public boolean inserir(TbVal tbval, Persistencia persistencia) throws Exception {
        try {
            TbValDao tbvaldao = new TbValDao();
            tbval.setDt_Alter(DataAtual.getDataAtual("SQL"));
            tbval.setHr_Alter(DataAtual.getDataAtual("HORA"));
            tbval = (TbVal) FuncoesString.removeAcentoObjeto(tbval);
            boolean success = tbvaldao.gravaTbVal(tbval, persistencia);
            return success;
        } catch (Exception e) {
            throw new Exception("ControlerTbVal.falhagera<message>" + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2240;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2240Dao {

    private String matrConsulta;

    public List<S2240> get(List<S2240> funcions, String codFil, String compet, String ambiente, String tipo, Persistencia persistencia) throws Exception {
        try {
            List<S2240> retorno = new ArrayList<>();
            S2240 s2240;

            String sql = " Select (Select top 1 substring(xml_retorno,\n"
                    + "                        charindex('<nrRecibo>',xml_retorno)+10, \n"
                    + "                        charindex('</nrRecibo>',xml_retorno) - charindex('<nrRecibo>',xml_retorno)-10) "
                    + "                             vinculo_nrRecInfPrelim \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where (z.Identificador = Convert(BigInt,Funcion.CPF) \n"
                    + "                              or z.Identificador = Convert(BigInt,Funcion.Matr)) \n"
                    + "                                 and z.evento = 'S-2240' \n"
                    + "                                 and z.CodFil = ? \n"
                    + "                                 and z.Compet = ? \n"
                    + "                                 and z.Ambiente = ? \n"
                    + "                                 and len(Xml_Retorno) > 0 \n"
                    + "                                 and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') "
                    + " Order by Sequencia desc) vinculo_nrRecInfPrelim,"
                    + " (Select top 01 substring(xml_retorno,  charindex('<nrRecibo>',xml_retorno)+10, "
                    + "    charindex('</nrRecibo>',xml_retorno) - charindex('<nrRecibo>',xml_retorno)-10) ideEvento_nrRecibo "
                    + "    From XmleSocial z "
                    + "    where (z.Identificador = Funcion.CPF) "
                    + "                     and z.evento = 'S-2240'"
                    + "                     and z.CodFil = ? "
                    //+ "                     and z.Compet = ? "
                    + "                     and z.Ambiente = ? "
                    + "                     and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') "
                    + " Order by Sequencia Desc) ideEvento_nrRecibo, "
                    + " funcion.PgCtSin ,RHHorario.tipocomp, RHHorario.chcomp, Funcion.CPF trabalhador_cpfTrab, "
                    + " Funcion.PIS trabalhador_nisTrab, Funcion.Nome trabalhador_nmTrab, Funcion.Nacionalid,  "
                    + " Funcion.Sexo trabalhador_sexo, Funcion.Raca trabalhador_racaCor,  Funcion.EstCivil trabalhador_estCiv, "
                    + " Funcion.Instrucao trabalhador_grauInstr, Funcion.TipoAdm trabalhador_indPriEmpr, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto, "
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie CTPS_serieCtps, "
                    + " Funcion.CTPS_UF CTPS_ufCtps, Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor, "
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped, "
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid, "
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd, "
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, Municipios.CodIBGE brasil_codMunic, "
                    + " Funcion.UF brasil_uf, Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, Funcion.Email contato_emailPrinc, "
                    + " Convert(BigInt, Funcion.Matr) vinculo_matricula, substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoCeletista_dtAdm, "
                    + " Case when Len(Funcion.CodPonto) > 0 then CONVERT(VARCHAR, Funcion.CodPonto) else CONVERT(VARCHAR, Funcion.Matr) end ideTrabalhador_matrCodPonto,"
                    + " Funcion.Matr,"
                    + " 1 LocalAmb," 
                    + " Fornec.CNPJ infoCeletista_cnpjSindCategProf, Funcion.Salario remuneracao_vrSalFx,  Funcion.FormaPgto remuneracao_undSalFixo, "
                    + " Filiais.CNPJ localTrabGeral_nrInsc, convert(int, Funcion.Cargo) infoContrato_codCargo,  RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, "
                    + " RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, RHEscala.HrSabNot, RHEscala.HrDomDiu, "
                    + " RHEscala.HrDomNot, Convert(bigint,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, RHHorario.D1, "
                    + " RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, Funcion.Situacao, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Demis,111),'/','-'),0,11) desligamento_dtDesligamento, "
                    + " Case when Funcion.TipoADM = 70 then 2 else 1 end infoCeletista_tpAdmissao,  "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc,"
                    + " FuncionAdic.EstClassTrab, FuncionAdic.EstCasadoBr, FuncionAdic.EstFilhosBr, substring(replace(convert(varchar,FuncionAdic.EstDtChegada,111),'/','-'),0,11) EstDtChegada, "
                    + " Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                       (Select Max(CodIBGE) From Municipios where Nome = Substring(Naturalid,1,(CharIndex('/',Naturalid)-1)) "
                    + "                                                         and UF = Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) and Len(Naturalid) > 0) "
                    + "                    else '0' end nascimento_codMunic, "
                    + "                    Case when (CharIndex('/',Naturalid) > 1) then"
                    + "                    (Select top 1 Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) UF from Funcion where Matr = Funcion.Matr) "
                    + " else '0' end nascimento_uf, "
                    + " Case when Funcion.vinculo in ('J','M') then '103' "
                    + "      when Funcion.vinculo = 'D' then '721' "
                    + "      when Funcion.Vinculo = 'S' then '723' "
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + "      when Funcion.TrabIntermitente = 'S' then '111' "
                    + "      else '101' end infoContrato_codCateg, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select top 1 case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where (z.Identificador = Funcion.CPF) "
                    + "             and z.evento = 'S-2240' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select top 1 case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where (z.Identificador = Funcion.CPF) "
                    + "             and z.evento = 'S-2240' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select top 1 case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where (z.Identificador = Funcion.CPF) "
                    + "             and z.evento = 'S-2240' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso "
                    + " from Funcion"
                    + " Left Join FPMensal  on Funcion.Matr = FPMensal.Matr "
                    + "       and FPMensal.CodMovFp = (Select top 1 substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                       from FPPeriodos where DtInicio = '" + compet.replace("-", "") + "01') "
                    + " Left Join Sindicatos  on Sindicatos.Codigo = FPMensal.Sindicato "
                    + " Left Join PstServ  on PstServ.Secao  = FPMensal.Secao "
                    + "                   and PstServ.CodFil = FPMensal.CodFil "
                    + " Left Join Clientes  on Clientes.Codigo = PstServ.CodCli "
                    + "                    and Clientes.CodFil = PstServ.CodFil "
                    + " Left Join RHEscala  on RHEscala.Codigo = FPMEnsal.Escala "
                    + "                    and RHEscala.CodFil = FPMEnsal.CodFil "
                    + " Left Join RHHorario  on  RHHorario.Codigo    = FPMensal.Horario "
                    + "                      and RHHorario.CodFil    = FPMensal.CodFil "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade "
                    + "                     and Municipios.UF   = Funcion.UF"
                    + " Left Join Fornec on Fornec.Codigo = Sindicatos.CodForn"
                    + " Left Join FuncionAdic  on FuncionAdic.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' "
                    + " and Funcion.codfil = ? "
                    + " and Funcion.Matr IN (Select FPMensal.Matr from FPmensal \n"
                    + "Left Join PStServ on PStServ.Secao = FPmensal.Secao\n"
                    + "                 and PstServ.CodFil = FPMensal.Codfil\n"
                    + "Left Join PstServTpAmb on PstServTpAmb.Codigo = PstServ.TpAmb\n"
                    + "where FPMensal.CodMovFP = " + compet.replace("-", "").replace("20", "")
                    + "  and FPmensal.TipoFP = 'MEN'\n"
                    + "  and PstServ.TpAmb <> ''\n"
                    + "  and PstServ.TpAmb is not null\n"
                    //+ "  and PstServTpAmb.CodAgNoc <> '09.01.001'\n" Vai qualquer ambiente - Carlos 13/01/2023
                    + "  and PstServTpAmb.CodAgNoc is not null \n" + ")";
//            if (null != funcions && funcions.size() == 1) {
//                sql += " and Funcion.matr = ? ";
//            } else if (null != funcions && funcions.size() > 0) {
//                sql += " and (Funcion.matr = ? ";
//                for (int i = 1; i < funcions.size(); i++) {
//                    sql += " OR Funcion.matr = ? ";
//                }
//                sql += ")";
//            }
            sql += " and FPMensal.situacao <> 'D' "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            //consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
//            if (null != funcions) {
//                for (S2240 funcion : funcions) {
//                    consulta.setString(funcion.getIdeVinculo_matr());
//                }
//            }

            consulta.select();

            if (null == tipo || !tipo.equals("")) {
                tipo = "1";
            }

            while (consulta.Proximo()) {
                s2240 = new S2240();

                s2240.setSucesso(consulta.getInt("sucesso"));
                s2240.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2240.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));

                s2240.setIdeEvento_procEmi("1");
                s2240.setIdeEvento_verProc("Satellite eSocial");

                s2240.setIdeEvento_indRetif(tipo);
                s2240.setIdeEvento_nrRecibo(tipo.equals("2") ? consulta.getString("ideEvento_nrRecibo") : "");
                s2240.setIdeEvento_tpAmb(ambiente);

                s2240.setIdeVinculo_codCateg(consulta.getString("infoContrato_codCateg"));
                s2240.setIdeVinculo_cpfTrab(consulta.getString("trabalhador_cpfTrab"));
                s2240.setIdeVinculo_matricula(consulta.getString("ideTrabalhador_matrCodPonto"));
                s2240.setIdeVinculo_matr(consulta.getString("Matr"));

                retorno.add(s2240);
            }

            consulta.close();

            // Matriculas para consultas
            String MatriculasConsulta = "";

            for (S2240 item : retorno) {
                if (!MatriculasConsulta.equals("")) {
                    MatriculasConsulta += ",";
                }

                MatriculasConsulta += item.getIdeVinculo_matr().replace(".0", "");
            }

            sql = "Select PstServ.Local, PstServTpAmb.*, Fornec.Fantasia, Fornec.CPF, FPmensal.Matr, Funcion.CodPonto, Clientes.CGC from FPmensal \n"
                    + "Left Join PStServ on PStServ.Secao = FPmensal.Secao \n"
                    + "                 and PstServ.CodFil = FPMensal.Codfil \n"
                    + "Left Join Clientes on Clientes.Codigo = PstServ.CodCli \n"
                    + "                  and Clientes.CodFil = PstServ.CodFil \n"
                    + "Left Join PstServTpAmb on PstServTpAmb.Codigo = PstServ.TpAmb \n"
                    + "Left Join Fornec on Fornec.Codigo = PstServTpAmb.CodFornec \n"
                    + "Left Join Funcion on Funcion.Matr = Fpmensal.Matr \n"

                    + "where FPMensal.CodMovFP = "+compet.replace("-", "").replace("20","")
                    + "  and FPmensal.TipoFP = 'MEN'\n"
                    + "  and PstServ.TpAmb <> ''\n"
                    + "  and PstServ.TpAmb is not null \n"
                    //+ "  and PstServTpAmb.CodAgNoc <> '09.01.001'\n"
                    + "  and PstServTpAmb.CodAgNoc is not null \n";
            if (!MatriculasConsulta.equals("")) {
                sql = sql
                        + "AND   Funcion.Matr IN(" + MatriculasConsulta + ")";
            }
            consulta = new Consulta(sql, persistencia);
            consulta.select();

            matrConsulta = "";

            while (consulta.Proximo()) {
                matrConsulta = consulta.getString("matr");
                
                if (null != consulta.getString("LocalAmb") && !consulta.getString("LocalAmb").equals("")) {
                    retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_localAmb(consulta.getInt("LocalAmb"));
                }
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_dtIniCondicao(consulta.getString("Dt_alter").substring(0,10));                                
                
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_dscSetor(consulta.getString("Descricao"));               
                
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_dscAtivDes(consulta.getString("DscAtivDes"));               
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_codAgNoc(consulta.getString("CodAgNoc"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_dscAgNoc(consulta.getString("DscAgNoc"));
                
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_tpAval(consulta.getInt("TpAval"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_intConc(consulta.getInt("IntConc"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_limTol(consulta.getInt("LimTol"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_unMed(consulta.getInt("UnMed"));

                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_tecMedicao(consulta.getString("TecMed"));
                
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_utilizEPC(consulta.getInt("UtilizEPC"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_eficEpc(consulta.getString("EficEPC"));
                
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_utilizEPI(consulta.getInt("UtilizEPI"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_eficEpi(consulta.getString("EficEPI"));
                
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_docAval(consulta.getString("DocAval"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_dscEPI(consulta.getString("DscEPI"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_medProtecao(consulta.getString("MedProtecao"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_condFuncto(consulta.getString("CondFuncto"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_usoInint(consulta.getString("UsoInInt"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_przValid(consulta.getString("PrzValid"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_periodicTroca(consulta.getString("PeriodicTroca"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_higienizacao(consulta.getString("higienizacao"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_cpfResp(consulta.getString("CPF"));
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_nrInsc(consulta.getString("CGC"));  
                int valor = 0;;
                if (!"".equals(consulta.getString("DSCOC"))) {
                    valor = consulta.getInt("DSCOC");
                }
                retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_ideOC(valor);
               if (consulta.getString("DSCOC").equals(1)){
                   retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_desOC("CRM");
               }else{
                   retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_desOC("CREA");
               }
               retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_nrOC(consulta.getString("NrOC"));
               retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_ufOC(consulta.getString("UFOC"));
               if (!consulta.getString("Obs").equals("")){
                   retorno.stream().filter(e -> e.getIdeVinculo_matr().equals(matrConsulta)).findFirst().get().setInfoExpRisco_obsCompl(consulta.getString("OBS"));
               }
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception();
        }
    }
}

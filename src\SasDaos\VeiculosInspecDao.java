/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.VeiculosInspec;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class VeiculosInspecDao {

    public List<VeiculosInspec> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<VeiculosInspec> retorno = new ArrayList<>();
            String sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY sequencia desc) AS RowNum, pessoa.nome, VeiculosInspec.* "
                    + " FROM VeiculosInspec "
                    + " left join pessoa on pessoa.codigo = VeiculosInspec.codPessoaInspec"
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql += " codPessoaInspec IS NOT null) AS RowConstrainedResult "
                    + " WHERE RowNum >= ? AND RowNum < ? "
                    + " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            VeiculosInspec veiculosInspec;
            while (consulta.Proximo()) {
                veiculosInspec = new VeiculosInspec();
                veiculosInspec.setNome(consulta.getString("nome"));
                veiculosInspec.setSequencia(consulta.getString("Sequencia"));
                veiculosInspec.setNumero(consulta.getString("Numero"));
                veiculosInspec.setData(consulta.getString("Data"));
                veiculosInspec.setHora(consulta.getString("Hora"));
                veiculosInspec.setMarcaModelo(consulta.getString("MarcaModelo"));
                veiculosInspec.setKMIni(consulta.getString("KMIni"));
                veiculosInspec.setImgKMIni(consulta.getString("imgKMIni"));
                veiculosInspec.setKMFim(consulta.getString("KMFim"));
                veiculosInspec.setImgKMFim(consulta.getString("imgKMFim"));
                veiculosInspec.setCombustivel(consulta.getString("Combustivel"));
                veiculosInspec.setImgCombustivel(consulta.getString("imgCombustivel"));
                veiculosInspec.setProblemaLuzes(consulta.getString("ProblemaLuzes"));
                veiculosInspec.setRelatoDanos(consulta.getString("RelatoDanos"));
                veiculosInspec.setImgRelatoDanos(consulta.getString("imgRelatoDanos"));
                veiculosInspec.setRelatoProblemas(consulta.getString("RelatoProblemas"));
                veiculosInspec.setOperador(consulta.getString("Operador"));
                veiculosInspec.setDt_Alter(consulta.getString("Dt_Alter"));
                veiculosInspec.setHr_Alter(consulta.getString("Hr_Alter"));
                veiculosInspec.setCodPessoaInspec(consulta.getString("CodPessoaInspec"));
                retorno.add(veiculosInspec);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("VeiculosInspecDao.getInspecoesData - " + e.getMessage() + "\r\n"
                    + " Select * from VeiculosInspec "
                    + " where data = ? ");
        }
    }

    /**
     * Lista todas as inspeções por data. Se codPessoaInspec = "", mostra de
     * todas as pessoas.
     *
     * @param data
     * @param codPessoaInspec
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<VeiculosInspec> getInspecoesData(String data, String codPessoaInspec, Persistencia persistencia) throws Exception {
        try {
            List<VeiculosInspec> retorno = new ArrayList<>();
            String sql = " Select * from VeiculosInspec "
                    + " where data = ? ";
            if (!codPessoaInspec.equals("")) {
                sql += " and CodPessoaInspec = ? ";
            }
            sql += " order by data desc, hora desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            if (!codPessoaInspec.equals("")) {
                consulta.setString(codPessoaInspec);
            }
            consulta.select();
            VeiculosInspec veiculosInspec;
            while (consulta.Proximo()) {
                veiculosInspec = new VeiculosInspec();
                veiculosInspec.setSequencia(consulta.getString("Sequencia"));
                veiculosInspec.setNumero(consulta.getString("Numero"));
                veiculosInspec.setData(consulta.getString("Data"));
                veiculosInspec.setHora(consulta.getString("Hora"));
                veiculosInspec.setMarcaModelo(consulta.getString("MarcaModelo"));
                veiculosInspec.setKMIni(consulta.getString("KMIni"));
                veiculosInspec.setImgKMIni(consulta.getString("imgKMIni"));
                veiculosInspec.setKMFim(consulta.getString("KMFim"));
                veiculosInspec.setImgKMFim(consulta.getString("imgKMFim"));
                veiculosInspec.setCombustivel(consulta.getString("Combustivel"));
                veiculosInspec.setImgCombustivel(consulta.getString("imgCombustivel"));
                veiculosInspec.setProblemaLuzes(consulta.getString("ProblemaLuzes"));
                veiculosInspec.setRelatoDanos(consulta.getString("RelatoDanos"));
                veiculosInspec.setImgRelatoDanos(consulta.getString("imgRelatoDanos"));
                veiculosInspec.setRelatoProblemas(consulta.getString("RelatoProblemas"));
                veiculosInspec.setOperador(consulta.getString("Operador"));
                veiculosInspec.setDt_Alter(consulta.getString("Dt_Alter"));
                veiculosInspec.setHr_Alter(consulta.getString("Hr_Alter"));
                veiculosInspec.setCodPessoaInspec(consulta.getString("CodPessoaInspec"));
                retorno.add(veiculosInspec);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("VeiculosInspecDao.getInspecoesData - " + e.getMessage() + "\r\n"
                    + " Select * from VeiculosInspec "
                    + " where data = ? "
                    + (!codPessoaInspec.equals("") ? " and CodPessoaInspec = " + codPessoaInspec : ""));
        }
    }

    /**
     * Seleciona a próxima sequencia disponível para inspeção
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getSequencia(Persistencia persistencia) throws Exception {
        try {
            String retorno = "0";
            String sql = "select isnull(max(sequencia),0)+1 sequencia from veiculosInspec";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getString("sequencia");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("VeiculosInspecDao.getSequencia - " + e.getMessage() + "\r\n"
                    + "select isnull(max(sequencia),0)+1 sequencia from veiculosInspec");
        }
    }

    /**
     * Tenta inserir a inspeção e retorna a sequencia gerada
     *
     * @param veiculosInspec
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String insereInspecao(VeiculosInspec veiculosInspec, Persistencia persistencia) throws Exception {
        int cont = 1;
        String erro = "";
        while (true) {
            try {
                String sequencia = "0";
                String sqlSequencia = "select isnull(max(sequencia),0)+1 sequencia from veiculosInspec";
                Consulta consulta = new Consulta(sqlSequencia, persistencia);
                consulta.select();
                while (consulta.Proximo()) {
                    sequencia = consulta.getString("sequencia");
                }
                consulta.Close();

                String sql = " INSERT INTO veiculosInspec (Sequencia, Numero, Data, Hora, MarcaModelo, KMIni, imgKMIni, KMFim, imgKMFim, "
                        + " Combustivel, imgCombustivel, ProblemaLuzes, RelatoDanos, imgRelatoDanos, RelatoProblemas, "
                        + " Operador, Dt_Alter, Hr_Alter, CodPessoaInspec) "
                        + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
                Consulta pconsulta = new Consulta(sql, persistencia);
                pconsulta.setString(sequencia);
                pconsulta.setString(veiculosInspec.getNumero());
                pconsulta.setString(veiculosInspec.getData());
                pconsulta.setString(veiculosInspec.getHora());
                pconsulta.setString(veiculosInspec.getMarcaModelo());
                pconsulta.setString(veiculosInspec.getKMIni());
                pconsulta.setString(veiculosInspec.getImgKMIni());
                pconsulta.setString(veiculosInspec.getKMFim());
                pconsulta.setString(veiculosInspec.getImgKMFim());
                pconsulta.setString(veiculosInspec.getCombustivel());
                pconsulta.setString(veiculosInspec.getImgCombustivel());
                pconsulta.setBigDecimal(veiculosInspec.getProblemaLuzes());
                pconsulta.setString(veiculosInspec.getRelatoDanos());
                pconsulta.setString(veiculosInspec.getImgRelatoDanos());
                pconsulta.setString(veiculosInspec.getRelatoProblemas());
                pconsulta.setString(veiculosInspec.getOperador());
                pconsulta.setString(veiculosInspec.getDt_Alter());
                pconsulta.setString(veiculosInspec.getHr_Alter());
                pconsulta.setString(veiculosInspec.getCodPessoaInspec());
                pconsulta.insert();
                pconsulta.close();
                return sequencia;
            } catch (Exception e) {
                erro = e.getMessage();
                cont++;
            }
            if (cont == 20) {
                throw new Exception("VeiculosInspecDao.insereInspecao - " + erro);
            }
        }
    }

    /**
     * Atualiza uma inspeção.
     *
     * @param veiculosInspec
     * @param persistencia
     */
    public void updateVeiculosInspec(VeiculosInspec veiculosInspec, Persistencia persistencia) throws Exception {
        try {
            String sql = "update veiculosInspec set Numero = ?, MarcaModelo = ?, KMIni = ?, KMFim = ?, Combustivel = ?,"
                    + " ProblemaLuzes = ?, RelatoDanos = ?, RelatoProblemas = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ?,"
                    + " CodPessoaInspec = ? ";
            if (!veiculosInspec.getImgKMIni().equals("")) {
                sql += ", imgKMIni = ? ";
            }
            if (!veiculosInspec.getImgKMFim().equals("")) {
                sql += ", imgKMFim = ? ";
            }
            if (!veiculosInspec.getImgCombustivel().equals("")) {
                sql += ", imgCombustivel = ? ";
            }
            if (!veiculosInspec.getImgRelatoDanos().equals("")) {
                sql += ", imgRelatoDanos = ? ";
            }
            sql += " WHERE sequencia = ? ";

            Consulta pconsulta = new Consulta(sql, persistencia);
            pconsulta.setString(veiculosInspec.getNumero());
            pconsulta.setString(veiculosInspec.getMarcaModelo());
            pconsulta.setString(veiculosInspec.getKMIni());
            pconsulta.setString(veiculosInspec.getKMFim());
            pconsulta.setString(veiculosInspec.getCombustivel());
            pconsulta.setBigDecimal(veiculosInspec.getProblemaLuzes());
            pconsulta.setString(veiculosInspec.getRelatoDanos());
            pconsulta.setString(veiculosInspec.getRelatoProblemas());
            pconsulta.setString(veiculosInspec.getOperador());
            pconsulta.setString(veiculosInspec.getDt_Alter());
            pconsulta.setString(veiculosInspec.getHr_Alter());
            pconsulta.setString(veiculosInspec.getCodPessoaInspec());

            if (!veiculosInspec.getImgKMIni().equals("")) {
                pconsulta.setString(veiculosInspec.getImgKMIni());
            }
            if (!veiculosInspec.getImgKMFim().equals("")) {
                pconsulta.setString(veiculosInspec.getImgKMFim());
            }
            if (!veiculosInspec.getImgCombustivel().equals("")) {
                pconsulta.setString(veiculosInspec.getImgCombustivel());
            }
            if (!veiculosInspec.getImgRelatoDanos().equals("")) {
                pconsulta.setString(veiculosInspec.getImgRelatoDanos());
            }

            pconsulta.setString(veiculosInspec.getSequencia());
            pconsulta.update();
            pconsulta.close();
        } catch (Exception e) {
            throw new Exception("VeiculosInspecDao.updateVeiculosInspec - " + e.getMessage());
        }
    }
}

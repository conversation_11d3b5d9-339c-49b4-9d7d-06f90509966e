/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class DashBoard {

    private String Dia;
    private String QdeGuias;
    private String QdeRotas;
    private String QdeParadas;
    private BigDecimal TotalValorTransportado;
    private String TotalKmTransportado;
    private String Rota;

    public DashBoard() {
        this.Dia = "";
        this.QdeGuias = "";
        this.QdeRotas = "";
        this.QdeParadas = "";
        this.TotalValorTransportado = BigDecimal.ZERO;
        this.TotalKmTransportado = "";
        this.Rota = "";
    }

    public String getDia() {
        return Dia;
    }

    public void setDia(String Dia) {
        this.Dia = Dia;
    }

    public String getQdeGuias() {
        return QdeGuias;
    }

    public void setQdeGuias(String QdeGuias) {
        this.QdeGuias = QdeGuias;
    }

    public String getQdeRotas() {
        return QdeRotas;
    }

    public void setQdeRotas(String QdeRotas) {
        this.QdeRotas = QdeRotas;
    }

    public String getQdeParadas() {
        return QdeParadas;
    }

    public void setQdeParadas(String QdeParadas) {
        this.QdeParadas = QdeParadas;
    }

    public BigDecimal getTotalValorTransportado() {
        return TotalValorTransportado;
    }

    public void setTotalValorTransportado(BigDecimal TotalValorTransportado) {
        this.TotalValorTransportado = TotalValorTransportado;
    }

    public String getTotalKmTransportado() {
        return TotalKmTransportado;
    }

    public void setTotalKmTransportado(String TotalKmTransportado) {
        this.TotalKmTransportado = TotalKmTransportado;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

}

/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Contatos.ContatoSatMobWeb;
import Dados.Persistencia;
import SasBeans.Contatos;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ContatosLazyList extends LazyDataModel<Contatos> {

    private static final long serialVersionUID = 1L;
    private List<Contatos> contatos;
    private final ContatoSatMobWeb contatosmobweb;
    private Persistencia persistencia;

    public ContatosLazyList(Persistencia pst) {
        this.contatosmobweb = new ContatoSatMobWeb();
        this.persistencia = pst;
    }

    @Override
    public List<Contatos> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.contatos = this.contatosmobweb.listagemPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.contatosmobweb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.contatos;
    }

    @Override
    public Object getRowKey(Contatos contato) {
        return contato.getCodigo();
    }

    @Override
    public Contatos getRowData(String codigo) {
        for (Contatos contato : this.contatos) {
            if (contato.getCodigo().equals(new BigDecimal(codigo))) {
                return contato;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

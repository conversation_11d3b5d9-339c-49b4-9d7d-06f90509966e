/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 21-Aug-2018, 09:40:06
    Author     : SASWRichard
*/


.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.cadastrar{
    width: 90vh;
    min-width: 100%;
}

.calendario .ui-inputfield{
    width: 100% !important;
}

.datepicker .ui-timepicker-div .ui-state-active,
.datepicker .ui-timepicker-div .ui-state-highlight,
.calendario .ui-timepicker-div .ui-state-active,
.calendario .ui-timepicker-div .ui-state-highlight{
    color: #333333;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
    cursor: pointer;
    background-color: #f5f5f5;
    *background-color: #e6e6e6;
    background-image: -ms-linear-gradient(top, #ffffff, #e6e6e6);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e6e6e6));
    background-image: -webkit-linear-gradient(top, #ffffff, #e6e6e6);
    background-image: -o-linear-gradient(top, #ffffff, #e6e6e6);
    background-image: linear-gradient(top, #ffffff, #e6e6e6);
    background-image: -moz-linear-gradient(top, #ffffff, #e6e6e6);
    background-repeat: repeat-x;
    border: 1px solid #cccccc;
    border-top-color: rgb(204, 204, 204);
    border-right-color: rgb(204, 204, 204);
    border-bottom-color: rgb(204, 204, 204);
    border-left-color: rgb(204, 204, 204);
    *border: 0;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    border-color: #e6e6e6 #e6e6e6 #bfbfbf;
    border-bottom-color: #b3b3b3;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffffff', endColorstr='#e6e6e6', GradientType=0);
    filter: progid:dximagetransform.microsoft.gradient(enabled=false);
    *zoom: 1;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ui-datepicker-current-day .ui-state-default, 
.ui-datepicker-current-day .ui-state-highlight{
    background-color: #d72525 !important;
    color: white !important;
    color: #ffffff;
    text-decoration: none;
    background-position: 0px;
    background-image: -moz-linear-gradient(top, #d72525, #d72525);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#d72525), to(#d72525));
    background-image: -webkit-linear-gradient(top, #d72525, #d72525);
    background-image: -o-linear-gradient(top, #d72525, #d72525);
    background-image: linear-gradient(to bottom, #d72525, #d72525);
    background-repeat: repeat-x;
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffd72525', endColorstr='#ffd72525', GradientType=0);
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight{
    background-color: #5d1313 !important;
    color: white !important;
    color: #ffffff;
    text-decoration: none;
    background-position: 0px;
    background-image: -moz-linear-gradient(top, #d72525, #5d1313);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#d72525), to(#5d1313));
    background-image: -webkit-linear-gradient(top, #d72525, #5d1313);
    background-image: -o-linear-gradient(top, #d72525, #5d1313);
    background-image: linear-gradient(to bottom, #d72525, #5d1313);
    background-repeat: repeat-x;
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffd72525', endColorstr='#ff5d1313', GradientType=0);
}

.ui-slider {
    position: relative;
    text-align: left;
    background: #d72525;
    border: none;
    -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.6) inset;
    -moz-box-shadow: 0 1px 3px rgba(0,0,0,0.6) inset;
    box-shadow: 0 1px 3px rgba(0,0,0,0.6) inset;
}

.ui-dialog.ui-widget-content .ui-dialog-title {
    font-size: 24px;
    width: 90%;
}

.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary{
    float: right;
    color: #ffffff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #3ecc00;
    *background-color: #39bd00;
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#39bd00), to(#3ecc00));
    background-image: -webkit-linear-gradient(top, #39bd00, #3ecc00);
    background-image: -o-linear-gradient(top, #39bd00, #3ecc00);
    background-image: linear-gradient(to bottom, #39bd00, #3ecc00);
    background-image: -moz-linear-gradient(top, #39bd00, #3ecc00);
    background-repeat: repeat-x;
    border-color: #0044cc #0044cc #002a80;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ff39bd00', endColorstr='#ff3ecc00', GradientType=0);
    filter: progid:dximagetransform.microsoft.gradient(enabled=false);
}

.ui-priority-primary:hover{
    color: #ffffff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #39bd00;
    *background-color: #39bd00;
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#39bd00), to(#39bd00));
    background-image: -webkit-linear-gradient(top, #39bd00, #39bd00);
    background-image: -o-linear-gradient(top, #39bd00, #39bd00);
    background-image: linear-gradient(to bottom, #39bd00, #39bd00);
    background-image: -moz-linear-gradient(top, #39bd00, #39bd00);
    background-repeat: repeat-x;
    border-color: #0044cc #0044cc #002a80;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ff39bd00', endColorstr='#ff39bd00', GradientType=0);
    filter: progid:dximagetransform.microsoft.gradient(enabled=false);
}

.ui-priority-secundary, .ui-widget-content .ui-priority-secundary, .ui-widget-header .ui-priority-secundary{
    float: right;
    color: #ffffff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #a20000;
    *background-color: #c20000;
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#a20000), to(#c20000));
    background-image: -webkit-linear-gradient(top, #a20000, #c20000);
    background-image: -o-linear-gradient(top, #a20000, #c20000);
    background-image: linear-gradient(to bottom, #a20000, #c20000);
    background-image: -moz-linear-gradient(top, #a20000, #c20000);
    background-repeat: repeat-x;
    border-color: #0044cc #0044cc #002a80;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffa20000', endColorstr='#ffc20000', GradientType=0);
    filter: progid:dximagetransform.microsoft.gradient(enabled=false);
}

.ui-priority-secundary:hover{
    color: #ffffff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #a20000;
    *background-color: #a20000;
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#a20000), to(#a20000));
    background-image: -webkit-linear-gradient(top, #a20000, #a20000);
    background-image: -o-linear-gradient(top, #a20000, #a20000);
    background-image: linear-gradient(to bottom, #a20000, #a20000);
    background-image: -moz-linear-gradient(top, #a20000, #a20000);
    background-repeat: repeat-x;
    border-color: #0044cc #0044cc #002a80;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffa20000', endColorstr='#ffa20000', GradientType=0);
    filter: progid:dximagetransform.microsoft.gradient(enabled=false);
}

@media all and (min-width: 768px) {
    .cadastrar{
        width: 700px;
    }
}
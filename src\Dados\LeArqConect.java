package Dados;

import Dados.Pool.DadosBancos;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.InputStream;
import java.io.Reader;
import java.util.StringTokenizer;

/**
 *
 * <AUTHOR>
 */
public class LeArqConect {

    private String empresa, url, usuario, senha;

    public DadosBancos mapeia(String param, String caminho) {
        DadosBancos retorno = null;
        String linha;
        try {

            BufferedReader leitor;
            // instancia do arquivo que vou ler  
            try {
                FileReader reader = new FileReader(caminho);
                leitor = new BufferedReader(reader);
            } catch (Exception e) {
                InputStream in = getClass().getResourceAsStream(caminho);
                Reader reader = new java.io.InputStreamReader(in);
                leitor = new BufferedReader(reader);
            }

            linha = leitor.readLine();
            while (linha != null) {
                try {
                    StringTokenizer st = new StringTokenizer(linha, "}");
                    empresa = st.nextToken();

                    if (empresa.toUpperCase().equals(param.toUpperCase())) {
                        url = "jdbc:sqlserver://" + st.nextToken();
                        usuario = st.nextToken();
                        senha = st.nextToken().replace("&amp;", "&");

                        
                        retorno = new DadosBancos();
                        retorno.setEmpresa(empresa);
                        retorno.setUrl(url);
                        retorno.setUsuario(usuario);
                        retorno.setSenha(senha);
                        
                       /*
                        //Brinks 25/04/2024
                        retorno = new DadosBancos();
                        retorno.setEmpresa("SATBRINKS");
                        retorno.setUrl("nlb-sql-externo-interno-ac3556fc95d666d9.elb.sa-east-1.amazonaws.com:1433");
                        retorno.setUsuario("SASWADMIN");
                        retorno.setSenha("SAS0S7uX)6x84h");
                        */
                        linha = null;
                        
                    } else {
                        linha = leitor.readLine();
                    }
                    /*empresa = st.nextToken();
                    if (empresa.equals(param)){
                        jndi = st.nextToken();
                        retorno = new DadosBancos();
                        retorno.setEmpresa(empresa);
                        retorno.setJndi(jndi);
                        
                        
                        
                        linha = null;
                    } else linha=leitor.readLine();*/
                } catch (Exception e2) {
                    linha = leitor.readLine();
                }
            }
            leitor.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return retorno;
    }
}

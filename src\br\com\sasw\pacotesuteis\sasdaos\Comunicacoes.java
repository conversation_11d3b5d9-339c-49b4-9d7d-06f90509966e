/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

/**
 *
 * <AUTHOR>
 */
public class Comunicacoes {

    private String Numero;
    private String CodFil;
    private String DtCadastro;
    private String HrCadastro;
    private String DtLimite;
    private String SLA_Dias;
    private String CodRemet;
    private String Tipo;
    private String Assunto;
    private String Detalhes;
    private String SecaoDest;
    private String CodDest;
    private String Parecer;
    private String ParecerTeor;
    private String Solucao;
    private String SolucaoDet;
    private String Situacao;
    private String DtFechamento;
    private String HrFechamento;
    private String CodContato;
    private String Oper_Excl;
    private String Dt_Excl;
    private String Hr_Excl;
    private String Flag_Excl;

    private String Remetente;
    
    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDtCadastro() {
        return DtCadastro;
    }

    public void setDtCadastro(String DtCadastro) {
        this.DtCadastro = DtCadastro;
    }

    public String getHrCadastro() {
        return HrCadastro;
    }

    public void setHrCadastro(String HrCadastro) {
        this.HrCadastro = HrCadastro;
    }

    public String getDtLimite() {
        return DtLimite;
    }

    public void setDtLimite(String DtLimite) {
        this.DtLimite = DtLimite;
    }

    public String getSLA_Dias() {
        return SLA_Dias;
    }

    public void setSLA_Dias(String SLA_Dias) {
        this.SLA_Dias = SLA_Dias;
    }

    public String getCodRemet() {
        return CodRemet;
    }

    public void setCodRemet(String CodRemet) {
        this.CodRemet = CodRemet;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getAssunto() {
        return Assunto;
    }

    public void setAssunto(String Assunto) {
        this.Assunto = Assunto;
    }

    public String getDetalhes() {
        return Detalhes;
    }

    public void setDetalhes(String Detalhes) {
        this.Detalhes = Detalhes;
    }

    public String getSecaoDest() {
        return SecaoDest;
    }

    public void setSecaoDest(String SecaoDest) {
        this.SecaoDest = SecaoDest;
    }

    public String getCodDest() {
        return CodDest;
    }

    public void setCodDest(String CodDest) {
        this.CodDest = CodDest;
    }

    public String getParecer() {
        return Parecer;
    }

    public void setParecer(String Parecer) {
        this.Parecer = Parecer;
    }

    public String getParecerTeor() {
        return ParecerTeor;
    }

    public void setParecerTeor(String ParecerTeor) {
        this.ParecerTeor = ParecerTeor;
    }

    public String getSolucao() {
        return Solucao;
    }

    public void setSolucao(String Solucao) {
        this.Solucao = Solucao;
    }

    public String getSolucaoDet() {
        return SolucaoDet;
    }

    public void setSolucaoDet(String SolucaoDet) {
        this.SolucaoDet = SolucaoDet;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getDtFechamento() {
        return DtFechamento;
    }

    public void setDtFechamento(String DtFechamento) {
        this.DtFechamento = DtFechamento;
    }

    public String getHrFechamento() {
        return HrFechamento;
    }

    public void setHrFechamento(String HrFechamento) {
        this.HrFechamento = HrFechamento;
    }

    public String getCodContato() {
        return CodContato;
    }

    public void setCodContato(String CodContato) {
        this.CodContato = CodContato;
    }

    public String getOper_Excl() {
        return Oper_Excl;
    }

    public void setOper_Excl(String Oper_Excl) {
        this.Oper_Excl = Oper_Excl;
    }

    public String getDt_Excl() {
        return Dt_Excl;
    }

    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    public String getHr_Excl() {
        return Hr_Excl;
    }

    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    public String getRemetente() {
        return Remetente;
    }

    public void setRemetente(String Remetente) {
        this.Remetente = Remetente;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Pe_Cargo {

    private String Codigo;
    private String Cod_Cargo;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String Descricao;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getCod_Cargo() {
        return Cod_Cargo;
    }

    public void setCod_Cargo(String Cod_Cargo) {
        this.Cod_Cargo = Cod_Cargo;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 37 * hash + Objects.hashCode(this.Codigo);
        hash = 37 * hash + Objects.hashCode(this.Cod_Cargo);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Pe_Cargo other = (Pe_Cargo) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        if (!Objects.equals(this.Cod_Cargo, other.Cod_Cargo)) {
            return false;
        }
        return true;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }
}

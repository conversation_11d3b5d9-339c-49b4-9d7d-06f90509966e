/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Dietas;

import Dados.Persistencia;
import SasDaos.DietaDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Dieta {

    public List<SasBeansCompostas.Dieta> listaDieta(Persistencia persistencia) throws Exception {
        try {
            DietaDao dashDao = new DietaDao();
            return dashDao.listaDieta(persistencia);
        } catch (Exception e) {
            throw new Exception("Dieta.listaDieta<message>" + e.getMessage());
        }
    }

    public void salvarDieta(Persistencia persistencia, SasBeansCompostas.Dieta dieta) throws Exception {
        try {
            DietaDao dashDao = new DietaDao();
            dashDao.salvarDieta(persistencia, dieta);
        } catch (Exception e) {
            throw new Exception("Dieta.salvarDieta<message>" + e.getMessage());
        }
    }
}

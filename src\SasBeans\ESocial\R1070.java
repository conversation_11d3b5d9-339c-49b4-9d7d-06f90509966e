/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class R1070 {

    private String id;
    private int sucesso;

    /**
     * 1 - Produção / 2 - Homologação
     */
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    /**
     * J = 1 - CNPJ / F = 2 - CPF
     */
    private String ideEmpregador_tpInsc;
    private String infoCadastro_classTrib;

    /**
     * Copy(CNPJ,1,08)+</nrInsc> //Raiz CNPJ
     */
    private String ideEmpregador_nrInsc;
    private String infoCadastro_nmRazao;
    private String idePeriodo_iniValid;

    private String ideProcesso_tpProc;
    private String ideProcesso_nrProc;
    private String ideProcesso_iniValid;
    private String ideProcesso_indAutoria;
    private String infoSusp_codSusp;
    private String infoSusp_indSusp;
    private String infoSusp_dtDescisao;
    private String infoSusp_indDeposito;
    private String dadosProcJud_ufVara;
    private String dadosProcJud_codMunic;
    private String dadosProcJud_idVara;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getInfoCadastro_nmRazao() {
        return infoCadastro_nmRazao;
    }

    public void setInfoCadastro_nmRazao(String infoCadastro_nmRazao) {
        this.infoCadastro_nmRazao = infoCadastro_nmRazao;
    }

    public String getIdePeriodo_iniValid() {
        return idePeriodo_iniValid;
    }

    public void setIdePeriodo_iniValid(String idePeriodo_iniValid) {
        this.idePeriodo_iniValid = idePeriodo_iniValid;
    }

    public String getIdeProcesso_tpProc() {
        return ideProcesso_tpProc;
    }

    public void setIdeProcesso_tpProc(String ideProcesso_tpProc) {
        this.ideProcesso_tpProc = ideProcesso_tpProc;
    }

    public String getIdeProcesso_nrProc() {
        return ideProcesso_nrProc;
    }

    public void setIdeProcesso_nrProc(String ideProcesso_nrProc) {
        this.ideProcesso_nrProc = ideProcesso_nrProc;
    }

    public String getIdeProcesso_iniValid() {
        return ideProcesso_iniValid;
    }

    public void setIdeProcesso_iniValid(String ideProcesso_iniValid) {
        this.ideProcesso_iniValid = ideProcesso_iniValid;
    }

    public String getIdeProcesso_indAutoria() {
        return ideProcesso_indAutoria;
    }

    public void setIdeProcesso_indAutoria(String ideProcesso_indAutoria) {
        this.ideProcesso_indAutoria = ideProcesso_indAutoria;
    }

    public String getInfoSusp_codSusp() {
        return infoSusp_codSusp;
    }

    public void setInfoSusp_codSusp(String infoSusp_codSusp) {
        this.infoSusp_codSusp = infoSusp_codSusp;
    }

    public String getInfoSusp_indSusp() {
        return infoSusp_indSusp;
    }

    public void setInfoSusp_indSusp(String infoSusp_indSusp) {
        this.infoSusp_indSusp = infoSusp_indSusp;
    }

    public String getInfoSusp_dtDescisao() {
        return infoSusp_dtDescisao;
    }

    public void setInfoSusp_dtDescisao(String infoSusp_dtDescisao) {
        this.infoSusp_dtDescisao = infoSusp_dtDescisao;
    }

    public String getInfoSusp_indDeposito() {
        return infoSusp_indDeposito;
    }

    public void setInfoSusp_indDeposito(String infoSusp_indDeposito) {
        this.infoSusp_indDeposito = infoSusp_indDeposito;
    }

    public String getDadosProcJud_ufVara() {
        return dadosProcJud_ufVara;
    }

    public void setDadosProcJud_ufVara(String dadosProcJud_ufVara) {
        this.dadosProcJud_ufVara = dadosProcJud_ufVara;
    }

    public String getDadosProcJud_codMunic() {
        return dadosProcJud_codMunic;
    }

    public void setDadosProcJud_codMunic(String dadosProcJud_codMunic) {
        this.dadosProcJud_codMunic = dadosProcJud_codMunic;
    }

    public String getDadosProcJud_idVara() {
        return dadosProcJud_idVara;
    }

    public void setDadosProcJud_idVara(String dadosProcJud_idVara) {
        this.dadosProcJud_idVara = dadosProcJud_idVara;
    }

    public String getInfoCadastro_classTrib() {
        return infoCadastro_classTrib;
    }

    public void setInfoCadastro_classTrib(String infoCadastro_classTrib) {
        this.infoCadastro_classTrib = infoCadastro_classTrib;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.CxFGuiasVol;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GuiasPortal {

    public String RPV;
    public String guia;
    public String serie;
    public String moeda;
    public String qtdeVolumes;
    public String valor;
    public List<CxFGuiasVol> volumes;

    public String getRPV() {
        return RPV;
    }

    public void setRPV(String RPV) {
        this.RPV = RPV;
    }

    public String getGuia() {
        return guia;
    }

    public void setGuia(String guia) {
        this.guia = guia;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getQtdeVolumes() {
        return qtdeVolumes;
    }

    public void setQtdeVolumes(String qtdeVolumes) {
        this.qtdeVolumes = qtdeVolumes;
    }

    public List<CxFGuiasVol> getVolumes() {
        return volumes;
    }

    public void setVolumes(List<CxFGuiasVol> volumes) {
        this.volumes = volumes;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }
}

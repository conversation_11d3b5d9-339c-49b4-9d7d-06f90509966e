package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Rt_Escala;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class Rt_EscalaDao {

    /**
     * Cria escala de supervisão
     *
     * @param escalas Objeto de escala
     * @param persistencia conexão com a base de dados
     * @throws Exception
     */
    public void criarEscala(Rt_Escala escalas, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO rt_escala(sequencia, matr, data, "
                    + "funcao, hora, operador, dt_alter, hr_alter) "
                    + "VALUES (?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setBigDecimal(escalas.getMatr());
            consulta.setString(escalas.getData());
            consulta.setString(escalas.getFuncao());
            consulta.setString(escalas.getHora());
            consulta.setString(escalas.getOperador());
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_EscalaDao.criarEscala - " + e.getMessage() + "\r\n"
                    + "INSERT INTO rt_escala(sequencia, matr, data, "
                    + "funcao, hora, operador, dt_alter, hr_alter) "
                    + "VALUES (?,?,?,?,?,?,?,?)");
        }
    }

    /**
     * Atualiza a escala de um funcionário
     *
     * @param escalas Objeto de escala
     * @param persistencia conexão com a base de dados
     * @throws Exception
     */
    public void atualizarEscala(Rt_Escala escalas, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE rt_escala SET data = ?, "
                    + " funcao = ?, hora = ?, operador = ?, dt_alter = ?, hr_alter = ? \n"
                    + " WHERE sequencia = ? AND matr = ? ";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(escalas.getData());
            consulta.setString(escalas.getFuncao());
            consulta.setString(escalas.getHora());
            consulta.setString(escalas.getOperador());
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());
            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setBigDecimal(escalas.getMatr());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_EscalaDao.atualizarEscala  - " + e.getMessage() + "\r\n"
                    + " UPDATE rt_escala SET data = " + escalas.getData() + ", "
                    + " funcao = " + escalas.getFuncao() + ", hora = " + escalas.getHora() + ", operador = " + escalas.getOperador() + ", "
                    + " dt_alter = " + escalas.getDt_alter() + ", hr_alter = " + escalas.getHr_alter() + " \n"
                    + " WHERE sequencia = " + escalas.getSequencia() + " AND matr = " + escalas.getMatr());
        }
    }

    /**
     * Atualiza a escala de um funcionário
     *
     * @param escalas Objeto de escala
     * @param persistencia conexão com a base de dados
     * @throws Exception
     */
    public boolean existeEscala(Rt_Escala escalas, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = false;

            String sql = "SELECT sequencia, matr, data, funcao, hora, \n"
                    + "operador, dt_alter, hr_alter FROM rt_escala \n"
                    + "WHERE sequencia = ? and matr = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setBigDecimal(escalas.getMatr());
            consulta.select();

            if (consulta.Proximo()) {
                retorno = true;
            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_EscalaDao.existeEscala  - " + e.getMessage() + "\r\n"
                    + " SELECT sequencia, matr, data, funcao, hora, \n"
                    + "operador, dt_alter, hr_alter FROM rt_escala \n"
                    + " WHERE sequencia = " + escalas.getSequencia() + " AND matr = " + escalas.getMatr());
        }
    }

    public Rt_Escala SelecionaRt_EscalaSatMobWeb(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        Rt_Escala retorno = new Rt_Escala();
        try {

            String sql = "SELECT sequencia, matr, data, funcao, hora,"
                    + "operador, dt_alter, hr_alter FROM rt_escala WHERE sequencia = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();

            while (consulta.Proximo()) {
                retorno.setSequencia(consulta.getBigDecimal("sequencia"));
                retorno.setMatr(consulta.getBigDecimal("matr"));
                retorno.setData(consulta.getString("data"));
                retorno.setFuncao(consulta.getString("funcao"));
                retorno.setHora(consulta.getString("hora"));
                retorno.setOperador(consulta.getString("operador"));
                retorno.setDt_alter(consulta.getString("dt_alter"));
                retorno.setHr_alter(consulta.getString("hr_alter"));
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("rt_escala.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    /**
     * Faz a exclusão dos valores de Rt_Escala uma vez que a rota está sendo
     * excluída no projeto SatMobWeb
     *
     * @param sequencia Sequência da rota (Rt_Escala)
     * @param persistencia Conexão com o banco
     * @throws Exception
     */
    public void ExcluirRt_EscalaSatMobWeb(String sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "DELETE FROM rt_escala WHERE sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.select();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("rt_escala.falhageral<message>" + e.getMessage());
        }
    }
}

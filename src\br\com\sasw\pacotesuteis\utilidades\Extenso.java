package br.com.sasw.pacotesuteis.utilidades;

import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.getMoeda;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.getMoedaEN;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.getMoedaES;
import java.text.DecimalFormat;

/**
 *
 * <AUTHOR>
 */
public class Extenso {

    private static final String[] TENSNAMES = {"", " ten", " twenty", " thirty", " forty", " fifty", " sixty", " seventy", " eighty", " ninety"};
    private static final String[] NUMNAMES = {"", " one", " two", " three", " four", " five", " six", " seven", " eight", " nine", " ten", " eleven", " twelve", " thirteen", " fourteen", " fifteen", " sixteen", " seventeen", " eighteen", " nineteen"};

    public static String valorPorExtenso(double vlr) {
        if (vlr == 0) {
            return ("zero");
        }

        long inteiro = (long) Math.abs(vlr); // parte inteira do valor
        double resto = vlr - inteiro;       // parte fracionÃ¡ria do valor

        String vlrS = String.valueOf(inteiro);
        if (vlrS.length() > 15) {
            return ("Erro: valor superior a 999 trilhões.");
        }

        String s = "", saux, vlrP;
        String centavos = String.valueOf((int) Math.round(resto * 100));

        String[] unidade = {"", "um", "dois", "três", "quatro", "cinco",
            "seis", "sete", "oito", "nove", "dez", "onze",
            "doze", "treze", "quatorze", "quinze", "dezesseis",
            "dezessete", "dezoito", "dezenove"};
        String[] centena = {"", "cento", "duzentos", "trezentos",
            "quatrocentos", "quinhentos", "seiscentos",
            "setecentos", "oitocentos", "novecentos"};
        String[] dezena = {"", "", "vinte", "trinta", "quarenta", "cinquenta",
            "sessenta", "setenta", "oitenta", "noventa"};
        String[] qualificaS = {"", "mil", "milhão", "bilhão", "trilhão"};
        String[] qualificaP = {"", "mil", "milhões", "bilhões", "trilhões"};

// definindo o extenso da parte inteira do valor
        int n, unid, dez, cent, tam, i = 0;
        boolean umReal = false, tem = false;
        while (!vlrS.equals("0")) {
            tam = vlrS.length();
// retira do valor a 1a. parte, 2a. parte, por exemplo, para 123456789:
// 1a. parte = 789 (centena)
// 2a. parte = 456 (mil)
// 3a. parte = 123 (milhÃµes)
            if (tam > 3) {
                vlrP = vlrS.substring(tam - 3, tam);
                vlrS = vlrS.substring(0, tam - 3);
            } else { // Ãºltima parte do valor
                vlrP = vlrS;
                vlrS = "0";
            }
            if (!vlrP.equals("000")) {
                saux = "";
                if (vlrP.equals("100")) {
                    saux = "cem";
                } else {
                    n = Integer.parseInt(vlrP, 10);  // para n = 371, tem-se:
                    cent = n / 100;                  // cent = 3 (centena trezentos)
                    dez = (n % 100) / 10;            // dez  = 7 (dezena setenta)
                    unid = (n % 100) % 10;           // unid = 1 (unidade um)
                    if (cent != 0) {
                        saux = centena[cent];
                    }
                    if ((dez != 0) || (unid != 0)) {
                        if ((n % 100) <= 19) {
                            if (saux.length() != 0) {
                                saux = saux + " e " + unidade[n % 100];
                            } else {
                                saux = unidade[n % 100];
                            }
                        } else {
                            if (saux.length() != 0) {
                                saux = saux + " e " + dezena[dez];
                            } else {
                                saux = dezena[dez];
                            }
                            if (unid != 0) {
                                if (saux.length() != 0) {
                                    saux = saux + " e " + unidade[unid];
                                } else {
                                    saux = unidade[unid];
                                }
                            }
                        }
                    }
                }
                if (vlrP.equals("1") || vlrP.equals("001")) {
                    if (i == 0) { // 1a. parte do valor (um real)
                        umReal = true;
                    } else {
                        saux = saux + " " + qualificaS[i];
                    }
                } else if (i != 0) {
                    saux = saux + " " + qualificaP[i];
                }
                if (s.length() != 0) {
                    s = saux + ", " + s;
                } else {
                    s = saux;
                }
            }
            if (((i == 0) || (i == 1)) && s.length() != 0) {
                tem = true; // tem centena ou mil no valor
            }
            i = i + 1; // prÃ³ximo qualificador: 1- mil, 2- milhÃ£o, 3- bilhÃ£o, ...
        }

        if (s.length() != 0) {
            if (umReal) {
                s = s + " real";
            } else if (tem) {
                s = s + " reais";
            } else {
                s = s + " de reais";
            }
        }

// definindo o extenso dos centavos do valor
        if (!centavos.equals("0")) { // valor com centavos
            if (s.length() != 0) { // se nÃ£o Ã© valor somente com centavos
                s = s + " e ";
            }
            if (centavos.equals("1")) {
                s = s + "um centavo";
            } else {
                n = Integer.parseInt(centavos, 10);
                if (n <= 19) {
                    s = s + unidade[n];
                } else {             // para n = 37, tem-se:
                    unid = n % 10;   // unid = 37 % 10 = 7 (unidade sete)
                    dez = n / 10;    // dez  = 37 / 10 = 3 (dezena trinta)
                    s = s + dezena[dez];
                    if (unid != 0) {
                        s = s + " e " + unidade[unid];
                    }
                }
                s = s + " centavos";
            }
        }
        return (s);
    }

    public static String valorPorExtensoPT(double vlr, String moeda) {
        if (vlr == 0) {
            return ("zero");
        }

        long inteiro = (long) Math.abs(vlr); // parte inteira do valor
        double resto = vlr - inteiro;       // parte fracionÃ¡ria do valor

        String vlrS = String.valueOf(inteiro);
        if (vlrS.length() > 15) {
            return ("Erro: valor superior a 999 trilhões.");
        }

        String s = "", saux, vlrP;
        String centavos = String.valueOf((int) Math.round(resto * 100));

        String[] unidade = {"", "um", "dois", "três", "quatro", "cinco",
            "seis", "sete", "oito", "nove", "dez", "onze",
            "doze", "treze", "quatorze", "quinze", "dezesseis",
            "dezessete", "dezoito", "dezenove"};
        String[] centena = {"", "cento", "duzentos", "trezentos",
            "quatrocentos", "quinhentos", "seiscentos",
            "setecentos", "oitocentos", "novecentos"};
        String[] dezena = {"", "", "vinte", "trinta", "quarenta", "cinquenta",
            "sessenta", "setenta", "oitenta", "noventa"};
        String[] qualificaS = {"", "mil", "milhão", "bilhão", "trilhão"};
        String[] qualificaP = {"", "mil", "milhões", "bilhões", "trilhões"};

// definindo o extenso da parte inteira do valor
        int n, unid, dez, cent, tam, i = 0;
        boolean umReal = false, tem = false;
        while (!vlrS.equals("0")) {
            tam = vlrS.length();
// retira do valor a 1a. parte, 2a. parte, por exemplo, para 123456789:
// 1a. parte = 789 (centena)
// 2a. parte = 456 (mil)
// 3a. parte = 123 (milhÃµes)
            if (tam > 3) {
                vlrP = vlrS.substring(tam - 3, tam);
                vlrS = vlrS.substring(0, tam - 3);
            } else { // Ãºltima parte do valor
                vlrP = vlrS;
                vlrS = "0";
            }
            if (!vlrP.equals("000")) {
                saux = "";
                if (vlrP.equals("100")) {
                    saux = "cem";
                } else {
                    n = Integer.parseInt(vlrP, 10);  // para n = 371, tem-se:
                    cent = n / 100;                  // cent = 3 (centena trezentos)
                    dez = (n % 100) / 10;            // dez  = 7 (dezena setenta)
                    unid = (n % 100) % 10;           // unid = 1 (unidade um)
                    if (cent != 0) {
                        saux = centena[cent];
                    }
                    if ((dez != 0) || (unid != 0)) {
                        if ((n % 100) <= 19) {
                            if (saux.length() != 0) {
                                saux = saux + " e " + unidade[n % 100];
                            } else {
                                saux = unidade[n % 100];
                            }
                        } else {
                            if (saux.length() != 0) {
                                saux = saux + " e " + dezena[dez];
                            } else {
                                saux = dezena[dez];
                            }
                            if (unid != 0) {
                                if (saux.length() != 0) {
                                    saux = saux + " e " + unidade[unid];
                                } else {
                                    saux = unidade[unid];
                                }
                            }
                        }
                    }
                }
                if (vlrP.equals("1") || vlrP.equals("001")) {
                    if (i == 0) { // 1a. parte do valor (um real)
                        umReal = true;
                    } else {
                        saux = saux + " " + qualificaS[i];
                    }
                } else if (i != 0) {
                    saux = saux + " " + qualificaP[i];
                }
                if (s.length() != 0) {
                    s = saux + ", " + s;
                } else {
                    s = saux;
                }
            }
            if (((i == 0) || (i == 1)) && s.length() != 0) {
                tem = true; // tem centena ou mil no valor
            }
            i = i + 1; // prÃ³ximo qualificador: 1- mil, 2- milhÃ£o, 3- bilhÃ£o, ...
        }

        if (s.length() != 0) {
            if (umReal) {
                s = s + " " + moeda;
            } else if (tem) {
                s = s + " " + moeda;
            } else {
                s = s + " de " + moeda;
            }
        }

// definindo o extenso dos centavos do valor
        if (!centavos.equals("0")) { // valor com centavos
            if (s.length() != 0) { // se nÃ£o Ã© valor somente com centavos
                s = s + " e ";
            }
            if (centavos.equals("1")) {
                s = s + "um centavo";
            } else {
                n = Integer.parseInt(centavos, 10);
                if (n <= 19) {
                    s = s + unidade[n];
                } else {             // para n = 37, tem-se:
                    unid = n % 10;   // unid = 37 % 10 = 7 (unidade sete)
                    dez = n / 10;    // dez  = 37 / 10 = 3 (dezena trinta)
                    s = s + dezena[dez];
                    if (unid != 0) {
                        s = s + " e " + unidade[unid];
                    }
                }
                s = s + " centavos";
            }
        }
        return (s);
    }

    public static String valorPorExtenso(double number, String idioma, String moeda) {
        String retorno = "";
        switch (idioma) {
            case "en":
                retorno = valorPorExtensoIngles(number, getMoedaEN(moeda));
                break;
            case "es":
                retorno = valorPorExtensoEspanhol(number, getMoedaES(moeda));
                break;
            default:
                retorno = valorPorExtensoPT(number, getMoeda(moeda));
        }
        return retorno;
    }

    public static String valorPorExtenso(double number, String idioma) {
        String retorno = "";
        switch (idioma) {
            case "en":
                retorno = valorPorExtensoIngles(number);
                break;
            case "es":
                retorno = valorPorExtensoEspanhol(number);
                break;
            default:
                retorno = valorPorExtenso(number);
        }
        return retorno;
    }

    public static String valorPorExtensoEspanhol(double number, String moeda) {

        if (number == 0) {
            return ("Cero");
        }

        long inteiro = (long) number; // parte inteira do valor
        long resto = (long) (Math.abs(number * 100 - inteiro * 100)); // parte fracionÃ¡ria do valor

        return cantidadConLetra(inteiro) + " " + moeda
                + ((resto == 0) ? "" : " y " + cantidadConLetra(resto)
                        + (resto == 1 ? "centavo" : "centavos"));

    }

    public static String valorPorExtensoEspanhol(double number) {

        if (number == 0) {
            return ("Cero");
        }

        long inteiro = (long) number; // parte inteira do valor
        long resto = (long) (Math.abs(number * 100 - inteiro * 100)); // parte fracionÃ¡ria do valor

        return cantidadConLetra(inteiro)
                + (inteiro == 1 ? "peso" : "pesos")
                + ((resto == 0) ? "" : " y " + cantidadConLetra(resto)
                        + (resto == 1 ? "centavo" : "centavos"));

    }

    public static String cantidadConLetra(double parteEntera) {
        StringBuilder result = new StringBuilder();
        int triUnidades = (int) ((parteEntera % 1000));
        int triMiles = (int) ((parteEntera / 1000) % 1000);
        int triMillones = (int) ((parteEntera / 1000000) % 1000);
        int triMilMillones = (int) ((parteEntera / 1000000000) % 1000);

        if (parteEntera == 0) {
            result.append("Cero ");
            return result.toString();
        }

        if (triMilMillones > 0) {
            result.append(triTexto(triMilMillones).toString() + "Mil ");
        }
        if (triMillones > 0) {
            result.append(triTexto(triMillones).toString());
        }

        if (triMilMillones == 0 && triMillones == 1) {
            result.append("Millón ");
        } else if (triMilMillones > 0 || triMillones > 0) {
            result.append("Millones ");
        }

        if (triMiles > 0) {
            result.append(triTexto(triMiles).toString() + "Mil ");
        }
        if (triUnidades > 0) {
            result.append(triTexto(triUnidades).toString());
        }

        return result.toString();
    }

    /**
     * Convierte una cantidad de tres cifras a su representación escrita con
     * letra.
     *
     * @param n La cantidad a convertir.
     * @return Una cadena de texto que contiene la representación con letra del
     * número que se recibió como argumento.
     */
    private static StringBuilder triTexto(int n) {
        StringBuilder result = new StringBuilder();
        int centenas = n / 100;
        int decenas = (n % 100) / 10;
        int unidades = (n % 10);

        switch (centenas) {
            case 0:
                break;
            case 1:
                if (decenas == 0 && unidades == 0) {
                    result.append("Cien ");
                    return result;
                } else {
                    result.append("Ciento ");
                }
                break;
            case 2:
                result.append("Doscientos ");
                break;
            case 3:
                result.append("Trescientos ");
                break;
            case 4:
                result.append("Cuatrocientos ");
                break;
            case 5:
                result.append("Quinientos ");
                break;
            case 6:
                result.append("Seiscientos ");
                break;
            case 7:
                result.append("Setecientos ");
                break;
            case 8:
                result.append("Ochocientos ");
                break;
            case 9:
                result.append("Novecientos ");
                break;
        }

        switch (decenas) {
            case 0:
                break;
            case 1:
                if (unidades == 0) {
                    result.append("Diez ");
                    return result;
                } else if (unidades == 1) {
                    result.append("Once ");
                    return result;
                } else if (unidades == 2) {
                    result.append("Doce ");
                    return result;
                } else if (unidades == 3) {
                    result.append("Trece ");
                    return result;
                } else if (unidades == 4) {
                    result.append("Catorce ");
                    return result;
                } else if (unidades == 5) {
                    result.append("Quince ");
                    return result;
                } else {
                    result.append("Dieci");
                }
                break;
            case 2:
                if (unidades == 0) {
                    result.append("Veinte ");
                    return result;
                } else {
                    result.append("Veinti");
                }
                break;
            case 3:
                result.append("Treinta ");
                break;
            case 4:
                result.append("Cuarenta ");
                break;
            case 5:
                result.append("Cincuenta ");
                break;
            case 6:
                result.append("Sesenta ");
                break;
            case 7:
                result.append("Setenta ");
                break;
            case 8:
                result.append("Ochenta ");
                break;
            case 9:
                result.append("Noventa ");
                break;
        }

        if (decenas > 2 && unidades > 0) {
            result.append("y ");
        }

        switch (unidades) {
            case 0:
                break;
            case 1:
                result.append("Un ");
                break;
            case 2:
                result.append("Dos ");
                break;
            case 3:
                result.append("Tres ");
                break;
            case 4:
                result.append("Cuatro ");
                break;
            case 5:
                result.append("Cinco ");
                break;
            case 6:
                result.append("Seis ");
                break;
            case 7:
                result.append("Siete ");
                break;
            case 8:
                result.append("Ocho ");
                break;
            case 9:
                result.append("Nueve ");
                break;
        }

        return result;
    }

    public static String valorPorExtensoIngles(double number) {

        if (number == 0) {
            return ("zero");
        }

        long inteiro = (long) number; // parte inteira do valor
        long resto = (long) (Math.abs(number * 100 - inteiro * 100)); // parte fracionÃ¡ria do valor

        return convert(inteiro)
                + (inteiro == 1 ? " dollar" : " dollars")
                + ((resto == 0) ? "" : " and " + convert(resto)
                        + (resto == 1 ? " cent" : " cents"));
    }

    public static String valorPorExtensoIngles(double number, String moeda) {

        if (number == 0) {
            return ("zero");
        }

        long inteiro = (long) number; // parte inteira do valor
        long resto = (long) (Math.abs(number * 100 - inteiro * 100)); // parte fracionÃ¡ria do valor

        return convert(inteiro) + " " + moeda
                + ((resto == 0) ? "" : " and " + convert(resto) + (resto == 1 ? " cent" : " cents"));
    }

    public static String convert(double number) {
        // 0 to 999 999 999 999
        if (number == 0) {
            return "zero";
        }

        // pad with "0"
        String mask = "000000000000";
        DecimalFormat df = new DecimalFormat(mask);
        String snumber = df.format(number);

        // XXXnnnnnnnnn
        int billions = Integer.parseInt(snumber.substring(0, 3));
        // nnnXXXnnnnnn
        int millions = Integer.parseInt(snumber.substring(3, 6));
        // nnnnnnXXXnnn
        int hundredThousands = Integer.parseInt(snumber.substring(6, 9));
        // nnnnnnnnnXXX
        int thousands = Integer.parseInt(snumber.substring(9, 12));

        String tradBillions;
        switch (billions) {
            case 0:
                tradBillions = "";
                break;
            case 1:
                tradBillions = convertLessThanOneThousand(billions) + " billion ";
                break;
            default:
                tradBillions = convertLessThanOneThousand(billions) + " billion ";
        }
        String result = tradBillions;

        String tradMillions;
        switch (millions) {
            case 0:
                tradMillions = "";
                break;
            case 1:
                tradMillions = convertLessThanOneThousand(millions) + " million ";
                break;
            default:
                tradMillions = convertLessThanOneThousand(millions) + " million ";
        }
        result = result + tradMillions;

        String tradHundredThousands;
        switch (hundredThousands) {
            case 0:
                tradHundredThousands = "";
                break;
            case 1:
                tradHundredThousands = "one thousand ";
                break;
            default:
                tradHundredThousands = convertLessThanOneThousand(hundredThousands) + " thousand ";
        }
        result = result + tradHundredThousands;

        String tradThousand;
        tradThousand = convertLessThanOneThousand(thousands);
        result = result + tradThousand;

        // remove extra spaces!
        return result.replaceAll("^\\s+", "").replaceAll("\\b\\s{2,}\\b", " ");
    }

    private static String convertLessThanOneThousand(int number) {
        String soFar;
        if (number % 100 < 20) {
            soFar = NUMNAMES[number % 100];
            number /= 100;
        } else {
            soFar = NUMNAMES[number % 10];
            number /= 10;

            soFar = TENSNAMES[number % 10] + soFar;
            number /= 10;
        }
        if (number == 0) {
            return soFar;
        }
        return NUMNAMES[number] + " hundred" + soFar;
    }
}

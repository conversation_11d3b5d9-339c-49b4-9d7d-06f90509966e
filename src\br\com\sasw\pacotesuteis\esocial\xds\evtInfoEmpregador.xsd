﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtInfoEmpregador/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtInfoEmpregador/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtInfoEmpregador">
                    <xs:annotation>
                        <xs:documentation>Evento de informacoes do empregador</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeCadastro">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes do Empregador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:choice>
                                            <xs:element name="inclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Inclusao de novas informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="idePeriodo" type="TIdePeriodo">
                                                            <xs:annotation>
                                                                <xs:documentation>Periodo de validade das informacoes incluidas</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="infoCadastro" type="TInfoEmpregador">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes do empregador</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="alteracao">
                                                <xs:annotation>
                                                    <xs:documentation>Alteracao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="idePeriodo" type="TIdePeriodo">
                                                            <xs:annotation>
                                                                <xs:documentation>Periodo de validade das informacoes alteradas</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="infoCadastro" type="TInfoEmpregador">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes do empregador</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="novaValidade" type="TPeriodoValidade" minOccurs="0">
                                                            <xs:annotation>
                                                                <xs:documentation>Novo periodo de validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="exclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Exclusao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="idePeriodo" type="TIdePeriodo">
                                                            <xs:annotation>
                                                                <xs:documentation>Periodo de validade das informacoes excluidas</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeCadastro">
        <xs:annotation>
            <xs:documentation>Identificacao de evento de cadastro/tabelas</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdePeriodo">
        <xs:annotation>
            <xs:documentation>Identificacao do Periodo de Validade das Informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TInfoEmpregador">
        <xs:annotation>
            <xs:documentation>Informacoes do Empregador</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="nmRazao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome/Razao Social</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="classTrib">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Enquadramento do contribuinte</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="2"/>
                        <xs:pattern value="\d{2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="natJurid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Natureza Juridica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="4"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indCoop" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Cooperativa</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indConstr" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Construtora</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indDesFolha">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Desoneracao da Folha</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indOpcCP" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Opcao da CP</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indOptRegEletron">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Opcao p/ registro eletrônico de empregados</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indEntEd" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Entidade educativa sem fins lucrativos?</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[N|S]"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indEtt">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Empresa de Trabalho Temporario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[N|S]"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRegEtt" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do registro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="30"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dadosIsencao" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Informacoes Complementares - Empresas Isentas - Dados da Isencao</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="ideMinLei">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Ministério/Lei que concedeu isencao</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="70"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nrCertif">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do CEBAS</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="40"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="dtEmisCertif">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Data de Emissao do Certificado/publicacao da Lei</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:date">
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="dtVencCertif">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Data de vencimento do certificado</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:date">
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nrProtRenov" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Protocolo do pedido de renovacao</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="40"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="dtProtRenov" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Data do protocolo de renovacao</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:date">
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="dtDou" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Data de Publicacao no DOU</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:date">
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="pagDou" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número da pagina do DOU</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                    <xs:pattern value="\d{1,5}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="contato">
                <xs:annotation>
                    <xs:documentation>Informacoes de contato</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="nmCtt">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome do contato na empresa</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="70"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="cpfCtt">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número de Inscricao no CPF</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="11"/>
                                    <xs:pattern value="\d{11}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="foneFixo" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do telefone com DDD</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="8"/>
                                    <xs:maxLength value="13"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="foneCel" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Telefone celular</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="8"/>
                                    <xs:maxLength value="13"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="email" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Endereco eletrônico</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="6"/>
                                    <xs:maxLength value="60"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="infoOP" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Informacoes relativas a Orgaos Públicos</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="nrSiafi">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>SIAFI</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:pattern value="\w{1,6}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="infoEFR" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Informacoes relativas a Ente Federativo Responsavel - EFR</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="ideEFR">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Identificacao se o OPP é EFR</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:pattern value="[N|S]"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="cnpjEFR" minOccurs="0">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>CNPJ</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:pattern value="\d{14}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="infoEnte" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Informacoes relativas ao ente federativo estadual, distrital ou municipal</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="nmEnte">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Nome do Ente Federativo</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:minLength value="1"/>
                                                <xs:maxLength value="100"/>
                                                <xs:whiteSpace value="preserve"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="uf">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Sigla da UF</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:enumeration value="AC"/>
                                                <xs:enumeration value="AL"/>
                                                <xs:enumeration value="AP"/>
                                                <xs:enumeration value="AM"/>
                                                <xs:enumeration value="BA"/>
                                                <xs:enumeration value="CE"/>
                                                <xs:enumeration value="DF"/>
                                                <xs:enumeration value="ES"/>
                                                <xs:enumeration value="GO"/>
                                                <xs:enumeration value="MA"/>
                                                <xs:enumeration value="MT"/>
                                                <xs:enumeration value="MS"/>
                                                <xs:enumeration value="MG"/>
                                                <xs:enumeration value="PA"/>
                                                <xs:enumeration value="PB"/>
                                                <xs:enumeration value="PR"/>
                                                <xs:enumeration value="PE"/>
                                                <xs:enumeration value="PI"/>
                                                <xs:enumeration value="RJ"/>
                                                <xs:enumeration value="RN"/>
                                                <xs:enumeration value="RS"/>
                                                <xs:enumeration value="RO"/>
                                                <xs:enumeration value="RR"/>
                                                <xs:enumeration value="SC"/>
                                                <xs:enumeration value="SP"/>
                                                <xs:enumeration value="SE"/>
                                                <xs:enumeration value="TO"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="codMunic" minOccurs="0">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>COdigo do Municipio</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:integer">
                                                <xs:pattern value="\d{7}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="indRPPS">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Informar se o ente possui RPPS</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:pattern value="[N|S]"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="subteto">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Subteto</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:byte">
                                                <xs:pattern value="\d"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="vrSubteto">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Valor do subteto</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="14"/>
                                                <xs:fractionDigits value="2"/>
                                                <xs:maxInclusive value="999999999999"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="infoOrgInternacional" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Informacoes exclusivas de organismos internacionais e outras instituicoes extraterritoriais</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="indAcordoIsenMulta">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Acordo internacional para isencao de multa</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="softwareHouse" minOccurs="0" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Informacoes do desenvolvedor do Software</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="cnpjSoftHouse">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>CNPJ da Software House</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="14"/>
                                    <xs:pattern value="\d{14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nmRazao">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome/Razao Social</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="100"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nmCont">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome do contato na empresa</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="70"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="telefone">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do telefone com DDD</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="8"/>
                                    <xs:maxLength value="13"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="email" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Endereco eletrônico</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="6"/>
                                    <xs:maxLength value="60"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="infoComplementares">
                <xs:annotation>
                    <xs:documentation>Informacoes complementares sobre o declarante</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="situacaoPJ" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Informacoes Complementares - Pessoa Juridica</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="indSitPJ">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Situacao da PJ</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:byte">
                                                <xs:pattern value="\d"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="situacaoPF" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Informacoes Complementares - Pessoa Fisica</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="indSitPF">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Situacao da PF</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:byte">
                                                <xs:pattern value="\d"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TPeriodoValidade">
        <xs:annotation>
            <xs:documentation>Periodo de validade das informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

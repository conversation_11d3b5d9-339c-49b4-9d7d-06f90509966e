package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class CReceber {

    private BigDecimal Sequencia;
    private BigDecimal CodFil;
    private BigDecimal OC;
    private BigDecimal CodCli;
    private String Cliente;
    private BigDecimal NF;
    private BigDecimal Praca;
    private String Serie;
    private BigDecimal Pedido;
    private Integer TipoTit;
    private Integer FormaPgto;
    private String DtVenc;
    private String DtPrevPg;
    private String Compet;
    private String CompetPg;
    private String DtPagto;
    private BigDecimal Valor;
    private String Obs;
    private Integer ContaFin;
    private String CCusto;
    private Integer BoletoSeq;
    private BigDecimal TCobran;
    private String NossoNumero;
    private String Boleto;
    private String CMC7;
    private BigDecimal ValorBaixa;
    private BigDecimal ValorPago;
    private BigDecimal Juros;
    private BigDecimal Desconto;
    private BigDecimal CodConta;
    private BigDecimal Cheque;
    private String Banco;
    private String Agencia;
    private String ContaC;
    private String Situacao;
    private BigDecimal SeqDesdobra;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String NRed;
    private String DiasAtraso;
    private String DataNF;
    private Double TotalValor;
    
    private String Qtde;
    private String FormaPgtoDescr;
    private String Rota;
    
    private Float OnePayTotal;
    private Float OnePayTaxa;
    private Float OnePayLiquido;

    public CReceber() {
        this.Sequencia = new BigDecimal("0");
        this.CodFil = new BigDecimal("0");
        this.OC = new BigDecimal("0");
        this.CodCli = new BigDecimal("0");
        this.Cliente = "";
        this.NF = new BigDecimal("0");
        this.Praca = new BigDecimal("0");
        this.Serie = "";
        this.Pedido = new BigDecimal("0");
        this.TipoTit = 0;
        this.FormaPgto = 0;
        this.DtVenc = "";
        this.DtPrevPg = "";
        this.Compet = "";
        this.CompetPg = "";
        this.DtPagto = "";
        this.Valor = new BigDecimal("0");
        this.Obs = "";
        this.ContaFin = 0;
        this.CCusto = "";
        this.BoletoSeq = 0;
        this.TCobran = new BigDecimal("0");
        this.NossoNumero = "";
        this.Boleto = "";
        this.CMC7 = "";
        this.ValorBaixa = new BigDecimal("0");
        this.ValorPago = new BigDecimal("0");
        this.Juros = new BigDecimal("0");
        this.Desconto = new BigDecimal("0");
        this.CodConta = new BigDecimal("0");
        this.Cheque = new BigDecimal("0");
        this.Banco = "";
        this.Agencia = "";
        this.ContaC = "";
        this.Situacao = "";
        this.SeqDesdobra = new BigDecimal("0");
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
        
        this.DiasAtraso = "";
        this.NRed = "";
        this.DataNF = "";
        this.TotalValor = 0.0;
        
        this.Qtde = "0";
        this.FormaPgtoDescr="";
        this.Rota = "";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public BigDecimal getOC() {
        return OC;
    }

    public void setOC(String OC) {
        try {
            this.OC = new BigDecimal(OC);
        } catch (Exception e) {
            this.OC = new BigDecimal("0");
        }
    }

    public BigDecimal getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        try {
            this.CodCli = new BigDecimal(CodCli);
        } catch (Exception e) {
            this.CodCli = new BigDecimal("0");
        }
    }

    public String getCliente() {
        return Cliente;
    }

    public void setCliente(String Cliente) {
        this.Cliente = Cliente;
    }

    public BigDecimal getNF() {
        return NF;
    }

    public void setNF(String NF) {
        try {
            this.NF = new BigDecimal(NF);
        } catch (Exception e) {
            this.NF = new BigDecimal("0");
        }
    }

    public BigDecimal getPraca() {
        return Praca;
    }

    public void setPraca(String Praca) {
        try {
            this.Praca = new BigDecimal(Praca);
        } catch (Exception e) {
            this.Praca = new BigDecimal("0");
        }
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public BigDecimal getPedido() {
        return Pedido;
    }

    public void setPedido(String Pedido) {
        try {
            this.Pedido = new BigDecimal(Pedido);
        } catch (Exception e) {
            this.Pedido = new BigDecimal("0");
        }
    }

    public Integer getTipoTit() {
        return TipoTit;
    }

    public void setTipoTit(Integer TipoTit) {
        this.TipoTit = TipoTit;
    }

    public Integer getFormaPgto() {
        return FormaPgto;
    }

    public void setFormaPgto(Integer FormaPgto) {
        this.FormaPgto = FormaPgto;
    }

    public String getDtVenc() {
        return DtVenc;
    }

    public void setDtVenc(String DtVenc) {
        this.DtVenc = DtVenc;
    }

    public String getDtPrevPg() {
        return DtPrevPg;
    }

    public void setDtPrevPg(String DtPrevPg) {
        this.DtPrevPg = DtPrevPg;
    }

    public String getCompet() {
        return Compet;
    }

    public void setCompet(String Compet) {
        this.Compet = Compet;
    }

    public String getCompetPg() {
        return CompetPg;
    }

    public void setCompetPg(String CompetPg) {
        this.CompetPg = CompetPg;
    }

    public String getDtPagto() {
        return DtPagto;
    }

    public void setDtPagto(String DtPagto) {
        this.DtPagto = DtPagto;
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public Integer getContaFin() {
        return ContaFin;
    }

    public void setContaFin(Integer ContaFin) {
        this.ContaFin = ContaFin;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public Integer getBoletoSeq() {
        return BoletoSeq;
    }

    public void setBoletoSeq(Integer BoletoSeq) {
        this.BoletoSeq = BoletoSeq;
    }

    public BigDecimal getTCobran() {
        return TCobran;
    }

    public void setTCobran(String TCobran) {
        try {
            this.TCobran = new BigDecimal(TCobran);
        } catch (Exception e) {
            this.TCobran = new BigDecimal("0");
        }
    }

    public String getNossoNumero() {
        return NossoNumero;
    }

    public void setNossoNumero(String NossoNumero) {
        this.NossoNumero = NossoNumero;
    }

    public String getBoleto() {
        return Boleto;
    }

    public void setBoleto(String Boleto) {
        this.Boleto = Boleto;
    }

    public String getCMC7() {
        return CMC7;
    }

    public void setCMC7(String CMC7) {
        this.CMC7 = CMC7;
    }

    public BigDecimal getValorBaixa() {
        return ValorBaixa;
    }

    public void setValorBaixa(String ValorBaixa) {
        try {
            this.ValorBaixa = new BigDecimal(ValorBaixa);
        } catch (Exception e) {
            this.ValorBaixa = new BigDecimal("0");
        }
    }

    public BigDecimal getValorPago() {
        return ValorPago;
    }

    public void setValorPago(String ValorPago) {
        try {
            this.ValorPago = new BigDecimal(ValorPago);
        } catch (Exception e) {
            this.ValorPago = new BigDecimal("0");
        }
    }

    public BigDecimal getJuros() {
        return Juros;
    }

    public void setJuros(String Juros) {
        try {
            this.Juros = new BigDecimal(Juros);
        } catch (Exception e) {
            this.Juros = new BigDecimal("0");
        }
    }

    public BigDecimal getDesconto() {
        return Desconto;
    }

    public void setDesconto(String Desconto) {
        try {
            this.Desconto = new BigDecimal(Desconto);
        } catch (Exception e) {
            this.Desconto = new BigDecimal("0");
        }
    }

    public BigDecimal getCodConta() {
        return CodConta;
    }

    public void setCodConta(String CodConta) {
        try {
            this.CodConta = new BigDecimal(CodConta);
        } catch (Exception e) {
            this.CodConta = new BigDecimal("0");
        }
    }

    public BigDecimal getCheque() {
        return Cheque;
    }

    public void setCheque(String Cheque) {
        try {
            this.Cheque = new BigDecimal(Cheque);
        } catch (Exception e) {
            this.Cheque = new BigDecimal("0");
        }
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getContaC() {
        return ContaC;
    }

    public void setContaC(String ContaC) {
        this.ContaC = ContaC;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public BigDecimal getSeqDesdobra() {
        return SeqDesdobra;
    }

    public void setSeqDesdobra(String SeqDesdobra) {
        try {
            this.SeqDesdobra = new BigDecimal(SeqDesdobra);
        } catch (Exception e) {
            this.SeqDesdobra = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getDiasAtraso() {
        return DiasAtraso;
    }

    public void setDiasAtraso(String DiasAtraso) {
        this.DiasAtraso = DiasAtraso;
    }

    public String getDataNF() {
        return DataNF;
    }

    public void setDataNF(String DataNF) {
        this.DataNF = DataNF;
    }

    public Double getTotalValor() {
        return TotalValor;
    }

    public void setTotalValor(Double TotalValor) {
        this.TotalValor = TotalValor;
    }

    public String getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        this.Qtde = Qtde;
    }

    public String getFormaPgtoDescr() {
        return FormaPgtoDescr;
    }

    public void setFormaPgtoDescr(String FormaPgtoDescr) {
        this.FormaPgtoDescr = FormaPgtoDescr;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public Float getOnePayTotal() {
        return OnePayTotal;
    }

    public void setOnePayTotal(Float OnePayTotal) {
        this.OnePayTotal = OnePayTotal;
    }

    public Float getOnePayTaxa() {
        return OnePayTaxa;
    }

    public void setOnePayTaxa(Float OnePayTaxa) {
        this.OnePayTaxa = OnePayTaxa;
    }

    public Float getOnePayLiquido() {
        return OnePayLiquido;
    }

    public void setOnePayLiquido(Float OnePayLiquido) {
        this.OnePayLiquido = OnePayLiquido;
    }
    
    
}

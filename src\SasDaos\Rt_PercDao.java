package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxFGuias;
import SasBeans.Pedido;
import SasBeans.Rt_Perc;
import SasBeansCompostas.Rt_PercRt_Perc;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.LC2Date;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Horaminuto;
import br.com.sasw.pacotesuteis.utilidades.Sqls;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rt_PercDao {

    public boolean existeRt_Perc(String sequencia, int parada, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT TOP 1 * FROM Rt_Perc WHERE Sequencia = ? AND Parada = ? AND Flag_Excl <> '*' ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setInt(parada);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.existeRt_Perc - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 * FROM Rt_Perc WHERE Sequencia = " + sequencia + " AND Parada = " + parada + " AND Flag_Excl <> '*' ");
        }
    }

    /**
     * Lista os serviços de entrega progamados para uma rota o cliente de
     * destino.
     *
     * @param sequencia
     * @param codCli2
     * @param hora1
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rt_Perc> listarServicosEntrega(String sequencia, String codCli2, String hora1, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> retorno = new ArrayList<>();
            String sql = "SELECT Parada, Hora1, NRed, CodCli1 \n"
                    + " FROM Rt_Perc \n"
                    + " WHERE Sequencia = ? \n"
                    + "     AND CodCli1 = ? \n"
                    + "     AND Hora1 >= ? \n"
                    + "     AND Flag_Excl <> '*'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(codCli2);
            consulta.setString(hora1);
            consulta.select();
            Rt_Perc rt_Perc;
            while (consulta.Proximo()) {
                rt_Perc = new Rt_Perc();
                rt_Perc.setParada(consulta.getInt("parada"));
                rt_Perc.setHora1(consulta.getString("hora1"));
                rt_Perc.setNRed(consulta.getString("nred"));
                rt_Perc.setCodCli1(consulta.getString("CodCli1"));
                retorno.add(rt_Perc);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.servicoHora1 - " + e.getMessage() + "\r\n"
                    + "SELECT Parada, Hora1, NRed, CodCli1 \n"
                    + " FROM Rt_Perc \n"
                    + " WHERE Sequencia = " + sequencia + "\n"
                    + "     AND CodCli1 = " + codCli2 + "n"
                    + "     AND Hora1 >= " + hora1 + " \n"
                    + "     AND Flag_Excl <> '*'");
        }
    }

    public void excluirTrajeto(Rt_Perc Trajeto, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            sql.append("UPDATE Pedido_Upd\n");
            sql.append(" SET   \n");
            sql.append(" Pedido_Upd.Situacao = 'PD',\n");
            sql.append(" Pedido_Upd.SeqRota  = NULL,\n");
            sql.append(" Pedido_Upd.Parada   = NULL\n");
            sql.append(" FROM Pedido\n");
            sql.append(" JOIN Rt_Perc\n");
            sql.append("   ON Pedido.Numero = Rt_Perc.Pedido\n");
            sql.append("  AND Pedido.CodFil = Rt_Perc.CodFil\n");
            sql.append(" JOIN Rt_Perc Rt_Perc_Upd\n");
            sql.append("   ON Rt_Perc.Sequencia = Rt_Perc_Upd.Sequencia\n");
            sql.append("  AND Rt_Perc.Parada  = Rt_Perc_Upd.Parada\n");
            sql.append(" JOIN Pedido Pedido_Upd \n");
            sql.append("   ON Rt_Perc_Upd.Pedido = Pedido_Upd.Numero\n");
            sql.append("  AND Rt_Perc_Upd.CodFil = Pedido_Upd.CodFil\n");
            sql.append(" WHERE Pedido.Numero = ?\n");
            sql.append(" AND   Pedido.CodFil = ?;\n");

            sql.append(" UPDATE Rt_Perc\n");
            sql.append(" SET   \n");
            sql.append(" Rt_Perc.Flag_Excl = '*',\n");
            sql.append(" Rt_Perc.OperExcl  = ?,\n");
            sql.append(" Rt_Perc.Dt_Excl   = ?,\n");
            sql.append(" Rt_Perc.Hr_Excl   = ?,\n");
            sql.append(" Rt_Perc.Pedido    = NULL\n");
            sql.append(" WHERE Rt_Perc.Sequencia = ?\n");
            sql.append(" AND   Rt_Perc.Parada = ?\n");
            sql.append(" AND   Rt_Perc.CodFil = ?;");

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            consulta.setBigDecimal(Trajeto.getPedido());
            consulta.setBigDecimal(Trajeto.getCodFil());

            consulta.setString(Trajeto.getOperExcl());
            consulta.setDate(DataAtual.LC2Date(Trajeto.getDt_Excl()));
            consulta.setString(Trajeto.getHr_Excl());
            consulta.setBigDecimal(Trajeto.getSequencia());
            consulta.setInt(Trajeto.getParada());
            consulta.setBigDecimal(Trajeto.getCodFil());

            consulta.update();

        } catch (Exception e) {
            throw new Exception("Rt_PercDao.cancelarPedido - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    public List<Rt_Perc> listarParadasPreOrder(String sequencia, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> retorno = new ArrayList<>();
            String sql = "    Select  Rt_Perc.Parada\n"
                    + "        from Rt_Perc \n"
                    + "        left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "        left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                           and PreOrder.CodFil  = Rt_Perc.CodFil\n"
                    + "                           and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "        left join RPV on RPV.RPV = PreOrder.RPV\n"
                    + "                  and RPV.parada = Rt_Perc.parada\n"
                    + "        left join PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "        left join Clientes CliDstX on  CliDstX.Codigo = PreOrder.CodCli2\n"
                    + "                                   and CliDstX.CodFil = PreOrder.CodFil\n"
                    + "        left join filiais  on Filiais.Codfil = Rotas.Codfil\n"
                    + "        left Join Pessoa on Pessoa.codigo = RPV.CodPessoaAut\n"
                    + "        where \n"
                    + "Rotas.sequencia = ? \n"
                    + "          and Rt_Perc.Flag_Excl <> '*'\n"
                    + "          and RPV.Flag_Excl <> '*'\n"
                    + "          and RPV.Guia is not null\n"
                    + "Group By Rt_Perc.Parada";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.select();
            Rt_Perc rt_Perc;
            while (consulta.Proximo()) {
                rt_Perc = new Rt_Perc();
                rt_Perc.setParada(consulta.getInt("parada"));
                retorno.add(rt_Perc);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarParadasPreOrder - " + e.getMessage() + "\r\n"
                    + "    Select  Rt_Perc.Parada\n"
                    + "        from Rt_Perc \n"
                    + "        left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "        left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                           and PreOrder.CodFil  = Rt_Perc.CodFil\n"
                    + "                           and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "        left join RPV on RPV.RPV = PreOrder.RPV\n"
                    + "                  and RPV.parada = Rt_Perc.parada\n"
                    + "        left join PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "        left join Clientes CliDstX on  CliDstX.Codigo = PreOrder.CodCli2\n"
                    + "                                   and CliDstX.CodFil = PreOrder.CodFil\n"
                    + "        left join filiais  on Filiais.Codfil = Rotas.Codfil\n"
                    + "        left Join Pessoa on Pessoa.codigo = RPV.CodPessoaAut\n"
                    + "        where \n"
                    + "Rotas.sequencia = " + sequencia + " \n"
                    + "          and Rt_Perc.Flag_Excl <> '*'\n"
                    + "          and RPV.Flag_Excl <> '*'\n"
                    + "          and Guia is not null\n"
                    + "Group By Rt_Perc.Parada");
        }
    }

    public List<Rt_Perc> listarParadasPreOrder(String dtIni, String dtFim, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> retorno = new ArrayList<>();
            String sql = "     Select Rotas.Rota, rotas.Data\n"
                    + "        from Rt_Perc \n"
                    + "        left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "        left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                           and PreOrder.CodFil  = Rt_Perc.CodFil\n"
                    + "                           and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "        left join RPV on RPV.RPV = PreOrder.RPV\n"
                    + "                  and RPV.parada = Rt_Perc.parada\n"
                    + "        where \n"
                    + "Rotas.data between ? and ? \n"
                    + "          and Rt_Perc.Flag_Excl <> '*'\n"
                    + "          and RPV.Flag_Excl <> '*'\n"
                    + "          and Guia is not null\n"
                    + "group by Rotas.Rota, rotas.Data";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtIni);
            consulta.setString(dtFim);
            consulta.select();
            Rt_Perc rt_Perc;
            while (consulta.Proximo()) {
                rt_Perc = new Rt_Perc();
                rt_Perc.setParada(consulta.getInt("parada"));
                retorno.add(rt_Perc);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarParadasPreOrder - " + e.getMessage() + "\r\n"
                    + "    Select  Rt_Perc.Parada\n"
                    + "        from Rt_Perc \n"
                    + "        left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "        left join PreOrder on PreOrder.DtColeta = Rotas.Data\n"
                    + "                           and PreOrder.CodFil  = Rt_Perc.CodFil\n"
                    + "                           and PreOrder.CodCli1 = Rt_perc.CodCli1\n"
                    + "        left join RPV on RPV.RPV = PreOrder.RPV\n"
                    + "                  and RPV.parada = Rt_Perc.parada\n"
                    + "        left join PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "        left join Clientes CliDstX on  CliDstX.Codigo = PreOrder.CodCli2\n"
                    + "                                   and CliDstX.CodFil = PreOrder.CodFil\n"
                    + "        left join filiais  on Filiais.Codfil = Rotas.Codfil\n"
                    + "        left Join Pessoa on Pessoa.codigo = RPV.CodPessoaAut\n"
                    + "        where \n"
                    + "Rotas.sequencia = " + " \n"
                    + "          and Rt_Perc.Flag_Excl <> '*'\n"
                    + "          and RPV.Flag_Excl <> '*'\n"
                    + "          and Guia is not null\n"
                    + "Group By Rt_Perc.Parada");
        }
    }

    /**
     * Verifca a existencia de algum trajeto antes de inserir um novo
     *
     * @param sequencia
     * @param hora1
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Rt_Perc servicoHora1(String sequencia, String hora1, Persistencia persistencia) throws Exception {
        try {
            Rt_Perc retorno = null;
            String sql = "SELECT Parada, Hora1, NRed, CodCli1 \n"
                    + " FROM Rt_Perc \n"
                    + " WHERE Sequencia = ? \n"
                    + "     AND Hora1 = ? \n"
                    + "     AND Flag_Excl <> '*'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(hora1.replace(":", ""));
            consulta.select();
            while (consulta.Proximo()) {
                retorno = new Rt_Perc();
                retorno.setParada(consulta.getInt("parada"));
                retorno.setHora1(consulta.getString("hora1"));
                retorno.setNRed(consulta.getString("nred"));
                retorno.setCodCli1(consulta.getString("CodCli1"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.servicoHora1 - " + e.getMessage() + "\r\n"
                    + "SELECT Parada, Hora1, NRed, CodCli1 \n"
                    + " FROM Rt_Perc \n"
                    + " WHERE Sequencia = " + sequencia + "\n"
                    + "     AND Hora1 = +" + hora1 + " \n"
                    + "     AND Flag_Excl <> '*'");
        }
    }

    /**
     * Lista informações do destino
     *
     * @param sequencia sequencia da rota
     * @param horaprev horaprevista
     * @param codCli codigo do cliente
     * @param persistencia conexão com o banoc
     * @return
     * @throws Exception
     */
    public Rt_Perc listaDestino(String sequencia, String horaprev, String codCli, Persistencia persistencia) throws Exception {
        Rt_Perc destino = new Rt_Perc();
        try {
            String sql = "SELECT HrCheg, HrSaida FROM rt_perc WHERE "
                    + "Sequencia = ? AND Hora1 = ? AND CodCli1 = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(horaprev);
            consulta.setString(codCli);
            consulta.select();

            while (consulta.Proximo()) {
                destino.setHrCheg(consulta.getString("HrCheg"));
                destino.setHrSaida(consulta.getString("HrSaida"));
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listaDestino - " + e.getMessage() + "\r\n"
                    + "SELECT HrCheg, HrSaida FROM rt_perc WHERE "
                    + "Sequencia = " + sequencia + " AND Hora1 = " + horaprev + " AND CodCli1 = " + codCli);
        }
        return destino;
    }

    public boolean existePedido(String sequencia, String parada, Persistencia persistencia) throws Exception {
        boolean retorno = false;
        try {
            String sql = "SELECT ISNULL(Rt_perc.Pedido,0) Pedido"
                    + " FROM Rt_perc "
                    + " WHERE Rt_perc.Sequencia = ? AND Rt_Perc.Parada = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            BigDecimal pedido = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                pedido = consulta.getBigDecimal("Pedido");
            }
            if (pedido.compareTo(BigDecimal.ZERO) == 0) {
                retorno = false;
            } else {
                retorno = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.existePedido - " + e.getMessage() + "\r\n"
                    + "SELECT ISNULL(Rt_perc.Pedido,0) Pedido"
                    + " FROM Rt_perc "
                    + " WHERE Rt_perc.Sequencia = " + sequencia + " AND Rt_Perc.Parada = " + parada);
        }
        return retorno;
    }

    /**
     * Carrega informações sobre o destino
     *
     * @param sequencia sequencia da rota
     * @param parada número da parada
     * @param persistencia conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public Rt_Perc listaOrigem(String sequencia, String parada, Persistencia persistencia) throws Exception {
        Rt_Perc origem = new Rt_Perc();
        try {
            String sql = "SELECT Rt_Perc.CodCli2 codclidst, Rt_Perc.Hora1D horaprevdst, "
                    + "Rt_perc.HrCheg hrCheg, Rt_perc.HrSaida hrSaida, "
                    + "convert(DATE,Rt_perc.Dt_Fech) coleta, Escala.Rota rota, "
                    + "Escala.Veiculo veiculo  "
                    + "  FROM Rt_perc  "
                    + "    JOIN Escala ON  "
                    + "        Escala.SeqRota = Rt_perc.Sequencia  "
                    + "  WHERE Rt_perc.Sequencia = ? AND Rt_Perc.Parada = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            while (consulta.Proximo()) {
                origem.setCodCli2(consulta.getString("codclidst"));
                origem.setHora1D(consulta.getString("horaprevdst"));
                origem.setHrCheg(consulta.getString("hrCheg"));
                origem.setHrSaida(consulta.getString("hrSaida"));
                origem.setColetado(consulta.getString("coleta"));
                origem.setRota(consulta.getString("rota"));
                origem.setVeiculo(consulta.getString("veiculo"));
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listaOrigem - " + e.getMessage() + "\r\n"
                    + "SELECT Rt_Perc.CodCli2 codclidst, Rt_Perc.Hora1D horaprevdst, "
                    + "Rt_perc.HrCheg hrCheg, Rt_perc.HrSaida hrSaida, "
                    + "convert(DATE,Rt_perc.Dt_Fech) coleta, Escala.Rota rota, "
                    + "Escala.Veiculo veiculo  "
                    + "  FROM Rt_perc  "
                    + "    JOIN Escala ON  "
                    + "        Escala.SeqRota = Rt_perc.Sequencia  "
                    + "  WHERE Rt_perc.Sequencia = " + sequencia + " AND Rt_Perc.Parada = " + parada);
        }
        return origem;
    }

    /**
     * Verifica a existencia do caixa forte
     *
     * @param parada numero da parada
     * @param sequencia sequencia da rota
     * @param persistencia conexao com o banco de dados
     * @return se e caixa forte
     * @throws Exception
     */
    public boolean isCaixaForte(String parada, String sequencia, Persistencia persistencia) throws Exception {
        boolean caixaForte = false;
        try {
            String sql = "SELECT count(*) FROM Rt_Perc JOIN CxForte ON Rt_Perc.CodCli1 = CxForte.CodCli "
                    + "   WHERE Rt_Perc.Sequencia = ? AND Rt_Perc.Parada = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade++;
            }

            if (quantidade > 0) {
                caixaForte = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.isCaixaForte - " + e.getMessage() + "\r\n"
                    + "SELECT count(*) FROM Rt_Perc JOIN CxForte ON Rt_Perc.CodCli1 = CxForte.CodCli "
                    + "   WHERE Rt_Perc.Sequencia = " + sequencia + " AND Rt_Perc.Parada = " + parada);
        }
        return caixaForte;
    }

    /*public void salvarBaixaHorarios(Rt_Perc rt_perc, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            sql.append("DECLARE @CodSequencia INT\n");
            sql.append(" SET @CodSequencia = (SELECT COUNT(*)\n");
            sql.append("                      FROM Rt_PercSLA\n");
            sql.append("                      WHERE Sequencia = '").append(rt_perc.getSequencia()).append("'\n");
            sql.append("                      AND   Parada    = '").append(rt_perc.getParada()).append("'\n");
            sql.append("                      AND   CodFil    = '").append(rt_perc.getCodFil()).append("')");

            sql.append(" Update Rt_Perc set");
            sql.append(" HrCheg      = '").append(rt_perc.getHrCheg()).append("',");
            sql.append(" Atraso      = '").append(rt_perc.getAtraso()).append("'");
            if (null != rt_perc.getHrSaida()
                    && !rt_perc.getHrSaida().equals("")) {
                sql.append(", HrSaida     = '").append(rt_perc.getHrSaida()).append("',");
                sql.append(" TempoEspera  = '").append(rt_perc.getTempoEspera()).append("',");
                sql.append(" HrBaixa      = '").append(rt_perc.getHrBaixa()).append("'");
                sql.append(" WHERE Sequencia = '").append(rt_perc.getSequencia()).append("'");
                sql.append(" AND   Parada    = '").append(rt_perc.getParada()).append("'");
            }

        } catch (Exception e) {
            throw new Exception("Rt_PercDao.salvarBaixaHorarios - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }*/
    /**
     * Recupera informacoes dos clientes já baixados
     *
     * @param sequencia sequencia da rota
     * @param persistencia conexao com o banco de dados
     * @return lista de clientes
     * @throws Exception
     */
    public List<Rt_Perc> listaClientesBaixados(String sequencia, Persistencia persistencia) throws Exception {
        List<Rt_Perc> clientes = new ArrayList<>();
        try {
            String sql = "SELECT Rt_Perc.Parada, Rt_Perc.NRed, Rt_Perc.ER, RPV.Impresso, count(preorder.sequencia) QtdPreOrder "
                    + " FROM Rt_Perc "
                    + " JOIN RPV ON RPV.Parada = Rt_Perc.Parada AND RPV.SeqRota = Rt_Perc.Sequencia "
                    + " left join Rotas ON Rotas.Sequencia = Rt_Perc.Sequencia "
                    + " left join PreOrder ON PreOrder.DtColeta = Rotas.Data "
                    + "                  AND PreOrder.CodFil  = Rt_Perc.CodFil "
                    + "                  AND PreOrder.CodCli1 = Rt_perc.CodCli1 "
                    + "                  AND PreOrder.RPV is not null "
                    + " WHERE Rt_Perc.Sequencia = ? AND HrBaixa <> '' "
                    + " GROUP BY Rt_Perc.Parada, Rt_Perc.NRed, Rt_Perc.ER, RPV.Impresso, Rt_Perc.HrCheg "
                    + " ORDER BY HrCheg DESC ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.select();

            Rt_Perc cliente;
            while (consulta.Proximo()) {
                cliente = new Rt_Perc();
                cliente.setER(consulta.getString("ER"));
                cliente.setNRed(consulta.getString("NRed"));
                cliente.setParada(consulta.getInt("Parada"));
                cliente.setImpresso(consulta.getInt("Impresso"));
                cliente.setPedido(consulta.getString("QtdPreOrder"));
                clientes.add(cliente);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listaClientesBaixados - " + e.getMessage() + "\r\n"
                    + "SELECT Rt_Perc.Parada, Rt_Perc.NRed, Rt_Perc.ER, RPV.Impresso, count(preorder.sequencia) QtdPreOrder "
                    + " FROM Rt_Perc "
                    + " JOIN RPV ON RPV.Parada = Rt_Perc.Parada AND RPV.SeqRota = Rt_Perc.Sequencia "
                    + " left join Rotas ON Rotas.Sequencia = Rt_Perc.Sequencia "
                    + " left join PreOrder ON PreOrder.DtColeta = Rotas.Data "
                    + "                  AND PreOrder.CodFil  = Rt_Perc.CodFil "
                    + "                  AND PreOrder.CodCli1 = Rt_perc.CodCli1 "
                    + "                  AND PreOrder.RPV is not null  "
                    + " WHERE Sequencia = " + sequencia + " AND HrBaixa != '' "
                    + " GROUP BY Rt_Perc.Parada, Rt_Perc.NRed, Rt_Perc.ER, RPV.Impresso, Rt_Perc.HrCheg "
                    + " ORDER BY HrCheg DESC");
        }
        return clientes;
    }

    /**
     * Atualiza hora de chegada e atraso(min) da Parada da Rota Selecionada
     *
     * @param sequencia - Seqüência de Rota
     * @param parada - Número da Parada
     * @param hrCheg - Horario de chegada
     * @param atraso - Atraso (min)
     * @param persistencia - Conexão ao banco
     * @return - Lista com dados da parada
     * @throws java.lang.Exception - pode gerar exception
     */
    public boolean updateHrCheg(String sequencia, String parada, String hrCheg,
            String atraso, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE Rt_perc SET HrCheg = ?, Atraso = ?"
                    + " WHERE Sequencia = ?"
                    + " AND Parada = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(hrCheg);
            consulta.setString(atraso);
            consulta.setString(sequencia);
            consulta.setString(parada);

            consulta.update();
            consulta.close();
            return true;
        } catch (SQLException e) {
            throw new Exception("Rt_PercDao.updateHrCheg - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_perc SET HrCheg = " + hrCheg + ", Atraso = " + atraso
                    + " WHERE Sequencia = " + sequencia
                    + " AND Parada = " + parada);
        }
    }

    /**
     * Atualiza hora saída
     *
     * @param Sequencia - Seqüência de Rota
     * @param Parada - Número da Parada
     * @param HrSaida - Horario de Saida
     * @param HrCheg - horario de chegada
     * @param data - data atual
     * @param persistencia - Conexão ao banco
     * @return - Lista com dados da parada
     * @throws java.lang.Exception - pode gerar exception
     */
    public boolean updateHrSaida(String Sequencia, String Parada, String HrSaida, String HrCheg, String data, Persistencia persistencia) throws Exception {
        Horaminuto conversor = new Horaminuto();
        conversor.setHoras(HrSaida, HrCheg);
        String TempoEspera = String.valueOf(conversor.iDifHora1Hora2min());
        try {

            String sql = "UPDATE Rt_perc SET HrSaida =?, HrBaixa =?, Hr_Fech =?,"
                    + " OperFech=?, Dt_Fech=?, TempoEspera =?"
                    + " WHERE Sequencia =? AND Parada =?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(HrSaida);
            consulta.setString(HrSaida);
            consulta.setString(HrSaida);
            consulta.setString("SatMob");
            consulta.setString(data);
            consulta.setString(TempoEspera);
            consulta.setString(Sequencia);
            consulta.setString(Parada);

            consulta.update();
            consulta.close();

            return true;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.updateHrSaida - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_perc SET HrSaida =" + HrSaida + ", HrBaixa =" + HrSaida + ", Hr_Fech =" + HrSaida + ","
                    + " OperFech=" + "SatMob" + ", Dt_Fech=" + data + ", TempoEspera =" + TempoEspera
                    + " WHERE Sequencia =" + Sequencia + " AND Parada =" + Parada);
        }
    }

    /**
     * Atualiza hora saída
     *
     * @param Sequencia - Seqüência de Rota
     * @param Parada - Número da Parada
     * @param HrSaida - Horario de Saida
     * @param HrCheg - horario de chegada
     * @param Atraso - tempo de atraso no atendimto (hora chega menos hora
     * provista)
     * @param Tempo_Espera - tempo para efetuar o serviço (hora saida menos hora
     * chegada)
     * @param OBS
     * @param persistencia - Conexão ao banco
     * @return - Lista com dados da parada
     * @throws java.lang.Exception - pode gerar exception
     */
    public boolean updateBaixaHorarios(String Sequencia, String Parada, String HrSaida, String HrCheg,
            String Atraso, String Tempo_Espera, String OBS,
            Persistencia persistencia) throws Exception {
        String data_sql = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        Horaminuto conversor = new Horaminuto();
        conversor.setHoras(HrSaida, HrCheg);
        String TempoEspera = String.valueOf(conversor.iDifHora1Hora2min());
        try {

            String sql = "UPDATE Rt_perc SET HrCheg = ?, Atraso = ?, HrSaida =?, HrBaixa =?, Hr_Fech =?,"
                    + " OperFech=?, Dt_Fech=?, TempoEspera =?, "
                    + " Observ = substring (observ + ?, 0, 20)"
                    + " WHERE Sequencia =? AND Parada =?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(HrCheg);
            consulta.setString(Atraso);
            consulta.setString(HrSaida);
            consulta.setString(HrSaida);
            consulta.setString(HrSaida);
            consulta.setString("SatMob");
            consulta.setString(data_sql);
            consulta.setString(TempoEspera);
            consulta.setString(OBS);
            consulta.setString(Sequencia);
            consulta.setString(Parada);
            consulta.update();
            consulta.close();

            return true;
        } catch (SQLException e) {
            throw new Exception("Rt_PercDao.updateBaixaHorarios - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_perc SET HrCheg = " + HrCheg + ", Atraso = " + Atraso + ", HrSaida =" + HrSaida + ", HrBaixa =" + HrSaida + ", Hr_Fech =" + HrSaida + ","
                    + " OperFech=" + "SatMob" + ", Dt_Fech=" + data_sql + ", TempoEspera =" + TempoEspera + ", "
                    + " Observ = substring (observ + " + OBS + ", 0, 20)"
                    + " WHERE Sequencia =" + Sequencia + " AND Parada =" + Parada);
        }
    }

    public boolean updateBaixaHorarios(String Sequencia, String Parada, String HrSaida, String HrCheg,
            String Atraso, String Tempo_Espera, String OBS, String data_sql,
            Persistencia persistencia) throws Exception {
        Horaminuto conversor = new Horaminuto();
        conversor.setHoras(HrSaida, HrCheg);
        String TempoEspera = String.valueOf(conversor.iDifHora1Hora2min());
        try {
            String sql = "UPDATE Rt_perc SET HrCheg = ?, Atraso = ?, HrSaida =?, HrBaixa =?, Hr_Fech =?,"
                    + " OperFech=?, Dt_Fech=?, TempoEspera =?, "
                    + " Observ = substring (observ + ?, 0, 120)"
                    + " WHERE Sequencia =? AND Parada =?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(HrCheg);
            consulta.setString(Atraso);
            consulta.setString(HrSaida);
            consulta.setString(HrSaida);
            consulta.setString(HrSaida);
            consulta.setString("SatMob");
            consulta.setString(data_sql);
            consulta.setString(TempoEspera);
            consulta.setString(OBS);
            consulta.setString(Sequencia);
            consulta.setString(Parada);
            consulta.update();
            consulta.close();

            return true;
        } catch (SQLException e) {
            throw new Exception("Rt_PercDao.updateBaixaHorarios - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_perc SET HrCheg = " + HrCheg + ", Atraso = " + Atraso + ", HrSaida =" + HrSaida + ", HrBaixa =" + HrSaida + ", Hr_Fech =" + HrSaida + ","
                    + " OperFech=" + "SatMob" + ", Dt_Fech=" + data_sql + ", TempoEspera =" + TempoEspera + ", "
                    + " Observ = substring (observ + " + OBS + ", 0, 120)"
                    + " WHERE Sequencia =" + Sequencia + " AND Parada =" + Parada);
        }
    }

    /**
     * Busca parada de recolhimento e parada de entrega do malote
     *
     * @param Sequencia - seqüência de rota
     * @param Parada - parada da rota
     * @param persistencia - conexão ao banco
     * @return - Retorna lista com AS paradas
     * @throws Exception - pode gerar exception
     */
    public List<Rt_PercRt_Perc> ClientesParada(String Sequencia, String Parada, Persistencia persistencia) throws Exception {

        List<Rt_PercRt_Perc> listRet = new ArrayList();
        String sql = "select rt_perc1.codcli1, rt_perc1.codcli2, "
                + " rt_perc2.codcli1 codcli3, rt_perc2.codcli2 codcli4 "
                + " FROM Rt_Perc AS rt_perc1 "
                + " left join Rt_Perc AS rt_perc2 ON rt_perc1.sequencia=rt_perc2.sequencia "
                + "                              AND rt_perc1.dpar=rt_perc2.parada "
                + " WHERE rt_perc1.sequencia=? AND rt_perc1.parada=?";
        try {
            Consulta rs = new Consulta(sql, persistencia);
            rs.setString(Sequencia);
            rs.setString(Parada);
            rs.select();
            Rt_PercRt_Perc rt_perc;
            Rt_Perc rt1;
            Rt_Perc rt2;
            while (rs.Proximo()) {
                rt_perc = new Rt_PercRt_Perc();
                rt1 = new Rt_Perc();
                rt2 = new Rt_Perc();
                rt1.setCodCli1(rs.getString("codcli1"));
                rt1.setCodCli2(rs.getString("codcli2"));
                rt2.setCodCli1(rs.getString("codcli3"));
                rt2.setCodCli2(rs.getString("codcli4"));
                rt_perc.setOrigem(rt1);
                rt_perc.setDestino(rt2);
                listRet.add(rt_perc);
            }
            rs.Close();
            return listRet;
        } catch (Exception e) {
            //throw new Exception("Rt_PercDao. - " + e.getMessage());
            return listRet;
        }
    }

    /**
     * Atualiza Obs
     *
     * @param sequencia - seqüência de rota
     * @param parada - seqüência de rota
     * @param obs - Observação rota
     * @param persistencia - conexão ao banco
     * @throws Exception - pode gerar exception
     */
    public void atualizaOBS(String sequencia, String parada, String obs, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE Rt_Perc "
                    + " set Observ=?"
                    + " WHERE Sequencia=?"
                    + " AND Parada=?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(FuncoesString.RecortaString(obs, 0, 20));
            consulta.setString(sequencia);
            consulta.setString(parada);

            consulta.update();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Rt_PercDao.atualizaOBS - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_Perc "
                    + " set Observ=" + FuncoesString.RecortaString(obs, 0, 20)
                    + " WHERE Sequencia=" + sequencia
                    + " AND Parada=" + parada);
        }
    }

    /**
     * Substrai o valor da guia no campo valor da rt_perc
     *
     * @param valor - Valor
     * @param sequencia - Número da sequencia
     * @param parada - Número de paradas
     * @param persistencia - Conexão ao banco
     * @throws Exception
     */
    public void atualizaValGuia(String valor, String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE rt_perc set valor = valor-?"
                    + " WHERE sequencia=?"
                    + " AND parada=?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(sequencia);
            consulta.setString(parada);

            consulta.update();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Rt_PercDao.atualizaValGuia - " + e.getMessage() + "\r\n"
                    + "UPDATE rt_perc set valor = valor-" + valor
                    + " WHERE sequencia=" + sequencia
                    + " AND parada=" + parada);
        }
    }

    /*
     * Define valor da parada
     * @param persistencia - Conexão ao banco
     * @param sequencia - Seqüência de Rota
     * @param valor - Valor
     * @param parada - Número da parada
     * @throws java.lang.Exception - pode gerar exception
     */
    public void defineValorParada(Persistencia persistencia, String sequencia, String valor, String parada) throws Exception {

        String sql = "UPDATE Rt_Perc set Valor = ?"
                + " WHERE Sequencia=? AND Parada=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.defineValorParada - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_Perc set Valor = " + valor
                    + " WHERE Sequencia=" + sequencia + " AND Parada=" + parada);
        }
    }

    /*
     * Atualiza rota percorrida
     * @param persistencia - Conexão ao banco
     * @param sequencia - Seqüência de Rota
     * @param valor - Valor
     * @param parada - Número da parada
     * @throws java.lang.Exception - pode gerar exception
     */
    public void atualizaValorParada(Persistencia persistencia, String sequencia, String valor, String parada) throws Exception {

        String sql = "UPDATE Rt_Perc set Valor = Valor + ?"
                + " WHERE Sequencia=? AND Parada=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.atualizaValorParada - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_Perc set Valor = Valor + " + valor
                    + " WHERE Sequencia=" + sequencia + " AND Parada=" + parada);
        }
    }

    public void atualizaValorRB(Persistencia persistencia, String CodFil, String sequencia, String valor, String parada) throws Exception {

        String sql = "UPDATE Rt_Perc set Valor = Valor - ?"
                + " WHERE CodFil=? AND Sequencia=? AND Parada=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(CodFil);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.atualizaValorRB - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_Perc set Valor = Valor - " + valor
                    + " WHERE CodFil=" + CodFil + " AND Sequencia=" + sequencia + " AND Parada=" + parada);
        }
    }

    /**
     * Verifica Os guias
     *
     * @param persistencia - Conexão ao banco
     * @param sequencia - Número da sequência
     * @param parada - Número da Parada
     * @return - Retorna um codcli1
     * @throws java.lang.Exception - pode gerar exception
     */
    public String ClienteParada(Persistencia persistencia, String sequencia, String parada) throws Exception {
        String codcli1 = "";
        String sql = "select codcli1 "
                + " FROM Rt_Perc "
                + " WHERE sequencia=?"
                + " AND parada=?";
        try {
            Consulta rs = new Consulta(sql, persistencia);
            rs.setString(sequencia);
            rs.setInt(Integer.parseInt(parada));
            rs.select();
            while (rs.Proximo()) {
                codcli1 = rs.getString("codcli1");
            }
            rs.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.ClienteParada - " + e.getMessage() + "\r\n"
                    + "select codcli1 "
                    + " FROM Rt_Perc "
                    + " WHERE sequencia=" + sequencia
                    + " AND parada=" + parada);
        }
        return codcli1;
    }

    /**
     * Retorna a última parada da sequencia de rota
     *
     * @param sequencia - sequencia da rota
     * @param persistencia - conexão ao banco de dados
     * @return - retorna a maior parada da rota atual
     * @throws Exception
     */
    public int getMaxParada(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        int retorno = 0;
        try {
            String sql = "select max(parada) 'parada' "
                    + " FROM rt_perc "
                    + " WHERE sequencia = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(sequencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = consult.getInt("parada");
                } catch (Exception ex) {
                    retorno = 0;
                }
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.getMaxParada - " + e.getMessage() + "\r\n"
                    + "select max(parada) 'parada' "
                    + " FROM rt_perc "
                    + " WHERE sequencia = " + sequencia);
        }
        return retorno;
    }

    /**
     * Inserção de parada
     *
     * @param rt_perc - parada a ser inserido
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void Inserir(Rt_Perc rt_perc, Persistencia persistencia) throws Exception {
        try {
            String sql = Sqls.montaInsert(new Rt_Perc());
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rt_perc.getSequencia());
            consulta.setInt(rt_perc.getParada());
            consulta.setBigDecimal(rt_perc.getCodFil());
            consulta.setString(rt_perc.getHora1());
            consulta.setString(rt_perc.getER());
            consulta.setString(rt_perc.getCodCli1());
            consulta.setString(rt_perc.getNRed());
            consulta.setString(rt_perc.getCodCli2());
            consulta.setString(rt_perc.getHora1D());
            consulta.setString(rt_perc.getTipoSrv());
            consulta.setString(rt_perc.getChave());
            consulta.setInt(rt_perc.getDPar());
            consulta.setString(rt_perc.getRegiao());
            consulta.setString(rt_perc.getObserv());
            consulta.setBigDecimal(rt_perc.getValor());
            consulta.setString(rt_perc.getHrBaixa());
            consulta.setString(rt_perc.getHrCheg());
            consulta.setString(rt_perc.getHrSaida());
            consulta.setBigDecimal(rt_perc.getAtraso());
            consulta.setBigDecimal(rt_perc.getTempoEspera());
            consulta.setBigDecimal(rt_perc.getOS());
            consulta.setString(rt_perc.getOperIncl());
            consulta.setDate(DataAtual.LC2Date(rt_perc.getDt_Incl()));
            consulta.setString(rt_perc.getHr_Incl());
            consulta.setString(rt_perc.getOperador());
            consulta.setDate(DataAtual.LC2Date(rt_perc.getDt_Alter()));
            consulta.setString(rt_perc.getHr_Alter());
            consulta.setString(rt_perc.getOperExcl());
            consulta.setDate(DataAtual.LC2Date(rt_perc.getDt_Excl()));
            consulta.setString(rt_perc.getHr_Excl());
            consulta.setString(rt_perc.getOperFech());
            consulta.setDate(DataAtual.LC2Date(rt_perc.getDt_Fech()));
            consulta.setString(rt_perc.getHr_Fech());
            consulta.setString(rt_perc.getCodLan());
            consulta.setBigDecimal(rt_perc.getPedido());
            consulta.setString(rt_perc.getFlag_Excl());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.Inserir - " + e.getMessage());
        }
    }

    /**
     * Atualiza a flag_excl da Rt_Perc
     *
     * @param rt_perc - campos importante: flag_excl, oper_excl, dt_excl,
     * hr_excl, sequencia e parada
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void Atualiza_Flag_Excl(Rt_Perc rt_perc, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE rt_perc set flag_Excl = ?, operexcl=?, dt_excl=?, hr_excl=? WHERE "
                    + " sequencia = ? AND parada = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rt_perc.getFlag_Excl());
            consulta.setString(rt_perc.getOperExcl());
            consulta.setDate(DataAtual.LC2Date(rt_perc.getDt_Excl()));
            consulta.setString(rt_perc.getHr_Excl());
            consulta.setBigDecimal(rt_perc.getSequencia());
            consulta.setInt(rt_perc.getParada());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.Atualiza_Flag_Excl - " + e.getMessage() + "\r\n"
                    + "UPDATE rt_perc set flag_Excl = " + rt_perc.getFlag_Excl() + ", operexcl=" + rt_perc.getOperExcl() + ", dt_excl=" + rt_perc.getDt_Excl() + ", "
                    + " hr_excl=" + rt_perc.getHr_Excl() + " WHERE "
                    + " sequencia = " + rt_perc.getSequencia() + " AND parada = " + rt_perc.getParada());
        }
    }

    /**
     * Excluir todas AS paradas de uma rota (usado somente quando se exclui a
     * rota)
     *
     * @param Sequencia - Sequencia da rota
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void ExcluirPardasRota(BigDecimal Sequencia, Persistencia persistencia) throws Exception {
        try {
            String Sql = "delete FROM rt_perc WHERE sequencia = ?";
            Consulta consulta = new Consulta(Sql, persistencia);
            consulta.setBigDecimal(Sequencia);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.ExcluirPardasRota - " + e.getMessage() + "\r\n"
                    + "delete FROM rt_perc WHERE sequencia = " + Sequencia);
        }
    }

    /**
     * Obtem o maximo da sequencia do trajeto
     *
     * @param persistencia Conexão com o banco de dados
     * @return sequencia máximo
     * @throws Exception
     */
    public BigDecimal maxSequencia(Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT max(sequencia) seq FROM rt_perc";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                return consulta.getBigDecimal("seq");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.maxSequencia - " + e.getMessage() + "\r\n"
                    + "SELECT max(sequencia) seq FROM rt_perc");
        }
        return new BigDecimal("");
    }

    /**
     * Cria trajetos para supervisao
     *
     * @param rt_Perc Objeto contendo informações - Campos necessários:
     * sequencia, parada, CodFil, Hora1, er, codCli1, nred, tiposrv, regiao,
     * observ
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void inserirTrajetos(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO rt_perc (sequencia, parada, CodFil, Hora1,"
                    + "er, codCli1, nred, tiposrv, flag_excl) VALUES(?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(rt_Perc.getSequencia());
            consulta.setInt(rt_Perc.getParada());
            consulta.setBigDecimal(rt_Perc.getCodFil());
            consulta.setString(rt_Perc.getHora1());
            consulta.setString(rt_Perc.getER());
            consulta.setString(rt_Perc.getCodCli1());
            consulta.setString(rt_Perc.getNRed());
            consulta.setString(rt_Perc.getTipoSrv());
            consulta.setString(rt_Perc.getFlag_Excl());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.inserirTrajetos - " + e.getMessage() + "\r\n"
                    + "INSERT INTO rt_perc (sequencia, parada, CodFil, Hora1,"
                    + "er, codCli1, nred, tiposrv, flag_excl) VALUES(" + rt_Perc.getSequencia() + "," + rt_Perc.getParada() + "," + rt_Perc.getCodFil() + ","
                    + rt_Perc.getHora1() + "," + rt_Perc.getER() + "," + rt_Perc.getCodCli1() + "," + rt_Perc.getNRed() + "," + rt_Perc.getTipoSrv() + ","
                    + rt_Perc.getFlag_Excl() + ")");
        }
    }

    /**
     * Cria trajetos para supervisao
     *
     * @param rt_Perc Objeto contendo informações - Campos necessários:
     * sequencia, parada, CodFil, Hora1, er, codCli1, nred, tiposrv, regiao,
     * observ
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void inserirTrajeto(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO Rt_Perc (Sequencia, CodFil, Parada, Hora1, ER, TipoSrv,\n"
                    + "    CodCli1, CodCli2, Hora1D, DPar, \n"
                    + "    Pedido, Valor, Observ, Flag_Excl, \n"
                    + "    Operador, Dt_Alter, Hr_Alter, OS,\n"
                    + "    Dt_Incl, Hr_Incl, OperIncl,\n"
                    + "    NRed, Regiao)\n"
                    + "VALUES (?, ?, ?, ?, ?, ?,\n"
                    + "    ?, ?, ?, ?, \n"
                    + "    ?, ?, ?, ?, \n"
                    + "    ?, ?, ?, ?,\n"
                    + "    ?, ?, ?, \n"
                    + "    (SELECT TOP 1 NRed FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?),\n"
                    + "    (SELECT TOP 1 Regiao FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?))";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(rt_Perc.getSequencia());
            consulta.setBigDecimal(rt_Perc.getCodFil());
            consulta.setInt(rt_Perc.getParada());
            consulta.setString(rt_Perc.getHora1().replace(":", ""));
            consulta.setString(rt_Perc.getER());
            consulta.setString(rt_Perc.getTipoSrv());

            consulta.setString(rt_Perc.getCodCli1());
            consulta.setString(rt_Perc.getCodCli2());
            consulta.setString(rt_Perc.getHora1D().replace(":", ""));
            consulta.setInt(rt_Perc.getDPar());

            consulta.setBigDecimal(rt_Perc.getPedido());
            consulta.setString(rt_Perc.getValor());
            consulta.setString(rt_Perc.getObserv());
            consulta.setString(rt_Perc.getFlag_Excl());

            consulta.setString(rt_Perc.getOperador());
            consulta.setString(rt_Perc.getDt_Alter().toString());
            consulta.setString(rt_Perc.getHr_Alter());
            consulta.setBigDecimal(rt_Perc.getOS());

            consulta.setString(rt_Perc.getDt_Incl().toString());
            consulta.setString(rt_Perc.getHr_Incl());
            consulta.setString(rt_Perc.getOperIncl());

            consulta.setString(rt_Perc.getCodCli1());
            consulta.setBigDecimal(rt_Perc.getCodFil());

            consulta.setString(rt_Perc.getCodCli1());
            consulta.setBigDecimal(rt_Perc.getCodFil());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.inserirTrajeto - " + e.getMessage() + "\r\n"
                    + "INSERT INTO Rt_Perc (Sequencia, CodFil, Parada, Hora1, ER, TipoSrv,\n"
                    + "    CodCli1, CodCli2, Hora1D, DPar, \n"
                    + "    Pedido, Valor, Observ, Flag_Excl, \n"
                    + "    Operador, Dt_Alter, Hr_Alter, OS,\n"
                    + "    Dt_Incl, Hr_Incl, OperIncl)\n"
                    + "VALUES (" + rt_Perc.getSequencia() + ", " + rt_Perc.getCodFil() + ", " + rt_Perc.getParada() + ", " + rt_Perc.getHora1() + ", " + rt_Perc.getER() + ", " + rt_Perc.getTipoSrv() + ","
                    + "    " + rt_Perc.getCodCli1() + ", " + rt_Perc.getCodCli2() + ", " + rt_Perc.getHora1D() + ", " + rt_Perc.getDPar() + ", "
                    + "    " + rt_Perc.getPedido() + ", " + rt_Perc.getValor() + ", " + rt_Perc.getObserv() + ", " + rt_Perc.getFlag_Excl() + ", "
                    + "    " + rt_Perc.getOperador() + ", " + rt_Perc.getDt_Alter() + ", " + rt_Perc.getHr_Alter() + ", " + rt_Perc.getOS() + ","
                    + "    " + rt_Perc.getDt_Incl() + ", " + rt_Perc.getHr_Incl() + ", " + rt_Perc.getOperIncl() + ")");
        }
    }

    /**
     * Lista os registros do trajeto
     *
     * @param sequencia sequencia da rota
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os trajetos
     * @throws Exception
     */
    public List<Rt_Perc> listarTrajetos(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        List<Rt_Perc> trajetos = new ArrayList();
        try {
            String sql = "SELECT * FROM rt_perc WHERE sequencia = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();

            Rt_Perc trajeto = null;
            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();

                trajeto.setSequencia(consulta.getString("sequencia"));
                trajeto.setParada(consulta.getInt("parada"));
                trajeto.setCodFil(consulta.getString("codfil"));
                trajeto.setHora1(consulta.getString("hora1"));
                trajeto.setER(consulta.getString("er"));
                trajeto.setCodCli1(consulta.getString("codcli1"));
                trajeto.setNRed(consulta.getString("nred"));
                trajeto.setCodCli2(consulta.getString("codcli2"));
                trajeto.setHora1D(consulta.getString("hora1d"));
                trajeto.setTipoSrv(consulta.getString("tiposrv"));
                trajeto.setChave(consulta.getString("chave"));
                trajeto.setDPar(consulta.getInt("DPAR"));
                trajeto.setRegiao(consulta.getString("regiao"));
                trajeto.setObserv(consulta.getString("observ"));
                trajeto.setValor(consulta.getString("valor"));
                trajeto.setHrBaixa(consulta.getString("hrbaixa"));
                trajeto.setHrSaida(consulta.getString("hrsaida"));
                trajeto.setHrCheg(consulta.getString("hrcheg"));
                trajeto.setAtraso(consulta.getString("atraso"));
                trajeto.setTempoEspera(consulta.getString("tempoespera"));
                trajeto.setOS(consulta.getString("OS"));
                trajeto.setOperIncl(consulta.getString("OperIncl"));
                trajeto.setDt_Alter(consulta.getLocalDate("dt_incl"));
                trajeto.setHr_Incl(consulta.getString("hr_incl"));
                trajeto.setOperador(consulta.getString("operador"));
                trajeto.setDt_Alter(consulta.getLocalDate("dt_alter"));
                trajeto.setHr_Alter(consulta.getString("hr_alter"));
                trajeto.setOperExcl(consulta.getString("operexcl"));
                trajeto.setHr_Excl(consulta.getString("hr_excl"));
                trajeto.setOperFech(consulta.getString("operfech"));
                trajeto.setHr_Fech(consulta.getString("hr_fech"));
                trajeto.setCodLan(consulta.getString("codlan"));
                trajeto.setPedido(consulta.getString("pedido"));
                trajeto.setFlag_Excl(consulta.getString("flag_excl"));

                trajetos.add(trajeto);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarTrajetos - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM rt_perc WHERE sequencia = " + sequencia);
        }
        return trajetos;
    }

    /**
     * Inseri cliente na rota
     *
     * @param perc Objeto de rt_perc
     * @param persistencia conexão com o banco de dados
     * @throws Exception
     */
    public void inserirClientes(Rt_Perc perc, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO Rt_Perc (sequencia, parada, codfil, "
                    + "hora1, er, codcli1, nred, tiposrv, operincl, hr_incl,"
                    + " operador, hr_alter,flag_excl,dt_alter, dt_incl) VALUES(?, ?, ?, "
                    + "?, ?, ?, ?, ?, ?, ?,"
                    + " ?, ?,?, convert(DATE, getDate()), convert(DATE, getDate()))";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(perc.getSequencia());
            consulta.setInt(perc.getParada());
            consulta.setBigDecimal(perc.getCodFil());
            consulta.setString(perc.getHora1());
            consulta.setString(perc.getER());
            consulta.setString(perc.getCodCli1());
            consulta.setString(perc.getNRed());
            consulta.setString(perc.getTipoSrv());
            consulta.setString(perc.getOperIncl());
            consulta.setString(perc.getHr_Incl());
            consulta.setString(perc.getOperador());
            consulta.setString(perc.getHr_Alter());
            consulta.setString(perc.getFlag_Excl());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.inserirClientes - " + e.getMessage() + "\r\n"
                    + "INSERT INTO Rt_Perc (sequencia, parada, codfil, "
                    + "hora1, er, codcli1, nred, tiposrv, operincl, hr_incl,"
                    + " operador, hr_alter,flag_excl,dt_alter, dt_incl) VALUES(" + perc.getSequencia() + "," + perc.getParada() + "," + perc.getCodFil() + ","
                    + perc.getHora1() + "," + perc.getER() + "," + perc.getCodCli1() + "," + perc.getNRed() + "," + perc.getTipoSrv() + "," + perc.getOperIncl() + ","
                    + perc.getHr_Incl() + "," + perc.getOperador() + "," + perc.getHr_Alter() + "," + perc.getFlag_Excl() + ", "
                    + " convert(DATE, getDate()), convert(DATE, getDate()))");

        }
    }

    public void inserirClientes(Rt_Perc perc, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO Rt_Perc (sequencia, parada, codfil, "
                    + "hora1, er, codcli1, nred, tiposrv, operincl, hr_incl,"
                    + " operador, hr_alter,flag_excl,dt_alter, dt_incl) VALUES(?, ?, ?, "
                    + "?, ?, ?, ?, ?, ?, ?,"
                    + " ?, ?, ?, ?,?)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(perc.getSequencia());
            consulta.setInt(perc.getParada());
            consulta.setBigDecimal(perc.getCodFil());
            consulta.setString(perc.getHora1());
            consulta.setString(perc.getER());
            consulta.setString(perc.getCodCli1());
            consulta.setString(perc.getNRed());
            consulta.setString(perc.getTipoSrv());
            consulta.setString(perc.getOperIncl());
            consulta.setString(perc.getHr_Incl());
            consulta.setString(perc.getOperador());
            consulta.setString(perc.getHr_Alter());
            consulta.setString(perc.getFlag_Excl());
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.inserirClientes - " + e.getMessage() + "\r\n"
                    + "INSERT INTO Rt_Perc (sequencia, parada, codfil, "
                    + "hora1, er, codcli1, nred, tiposrv, operincl, hr_incl,"
                    + " operador, hr_alter,flag_excl,dt_alter, dt_incl) VALUES(" + perc.getSequencia() + "," + perc.getParada() + "," + perc.getCodFil() + ","
                    + perc.getHora1() + "," + perc.getER() + "," + perc.getCodCli1() + "," + perc.getNRed() + "," + perc.getTipoSrv() + "," + perc.getOperIncl() + ","
                    + perc.getHr_Incl() + "," + perc.getOperador() + "," + perc.getHr_Alter() + "," + perc.getFlag_Excl() + ", " + dataAtual + ", " + dataAtual + ")");
        }
    }

    /**
     * Cria trajetos para supervisao no projeto SatMobWeb
     *
     * @param rt_Perc Objeto contendo informações - Campos necessários:
     * sequencia, parada, CodFil, Hora1, er, codCli1, nred, tiposrv, Flag_excl,
     * operincl, dt_incl, hr_incl, operador, dt_alter, hr_alter
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void inserirTrajetosSatMobWeb(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO rt_perc (sequencia, parada, CodFil, Hora1,"
                    + "er, codCli1, nred, tiposrv, flag_excl, operincl, dt_incl, hr_incl,"
                    + "operador, dt_alter, hr_alter, Observ, OperFech, HrBaixa, OS) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(rt_Perc.getSequencia());
            consulta.setInt(rt_Perc.getParada());
            consulta.setBigDecimal(rt_Perc.getCodFil());
            consulta.setString(rt_Perc.getHora1());
            consulta.setString(rt_Perc.getER());
            consulta.setString(rt_Perc.getCodCli1());
            consulta.setString(rt_Perc.getNRed());
            consulta.setString(rt_Perc.getTipoSrv());
            consulta.setString(rt_Perc.getFlag_Excl());
            consulta.setString(rt_Perc.getOperIncl());
            consulta.setDate(DataAtual.LC2Date(rt_Perc.getDt_Incl()));
            consulta.setString(rt_Perc.getHr_Incl());
            consulta.setString(rt_Perc.getOperador());
            consulta.setDate(DataAtual.LC2Date(rt_Perc.getDt_Alter()));
            consulta.setString(rt_Perc.getHr_Alter());
            consulta.setString(rt_Perc.getObserv());
            consulta.setString(rt_Perc.getOperFech());
            consulta.setString(rt_Perc.getHrBaixa());
            consulta.setBigDecimal(rt_Perc.getOS());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.inserirTrajetosSatMobWeb - " + e.getMessage() + "\r\n"
                    + "INSERT INTO rt_perc (sequencia, parada, CodFil, Hora1,"
                    + "er, codCli1, nred, tiposrv, flag_excl, operincl, dt_incl, hr_incl,"
                    + "operador, dt_alter, hr_alter, Observ) VALUES(" + rt_Perc.getSequencia() + "," + rt_Perc.getParada() + "," + rt_Perc.getCodFil() + ","
                    + rt_Perc.getHora1() + "," + rt_Perc.getER() + "," + rt_Perc.getCodCli1() + "," + rt_Perc.getNRed() + "," + rt_Perc.getTipoSrv() + ","
                    + rt_Perc.getFlag_Excl() + "," + rt_Perc.getOperIncl() + "," + rt_Perc.getDt_Incl() + "," + rt_Perc.getHr_Incl() + ","
                    + rt_Perc.getOperador() + "," + rt_Perc.getDt_Alter() + "," + rt_Perc.getHr_Alter() + "," + rt_Perc.getObserv() + ")");
        }
    }

    public void atualizarTrajeto(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE Rt_Perc set Hora1 = ?,  ER = ?, TipoSrv = ?, CodCli1 = ?, NRed = ?, Regiao = ?, CodCli2 = ?, \n"
                    + " Hora1D = ?, DPar = ?, Observ = ?, OS = ?, Valor = ?, Pedido = NULL, Operador = ?, Dt_Alter = ?, Hr_Alter = ? \n"
                    + " WHERE Sequencia = ? AND Parada = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rt_Perc.getHora1());
            consulta.setString(rt_Perc.getER());
            consulta.setString(rt_Perc.getTipoSrv());
            consulta.setString(rt_Perc.getCodCli1());
            consulta.setString(rt_Perc.getNRed());
            consulta.setString(rt_Perc.getRegiao());
            consulta.setString(rt_Perc.getCodCli2());
            consulta.setString(rt_Perc.getHora1D());
            consulta.setInt(rt_Perc.getDPar());
            consulta.setString(rt_Perc.getObserv());
            consulta.setBigDecimal(rt_Perc.getOS());
            consulta.setString(rt_Perc.getValor());
            consulta.setString(rt_Perc.getOperador());
            consulta.setDate(LC2Date(rt_Perc.getDt_Alter()));
            consulta.setString(rt_Perc.getHr_Alter());
            consulta.setBigDecimal(rt_Perc.getSequencia());
            consulta.setInt(rt_Perc.getParada());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.atualizarTrajeto - " + e.getMessage() + "\n\r"
                    + " UPDATE Rt_Perc set Hora1 = ?,  ER = ?, TipoSrv = ?, CodCli1 = ?, NRed = ?, Regiao = ?, CodCli2 = ?, \n"
                    + " Hora1D = ?, DPar = ?, Observ = ?, OS = ?, Valor = ?, Pedido = NULL, Operador = ?, Dt_Alter = ?, Hr_Alter = ? \n"
                    + " WHERE Sequencia = ? AND Parada = ? ");
        }
    }

    public void atualizarOSEmpresaCacamba(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "UPDATE Rt_Perc\n"
                    + " SET Rt_Perc.OS = os_vig.OS\n"
                    + " FROM Rt_Perc \n"
                    + " JOIN os_vig \n"
                    + "   ON Rt_Perc.codcli1 = os_vig.cliente\n"
                    + " WHERE Sequencia = ?\n"
                    + " AND   Rt_Perc.OS = 0\n"
                    + " AND   Rt_Perc.codcli1 NOT IN(9996900, 9996015)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(rt_Perc.getSequencia());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.atualizarOSEmpresaCacamba - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Atualizar trajetos da rota do projeto SatMobWeb
     *
     * @param rt_Perc Objeto de rt_parc
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void atualizarTrajetosSatMobWeb(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE rt_perc SET CodFil = ?, Hora1 = ?,"
                    + "er = ?, codCli1 = ?, nred = ?, tiposrv = ?, "
                    + "operador = ?, dt_alter = ?, hr_alter = ?, "
                    + "Observ = ?"
                    + " WHERE sequencia = ? AND parada = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(rt_Perc.getCodFil());
            consulta.setString(rt_Perc.getHora1());
            consulta.setString(rt_Perc.getER());
            consulta.setString(rt_Perc.getCodCli1());
            consulta.setString(rt_Perc.getNRed());
            consulta.setString(rt_Perc.getTipoSrv());
            consulta.setString(rt_Perc.getOperador());
            consulta.setDate(DataAtual.LC2Date(rt_Perc.getDt_Alter()));
            consulta.setString(rt_Perc.getHr_Alter());
            consulta.setString(rt_Perc.getObserv());
            consulta.setBigDecimal(rt_Perc.getSequencia());
            consulta.setInt(rt_Perc.getParada());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.atualizarTrajetosSatMobWeb - " + e.getMessage() + "\r\n"
                    + "UPDATE rt_perc SET CodFil = " + rt_Perc.getCodFil() + ", Hora1 = " + rt_Perc.getHora1() + ","
                    + "er = " + rt_Perc.getER() + ", codCli1 = " + rt_Perc.getCodCli1() + ", nred = " + rt_Perc.getNRed() + ", tiposrv = " + rt_Perc.getTipoSrv() + ", "
                    + "operador = " + rt_Perc.getOperador() + ", dt_alter = " + rt_Perc.getDt_Alter() + ", hr_alter = " + rt_Perc.getHr_Alter() + ", "
                    + "Observ = " + rt_Perc.getObserv() + ""
                    + " WHERE sequencia = " + rt_Perc.getSequencia() + " AND parada = " + rt_Perc.getParada());
        }
    }

    /**
     * Excluir todas AS paradas de uma rota (usado somente quando se exclui a
     * rota)
     *
     * @param rt_Perc Paradas a serem excluídas
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void ExcluirParadasRotaSatMobWeb(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            String Sql = "UPDATE rt_perc SET flag_excl = ?,"
                    + "operexcl = ?, dt_excl = ?, hr_excl = ?"
                    + " WHERE sequencia = ?";

            Consulta consulta = new Consulta(Sql, persistencia);
            consulta.setString("*");
            consulta.setString(rt_Perc.getOperExcl());
            consulta.setDate(DataAtual.LC2Date(rt_Perc.getDt_Excl()));
            consulta.setString(rt_Perc.getHr_Excl());
            consulta.setBigDecimal(rt_Perc.getSequencia());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.ExcluirParadasRotaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "UPDATE rt_perc SET flag_excl = " + "*" + ","
                    + "operexcl = " + rt_Perc.getOperExcl() + ", dt_excl = " + rt_Perc.getDt_Excl() + ", hr_excl = " + rt_Perc.getHr_Excl()
                    + " WHERE sequencia = " + rt_Perc.getSequencia());
        }
    }

    /**
     * Insere flag_excl em apenas um trajeto
     *
     * @param rt_Perc Trajeto a ser excluído
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void ExcluirParadaSatMobWeb(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            String Sql = "UPDATE rt_perc SET flag_excl = ?,"
                    + " operexcl = ?, dt_excl = ?, hr_excl = ?"
                    + " WHERE sequencia = ? AND parada = ?";

            Consulta consulta = new Consulta(Sql, persistencia);
            consulta.setString("*");
            consulta.setString(rt_Perc.getOperExcl());
            consulta.setDate(DataAtual.LC2Date(rt_Perc.getDt_Excl()));
            consulta.setString(rt_Perc.getHr_Excl());
            consulta.setBigDecimal(rt_Perc.getSequencia());
            consulta.setInt(rt_Perc.getParada());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.ExcluirParadaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "UPDATE rt_perc SET flag_excl = " + "*" + ","
                    + " operexcl = " + rt_Perc.getOperExcl() + ", dt_excl = " + rt_Perc.getDt_Excl() + ", hr_excl = " + rt_Perc.getHr_Excl()
                    + " WHERE sequencia = " + rt_Perc.getSequencia() + " AND parada = " + rt_Perc.getParada());
        }
    }

    public String consultarDataSequencia(BigDecimal sequencia, String CodFil, Persistencia persistencia) throws Exception {
        String sql = "", Retorno = "";

        try {
            sql = "SELECT Data FROM Rotas\n"
                    + " WHERE Rotas.Sequencia = ?\n"
                    + " AND   Rotas.CodFil    = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.setString(CodFil);

            consulta.select();

            while (consulta.Proximo()) {
                Retorno = consulta.getString("Data");
            }

            consulta.close();

            return Retorno;

        } catch (Exception e) {
            throw new Exception("Rt_PercDao.ExcluirParadaSatMobWeb - " + sql);
        }
    }

    /**
     * Lista os registros do trajeto
     *
     * @param sequencia sequencia da rota
     * @param excl Booleano para mostrar ou não rotas excluídas
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os trajetos
     * @throws Exception
     */
    public List<Rt_Perc> listarTrajetosSatMobWeb(BigDecimal sequencia, Boolean excl, Persistencia persistencia) throws Exception {
        List<Rt_Perc> trajetos = new ArrayList();
        try {
            String sql = "SELECT\n"
                    + " (SELECT * FROM (SELECT (';' + CONVERT(varchar(100), CONVERT(BIGINT, Rt_Guias.Guia)) + '_' + Rt_Guias.Serie) Guias\n"
                    + "            FROM Rt_Guias (nolock)\n"
                    + "            JOIN XMLGTVE (nolock)\n"
                    + "              ON Rt_Guias.Guia  = XMLGTVE.Guia\n"
                    + "             AND Rt_Guias.Serie = XMLGTVE.Serie\n"
                    + "            WHERE Rt_Guias.Sequencia = Rotas.Sequencia\n"
                    + "            AND   Rt_Guias.Parada    = Rt_Perc.Parada) a For XML PATH('')) AS guiasGTVe,"
                    + "(SELECT * FROM \n"
                    + "    (SELECT (';' + CONVERT(varchar(100), CONVERT(BIGINT, Rotas.Sequencia)) + '_' +  CONVERT(varchar(100), CONVERT(BIGINT, Rt_Perc.Parada)) + '_' \n"
                    + "            + CONVERT(varchar(100), '0') + '_' + Clientes.Nred + '_' + 'BRL') Guias\n"
                    + "            FROM PedidoRefeicao (nolock)\n"
                    + "            LEFT JOIN Pedido\n"
                    + "                    ON PedidoRefeicao.CodFil = Pedido.CodFil\n"
                    + "                   AND PedidoRefeicao.Data   = Pedido.Data\n"
                    + "                   AND PedidoRefeicao.CodCli = Pedido.CodCli2\n"
                    + "            LEFT JOIN Clientes\n"
                    + "                ON PedidoRefeicao.Codcli = Clientes.Codigo\n"
                    + "            WHERE Pedido.SeqRota = Rotas.Sequencia\n"
                    + "            AND Pedido.Parada = Rt_Perc.Parada\n"
                    + "    UNION\n"
                    + "SELECT (';' + CONVERT(varchar(100), CONVERT(BIGINT, Rt_Guias.Guia)) + '_' + Rt_Guias.Serie + '_' \n"
                    + "            + CONVERT(varchar(100), Rt_Guias.Valor) + '_' + Clientes.Nred + '_' + Rt_GuiasMoeda.Moeda) Guias\n"
                    + "            FROM Rt_Guias (nolock)\n"
                    + "            LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                                    AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                                    AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                                    AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + "            left join CxfGuias on  CxfGuias.Guia = Rt_Guias.Guia\n"
                    + "                               AND CxfGuias.Serie = Rt_Guias.Serie\n"
                    + "            left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                             AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "            left join Clientes on  Clientes.Codigo = OS_Vig.Cliente\n"
                    + "                             AND Clientes.CodFil = OS_Vig.CodFil\n"
                    + "            WHERE Rt_Guias.Sequencia = Rotas.Sequencia\n"
                    + "            AND Rt_Guias.Parada      = Rt_Perc.Parada\n"
                    + "    UNION \n"
                    + "    SELECT (';' + CONVERT(varchar(100), CONVERT(BIGINT, CxfGuias.Guia)) + '_' + CxfGuias.Serie + '_' \n"
                    + "                + CONVERT(varchar(100), CxfGuias.Valor) + '_' + Clientes.Nred + '_' + Pedido.TipoMoeda) Guias\n"
                    + "    from CxfGuias (nolock)\n"
                    + "    Left join CxfSaidas  on CxfSaidas.SeqRota = CxfGuias.SeqRotaSai\n"
                    + "                        and CxfSaidas.Remessa = CxfGuias.Remessa\n"
                    + "    Left join Clientes  on Clientes.Codigo = CxfGuias.CliDst\n"
                    + "                       and Clientes.CodFil = CxfGuias.CodFil\n"
                    + "    Left join TesSaidas on TesSaidas.Guia = CxfGuias.Guia \n"
                    + "                       and TesSaidas.Serie = CxfGuias.Serie\n"
                    + "    Left join Pedido    on Pedido.Numero = TesSaidas.Pedido\n"
                    + "                       and Pedido.CodFil = TesSaidas.CodFil					 \n"
                    + "    Where CxfGuias.SeqRotaSai = Rotas.Sequencia\n"
                    + "    and CxfSaidas.Dt_Saida is null\n"
                    + "    and LEFT(CxfGuias.CliOri,3) = LEFT(Rt_Perc.CodCli1,3)\n"
                    + "    and LEFT(CxfGuias.CliOri,3) = '999'\n"
                    //                    + "    UNION \n"
                    //                    + "    SELECT (';' + CONVERT(varchar(100), CONVERT(BIGINT, TesSaidas.Guia)) + '_' + TesSaidas.Serie + '_' \n"
                    //                    + "        +  CONVERT(varchar(100), TesSaidas.TotalGeral) + '_' + Clientes.Nred + '_' + Paramet.MoedaPdrMobile) Guias\n"
                    //                    + "            FROM TesSaidas (nolock)\n"
                    //                    + "            Left Join Rt_Perc rtp on  rtp.Pedido    = TesSaidas.Pedido   \n"
                    //                    + "                                  and rtp.CodCli1   = TesSaidas.CodCli2 \n"
                    //                    + "                                  and rtp.ER        =  'E'\n"
                    //                    + "                                  and rtp.Flag_Excl <> '*'\n"
                    //                    + "            Left join Clientes  on Clientes.Codigo = TesSaidas.CodCli2\n"
                    //                    + "                               and Clientes.CodFil = TesSaidas.CodFil\n"
                    //                    + "            LEFT JOIN Paramet ON Paramet.filial_PDR = TesSaidas.CodFil\n"
                    //                    + "            WHERE rtp.Sequencia =  Rotas.Sequencia\n"
                    //                    + "            AND rtp.Parada      = Rt_Perc.Parada\n"
                    + "      UNION\n"
                    + "            SELECT (';' + CONVERT(varchar(100), CONVERT(BIGINT, Rt_Guias.Guia)) + '_' + Rt_Guias.Serie + '_' \n"
                    + "            + CONVERT(varchar(100), Rt_Guias.Valor) + '_' + Clientes.Nred + '_' + Rt_GuiasMoeda.Moeda) Guias\n"
                    + "            FROM Rt_perc RtPerc (nolock)\n"
                    + "                    inner join Rt_Perc RtOri (nolock) on RtOri.Sequencia = RtPerc.Sequencia\n"
                    + "                                            and isnull(RtOri.dPar,0) = RtPerc.Parada\n"
                    + "                    Left join Rt_Guias (nolock)  on Rt_Guias.Sequencia = RtOri.Sequencia\n"
                    + "                                                and Rt_Guias.Parada = RtOri.Parada \n"
                    + "            LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                                    AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                                    AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                                    AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + "            left join CxfGuias on  CxfGuias.Guia = Rt_Guias.Guia\n"
                    + "                               AND CxfGuias.Serie = Rt_Guias.Serie\n"
                    + "            left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                             AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "            left join Clientes on  Clientes.Codigo = OS_Vig.Cliente\n"
                    + "                             AND Clientes.CodFil = OS_Vig.CodFil\n"
                    + "            WHERE RtPerc.Sequencia = Rotas.Sequencia\n"
                    + "            AND RtPerc.Parada      = Rt_Perc.Parada\n"
                    + "    ) a For XML PATH('')) DadosGuias,\n"
                    + " LEFT(cliori.latitude,  6) + REPLACE(SUBSTRING(cliori.latitude,  7, LEN(cliori.latitude)  -1 ),'.','') latitude, \n"
                    + " LEFT(cliori.longitude, 6) + REPLACE(SUBSTRING(cliori.longitude, 7, LEN(cliori.longitude) -1 ),'.','') longitude, \n"
                    + " Rotas.rota, \n"
                    + " CliDst.nred nredDst, \n"
                    + " rt_perc.*, \n"
                    + " cliori.Nome NomeOri, \n"
                    + " CliOri.Ende EndeOri, \n"
                    + " CliOri.Bairro BairroOri, \n"
                    + " CliOri.Cidade CidadeOri,\n"
                    + " cliori.Estado EstadoOri, \n"
                    + " cliori.Cep CepOri, \n"
                    + " convert(date,Rotas.Data) data, \n"
                    + " Rt_PercSLA.HrChegVei, \n"
                    + " Rt_PercSLA.HrSaidaVei, \n"
                    + " Convert(varchar,CliOri.Banco) BancoOri, \n"
                    + " cliDst.Nome NomeDst, \n"
                    + " CliDst.Ende EndeDst, \n"
                    + " CliDst.Bairro BairroDst, \n"
                    + " CliDst.Cidade CidadeDst,\n"
                    + " cliDst.Estado EstadoDst, \n"
                    + " cliDst.Cep CepDst, \n"
                    + " (SELECT Top 1\n"
                    + "  CONVERT(VARCHAR, ISNULL(Rastrear.Latitude,'')) + '_' + \n"
                    + "  CONVERT(VARCHAR, ISNULL(Rastrear.Longitude,'')) + '_' + \n"
                    + "  CONVERT(VARCHAR, ISNULL(Rastrear.Hora,'')) + '_' + \n"
                    + "  CONVERT(VARCHAR, ISNULL(RastrearStat.Hora,'')) + '_' + \n"
                    + "  CONVERT(VARCHAR, ISNULL(Rt_perc.HrCheg,'')) + '_' + \n"
                    + "  CAST(CAST(dbo.fun_CalcDistancia(Rastrear.Latitude, Rastrear.Longitude, LEFT(cliori.latitude,  6) + REPLACE(SUBSTRING(cliori.latitude,  7, LEN(cliori.latitude)  -1 ),'.',''), LEFT(cliori.longitude, 6) + REPLACE(SUBSTRING(cliori.longitude, 7, LEN(cliori.longitude) -1 ),'.','')) AS DECIMAL(18,3)) AS VARCHAR(30)) AS dadosBaixa"
                    + "  FROM Rt_perc rtPerc\n"
                    + "  Left JOIN Rotas\n"
                    + "    ON Rotas.Sequencia = rtPerc.Sequencia \n"
                    + "  left JOIN RastrearStat\n"
                    + "    ON RastrearStat.Data = Rotas.Data        \n"
                    + "   AND RastrearStat.SeqRota = rtPerc.Sequencia \n"
                    + "  Left JOIN Rastrear\n"
                    + "    ON Rastrear.Codigo = RastrearStat.Sequencia \n"
                    + "   AND Rastrear.Hora  >= (rt_perc.HrCheg + '00:00:00')\n"
                    + "  WHERE rtPerc.Sequencia = rt_perc.Sequencia\n"
                    + "  AND   rtPerc.Parada    = rt_perc.parada\n"
                    + "  ORDER BY Rastrear.Hora ASC) AS dadosBaixa"
                    + " FROM rt_perc WITH (NOLOCK) \n"
                    + " LEFT JOIN Rotas AS Rotas WITH (NOLOCK) \n"
                    + "   ON Rotas.Sequencia = rt_perc.sequencia \n"
                    + "  AND Rotas.Flag_Excl <> '*' \n"
                    + " LEFT JOIN Clientes AS CliOri WITH (NOLOCK)\n"
                    + "   ON  CliOri.Codigo = rt_perc.CodCli1 \n"
                    + "  AND CliOri.CodFil = Rotas.CodFil \n"
                    + " LEFT JOIN Clientes AS CliDst WITH (NOLOCK) \n"
                    + "   ON CliDst.Codigo = rt_perc.CodCli2 \n"
                    + "  AND CliDst.CodFil = Rotas.CodFil \n"
                    + " LEFT JOIN Rt_PercSLA WITH (NOLOCK) \n"
                    + "   ON Rt_PercSLA.Sequencia = Rt_Perc.Sequencia \n"
                    + "  AND Rt_PercSLA.Parada = Rt_Perc.Parada                     \n"
                    + " WHERE rt_perc.sequencia = ?\n"
                    + " AND len(cliori.latitude)  > 0  \n"
                    + " AND len(cliori.longitude) > 0 \n"
                    + " AND CliOri.Nred not like '%almoco%' \n"
                    + " AND CliOri.Nred not like '%almoço%' \n";
            if (!excl) {
                sql += "AND rt_perc.flag_excl <> '*' \n ";
            }
            sql += " ORDER BY case when len(Rt_Perc.HrCheg) > 0 then Replace(Rt_Perc.HrCheg,':','') else rt_Perc.Hora1 end;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();
            

            Rt_Perc trajeto;

            try {
                while (consulta.Proximo()) {
                    trajeto = new Rt_Perc();
                    trajeto.setRota(consulta.getString("rota"));
                    trajeto.setSequencia(consulta.getString("sequencia"));
                    trajeto.setData(consulta.getString("data"));
                    trajeto.setParada(consulta.getInt("parada"));
                    trajeto.setCodFil(consulta.getString("codfil"));
                    trajeto.setHora1(consulta.getString("hora1"));
                    trajeto.setER(consulta.getString("er"));
                    trajeto.setCodCli1(consulta.getString("codcli1"));
                    trajeto.setNRed(consulta.getString("nred"));
                    trajeto.setCodCli2(consulta.getString("codcli2"));
                    trajeto.setNRedDst(consulta.getString("nredDst"));
                    trajeto.setHora1D(consulta.getString("hora1d"));
                    trajeto.setTipoSrv(consulta.getString("tiposrv"));
                    trajeto.setChave(consulta.getString("chave"));
                    trajeto.setDPar(consulta.getInt("DPAR"));
                    trajeto.setRegiao(consulta.getString("regiao"));
                    trajeto.setObserv(consulta.getString("observ"));
                    trajeto.setValor(consulta.getString("valor"));
                    trajeto.setHrBaixa(consulta.getString("hrbaixa"));
                    trajeto.setHrSaida(consulta.getString("hrsaida"));
                    trajeto.setHrCheg(consulta.getString("hrcheg"));
                    trajeto.setAtraso(consulta.getString("atraso"));
                    trajeto.setTempoEspera(consulta.getString("tempoespera"));
                    trajeto.setOS(consulta.getString("OS"));
                    trajeto.setOperIncl(consulta.getString("OperIncl"));
                    trajeto.setDt_Alter(consulta.getLocalDate("dt_incl"));
                    trajeto.setHr_Incl(consulta.getString("hr_incl"));
                    trajeto.setOperador(consulta.getString("operador"));
                    trajeto.setDt_Alter(consulta.getLocalDate("dt_alter"));
                    trajeto.setHr_Alter(consulta.getString("hr_alter"));
                    trajeto.setOperExcl(consulta.getString("operexcl"));
                    trajeto.setHr_Excl(consulta.getString("hr_excl"));
                    trajeto.setOperFech(consulta.getString("operfech"));
                    trajeto.setHr_Fech(consulta.getString("hr_fech"));
                    trajeto.setCodLan(consulta.getString("codlan"));
                    trajeto.setPedido(consulta.getString("pedido"));
                    trajeto.setFlag_Excl(consulta.getString("flag_excl"));

                    trajeto.setLatitude(consulta.getString("latitude"));
                    trajeto.setLongitude(consulta.getString("longitude"));

                    trajeto.setNomeOri(consulta.getString("NomeOri"));
                    trajeto.setEnderecoOri(consulta.getString("EndeOri"));
                    trajeto.setBairroOri(consulta.getString("BairroOri"));
                    trajeto.setCidadeOri(consulta.getString("CidadeOri"));
                    trajeto.setUfOri(consulta.getString("EstadoOri"));
                    trajeto.setCepOri(consulta.getString("CepOri"));

                    trajeto.setNomeDst(consulta.getString("NomeDst"));
                    trajeto.setEnderecoDst(consulta.getString("EndeDst"));
                    trajeto.setBairroDst(consulta.getString("BairroDst"));
                    trajeto.setCidadeDst(consulta.getString("CidadeDst"));
                    trajeto.setUfDst(consulta.getString("EstadoDst"));
                    trajeto.setCepDst(consulta.getString("CepDst"));

                    trajeto.setHrChegVei(consulta.getString("HrChegVei"));
                    trajeto.setHrSaidaVei(consulta.getString("HrSaidaVei"));
                    trajeto.setBanco(consulta.getString("BancoOri"));

                    trajeto.setGuia(consulta.getString("DadosGuias").replace("</Guias><Guias>", "").replace("<Guias>;", "").replace("</Guias>", ""));

                    trajeto.setDadosBaixa(consulta.getString("dadosBaixa"));

                    trajeto.setGuiasGTVe(consulta.getString("guiasGTVe").replace("</Guias><Guias>", "").replace("<Guias>;", "").replace("</Guias>", ""));

                    trajetos.add(trajeto);
                }
            } catch (Exception ex) {
                String Teste = ex.getMessage();
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarTrajetosSatMobWeb - " + e.getMessage());
        }
        return trajetos;
    }

    public List<Rt_Perc> listarTrajetosSatMobWebData(String Data, String CodFil, String Rota, Boolean excl, Persistencia persistencia) throws Exception {
        List<Rt_Perc> trajetos = new ArrayList();
        try {
            String sql = "SELECT\n"
                    + " (SELECT * FROM (SELECT (';' + CONVERT(varchar(100), CONVERT(BIGINT, Rt_Guias.Guia)) + '_' + Rt_Guias.Serie) Guias\n"
                    + "            FROM Rt_Guias (nolock)\n"
                    + "            JOIN XMLGTVE (nolock)\n"
                    + "              ON Rt_Guias.Guia  = XMLGTVE.Guia\n"
                    + "             AND Rt_Guias.Serie = XMLGTVE.Serie\n"
                    + "            WHERE Rt_Guias.Sequencia = Rotas.Sequencia\n"
                    + "            AND   Rt_Guias.Parada    = Rt_Perc.Parada) a For XML PATH('')) AS guiasGTVe,"
                    + " STUFF((SELECT ';' + CONVERT(varchar(100), CONVERT(BIGINT, Rt_Guias.Guia)) + '_' + Rt_Guias.Serie\n"
                    + "        FROM Rt_Guias (nolock)\n"
                    + "        WHERE Rt_Guias.Sequencia = Rotas.Sequencia\n"
                    + "        AND Rt_Guias.Parada      = Rt_Perc.Parada\n"
                    + "        ORDER BY Rt_Guias.Guia\n"
                    + "        For XML PATH('')),1,1,'') DadosGuias,\n"
                    + " cliori.latitude, \n"
                    + " cliori.longitude, \n"
                    + " Rotas.rota, \n"
                    + " CliDst.nred nredDst, \n"
                    + " rt_perc.*, \n"
                    + " cliori.Nome NomeOri, \n"
                    + " CliOri.Ende EndeOri, \n"
                    + " CliOri.Bairro BairroOri, \n"
                    + " CliOri.Cidade CidadeOri,\n"
                    + " cliori.Estado EstadoOri, \n"
                    + " cliori.Cep CepOri, \n"
                    + " convert(date,Rotas.Data) data, \n"
                    + " Rt_PercSLA.HrChegVei, \n"
                    + " Rt_PercSLA.HrSaidaVei, \n"
                    + " Convert(varchar,CliOri.Banco) BancoOri, \n"
                    + " cliDst.Nome NomeDst, \n"
                    + " CliDst.Ende EndeDst, \n"
                    + " CliDst.Bairro BairroDst, \n"
                    + " CliDst.Cidade CidadeDst,\n"
                    + " cliDst.Estado EstadoDst, \n"
                    + " cliDst.Cep CepDst \n"
                    + " FROM rt_perc WITH (NOLOCK) \n"
                    + " LEFT JOIN Rotas AS Rotas WITH (NOLOCK) \n"
                    + "   ON Rotas.Sequencia = rt_perc.sequencia \n"
                    + "  AND Rotas.Flag_Excl <> '*' \n"
                    + " LEFT JOIN Clientes AS CliOri WITH (NOLOCK)\n"
                    + "   ON  CliOri.Codigo = rt_perc.CodCli1 \n"
                    + "  AND CliOri.CodFil = Rotas.CodFil \n"
                    + " LEFT JOIN Clientes AS CliDst WITH (NOLOCK) \n"
                    + "   ON CliDst.Codigo = rt_perc.CodCli2 \n"
                    + "  AND CliDst.CodFil = Rotas.CodFil \n"
                    + " LEFT JOIN Rt_PercSLA WITH (NOLOCK) \n"
                    + "   ON Rt_PercSLA.Sequencia = Rt_Perc.Sequencia \n"
                    + "  AND Rt_PercSLA.Parada = Rt_Perc.Parada                     \n"
                    + " WHERE len(cliori.latitude)  > 0  \n";

            if (!Rota.equals("")) {
                sql += "  AND Rotas.Rota = ?\n";
            }

            sql += " AND len(cliori.longitude) > 0 \n"
                    + " AND Rotas.Data   = ?\n"
                    + " AND Rotas.CodFil = ? \n"
                    + " AND CliOri.Nred not like '%almoco%' \n"
                    + " AND CliOri.Nred not like '%almoço%' \n";

            if (!excl) {
                sql += "AND rt_perc.flag_excl <> '*'  ";
            }
            sql += " ORDER BY case when len(Rt_Perc.HrCheg) > 0 then Replace(Rt_Perc.HrCheg,':','') else rt_Perc.Hora1 end;";

            Consulta consulta = new Consulta(sql, persistencia);
            if (!Rota.equals("")) {
                consulta.setString(Rota);
            }
            consulta.setString(Data);
            consulta.setString(CodFil);

            consulta.select();

            Rt_Perc trajeto;

            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();
                trajeto.setRota(consulta.getString("rota"));
                trajeto.setSequencia(consulta.getString("sequencia"));
                trajeto.setData(consulta.getString("data"));
                trajeto.setParada(consulta.getInt("parada"));
                trajeto.setCodFil(consulta.getString("codfil"));
                trajeto.setHora1(consulta.getString("hora1"));
                trajeto.setER(consulta.getString("er"));
                trajeto.setCodCli1(consulta.getString("codcli1"));
                trajeto.setNRed(consulta.getString("nred"));
                trajeto.setCodCli2(consulta.getString("codcli2"));
                trajeto.setNRedDst(consulta.getString("nredDst"));
                trajeto.setHora1D(consulta.getString("hora1d"));
                trajeto.setTipoSrv(consulta.getString("tiposrv"));
                trajeto.setChave(consulta.getString("chave"));
                trajeto.setDPar(consulta.getInt("DPAR"));
                trajeto.setRegiao(consulta.getString("regiao"));
                trajeto.setObserv(consulta.getString("observ"));
                trajeto.setValor(consulta.getString("valor"));
                trajeto.setHrBaixa(consulta.getString("hrbaixa"));
                trajeto.setHrSaida(consulta.getString("hrsaida"));
                trajeto.setHrCheg(consulta.getString("hrcheg"));
                trajeto.setAtraso(consulta.getString("atraso"));
                trajeto.setTempoEspera(consulta.getString("tempoespera"));
                trajeto.setOS(consulta.getString("OS"));
                trajeto.setOperIncl(consulta.getString("OperIncl"));
                trajeto.setDt_Alter(consulta.getLocalDate("dt_incl"));
                trajeto.setHr_Incl(consulta.getString("hr_incl"));
                trajeto.setOperador(consulta.getString("operador"));
                trajeto.setDt_Alter(consulta.getLocalDate("dt_alter"));
                trajeto.setHr_Alter(consulta.getString("hr_alter"));
                trajeto.setOperExcl(consulta.getString("operexcl"));
                trajeto.setHr_Excl(consulta.getString("hr_excl"));
                trajeto.setOperFech(consulta.getString("operfech"));
                trajeto.setHr_Fech(consulta.getString("hr_fech"));
                trajeto.setCodLan(consulta.getString("codlan"));
                trajeto.setPedido(consulta.getString("pedido"));
                trajeto.setFlag_Excl(consulta.getString("flag_excl"));

                trajeto.setLatitude(consulta.getString("latitude"));
                trajeto.setLongitude(consulta.getString("longitude"));

                trajeto.setNomeOri(consulta.getString("NomeOri"));
                trajeto.setEnderecoOri(consulta.getString("EndeOri"));
                trajeto.setBairroOri(consulta.getString("BairroOri"));
                trajeto.setCidadeOri(consulta.getString("CidadeOri"));
                trajeto.setUfOri(consulta.getString("EstadoOri"));
                trajeto.setCepOri(consulta.getString("CepOri"));

                trajeto.setNomeDst(consulta.getString("NomeDst"));
                trajeto.setEnderecoDst(consulta.getString("EndeDst"));
                trajeto.setBairroDst(consulta.getString("BairroDst"));
                trajeto.setCidadeDst(consulta.getString("CidadeDst"));
                trajeto.setUfDst(consulta.getString("EstadoDst"));
                trajeto.setCepDst(consulta.getString("CepDst"));

                trajeto.setHrChegVei(consulta.getString("HrChegVei"));
                trajeto.setHrSaidaVei(consulta.getString("HrSaidaVei"));
                trajeto.setBanco(consulta.getString("BancoOri"));

                trajeto.setGuia(consulta.getString("DadosGuias"));

                trajeto.setGuiasGTVe(consulta.getString("guiasGTVe").replace("</Guias><Guias>", "").replace("<Guias>;", "").replace("</Guias>", ""));

                trajetos.add(trajeto);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarTrajetosSatMobWeb - " + e.getMessage());
        }
        return trajetos;
    }

    /**
     * Lista os registros do trajeto
     *
     * @param sequencia sequencia da rota
     * @param excl Booleano para mostrar ou não rotas excluídas
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os trajetos
     * @throws Exception
     */
    public List<Rt_Perc> listarTrajetosSatMobWebSemCoordenada(BigDecimal sequencia, Boolean excl, Persistencia persistencia) throws Exception {
        List<Rt_Perc> trajetos = new ArrayList();
        try {
            String sql = "SELECT cliori.latitude, cliori.longitude, Rotas.rota, CliDst.nred nredDst, rt_perc.*, "
                    + " cliori.Nome NomeOri, CliOri.Ende EndeOri, CliOri.Bairro BairroOri, CliOri.Cidade CidadeOri,"
                    + " cliori.Estado EstadoOri, cliori.Cep CepOri, convert(date,Rotas.Data) data, "
                    + " Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei, Convert(varchar,CliOri.Banco) BancoOri "
                    + " FROM rt_perc WITH (NOLOCK) "
                    + " LEFT JOIN Rotas AS Rotas WITH (NOLOCK) ON Rotas.Sequencia = rt_perc.sequencia "
                    + "                          AND Rotas.Flag_Excl <> '*' "
                    + " LEFT JOIN Clientes AS CliOri WITH (NOLOCK) ON  CliOri.Codigo = rt_perc.CodCli1 "
                    + "                              AND CliOri.CodFil = Rotas.CodFil "
                    + " LEFT JOIN Clientes AS CliDst WITH (NOLOCK)  ON CliDst.Codigo = rt_perc.CodCli2 "
                    + "                              AND CliDst.CodFil = Rotas.CodFil "
                    + " LEFT JOIN Rt_PercSLA WITH (NOLOCK) on Rt_PercSLA.Sequencia = Rt_Perc.Sequencia "
                    + "                      and Rt_PercSLA.Parada = Rt_Perc.Parada "
                    + " WHERE rt_perc.sequencia = ? ";

            if (!excl) {
                sql = sql + "AND rt_perc.flag_excl <> '*'  ";
            }
            sql = sql + " and CliOri.Nred not like '%almoco%' "
                    + " and CliOri.Nred not like '%almoço%' "
                    + " order by case when len(Rt_Perc.HrCheg) > 0 then Replace(Rt_Perc.HrCheg,':','') else  rt_Perc.Hora1 end ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();

            Rt_Perc trajeto = new Rt_Perc();

            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();
                trajeto.setRota(consulta.getString("rota"));
                trajeto.setSequencia(consulta.getString("sequencia"));
                trajeto.setData(consulta.getString("data"));
                trajeto.setParada(consulta.getInt("parada"));
                trajeto.setCodFil(consulta.getString("codfil"));
                trajeto.setHora1(consulta.getString("hora1"));
                trajeto.setER(consulta.getString("er"));
                trajeto.setCodCli1(consulta.getString("codcli1"));
                trajeto.setNRed(consulta.getString("nred"));
                trajeto.setCodCli2(consulta.getString("codcli2"));
                trajeto.setNRedDst(consulta.getString("nredDst"));
                trajeto.setHora1D(consulta.getString("hora1d"));
                trajeto.setTipoSrv(consulta.getString("tiposrv"));
                trajeto.setChave(consulta.getString("chave"));
                trajeto.setDPar(consulta.getInt("DPAR"));
                trajeto.setRegiao(consulta.getString("regiao"));
                trajeto.setObserv(consulta.getString("observ"));
                trajeto.setValor(consulta.getString("valor"));
                trajeto.setHrBaixa(consulta.getString("hrbaixa"));
                trajeto.setHrSaida(consulta.getString("hrsaida"));
                trajeto.setHrCheg(consulta.getString("hrcheg"));
                trajeto.setAtraso(consulta.getString("atraso"));
                trajeto.setTempoEspera(consulta.getString("tempoespera"));
                trajeto.setOS(consulta.getString("OS"));
                trajeto.setOperIncl(consulta.getString("OperIncl"));
                trajeto.setDt_Alter(consulta.getLocalDate("dt_incl"));
                trajeto.setHr_Incl(consulta.getString("hr_incl"));
                trajeto.setOperador(consulta.getString("operador"));
                trajeto.setDt_Alter(consulta.getLocalDate("dt_alter"));
                trajeto.setHr_Alter(consulta.getString("hr_alter"));
                trajeto.setOperExcl(consulta.getString("operexcl"));
                trajeto.setHr_Excl(consulta.getString("hr_excl"));
                trajeto.setOperFech(consulta.getString("operfech"));
                trajeto.setHr_Fech(consulta.getString("hr_fech"));
                trajeto.setCodLan(consulta.getString("codlan"));
                trajeto.setPedido(consulta.getString("pedido"));
                trajeto.setFlag_Excl(consulta.getString("flag_excl"));

                trajeto.setLatitude(consulta.getString("latitude"));
                trajeto.setLongitude(consulta.getString("longitude"));

                trajeto.setNomeOri(consulta.getString("NomeOri"));
                trajeto.setEnderecoOri(consulta.getString("EndeOri"));
                trajeto.setBairroOri(consulta.getString("BairroOri"));
                trajeto.setCidadeOri(consulta.getString("CidadeOri"));
                trajeto.setUfOri(consulta.getString("EstadoOri"));
                trajeto.setCepOri(consulta.getString("CepOri"));

                trajeto.setHrChegVei(consulta.getString("HrChegVei"));
                trajeto.setHrSaidaVei(consulta.getString("HrSaidaVei"));
                trajeto.setBanco(consulta.getString("BancoOri"));

                trajetos.add(trajeto);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarTrajetosSatMobWeb - " + e.getMessage());
        }
        return trajetos;
    }

    /**
     * Lista os registros do trajeto
     *
     * @param sequencia sequencia da rota
     * @param excl Booleano para mostrar ou não rotas excluídas
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os trajetos
     * @throws Exception
     */
    public List<Rt_Perc> listarTrajetosSatMobWebTodasRotas(BigDecimal sequencia, Boolean excl, Persistencia persistencia) throws Exception {
        List<Rt_Perc> trajetos = new ArrayList();
        try {
            String sql = "SELECT cliori.latitude, cliori.longitude, Rotas.rota, CliDst.nred nredDst, rt_perc.*, "
                    + " cliori.Nome NomeOri, CliOri.Ende EndeOri, CliOri.Bairro BairroOri, CliOri.Cidade CidadeOri,"
                    + " cliori.Estado EstadoOri, cliori.Cep CepOri, convert(date,Rotas.Data) data, "
                    + " Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei, CliOri.Banco BancoOri "
                    + " FROM rt_perc WITH (NOLOCK) "
                    + " LEFT JOIN Rotas AS Rotas WITH (NOLOCK) ON Rotas.Sequencia = rt_perc.sequencia "
                    + "                          AND Rotas.Flag_Excl <> '*' "
                    + " LEFT JOIN Clientes AS CliOri WITH (NOLOCK) ON  CliOri.Codigo = rt_perc.CodCli1 "
                    + "                              AND CliOri.CodFil = Rotas.CodFil "
                    + " LEFT JOIN Clientes AS CliDst WITH (NOLOCK)  ON CliDst.Codigo = rt_perc.CodCli2 "
                    + "                              AND CliDst.CodFil = Rotas.CodFil "
                    + " LEFT JOIN Rt_PercSLA WITH (NOLOCK) on Rt_PercSLA.Sequencia = Rt_Perc.Sequencia "
                    + "                      and Rt_PercSLA.Parada = Rt_Perc.Parada "
                    + " WHERE rt_perc.sequencia = ? ";

            if (!excl) {
                sql = sql + "AND rt_perc.flag_excl <> '*'  ";
            }
            sql = sql + " and len(cliori.latitude) > 0  "
                    + " and len(cliori.longitude) > 0 "
                    + " order by rt_Perc.Hora1";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();

            Rt_Perc trajeto = new Rt_Perc();

            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();
                trajeto.setRota(consulta.getString("rota"));
                trajeto.setSequencia(consulta.getString("sequencia"));
                trajeto.setData(consulta.getString("data"));
                trajeto.setParada(consulta.getInt("parada"));
                trajeto.setCodFil(consulta.getString("codfil"));
                trajeto.setHora1(consulta.getString("hora1"));
                trajeto.setER(consulta.getString("er"));
                trajeto.setCodCli1(consulta.getString("codcli1"));
                trajeto.setNRed(consulta.getString("nred"));
                trajeto.setCodCli2(consulta.getString("codcli2"));
                trajeto.setNRedDst(consulta.getString("nredDst"));
                trajeto.setHora1D(consulta.getString("hora1d"));
                trajeto.setTipoSrv(consulta.getString("tiposrv"));
                trajeto.setChave(consulta.getString("chave"));
                trajeto.setDPar(consulta.getInt("DPAR"));
                trajeto.setRegiao(consulta.getString("regiao"));
                trajeto.setObserv(consulta.getString("observ"));
                trajeto.setValor(consulta.getString("valor"));
                trajeto.setHrBaixa(consulta.getString("hrbaixa"));
                trajeto.setHrSaida(consulta.getString("hrsaida"));
                trajeto.setHrCheg(consulta.getString("hrcheg"));
                trajeto.setAtraso(consulta.getString("atraso"));
                trajeto.setTempoEspera(consulta.getString("tempoespera"));
                trajeto.setOS(consulta.getString("OS"));
                trajeto.setOperIncl(consulta.getString("OperIncl"));
                trajeto.setDt_Alter(consulta.getLocalDate("dt_incl"));
                trajeto.setHr_Incl(consulta.getString("hr_incl"));
                trajeto.setOperador(consulta.getString("operador"));
                trajeto.setDt_Alter(consulta.getLocalDate("dt_alter"));
                trajeto.setHr_Alter(consulta.getString("hr_alter"));
                trajeto.setOperExcl(consulta.getString("operexcl"));
                trajeto.setHr_Excl(consulta.getString("hr_excl"));
                trajeto.setOperFech(consulta.getString("operfech"));
                trajeto.setHr_Fech(consulta.getString("hr_fech"));
                trajeto.setCodLan(consulta.getString("codlan"));
                trajeto.setPedido(consulta.getString("pedido"));
                trajeto.setFlag_Excl(consulta.getString("flag_excl"));

                trajeto.setLatitude(consulta.getString("latitude"));
                trajeto.setLongitude(consulta.getString("longitude"));

                trajeto.setNomeOri(consulta.getString("NomeOri"));
                trajeto.setEnderecoOri(consulta.getString("EndeOri"));
                trajeto.setBairroOri(consulta.getString("BairroOri"));
                trajeto.setCidadeOri(consulta.getString("CidadeOri"));
                trajeto.setUfOri(consulta.getString("EstadoOri"));
                trajeto.setCepOri(consulta.getString("CepOri"));

                trajeto.setHrChegVei(consulta.getString("HrChegVei"));
                trajeto.setHrSaidaVei(consulta.getString("HrSaidaVei"));

                trajeto.setBanco(consulta.getString("BancoOri"));

                trajetos.add(trajeto);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarTrajetosSatMobWeb - " + e.getMessage());
        }
        return trajetos;
    }

    private String QueryPedidos(String codFil, String data, String Numero) {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT");
        sql.append(" TAB.Tipo,");
        sql.append(" TAB.CodFil,");
        sql.append(" TAB.Data,");
        sql.append(" TAB.Numero,");
        if (Numero.equals("")) {
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_NRed      ELSE TAB.Cli2_NRed      END AS Cli_Nred,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_Nome      ELSE TAB.Cli2_Nome      END AS Cli_Nome,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_Ende      ELSE TAB.Cli2_Ende      END AS Cli_Ende,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_Bairro    ELSE TAB.Cli2_Bairro    END AS Cli_Bairro,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_Cidade    ELSE TAB.Cli2_Cidade    END AS Cli_Cidade,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_Estado    ELSE TAB.Cli2_Estado    END AS Cli_Estado,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_CEP       ELSE TAB.Cli2_CEP       END AS Cli_CEP,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_Latitude  ELSE TAB.Cli2_Latitude  END AS Cli_Latitude,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli1_Longitude ELSE TAB.Cli2_Longitude END AS Cli_Longitude,");

            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli2_NRed      ELSE TAB.Cli1_NRed      END AS Cli2_Nred,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Cli2_Nome      ELSE TAB.Cli1_Nome      END AS Cli2_Nome,");

            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Hora1D         ELSE TAB.Hora1O         END AS Hora1,");
            sql.append(" CASE WHEN TAB.Tipo = 'R' THEN TAB.Hora2D         ELSE TAB.Hora2O         END AS Hora2,");
        } else {
            sql.append(" TAB.Cli1_NRed,");
            sql.append(" TAB.Cli1_Nome,");
            sql.append(" TAB.Cli1_Ende,");
            sql.append(" TAB.Cli1_Bairro,");
            sql.append(" TAB.Cli1_Cidade,");
            sql.append(" TAB.Cli1_Estado,");
            sql.append(" TAB.Cli1_CEP,");
            sql.append(" TAB.Cli1_Latitude,");
            sql.append(" TAB.Cli1_Longitude,");
            sql.append(" TAB.Hora1O,");
            sql.append(" TAB.Hora2O,");

            sql.append(" TAB.Cli2_NRed,");
            sql.append(" TAB.Cli2_Nome,");
            sql.append(" TAB.Cli2_Ende,");
            sql.append(" TAB.Cli2_Bairro,");
            sql.append(" TAB.Cli2_Cidade,");
            sql.append(" TAB.Cli2_Estado,");
            sql.append(" TAB.Cli2_CEP,");
            sql.append(" TAB.Cli2_Latitude,");
            sql.append(" TAB.Cli2_Longitude,");
            sql.append(" TAB.Hora1D,");
            sql.append(" TAB.Hora2D,");
        }
        sql.append(" TAB.Valor,");
        sql.append(" TAB.Solicitante,");
        sql.append(" TAB.CodCli1,");
        sql.append(" TAB.CodCli2,");
        sql.append(" TAB.Regiao1Desc,");
        sql.append(" TAB.Regiao2Desc,");
        sql.append(" TAB.Regiao1,");
        sql.append(" TAB.Regiao2,");
        sql.append(" TAB.Obs");

        sql.append(" FROM(Select");
        sql.append("      Pedido.CodFil,");
        sql.append("      Pedido.Data,");
        sql.append("      Pedido.Numero,");
        sql.append("      Pedido.Hora1O,");
        sql.append("      Pedido.Hora2O,");
        sql.append("      Pedido.Hora1D,");
        sql.append("      Pedido.Hora2D,");
        sql.append("      Cli1.Ende Cli1_Ende,");
        sql.append("      Cli1.Bairro Cli1_Bairro,");
        sql.append("      Cli1.Cidade Cli1_Cidade,");
        sql.append("      Cli1.Estado Cli1_Estado,");
        sql.append("      Cli1.CEP Cli1_CEP,");
        sql.append("      Cli1.NRed Cli1_NRed,");
        sql.append("      Cli1.Nome Cli1_Nome,");
        sql.append("      Cli1.Latitude Cli1_Latitude,");
        sql.append("      Cli1.Longitude Cli1_Longitude,");
        sql.append("      Cli2.NRed Cli2_NRed,");
        sql.append("      Cli2.Nome Cli2_Nome,");
        sql.append("      Cli2.Ende Cli2_Ende,");
        sql.append("      Cli2.Bairro Cli2_Bairro,");
        sql.append("      Cli2.Cidade Cli2_Cidade,");
        sql.append("      Cli2.Estado Cli2_Estado,");
        sql.append("      Cli2.CEP Cli2_CEP,");
        sql.append("      Cli2.Latitude Cli2_Latitude,");
        sql.append("      Cli2.Longitude Cli2_Longitude,");
        sql.append("      (Select Case when Count(*) > 0 then 'E' else 'R' end from CXForte  where CXForte.CodCli = Pedido.CodCli1 and CXForte.CodFil = Pedido.CodFil) Tipo,");
        sql.append("      Pedido.Valor,");
        sql.append("      Pedido.Solicitante,");
        sql.append("      Pedido.CodCli1,");
        sql.append("      Pedido.CodCli2,");
        sql.append("      Regiao1.Descricao Regiao1Desc,");
        sql.append("      Regiao2.Descricao Regiao2Desc,");
        sql.append("      Pedido.Regiao1,");
        sql.append("      Pedido.Regiao2,");
        sql.append("      Pedido.Obs");
        sql.append("      from Pedido");
        sql.append("      Left join Clientes Cli1 on Cli1.Codigo = Pedido.CodCli1");
        sql.append("                             and Cli1.CodFil = Pedido.CodFil");
        sql.append("      Left join Clientes Cli2 on Cli2.Codigo = Pedido.CodCli2");
        sql.append("                             and Cli2.CodFil = Pedido.CodFil");
        sql.append("      Left join Regiao Regiao1 on Pedido.Regiao1 = Regiao1.Regiao");
        sql.append("                              and Pedido.CodFil = Regiao1.CodFil");
        sql.append("      Left join Regiao Regiao2 on Pedido.Regiao2 = Regiao2.Regiao");
        sql.append("                              and Pedido.CodFil = Regiao2.CodFil");
        sql.append("      where Pedido.CodFil   = ?");
        if (!data.equals("")) {
            sql.append("        and Pedido.data     = ?");
        }
        if (!Numero.equals("")) {
            sql.append("        and Pedido.Numero     = ?");
        }
        sql.append("        and Pedido.Situacao = 'PD' ");
        sql.append("        and Pedido.Flag_Excl <> '*') AS TAB");

        return sql.toString();
    }

    /**
     * Lista os pedidos do dia
     *
     * @param codFil codigo da filial que sera consultada
     * @param data Data que serão pesquisados pedidos
     * @param persistencia
     * @return lista contendo os pedidos
     * @throws Exception
     */
    public List<Rt_Perc> listarPedidosSatMobWeb(String codFil, String data, Persistencia persistencia) throws Exception {
        List<Rt_Perc> pedidos = new ArrayList();
        try {
            String sqlAjustaLatLon = "UPDATE Clientes SET Latitude  = REPLACE(Latitude,  '- ','-') where REPLACE(Latitude, '- ','') <> Latitude;\n"
                    + " UPDATE Clientes SET Longitude = REPLACE(Longitude, '- ','-') where REPLACE(Longitude,'- ','') <> Longitude;\n"
                    + " UPDATE Clientes SET Latitude  = REPLACE(Latitude,  ' -','-') where REPLACE(Latitude, ' -','') <> Latitude;\n"
                    + " UPDATE Clientes SET Longitude = REPLACE(Longitude, ' -','-') where REPLACE(Longitude,' -','') <> Longitude;\n"
                    + " UPDATE Clientes SET Longitude = REPLACE(Longitude, ',','')   where REPLACE(Longitude,',','')  <> Longitude;\n"
                    + " UPDATE Clientes SET Latitude = REPLACE(Latitude,   ',','')   where REPLACE(Latitude, ',','')  <> Latitude;";

            Consulta consulta = new Consulta(sqlAjustaLatLon, persistencia);
            consulta.update();

            consulta = new Consulta(QueryPedidos(codFil, data, ""), persistencia);
            consulta.setString(codFil);
            consulta.setString(data);
            consulta.select();

            Rt_Perc pedido = new Rt_Perc();

            while (consulta.Proximo()) {
                pedido = new Rt_Perc();

                pedido.setCodFil(consulta.getString("CodFil"));
                pedido.setData(consulta.getString("Data"));
                pedido.setHora1(consulta.getString("Hora1"));
                pedido.setPedido(consulta.getString("Numero"));
                pedido.setNumero(consulta.getString("Numero"));
                pedido.setLatitude(consulta.getString("Cli_Latitude"));
                pedido.setLongitude(consulta.getString("Cli_Longitude"));
                pedido.setNRed(consulta.getString("Cli_Nred"));

                pedido.setHora2(consulta.getString("Hora2"));
                pedido.setER(consulta.getString("Tipo"));
                pedido.setNomeOri(consulta.getString("Cli_Nome"));
                pedido.setEnderecoOri(consulta.getString("Cli_Ende"));
                pedido.setBairroOri(consulta.getString("Cli_Bairro"));
                pedido.setCidadeOri(consulta.getString("Cli_Cidade"));
                pedido.setUfOri(consulta.getString("Cli_Estado"));
                pedido.setCepOri(consulta.getString("Cli_CEP"));

                pedido.setNRedDst(consulta.getString("Cli2_Nred"));
                pedido.setNomeDst(consulta.getString("Cli2_Nome"));

                pedido.setValor(consulta.getString("Valor"));
                pedido.setSolicitante(consulta.getString("Solicitante"));
                pedido.setCodCli1(consulta.getString("CodCli1"));
                pedido.setCodCli2(consulta.getString("CodCli2"));
                pedido.setRegiao(consulta.getString("Regiao1"));
                pedido.setRegiaoDst(consulta.getString("Regiao2"));
                pedido.setObserv(consulta.getString("Obs"));

                pedidos.add(pedido);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarPedidosSatMobWeb - " + e.getMessage());
        }
        return pedidos;
    }

    /**
     * Consultar Pedido
     *
     * @param codFil codigo da filial que sera consultada
     * @param numero Numero do pedido
     * @param persistencia
     * @return Dados do pedido requisitado
     * @throws Exception
     */
    public Pedido consultarPedidosSatMobWeb(String codFil, String numero, Persistencia persistencia) throws Exception {
        Pedido pedido = new Pedido();

        try {
            Consulta consulta = new Consulta(QueryPedidos(codFil, "", numero), persistencia);
            consulta.setString(codFil);
            consulta.setBigDecimal(numero);
            consulta.select();

            while (consulta.Proximo()) {
                pedido.setCodFil(consulta.getString("CodFil"));
                pedido.setData(consulta.getString("Data"));
                pedido.setHora1D(consulta.getString("Hora1D"));
                pedido.setHora1O(consulta.getString("Hora1O"));
                pedido.setHora2D(consulta.getString("Hora2D"));
                pedido.setHora2O(consulta.getString("Hora2O"));
                pedido.setNumero(consulta.getString("Numero"));

                pedido.setCli1Lat(consulta.getString("Cli1_Latitude"));
                pedido.setCli1Lon(consulta.getString("Cli1_Longitude"));
                pedido.setCli2Lat(consulta.getString("Cli2_Latitude"));
                pedido.setCli2Lon(consulta.getString("Cli2_Longitude"));

                pedido.setValor(consulta.getString("Valor"));
                pedido.setSolicitante(consulta.getString("Solicitante"));
                pedido.setCodCli1(consulta.getString("CodCli1"));
                pedido.setCodCli2(consulta.getString("CodCli2"));
                pedido.setRegiaoDesc1(consulta.getString("Regiao1Desc"));
                pedido.setRegiaoDesc2(consulta.getString("Regiao2Desc"));
                pedido.setRegiao1(consulta.getString("Regiao1"));
                pedido.setRegiao2(consulta.getString("Regiao2"));
                pedido.setObs(consulta.getString("Obs"));

                pedido.setCli1Nome(consulta.getString("Cli1_Nome"));
                pedido.setCli1Nred(consulta.getString("Cli1_NRed"));
                pedido.setCli1Bairro(consulta.getString("Cli1_Bairro"));
                pedido.setCli1Ende(consulta.getString("Cli1_Ende"));
                pedido.setCli1CEP(consulta.getString("Cli1_CEP"));
                pedido.setCli1Cidade(consulta.getString("Cli1_Cidade"));
                pedido.setCli1Estado(consulta.getString("Cli1_Estado"));

                pedido.setCli2Nome(consulta.getString("Cli2_Nome"));
                pedido.setCli2Nred(consulta.getString("Cli2_NRed"));
                pedido.setCli2Bairro(consulta.getString("Cli2_Bairro"));
                pedido.setCli2Ende(consulta.getString("Cli2_Ende"));
                pedido.setCli2CEP(consulta.getString("Cli2_CEP"));
                pedido.setCli2Cidade(consulta.getString("Cli2_Cidade"));
                pedido.setCli2Estado(consulta.getString("Cli2_Estado"));
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.consultarPedidosSatMobWeb - " + e.getMessage());
        }
        return pedido;
    }

    /**
     * Obtem informacoes nred
     *
     * @param parada numero da parada
     * @param sequencia sequencia da rota
     * @param persistencia conexao com o banco
     * @return nome reduzido
     * @throws Exception
     */
    public String obterNred(String parada, String sequencia, Persistencia persistencia) throws Exception {
        String nred = "";
        try {
            String sql = "SELECT NRed FROM Rt_Perc WHERE Sequencia = ? AND Parada = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            while (consulta.Proximo()) {
                nred = consulta.getString("NRed");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.obterNred - " + e.getMessage() + "\r\n"
                    + "SELECT NRed FROM Rt_Perc WHERE Sequencia = " + sequencia + " AND Parada = " + parada);
        }
        return nred;
    }

    /**
     * Retorna dados para roteirizacao
     *
     * @param data
     * @param codfil
     * @param horainicial
     * @param horafinal
     * @param lat
     * @param lon
     * @param persistencia conexao com o banco
     * @return Lista de sugestao de roteirizacao
     */
    public List<Rt_Perc> listaRoteirizacao(String data, String codfil, String horainicial, String horafinal, String lat, String lon, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Rt_Perc> listRtPerc = new ArrayList();
            Rt_Perc rt_Perc;
            sql = "Select top 20\n"
                    + " Clientes.Nred, Clientes.Latitude, Clientes.Longitude,  Rotas.Rota, Rt_Perc.Hora1, Rt_Perc.Parada, Rt_Perc.ER,\n"
                    + " Round(dbo.Fun_CalcDistancia(Clientes.Latitude, Clientes.Longitude, ?, ?) ,2) Distancia, Rt_Perc.Sequencia\n"
                    + " From Rt_Perc \n"
                    + " Left Join Rotas   on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + " Left join Clientes  on Clientes.codigo = Rt_Perc.CodCli1\n"
                    + "                    and Clientes.codfil = Rotas.CodFil\n"
                    + " where Rotas.data = ?\n"
                    + " and Rt_perc.Flag_Excl <> '*'\n"
                    + " and Rotas.CodFil = ?\n"
                    + " and Rotas.TpVeic <> 'N'\n"
                    + " and ((Rt_Perc.Hora1 >= ? AND\n"
                    + "       Rt_perc.Hora1 <= ?) OR Round(dbo.Fun_CalcDistancia(Clientes.Latitude, Clientes.Longitude, ?, ?) ,2) < 20)\n"
                    + " and Rt_perc.HrSaida IS NULL\n"
                    + " Order by Distancia";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(lat);
            consulta.setString(lon);
            consulta.setString(data);
            consulta.setBigDecimal(codfil);
            consulta.setString(horainicial);
            consulta.setString(horafinal);
            consulta.setString(lat);
            consulta.setString(lon);

            consulta.select();

            while (consulta.Proximo()) {
                rt_Perc = new Rt_Perc();

                rt_Perc.setNRed(consulta.getString("Nred"));
                rt_Perc.setHora1(consulta.getString("Hora1"));
                rt_Perc.setParada(consulta.getInt("Parada"));
                rt_Perc.setER(consulta.getString("ER"));
                rt_Perc.setDistancia(consulta.getString("Distancia"));
                rt_Perc.setRota(consulta.getString("Rota"));
                rt_Perc.setSequencia(consulta.getString("Sequencia"));
                rt_Perc.setLatitude(consulta.getString("Latitude"));
                rt_Perc.setLongitude(consulta.getString("Longitude"));

                listRtPerc.add(rt_Perc);
            }

            return listRtPerc;

        } catch (Exception e) {
            throw new Exception("Rt_PercDao.getHora1 - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public Rt_Perc mapaSugestaoRoteirizacao(String sequencia, String codfil, String hora1, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "DECLARE @inHora      AS VARCHAR(5)\n"
                    + " DECLARE @inSequencia AS FLOAT\n"
                    + " DECLARE @inCodFil AS FLOAT\n"
                    + " SET @inHora      = ?\n"
                    + " SET @inSequencia = ?\n"
                    + " SET @inCodFil    = ?\n"
                    + " SELECT\n"
                    + " Clientes.Latitude               AS lat_atual,\n"
                    + " Clientes.Longitude              AS lon_atual,\n"
                    + " Rt_percReferencia.Hora1         AS hora1_parada_atual,\n"
                    + " ClienteParadaAnterior.Latitude  AS lat_parada_anterior,\n"
                    + " ClienteParadaAnterior.Longitude AS lon_parada_anterior,\n"
                    + " ParadaAnterior.Hora1            AS hora1_parada_anterior,\n"
                    + " ClienteParadaSeguinte.Latitude  AS lat_parada_seguinte,\n"
                    + " ClienteParadaSeguinte.Longitude AS lon_parada_seguinte,\n"
                    + " ParadaSeguinte.Hora1            AS hora1_parada_seguinte\n"
                    + " from Rt_perc                                                    AS Rt_percReferencia\n"
                    + " LEFT JOIN Clientes\n"
                    + "   ON Rt_percReferencia.CodCli1      = Clientes.Codigo\n"
                    + "  AND Rt_percReferencia.CodFil       = Clientes.CodFil\n"
                    + " LEFT JOIN (SELECT\n"
                    + "            MAX(Rt_perc.Hora1) AS UltimaHora1,\n"
                    + "            Rt_perc.Sequencia\n"
                    + "            FROM Rt_perc\n"
                    + "            LEFT JOIN Clientes\n"
                    + "              ON Rt_perc.CodCli1     = Clientes.Codigo\n"
                    + "             AND Rt_perc.CodFil      = Clientes.CodFil\n"
                    + "            LEFT JOIN Rt_perc ParadaAtual\n"
                    + "              ON Rt_perc.Sequencia   = ParadaAtual.Sequencia\n"
                    + "             AND ParadaAtual.Hora1   = @inHora\n"
                    + "            WHERE Rt_perc.Sequencia  = @inSequencia\n"
                    + "            AND   Rt_perc.CodCli1   <> ParadaAtual.CodCli1\n"
                    + "            AND   Rt_perc.Hora1      < @inHora\n"
                    + "            AND   Rt_perc.CodFil     = @inCodFil\n"
                    + "            GROUP BY Rt_perc.Sequencia) AS ParadaAnteriorRef  \n"
                    + "   ON Rt_percReferencia.Sequencia    = ParadaAnteriorRef.Sequencia\n"
                    + " LEFT JOIN Rt_perc                                              AS ParadaAnterior\n"
                    + "   ON ParadaAnteriorRef.Sequencia    = ParadaAnterior.Sequencia\n"
                    + "  AND ParadaAnteriorRef.UltimaHora1  = ParadaAnterior.Hora1\n"
                    + " LEFT JOIN Clientes                                             AS ClienteParadaAnterior\n"
                    + "   ON ParadaAnterior.CodCli1         = ClienteParadaAnterior.Codigo\n"
                    + " LEFT JOIN (SELECT\n"
                    + "            MIN(Rt_perc.Hora1) AS UltimaHora1,\n"
                    + "            Rt_perc.Sequencia\n"
                    + "            FROM Rt_perc\n"
                    + "            LEFT JOIN Clientes\n"
                    + "              ON Rt_perc.CodCli1     =  Clientes.Codigo\n"
                    + "             AND Rt_perc.CodFil      =  Clientes.CodFil\n"
                    + "            LEFT JOIN Rt_perc ParadaAtual\n"
                    + "              ON Rt_perc.Sequencia   =  ParadaAtual.Sequencia\n"
                    + "             AND ParadaAtual.Hora1   =  @inHora\n"
                    + "            WHERE Rt_perc.Sequencia  =  @inSequencia\n"
                    + "            AND   Rt_perc.CodCli1    <> ParadaAtual.CodCli1\n"
                    + "            AND   Rt_perc.Hora1      >  @inHora\n"
                    + "            AND   Rt_perc.CodFil     = @inCodFil\n"
                    + "            GROUP BY Rt_perc.Sequencia) AS ParadaSeguinteRef  \n"
                    + "   ON Rt_percReferencia.Sequencia    =  ParadaSeguinteRef.Sequencia\n"
                    + " LEFT JOIN Rt_perc                                              AS ParadaSeguinte\n"
                    + "   ON ParadaSeguinteRef.Sequencia    =  ParadaSeguinte.Sequencia\n"
                    + "  AND ParadaSeguinteRef.UltimaHora1  =  ParadaSeguinte.Hora1\n"
                    + " LEFT JOIN Clientes                                             AS ClienteParadaSeguinte\n"
                    + "   ON ParadaSeguinte.CodCli1         =  ClienteParadaSeguinte.Codigo  \n"
                    + " WHERE Rt_percReferencia.Sequencia   =  @inSequencia\n"
                    + " AND   Rt_percReferencia.Hora1       =  @inHora\n"
                    + " AND   Rt_percReferencia.CodFil      =  @inCodFil";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(hora1);
            consulta.setBigDecimal(sequencia);
            consulta.setBigDecimal(codfil);
            consulta.select();

            Rt_Perc retorno = new Rt_Perc();

            while (consulta.Proximo()) {
                retorno.setLatAnterior(consulta.getString("lat_parada_anterior"));
                retorno.setLonAnterior(consulta.getString("lon_parada_anterior"));
                retorno.setHora1ParadaAnterior(consulta.getString("hora1_parada_anterior"));

                retorno.setLatAtual(consulta.getString("lat_atual"));
                retorno.setLonAtual(consulta.getString("lon_atual"));
                retorno.setHora1ParadaAtual(consulta.getString("hora1_parada_atual"));

                retorno.setLatSeguinte(consulta.getString("lat_parada_seguinte"));
                retorno.setLonSeguinte(consulta.getString("lon_parada_seguinte"));
                retorno.setHora1ParadaSeguinte(consulta.getString("hora1_parada_seguinte"));
            }

            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.mapaSugestaoRoteirizacao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public String getHora1(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT hora1 "
                    + " FROM Rt_perc"
                    + " WHERE Rt_perc.Sequencia = ? AND Rt_Perc.Parada = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            String hora1 = "";
            while (consulta.Proximo()) {
                hora1 = consulta.getString("hora1");
            }
            consulta.Close();
            return hora1;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.getHora1 - " + e.getMessage() + "\r\n"
                    + "SELECT hora1 "
                    + " FROM Rt_perc"
                    + " WHERE Rt_perc.Sequencia = " + sequencia + " AND Rt_Perc.Parada = " + parada);
        }
    }

    public String ER(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT ER "
                    + " FROM Rt_perc "
                    + "WHERE Sequencia = ? AND Rt_Perc.Parada = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            String er = "";
            while (consulta.Proximo()) {
                er = consulta.getString("ER");
            }
            consulta.Close();
            return er;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.ER - " + e.getMessage() + "\r\n"
                    + "SELECT ER "
                    + " FROM Rt_perc "
                    + "WHERE Sequencia = " + sequencia + " AND Parada = " + parada);
        }
    }

    /**
     * Busca o número da OS da parada
     *
     * @param sequencia
     * @param parada
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String obterOS(String sequencia, String parada, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            Consulta consulta;
            String OS = "", Retorno = "";

            // OBS.: Nova lógica passada por Carlos/SASw em 02/09/2021 - No momento em que estava em implantação na Fidelys
            //       >> Verificar se existe Pedido em Rt_Perc para sequencia e parada. Caso positivo, pegar OS do pedido. Senão, OS de Rt_Perc
            sql = "SELECT \n"
                    + "Pedido.OS\n"
                    + "FROM Rt_Perc\n"
                    + "JOIN Pedido\n"
                    + "  ON Rt_Perc.Pedido = Pedido.Numero\n"
                    + " AND Rt_Perc.CodFil = Pedido.CodFil\n"
                    + "WHERE Rt_Perc.Sequencia = ?\n"
                    + "AND   Rt_Perc.Parada    = ?";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            while (consulta.Proximo()) {
                OS = consulta.getString("OS");
            }

            consulta.close();

            if (null == OS || OS.equals("0") || OS.equals("")) {
                // Não tem OS no pedido - Deve consultar direto em Rt_Perc
                sql = "SELECT OS\n"
                        + " FROM Rt_perc\n"
                        + " WHERE Sequencia = ? AND Rt_Perc.Parada = ? ";

                consulta = new Consulta(sql, persistencia);
                consulta.setString(sequencia);
                consulta.setString(parada);
                consulta.select();

                if (consulta.Proximo()) {
                    Retorno = consulta.getString("OS");
                }
                consulta.Close();

            } else {
                // Tem OS no pedido, portanto retorna o número
                Retorno = OS;
            }

            return Retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.obterOS - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Rt_Perc> alivios(String dtInicio, String dtFim, String codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> retorno = new ArrayList<>();
            String sql = "Select Rotas.CodFil, Clientes.Nred, Clientes.Agencia, Clientes.SubAgencia, Sum(Rt_Guias.Valor) Valor\n"
                    + "From Rt_Perc \n"
                    + "Left join Rotas  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join Clientes  on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                   and Clientes.CodFil = Rotas.CodFil\n"
                    + "where Rotas.data Between ? and ?\n"
                    + "  and Rt_Perc.ER = 'R' \n"
                    + "  and Rt_Perc.CodCli1 in (Select CodCli from PessoaCliAut where PessoaCliAut.CodFil = Rotas.CodFil and PessoaCliAut.Codigo = ?)\n"
                    + "  and Rotas.Flag_Excl <> '*'\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "  and Rt_Guias.Guia > 0\n"
                    + "Group by Rotas.CodFil, Clientes.Nred, Clientes.Agencia, Clientes.SubAgencia";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtInicio);
            consulta.setString(dtFim);
            consulta.setString(codPessoa);
            consulta.select();
            Rt_Perc rt_Perc;
            while (consulta.Proximo()) {
                rt_Perc = new Rt_Perc();
                rt_Perc.setCodFil(consulta.getString("codFil"));
                rt_Perc.setNRed(consulta.getString("Agencia") + "/" + consulta.getString("SubAgencia") + " " + consulta.getString("NRed"));
                rt_Perc.setValor(consulta.getString("Valor"));
                retorno.add(rt_Perc);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.alivios - " + e.getMessage() + "\r\n"
                    + "Select Rotas.CodFil, Clientes.Nred, Clientes.Agencia, Clientes.SubAgencia, Sum(Rt_Guias.Valor) Valor\n"
                    + "From Rt_Perc \n"
                    + "Left join Rotas  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join Clientes  on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                   and Clientes.CodFil = Rotas.CodFil\n"
                    + "where Rotas.data Between " + dtInicio + " and " + dtFim + "\n"
                    + "  and Rt_Perc.ER = 'R' \n"
                    + "  and Rt_Perc.CodCli1 in (Select CodCli from PessoaCliAut where PessoaCliAut.CodFil = Rotas.CodFil and PessoaCliAut.Codigo = " + codPessoa + ")\n"
                    + "  and Rotas.Flag_Excl <> '*'\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "  and Rt_Guias.Guia > 0\n"
                    + "Group by Rotas.CodFil, Clientes.Nred, Clientes.Agencia, Clientes.SubAgencia");
        }
    }

    public List<Rt_Perc> reforcos(String dtInicio, String dtFim, String codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> retorno = new ArrayList<>();
            String sql = "Select Rotas.CodFil, Clientes.Nred, Clientes.Agencia, Clientes.SubAgencia, Sum(Rt_Guias.Valor) Valor\n"
                    + "From Rt_Perc \n"
                    + "Left join Rotas  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join Clientes  on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                   and Clientes.CodFil = Rotas.CodFil\n"
                    + "where Rotas.data Between ? and ?\n"
                    + "  and Rt_Perc.ER = 'E' \n"
                    + "  and Rt_Perc.CodCli1 in (Select CodCli from PessoaCliAut where PessoaCliAut.CodFil = Rotas.CodFil and PessoaCliAut.Codigo = ?)\n"
                    + "  and Rotas.Flag_Excl <> '*'\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "  and Rt_Guias.Guia > 0\n"
                    + "Group by Rotas.CodFil, Clientes.Nred, Clientes.Agencia, Clientes.SubAgencia";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtInicio);
            consulta.setString(dtFim);
            consulta.setString(codPessoa);
            consulta.select();
            Rt_Perc rt_Perc;
            while (consulta.Proximo()) {
                rt_Perc = new Rt_Perc();
                rt_Perc.setCodFil(consulta.getString("codFil"));
                rt_Perc.setNRed(consulta.getString("Agencia") + "/" + consulta.getString("SubAgencia") + " " + consulta.getString("NRed"));
                rt_Perc.setValor(consulta.getString("Valor"));
                retorno.add(rt_Perc);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.alivios - " + e.getMessage() + "\r\n"
                    + "Select Rotas.CodFil, Clientes.Nred, Clientes.Agencia, Clientes.SubAgencia, Sum(Rt_Guias.Valor) Valor\n"
                    + "From Rt_Perc \n"
                    + "Left join Rotas  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join Clientes  on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                   and Clientes.CodFil = Rotas.CodFil\n"
                    + "where Rotas.data Between " + dtInicio + " and " + dtFim + "\n"
                    + "  and Rt_Perc.ER = 'R' \n"
                    + "  and Rt_Perc.CodCli1 in (Select CodCli from PessoaCliAut where PessoaCliAut.CodFil = Rotas.CodFil and PessoaCliAut.Codigo = " + codPessoa + ")\n"
                    + "  and Rotas.Flag_Excl <> '*'\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "  and Rt_Guias.Guia > 0\n"
                    + "Group by Rotas.CodFil, Clientes.Nred, Clientes.Agencia, Clientes.SubAgencia");
        }
    }

    public Rt_Perc listarGuiasRt_Guias(String guia, String serie, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select Rt_perc.Sequencia,  Rt_Perc.CodCli1, Rt_Perc.Hora1, \n"
                    + "Rt_Perc.ER, Rt_Perc.Parada, OS_Vig.Cliente, OS_Vig.NRed, OS_Vig.CliDst, \n"
                    + "OS_Vig.NRedDst, Rt_Guias.Guia, Rt_Guias.Serie, Rt_Perc.Valor TotalGeral, Rt_Guias.CodFil, \n"
                    + "Rt_Guias.OS \n"
                    + "from Rt_Guias \n"
                    + "left join OS_Vig  on OS_Vig.OS  = Rt_Guias.OS \n"
                    + "                 and OS_Vig.CodFil =  Rt_Guias.CodFil \n"
                    + "left join Rt_Perc on OS_Vig.CliDst = Rt_Perc.CodCli1 \n"
                    + "                 and Rt_Perc.Flag_Excl <> '*'\n"
                    + "                 and Rt_Perc.ER          = 'R'\n"
                    + "                 and Rt_Perc.Sequencia   = rt_guias.Sequencia \n"
                    + "where Rt_Guias.Guia = ?\n"
                    + "  and Rt_Guias.Serie = ?\n"
                    + "  and Rt_Guias.CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.select();
            Rt_Perc rt_perc = null;
            if (consulta.Proximo()) {
                rt_perc = new Rt_Perc();
                rt_perc.setSequencia(consulta.getString("Sequencia"));
                rt_perc.setCodCli1(consulta.getString("CodCli1"));
                rt_perc.setHora1(consulta.getString("Hora1"));
                rt_perc.setER(consulta.getString("ER"));
                rt_perc.setParada(consulta.getInt("Parada"));
                rt_perc.setCliOri(consulta.getString("Cliente"));
                rt_perc.setNRed(consulta.getString("NRed"));
                rt_perc.setCliDst(consulta.getString("CliDst"));
                rt_perc.setNRedDst(consulta.getString("NRedDst"));
                rt_perc.setGuia(consulta.getString("Guia"));
                rt_perc.setSerie(consulta.getString("Serie"));
                rt_perc.setValor(consulta.getString("TotalGeral"));
                rt_perc.setCodFil(consulta.getString("CodFil"));
                rt_perc.setOS(consulta.getString("OS"));
            }
            consulta.close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarGuiasRt_Guias - " + e.getMessage() + "\r\n"
                    + " Select Rt_perc.Sequencia,  Rt_Perc.CodCli1, Rt_Perc.Hora1, \n"
                    + "Rt_Perc.ER, Rt_Perc.Parada, OS_Vig.Cliente, OS_Vig.NRed, OS_Vig.CliDst, \n"
                    + "OS_Vig.NRedDst, Rt_Guias.Guia, Rt_Guias.Serie, Rt_Perc.Valor TotalGeral, Rt_Guias.CodFil, \n"
                    + "Rt_Guias.OS \n"
                    + "from Rt_Guias \n"
                    + "left join OS_Vig  on OS_Vig.OS  = Rt_Guias.OS \n"
                    + "                 and OS_Vig.CodFil =  Rt_Guias.CodFil \n"
                    + "left join Rt_Perc on OS_Vig.CliDst = Rt_Perc.CodCli1 \n"
                    + "                 and Rt_Perc.Flag_Excl <> '*'\n"
                    + "                 and Rt_Perc.ER          = 'R'\n"
                    + "                 and Rt_Perc.Sequencia   = rt_guias.Sequencia \n"
                    + "where Rt_Guias.Guia = " + guia + "\n"
                    + "  and Rt_Guias.Serie = " + serie + "\n"
                    + "  and Rt_Guias.CodFil = " + codFil);
        }
    }

    public Rt_Perc listarGuiasGTV(String guia, String serie, String codFil, String seqRota, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Rt_perc.Sequencia,  Rt_Perc.CodCli1, Rt_Perc.Hora1, \n"
                    + " Rt_Perc.ER, Rt_Perc.Parada, OS_Vig.Cliente, OS_Vig.NRed, OS_Vig.CliDst, \n"
                    + " OS_Vig.NRedDst, GTV.Guia, GTV.Serie, Rt_Perc.Valor TotalGeral, GTV.CodFil, \n"
                    + " GTV.OS from GTV \n"
                    + " left join OS_Vig  on GTV.OS     = OS_Vig.OS \n"
                    + "                  and GTV.CodFil = OS_Vig.CodFil \n"
                    + " left join Rt_Perc on OS_Vig.CliDst = Rt_Perc.CodCli1 \n"
                    + "                  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "                  and Rt_Perc.ER          = 'R'\n"
                    + "                  and Rt_Perc.Sequencia   = ?\n"
                    + " where GTV.Guia = ?\n"
                    + "   and GTV.Serie = ?\n"
                    + "   and GTV.CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.select();
            Rt_Perc rt_perc = null;
            if (consulta.Proximo()) {
                rt_perc = new Rt_Perc();
                rt_perc.setSequencia(consulta.getString("Sequencia"));
                rt_perc.setCodCli1(consulta.getString("CodCli1"));
                rt_perc.setHora1(consulta.getString("Hora1"));
                rt_perc.setER(consulta.getString("ER"));
                rt_perc.setParada(consulta.getInt("Parada"));
                rt_perc.setCliOri(consulta.getString("Cliente"));
                rt_perc.setNRed(consulta.getString("NRed"));
                rt_perc.setCliDst(consulta.getString("CliDst"));
                rt_perc.setNRedDst(consulta.getString("NRedDst"));
                rt_perc.setGuia(consulta.getString("Guia"));
                rt_perc.setSerie(consulta.getString("Serie"));
                rt_perc.setValor(consulta.getString("TotalGeral"));
                rt_perc.setCodFil(consulta.getString("CodFil"));
                rt_perc.setOS(consulta.getString("OS"));
            }
            consulta.close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarGuiasGTV - " + e.getMessage() + "\r\n"
                    + "Select Rt_perc.Sequencia,  Rt_Perc.CodCli1, Rt_Perc.Hora1, \n"
                    + " Rt_Perc.ER, Rt_Perc.Parada, OS_Vig.Cliente, OS_Vig.NRed, OS_Vig.CliDst, \n"
                    + " OS_Vig.NRedDst, GTV.Guia, GTV.Serie, Rt_Perc.Valor TotalGeral, GTV.CodFil, \n"
                    + " GTV.OS from GTV \n"
                    + " left join OS_Vig  on GTV.OS     = OS_Vig.OS \n"
                    + "                  and GTV.CodFil = OS_Vig.CodFil \n"
                    + " left join Rt_Perc on OS_Vig.CliDst = Rt_Perc.CodCli1 \n"
                    + "                  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "                  and Rt_Perc.ER          = 'R'\n"
                    + "                  and Rt_Perc.Sequencia   = " + seqRota + "\n"
                    + "where GTV.Guia = " + guia + "\n"
                    + "  and GTV.Serie = " + serie + "\n"
                    + "  and GTV.CodFil = " + codFil);
        }
    }

    public Rt_Perc listarGuiasCxfGuias(String guia, String serie, String codFil, String seqRota, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Rt_perc.Sequencia,  Rt_Perc.CodCli1, Rt_Perc.Hora1, \n"
                    + "Rt_Perc.ER, Rt_Perc.Parada, CxfGuias.CliOri Cliente, Clientes.NRed, CxfGuias.CliDst, \n"
                    + "CliDst.Nred NRedDst, CxFGuias.Guia, CxFGuias.Serie, CxFGuias.Valor TotalGeral, \n"
                    + " CxFGuias.CodFil, CxFGuias.OS from CxfGuias \n"
                    + " left join Rt_Perc on CxfGuias.CliOri = Rt_Perc.CodCli1 \n"
                    + "                 and Rt_Perc.Flag_Excl <>  '*'\n"
                    + "                 and Rt_Perc.ER          = 'R'\n"
                    + "                 and Rt_Perc.Sequencia   = ?\n"
                    + " Left Join Clientes on  Clientes.Codigo      = CxfGuias.CliOri \n"
                    + "                    and Clientes.CodFil      = CxfGuias.CodFil \n"
                    + " Left Join Clientes CliDst on  CliDst.Codigo = CxfGuias.CliDst \n"
                    + "                           and CliDst.CodFil = CxfGuias.CodFil \n"
                    + " where CxFGuias.Guia = ?\n"
                    + "   and CxFGuias.Serie = ?\n"
                    + "   and CxFGuias.CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.select();
            Rt_Perc rt_perc = null;
            if (consulta.Proximo()) {
                rt_perc = new Rt_Perc();
                rt_perc.setSequencia(consulta.getString("Sequencia"));
                rt_perc.setCodCli1(consulta.getString("CodCli1"));
                rt_perc.setHora1(consulta.getString("Hora1"));
                rt_perc.setER(consulta.getString("ER"));
                rt_perc.setParada(consulta.getInt("Parada"));
                rt_perc.setCliOri(consulta.getString("Cliente"));
                rt_perc.setNRed(consulta.getString("NRed"));
                rt_perc.setCliDst(consulta.getString("CliDst"));
                rt_perc.setNRedDst(consulta.getString("NRedDst"));
                rt_perc.setGuia(consulta.getString("Guia"));
                rt_perc.setSerie(consulta.getString("Serie"));
                rt_perc.setValor(consulta.getString("TotalGeral"));
                rt_perc.setCodFil(consulta.getString("CodFil"));
                rt_perc.setOS(consulta.getString("OS"));
            }
            consulta.close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarGuiasCxfGuias - " + e.getMessage() + "\r\n"
                    + "Select Rt_perc.Sequencia,  Rt_Perc.CodCli1, Rt_Perc.Hora1, \n"
                    + "Rt_Perc.ER, Rt_Perc.Parada, CxfGuias.CliOri Cliente, Clientes.NRed, CxfGuias.CliDst, \n"
                    + "CliDst.Nred NRedDst, CxFGuias.Guia, CxFGuias.Serie, CxFGuias.Valor TotalGeral, \n"
                    + " CxFGuias.CodFil, CxFGuias.OS from CxfGuias \n"
                    + " left join Rt_Perc on CxfGuias.CliOri = Rt_Perc.CodCli1 \n"
                    + "                 and Rt_Perc.Flag_Excl <>  '*'\n"
                    + "                 and Rt_Perc.ER          = 'R'\n"
                    + "                 and Rt_Perc.Sequencia   = " + seqRota + "\n"
                    + " Left Join Clientes on  Clientes.Codigo      = CxfGuias.CliOri \n"
                    + "                    and Clientes.CodFil      = CxfGuias.CodFil \n"
                    + " Left Join Clientes CliDst on  CliDst.Codigo = CxfGuias.CliDst \n"
                    + "                           and CliDst.CodFil = CxfGuias.CodFil \n"
                    + "where CxFGuias.Guia = " + guia + "\n"
                    + "  and CxFGuias.Serie = " + serie + "\n"
                    + "  and CxFGuias.CodFil = " + codFil);
        }
    }

    public Rt_Perc listarGuiasTesSaidas(String guia, String serie, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Rt_perc.Sequencia,  Rt_Perc.CodCli1, Rt_Perc.Hora1, \n"
                    + "Rt_Perc.ER, Rt_Perc.Parada, Rt_Perc.Nred, Clientes.NRed NRedDst, TesSaidas.CodCli1 Cliente, TesSaidas.Guia, TesSaidas.Serie, \n"
                    + "TesSaidas.CodCli2 CliDst,TesSaidas.CodFil, TesSaidas.TotalGeral, GTV.OS  from TesSaidas \n"
                    + "left join Rt_Perc on TesSaidas.Codcli2 = Rt_Perc.CodCli2 \n"
                    + "                 and Rt_Perc.flag_excl <>  '*'\n"
                    + "                 and Rt_Perc.Pedido    = TesSaidas.Pedido \n"
                    + " left join Clientes on Clientes.Codigo = TesSaidas.CodCli1 \n"
                    + "                    and Clientes.CodFil = TesSaidas.CodFil \n"
                    + " left join GTV      on  GTV.Guia         = TesSaidas.Guia   \n"
                    + "                    and GTV.Serie        = TesSaidas.Serie  \n"
                    + "where TesSaidas.Guia = ?\n"
                    + "  and TesSaidas.Serie = ?\n"
                    + "  and TesSaidas.CodFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.select();
            Rt_Perc rt_perc = null;
            if (consulta.Proximo()) {
                rt_perc = new Rt_Perc();
                rt_perc.setSequencia(consulta.getString("Sequencia"));
                rt_perc.setCodCli1(consulta.getString("CodCli1"));
                rt_perc.setHora1(consulta.getString("Hora1"));
                rt_perc.setER(consulta.getString("ER"));
                rt_perc.setParada(consulta.getInt("Parada"));
                rt_perc.setCliOri(consulta.getString("Cliente"));
                rt_perc.setNRed(consulta.getString("NRed"));
                rt_perc.setCliDst(consulta.getString("CliDst"));
                rt_perc.setNRedDst(consulta.getString("NRedDst"));
                rt_perc.setGuia(consulta.getString("Guia"));
                rt_perc.setSerie(consulta.getString("Serie"));
                rt_perc.setValor(consulta.getString("TotalGeral"));
                rt_perc.setCodFil(consulta.getString("CodFil"));
                rt_perc.setOS(consulta.getString("OS"));
            }
            consulta.close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarGuiasTesSaidas - " + e.getMessage() + "\r\n"
                    + "Select Rt_perc.Sequencia,  Rt_Perc.CodCli1, Rt_Perc.Hora1, \n"
                    + "Rt_Perc.ER, Rt_Perc.Parada, Rt_Perc.Nred, Clientes.NRed NRedDst, TesSaidas.CodCli1 Cliente, TesSaidas.Guia, TesSaidas.Serie, \n"
                    + "TesSaidas.CodCli2 CliDst,TesSaidas.CodFil, TesSaidas.TotalGeral, GTV.OS  from TesSaidas \n"
                    + "left join Rt_Perc on TesSaidas.Codcli2 = Rt_Perc.CodCli2 \n"
                    + "                 and Rt_Perc.flag_excl <>  '*'\n"
                    + "                 and Rt_Perc.Pedido    = TesSaidas.Pedido \n"
                    + " left join Clientes on Clientes.Codigo = TesSaidas.CodCli1 \n"
                    + "                    and Clientes.CodFil = TesSaidas.CodFil \n"
                    + " left join GTV      on  GTV.Guia         = TesSaidas.Guia   \n"
                    + "                    and GTV.Serie        = TesSaidas.Serie  \n"
                    + "where TesSaidas.Guia = " + guia + "\n"
                    + "  and TesSaidas.Serie = " + serie + "\n"
                    + "  and TesSaidas.CodFil = " + codFil);
        }
    }

    public Rt_Perc listarGuiasPedido(String guia, String serie, String seqRota, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Pedido.parada Parada, Rt_Perc.Parada ParadaRT \n"
                    + "from Pedido \n"
                    + "Left join TesSaidas on  TesSaidas.Pedido = Pedido.Numero \n"
                    + "                    and TesSaidas.CodFil = Pedido.CodFil \n"
                    + "left Join rt_Perc on  Rt_perc.Sequencia = Pedido.SeqRota \n"
                    + "                     and Rt_Perc.Pedido = Pedido.Numero \n"
                    + " where TesSaidas.Guia   = ?\n"
                    + "   and TesSaidas.Serie  = ?\n"
                    + "   and Pedido.SeqRota   = ?\n"
                    + "   and TesSaidas.CodFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(seqRota);
            consulta.setString(codFil);
            consulta.select();
            Rt_Perc rt_perc = null;
            if (consulta.Proximo()) {
                rt_perc = new Rt_Perc();
                rt_perc.setParada(consulta.getInt("ParadaRT"));
                rt_perc.setPedido(consulta.getString("Parada"));
            }
            consulta.close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listarGuiasTesSaidas - " + e.getMessage() + "\r\n"
                    + "Select Pedido.parada Parada, Rt_Perc.Parada ParadaRT \n"
                    + "from Pedido \n"
                    + "Left join TesSaidas on  TesSaidas.Pedido = Pedido.Numero \n"
                    + "                    and TesSaidas.CodFil = Pedido.CodFil \n"
                    + "left Join rt_Perc on  Rt_perc.Sequencia = Pedido.SeqRota \n"
                    + "                     and Rt_Perc.Pedido = Pedido.Numero \n"
                    + "where TesSaidas.Guia = " + guia + "\n"
                    + "  and TesSaidas.Serie = " + serie + "\n"
                    + "  and Pedido.SeqRota   = " + seqRota + "\n"
                    + "  and TesSaidas.CodFil = " + codFil);
        }
    }

    public Rt_Perc buscarPedido(String seqRota, String parada, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Pedido.Numero Pedido, Pedido.SeqRota, Pedido.Parada \n"
                    + " from Rt_Perc \n"
                    + " Left Join Pedido on  Pedido.SeqRota = Rt_perc.Sequencia \n"
                    + "                  and Pedido.Parada  = Rt_Perc.Parada    \n"
                    + "                  and Rt_Perc.Flag_Excl <> '*'\n"
                    + " where Rt_Perc.Sequencia = ?\n"
                    + "   and Rt_Perc.Parada    = ?\n"
                    + "   and Rt_Perc.Flag_Excl <> '*'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(parada);
            consulta.select();
            Rt_Perc rt_perc = null;
            if (consulta.Proximo()) {
                rt_perc = new Rt_Perc();
                rt_perc.setPedido(consulta.getString("Pedido"));
                rt_perc.setSequencia(consulta.getString("SeqRota"));
                rt_perc.setParada(consulta.getInt("Parada"));
            }
            consulta.close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.buscarPedido - " + e.getMessage() + "\r\n"
                    + "Select Pedido.Numero Pedido, Pedido.SeqRota, Pedido.Parada \n"
                    + " from Rt_Perc \n"
                    + " Left Join Pedido on  Pedido.SeqRota = Rt_perc.Sequencia \n"
                    + "                  and Pedido.Parada  = Rt_Perc.Parada    \n"
                    + "                  and Rt_Perc.Flag_Excl <> '*'\n"
                    + " where Rt_Perc.Sequencia = " + seqRota + "\n"
                    + "   and Rt_Perc.Parada    = " + parada + "\n"
                    + "   and Rt_Perc.Flag_Excl <> '*'");
        }
    }

    public CxFGuias buscarCxFGuias(String seqRota, String remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Remessa, Max(DtSai) DtSai, Max(HrSai) HrSai, Count(*) Qtde, Sum(CXFguias.Valor) as Valor \n"
                    + " from Rt_Perc \n"
                    + " left Join rotas     on  Rotas.Sequencia = Rt_Perc.Sequencia \n"
                    + " Left Join CXFGuias on CXFGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                    and CXFGuias.Hora1D = Rt_Perc.Hora1 \n"
                    + " where Rotas.Sequencia = ?\n"
                    + "   and (Rt_Perc.Flag_Excl <> '*'\n"
                    + "        and Rt_Perc.OperExcl not like 'TRF-%') \n"
                    + "   and Rt_Perc.ER        = 'E'\n"
                    + "   and CxFGuias.DtSai is not null \n"
                    + "   and CxFGuias.Remessa  = ?\n"
                    + " Group by  Remessa \n"
                    + " Order by Remessa desc ;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.select();
            CxFGuias retorno = null;
            if (consulta.Proximo()) {
                retorno = new CxFGuias();
                retorno.setRemessa(consulta.getString("remessa"));
                retorno.setDtSai(consulta.getString("DtSai"));
                retorno.setHrSai(consulta.getString("HrSai"));
                retorno.setQtde(consulta.getString("Qtde"));
                retorno.setValor(consulta.getString("Valor"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.buscarCxFGuias - " + e.getMessage() + "\r\n"
                    + "Select Remessa, Max(DtSai) DtSai, Max(HrSai) HrSai, Count(*) Qtde, Sum(CXFguias.Valor) as Valor \n"
                    + " from Rt_Perc \n"
                    + " left Join rotas     on  Rotas.Sequencia = Rt_Perc.Sequencia \n"
                    + " Left Join CXFGuias on CXFGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                    and CXFGuias.Hora1D = Rt_Perc.Hora1\n"
                    + " where Rotas.Sequencia = " + seqRota + "\n"
                    + "   and (Rt_Perc.Flag_Excl <> '*'\n"
                    + "        and Rt_Perc.OperExcl not like 'TRF-%') \n"
                    + "   and Rt_Perc.ER        = 'E'\n"
                    + "   and CxFGuias.DtSai is not null \n"
                    + "   and CxFGuias.Remessa  = " + remessa + "\n"
                    + " Group by  Remessa \n"
                    + " Order by Remessa desc ;");
        }
    }

    public String obterQtdeVolumesRmessa(String seqRota, String remessa, boolean EC, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Remessa, Sum(CxfGuiasVol.Qtde) QtdeVol \n"
                    + " from Rt_Perc \n"
                    + " left Join rotas     on  Rotas.Sequencia = Rt_Perc.Sequencia \n"
                    + " Left Join CXFGuias on CXFGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                    and CXFGuias.Hora1D = Rt_Perc.Hora1 \n"
                    + " Left Join CxfGuiasVol on  CxfGuiasVol.Guia = CxfGuias.Guia \n"
                    + "                       and CxfGuiasVol.Serie = CxfGuias.Serie \n"
                    + " where Rotas.Sequencia = ?\n"
                    + "   and Rt_Perc.Flag_Excl <> '*'\n"
                    + "   and (Rt_Perc.ER        = 'E'\n";
            if (EC) {
                sql += "     or Rt_Perc.ER = 'EC')\n";
            } else {
                sql += "       and Rt_Perc.OperExcl not like 'TRF-%') \n";
            }
            sql += "   and CxFGuias.DtSai is not null \n"
                    + "   and CxFGuias.Remessa  = ?\n"
                    + " Group by  Remessa \n"
                    + " Order by Remessa desc ;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.select();
            String retorno = null;
            if (consulta.Proximo()) {
                retorno = consulta.getString("QtdeVol");
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.obterQtdeVolumesRmessa - " + e.getMessage() + "\r\n"
                    + "Select Remessa, Sum(CxfGuiasVol.Qtde) QtdeVol \n"
                    + " from Rt_Perc \n"
                    + " left Join rotas     on  Rotas.Sequencia = Rt_Perc.Sequencia \n"
                    + " Left Join CXFGuias on CXFGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                    and CXFGuias.Hora1D = Rt_Perc.Hora1 \n"
                    + " Left Join CxfGuiasVol on  CxfGuiasVol.Guia = CxfGuias.Guia \n"
                    + "                       and CxfGuiasVol.Serie = CxfGuias.Serie \n"
                    + " where Rotas.Sequencia = " + seqRota + ""
                    + "   and Rt_Perc.Flag_Excl <> '*'\n"
                    + "   and (Rt_Perc.ER        = 'E'\n"
                    + (EC ? "     or Rt_Perc.ER = 'EC')\n" : "       and Rt_Perc.OperExcl not like 'TRF-%') \n")
                    + "   and CxFGuias.DtSai is not null \n"
                    + "   and CxFGuias.Remessa  = " + remessa + "\n"
                    + " Group by  Remessa \n"
                    + " Order by Remessa desc ;");
        }
    }

    public String obterValorCxFGuias(String seqRota, String remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select  Sum(CXFguias.Valor) as Valor from Rt_Perc \n"
                    + " left Join Rotas    on  Rotas.Sequencia     = Rt_Perc.Sequencia \n"
                    + " Left Join CXFGuias on  CXFGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                    and CXFGuias.Hora1D     = Rt_Perc.Hora1     \n"
                    + " where Rt_Perc.Sequencia =  ? \n"
                    + "   and Rt_Perc.Flag_Excl <> '*' \n"
                    + "   and Rt_Perc.ER        like  'E%' \n"
                    + "   and CXFGuias.Remessa  = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.select();
            String retorno = null;
            if (consulta.Proximo()) {
                retorno = consulta.getString("Valor");
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.obterValorCxFGuias - " + e.getMessage() + "\r\n"
                    + "Select  Sum(CXFguias.Valor) as Valor from Rt_Perc \n"
                    + " left Join Rotas    on  Rotas.Sequencia     = Rt_Perc.Sequencia \n"
                    + " Left Join CXFGuias on  CXFGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                    and CXFGuias.Hora1D     = Rt_Perc.Hora1     \n"
                    + " where Rt_Perc.Sequencia =  " + seqRota + " \n"
                    + "   and Rt_Perc.Flag_Excl <> '*' \n"
                    + "   and Rt_Perc.ER        like  'E%' \n"
                    + "   and CXFGuias.Remessa  = " + remessa);
        }
    }

    public List<Rt_Perc> getFromSequenciaCodCli2(String sequencia, String codCli2, Persistencia persistencia) throws Exception {
        String sql = "SELECT Rt_perc.Sequencia, Rt_Perc.Parada, Rt_perc.Hora1, Rt_Perc.CodCli1, Rt_Perc.NRed\n"
                + "FROM Rt_Perc\n"
                + "WHERE Rt_Perc.Sequencia = ? \n"
                + "  AND Rt_Perc.Flag_Excl <> '*'\n"
                + "  AND Rt_Perc.CodCLi2 = ? \n"
                + "ORDER BY Rt_Perc.Hora1;";

        Consulta consulta = null;
        List<Rt_Perc> lista = new ArrayList<>();
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(codCli2);
            consulta.select();

            while (consulta.Proximo()) {
                Rt_Perc item = new Rt_Perc();
                item.setSequencia(consulta.getString("Sequencia"));
                item.setParada(consulta.getInt("Parada"));
                item.setHora1(consulta.getString("Hora1"));
                item.setCodCli1(consulta.getString("CodCli1"));
                item.setNRed(consulta.getString("NRed"));

                lista.add(item);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public List<Rt_Perc> getListaHora1DeSequencia(String sequencia, Persistencia persistencia) throws Exception {
        String sql = "SELECT Hora1\n"
                + "FROM Rt_Perc\n"
                + "WHERE Sequencia = ? \n"
                + "  AND Flag_Excl <> '*'\n"
                + "ORDER BY Hora1 DESC;";

        List<Rt_Perc> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.select();
            while (consulta.Proximo()) {
                Rt_Perc item = new Rt_Perc();
                item.setHora1(consulta.getString("Hora1"));

                lista.add(item);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public String getHora1DeSequenciaECodCli(String sequencia, String codCli, Persistencia persistencia) throws Exception {
        String sql = "SELECT TOP 1 Hora1\n"
                + "FROM Rt_Perc\n"
                + "WHERE Sequencia = ? \n"
                + "  AND CodCli1 = ? \n"
                + "  AND Flag_Excl <> '*'\n"
                + "ORDER BY Hora1 DESC;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(codCli);
            consulta.select();
            if (consulta.Proximo()) {
                return consulta.getString("Hora1");
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return null;
    }

    public List<Rt_Perc> getHora1DeSequenciaECodCli1e2(String sequencia,
            String codCli1, String codCli2, Persistencia persistencia) throws Exception {
        String sql = "SELECT Parada, Hora1\n"
                + "FROM Rt_Perc\n"
                + "WHERE Sequencia = ? \n"
                + "  AND CodCli1 = ? \n"
                + "  AND CodCli2 = ? \n"
                + "  AND Flag_Excl <> '*'\n"
                + "ORDER BY Hora1 DESC;";

        List<Rt_Perc> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(codCli1);
            consulta.setString(codCli2);
            consulta.select();
            while (consulta.Proximo()) {
                Rt_Perc item = new Rt_Perc();
                item.setParada(consulta.getInt("Parada"));
                item.setHora1(consulta.getString("Hora1"));

                lista.add(item);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public List<Rt_Perc> getRtPercListFromDataNRed(String data, String nomeRed, Persistencia persistencia) throws Exception {
        String sql = "SELECT Rt_Perc.Parada, Rt_Perc.Operador, Rt_Perc.Valor ValorTotal\n"
                + "FROM Rotas\n"
                + "         LEFT JOIN Rt_Perc ON Rt_perc.Sequencia = Rotas.Sequencia\n"
                + "WHERE Rotas.Data = ? \n"
                + "  AND Rt_Perc.Nred = ? \n"
                + "  AND Rt_Perc.Operador LIKE 'Trb%';";

        List<Rt_Perc> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(nomeRed);

            consulta.select();
            while (consulta.Proximo()) {
                Rt_Perc item = new Rt_Perc();
                item.setParada(consulta.getInt("Parada"));
                item.setOperador(consulta.getString("Operador"));
                item.setValor(consulta.getString("ValorTotal"));

                lista.add(item);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public List<String> CarregaCacambasEntrega(String seqRota, Persistencia persistencia) throws Exception {
        // Observação, fixados códigos de referencia da Eco Visão
        // Códigos fixos: 9996900,9996015,8886318,8886987
        // 9996900 - BASE SIA
        // 9996015 - ATERRO
        // 8886318 - GARAJAO
        // 8886987 - BASE RK

        String sql = "SELECT DISTINCT\n"
                + "CxFGuiasVol.Lacre\n"
                + "FROM Rt_Perc AS Rt_PercRec\n"
                + "JOIN Rt_guias \n"
                + "  ON Rt_PercRec.Sequencia = Rt_guias.Sequencia\n"
                + " AND Rt_PercRec.Parada = Rt_guias.Parada\n"
                + " AND Rt_PercRec.CodFil = Rt_guias.CodFil\n"
                + "JOIN CxFGuiasVol\n"
                + "  ON CxFGuiasVol.Guia = Rt_guias.Guia\n"
                + " AND CxFGuiasVol.Serie = Rt_guias.Serie\n"
                + " AND CxFGuiasVol.CodFil = Rt_Guias.CodFil\n"
                + "LEFT JOIN(SELECT\n"
                + "           CxFGuiasVol.Lacre,\n"
                + "           MAX(Rt_Perc.Hora1) Hora1\n"
                + "           FROM Rt_Perc\n"
                + "           JOIN Rt_guias\n"
                + "             ON Rt_Perc.Sequencia = Rt_guias.Sequencia\n"
                + "            AND Rt_Perc.Parada = Rt_guias.Parada\n"
                + "            AND Rt_Perc.CodFil = Rt_guias.CodFil\n"
                + "           JOIN CxFGuiasVol\n"
                + "             ON CxFGuiasVol.Guia = Rt_guias.Guia\n"
                + "            AND CxFGuiasVol.Serie = Rt_guias.Serie\n"
                + "            AND CxFGuiasVol.CodFil = Rt_Guias.CodFil\n"
                + "           WHERE Rt_Perc.Sequencia = ?\n"
                + "           AND   Rt_Perc.Flag_excl <> '*'\n"
                + "           AND   Rt_Perc.ER = 'E'\n"
                + "           AND   Rt_Perc.HrSaida IS NOT NULL\n"
                + "           GROUP BY CxFGuiasVol.Lacre) AS Entregues\n"
                + "  ON CxFGuiasVol.Lacre = Entregues.Lacre \n"
                + "WHERE Rt_PercRec.Sequencia = ?\n"
                + "AND   Rt_PercRec.Flag_excl <> '*'\n"
                + "AND   Rt_PercRec.HrSaida IS NOT NULL\n"
                + "AND   Rt_PercRec.ER = 'R'\n"
                + "AND   (Entregues.Lacre IS NULL OR Entregues.Hora1 < Rt_PercRec.Hora1)";

        List<String> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(seqRota);

            consulta.select();
            while (consulta.Proximo()) {
                lista.add(consulta.getString("Lacre"));
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public List<String> CarregaCacambasRecolhimento(String codFil, String Data, String codCli, String SeqRota, Persistencia persistencia) throws Exception {
        
        String sql = "Select  ROW_NUMBER() OVER(ORDER BY CxfguiasVol.Lacre, Rotas.Data, Rt_Perc.Hora1 ASC) AS Row,CxfGuiasVol.Lacre IDEquip, Rotas.Data, Rotas.Sequencia SeqRota, Rotas.Rota, Rt_Perc.Parada, Rt_Perc.ER, Rt_perc.CodCli1,\n"
                + "          Rt_Perc.NRed, Rt_Perc.Hora1, Rt_Perc.HrCheg, Rt_Perc.HrSaida, Rt_Perc.TempoEspera,Funcion.Nome_Guer Motorista, Veiculos.Numero,\n"
                + "            Veiculos.Placa+'/'+Veiculos.UF_Placa Placa, VeiculosMod.Descricao Modelo, CxfGuiasVol.Guia, CxfGuiasVol.Serie INTO #tmp\n"
                + "            from Rt_perc\n"
                + "            left join Rotas on Rotas.sequencia = Rt_Perc.Sequencia\n"
                + "            left join Rt_Guias on Rt_guias.Sequencia = Rt_Perc.Sequencia\n"
                + "                               and Rt_Guias.Parada = Rt_Perc.Parada\n"
                + "            left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_guias.Guia\n"
                + "                                 and CxfGuiasVol.Serie = Rt_guias.Serie\n"
                + "            left join Escala on Escala.SeqRota = Rotas.Sequencia\n"
                + "            left join Funcion on Funcion.Matr = Escala.Matrmot\n"
                + "            left join Veiculos on Veiculos.Numero = Escala.Veiculo\n"
                + "            left join VeiculosMod on VeiculosMod.Codigo = Veiculos.Modelo\n"
                //+ "            where Rotas.Data  >= ?\n"
                + "            where Rotas.CodFil = ?\n"
                + "              and Rt_perc.Sequencia = ?\n"
                + "              and CxfGuiasVol.Lacre is not null\n"
                + "              and CxfGuiasVol.Lacre <> '';\n"
                + "            \n"
                + "            \n"
                + "SELECT \n"
                + "A.IDEquip,\n"
                + "A.CodCli1\n"
                + "FROM #tmp AS A\n"
                + "JOIN (SELECT\n"
                + "     MAX(Row) Row,\n"
                + "     IDEquip\n"
                + "     FROM #tmp\n"
                + "     GROUP BY IDEquip) AS B\n"
                + "  ON A.Row = B.Row\n"
                + "WHERE A.CodCli1 = ?\n"
                + "ORDER BY A.Row";

        List<String> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            //consulta.setString(Data);
            consulta.setString(codFil);
            consulta.setString(SeqRota);
            consulta.setString(codCli);

            consulta.select();
            while (consulta.Proximo()) {
                lista.add(consulta.getString("IDEquip"));
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public String obterSeqRota(String codPessoa, String data, Persistencia persistencia) throws Exception {
        String sql = "", Retorno = "";

        try {
            sql = "SELECT \n"
                    + "Escala.SeqRota\n"
                    + "FROM Escala\n"
                    + "JOIN Pessoa\n"
                    + "  ON Escala.MatrChe = Pessoa.Matr\n"
                    + "JOIN Rotas\n"
                    + "  ON Escala.SeqRota = Rotas.Sequencia\n"
                    + "WHERE Escala.data = ?\n"
                    + "AND   Rotas.flag_excl <> '*'\n"
                    + "AND   Pessoa.Codigo = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setBigDecimal(codPessoa);

            consulta.select();

            while (consulta.Proximo()) {
                Retorno = consulta.getString("SeqRota");
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.obterSeqRota - " + e.getMessage() + "\r\n" + sql);
        }
    }
}

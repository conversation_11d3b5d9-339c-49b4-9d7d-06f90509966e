/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Pessoas.PessoasSatMobWeb;
import Dados.Persistencia;
import SasBeans.Pessoa;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PessoaLazyList extends LazyDataModel<Pessoa> {

    private static final long serialVersionUID = 1L;
    private List<Pessoa> pessoas;
    private final PessoasSatMobWeb pessoasatmobweb;
    private Persistencia persistencia;

    public PessoaLazyList(Persistencia pst) {
        pessoasatmobweb = new PessoasSatMobWeb();
        persistencia = pst;
    }

    @Override
    public List<Pessoa> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            pessoas = pessoasatmobweb.listaPaginada(first, pageSize, filters, persistencia);

            // set the total of players
            setRowCount(pessoasatmobweb.Contagem(filters, persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return pessoas;
    }

    @Override
    public Object getRowKey(Pessoa pessoa) {
        return pessoa.getCodigo();
    }

    @Override
    public Pessoa getRowData(String codigo) {
        for (Pessoa pessoa : pessoas) {
            if (codigo.equals(pessoa.getCodigo().toPlainString())) {
                return pessoa;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

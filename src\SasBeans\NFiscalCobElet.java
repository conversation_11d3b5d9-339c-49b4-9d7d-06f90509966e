/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class NFiscalCobElet {

    private BigDecimal Praca;
    private String Serie;
    private BigDecimal Numero;
    private BigDecimal Sequencia;
    private BigDecimal Ordem;
    private String DtEnvio;
    private String HrEnvio;
    private String Operador;
    private String MsgLida;
    private String vBoleto;
    private String vNFiscal;
    private String vOutros;

    public BigDecimal getPraca() {
        return Praca;
    }

    public void setPraca(BigDecimal Praca) {
        this.Praca = Praca;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public BigDecimal getNumero() {
        return Numero;
    }

    public void setNumero(BigDecimal Numero) {
        this.Numero = Numero;
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public BigDecimal getOrdem() {
        return Ordem;
    }

    public void setOrdem(BigDecimal Ordem) {
        this.Ordem = Ordem;
    }

    public String getDtEnvio() {
        return DtEnvio;
    }

    public void setDtEnvio(String DtEnvio) {
        this.DtEnvio = DtEnvio;
    }

    public String getHrEnvio() {
        return HrEnvio;
    }

    public void setHrEnvio(String HrEnvio) {
        this.HrEnvio = HrEnvio;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getMsgLida() {
        return MsgLida;
    }

    public void setMsgLida(String MsgLida) {
        this.MsgLida = MsgLida;
    }

    public String getvBoleto() {
        return vBoleto;
    }

    public void setvBoleto(String vBoleto) {
        this.vBoleto = vBoleto;
    }

    public String getvNFiscal() {
        return vNFiscal;
    }

    public void setvNFiscal(String vNFiscal) {
        this.vNFiscal = vNFiscal;
    }

    public String getvOutros() {
        return vOutros;
    }

    public void setvOutros(String vOutros) {
        this.vOutros = vOutros;
    }
}

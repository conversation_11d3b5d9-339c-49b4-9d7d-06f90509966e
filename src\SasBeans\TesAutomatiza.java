/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class TesAutomatiza {

    BigDecimal Sequencia;
    LocalDate Data;
    BigDecimal CodPessoa;
    BigDecimal Matr;
    BigDecimal CodFil;
    String CodTes;
    int Camera;
    int TipoContadora;
    String Operador;
    LocalDate Dt_alter;
    String Hr_alter;
    String DescFil;
    String DescTes;
    String cidadeFil;

    String Nome;
    String CargoDesc;
    String Tesouraria;

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getCargoDesc() {
        return CargoDesc;
    }

    public void setCargoDesc(String CargoDesc) {
        this.CargoDesc = CargoDesc;
    }

    public String getTesouraria() {
        return Tesouraria;
    }

    public void setTesouraria(String Tesouraria) {
        this.Tesouraria = Tesouraria;
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(BigDecimal Matr) {
        this.Matr = Matr;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodTes() {
        return CodTes;
    }

    public void setCodTes(String CodTes) {
        this.CodTes = CodTes;
    }

    public int getCamera() {
        return Camera;
    }

    public void setCamera(int Camera) {
        this.Camera = Camera;
    }

    public int getTipoContadora() {
        return TipoContadora;
    }

    public void setTipoContadora(int TipoContadora) {
        this.TipoContadora = TipoContadora;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(LocalDate Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getDescFil() {
        return DescFil;
    }

    public void setDescFil(String DescFil) {
        this.DescFil = DescFil;
    }

    public String getDescTes() {
        return DescTes;
    }

    public void setDescTes(String DescTes) {
        this.DescTes = DescTes;
    }

    public String getCidadeFil() {
        return cidadeFil;
    }

    public void setCidadeFil(String cidadeFil) {
        this.cidadeFil = cidadeFil;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class StatWeb {

    private String Empresa;

    private String Data;
    private String Hora;
    private String Consulta;
    private String Inclusao;
    private String Alteracao;
    private String Exclusao;

    public String getEmpresa() {
        return Empresa;
    }

    public void setEmpresa(String Empresa) {
        this.Empresa = Empresa;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getConsulta() {
        return Consulta;
    }

    public void setConsulta(String Consulta) {
        this.Consulta = Consulta;
    }

    public String getInclusao() {
        return Inclusao;
    }

    public void setInclusao(String Inclusao) {
        this.Inclusao = Inclusao;
    }

    public String getAlteracao() {
        return Alteracao;
    }

    public void setAlteracao(String Alteracao) {
        this.Alteracao = Alteracao;
    }

    public String getExclusao() {
        return Exclusao;
    }

    public void setExclusao(String Exclusao) {
        this.Exclusao = Exclusao;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.Empresa);
        hash = 97 * hash + Objects.hashCode(this.Data);
        hash = 97 * hash + Objects.hashCode(this.Hora);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final StatWeb other = (StatWeb) obj;
        if (!Objects.equals(this.Empresa, other.Empresa)) {
            return false;
        }
        if (!Objects.equals(this.Data, other.Data)) {
            return false;
        }
        if (!Objects.equals(this.Hora, other.Hora)) {
            return false;
        }
        return true;
    }
}

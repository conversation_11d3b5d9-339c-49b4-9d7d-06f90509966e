package br.com.sasw.pacotesuteis.utilidades;

public class SatCripto {

    private static int TamCripto, TamChave, PosLetra, Pos;

    private static final String chave = "Active Solutions SAS Systems";

    public static String Criptografar(String vCriptografar, String vChave) {
        String vRetorno = "";
        TamCripto = vCriptografar.length();
        TamChave = vChave.length();
        for (int i = 0; i < TamCripto; i++) {
            Pos = i % TamChave;
            if (Pos == -1) {
                Pos = TamChave - 1;
            }
            PosLetra = (((int) (vCriptografar.charAt(i))) ^ ((int) (vChave.charAt(Pos))));
            if ((PosLetra == 0) || (PosLetra == 26) || (PosLetra == 13) || (PosLetra == 44) || (PosLetra == 96)) {
                PosLetra = vCriptografar.charAt(i);
            }
            vRetorno += (char) PosLetra;
        }
        return vRetorno;
    }

    public static String getChave() {
        return chave;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.RelatorioCxForte;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioCxForteDao {

    public List<RelatorioCxForte> buscarGuiasDeclaradas(String codFil, String seqRota, Persistencia persistencia) throws Exception {
        try {
            List<RelatorioCxForte> retorno = new ArrayList<>();
            RelatorioCxForte relatorioCxForte;
            String sql = "Select \n"
                    + "Count(Distinct Convert(Varchar(max),Convert(BigInt,Rt_Guias.Guia))+Rt_Guias.Serie) QtdeGuias, \n"
                    + "Count(CXfGuiasVol.Qtde) QtdeVolumes, Rt_Perc.dpar <PERSON>da, Rt_PercDst.NRed, Rt_PercDst.Hora1 Hora,\n"
                    + " CONVERT(VarChar, Rotas.Data, 112) Data \n"
                    + "From Rotas \n"
                    + "Left Join Rt_guias on  Rt_Guias.Sequencia   = Rotas.Sequencia \n"
                    + "                  and Rotas.CodFil         = ?\n"
                    + "Left Join Rt_Perc  on  Rt_perc.Sequencia    = Rt_guias.Sequencia \n"
                    + "					and Rt_Perc.Parada       = Rt_Guias.Parada    \n"
                    + "					and Rt_Perc.CodCli2      = (SELECT TOP 1 CodCli FROM CxForte WHERE CodFil = Rt_Perc.CodFil Order by dtFecha desc)\n"
                    + "					and Rt_Perc.Flag_Excl   <> '*'\n"
                    + "Left Join  Rt_Perc Rt_PercDst  on  Rt_PercDst.Sequencia    = Rt_Perc.Sequencia \n"
                    + "					and  Rt_PercDst.Parada =  Rt_Perc.Dpar\n"
                    + "					and Rt_PercDst.Flag_Excl   <> '*'\n"
                    + "Left Join CxfGuias    on  CxfGuias.SeqRota  = Rotas.Sequencia \n"
                    + "                    and CxfGuias.RotaEnt  = Rotas.Rota      \n"
                    + "                    and CxfGuias.RotaEnt  <> '090'\n"
                    + "                            and CxfGuias.Guia = Rt_Guias.Guia \n"
                    + "Left Join CxfGuiasVol on  CxfGuiasVol.Guia  = Rt_Guias.Guia \n"
                    + "                    and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    + "where Rotas.Sequencia =  ?\n"
                    + "  and (Rt_perc.ER = 'R'\n"
                    + "    or Rt_perc.ER = 'T')\n"
                    + "  and (Substring(Rt_Perc.Operador,1,3) <> 'Trb'\n"
                    + "   or  Rt_Perc.Operador is null)\n"
                    + "group by Rt_Perc.dpar, Rt_PercDst.NRed, Rt_PercDst.Hora1, Rotas.Data ;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.select();
            while (consulta.Proximo()) {
                relatorioCxForte = new RelatorioCxForte();
                relatorioCxForte.setQtdeGuias(consulta.getString("QtdeGuias"));
                relatorioCxForte.setQtdeVolumes(consulta.getString("QtdeVolumes"));
                relatorioCxForte.setParada(consulta.getString("Parada"));
                relatorioCxForte.setNRed(consulta.getString("NRed"));
                relatorioCxForte.setHora(consulta.getString("Hora"));
                relatorioCxForte.setData(consulta.getString("Data"));
                //relatorioCxForte.setNomeCompleto(consulta.getString("NomeCompleto"));

                retorno.add(relatorioCxForte);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RelatorioCxForteDao.buscarGuiasDeclaradas - " + e.getMessage() + "\r\n"
                    + "Select \n"
                    + "Count(Distinct Convert(Varchar(max),Convert(BigInt,Rt_Guias.Guia))+Rt_Guias.Serie) QtdeGuias, \n"
                    + "Count(CXfGuiasVol.Qtde) QtdeVolumes, Rt_Perc.dpar Parada, Rt_PercDst.NRed, Rt_PercDst.Hora1 Hora,\n"
                    + " CONVERT(VarChar, Rotas.Data, 112) Data \n"
                    + "From Rotas \n"
                    + "Left Join Rt_guias on  Rt_Guias.Sequencia   = Rotas.Sequencia \n"
                    + "                  and Rotas.CodFil         = " + codFil + "\n"
                    + "Left Join Rt_Perc  on  Rt_perc.Sequencia    = Rt_guias.Sequencia \n"
                    + "					and Rt_Perc.Parada       = Rt_Guias.Parada    \n"
                    + "					and Rt_Perc.CodCli2      = (SELECT TOP 1 CodCli FROM CxForte WHERE CodFil = Rt_Perc.CodFil Order by dtFecha desc)\n"
                    + "					and Rt_Perc.Flag_Excl   <> '*'\n"
                    + "Left Join  Rt_Perc Rt_PercDst  on  Rt_PercDst.Sequencia    = Rt_Perc.Sequencia \n"
                    + "					and  Rt_PercDst.Parada =  Rt_Perc.Dpar\n"
                    + "					and Rt_PercDst.Flag_Excl   <> '*'\n"
                    + "Left Join CxfGuias    on  CxfGuias.SeqRota  = Rotas.Sequencia \n"
                    + "                    and CxfGuias.RotaEnt  = Rotas.Rota      \n"
                    + "                    and CxfGuias.RotaEnt  <> '090'\n"
                    + "                            and CxfGuias.Guia = Rt_Guias.Guia \n"
                    + "Left Join CxfGuiasVol on  CxfGuiasVol.Guia  = Rt_Guias.Guia \n"
                    + "                    and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    + "where Rotas.Sequencia =  " + seqRota + "\n"
                    + "  and (Rt_perc.ER = 'R'\n"
                    + "    or Rt_perc.ER = 'T')\n"
                    + "  and (Substring(Rt_Perc.Operador,1,3) <> 'Trb'\n"
                    + "   or  Rt_Perc.Operador is null)\n"
                    + "group by Rt_Perc.dpar, Rt_PercDst.NRed, Rt_PercDst.Hora1 ;");
        }
    }

    public List<RelatorioCxForte> buscarGuiasRecepcionadas(String codFil, String seqRota, Persistencia persistencia) throws Exception {
        try {
            List<RelatorioCxForte> retorno = new ArrayList<>();
            RelatorioCxForte relatorioCxForte;
            String sql = "Select Count(Distinct Convert\n"
                    + "(Varchar(max),Convert(BigInt,Rt_Guias.Guia))+Rt_Guias.Serie) QtdeGuias, \n"
                    + "Count(CXfGuiasVol.Qtde) QtdeVolumes, Rt_Perc.dpar Parada, Rt_PercDst.HrSaida Hora,\n"
                    + "SasPWCXF.NomeCompleto, CONVERT(VarChar, Rotas.Data, 112) Data \n"
                    + "From Rotas \n"
                    + "Left Join Rt_guias on  Rt_Guias.Sequencia   = Rotas.Sequencia \n"
                    + "                and Rotas.CodFil         = ?\n"
                    + "Left Join Rt_Perc  on  Rt_perc.Sequencia    = Rt_guias.Sequencia \n"
                    + "                and Rt_Perc.Parada       = Rt_Guias.Parada    \n"
                    + "                and Rt_Perc.CodCli2      = (SELECT TOP 1 CodCli FROM CxForte WHERE CodFil = Rt_Perc.CodFil Order by dtFecha desc)\n"
                    + "                and Rt_Perc.Flag_Excl   <> '*'\n"
                    + "Left Join  Rt_Perc Rt_PercDst  on  Rt_PercDst.Sequencia    = Rt_Perc.Sequencia \n"
                    + "                                    and  Rt_PercDst.Parada =  Rt_Perc.Dpar\n"
                    + "                                    and Rt_PercDst.Flag_Excl   <> '*'\n"
                    + "Left Join CxfGuias    on  CxfGuias.Guia     = Rt_Guias.Guia  \n"
                    + "                    and CxfGuias.Serie    = Rt_Guias.Serie \n"
                    + "                    and CxfGuias.RotaEnt  <> '090'\n"
                    + "                    and CxfGuias.DtEnt = Rotas.Data \n"
                    + "Left Join CxfGuiasVol on  CxfGuiasVol.Guia  = CxfGuias.Guia \n"
                    + "                    and CxfGuiasVol.Serie = CxfGuias.Serie \n"
                    + "Left Join (Select Substring(SASPW.Nome,1,6) Nome, Pessoa.Nome NomeCompleto, Pessoa.Codigo\n"
                    + "                     From SASpw \n"
                    + "                     Left join Pessoa  on Pessoa.Codigo = SASpw.CodPessoa\n"
                    + "                     where SASpw.Situacao = 'A') SasPWCXF \n"
                    + "            on (SasPWCXF.Nome = Replace(Cxfguias.OperEnt, 'SPM-','') \n"
                    + "            or ((Replicate('0', 6-len(Convert(Varchar,Convert(BigInt,SasPWCXF.Codigo))))+Convert(Varchar,Convert(BigInt,SasPWCXF.Codigo)))  \n"
                    + "                = Replace(Cxfguias.OperEnt, 'SPM-','')))\n"
                    + "where Rotas.Sequencia =  ?\n"
                    + "and CxfGuias.DtEnt is not Null \n"
                    + "and CxfGuias.RotaEnt = 1\n"
                    + "Group by SasPWCXF.NomeCompleto, Rt_Perc.dpar, Rt_PercDst.HrSaida, Rotas.Data ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.select();
            while (consulta.Proximo()) {
                relatorioCxForte = new RelatorioCxForte();
                relatorioCxForte.setQtdeGuias(consulta.getString("QtdeGuias"));
                relatorioCxForte.setQtdeVolumes(consulta.getString("QtdeVolumes"));
                relatorioCxForte.setParada(consulta.getString("Parada"));
//                relatorioCxForte.setNRed(consulta.getString("NRed"));
                relatorioCxForte.setHora(consulta.getString("Hora"));
                relatorioCxForte.setData(consulta.getString("Data"));
                relatorioCxForte.setNomeCompleto(consulta.getString("NomeCompleto"));

                retorno.add(relatorioCxForte);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RelatorioCxForteDao.buscarGuiasRecepcionadas - " + e.getMessage() + "\r\n"
                    + "Select Count(Distinct Convert\n"
                    + "(Varchar(max),Convert(BigInt,Rt_Guias.Guia))+Rt_Guias.Serie) QtdeGuias, \n"
                    + "Count(CXfGuiasVol.Qtde) QtdeVolumes, Rt_Perc.dpar Parada, Rt_PercDst.HrSaida Hora,\n"
                    + "SasPWCXF.NomeCompleto, CONVERT(VarChar, Rotas.Data, 112) Data \n"
                    + "From Rotas \n"
                    + "Left Join Rt_guias on  Rt_Guias.Sequencia   = Rotas.Sequencia \n"
                    + "                and Rotas.CodFil         = " + codFil + "\n"
                    + "Left Join Rt_Perc  on  Rt_perc.Sequencia    = Rt_guias.Sequencia \n"
                    + "                and Rt_Perc.Parada       = Rt_Guias.Parada    \n"
                    + "                and Rt_Perc.CodCli2      = (SELECT TOP 1 CodCli FROM CxForte WHERE CodFil = Rt_Perc.CodFil Order by dtFecha desc)\n"
                    + "                and Rt_Perc.Flag_Excl   <> '*'\n"
                    + "Left Join  Rt_Perc Rt_PercDst  on  Rt_PercDst.Sequencia    = Rt_Perc.Sequencia \n"
                    + "                                    and  Rt_PercDst.Parada =  Rt_Perc.Dpar\n"
                    + "                                    and Rt_PercDst.Flag_Excl   <> '*'\n"
                    + "Left Join CxfGuias    on  CxfGuias.Guia     = Rt_Guias.Guia  \n"
                    + "                    and CxfGuias.Serie    = Rt_Guias.Serie \n"
                    + "                    and CxfGuias.RotaEnt  <> '090'\n"
                    + "                    and CxfGuias.DtEnt = Rotas.Data \n"
                    + "Left Join CxfGuiasVol on  CxfGuiasVol.Guia  = CxfGuias.Guia \n"
                    + "                    and CxfGuiasVol.Serie = CxfGuias.Serie \n"
                    + " Left Join (Select Substring(SASPW.Nome,1,6) Nome, Pessoa.Nome NomeCompleto, Pessoa.Codigo\n"
                    + "Left Join (Select Substring(SASPW.Nome,1,6) Nome, Pessoa.Nome NomeCompleto, Pessoa.Codigo\n"
                    + "                     From SASpw \n"
                    + "                     Left join Pessoa  on Pessoa.Codigo = SASpw.CodPessoa\n"
                    + "                     where SASpw.Situacao = 'A') SasPWCXF \n"
                    + "            on (SasPWCXF.Nome = Replace(Cxfguias.OperEnt, 'SPM-','') \n"
                    + "            or ((Replicate('0', 6-len(Convert(Varchar,Convert(BigInt,SasPWCXF.Codigo))))+Convert(Varchar,Convert(BigInt,SasPWCXF.Codigo)))  \n"
                    + "                = Replace(Cxfguias.OperEnt, 'SPM-','')))"
                    + "where Rotas.Sequencia =  " + seqRota + "\n"
                    + "and CxfGuias.DtEnt is not Null \n"
                    + "and CxfGuias.RotaEnt = 1\n"
                    + "Group by SasPWCXF.NomeCompleto, Rt_Perc.dpar, Rt_PercDst.HrSaida, Rotas.Data");
        }
    }

}

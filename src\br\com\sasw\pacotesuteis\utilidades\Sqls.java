package br.com.sasw.pacotesuteis.utilidades;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.PreparedStatement;

/**
 *
 * <AUTHOR>
 */
public class Sqls {

    /**
     * Monta insert usando os campos da clase Model (Bean)
     *
     * @param objeto - Model (bean)
     * @return
     * @throws Exception
     */
    public static String montaInsert(Object objeto) throws Exception {
        try {
            String sql = "insert into " + objeto.getClass().getSimpleName() + " ( ";
            String campos = " values ( ";
            Object object;
            Class classDef = Class.forName(objeto.getClass().getCanonicalName());
            object = classDef.newInstance();
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field f : fields) {
                if (!objeto.getClass().getSimpleName().toLowerCase().equals("clientes") ||
                        ((objeto.getClass().getSimpleName().toLowerCase().equals("clientes")
                        && !f.getName().equals("Foto") && !f.getName().equals("Selecionado")))){
                    sql += "," + f.getName();
                    campos += ",?";
                }
            }
            sql = sql.replaceFirst("\\,", "");
            campos = campos.replaceFirst("\\,", "");
            sql += " ) " + campos + " ) ";
            return sql;
        } catch (Exception e) {
            throw new Exception("Falha ao montar insert\r\n" + e.getMessage());
        }
    }

    /**
     * Montar o sql para o UPDATE
     *
     * @param objeto objeto da class
     * @param where limitadores do sql
     * @return sql montado
     * @throws Exception
     */
    public static String montarUpdate(Object objeto, String where) throws Exception {
        try {
            String sql = " UPDATE " + objeto.getClass().getSimpleName() + " SET ";
            Object object;
            Class classDef = Class.forName(objeto.getClass().getCanonicalName());
            object = classDef.newInstance();
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field f : fields) {
                if (!objeto.getClass().getSimpleName().toLowerCase().equals("clientes") ||
                        ((objeto.getClass().getSimpleName().toLowerCase().equals("clientes")
                        && !f.getName().equals("Foto") && !f.getName().equals("Selecionado")))){
                    sql += f.getName() + " = ?,";
                }
            }
            if (sql.contains(",")) {
                sql = sql.substring(0, sql.length() - 1);
            }
            sql += " WHERE " + where;
            return sql;
        } catch (Exception e) {
            throw new Exception("Falha ao montar update\r\n" + e.getMessage());
        }
    }

    /**
     * Carrega os dados no PreparedStatement Exemplo de uso String sql =
     * Utilidades.Sqls.montaInsert(funcion); PreparedStatement ps =
     * persistencia.getState(sql); ps = Utilidades.Sqls.carregaPS(ps, funcion);
     * ps.execute();
     *
     * @param mstat - PreparedStatement a ser carregado
     * @param objeto - objeto instanciado como os dados a serem passados ao sql
     * @return - Objeto PreparedStatement já carregado de dados
     * @throws Exception
     */
    public static PreparedStatement carregaPS(PreparedStatement mstat, Object objeto) throws Exception {
        try {
            Object object;
            Class classDef = Class.forName(objeto.getClass().getCanonicalName());
            object = classDef.newInstance();
            Field[] fields = object.getClass().getDeclaredFields();
            Method[] methods = object.getClass().getMethods();
            PreparedStatement stat = mstat;
            for (int i = 0; i < fields.length; i++) {
                for (Method m : methods) {
                    if (m.getName().equals("get" + fields[i].getName())) {
                        try {
                            stat.setObject(i + 1, m.invoke(objeto, (Object) null));
                        } catch (Exception e) {
                            System.out.println(fields[i].getName());
                            stat.setObject(i + 1, null);
                        } finally {
                            break;
                        }
                    }
                }
            }
            return stat;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar intens no parametros do PreparedStatement\r\n" + e.getMessage());
        }
    }
}

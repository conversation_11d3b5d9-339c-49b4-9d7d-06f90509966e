/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.configuracoes;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.HandlerXlsx;
import br.com.sasw.utils.Messages;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Named(value = "importarMB")
@ViewScoped
public class ImportarMB implements Serializable {

    private UploadedFile uploadedFile;
    private StreamedContent downloadFile;
    private InputStream inputStream;
    private HandlerXlsx handler;
    private Persistencia persistencia, central;
    private int parametro;
    private String caminho, nome, banco, operador, caminholog, log, saidaArq, titulo, imagem;
    private ArquivoLog logerro;
    private BigDecimal codPessoa;

    public ImportarMB() {
        operador = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("nome");
        banco = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("banco");
        handler = new HandlerXlsx(operador);
        log = new String();
        codPessoa = (BigDecimal) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
    }

    public void Persistencia(Persistencia pp, Persistencia local) {
        try {
            this.persistencia = pp;
            if (this.persistencia == null) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.central = local;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }
            handler.Persistencia(pp, local);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminholog);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void HandleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                this.uploadedFile = fileUploadEvent.getFile();
                this.inputStream = new BufferedInputStream(this.uploadedFile.getInputstream());
                this.handler.DefineSaida(this.inputStream, this.parametro);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminholog);
        }
    }

    public void LeParametro() {
        try {
            PrimeFaces.current().ajax().update("uploadForm");
            switch (this.parametro) {
                case 1:
                    ArqPessoa();
                    this.titulo = Messages.getMessageS("Pessoas");
                    this.imagem = "assets/img/icone_satmob_pessoas.png";
                    break;
                case 2:
                    ArqFuncionarios();
                    this.titulo = Messages.getMessageS("Funcionarios");
                    this.imagem = "assets/img/icone_satmob_funcionarios.png";
                    break;
                case 3:
                    ArqClientes();
                    this.titulo = Messages.getMessageS("Clientes");
                    this.imagem = "assets/img/icone_satmob_clientes.png";
                    break;
                case 4:
                    ArqFiliais();
                    this.titulo = Messages.getMessageS("Filiais");
                    this.imagem = "assets/img/icone_satmob_filiais.png";
                    break;
                case 5:
                    ArqFiliais();
                    this.titulo = Messages.getMessageS("Filiais");
                    this.imagem = "assets/img/icone_satmob_filiais.png";
                    break;
                case 0:
                default:
                    throw new Exception("");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminholog);
        }
    }

    private void ArqPessoa() {
        try {
            this.caminho = "/WEB-INF/Arquivos/Pessoas.xlsx";
            this.nome = "Pessoas.xlsx";
            this.inputStream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(this.caminho);
            this.downloadFile = new DefaultStreamedContent(this.inputStream, "application/xls", this.nome);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminholog);
        }
    }

    private void ArqFuncionarios() {
        try {
            this.caminho = "/WEB-INF/Arquivos/Funcionarios.xlsx";
            this.nome = "Funcionarios.xlsx";
            this.inputStream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(this.caminho);
            this.downloadFile = new DefaultStreamedContent(this.inputStream, "application/xls", this.nome);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminholog);
        }
    }

    private void ArqClientes() {
        try {
            this.caminho = "/WEB-INF/Arquivos/Clientes.xlsx";
            this.nome = "Clientes.xlsx";
            this.inputStream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(this.caminho);
            this.downloadFile = new DefaultStreamedContent(this.inputStream, "application/xls", this.nome);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminholog);
        }
    }

    private void ArqFiliais() {
        try {
            this.caminho = "/WEB-INF/Arquivos/Filiais.xlsx";
            this.nome = "Filiais.xlsx";
            this.inputStream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(this.caminho);
            this.downloadFile = new DefaultStreamedContent(this.inputStream, "application/xls", this.nome);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminholog);
        }
    }

    public UploadedFile getUploadedFile() {
        return uploadedFile;
    }

    public void setUploadedFile(UploadedFile uploadedFile) {
        this.uploadedFile = uploadedFile;
    }

    public StreamedContent getDownloadFile() {
        return downloadFile;
    }

    public void setDownloadFile(StreamedContent downloadFile) {
        this.downloadFile = downloadFile;
    }

    public int getParametro() {
        return parametro;
    }

    public void setParametro(int parametro) {
        this.parametro = parametro;
    }

    public String getSaidaArq() {
        return saidaArq;
    }

    public void setSaidaArq(String saidaArq) {
        this.saidaArq = saidaArq;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public HandlerXlsx getHandler() {
        return handler;
    }

    public void setHandler(HandlerXlsx handler) {
        this.handler = handler;
    }

    public String getImagem() {
        return imagem;
    }

    public void setImagem(String imagem) {
        this.imagem = imagem;
    }
}

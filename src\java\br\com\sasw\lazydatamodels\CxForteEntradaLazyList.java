package br.com.sasw.lazydatamodels;

import Controller.CxForte.CxForteSatMobWeb;
import Dados.Persistencia;
import SasBeansCompostas.CxFEntradasDTO;
import br.com.sasw.utils.Messages;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class CxForteEntradaLazyList extends LazyDataModel<CxFEntradasDTO> {

    private List<CxFEntradasDTO> lista;
    private final CxForteSatMobWeb service;

    public CxForteEntradaLazyList(Persistencia persistencia) {
        service = new CxForteSatMobWeb(persistencia);
    }

    private boolean ready(Map filters) {
        String rota = (String) filters.get("rota");
        String sequencia = (String) filters.get("sequencia");
        String codFil = (String) filters.get("codFil");
        String data = (String) filters.get("data");

        return !rota.equals("") && !sequencia.equals("") && !codFil.equals("") && !data.equals("");
    }

    @Override
    public List<CxFEntradasDTO> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            if (ready(filters)) {
                lista = service.listaTodasEntradasCxForte(filters);
                setRowCount(service.contagemEntradasCxForte(filters));
            } else {
                lista = new ArrayList<>();
                setRowCount(0);
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return lista;
    }

    @Override
    public Object getRowKey(CxFEntradasDTO row) {
        return row.getSequencia() + ";" + row.getParada() + ";" + row.getCodCli1();
    }

    @Override
    public CxFEntradasDTO getRowData(String key) {
        for (CxFEntradasDTO row : lista) {
            if (key.equals(getRowKey(row))) {
                return row;
            }
        }
        return null;
    }
}

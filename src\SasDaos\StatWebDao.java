/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.StatWeb;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class StatWebDao {

    /**
     * Lista todos os dados de uma data
     *
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<StatWeb> dadosDia(String data, Persistencia persistencia) throws Exception {
        try {
            List<StatWeb> retorno = new ArrayList<>();
            String sql = " SELECT * from StatWeb where Data = ? ";
//                    + " AND (Consulta > 0 OR Inclusao > 0 OR Alteracao > 0 OR Exclusao  > 0)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.select();
            StatWeb statWeb;
            while (consulta.Proximo()) {
                statWeb = new StatWeb();
                statWeb.setData(consulta.getString("Data"));
                statWeb.setHora(consulta.getString("Hora"));
                statWeb.setConsulta(consulta.getString("Consulta"));
                statWeb.setInclusao(consulta.getString("Inclusao"));
                statWeb.setAlteracao(consulta.getString("Alteracao"));
                statWeb.setExclusao(consulta.getString("Exclusao"));
                statWeb.setEmpresa(persistencia.getEmpresa());
                retorno.add(statWeb);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("StatWebDao.dadosDia - " + e.getMessage() + "\r\n"
                    + " SELECT * from StatWeb where Data = " + data);
        }
    }

    /**
     * Lista todos os dados de uma data
     *
     * @param data
     * @param hora
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<StatWeb> dadosDiaHora(String data, String hora, Persistencia persistencia) throws Exception {
        try {
            List<StatWeb> retorno = new ArrayList<>();
            String sql = " SELECT * from StatWeb where Data = ? and Hora = ? ";
//                    + " AND (Consulta > 0 OR Inclusao > 0 OR Alteracao > 0 OR Exclusao  > 0)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(hora);
            consulta.select();
            StatWeb statWeb;
            while (consulta.Proximo()) {
                statWeb = new StatWeb();
                statWeb.setData(consulta.getString("Data"));
                statWeb.setHora(consulta.getString("Hora"));
                statWeb.setConsulta(consulta.getString("Consulta"));
                statWeb.setInclusao(consulta.getString("Inclusao"));
                statWeb.setAlteracao(consulta.getString("Alteracao"));
                statWeb.setExclusao(consulta.getString("Exclusao"));
                statWeb.setEmpresa(persistencia.getEmpresa());
                retorno.add(statWeb);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("StatWebDao.dadosDia - " + e.getMessage() + "\r\n"
                    + " SELECT * from StatWeb where Data = " + data);
        }
    }

    /**
     * Lista os dados por dia no período
     *
     * @param data1
     * @param data2
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<StatWeb> dadosDiarioPeriodo(String data1, String data2, Persistencia persistencia) throws Exception {
        try {
            List<StatWeb> retorno = new ArrayList<>();
            String sql = " SELECT * from StatWeb where Data between ? and ? "
                    + " ORDER BY data asc, hora asc";
//                    + " AND (Consulta > 0 OR Inclusao > 0 OR Alteracao > 0 OR Exclusao  > 0)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data1);
            consulta.setString(data2);
            consulta.select();
            StatWeb statWeb;
            while (consulta.Proximo()) {
                statWeb = new StatWeb();
                statWeb.setData(consulta.getString("Data"));
                statWeb.setHora(consulta.getString("Hora"));
                statWeb.setConsulta(consulta.getString("Consulta"));
                statWeb.setInclusao(consulta.getString("Inclusao"));
                statWeb.setAlteracao(consulta.getString("Alteracao"));
                statWeb.setExclusao(consulta.getString("Exclusao"));
                statWeb.setEmpresa(persistencia.getEmpresa());
                retorno.add(statWeb);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("StatWebDao.dadosDia - " + e.getMessage() + "\r\n"
                    + " SELECT * from StatWeb where between " + data1 + " and " + data2
                    + " ORDER BY data asc, hora asc");
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.cofre;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import SasBeansCompostas.UsuarioSatMobWeb;
import br.com.sasw.lazydatamodels.PessoaLoginLazyList;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "acessoscofre")
@ViewScoped
public class AcessosCofreMB implements Serializable {

    private LazyDataModel<UsuarioSatMobWeb> usuarios = null;
    private Map filters;
    private Persistencia persistencia, central;
    private final String codfil, banco, codcli, operador, caminho;
    private final BigDecimal codpessoa;
    private final ArquivoLog logerro;
    private String log;

    public AcessosCofreMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codcli = (String) fc.getExternalContext().getSessionMap().get("cliente");
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");

        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog(this.getClass().getSimpleName());
    }

    public void Persistencias(Persistencia pp, Persistencia central) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + this.banco);
            }
            this.central = central;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.filters = new HashMap();
            this.filters.put(" s.situacao = ? ", "A");
            this.filters.put(" s.codfil = ? ", this.codfil);
            this.filters.put(" p.nome like ? ", "");
            this.filters.put(" s.nivelx like ? ", "");
            this.filters.put(" p.codigo in (Select PessoaCliAut.Codigo"
                    + "                     from PessoaCliAut "
                    + "                     Left Join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli"
                    + "                                        and Clientes.CodFil = PessoaCliAut.CodFil"
                    + "                     where pessoacliaut.flag_excl <> '*'"
                    + "                     and Clientes.Nred like ?) ", "");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<UsuarioSatMobWeb> getAllAcessos() {
        if (this.usuarios == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            this.usuarios = new PessoaLoginLazyList(this.persistencia, this.central);
            try {

            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
        return this.usuarios;
    }
}

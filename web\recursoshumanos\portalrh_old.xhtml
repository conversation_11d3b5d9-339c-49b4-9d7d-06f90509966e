<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/portalrh.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">
            <p:growl id="msgs"/>
            <div id="header"></div>
            <h:form id="main">
                <div class="bottomrow" id="principal" style="background: url('../assets/img/st_back#{login.origem}.png') no-repeat top center; background-size: 50%">
                    <div class="faixadireita">
                        <div class="icones">
                            <p:commandLink actionListener="#{pontos.setPersistencia(login.pp)}"
                                           action="#{pontos.buscaPeriodos}"
                                           update="folhadeponto msgs">
                                <p:graphicImage url="../assets/img/icone_satmob_fopag_G.png" width="100px" />
                            </p:commandLink>
                            <p:commandLink actionListener="#{pontos.setPersistencia(login.pp)}"
                                           action="#{pontos.buscaPeriodos}"
                                           update="folhadeponto msgs">
                                <h:outputText style="color: #145B9B" value="#{localemsgs.FolhaPonto}"/>
                            </p:commandLink>
                        </div>
                        
                        <div class="icones">
                            <p:commandLink actionListener="#{contracheque.setPersistencia(login.pp,login.pool)}"
                                           action="#{contracheque.buscaPeriodos}"
                                           update="contracheques msgs">
                                <p:graphicImage url="../assets/img/icone_satmob_contracheque_G.png" width="100px" />
                            </p:commandLink>
                            <p:commandLink actionListener="#{contracheque.setPersistencia(login.pp,login.pool)}"
                                           action="#{contracheque.buscaPeriodos}"
                                           update="contracheques msgs">
                                <h:outputText style="color: #145B9B" value="#{localemsgs.Contracheque}"/>
                            </p:commandLink>
                        </div>
                        
                        <div class="icones">
                            <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                           action="#{funcoesadm.logsFuncion()}"
                                           update="adm msgs">
                                <p:graphicImage url="../assets/img/icone_satmob_contracheque_funcaoadm_G.png" width="100px" />
                            </p:commandLink>
                            <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                           action="#{funcoesadm.logsFuncion()}"
                                           update="adm msgs">
                                <h:outputText style="color: #145B9B" value="#{localemsgs.FuncoesAdm}"/>
                            </p:commandLink>
                        </div>
                        
                        <div class="icones">
                            <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                           oncomplete="PF('dlgTrocarSenha').show()" update="msgs trocarsenha">
                                <p:graphicImage url="../assets/img/icone_satmob_trocarsenhaG.png" width="100px" />
                            </p:commandLink>
                            <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                           oncomplete="PF('dlgTrocarSenha').show()" update="msgs trocarsenha">
                                <h:outputText style="color: #145B9B" value="#{localemsgs.TrocarSenha}"/>
                            </p:commandLink>
                        </div>
                        
                        <div class="icones">
                            <p:commandLink actionListener="#{rendimentos.setPersistencia(login.pp)}"
                                           action="#{rendimentos.imprimirRendimentos}" update="msgs"
                                           ajax="false" rendered="#{login.ano ne null}">
                                <p:graphicImage url="../assets/img/icone_satmob_rendimentos_G.png" width="100px" height="100px"/>
                            </p:commandLink>
                            <p:commandLink actionListener="#{rendimentos.setPersistencia(login.pp)}"
                                           action="#{rendimentos.imprimirRendimentos}" update="msgs"
                                           ajax="false" rendered="#{login.ano ne null}">
                                <p:outputLabel style="color: #145B9B" value="#{localemsgs.InformeRendimentos} #{login.ano}"/>
                            </p:commandLink> 
                        </div>
                        
                        <div class="icones">
                            <p:commandLink action="#{login.logOutRH}" update="msgs">
                                <p:graphicImage url="../assets/img/icone_sair.png" height="80" width="80"/>
                            </p:commandLink> 
                            <p:commandLink action="#{login.logOutRH}" update="msgs">
                                <p:outputLabel style="color: #145B9B" value="#{localemsgs.Sair}"/>
                            </p:commandLink> 
                        </div>
                    </div>
                    <div class="faixaesquerdabalao">
                        <div class="titulobalao">
                            <h:outputText value="#{login.avisoportal.assunto}" escape="false"/>
                        </div>
                        &nbsp;
                        <h4><h:outputText value="#{login.avisoportal.mensagem}" escape="false"/></h4>
                    </div>
                    <div class="faixameio">
                        <h2>
                            <h:outputText value="#{login.funcion.pessoa.sexo eq 'F' ? localemsgs.BemVinda : localemsgs.BemVindo}"/>
                        </h2>
                        <div class="logo">
                            <h:outputText value="#{login.funcion.pessoa.nome}"/>
                        </div>

                    </div>
                </div>
            </h:form>
            
            <h:form id="trocarsenha"> 
                <p:dialog widgetVar="dlgTrocarSenha" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_trocarsenhaG.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panelGrid columns="2" styleClass="pnl">
                        <p:outputLabel for="atual" value="#{localemsgs.SenhaAtual}:"/>
                        <p:password id="atual" autocomplete="off" value="#{funcoesadm.senhaAtual}"/>

                        <p:outputLabel for="nova" value="#{localemsgs.NovaSenha}:"/>
                        <p:password id="nova" match="confirmar" autocomplete="off" required="true" 
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                    validatorMessage="Senhas não conferem" value="#{funcoesadm.senhaNova}"/>

                        <p:outputLabel for="confirmar" value="#{localemsgs.Confirmacao}:"/>
                        <p:password id="confirmar" autocomplete="off" value="#{funcoesadm.senhaNova}"/>
                    </p:panelGrid>
                    &nbsp;
                    <div class="pnl">
                        <p:commandButton value="#{localemsgs.TrocarSenha}" style="width: 130px !important;" 
                                         action="#{login.setPwweb(funcoesadm.trocarSenha(login.pwweb))}"
                                         update="msgs" class="custom-button"/>
                    </div>
                </p:dialog>
            </h:form>
            
            <h:form id="datas">
                <p:dialog widgetVar="dlgDatas" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_contracheque_funcaoadm_P.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.FuncoesAdm}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panelGrid columns="2" styleClass="pnl">
                        <p:outputLabel for="matr" value="#{localemsgs.Matr}:"/>
                        <p:inputText id="matr" value="#{funcoesadm.matr}"/>

                        <p:outputLabel for="dini" value="Data inicial:"/>
                        <p:inputMask id="dini" value="#{funcoesadm.dataIni}" mask="99/99/9999"/>

                        <p:outputLabel for="dfim" value="Data final:"/>
                        <p:inputMask id="dfim" value="#{funcoesadm.dataFim}" mask="99/99/9999"/>

                        <p:commandButton value="#{localemsgs.Contracheque}" style="width: 130px !important;" 
                                         action="#{funcoesadm.listaDatasCC}"
                                         update="msgs info" class="custom-button"/>

                        <p:commandButton value="#{localemsgs.FolhaDePonto}" style="width: 130px !important;"
                                         action="#{funcoesadm.listaDatasFP}"
                                         update="msgs info" class="custom-button"/>
                    </p:panelGrid>
                    <p:scrollPanel id="info" mode="native" style="width:100%;height:200px">
                        <table style="text-align: center; width: 100%">
                            <c:forEach var="dados" items="#{funcoesadm.dados}">
                                <tr>
                                    <td>
                                        <h:outputText value="#{dados}" styleClass="textoazul"/>
                                    </td>
                                </tr>
                            </c:forEach>
                        </table>
                    </p:scrollPanel>
                </p:dialog>
            </h:form>
            
            <h:form id="adm">
                <p:dialog widgetVar="dlgFuncoesAdm" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_contracheque_funcaoadm_P.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.FuncoesAdm}" style="color:#022a48" /> 
                    </f:facet>
                    <p:accordionPanel styleClass="accordion" activeIndex="null" id="accordionAdm">
                        <p:tab title="#{localemsgs.Matricula}" id="matricula">
                            <p:accordionPanel styleClass="nestedAccordion">
                                <p:tab title="#{localemsgs.FolhaDePonto}">
                                    <table style="text-align: center; width: 100%">
                                        <c:forEach var="fp" items="#{funcoesadm.folhasp}">
                                            <tr>
                                                <td>
                                                    <h:outputText value="#{fp}" styleClass="textoazul"/>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </table>
                                </p:tab>
                                <p:tab title="#{localemsgs.Contracheque}">
                                    <table style="text-align: center; width: 100%">
                                        <c:forEach var="cc" items="#{funcoesadm.cCheque}">
                                            <tr>
                                                <td>
                                                    <h:outputText value="#{cc}" styleClass="textoazul"/>
                                                </td> 
                                            </tr>
                                        </c:forEach>
                                    </table>
                                </p:tab>
                            </p:accordionPanel>
                        </p:tab>
                        <p:tab title="#{localemsgs.Mensagens}" id="mensagens">
                            <div style="text-align: center">
                                <h:outputText value="Assunto" styleClass="textoazul"/>
                                <p:inputText value="#{funcoesadm.assunto}" style="width: 97%"/>

                                <h:outputText value="Aviso" styleClass="textoazul"/>
                                <p:inputTextarea value="#{funcoesadm.aviso}" styleClass="textoazul" style="width: 97%"/>
                                
                                <p:commandButton value="#{localemsgs.Enviar}" style="width: 130px !important;" 
                                                 update="main msgs" class="custom-button"
                                                 actionListener="#{funcoesadm.enviarMensagem}"
                                                 action="#{login.atualizaMensagem}"/>
                            </div>
                        </p:tab>
                        <p:tab title="#{localemsgs.ListaDatas}" id="listadatas">
                            <p:panelGrid columns="2" styleClass="pnl">
                                <p:outputLabel for="matr" value="#{localemsgs.Matricula}:"/>
                                <p:inputText id="matr" value="#{funcoesadm.matr}"/>

                                <p:outputLabel for="dini" value="Data inicial:"/>
                                <p:inputMask id="dini" value="#{funcoesadm.dataIni}" mask="99/99/9999"/>

                                <p:outputLabel for="dfim" value="Data final:"/>
                                <p:inputMask id="dfim" value="#{funcoesadm.dataFim}" mask="99/99/9999"/>
                                
                                <p:commandButton value="#{localemsgs.Contracheque}" style="width: 130px !important;" 
                                                 action="#{funcoesadm.listaDatasCC}" oncomplete="PF('dlgDatas').show()"
                                                 update="msgs datas:info" class="custom-button"/>

                                <p:commandButton value="#{localemsgs.FolhaDePonto}" style="width: 130px !important;"
                                                 action="#{funcoesadm.listaDatasFP}" oncomplete="PF('dlgDatas').show()"
                                                 update="msgs datas:info" class="custom-button"/>
                            </p:panelGrid>
                        </p:tab>
                    </p:accordionPanel>
                </p:dialog>
            </h:form>
            
            <h:form id="logfuncion">
                <p:dialog widgetVar="dlgFuncoesFuncion" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_contracheque_funcaoadm_P.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.FuncoesAdm}" style="color:#022a48" /> 
                    </f:facet>
                    <p:accordionPanel styleClass="accordion">
                        <p:tab title="#{localemsgs.FolhaDePonto}">
                            <table style="text-align: center; width: 100%">
                                <c:forEach var="fp" items="#{funcoesadm.folhasp}">
                                    <tr>
                                        <td>
                                            <h:outputText value="#{fp}" styleClass="textoazul"/>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:tab>
                        <p:tab title="#{localemsgs.Contracheque}">
                            <table style="text-align: center; width: 100%">
                                <c:forEach var="cc" items="#{funcoesadm.cCheque}">
                                    <tr>
                                        <td>
                                            <h:outputText value="#{cc}" styleClass="textoazul"/>
                                        </td> 
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:tab>
                    </p:accordionPanel>
                </p:dialog>
            </h:form>
            
            <h:form id="folhadeponto">
                <p:dialog widgetVar="dlgFolhadePonto" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_fopag_P.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.FolhaDePonto}" style="color:#022a48" /> 
                    </f:facet>
                    <table style="text-align: center; width: 100%">
                        <h:outputText class="textoazul" value="#{localemsgs.SemRegistros}" rendered="#{pontos.periodos.isEmpty()}"/>
                        <c:forEach var="periodos" items="#{pontos.periodos}">
                            <tr>
                                <td>
                                    <p:commandLink value="#{periodos.dt_Ini} a #{periodos.dt_Fim} - #{periodos.codFil.toBigInteger()}: #{periodos.matr.toBigInteger()}" target="_blank"
                                                   action="#{pontos.imprimirFolhaDePonto(periodos)}"
                                                   update="msgs" styleClass="linkazul"
                                                   ajax="false"/>
                                </td>
                            </tr>
                        </c:forEach>
                    </table>
                </p:dialog>
            </h:form>
            
            <h:form id="contracheques">
                <p:dialog widgetVar="dlgContracheque" draggable="false" focus="contracheques"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" height="400" showEffect="drop" hideEffect="drop">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_contracheque_G.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Contracheque}" style="color:#022a48" /> 
                    </f:facet>
                    

                    <p:panel class="col-md-12 col-sm-12 col-xs-12" id="panelPeriodo">
                        <div class="col-md-3 col-sm-3 col-xs-3">
                            <p:outputLabel for="periodo" value="#{localemsgs.Periodo}:" />
                        </div>
                        <div class="col-md-9 col-sm-9 col-xs-9">
                            <p:datePicker id="periodo" view="month" value="#{contracheque.periodo}" pattern="MM/yyyy" inputStyle="width: 80px;"
                                          yearNavigator="true" yearRange="2000:2030" showIcon="true" readonlyInput="true">
                                <p:ajax event="dateSelect" listener="#{contracheque.buscaPeriodo}" update="msgs"/>
                            </p:datePicker>
                        </div>                        
                    </p:panel>
 
                    <p:accordionPanel id="periodos" value="#{contracheque.fpperiodos}"  var="fpperiodos" dynamic="true" styleClass="accordion">
                        <p:ajax event="tabChange" listener="#{contracheque.buscarFpmensais}" update="msgs" />
                        <p:tab title="#{fpperiodos.dtInicioF}" id="periodocontracheque">
                            <table style="text-align: center; width: 100%">
                                <tr>
                                    <td>
                                        <h:outputText class="textoazul" value="#{localemsgs.SemRegistros}"
                                                      rendered="#{contracheque.fpmensais.isEmpty()}"/>
                                    </td>
                                </tr>
                                <c:forEach var="fp" items="#{contracheque.fpmensais}">
                                    <tr>
                                        <td>
                                            <p:commandLink value="#{fp.tipoFpFormatado} - #{fp.codFil.toBigInteger()}: #{fp.matr.toBigInteger()}" target="_blank"
                                                           action="#{contracheque.imprimirContracheque(fp)}" 
                                                           update="msgs" styleClass="linkazul"
                                                           ajax="false"/>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:tab>
                    </p:accordionPanel>
                </p:dialog>
            </h:form>
            
            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>


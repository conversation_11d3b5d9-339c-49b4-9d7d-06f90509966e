package br.com.sasw.managedBeans;

import Arquivo.ArquivoLog;
import Controller.Graficos.GraficoSatMobWeb;
import Controller.Login.LoginSatMobWeb;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Filiais;
import SasBeans.GraficoBean;
import SasBeans.PessoaLogin;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.chart.Axis;
import org.primefaces.model.chart.AxisType;
import org.primefaces.model.chart.CategoryAxis;
import org.primefaces.model.chart.ChartSeries;
import org.primefaces.model.chart.LegendPlacement;
import org.primefaces.model.chart.LineChartModel;
import org.primefaces.model.chart.PieChartModel;

@ManagedBean
@Named(value = "graficomb")
@ViewScoped
public class GraficosMB implements Serializable {

    private LoginSatMobWeb login;
    private SasPoolPersistencia pool;
    private PessoaLogin empresa;
    private Persistencia persistencia, pp;
    private List<Filiais> lista, filialselecionada;
    private Filiais filiais;
    private List<GraficoBean> listagra;
    private Map filters;
    private Date dataSelecionada1, dataSelecionada2;
    private String log, caminho, banco, Dt_Ini, Dt_Fim;
    private LocalDate dataInicio, dataFim;
    private BigDecimal codPessoa;
    private String codFil;
    private ArquivoLog logerro;
    private LineChartModel lineModelHEs, lineModelEvoFaltas, lineModelEvoSuspensoes, lineModelEvoAtMedicos;
    private PieChartModel pieModelHE50, pieModelHE100, pieModelUltimoMes, pieModelSuspUltimo, pieModelAtUltimoMes;
    private List<Filiais> listaFil;
    private GraficoSatMobWeb graficoSatMobWeb;
    private List<String> selecao;
    private List<BigDecimal> selecaoCod;
    private String orientation = "vertical";
    private List<String> selectedOptions;
    private boolean click, limparFiltros, mostrarFiliais;
    private String menuEscolha;
    private int total;
    private LazyDataModel<GraficoBean> graficos = null;

    public GraficosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, -150);
        dataSelecionada1 = c.getTime();
        dataInicio = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Dt_Ini = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        Calendar c2 = Calendar.getInstance();
        c2.setTime(Date.from(Instant.now()));
        c2.set(Calendar.DAY_OF_MONTH, c2.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataSelecionada2 = c2.getTime();
        dataFim = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Dt_Fim = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        graficoSatMobWeb = new GraficoSatMobWeb();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        log = new String();
        logerro = new ArquivoLog();
        filiais = new Filiais();
        listagra = new ArrayList<>();
        selecao = new ArrayList<>();
        menuEscolha = new String();

    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;

            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.filters = new HashMap();

            this.filters.put("RH_Ctrl.dt_ini between ? and ?", Arrays.asList(this.Dt_Ini, this.Dt_Fim));
            this.filters.put("RH_Ctrl.codFil = ?", Arrays.asList(this.codFil.toString()));

            listar();

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }

        }
    }

    public void escreverData1() {
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            this.dataSelecionada1 = df.parse(this.Dt_Ini);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void escreverData2() {
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            this.dataSelecionada2 = df.parse(this.Dt_Fim);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void selectionarData1(SelectEvent data) {
        this.dataInicio = null;
        this.dataSelecionada1 = (Date) data.getObject();
        this.dataInicio = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        this.Dt_Ini = this.dataInicio.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//        SimpleDateFormat dtIni = new SimpleDateFormat("yyyyMMdd");
//        this.Dt_Ini = (dtIni.format(dataInicio));

    }

    public void selectionarData2(SelectEvent data) {
        this.dataFim = null;
        this.dataSelecionada2 = (Date) data.getObject();
        this.dataFim = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        this.Dt_Fim = this.dataFim.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public void selecionarData() {
        try {
            this.dataInicio = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            this.dataFim = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            this.Dt_Ini = this.dataInicio.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.Dt_Fim = this.dataFim.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String manterMenu = this.menuEscolha;

            if (this.dataSelecionada1.after(this.dataSelecionada2)) {
                throw new Exception("IntervaloInvalido");
            }
            String valor = "?";
            for (int i = 1; i < selecao.size(); i++) {
                valor += ",?";
            }
            if (this.mostrarFiliais) {
                this.filters.put("RH_Ctrl.dt_ini between ? and ?", Arrays.asList(this.Dt_Ini, this.Dt_Fim));
                this.filters.put("RH_Ctrl.codFil in (" + valor + ") ", this.selecao);
            } else {
                this.filters.put(" RH_Ctrl.codFil = ? ", Arrays.asList(this.codFil.toString()));
                this.filters.put("RH_Ctrl.dt_ini between ? and ?", Arrays.asList(this.Dt_Ini, this.Dt_Fim));
            }

            listar();
            PrimeFaces.current().ajax().update("cabecalho");
            this.menuEscolha = new String();
            this.menuEscolha = manterMenu;
        } catch (Exception e) {
            PrimeFaces.current().ajax().update("msgs");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

// Listagem onde é chamado os Graficos!!!
    public void listar() {
        try {
            mostraHoraUltimo();
            mostraFaltas();
            mostraSuspensoes();
            mostraAtestado();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    // Horas 50% Ultimo Mês
    // Grafico pizza
    public void mostraHoraUltimo() {
        this.menuEscolha = new String();
        this.menuEscolha = "HoraExtra";

        try {

            this.listagra = graficoSatMobWeb.listarGrafico(this.filters, this.persistencia);
            this.pieModelHE50 = new PieChartModel();
            if (!listagra.isEmpty()) {
                for (GraficoBean gra : listagra) {

                    this.pieModelHE50.set(gra.getDescricao(), gra.getHE50().doubleValue());
                }

                this.pieModelHE50.setTitle(Messages.getMessageS("Horas50"));
                this.pieModelHE50.setLegendPosition("w");
                this.pieModelHE50.setShowDataLabels(true);
                this.pieModelHE50.setDiameter(140);
                this.pieModelHE50.setDataFormat("percent");
            } else {
                this.pieModelHE50 = null;
            }
        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

//horas 100% ultimo mes
// Grafico Pizza
//    public void getGraficoPizza2() {
        try {

            this.listagra = graficoSatMobWeb.listarGraficoHE100(this.filters, this.persistencia);
            this.pieModelHE100 = new PieChartModel();
            if (!listagra.isEmpty()) {
                for (GraficoBean gra : listagra) {
                    this.pieModelHE100.set(gra.getDescricao(), gra.getHE100().doubleValue());
                }
                this.pieModelHE100.setTitle(Messages.getMessageS("Horas100"));
                this.pieModelHE100.setLegendPosition("w");
                this.pieModelHE100.setShowDataLabels(true);
                this.pieModelHE100.setDiameter(140);
                this.pieModelHE100.setDataFormat("percent");
            } else {
                this.pieModelHE100 = null;
            }
        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

        //Lista Horas Extras
        //Grafico de Linhas
        try {

            this.listagra = graficoSatMobWeb.listarGraficoHEs(this.filters, this.persistencia);
            this.lineModelHEs = new LineChartModel();
            String controle;
            float menor, maior;

            if (!listagra.isEmpty()) {
                ChartSeries compet = new ChartSeries();
                controle = listagra.get(0).getDescricao();
                maior = 0;
                menor = Float.MAX_VALUE;
                for (GraficoBean gra : listagra) {
                    //valores para o Compet
                    if (controle.equals(gra.getDescricao())) {
                        compet.setLabel(gra.getDescricao());
                        compet.set(gra.getDt_Fim(), gra.getHE100().floatValue());

                    } else {
                        this.lineModelHEs.addSeries(compet);
                        compet = new ChartSeries();
                        compet.setLabel(gra.getDescricao());
                        compet.set(gra.getDt_Fim(), gra.getHE100().floatValue());
                        controle = gra.getDescricao();
                    }
                    if (menor > gra.getHE100().floatValue()) {
                        menor = gra.getHE100().floatValue();
                    }
                    if (maior < gra.getHE100().floatValue()) {
                        maior = gra.getHE100().floatValue();
                    }

                }
                this.lineModelHEs.addSeries(compet);
                this.lineModelHEs.setTitle(Messages.getMessageS("EvoHoras"));
                this.lineModelHEs.setExtender("chartExtender");
                this.lineModelHEs.setLegendPosition("ne");
                this.lineModelHEs.setLegendPlacement(LegendPlacement.OUTSIDE);
                this.lineModelHEs.setShowPointLabels(true);
                lineModelHEs.getAxes().put(AxisType.X, new CategoryAxis(Messages.getMessageS("Compet")));
                this.lineModelHEs.setAnimate(true);

                Axis yAxis = lineModelHEs.getAxis(AxisType.Y);
                yAxis.setMin(0);
                yAxis.setMax(maior + 30);
                yAxis.setLabel(Messages.getMessageS("HorasExtras"));
            } else {
                this.lineModelHEs = null;
            }
        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    //Faltas ultimo Mês
    //Grafico pizza
    public void mostraFaltas() {
        this.menuEscolha = new String();
        this.menuEscolha = "FaltasUltimo";
        try {
            this.listagra = graficoSatMobWeb.listarGraficoFaltasUltimo(this.filters, this.persistencia);
            this.pieModelUltimoMes = new PieChartModel();
            if (!listagra.isEmpty()) {
                for (GraficoBean gra : listagra) {
                    this.pieModelUltimoMes.set(gra.getDescricao(), gra.getFaltas().floatValue());
                }
                this.pieModelUltimoMes.setTitle(Messages.getMessageS("FaltasUltimoMes"));
                this.pieModelUltimoMes.setLegendPosition("w");
                this.pieModelUltimoMes.setShowDataLabels(true);
                this.pieModelUltimoMes.setDiameter(140);
                this.pieModelUltimoMes.setDataFormat("percent");

            } else {
                this.pieModelUltimoMes = null;
            }
        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

//    Lista Evolução Faltas
//    Grafico linha
        try {

            this.listagra = graficoSatMobWeb.listarEvolucaoFaltas(this.filters, this.persistencia);
            this.lineModelEvoFaltas = new LineChartModel();

            String controle;
            float menor, maior;
            if (!listagra.isEmpty()) {
                ChartSeries compet = new ChartSeries();
                controle = listagra.get(0).getDescricao();
                maior = 0;
                menor = Float.MAX_VALUE;
                for (GraficoBean gra : listagra) {
                    //valores para o Compet
                    if (controle.equals(gra.getDescricao())) {
                        compet.setLabel(gra.getDescricao());
                        compet.set(gra.getDt_Fim(), gra.getFaltas().floatValue());

                    } else {
                        this.lineModelEvoFaltas.addSeries(compet);
                        compet = new ChartSeries();
                        compet.setLabel(gra.getDescricao());
                        compet.set(gra.getDt_Fim(), gra.getFaltas().floatValue());
                        controle = gra.getDescricao();
                    }
                    if (menor > gra.getFaltas().floatValue()) {
                        menor = gra.getFaltas().floatValue();
                    }
                    if (maior < gra.getFaltas().floatValue()) {
                        maior = gra.getFaltas().floatValue();
                    }

                }
                this.lineModelEvoFaltas.addSeries(compet);
                this.lineModelEvoFaltas.setTitle(Messages.getMessageS("EvoFaltas"));
                this.lineModelEvoFaltas.setLegendPosition("ne");
                this.lineModelEvoFaltas.setExtender("chartExtender");
                this.lineModelEvoFaltas.setLegendPlacement(LegendPlacement.OUTSIDE);
                this.lineModelEvoFaltas.setShowPointLabels(true);
                lineModelEvoFaltas.getAxes().put(AxisType.X, new CategoryAxis(Messages.getMessageS("Compet")));
                Axis yAxis = lineModelEvoFaltas.getAxis(AxisType.Y);
                yAxis.setMin(0);
                yAxis.setMax(maior + 5);
                yAxis.setLabel(Messages.getMessageS("EvoFaltas"));

            } else {
                this.lineModelEvoFaltas = null;
            }

        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

    }

    // Suspenção Ultimo Mes
    //Grafico pizza
    public void mostraSuspensoes() {
        this.menuEscolha = new String();
        this.menuEscolha = "Suspencao";
//    public void getgraficoPizzaSuspUltimo() {
        try {
            this.listagra = graficoSatMobWeb.listarSuspUltimo(this.filters, this.persistencia);
            this.pieModelSuspUltimo = new PieChartModel();
            if (!listagra.isEmpty()) {
                for (GraficoBean gra : listagra) {
                    this.pieModelSuspUltimo.set(gra.getDescricao(), gra.getSuspensao().floatValue());
                }
                this.pieModelSuspUltimo.setTitle(Messages.getMessageS("SuspUltimoMes"));
                this.pieModelSuspUltimo.setLegendPosition("w");
                this.pieModelSuspUltimo.setShowDataLabels(true);
                this.pieModelSuspUltimo.setDiameter(140);
                this.pieModelSuspUltimo.setDataFormat("percent");

            } else {
                this.pieModelSuspUltimo = null;
            }
        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

        //Evolução Suspensões
        //Grafico linha
        try {
            this.listagra = graficoSatMobWeb.listaEvoSuspensoes(this.filters, this.persistencia);

            this.lineModelEvoSuspensoes = new LineChartModel();
            String controle;
            float menor, maior;
            if (!listagra.isEmpty()) {
                ChartSeries compet = new ChartSeries();
                controle = listagra.get(0).getDescricao();
                maior = 0;
                menor = Float.MAX_VALUE;
                for (GraficoBean gra : listagra) {
                    //valores para o Compet
                    if (controle.equals(gra.getDescricao())) {
                        compet.setLabel(gra.getDescricao());
                        compet.set(gra.getDt_Fim(), gra.getSuspensao().floatValue());

                    } else {
                        this.lineModelEvoSuspensoes.addSeries(compet);
                        compet = new ChartSeries();
                        compet.setLabel(gra.getDescricao());
                        compet.set(gra.getDt_Fim(), gra.getSuspensao().floatValue());
                        controle = gra.getDescricao();
                    }
                    if (menor > gra.getSuspensao().floatValue()) {
                        menor = gra.getSuspensao().floatValue();
                    }
                    if (maior < gra.getSuspensao().floatValue()) {
                        maior = gra.getSuspensao().floatValue();
                    }

                }
                this.lineModelEvoSuspensoes.addSeries(compet);
                this.lineModelEvoSuspensoes.setTitle(Messages.getMessageS("EvoSusp"));
                this.lineModelEvoSuspensoes.setExtender("chartExtender");
                this.lineModelEvoSuspensoes.setLegendPosition("ne");
                this.lineModelEvoSuspensoes.setLegendPlacement(LegendPlacement.OUTSIDE);
                this.lineModelEvoSuspensoes.setShowPointLabels(true);
                lineModelEvoSuspensoes.getAxes().put(AxisType.X, new CategoryAxis(Messages.getMessageS("Compet")));
                Axis yAxis = lineModelEvoSuspensoes.getAxis(AxisType.Y);
                yAxis.setMin(0);
                yAxis.setMax(maior + 2);
                yAxis.setLabel(Messages.getMessageS("Suspensao"));
            } else {
                this.lineModelEvoSuspensoes = null;
            }
        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    // Atestados médicos ultimo Mês
    //Grafico pizza
    public void mostraAtestado() {
        this.menuEscolha = new String();
        this.menuEscolha = "Atestado";
        try {
            this.listagra = graficoSatMobWeb.listaAtMedicosUltimoMes(this.filters, this.persistencia);
            this.pieModelAtUltimoMes = new PieChartModel();
            if (!listagra.isEmpty()) {
                for (GraficoBean gra : listagra) {
                    this.pieModelAtUltimoMes.set(gra.getDescricao(), gra.getAtMedico().floatValue());
                }
                this.pieModelAtUltimoMes.setTitle(Messages.getMessageS("AtestadoUltimoMes"));
                this.pieModelAtUltimoMes.setLegendPosition("w");
                this.pieModelAtUltimoMes.setShowDataLabels(true);
                this.pieModelAtUltimoMes.setDiameter(140);
                this.pieModelAtUltimoMes.setDataFormat("percent");
            } else {
                this.pieModelAtUltimoMes = null;
            }
        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

        //Evolução Atestados Médicos
        //Grafico linha
        try {

            this.listagra = graficoSatMobWeb.listaEvoAtMedicos(this.filters, this.persistencia);
            this.lineModelEvoAtMedicos = new LineChartModel();
            String controle;
            float menor, maior;
            if (!listagra.isEmpty()) {
                ChartSeries compet = new ChartSeries();
                controle = listagra.get(0).getDescricao();
                maior = 0;
                menor = Float.MAX_VALUE;
                for (GraficoBean gra : listagra) {
                    //valores para o Compet
                    if (controle.equals(gra.getDescricao())) {
                        compet.setLabel(gra.getDescricao());
                        compet.set(gra.getDt_Fim(), gra.getAtMedico().floatValue());

                    } else {
                        this.lineModelEvoAtMedicos.addSeries(compet);
                        compet = new ChartSeries();
                        compet.setLabel(gra.getDescricao());
                        compet.set(gra.getDt_Fim(), gra.getAtMedico().floatValue());
                        controle = gra.getDescricao();
                    }
                    if (menor > gra.getAtMedico().floatValue()) {
                        menor = gra.getAtMedico().floatValue();
                    }
                    if (maior < gra.getAtMedico().floatValue()) {
                        maior = gra.getAtMedico().floatValue();
                    }

                }
                this.lineModelEvoAtMedicos.addSeries(compet);
                this.lineModelEvoAtMedicos.setTitle(Messages.getMessageS("EvoAtestados"));
                this.lineModelEvoAtMedicos.setExtender("chartExtender");
                this.lineModelEvoAtMedicos.setLegendPosition("ne");
                this.lineModelEvoAtMedicos.setLegendPlacement(LegendPlacement.OUTSIDE);
                this.lineModelEvoAtMedicos.setShowPointLabels(true);
                lineModelEvoAtMedicos.getAxes().put(AxisType.X, new CategoryAxis(Messages.getMessageS("Compet")));
                Axis yAxis = lineModelEvoAtMedicos.getAxis(AxisType.Y);
                yAxis.setMin(0);
                yAxis.setMax(maior + 5);
                yAxis.setLabel(Messages.getMessageS("Atestado"));
            } else {
                this.lineModelEvoAtMedicos = null;
            }
        } catch (Exception ex) {
            Logger.getLogger(GraficosMB.class.getName()).log(Level.SEVERE, null, ex);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + ex.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void MostrarFiliais() {
        String manterMenu = this.menuEscolha;
        this.filters = new HashMap();
        if (this.mostrarFiliais) {
            if (!this.selecao.isEmpty()) {
                String valor = "?";
                for (int i = 1; i < selecao.size(); i++) {
                    valor += ",?";
                }
                this.filters.put("RH_Ctrl.dt_ini between ? and ?", Arrays.asList(this.Dt_Ini, this.Dt_Fim));
                this.filters.put("RH_Ctrl.codFil in (" + valor + ") ", this.selecao);

            } else {
                this.filters.put("RH_Ctrl.dt_ini between ? and ?", Arrays.asList(this.Dt_Ini, this.Dt_Fim));
                this.filters.replace(" RH_Ctrl.codFil = ? ", "");
            }

        } else {
            this.filters.put(" RH_Ctrl.codFil = ? ", Arrays.asList(this.codFil.toString()));
            this.filters.put("RH_Ctrl.dt_ini between ? and ?", Arrays.asList(this.Dt_Ini, this.Dt_Fim));
        }
        listar();
        this.menuEscolha = new String();
        this.menuEscolha = manterMenu;
    }

    public LineChartModel getLineModelHEs() {
        return lineModelHEs;
    }

    public void setLineModelHEs(LineChartModel lineModelHEs) {
        this.lineModelHEs = lineModelHEs;
    }

    public PieChartModel getPieModelHE50() {
        return pieModelHE50;
    }

    public void setPieModel1(PieChartModel pieModelHE50) {
        this.pieModelHE50 = pieModelHE50;
    }

    public PieChartModel getPieModelHE100() {
        return pieModelHE100;
    }

    public void setPieModelHE100(PieChartModel pieModelHE100) {
        this.pieModelHE100 = pieModelHE100;
    }

    public PieChartModel getPieModelUltimoMes() {
        return pieModelUltimoMes;
    }

    public void setPieModelUltimoMes(PieChartModel pieModelUltimoMes) {
        this.pieModelUltimoMes = pieModelUltimoMes;
    }

    public LineChartModel getLineModelEvoFaltas() {
        return lineModelEvoFaltas;
    }

    public void setLineModelEvoFaltas(LineChartModel lineModelEvoFaltas) {
        this.lineModelEvoFaltas = lineModelEvoFaltas;
    }

    public PieChartModel getPieModelSuspUltimo() {
        return pieModelSuspUltimo;
    }

    public void setPieModelSuspUltimo(PieChartModel pieModelSuspUltimo) {
        this.pieModelSuspUltimo = pieModelSuspUltimo;
    }

    public LineChartModel getLineModelEvoSuspensoes() {
        return lineModelEvoSuspensoes;
    }

    public void setLineModelEvoSuspensoes(LineChartModel lineModelEvoSuspensoes) {
        this.lineModelEvoSuspensoes = lineModelEvoSuspensoes;
    }

    public PieChartModel getPieModelAtUltimoMes() {
        return pieModelAtUltimoMes;
    }

    public void setPieModelAtUltimoMes(PieChartModel pieModelAtUltimoMes) {
        this.pieModelAtUltimoMes = pieModelAtUltimoMes;
    }

    public LineChartModel getLineModelEvoAtMedicos() {
        return lineModelEvoAtMedicos;
    }

    public void setLineModelEvoAtMedicos(LineChartModel lineModelEvoAtMedicos) {
        this.lineModelEvoAtMedicos = lineModelEvoAtMedicos;
    }

    public String getDt_Ini() {
        return Dt_Ini;
    }

    public void setDt_Ini(String Dt_Ini) {
        this.Dt_Ini = Dt_Ini;
    }

    public String getDt_Fim() {
        return Dt_Fim;
    }

    public void setDt_Fim(String Dt_Fim) {
        this.Dt_Fim = Dt_Fim;
    }

    public List<GraficoBean> getListagra() {
        return listagra;
    }

    public void setListagra(List<GraficoBean> listagra) {
        this.listagra = listagra;
    }

    public LocalDate getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(LocalDate dataInicio) {
        this.dataInicio = dataInicio;
    }

    public LocalDate getDataFim() {
        return dataFim;
    }

    public void setDataFim(LocalDate dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataSelecionada2() {
        return dataSelecionada2;
    }

    public void setDataSelecionada2(Date dataSelecionada2) {
        this.dataSelecionada2 = dataSelecionada2;
    }

    public List<Filiais> getFilialselecionada() {
        return filialselecionada;
    }

    public void setFilialselecionada(List<Filiais> filialselecionada) {
        this.filialselecionada = filialselecionada;
    }

    public List<Filiais> getLista() {
        return lista;
    }

    public void setLista(List<Filiais> lista) {
        this.lista = lista;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public List<String> getSelecao() {
        return selecao;
    }

    public void setSelecao(List<String> selecao) {
        this.selecao = selecao;
    }

    public List<BigDecimal> getSelecaoCod() {
        return selecaoCod;
    }

    public void setSelecaoCod(List<BigDecimal> selecaoCod) {
        this.selecaoCod = selecaoCod;
    }

    public String getOrientation() {
        return orientation;
    }

    public void setOrientation(String orientation) {
        this.orientation = orientation;
    }

    public List<String> getSelectedOptions() {
        return selectedOptions;
    }

    public void setSelectedOptions(List<String> selectedOptions) {
        this.selectedOptions = selectedOptions;
    }

    public String getMenuEscolha() {
        return menuEscolha;
    }

    public void setMenuEscolha(String menuEscolha) {
        this.menuEscolha = menuEscolha;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

}

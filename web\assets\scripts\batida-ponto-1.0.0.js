/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

function carregarRelatorio(matr, token, pw) {
    
    var baseurl = "https://mobile.sasw.com.br/SatWebService/api/ws-batida";
//    var baseurl = "http://10.1.1.20:8080/SatWebService/api/ws-batida";
    //var baseurl = "http://192.168.0.196:8080/SatWebService/api/ws-batida";
    var xmlhttp = new XMLHttpRequest();
    xmlhttp.open("POST", baseurl, true);
    xmlhttp.onreadystatechange = function () {
        if (xmlhttp.readyState === 4 && xmlhttp.status === 200) {
            var resposta = JSON.parse(xmlhttp.responseText);
            try {
                console.log(resposta);
                var erro = resposta.erro;
                if (erro == null) {
                    if (resposta.resumo && resposta.resumo == 'ND') {
                        $("#loading").hide();
                        $("#alertaLogin").show();
                        return false;
                    }
                    
                    var relatorio = resposta.relatorio;
                    var filial = resposta.filial;
                    var resumo = resposta.resumo;
                    var funcion = resposta.funcion;
                    var batidas = resposta.batidas;

                    var tbltop = '<div class="cadastrar" id="cadastrar" style="max-width: 100% !important;background-color: #FFF !important">\n';

                    var head = "<div class=\"col-md-12 col-sm-12 col-xs-12 divTopoBatida\" style=\"height: 150px !important; background-color: #FFF !important\">\n";
                    head += "   <table class=\"DadosCabecalhoPonto\">\n";
                    head += "     <tr>\n";
                    head += "        <td rowspan=\"4\" style=\"vertical-align: middle; text-align: center; padding-right: 8px !important\"><img src=\"" + filial.logo + "\" /></td>\n";
                    head += "       <td style=\"border-bottom: thin dashed #DDD;\">" + filial.razaosocial + "</td>\n";
                    head += "     </tr>\n";
                    head += "     <tr>\n";
                    head += "       <td style=\"border-bottom: thin dashed #DDD;\">" + filial.endereco + "</td>\n";
                    head += "     </tr>\n";
                    head += "     <tr>\n";
                    head += "       <td style=\"border-bottom: thin dashed #DDD;\">" + funcion.contrato + "</td>\n";
                    head += "     </tr>\n";
                    head += "     <tr>\n";
                    head += "       <td>" + filial.fone + "</td>\n";
                    head += "     </tr>\n";
                    head += "     <tr>\n";
                    head += "        <td colspan=\"2\" class=\"batida-entrada\">" + relatorio.titulo + "</td>\n";
                    head += "     </tr>\n";
                    head += "   </table>\n";
                    head += "</div>\n";

                    var main = "<div class=\"col-md-12 col-sm-12 col-xs-12\" style=\"height: calc(100vh - 150px); overflow: auto !important; padding: 8px 16px 8px 16px !important; \">\n";

                    main += "<div class=\"col-md-8 col-sm-10 col-xs-12\" style=\"float: none; margin: 0 auto; height: 100%; margin-bottom: 100px;\">\n";
                    
                    main += "<div class=\"col-md-12 col-sm-12 col-xs-12\" style=\"padding: 4px 8px; border: 2px solid #DDD; \n";
                    main += "    background: #FFF; border-radius: 6px; border-top: 4px solid #3C8DBC !important; margin-top: 6px;\">\n";
                    main += "    <div class=\"divTable resumoBatida \">\n";
                    main += "        <div class=\"divTableHeading\">\n";
                    main += "            <div class=\"divTableRow\">\n";
                    main += "                <div class=\"divTableHead\">"+relatorio.texto_data+"</div>\n";
                    main += "                <div class=\"divTableHead\">"+relatorio.hora+"&nbsp;1</div>\n";
                    main += "                <div class=\"divTableHead\">"+relatorio.hora+"&nbsp;2</div>\n";
                    main += "                <div class=\"divTableHead\">"+relatorio.hora+"&nbsp;3</div>\n";
                    main += "                <div class=\"divTableHead\">"+relatorio.hora+"&nbsp;4</div>\n";
                    main += "            </div>\n";
                    main += "        </div>\n";
                    main += "        <div class=\"divTableBody\">\n";
                    main += "            <div class=\"divTableRow\">\n";
                    main += "                <div class=\"divTableCell\">"+relatorio.data+"</div>\n";
                    main += "                <div class=\"divTableCell\">"+resumo.hora1+"</div>\n";
                    main += "                <div class=\"divTableCell\">"+resumo.hora2+"</div>\n";
                    main += "                <div class=\"divTableCell\">"+resumo.hora3+"</div>\n";
                    main += "                <div class=\"divTableCell\">"+resumo.hora4+"</div>\n";
                    main += "            </div>\n";
                    main += "        </div>\n";
                    main += "        <div class=\"divTableFoot tableFootStyle\">\n";
                    main += "            <div class=\"divTableRow\">\n";
                    main += "                <div class=\"divTableCell\">"+relatorio.carga+"</div>\n";
                    main += "                <div class=\"divTableCell\">"+resumo.carga+"</div>\n";
                    if(resumo.irregular == 1){
                        main += "                <div class=\"divTableCell pontoIrregular\">"+relatorio.irregular+"</div>\n";
                    }
                    main += "            </div>\n";
                    if(resumo.intervalo){
                        main += "            <div class=\"divTableRow\">\n";
                        main += "                <div class=\"divTableCell\">"+relatorio.intervalo+"</div>\n";
                        main += "                <div class=\"divTableCell\">"+resumo.intervalo+"</div>\n";
                        main += "            </div>\n";
                    }
                    main += "        </div>\n";
                    main += "    </div>\n";
                    main += "</div>\n";

                    main += '<label style=\"background-color: #303030; color: #FFF; font-weight: bold; padding: 4px 10px 4px 20px; margin: 8px 0px 0px 0px !important; width: 100%; border-radius:20px; width: 100%\"><i class=\"fa fa-calendar\" aria-hidden=\"true\"></i>&nbsp;&nbsp;' + relatorio.data + '</label> \n';

                    var classeHistorico, tituloHistorico;
                    for (i = 0; i < batidas.length; i++) {
                        if (batidas[i].batida % 2 === 0) {
                            tituloHistorico = relatorio.saida;
                            classeHistorico = "batida-saida";
                        } else {
                            tituloHistorico = relatorio.entrada;
                            classeHistorico = "batida-entrada";
                        }

                        main += "    <div class=\"col-md-12 col-sm-12 col-xs-12\" style=\" padding-top: 10px; border: 2px solid #DDD; \n\
                                                background: #FFF; border-radius: 6px; border-top: 4px solid #3C8DBC !important; margin-top: 6px;\">\n";
                        main += "       <div class=\"col-md-6 col-sm-6 col-xs-12\">\n";
                        main += "             <div title=\"" + relatorio.cliquefoto + "\" style=\"float: left; background-color: #EEE; border: 2px solid #DDD; \n\
                                            width: 100px; height: 100px; border-radius: 50%; background-image: url(" + batidas[i].foto + ");\n\
                                            background-size: cover; background-position: center center; cursor: pointer;\"\n\
                                            onclick=\"AbrirFoto('" + batidas[i].foto + "');\"></div>\n";
                        main += "             <div style=\"float: left; width: calc(100% - 118px); margin-left: 18px\">\n";
                        main += "                 <table class=\"DetalhesPonto\" style=\"width: 100%\">\n";
                        main += "                     <tr>\n";
                        main += "                         <td>" + batidas[i].funcionario + "</td>\n";
                        main += "                     </tr>\n";
                        main += "                     <tr>\n";
                        main += "                         <td><label class=\"" + classeHistorico + "\" style=\"width: 130px !important;\">" + tituloHistorico + "</label></td>\n";
                        main += "                     </tr>\n";
                        main += "                     <tr>\n";
                        main += "                         <td><i class=\"fa fa-clock-o\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + relatorio.hora + ": </b>" + batidas[i].hora + "</td>\n";
                        main += "                     </tr>\n";
                        main += "                     <tr>\n";
                        main += "                         <td><i class=\"fa fa-map-marker\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + relatorio.local + ": </b>" + batidas[i].local + "</td>\n";
                        main += "                     </tr>\n";
                        main += "                     <tr>\n";
                        main += "                         <td><i class=\"fa fa-map-pin\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + relatorio.distancia + ": </b>" + batidas[i].distancia + "</td>\n";
                        main += "                     </tr>\n";
                        main += "                 </table>\n";
                        main += "             </div>\n";
                        main += "         </div>\n";
                        main += "           <div class=\"col-md-6 col-sm-6 col-xs-12\">\n";
                        main += "               <div id=\"mapa_" + batidas[i].batida + "\" class=\"map\"  ref=\"posicao\" posicao=\"" + batidas[i].latitude + "|" + batidas[i].longitude + "\"> </div>\n";
                        main += "           </div>\n";
                        main += "       </div>\n";
                    }
                    var tblbottom = "</div></div>";
                    var tbl = tbltop + head + main + tblbottom;
                    $("#loading").hide();
                    $('#content').html(tbl);

                    InicializarMapas();
                } else {
                    $("#loading").hide();
                    $('#mensageErro').html(erro);
                    $("#alertaErro").show();
                    $('*').on('click', function () {
                        location.reload();
                    });
                }
            } catch (err) {
                $("#loading").hide();
                document.getElementById("mensageErro").innerHTML = err.message;
                $("#alertaErro").show();
                $('#logo_satweb').on('click', function () {
                    location.reload();
                });
            }
        } else {
            $("#loading").hide();
            $("#alertaErro").show();
        }
    };
    xmlhttp.send("matr=" + matr + "&token=" + token + "&pw=" + pw);
}

function InicializarMapas() {
    $('.map').each(function () {
        InicializarMapa($(this));
    });
}

function InicializarMapa($obj) {
    return new Promise(resolve => {
        if ($obj.attr('posicao') !== '|') {

            var map = new ol.Map({
                target: $obj.attr('id'),
                layers: [
                    new ol.layer.Tile({
                        source: new ol.source.OSM()
                    })
                ],
                view: new ol.View({
                    center: ol.proj.fromLonLat([$obj.attr('posicao').split('|')[1], $obj.attr('posicao').split('|')[0]]),
                    zoom: 15
                })
            });

            var iconStyle = new ol.style.Style({
                image: new ol.style.Icon({
                    src: 'https://mobile.sasw.com.br:9091/satmobile/img/pin_funcionario.png'
                })
            });
            var marker = new ol.Feature({
                geometry: new ol.geom.Point(ol.proj.fromLonLat([$obj.attr('posicao').split('|')[1], $obj.attr('posicao').split('|')[0]]))
            });
            marker.setStyle(iconStyle);

            var layer = new ol.layer.Vector({
                source: new ol.source.Vector({
                    features: [
                        marker
                    ]
                })
            });

            map.addLayer(layer);
        } else
            resolve();
    });
}
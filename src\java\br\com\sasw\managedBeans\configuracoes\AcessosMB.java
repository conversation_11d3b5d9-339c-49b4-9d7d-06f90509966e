/*
 */
package br.com.sasw.managedBeans.configuracoes;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Controller.Pessoas.PessoasSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaLogin;
import SasBeans.PessoaPortalSrv;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.Saspw;
import SasBeans.Saspwac;
import SasBeans.Sysdef;
import SasBeansCompostas.SaspwacSysdef;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasDaos.PessoaDao;
import br.com.sasw.lazydatamodels.PessoaLoginLazyList;
import br.com.sasw.managedBeans.recursoshumanos.PessoasMB;
import br.com.sasw.pacotesuteis.controller.acessos.AcessosSatMobWeb;
import br.com.sasw.pacotesuteis.sasbeans.PortalSrv;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.pacotesuteis.utilidades.Logos;
import br.com.sasw.utils.Logger;
import static br.com.sasw.utils.Mascaras.removeMascara;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@Named(value = "acessos")
@ViewScoped
public class AcessosMB implements Serializable {

    private final String banco;
    private final BigDecimal codPessoa;
    private String codFil, operador, sistema, subsistema, nome, grupoSelecionado, nomeFilial, caminho, log, codGrupo, nivel, dataTela;
    private ArquivoLog logerro;
    private final AcessosSatMobWeb acessossatmobweb;
    private final ClientesSatMobWeb clientessatmobweb;
    private Persistencia persistencia, central, pessoaLoginPersistencia;
    private List<Saspw> lista, usuariosGrupo;
    private UsuarioSatMobWeb selecionado, novo, pessoaLoginUsuario;
    private List<SASGrupos> grupos, gruposClientes;
    private SASGrupos novoGrupo, grupoEdicao;
    private List<SasPWFill> filiais, todasFiliais, pessoaLoginFiliais;
    private Filiais filiaisCabecalho;
    private final RotasSatWeb rotassatweb;
    private SasPWFill filialSelecionada, novaFilial, filial, pessoaLoginFilial;
    private List<SaspwacSysdef> permissoes;
    private SaspwacSysdef permissaoSelecionada, novaPermissao;
    private List<Sysdef> todasPermissoes;
    private List<PessoaCliAut> clientes;
    private List<Clientes> todosClientes, clientesGrupo;
    private List<PessoaPortalSrv> servicos;
    private List<PortalSrv> todosServicos;
    private PessoaPortalSrv novoServico;
    private PortalSrv servico;
    private List<PessoaLogin> listaPessoaLogin;
    private PessoaLogin pessoaLogin;
    private String novoBanco;
    private Clientes todosClientesSelecao, clienteGrupoSelecionado;
    private PessoaCliAut novoCliente, clienteSelecionado;
    private Sysdef todasPermissoesSelecionada;
    private Boolean alteracao, inclusao, exclusao, mostrarFiliais, flagSomenteAtivos, limparFiltros, flag_exclPessoaCliAut, satMobEW,
            clienteAdministrador;
    private PessoasMB pessoas;
    private Pessoa pessoa, novaPessoa;
    private int flag, total;
    private LazyDataModel<UsuarioSatMobWeb> usuarios = null;
    private Map filters, niveis, mapGrupos;
    private boolean spm;

    public AcessosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        codGrupo = (String) fc.getExternalContext().getSessionMap().get("codgrupo");
        nivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
        clienteAdministrador = new BigDecimal(this.codGrupo).intValue() >= 1000;
        acessossatmobweb = new AcessosSatMobWeb();
        clientessatmobweb = new ClientesSatMobWeb();
        lista = new ArrayList<>();
        selecionado = new UsuarioSatMobWeb();
        novo = new UsuarioSatMobWeb();
        rotassatweb = new RotasSatWeb();
        filiais = new ArrayList<>();
        filialSelecionada = new SasPWFill();
        novaFilial = new SasPWFill();
        filial = new SasPWFill();

        String CodFilObj = codFil;

        for (int I = codFil.length(); I < 4; I++) {
            CodFilObj = "0" + CodFilObj;
        }

        filial.setCodfilAc(CodFilObj);
        filial.setCodFil(CodFilObj);
        filial.setDescricao(nomeFilial);
        permissoes = new ArrayList<>();
        permissaoSelecionada = new SaspwacSysdef();
        novaPermissao = new SaspwacSysdef();
        todasPermissoesSelecionada = new Sysdef();
        sistema = new String();
        subsistema = new String();
        pessoa = new Pessoa();
        inclusao = false;
        exclusao = false;
        alteracao = false;
        flag = 1;
        flagSomenteAtivos = true;
        mostrarFiliais = false;
        log = new String();
        dataTela = DataAtual.getDataAtual("SQL");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog(this.getClass().getSimpleName());

        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String caminho = request.getQueryString();

        if (null == caminho
                || (null != caminho && !caminho.contains("ref=msl"))) {
            spm = false;
        } else {
            spm = true;
        }
    }

    public void Persistencias(Persistencia pp, Persistencia central) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + this.banco);
            }
            this.central = central;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }
            this.pessoas = new PessoasMB(this.persistencia, this.central);

            if (this.clienteAdministrador) {
                gerarLog("Listando grupos - SASGrupos: " + this.codGrupo);
                this.grupos = this.acessossatmobweb.listarGrupos(this.codGrupo, this.persistencia);
            } else {
                gerarLog("Listando todos grupos - SASGrupos");
                this.grupos = this.acessossatmobweb.listarGrupos(this.persistencia);
            }

            gerarLog("Listando todas filiais - Filiais");
            this.todasFiliais = this.acessossatmobweb.listarTodasFiliais(this.persistencia);

            gerarLog("Listando todas permissoes - Sysdef");
            this.todasPermissoes = this.acessossatmobweb.listarTodasPermissoes(this.persistencia);

            this.todosServicos = this.acessossatmobweb.listarServicos(this.central);

            this.novaPermissao.setSaspwac(new Saspwac());
            this.novaPermissao.setSysdef(new Sysdef());

            if (this.grupos.size() == 1) {
                this.novo.setGrupo(this.grupos.get(0));
            } else {
                this.novo.setGrupo(new SASGrupos());
            }
            this.novo.setPessoa(new Pessoa());
            this.novo.setPessoalogin(new PessoaLogin());
            this.novo.setSaspw(new Saspw());

            this.filiaisCabecalho = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);
            this.niveis = gerarNiveis();

            this.filters = new HashMap();
            this.filters.put(" s.situacao = ? ", "A");
            this.filters.put(" s.codfil = ? ", this.codFil.equals("0") ? "" : this.codFil);
            this.filters.put(" p.nome like ? ", "");
            this.filters.put(" s.nivelx like ? ", "");
            this.filters.put(" s.codgrupo = ? ", this.clienteAdministrador ? this.codGrupo : "");
            this.filters.put(" p.codigo in (Select PessoaCliAut.Codigo"
                    + "                     from PessoaCliAut "
                    + "                     Left Join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli"
                    + "                                        and Clientes.CodFil = PessoaCliAut.CodFil"
                    + "                     where pessoacliaut.flag_excl <> '*'"
                    + "                     and Clientes.Nred like ?) ", "");

            gerarLog("Contando acessos.");
            this.total = this.acessossatmobweb.contagemAcessos(this.filters, this.persistencia, this.central);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    private Map gerarNiveis() {
        Map map = new HashMap<>();
        // Se o nível não for algum nível especial, adiciona todos os niveis
        if (this.nivel.equals("1")
                || this.nivel.equals("2")
                || this.nivel.equals("3")
                || this.nivel.equals("4")
                || this.nivel.equals("8")
                || this.nivel.equals("9")) {
            map.put(Messages.getMessageS("Administrador"), "9");
            map.put(Messages.getMessageS("Operacao"), "1");
            map.put(Messages.getMessageS("Manutencao"), "2");
            map.put(Messages.getMessageS("Gerencia"), "3");
            map.put(Messages.getMessageS("PortalRH"), "4");
            map.put(Messages.getMessageS("GTV"), "5");
            map.put(Messages.getMessageS("AssinarGTV"), "7");
            if (!spm) {
                map.put(Messages.getMessageS("SatMobEW"), "8");
            } else {
                map.put(Messages.getMessageS("SPMEW"), "8");
            }
            map.put(Messages.getMessageS("CofreInteligente"), "6");
        }

        /**
         * Se o nível do usuário for algum nível especial, adiciona somente o
         * nível à lista
         */
        // GTV
        if (this.nivel.equals("5") || this.nivel.equals("7")) {
            map.put(Messages.getMessageS("GTV"), "5");
            map.put(Messages.getMessageS("AssinarGTV"), "7");
        }

        // COFRE     
        if (this.nivel.equals("6")) {
            map.put(Messages.getMessageS("CofreInteligente"), "6");
        }

        // EW     
        if (this.nivel.equals("8")) {
            map.put(Messages.getMessageS("SatMobEW"), "8");
        }
        return map;
    }

    public void novoUsuario() {
        try {
            this.satMobEW = false;
            this.novo = new UsuarioSatMobWeb();
            this.selecionado = new UsuarioSatMobWeb();

            if (this.grupos.size() == 1) {
                this.novo.setGrupo(this.grupos.get(0));
            } else {
                this.novo.setGrupo(new SASGrupos());
            }
            this.novo.setPessoa(new Pessoa());
            this.novo.setPessoalogin(new PessoaLogin());
            this.novo.setSaspw(new Saspw());
            this.novo.getSaspw().setSituacao("A");

            if (this.grupos.size() == 1) {
                this.selecionado.setGrupo(this.grupos.get(0));
            } else {
                this.selecionado.setGrupo(new SASGrupos());
            }
            this.selecionado.setPessoa(new Pessoa());
            this.selecionado.setPessoalogin(new PessoaLogin());
            this.selecionado.setSaspw(new Saspw());

            gerarLog("Buscando informação da filial " + this.codFil);
            this.filiais = new ArrayList<>();
            this.filial = this.acessossatmobweb.buscarFilial(this.codFil, this.persistencia);
            if (null != this.filial) {
                this.filial.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.filial.setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.filial.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
                this.filiais.add(this.filial);
            }
            this.permissoes = new ArrayList<>();
            this.novaPermissao = new SaspwacSysdef();
            this.novaPermissao.setSaspwac(new Saspwac());
            this.clientes = new ArrayList<>();
            this.novoCliente = new PessoaCliAut();
            this.clienteSelecionado = new PessoaCliAut();
            this.todosClientesSelecao = null;
            this.pessoa = new Pessoa();
            this.pessoa.setCodigo("-1");
            this.flag = 1;
            this.servico = new PortalSrv();
            this.novoServico = new PessoaPortalSrv();
            this.servicos = new ArrayList<>();
            PrimeFaces.current().resetInputs("formEditar:editar");
            PrimeFaces.current().executeScript("PF('dlgEditar').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }
    public void novoUsuarioPessoaAcesso() {
        try {
            this.satMobEW = false;
            this.novo = new UsuarioSatMobWeb();
            this.selecionado = new UsuarioSatMobWeb();

            if (this.grupos.size() == 1) {
                this.novo.setGrupo(this.grupos.get(0));
            } else {
                this.novo.setGrupo(new SASGrupos());
            }
            this.novo.setPessoa(new Pessoa());
            this.novo.setPessoalogin(new PessoaLogin());
            this.novo.setSaspw(new Saspw());
            this.novo.getSaspw().setSituacao("A");

            if (this.grupos.size() == 1) {
                this.selecionado.setGrupo(this.grupos.get(0));
            } else {
                this.selecionado.setGrupo(new SASGrupos());
            }
            this.selecionado.setPessoa(new Pessoa());
            this.selecionado.setPessoalogin(new PessoaLogin());
            this.selecionado.setSaspw(new Saspw());

            gerarLog("Buscando informação da filial " + this.codFil);
            this.filiais = new ArrayList<>();
            this.filial = this.acessossatmobweb.buscarFilial(this.codFil, this.persistencia);
            if (null != this.filial) {
                this.filial.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.filial.setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.filial.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
                this.filiais.add(this.filial);
            }
            this.permissoes = new ArrayList<>();
            this.novaPermissao = new SaspwacSysdef();
            this.novaPermissao.setSaspwac(new Saspwac());
            this.clientes = new ArrayList<>();
            this.novoCliente = new PessoaCliAut();
            this.clienteSelecionado = new PessoaCliAut();
            this.todosClientesSelecao = null;
            this.pessoa = new Pessoa();
            this.pessoa.setCodigo("-1");
            this.flag = 1;
            this.servico = new PortalSrv();
            this.novoServico = new PessoaPortalSrv();
            this.servicos = new ArrayList<>();
            PrimeFaces.current().resetInputs("formEditar:editar");
            PrimeFaces.current().executeScript("PF('dlgEditar').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void novoUsuarioEW() {
        try {
            this.satMobEW = true;
            this.novo = new UsuarioSatMobWeb();
            this.selecionado = new UsuarioSatMobWeb();
            this.novo.setGrupo(new SASGrupos());
            this.novo.setPessoa(new Pessoa());
            this.novo.setPessoalogin(new PessoaLogin());
            this.novo.setSaspw(new Saspw());
            this.novo.getSaspw().setSituacao("A");
            this.selecionado.setGrupo(new SASGrupos());
            this.selecionado.setPessoa(new Pessoa());
            this.selecionado.setPessoalogin(new PessoaLogin());
            this.selecionado.setSaspw(new Saspw());

            gerarLog("Buscando informação da filial " + this.codFil);
            this.filial = this.acessossatmobweb.buscarFilial(this.codFil, this.persistencia);
            this.filial.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.filial.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.filial.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
            this.filiais = new ArrayList<>();
            this.filiais.add(this.filial);
            this.clientes = new ArrayList<>();
            this.novoCliente = new PessoaCliAut();
            this.clienteSelecionado = new PessoaCliAut();
            this.todosClientesSelecao = null;
            this.flag = 1;
            PrimeFaces.current().resetInputs("formEditar:editar");
            PrimeFaces.current().executeScript("PF('dlgEditar').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrar() throws Exception {
        this.novo.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
        switch (this.novo.getSaspw().getNivelx()) {
            case "1":
                this.novo.getSaspw().setNivelOP("Operação");
                break;
            case "2":
                this.novo.getSaspw().setNivelOP("Manutenção");
                break;
            case "3":
                this.novo.getSaspw().setNivelOP("Gerência");
                break;
            case "4":
                this.novo.getSaspw().setNivelOP("Portal RH");
                break;
            case "5":
                this.novo.getSaspw().setNivelOP("GTV");
                break;
            case "6":
                this.novo.getSaspw().setNivelOP("Cofre Int.");
                break;
            case "7":
                this.novo.getSaspw().setNivelOP("Assinar GTV-e");
                break;
            case "8":
                this.novo.getSaspw().setNivelOP("SatMobEW");
                break;
            case "9":
                this.novo.getSaspw().setNivelOP("Diretoria");
                break;
            case "10":
                this.novo.getSaspw().setNivelOP("Chamados");
                break;
            default:
                this.novo.getSaspw().setNivelOP("");
                break;
        }
        try {
            this.novo.getSaspw().setNivelOP(this.novo.getSaspw().getNivelOP().toUpperCase());
            this.novo.getSaspw().setDescricao(this.novo.getSaspw().getDescricao().toUpperCase());
            this.novo.getSaspw().setMotivo(this.novo.getSaspw().getMotivo().toUpperCase());
            this.novo.getPessoalogin().setNivel(this.novo.getSaspw().getNivelx());
            this.novo.getPessoalogin().setBancoDados(this.persistencia.getEmpresa());
            this.novo.getSaspw().setCodGrupo(Integer.valueOf(this.novo.getGrupo().getCodigo()));

            gerarLog("Criando acessos.");
            this.acessossatmobweb.criarAcesso(this.novo, this.persistencia, this.central);

            if (!this.permissoes.isEmpty()) {
                gerarLog("Inserindo permissões.");
            }
            for (SaspwacSysdef permissao : this.permissoes) {
                permissao.getSaspwac().setNome(this.novo.getSaspw().getNome());
                this.acessossatmobweb.criarPermissoesIndividuais(permissao.getSaspwac(), this.persistencia);
            }

            if (!this.filiais.isEmpty()) {
                gerarLog("Inserindo filiais.");
            }
            for (SasPWFill f : this.filiais) {
                f.setNome(this.novo.getSaspw().getNome());
                this.acessossatmobweb.inserirFilial(f, this.persistencia);
            }

            if (!this.filiais.isEmpty()) {
                gerarLog("Inserindo clientes autorizados.");
            }
            for (PessoaCliAut c : this.clientes) {
                c.setCodigo(this.novo.getSaspw().getCodPessoa().toPlainString());
                c.setCodFil(this.novo.getSaspw().getCodFil());

                this.acessossatmobweb.inserirCliente(c, this.persistencia);
            }

            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.novo) + "\r\n"
                    + "\r\n" + Logger.listaObjetosCompostos2String(this.permissoes) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.filiais) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrarPessoaAcesso() throws Exception {
        this.novo.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
        switch (this.novo.getSaspw().getNivelx()) {
            case "1":
                this.novo.getSaspw().setNivelOP("Operação");
                break;
            case "2":
                this.novo.getSaspw().setNivelOP("Manutenção");
                break;
            case "3":
                this.novo.getSaspw().setNivelOP("Gerência");
                break;
            case "4":
                this.novo.getSaspw().setNivelOP("Portal RH");
                break;
            case "5":
                this.novo.getSaspw().setNivelOP("GTV");
                break;
            case "6":
                this.novo.getSaspw().setNivelOP("Cofre Int.");
                break;
            case "7":
                this.novo.getSaspw().setNivelOP("Assinar GTV-e");
                break;
            case "8":
                this.novo.getSaspw().setNivelOP("SatMobEW");
                break;
            case "9":
                this.novo.getSaspw().setNivelOP("Diretoria");
                break;
            case "10":
                this.novo.getSaspw().setNivelOP("Chamados");
                break;
            default:
                this.novo.getSaspw().setNivelOP("");
                break;
        }
        try {            
//            PessoasSatMobWeb pessoasSatMobWeb = new PessoasSatMobWeb();
//            BidDecimal codPessoaWeb :=  pessoasSatMobWeb.gerarCodPessoaWeb(
//                    this.novo.getPessoa(), persistencia, central);
            PessoaDao pessoaDao = new PessoaDao();
            String codigoPessoaCentral = pessoaDao.inserirPessoaExpressaCentral(
                    this.novo.getPessoa(), central);
            codigoPessoaCentral = codigoPessoaCentral.replace(".0", "");

            this.novo.getPessoa().setCodPessoaWEB(codigoPessoaCentral);
            String codigoPessoaLocal = pessoaDao.inserirPessoaExpressa(
                    this.novo.getPessoa(), persistencia);
            codigoPessoaLocal = codigoPessoaLocal.replace(".0", "");
//
//            this.novo.getSaspw().setNivelOP(this.novo.getSaspw().getNivelOP().toUpperCase());
            this.novo.getSaspw().setNomeCompleto(this.novo.getPessoa().getNome()
                    .toUpperCase());
            this.novo.getSaspw().setPW(this.novo.getPessoa().getPWWeb());
            this.novo.getSaspw().setNome(codigoPessoaLocal);
            this.novo.getSaspw().setCodigo(codigoPessoaLocal);
            this.novo.getSaspw().setCodPessoa(codigoPessoaLocal);
            this.novo.getSaspw().setCodPessoaWeb(codigoPessoaCentral);            
            this.novo.getPessoalogin().setNivel(this.novo.getSaspw().getNivelx());;
            this.novo.getPessoalogin().setBancoDados(this.persistencia.getEmpresa());
            //this.novo.getSaspw().setCodGrupo(Integer.valueOf(this.novo.getGrupo().getCodigo()));
            this.novo.getPessoalogin().setCodigo(new BigDecimal(
                    codigoPessoaCentral));
            this.novo.getPessoalogin().setCodPessoaBD(new BigDecimal(
                    codigoPessoaLocal));            

            gerarLog("Criando acessos.");
            this.acessossatmobweb.criarAcesso(this.novo, this.persistencia, this.central);

            if (!this.permissoes.isEmpty()) {
                gerarLog("Inserindo permissões.");
            }
            for (SaspwacSysdef permissao : this.permissoes) {
                permissao.getSaspwac().setNome(this.novo.getSaspw().getNome());
                this.acessossatmobweb.criarPermissoesIndividuais(permissao.getSaspwac(), this.persistencia);
            }

            if (!this.filiais.isEmpty()) {
                gerarLog("Inserindo filiais.");
            }
            for (SasPWFill f : this.filiais) {
                f.setNome(this.novo.getSaspw().getNome());
                this.acessossatmobweb.inserirFilial(f, this.persistencia);
            }

            if (!this.filiais.isEmpty()) {
                gerarLog("Inserindo clientes autorizados.");
            }
            for (PessoaCliAut c : this.clientes) {
                c.setCodigo(this.novo.getSaspw().getCodPessoa().toPlainString());
                c.setCodFil(this.novo.getSaspw().getCodFil());

                this.acessossatmobweb.inserirCliente(c, this.persistencia);
            }

            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.novo) + "\r\n"
                    + "\r\n" + Logger.listaObjetosCompostos2String(this.permissoes) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.filiais) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }
    public void cadastrarEW() throws Exception {
        try {
            this.novo.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
            this.novo.getSaspw().setCodGrupo(8);
            this.novo.getSaspw().setNivelx("8");
            this.novo.getSaspw().setNivelOP("SatMobEW");
            this.novo.getSaspw().setDescricao(Messages.getMessageS("Cliente") + ": SatMobEW");
            this.novo.getSaspw().setMotivo("UsuarioExpresso");
            this.novo.getPessoalogin().setNivel("8");
            this.novo.getPessoalogin().setBancoDados(this.persistencia.getEmpresa());

            gerarLog("Criando acesso EW.");
            Pessoa p = this.acessossatmobweb.criarAcessoEW(this.novo, this.persistencia, this.central);

            if (!this.filiais.isEmpty()) {
                gerarLog("Inserindo clientes autorizados.");
            }
            for (PessoaCliAut c : this.clientes) {
                c.setCodigo(p.getCodigo().toBigInteger().toString());
                c.setCodFil(this.novo.getSaspw().getCodFil());

                this.acessossatmobweb.inserirCliente(c, this.persistencia);
            }

            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.novo) + "\r\n"
                    + "\r\n" + Logger.listaObjetosCompostos2String(this.permissoes) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.filiais) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dblSelect(SelectEvent event) {
        this.selecionado = (UsuarioSatMobWeb) event.getObject();
        this.pessoa = this.selecionado.getPessoa();
        buttonAction(null);
    }

    public void desbloquearUsuario() {
        if (null == this.selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                PessoaDao pessoaDao = new PessoaDao();
                pessoaDao.desbloquearUsuario(this.selecionado.getPessoa().getEmail(), this.persistencia);
                pessoaDao.desbloquearUsuario(this.selecionado.getPessoa().getEmail(), this.central);
                
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("UsuarioDesbloqueado"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        }
    }

    public void buttonAction(ActionEvent actionEvent) {
        if (null == this.selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                PrimeFaces.current().resetInputs("formEditar");
                this.novo = this.selecionado;

                gerarLog("Carregando informações filial " + this.novo.getSaspw().getCodFil());
                this.filial = this.acessossatmobweb.buscarFilial(this.novo.getSaspw().getCodFil(), this.persistencia);
                prepararEdicao();
                this.flag = 2;
                PrimeFaces.current().executeScript("PF('dlgEditar').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        }
    }

    public void prepararEdicao() {
        try {
            this.flag_exclPessoaCliAut = false;
            this.novoCliente = new PessoaCliAut();

            gerarLog("Carregando informações clientes autorizados.");
            this.clientes = this.acessossatmobweb.listarClientes(this.novo.getPessoa().getCodigo(), this.flag_exclPessoaCliAut, this.persistencia);
            this.todosClientesSelecao = null;
            if (nivel.equals("8")) {
                this.satMobEW = true;
            } else {
                this.satMobEW = false;

                gerarLog("Carregando filiais do usuário.");
                this.filiais = this.acessossatmobweb.listarFiliaisPermissao(this.novo.getSaspw().getNome(), this.persistencia);

                this.listaPessoaLogin = this.acessossatmobweb.listarPessoaLogin(this.novo.getPessoa().getCodPessoaWEB(), this.central);
                this.servicos = this.acessossatmobweb.listarServicosUsuario(this.novo.getPessoa().getCodPessoaWEB(), this.central);
                //this.servicos = this.acessossatmobweb.listarServicosUsuario(this.novo.getPessoalogin().getCodigo(), this.central);
                gerarLog("Carregando permissões do usuário.");
                this.permissoes = this.acessossatmobweb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);

                this.pessoa = new Pessoa();
                this.pessoa.setCodigo(this.novo.getPessoalogin().getCodPessoaBD());
                if (null == this.pessoa.getCodigo() || this.pessoa.getCodigo().equals("")) {
                    this.pessoa.setCodigo(this.novo.getPessoa().getCodigo());
                }
                this.pessoa.setNome("");

                gerarLog("Carregando informação de pessoa do usuário.");
                if (null != this.pessoa.getCodigo()) {
                    this.pessoa = this.pessoas.ListarParametro(this.pessoa).get(0);
                }
                this.novaFilial = new SasPWFill();
                this.novaPermissao = new SaspwacSysdef();
                this.novaPermissao.setSysdef(new Sysdef());
                this.novaPermissao.setSaspwac(new Saspwac());
                this.servico = new PortalSrv();
                this.novoServico = new PessoaPortalSrv();
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void editar() {
        try {
            this.novo.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            switch (this.novo.getSaspw().getNivelx()) {
                case "1":
                    this.novo.getSaspw().setNivelOP("Operação");
                    break;
                case "2":
                    this.novo.getSaspw().setNivelOP("Manutenção");
                    break;
                case "3":
                    this.novo.getSaspw().setNivelOP("Gerência");
                    break;
                case "4":
                    this.novo.getSaspw().setNivelOP("Portal RH");
                    break;
                case "5":
                    this.novo.getSaspw().setNivelOP("GTV");
                    break;
                case "6":
                    this.novo.getSaspw().setNivelOP("Cofre Int.");
                    break;
                case "7":
                    this.novo.getSaspw().setNivelOP("Assinar GTV-e");
                    break;
                case "8":
                    this.novo.getSaspw().setNivelOP("SatMobEW");
                    break;
                case "9":
                    this.novo.getSaspw().setNivelOP("Diretoria");
                    break;
                case "10":
                    this.novo.getSaspw().setNivelOP("Chamados");
                    break;
                default:
                    this.novo.getSaspw().setNivelOP("");
                    break;
            }
            this.novo.getSaspw().setNivelOP(this.novo.getSaspw().getNivelOP().toUpperCase());
            this.novo.getSaspw().setDescricao(this.novo.getSaspw().getDescricao().toUpperCase());
            this.novo.getSaspw().setMotivo(this.novo.getSaspw().getMotivo().toUpperCase());
            this.novo.getSaspw().setCodGrupo(Integer.valueOf(this.novo.getGrupo().getCodigo()));
            this.novo.getSaspw().setCodPessoa(this.pessoa.getCodigo().toPlainString().substring(0, this.pessoa.getCodigo().toPlainString().lastIndexOf(".0")));
            this.novo.getSaspw().setCodPessoaWeb(this.pessoa.getCodPessoaWEB().toPlainString());
            this.novo.getSaspw().setNomeCompleto(this.pessoa.getNome());
            this.novo.getSaspw().setCodigo(this.pessoa.getCodigo().toPlainString().substring(0, this.pessoa.getCodigo().toPlainString().lastIndexOf(".0")));
            this.novo.getPessoalogin().setCodigo(this.pessoa.getCodPessoaWEB());
            this.novo.getPessoalogin().setCodPessoaBD(this.pessoa.getCodigo());
            this.novo.getPessoalogin().setNivel(this.novo.getSaspw().getNivelx());

            gerarLog("Salvado alterações cadasstro de usuário.");
            this.acessossatmobweb.editarAcesso(this.novo, this.persistencia, this.central);
            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void editarPessoaAcesso() {
        try {
            this.novo.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            switch (this.novo.getSaspw().getNivelx()) {
                case "1":
                    this.novo.getSaspw().setNivelOP("Operação");
                    break;
                case "2":
                    this.novo.getSaspw().setNivelOP("Manutenção");
                    break;
                case "3":
                    this.novo.getSaspw().setNivelOP("Gerência");
                    break;
                case "4":
                    this.novo.getSaspw().setNivelOP("Portal RH");
                    break;
                case "5":
                    this.novo.getSaspw().setNivelOP("GTV");
                    break;
                case "6":
                    this.novo.getSaspw().setNivelOP("Cofre Int.");
                    break;
                case "7":
                    this.novo.getSaspw().setNivelOP("Assinar GTV-e");
                    break;
                case "8":
                    this.novo.getSaspw().setNivelOP("SatMobEW");
                    break;
                case "9":
                    this.novo.getSaspw().setNivelOP("Diretoria");
                    break;
                case "10":
                    this.novo.getSaspw().setNivelOP("Chamados");
                    break;
                default:
                    this.novo.getSaspw().setNivelOP("");
                    break;
            }
            this.novo.getSaspw().setNivelOP(this.novo.getSaspw().getNivelOP().toUpperCase());
            this.novo.getSaspw().setCodPessoa(this.pessoa.getCodigo().toPlainString().substring(0, this.pessoa.getCodigo().toPlainString().lastIndexOf(".0")));
            this.novo.getSaspw().setCodPessoaWeb(this.pessoa.getCodPessoaWEB().toPlainString());
            this.novo.getSaspw().setNomeCompleto(this.novo.getPessoa().getNome().toUpperCase());
            this.novo.getSaspw().setCodigo(this.pessoa.getCodigo().toPlainString().substring(0, this.pessoa.getCodigo().toPlainString().lastIndexOf(".0")));
            this.novo.getPessoalogin().setCodigo(this.pessoa.getCodPessoaWEB());
            this.novo.getPessoalogin().setCodPessoaBD(this.pessoa.getCodigo());
            this.novo.getPessoalogin().setNivel(this.novo.getSaspw().getNivelx());

            gerarLog("Salvado alterações cadasstro de usuário.");
            this.acessossatmobweb.editarAcessoPessoa(this.novo, this.persistencia, this.central);
            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

   
    public void editarEW() {
        try {
            gerarLog("Salvado alteração de senha do usuário.");
            this.acessossatmobweb.trocarSenhaCliente(this.novo.getPessoa().getCodigo(), this.novo.getPessoa().getPWWeb(),
                    FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia, this.central);
            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarPessoa(SelectEvent event) {
        try {
            gerarLog("Carregando informações da pessoa selecionada;");
            System.out.println("CODIGO: " + ((Pessoa) event.getObject()).getCodigo().toPlainString());
            this.pessoa = this.pessoas.ListarParametro(((Pessoa) event.getObject())).get(0);
            System.out.println("CODIGO: " + this.pessoa.getCodigo().toPlainString());
            this.novo.getSaspw().setCodPessoa(this.pessoa.getCodigo().toPlainString());
            if (null == this.pessoa.getCodPessoaWEB()) {
                this.pessoa = new Pessoa();
                throw new Exception(Messages.getMessageS("PessoaInvalida"));
            }
            gerarLog("Buscando cadastro existente da pessoa selecionada.");
            UsuarioSatMobWeb usuarioSatMobWeb = this.acessossatmobweb.buscarUsuario(this.pessoa.getCodPessoaWEB(), this.persistencia, this.central);

            if (null != usuarioSatMobWeb && !this.pessoa.getCodPessoaWEB().toPlainString().replace(".0", "").equals("0")) {
                this.selecionado = usuarioSatMobWeb;
                buttonAction(null);
            } else {
                this.novo.setPessoa(this.pessoa);
                if (null != this.novo.getPessoa().getMatr()
                        && this.novo.getPessoa().getMatr().compareTo(new BigDecimal("80000")) == 1
                        && this.novo.getPessoa().getMatr().compareTo(new BigDecimal("90000")) == -1) {
                    this.novo.getSaspw().setNome(this.pessoa.getEndereco());
                } else {
                    this.novo.getSaspw().setNome(this.pessoa.getCodigo().toBigInteger().toString());
                }
                this.novo.getSaspw().setCodPessoa(this.pessoa.getCodigo().toBigInteger().toString());
                this.novo.getSaspw().setCodPessoaWeb(this.pessoa.getCodPessoaWEB().toPlainString());
                this.novo.getSaspw().setNomeCompleto(this.pessoa.getNome());
                this.novo.getSaspw().setCodigo(this.pessoa.getCodigo().toBigInteger().toString());
                this.novo.getPessoalogin().setCodigo(this.pessoa.getCodPessoaWEB());
                this.novo.getPessoalogin().setCodPessoaBD(this.pessoa.getCodigo());
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void novoCliente() {
        this.todosClientesSelecao = null;
        this.nome = null;
        this.todosClientes = new ArrayList<>();
        PrimeFaces.current().resetInputs("formCadastrarCliente:adicionarClientes");
        PrimeFaces.current().executeScript("PF('dlgAdicionarClientes').show()");
    }

    public void selecionarClienteCadastro(Clientes cli) {
        System.out.println(cli);
        if (cli.isSelecionado()) {
            this.novoCliente.setCodCli(cli.getCodigo());
            this.novoCliente.setCodFil(cli.getCodFil().toPlainString());
            this.novoCliente.setNomeCli(cli.getNRed());
            adicionarCliente();
        } else {
            this.clienteSelecionado = new PessoaCliAut();
            this.clienteSelecionado.setCodCli(cli.getCodigo());
            this.clienteSelecionado.setCodFil(cli.getCodFil().toBigInteger().toString());
            if (this.flag == 2) {
                this.clienteSelecionado.setCodigo(this.novo.getPessoa().getCodigo().toBigInteger().toString());
            }
            apagarCliente();
        }
    }

    public List<Clientes> listarClientes() {
        String query = this.nome;
        this.todosClientes = new ArrayList<>();
        try {
            if (this.satMobEW) {
                gerarLog("Buscando lista de clientes contendo " + query);
                List<Clientes> retorno = this.acessossatmobweb.buscarClientes(query, this.persistencia);
                for (Clientes c : retorno) {
                    c.setNome(c.getNRed() + " - " + c.getNome());
                    this.todosClientes.add(c);
                }
            } else {
                if (this.flag == 2) {
                    List<Clientes> retorno;
                    gerarLog("Buscando lista de clientes contendo " + query);
                    if (this.clienteAdministrador) {
                        retorno = this.acessossatmobweb.buscarClientes(this.filiais, this.codPessoa.toPlainString(), query, this.persistencia);
                    } else {
                        retorno = this.acessossatmobweb.buscarClientes(this.novo.getSaspw().getNome(), query, this.persistencia);
                    }

                    for (Clientes c : retorno) {
                        this.todosClientes.add(c);
                        for (PessoaCliAut pca : this.clientes) {
                            if (pca.getCodCli().replace(".0", "").equals(c.getCodigo().replace(".0", ""))
                                    && pca.getCodFil().replace(".0", "").equals(c.getCodFil().toString().replace(".0", ""))) {
                                c.setSelecionado(true);
                                break;
                            }
                        }
                    }
                } else if (this.flag == 1) {
                    List<Clientes> retorno;
                    gerarLog("Buscando lista de clientes contendo " + query);
                    if (this.clienteAdministrador) {
                        retorno = this.acessossatmobweb.buscarClientes(this.filiais, this.codPessoa.toPlainString(), query, this.persistencia);
                    } else {
                        retorno = this.acessossatmobweb.buscarClientes(this.filiais, query, this.persistencia);
                    }

                    for (Clientes c : retorno) {
                        this.todosClientes.add(c);
                        for (PessoaCliAut pca : this.clientes) {
                            if (pca.getCodCli().replace(".0", "").equals(c.getCodigo().replace(".0", ""))
                                    && pca.getCodFil().replace(".0", "").equals(c.getCodFil().toString().replace(".0", ""))) {
                                c.setSelecionado(true);
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return this.todosClientes;
    }

    public List<Clientes> listarClientes(String query) {
        this.todosClientes = new ArrayList<>();
        try {
            if (this.satMobEW) {
                gerarLog("Buscando lista de clientes contendo " + query);
                List<Clientes> retorno = this.acessossatmobweb.buscarClientes(query, this.persistencia);
                for (Clientes c : retorno) {
                    c.setNome(c.getNRed() + " - " + c.getNome());
                    this.todosClientes.add(c);
                }
            } else {
                if (this.flag == 2) {
                    List<Clientes> retorno;
                    gerarLog("Buscando lista de clientes contendo " + query);
                    if (this.clienteAdministrador) {
                        retorno = this.acessossatmobweb.buscarClientes(this.filiais, this.codPessoa.toPlainString(), query, this.persistencia);
                    } else {
                        retorno = this.acessossatmobweb.buscarClientes(this.novo.getSaspw().getNome(), query, this.persistencia);
                    }
                    String f;
                    for (Clientes c : retorno) {
                        f = c.getCodFil().toString().substring(0, c.getCodFil().toString().indexOf(".0"));
                        c.setNome(f + ": " + c.getNRed() + " - " + c.getNome());
                        this.todosClientes.add(c);
                    }
                } else if (this.flag == 1) {
                    List<Clientes> retorno;
                    gerarLog("Buscando lista de clientes contendo " + query);
                    if (this.clienteAdministrador) {
                        retorno = this.acessossatmobweb.buscarClientes(this.filiais, this.codPessoa.toPlainString(), query, this.persistencia);
                    } else {
                        retorno = this.acessossatmobweb.buscarClientes(this.filiais, query, this.persistencia);
                    }
//                    List<Clientes> retorno = this.acessossatmobweb.buscarClientes(this.filiais, query, this.persistencia);
                    String f;
                    for (Clientes c : retorno) {
                        f = c.getCodFil().toString().substring(0, c.getCodFil().toString().indexOf(".0"));
                        c.setNome(f + ": " + c.getNRed() + " - " + c.getNome());
                        this.todosClientes.add(c);
                    }
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return this.todosClientes;
    }

    public List<String> listarClientesPesquisa(String query) {
        List<String> retorno = new ArrayList<>();
        try {
            this.todosClientes = this.acessossatmobweb.buscarClientes(this.codPessoa, query, this.persistencia);
            for (Clientes c : this.todosClientes) {
                retorno.add(c.getNRed());
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return retorno;
    }

    public void selecionarCliente(SelectEvent event) {
        try {
            Clientes c = ((Clientes) event.getObject());
            this.todosClientesSelecao = this.clientessatmobweb.ListaClientes(c.getCodFil().toString(), c.getCodigo(), "", "", "", this.persistencia).get(0);
            this.novoCliente.setCodCli(c.getCodigo());
            this.novoCliente.setCodFil(c.getCodFil().toPlainString());
            this.novoCliente.setNomeCli(this.todosClientesSelecao.getNRed());
            PrimeFaces.current().executeScript("PF('dlgAdicionarClientes').initPosition();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void adicionarCliente() {
        this.novoCliente.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novoCliente.setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.novoCliente.setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.novoCliente.setFlag_Excl("");
        if (this.flag == 1) {
            Boolean add = true;
            for (PessoaCliAut pca : this.clientes) {
                if (pca.getCodCli().equals(this.novoCliente.getCodCli())
                        && pca.getCodFil().replace(".0", "").equals(this.novoCliente.getCodFil().toString().replace(".0", ""))) {
                    add = false;
                    break;
                }
            }
            if (add) {
                this.clientes.add(this.novoCliente);
            }
        } else if (this.flag == 2) {
            try {
                this.novoCliente.setCodigo(this.novo.getPessoa().getCodigo().toPlainString());
                gerarLog("Inserindo novo cliente");
                this.acessossatmobweb.inserirCliente(this.novoCliente, this.persistencia);

                gerarLog("Listando clientes.");
                this.clientes = this.acessossatmobweb.listarClientes(this.novo.getPessoa().getCodigo(), this.flag_exclPessoaCliAut, this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                        + "\r\n" + Logger.objeto2String(this.novoCliente) + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
        this.novoCliente = new PessoaCliAut();
    }

    public void apagarCliente() {
        if (this.flag == 1) {
            for (PessoaCliAut p : this.clientes) {
                if (p.getCodCli().equals(this.clienteSelecionado.getCodCli())
                        && p.getCodFil().replace(".0", "").equals(this.clienteSelecionado.getCodFil().toString().replace(".0", ""))) {
                    this.clientes.remove(p);
                    break;
                }
            }
        } else if (this.flag == 2) {
            try {
                gerarLog("Apagando cliente.");
                this.acessossatmobweb.apagarCliente(this.clienteSelecionado, this.persistencia);

                gerarLog("Listando clientes.");
                this.clientes = this.acessossatmobweb.listarClientes(this.novo.getPessoa().getCodigo(), this.flag_exclPessoaCliAut, this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void ListarPessoaCliAut() {
        try {
            gerarLog("Listando clientes autorizados.");
            this.clientes = this.acessossatmobweb.listarClientes(this.novo.getPessoa().getCodigo(), this.flag_exclPessoaCliAut, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void SelecionarPessoaCliAut(SelectEvent event) {
        this.clienteSelecionado = (PessoaCliAut) event.getObject();
    }

    public void AdicionarPermissao() throws Exception {
        this.novaPermissao.getSaspwac().setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.novaPermissao.getSaspwac().setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.novaPermissao.getSaspwac().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novaPermissao.getSaspwac().setSistema(this.novaPermissao.getSysdef().getCodigo());
        this.novaPermissao.getSaspwac().setAlteracao(this.alteracao ? 1 : 0);
        this.novaPermissao.getSaspwac().setInclusao(this.inclusao ? 1 : 0);
        this.novaPermissao.getSaspwac().setExclusao(this.exclusao ? 1 : 0);
        if (this.flag == 1) {
            Boolean add = true;
            for (SaspwacSysdef permissao : this.permissoes) {
                if (permissao.getSaspwac().getSistema().equals(this.novaPermissao.getSaspwac().getSistema())) {
                    add = false;
                }
            }
            if (add) {
                this.permissoes.add(this.novaPermissao);
                this.novaPermissao = new SaspwacSysdef();
            }
        } else if (this.flag == 2) {
            try {
                this.novaPermissao.getSaspwac().setNome(this.novo.getSaspw().getNome());

                gerarLog("Inserindo permissão;");
                this.acessossatmobweb.criarPermissoesIndividuais(this.novaPermissao.getSaspwac(), this.persistencia);

                gerarLog("Listando permissões.");
                this.permissoes = this.acessossatmobweb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);
                this.novaPermissao = new SaspwacSysdef();
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
        PrepararPermissao();
    }

    public void PermissoesGrupo() {
        int indice = this.grupos.indexOf(this.novo.getGrupo());
        this.grupoSelecionado = Messages.getMessageS("AdicionarPermissoesGrupo") + this.grupos.get(indice).getDescricao() + "?";
        PrimeFaces.current().executeScript("PF('permissaoGrupo').show()");
    }

    public void AdicionarPermissoes() throws Exception {
        try {
            gerarLog("Listando permissões.");
            List<Sysdef> permissoesGrupo = this.acessossatmobweb.listarPermissoesGrupo(new BigDecimal(this.novo.getGrupo().getCodigo()), this.persistencia);
            for (Sysdef permissaogrupo : permissoesGrupo) {
                SaspwacSysdef permissao = new SaspwacSysdef();
                permissao.setSaspwac(new Saspwac());
                permissao.setSysdef(permissaogrupo);
                permissao.getSaspwac().setAlteracao(this.alteracao ? 1 : 0);
                permissao.getSaspwac().setInclusao(this.inclusao ? 1 : 0);
                permissao.getSaspwac().setExclusao(this.exclusao ? 1 : 0);
                permissao.getSaspwac().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                permissao.getSaspwac().setDt_Alter(DataAtual.getDataAtual("SQL"));
                permissao.getSaspwac().setHr_Alter(DataAtual.getDataAtual("HORA"));
                permissao.getSaspwac().setSistema(permissaogrupo.getCodigo());
                Boolean add = true;
                for (SaspwacSysdef permissaopermissao : this.permissoes) {
                    if (permissao.getSaspwac().getSistema().equals(permissaopermissao.getSaspwac().getSistema())) {
                        add = false;
                        break;
                    }
                }
                if (add) {
                    this.permissoes.add(permissao);
                }
            }
            cadastrar();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void AbrirDialogoPermissao() {
        if (null == this.permissaoSelecionada
                || null == this.permissaoSelecionada.getSaspwac()
                || null == this.permissaoSelecionada.getSysdef()) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, "SelecionePermissao", null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.alteracao = false;
            if (this.permissaoSelecionada.getSaspwac().getAlteracao() == 1) {
                this.alteracao = true;
            }
            this.inclusao = false;
            if (this.permissaoSelecionada.getSaspwac().getInclusao() == 1) {
                this.inclusao = true;
            }
            this.exclusao = false;
            if (this.permissaoSelecionada.getSaspwac().getExclusao() == 1) {
                this.exclusao = true;
            }
            PrimeFaces.current().resetInputs("formEditar:editarPermissoes");
            PrimeFaces.current().executeScript("PF('dlgEditarPermissoes').show()");
        }
    }

    public void SelecionarPermissao(SelectEvent event) {
        this.permissaoSelecionada = (SaspwacSysdef) event.getObject();
    }

    public void PrepararPermissao() {
        this.novaPermissao = new SaspwacSysdef();
        this.novaPermissao.setSaspwac(new Saspwac());
        this.novaPermissao.setSysdef(new Sysdef());
        this.alteracao = false;
        this.inclusao = false;
        this.exclusao = false;
        this.subsistema = new String();
        this.sistema = new String();
    }

    public void EditarPermissao() throws Exception {
        this.permissaoSelecionada.getSaspwac().setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.permissaoSelecionada.getSaspwac().setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.permissaoSelecionada.getSaspwac().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.permissaoSelecionada.getSaspwac().setAlteracao(this.alteracao ? 1 : 0);
        this.permissaoSelecionada.getSaspwac().setExclusao(this.exclusao ? 1 : 0);
        this.permissaoSelecionada.getSaspwac().setInclusao(this.inclusao ? 1 : 0);
        if (this.flag == 1) {
        } else if (this.flag == 2) {
            try {
                this.permissaoSelecionada.getSaspwac().setNome(this.novo.getSaspw().getNome());

                gerarLog("Editando permissão.");
                this.acessossatmobweb.editarPermissoesIndividuais(this.permissaoSelecionada.getSaspwac(), this.persistencia);

                gerarLog("Listando permissões.");
                this.permissoes = this.acessossatmobweb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void ApagarPermissao() {
        if (this.flag == 1) {
            for (SaspwacSysdef permissao : this.permissoes) {
                if (permissao.getSaspwac().getSistema().equals(this.permissaoSelecionada.getSaspwac().getSistema())) {
                    this.permissoes.remove(permissao);
                    break;
                }
            }
        } else if (this.flag == 2) {
            try {
                gerarLog("Excluindo permissão.");
                this.acessossatmobweb.apagarPermissoes(this.novo.getSaspw().getNome(), this.permissaoSelecionada.getSaspwac().getSistema().toPlainString(), this.persistencia);

                gerarLog("Listando permissões.");
                this.permissoes = this.acessossatmobweb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void AtualizarSistema() {
        this.novaPermissao.getSaspwac().setSistema(this.sistema);
        this.novaPermissao.getSysdef().setSubSistema(this.subsistema);
    }

    public void selecionarSistema(SelectEvent event) {
        this.sistema = ((Sysdef) event.getObject()).getCodigo();
        this.subsistema = ((Sysdef) event.getObject()).getSubSistema();
    }

    public void SelecionarFilialUsuario(SelectEvent event) {
//        this.filial = (SasPWFill) event.getObject();
        if (null != this.filial) {
            this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
            this.selecionado.getSaspw().setCodFil(this.filial.getCodfilAc());
        } else {
            this.selecionado.getSaspw().setCodFil("0");
        }
    }

    public void SelecionarFilial(SelectEvent event) {
        this.filialSelecionada = (SasPWFill) event.getObject();
    }

    public void SelecionarNovaFilial(SelectEvent event) {
        this.novaFilial = (SasPWFill) event.getObject();
    }

    public void AdicionarFilial() throws Exception {
        this.novaFilial.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novaFilial.setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.novaFilial.setHr_Alter(DataAtual.getDataAtual("HORA"));
        if (this.flag == 1) {
            Boolean add = true;
            for (SasPWFill f : this.filiais) {
                if (f.getCodfilAc().equals(this.novaFilial.getCodfilAc())) {
                    add = false;
                }
            }
            if (add) {
                this.filiais.add(this.novaFilial);
            }
        } else if (this.flag == 2) {
            try {
                this.novaFilial.setCodigo(this.novo.getSaspw().getCodPessoa().toPlainString());
                this.novaFilial.setNome(this.novo.getSaspw().getNome());

                gerarLog("Inserindo filial.");
                this.acessossatmobweb.inserirFilial(this.novaFilial, this.persistencia);

                gerarLog("Listando filiais.");
                this.filiais = this.acessossatmobweb.listarFiliaisPermissao(this.novo.getSaspw().getNome(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
        this.novaFilial = new SasPWFill();
    }

    public void ApagarFilial() {
        if (this.flag == 1) {
            for (SasPWFill fil : this.filiais) {
                if (fil.equals(this.filialSelecionada)) {
                    filiais.remove(fil);
                    break;
                }
            }
        } else if (this.flag == 2) {
            try {
                gerarLog("Apagando filial.");
                this.acessossatmobweb.apagarFilial(this.novo.getSaspw().getNome(), this.filialSelecionada.getCodfilAc(), this.persistencia);

                gerarLog("Listando filiais.");
                this.filiais = this.acessossatmobweb.listarFiliaisPermissao(this.novo.getSaspw().getNome(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void grupos() {
        this.novoGrupo = new SASGrupos();
        PrimeFaces.current().executeScript("PF('dlgGrupos').show();");
    }

    public void selecionarGrupo(SelectEvent event) {
        this.grupoEdicao = (SASGrupos) event.getObject();
    }

    public void dblSelecionarGrupo(SelectEvent event) {
        selecionarGrupo(event);
        detalhesGrupo();
    }

    public void detalhesGrupo() {
        if (null == this.grupoEdicao) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneGrupo"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.mapGrupos = new HashMap<>();
                //            filtroRelatorio.put(" saspw.situacao = ? ", "A");
                this.mapGrupos.put(" saspw.codGrupo = ? ", this.grupoEdicao.getCodigo());
                this.usuariosGrupo = this.acessossatmobweb.listarUsuariosGrupo(this.mapGrupos, this.persistencia);
                this.clientesGrupo = this.acessossatmobweb.listarClientesGrupo(this.grupoEdicao.getCodigo(), this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgDetalhesGrupo').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        }
    }

    public void cadastrarGrupo() {
        try {
            if (null == this.novoGrupo.getDescricao() || this.novoGrupo.getDescricao().equals("")) {
                throw new Exception("PreenchaDescricao");
            }

            this.acessossatmobweb.adiconarGrupos(this.novoGrupo, this.persistencia);

            this.novoGrupo = new SASGrupos();
            this.grupos = this.acessossatmobweb.listarGrupos(this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void editarGrupo() {
        try {
            if (null == this.grupoEdicao.getDescricao() || this.grupoEdicao.getDescricao().equals("")) {
                throw new Exception("PreenchaDescricao");
            }

            this.acessossatmobweb.editarGrupos(this.grupoEdicao, this.persistencia);

            this.grupos = this.acessossatmobweb.listarGrupos(this.persistencia);

            this.mapGrupos = new HashMap<>();
            this.mapGrupos.put(" saspw.codGrupo = ? ", this.grupoEdicao.getCodigo());
            this.usuariosGrupo = this.acessossatmobweb.listarUsuariosGrupo(this.mapGrupos, this.persistencia);
            this.clientesGrupo = this.acessossatmobweb.listarClientesGrupo(this.grupoEdicao.getCodigo(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void LimparFiltros() {
        this.filters.replace(" s.situacao = ? ", "A");
        this.flagSomenteAtivos = true;
        this.filters.replace(" s.codfil = ? ", this.codFil);
        this.mostrarFiliais = false;
        this.filters.replace(" p.nome like ? ", "");
        this.filters.replace(" s.nivelx like ? ", "");
        this.filters.replace(" s.codgrupo = ? ", this.clienteAdministrador ? this.codGrupo : "");
        this.filters.replace(" p.codigo in (Select PessoaCliAut.Codigo"
                + "                     from PessoaCliAut "
                + "                     Left Join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli"
                + "                                        and Clientes.CodFil = PessoaCliAut.CodFil"
                + "                     left join satellite.dbo.pessoalogin p on p.codpessoabd = pessoacliaut.codigo"
                + "                     where pessoacliaut.flag_excl <> '*' and p.codigo is not null"
                + "                     and Clientes.Nred like ?) ", "");
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        gerarLog("Listando acessos.");
        getAllAcessos();
        dt.setFirst(0);
        this.limparFiltros = false;
    }

    public LazyDataModel<UsuarioSatMobWeb> getAllAcessos() {
        if (this.usuarios == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            this.usuarios = new PessoaLoginLazyList(this.persistencia, this.central);
            try {
                gerarLog("Contando acessos.");
                this.total = this.acessossatmobweb.contagemAcessos(this.filters, this.persistencia, this.central);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
        return this.usuarios;
    }

    public void PrePesquisar() {
        this.selecionado = new UsuarioSatMobWeb();
        this.selecionado.setGrupo(new SASGrupos());
        this.selecionado.setPessoa(new Pessoa());
        this.selecionado.setPessoalogin(new PessoaLogin());
        this.selecionado.setSaspw(new Saspw());
        this.todosClientesSelecao = new Clientes();
    }

    public void atualizarGrupos() {
        try {
            this.acessossatmobweb.atualizarGrupos(this.persistencia);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ListaGruposAtualizadosSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void SomenteAtivos() {
        if (this.flagSomenteAtivos) {
            this.filters.replace(" s.situacao = ? ", "A");
        } else {
            this.filters.replace(" s.situacao = ? ", "");
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        gerarLog("Listando acessos.");
        getAllAcessos();
        dt.setFirst(0);
    }

    public void MostrarFiliais() {
        if (this.mostrarFiliais) {
            this.filters.replace(" s.codfil = ? ", "");
        } else {
            this.filters.replace(" s.codfil = ? ", this.codFil);
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        gerarLog("Listando acessos.");
        getAllAcessos();
        dt.setFirst(0);
    }

    public void PesquisaPaginada() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");

        if (this.selecionado.getSaspw().getCodFil().equals("0")) {
            this.filters.replace(" s.codfil = ? ", "");
            this.mostrarFiliais = true;
        } else {
            this.filters.replace(" s.codfil = ? ", this.selecionado.getSaspw().getCodFil());
            this.mostrarFiliais = false;
        }

        if (null == this.selecionado.getSaspw().getSituacao()) {
            this.filters.replace(" s.situacao = ? ", "");
            this.flagSomenteAtivos = false;
        } else {
            this.filters.replace(" s.situacao = ? ", this.selecionado.getSaspw().getSituacao());
            if (this.selecionado.getSaspw().getSituacao().equals("A")) {
                this.flagSomenteAtivos = true;
            } else {
                this.flagSomenteAtivos = false;
            }
        }

        if (this.selecionado.getPessoa().getNome().equals("")) {
            this.filters.replace(" p.nome like ? ", "");
        } else {
            this.filters.replace(" p.nome like ? ", "%" + this.selecionado.getPessoa().getNome() + "%");
        }

        if (null == this.selecionado.getSaspw().getNivelx()) {
            this.filters.replace(" s.nivelx like ? ", "");
        } else {
            this.filters.replace(" s.nivelx like ? ", "%" + this.selecionado.getSaspw().getNivelx() + "%");
        }

        if (this.selecionado.getSaspw().getCodGrupo() == 0) {
            this.filters.replace(" s.codgrupo = ? ", this.clienteAdministrador ? this.codGrupo : "");
        } else {
            this.filters.replace(" s.codgrupo = ? ", String.valueOf(this.selecionado.getSaspw().getCodGrupo()));
        }

        if (null == this.todosClientesSelecao.getNRed()) {
            this.filters.replace(" p.codigo in (Select PessoaCliAut.Codigo"
                    + "                     from PessoaCliAut "
                    + "                     Left Join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli"
                    + "                                        and Clientes.CodFil = PessoaCliAut.CodFil"
                    + "                     where pessoacliaut.flag_excl <> '*'"
                    + "                     and Clientes.Nred like ?) ", "");
        } else {
            this.filters.replace(" p.codigo in (Select PessoaCliAut.Codigo"
                    + "                     from PessoaCliAut "
                    + "                     Left Join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli"
                    + "                                        and Clientes.CodFil = PessoaCliAut.CodFil"
                    + "                     where pessoacliaut.flag_excl <> '*'"
                    + "                     and Clientes.Nred like ?) ", "%" + this.todosClientesSelecao.getNRed() + "%");
        }

        dt.setFilters(this.filters);
        gerarLog("Listando acessos.");
        getAllAcessos();
        dt.setFirst(0);
    }

    public void PesquisaPaginadaPessoaAcesso() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");

        if (this.selecionado.getSaspw().getCodFil().equals("0")) {
            this.filters.replace(" s.codfil = ? ", "");
            this.mostrarFiliais = true;
        } else {
            this.filters.replace(" s.codfil = ? ", this.selecionado.getSaspw().getCodFil());
            this.mostrarFiliais = false;
        }

        if (this.selecionado.getPessoa().getNome().equals("")) {
            this.filters.replace(" p.nome like ? ", "");
        } else {
            this.filters.replace(" p.nome like ? ", "%" + this.selecionado.getPessoa().getNome() + "%");
        }

        dt.setFilters(this.filters);
        gerarLog("Listando acessos.");
        getAllAcessos();
        dt.setFirst(0);
    }

    public void gerarRelatorioUsuariosGrupo() {
        try {
            Map filtroRelatorio = new HashMap<>();
//            filtroRelatorio.put(" saspw.situacao = ? ", "A");
            filtroRelatorio.put(" saspw.codGrupo = ? ", new BigDecimal(this.codGrupo).intValue() > 1000 ? this.codGrupo : "");
            this.usuariosGrupo = this.acessossatmobweb.listarUsuariosClientes(filtroRelatorio, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgRelatorioUsuario').show();");
        } catch (Exception e) {

        }
    }

    public void gerarRelatorio() {
        try {
            String cabecalho = "                <tr>\n"
                    + "                    <td colspan=\"4\">\n"
                    + "                        <table>\n"
                    + "                            <tbody>\n"
                    + "                                <tr>\n"
                    + "                                    <td>Unidade:</td>\n"
                    + "                                    <td><strong>@Unidade</strong></td>\n"
                    + "                                    <td>- @Agencia</td>\n"
                    + "                                </tr>\n"
                    + "                                <tr>\n"
                    + "                                    <td></td>\n"
                    + "                                    <td colspan=\"3\" style=\"font-size:9px\">@Endereco</td>\n"
                    + "                                </tr>\n"
                    + "                            </tbody>\n"
                    + "                        </table>\n"
                    + "                    </td>\n"
                    + "                </tr>\n"
                    + "                <tr>\n"
                    + "                    <td style=\"background: #efefef;\">Usuário</td>\n"
                    + "                    <td style=\"background: #efefef;\">Email</td>\n"
                    + "                    <td style=\"background: #efefef;\">Situação</td>\n"
                    + "                    <td style=\"background: #efefef;\">Controle de Usuários</td>\n"
                    + "                </tr>\n";

            File file = new File(this.getClass().getResource("relatorioESocial.html").getPath().replace("%20", " "));
            FileInputStream fis = new FileInputStream(file);
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            fis.close();
            String html = new String(data, "UTF-8");
            StringBuilder relatorio = new StringBuilder();
            StringBuilder us;
            String cli = "";
            for (Saspw saspw : this.usuariosGrupo) {
                us = new StringBuilder();
                if (!cli.equals(saspw.getCliente())) {
                    cli = saspw.getCliente();
                    relatorio.append("                <tr>\n"
                            + "				<td colspan=\"4\"></br></td>\n"
                            + "                </tr>\n");
                    relatorio.append("                <tr>\n"
                            + "				<td colspan=\"4\"><hr></td>\n"
                            + "                </tr>\n");
                    relatorio.append(cabecalho.replace("@Unidade", cli).replace("@Endereco", saspw.getNomeCodFil())
                            .replace("@Agencia", saspw.getSubAgencia().equals("") ? saspw.getAgencia() : saspw.getAgencia() + "/" + saspw.getSubAgencia()));
                }
                us.append("                <tr>\n");
                us.append("                    <td>").append(saspw.getNomeCompleto()).append("</td>\n");
                us.append("                    <td>").append(saspw.getEmail()).append("</td>\n");
                us.append("                    <td>").append(saspw.getSituacao().equals("A") ? "Ativo" : "Bloqueado").append("</td>\n");
                us.append("                    <td>").append(saspw.getMotivo().equals("Sim") ? "<strong>Sim</strong>" : "Não").append("</td>\n");
                us.append("                </tr>\n");
                relatorio.append(us);

            }
            html = html.replace("@Filial", this.nomeFilial);
            html = html.replace("@ImagemLogo", Logos.getLogoAnexo(this.persistencia.getEmpresa(), "0"));
//            html = html.replace("@ImagemLogo", Logos.getLogo(this.persistencia.getEmpresa(), "0"));
            html = html.replace("@TituloRelatorio", "Controle de Acesso de Usuários");
            html = html.replace("@TituloPaginaRelatorio", "Controle de Acesso de Usuários");
            html = html.replace("@SubTituloRelatorio", this.grupos.get(0).getDescricao());
            html = html.replace("@DadosRelatorio", relatorio.toString());

            // System.out.println(html);
            FacesContext.getCurrentInstance().getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
            response.setHeader("Content-disposition", "inline; filename=\"relatorio.pdf\"");
            response.setCharacterEncoding("UTF-8");
            OutputStream ouputStream = response.getOutputStream();

            Tidy tidy = new Tidy();
            tidy.setShowWarnings(false);
            InputStream input = new ByteArrayInputStream(html.getBytes());
            Document doc = tidy.parseDOM(input, null);
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocument(doc, null);
            renderer.layout();
            renderer.createPDF(ouputStream);
            input.close();
            ouputStream.close();

            ouputStream.flush();
            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        PrimeFaces.current().ajax().update("formRelatorioUsuario:editar");
    }

    public void adicionarServico() throws Exception {
        this.novoServico.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novoServico.setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.novoServico.setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.novoServico.setOper_Incl(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novoServico.setDt_Incl(DataAtual.getDataAtual("SQL"));
        this.novoServico.setHr_Incl(DataAtual.getDataAtual("HORA"));
        this.novoServico.setServico(this.servico.getCodigo().replace(".0", ""));
        this.novoServico.setDescricao(this.servico.getDescricao());

        if (this.flag == 1) {
            Boolean add = true;
            for (PessoaPortalSrv p : this.servicos) {
                if (p.getServico().replace(".0", "").equals(this.novoServico.getServico())) {
                    add = false;
                }
            }
            if (add) {
                this.servicos.add(this.novoServico);
            }
        } else if (this.flag == 2) {
            try {
                this.novoServico.setCodigo(this.novo.getPessoa().getCodPessoaWEB().toPlainString());
                this.acessossatmobweb.inserirServico(this.novoServico, this.central);

                this.servicos = this.acessossatmobweb.listarServicosUsuario(this.novo.getPessoa().getCodPessoaWEB(), this.central);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                        + "\r\n" + Logger.objeto2String(this.novoCliente) + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
        this.servico = new PortalSrv();
        this.novoServico = new PessoaPortalSrv();
    }

    public void removerServico() {
        if (this.flag == 1) {
            for (PessoaPortalSrv p : this.servicos) {
                if (p.getServico().replace(".0", "").equals(this.novoServico.getServico())) {
                    this.servicos.remove(this.novoServico);
                    break;
                }
            }
        } else if (this.flag == 2) {
            try {
                this.acessossatmobweb.apagarServicoUsuario(this.novoServico, this.central);

                this.servicos = this.acessossatmobweb.listarServicosUsuario(this.novo.getPessoa().getCodPessoaWEB(), this.central);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void selecionarBanco(Persistencia novaPersistencia) {
        try {
            this.pessoaLoginPersistencia = novaPersistencia;

            if (this.pessoaLoginPersistencia == null) {
                throw new Exception(getMessageS("ImpossivelConectarBanco") + " " + this.novoBanco);
            }

            this.pessoaLoginUsuario = new UsuarioSatMobWeb();
            this.pessoaLoginFiliais = this.acessossatmobweb.listarTodasFiliais(this.pessoaLoginPersistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void adicionarBanco() {

        this.pessoaLoginUsuario.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.pessoaLoginUsuario.getSaspw().setCodFil(this.pessoaLoginFilial.getCodfilAc());
        switch (this.pessoaLoginUsuario.getSaspw().getNivelx()) {
            case "1":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("Operação");
                break;
            case "2":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("Manutenção");
                break;
            case "3":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("Gerência");
                break;
            case "4":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("Portal RH");
                break;
            case "5":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("GTV");
                break;
            case "6":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("Cofre Int.");
                break;
            case "7":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("Assinar GTV-e");
                break;
            case "8":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("SatMobEW");
                break;
            case "9":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("Diretoria");
                break;
            case "10":
                this.pessoaLoginUsuario.getSaspw().setNivelOP("Chamados");
                break;
            default:
                this.pessoaLoginUsuario.getSaspw().setNivelOP("");
                break;
        }
        try {

            this.novaPessoa = new Pessoa();
            this.novaPessoa.setNome(this.novo.getPessoa().getNome().toUpperCase());
            this.novaPessoa.setEmail(this.novo.getPessoa().getEmail());
            this.novaPessoa.setPWWeb(this.novo.getPessoa().getPWWeb());
            this.novaPessoa.setCPF(removeMascara(this.novo.getPessoa().getCPF()));
            this.novaPessoa.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.novaPessoa.setDt_Alter(getDataAtual("SQL"));
            this.novaPessoa.setHr_Alter(getDataAtual("HORA"));
            this.novaPessoa = this.acessossatmobweb.inserirNovaPessoa(this.novaPessoa, this.pessoaLoginPersistencia, this.central);

            this.pessoaLoginUsuario.setPessoa(this.novaPessoa);

            this.pessoaLoginUsuario.getSaspw().setNome(this.pessoaLoginUsuario.getPessoa().getCodigo().toBigInteger().toString());
            this.pessoaLoginUsuario.getSaspw().setCodPessoa(this.pessoaLoginUsuario.getPessoa().getCodigo().toBigInteger().toString());
            this.pessoaLoginUsuario.getSaspw().setCodPessoaWeb(this.pessoaLoginUsuario.getPessoa().getCodPessoaWEB().toPlainString());
            this.pessoaLoginUsuario.getSaspw().setNomeCompleto(this.pessoaLoginUsuario.getPessoa().getNome());
            this.pessoaLoginUsuario.getSaspw().setCodigo(this.pessoaLoginUsuario.getPessoa().getCodigo().toBigInteger().toString());
            this.pessoaLoginUsuario.getPessoalogin().setCodigo(this.pessoaLoginUsuario.getPessoa().getCodPessoaWEB());
            this.pessoaLoginUsuario.getPessoalogin().setCodPessoaBD(this.pessoaLoginUsuario.getPessoa().getCodigo());

            this.pessoaLoginUsuario.getSaspw().setNivelOP(this.pessoaLoginUsuario.getSaspw().getNivelOP().toUpperCase());
            this.pessoaLoginUsuario.getSaspw().setDescricao(this.novo.getSaspw().getDescricao().toUpperCase());
            this.pessoaLoginUsuario.getSaspw().setMotivo(this.novo.getSaspw().getMotivo().toUpperCase());
            this.pessoaLoginUsuario.getPessoalogin().setNivel(this.pessoaLoginUsuario.getSaspw().getNivelx());
            this.pessoaLoginUsuario.getPessoalogin().setBancoDados(this.pessoaLoginPersistencia.getEmpresa());
            this.pessoaLoginUsuario.getSaspw().setCodGrupo(Integer.valueOf(this.pessoaLoginUsuario.getGrupo().getCodigo()));

            this.acessossatmobweb.criarAcesso(this.pessoaLoginUsuario, this.pessoaLoginPersistencia, this.central);

            this.pessoaLoginFilial.setCodFil(this.pessoaLoginUsuario.getSaspw().getCodFil());
            this.pessoaLoginFilial.setNome(this.pessoaLoginUsuario.getPessoa().getCodigo().toBigInteger().toString());
            this.pessoaLoginFilial.setCodigo(this.pessoaLoginUsuario.getPessoa().getCodigo().toBigInteger().toString());
            this.pessoaLoginFilial.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.acessossatmobweb.inserirFilial(this.pessoaLoginFilial, this.pessoaLoginPersistencia);

            this.listaPessoaLogin = this.acessossatmobweb.listarPessoaLogin(this.novo.getPessoa().getCodPessoaWEB(), this.central);
            PrimeFaces.current().executeScript("PF('dlgAdicionarBanco').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public List<Saspw> getLista() {
        return lista;
    }

    public void setLista(List<Saspw> lista) {
        this.lista = lista;
    }

    public UsuarioSatMobWeb getNovo() {
        return novo;
    }

    public void setNovo(UsuarioSatMobWeb novo) {
        this.novo = novo;
    }

    public List<SASGrupos> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<SASGrupos> grupos) {
        this.grupos = grupos;
    }

    public List<SasPWFill> getFiliais() {
        return filiais;
    }

    public void setFiliais(List<SasPWFill> filiais) {
        this.filiais = filiais;
    }

    public List<SaspwacSysdef> getPermissoes() {
        return permissoes;
    }

    public void setPermissoes(List<SaspwacSysdef> permissoes) {
        this.permissoes = permissoes;
    }

    public SasPWFill getFilialSelecionada() {
        return filialSelecionada;
    }

    public void setFilialSelecionada(SasPWFill filialSelecionada) {
        this.filialSelecionada = filialSelecionada;
    }

    public List<SasPWFill> getTodasFiliais() {
        return todasFiliais;
    }

    public void setTodasFiliais(List<SasPWFill> todasFiliais) {
        this.todasFiliais = todasFiliais;
    }

    public SasPWFill getNovaFilial() {
        return novaFilial;
    }

    public void setNovaFilial(SasPWFill novaFilial) {
        this.novaFilial = novaFilial;
    }

    public SaspwacSysdef getPermissaoSelecionada() {
        return permissaoSelecionada;
    }

    public void setPermissaoSelecionada(SaspwacSysdef permissaoSelecionada) {
        this.permissaoSelecionada = permissaoSelecionada;
    }

    public SaspwacSysdef getNovaPermissao() {
        return novaPermissao;
    }

    public void setNovaPermissao(SaspwacSysdef novaPermissao) {
        this.novaPermissao = novaPermissao;
    }

    public List<Sysdef> getTodasPermissoes() {
        return todasPermissoes;
    }

    public void setTodasPermissoes(List<Sysdef> todasPermissoes) {
        this.todasPermissoes = todasPermissoes;
    }

    public Sysdef getTodasPermissoesSelecionada() {
        return todasPermissoesSelecionada;
    }

    public void setTodasPermissoesSelecionada(Sysdef todasPermissoesSelecionada) {
        this.todasPermissoesSelecionada = todasPermissoesSelecionada;
    }

    public Boolean getAlteracao() {
        return alteracao;
    }

    public void setAlteracao(Boolean alteracao) {
        this.alteracao = alteracao;
    }

    public Boolean getInclusao() {
        return inclusao;
    }

    public void setInclusao(Boolean inclusao) {
        this.inclusao = inclusao;
    }

    public Boolean getExclusao() {
        return exclusao;
    }

    public void setExclusao(Boolean exclusao) {
        this.exclusao = exclusao;
    }

    public PessoasMB getPessoas() {
        return pessoas;
    }

    public void setPessoas(PessoasMB pessoas) {
        this.pessoas = pessoas;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getSistema() {
        return sistema;
    }

    public void setSistema(String sistema) {
        this.sistema = sistema;
    }

    public String getSubsistema() {
        return subsistema;
    }

    public void setSubsistema(String subsistema) {
        this.subsistema = subsistema;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public Pessoa getPessoa() {
        try {
            return this.pessoas.ListarParametro(pessoa).get(0);
        } catch (Exception e) {
            return new Pessoa();
        }
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Boolean getMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(Boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public Boolean getFlagSomenteAtivos() {
        return flagSomenteAtivos;
    }

    public void setFlagSomenteAtivos(Boolean flagSomenteAtivos) {
        this.flagSomenteAtivos = flagSomenteAtivos;
    }

    

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public String getGrupoSelecionado() {
        return grupoSelecionado;
    }

    public void setGrupoSelecionado(String grupoSelecionado) {
        this.grupoSelecionado = grupoSelecionado;
    }

    public List<PessoaCliAut> getClientes() {
        return clientes;
    }

    public void setClientes(List<PessoaCliAut> clientes) {
        this.clientes = clientes;
    }

    public List<Clientes> getTodosClientes() {
        return todosClientes;
    }

    public void setTodosClientes(List<Clientes> todosClientes) {
        this.todosClientes = todosClientes;
    }

    public PessoaCliAut getNovoCliente() {
        return novoCliente;
    }

    public void setNovoCliente(PessoaCliAut novoCliente) {
        this.novoCliente = novoCliente;
    }

    public PessoaCliAut getClienteSelecionado() {
        return clienteSelecionado;
    }

    public void setClienteSelecionado(PessoaCliAut clienteSelecionado) {
        this.clienteSelecionado = clienteSelecionado;
    }

    public Clientes getTodosClientesSelecao() {
        return todosClientesSelecao;
    }

    public void setTodosClientesSelecao(Clientes todosClientesSelecao) {
        this.todosClientesSelecao = todosClientesSelecao;
    }

    public Boolean getFlag_exclPessoaCliAut() {
        return flag_exclPessoaCliAut;
    }

    public void setFlag_exclPessoaCliAut(Boolean flag_exclPessoaCliAut) {
        this.flag_exclPessoaCliAut = flag_exclPessoaCliAut;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public UsuarioSatMobWeb getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(UsuarioSatMobWeb selecionado) {
        this.selecionado = selecionado;
    }

    public Boolean getSatMobEW() {
        return satMobEW;
    }

    public void setSatMobEW(Boolean satMobEW) {
        this.satMobEW = satMobEW;
    }

    public Map getNiveis() {
        return niveis;
    }

    public void setNiveis(Map niveis) {
        this.niveis = niveis;
    }

    public Boolean getClienteAdministrador() {
        return clienteAdministrador;
    }

    public void setClienteAdministrador(Boolean clienteAdministrador) {
        this.clienteAdministrador = clienteAdministrador;
    }

    public void gerarLog(String texto) {
        this.logerro.GravaMetodos(texto, this.caminho);
    }

    public List<Saspw> getUsuariosGrupo() {
        return usuariosGrupo;
    }

    public void setUsuariosGrupo(List<Saspw> usuariosGrupo) {
        this.usuariosGrupo = usuariosGrupo;
    }

    public List<SASGrupos> getGruposClientes() {
        return gruposClientes;
    }

    public void setGruposClientes(List<SASGrupos> gruposClientes) {
        this.gruposClientes = gruposClientes;
    }

    public SASGrupos getNovoGrupo() {
        return novoGrupo;
    }

    public void setNovoGrupo(SASGrupos novoGrupo) {
        this.novoGrupo = novoGrupo;
    }

    public SASGrupos getGrupoEdicao() {
        return grupoEdicao;
    }

    public void setGrupoEdicao(SASGrupos grupoEdicao) {
        this.grupoEdicao = grupoEdicao;
    }

    public List<Clientes> getClientesGrupo() {
        return clientesGrupo;
    }

    public void setClientesGrupo(List<Clientes> clientesGrupo) {
        this.clientesGrupo = clientesGrupo;
    }

    public Clientes getClienteGrupoSelecionado() {
        return clienteGrupoSelecionado;
    }

    public void setClienteGrupoSelecionado(Clientes clienteGrupoSelecionado) {
        this.clienteGrupoSelecionado = clienteGrupoSelecionado;
    }

    public List<PessoaPortalSrv> getServicos() {
        return servicos;
    }

    public void setServicos(List<PessoaPortalSrv> servicos) {
        this.servicos = servicos;
    }

    public List<PortalSrv> getTodosServicos() {
        return todosServicos;
    }

    public void setTodosServicos(List<PortalSrv> todosServicos) {
        this.todosServicos = todosServicos;
    }

    public PortalSrv getServico() {
        return servico;
    }

    public void setServico(PortalSrv servico) {
        this.servico = servico;
    }

    public PessoaPortalSrv getNovoServico() {
        return novoServico;
    }

    public void setNovoServico(PessoaPortalSrv novoServico) {
        this.novoServico = novoServico;
    }

    public List<PessoaLogin> getListaPessoaLogin() {
        return listaPessoaLogin;
    }

    public void setListaPessoaLogin(List<PessoaLogin> listaPessoaLogin) {
        this.listaPessoaLogin = listaPessoaLogin;
    }

    public PessoaLogin getPessoaLogin() {
        return pessoaLogin;
    }

    public void setPessoaLogin(PessoaLogin pessoaLogin) {
        this.pessoaLogin = pessoaLogin;
    }

    public String getNovoBanco() {
        return novoBanco;
    }

    public void setNovoBanco(String novoBanco) {
        this.novoBanco = novoBanco;
    }

    public List<SasPWFill> getPessoaLoginFiliais() {
        return pessoaLoginFiliais;
    }

    public void setPessoaLoginFiliais(List<SasPWFill> pessoaLoginFiliais) {
        this.pessoaLoginFiliais = pessoaLoginFiliais;
    }

    public SasPWFill getPessoaLoginFilial() {
        return pessoaLoginFilial;
    }

    public void setPessoaLoginFilial(SasPWFill pessoaLoginFilial) {
        this.pessoaLoginFilial = pessoaLoginFilial;
    }

    public Persistencia getPessoaLoginPersistencia() {
        return pessoaLoginPersistencia;
    }

    public void setPessoaLoginPersistencia(Persistencia pessoaLoginPersistencia) {
        this.pessoaLoginPersistencia = pessoaLoginPersistencia;
    }

    public UsuarioSatMobWeb getPessoaLoginUsuario() {
        return pessoaLoginUsuario;
    }

    public void setPessoaLoginUsuario(UsuarioSatMobWeb pessoaLoginUsuario) {
        this.pessoaLoginUsuario = pessoaLoginUsuario;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFiliaisCabecalho() {
        return filiaisCabecalho;
    }

    public void setFiliaisCabecalho(Filiais filiaisCabecalho) {
        this.filiaisCabecalho = filiaisCabecalho;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isSpm() {
        return spm;
    }

    public void setSpm(boolean spm) {
        this.spm = spm;
    }
    
    
}

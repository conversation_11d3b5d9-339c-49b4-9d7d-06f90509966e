/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class TmktDetPst {

    private BigDecimal Sequencia;
    private Integer Andamento;
    private String Data;
    private String Hora;
    private String TipoCont;
    private BigDecimal CodPessoa;
    private String Secao;
    private BigDecimal CodFil;
    private String Contato;
    private String Fone;
    private String Fone2;
    private Integer QtdeFotos;
    private String Historico;
    private String Detalhes;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_alter;
    private String Ciente;
    private String Situacao;
    private BigDecimal Matr;
    private String Latitude;
    private String Longitude;
    private String Precisao;
    private boolean FiltroWeb;
    private String Fotos;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public Integer getAndamento() {
        return Andamento;
    }

    public void setAndamento(Integer Andamento) {
        this.Andamento = Andamento;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getTipoCont() {
        return TipoCont;
    }

    public void setTipoCont(String TipoCont) {
        this.TipoCont = TipoCont;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public String getContato() {
        return Contato;
    }

    public void setContato(String Contato) {
        this.Contato = Contato;
    }

    public String getFone() {
        return Fone;
    }

    public void setFone(String Fone) {
        this.Fone = Fone;
    }

    public String getFone2() {
        return Fone2;
    }

    public void setFone2(String Fone2) {
        this.Fone2 = Fone2;
    }

    public Integer getQtdeFotos() {
        return QtdeFotos;
    }

    public void setQtdeFotos(Integer QtdeFotos) {
        this.QtdeFotos = QtdeFotos;
    }

    public String getHistorico() {
        return Historico;
    }

    public void setHistorico(String Historico) {
        this.Historico = Historico;
    }

    public String getDetalhes() {
        return Detalhes;
    }

    public void setDetalhes(String Detalhes) {
        this.Detalhes = Detalhes;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getCiente() {
        return Ciente;
    }

    public void setCiente(String Ciente) {
        this.Ciente = Ciente;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }

    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getPrecisao() {
        return Precisao;
    }

    public void setPrecisao(String Precisao) {
        this.Precisao = Precisao;
    }

    public boolean isFiltroWeb() {
        return FiltroWeb;
    }

    public void setFiltroWeb(boolean FiltroWeb) {
        this.FiltroWeb = FiltroWeb;
    }

    public String getFotos() {
        return Fotos;
    }

    public void setFotos(String Fotos) {
        this.Fotos = Fotos;
    }
}

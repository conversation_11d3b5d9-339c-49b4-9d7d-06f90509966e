package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ATMTabOcor {

    private BigDecimal Codigo;
    private String Descricao;
    private Integer Tipo;
    private String Operador;
    private LocalDate Dt_Alter;
    private String HR_Alter;

    // get and set of Codigo
    public BigDecimal getCodigo() {
        return this.Codigo;
    }

    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    // get and set of Descricao
    public String getDescricao() {
        return this.Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    // get and set of Tipo
    public Integer getTipo() {
        return this.Tipo;
    }

    public void setTipo(Integer Tipo) {
        this.Tipo = Tipo;
    }

    // get and set of Operador
    public String getOperador() {
        return this.Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    // get and set of Dt_Alter
    public LocalDate getDt_Alter() {
        return this.Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    // get and set of HR_Alter
    public String getHR_Alter() {
        return this.HR_Alter;
    }

    public void setHR_Alter(String HR_Alter) {
        this.HR_Alter = HR_Alter;
    }
}

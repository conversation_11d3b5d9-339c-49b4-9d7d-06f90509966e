/*
 */
package Controller.Propostas;

import Dados.Persistencia;
import SasBeans.Contatos;
import SasBeans.Filiais;
import SasBeans.Frequencias;
import SasBeans.Pessoa;
import SasBeans.Produtos;
import SasBeans.PropCml;
import SasBeans.PropCmlMod;
import SasBeans.PropCmlModItens;
import SasBeans.PropCondPgto;
import SasBeans.PropProd;
import SasBeansCompostas.ProdutosPropProdServ;
import SasDaos.ContatosDao;
import SasDaos.FiliaisDao;
import SasDaos.FrequenciasDao;
import SasDaos.PessoaDao;
import SasDaos.ProdutosDao;
import SasDaos.PropCmlDao;
import SasDaos.PropCmlModDao;
import SasDaos.PropCmlModItensDao;
import SasDaos.PropCondPgtoDao;
import SasDaos.PropProdDao;
import SasDaos.TbValDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PropostasSatMobWeb {

    /**
     * Contagem do cadastro de propostas
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            PropCmlDao propostasdao = new PropCmlDao();
            retorno = propostasdao.totalPropostasMobWeb(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de propostas
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PropCml> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<PropCml> retorno;
            PropCmlDao propostasdao = new PropCmlDao();
            retorno = propostasdao.listaPaginada(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca contatos para inserir na proposta
     *
     * @param query
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Contatos> buscarContatos(String query, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            ContatosDao contatosdao = new ContatosDao();
            return contatosdao.buscarContatos(query, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cadastra uma proposta e os produtos de uma proposta comercial
     *
     * @param proposta
     * @param produtos
     * @param persistencia
     * @throws Exception
     */
    public void cadastrarProposta(PropCml proposta, List<Produtos> produtos, Persistencia persistencia) throws Exception {
        try {
            PropCmlDao propostasdao = new PropCmlDao();
            PropProdDao propproddao = new PropProdDao();
            proposta.setNumero(propostasdao.getNumero(persistencia));
            propostasdao.inserirProposta(proposta, persistencia);
            PropProd produtoProposta;
            for (Produtos p : produtos) {
                produtoProposta = new PropProd();
                produtoProposta.setCodFil(proposta.getCodFil());
                produtoProposta.setCodPro(p.getCodigo());
                produtoProposta.setCustoUn(p.getPrecoCusto());
                produtoProposta.setDt_Alter(DataAtual.getDataAtual("SQL"));
                produtoProposta.setDt_Incl(LocalDate.now());
                produtoProposta.setHr_Alter(DataAtual.getDataAtual("HORA"));
                produtoProposta.setHr_Incl(DataAtual.getDataAtual("HORA"));
                produtoProposta.setNumero(proposta.getNumero());
                produtoProposta.setOperIncl(proposta.getOperIncl());
                produtoProposta.setOperador(p.getOperador());
                produtoProposta.setQtde(p.getQtde());
                produtoProposta.setValorUn(p.getPrecoVenda());
                produtoProposta.setValorTot(produtoProposta.getValorUn().multiply(produtoProposta.getQtde()));
                produtoProposta.setOrdem(propproddao.getOrdem(proposta.getNumero(), proposta.getCodFil(), persistencia));
                propproddao.inserirProduto(produtoProposta, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Edita uma proposta e seus produtos
     *
     * @param proposta
     * @param produtos
     * @param persistencia
     * @throws Exception
     */
    public void editarProposta(PropCml proposta, List<Produtos> produtos, Persistencia persistencia) throws Exception {
        try {
//            PropCmlDao propostasdao = new PropCmlDao();
//            PropProdDao propproddao = new PropProdDao();
//            ProdutosDao produtodao = new ProdutosDao();
//            propostasdao.atualizarProposta(proposta, persistencia);
//            List<ProdutosPropProdServ> produtosProposta = produtodao.listaItensPropostas(proposta.getNumero(), proposta.getCodFil(), persistencia);
//            for(Produtos produto : produtos){
//                Boolean encontrado = false;
//                for(ProdutosPropProdServ p : produtosProposta){
//                    if(p.getProduto().getCodigo().intValue() == produto.getCodigo().intValue()){
//                        encontrado = true;
//                        PropProd produtoProposta = new PropProd();
//                        produtoProposta.setCodFil(proposta.getCodFil());
//                        produtoProposta.setCodPro(produto.getCodigo());
//                        produtoProposta.setCustoUn(produto.getPrecoCusto());
//                        produtoProposta.setDt_Alter(DataAtual.getDataAtual("SQL"));
//                        produtoProposta.setHr_Alter(DataAtual.getDataAtual("HORA"));
//                        produtoProposta.setNumero(proposta.getNumero());
//                        produtoProposta.setOperador(proposta.getOperador());
//                        produtoProposta.setQtde(produto.getQtde());
//                        produtoProposta.setValorUn(produto.getPrecoVenda());
//                        produtoProposta.setValorTot(produtoProposta.getValorUn().multiply(produtoProposta.getQtde()));
//                        produtoProposta.setOrdem(propproddao.getOrdemEdicao(proposta.getNumero(),proposta.getCodFil(), produto.getCodigo(), persistencia));
//                        propproddao.atualizarProduto(produtoProposta, persistencia);
//                        produtosProposta.remove(p);
//                        break;
//                    }
//                }
//                if(!encontrado){
//                    PropProd produtoProposta = new PropProd();
//                    produtoProposta.setCodFil(proposta.getCodFil());
//                    produtoProposta.setCodPro(produto.getCodigo());
//                    produtoProposta.setCustoUn(produto.getPrecoCusto());
//                    produtoProposta.setDt_Alter(DataAtual.getDataAtual("SQL"));
//                    produtoProposta.setDt_Incl(LocalDate.now());
//                    produtoProposta.setHr_Alter(DataAtual.getDataAtual("HORA"));
//                    produtoProposta.setHr_Incl(DataAtual.getDataAtual("HORA"));
//                    produtoProposta.setNumero(proposta.getNumero());
//                    produtoProposta.setOperIncl(proposta.getOperIncl());
//                    produtoProposta.setOperador(proposta.getOperador());
//                    produtoProposta.setQtde(produto.getQtde());
//                    produtoProposta.setValorUn(produto.getPrecoVenda());
//                    produtoProposta.setValorTot(produtoProposta.getValorUn().multiply(produtoProposta.getQtde()));
//                    produtoProposta.setOrdem(propproddao.getOrdem(proposta.getNumero(),proposta.getCodFil(),persistencia));
//                    propproddao.inserirProduto(produtoProposta, persistencia);
//                }
//            }
//            for(Produtos p : produtosProposta){
//                PropProd produtoProposta = new PropProd();
//                produtoProposta.setCodFil(proposta.getCodFil());
//                produtoProposta.setCodPro(p.getCodigo());
//                produtoProposta.setNumero(proposta.getNumero());
//                produtoProposta.setOrdem(propproddao.getOrdemEdicao(proposta.getNumero(),proposta.getCodFil(), p.getCodigo(), persistencia));
//                propproddao.removerProduto(produtoProposta, persistencia);
//            }
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os produtos de uma proposta
     *
     * @param numero
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<ProdutosPropProdServ> listaProdutosProposta(BigDecimal numero, BigDecimal codFil, Persistencia persistencia) throws Exception {
        try {
            ProdutosDao propproddao = new ProdutosDao();
            return propproddao.listarItensProposta(numero, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca sugestão de referencias para cadastro de proposta
     *
     * @param query
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> listaReferencias(String query, Persistencia persistencia) throws Exception {
        try {
            TbValDao tbvaldao = new TbValDao();
            return tbvaldao.buscaDescLista(41, query, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca informações do contato de uma proposta
     *
     * @param codigo
     * @param persitencia
     * @return
     * @throws Exception
     */
    public Contatos selecionarContato(BigDecimal codigo, Persistencia persitencia) throws Exception {
        try {
            ContatosDao contatosdao = new ContatosDao();
            return contatosdao.selecionarContato(codigo, persitencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca os dados da filial de uma propostaa
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Filiais getFilialProposta(BigDecimal codFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao FiliaisDao = new FiliaisDao();
            return FiliaisDao.getFilial(codFil.toBigInteger().toString(), persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca o detalhe de um modelo de proposta
     *
     * @param codModelo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getDetalheModeloProposta(BigDecimal codModelo, Persistencia persistencia) throws Exception {
        try {
            PropCmlDao propcmldao = new PropCmlDao();
            return propcmldao.getPropCmlModDetalhe(null == codModelo || codModelo.equals(BigDecimal.ZERO) ? BigDecimal.ONE : codModelo, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os itens de uma proposta
     *
     * @param codModelo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PropCmlModItens> getItensProposta(BigDecimal codModelo, Persistencia persistencia) throws Exception {
        try {
            PropCmlModItensDao propcmlmoditensdao = new PropCmlModItensDao();
            return propcmlmoditensdao.listar(null == codModelo || codModelo.equals(BigDecimal.ZERO) ? BigDecimal.ONE : codModelo, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista as condições de pagamento para uma proposta
     *
     * @param numero
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PropCondPgto> getCondicoesPagamentoProposta(BigDecimal numero, BigDecimal codFil, Persistencia persistencia) throws Exception {
        try {
            PropCondPgtoDao propcondpgtodao = new PropCondPgtoDao();
            return propcondpgtodao.listar(numero, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Insere uma nova condição
     *
     * @param condicao
     * @param persistencia
     * @throws Exception
     */
    public void cadastrarCondicao(PropCondPgto condicao, Persistencia persistencia) throws Exception {
        try {
            PropCondPgtoDao propcondpgtodao = new PropCondPgtoDao();
            propcondpgtodao.inserirCondicao(condicao, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista informações das pessoas listadas em uma proposta
     *
     * @param proposta
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pessoa> getPessoasProposta(PropCml proposta, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            List<Pessoa> pessoas = new ArrayList<>();
            if (null != proposta.getCodPessoa()) {
                pessoas.add(pessoadao.getPessoaCodigo(proposta.getCodPessoa(), persistencia));
            }
            if (null != proposta.getCodPessoa2()) {
                pessoas.add(pessoadao.getPessoaCodigo(proposta.getCodPessoa2(), persistencia));
            }
            return pessoas;
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os modelos de proposta
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PropCmlMod> getModelos(Persistencia persistencia) throws Exception {
        try {
            PropCmlModDao propcmlmoddao = new PropCmlModDao();
            return propcmlmoddao.listarModelos(persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Atualiza o modelo de uma proposta
     *
     * @param proposta
     * @param persistencia
     * @throws Exception
     */
    public void atualizarModeloProposta(PropCml proposta, Persistencia persistencia) throws Exception {
        try {
            PropCmlDao propcmldao = new PropCmlDao();
            propcmldao.atualiarCodModelo(proposta, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Apaga uma condição de pagamento de uma proposta
     *
     * @param condicao
     * @param persistencia
     * @throws Exception
     */
    public void removerCondicao(PropCondPgto condicao, Persistencia persistencia) throws Exception {
        try {
            PropCondPgtoDao propcondpgtodao = new PropCondPgtoDao();
            propcondpgtodao.removerCondicao(condicao, persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }

    public List<Frequencias> listarFrequencias(Persistencia persistencia) throws Exception {
        try {
            FrequenciasDao frequenciasdao = new FrequenciasDao();
            return frequenciasdao.listarFrequencias(persistencia);
        } catch (Exception e) {
            throw new Exception("propostas.falhageral<message>" + e.getMessage());
        }
    }
}

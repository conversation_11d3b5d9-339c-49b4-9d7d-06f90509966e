/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class PessoaCofre {

    private BigDecimal CodPessoa;
    private String BancoDados;
    private BigDecimal CodCofre;
    private String Nome;
    private String Rg;
    private String Nred;

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("0");
        }
    }

    public String getBancoDados() {
        return BancoDados;
    }

    public void setBancoDados(String BancoDados) {
        this.BancoDados = BancoDados;
    }

    public BigDecimal getCodCofre() {
        return CodCofre;
    }

    public void setCodCofre(String CodCofre) {
        try {
            this.CodCofre = new BigDecimal(CodCofre);
        } catch (Exception e) {
            this.CodCofre = new BigDecimal("0");
        }
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getRg() {
        return Rg;
    }

    public void setRg(String Rg) {
        this.Rg = Rg;
    }

    public String getNred() {
        return Nred;
    }

    public void setNred(String Nred) {
        this.Nred = Nred;
    }

}

/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Guias.PedidosPreOrderSatWeb;
import Dados.Persistencia;
import SasBeans.PreOrder;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PedidosPreOrderLazyList extends LazyDataModel<PreOrder> {

    private static final long serialVersionUID = 1L;
    private List<PreOrder> pedidos;
    private final PedidosPreOrderSatWeb pedidosweb;
    private Persistencia persistencia;

    public PedidosPreOrderLazyList(Persistencia pst) {
        this.pedidosweb = new PedidosPreOrderSatWeb();
        this.persistencia = pst;
    }

    @Override
    public List<PreOrder> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.pedidos = this.pedidosweb.listaPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.pedidosweb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.pedidos;
    }

    @Override
    public Object getRowKey(PreOrder preOrder) {
        return preOrder.getPedidoCliente();
    }

    @Override
    public PreOrder getRowData(String pedidoCliente) {
        for (PreOrder preOrder : this.pedidos) {
            if (pedidoCliente.equals(preOrder.getPedidoCliente())) {
                return preOrder;
            }
        }
        return null;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

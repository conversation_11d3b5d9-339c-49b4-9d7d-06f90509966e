/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1202 {

    private String evtRmnRPPS_Id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_indApuracao;
    private String ideEvento_perApur;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;

    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;

    private String ideTrabalhador_cpfTrab;
    private String ideTrabalhador_nisTrab;
    private String ideTrabalhador_qtdDepFP;

    private String procJudTrab_tpTrib;
    private String procJudTrab_nrProcJud;
    private String procJudTrab_codSusp;

    private String dmDev_ideDmDev;

    private String infoPerApur_ideEstab_tpInsc;
    private String infoPerApur_ideEstab_nrInsc;

    private String remunPerApur_matricula;
    private String remunPerApur_codCateg;

    private List<ItensRemun> remunPerApur_itensRemun;

    private String detOper_cnpjOper;
    private String detOper_regANS;
    private String detOper_vrPgTit;

    private String detPlano_tpDep;
    private String detPlano_cpfDep;
    private String detPlano_nmDep;
    private String detPlano_dtNascto;
    private String detPlano_vlrPgDep;

    private String ideADC_dtLei;
    private String ideADC_nrLei;
    private String ideADC_dtEf;

    private String perRef_ideEstab_tpInsc;
    private String perRef_ideEstab_nrInsc;

    private String remunPerAnt_matricula;
    private String remunPerAnt_codCateg;

    private List<ItensRemun> remunPerAnt_itensRemun;

    public static class ItensRemun {

        private String itensRemun_codRubr;
        private String itensRemun_ideTabRubr;
        private String itensRemun_qtdRubr;
        private String itensRemun_fatorRubr;
        private String itensRemun_vrUnit;
        private String itensRemun_vrRubr;

        public String getItensRemun_codRubr() {
            return itensRemun_codRubr;
        }

        public void setItensRemun_codRubr(String itensRemun_codRubr) {
            this.itensRemun_codRubr = itensRemun_codRubr;
        }

        public String getItensRemun_ideTabRubr() {
            return itensRemun_ideTabRubr;
        }

        public void setItensRemun_ideTabRubr(String itensRemun_ideTabRubr) {
            this.itensRemun_ideTabRubr = itensRemun_ideTabRubr;
        }

        public String getItensRemun_qtdRubr() {
            return itensRemun_qtdRubr;
        }

        public void setItensRemun_qtdRubr(String itensRemun_qtdRubr) {
            this.itensRemun_qtdRubr = itensRemun_qtdRubr;
        }

        public String getItensRemun_fatorRubr() {
            return itensRemun_fatorRubr;
        }

        public void setItensRemun_fatorRubr(String itensRemun_fatorRubr) {
            this.itensRemun_fatorRubr = itensRemun_fatorRubr;
        }

        public String getItensRemun_vrUnit() {
            return itensRemun_vrUnit;
        }

        public void setItensRemun_vrUnit(String itensRemun_vrUnit) {
            this.itensRemun_vrUnit = itensRemun_vrUnit;
        }

        public String getItensRemun_vrRubr() {
            return itensRemun_vrRubr;
        }

        public void setItensRemun_vrRubr(String itensRemun_vrRubr) {
            this.itensRemun_vrRubr = itensRemun_vrRubr;
        }

    }

    public String getEvtRmnRPPS_Id() {
        return evtRmnRPPS_Id;
    }

    public void setEvtRmnRPPS_Id(String evtRmnRPPS_Id) {
        this.evtRmnRPPS_Id = evtRmnRPPS_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_indApuracao() {
        return ideEvento_indApuracao;
    }

    public void setIdeEvento_indApuracao(String ideEvento_indApuracao) {
        this.ideEvento_indApuracao = ideEvento_indApuracao;
    }

    public String getIdeEvento_perApur() {
        return ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeTrabalhador_cpfTrab() {
        return ideTrabalhador_cpfTrab;
    }

    public void setIdeTrabalhador_cpfTrab(String ideTrabalhador_cpfTrab) {
        this.ideTrabalhador_cpfTrab = ideTrabalhador_cpfTrab;
    }

    public String getIdeTrabalhador_nisTrab() {
        return ideTrabalhador_nisTrab;
    }

    public void setIdeTrabalhador_nisTrab(String ideTrabalhador_nisTrab) {
        this.ideTrabalhador_nisTrab = ideTrabalhador_nisTrab;
    }

    public String getIdeTrabalhador_qtdDepFP() {
        return ideTrabalhador_qtdDepFP;
    }

    public void setIdeTrabalhador_qtdDepFP(String ideTrabalhador_qtdDepFP) {
        this.ideTrabalhador_qtdDepFP = ideTrabalhador_qtdDepFP;
    }

    public String getProcJudTrab_tpTrib() {
        return procJudTrab_tpTrib;
    }

    public void setProcJudTrab_tpTrib(String procJudTrab_tpTrib) {
        this.procJudTrab_tpTrib = procJudTrab_tpTrib;
    }

    public String getProcJudTrab_nrProcJud() {
        return procJudTrab_nrProcJud;
    }

    public void setProcJudTrab_nrProcJud(String procJudTrab_nrProcJud) {
        this.procJudTrab_nrProcJud = procJudTrab_nrProcJud;
    }

    public String getProcJudTrab_codSusp() {
        return procJudTrab_codSusp;
    }

    public void setProcJudTrab_codSusp(String procJudTrab_codSusp) {
        this.procJudTrab_codSusp = procJudTrab_codSusp;
    }

    public String getDmDev_ideDmDev() {
        return dmDev_ideDmDev;
    }

    public void setDmDev_ideDmDev(String dmDev_ideDmDev) {
        this.dmDev_ideDmDev = dmDev_ideDmDev;
    }

    public String getInfoPerApur_ideEstab_tpInsc() {
        return infoPerApur_ideEstab_tpInsc;
    }

    public void setInfoPerApur_ideEstab_tpInsc(String infoPerApur_ideEstab_tpInsc) {
        this.infoPerApur_ideEstab_tpInsc = infoPerApur_ideEstab_tpInsc;
    }

    public String getInfoPerApur_ideEstab_nrInsc() {
        return infoPerApur_ideEstab_nrInsc;
    }

    public void setInfoPerApur_ideEstab_nrInsc(String infoPerApur_ideEstab_nrInsc) {
        this.infoPerApur_ideEstab_nrInsc = infoPerApur_ideEstab_nrInsc;
    }

    public String getRemunPerApur_matricula() {
        return remunPerApur_matricula;
    }

    public void setRemunPerApur_matricula(String remunPerApur_matricula) {
        this.remunPerApur_matricula = remunPerApur_matricula;
    }

    public String getRemunPerApur_codCateg() {
        return remunPerApur_codCateg;
    }

    public void setRemunPerApur_codCateg(String remunPerApur_codCateg) {
        this.remunPerApur_codCateg = remunPerApur_codCateg;
    }

    public String getDetOper_cnpjOper() {
        return detOper_cnpjOper;
    }

    public void setDetOper_cnpjOper(String detOper_cnpjOper) {
        this.detOper_cnpjOper = detOper_cnpjOper;
    }

    public String getDetOper_regANS() {
        return detOper_regANS;
    }

    public void setDetOper_regANS(String detOper_regANS) {
        this.detOper_regANS = detOper_regANS;
    }

    public String getDetOper_vrPgTit() {
        return detOper_vrPgTit;
    }

    public void setDetOper_vrPgTit(String detOper_vrPgTit) {
        this.detOper_vrPgTit = detOper_vrPgTit;
    }

    public String getDetPlano_tpDep() {
        return detPlano_tpDep;
    }

    public void setDetPlano_tpDep(String detPlano_tpDep) {
        this.detPlano_tpDep = detPlano_tpDep;
    }

    public String getDetPlano_cpfDep() {
        return detPlano_cpfDep;
    }

    public void setDetPlano_cpfDep(String detPlano_cpfDep) {
        this.detPlano_cpfDep = detPlano_cpfDep;
    }

    public String getDetPlano_nmDep() {
        return detPlano_nmDep;
    }

    public void setDetPlano_nmDep(String detPlano_nmDep) {
        this.detPlano_nmDep = detPlano_nmDep;
    }

    public String getDetPlano_dtNascto() {
        return detPlano_dtNascto;
    }

    public void setDetPlano_dtNascto(String detPlano_dtNascto) {
        this.detPlano_dtNascto = detPlano_dtNascto;
    }

    public String getDetPlano_vlrPgDep() {
        return detPlano_vlrPgDep;
    }

    public void setDetPlano_vlrPgDep(String detPlano_vlrPgDep) {
        this.detPlano_vlrPgDep = detPlano_vlrPgDep;
    }

    public String getIdeADC_dtLei() {
        return ideADC_dtLei;
    }

    public void setIdeADC_dtLei(String ideADC_dtLei) {
        this.ideADC_dtLei = ideADC_dtLei;
    }

    public String getIdeADC_nrLei() {
        return ideADC_nrLei;
    }

    public void setIdeADC_nrLei(String ideADC_nrLei) {
        this.ideADC_nrLei = ideADC_nrLei;
    }

    public String getIdeADC_dtEf() {
        return ideADC_dtEf;
    }

    public void setIdeADC_dtEf(String ideADC_dtEf) {
        this.ideADC_dtEf = ideADC_dtEf;
    }

    public String getPerRef_ideEstab_tpInsc() {
        return perRef_ideEstab_tpInsc;
    }

    public void setPerRef_ideEstab_tpInsc(String perRef_ideEstab_tpInsc) {
        this.perRef_ideEstab_tpInsc = perRef_ideEstab_tpInsc;
    }

    public String getPerRef_ideEstab_nrInsc() {
        return perRef_ideEstab_nrInsc;
    }

    public void setPerRef_ideEstab_nrInsc(String perRef_ideEstab_nrInsc) {
        this.perRef_ideEstab_nrInsc = perRef_ideEstab_nrInsc;
    }

    public String getRemunPerAnt_matricula() {
        return remunPerAnt_matricula;
    }

    public void setRemunPerAnt_matricula(String remunPerAnt_matricula) {
        this.remunPerAnt_matricula = remunPerAnt_matricula;
    }

    public String getRemunPerAnt_codCateg() {
        return remunPerAnt_codCateg;
    }

    public void setRemunPerAnt_codCateg(String remunPerAnt_codCateg) {
        this.remunPerAnt_codCateg = remunPerAnt_codCateg;
    }

    public List<ItensRemun> getRemunPerApur_itensRemun() {
        return remunPerApur_itensRemun;
    }

    public void setRemunPerApur_itensRemun(List<ItensRemun> remunPerApur_itensRemun) {
        this.remunPerApur_itensRemun = remunPerApur_itensRemun;
    }

    public List<ItensRemun> getRemunPerAnt_itensRemun() {
        return remunPerAnt_itensRemun;
    }

    public void setRemunPerAnt_itensRemun(List<ItensRemun> remunPerAnt_itensRemun) {
        this.remunPerAnt_itensRemun = remunPerAnt_itensRemun;
    }
}

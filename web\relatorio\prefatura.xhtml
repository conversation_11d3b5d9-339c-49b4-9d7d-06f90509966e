<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.PreFatura} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/charts.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -3px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 767px) and (min-width: 10px) {  
                    a[id*="cabecalho"]:not([title*="Voltar"]){
                        display: none !important;
                    }
                }

                @media only screen and (max-width: 640px) and (min-width: 10px){
                    #divCalendario {
                        margin-top: 0px !important;
                        right: 0px !important;
                    }
                    
                    [id*="main"]{
                        top: auto !important;
                    }
                    
                    .FundoPagina{
                        margin-top: 50px !important;   
                    }
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{prefatura.Persistencia(login.pp)}" />
                <f:viewParam name="portal" value="#{prefatura.portal}" />
            </f:metadata>
            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_Dashboard_rota.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.PreFatura}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{prefatura.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{prefatura.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{prefatura.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{prefatura.filiais.bairro}, #{prefatura.filiais.cidade}/#{trajeto.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:commandLink action="#{prefatura.dataAnterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{prefatura.dataTela}" 
                                                locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                        <p:ajax event="dateSelect" listener="#{prefatura.selecionarData}" update="main cabecalho msgs" />
                                    </p:calendar>

                                    <p:commandLink action="#{prefatura.dataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>
                <h:form id="main">

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela" var="trajeto"
                                                 value="#{prefatura.trajetos}"
                                                 sortBy="#{trajeto.rota}"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true"
                                                 class="tabela DataGrid"
                                                 scrollHeight="100%"  >
                                        <f:facet name="header">
                                            #{localemsgs.PreFatura}
                                        </f:facet>
                                        <p:column headerText="#{localemsgs.Rota}" groupRow="true" style="text-align: center; background-color: #969696 !important; color:#FFF !important;">
                                            <h:outputText value="#{trajeto.rota}" style="font-size:16pt !important; text-shadow:1px 1px #000" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Origem}" style="text-align: center">
                                            <h:outputText value="#{trajeto.NRed}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ER}" style="text-align: center;white-space:break-spaces">
                                            <h:outputText value="#{trajeto.ER}"/>
                                        </p:column>
    <!--                                    <p:column headerText="#{localemsgs.Destino}" style="text-align: center">
                                            <h:outputText value="#{trajeto.NRedDst}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora1}" style="text-align: center">
                                            <h:outputText value="#{trajeto.hora1}" converter="conversorHora"/>
                                        </p:column>                                        -->
                                        <p:column headerText="#{localemsgs.HrChegVei}" style="text-align: center">
                                            <h:outputText value="#{trajeto.hrChegVei}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Chegada}" style="text-align: center">
                                            <h:outputText value="#{trajeto.hrCheg}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Saida}" style="text-align: center">
                                            <h:outputText value="#{trajeto.hrSaida}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HrSaidaVei}" style="text-align: center">
                                            <h:outputText value="#{trajeto.hrSaidaVei}" converter="conversorHora"/>
                                        </p:column>

                                        <!-- ui-datatable-summaryrow ui-widget-header -->

<!--                                    <p:column headerText="#{localemsgs.Atraso}" style="text-align: right">
    <h:outputText value="#{trajeto.atraso}" converter="conversor0"/>
</p:column>
<p:column headerText="#{localemsgs.Antecipado}" style="text-align: right">
    <h:outputText value="#{trajeto.antecipado}" converter="conversor0" />
</p:column>
<p:column headerText="#{localemsgs.TempoEspera}" style="text-align: right">
    <h:outputText value="#{trajeto.tempoEspera}" converter="conversor0"/>
</p:column>
<p:column headerText="#{localemsgs.HsImprod}" style="text-align: right">
    <h:outputText value="#{trajeto.hsImprod}" converter="conversor0"/>
</p:column>                                            -->
                                        <p:column headerText="#{localemsgs.Guias}" style="text-align: center;white-space:break-spaces">
                                            <h:outputText value="#{trajeto.guia}" />
                                        </p:column>
<!--                                        <p:column headerText="#{localemsgs.Serie}" style="text-align: center">
                                            <h:outputText value="#{trajeto.serie}" />
                                        </p:column> -->

                                        <p:column headerText="#{localemsgs.Montante}" style="text-align: center">
                                            <h:outputText value="#{trajeto.montante}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorEmbarque}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorEmb}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorADV}" style="text-align: center;white-space:break-spaces">
                                            <h:outputText value="#{trajeto.valorAdv}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorTE}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorTE}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorProcDN}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorProcDN}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorProcMD}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorProcMD}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorCustodia}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorCustodia}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorTotal}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorEmb+trajeto.valorAdv+trajeto.valorTE+trajeto.valorProcDN+trajeto.valorProcMD+trajeto.valorCustodia}" converter="conversormoeda" />
                                        </p:column>
                                        <p:summaryRow>
                                            <p:column colspan="8" style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{localemsgs.TotalRota}: #{trajeto.rota}" style="color:#FFF !important"/>
                                            </p:column>
                                            <p:column  style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalMontante}" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>
                                            <p:column  style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalValorEmb}" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>
                                            <p:column  style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalValorAdv}" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>
                                            <p:column  style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalValorTE}" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>

                                            <p:column  style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalProcDN}" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>
                                            <p:column  style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalProcMD}" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>
                                            <p:column  style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalCustodia}" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>
                                            <p:column  style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalValorTE + trajeto.totalValorEmb + trajeto.totalValorAdv + trajeto.totalProcDN + trajeto.totalProcMD + trajeto.totalCustodia }" converter="conversormoeda" style="color:#FFF !important"/>
                                            </p:column>
                                        </p:summaryRow>
                                    </p:dataTable>
                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 146) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 146) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>


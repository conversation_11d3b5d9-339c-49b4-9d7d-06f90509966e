/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesSaidas;
import SasBeans.TesSaidasDN;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesSaidasDNDao {

    /**
     * Lista composições para guia de ATM
     *
     * @param guia
     * @param serie
     * @param doctos
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesSaidasDN> listaComposicoesATM(String guia, String serie, boolean doctos, Persistencia persistencia) throws Exception {
        List<TesSaidasDN> composicaoDN = new ArrayList<>();
        try {
            String sql = "Select TesSaidasDN.Codigo, TesSaidasDN.Docto, TesSaidasDN.Qtde, TesSaidasDN.Valor, TesSaidasLacres.*\n"
                    + "from TesSaidasDN \n"
                    + "left Join <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on  TesSaidasLacres.Guia   = TesSaidasDN.Guia   \n"
                    + "                          and TesSaidasLacres.Serie  = TesSaidasDN.Serie  \n"
                    + "                          and TesSaidasLacres.Malote = 001                \n"
                    + "where TesSaidasDN.Guia = ?\n"
                    + "  and TesSaidasDN.Serie = ?\n";
            if (doctos) {
                sql += "  and TesSaidasDN.Docto >= '1'\n"
                        + "  and TesSaidasDN.Docto <= '4'";
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            TesSaidasDN tesentdn;
            while (consult.Proximo()) {
                tesentdn = new TesSaidasDN();
                tesentdn.setCodigo(consult.getString("Codigo"));
                tesentdn.setDocto(consult.getString("Docto"));
                tesentdn.setQtde(consult.getString("Qtde"));
                tesentdn.setValor(consult.getString("Valor"));
                tesentdn.setLacre(consult.getString("Lacre"));
                composicaoDN.add(tesentdn);
            }
            consult.Close();
            return composicaoDN;
        } catch (Exception e) {
            throw new Exception("TesSaidasDNDao.listaComposicoesATM - " + e.getMessage() + "\r\n"
                    + "Select TesSaidasDN.Codigo, TesSaidasDN.Docto, TesSaidasDN.Qtde, TesSaidasDN.Valor, TesSaidasLacres.*\n"
                    + "from TesSaidasDN \n"
                    + "left Join TesSaidasLacres on  TesSaidasLacres.Guia   = TesSaidasDN.Guia   \n"
                    + "                          and TesSaidasLacres.Serie  = TesSaidasDN.Serie  \n"
                    + "                          and TesSaidasLacres.Malote = 001                \n"
                    + "where TesSaidasDN.Guia = " + guia
                    + "  and TesSaidasDN.Serie = " + serie
                    + (doctos ? "  and TesSaidasDN.Docto >= '1'\n  and TesSaidasDN.Docto <= '4'" : ""));
        }
    }

    /**
     * Lista composicoes
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesSaidasDN> listaComposicoes(String guia, String serie, Persistencia persistencia) throws Exception {
        List<TesSaidasDN> composicaoDN = new ArrayList<>();
        try {
            String sql;
            sql = "Select codigo, qtde from TesSaidasDN"
                    + " Where Guia = ?"
                    + "  and Serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            TesSaidasDN tesentdn;
            while (consult.Proximo()) {
                tesentdn = new TesSaidasDN();
                tesentdn.setCodigo(consult.getString("codigo"));
                tesentdn.setQtde(consult.getString("qtde"));
                composicaoDN.add(tesentdn);
            }
            consult.Close();
            return composicaoDN;
        } catch (Exception e) {
            throw new Exception("TesSaidasDNDao.ListaComposicoes - " + e.getMessage() + "\r\n"
                    + "elect codigo, qtde from TesSaidasDN"
                    + " Where Guia = " + guia
                    + "  and Serie = " + serie);
        }
    }

    public List<TesSaidasDN> listaComposicoes(List<TesSaidas> tesSaidasList, String serie, Persistencia persistencia) throws Exception {
        try {
            List<TesSaidasDN> retorno = new ArrayList<>();
            String sql = " SELECT *, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC "
                    + " FROM TesSaidasDN "
                    + " WHERE guia in (";
            for (TesSaidas tesSaidas : tesSaidasList) {
                sql += " ?,";
            }
            sql = sql.substring(0, sql.length() - 1) + " ) AND serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            for (TesSaidas tesSaidas : tesSaidasList) {
                consulta.setString(tesSaidas.getGuia());
            }
            consulta.setString(serie);
            consulta.select();
            TesSaidasDN tesSaidasDN;
            while (consulta.Proximo()) {
                tesSaidasDN = new TesSaidasDN();
                tesSaidasDN.setGuia(consulta.getString("Guia"));
                tesSaidasDN.setSerie(consulta.getString("Serie"));
                tesSaidasDN.setCodigo(consulta.getString("Codigo"));
                tesSaidasDN.setDocto(consulta.getString("Docto"));
                tesSaidasDN.setQtde(consulta.getString("Qtde"));
                tesSaidasDN.setValor(consulta.getString("Valor"));
                tesSaidasDN.setOperador(consulta.getString("Operador"));
                tesSaidasDN.setDt_Alter(consulta.getString("Dt_AlterC"));
                tesSaidasDN.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesSaidasDN);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesSaidasDNDao.listaComposicoes - " + e.getMessage());
        }
    }

    /**
     * Insere uma nova entrada na tabela TesSaidasDN
     *
     * @param tesSaidasDN
     * @param persistencia
     * @throws Exception
     */
    public void inserirTesSaidasDN(TesSaidasDN tesSaidasDN, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO TesSaidasDN values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tesSaidasDN.getGuia());
            consulta.setString(tesSaidasDN.getSerie());
            consulta.setString(tesSaidasDN.getCodigo());
            consulta.setString(tesSaidasDN.getDocto());
            consulta.setString(tesSaidasDN.getQtde());
            consulta.setString(tesSaidasDN.getValor());
            consulta.setString(tesSaidasDN.getTipoCed());
            consulta.setString(tesSaidasDN.getOperador());
            consulta.setString(tesSaidasDN.getDt_Alter());
            consulta.setString(tesSaidasDN.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesSaidasDNDao.inserirTesSaidasDN - " + e.getMessage() + "\r\n"
                    + " INSERT INTO TesSaidasDN values (" + tesSaidasDN.getGuia() + "," + tesSaidasDN.getSerie() + ", \n"
                    + tesSaidasDN.getCodigo() + "," + tesSaidasDN.getDocto() + "," + tesSaidasDN.getQtde() + "," + tesSaidasDN.getValor() + ", \n"
                    + tesSaidasDN.getTipoCed() + "," + tesSaidasDN.getOperador() + "," + tesSaidasDN.getDt_Alter() + ",\n"
                    + tesSaidasDN.getHr_Alter() + ") ");
        }
    }

    public void excluirComposicao(String guia, String serie, String docto, String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "delete from TesSaidasDN "
                    + " where Guia = ?"
                    + " and Serie =  ?"
                    + " and codigo = ?"
                    + " and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codigo);
            consulta.setString(docto);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesSaidasDNDao.excluirComposicao - " + e.getMessage() + "\r\n"
                    + "delete from TesSaidasDN "
                    + " where Guia = " + guia
                    + " and Serie =  " + serie
                    + " and codigo = " + codigo
                    + " and docto = " + docto);
        }
    }
}

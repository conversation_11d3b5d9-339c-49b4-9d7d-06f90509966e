<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css"  href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css"  href="../assets/css/pedidos.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />

            <style>
                .FundoPagina [id*="tabela"] thead tr th:nth-child(6),
                .FundoPagina [id*="tabela"] thead tr th:nth-child(8){
                    background: #101010 !important;
                }
                
                .FundoPagina [id*="tabela"] thead tr th:nth-child(7),
                .FundoPagina [id*="tabela"] thead tr th:nth-child(9){
                    background: #303030 !important;
                }
                
                .FundoPagina [id*="tabela"] thead tr th:nth-child(10){
                    background: darkred !important;
                }
                
                .ui-datatable-summaryrow .ui-column-title{
                    display: none !important;
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    .tabela .ui-datatable-scrollable-body{
                        flex-grow: 1;

                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }

                    .DataGrid [role="columnheader"] > span {
                        top: -1px !important;
                        position: relative !important;
                    }
                }

                .DataGrid input{
                    max-width: 100px !important;
                    text-align:center !important;
                    background-color: #FFF !important;
                }

                .DataGrid input:hover{
                    background-color: #FFF !important;
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }
                #divCorporativo{
                    display:none !important;
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }


                #body {
                    height: calc(100% - 48px);
                    position: relative;
                    display: flex;
                    flex-direction: column;
                }

                #main {
                    flex-grow: 1;
                }

                #formPesquisar .ui-radiobutton {
                    background: transparent !important;
                }

                .ui-datatable-scrollable-body,
                .FundoPagina > .ui-panel-content {
                    height: 100% !important;
                }

                .FundoPagina {
                    border: thin solid #CCC !important;
                    border-top:4px solid #3C8DBC !important;
                }
            </style>
        </h:head>

        <h:body id="h" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{pedidosMB.persistencia(login.pp)}" />
                <f:viewAction action="#{pessoa.Persistencias(login.pp, login.satellite)}"/>
                <f:viewAction action="#{pedidosMB.allPedidos}"/>
            </f:metadata>

            <p:growl id="msgs" />

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_satmob_contratosG.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Pedidos}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText value="#{pedidosMB.data1}" converter="conversorData" />
                                            <h:outputText value=" - "/>
                                            <h:outputText value="#{pedidosMB.data2}" converter="conversorData"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{pedidosMB.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{pedidosMB.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{pedidosMB.filiais.bairro}, #{pedidosMB.filiais.cidade}/#{pedidosMB.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="padding-top:6px !important; text-align: right !important">

                                    <!--Botão voltar-->
                                    <p:commandLink action="#{pedidosMB.dataAnterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 40px"/>
                                    </p:commandLink>

                                    <!--Calendário-->
                                    <p:commandLink
                                        id="calendar"
                                        oncomplete="PF('oCalendarios').show();"
                                        styleClass="botao"
                                        update="main cabecalho msgs"
                                        >
                                        <p:graphicImage
                                            url="../assets/img/icone_escaladodia.png"
                                            style="align-self: center;height: 40px"
                                            />
                                    </p:commandLink>

                                    <!--Botão avançar-->
                                    <p:commandLink action="#{pedidosMB.dataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 40px"/>
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2" style="padding:0px 10px 0px 0px !important; text-align: right !important">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" style="margin-top:-0px !important" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <!--Centro-->
                <h:form id="main">
                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:dataTable id="tabela"
                                     value="#{pedidosMB.pedidos}"
                                     var="lista"
                                     selectionMode="single"
                                     selection="#{pedidosMB.pedidoRefeicaoSelecionadoGride}"
                                     emptyMessage="#{localemsgs.SemRegistros}"
                                     scrollable="true"
                                     class="tabela DataGrid"
                                     reflow="true"
                                     style="font-size: 12px; background: #FFF"
                                     rowKey="#{lista.sequencia}"
                                     >
                                     <p:ajax event="rowDblselect" listener="#{pedidosMB.dblSelectRefeicao}" update="pedido msgs" />
                            <p:column headerText="#{localemsgs.Sequencia}" class="text-center">
                                <h:outputText value="#{lista.sequencia}" title="#{lista.sequencia}" converter="conversor0" class="text-center"></h:outputText>
                                <h:inputHidden value="#{lista.codFil}" converter="conversor0" class="text-center"></h:inputHidden>
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}" class="text-center">
                                <h:outputText value="#{lista.data}" title="#{lista.data}" converter="conversorData" class="text-center">
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Solicitante}" class="text-center">
                                <h:outputText value="#{lista.solicitante}" title="#{lista.solicitante}" class="text-center">
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Cliente}" class="text-center">
                                <h:outputText value="#{lista.nred}" title="#{lista.nred}" class="text-center">
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.OBS}" class="text-center">
                                <h:outputText value="#{lista.obs}" title="#{lista.obs}" class="text-center">
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.TotalCafe}" class="text-center">
                                <h:outputText value="#{lista.qtdeCafe}" title="#{lista.qtdeCafe}" converter="conversor0" class="text-center">
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.TotalAlmoco}" class="text-center">
                                <h:outputText value="#{lista.qtdeAlmoco}" title="#{lista.qtdeAlmoco}" converter="conversor0" class="text-center">
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.TotalJanta}" class="text-center">
                                <h:outputText value="#{lista.qtdeJantar}" title="#{lista.qtdeJantar}" converter="conversor0" class="text-center">
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.TotalCeia}" class="text-center">
                                <h:outputText value="#{lista.qtdeCeia}" title="#{lista.qtdeCeia}" converter="conversor0" class="text-center">
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.TotalGeral}" class="text-center">
                                <h:outputText value="#{lista.totalGeral}" title="#{lista.totalGeral}" converter="conversor0" class="text-center">
                                </h:outputText>
                            </p:column>
                            
                            <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                <h:outputText value="#{lista.operador}"  class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                <h:outputText value="#{lista.dt_Alter}" converter="conversorData"  class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                <h:outputText converter="conversorHora" value="#{lista.hr_Alter}"  class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Situacao}" class="text-center">
                                <h:outputText value="#{lista.situacao}"  class="text-center"/>
                            </p:column>

                        </p:dataTable>
                    </p:panel>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 50px !important; background: transparent; height:200px !important;" id="botoes">
                        <p:remoteCommand name="execExclusao" partialSubmit="true" 
                                process="@this" 
                                update="msgs main" 
                                actionListener="#{pedidosMB.excluirPedidoRefeicao()}" />    
                        
                        
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Novo}"
                                           actionListener="#{pedidosMB.preCadastroRefeicao}"
                                           oncomplete="PF('dlgPedido').show();"
                                           update="pedido tabela">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">    
                            <p:commandLink title="#{localemsgs.Editar}"
                                           actionListener="#{pedidosMB.preEdicaoRefeicao}"
                                           update="pedido">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">    
                            <p:commandLink title="#{localemsgs.Excluir}"
                                           actionListener="#{pedidosMB.preExclusaoRefeicao}"
                                           update="pedido">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                            </p:commandLink>
                        </div>
                        
                        <div style="padding-bottom: 10px">    
                            <p:commandLink title="#{localemsgs.Pedidos}/#{localemsgs.RotasValores}" rendered="#{login.pp.empresa eq 'SATMAXIMA'}" 
                                           action="pedidos.xhtml?faces-redirect=true">
                                <p:graphicImage url="../assets/img/icone_historico.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <h:form id="calendarios" style="overflow:hidden !important; top:0 !important; right:0 !important;bottom:0 !important;left:0 !important; margin:0 auto !important;">
                    <p:dialog
                        widgetVar="oCalendarios"
                        positionType="absolute"
                        draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true" position="center center"
                        showEffect="drop" hideEffect="drop" closeOnEscape="false" id="oCalendarios" onShow="PF('oCalendarios').initPosition()"
                        style="height:95% !important; max-height:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important; margin:0 auto !important; overflow:auto !important">

                        <div class="ui-grid-row ui-grid-responsive" style="overflow:hidden !important;">
                            <div class="ui-grid-col-6" style="padding-bottom:20px !important;overflow:hidden !important;">
                                <div class="ui-grid-row">
                                    <div style="width: 50%; float: left">
                                        <p:outputLabel for="call" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}" />
                                    </div>
                                    <div style="width: 50%; float: left">
                                        <p:inputText id="empty" style="width: 0px; z-index: -1; position: fixed" />
                                        <p:inputMask id="call" value="#{pedidosMB.d1}" mask="99/99/9999"
                                                     style="width: 85px" converter="conversorData" >
                                            <p:ajax event="blur" listener="#{pedidosMB.escreverData1()}" update="calendario1" />
                                        </p:inputMask>
                                    </div>
                                </div>
                                <p:calendar id="calendario1" styleClass="calendario" mode="inline"
                                            value="#{pedidosMB.dataSelecionada1}" title="#{localemsgs.DataInicial}"
                                            label="#{localemsgs.DataInicial}" pattern="yyyyMMdd"
                                            required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataInicial}"
                                            locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{pedidosMB.selecionarData1}" update="call cal2 calendario2" />
                                </p:calendar>
                            </div>
                            <div class="ui-grid-col-6" style="padding-bottom:20px !important;overflow:hidden !important;">
                                <div class="ui-grid-row">
                                    <div style="width: 50%; float: left">
                                        <p:outputLabel for="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}" />
                                    </div>
                                    <div style="width: 50%; float: left">
                                        <p:inputMask id="cal2" value="#{pedidosMB.d2}" mask="99/99/9999"
                                                     style="width: 85px" converter="conversorData" >
                                            <p:ajax event="blur" listener="#{pedidosMB.escreverData2()}" update="calendario2" />
                                        </p:inputMask>
                                    </div>
                                </div>
                                <p:calendar id="calendario2" styleClass="calendario" mode="inline"
                                            value="#{pedidosMB.dataSelecionada2}" title="#{localemsgs.DataFinal}"
                                            label="#{localemsgs.DataFinal}" pattern="yyyyMMdd"
                                            required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataFinal}"
                                            locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{pedidosMB.selecionarData2}" update="call cal2 calendario1" />
                                </p:calendar>
                            </div>
                            <div style="text-align: right; float: right">
                                <p:commandLink action="#{pedidosMB.selecionarData()}" style="float: right" oncomplete="PF('oCalendarios').hide()">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" height="40" />
                                </p:commandLink>
                            </div>
                        </div>

                        <script>
                            function ResizeWindow() {
                                $(window).resize();
                            }
                        </script>
                    </p:dialog>
                </h:form>

                <!-- Inserção -->
                <h:form id="pedido" class="form-inline" style="height:auto !important">
                    <p:dialog widgetVar="dlgPedido" id="telaPedido" positionType="absolute" responsive="true"
                              draggable="false" styleClass="dialogo" modal="true" closable="true" resizable="true"
                              dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="true" focus="data" appendTo="@(body)"
                              style="max-height:95% !important; min-width:70% !important; min-width: 400px; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: auto !important; border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; overflow:hidden !important"
                              >
                        <f:facet name="header">
                            <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Pedido}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent" class="cadastrar">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3, ui-grid-col-9" layout="grid">
                                <p:outputLabel for="data" value="(*)&nbsp;#{localemsgs.Data}:"/>
                                <p:calendar value="#{pedidosMB.pedidoRefeicaoSelecionado.data}" converter="conversorData" id="data" pattern="dd/MM/yyyy" class="calendariopedido" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"></p:calendar>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3, ui-grid-col-9" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="solicitante" value="#{localemsgs.Solicitante}:"/>
                                <p:inputText id="solicitante" value="#{pedidosMB.pedidoRefeicaoSelecionado.solicitante}" style="width: 100%" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Solicitante}"></p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3, ui-grid-col-9" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="obs" value="#{localemsgs.Obs}:"/>
                                <p:inputTextarea id="obs" value="#{pedidosMB.pedidoRefeicaoSelecionado.obs}" label="#{localemsgs.Obs}" style="width: 100%" rows="3" maxlength="80" required="false"></p:inputTextarea>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3, ui-grid-col-9" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="cliente" value="#{localemsgs.Cliente}:"/>
                                <p:autoComplete id="cliente" value="#{pedidosMB.clienteDestino}" styleClass="cliente"
                                                style="width: 100%"
                                                completeMethod="#{pessoa.ListarClientes}" required="true" scrollHeight="200"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}" forceSelection="true"
                                                var="cli" itemLabel="#{cli.NRed}" itemValue="#{cli}" converter="conversorCliente">
                                    <p:ajax event="itemSelect" listener="#{pedidosMB.selecionarDestRefeicao}" update="pnlNovoPedido msgs" />
                                </p:autoComplete>
                            </p:panelGrid>

                            <p:panel id="pnlNovoPedido" style="width:100% !important; height:290px !important; margin: 0px !important; padding:0px !Important" >
                                <div style="max-height:320px !important; padding: 0px !important; overflow: auto">
                                    <p:dataTable
                                        id="tabelaNovoPedido"
                                        value="#{pedidosMB.listaPedidoRefeicao}"
                                        rowKey="#{lista.secao}"
                                        paginator="false"
                                        paginatorTemplate="false"
                                        lazy="true"
                                        reflow="true"
                                        var="lista"
                                        styleClass="tabela"
                                        selectionMode="single"
                                        disabledSelection="true"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        scrollable="true"
                                        class="tabela DataGrid"
                                        scrollWidth="100%"
                                        sortBy="#{lista.NRed1}" 
                                        style="display: flex; flex-direction: column; font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                        >
                                        <!--<p:ajax event="rowDblselect" listener="#{pedidosMB.dblSelect}" update="formDetalhes msgs" />-->

                                        <p:column headerText="#{localemsgs.BlocoAla}" class="text-center">
                                            <h:outputText value="#{lista.cli2Nred}" title="#{lista.tipo}" class="text-center"></h:outputText>
                                            <h:inputHidden value="#{lista.secao}"></h:inputHidden>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CafeManha}" class="text-center">
                                            <p:inputNumber value="#{lista.qtdeCafe}" decimalPlaces="0" style="text-align:center !important;" class="text-center" maxlength="4"></p:inputNumber>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Almoco}" class="text-center">
                                            <p:inputNumber value="#{lista.qtdeAlmoco}" decimalPlaces="0" style="text-align:center !important" class="text-center" maxlength="4"></p:inputNumber>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Janta}"  class="text-center">
                                            <p:inputNumber value="#{lista.qtdeJanta}" decimalPlaces="0" style="text-align:center !important" class="text-center" maxlength="4"></p:inputNumber>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Ceia}"  class="text-center">
                                            <p:inputNumber value="#{lista.qtdeCeia}" decimalPlaces="0" style="text-align:center !important" class="text-center" maxlength="4"></p:inputNumber>
                                        </p:column>
                                        <p:summaryRow>
                                            <p:column style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{localemsgs.TotalRefeicao}" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="0" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="0" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="0" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="0" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                        </p:summaryRow>
                                    </p:dataTable>
                                    <div style="width: 100%; background-color: #DDD; height: 34px; font-weight: bold; font-size: 10pt !important; padding-top:6px !important; margin-bottom: 6px !important; color: #404040 !important; margin-top: 0px !Important">
                                        <label style="width: 170px !important; float:left; text-align: right !important; text-transform: uppercase;font-size: 10pt !important; margin-top: 1px">
                                            <b>#{localemsgs.TotalGeral}</b>
                                        </label>    
                                        <label id="lblTotalGeral" style="width: calc(100% - 170px); text-align: center; float:left; font-size: 12pt !important;">
                                            0
                                        </label>    
                                    </div>
                                </div>
                            </p:panel>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; padding: 0px 0px 0px 0px !important;">
                                <p:commandLink id="btSalvar" action="#{pedidosMB.cadastrarPedidoMaxima}" update="main:tabela msgs"
                                               partialSubmit="true"  process="@this,data,solicitante,obs,cliente,pnlNovoPedido"
                                               styleClass="btn btn-primary" style="margin-top: 20px !important">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>    
                            </div>
                        </p:panel>
                    </p:dialog>
                    <script type="text/javascript">

                        $(document)
                                .on('focus', '[id*="pnlNovoPedido"] [id*="tabela"] tbody tr:not(.ui-datatable-summaryrow) td input', function () {
                                    $(this).select();
                                })
                                .on('change', '[id*="pnlNovoPedido"] [id*="tabela"] tbody tr:not(.ui-datatable-summaryrow) td input', function () {
                                    CalcularTotais();
                                })
                                ;

                        function CalcularTotais() {
                            let SomaGeral = 0;
                            $('.ui-datatable-summaryrow').find('td:not(:first-child)').each(function () {
                                let SomaTotal = 0;

                                $('[id*="pnlNovoPedido"] [id*="tabela"] tbody tr:not(.ui-datatable-summaryrow) td:nth-child(' + ($(this).index() + 1) + ')').each(function () {
                                    if ($(this).find('input[type="hidden"]').val().trim() != '')
                                        SomaTotal += eval($(this).find('input[type="hidden"]').val());
                                });

                                $(this).find('span:not(.ui-column-title)').text(SomaTotal.toString());
                                SomaGeral += SomaTotal;
                            });

                            $('#lblTotalGeral').html('<b>' + SomaGeral.toString() + '</b>');
                        }
                    </script>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <!--Rodapé-->
            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <h:outputText value="#{localemsgs.Corporativo}: " />
                                <p:selectBooleanCheckbox value="#{pedidosMB.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{pedidosMB.mostrarFiliais()}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>

    </f:view>
</html>

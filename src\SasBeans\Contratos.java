package SasBeans;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class Contratos {

    private String Contrato;
    private String CodFil;
    private String Descricao;
    private String Tipo;
    private String Situacao;
    private String SituacaoDesc;
    private String Identif;
    private String Validade;
    private String Dt_Inicio;
    private String Dt_Termino;
    private String CliFat;
    private String ContratoCli;
    private String RefArq;
    private String GrpReajuste;
    private String GrpPagamento;
    private String OBS;
    private String Assinatura;
    private String Processo;
    private String OperIncl;
    private String Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    // Info Cliente
    private String NRed;
    private String Nome;
    private String Banco;
    private String Endereco;
    private String Cidade;
    private String Estado;
    private String CEP;
    private String Fone1;
    private String Email;
    private String Contato;
    private String CNPJ;

    public Contratos() {
        Contrato = "";
        CodFil = "";
        Descricao = "";
        Tipo = "";
        Situacao = "";
        SituacaoDesc = "";
        Identif = "";
        Validade = "";
        Dt_Inicio = "";
        Dt_Termino = "";
        CliFat = "";
        ContratoCli = "";
        RefArq = "";
        GrpReajuste = "";
        GrpPagamento = "";
        OBS = "";
        Assinatura = "";
        Processo = "";
        OperIncl = "";
        Dt_Incl = "";
        Hr_Incl = "";
        Operador = "";
        Dt_Alter = "";
        Hr_Alter = "";
        NRed = "";
        Nome = "";
        Banco = "";
        Endereco = "";
        Cidade = "";
        Estado = "";
        CEP = "";
        Fone1 = "";
        Email = "";
        Contato = "";
        CNPJ = "";
    }

    public Contratos(Contratos original) {
        Contrato = original.getContrato();
        CodFil = original.getCodFil();
        Descricao = original.getDescricao();
        Tipo = original.getTipo();
        Situacao = original.getSituacao();
        SituacaoDesc = original.getSituacaoDesc();
        Identif = original.getIdentif();
        Validade = original.getValidade();
        Dt_Inicio = original.getDt_Inicio();
        Dt_Termino = original.getDt_Termino();
        CliFat = original.getCliFat();
        ContratoCli = original.getContratoCli();
        RefArq = original.getRefArq();
        GrpReajuste = original.getGrpReajuste();
        GrpPagamento = original.getGrpPagamento();
        OBS = original.getOBS();
        Assinatura = original.getAssinatura();
        Processo = original.getProcesso();
        OperIncl = original.getOperIncl();
        Dt_Incl = original.getDt_Incl();
        Hr_Incl = original.getHr_Incl();
        Operador = original.getOperador();
        Dt_Alter = original.getDt_Alter();
        Hr_Alter = original.getHr_Alter();
        NRed = original.getNRed();
        Nome = original.getNome();
        Banco = original.getBanco();
        Endereco = original.getEndereco();
        Cidade = original.getCidade();
        Estado = original.getEstado();
        CEP = original.getCEP();
        Fone1 = original.getFone1();
        Email = original.getEmail();
        Contato = original.getContato();
        CNPJ = original.getCNPJ();
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getSituacaoDesc() {
        return SituacaoDesc;
    }

    public void setSituacaoDesc(String SituacaoDesc) {
        this.SituacaoDesc = SituacaoDesc;
    }

    public String getIdentif() {
        return Identif;
    }

    public void setIdentif(String Identif) {
        this.Identif = Identif;
    }

    public String getValidade() {
        return Validade;
    }

    public void setValidade(String Validade) {
        this.Validade = Validade;
    }

    public String getDt_Inicio() {
        return Dt_Inicio;
    }

    public void setDt_Inicio(String Dt_Inicio) {
        this.Dt_Inicio = Dt_Inicio;
    }

    public String getDt_Termino() {
        return Dt_Termino;
    }

    public void setDt_Termino(String Dt_Termino) {
        this.Dt_Termino = Dt_Termino;
    }

    public String getCliFat() {
        return CliFat;
    }

    public void setCliFat(String CliFat) {
        this.CliFat = CliFat;
    }

    public String getContratoCli() {
        return ContratoCli;
    }

    public void setContratoCli(String ContratoCli) {
        this.ContratoCli = ContratoCli;
    }

    public String getRefArq() {
        return RefArq;
    }

    public void setRefArq(String RefArq) {
        this.RefArq = RefArq;
    }

    public String getGrpReajuste() {
        return GrpReajuste;
    }

    public void setGrpReajuste(String GrpReajuste) {
        this.GrpReajuste = GrpReajuste;
    }

    public String getGrpPagamento() {
        return GrpPagamento;
    }

    public void setGrpPagamento(String GrpPagamento) {
        this.GrpPagamento = GrpPagamento;
    }

    public String getOBS() {
        return OBS;
    }

    public void setOBS(String OBS) {
        this.OBS = OBS;
    }

    public String getAssinatura() {
        return Assinatura;
    }

    public void setAssinatura(String Assinatura) {
        this.Assinatura = Assinatura;
    }

    public String getProcesso() {
        return Processo;
    }

    public void setProcesso(String Processo) {
        this.Processo = Processo;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public String getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getEstado() {
        return Estado;
    }

    public void setEstado(String Estado) {
        this.Estado = Estado;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getFone1() {
        return Fone1;
    }

    public void setFone1(String Fone1) {
        this.Fone1 = Fone1;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getContato() {
        return Contato;
    }

    public void setContato(String Contato) {
        this.Contato = Contato;
    }

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public boolean isVencido() {
        try {
            return LocalDate.parse(this.Dt_Termino.split(" ")[0], DateTimeFormatter.ISO_DATE).compareTo(LocalDate.now()) < 0;
        } catch (Exception e) {
            return false;
        }
    }
}

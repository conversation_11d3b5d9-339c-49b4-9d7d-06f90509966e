package br.com.sasw.esocial;

import br.com.sasw.utils.XML;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.security.KeyStore;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;

public class ComunicadorReinf {

    public static String URL_LOTE_ENVIO_PRODUCAO
            = "https://reinf.receita.economia.gov.br/recepcao/lotes";
    public static String URL_LOTE_ENVIO_HOMOLOGACAO
            = "https://pre-reinf.receita.economia.gov.br/recepcao/lotes";
    public static String URL_LOTE_CONSULTA_PRODUCAO
            = "https://reinf.receita.economia.gov.br/consulta/lotes/";
    public static String URL_LOTE_CONSULTA_HOMOLOGACAO
            = "https://pre-reinf.receita.economia.gov.br/consulta/lotes/";

    public static String METODO_POST = "POST";
    public static String METODO_GET = "GET";

    public static String enviarLote(String URL, String corpo, InputStream certificado, String senhaCertificado) {
        String resultado = enviarRequisicao(URL, corpo, METODO_POST, certificado,
                senhaCertificado);
        return resultado;
    }

    public static String consultarLote(String URL, String xmlRetorno,
            InputStream certificado, String senhaCertificado) {
        String protocolo = "";
        try {
            protocolo = XML.buscarTag("protocoloEnvio", 0, xmlRetorno);
        } catch (Exception ex) {
            Logger.getLogger(ComunicadorReinf.class.getName()).log(Level.SEVERE, null, ex);
        }
        String resultado = enviarRequisicao(URL + protocolo, "", METODO_GET,
                certificado, senhaCertificado);
        return resultado;
    }

    public static String enviarRequisicao(String urlStr, String entrada,
            String metodo, InputStream certificado, String senhaCertificado) {
        try {
            URL url = new URL(urlStr);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();

            // Carrega o certificado PFX
            KeyStore clientStore = KeyStore.getInstance("PKCS12");
            clientStore.load(certificado, senhaCertificado.toCharArray());

            // Cria o KeyManagerFactory
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(
                    KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(clientStore, senhaCertificado.toCharArray());

            // Cria o SSLContext
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(kmf.getKeyManagers(), null, null);

            // Configura a conexão HTTPS
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            connection.setRequestMethod(metodo);
            connection.setRequestProperty("Content-Type", "application/xml"); 
            connection.setDoOutput(true);

            if (entrada.length() > 0) {
                // Envia o corpo da requisição
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = entrada.getBytes("utf-8");
                    os.write(input, 0, input.length);
                }
            }

            // Obtém a resposta
            //int responseCode = connection.getResponseCode();

            try (InputStream is = connection.getInputStream()) {
                // Processa a resposta 
                java.util.Scanner s = new java.util.Scanner(is).useDelimiter("\\A");
                String resultado = s.hasNext() ? s.next() : "";
                return resultado;
            } catch (IOException e) {
                // Se houver um erro ao obter o InputStream (ex: 4xx ou 5xx)
                try (InputStream es = connection.getErrorStream()) {
                    if (es != null) {
                        java.util.Scanner s = new java.util.Scanner(es).
                                useDelimiter("\\A");
                        String errorResult = s.hasNext() ? s.next() : "";
                        throw new Exception("Erro na resposta: " + errorResult);
                    } else {
                        throw new Exception("Erro na resposta, mas ErrorStream está vazio.");
                    }
                }
            } finally {
                connection.disconnect();
            }
        } catch (Exception ex) {
            Logger.getLogger(ComunicadorReinf.class.getName()).log(Level.SEVERE,
                    null, ex);
        }
        return "";
    }
}

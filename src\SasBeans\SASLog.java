/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class SASLog {

    private String Sequencia;
    private String Tabela;
    private BigDecimal Codigo;
    private BigDecimal CodFil;
    private String Comando;
    private String Historico;
    private String Operador;
    private String Data;
    private String Hora;

    /**
     * @return the Sequencia
     */
    public String getSequencia() {
        return Sequencia;
    }

    /**
     * @param Sequencia the Sequencia to set
     */
    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    /**
     * @return the Tabela
     */
    public String getTabela() {
        return Tabela;
    }

    /**
     * @param Tabela the Tabela to set
     */
    public void setTabela(String Tabela) {
        this.Tabela = Tabela;
    }

    /**
     * @return the Codigo
     */
    public BigDecimal getCodigo() {
        return Codigo;
    }

    /**
     * @param Codigo the Codigo to set
     */
    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Comando
     */
    public String getComando() {
        return Comando;
    }

    /**
     * @param Comando the Comando to set
     */
    public void setComando(String Comando) {
        this.Comando = Comando;
    }

    /**
     * @return the Historico
     */
    public String getHistorico() {
        return Historico;
    }

    /**
     * @param Historico the Historico to set
     */
    public void setHistorico(String Historico) {
        this.Historico = Historico;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Data
     */
    public String getData() {
        return Data;
    }

    /**
     * @param Data the Data to set
     */
    public void setData(String Data) {
        this.Data = Data;
    }

    /**
     * @return the Hora
     */
    public String getHora() {
        return Hora;
    }

    /**
     * @param Hora the Hora to set
     */
    public void setHora(String Hora) {
        this.Hora = Hora;
    }

}

package SasBeansCompostas;

import SasBeans.Clientes;
import SasBeans.PstServ;
import SasBeans.TmktDetPst;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TmktDetPstPstServClientes {

    public TmktDetPst tmktdetpst;
    public PstServ pstserv;
    public Clientes clientes;
    public Double distsup;
    public List<String> fotos;
    public Integer qtdEntrevistas;
    public String funcionario;

    public TmktDetPstPstServClientes() {
        this.tmktdetpst = new TmktDetPst();
        this.pstserv = new PstServ();
        this.clientes = new Clientes();
        this.distsup = 0.0;
        this.fotos = new ArrayList<>();
        this.qtdEntrevistas = 0;
    }

    public TmktDetPst getTmktdetpst() {
        return tmktdetpst;
    }

    public void setTmktdetpst(TmktDetPst tmktdetpst) {
        this.tmktdetpst = tmktdetpst;
    }

    public PstServ getPstserv() {
        return pstserv;
    }

    public void setPstserv(PstServ pstserv) {
        this.pstserv = pstserv;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    public Double getDistsup() {
        return distsup;
    }

    public void setDistsup(Double distsup) {
        this.distsup = distsup;
    }

    public List<String> getFotos() {
        return fotos;
    }

    public void setFotos(List<String> fotos) {
        this.fotos = fotos;
    }

    public Integer getQtdEntrevistas() {
        return qtdEntrevistas;
    }

    public void setQtdEntrevistas(Integer qtdEntrevistas) {
        this.qtdEntrevistas = qtdEntrevistas;
    }

    public String getFuncionario() {
        return funcionario;
    }

    public void setFuncionario(String funcionario) {
        this.funcionario = funcionario;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1207 {

    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_indApuracao;
    private String ideEvento_perApur;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;

    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideBenef_cpfBenef;

    private List<DmDev> dmDev;

    public static class DmDev {

        private String dmDev_tpBenef;
        private String dmDev_nrBenefic;
        private String dmDev_ideDmDev;
        private List<Itens> itens;

        public String getDmDev_tpBenef() {
            return dmDev_tpBenef;
        }

        public void setDmDev_tpBenef(String dmDev_tpBenef) {
            this.dmDev_tpBenef = dmDev_tpBenef;
        }

        public String getDmDev_nrBenefic() {
            return dmDev_nrBenefic;
        }

        public void setDmDev_nrBenefic(String dmDev_nrBenefic) {
            this.dmDev_nrBenefic = dmDev_nrBenefic;
        }

        public String getDmDev_ideDmDev() {
            return dmDev_ideDmDev;
        }

        public void setDmDev_ideDmDev(String dmDev_ideDmDev) {
            this.dmDev_ideDmDev = dmDev_ideDmDev;
        }

        public List<Itens> getItens() {
            return itens;
        }

        public void setItens(List<Itens> itens) {
            this.itens = itens;
        }
    }

    public static class Itens {

        private String itens_codRubr;
        private String itens_ideTabRubr;
        private String itens_vrRubr;

        public String getItens_codRubr() {
            return itens_codRubr;
        }

        public void setItens_codRubr(String itens_codRubr) {
            this.itens_codRubr = itens_codRubr;
        }

        public String getItens_ideTabRubr() {
            return itens_ideTabRubr;
        }

        public void setItens_ideTabRubr(String itens_ideTabRubr) {
            this.itens_ideTabRubr = itens_ideTabRubr;
        }

        public String getItens_vrRubr() {
            return itens_vrRubr;
        }

        public void setItens_vrRubr(String itens_vrRubr) {
            this.itens_vrRubr = itens_vrRubr;
        }
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_indApuracao() {
        return ideEvento_indApuracao;
    }

    public void setIdeEvento_indApuracao(String ideEvento_indApuracao) {
        this.ideEvento_indApuracao = ideEvento_indApuracao;
    }

    public String getIdeEvento_perApur() {
        return ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeBenef_cpfBenef() {
        return ideBenef_cpfBenef;
    }

    public void setIdeBenef_cpfBenef(String ideBenef_cpfBenef) {
        this.ideBenef_cpfBenef = ideBenef_cpfBenef;
    }

    public List<DmDev> getDmDev() {
        return dmDev;
    }

    public void setDmDev(List<DmDev> dmDev) {
        this.dmDev = dmDev;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>

            <style>
                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    .tabela .ui-datatable-scrollable-body{
                        flex-grow: 1;

                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }
                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }


                #body {
                    height: calc(100% - 40px);
                    position: relative;
                    display: flex;
                    flex-direction: column;
                }

                #main {
                    flex-grow: 1;
                }

                #formPesquisar .ui-radiobutton {
                    background: transparent !important;
                }

                .ui-datatable-scrollable-body,
                .FundoPagina > .ui-panel-content {
                    height: 100% !important;
                }

                .FundoPagina {
                    border: thin solid #CCC !important;
                    border-top:4px solid #3C8DBC !important;
                }

                [id*="tabGeral"],
                [id*="tabDocumentos"]{
                    background-color:#FFF !important;
                    padding-right: 20px!important;
                }
            </style>
        </h:head>

        <h:body id="h" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{nfiscal.Persistencias(login.pp)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;"
                                     >
                                    <img src="../assets/img/icone_notafiscal_g.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.NFiscais}</label>
                                </div>

                                <div class="ui-grid-col-4" style="align-self: center; text-align: center; font-size: 12px;">
                                    <h:outputText value="#{localemsgs.Periodo}: "/>
                                    <h:outputText value="#{nfiscal.data2}" converter="conversorData" />
                                    <h:outputText value=" - "/>
                                    <h:outputText value="#{nfiscal.data1}" converter="conversorData"/>
                                </div>

                                <div class="ui-grid-col-3" style="align-self: center; text-align: center;">
                                    <p:commandLink action="#{supervisao.DataAnterior}"  update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px"/>
                                    </p:commandLink>

                                    <p:commandLink id="calendar" oncomplete="PF('oCalendarios').loadContents();"
                                                   styleClass="botao" update="cabecalho">
                                        <p:graphicImage url="../assets/img/icone_escaladodia.png" style="align-self: center;height: 40px"/>
                                    </p:commandLink>

                                    <p:commandLink action="#{supervisao.DataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px"/>
                                    </p:commandLink>
                                </div>

                                <div id="divDadosFilial"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="text-align: center !important;"
                                     >
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{nfiscal.filiais.descricao}
                                            <label id="btTrocarFilial"
                                                   onclick="top.location.href = '../param.xhtml'"
                                                   >
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco">
                                            #{nfiscal.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{nfiscal.filiais.bairro}, #{nfiscal.filiais.cidade}/#{nfiscal.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3">
                                    <p:commandLink title="#{localemsgs.Voltar}" onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:dataTable
                            id="tabela"
                            value="#{nfiscal.allNFiscal}"
                            selection="#{nfiscal.selecionado}"
                            rowStyleClass="#{lista.situacao eq 'C' ? 'cancelada' : (lista.dt_audit ne null ? 'emitida' : 'nao-emitida')}"
                            paginator="true"
                            rows="15"
                            lazy="true"
                            reflow="true"
                            rowsPerPageTemplate="5,10,15, 20, 25"
                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.NFiscais}"
                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                            var="lista"
                            styleClass="tabela"
                            selectionMode="single"
                            emptyMessage="#{localemsgs.SemRegistros}"
                            scrollable="true"
                            class="DataGrid"
                            scrollWidth="100%"
                            style="display: flex; flex-direction: column; font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                            >
                            <p:ajax event="rowDblselect" oncomplete="PF('dlgUpload').show();" update="formUpload msgs"/>
                            <p:column headerText="#{localemsgs.CodFil}" class="celula-right" style="width: 50px">
                                <h:outputText value="#{lista.codFil}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Serie}" style="width: 60px">
                                <h:outputText value="#{lista.serie}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Praca}" style="width: 60px">
                                <h:outputText value="#{lista.praca}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.NRedPraca}" style="width: 200px">
                                <h:outputText value="#{lista.NRedPraca}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Numero}" style="width: 80px" class="text-right">
                                <h:outputText value="#{lista.numero}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Valor}" style="width: 200px" class="text-right">
                                <h:outputText value="#{lista.valor}" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}" style="width: 80px">
                                <h:outputText value="#{lista.data}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.RazaoSocial}">
                                <h:outputText value="#{lista.NRed}"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 80px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.pdf}"
                                           actionListener="#{nfiscal.gerarDownloadDanfe}"
                                           ajax="false"
                                           update="msgs">
                                <p:fileDownload value="#{nfiscal.arquivoDownload}" />
                                <p:graphicImage url="../assets/img/icone_pdf.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.xml}" actionListener="#{nfiscal.gerarDownloadNfe}"
                                           ajax="false"
                                           update="msgs">
                                <p:fileDownload value="#{nfiscal.arquivoDownload}" />
                                <p:graphicImage url="../assets/img/icone_xml.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" action="#{nfiscal.prePesquisa}"
                                           update="formPesquisar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{nfiscal.limpaFiltros}"
                                           update=":main:tabela :msgs :cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>

                    <p:overlayPanel
                        id="calendarios"
                        for="cabecalho:calendar"
                        hideEffect="fade"
                        dynamic="true"
                        dismissable="false"
                        style="font-size: 14px;"
                        widgetVar="oCalendarios"
                        my="top"
                        at="bottom"
                        class="overlay"
                        >
                        <div class="ui-grid-row ui-grid-responsive">
                            <div class="ui-grid-col-6">
                                <div class="ui-grid-row" style="margin: 5px">
                                    <h:outputText id="cal1" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}"/>
                                </div>
                                <div class="ui-grid-row" style="margin: 5px">
                                    <p:calendar id="calendario1" styleClass="calendario"
                                                value="#{nfiscal.dataSelecionada2}" mask="true"
                                                title="#{localemsgs.DataInicial}" label="#{localemsgs.DataInicial}"
                                                pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"
                                                />
                                </div>
                            </div>

                            <div class="ui-grid-col-6">
                                <div class="ui-grid-row" style="margin: 5px">
                                    <h:outputText id="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}"/>
                                </div>
                                <div class="ui-grid-row" style="margin: 5px">
                                    <p:calendar id="calendario2" styleClass="calendario"
                                                value="#{nfiscal.dataSelecionada1}" mask="true"
                                                title="#{localemsgs.DataFinal}" label="#{localemsgs.DataFinal}"
                                                pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: right; float: right">
                            <p:commandLink action="#{nfiscal.selecionarData}" style="float: right"
                                           update="main msgs cabecalho">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" height="40" />
                            </p:commandLink>
                        </div>
                    </p:overlayPanel>
                </h:form>

                <h:form class="form-inline" id="formUpload">
                    <p:dialog widgetVar="dlgUpload" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                              style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.UploadNFiscal}" style="color:#022a48;" />
                        </f:facet>
                        <p:panel id="panelUpload" style="background-color: transparent" styleClass="cadastrarArquivo">

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-4,ui-grid-col-2,ui-grid-col-4,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="codFil" value="#{localemsgs.CodFil}: "/>
                                <p:inputText id="codFil" value="#{nfiscal.selecionado.codFil.toBigInteger().toString()}" style="width: 100%; text-align: right" readonly="true"/>

                                <p:outputLabel for="serie" value="#{localemsgs.Serie}: "/>
                                <p:inputText id="serie" value="#{nfiscal.selecionado.serie}" style="width: 100%; text-align: right" readonly="true"/>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-4,ui-grid-col-2,ui-grid-col-2,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="praca" value="#{localemsgs.Praca}: "/>
                                <p:inputText id="praca" value="#{nfiscal.selecionado.praca}" style="width: 100%; text-align: right" readonly="true"/>

                                <p:outputLabel for="numero" value="#{localemsgs.Numero}: "/>
                                <p:inputText id="numero" value="#{nfiscal.selecionado.numero}" style="width: 100%; text-align: right" readonly="true"/>
                            </p:panelGrid>

                            <p:fileUpload id="espacoUpload" auto="true" skinSimple="true" label="#{localemsgs.SelecioneArquivo}"
                                          update="msgs"
                                          onstart="PF('pfBlock').show();" oncomplete="PF('dlgUpload').initPosition();PF('pfBlock').hide();"
                                          class="upload" multiple="true" dragDropSupport="true"
                                          fileUploadListener="#{nfiscal.realizarUpload}" mode="advanced"
                                          allowTypes="/(\.|\/)(xml)$/" previewWidth="10" invalidFileMessage="#{localemsgs.ArquivoInvalido}">
                            </p:fileUpload>

                            <p:commandLink oncomplete="PF('dlgUpload').hide();"
                                           title="#{localemsgs.Enviar}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisar NFiscal-->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true" focus="serie"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_notafiscal_g.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarNFiscal}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <h:outputText id="cal1" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}"/>
                                </div>
                                <div style="width: 25%; float: left">
                                    <p:calendar id="calendario1" styleClass="calendario"
                                                value="#{nfiscal.dataSelecionada2}" mask="true"
                                                title="#{localemsgs.DataInicial}" label="#{localemsgs.DataInicial}"
                                                pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                                </div>
                                <div style="width: 25%; float: left; padding-left: 3px">
                                    <h:outputText id="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}"/>
                                </div>
                                <div style="width: 25%; float: left">
                                    <p:calendar id="calendario2" styleClass="calendario"
                                                value="#{nfiscal.dataSelecionada1}" mask="true"
                                                title="#{localemsgs.DataFinal}" label="#{localemsgs.DataFinal}"
                                                pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="filial" value="#{localemsgs.Filial}:"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="filial" value="#{nfiscal.filial}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains" style="width: 100%" label="">
                                        <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                                       itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="serie" value="#{localemsgs.Serie}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="serie" value="#{nfiscal.selecionado.serie}" label="#{localemsgs.Serie}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="serie" value="#{localemsgs.Serie}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="praca" value="#{localemsgs.Praca}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="praca" value="#{nfiscal.selecionado.praca}" label="#{localemsgs.Praca}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="praca" value="#{localemsgs.Praca}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="numero" value="#{localemsgs.Numero}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="numero" value="#{nfiscal.selecionado.numero}" label="#{localemsgs.Numero}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="numero" value="#{localemsgs.Numero}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="situacao" value="#{localemsgs.Situacao}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneRadio id="situacao" value="#{nfiscal.selecionado.situacao}" unselectable="true" style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Ativa}" itemValue="A" />
                                        <f:selectItem itemLabel="#{localemsgs.Cancelada}" itemValue="C" />
                                    </p:selectOneRadio>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="cliente" value="#{localemsgs.Cliente}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:autoComplete id="cliente" value="#{nfiscal.cliente}" completeMethod="#{nfiscal.buscarClientes}"
                                                    label="#{localemsgs.ClienteContratante}" forceSelection="true"  styleClass="cidade"
                                                    style="width: 100%" minQueryLength="3" scrollHeight="200"
                                                    var="cont" itemValue="#{cont}" itemLabel="#{cont.NRed}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{nfiscal.clientes}" />
                                    </p:autoComplete>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{nfiscal.pesquisar}"
                                               update=":msgs :cabecalho corporativo"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <h:outputText value="#{localemsgs.Corporativo}: " />
                                <p:selectBooleanCheckbox value="#{nfiscal.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{nfiscal.mostraFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <h:outputText value="#{localemsgs.SomenteAtivos}: " />
                                <p:selectBooleanCheckbox value="#{nfiscal.mostrarAtivos}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{nfiscal.mostraAtivos}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

/*
 */
package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Controller.ContraCheque.ContraChequeSatWeb;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.FPMensal;
import SasBeans.FPPeriodos;
import SasBeans.SASLog;
import SasBeansCompostas.ContraCheque;
import SasDaos.ContraChequeDao;
import br.com.sasw.arquivos.PDF;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.TabChangeEvent;
import java.util.Base64;

/**
 *
 * <AUTHOR>
 */
@Named(value = "contracheque")
@ViewScoped
public class ContrachequeMB implements Serializable {

    private Persistencia persistencia;
    private List<FPPeriodos> fpperiodos;
    private List<FPMensal> fpmensais;
    private List<ContraCheque> contracheque, listLogsContraCheques;
    private ContraChequeSatWeb contrachequemobweb;
    private Date periodo, periodoIni, periodoFim;
    private BigDecimal codPessoa;
    private String codFil, caminho, banco, matricula, empresa, log, operador, base64Assinatura, secao, nomeColaborador;
    private ArquivoLog logerro;
    private FPMensal fpContraCheque;
    private ContraChequeDao contraChequeDao;
    private Boolean corporativo;
    private Persistencia persistenciaLocal, persistenciaCentral;
    private ContraCheque logSelecionado;
    private String chavePesquisa = "MATR", valorPesquisa = "";
    private List<SASLog> logList;

    public ContrachequeMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        try {
            empresa = ((String) fc.getExternalContext().getSessionMap().get("empresa")).toUpperCase();
        } catch (Exception e) {
            empresa = (String) fc.getExternalContext().getSessionMap().get("empresa");
        }
        matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        try {
            matricula = matricula.replace(".0", "");
        } catch (Exception e) {
            matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        }
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\Contracheques\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        log = new String();
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        logerro = new ArquivoLog();
        base64Assinatura = "";

        periodo = Calendar.getInstance().getTime();

        Date dt = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(dt);
        c.add(Calendar.DATE, -31);
        dt = c.getTime();

        periodoIni = dt;
        periodoFim = dt;
        contraChequeDao = new ContraChequeDao();
        corporativo = false;
        listLogsContraCheques = new ArrayList<>();
        logSelecionado = new ContraCheque();
        logList = new ArrayList<>();
    }

    public void Persistencias(Persistencia pstLocal, Persistencia pstCentral) {
        try {
            persistenciaCentral = pstCentral;
            if (null == persistenciaCentral) {
                throw new Exception("ImpossivelConectarSatellite");
            }
            persistenciaLocal = pstLocal;
            if (null == persistenciaLocal) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void buscaPeriodos() {
        try {
            this.fpperiodos = this.contrachequemobweb.getPeriodos(this.persistencia);
            if (this.fpperiodos.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemContracheques"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.periodo = new SimpleDateFormat("yyMM").parse(this.fpperiodos.get(0).getCodMovFP().toPlainString());
                this.fpmensais = this.contrachequemobweb.getContrachqeuesPeriodo(this.fpperiodos.get(0).getCodMovFP().toPlainString(),
                        this.fpperiodos.get(0).getDtInicioF(), this.matricula, this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgContracheque').show()");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void buscaPeriodo(SelectEvent compet) {
        try {
            this.periodo = (Date) compet.getObject();
            this.fpperiodos = new ArrayList<>();
            FPPeriodos fpp = new FPPeriodos();
            fpp.setDtInicioF(this.periodo.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
            fpp.setCodMovFP(new SimpleDateFormat("yyMM").format(this.periodo));
            this.fpperiodos.add(fpp);
            this.fpmensais = this.contrachequemobweb.getContrachqeuesPeriodo(this.fpperiodos.get(0).getCodMovFP().toPlainString(),
                    this.fpperiodos.get(0).getDtInicioF(), this.matricula, this.persistencia);
            PrimeFaces.current().ajax().update("contracheques:periodos");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void buscarFpmensais(TabChangeEvent event) {
        try {
            String sCodmovfp = event.getTab().getTitle().substring(5) + event.getTab().getTitle().substring(0, 2);
            this.fpmensais = this.contrachequemobweb.getContrachqeuesPeriodo(sCodmovfp, event.getTab().getTitle(), this.matricula, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private String guardarAssinatura(String imagem, String matr, String compet) throws FileNotFoundException, IOException {
        imagem = imagem.replace("data:image/png;base64,", "");
        //byte[] montar;
        //montar = new sun.misc.BASE64Decoder().decodeBuffer(imagem);
        // decode
        byte[] montar = Base64.getDecoder().decode(imagem);

        String caminho = "C:/xampp/htdocs/satellite/fotos/" + this.persistencia.getEmpresa() + "/SaslogPortal/" + matr + "/" + compet;
        File diretorio = new File(caminho);
        if (!diretorio.exists()) {
            diretorio.mkdirs();  // cria diretórios caso não estejam criados
        }

        String nomeArquivo = DataAtual.getDataAtual("SQL") + "_" + DataAtual.getDataAtual("HHMMSS") + ".png";
        String nome = caminho + "/" + nomeArquivo;

        FileOutputStream fos = new FileOutputStream(nome);
        fos.write(montar);
        FileDescriptor fd = fos.getFD();
        fos.flush();
        fd.sync();
        fos.close();

        return nome;
    }

    public void imprimirContrachequeDetExec(FPMensal contraChequeFP, boolean acessoLog) {
        try {
            FPMensal fp = contraChequeFP;

            String caminhoAssinatura = "";

            if (!acessoLog) {
                if (null != base64Assinatura && !base64Assinatura.equals("")) {
                    caminhoAssinatura = guardarAssinatura(base64Assinatura, fp.getMatr().toPlainString().replace(".0", ""), fp.getCodMovFP().toPlainString().replace(".0", ""));
                }
            } else if (null != contraChequeFP.getCaminhoAssinatura() && !contraChequeFP.getCaminhoAssinatura().equals("")) {
                caminhoAssinatura = contraChequeFP.getCaminhoAssinatura();
            }

            if (null == fp) {
                throw new Exception("CodigoInvalido");
            }
            PDF pdf;
            if (this.persistencia.getEmpresa().toUpperCase().equals("SATTRANSEXCEL")) {
                pdf = new PDF("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                        + this.banco + "\\Contracheques\\", fp.getCodFil().toBigInteger().toString(), fp.getMatr().toBigInteger() + ".pdf");
            } else {
                pdf = new PDF("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                        + this.banco + "\\Contracheques\\", fp.getMatr().toBigInteger() + ".pdf");
            }
            String composicao = fp.getCodMovFP().toPlainString().substring(2, 4) + "/20" + fp.getCodMovFP().toPlainString().substring(0, 2);
            this.contracheque = this.contrachequemobweb.getCabecalhoCC(fp.getCodMovFP().toPlainString(), fp.getMatr().toBigInteger().toString(), fp.getTipoFP(), this.persistencia);
            pdf.CabecalhoContraCheque(FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator),
                    this.persistencia.getEmpresa(), composicao, this.contracheque.get(0), fp.getTipoFP());
            pdf.CorpoContraChequeTitulo();

            this.contracheque = this.contrachequemobweb.getComposicaoCC(fp.getCodMovFP().toPlainString(), fp.getMatr().toBigInteger().toString(), fp.getTipoFP(), this.persistencia);
            BigDecimal Acumula_liquido = new BigDecimal("0.00");
            BigDecimal Acumula_desconto = new BigDecimal("0.00");

            for (ContraCheque cc : this.contracheque) {
                pdf.CorpoContraChequeLinhas(composicao, cc, 0);
                if (cc.getfPLancamentos().getTipo().equals("V")) {
                    Acumula_liquido = Acumula_liquido.add(cc.getfPLancamentos().getValorCalc());
                } else {
                    Acumula_desconto = Acumula_desconto.add(cc.getfPLancamentos().getValorCalc());
                }
            }
            pdf.CorpoContraChequeLinhas("", new ContraCheque(), 1);
            this.contracheque = this.contrachequemobweb.getBaseCC(fp.getCodMovFP().toPlainString(), fp.getMatr().toBigInteger().toString(), fp.getTipoFP(), this.persistencia);
            this.contracheque.get(0).getfPMensal().setProventos(Acumula_liquido.toString());
            this.contracheque.get(0).getfPMensal().setDescontos(Acumula_desconto.toString());
            this.contracheque.get(0).getfPMensal().setLiquido(Acumula_liquido.subtract(Acumula_desconto).toString());
            String codigo = fp.getMatr().toBigInteger().toString() + "." + fp.getCodFil().toBigInteger().toString()
                    + "." + fp.getCodMovFP().toPlainString().substring(0, 4) + "." + fp.getTipoFP() + "." + this.empresa + "." + composicao + ".VCC";
            pdf.RodapeContraCheque(this.contracheque.get(0), codigo, caminhoAssinatura);

            if (!acessoLog) {
                if (null != base64Assinatura && !base64Assinatura.equals("")) {
                    this.contrachequemobweb.geraLogContraCheque(fp.getMatr().toBigInteger().toString(), fp.getCodFil().toPlainString(), composicao, fp.getTipoFP(), caminhoAssinatura, this.persistencia);
                } else {
                    this.contrachequemobweb.geraLogContraCheque(fp.getMatr().toBigInteger().toString(), fp.getCodFil().toPlainString(), composicao, fp.getTipoFP(), this.persistencia);
                }
            }
            pdf.FechaPdf();
            pdf.service();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void imprimirContrachequeDet() {
        try {
            imprimirContrachequeDetExec(this.fpContraCheque, false);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void imprimirContracheque(FPMensal fp) {
        try {
            if (null == fp) {
                throw new Exception("CodigoInvalido");
            }

            this.fpContraCheque = fp;

            if (persistencia.getEmpresa().equals("SATG5ESTRELAS") || persistencia.getEmpresa().equals("SATINVLMT")) {
                PrimeFaces.current().executeScript("AbrirFormAssinatura()");
            } else {
                base64Assinatura = "";
                PrimeFaces.current().executeScript("ImprimeContraCheque()");
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void imprimirContraChequeLog() {
        imprimirContrachequeDetExec(this.logSelecionado.getfPMensal(), true);
    }

    public void dblSelectLog(SelectEvent event) {
        if (this.logSelecionado != null
                && this.logSelecionado.getFuncion() != null
                && this.logSelecionado.getfPMensal() != null) {
            this.logSelecionado.getfPMensal().setMatr(this.logSelecionado.getFuncion().getMatr().toPlainString().replace(".0", ""));
            this.logSelecionado.getfPMensal().setCodFil(this.logSelecionado.getFuncion().getCodFil().toPlainString().replace(".0", ""));
            PrimeFaces.current().executeScript("$('[id*=\"btImprimeContraCheque\"]').click()");
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void carregarLogList() throws Exception {
        if (this.logSelecionado != null && this.logSelecionado.getFuncion() != null && this.logSelecionado.getFuncion().getMatr() != null && this.logSelecionado.getFuncion().getMatr() != BigDecimal.ZERO) {
            this.logList = this.contraChequeDao.logMatr(this.logSelecionado.getFuncion().getMatr().toPlainString().replace(".0", ""), this.logSelecionado.getFuncion().getCodFil().toPlainString().replace(".0", ""), this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgLogs').show()");
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void carregaListLogsContraCheques() throws Exception {
        this.matricula = "";
        this.secao = "";
        this.nomeColaborador = "";

        if (null != this.valorPesquisa && !this.valorPesquisa.equals("")) {
            switch (this.chavePesquisa) {
                case "MATR":
                    this.matricula = this.valorPesquisa;
                    break;

                case "NOME":
                    this.nomeColaborador = this.valorPesquisa;
                    break;

                case "POSTO":
                    this.secao = this.valorPesquisa;
                    break;
            }
        }

        Format formatter = new SimpleDateFormat("yyyy-MM-dd");
        String[] splitCodIni = formatter.format(periodoIni).split("-");
        String[] splitCodFim = formatter.format(periodoFim).split("-");

        String codMovFPIni = splitCodIni[0].substring(2) + splitCodIni[1], codMovFPFim = splitCodFim[0].substring(2) + splitCodFim[1];
        this.logSelecionado = new ContraCheque();

        this.listLogsContraCheques = contraChequeDao.logContraChequeGride(codMovFPIni, codMovFPFim, !corporativo ? this.codFil : "", this.matricula, this.secao, this.nomeColaborador, this.persistenciaLocal);
    }

    public void setPersistencia(Persistencia persistencia, SasPoolPersistencia pool) {
        try {
            if (persistencia.getEmpresa().equals("SATCORPVS") || persistencia.getEmpresa().equals("SATCORPVSPE")) {
                this.contrachequemobweb = new ContraChequeSatWeb(pool.getConexao("CORPVSFOLHA"), this.codFil, this.matricula);
            } else {
                this.contrachequemobweb = new ContraChequeSatWeb();
            }
            this.persistencia = persistencia;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<FPPeriodos> getFpperiodos() {
        return fpperiodos;
    }

    public void setFpperiodos(List<FPPeriodos> fpperiodos) {
        this.fpperiodos = fpperiodos;
    }

    public List<FPMensal> getFpmensais() {
        return fpmensais;
    }

    public void setFpmensais(List<FPMensal> fpmensais) {
        this.fpmensais = fpmensais;
    }

    public Date getPeriodo() {
        return periodo;
    }

    public void setPeriodo(Date periodo) {
        this.periodo = periodo;
    }

    public String getBase64Assinatura() {
        return base64Assinatura;
    }

    public void setBase64Assinatura(String base64Assinatura) {
        this.base64Assinatura = base64Assinatura;
    }

    public Date getPeriodoIni() {
        return periodoIni;
    }

    public void setPeriodoIni(Date periodoIni) {
        this.periodoIni = periodoIni;
    }

    public Date getPeriodoFim() {
        return periodoFim;
    }

    public void setPeriodoFim(Date periodoFim) {
        this.periodoFim = periodoFim;
    }

    public Boolean getCorporativo() {
        return corporativo;
    }

    public void setCorporativo(Boolean corporativo) {
        this.corporativo = corporativo;
    }

    public List<ContraCheque> getListLogsContraCheques() {
        return listLogsContraCheques;
    }

    public void setListLogsContraCheques(List<ContraCheque> listLogsContraCheques) {
        this.listLogsContraCheques = listLogsContraCheques;
    }

    public ContraCheque getLogSelecionado() {
        return logSelecionado;
    }

    public void setLogSelecionado(ContraCheque logSelecionado) {
        this.logSelecionado = logSelecionado;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public List<SASLog> getLogList() {
        return logList;
    }

    public void setLogList(List<SASLog> logList) {
        this.logList = logList;
    }
}

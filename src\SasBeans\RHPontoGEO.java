package SasBeans;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class RHPontoGEO {

    private BigDecimal matr;

    private int batida;

    private String dtCompet;
    private String latitude;
    private String longitude;

    public RHPontoGEO() {
        matr = new BigDecimal("0");

        batida = 1;

        dtCompet = "";
        latitude = "";
        longitude = "";
    }

    public BigDecimal getMatr() {
        return matr;
    }

    public void setMatr(BigDecimal matr) {
        this.matr = matr;
    }

    public int getBatida() {
        return batida;
    }

    public void setBatida(int batida) {
        this.batida = batida;
    }

    public String getDtCompet() {
        return dtCompet;
    }

    public void setDtCompet(String dtCompet) {
        this.dtCompet = dtCompet;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

}

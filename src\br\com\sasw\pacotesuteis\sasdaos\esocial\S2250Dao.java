/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2250;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2250Dao {

    public List<S2250> get(String codFil, String compet, String ambiente, boolean inicio, Persistencia persistencia) throws Exception {
        try {
            List<S2250> retorno = new ArrayList<>();
            String sql = "Select \n"
                    + "'1' ideEvento_indRetif, \n"
                    + "'' ideEvento_nrRecibo, \n"
                    + "'1' ideEvento_procEmi, \n"
                    + "'Satellite eSocial' ideEvento_verProc, \n"
                    + "Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, \n"
                    + "Substring(Filiais.CNPJ,1,8) ideEmpregador_nrInsc,\n"
                    + "Funcion.CPF  ideVinculo_cpfTrab,\n"
                    + "Funcion.PIS ideVinculo_nisTrab,\n"
                    + "Convert(Bigint,Funcion.Matr) ideVinculo_matricula,\n"
                    + "substring(replace(convert(varchar,RHAvisoPrevio.Data,111),'/','-'),0,11) detAvPrevio_dtAvPrv,\n"
                    + "substring(replace(convert(varchar,RHAvisoPrevio.DtFim,111),'/','-'),0,11) detAvPrevio_dtPrevDeslig,\n"
                    + "Case when RHAvisoPrevio.Iniciativa = 2 and RHAvisoPrevio.Tipo = 1 and RHAvisoPrevio.OpcaoAviso = 1 then '1'\n"
                    + "     when RHAvisoPrevio.Iniciativa = 2 and RHAvisoPrevio.Tipo = 1 and RHAvisoPrevio.OpcaoAviso = 2 then '2'\n"
                    + "	 when RHAvisoPrevio.Iniciativa = 1 and RHAvisoPrevio.Tipo = 1  then '4'\n"
                    + "	 when RHAvisoPrevio.Iniciativa = 3 and RHAvisoPrevio.Tipo = 1  then '6' end detAvPrevio_tpAvPrevio,\n"
                    + "RHAvisoPrevio.Obs detAvPrevio_observacao, \n"
                    + " (select max(sucesso) from ( \n"
                    + " (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  \n"
                    + " From XmleSocial z where z.Identificador = Funcion.Matr  \n"
                    + "                and z.evento = 'S-2250'  \n"
                    + "                and z.CodFil = ?  \n"
                    + "                and z.Compet = ?  \n"
                    + "                and z.Ambiente = ?  \n"
                    + "                and (z.Xml_Retorno like '%aguardando%'  \n"
                    + "                        or z.Xml_Retorno = ''  \n"
                    + "                        or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))  \n"
                    + " union  \n"
                    + " (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  \n"
                    + " From XmleSocial z where z.Identificador = Funcion.Matr  \n"
                    + "                and z.evento = 'S-2250'  \n"
                    + "                and z.CodFil = ?  \n"
                    + "                and z.Compet = ?  \n"
                    + "                and z.Ambiente = ?  \n"
                    + "                and (z.Xml_Retorno like '%<ocorrencia>%'  \n"
                    + "                        or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  \n"
                    + " union  \n"
                    + " (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  \n"
                    + " From XmleSocial z where z.Identificador = Funcion.Matr  \n"
                    + "                and z.evento = 'S-2250'  \n"
                    + "                and z.CodFil = ?  \n"
                    + "                and z.Compet = ?  \n"
                    + "                and z.Ambiente = ?  \n"
                    + "                and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso  \n"
                    + "from RHAvisoPrevio \n"
                    + "Left join Funcion  on Funcion.Matr = RHAvisoPrevio.Matr \n"
                    + "Left join Filiais  on Filiais.CodFil = Funcion.CodFil \n"
                    + "where substring(convert(varchar,RHAvisoPrevio.DtInicio ,121),1,7) = ? \n"
                    + "  and Funcion.CodFil = ? \n"
                    + "  and RHAvisoPrevio.Tipo = 1 ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.select();
            S2250 s2250;
            while (consulta.Proximo()) {
                s2250 = new S2250();
                s2250.setSucesso(consulta.getInt("sucesso"));
                s2250.setIdeEvento_indRetif(consulta.getString("ideEvento_indRetif"));
                s2250.setIdeEvento_nrRecibo(consulta.getString("ideEvento_nrRecibo"));
                s2250.setIdeEvento_tpAmb(ambiente);
                s2250.setIdeEvento_procEmi(consulta.getString("ideEvento_procEmi"));
                s2250.setIdeEvento_verProc(consulta.getString("ideEvento_verProc"));
                s2250.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2250.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2250.setIdeVinculo_cpfTrab(consulta.getString("ideVinculo_cpfTrab"));
                s2250.setIdeVinculo_nisTrab(consulta.getString("ideVinculo_nisTrab"));
                s2250.setIdeVinculo_matricula(consulta.getString("ideVinculo_matricula"));
                s2250.setDetAvPrevio_dtAvPrevio(consulta.getString("detAvPrevio_dtAvPrv"));
                s2250.setDetAvPrevio_dtPrevDeslig(consulta.getString("detAvPrevio_dtPrevDeslig"));
                s2250.setDetAvPrevio_tpAvPrevio(consulta.getString("detAvPrevio_tpAvPrevio"));
                s2250.setDetAvPrevio_observacao(consulta.getString("detAvPrevio_observacao"));
                retorno.add(s2250);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S2250Dao.get - " + e.getMessage());
        }
    }

}

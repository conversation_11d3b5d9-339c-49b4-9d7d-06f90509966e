<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:composite = "http://java.sun.com/jsf/composite"
      xmlns:components="http://xmlns.jcp.org/jsf/composite/components"
      >
    <composite:interface>
        <composite:attribute name="titulo" required="true" />
        <composite:attribute name="imagem" required="true" />
        <composite:attribute name="dataInicio" required="true" />
        <composite:attribute name="dataFim" required="true" />
        <composite:attribute name="descricao" required="true" />
        <composite:attribute name="endereco" required="true" />
        <composite:attribute name="bairro" required="true" />
        <composite:attribute name="cidade" required="true" />
        <composite:attribute name="UF" required="true" />
        <composite:attribute name="actionVoltar" required="true"
                             method-signature="java.lang.String action()" />
        <composite:attribute name="actionAvancar" required="true"
                             method-signature="java.lang.String action()" />
    </composite:interface>

    <composite:implementation>
        <header id="#{cc.id}">
            <h:form id="form">
                <div class="ui-grid ui-grid-responsive">
                    <div
                        class="ui-grid-row cabecalho"
                        style="margin-right: 40px;" 
                        >
                        <components:tituloPeriodo
                            id="divTopoTela"
                            titulo="#{cc.attrs.titulo}"
                            imagem="#{cc.attrs.imagem}"
                            dataInicio="#{cc.attrs.dataInicio}"
                            dataFim="#{cc.attrs.dataFim}"
                            />

                        <components:empresaFilial
                            id="divDadosFilial"
                            descricao="#{cc.attrs.descricao}"
                            endereco="#{cc.attrs.endereco}"
                            bairro="#{cc.attrs.bairro}"
                            cidade="#{cc.attrs.cidade}"
                            UF="#{cc.attrs.UF}"
                            />

                        <components:calendarioIntervalo
                            id="calendario-topo"
                            update="main cabecalho msgs"
                            oncomplete="PF('oCalendarios').loadContents();"
                            actionVoltar="#{cc.attrs.actionVoltar}"
                            actionAvancar="#{cc.attrs.actionAvancar}"
                            />

                        <components:botaoVoltar id="divBotaoVoltar" />
                    </div>
                </div>
            </h:form>
        </header>
    </composite:implementation>
</html>

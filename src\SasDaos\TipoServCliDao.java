/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TipoSrvCli;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TipoServCliDao {

    String sql;

    public List<TipoSrvCli> getTipoSrvCli(Persistencia persistencia) throws Exception {
        sql = " Select Codigo, Descricao, Banco, TAtend, TCob, TCar, TipoSrv, "
                + " ER, CodInterf, Aditivo, Exportar, SubCentro, Operador, "
                + " Dt_Alter, Hr_Alter "
                + " from TipoSrvCli ";

        TipoSrvCli oTipoSrvCli;
        List<TipoSrvCli> lTipoSrvCli;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            lTipoSrvCli = new ArrayList();
            while (consult.Proximo()) {
                oTipoSrvCli = new TipoSrvCli();

                oTipoSrvCli.setCodigo(consult.getString("Codigo"));
                oTipoSrvCli.setDescricao(consult.getString("Descricao"));
                oTipoSrvCli.setBanco(consult.getString("Banco"));
                oTipoSrvCli.setTAtend(consult.getString("TAtend"));
                oTipoSrvCli.setTCob(consult.getString("TCob"));
                oTipoSrvCli.setTCar(consult.getString("TCar"));
                oTipoSrvCli.setTipoSrv(consult.getString("TipoSrv"));
                oTipoSrvCli.setER(consult.getString("ER"));
                oTipoSrvCli.setCodInterf(consult.getString("CodInterf"));
                oTipoSrvCli.setAditivo(consult.getString("Aditivo"));
                oTipoSrvCli.setExportar(consult.getString("Exportar"));
                oTipoSrvCli.setSubCentro(consult.getString("SubCentro"));
                oTipoSrvCli.setOperador(consult.getString("Operador"));
                oTipoSrvCli.setDt_Alter(consult.getString("Dt_Alter"));
                oTipoSrvCli.setHr_Alter(consult.getString("Hr_Alter"));

                lTipoSrvCli.add(oTipoSrvCli);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar controle RH - " + e.getMessage());
        }
        return lTipoSrvCli;
    }
}

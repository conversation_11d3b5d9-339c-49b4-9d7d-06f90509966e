/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class Regional {

    private String Codigo;
    private String Descricao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the Codigo
     */
    public String getCodigo() {
        return Codigo;
    }

    /**
     * @param Codigo the Codigo to set
     */
    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    /**
     * @return the Descricao
     */
    public String getDescricao() {
        return Descricao;
    }

    /**
     * @param Descricao the Descricao to set
     */
    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public String getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Propostas.PropostasSatMobWeb;
import Dados.Persistencia;
import SasBeans.PropCml;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PropostasLazyList extends LazyDataModel<PropCml> {

    private static final long serialVersionUID = 1L;
    private List<PropCml> propostas;
    private final PropostasSatMobWeb propostasmobweb;
    private Persistencia persistencia;

    public PropostasLazyList(Persistencia pst) {
        this.propostasmobweb = new PropostasSatMobWeb();
        this.persistencia = pst;
    }

    @Override
    public List<PropCml> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.propostas = this.propostasmobweb.listagemPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.propostasmobweb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.propostas;
    }

    @Override
    public Object getRowKey(PropCml proposta) {
        return proposta.getNumero();
    }

    @Override
    public PropCml getRowData(String numero) {
        for (PropCml proposta : this.propostas) {
            if (proposta.getNumero().equals(new BigDecimal(numero))) {
                return proposta;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

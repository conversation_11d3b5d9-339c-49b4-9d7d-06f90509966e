/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.QueueGuias;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueueGuiasDao {

    // create
    public boolean gravaQueueGuias(QueueGuias queueguias, Persistencia persistencia) {
        boolean retorno;
        String sql = "insert into queueguias (Sequencia,Parada,Guia,Serie,Operador,Dt_Alter,Hr_Alter) "
                + "Values (?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(queueguias.getSequencia());
            consulta.setInt(queueguias.getParada());
            consulta.setBigDecimal(queueguias.getGuia());
            consulta.setString(queueguias.getSerie());
            consulta.setString(queueguias.getOperador());
            consulta.setString(queueguias.getDt_Alter().toString());
            consulta.setString(queueguias.getHr_Alter());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // read
    public List<QueueGuias> buscaQueueGuias(Persistencia persistencia) throws Exception {
        List<QueueGuias> listQueueGuias;
        try {
            QueueGuias queueguias;
            Consulta consult = new Consulta("select Sequencia,Parada,Guia,Serie,Operador,Dt_Alter,Hr_Alter "
                    + "from queueguias", persistencia);
            consult.select();
            listQueueGuias = new ArrayList();
            while (consult.Proximo()) {
                queueguias = new QueueGuias();
                queueguias.setSequencia(consult.getString("Sequencia"));
                queueguias.setParada(consult.getInt("Parada"));
                queueguias.setGuia(consult.getString("Guia"));
                queueguias.setSerie(consult.getString("Serie"));
                queueguias.setOperador(consult.getString("Operador"));
                queueguias.setDt_Alter(consult.getDate("Dt_Alter").toLocalDate());
                queueguias.setHr_Alter(consult.getString("Hr_Alter"));
                listQueueGuias.add(queueguias);
            }
            consult.Close();
        } catch (Exception e) {
            listQueueGuias = null;
            throw new Exception("Falha ao executar buscaQueueGuias " + e.getMessage());
        }
        return listQueueGuias;
    }

    // update
    public void atualizarQueueGuias(QueueGuias queueguias, Persistencia persistencia) throws Exception {
        String sql = "update queueguias set Sequencia=?, Parada=?, Guia=?, Serie=?, Operador=?,"
                + "Dt_Alter=?, Hr_Alter=? "
                + "where Sequencia=? and Parada=? and Guia=? and Serie=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(queueguias.getSequencia());
            consulta.setInt(queueguias.getParada());
            consulta.setBigDecimal(queueguias.getGuia());
            consulta.setString(queueguias.getSerie());
            consulta.setString(queueguias.getOperador());
            consulta.setString(queueguias.getDt_Alter().toString());
            consulta.setString(queueguias.getHr_Alter());
            consulta.setBigDecimal(queueguias.getSequencia());
            consulta.setInt(queueguias.getParada());
            consulta.setBigDecimal(queueguias.getGuia());
            consulta.setString(queueguias.getSerie());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new RuntimeException("Falha ao executar " + e.getMessage());
        }
    }

    // delete
    public void excluirQueueGuias(QueueGuias queueguias, Persistencia persistencia) throws Exception {
        String sql = "delete from queueguias "
                + "where Sequencia=? and Parada=? and Guia=? and Serie=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(queueguias.getSequencia());
            consulta.setInt(queueguias.getParada());
            consulta.setBigDecimal(queueguias.getGuia());
            consulta.setString(queueguias.getSerie());
            consulta.delete();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Erro ao executar excluirQueueGuias " + e.getMessage());
        }
    }
}

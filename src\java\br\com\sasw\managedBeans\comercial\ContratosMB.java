/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.comercial;

import Arquivo.ArquivoLog;
import Controller.Contratos.ContratosSatMobWeb;
import Controller.OS_Vig.OS_VigSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Controller.TbVal.ControlerTbVal;
import Dados.Persistencia;
import SasBeans.Bancos;
import SasBeans.Clientes;
import SasBeans.ContrVig;
import SasBeans.Contratos;
import SasBeans.ContratosDoctos;
import SasBeans.ContratosReaj;
import SasBeans.CtrItens;
import SasBeans.CtrItensAntReajustes;
import SasBeans.Filiais;
import SasBeans.ReajustesGrp;
import SasBeans.SasPWFill;
import SasBeans.TbVal;
import br.com.sasw.lazydatamodels.ContrVigLazyList;
import br.com.sasw.lazydatamodels.ContratosLazyList;
import br.com.sasw.lazydatamodels.ContratosReajLazyList;
import br.com.sasw.lazydatamodels.CtrItensAntReajustesLazyList;
import br.com.sasw.lazydatamodels.CtrItensLazyList;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import javax.validation.constraints.NotNull;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Named(value = "contratos")
@ViewScoped
public class ContratosMB implements Serializable {

    private Persistencia persistencia;
    private String log;
    private final String caminho;
    private final String nomeFilial;
    private final String banco;
    private final String operador;
    private final String codPessoa;
    private final String codFil;
    private final String dataTela;
    private final ArquivoLog logerro;
    private final Map filters = new HashMap(),
            filtroSub = new HashMap(),
            filtroReajuste = new HashMap(),
            filtroItem = new HashMap(),
            filtroItemAnterior = new HashMap();
    private Map tipoCalc, tipoPosto;
    @NotNull
    private final ContratosSatMobWeb contratosSatWeb;
    @NotNull
    private Contratos contratoSelecionado, contratoCadastro, contratoPesquisa;
    private LazyDataModel<Contratos> contratos = null;
    private ContrVig subContratoSelecionado;
    private LazyDataModel<ContrVig> subContratos = null;
    private ContratosReaj reajusteSelecionado;
    private LazyDataModel<ContratosReaj> reajustes = null;
    private CtrItens ctrItemSelecionado;
    private LazyDataModel<CtrItens> subContratoItens = null;
    private CtrItensAntReajustes itemAnteriorSelecionado;
    private LazyDataModel<CtrItensAntReajustes> itensAnteriores = null;
    private TbVal tbValCadastro;
    @NotNull
    // TODO: implementar cada flag de edição:
    private boolean editandoContrato = false,
            editandoSubContrato = false,
            editandoReajuste = false,
            editandoItem = false;
    private boolean mostraFiliais, limpaFiltros, mostraAtivos, mostraVencer, mostrarPrecos;
    private SasPWFill cadastroFilial;
    private Bancos identificacaoBanco;
    private List<Bancos> listaBancos;
    private Clientes clienteContrato;
    private List<Clientes> listaClientes;
    private List<String> descricoes;
    private ReajustesGrp grupoReajuste;
    private List<ReajustesGrp> gruposReajuste;
    private TbVal grupoPagamento;
    private List<TbVal> gruposPagamento;
    private List<ContratosDoctos> documentos;
    private UploadedFile uploadedFile;
    private StreamedContent download;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private final OS_VigSatMobWeb osVigController;
    private final ControlerTbVal controllerTbVal;
    private int tabelaSelecionada, ultimoCodigoTbVal;
    private String tituloTabelaSelecionada;
    // Constantes:
    private final String MAIN_TABELA = "main:tabela";
    private final String CONTRATO_CODFIL = " Contratos.codfil = ? ",
            SITUACAO = " Contratos.situacao = ? ",
            TERMINO = " Contratos.Dt_Termino < ? ",
            NOME = " Clientes.nome LIKE ? ",
            NRED = " Clientes.NRed LIKE ? ",
            IDENTIF = " Contratos.identif LIKE ? ",
            DESCRICAO = " Contratos.descricao LIKE ? ",
            BANCO,
            SUB_CODFIL = "codFil = ?",
            REAJUSTE_CODFIL = "ContratosReaj.CodFil = ?",
            ITEM_CODFIL = "contrvig.CodFil = ?",
            ITEM_ANTERIOR_CODFIL = "CtrItensAnt.CodFil = ?";
    private final int TABELA_DESCRICAO_SERVICOS = 42,
            TABELA_GRUPO_PAGAMENTO = 46;

    public ContratosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = ((BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa")).toBigInteger().toString();
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa + ".txt";
        contratosSatWeb = new ContratosSatMobWeb();
        osVigController = new OS_VigSatMobWeb();
        controllerTbVal = new ControlerTbVal();
        logerro = new ArquivoLog();
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");

        // filtros:
        BANCO = " Contratos.codfil IN (SELECT filiais.codfil"
                + "    FROM saspw"
                + "    INNER JOIN saspwfil ON saspwfil.nome = saspw.nome"
                + "    INNER JOIN filiais ON filiais.codfil = saspwfil.codfilac"
                + "    INNER JOIN paramet ON paramet.filial_pdr = filiais.codfil"
                + "    WHERE saspw.codpessoa = ? AND paramet.path = '" + banco + "')";
        filters.put(CONTRATO_CODFIL, codFil);
        filters.put(BANCO, codPessoa);
        filters.put(SITUACAO, "");
        filters.put(TERMINO, "");
        filters.put(NOME, "");
        filters.put(NRED, "");
        filters.put(IDENTIF, "");
        filters.put(DESCRICAO, "");

        filtroSub.put(SUB_CODFIL, mostraFiliais ? "" : codFil);
        filtroReajuste.put(REAJUSTE_CODFIL, mostraFiliais ? "" : codFil);
        filtroItem.put(ITEM_CODFIL, codFil);
        filtroItemAnterior.put(ITEM_ANTERIOR_CODFIL, codFil);
    }

    private void logaErro(final Exception e, final String methodName) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
        log = this.getClass().getSimpleName() + "\r\n"
                + methodName + "\r\n"
                + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    public void Persistencias(Persistencia persistencia) {
        try {
            this.persistencia = persistencia;
            if (null == persistencia) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
            }

            gruposPagamento = contratosSatWeb.obterGruposPagamento(persistencia);
            descricoes = contratosSatWeb.obterDescricoes(persistencia);
            gruposReajuste = contratosSatWeb.obterListaReajustesGrp(persistencia);
            listaBancos = contratosSatWeb.obterListaIdentificacaoCliente(persistencia);
            filiais = rotassatweb.buscaInfoFilial(codFil, persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    // Contratos
    public LazyDataModel<Contratos> lazyGetAllContratos() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(MAIN_TABELA);
            filters.replace(CONTRATO_CODFIL, codFil);
            filters.replace(BANCO, codPessoa);
            filters.replace(SITUACAO, "");
            filters.replace(TERMINO, "");
            dt.setFilters(filters);
            contratos = new ContratosLazyList(persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return contratos;
    }

    private void ativarModalContrato() {
        TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formContrato:tabs");
        tabs.setActiveIndex(0);
        PrimeFaces.current().resetInputs("formContrato:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
    }

    private void esconderModalContrato(String mensagem) {
        PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagem), null);
        FacesContext.getCurrentInstance().addMessage(null, message);
    }

    public void preCadastroContrato() {
        try {
            editandoContrato = false;

            contratoCadastro = new Contratos();
            contratoCadastro.setValidade("D");
            LocalDate data = LocalDate.now();
            contratoCadastro.setDt_Inicio(data.toString());
            contratoCadastro.setDt_Termino(data.plusYears(2).toString());

            cadastroFilial = contratosSatWeb.buscaFilial(codFil, codPessoa, persistencia);
            clienteContrato = null;
            listaClientes = new ArrayList<>();
            documentos = new ArrayList<>();

            grupoPagamento = new TbVal();
            grupoReajuste = new ReajustesGrp();
            identificacaoBanco = new Bancos();

            ativarModalContrato();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void calcularIdContrato() {
        try {
            int sequencia = contratosSatWeb.obterSequencial(identificacaoBanco.getBanco(), contratoCadastro.getTipo(), persistencia);
            String contratoId = String.format("%s.%s%03d", identificacaoBanco.getBanco(), contratoCadastro.getTipo(), sequencia);
            contratoCadastro.setContrato(contratoId);
        } catch (Exception e) {
            // TODO
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private boolean validaContrato(Contratos contrato) {
        return false;
    }

    public void cadastrarContrato() {
        try {
            contratoCadastro.setCodFil(cadastroFilial.getCodfilAc());
            contratoCadastro.setCliFat(clienteContrato.getCodigo());
            contratoCadastro.setGrpPagamento(String.valueOf(grupoPagamento.getCodigo()));
            contratoCadastro.setGrpReajuste(grupoReajuste.getCodigo());
            contratoCadastro.setIdentif(contratoCadastro.getIdentif().toUpperCase());
            this.contratoCadastro.setHr_Alter(getDataAtual("HORA"));
            this.contratoCadastro.setDt_Alter(getDataAtual("SQL"));

            contratosSatWeb.cadastrarContrato(contratoCadastro, persistencia);
            esconderModalContrato("CadastroSucesso");
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void preEdicaoContrato() {
        if (null == contratoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneContrato"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                editandoContrato = true;
                contratoCadastro = new Contratos(contratoSelecionado);
                String codFilCadastro = contratoCadastro.getCodFil();
                String codCli = contratoCadastro.getCliFat();
                String contrato = contratoCadastro.getContrato();
                String grpPagamento = contratoCadastro.getGrpPagamento();
                String grpReajuste = contratoCadastro.getGrpReajuste();

                cadastroFilial = contratosSatWeb.buscaFilial(codFilCadastro, codPessoa, persistencia);
                documentos = contratosSatWeb.listarDocumentosContrato(contrato, codFilCadastro, persistencia);
                clienteContrato = contratosSatWeb.obterCliente(codCli, codFilCadastro, persistencia);
                listaClientes = new ArrayList<>();
                listaClientes.add(clienteContrato);

                grupoPagamento = new TbVal();
                grupoPagamento.setTabela(TABELA_GRUPO_PAGAMENTO);
                grupoPagamento.setCodigo(Integer.valueOf(grpPagamento));

                grupoReajuste = new ReajustesGrp();
                grupoReajuste.setCodigo(grpReajuste);

                identificacaoBanco = new Bancos();
                identificacaoBanco.setBanco(contrato.substring(0, Math.min(3, contrato.length())));

                lazyGetAllSubContratos();
                lazyGetAllReajustes();
                ativarModalContrato();
            } catch (Exception e) {
                logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            }
        }
    }

    public void editarContrato() {
        try {
            contratoCadastro.setCliFat(clienteContrato.getCodigo());
            contratoCadastro.setGrpPagamento(String.valueOf(grupoPagamento.getCodigo()));
            contratoCadastro.setGrpReajuste(grupoReajuste.getCodigo());
            contratoCadastro.setIdentif(contratoCadastro.getIdentif().toUpperCase());
            this.contratoCadastro.setHr_Alter(getDataAtual("HORA"));
            this.contratoCadastro.setDt_Alter(getDataAtual("SQL"));

            contratosSatWeb.editarContrato(contratoCadastro, persistencia);
            esconderModalContrato("EdicaoSucesso");
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    //    public void deletarContrato() {
    //        try {
    //            contratosSatWeb.deletar____(contratoCadastro, persistencia);
    //            esconderModalContrato("DeletadoSucesso");
    //        } catch (Exception e) {
    //            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
    //        }
    //    }
    public void prePesquisaContrato() {
        contratoPesquisa = new Contratos();
        PrimeFaces.current().resetInputs("formPesquisar");
        PrimeFaces.current().executeScript("PF('dlgPesquisar').show();");
    }

    public void pesquisarContrato() {
        contratos = null;
        filters.replace(CONTRATO_CODFIL, contratoPesquisa.getCodFil() == null || contratoPesquisa.getCodFil().equals("") ? "" : contratoPesquisa.getCodFil());
        filters.replace(NOME, contratoPesquisa.getNome() == null || contratoPesquisa.getNome().equals("") ? "" : "%" + contratoPesquisa.getNome() + "%");
        filters.replace(NRED, contratoPesquisa.getNRed() == null || contratoPesquisa.getNRed().equals("") ? "" : "%" + contratoPesquisa.getNRed() + "%");
        filters.replace(IDENTIF, contratoPesquisa.getIdentif() == null || contratoPesquisa.getIdentif().equals("") ? "" : "%" + contratoPesquisa.getIdentif() + "%");
        filters.replace(DESCRICAO, contratoPesquisa.getDescricao() == null || contratoPesquisa.getDescricao().equals("") ? "" : "%" + contratoPesquisa.getDescricao() + "%");
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(MAIN_TABELA);
        dt.setFilters(filters);

        contratos = new ContratosLazyList(persistencia);

        lazyGetAllContratos();
        dt.setFirst(0);
    }

    public void verificacaoEdicaoContrato() {
        if (contratoCadastro.getCliFat().equals(clienteContrato.getCodigo())) {
            editarContrato();
        } else {
            PrimeFaces.current().executeScript("PF('cdglClienteContratante').show();");
        }
    }

    ////////////////////////////////////////////////////////////////////////////
    // Sub Contratos
    public void lazyGetAllSubContratos() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("formContrato:tabs:tabelaSubContratos");
            filtroSub.replace(SUB_CODFIL, mostraFiliais ? "" : codFil);
            dt.setFilters(filtroSub);
            subContratos = new ContrVigLazyList(contratoSelecionado.getContrato(), persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void ativarModalSubContrato() {
        TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formSubContrato:tabs");
        tabs.setActiveIndex(0);
        PrimeFaces.current().resetInputs("formSubContrato:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgSubContrato').show();");
    }

    // TODO
    private void esconderModalSubContrato(String mensagem) {
        PrimeFaces.current().executeScript("PF('dlgSubContrato').hide();");
        PrimeFaces.current().ajax().update("formContrato:tabs:tabelaSubContratos");
        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagem), null);
        FacesContext.getCurrentInstance().addMessage(null, message);
    }

    public void preCadastroSubContrato() {
        try {
            editandoSubContrato = false;

            subContratoSelecionado = new ContrVig();
            LocalDate data = LocalDate.now();
            subContratoSelecionado.setDt_Inicio(data);
            subContratoSelecionado.setDt_Termino(data.plusYears(2));

            ativarModalSubContrato();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void cadastrarSubContrato() {
        try {
            subContratoSelecionado.setCodFil(new BigDecimal(cadastroFilial.getCodfilAc()));
            subContratoSelecionado.setCliFat(clienteContrato.getCodigo());

            subContratoSelecionado.setContrato(contratoSelecionado.getContrato());
            // form opt    subContratoSelecionado.setDescricao();
            // form obr    subContratoSelecionado.setTipo();
            subContratoSelecionado.setSituacao("A");
            // form opt    subContratoSelecionado.setIdentif();
            subContratoSelecionado.setIDContrato(contratoSelecionado.getContrato());
            // ???    subContratoSelecionado.setContratoCli(contratoSelecionado.getCodFil());
            // ???    subContratoSelecionado.setRefArq();
            // ???    subContratoSelecionado.setObs();
            subContratoSelecionado.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            subContratoSelecionado.setHr_Alter(DataAtual.getDataAtual("HORA"));
            subContratoSelecionado.setDt_Alter(LocalDate.now());
            // form obr    subContratoSelecionado.setDt_inicio();
            // form obr    subContratoSelecionado.setDt_termino();

            osVigController.inserirSubContrato(subContratoSelecionado, persistencia);
            esconderModalSubContrato("CadastroSucesso");
            PrimeFaces.current().resetInputs("formContrato:tabs:tabelaSubContratos");
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void preEdicaoSubContratos() {
        if (null == subContratoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneContrato"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                editandoSubContrato = true;
                lazyGetAllItens();
                lazyGetAllItensAnteriores();
                ativarModalSubContrato();
            } catch (Exception e) {
                logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            }
        }
    }

    public void editarSubContrato() {
        try {
            subContratoSelecionado.setCliFat(clienteContrato.getCodigo());
            contratosSatWeb.editarContratoAnexo(subContratoSelecionado, persistencia);
            esconderModalSubContrato("EdicaoSucesso");
            PrimeFaces.current().resetInputs("formContrato:tabs:tabelaSubContratos");
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    ////////////////////////////////////////////////////////////////////////////
    // Reajustes
    public void lazyGetAllReajustes() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("formContrato:tabs:tabelaReajustes");
            filtroReajuste.replace(REAJUSTE_CODFIL, mostraFiliais ? "" : codFil);
            dt.setFilters(filtroReajuste);
            reajustes = new ContratosReajLazyList(contratoSelecionado.getContrato(), persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void ativarModalReajuste() {
        PrimeFaces.current().resetInputs("formReajuste:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgReajuste').show();");
    }

    public void preCadastroReajuste() {
        try {
            editandoReajuste = false;

            reajusteSelecionado = new ContratosReaj();
            ativarModalReajuste();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    /*public void cadastrarReajuste() {
        try {
            contratoSelecionado.setCodFil(cadastroFilial.getCodfilAc());
            contratoSelecionado.setCliFat(clienteContrato.getCodigo());

            contratosSatWeb.cadastrarReajuste(reajusteSelecionado, persistencia);
            esconderModalContrato("CadastroSucesso");
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }*/

    public void preEdicaoReajustes() {
        if (null == reajusteSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneContrato"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                editandoReajuste = true;
                ativarModalReajuste();
            } catch (Exception e) {
                logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            }
        }
    }
//    public void editarReajuste() {
//        try {
//            reajusteSelecionado.setCliFat(clienteContrato.getCodigo());
//
//            contratosSatWeb.editarReajustes(reajusteSelecionado, persistencia);
//            esconderModalContrato("EdicaoSucesso");
//        } catch (Exception e) {
//            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
//        }
//    }
//    public void deletarReajuste() {
//        try {
//            contratosSatWeb.deletarReajuste(reajusteSelecionado, persistencia);
//            esconderModalContrato("DeletadoSucesso");
//        } catch (Exception e) {
//            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
//        }
//    }
    ////////////////////////////////////////////////////////////////////////////
    // Itens

    public void lazyGetAllItens() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("formSubContrato:tabs:tabelaItensFaturar");
            filtroItem.replace(ITEM_CODFIL, codFil);
            dt.setFilters(filtroItem);
            subContratoItens = new CtrItensLazyList(subContratoSelecionado.getContrato(), persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void ativarModalItem() {
        PrimeFaces.current().resetInputs("formItensContrato:cadastrar");
        PrimeFaces.current().ajax().update("formItensContrato:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgItensContrato').show();");
    }

    // TODO
    private void esconderModalItem(String mensagem) {
        PrimeFaces.current().executeScript("PF('dlgItensContrato').hide();");
        PrimeFaces.current().ajax().update("formSubContrato:tabs:tabelaItensFaturar");
        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagem), null);
        FacesContext.getCurrentInstance().addMessage(null, message);
    }

    public void preCadastroItem() {
        try {
            editandoItem = false;
            ctrItemSelecionado = new CtrItens();
            tipoPosto = gerarTipoPosto();
            tipoCalc = gerarTipoCal();
            PrimeFaces.current().resetInputs("formItensContrato");
            ativarModalItem();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void cadastrarItem() {
        try {
            ctrItemSelecionado.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            ctrItemSelecionado.setDt_Alter(DataAtual.getDataAtual("SQL"));
            ctrItemSelecionado.setHr_Alter(DataAtual.getDataAtual("Hora"));
            contratosSatWeb.cadastrarItemContrato(subContratoSelecionado, ctrItemSelecionado, persistencia);
            esconderModalItem("EdicaoSucesso");
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void preEdicaoItensContrato() {
        try {
            if (null == ctrItemSelecionado) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneCtrItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                editandoItem = true;
                tipoPosto = gerarTipoPosto();
                tipoCalc = gerarTipoCal();
                ativarModalItem();
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void editarItem() {
        try {
            ctrItemSelecionado.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            ctrItemSelecionado.setDt_Alter(DataAtual.getDataAtual("SQL"));
            ctrItemSelecionado.setHr_Alter(DataAtual.getDataAtual("Hora"));
            contratosSatWeb.editarItemContrato(ctrItemSelecionado, persistencia);
            esconderModalItem("EdicaoSucesso");
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

//    public void deletarItem() {
//        try {
//            contratosSatWeb.deletarItem(ctrItemSelecionado, persistencia);
//            esconderModalContrato("DeletadoSucesso");
//        } catch (Exception e) {
//            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
//        }
//    }
    ////////////////////////////////////////////////////////////////////////////
    // Itens Anteriores
    public void lazyGetAllItensAnteriores() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("formSubContrato:tabs:tabelaItensAnteriores");
            filtroItemAnterior.replace(ITEM_ANTERIOR_CODFIL, codFil);
            dt.setFilters(filtroItemAnterior);
            itensAnteriores = new CtrItensAntReajustesLazyList(subContratoSelecionado.getContrato(), persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void preEdicaoItensAnterior() {
        try {
            if (null == itemAnteriorSelecionado) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneCtrItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                editandoItem = true;
                tipoPosto = gerarTipoPosto();
                tipoCalc = gerarTipoCal();
                ativarModalItem();
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    ////////////////////////////////////////////////////////////////////////////
    // TbVal
    private void ativarModalTbVal() {
        PrimeFaces.current().resetInputs("formCadastroGrupoPagamento");
        PrimeFaces.current().executeScript("PF('dlgCadastroGrupoPagamento').show();");
    }

    private void esconderModalTbVal(String mensagem) {
        PrimeFaces.current().executeScript("PF('dlgCadastroGrupoPagamento').hide();");
        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagem), null);
        FacesContext.getCurrentInstance().addMessage(null, message);
    }

    public void preCadastroGrupoPagamento() {
        preCadastroTbVal(TABELA_GRUPO_PAGAMENTO);
    }

    public void preCadastroDescricaoServicos() {
        preCadastroTbVal(TABELA_DESCRICAO_SERVICOS);
    }

    private Map<Integer, String> getTipoTabela() {
        Map map = new HashMap<>();
        map.put(TABELA_DESCRICAO_SERVICOS, getMessageS("TabelaPropostaComercial"));
        map.put(TABELA_GRUPO_PAGAMENTO, getMessageS("ContratosGrpPagamento"));
        return map;
    }

    public void preCadastroTbVal(int tabela) {
        try {
            tabelaSelecionada = tabela;
            tituloTabelaSelecionada = String.format("%03d %s", tabela, getTipoTabela().get(tabela));
            tbValCadastro = new TbVal();
            tbValCadastro.setTabela(tabela);
            ultimoCodigoTbVal = controllerTbVal.getMaxCodigoDeTabela(tabela, persistencia) - 1;
            ativarModalTbVal();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void inserirTbVal() {
        switch (tabelaSelecionada) {
            case TABELA_DESCRICAO_SERVICOS: {
                inserirDescricaoServico();
                break;
            }
            case TABELA_GRUPO_PAGAMENTO: {
                inserirGrupoPagamento();
                break;
            }
            default:
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Erro"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    private void inserirGrupoPagamento() {
        try {
            tbValCadastro.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            boolean success = controllerTbVal.inserir(tbValCadastro, persistencia);
            if (success) {
                gruposPagamento = contratosSatWeb.obterGruposPagamento(persistencia);
                esconderModalTbVal("CadastroSucesso");
            } else {
                throw new Exception("ErroValidacao");
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void inserirDescricaoServico() {
        try {
            tbValCadastro.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            boolean success = controllerTbVal.inserir(tbValCadastro, persistencia);
            if (success) {
                descricoes = contratosSatWeb.obterDescricoes(persistencia);
                esconderModalTbVal("CadastroSucesso");
            } else {
                throw new Exception("ErroValidacao");
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    ////////////////////////////////////////////////////////////////////////////
    // Miscelâneo
    public List<Clientes> buscarClientes(String query) {
        try {
            listaClientes = contratosSatWeb.buscarClientes(query, cadastroFilial.getCodfilAc(), persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return listaClientes;
    }

    public void handleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                uploadedFile = fileUploadEvent.getFile();
                ContratosDoctos docto = new ContratosDoctos();
                docto.setContrato(contratoCadastro.getContrato());
                docto.setCodFil(contratoCadastro.getCodFil());
                docto.setDtArquivo(DataAtual.getDataAtual("SQL"));

                docto.setDescricao(uploadedFile.getFileName());
                docto.setDt_Inicio(contratoCadastro.getDt_Inicio());
                docto.setDt_Termino(contratoCadastro.getDt_Termino());
                docto.setComissao("0");
                docto.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                docto.setDt_alter(DataAtual.getDataAtual("SQL"));
                docto.setHr_Alter(DataAtual.getDataAtual("HORA"));

                docto = contratosSatWeb.inserirDocumento(docto, persistencia);

                new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + persistencia.getEmpresa() + "\\ContratosDoctos\\"
                        + contratoCadastro.getCodFil().replace(".0", "") + "\\"
                        + contratoCadastro.getContrato() + "\\"
                        + DataAtual.getDataAtual("SQL")).mkdirs();
                String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + persistencia.getEmpresa() + "\\ContratosDoctos\\"
                        + contratoCadastro.getCodFil().replace(".0", "") + "\\"
                        + contratoCadastro.getContrato() + "\\"
                        + DataAtual.getDataAtual("SQL") + "\\"
                        + docto.getOrdem() + "-" + docto.getDescricao();

                File file = new File(arquivo);
                FileOutputStream output = new FileOutputStream(file);
                output.write(uploadedFile.getContents());
                output.close();

                documentos = contratosSatWeb.listarDocumentosContrato(contratoCadastro.getContrato(),
                        contratoCadastro.getCodFil(), persistencia);
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void handleFileDownload(ContratosDoctos docto) {
        try {
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + persistencia.getEmpresa() + "\\ContratosDoctos\\"
                    + docto.getCodFil().replace(".0", "") + "\\"
                    + docto.getContrato() + "\\"
                    + Mascaras.removeMascaraData(docto.getDtArquivo()) + "\\"
                    + docto.getOrdem() + "-" + docto.getDescricao();
            InputStream stream = new FileInputStream(arquivo);
            download = new DefaultStreamedContent(stream, "application/" + docto.getDescricao().split("\\.")[docto.getDescricao().split("\\.").length - 1], docto.getDescricao());
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    /**
     * Excluí o documento no servidor O documento excluído e movido para uma
     * pasta de removidos O nome do documento entao e alterado para o formato
     * "Nome_UsuarioQueRealizouAcao_yyyy-MM-dd HH-mm-ss.extensao"
     *
     * @param docto
     */
    public void handleFileDelete(ContratosDoctos docto) {
        try {
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + persistencia.getEmpresa() + "\\ContratosDoctos\\"
                    + docto.getCodFil().replace(".0", "") + "\\"
                    + docto.getContrato() + "\\"
                    + docto.getDtArquivo() + "\\"
                    + docto.getOrdem() + "-" + docto.getDescricao();
            File file = new File(arquivo);

            //cria path de removidos
            File removidos = new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + persistencia.getEmpresa() + "\\ContratosDoctos\\"
                    + docto.getCodFil().replace(".0", "") + "\\"
                    + docto.getContrato() + "\\Removidos");
            removidos.mkdirs();

            if (docto.getDescricao().lastIndexOf(".") > 0) {
                String nome = docto.getOrdem() + "-" + docto.getDescricao().substring(0, docto.getDescricao().lastIndexOf("."));
                String tipo = docto.getDescricao().split("\\.")[docto.getDescricao().split("\\.").length - 1];

                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
                LocalDateTime now = LocalDateTime.now();

                file.renameTo(new File(removidos + "\\" + nome + "_"
                        + FuncoesString.RecortaAteEspaço(operador, 0, 10) + "_" + dtf.format(now) + "." + tipo));
            }
            contratosSatWeb.deletarDocumento(docto, persistencia);

            documentos = contratosSatWeb.listarDocumentosContrato(contratoCadastro.getContrato(),
                    contratoCadastro.getCodFil(), persistencia);
            //file.delete();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void mostrarFiliais() {
        filters.replace(CONTRATO_CODFIL, mostraFiliais ? "" : codFil);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(MAIN_TABELA);
        dt.setFilters(filters);
        lazyGetAllContratos();
        dt.setFirst(0);
    }

    public void mostrarAtivos() {
        filters.replace(SITUACAO, mostraAtivos ? "A" : "");
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(MAIN_TABELA);
        dt.setFilters(filters);
        lazyGetAllContratos();
        dt.setFirst(0);
    }

    public void mostrarAVencer() {
        filters.replace(TERMINO, mostraVencer ? getDataAtual("SQL") : "");
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(MAIN_TABELA);
        dt.setFilters(filters);
        lazyGetAllContratos();
        dt.setFirst(0);
    }

    public void limparFiltros() {
        filters.replace(CONTRATO_CODFIL, codFil);
        filters.replace(BANCO, codPessoa);
        filters.replace(SITUACAO, "");
        filters.replace(TERMINO, "");
        filters.replace(NOME, "");
        filters.replace(NRED, "");
        filters.replace(IDENTIF, "");
        filters.replace(DESCRICAO, "");

        mostraFiliais = false;
        mostraAtivos = false;
        mostraVencer = false;
        limpaFiltros = false;

        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(MAIN_TABELA);
        dt.setFilters(filters);
        lazyGetAllContratos();
        dt.setFirst(0);
    }

    private Map gerarTipoPosto() {
        Map map = new HashMap<>();
        map.put(getMessageS("Embarques"), "010");
        map.put(getMessageS("EmbarquesFranquia"), "011");
        map.put(getMessageS("EmbarquesFranquia"), "012");
        map.put(getMessageS("EmbNumerarioConcomitDiaria"), "013");
        map.put(getMessageS("EmbEnvelopeConcomitDiaria"), "014");
        map.put(getMessageS("EmbSuprimentoDifenciado"), "015");
        map.put(getMessageS("EmbRecolhimentoDifenciado"), "016");
        map.put(getMessageS("SuprimentoConcomitante"), "017");
        map.put(getMessageS("CargaDescarga"), "018");
        map.put(getMessageS("EmbarquesCobrancaEntrega"), "019");
        map.put(getMessageS("AdValorem"), "020");
        map.put(getMessageS("TempoEsperaMinutos"), "030");
        map.put(getMessageS("TempoEsperaHora"), "031");
        map.put(getMessageS("Envelopes"), "040");
        map.put(getMessageS("Bananinha"), "041");
        map.put(getMessageS("Sangria"), "042");
        map.put(getMessageS("KitTroco"), "046");
        map.put(getMessageS("ChequesUnitario"), "047");
        map.put(getMessageS("UnidadesProdServ"), "049");
        map.put(getMessageS("Milheiros"), "050");
        map.put(getMessageS("MilheirosBom"), "051");
        map.put(getMessageS("MilheirosDilacerado"), "052");
        map.put(getMessageS("MilheirosMoedas"), "053");
        map.put(getMessageS("MilheirosCheques"), "054");
        map.put(getMessageS("MilheirosCheques"), "055");
        map.put(getMessageS("MilheirosTicket"), "056");
        map.put(getMessageS("MilheirosCheques"), "057");
        map.put(getMessageS("MilheirosSuprimento"), "058");
        map.put(getMessageS("MilheirosSuprimentoMoedas"), "059");
        map.put(getMessageS("Custodia"), "060");
        map.put(getMessageS("CustodiaMoedas"), "061");
        map.put(getMessageS("CustodiaProdutosServi"), "062");
        map.put(getMessageS("CustodiaPermanenicaDi"), "063");
        map.put(getMessageS("CustodiaPassagem"), "064");
        map.put(getMessageS("KMRodados"), "070");
        map.put(getMessageS("KMTerra"), "072");
        map.put(getMessageS("FixoMensal"), "080");
        map.put(getMessageS("FixoMensalAdValorem"), "081");
        map.put(getMessageS("FixoMensalTesouraria"), "086");
        map.put(getMessageS("TempoCoberturaPagamento"), "090");
        map.put(getMessageS("Malotes"), "091");
        map.put(getMessageS("EntregaRecolhimentoMalotes"), "092");
        map.put(getMessageS("EnvelopeSangria"), "093");
        return map;
    }

    private Map gerarTipoCal() {
        Map map = new HashMap<>();
        map.put(getMessageS("PorMes"), "M");
        map.put(getMessageS("PorDia"), "D");
        map.put(getMessageS("PorHora"), "H");
        map.put(getMessageS("PorQtde"), "Q");
        map.put(getMessageS("IndiceSValor"), "I");
        return map;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public String getOperador() {
        return operador;
    }

    public Contratos getContratoSelecionado() {
        return contratoSelecionado;
    }

    public void setContratoSelecionado(Contratos contratoSelecionado) {
        this.contratoSelecionado = contratoSelecionado;
    }

    public boolean isMostraFiliais() {
        return mostraFiliais;
    }

    public void setMostraFiliais(boolean mostraFiliais) {
        this.mostraFiliais = mostraFiliais;
    }

    public boolean isLimpaFiltros() {
        return limpaFiltros;
    }

    public void setLimpaFiltros(boolean limpaFiltros) {
        this.limpaFiltros = limpaFiltros;
    }

    public boolean getEditandoContrato() {
        return editandoContrato;
    }

    public SasPWFill getCadastroFilial() {
        return cadastroFilial;
    }

    public void setCadastroFilial(SasPWFill cadastroFilial) {
        this.cadastroFilial = cadastroFilial;
    }

    public Clientes getClienteContrato() {
        return clienteContrato;
    }

    public void setClienteContrato(Clientes clienteContrato) {
        this.clienteContrato = clienteContrato;
    }

    public List<Clientes> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<Clientes> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public List<ContratosDoctos> getDocumentos() {
        return documentos;
    }

    public void setDocumentos(List<ContratosDoctos> documentos) {
        this.documentos = documentos;
    }

    public StreamedContent getDownload() {
        return download;
    }

    public void setDownload(StreamedContent download) {
        this.download = download;
    }

    public List<TbVal> getGruposPagamento() {
        return gruposPagamento;
    }

    public void setGruposPagamento(List<TbVal> gruposPagamento) {
        this.gruposPagamento = gruposPagamento;
    }

    public TbVal getGrupoPagamento() {
        return grupoPagamento;
    }

    public void setGrupoPagamento(TbVal grupoPagamento) {
        this.grupoPagamento = grupoPagamento;
    }

    public List<ReajustesGrp> getGruposReajuste() {
        return gruposReajuste;
    }

    public void setGruposReajuste(List<ReajustesGrp> gruposReajuste) {
        this.gruposReajuste = gruposReajuste;
    }

    public ReajustesGrp getGrupoReajuste() {
        return grupoReajuste;
    }

    public void setGrupoReajuste(ReajustesGrp grupoReajuste) {
        this.grupoReajuste = grupoReajuste;
    }

    public List<Bancos> getListaBancos() {
        return listaBancos;
    }

    public void setListaBancos(List<Bancos> listaBancos) {
        this.listaBancos = listaBancos;
    }

    public Bancos getIdentificacaoBanco() {
        return identificacaoBanco;
    }

    public void setIdentificacaoBanco(Bancos identificacaoBanco) {
        this.identificacaoBanco = identificacaoBanco;
    }

    public List<String> getDescricoes() {
        return descricoes;
    }

    public void setDescricoes(List<String> descricoes) {
        this.descricoes = descricoes;
    }

    public boolean isMostraAtivos() {
        return mostraAtivos;
    }

    public void setMostraAtivos(boolean mostraAtivos) {
        this.mostraAtivos = mostraAtivos;
    }

    public boolean isMostraVencer() {
        return mostraVencer;
    }

    public void setMostraVencer(boolean mostraVencer) {
        this.mostraVencer = mostraVencer;
    }

    public String getDataTela() {
        return dataTela;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public LazyDataModel<Contratos> getAllContratos() {
        if (contratos == null) {
            lazyGetAllContratos();
        }

        return contratos;
    }

    public LazyDataModel<ContrVig> getAllSubContratos() {
        return subContratos;
    }

    public LazyDataModel<ContratosReaj> getAllReajustes() {
        return reajustes;
    }

    public LazyDataModel<CtrItens> getAllSubContratosItens() {
        return subContratoItens;
    }

    public LazyDataModel<CtrItensAntReajustes> getAllItensAnteriores() {
        if (itensAnteriores == null) {
            lazyGetAllItensAnteriores();
        }

        return itensAnteriores;
    }

    public ContrVig getSubContratoSelecionado() {
        return subContratoSelecionado;
    }

    public void setSubContratoSelecionado(ContrVig subContratoSelecionado) {
        this.subContratoSelecionado = subContratoSelecionado;
    }

    public boolean isMostrarPrecos() {
        return mostrarPrecos;
    }

    public void setMostrarPrecos(boolean mostrarPrecos) {
        this.mostrarPrecos = mostrarPrecos;
    }

    public CtrItens getCtrItemSelecionado() {
        return ctrItemSelecionado;
    }

    public void setCtrItemSelecionado(CtrItens ctrItemSelecionado) {
        this.ctrItemSelecionado = ctrItemSelecionado;
    }

    public Map getTipoCalc() {
        return tipoCalc;
    }

    public void setTipoCalc(Map tipoCalc) {
        this.tipoCalc = tipoCalc;
    }

    public Map getTipoPosto() {
        return tipoPosto;
    }

    public void setTipoPosto(Map tipoPosto) {
        this.tipoPosto = tipoPosto;
    }

    public boolean isEditandoContrato() {
        return editandoContrato;
    }

    public boolean isEditandoSubContrato() {
        return editandoSubContrato;
    }

    public boolean isEditandoReajuste() {
        return editandoReajuste;
    }

    public boolean isEditandoItem() {
        return editandoItem;
    }

    public ContratosReaj getReajusteSelecionado() {
        return reajusteSelecionado;
    }

    public void setReajusteSelecionado(ContratosReaj reajusteSelecionado) {
        this.reajusteSelecionado = reajusteSelecionado;
    }

    public CtrItensAntReajustes getItemAnteriorSelecionado() {
        return itemAnteriorSelecionado;
    }

    public void setItemAnteriorSelecionado(CtrItensAntReajustes itemAnteriorSelecionado) {
        this.itemAnteriorSelecionado = itemAnteriorSelecionado;
    }

    public TbVal getTbValCadastro() {
        return tbValCadastro;
    }

    public void setTbValCadastro(TbVal tbValCadastro) {
        this.tbValCadastro = tbValCadastro;
    }

    public int getUltimoCodigoTbVal() {
        return ultimoCodigoTbVal;
    }

    public int getTabelaSelecionada() {
        return tabelaSelecionada;
    }

    public String getTituloTabelaSelecionada() {
        return tituloTabelaSelecionada;
    }

    public Contratos getContratoCadastro() {
        return contratoCadastro;
    }

    public void setContratoCadastro(Contratos contratoCadastro) {
        this.contratoCadastro = contratoCadastro;
    }

    public Contratos getContratoPesquisa() {
        return contratoPesquisa;
    }

    public void setContratoPesquisa(Contratos contratoPesquisa) {
        this.contratoPesquisa = contratoPesquisa;
    }
}

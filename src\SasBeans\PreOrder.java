/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class PreOrder {

    private String Sequencia;
    private String CodFil;
    private String Banco;
    private String Agencia;
    private String SubAgencia;
    private String RPV;
    private String DtColeta;
    private String DtEntrega;
    private String AgenciaOri;
    private String SubAgenciaOri;
    private String CodCli1;
    private String NRed1;
    private String Hora1O;
    private String Hora2O;
    private String CodCli2;
    private String NRed2;
    private String Hora1D;
    private String Hora2D;
    private String Solicitante;
    private String PedidoCliente;
    private String Valor;
    private String Obs;
    private String ClassifSrv;
    private String OperIncl;
    private String Dt_Incl;
    private String Hr_Incl;
    private String OS;
    private String ChequesQtde;
    private String ChequesValor;
    private String Guia;
    private String Serie;
    private String Pedido;
    private String Lote;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String OperExcl;
    private String Dt_Excl;
    private String Hr_Excl;
    private String Situacao;
    private String Flag_Excl;

    private String PreOrderVolOrdem;
    private String PreOrderVolQtde;
    private String PreOrderVolLacre;
    private String PreOrderVolTipo;
    private String PreOrderVolValor;
    private String PreOrderVolObs;

    private String IEFat;
    private String NRedFat;
    private String EndeFat;
    private String BaiFat;
    private String CidFat;
    private String UFFat;
    private String CGCFat;

    private String NRedOri;
    private String EndeOri;
    private String BaiOri;
    private String CidOri;
    private String UFOri;

    private String NRedDst;
    private String EndeDst;
    private String BaiDst;
    private String CidDst;
    private String UFDst;

    private String Assinatura;
    private String AssinaturaDestino;

    private String ColetaOri;
    private String VeiculoOri;
    private String RotaOri;
    private String Hora1;
    private String Hora2;
    private String ColetaDst;
    private String VeiculoDst;
    private String RotaDst;
    private String HoraChegada;
    private String HoraSaida;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getSubAgencia() {
        return SubAgencia;
    }

    public void setSubAgencia(String SubAgencia) {
        this.SubAgencia = SubAgencia;
    }

    public String getAgenciaOri() {
        return AgenciaOri;
    }

    public void setAgenciaOri(String AgenciaOri) {
        this.AgenciaOri = AgenciaOri;
    }

    public String getSubAgenciaOri() {
        return SubAgenciaOri;
    }

    public void setSubAgenciaOri(String SubAgenciaOri) {
        this.SubAgenciaOri = SubAgenciaOri;
    }

    public String getDtColeta() {
        return DtColeta;
    }

    public void setDtColeta(String DtColeta) {
        this.DtColeta = DtColeta;
    }

    public String getLote() {
        return Lote;
    }

    public void setLote(String Lote) {
        this.Lote = Lote;
    }

    public String getDtEntrega() {
        return DtEntrega;
    }

    public void setDtEntrega(String DtEntrega) {
        this.DtEntrega = DtEntrega;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getNRed1() {
        return NRed1;
    }

    public void setNRed1(String NRed1) {
        this.NRed1 = NRed1;
    }

    public String getHora1O() {
        return Hora1O;
    }

    public void setHora1O(String Hora1O) {
        this.Hora1O = Hora1O;
    }

    public String getHora2O() {
        return Hora2O;
    }

    public void setHora2O(String Hora2O) {
        this.Hora2O = Hora2O;
    }

    public String getCodCli2() {
        return CodCli2;
    }

    public void setCodCli2(String CodCli2) {
        this.CodCli2 = CodCli2;
    }

    public String getNRed2() {
        return NRed2;
    }

    public void setNRed2(String NRed2) {
        this.NRed2 = NRed2;
    }

    public String getHora1D() {
        return Hora1D;
    }

    public void setHora1D(String Hora1D) {
        this.Hora1D = Hora1D;
    }

    public String getHora2D() {
        return Hora2D;
    }

    public void setHora2D(String Hora2D) {
        this.Hora2D = Hora2D;
    }

    public String getSolicitante() {
        return Solicitante;
    }

    public void setSolicitante(String Solicitante) {
        this.Solicitante = Solicitante;
    }

    public String getPedidoCliente() {
        return PedidoCliente;
    }

    public void setPedidoCliente(String PedidoCliente) {
        this.PedidoCliente = PedidoCliente;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getClassifSrv() {
        return ClassifSrv;
    }

    public void setClassifSrv(String ClassifSrv) {
        this.ClassifSrv = ClassifSrv;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public String getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getChequesQtde() {
        return ChequesQtde;
    }

    public void setChequesQtde(String ChequesQtde) {
        this.ChequesQtde = ChequesQtde;
    }

    public String getChequesValor() {
        return ChequesValor;
    }

    public void setChequesValor(String ChequesValor) {
        this.ChequesValor = ChequesValor;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOperExcl() {
        return OperExcl;
    }

    public void setOperExcl(String OperExcl) {
        this.OperExcl = OperExcl;
    }

    public String getDt_Excl() {
        return Dt_Excl;
    }

    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    public String getHr_Excl() {
        return Hr_Excl;
    }

    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    public String getRPV() {
        return RPV;
    }

    public void setRPV(String RPV) {
        this.RPV = RPV;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getPedido() {
        return Pedido;
    }

    public void setPedido(String Pedido) {
        this.Pedido = Pedido;
    }

    public String getPreOrderVolOrdem() {
        return PreOrderVolOrdem;
    }

    public void setPreOrderVolOrdem(String PreOrderVolOrdem) {
        this.PreOrderVolOrdem = PreOrderVolOrdem;
    }

    public String getPreOrderVolQtde() {
        return PreOrderVolQtde;
    }

    public void setPreOrderVolQtde(String PreOrderVolQtde) {
        this.PreOrderVolQtde = PreOrderVolQtde;
    }

    public String getPreOrderVolLacre() {
        return PreOrderVolLacre;
    }

    public void setPreOrderVolLacre(String PreOrderVolLacre) {
        this.PreOrderVolLacre = PreOrderVolLacre;
    }

    public String getPreOrderVolTipo() {
        return PreOrderVolTipo;
    }

    public void setPreOrderVolTipo(String PreOrderVolTipo) {
        this.PreOrderVolTipo = PreOrderVolTipo;
    }

    public String getPreOrderVolValor() {
        return PreOrderVolValor;
    }

    public void setPreOrderVolValor(String PreOrderVolValor) {
        this.PreOrderVolValor = PreOrderVolValor;
    }

    public String getPreOrderVolObs() {
        return PreOrderVolObs;
    }

    public void setPreOrderVolObs(String PreOrderVolObs) {
        this.PreOrderVolObs = PreOrderVolObs;
    }

    public String getNRedFat() {
        return NRedFat;
    }

    public void setNRedFat(String NRedFat) {
        this.NRedFat = NRedFat;
    }

    public String getEndeFat() {
        return EndeFat;
    }

    public void setEndeFat(String EndeFat) {
        this.EndeFat = EndeFat;
    }

    public String getBaiFat() {
        return BaiFat;
    }

    public void setBaiFat(String BaiFat) {
        this.BaiFat = BaiFat;
    }

    public String getCidFat() {
        return CidFat;
    }

    public void setCidFat(String CidFat) {
        this.CidFat = CidFat;
    }

    public String getUFFat() {
        return UFFat;
    }

    public void setUFFat(String UFFat) {
        this.UFFat = UFFat;
    }

    public String getCGCFat() {
        return CGCFat;
    }

    public void setCGCFat(String CGCFat) {
        this.CGCFat = CGCFat;
    }

    public String getNRedOri() {
        return NRedOri;
    }

    public void setNRedOri(String NRedOri) {
        this.NRedOri = NRedOri;
    }

    public String getEndeOri() {
        return EndeOri;
    }

    public void setEndeOri(String EndeOri) {
        this.EndeOri = EndeOri;
    }

    public String getBaiOri() {
        return BaiOri;
    }

    public void setBaiOri(String BaiOri) {
        this.BaiOri = BaiOri;
    }

    public String getCidOri() {
        return CidOri;
    }

    public void setCidOri(String CidOri) {
        this.CidOri = CidOri;
    }

    public String getUFOri() {
        return UFOri;
    }

    public void setUFOri(String UFOri) {
        this.UFOri = UFOri;
    }

    public String getNRedDst() {
        return NRedDst;
    }

    public void setNRedDst(String NRedDst) {
        this.NRedDst = NRedDst;
    }

    public String getEndeDst() {
        return EndeDst;
    }

    public void setEndeDst(String EndeDst) {
        this.EndeDst = EndeDst;
    }

    public String getBaiDst() {
        return BaiDst;
    }

    public void setBaiDst(String BaiDst) {
        this.BaiDst = BaiDst;
    }

    public String getCidDst() {
        return CidDst;
    }

    public void setCidDst(String CidDst) {
        this.CidDst = CidDst;
    }

    public String getUFDst() {
        return UFDst;
    }

    public void setUFDst(String UFDst) {
        this.UFDst = UFDst;
    }

    public String getAssinatura() {
        return Assinatura;
    }

    public void setAssinatura(String Assinatura) {
        this.Assinatura = Assinatura;
    }

    public String getAssinaturaDestino() {
        return AssinaturaDestino;
    }

    public void setAssinaturaDestino(String AssinaturaDestino) {
        this.AssinaturaDestino = AssinaturaDestino;
    }

    public String getColetaOri() {
        return ColetaOri;
    }

    public void setColetaOri(String ColetaOri) {
        this.ColetaOri = ColetaOri;
    }

    public String getRotaOri() {
        return RotaOri;
    }

    public void setRotaOri(String RotaOri) {
        this.RotaOri = RotaOri;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getHora2() {
        return Hora2;
    }

    public void setHora2(String Hora2) {
        this.Hora2 = Hora2;
    }

    public String getColetaDst() {
        return ColetaDst;
    }

    public void setColetaDst(String ColetaDst) {
        this.ColetaDst = ColetaDst;
    }

    public String getRotaDst() {
        return RotaDst;
    }

    public void setRotaDst(String RotaDst) {
        this.RotaDst = RotaDst;
    }

    public String getHoraChegada() {
        return HoraChegada;
    }

    public void setHoraChegada(String HoraChegada) {
        this.HoraChegada = HoraChegada;
    }

    public String getHoraSaida() {
        return HoraSaida;
    }

    public void setHoraSaida(String HoraSaida) {
        this.HoraSaida = HoraSaida;
    }

    public String getIEFat() {
        return IEFat;
    }

    public void setIEFat(String IEFat) {
        this.IEFat = IEFat;
    }

    public String getVeiculoOri() {
        return VeiculoOri;
    }

    public void setVeiculoOri(String VeiculoOri) {
        this.VeiculoOri = VeiculoOri;
    }

    public String getVeiculoDst() {
        return VeiculoDst;
    }

    public void setVeiculoDst(String VeiculoDst) {
        this.VeiculoDst = VeiculoDst;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 47 * hash + Objects.hashCode(this.Sequencia);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PreOrder other = (PreOrder) obj;
        if (!Objects.equals(this.Sequencia, other.Sequencia)) {
            return false;
        }
        return true;
    }
}

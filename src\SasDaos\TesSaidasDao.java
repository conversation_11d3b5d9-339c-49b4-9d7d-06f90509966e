package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Pedido;
import SasBeans.TesSaidas;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesSaidasDao {

    private Persistencia persistencia;

    public TesSaidasDao() {
    }

    public TesSaidasDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public List<TesSaidas> listaTesSaidas(String codCli1, String codFil, String serie, Persistencia persistencia) throws Exception {
        try {
            List<TesSaidas> retorno = new ArrayList<>();
            String sql = " SELECT top 2000 *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM TesSaidas WHERE CodCli1 = ? AND CodFil = ? AND Serie = ? \n"
                    + " ORDER BY Guia DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli1);
            consulta.setString(codFil);
            consulta.setString(serie);
            consulta.select();
            TesSaidas tesSaidas;
            while (consulta.Proximo()) {
                tesSaidas = new TesSaidas();
                tesSaidas.setGuia(consulta.getString("Guia"));
                tesSaidas.setSerie(consulta.getString("Serie"));
                tesSaidas.setCodFil(consulta.getString("CodFil"));
                tesSaidas.setData(consulta.getString("DataC"));
                tesSaidas.setCodCli1(consulta.getString("CodCli1"));
                tesSaidas.setTipoMov(consulta.getString("TipoMov"));
                tesSaidas.setCodCli2(consulta.getString("CodCli2"));
                tesSaidas.setTotalDN(consulta.getString("TotalDN"));
                tesSaidas.setTotalGeral(consulta.getString("TotalGeral"));
                tesSaidas.setContaTes(consulta.getString("ContaTes"));
                tesSaidas.setTipoSrv(consulta.getString("TipoSrv"));
                tesSaidas.setCodSrv(consulta.getString("CodSrv"));
                tesSaidas.setObs(consulta.getString("Obs"));
                tesSaidas.setSituacao(consulta.getString("Situacao"));
                tesSaidas.setHrInicio(consulta.getString("HrInicio"));
                tesSaidas.setHrFinal(consulta.getString("HrFinal"));
                tesSaidas.setBaixa(consulta.getString("Baixa"));
                tesSaidas.setOperador(consulta.getString("Operador"));
                tesSaidas.setDt_Alter(consulta.getString("Dt_AlterC"));
                tesSaidas.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesSaidas);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesSaidasDao.listaTesSaidas - " + e.getMessage() + "\r\n"
                    + " SELECT *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM TesSaidas WHERE CodCli1 = " + codCli1 + " AND CodFil = " + codFil + " AND Serie = " + serie);
        }
    }

    public List<TesSaidas> listaTesSaidas(String codCli1, String codFil, String serie, String data, Persistencia persistencia) throws Exception {
        try {
            List<TesSaidas> retorno = new ArrayList<>();
            String sql = " SELECT *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM TesSaidas WHERE CodCli1 = ? AND CodFil = ? AND Serie = ? AND data = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli1);
            consulta.setString(codFil);
            consulta.setString(serie);
            consulta.setString(data);
            consulta.select();
            TesSaidas tesSaidas;
            while (consulta.Proximo()) {
                tesSaidas = new TesSaidas();
                tesSaidas.setGuia(consulta.getString("Guia"));
                tesSaidas.setSerie(consulta.getString("Serie"));
                tesSaidas.setCodFil(consulta.getString("CodFil"));
                tesSaidas.setData(consulta.getString("DataC"));
                tesSaidas.setCodCli1(consulta.getString("CodCli1"));
                tesSaidas.setTipoMov(consulta.getString("TipoMov"));
                tesSaidas.setCodCli2(consulta.getString("CodCli2"));
                tesSaidas.setTotalDN(consulta.getString("TotalDN"));
                tesSaidas.setTotalGeral(consulta.getString("TotalGeral"));
                tesSaidas.setContaTes(consulta.getString("ContaTes"));
                tesSaidas.setTipoSrv(consulta.getString("TipoSrv"));
                tesSaidas.setCodSrv(consulta.getString("CodSrv"));
                tesSaidas.setObs(consulta.getString("Obs"));
                tesSaidas.setSituacao(consulta.getString("Situacao"));
                tesSaidas.setHrInicio(consulta.getString("HrInicio"));
                tesSaidas.setHrFinal(consulta.getString("HrFinal"));
                tesSaidas.setBaixa(consulta.getString("Baixa"));
                tesSaidas.setOperador(consulta.getString("Operador"));
                tesSaidas.setDt_Alter(consulta.getString("Dt_AlterC"));
                tesSaidas.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesSaidas);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesSaidasDao.listaTesSaidas - " + e.getMessage() + "\r\n"
                    + " SELECT *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM TesSaidas WHERE CodCli1 = " + codCli1 + " AND CodFil = " + codFil + " AND Serie = " + serie + " AND Data = " + data);
        }
    }

    /**
     * Busca uma TesSaidas no banco com este id
     *
     * @param codFil
     * @param guia
     * @param serie
     * @return TesSaidas encontrada, ou null
     * @throws Exception
     */
    public TesSaidas getTesSaidasById(String codFil, String guia, String serie) throws Exception {
        String sql = "SELECT TOP 1\n"
                + "Guia, Serie, CodCli1, CodCli2, TotalGeral, Data, Baixa\n"
                + "FROM TesSaidas\n"
                + "WHERE Guia = ? \n"
                + "    AND Serie = ? \n"
                + "    AND CodFil = ? \n;";

        TesSaidas tesSaida = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.select();

            if (consulta.Proximo()) {
                tesSaida = new TesSaidas();

                tesSaida.setGuia(consulta.getString("Guia"));
                tesSaida.setSerie(consulta.getString("Serie"));
                tesSaida.setCodCli1(consulta.getString("CodCli1"));
                tesSaida.setCodCli2(consulta.getString("CodCli2"));
                tesSaida.setTotalGeral(consulta.getString("TotalGeral"));
                tesSaida.setData(consulta.getString("Data"));
                tesSaida.setBaixa(consulta.getString("Baixa"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return tesSaida;
    }

    public void inserirTesSaidas(TesSaidas tesSaidas, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO TesSaidas (Guia, Serie, CodGTV, CodFil, Data, CodCli1, TipoMov, CodCli2, CodCli3, TotalDN, TotalGeral, \n"
                    + " Diferenca, ContaTes, TipoSRV, Situacao, Pedido, Baixa, Dt_Pedido, Operador, Dt_Alter, Hr_Alter, HrInicio) VALUES \n"
                    + " (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tesSaidas.getGuia());
            consulta.setString(tesSaidas.getSerie());
            consulta.setString(tesSaidas.getCodGTV());
            consulta.setString(tesSaidas.getCodFil());
            consulta.setString(tesSaidas.getData());
            consulta.setString(tesSaidas.getCodCli1());
            consulta.setString(tesSaidas.getTipoMov());
            consulta.setString(tesSaidas.getCodCli2());
            consulta.setString(tesSaidas.getCodCli3());
            consulta.setString(tesSaidas.getTotalDN());
            consulta.setString(tesSaidas.getTotalGeral());
            consulta.setString(tesSaidas.getDiferenca());
            consulta.setString(tesSaidas.getContaTes());
            consulta.setString(tesSaidas.getTipoSrv());
            consulta.setString(tesSaidas.getSituacao());
            consulta.setString(tesSaidas.getPedido());
            consulta.setString(tesSaidas.getBaixa());
            consulta.setString(tesSaidas.getDt_Pedido());
            consulta.setString(tesSaidas.getOperador());
            consulta.setString(tesSaidas.getDt_Alter());
            consulta.setString(tesSaidas.getHr_Alter());
            consulta.setString(tesSaidas.getHrInicio());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesSaidaDao.inserirTesSaidas - " + e.getMessage());
        }
    }

    public List<TesSaidas> getTesSaidaEntrega(String sequencia, String parada, String codfil, Persistencia persistencia) throws Exception {
        List<TesSaidas> listTesSaidas = new ArrayList();
        //List<Pedido> ListPedido = new ArrayList();
        try {
            Consulta rsgtv = new Consulta("Select "
                    + " TesSaidas.Guia, "
                    + " TesSaidas.Serie, "
                    + " TesSaidas.TotalGeral, "
                    + " Pedido.OS "
                    + " from Pedido"
                    + " left join TesSaidas as TesSaidas on  TesSaidas.Pedido  = Pedido.Numero "
                    + " and TesSaidas.Codcli2 = Pedido.Codcli2 "
                    + " and TesSaidas.CodFil  = Pedido.CodFil  "
                    + " Left Join Rt_Guias as Rt_Guias  on  Rt_Guias.Guia  = TesSaidas.Guia "
                    + " and Rt_Guias.Serie = TesSaidas.Serie "
                    + " where Pedido.SeqRota = ?"
                    + " and Pedido.Parada  = ?"
                    + " and Pedido.CodFil  = ?", persistencia);
            rsgtv.setString(sequencia);
            rsgtv.setString(parada);
            rsgtv.setString(codfil);
            rsgtv.select();
            while (rsgtv.Proximo()) {
                TesSaidas temp = new TesSaidas();
                Pedido temp2 = new Pedido();
                temp.setGuia(rsgtv.getString("Guia").substring(0, rsgtv.getString("Guia").indexOf(".")));
                temp.setSerie(rsgtv.getString("Serie"));
                temp.setTotalGeral(rsgtv.getString("TotalGeral"));
                temp2.setOS(rsgtv.getString("OS"));
                listTesSaidas.add(temp);
                //ListPedido.add(temp2);
            }
            rsgtv.Close();
            return listTesSaidas;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar Guias - " + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class MobileContatos {

    private String Nome;
    private BigDecimal CodPessoa;
    private BigDecimal CodContato;
    private int Situacao;
    private String Operador;
    private String Data;
    private String Hora;
    private String vistorUltimo;
    private String rota;

    public String getVistorUltimo() {
        return vistorUltimo;
    }

    public void setVistorUltimo(String vistorUltimo) {
        this.vistorUltimo = vistorUltimo;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public BigDecimal getCodContato() {
        return CodContato;
    }

    public void setCodContato(BigDecimal CodContato) {
        this.CodContato = CodContato;
    }

    public int getSituacao() {
        return Situacao;
    }

    public void setSituacao(int Situacao) {
        this.Situacao = Situacao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getRota() {
        return rota;
    }

    public void setRota(String rota) {
        this.rota = rota;
    }
}

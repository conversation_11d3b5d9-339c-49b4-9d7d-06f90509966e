/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 *
 * <AUTHOR>
 */
public class LerArquivo {

    /**
     * Lê o conteúdo de um arquivo a partir de um inputStream
     *
     * @param inputStream
     * @return
     * @throws Exception
     */
    public static String obterConteudo(InputStream inputStream) throws Exception {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = inputStream.read(buffer)) != -1) {
            result.write(buffer, 0, length);
        }
        String retorno = result.toString("UTF-8");
        inputStream.close();
        result.close();
        return retorno;
    }
}

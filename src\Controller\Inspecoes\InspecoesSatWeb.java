/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Inspecoes;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Inspecoes;
import SasBeans.InspecoesItens;
import SasBeans.PstInspecao;
import SasBeans.PstServ;
import SasBeansCompostas.PstInspecaoRelatorio;
import SasDaos.ClientesDao;
import SasDaos.FiliaisDao;
import SasDaos.InspecoesDao;
import SasDaos.InspecoesItensDao;
import SasDaos.PstInspecaoDao;
import SasDaos.PstServDao;
import br.com.sasw.pacotesuteis.sasbeans.InspecoesItensLista;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarString;
import br.com.sasw.pacotesuteis.utilidades.LerArquivo;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class InspecoesSatWeb {

    /**
     * Lista todas as inspeções da empresa
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Inspecoes> getAllInspecoes(Persistencia persistencia) throws Exception {
        try {
            InspecoesDao inspecoesDao = new InspecoesDao();
            return inspecoesDao.getAllInspecoes(persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Insere uma nova inspeção no banco
     *
     * @param inspecao
     * @param persistencia
     * @throws Exception
     */
    public void inserirInspecao(Inspecoes inspecao, Persistencia persistencia) throws Exception {
        try {
            InspecoesDao inspecoesDao = new InspecoesDao();
            inspecoesDao.putInspecao(inspecao, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Apaga uma inspeção no banco
     *
     * @param inspecao
     * @param persistencia
     * @throws Exception
     */
    public void removerInspecao(Inspecoes inspecao, Persistencia persistencia) throws Exception {
        try {
            InspecoesDao inspecoesDao = new InspecoesDao();
            inspecoesDao.deleteInspecao(inspecao, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Lista todas as inspeções da empresa
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<InspecoesItens> getAllInspecoesItens(String codigo, Persistencia persistencia) throws Exception {
        try {
            InspecoesItensDao inspecoesItensDao = new InspecoesItensDao();
            return inspecoesItensDao.getItensInspecao(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Insere uma nova inspeção no banco
     *
     * @param inspecaoItens
     * @param persistencia
     * @throws Exception
     */
    public void inserirInspecaoItem(InspecoesItens inspecaoItens, Persistencia persistencia) throws Exception {
        try {
            InspecoesItensDao inspecoesDao = new InspecoesItensDao();
            inspecoesDao.putInspecaoItem(inspecaoItens, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Insere uma nova inspeção no banco
     *
     * @param inspecaoItens
     * @param inspecaoItensLista
     * @param persistencia
     * @throws Exception
     */
    public void inserirInspecaoItem(InspecoesItens inspecaoItens, List<InspecoesItensLista> inspecaoItensLista, Persistencia persistencia) throws Exception {
        try {
            InspecoesItensDao inspecoesDao = new InspecoesItensDao();
            inspecoesDao.putInspecaoItem(inspecaoItens, inspecaoItensLista, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * remove um item de inspeção do banco
     *
     * @param inspecaoItens
     * @param persistencia
     * @throws Exception
     */
    public void removerInspecaoItem(InspecoesItens inspecaoItens, Persistencia persistencia) throws Exception {
        try {
            InspecoesItensDao inspecoesDao = new InspecoesItensDao();
            inspecoesDao.deleteInspecaoItem(inspecaoItens, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * edita um item de inspeção do banco
     *
     * @param inspecaoItens
     * @param persistencia
     * @throws Exception
     */
    public void editarItemInspecao(InspecoesItens inspecaoItens, List<InspecoesItensLista> inspecoesItensLista, Persistencia persistencia) throws Exception {
        try {
            InspecoesItensDao inspecoesDao = new InspecoesItensDao();
            inspecoesDao.updateInspecaoItem(inspecaoItens, inspecoesItensLista, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Listagem paginada de inspeções de posto
     *
     * @param primeiro
     * @param linhas
     * @param filtros
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstInspecao> getPstInspecoes(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            PstInspecaoDao pstInspecaoDao = new PstInspecaoDao();
            return pstInspecaoDao.listagemPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    public List<PstInspecao> getDetalhesPstInspecoes(PstInspecao pstInspecao, Persistencia persistencia) throws Exception {
        try {
            PstInspecaoDao pstInspecaoDao = new PstInspecaoDao();
            return pstInspecaoDao.listaInspecoes(pstInspecao.getSecao(), pstInspecao.getCodfil(), pstInspecao.getData(),
                    pstInspecao.getMatr(), pstInspecao.getCodigo(), persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    public String getRelatorio(PstInspecao pstInspecao, String idioma, Persistencia persistencia) throws Exception {
        InspecoesDao inspecoesDao = new InspecoesDao();
        Inspecoes inspecoes = inspecoesDao.getInspecoes(pstInspecao.getCodInspecao(), persistencia);

        ClientesDao clientesDao = new ClientesDao();
        Clientes cliente = clientesDao.clientePosto(pstInspecao.getSecao(), pstInspecao.getCodfil(), persistencia);

        FiliaisDao filiaisDao = new FiliaisDao();
        Filiais filial = filiaisDao.getFilial(pstInspecao.getCodfil(), persistencia);

        String htmlEmail = LerArquivo.obterConteudo(InspecoesSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));

        htmlEmail = htmlEmail.replace("@TituloPagina", "@RelatorioInspecao");

        htmlEmail = htmlEmail.replace("@TituloRelatorio", inspecoes.getDescricao());
        htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
        htmlEmail = htmlEmail.replace("@TituloInfo", "");
        htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". " + cliente.getCidade() + "/" + cliente.getEstado());
        htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));
        htmlEmail = htmlEmail.replace("@Detalhes", "@Detalhes");

        String padrao2colunas = LerArquivo.obterConteudo(InspecoesSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

        StringBuilder relatorioEmail = new StringBuilder();

        relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Posto").replace("@TextoPadrao", cliente.getContato()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getNRed() + " - " + cliente.getNome()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getEnde() + " - " + cliente.getBairro()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getCidade() + "/" + cliente.getEstado() + " - "
                + (idioma.equals("en") ? (cliente.getCEP().startsWith("000") ? cliente.getCEP().replace("000", "") : cliente.getCEP()) : formatarString(cliente.getCEP(), "#####-###"))));

        String dataHtml;
        try {
            LocalDate datetime = LocalDate.parse(pstInspecao.getData(), DateTimeFormatter.ofPattern("yyyyMMdd"));
            dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        } catch (Exception xxx) {
            dataHtml = pstInspecao.getData();
        }

        relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Data").replace("@TextoPadrao", dataHtml));

        htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

        htmlEmail = htmlEmail.replace("@Script", "");
        htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(persistencia.getEmpresa(), "0"));
        htmlEmail = htmlEmail.replace("@URL", "");
        htmlEmail = htmlEmail.replace("@MensagemUrl", "");
        return htmlEmail;
    }

    /**
     * Conta a quantidade de filtros
     *
     * @param filtros
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public int qtdPstInspecoes(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            PstInspecaoDao pstInspecaoDao = new PstInspecaoDao();
            return pstInspecaoDao.contagem(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("InspecoesSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listar postos para filtro
     *
     * @param codPessoa
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstServ> getPstSerList(BigDecimal codPessoa, String codfil, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstServDao = new PstServDao();
            return pstServDao.listarPostosPessoa(codPessoa, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Busca inspeção a partir de codFil e seção
     *
     * @param codFil código da filial
     * @param dataIni data inicial
     * @param persistencia
     * @return lista de PstInspecao e Cliente
     * @throws Exception
     */
    public List<PstInspecaoRelatorio> listaInspecaoRelatorio(String codFil, String dataIni, Persistencia persistencia) throws Exception {
        PstInspecaoDao dao = new PstInspecaoDao();
        return dao.listaInspecaoRelatorio(codFil, dataIni, persistencia);
    }
}

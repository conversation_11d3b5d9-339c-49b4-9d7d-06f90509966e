/*
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.Messages;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("tradutorEmpresas")
public class TradutorEmpresas implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        String[] empresas = value.toString().split(", ");
        String retorno = new String();
        for (String empresa : empresas) {
            retorno = retorno + Messages.getMessageS(empresa) + ", ";
        }
        retorno = retorno.substring(0, retorno.lastIndexOf(", "));
        return retorno;
    }
}

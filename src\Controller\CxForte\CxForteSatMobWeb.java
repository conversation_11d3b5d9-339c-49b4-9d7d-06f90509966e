package Controller.CxForte;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.GTV;
import SasBeans.OS_Vig;
import SasBeans.Rt_Perc;
import SasBeans.TesSaidas;
import SasBeansCompostas.CxFEntradasDTO;
import SasBeansCompostas.CxFGuiasGTVDTO;
import SasDaos.CxFGuiasDao;
import SasDaos.CxFGuiasVolDao;
import SasDaos.CxForteDao;
import SasDaos.GTVDao;
import SasDaos.OS_VigDao;
import SasDaos.Rt_GuiasDao;
import SasDaos.Rt_PercDao;
import SasDaos.TesSaidasDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CxForteSatMobWeb {

    private final Persistencia persistencia;
    private final CxForteDao cxForteDao;
    private final CxFGuiasDao cxfGuiasDao;
    private final TesSaidasDao tesSaidasDao;
    private final CxFGuiasVolDao cxfguiasvolDao;
    private final GTVDao gtvDao;
    private final OS_VigDao OSVigDao;
    private final Rt_PercDao rt_PercDao;
    private final Rt_GuiasDao rt_GuiasDao;

    public CxForteSatMobWeb(Persistencia persistencia) {
        this.persistencia = persistencia;
        cxForteDao = new CxForteDao(persistencia);
        cxfGuiasDao = new CxFGuiasDao(persistencia);
        tesSaidasDao = new TesSaidasDao(persistencia);
        cxfguiasvolDao = new CxFGuiasVolDao(persistencia);
        gtvDao = new GTVDao();
        OSVigDao = new OS_VigDao();
        rt_PercDao = new Rt_PercDao();
        rt_GuiasDao = new Rt_GuiasDao();
    }

    public List<CxForte> getCxForteListFromCodFil(String codFil) throws Exception {
        try {
            BigDecimal cod = new BigDecimal(codFil);
            return cxForteDao.caixasFortes(cod, persistencia);
        } catch (Exception e) {
            // listarQuantidadesEValores
            throw e;
        }
    }

    public List<CxFEntradasDTO> listaTodasEntradasCxForte(Map filters) throws Exception {
        try {
            return cxForteDao.listaTodasEntradasCxForte(filters);
        } catch (Exception e) {
            // listarQuantidadesEValores
            throw e;
        }
    }

    public int contagemEntradasCxForte(Map filters) throws Exception {
        try {
            return cxForteDao.contagemEntradasCxForte(filters);
        } catch (Exception e) {
            // listarQuantidadesEValores
            throw e;
        }
    }

    public List<CxFGuias> getCxfGuiasEntrega2(String seqRota, String hora1) throws Exception {
        try {
            return cxfGuiasDao.getCxfGuiasEntrega2(seqRota, hora1);
        } catch (Exception e) {
            // listarQuantidadesEValores
            throw e;
        }
    }

    public boolean isGuiaPendenteTesouraria(String codFil, String guia, String serie) throws Exception {
        TesSaidas tesSaida = tesSaidasDao.getTesSaidasById(codFil, guia, serie);
        if (tesSaida == null) {
            // throw new Exception("getRtPercListFromDataNRed: TESSaida nao encontrada.");
        } else if (tesSaida.getBaixa().equals("N")) {
            return true;
        }
        return false;
    }

    public CxFGuiasVol obtemPrimeiroTipoGuiasVol(String codFil, String guia, String serie) throws Exception {
        try {
            List<CxFGuiasVol> guiasVol = cxfguiasvolDao.listarTiposComId(codFil, guia, serie);
            return guiasVol.isEmpty() ? null : guiasVol.get(0);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public CxFGuiasVol obtemPrimeiroQuantidadesEValores(String codFil, String guia, String serie) throws Exception {
        try {
            List<CxFGuiasVol> guiasVol = cxfguiasvolDao.listarQuantidadesEValores(codFil, guia, serie);
            return guiasVol.get(0);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    ////////////////////////////////////////////////////////////////////////////
    public List<Rt_Perc> getFromSequenciaCodCli2(String sequencia, String codCliCaixaForte) throws Exception {
        try {
            return rt_PercDao.getFromSequenciaCodCli2(sequencia, codCliCaixaForte, persistencia);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public List<CxFGuiasGTVDTO> getListaCxForteEntBuscaL(String codFil, String rota, String guia, String serie, String sequencia) throws Exception {
        try {
            if (rota.equals("090")) {
                return cxForteDao.getCxForteEntBuscaTesSaida(codFil, guia, serie, sequencia);
            } else {
                return cxForteDao.getCxForteEntBuscaGTV(codFil, guia, serie, sequencia);
            }
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public boolean semGuiaSerieRtGuias(String guia, String serie) throws Exception {
        try {
            return rt_GuiasDao.contagemGuiaSerie(guia, serie, persistencia) == 0;
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public List<Rt_Perc> getListaHora1DeSequencia(String sequencia) throws Exception {
        try {
            return rt_PercDao.getListaHora1DeSequencia(sequencia, persistencia);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public String getParadaCaixaForte(String sequencia, String codCxf) throws Exception {
        try {
            return rt_PercDao.getHora1DeSequenciaECodCli(sequencia, codCxf, persistencia);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public List<Rt_Perc> getParadasOriCaixaForte(String sequencia, String codCliOri, String codCxf) throws Exception {
        try {
            return rt_PercDao.getHora1DeSequenciaECodCli1e2(sequencia, codCliOri, codCxf, persistencia);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public Rt_Perc getRtPercFromDataNRed(String data, String nomeRed) throws Exception {
        try {
            List<Rt_Perc> lista = rt_PercDao.getRtPercListFromDataNRed(data, nomeRed, persistencia);
            return lista.isEmpty() ? null : lista.get(0);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public void updateOSDeGTV(String codFil, String guia, String serie, String OS) throws Exception {
        try {
            gtvDao.updateOSDeGTV(codFil, guia, serie, OS, persistencia);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public BigDecimal getTotalGeralRtGuias(String guia, String serie) throws Exception {
        try {
            return rt_GuiasDao.getTotalGeral(guia, serie, persistencia);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public BigDecimal getTotalGeralCxfGuiasVol(String guia, String serie) throws Exception {
        try {
            return cxfguiasvolDao.getTotalGeral(guia, serie, persistencia);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public OS_Vig buscaOsNaGtvPreImpressa(String codFil, String guia, String serie) throws Exception {
        try {
            return OSVigDao.getOSComGTVById(codFil, guia, serie, persistencia);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public List<OS_Vig> buscaOsParametroParada(String codFil, String codCli, String cliDst) throws Exception {
        try {
            if (codCli == null || (codCli.length() > 3 && codCli.charAt(3) != '7')) {
                return OSVigDao.getListaOSFromCodCli(codFil, codCli, null, null, persistencia);
            } else {
                // cliDst, codCli invertidos
                return OSVigDao.getListaOSFromCodCli(codFil, cliDst, codCli, "A", persistencia);
            }
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }
    ////////////////////////////////////////////////////////////////////////////

    public OS_Vig obterOS(String codFil, String guia, String serie) throws Exception {
        try {
            GTV gtv;
            List<GTV> lista = gtvDao.getGTV(guia, serie, codFil, persistencia);
            if (!lista.isEmpty()) {
                gtv = lista.get(0);
            } else {
                throw new Exception("TODO: getGTV vazio");
            }
            return OSVigDao.obterOS(codFil, gtv.getOS().toString(), persistencia);
        } catch (Exception e) {
            throw e;
        }
    }

    public Clientes obterCliente(String codFil, String OS) throws Exception {
        try {
            return OSVigDao.getClienteDeOS(codFil, OS, persistencia);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inserirGuiaSerie(
            String codFil,
            String codCXF,
            String guia,
            String serie,
            String clienteOrigem,
            String clienteDestino,
            String valor,
            String tipo,
            String rota,
            String usuario,
            String data,
            String horaAtual,
            String sequenciaRota,
            String OS,
            String hora1Parada
    ) throws Exception {
        try {
            if (cxfGuiasDao.verificaGuiaSerieExiste(guia, serie) == 0) {
                cxfGuiasDao.inserirGuiaSerie(codFil, codCXF, guia, serie, clienteOrigem, clienteDestino, valor, tipo, rota, usuario, data, horaAtual, sequenciaRota, OS, hora1Parada);
            } else {
                throw new Exception("GuiaEntrouEmCxForte");
            }
        } catch (Exception e) {
            throw e; // FIXME: distinguir exceção de SQL de GuiaEntrouEmCxForte
        }
    }

    public CxFGuias getSeqRotaSaiByGuiaSerie(String guia, String serie) throws Exception {
        try {
            return cxfGuiasDao.getSeqRotaSaiByGuiaSerie(guia, serie);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

    public void deletarGuiaSerie(
            String codFil,
            String codCXF,
            String guia,
            String serie,
            String operador
    ) throws Exception {
        try {
            cxForteDao.deletarGuiaSerie(codFil, codCXF, guia, serie, operador);
        } catch (Exception e) {
            // TODO
            throw e;
        }
    }

}

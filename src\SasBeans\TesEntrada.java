package SasBeans;

import SasBeansCompostas.TesConta;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TesEntrada {

    private BigDecimal CodFil;
    private BigDecimal Guia;
    private String Serie;
    private String Data;
    private String CodCli1;
    private String NRed1;
    private String Ende1;
    private String CodCli2;
    private String NRed2;
    private String TipoMov;
    private String CodSrv;
    private String Lote;
    private BigDecimal ContaTes;
    private BigDecimal OS;
    private BigDecimal Valor;
    private BigDecimal Qtde;
    private BigDecimal ChequesQtde;
    private BigDecimal ChequesValor;
    private BigDecimal TicketsQtde;
    private BigDecimal TicketsValor;
    private BigDecimal MoedasValor;
    private BigDecimal OCTQtde;
    private BigDecimal OCTValor;
    private BigDecimal ValorRec;
    private BigDecimal DifMaior;
    private BigDecimal DifMenor;
    private BigDecimal DivMaior;
    private BigDecimal DivMenor;
    private BigDecimal CedFalsaQtde;
    private BigDecimal CedFalsaValor;
    private BigDecimal MatrConf;
    private String HrInicio;
    private String HrFinal;
    private BigDecimal Tempo;
    private BigDecimal TotalDN;
    private BigDecimal TotalDD;
    private BigDecimal TotalMoeda;
    private BigDecimal QtdeCed;
    private String Obs;
    private String Situacao;
    private String Faturar;
    private BigDecimal NroMaqConf;
    private String OperIncl;
    private String Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String OperRec;
    private String Dt_Rec;
    private String Hr_Rec;
    private BigDecimal Remessa;
    private String ContaDesc;
    private String QtdeVol;
    private Clientes clienteOrigem, clienteDestino;
    private OS_Vig os_vig;
    private TesConta conta, tesLote;
    private List<CxFGuiasVol> volumes = new ArrayList();

    private String NomeConferente;
    private String FilialDescricao;
    
    private BigDecimal QtdeHora, ValorHora;
    private String TempoHora;
    
    private final BigDecimal ZERO = new BigDecimal("0");

    public TesConta getConta() {
        return conta;
    }

    public void setConta(TesConta conta) {
        this.conta = conta;
    }

    public TesConta getTesLote() {
        return tesLote;
    }

    public void setTesLote(TesConta tesLote) {
        this.tesLote = tesLote;
    }

    public List<CxFGuiasVol> getVolumes() {
        return volumes;
    }

    public void setVolumes(List<CxFGuiasVol> volumes) {
        this.volumes = volumes;
    }

    public TesEntrada() {
        this.CodFil = ZERO;
        this.Guia = ZERO;
        this.Serie = "";
        this.Data = "";
        this.CodCli1 = "";
        this.NRed1 = "";
        this.Ende1 = "";
        this.CodCli2 = "";
        this.NRed2 = "";
        this.TipoMov = "";
        this.CodSrv = "";
        this.Lote = "";
        this.ContaTes = ZERO;
        this.OS = ZERO;
        this.Valor = ZERO;
        this.Qtde = ZERO;
        this.ChequesQtde = ZERO;
        this.ChequesValor = ZERO;
        this.TicketsQtde = ZERO;
        this.TicketsValor = ZERO;
        this.MoedasValor = ZERO;
        this.OCTQtde = ZERO;
        this.OCTValor = ZERO;
        this.ValorRec = ZERO;
        this.DifMaior = ZERO;
        this.DifMenor = ZERO;
        this.DivMaior = ZERO;
        this.DivMenor = ZERO;
        this.CedFalsaQtde = ZERO;
        this.CedFalsaValor = ZERO;
        this.MatrConf = ZERO;
        this.HrInicio = "";
        this.HrFinal = "";
        this.Tempo = ZERO;
        this.TotalDN = ZERO;
        this.TotalDD = ZERO;
        this.TotalMoeda = ZERO;
        this.QtdeCed = ZERO;
        this.Obs = "";
        this.Situacao = "";
        this.Faturar = "";
        this.NroMaqConf = ZERO;
        this.OperIncl = "";
        this.Dt_Incl = "";
        this.Hr_Incl = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
        this.OperRec = "";
        this.Dt_Rec = "";
        this.Hr_Rec = "";
        this.Remessa = ZERO;
        this.ContaDesc = "";
        this.QtdeVol = "";

        this.clienteOrigem = new Clientes();
        this.clienteDestino = new Clientes();
        this.os_vig = new OS_Vig();
        this.conta = new TesConta();
        this.tesLote = new TesConta();
    }

    public TesEntrada(TesEntrada original) {
        this.CodFil = original.getCodFil();
        this.Guia = original.getGuia();
        this.Serie = original.getSerie();
        this.Data = original.getData();
        this.CodCli1 = original.getCodCli1();
        this.NRed1 = original.getNRed1();
        this.Ende1 = original.getEnde1();
        this.CodCli2 = original.getCodCli2();
        this.NRed2 = original.getNRed2();
        this.TipoMov = original.getTipoMov();
        this.CodSrv = original.getCodSrv();
        this.Lote = original.getLote();
        this.ContaTes = original.getContaTes();
        this.OS = original.getOS();
        this.Valor = original.getValor();
        this.Qtde = original.getQtde();
        this.ChequesQtde = original.getChequesQtde();
        this.ChequesValor = original.getChequesValor();
        this.TicketsQtde = original.getTicketsQtde();
        this.TicketsValor = original.getTicketsValor();
        this.MoedasValor = original.getMoedasValor();
        this.OCTQtde = original.getOCTQtde();
        this.OCTValor = original.getOCTValor();
        this.ValorRec = original.getValorRec();
        this.DifMaior = original.getDifMaior();
        this.DifMenor = original.getDifMenor();
        this.DivMaior = original.getDivMaior();
        this.DivMenor = original.getDivMenor();
        this.CedFalsaQtde = original.getCedFalsaQtde();
        this.CedFalsaValor = original.getCedFalsaValor();
        this.MatrConf = original.getMatrConf();
        this.HrInicio = original.getHrInicio();
        this.HrFinal = original.getHrFinal();
        this.Tempo = original.getTempo();
        this.TotalDN = original.getTotalDN();
        this.TotalDD = original.getTotalDD();
        this.TotalMoeda = original.getTotalMoeda();
        this.QtdeCed = original.getQtdeCed();
        this.Obs = original.getObs();
        this.Situacao = original.getSituacao();
        this.Faturar = original.getFaturar();
        this.NroMaqConf = original.getNroMaqConf();
        this.OperIncl = original.getOperIncl();
        this.Dt_Incl = original.getDt_Incl();
        this.Hr_Incl = original.getHr_Incl();
        this.Operador = original.getOperador();
        this.Dt_Alter = original.getDt_Alter();
        this.Hr_Alter = original.getHr_Alter();
        this.OperRec = original.getOperRec();
        this.Dt_Rec = original.getDt_Rec();
        this.Hr_Rec = original.getHr_Rec();
        this.Remessa = original.getRemessa();
        this.ContaDesc = original.getContaDesc();
        this.QtdeVol = original.getQtdeVol();
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getDt_Rec() {
        return Dt_Rec;
    }

    public void setDt_Rec(String Dt_Rec) {
        this.Dt_Rec = Dt_Rec;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("1");
        }
    }

    public BigDecimal getGuia() {
        return Guia;
    }

    public void setGuia(BigDecimal Guia) {
        this.Guia = Guia;
    }

    public void setGuia(String Guia) {
        try {
            this.Guia = new BigDecimal(Guia);
        } catch (Exception e) {
            this.Guia = new BigDecimal("0");
        }
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getCodCli2() {
        return CodCli2;
    }

    public void setCodCli2(String CodCli2) {
        this.CodCli2 = CodCli2;
    }

    public String getTipoMov() {
        return TipoMov;
    }

    public void setTipoMov(String TipoMov) {
        this.TipoMov = TipoMov;
    }

    public String getCodSrv() {
        return CodSrv;
    }

    public void setCodSrv(String CodSrv) {
        this.CodSrv = CodSrv;
    }

    public String getLote() {
        return Lote;
    }

    public void setLote(String Lote) {
        this.Lote = Lote;
    }

    public BigDecimal getContaTes() {
        return ContaTes;
    }

    public void setContaTes(BigDecimal ContaTes) {
        this.ContaTes = ContaTes;
    }

    public void setContaTes(String ContaTes) {
        try {
            this.ContaTes = new BigDecimal(ContaTes);
        } catch (Exception e) {
            this.ContaTes = new BigDecimal("0");
        }
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public BigDecimal getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        try {
            this.Qtde = new BigDecimal(Qtde);
        } catch (Exception e) {
            this.Qtde = new BigDecimal("0");
        }
    }

    public BigDecimal getChequesQtde() {
        return ChequesQtde;
    }

    public void setChequesQtde(String ChequesQtde) {
        try {
            this.ChequesQtde = new BigDecimal(ChequesQtde);
        } catch (Exception e) {
            this.ChequesQtde = new BigDecimal("0");
        }
    }

    public BigDecimal getChequesValor() {
        return ChequesValor;
    }

    public void setChequesValor(String ChequesValor) {
        try {
            this.ChequesValor = new BigDecimal(ChequesValor);
        } catch (Exception e) {
            this.ChequesValor = new BigDecimal("0");
        }
    }

    public BigDecimal getTicketsQtde() {
        return TicketsQtde;
    }

    public void setTicketsQtde(String TicketsQtde) {
        try {
            this.TicketsQtde = new BigDecimal(TicketsQtde);
        } catch (Exception e) {
            this.TicketsQtde = new BigDecimal("0");
        }
    }

    public BigDecimal getTicketsValor() {
        return TicketsValor;
    }

    public void setTicketsValor(String TicketsValor) {
        try {
            this.TicketsValor = new BigDecimal(TicketsValor);
        } catch (Exception e) {
            this.TicketsValor = new BigDecimal("0");
        }
    }

    public BigDecimal getMoedasValor() {
        return MoedasValor;
    }

    public void setMoedasValor(String MoedasValor) {
        try {
            this.MoedasValor = new BigDecimal(MoedasValor);
        } catch (Exception e) {
            this.MoedasValor = new BigDecimal("0");
        }
    }

    public BigDecimal getOCTQtde() {
        return OCTQtde;
    }

    public void setOCTQtde(String OCTQtde) {
        try {
            this.OCTQtde = new BigDecimal(OCTQtde);
        } catch (Exception e) {
            this.OCTQtde = new BigDecimal("0");
        }
    }

    public BigDecimal getOCTValor() {
        return OCTValor;
    }

    public void setOCTValor(String OCTValor) {
        try {
            this.OCTValor = new BigDecimal(OCTValor);
        } catch (Exception e) {
            this.OCTValor = new BigDecimal("0");
        }
    }

    public BigDecimal getValorRec() {
        return ValorRec;
    }

    public void setValorRec(String ValorRec) {
        try {
            this.ValorRec = new BigDecimal(ValorRec);
        } catch (Exception e) {
            this.ValorRec = new BigDecimal("0");
        }
    }

    public BigDecimal getDifMaior() {
        return DifMaior;
    }

    public void setDifMaior(String DifMaior) {
        try {
            this.DifMaior = new BigDecimal(DifMaior);
        } catch (Exception e) {
            this.DifMaior = new BigDecimal("0");
        }
    }

    public BigDecimal getDifMenor() {
        return DifMenor;
    }

    public void setDifMenor(String DifMenor) {
        try {
            this.DifMenor = new BigDecimal(DifMenor);
        } catch (Exception e) {
            this.DifMenor = new BigDecimal("0");
        }
    }

    public BigDecimal getDivMaior() {
        return DivMaior;
    }

    public void setDivMaior(String DivMaior) {
        try {
            this.DivMaior = new BigDecimal(DivMaior);
        } catch (Exception e) {
            this.DivMaior = new BigDecimal("0");
        }
    }

    public BigDecimal getDivMenor() {
        return DivMenor;
    }

    public void setDivMenor(String DivMenor) {
        try {
            this.DivMenor = new BigDecimal(DivMenor);
        } catch (Exception e) {
            this.DivMenor = new BigDecimal("0");
        }
    }

    public BigDecimal getCedFalsaQtde() {
        return CedFalsaQtde;
    }

    public void setCedFalsaQtde(String CedFalsaQtde) {
        try {
            this.CedFalsaQtde = new BigDecimal(CedFalsaQtde);
        } catch (Exception e) {
            this.CedFalsaQtde = new BigDecimal("0");
        }
    }

    public BigDecimal getCedFalsaValor() {
        return CedFalsaValor;
    }

    public void setCedFalsaValor(String CedFalsaValor) {
        try {
            this.CedFalsaValor = new BigDecimal(CedFalsaValor);
        } catch (Exception e) {
            this.CedFalsaValor = new BigDecimal("0");
        }
    }

    public BigDecimal getMatrConf() {
        return MatrConf;
    }

    public void setMatrConf(String MatrConf) {
        try {
            this.MatrConf = new BigDecimal(MatrConf);
        } catch (Exception e) {
            this.MatrConf = new BigDecimal("0");
        }
    }

    public String getHrInicio() {
        return HrInicio;
    }

    public void setHrInicio(String HrInicio) {
        this.HrInicio = HrInicio;
    }

    public String getHrFinal() {
        return HrFinal;
    }

    public void setHrFinal(String HrFinal) {
        this.HrFinal = HrFinal;
    }

    public BigDecimal getTempo() {
        return Tempo;
    }

    public void setTempo(String Tempo) {
        try {
            this.Tempo = new BigDecimal(Tempo);
        } catch (Exception e) {
            this.Tempo = new BigDecimal("0");
        }
    }

    public BigDecimal getTotalDN() {
        return TotalDN;
    }

    public void setTotalDN(String TotalDN) {
        try {
            this.TotalDN = new BigDecimal(TotalDN);
        } catch (Exception e) {
            this.TotalDN = new BigDecimal("0");
        }
    }

    public BigDecimal getTotalDD() {
        return TotalDD;
    }

    public void setTotalDD(String TotalDD) {
        try {
            this.TotalDD = new BigDecimal(TotalDD);
        } catch (Exception e) {
            this.TotalDD = new BigDecimal("0");
        }
    }

    public BigDecimal getTotalMoeda() {
        return TotalMoeda;
    }

    public void setTotalMoeda(String TotalMoeda) {
        try {
            this.TotalMoeda = new BigDecimal(TotalMoeda);
        } catch (Exception e) {
            this.TotalMoeda = new BigDecimal("0");
        }
    }

    public BigDecimal getQtdeCed() {
        return QtdeCed;
    }

    public void setQtdeCed(String QtdeCed) {
        try {
            this.QtdeCed = new BigDecimal(QtdeCed);
        } catch (Exception e) {
            this.QtdeCed = new BigDecimal("0");
        }
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getFaturar() {
        return Faturar;
    }

    public void setFaturar(String Faturar) {
        this.Faturar = Faturar;
    }

    public BigDecimal getNroMaqConf() {
        return NroMaqConf;
    }

    public void setNroMaqConf(String NroMaqConf) {
        try {
            this.NroMaqConf = new BigDecimal(NroMaqConf);
        } catch (Exception e) {
            this.NroMaqConf = new BigDecimal("0");
        }
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOperRec() {
        return OperRec;
    }

    public void setOperRec(String OperRec) {
        this.OperRec = OperRec;
    }

    public String getHr_Rec() {
        return Hr_Rec;
    }

    public void setHr_Rec(String Hr_Rec) {
        this.Hr_Rec = Hr_Rec;
    }

    public String getNRed1() {
        return NRed1;
    }

    public void setNRed1(String NRed1) {
        this.NRed1 = NRed1;
    }

    public String getNRed2() {
        return NRed2;
    }

    public void setNRed2(String NRed2) {
        this.NRed2 = NRed2;
    }

    public String getEnde1() {
        return Ende1;
    }

    public void setEnde1(String Ende1) {
        this.Ende1 = Ende1;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        return hash;
    }

    @Override
    public String toString() {
        return "TesEntrada{" + "CodFil=" + CodFil + ", Guia=" + Guia + ", Serie=" + Serie + '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TesEntrada other = (TesEntrada) obj;
        if (!Objects.equals(this.Serie, other.Serie)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        if (!Objects.equals(this.Guia, other.Guia)) {
            return false;
        }
        return true;
    }

    public BigDecimal getRemessa() {
        return Remessa;
    }

    public void setRemessa(BigDecimal Remessa) {
        this.Remessa = Remessa;
    }

    public String getContaDesc() {
        return ContaDesc;
    }

    public void setContaDesc(String ContaDesc) {
        this.ContaDesc = ContaDesc;
    }

    public String getQtdeVol() {
        return QtdeVol;
    }

    public void setQtdeVol(String QtdeVol) {
        this.QtdeVol = QtdeVol;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public void setOS(BigDecimal OS) {
        this.OS = OS;
    }

    public void setValor(BigDecimal Valor) {
        this.Valor = Valor;
    }

    public void setQtde(BigDecimal Qtde) {
        this.Qtde = Qtde;
    }

    public void setChequesQtde(BigDecimal ChequesQtde) {
        this.ChequesQtde = ChequesQtde;
    }

    public void setChequesValor(BigDecimal ChequesValor) {
        this.ChequesValor = ChequesValor;
    }

    public void setTicketsQtde(BigDecimal TicketsQtde) {
        this.TicketsQtde = TicketsQtde;
    }

    public void setTicketsValor(BigDecimal TicketsValor) {
        this.TicketsValor = TicketsValor;
    }

    public void setMoedasValor(BigDecimal MoedasValor) {
        this.MoedasValor = MoedasValor;
    }

    public void setOCTQtde(BigDecimal OCTQtde) {
        this.OCTQtde = OCTQtde;
    }

    public void setOCTValor(BigDecimal OCTValor) {
        this.OCTValor = OCTValor;
    }

    public void setValorRec(BigDecimal ValorRec) {
        this.ValorRec = ValorRec;
    }

    public void setDifMaior(BigDecimal DifMaior) {
        this.DifMaior = DifMaior;
    }

    public void setDifMenor(BigDecimal DifMenor) {
        this.DifMenor = DifMenor;
    }

    public void setDivMaior(BigDecimal DivMaior) {
        this.DivMaior = DivMaior;
    }

    public void setDivMenor(BigDecimal DivMenor) {
        this.DivMenor = DivMenor;
    }

    public void setCedFalsaQtde(BigDecimal CedFalsaQtde) {
        this.CedFalsaQtde = CedFalsaQtde;
    }

    public void setCedFalsaValor(BigDecimal CedFalsaValor) {
        this.CedFalsaValor = CedFalsaValor;
    }

    public void setMatrConf(BigDecimal MatrConf) {
        this.MatrConf = MatrConf;
    }

    public void setTempo(BigDecimal Tempo) {
        this.Tempo = Tempo;
    }

    public void setTotalDN(BigDecimal TotalDN) {
        this.TotalDN = TotalDN;
    }

    public void setTotalDD(BigDecimal TotalDD) {
        this.TotalDD = TotalDD;
    }

    public void setTotalMoeda(BigDecimal TotalMoeda) {
        this.TotalMoeda = TotalMoeda;
    }

    public void setQtdeCed(BigDecimal QtdeCed) {
        this.QtdeCed = QtdeCed;
    }

    public void setNroMaqConf(BigDecimal NroMaqConf) {
        this.NroMaqConf = NroMaqConf;
    }

    public Clientes getClienteOrigem() {
        return clienteOrigem;
    }

    public void setClienteOrigem(Clientes clienteOrigem) {
        this.clienteOrigem = clienteOrigem;
    }

    public Clientes getClienteDestino() {
        return clienteDestino;
    }

    public void setClienteDestino(Clientes clienteDestino) {
        this.clienteDestino = clienteDestino;
    }

    public OS_Vig getOs_vig() {
        return os_vig;
    }

    public void setOs_vig(OS_Vig os_vig) {
        this.os_vig = os_vig;
    }

    public String getNomeConferente() {
        return NomeConferente;
    }

    public void setNomeConferente(String NomeConferente) {
        this.NomeConferente = NomeConferente;
    }

    public String getFilialDescricao() {
        return FilialDescricao;
    }

    public void setFilialDescricao(String FilialDescricao) {
        this.FilialDescricao = FilialDescricao;
    }

    public BigDecimal getQtdeHora() {
        return QtdeHora;
    }

    public void setQtdeHora(BigDecimal QtdeHora) {
        this.QtdeHora = QtdeHora;
    }

    public BigDecimal getValorHora() {
        return ValorHora;
    }

    public void setValorHora(BigDecimal ValorHora) {
        this.ValorHora = ValorHora;
    }

    public String getTempoHora() {
        return TempoHora;
    }

    public void setTempoHora(String TempoHora) {
        this.TempoHora = TempoHora;
    }
}

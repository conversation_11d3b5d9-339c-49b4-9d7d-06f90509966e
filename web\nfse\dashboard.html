<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<html>
    <head>
        <title>SASw - Dashboard de Notas</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
        <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        <link type="text/css" href="../javax.faces.resource/fa/font-awesome.css.xhtml?ln=primefaces&v=7.0.RC3" rel="stylesheet" />
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
        <style>
            html, body{
                margin: 0px;
                padding: 0px;
                width: 100%;
                height: 100%;
                background-color: #E6E6E6;
            }

            body{
                padding: 0px;
            }

            .FundoDash{
                height: 33%;
                padding: 0px 5px 0px 5px;
            }

            .FundoDash > div{
                background-color: #FFF;
                border: thin solid #DDD;
                box-shadow: 2px 2px 3px #CCC;
                height: calc(100% - 15px);
                margin-top: 8px;
                width: 100%;
                padding: 4px 2px 4px 3px;
                border-radius: 12px;
            }

            .TituloDash{
                font-size: 12pt;
                color: #FFF;
                font-weight: bold;
                margin: 0px;
                background-color: steelblue;
                padding: 5px 2px 6px 10px;
                border-radius: 9px 9px 0px 0px;
                height: 26px;
            }

            .FundoDash > div > div{
                height: calc(100% - 27px);
                overflow: auto;
                margin: 0px;
                padding: 2px 0px 0px 0px;
                width: 100%;
                position: relative;
            }

            .Gride{
                font-size: 9pt;
                width: 100%;
                zoom: 0.8;
            }

            .Gride thead tr th{
                background-color: #000;
                color: #FFF;
                font-weight: bold;
                text-align: center;
                padding: 10px 8px 10px 8px;
                white-space: nowrap;
                border-bottom: thin solid #000;
                border-top: thin solid #000;
            }
            
            .Gride thead tr th:not(:last-child){
                border-right: thin solid #666;
            }
            
            .Gride thead tr th[ref-class="DT_NOTA"],
            .Gride thead tr th[ref-class="HR_NOTA"]{
                 background-color: red !important;
            }
            
            .Gride thead tr th[ref-class="DT_NOTA"]{
                 border-right: thin solid darkred !important;
            }
            
            .Gride thead tr th[ref-class="DT_ENVIO"],
            .Gride thead tr th[ref-class="HR_ENVIO"]{
                 background-color: forestgreen !important;
            }
            
            .Gride thead tr th[ref-class="DT_ENVIO"]{
                 border-right: thin solid darkgreen !important;
            }

            .Gride thead tr th[ref-class="DT_RETORNO"],
            .Gride thead tr th[ref-class="HR_RETORNO"]{
                 background-color: blue !important;
                 display: none;
            }
            
            .Gride thead tr th[ref-class="DT_RETORNO"]{
                 border-right: thin solid darkblue !important;
            }
            
            .Gride tbody tr:nth-child(even) td{
                background-color: #E9E9E9;
            }
            
            .Gride tbody tr td[ref-class="DT_NOTA"],
            .Gride tbody tr td[ref-class="HR_NOTA"]{
                background-color: #fbf2f2;
                color: red;
            }
            
            .Gride tbody tr:nth-child(even) td[ref-class="DT_NOTA"],
            .Gride tbody tr:nth-child(even) td[ref-class="HR_NOTA"]{
                background-color: #ffd6d6;
                color: red;
            }
            
            .Gride tbody tr td[ref-class="DT_ENVIO"],
            .Gride tbody tr td[ref-class="HR_ENVIO"]{
                background-color: #e8ffe8;
                color: forestgreen;
            }
            
            .Gride tbody tr:nth-child(even) td[ref-class="DT_ENVIO"],
            .Gride tbody tr:nth-child(even) td[ref-class="HR_ENVIO"]{
                background-color: #bce8bc;
                color: forestgreen;
            }
            
            .Gride tbody tr td[ref-class="DT_RETORNO"],
            .Gride tbody tr td[ref-class="HR_RETORNO"]{
                background-color: #e2f3f9;
                color: blue;
                display: none;
            }
            
            .Gride tbody tr:nth-child(even) td[ref-class="DT_RETORNO"],
            .Gride tbody tr:nth-child(even) td[ref-class="HR_RETORNO"]{
                background-color: #b3dae8;
                color: blue;
            }
            
            .Gride tbody tr td{
                color: #666;
                font-weight: 500;
                text-align: center;
                border: thin solid #DDD;
                white-space: nowrap;
                padding: 6px 4px 6px 4px;
            }

            ::-webkit-scrollbar {
                width: 5px;
                height: 6px;
                border-radius: 30px !important;
            }

            /* Track */
            ::-webkit-scrollbar-track {
                background: #999;
                border-radius: 30px !important;
            }

            /* Handle */
            ::-webkit-scrollbar-thumb {
                background: #666;
                border-radius: 30px !important;
            }

                /* Handle on hover */
                ::-webkit-scrollbar-thumb:hover {
                    background: #555;
                }
        </style>
    </head>
    <body>
        <div class="col-md-12 col-sm-12 col-xs-12" style="height: 50px; background-color: #FFF; border-bottom: thin solid #DDD;">
            <div class="col-md-6 col-sm-6 col-xs-4" style="padding: 0px">
                <img src="https://gruposas.com.br/wp-content/uploads/2021/01/saswbrlogo.png" style="height: 100%; max-width: 100%;" />
            </div>
            <div class="col-md-6 col-sm-6 col-xs-8" style="padding: 0px 0px 0px 0px; text-align: right">
                <label style="font-size: 14pt; display: block; margin:0px; white-space: nowrap"><b>DASHBOARD</b> de Notas</label>
                <label id="lblAtualizando" style="font-size: 10pt; display: block; margin:0px; color: #666">Atualizando em <b>10s</b></label>
            </div>
        </div>

        <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100% - 50px); background-color: transparent; padding-top: 6px; padding-right: 4px; padding-left: 4px;">
            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatBrasifort">
                <div>
                    <h3 class="TituloDash">BRASIFORT</h3>
                    <div>

                    </div>
                </div>
            </div>

            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatFidelys1">
                <div>
                    <h3 class="TituloDash">FIDELYS</h3>
                    <div>

                    </div>
                </div>
            </div>
            
            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatGlobal">
                <div>
                    <h3 class="TituloDash">GLOBAL</h3>
                    <div>

                    </div>
                </div>
            </div>

            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatIbl">
                <div>
                    <h3 class="TituloDash">IBL</h3>
                    <div>

                    </div>
                </div>
            </div>

            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatInterfort">
                <div>
                    <h3 class="TituloDash">INTERFORT</h3>
                    <div>

                    </div>
                </div>
            </div>

            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatInvlMt">
                <div>
                    <h3 class="TituloDash">INVIOSEG</h3>
                    <div>

                    </div>
                </div>
            </div>
            
            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatLoyal">
                <div>
                    <h3 class="TituloDash">LOYAL</h3>
                    <div>

                    </div>
                </div>
            </div>
            
            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatPreserve">
                <div>
                    <h3 class="TituloDash">PRESERVE</h3>
                    <div>

                    </div>
                </div>
            </div>

            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="SatServite">
                <div>
                    <h3 class="TituloDash">SERVITE</h3>
                    <div>

                    </div>
                </div>
            </div>
          <!--  
            <div class="col-md-4 col-sm-4 col-xs-12 FundoDash" id="master">
                <div>
                    <h3 class="TituloDash">SASW</h3>
                    <div>

                    </div>
                </div>
            </div>
          -->  
        </div>
    </body>
    <script>
        var TimerUpdate;
        var Segundos = 60;

        $(document).ready(function () {
            LimparGrideAcao();
        });

        jQuery.moveColumn = function (table, from, to) {
            var rows = jQuery('tr', table);
            var cols;
            rows.each(function() {
                cols = jQuery(this).children('th, td');
                cols.eq(from).detach().insertBefore(cols.eq(to));
            });
        }

        function LimparGrideAcao() {
            $('.FundoDash').attr('vazio', 'S');
            $('.FundoDash > div > div').html('<label style="width: 100%; text-align: center; font-size: 14pt; color: #999; height: 48px; position: absolute; top:0;right:0;bottom:0;left:0;margin:auto;">Carregando ...</label>');
            CarregarGride();
        }

        function TratarTimerAtualizacao() {
            Segundos = 60;

            TimerUpdate = setInterval(function () {
                if (Segundos == 0) {
                    clearInterval(TimerUpdate);
                    LimparGrideAcao();
                } else {
                    $('#lblAtualizando').html(`Atualizando em <b>${Segundos.toString()}s<b/>`);
                    Segundos--;
                }
            }, 1000);
        }

        function CarregarGride() {
            if ($('.FundoDash[vazio="S"]').length > 0) {
                $('#lblAtualizando').html('<i class="fa fa-refresh fa-spin fa-fw"></i>&nbsp;&nbsp;Atualizando ....');

                $('.FundoDash[vazio="S"]:eq(0)').each(function () {
                    $this = $(this).find('div > div');

//http://localhost:8080/SatWebService

                    $.ajax({
                        url: 'https://mobile.sasw.com.br/SatWebService/api/ws-nfe-dashboard/' + $(this).attr('id'),
                        method: 'post'
                    })
                            .done(function (response) {
                                if (response.length > 0) {
                                    let HTMLhead = '';
                                    let HTML = '';
                                    let Linha = 0;

                                    $.each(response, function (i, item) {
                                        HTML += '<tr>';

                                        Object.keys(item).forEach(function (Col) {
                                            let value = item[Col];

                                            if (value &&
                                                    value != null &&
                                                    value != 'null') {
                                                if (Col.toUpperCase() != 'NUMERO')
                                                    HTML += '<td ref-class="'+Col.toUpperCase()+'">' + value + '</td>';
                                                else
                                                    HTML += '<td ref-class="'+Col.toUpperCase()+'">' + value.replace('.0', '') + '</td>';
                                            } else
                                                HTML += '<td ref-class="'+Col.toUpperCase()+'"></td>';

                                            if (Linha == 0) {
                                                switch (Col.toUpperCase()) {
                                                    case 'NRED':
                                                        HTMLhead += '<th ref-class="'+Col.toUpperCase()+'">CLIENTE</th>';
                                                        break;

                                                    case 'NREDPRACA':
                                                        HTMLhead += '<th ref-class="'+Col.toUpperCase()+'">NOME_PRACA</th>';
                                                        break;

                                                    default:
                                                        HTMLhead += '<th ref-class="'+Col.toUpperCase()+'">' + Col.toUpperCase() + '</th>';
                                                        break;
                                                }

                                            }
                                        });

                                        HTML += '</tr>';
                                        Linha++;
                                    });

                                    HTML = '' +
                                            ' <table class="Gride">' +
                                            '  <thead>' +
                                            '    <tr>' + HTMLhead + '</tr>' +
                                            '  </thead>' +
                                            '  <tbody>' + HTML + '</tbody>' +
                                            ' </table>';

                                    $this.html(HTML);
                                    jQuery.moveColumn($this.find('.Gride'), 4, 13);
                                    jQuery.moveColumn($this.find('.Gride'), 4, 13);
                                    jQuery.moveColumn($this.find('.Gride'), 4, 13);
                                } else {
                                    $this.find('label').html('Não há Dados!')
                                }

                                $this.parents('.FundoDash').attr('vazio', 'N');

                                if ($('.FundoDash[vazio="S"]').length > 0) {
                                    CarregarGride();
                                } else {
                                    TratarTimerAtualizacao();
                                }
                            })
                            .fail(function () {
                                $this.parents('.FundoDash').attr('vazio', 'N');
                                $this.find('label').html('Erro ao Consultar!')

                                if ($('.FundoDash[vazio="S"]').length > 0) {
                                    CarregarGride();
                                } else {
                                    TratarTimerAtualizacao();
                                }
                            });
                });
            }
        }
    </script>
</html>

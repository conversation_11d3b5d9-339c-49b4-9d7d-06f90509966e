package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CxFGuias {

    private String CodFil;
    private String CodCli;
    private String Guia;
    private String Serie;
    private String CliOri;
    private String CliDst;
    private String Valor;
    private String Tipo;
    private String RotaEnt;
    private String OperEnt;
    private String DtEnt;
    private String HrEnt;
    private String RotaSai;
    private String Remessa;
    private String OperSai;
    private String DtSai;
    private String HrSai;
    private String SeqRota;
    private String Hora1;
    private String SeqRotaSai;
    private String Hora1D;
    private String OS;
    private String Lote;
    private int Volumes;
    private int VolDh;
    private String ValorDh;
    private int VolCh;
    private String ValorCh;
    private int VolMd;
    private String ValorMd;
    private int VolMet;
    private String ValorMet;
    private int VolMEstr;
    private String ValorMEstr;
    private int VolOutr;
    private String ValorOutr;
    private String PedidoDst;

    private String NRed;
    private String NRedDst;
    private String NRedOS;
    private String NRedCli;
    private String Hr_Saida;
    private String Qtde;

    public CxFGuias() {
        this.CodFil = "0";
        this.CodCli = "";
        this.Guia = "0";
        this.Serie = "";
        this.CliOri = "";
        this.CliDst = "";
        this.Valor = "0";
        this.Tipo = "";
        this.RotaEnt = "";
        this.OperEnt = "";
        this.DtEnt = null;
        this.HrEnt = "";
        this.RotaSai = "";
        this.Remessa = "0";
        this.OperSai = "";
        this.DtSai = null;
        this.HrSai = "";
        this.SeqRota = "0";
        this.Hora1 = "";
        this.SeqRotaSai = "0";
        this.Hora1D = "";
        this.OS = "0";
        this.Lote = "";
        this.Volumes = 0;
        this.VolDh = 0;
        this.ValorDh = "0";
        this.VolCh = 0;
        this.ValorCh = "0";
        this.VolMd = 0;
        this.ValorMd = "0";
        this.VolMet = 0;
        this.ValorMet = "0";
        this.VolMEstr = 0;
        this.ValorMEstr = "0";
        this.VolOutr = 0;
        this.ValorOutr = "0";
        this.PedidoDst = "0";
    }

    public CxFGuias(CxFGuias original) {
        CodFil = original.getCodFil();
        CodCli = original.getCodCli();
        Guia = original.getGuia();
        Serie = original.getSerie();
        CliOri = original.getCliOri();
        CliDst = original.getCliDst();
        Valor = original.getValor();
        Tipo = original.getTipo();
        RotaEnt = original.getRotaEnt();
        OperEnt = original.getOperEnt();
        DtEnt = original.getDtEnt();
        HrEnt = original.getHrEnt();
        RotaSai = original.getRotaSai();
        Remessa = original.getRemessa();
        OperSai = original.getOperSai();
        DtSai = original.getDtSai();
        HrSai = original.getHrSai();
        SeqRota = original.getSeqRota();
        Hora1 = original.getHora1();
        SeqRotaSai = original.getSeqRotaSai();
        Hora1D = original.getHora1D();
        OS = original.getOS();
        Lote = original.getLote();
        Volumes = original.getVolumes();
        VolDh = original.getVolDh();
        ValorDh = original.getValorDh();
        VolCh = original.getVolCh();
        ValorCh = original.getValorCh();
        VolMd = original.getVolMd();
        ValorMd = original.getValorMd();
        VolMet = original.getVolMet();
        ValorMet = original.getValorMet();
        VolMEstr = original.getVolMEstr();
        ValorMEstr = original.getValorMEstr();
        VolOutr = original.getVolOutr();
        ValorOutr = original.getValorOutr();
        PedidoDst = original.getPedidoDst();
        NRed = original.getNRed();
        NRedDst = original.getNRedDst();
        NRedOS = original.getNRedOS();
        NRedCli = original.getNRedCli();
        Hr_Saida = original.getHr_Saida();
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getCliOri() {
        return CliOri;
    }

    public void setCliOri(String CliOri) {
        this.CliOri = CliOri;
    }

    public String getCliDst() {
        return CliDst;
    }

    public void setCliDst(String CliDst) {
        this.CliDst = CliDst;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getRotaEnt() {
        return RotaEnt;
    }

    public void setRotaEnt(String RotaEnt) {
        this.RotaEnt = RotaEnt;
    }

    public String getOperEnt() {
        return OperEnt;
    }

    public void setOperEnt(String OperEnt) {
        this.OperEnt = OperEnt;
    }

    public String getDtEnt() {
        return DtEnt;
    }

    public void setDtEnt(String DtEnt) {
        this.DtEnt = DtEnt;
    }

    public String getHrEnt() {
        return HrEnt;
    }

    public void setHrEnt(String HrEnt) {
        this.HrEnt = HrEnt;
    }

    public String getRotaSai() {
        return RotaSai;
    }

    public void setRotaSai(String RotaSai) {
        this.RotaSai = RotaSai;
    }

    public String getRemessa() {
        return Remessa;
    }

    public void setRemessa(String Remessa) {
        this.Remessa = Remessa;
    }

    public String getOperSai() {
        return OperSai;
    }

    public void setOperSai(String OperSai) {
        this.OperSai = OperSai;
    }

    public String getDtSai() {
        return DtSai;
    }

    public void setDtSai(String DtSai) {
        this.DtSai = DtSai;
    }

    public String getHrSai() {
        return HrSai;
    }

    public void setHrSai(String HrSai) {
        this.HrSai = HrSai;
    }

    public String getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(String SeqRota) {
        this.SeqRota = SeqRota;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getSeqRotaSai() {
        return SeqRotaSai;
    }

    public void setSeqRotaSai(String SeqRotaSai) {
        this.SeqRotaSai = SeqRotaSai;
    }

    public String getHora1D() {
        return Hora1D;
    }

    public void setHora1D(String Hora1D) {
        this.Hora1D = Hora1D;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getLote() {
        return Lote;
    }

    public void setLote(String Lote) {
        this.Lote = Lote;
    }

    public int getVolumes() {
        return Volumes;
    }

    public void setVolumes(int Volumes) {
        this.Volumes = Volumes;
    }

    public int getVolDh() {
        return VolDh;
    }

    public void setVolDh(int VolDh) {
        this.VolDh = VolDh;
    }

    public String getValorDh() {
        return ValorDh;
    }

    public void setValorDh(String ValorDh) {
        this.ValorDh = ValorDh;
    }

    public int getVolCh() {
        return VolCh;
    }

    public void setVolCh(int VolCh) {
        this.VolCh = VolCh;
    }

    public String getValorCh() {
        return ValorCh;
    }

    public void setValorCh(String ValorCh) {
        this.ValorCh = ValorCh;
    }

    public int getVolMd() {
        return VolMd;
    }

    public void setVolMd(int VolMd) {
        this.VolMd = VolMd;
    }

    public String getValorMd() {
        return ValorMd;
    }

    public void setValorMd(String ValorMd) {
        this.ValorMd = ValorMd;
    }

    public int getVolMet() {
        return VolMet;
    }

    public void setVolMet(int VolMet) {
        this.VolMet = VolMet;
    }

    public String getValorMet() {
        return ValorMet;
    }

    public void setValorMet(String ValorMet) {
        this.ValorMet = ValorMet;
    }

    public int getVolMEstr() {
        return VolMEstr;
    }

    public void setVolMEstr(int VolMEstr) {
        this.VolMEstr = VolMEstr;
    }

    public String getValorMEstr() {
        return ValorMEstr;
    }

    public void setValorMEstr(String ValorMEstr) {
        this.ValorMEstr = ValorMEstr;
    }

    public int getVolOutr() {
        return VolOutr;
    }

    public void setVolOutr(int VolOutr) {
        this.VolOutr = VolOutr;
    }

    public String getValorOutr() {
        return ValorOutr;
    }

    public void setValorOutr(String ValorOutr) {
        this.ValorOutr = ValorOutr;

    }

    public String getPedidoDst() {
        return PedidoDst;
    }

    public void setPedidoDst(String PedidoDst) {
        this.PedidoDst = PedidoDst;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getNRedDst() {
        return NRedDst;
    }

    public void setNRedDst(String NRedDst) {
        this.NRedDst = NRedDst;
    }

    public String getNRedOS() {
        return NRedOS;
    }

    public void setNRedOS(String NRedOS) {
        this.NRedOS = NRedOS;
    }

    public String getNRedCli() {
        return NRedCli;
    }

    public void setNRedCli(String NRedCli) {
        this.NRedCli = NRedCli;
    }

    public String getHr_Saida() {
        return Hr_Saida;
    }

    public void setHr_Saida(String Hr_Saida) {
        this.Hr_Saida = Hr_Saida;
    }

    public String getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        this.Qtde = Qtde;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CxFGuias other = (CxFGuias) obj;
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        if (!Objects.equals(this.CodCli, other.CodCli)) {
            return false;
        }
        if (!Objects.equals(this.Guia, other.Guia)) {
            return false;
        }
        if (!Objects.equals(this.Serie, other.Serie)) {
            return false;
        }
        return true;
    }

}

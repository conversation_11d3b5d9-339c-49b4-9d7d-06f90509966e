package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class RastrearStat {

    private BigDecimal Sequencia;
    private BigDecimal CodFil;
    private BigDecimal SeqRota;
    private BigDecimal CodPessoa;
    private LocalDate DataPOS;
    private String HoraPOS;
    private LocalDate Data;
    private String Hora;
    private String Placa;

    public RastrearStat() {
        this.Sequencia = new BigDecimal("0");
        this.CodFil = new BigDecimal("0");
        this.SeqRota = new BigDecimal("0");
        this.CodPessoa = new BigDecimal("0");
        this.DataPOS = null;
        this.HoraPOS = "";
        this.Data = null;
        this.Hora = "";
        this.Placa = "";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(BigDecimal SeqRota) {
        this.SeqRota = SeqRota;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public LocalDate getDataPOS() {
        return DataPOS;
    }

    public void setDataPOS(LocalDate DataPOS) {
        this.DataPOS = DataPOS;
    }

    public String getHoraPOS() {
        return HoraPOS;
    }

    public void setHoraPOS(String HoraPOS) {
        this.HoraPOS = HoraPOS;
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getPlaca() {
        return Placa;
    }

    public void setPlaca(String Placa) {
        this.Placa = Placa;
    }

}

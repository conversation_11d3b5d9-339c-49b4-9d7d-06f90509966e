/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Rt_Hist;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rt_HistDao {

    public List<Rt_Hist> listarHistorico(BigDecimal sequencia, int parada, Persistencia persistencia) throws Exception {
        List<Rt_Hist> retorno = new ArrayList<>();
        try {
            String sql = " SELECT SubString(Historico,1,60) Historico, Rt_Hist.* "
                    + " FROM Rt_Hist "
                    + " WHERE Sequencia = ? AND Parada = ? "
                    + " ORDER BY Ordem ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.setInt(parada);
            consulta.select();
            Rt_Hist rt_hist;
            while (consulta.Proximo()) {
                rt_hist = new Rt_Hist();
                rt_hist.setHistorico(consulta.getString("Historico"));
                rt_hist.setOrdem(consulta.getBigDecimal("ordem"));
                rt_hist.setDt_Alter(consulta.getString("dt_alter"));
                rt_hist.setHr_Alter(consulta.getString("hr_alter"));
                rt_hist.setOperador(consulta.getString("operador"));
                retorno.add(rt_hist);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Falha ao buscar histórico - " + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S3000 {

    private String id;
    private int sucesso;

    /**
     * 1 - Produção / 2 - Homologação
     */
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEvento_identificador;

    /**
     * J = 1 - CNPJ / F = 2 - CPF
     */
    private String ideEmpregador_tpInsc;
    /**
     * Copy(CNPJ,1,08)+</nrInsc> //Raiz CNPJ
     */
    private String ideEmpregador_nrInsc;

    private String infoExclusao_tpEvento;
    private String infoExclusao_nrRecEvt;

    private String ideTrabalhador_cpfTrab;
    private String ideTrabalhador_nisTrab;

    private String ideFolhaPagto_indApuracao;
    private String ideFolhaPagto_perApur;

    public String getIdeFolhaPagto_indApuracao() {
        return ideFolhaPagto_indApuracao;
    }

    public void setIdeFolhaPagto_indApuracao(String ideFolhaPagto_indApuracao) {
        this.ideFolhaPagto_indApuracao = ideFolhaPagto_indApuracao;
    }

    public String getIdeFolhaPagto_perApur() {
        return ideFolhaPagto_perApur;
    }

    public void setIdeFolhaPagto_perApur(String ideFolhaPagto_perApur) {
        this.ideFolhaPagto_perApur = ideFolhaPagto_perApur;
    }

    public String getIdeTrabalhador_cpfTrab() {
        return ideTrabalhador_cpfTrab;
    }

    public void setIdeTrabalhador_cpfTrab(String ideTrabalhador_cpfTrab) {
        this.ideTrabalhador_cpfTrab = ideTrabalhador_cpfTrab;
    }

    public String getIdeTrabalhador_nisTrab() {
        return ideTrabalhador_nisTrab;
    }

    public void setIdeTrabalhador_nisTrab(String ideTrabalhador_nisTrab) {
        this.ideTrabalhador_nisTrab = ideTrabalhador_nisTrab;
    }

    public String getInfoExclusao_tpEvento() {
        return infoExclusao_tpEvento;
    }

    public void setInfoExclusao_tpEvento(String infoExclusao_tpEvento) {
        this.infoExclusao_tpEvento = infoExclusao_tpEvento;
    }

    public String getInfoExclusao_nrRecEvt() {
        return infoExclusao_nrRecEvt;
    }

    public void setInfoExclusao_nrRecEvt(String infoExclusao_nrRecEvt) {
        this.infoExclusao_nrRecEvt = infoExclusao_nrRecEvt;
    }

    public String getId() {
        return null == id ? "" : id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeContri_nrInsc() {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    public String getIdeEvento_identificador() {
        return ideEvento_identificador;
    }

    public void setIdeEvento_identificador(String ideEvento_identificador) {
        this.ideEvento_identificador = ideEvento_identificador;
    }

    public void getIdeEvento_identificador(String string) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

}

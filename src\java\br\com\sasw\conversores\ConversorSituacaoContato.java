/*
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorSituacaoContato")
public class ConversorSituacaoContato implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return value;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        String retorno;
        BigDecimal situacao = new BigDecimal(value.toString());
        switch (situacao.toBigInteger().toString()) {
            case "1":
                retorno = Messages.getMessageS("Prospect").toUpperCase();
                break;
            case "2":
                retorno = Messages.getMessageS("AguardandoContato").toUpperCase();
                break;
            case "3":
                retorno = Messages.getMessageS("ApresentacaoServicos").toUpperCase();
                break;
            case "4":
                retorno = Messages.getMessageS("Negociacao").toUpperCase();
                break;
            case "5":
                retorno = Messages.getMessageS("PilotoServicos").toUpperCase();
                break;
            case "6":
                retorno = Messages.getMessageS("FormalizacaoContratual").toUpperCase();
                break;
            case "7":
                retorno = Messages.getMessageS("ClienteAtivo").toUpperCase();
                break;
            default:
                retorno = value.toString().toUpperCase();
        }
        return retorno;
    }
}

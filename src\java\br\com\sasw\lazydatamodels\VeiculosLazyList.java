/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import br.com.sasw.utils.Messages;
import Dados.Persistencia;
import SasBeans.Veiculos;
import br.com.sasw.pacotesuteis.controller.veiculos.VeiculosSPM;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class VeiculosLazyList extends LazyDataModel<Veiculos> {

    private static final long serialVersionUID = 1L;
    private List<Veiculos> veiculos;
    private final VeiculosSPM veiculosSPM;
    private final Persistencia persistencia;
    private Map filters;
    private BigDecimal codPessoa;

    public VeiculosLazyList(Persistencia pst, BigDecimal codPessoa, Map filters) {
        this.veiculosSPM = new VeiculosSPM();
        this.persistencia = pst;
        this.filters = filters;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<Veiculos> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.veiculos = this.veiculosSPM.listagemPaginada(first, pageSize, this.codPessoa, this.filters, this.persistencia);

            setRowCount(this.veiculosSPM.contagem(this.filters, this.codPessoa, this.persistencia));

            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.veiculos;
    }

    @Override
    public Object getRowKey(Veiculos veiculo) {
        return veiculo.getNumero();
    }

    @Override
    public Veiculos getRowData(String numero) {
        try {
            int numeroVeiculo = Integer.valueOf(numero);
            for (Veiculos veiculo : this.veiculos) {
                if (numeroVeiculo == veiculo.getNumero()) {
                    return veiculo;
                }
            }
            return null;
        } catch (Exception e) {
            System.out.println("Veiculo: " + numero + "ERRO: " + e.getMessage());
            return null;
        }
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

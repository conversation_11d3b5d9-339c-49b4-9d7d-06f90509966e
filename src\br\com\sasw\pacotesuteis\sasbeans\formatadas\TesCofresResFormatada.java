package br.com.sasw.pacotesuteis.sasbeans.formatadas;

/**
 *
 * <AUTHOR>
 */
public class TesCofresResFormatada {

    private String CodCofre;
    private String Data;
    private String DiaSem;
    private String DiaSemT;
    private String Feriado;
    private String VlrDep;
    private String VlrRecDepD0;
    private String HrRecDepD0;
    private String VlrCredRecD0;
    private String VlrCredCorteD0;
    private String VlrTotalCred;
    private String VlrRecApos;
    private String HrRecApos;
    private String VlrD0Apos;
    private String VlrD1Apos;
    private String VlrTotalRec;
    private String VlrDepProxDU;
    private String SaldoFisTotal;
    private String SaldoFisCred;
    private String SaldoFisCst;
    private String CorSaldoFisCst;
    private String DataStr;

    public String getCodCofre() {
        return CodCofre;
    }

    public void setCodCofre(String CodCofre) {
        this.CodCofre = CodCofre;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getDiaSem() {
        return DiaSem;
    }

    public void setDiaSem(String DiaSem) {
        this.DiaSem = DiaSem;
    }

    public String getDiaSemT() {
        return DiaSemT;
    }

    public void setDiaSemT(String DiaSemT) {
        this.DiaSemT = DiaSemT;
    }

    public String getFeriado() {
        return Feriado;
    }

    public void setFeriado(String Feriado) {
        this.Feriado = Feriado;
    }

    public String getVlrDep() {
        return VlrDep;
    }

    public void setVlrDep(String VlrDep) {
        this.VlrDep = VlrDep;
    }

    public String getVlrRecDepD0() {
        return VlrRecDepD0;
    }

    public void setVlrRecDepD0(String VlrRecDepD0) {
        this.VlrRecDepD0 = VlrRecDepD0;
    }

    public String getHrRecDepD0() {
        return HrRecDepD0;
    }

    public void setHrRecDepD0(String HrRecDepD0) {
        this.HrRecDepD0 = HrRecDepD0;
    }

    public String getVlrCredRecD0() {
        return VlrCredRecD0;
    }

    public void setVlrCredRecD0(String VlrCredRecD0) {
        this.VlrCredRecD0 = VlrCredRecD0;
    }

    public String getVlrCredCorteD0() {
        return VlrCredCorteD0;
    }

    public void setVlrCredCorteD0(String VlrCredCorteD0) {
        this.VlrCredCorteD0 = VlrCredCorteD0;
    }

    public String getVlrTotalCred() {
        return VlrTotalCred;
    }

    public void setVlrTotalCred(String VlrTotalCred) {
        this.VlrTotalCred = VlrTotalCred;
    }

    public String getVlrRecApos() {
        return VlrRecApos;
    }

    public void setVlrRecApos(String VlrRecApos) {
        this.VlrRecApos = VlrRecApos;
    }

    public String getHrRecApos() {
        return HrRecApos;
    }

    public void setHrRecApos(String HrRecApos) {
        this.HrRecApos = HrRecApos;
    }

    public String getVlrD0Apos() {
        return VlrD0Apos;
    }

    public void setVlrD0Apos(String VlrD0Apos) {
        this.VlrD0Apos = VlrD0Apos;
    }

    public String getVlrD1Apos() {
        return VlrD1Apos;
    }

    public void setVlrD1Apos(String VlrD1Apos) {
        this.VlrD1Apos = VlrD1Apos;
    }

    public String getVlrTotalRec() {
        return VlrTotalRec;
    }

    public void setVlrTotalRec(String VlrTotalRec) {
        this.VlrTotalRec = VlrTotalRec;
    }

    public String getVlrDepProxDU() {
        return VlrDepProxDU;
    }

    public void setVlrDepProxDU(String VlrDepProxDU) {
        this.VlrDepProxDU = VlrDepProxDU;
    }

    public String getSaldoFisTotal() {
        return SaldoFisTotal;
    }

    public void setSaldoFisTotal(String SaldoFisTotal) {
        this.SaldoFisTotal = SaldoFisTotal;
    }

    public String getSaldoFisCred() {
        return SaldoFisCred;
    }

    public void setSaldoFisCred(String SaldoFisCred) {
        this.SaldoFisCred = SaldoFisCred;
    }

    public String getSaldoFisCst() {
        return SaldoFisCst;
    }

    public void setSaldoFisCst(String SaldoFisCst) {
        this.SaldoFisCst = SaldoFisCst;
    }

    public String getCorSaldoFisCst() {
        return CorSaldoFisCst;
    }

    public void setCorSaldoFisCst(String CorSaldoFisCst) {
        this.CorSaldoFisCst = CorSaldoFisCst;
    }

    public String getDataStr() {
        return DataStr;
    }

    public void setDataStr(String DataStr) {
        this.DataStr = DataStr;
    }

}

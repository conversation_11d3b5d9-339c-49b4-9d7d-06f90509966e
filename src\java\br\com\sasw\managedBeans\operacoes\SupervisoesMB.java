/*
 */
package br.com.sasw.managedBeans.operacoes;

import Arquivo.ArquivoLog;
import Controller.Login.LoginSatMobWeb;
import Controller.Supervisao.Supervisoes;
import Controller.TbVal.ControlerTbVal;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.SasPWFill;
import SasBeans.TbVal;
import SasBeansCompostas.PstHstQstTbValFuncion;
import SasBeansCompostas.TmktDetPstPstServClientes;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.lazydatamodels.SupervisoesLazyList;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.map.DefaultMapModel;
import org.primefaces.model.map.LatLng;
import org.primefaces.model.map.MapModel;
import org.primefaces.model.map.Marker;

/**
 *
 * <AUTHOR>
 */
@Named(value = "supervisao")
@ViewScoped
public class SupervisoesMB implements Serializable {

    private Persistencia persistencia;
    private BigDecimal codPessoa;
    private String codFil, nomeFilial, situacao, coordenadas, foto, detalhesQuestoes, fotoFuncion, fotoPosto, d1, d2, caminho, log, banco, operador;
    private ArquivoLog logerro;
    private List<TmktDetPstPstServClientes> listaSupervisao;
    private TmktDetPstPstServClientes supervisaoSelecionado, supervisao;
    private List<PstHstQstTbValFuncion> listaQuestionario, funcionarios, questoes;
    private PstHstQstTbValFuncion questionarioSelecionado;
    private PstServ posto;
    private Supervisoes supervisoesmobsatweb;
    private LoginSatMobWeb loginsatmobweb;
    private List<String> fotoQuestionario, listaSituacoes;
    private HashMap<String, String> respostas, situacoes;
    private MapModel pin;
    private Marker marker;
    private Clientes cliente;
    private Double distPstSup;
    private int flag, flagQuestao, posFotoFuncion, posFotoPosto, total;
    private Date dataSelecionada1, dataSelecionada2;
    private LocalDate data1, data2;
    private SasPWFill filial;
    private Boolean mostrarFiliais, limparFiltros;
    private List<TbVal> lista;
    private ControlerTbVal controlertbval;
    private TbVal questao;
    private LazyDataModel<TmktDetPstPstServClientes> supervisoes = null;
    private Map filters;

    public SupervisoesMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        supervisaoSelecionado = new TmktDetPstPstServClientes();
        questionarioSelecionado = new PstHstQstTbValFuncion();
        listaSupervisao = new ArrayList<>();
        listaQuestionario = new ArrayList<>();
        funcionarios = new ArrayList<>();
        questoes = new ArrayList<>();
        detalhesQuestoes = "";
        supervisoesmobsatweb = new Supervisoes();
        controlertbval = new ControlerTbVal();
        cliente = new Clientes();
        questao = new TbVal();
        fotoQuestionario = new ArrayList<>();
        respostas = new HashMap<>();
        respostas.put("-1", "Não se aplica");
        respostas.put("0", "Não");
        respostas.put("1", "Sim");
        situacoes = new HashMap<>();
        situacoes.put("OK", "Concluído");
        situacoes.put("PD", "Pendente");
        situacoes.put("AN", "Em andamento");
        listaSituacoes = new ArrayList<>();
        listaSituacoes.add("OK");
        listaSituacoes.add("PD");
        listaSituacoes.add("AN");
        situacao = new String();
        pin = new DefaultMapModel();
        marker = new Marker(new LatLng(0, 0), "");
        coordenadas = "-15.793841, -47.882762";
        filial = new SasPWFill();
        limparFiltros = false;
        mostrarFiliais = false;
        supervisao = new TmktDetPstPstServClientes();
        posFotoFuncion = 0;
        posFotoPosto = 0;
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, 1);
        dataSelecionada1 = c.getTime();
        data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataSelecionada2 = c.getTime();
        data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        log = new String();
        logerro = new ArquivoLog();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.filters = new HashMap();
            this.filters.put(" tmktdetpst.codfil = ? ", Arrays.asList(this.codFil));
            this.filters.put(" tmktdetpst.data between ? and ? ", Arrays.asList(this.d1, this.d2));
            this.total = this.supervisoesmobsatweb.Contagem(this.filters, this.codPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public SupervisoesMB(Persistencia pst) {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        supervisaoSelecionado = new TmktDetPstPstServClientes();
        questionarioSelecionado = new PstHstQstTbValFuncion();
        persistencia = pst;
        listaSupervisao = new ArrayList<>();
        listaQuestionario = new ArrayList<>();
        funcionarios = new ArrayList<>();
        questoes = new ArrayList<>();
        detalhesQuestoes = "";
        supervisoesmobsatweb = new Supervisoes();
        cliente = new Clientes();
        fotoQuestionario = new ArrayList<>();
        respostas = new HashMap<>();
        respostas.put("-1", Messages.getMessageS("NaoSeAplica"));
        respostas.put("0", Messages.getMessageS("Nao"));
        respostas.put("1", Messages.getMessageS("Sim"));
        situacoes = new HashMap<>();
        situacoes.put("OK", Messages.getMessageS("Concluido"));
        situacoes.put("PD", Messages.getMessageS("Pendente"));
        situacoes.put("AN", Messages.getMessageS("EmAndamento"));
        listaSituacoes = new ArrayList<>();
        listaSituacoes.add("OK");
        listaSituacoes.add("PD");
        listaSituacoes.add("AN");
        situacao = new String();
        pin = new DefaultMapModel();
        marker = new Marker(new LatLng(0, 0), "");
        coordenadas = "-15.793841, -47.882762";
        filial = new SasPWFill();
        posFotoFuncion = 0;
        posFotoPosto = 0;
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, 1);
        dataSelecionada1 = c.getTime();
        data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataSelecionada2 = c.getTime();
        data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        log = new String();
        caminho = FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator) + "msgerros_" + banco + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
    }

    public void ListarSupervisaoEdicaoPosto(PstServ posto, String dataSupervisao1, String dataSupervisao2) {
        try {
            this.listaSupervisao = this.supervisoesmobsatweb.ListaSupervisaoPosto(posto,
                    Mascaras.removeMascaraData(dataSupervisao1), Mascaras.removeMascaraData(dataSupervisao2), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dblSelect(SelectEvent event) {
        this.supervisaoSelecionado = (TmktDetPstPstServClientes) event.getObject();
        buttonAction(null);
    }

    public void buttonAction(ActionEvent actionEvent) {
        this.coordenadas = "-15.793841, -47.882762";
        if (null == this.supervisaoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneSupervisao"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.filial = this.loginsatmobweb.BuscaFilial(this.supervisaoSelecionado.getTmktdetpst().getCodFil().toPlainString(), this.codPessoa, this.persistencia);
                this.cliente = this.supervisaoSelecionado.getClientes();
                Double cliLat = 0.0, cliLon = 0.0, pstLat = 0.0, pstLon = 0.0;
                try {
                    cliLat = Double.parseDouble(this.supervisaoSelecionado.getClientes().getLatitude());
                    cliLon = Double.parseDouble(this.supervisaoSelecionado.getClientes().getLongitude());
                } catch (Exception e) {
                }
                try {
                    pstLat = Double.valueOf(this.supervisaoSelecionado.getTmktdetpst().getLatitude());
                    pstLon = Double.valueOf(this.supervisaoSelecionado.getTmktdetpst().getLongitude());
                } catch (Exception e) {
                }
                this.pin = new DefaultMapModel();
                if (cliLat != 0.0 || cliLon != 0.0) {
                    this.marker = new Marker(new LatLng(cliLat, cliLon), this.supervisaoSelecionado.getPstserv().getLocal());
                    this.marker.setFlat(true);
                    this.marker.setTitle(this.supervisaoSelecionado.getPstserv().getLocal());
                    this.marker.setIcon("https://mobile.sasw.com.br:9091/satmobile/img/novo_iconedourado_M.png");
                    this.pin.addOverlay(this.marker);
                }
                if (pstLat != 0.0 || pstLon != 0.0) {
                    this.marker = new Marker(new LatLng(pstLat, pstLon), this.supervisaoSelecionado.getTmktdetpst().getOperador());
                    this.marker.setFlat(true);
                    this.marker.setTitle(this.supervisaoSelecionado.getTmktdetpst().getOperador());
                    this.marker.setIcon("https://mobile.sasw.com.br:9091/satmobile/img/PIN_chefe_equipe_googlemaps.png");
                    this.pin.addOverlay(this.marker);
                }
                try {
                    if ((cliLat == 0.0 || cliLon == 0.0) && pstLat != 0.0 && pstLon != 0.0) {
                        cliLat = pstLat;
                        cliLon = pstLon;
                    } else if ((pstLat == 0.0 || pstLon == 0.0) && cliLat != 0.0 && cliLon != 0.0) {
                        pstLat = cliLat;
                        pstLon = cliLon;
                    } else if (cliLat == 0.0 && cliLon == 0.0 && pstLat == 0.0 && pstLon == 0.0) {
                        throw new Exception();
                    }
                    this.coordenadas = "" + (cliLat + pstLat) / 2 + ", " + (cliLon + pstLon) / 2;
                } catch (Exception e) {
                }
                CalculaDistPstSup();
                ListarQuestionarios();
                this.posFotoFuncion = 0;
                this.fotoFuncion = null;
                this.posFotoPosto = 0;
                if (!this.supervisaoSelecionado.getFotos().isEmpty()) {
                    this.fotoPosto = this.supervisaoSelecionado.getFotos().get(0);
                } else {
                    this.fotoPosto = null;
                }
                PrimeFaces.current().executeScript("PF('dlgListarSupervisoes').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }

        }
    }

    public void AtualizarFilial() {
        try {
            this.filial = this.loginsatmobweb.BuscaFilial(this.supervisaoSelecionado.getTmktdetpst().getCodFil().toPlainString(), this.codPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void EscreverData1() {
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            this.dataSelecionada1 = df.parse(this.d1);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void EscreverData2() {
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            this.dataSelecionada2 = df.parse(this.d2);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void SelecionarData1(SelectEvent data) {
        this.dataSelecionada1 = (Date) data.getObject();
        this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        this.d1 = this.data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //SelecionarData();
    }

    public void SelecionarData2(SelectEvent data) {
        this.dataSelecionada2 = (Date) data.getObject();
        this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        this.d2 = this.data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public void SelecionarData() {
        try {
            this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            this.d1 = this.data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.d2 = this.data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (this.dataSelecionada1.after(this.dataSelecionada2)) {
                throw new Exception("IntervaloInvalido");
            }
            this.filters.replace(" tmktdetpst.data between ? and ? ", Arrays.asList(this.d1, this.d2));
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllSupervisoes();
            dt.setFirst(0);
            PrimeFaces.current().ajax().update("msgs");
            PrimeFaces.current().ajax().update("main");
            PrimeFaces.current().ajax().update("cabecalho");
        } catch (Exception e) {
            PrimeFaces.current().ajax().update("msgs");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void DataAnterior() {
        try {
            if (null == this.data1) {
                Calendar c = Calendar.getInstance();
                c.setTime(Date.from(Instant.now()));
                c.set(Calendar.DAY_OF_MONTH, 1);
                this.dataSelecionada1 = c.getTime();
                this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d1 = this.data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
                this.dataSelecionada2 = c.getTime();
                this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d2 = this.data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            } else {
                Calendar c = Calendar.getInstance(), d = Calendar.getInstance();
                c.setTime(this.dataSelecionada1);
                c.add(Calendar.MONTH, -1);
                this.dataSelecionada1 = c.getTime();
                d.setTime(this.dataSelecionada2);
                d.add(Calendar.MONTH, -1);
                d.set(Calendar.DAY_OF_MONTH, d.getActualMaximum(Calendar.DAY_OF_MONTH));
                this.dataSelecionada2 = d.getTime();
                this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d1 = this.data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d2 = this.data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            this.filters.replace(" tmktdetpst.data between ? and ? ", Arrays.asList(this.d1, this.d2));
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllSupervisoes();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void DataPosterior() {
        try {
            if (null == this.data1) {
                Calendar c = Calendar.getInstance();
                c.setTime(Date.from(Instant.now()));
                c.set(Calendar.DAY_OF_MONTH, 1);
                this.dataSelecionada1 = c.getTime();
                this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d1 = this.data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
                this.dataSelecionada2 = c.getTime();
                this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d2 = this.data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            } else {
                Calendar c = Calendar.getInstance(), d = Calendar.getInstance();
                c.setTime(this.dataSelecionada1);
                c.add(Calendar.MONTH, 1);
                d.setTime(this.dataSelecionada2);
                d.add(Calendar.MONTH, 1);
                d.set(Calendar.DAY_OF_MONTH, d.getActualMaximum(Calendar.DAY_OF_MONTH));
                this.dataSelecionada1 = c.getTime();
                this.dataSelecionada2 = d.getTime();
                this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d1 = this.data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d2 = this.data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            this.filters.replace(" tmktdetpst.data between ? and ? ", Arrays.asList(this.d1, this.d2));
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllSupervisoes();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<String> Sequencias() {
        try {
            return this.supervisoesmobsatweb.ListarSequenciasSupervisao(this.supervisaoSelecionado, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public TmktDetPstPstServClientes CarregarSupervisao(TmktDetPstPstServClientes supervisao) {
        try {
            return this.supervisoesmobsatweb.BuscarSupervisao(supervisao, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public void ListarQuestionarios() {
        try {
            try {
                DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");
                Date date = (Date) formatter.parse(this.supervisaoSelecionado.getTmktdetpst().getData());
                SimpleDateFormat newFormat = new SimpleDateFormat("yyyyMMdd");
                this.supervisaoSelecionado.getTmktdetpst().setData(newFormat.format(date));
            } catch (Exception e) {
            }
            this.listaQuestionario = this.supervisoesmobsatweb.ListaQuestionarioSup(this.supervisaoSelecionado.getTmktdetpst().getSequencia(),
                    this.supervisaoSelecionado.getTmktdetpst().getData(), this.persistencia);
            List<Funcion> temp = new ArrayList<>();
            this.funcionarios = new ArrayList<>();
            this.questionarioSelecionado = new PstHstQstTbValFuncion();
            this.questoes = new ArrayList<>();
            this.detalhesQuestoes = "";
            if (!this.listaQuestionario.isEmpty()) {
                for (PstHstQstTbValFuncion iterator : this.listaQuestionario) {
                    if (!temp.contains(iterator.getFuncion())) {
                        temp.add(iterator.getFuncion());
                        this.funcionarios.add(iterator);
                    }
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void SelecionarQuestionario(SelectEvent event) {
        this.questionarioSelecionado = (PstHstQstTbValFuncion) event.getObject();
        this.posFotoFuncion = 0;
        if (this.questionarioSelecionado.getQtdefotos() != 0) {
            this.fotoFuncion = this.questionarioSelecionado.getEndfotos().get(this.posFotoFuncion);
        } else {
            this.fotoFuncion = new String();
        }
        this.questoes = new ArrayList<>();
        this.detalhesQuestoes = "";
        for (PstHstQstTbValFuncion iterator : this.listaQuestionario) {
            if (iterator.getPsthstqst().getMatr().equals(this.questionarioSelecionado.getPsthstqst().getMatr())) {
                this.questoes.add(iterator);
            }
        }
        if (this.questoes.size() > 1) {
            if (this.questoes.get(0).getTbval().getDescricao().equals("")) {
                this.detalhesQuestoes = this.questoes.get(0).getPsthstqst().getDetalhes();
                this.questoes.remove(0);
            }
        } else {
            this.detalhesQuestoes = this.questoes.get(0).getPsthstqst().getDetalhes();
        }
    }

    public void CalculaDistPstSup() {
        Double cliLat = 0.0, cliLon = 0.0, pstLat = 0.0, pstLon = 0.0;
        try {
            cliLat = Double.parseDouble(this.cliente.getLatitude());
            cliLon = Double.parseDouble(this.cliente.getLongitude());
        } catch (Exception e) {
        }
        try {
            pstLat = Double.valueOf(this.supervisaoSelecionado.getTmktdetpst().getLatitude());
            pstLon = Double.valueOf(this.supervisaoSelecionado.getTmktdetpst().getLongitude());
        } catch (Exception e) {
        }
        /* http://andrew.hedges.name/experiments/haversine/ */
        try {
            if (cliLat == 0.0 || cliLon == 0.0 || pstLat == 0.0 || pstLon == 0.0) {
                throw new Exception();
            }
            /* dlon = lon2 - lon1  */
            double lon = Math.toRadians(Double.parseDouble(this.cliente.getLongitude())
                    - Double.valueOf(this.supervisaoSelecionado.getTmktdetpst().getLongitude()));
            /* dlat = lat2 - lat1 */
            double lat = Math.toRadians(Double.parseDouble(this.cliente.getLatitude())
                    - Double.valueOf(this.supervisaoSelecionado.getTmktdetpst().getLatitude()));
            /* a = (sin(dlat/2))^2 + cos(lat1) * cos(lat2) * (sin(dlon/2))^2 */
            double a = Math.pow(Math.sin(lat / 2), 2)
                    + Math.cos(Math.toRadians(Double.valueOf(this.supervisaoSelecionado.getTmktdetpst().getLatitude())))
                    * Math.cos(Math.toRadians(Double.parseDouble(this.cliente.getLatitude())))
                    * Math.pow(Math.sin(lon / 2), 2);
            /* c = 2 * atan2( sqrt(a), sqrt(1-a) )  */
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            /* d = R * c (where R is the radius of the Earth), R = 6373 km  */
            double d = 6373 * c * 1000;
            this.distPstSup = BigDecimal.valueOf(d).setScale(3, RoundingMode.HALF_UP).doubleValue();
        } catch (Exception e) {
            this.distPstSup = 0.0;
        }
    }

    public void SelecionarSituacao(SelectEvent event) {
        this.situacao = (String) event.getObject();
    }

    public void AtualizarSituacao() {
        try {
            this.supervisaoSelecionado.getTmktdetpst().setSituacao(this.situacao);
            this.supervisaoSelecionado.getTmktdetpst().setDt_Alter(LocalDate.now());
            this.supervisaoSelecionado.getTmktdetpst().setHr_alter(DataAtual.getDataAtual("HORA"));
            this.supervisaoSelecionado.getTmktdetpst().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.supervisoesmobsatweb.AtualizarSituacao(this.supervisaoSelecionado, this.persistencia);
            //Listar();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void AtualizarSituacaoPosto(PstServ posto, String dataSupervisao1, String dataSupervisao2) {
        try {
            this.supervisaoSelecionado.getTmktdetpst().setSituacao(this.situacao);
            this.supervisaoSelecionado.getTmktdetpst().setDt_Alter(LocalDate.now());
            this.supervisaoSelecionado.getTmktdetpst().setHr_alter(DataAtual.getDataAtual("HORA"));
            this.supervisaoSelecionado.getTmktdetpst().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.supervisaoSelecionado.setPstserv(posto);
            this.supervisoesmobsatweb.AtualizarSituacao(this.supervisaoSelecionado, this.persistencia);
            ListarSupervisaoEdicaoPosto(this.supervisaoSelecionado.getPstserv(), dataSupervisao1, dataSupervisao2);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void PrePesquisar() {
        this.supervisao = new TmktDetPstPstServClientes();
        this.filial = new SasPWFill();
        this.dataSelecionada1 = null;
        this.data1 = null;
        this.dataSelecionada2 = null;
        this.data2 = null;
    }

    public void SelecionarDataInicio(SelectEvent data) {
        this.dataSelecionada1 = (Date) data.getObject();
    }

    public void SelecionarDataFim(SelectEvent data) {
        this.dataSelecionada2 = (Date) data.getObject();
    }

    public void LimparFiltros() {
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, 1);
        this.dataSelecionada1 = c.getTime();
        this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        this.dataSelecionada2 = c.getTime();
        this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        this.limparFiltros = false;
        this.filters.replace(" tmktdetpst.data between ? and ? ", Arrays.asList(this.data1.toString(), this.data2.toString()));
        this.filters.replace(" tmktdetpst.codfil = ? ", Arrays.asList(this.codFil));
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllSupervisoes();
        dt.setFirst(0);
    }

    public void MostrarFiliais() {
        if (this.mostrarFiliais) {
            this.filters.replace(" tmktdetpst.codfil = ? ", Arrays.asList());
        } else {
            this.filters.replace(" tmktdetpst.codfil = ? ", Arrays.asList(this.codFil));
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllSupervisoes();
        dt.setFirst(0);
    }

    public void AvancarFotoFuncion() {
        if (this.posFotoFuncion + 1 == this.questionarioSelecionado.getEndfotos().size()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosFim"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoFuncion = this.questionarioSelecionado.getEndfotos().get(this.posFotoFuncion + 1);
            this.posFotoFuncion = this.posFotoFuncion + 1;
        }
    }

    public void VoltarFotoFuncion() {
        if (this.posFotoFuncion == 0) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosInicio"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoFuncion = this.questionarioSelecionado.getEndfotos().get(this.posFotoFuncion - 1);
            this.posFotoFuncion = this.posFotoFuncion - 1;
        }
    }

    public void AvancarFotoPosto() {
        if (this.posFotoPosto + 1 == this.supervisaoSelecionado.getFotos().size()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosFim"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoPosto = this.supervisaoSelecionado.getFotos().get(this.posFotoPosto + 1);
            this.posFotoPosto = this.posFotoPosto + 1;
        }
    }

    public void VoltarFotoPosto() {
        if (this.posFotoPosto == 0) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosInicio"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoPosto = this.supervisaoSelecionado.getFotos().get(this.posFotoPosto - 1);
            this.posFotoPosto = this.posFotoPosto - 1;
        }
    }

    public void NovaQuestao() {
        this.questao = new TbVal();
        this.flagQuestao = 1;
    }

    public void PreEdicaoQuestao() {
        if (null == this.questao) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneQuestao"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.flagQuestao = 2;
            PrimeFaces.current().executeScript("PF('dlgCadastrarQuestao').show()");
        }
    }

    public void ListarQuestoes() {
        try {
            this.lista = this.controlertbval.Listagem312(this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void CadastrarQuestao() {
        try {
            this.questao.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.controlertbval.Inserir312(this.questao, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            PrimeFaces.current().executeScript("PF('dlgCadastrarQuestao').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        ListarQuestoes();
    }

    public void EditarQuestao() {
        try {
            this.questao.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.controlertbval.Atualizar312(this.questao, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            PrimeFaces.current().executeScript("PF('dlgCadastrarQuestao').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        ListarQuestoes();
    }

    public void ExcluirQuestao() {
        try {
            if (null == this.questao) {
                throw new Exception("SelecioneQuestao");
            }
            this.controlertbval.Excluir312(this.questao, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        ListarQuestoes();
    }

    public void ListarSupervisaoCliente(BigDecimal codfil, String codcli, String data, Persistencia persistencia) {
        try {
            this.listaSupervisao = this.supervisoesmobsatweb.ListaSupervisaoCliente(codfil, codcli, data, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<TmktDetPstPstServClientes> getAllSupervisoes() {
        if (this.supervisoes == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace(" tmktdetpst.codfil = ? ", Arrays.asList(this.codFil));
            this.filters.replace(" tmktdetpst.data between ? and ? ", Arrays.asList(this.d1, this.d2));
            dt.setFilters(this.filters);
            this.supervisoes = new SupervisoesLazyList(this.persistencia, this.codPessoa);
        }
        try {
            this.total = this.supervisoesmobsatweb.Contagem(this.filters, this.codPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.supervisoes;
    }

    public void PesquisaPaginada() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        if (null != this.filial) {
            this.filters.replace(" tmktdetpst.codfil = ? ", Arrays.asList(this.filial.getCodfilAc()));
            if (this.filial.getCodfilAc().equals(this.codFil)) {
                this.mostrarFiliais = false;
            } else {
                this.mostrarFiliais = false;
            }
        } else {
            this.filters.replace(" tmktdetpst.codfil = ? ", Arrays.asList());
            this.mostrarFiliais = true;
        }
        if (null != this.dataSelecionada1) {
            this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            this.filters.replace(" tmktdetpst.data between ? and ? ", Arrays.asList(this.data1.toString(), this.data2.toString()));
        } else {
            this.filters.replace(" tmktdetpst.data between ? and ? ", Arrays.asList());
        }

        dt.setFilters(this.filters);
        getAllSupervisoes();
        dt.setFirst(0);
        PrimeFaces.current().executeScript("PF('dlgPesquisar').hide()");
    }

    public List<TmktDetPstPstServClientes> getListaSupervisao() {
        return listaSupervisao;
    }

    public void setListaSupervisao(List<TmktDetPstPstServClientes> listaSupervisao) {
        this.listaSupervisao = listaSupervisao;
    }

    public TmktDetPstPstServClientes getSupervisaoSelecionado() {
        return supervisaoSelecionado;
    }

    public void setSupervisaoSelecionado(TmktDetPstPstServClientes supervisaoSelecionado) {
        this.supervisaoSelecionado = supervisaoSelecionado;
    }

    public List<PstHstQstTbValFuncion> getListaQuestionario() {
        return listaQuestionario;
    }

    public void setListaQuestionario(List<PstHstQstTbValFuncion> listaQuestionario) {
        this.listaQuestionario = listaQuestionario;
    }

    public PstHstQstTbValFuncion getQuestionarioSelecionado() {
        return questionarioSelecionado;
    }

    public void setQuestionarioSelecionado(PstHstQstTbValFuncion questionarioSelecionado) {
        this.questionarioSelecionado = questionarioSelecionado;
    }

    public List<PstHstQstTbValFuncion> getFuncionarios() {
        return funcionarios;
    }

    public void setFuncionarios(List<PstHstQstTbValFuncion> funcionarios) {
        this.funcionarios = funcionarios;
    }

    public List<PstHstQstTbValFuncion> getQuestoes() {
        return questoes;
    }

    public void setQuestoes(List<PstHstQstTbValFuncion> questoes) {
        this.questoes = questoes;
    }

    public HashMap<String, String> getRespostas() {
        return respostas;
    }

    public void setRespostas(HashMap<String, String> respostas) {
        this.respostas = respostas;
    }

    public List<String> getFotoQuestionario() {
        return fotoQuestionario;
    }

    public void setFotoQuestionario(List<String> fotoQuestionario) {
        this.fotoQuestionario = fotoQuestionario;
    }

    public MapModel getPin() {
        return pin;
    }

    public void setPin(MapModel pin) {
        this.pin = pin;
    }

    public Clientes getCliente() {
        return cliente;
    }

    public void setCliente(Clientes cliente) {
        this.cliente = cliente;
    }

    public String getCoordenadas() {
        return coordenadas;
    }

    public void setCoordenadas(String coordenadas) {
        this.coordenadas = coordenadas;
    }

    public TmktDetPstPstServClientes getSupervisao() {
        return supervisao;
    }

    public void setSupervisao(TmktDetPstPstServClientes supervisao) {
        this.supervisaoSelecionado = supervisao;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }

    public String getDetalhesQuestoes() {
        return detalhesQuestoes;
    }

    public void setDetalhesQuestoes(String detalhesQuestoes) {
        this.detalhesQuestoes = detalhesQuestoes;
    }

    public Double getDistPstSup() {
        return distPstSup;
    }

    public void setDistPstSup(Double distPstSup) {
        this.distPstSup = distPstSup;
    }

    public Marker getMarker() {
        return marker;
    }

    public void setMarker(Marker marker) {
        this.marker = marker;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public PstServ getPosto() {
        return posto;
    }

    public void setPosto(PstServ posto) {
        this.posto = posto;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public HashMap<String, String> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(HashMap<String, String> situacoes) {
        this.situacoes = situacoes;
    }

    public List<String> getListaSituacoes() {
        return listaSituacoes;
    }

    public void setListaSituacoes(List<String> listaSituacoes) {
        this.listaSituacoes = listaSituacoes;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public Boolean getMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(Boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public int getPosFotoFuncion() {
        return posFotoFuncion;
    }

    public void setPosFotoFuncion(int posFotoFuncion) {
        this.posFotoFuncion = posFotoFuncion;
    }

    public String getFotoFuncion() {
        return fotoFuncion;
    }

    public void setFotoFuncion(String fotoFuncion) {
        this.fotoFuncion = fotoFuncion;
    }

    public int getPosFotoPosto() {
        return posFotoPosto;
    }

    public void setPosFotoPosto(int posFotoPosto) {
        this.posFotoPosto = posFotoPosto;
    }

    public String getFotoPosto() {
        return fotoPosto;
    }

    public void setFotoPosto(String fotoPosto) {
        this.fotoPosto = fotoPosto;
    }

    public List<TbVal> getLista() {
        return lista;
    }

    public void setLista(List<TbVal> lista) {
        this.lista = lista;
    }

    public TbVal getQuestao() {
        return questao;
    }

    public void setQuestao(TbVal questao) {
        this.questao = questao;
    }

    public int getFlagQuestao() {
        return flagQuestao;
    }

    public void setFlagQuestao(int flagQuestao) {
        this.flagQuestao = flagQuestao;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataSelecionada2() {
        return dataSelecionada2;
    }

    public void setDataSelecionada2(Date dataSelecionada2) {
        this.dataSelecionada2 = dataSelecionada2;
    }

    public LocalDate getData1() {
        return data1;
    }

    public void setData1(LocalDate data1) {
        this.data1 = data1;
    }

    public LocalDate getData2() {
        return data2;
    }

    public void setData2(LocalDate data2) {
        this.data2 = data2;
    }

    public String getD1() {
        return d1;
    }

    public void setD1(String d1) {
        this.d1 = d1;
    }

    public String getD2() {
        return d2;
    }

    public void setD2(String d2) {
        this.d2 = d2;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}

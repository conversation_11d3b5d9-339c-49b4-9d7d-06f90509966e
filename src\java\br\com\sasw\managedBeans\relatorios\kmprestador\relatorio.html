<!DOCTYPE html>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<html>
    <head>
        <title>TODO supply a title</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style  type="text/css">
            @page { size: A4 landscape;}
            .page
            {
                -webkit-transform: rotate(-90deg); 
                -moz-transform:rotate(-90deg);
                filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
            }
            *{
                margin: 6px;
                padding: 3px;
                border: 0;
                font-size: 100%;
                font: inherit;
                box-sizing: border-box;
                -webkit-transform: rotate(-90deg); 
                -moz-transform:rotate(-90deg);
                filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
            }
            table{
                width: 100%;
            }

            .dados{
                font-size: 11px;
                border-collapse: collapse;
                text-align: left;
                width: 100%; 
                background: #fff; 
                overflow: hidden; 
                width: 100%;  
                font-size: 11px;
                padding: 3px; 
                color: #000000; 
                font-weight: normal;
            }

            .dados thead{
                border-top: 2px solid black; 
                border-left: 2px solid black; 
                border-right: 2px solid black; 
            }

            .dados thead td{
                text-align: center;
                border: 1px solid black;
                background: #dbe5f1;
                font-weight: bold;
            }

            .dados tbody{
                border-bottom: 2px solid black; 
                border-left: 2px solid black; 
                border-right: 2px solid black; 
            }

            .dados tbody td{
                border: 1px solid black; 
            }

            .rodape{
                table-layout: fixed;
                text-align: center;
                font-size: 11px;
                border-collapse: collapse;
                width: 100%; 
                background: #fff; 
                overflow: hidden; 
                width: 100%;  
                color: #000000; 
                font-weight: normal;
            }


        </style>
    </head>
    <body>
        <table style="table-layout: fixed; border-collapse: separate; text-align: left; width: 100%; 
               background: #fff; overflow: hidden; width: 100%; border-spacing: 0 3px;
               font-size: 12px; color: #000000; font-weight: normal;">
            <thead >
                <tr>
                    <td colspan="14" style="background: #dbe5f1; text-align: center; height: 40px; font-size: 20px; border: 4px double black;">Formulário Acerto DSE Individual - KM e Despesas</td>
                </tr>
            </thead>
            <tbody  style="font-weight: bold">
                <tr>
                    <td colspan="14" style="font-weight: bold">Colaborador</td>
                </tr>
                <tr>
                    <td>Nome:</td>
                    <td colspan="6" style="background: #f2f2f2; border: 2px solid black;">@NomeColaborador</td>
                    <td></td>
                    <td colspan="2">CPF/CNPJ:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@CPFColaborador</td>
                    <td colspan="2"></td>
                </tr>
                <tr>
                    <td>Cargo:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@CargoColaborador</td>
                    <td colspan="2">Cidade/Bairro (Residência):</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@EndeColaborador</td>
                    <td></td>
                    <td colspan="2">Matrícula:</td>
                    <td style="background: #f2f2f2; border: 2px solid black;">@MatriculaColaborador</td>
                    <td colspan="3"></td>
                </tr>
                <tr>
                    <td>Veículo:</td>
                    <td style="background: #f2f2f2; border: 2px solid black;">@VeiculoColaborador</td>
                    <td colspan="2"></br></td>
                    <td>Placa:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@PlacaColaborador</td>
                    <td></td>
                    <td colspan="2">Mês Ref:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@MesRefColaborador</td>
                    <td colspan="2"></td>
                </tr>
                <tr>
                    <td>Banco:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@BancoColaborador</td>
                    <td></br></td>
                    <td>Agência:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@AgenciaColaborador</td>
                    <td></td>
                    <td colspan="2">Conta:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@ContaColaborador</td>
                    <td colspan="2"></br></td>
                </tr>
                <tr>
                    <td>CC/Projeto:</td>
                    <td colspan="6" style="background: #f2f2f2; border: 2px solid black;">@ProjetoColaborador</td>
                    <td></br></td>
                    <td colspan="2">Empresa/Filial:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@FilialColaborador</td>
                    <td colspan="2"></br></td>
                </tr>
                <tr>
                    <td>Gestor:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@GestorColaborador</td>
                    <td colspan="5"></br></td>
                    <td colspan="2">Região/Projeto:</td>
                    <td colspan="2" style="background: #f2f2f2; border: 2px solid black;">@RegiaoColaborador</td>
                    <td colspan="2"></br></td>
                </tr>
            </tbody>
        </table>

        <table class="dados">
            <thead>
                <tr>
                    <td rowspan="2">Dia</td>
                    <td colspan="5">KM</td>
                    <td></td>
                    <td colspan="7">DESPESAS DE VIAGEM</td>
                </tr>
                <tr>
                    <td>Origem (Cidade-UF/Bairro)</td>
                    <td>Destino (Cidade-UF/Bairro)</td>
                    <td>KM Inicial (Hodômetro)</td>
                    <td>KM Final (Hodômetro)</td>
                    <td>KM Percorrido</td>
                    <td>KM Percorrido</td>
                    <td>Hotel</td>
                    <td>Alimentação</td>
                    <td>Táxi/Ônibus</td>
                    <td>Estacionamento</td>
                    <td>Pedágio</td>
                    <td>Cópias</td>
                    <td>Outros</td>
                </tr>
            </thead>
            <tbody>
                @TBody
            </tbody>
        </table>
        <table class="rodape">
            <tbody>
                <tr>
                    <td colspan="14"></br></td>
                </tr>
                <tr>
                    <td colspan="2" rowspan="3"></br></td>
                    <td rowspan="3"></br></td>
                    <td colspan="2" rowspan="3"></br></td>
                    <td rowspan="3" style="border: 2px solid black; font-size: 8px">EM CASO DE DESCONTO, O MESMO SERÁ FEITO EM FOLHA</td>
                    <td rowspan="3"></br></td>
                    <td colspan="7" style="border: 2px solid black; font-weight: bold; background: #dbe5f1;">DESPESAS DE VIAGEM</td>                    
                </tr>
                <tr>    
                    <td rowspan="2" colspan="2" style="border: 2px solid black; font-weight: bold;">VALORES(R$)</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;">ADIANTAMENTO</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;">REALIZADO</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;" colspan="2">SALDO</td>
                    <td style="border: 1px solid black; border-right: 2px solid black; font-size: 8px; background: #dbe5f1;">SITUAÇÃO</td>
                </tr>
                <tr>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;">@AdiantamentoDespesaViagem</td>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;">@RealizadoDespesaViagem</td>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;" colspan="2">@SaldoDespesaViagem</td>
                    <td style="border-right: 2px solid black; border-bottom: 2px solid black;">@SituacaoDespesaViagem</td>
                </tr>
                <tr>
                    <td style="border-top: 2px solid black" colspan="2">Colaborador</td>
                    <td></br></td>
                    <td style="border-top: 2px solid black" colspan="2">Gerente</td>
                    <td></br></td>
                    <td></br></td>
                    <td colspan="7"></br></td>  
                </tr>
                <tr>
                    <td colspan="5" rowspan="5"></br></td>
                    <td rowspan="5" style="border: 2px solid black; font-size: 8px">EM CASO DE DESCONTO O MESMO SERÁ NO TICKET CAR - EM CASO DE INSUFICIÊCIA, O DESCONTO SERÁ EM FOLHA.</td>
                    <td rowspan="5"></br></td>
                    <td colspan="7" style="border: 2px solid black; font-weight: bold; background: #dbe5f1;">DESLOCAMENTO</td>
                </tr>
                <tr>
                    <td rowspan="2" colspan="2" style="border: 2px solid black; font-weight: bold;">VALORES(KM)</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;">PLANEJADO</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;">REALIZADO</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;" colspan="2">SALDO</td>
                    <td style="border: 1px solid black; border-right: 2px solid black; font-size: 8px; background: #dbe5f1;">SITUAÇÃO</td>
                </tr>
                <tr>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;">@PlanejadoDeslocamentoKM</td>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;">@RealizadoDeslocamentoKM</td>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;" colspan="2">@SaldoDeslocamentoKM</td>
                    <td style="border-right: 2px solid black; border-bottom: 2px solid black;">@SituacaoDeslocamentoKM</td>
                </tr>
                <tr>
                    <td rowspan="2" colspan="2" style="border: 2px solid black; font-weight: bold;">VALORES(R$)</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;">PLANEJADO</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;">REALIZADO à PAGAR</td>
                    <td style="border: 1px solid black; font-size: 8px; background: #dbe5f1;" colspan="2">SALDO</td>
                    <td style="border: 1px solid black; border-right: 2px solid black; font-size: 8px; background: #dbe5f1;">SITUAÇÃO</td>
                </tr>
                <tr>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;">@AdiantamentoDeslocamentosRS</td>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;">@RealizadoDeslocamentosRS</td>
                    <td style="border: 1px solid black; border-bottom: 2px solid black;" colspan="2">@SaldoDeslocamentosRS</td>
                    <td style="border-right: 2px solid black; border-bottom: 2px solid black;">@SituacaoDeslocamentosRS</td>
                </tr>
            </tbody>
        </table>
    </body>
</html>

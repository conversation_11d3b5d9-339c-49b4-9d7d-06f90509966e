package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.SASLog;
import br.com.sasw.pacotesuteis.utilidades.Sqls;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class SASLogDao {

    /**
     * Grava no saslog a baixa de rota Esse método não gera log em caso de erro
     *
     * @param sequencia - Sequencia na saslog
     * @param seqrota - Sequencia de rota
     * @param parada - Parada
     * @param data_sql - Data do log
     * @param hrsaida - Horário de fechamento da parada
     * @param matricula - Matricula do operador do sistema
     * @param persistencia - conexão ao banco
     * @return true - gravou false - falhou
     */
    public boolean gravaSasLogParada(String sequencia, String seqrota, String parada, String data_sql, String hrsaida,
            String matricula, Persistencia persistencia) {
        boolean retorno;
        try {
            String sql = " Insert into SASLOG "
                    + "(Sequencia, Tabela, Codigo, CodFil, Comando, Historico, Operador, Data, Hora) "
                    + "Values (?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(new BigDecimal(sequencia));
            consulta.setString("Rt_Perc");
            consulta.setString(seqrota);
            consulta.setString(parada);
            consulta.setString("Baixa Trajeto Rota - Matricula: " + matricula);
            consulta.setString("Baixa Trajeto Rota - Matricula: " + matricula);
            consulta.setString("SatMobX");
            consulta.setString(data_sql);
            consulta.setString(hrsaida);
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception ex) {
            retorno = false;
        }
        return retorno;
    }

    /**
     * Retorna próxima posição no SASLOG
     *
     * @param persistencia - conexão ao banco
     * @return - posição a ser utilizada
     * @throws Exception
     */
    public String maxSasLog(Persistencia persistencia) throws Exception {
        String sql = "select max(Sequencia)+1 Sequencia from saslog";
        String retorno = "1";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = consult.getString("Sequencia");
                } catch (Exception e) {
                    retorno = "1";
                }
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("SASLogDao.maxSasLog - " + e.getMessage() + "\r\n"
                    + "select max(Sequencia)+1 Sequencia from saslog");
        }
        return retorno;
    }

    /**
     * Inserir Saslog
     *
     * @param saslog - objeto a ser inserido
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public boolean inserirSasLog(SASLog saslog, Persistencia persistencia) {
        boolean retorno;
        try {
            String sql = Sqls.montaInsert(saslog);
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(saslog.getSequencia());
            consulta.setString(saslog.getTabela());
            consulta.setBigDecimal(saslog.getCodigo());
            consulta.setBigDecimal(saslog.getCodFil());
            consulta.setString(saslog.getComando());
            consulta.setString(saslog.getHistorico());
            consulta.setString(saslog.getOperador());
            consulta.setString(saslog.getData());
            consulta.setString(saslog.getHora());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception ex) {
            retorno = false;
        }
        return retorno;
    }

}

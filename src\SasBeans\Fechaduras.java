/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Fechaduras {

    private BigDecimal Codigo;
    private BigDecimal CodFil;
    private int Tipo;
    private int TipoInst;
    private String Identif;
    private String CodCli;
    private String Status;
    private String Obs;
    private String SenhaManager;
    private String SenhaUsuario1;
    private String SenhaUsuario2;
    private String PK_Fechadura;
    private BigDecimal Veiculo;
    private String Operador;
    private String Dt_Alter;
    private String Hr_alter;
    private String Flag_Excl;
    private String OperExcl;
    private String Dt_Excl;
    private String Hr_Excl;
    private String ModTecban;

    /**
     * @return the Codigo
     */
    public BigDecimal getCodigo() {
        return Codigo;
    }

    /**
     * @param Codigo the Codigo to set
     */
    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Tipo
     */
    public int getTipo() {
        return Tipo;
    }

    /**
     * @param Tipo the Tipo to set
     */
    public void setTipo(int Tipo) {
        this.Tipo = Tipo;
    }

    /**
     * @return the TipoInst
     */
    public int getTipoInst() {
        return TipoInst;
    }

    /**
     * @param TipoInst the TipoInst to set
     */
    public void setTipoInst(int TipoInst) {
        this.TipoInst = TipoInst;
    }

    /**
     * @return the Identif
     */
    public String getIdentif() {
        return Identif;
    }

    /**
     * @param Identif the Identif to set
     */
    public void setIdentif(String Identif) {
        this.Identif = Identif;
    }

    /**
     * @return the CodCli
     */
    public String getCodCli() {
        return CodCli;
    }

    /**
     * @param CodCli the CodCli to set
     */
    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    /**
     * @return the Status
     */
    public String getStatus() {
        return Status;
    }

    /**
     * @param Status the Status to set
     */
    public void setStatus(String Status) {
        this.Status = Status;
    }

    /**
     * @return the Obs
     */
    public String getObs() {
        return Obs;
    }

    /**
     * @param Obs the Obs to set
     */
    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    /**
     * @return the SenhaManager
     */
    public String getSenhaManager() {
        return SenhaManager;
    }

    /**
     * @param SenhaManager the SenhaManager to set
     */
    public void setSenhaManager(String SenhaManager) {
        this.SenhaManager = SenhaManager;
    }

    /**
     * @return the SenhaUsuario1
     */
    public String getSenhaUsuario1() {
        return SenhaUsuario1;
    }

    /**
     * @param SenhaUsuario1 the SenhaUsuario1 to set
     */
    public void setSenhaUsuario1(String SenhaUsuario1) {
        this.SenhaUsuario1 = SenhaUsuario1;
    }

    /**
     * @return the SenhaUsuario2
     */
    public String getSenhaUsuario2() {
        return SenhaUsuario2;
    }

    /**
     * @param SenhaUsuario2 the SenhaUsuario2 to set
     */
    public void setSenhaUsuario2(String SenhaUsuario2) {
        this.SenhaUsuario2 = SenhaUsuario2;
    }

    public String getPK_Fechadura() {
        return PK_Fechadura;
    }

    public void setPK_Fechadura(String PK_Fechadura) {
        this.PK_Fechadura = PK_Fechadura;
    }
    
    public void setPK_Fechadura(Double PK_Fechadura) {
        this.PK_Fechadura = Double.toString(PK_Fechadura).replace(".0", "");
    }

    public void setPK_Fechadura(Integer PK_Fechadura) {
        this.PK_Fechadura = Integer.toString(PK_Fechadura);
    }

    

    /**
     * @return the Veiculo
     */
    public BigDecimal getVeiculo() {
        return Veiculo;
    }

    /**
     * @param Veiculo the Veiculo to set
     */
    public void setVeiculo(String Veiculo) {
        try {
            this.Veiculo = new BigDecimal(Veiculo);
        } catch (Exception e) {
            this.Veiculo = new BigDecimal("0");
        }
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

   
    /**
     * @return the Hr_alter
     */
    public String getHr_alter() {
        return Hr_alter;
    }

    /**
     * @param Hr_alter the Hr_alter to set
     */
    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    /**
     * @return the Flag_Excl
     */
    public String getFlag_Excl() {
        return Flag_Excl;
    }

    /**
     * @param Flag_Excl the Flag_Excl to set
     */
    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    /**
     * @return the OperExcl
     */
    public String getOperExcl() {
        return OperExcl;
    }

    /**
     * @param OperExcl the OperExcl to set
     */
    public void setOperExcl(String OperExcl) {
        this.OperExcl = OperExcl;
    }

    public String getDt_Excl() {
        return Dt_Excl;
    }

    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    

    /**
     * @return the Hr_Excl
     */
    public String getHr_Excl() {
        return Hr_Excl;
    }

    /**
     * @param Hr_Excl the Hr_Excl to set
     */
    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }


    /**
     * @return the ModTecban
     */
    public String getModTecban() {
        return ModTecban;
    }

    /**
     * @param Hr_Excl the Hr_Excl to set
     */
    public void setModTecban(String ModTecban) {
        this.ModTecban = ModTecban;
    }    
    
}

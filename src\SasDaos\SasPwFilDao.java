/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.SasPWFill;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SasPwFilDao {

    public void insereSasPwFil(SasPWFill sasPwFil, Persistencia persistencia) throws Exception {
        String sql = " insert into saspwfil(nome, codfilac, codfil, codigo, operador, dt_alter, hr_alter) "
                + "values(?,?,?,?,?,?,?) ";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sasPwFil.getNome());
            consulta.setBigDecimal(sasPwFil.getCodfilAc());                //codigo da filial em filiais
            consulta.setBigDecimal(sasPwFil.getCodFil());                  //codigo da filial do usário online
            consulta.setBigDecimal(sasPwFil.getCodigo());
            consulta.setString(sasPwFil.getOperador());

            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));

            consulta.insert();
            consulta.close();
        } catch (SQLException e) {
            throw new RuntimeException("Falha ao inserir Filiais " + e.getMessage());
        }
    }

    public String getIdiomaUsuario(String CodPessoa, Persistencia persistencia) throws Exception {
        StringBuilder str = new StringBuilder();

        try {
            String retorno = "";

            str.append("SELECT idioma");
            str.append(" FROM saspw");
            str.append(" WHERE CodPessoa = ?");

            Consulta consult = new Consulta(str.toString(), persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.select();

            while (consult.Proximo()) {
                if (!consult.getString("idioma").equals("")) {
                    retorno = consult.getString("idioma");
                }
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao consultar idioma - " + e.getMessage() + "\r\n" + str.toString());
        }
    }

    public String setIdiomaUsuario(String CodPessoa, String Idioma, Persistencia persistencia) throws Exception {
        StringBuilder str = new StringBuilder();

        try {
            String retorno = "";

            str.append("UPDATE saspw");
            str.append(" SET idioma = ?");
            str.append(" WHERE CodPessoa = ?");

            Consulta consult = new Consulta(str.toString(), persistencia);
            consult.setString(Idioma);
            consult.setBigDecimal(CodPessoa);
            consult.insert();
            consult.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao consultar idioma - " + e.getMessage() + "\r\n" + str.toString());
        }
    }

    public static List<SasPWFill> getFilialLogin(String CodPessoa, Persistencia persistencia) throws Exception {
        List<SasPWFill> listFil = new ArrayList();
        String sql = "select saspwfil.Nome, saspwfil.CodfilAc, filiais.Descricao, saspwfil.CodFil "
                + "from saspwfil "
                + "inner join saspw on saspwfil.nome = saspw.nome "
                + "inner join filiais on saspwfil.CodfilAc = filiais.codfil "
                + "where saspw.codpessoa =  ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodPessoa);
            consult.select();
            while (consult.Proximo()) {
                SasPWFill pl = new SasPWFill();
                pl.setCodigo(CodPessoa);
                pl.setNome(consult.getString("Nome"));
                pl.setCodfilAc(consult.getString("CodfilAc").replace(".0", ""));
                pl.setDescricao(consult.getString("Descricao"));
                pl.setCodFil(consult.getString("CodFil"));
                listFil.add(pl);
            }
            consult.Close();
            return listFil;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar filiais - " + e.getMessage());
        }
    }

    /**
     * Carrega Lista de Filiais autorizadas
     *
     * @param sNome - Login no Satellite (SatMobWeb usa o código de pessoa)
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<SasPWFill> getSasPWFill(String sNome, Persistencia persistencia) throws Exception {
        List<SasPWFill> listFil = new ArrayList();
        String sql = "select filiais.codfil, filiais.descricao, saspwfil.operador, saspwfil.dt_alter, saspwfil.hr_alter "
                + "from saspwfil "
                + "inner join filiais on saspwfil.CodfilAc = filiais.codfil "
                + "where saspwfil.nome = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sNome);
            consult.select();
            while (consult.Proximo()) {
                SasPWFill pl = new SasPWFill();
                pl.setCodfilAc(consult.getString("codfil").replace(".0", ""));
                pl.setDescricao(consult.getString("descricao"));
                pl.setOperador(consult.getString("operador"));
                pl.setDt_Alter(consult.getString("dt_alter"));
                pl.setHr_Alter(consult.getString("hr_alter"));
                listFil.add(pl);
            }
            consult.Close();
            return listFil;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar filiais - " + e.getMessage());
        }
    }

    /**
     * Carrega Lista de Filiais autorizadas, cujo a situação é 'A' - Ativa
     *
     * @param CodPessoa - Código de Pessoa do usuário
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<SasPWFill> getSasPWFill(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        List<SasPWFill> listFil = new ArrayList();
        String sql = "select saspw.codpessoa, saspw.CodGrupo, filiais.codfil, isnull(CONVERt(BigInt, pessoa.matr),0) Matr, "
                + " Replicate('0',4-Len(Convert(Varchar,Filiais.Codfil)))+Convert(Varchar,Filiais.Codfil)+' - '+Paramet.Nome_Empr descricao "
                + " from saspw "
                + " inner join saspwfil on saspwfil.nome = saspw.nome "
                + " inner join filiais on filiais.codfil = saspwfil.codfilac "
                + " inner join paramet on paramet.filial_pdr = filiais.codfil "
                + " inner join pessoa on pessoa.codigo = saspw.codpessoa "
                + " where saspw.codpessoa = ? and (paramet.path = ? or paramet.bancodados = ?) "
                + " group by filiais.codfil, saspw.codpessoa, saspw.CodGrupo, pessoa.matr, Paramet.Nome_Empr ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.setString(persistencia.getEmpresa());
            consult.setString(persistencia.getEmpresa());
            consult.select();
            while (consult.Proximo()) {
                SasPWFill pl = new SasPWFill();
                pl.setCodfilAc(consult.getString("codfil").replace(".0", ""));
                pl.setDescricao(consult.getString("descricao"));
                pl.setMatr(consult.getString("matr"));
                pl.setCodGrupo(consult.getString("CodGrupo"));
                listFil.add(pl);
            }
            consult.Close();
            if (listFil.isEmpty()) {
                throw new Exception("UsuarioSemPermissao");
            }
            return listFil;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar filiais - " + e.getMessage());
        }
    }

    /**
     * Carrega Dados de uma Filial autorizada
     *
     * @param CodFil - Código da Filial a ser buscada
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public SasPWFill buscaSasPWFillLogin(String CodFil, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        String sql = "select filiais.codfil, pessoa.matr, "
                + " Replicate('0',4-Len(Convert(Varchar,Filiais.Codfil)))+Convert(Varchar,Filiais.Codfil)+' - '+Paramet.Nome_Empr descricao"
                + " from saspw "
                + " inner join saspwfil on saspwfil.nome = saspw.nome"
                + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                + " inner join pessoa on pessoa.codigo = saspw.codpessoa "
                + " where saspw.codpessoa = ? and filiais.codfil = ? and (paramet.path = ? or paramet.bancodados = ?)";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.setString(CodFil);
            consult.setString(persistencia.getEmpresa());
            consult.setString(persistencia.getEmpresa());
            consult.select();
            SasPWFill pl = new SasPWFill();
            while (consult.Proximo()) {
                pl.setCodfilAc(consult.getString("codfil").replace(".0", ""));
                pl.setDescricao(consult.getString("descricao"));
                pl.setMatr(consult.getString("matr").replace(".0", ""));
            }
            consult.Close();
            return pl;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar filial - " + e.getMessage());
        }
    }

    /**
     * Carrega Dados de uma Filial autorizada
     *
     * @param CodFil - Código da Filial a ser buscada
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public SasPWFill buscaSasPWFill(String CodFil, Persistencia persistencia) throws Exception {
        String sql = "select filiais.codfil, descricao"
                + " from filiais "
                + " where filiais.codfil = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.select();
            SasPWFill pl = null;
            while (consult.Proximo()) {
                pl = new SasPWFill();
                pl.setCodfilAc(consult.getString("codfil").replace(".0", ""));
                pl.setDescricao(consult.getString("descricao"));
            }
            consult.Close();
            return pl;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar filial - " + e.getMessage());
        }
    }

    public List<SasPWFill> verificaSasPWFill(SasPWFill sasPWFill, Persistencia persistencia) throws Exception {
        List<SasPWFill> lSasPWFill = new ArrayList<>();
        String sql = "Select CodfilAc from saspwfil where CodfilAc = ? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);

            consult.setBigDecimal(sasPWFill.getCodfilAc());
            consult.select();
            while (consult.Proximo()) {
                SasPWFill pl = new SasPWFill();
                pl.setCodfilAc(consult.getString("CodfilAc"));
                lSasPWFill.add(pl);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao desassociar pessoa à empresas autorizadas - " + e.getMessage());
        }
        return lSasPWFill;
    }

    public Boolean deletaFilial(SasPWFill sasPWFill, Persistencia persistencia) throws Exception {
        Boolean resultado = true;
        String sql = "delete from saspwfil where CodfilAc = ? ";//and nome = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(sasPWFill.getCodfilAc());
            //consulta.setString( sasPWFill.getOperador());
            resultado = consulta.delete() > 0;
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao desassociar pessoa à empresas autorizadas - " + e.getMessage());
        }
        return resultado;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Processamento {

    private String identificador;

    private String id;
    private String protocolo;

    private String cdResposta;
    private String descResposta;
    private String versaoAppProcessamento;
    private String dhProcessamento;

    private List<Ocorrencia> ocorrencias;

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(String protocolo) {
        this.protocolo = protocolo;
    }

    public String getCdResposta() {
        return cdResposta;
    }

    public void setCdResposta(String cdResposta) {
        this.cdResposta = cdResposta;
    }

    public String getDescResposta() {
        return descResposta;
    }

    public void setDescResposta(String descResposta) {
        this.descResposta = descResposta;
    }

    public String getVersaoAppProcessamento() {
        return versaoAppProcessamento;
    }

    public void setVersaoAppProcessamento(String versaoAppProcessamento) {
        this.versaoAppProcessamento = versaoAppProcessamento;
    }

    public String getDhProcessamento() {
        return dhProcessamento;
    }

    public void setDhProcessamento(String dhProcessamento) {
        this.dhProcessamento = dhProcessamento;
    }

    public List<Ocorrencia> getOcorrencias() {
        return ocorrencias;
    }

    public void setOcorrencias(List<Ocorrencia> ocorrencias) {
        this.ocorrencias = ocorrencias;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.containers;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Municipios;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.RamosAtiv;
import SasBeans.Regiao;
import SasDaos.OS_VigDao;
import br.com.sasw.lazydatamodels.containers.PedidosLazyList;
import br.com.sasw.pacotesuteis.utilidades.BuscarEndereco;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import br.com.sasw.pacotesuteis.utilidades.ValidadorCPF_CNPJ;
import br.com.sasw.utils.LocaleController;
import br.com.sasw.utils.Mascaras;
import static br.com.sasw.utils.Mascaras.CNPJ;
import static br.com.sasw.utils.Mascaras.CPF;
import static br.com.sasw.utils.Mascaras.removeMascara;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.AjaxBehaviorEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.selectoneradio.SelectOneRadio;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.SelectEvent;
import org.primefaces.json.JSONObject;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.map.MapModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "pedidosContainer")
@ViewScoped
public class PedidosContainerMB implements Serializable {

    private final RotasSatWeb rotasSatWeb;
    private Persistencia persistencia;
    private LazyDataModel<Pedido> pedidos = null;
    private Map filters;
    private Filiais filiais;

    private Pedido pedido;
    private String codFil, dataTela, caminho, log, operador, banco,
            hora1, hora2, tipoPedido, pesquisaCliente, pesquisaNumero, pesquisaTipo, pesquisaSomenteData,
            escolha, cpfcnpj, ierg, centroMapa, qtdeCacambas, qtdeOrcamento;

    private Date datad;
    private List<OS_Vig> listaOsVig;
    private OS_Vig os_vig, novaOS_Vig;
    private BigDecimal codpessoa;
    private ArquivoLog logerro;
    private int flag;

    private SimpleDateFormat sdf;
    private Clientes novoCliente;
    private List<RamosAtiv> ramosAtiv;
    private RamosAtiv ramoAtiv;
    private MapModel mapaCliente;
    private List<Regiao> regioes;
    private Regiao regiao, novaRegiao;
    private List<Municipios> cidades;

    public PedidosContainerMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");

        dataTela = getDataAtual("SQL");
        pesquisaCliente = "";
        pesquisaNumero = "";
        pesquisaTipo = "T";
        pesquisaSomenteData = "S";

        rotasSatWeb = new RotasSatWeb();

        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
        sdf = new SimpleDateFormat("yyyyMMdd");
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;

            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.codFil = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("filial");

            this.filters = new HashMap();
            this.filters.put(" Pedido.Data >= ? ", Arrays.asList());
            this.filters.put(" Pedido.Data <= ? ", Arrays.asList());
            this.filters.put(" (Pedido.NRed1 like ? OR Pedido.NRed2 like ?) ", Arrays.asList());
            this.filters.put(" Pedido.Numero = ? ", Arrays.asList());
            this.filters.put(" Pedido.CodCli1 = ? ", Arrays.asList());
            this.filters.put(" Pedido.CodCli2 = ? ", Arrays.asList());
            this.filters.put(" Pedido.Flag_Excl <> ? ", Arrays.asList("*"));
            this.filters.put(" (Pedido.CodCli1 in (Select b.Cliente \n"
                    + "                          from PessoaCliAut a  \n"
                    + "                          Left Join OS_Vig b   \n"
                    + "                            on a.CodCli = b.CliFat                 \n"
                    + "                           and a.CodFil = b.CodFil   \n"
                    + "                          where a.CodFil = Pedido.CodFil     \n"
                    + "                          and a.Codigo = ?   \n"
                    + "                          and a.Flag_Excl <> '*')   OR   \n"
                    + "       Pedido.CodCli2 in (Select b.Cliente \n"
                    + "                          from PessoaCliAut a  \n"
                    + "                          Left Join OS_Vig b   \n"
                    + "                            on a.CodCli = b.CliFat                 \n"
                    + "                           and a.CodFil = b.CodFil   \n"
                    + "                          where a.CodFil = Pedido.CodFil     \n"
                    + "                          and a.Codigo = ?   \n"
                    + "                          and a.Flag_Excl <> '*')) ", Arrays.asList(this.codpessoa.toString(), this.codpessoa.toString()));

            this.filiais = this.rotasSatWeb.buscaInfoFilial(this.codFil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<Pedido> getAllPedidos() {
        if (this.pedidos == null) {
            this.filters.replace(" Pedido.Data >= ? ", Arrays.asList(dataTela));
            this.filters.replace(" Pedido.Data <= ? ", Arrays.asList());

            this.pedidos = new PedidosLazyList(this.persistencia, this.filters);
        } else {
            ((PedidosLazyList) this.pedidos).setFilters(this.filters);
        }
        return this.pedidos;
    }

    public void selecionarData(SelectEvent data) {
        limparFiltros();
        limparPesquisa();

        this.dataTela = (String) data.getObject();

        pesquisar();
    }

    public void pedidoAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);

            pesquisar();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void pedidoPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);

            pesquisar();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void pesquisar() {
        try {
            limparFiltros();

            if (!this.pesquisaCliente.equals("")) {
                this.filters.put(" (Pedido.NRed1 like ? OR Pedido.NRed2 like ?) ", Arrays.asList("%" + this.pesquisaCliente + "%", "%" + this.pesquisaCliente + "%"));
            }

            if (!this.pesquisaNumero.equals("")) {
                this.filters.put(" Pedido.Numero = ? ", Arrays.asList(this.pesquisaNumero));
            }

            switch (this.pesquisaTipo) {
                case "E":
                    this.filters.replace(" Pedido.CodCli1 = ? ", Arrays.asList("9996900"));

                    break;
                case "R":
                    this.filters.replace(" Pedido.CodCli2 = ? ", Arrays.asList("9996015"));

                    break;
            }

            if (this.pesquisaSomenteData.equals("S")) {
                this.filters.replace(" Pedido.Data >= ? ", Arrays.asList(this.dataTela));

                if (!this.dataTela.equals(getDataAtual("SQL"))) {
                    this.filters.replace(" Pedido.Data <= ? ", Arrays.asList(this.dataTela));
                } else {
                    this.filters.replace(" Pedido.Data <= ? ", Arrays.asList());
                }
            }

            getAllPedidos();

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private void limparFiltros() {
        this.filters.replace(" Pedido.Data >= ? ", Arrays.asList());
        this.filters.replace(" Pedido.Data <= ? ", Arrays.asList());
        this.filters.replace(" (Pedido.NRed1 like ? OR Pedido.NRed2 like ?) ", Arrays.asList());
        this.filters.replace(" Pedido.Numero = ? ", Arrays.asList());
        this.filters.replace(" Pedido.CodCli1 = ? ", Arrays.asList());
        this.filters.replace(" Pedido.CodCli2 = ? ", Arrays.asList());
    }

    private void limparPesquisa() {
        limparFiltros();

        this.pesquisaCliente = "";
        this.pesquisaNumero = "";
        this.pesquisaTipo = "T";
        this.pesquisaSomenteData = "S";

        this.filters.replace(" Pedido.Data >= ? ", Arrays.asList(this.dataTela));

        if (!this.dataTela.equals(getDataAtual("SQL"))) {
            this.filters.replace(" Pedido.Data <= ? ", Arrays.asList(this.dataTela));
        }

        getAllPedidos();
    }

    public void limparPesquisaBt() {
        dataTela = getDataAtual("SQL");
        limparPesquisa();
    }

    public void preCadastro() {

        this.hora1 = new String();
        this.hora2 = new String();

        this.datad = Date.from(Instant.now());

        this.tipoPedido = "recolhimento";

        this.pedido = new Pedido();
        this.pedido.setSolicitante(this.operador);
        this.pedido.setValor("0");
        this.pedido.setChequesQtde(0);
        this.pedido.setChequesValor("0");
        this.pedido.setTicketsQtde("0");
        this.pedido.setTicketsValor("0");
        this.pedido.setSeqRota("0");
        this.pedido.setNumero("0");
        this.pedido.setQtdeCacambas("1");

        this.os_vig = null;

        this.novaOS_Vig = new OS_Vig();
        this.novaOS_Vig.setOS("-1");
        this.novaOS_Vig.setNRed(getMessageS("ClienteNaoEncontrado"));

        this.flag = 1;

        PrimeFaces.current().resetInputs("cadastrarPedido");
        PrimeFaces.current().executeScript("PF('dlgPedido').show();");
    }

    public void abrirPedido(Pedido pedido) {
        try {
            this.pedido = pedido;
            this.flag = 2;
            this.qtdeCacambas = "1";

            if (this.pedido.getCodCli1().equals("9996900")) {
                this.tipoPedido = "suprimento"; // entrega

                // Buscando a OS
                this.os_vig = this.rotasSatWeb.buscarOSContainer(this.pedido.getCodCli2(), this.pedido.getData(), this.persistencia);

                this.hora1 = this.pedido.getHora1D();
                this.hora2 = this.pedido.getHora2D();
            } else {
                this.tipoPedido = "recolhimento";

                // Buscando a OS
                this.os_vig = this.rotasSatWeb.buscarOSContainer(this.pedido.getCodCli1(), this.pedido.getData(), this.persistencia);

                this.hora1 = this.pedido.getHora1O();
                this.hora2 = this.pedido.getHora2O();
            }

            this.datad = new SimpleDateFormat("yyyyMMdd").parse(this.pedido.getData());
            this.listaOsVig = new ArrayList<>();
            this.listaOsVig.add(this.os_vig);

            PrimeFaces.current().resetInputs("cadastrarPedido");
            PrimeFaces.current().ajax().update("cadastrarPedido");
            PrimeFaces.current().executeScript("PF('dlgPedido').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarCliente(SelectEvent event) {
        this.os_vig = ((OS_Vig) event.getObject());
        if (this.os_vig.getOS().equals("-1")) {
            this.os_vig = null;
            preCadastroCliente();
        }
    }

    public List<OS_Vig> buscarOS(String query) {
        try {
            this.novoCliente = new Clientes();
            this.novoCliente.setNRed(query);
            this.listaOsVig = this.rotasSatWeb.buscarOS(query, this.codpessoa.toString(), this.tipoPedido.equals("recolhimento"), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        if (this.tipoPedido.equals("suprimento")) {
            this.listaOsVig.add(this.novaOS_Vig);
        }
        return this.listaOsVig;
    }

    public void tipoServico(AjaxBehaviorEvent event) {
        this.tipoPedido = (String) ((SelectOneRadio) event.getSource()).getValue();
    }

    public void cadastrarPedido() {
        try {
            this.pedido.setCodFil(this.codFil);
            this.pedido.setQtdeCacambas(this.pedido.getQtdeCacambas());
            this.pedido.setSolicitante(RecortaString(this.pedido.getSolicitante().toUpperCase(), 0, 30));
            this.pedido.setSituacao("PD");
            this.pedido.setTipo("T");
            if (this.tipoPedido.equals("recolhimento")) {
                this.pedido.setCodCli1(this.os_vig.getCliente());
                this.pedido.setNRed1(this.os_vig.getNRed());

                // Destino da OS sempre é o aterro
                this.pedido.setCodCli2(this.os_vig.getCliDst());
                this.pedido.setNRed2(this.os_vig.getNRedDst());

                this.pedido.setHora1O(this.hora1);
                this.pedido.setHora2O(this.hora2);
                this.pedido.setHora1D(this.hora2);
                this.pedido.setHora2D("18:00");
            } else {
                // para entregas, a origem é sempre a base
                this.pedido.setCodCli1("9996900");
                this.pedido.setNRed1("BASE SIA TRECHO 17");

                this.pedido.setCodCli2(this.os_vig.getCliente());
                this.pedido.setNRed2(this.os_vig.getNRed());

                this.pedido.setHora1O("08:00");
                this.pedido.setHora2O(this.hora1);
                this.pedido.setHora1D(this.hora1);
                this.pedido.setHora2D(this.hora2);
            }

            this.pedido.setOS(this.os_vig.getOS().toString());
            this.pedido.setData(this.sdf.format(this.datad));
            this.pedido.setDt_Incl(getDataAtual("SQL"));
            this.pedido.setHr_Incl(getDataAtual("HORA"));
            this.pedido.setDt_Alter(getDataAtual("SQL"));
            this.pedido.setHr_Alter(getDataAtual("HORA"));
            this.pedido.setOperIncl(RecortaAteEspaço(this.operador, 0, 10));
            this.pedido.setOperador(RecortaAteEspaço(this.operador, 0, 10));

            this.pedido.setFlag_Excl("");
            this.pedido.setPedidoCliente(this.pedido.getPedidoCliente().toUpperCase());
            this.pedido.setObs(this.pedido.getObs().toUpperCase());
            if (this.pedido.getDt_Incl().equals(this.pedido.getData())) {
                this.pedido.setClassifSrv("E");
            } else {
                this.pedido.setClassifSrv("V");
            }

            FacesMessage message;

            if (this.pedido.getNumero().compareTo(BigDecimal.ZERO) == 0) {
                for (int i = 0; i < Integer.parseInt(this.pedido.getQtdeCacambas()); i++) {
                    this.pedido.setNumero(this.rotasSatWeb.inserirPedido(this.pedido, this.persistencia));

                    message = new FacesMessage(FacesMessage.SEVERITY_INFO,
                            getMessageS("CadastroSucesso") + " \r\n " + getMessageS("Pedido") + ": " + this.pedido.getNumero().toBigInteger(),
                            "\r\n " + getMessageS("Cliente") + ": " + this.os_vig.getNRed());
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }
            } else {
                this.rotasSatWeb.editarPedido(this.pedido, this.persistencia);
                message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("EdicaoSucesso"),
                        getMessageS("Pedido") + ": " + this.pedido.getNumero().toBigInteger());
                FacesContext.getCurrentInstance().addMessage(null, message);
            }

            PrimeFaces.current().executeScript("PF('dlgPedido').hide();");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void excluirPedido(Pedido pedido) {
        try {
            this.pedido = pedido;
            this.pedido.setTipo("T");
            this.pedido.setCodFil(this.codFil);
            this.pedido.setFlag_Excl("*");
            this.pedido.setDt_Excl(getDataAtual("SQL"));
            this.pedido.setHr_Excl(getDataAtual("HORA"));
            this.pedido.setOperExcl(RecortaAteEspaço(this.operador, 0, 10));

            FacesMessage message;
            if (this.pedido.getSituacao().equals("PD")) {
                this.rotasSatWeb.editarPedido(this.pedido, this.persistencia);

                OS_VigDao osvigdao = new OS_VigDao();
                osvigdao.acrescentarGtvQtde(this.pedido.getOS().toString(), this.pedido.getCodFil().toString(), this.persistencia);

                message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("ExclusaoSucesso"),
                        getMessageS("Pedido") + ": " + this.pedido.getNumero().toBigInteger());
                FacesContext.getCurrentInstance().addMessage(null, message);

            } else {
                message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("ExclusaoPedidoNaoPermitida"),
                        getMessageS("Pedido") + ": " + this.pedido.getNumero().toBigInteger());
                FacesContext.getCurrentInstance().addMessage(null, message);
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preCadastroCliente() {
        try {
            this.novoCliente.setCodFil("1");
            this.regioes = this.rotasSatWeb.listarRegioes("1", this.persistencia);
            this.regiao = null;
            this.ramosAtiv = this.rotasSatWeb.listarRamosAtiv(this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        PrimeFaces.current().resetInputs("formCadastrar");
        PrimeFaces.current().ajax().update("formCadastrar:cadastrar");
        TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
        tabs.setActiveIndex(0);
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
    }

    public void mascaraCNPJCPF() {
        try {
            if (this.cpfcnpj != null) {
                this.cpfcnpj = removeMascara(this.cpfcnpj);

                if (this.cpfcnpj.length() == 11) {
                    this.escolha = "cpf";
                    this.cpfcnpj = CPF(this.cpfcnpj);
                } else {
                    this.escolha = "cgc";
                    if (this.cpfcnpj.length() == 14) {
                        this.cpfcnpj = CNPJ(this.cpfcnpj);
                    }
                }
            }
            System.out.println("mascaraCNPJCPF: " + this.cpfcnpj);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void buscarEndereco() {
        try {
            String enderecoCompleto = new BuscarEndereco().BuscarPeloCEP(this.novoCliente.getCEP());
            JSONObject obj = new JSONObject(enderecoCompleto);
            if (Integer.parseInt(obj.get("resultado").toString()) == 1) {
                this.novoCliente.setBairro(obj.get("bairro").toString());
                this.novoCliente.setEnde(obj.get("tipo_logradouro").toString() + " " + obj.get("logradouro").toString());
                if (obj.get("uf").toString().equals("DF") || obj.get("uf").toString().equals("df")) {
                    this.novoCliente.setCidade("BRASILIA");
                    this.novoCliente.setEstado("DF");
                } else {
                    this.cidades = this.rotasSatWeb.listarMunicipios(obj.get("uf").toString(), obj.get("cidade").toString(), this.persistencia);
                    this.novoCliente.setCidade(this.cidades.get(0).getNome());
                    this.novoCliente.setEstado(this.cidades.get(0).getUF().substring(0, 2));
                }
                this.novoCliente.setCEP(Mascaras.CEP(this.novoCliente.getCEP()));
                PrimeFaces.current().executeScript("PF('dlgOk').show()");
            } else {
                this.novoCliente.setCEP(Mascaras.CEP(this.novoCliente.getCEP()));
                throw new Exception(Messages.getMessageS("EnderecoNaoEncontrado"));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<String> buscarCidade(String query) {
        try {
            List<String> retorno = new ArrayList<>();
            this.cidades = this.rotasSatWeb.listarMunicipios(query, this.persistencia);
            for (Municipios cidade : this.cidades) {
                retorno.add(cidade.getNome() + ", " + cidade.getUF());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public void selecionarCidade(SelectEvent event) {
        String[] parts = event.getObject().toString().split(", ");
        this.novoCliente.setCidade(parts[0]);
        this.novoCliente.setEstado(parts[1]);
    }

    public void cadastrarCliente() {
        try {
            this.novoCliente.setCodFil("1");
            this.novoCliente.setOper_Alt(RecortaAteEspaço(this.operador, 0, 10));
            this.novoCliente.setHr_Alter(getDataAtual("HORA"));
            this.novoCliente.setDt_Alter(LocalDate.now());

            if (this.novoCliente.getNome() != null) {
                this.novoCliente.setNome(this.novoCliente.getNome().toUpperCase());
            }
            if (this.novoCliente.getNRed() != null) {
                this.novoCliente.setNRed(this.novoCliente.getNRed().toUpperCase());
            }
            if (this.novoCliente.getEnde() != null) {
                this.novoCliente.setEnde(this.novoCliente.getEnde().toUpperCase());
            }
            if (this.novoCliente.getBairro() != null) {
                this.novoCliente.setBairro(this.novoCliente.getBairro().toUpperCase());
            }
            if (this.novoCliente.getCidade() != null) {
                this.novoCliente.setCidade(this.novoCliente.getCidade().toUpperCase());
            }
            if (this.novoCliente.getEstado() != null) {
                this.novoCliente.setEstado(this.novoCliente.getEstado().toUpperCase());
            }
            if (this.novoCliente.getIE() != null) {
                this.novoCliente.setIE(this.novoCliente.getIE().toUpperCase());
            }
            this.novoCliente.setSituacao("A");
            this.novoCliente.setDiaFechaFat(1);
            this.novoCliente.setDiaVencNF(1);
            if (this.regiao == null) {
                this.novoCliente.setRegiao("999");
            } else {
                this.novoCliente.setRegiao(this.regiao.getRegiao());
            }

            if (this.escolha != null) {
                if (this.escolha.equals("cpf")) {
                    this.novoCliente.setRG(this.ierg);
                } else {
                    if (this.ierg != null) {
                        this.novoCliente.setInsc_Munic(this.ierg.toUpperCase());
                    }
                }
            }

            this.novoCliente.setFone1(Mascaras.removeMascara(this.novoCliente.getFone1()));
            this.novoCliente.setFone2(Mascaras.removeMascara(this.novoCliente.getFone2()));

            Locale locale = LocaleController.getsCurrentLocale();
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!this.novoCliente.getCPF().isEmpty()) {
                    this.novoCliente.setCPF(Mascaras.removeMascara(this.novoCliente.getCPF()));
                    if (!ValidadorCPF_CNPJ.ValidarCPF(this.novoCliente.getCPF())) {
                        throw new Exception(Messages.getMessageS("CPFInvalido"));
                    }
                }
                if (!this.novoCliente.getCGC().isEmpty()) {
                    this.novoCliente.setCGC(Mascaras.removeMascara(this.novoCliente.getCGC()));
                    if (!ValidadorCPF_CNPJ.ValidarCNPJ(Mascaras.removeMascara(this.novoCliente.getCGC()))) {
                        throw new Exception(Messages.getMessageS("CNPJInvalido"));
                    }
                }
            }
            //this.novoCliente.setOrcamento(qtdeCacambas);

            this.novoCliente.setCEP(Mascaras.removeMascara(this.novoCliente.getCEP()));
            this.rotasSatWeb.inserirCliente(this.novoCliente, this.codpessoa, this.qtdeCacambas, this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Pedido getPedido() {
        return pedido;
    }

    public void setPedido(Pedido pedido) {
        this.pedido = pedido;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = hora1;
    }

    public String getHora2() {
        return hora2;
    }

    public void setHora2(String hora2) {
        this.hora2 = hora2;
    }

    public String getTipoPedido() {
        return tipoPedido;
    }

    public void setTipoPedido(String tipoPedido) {
        this.tipoPedido = tipoPedido;
    }

    public List<OS_Vig> getListaOsVig() {
        return listaOsVig;
    }

    public void setListaOsVig(List<OS_Vig> listaOsVig) {
        this.listaOsVig = listaOsVig;
    }

    public OS_Vig getOs_vig() {
        return os_vig;
    }

    public void setOs_vig(OS_Vig os_vig) {
        this.os_vig = os_vig;
    }

    public Date getDatad() {
        return datad;
    }

    public void setDatad(Date datad) {
        this.datad = datad;
    }

    public String getPesquisaCliente() {
        return pesquisaCliente;
    }

    public void setPesquisaCliente(String pesquisaCliente) {
        this.pesquisaCliente = pesquisaCliente;
    }

    public String getPesquisaNumero() {
        return pesquisaNumero;
    }

    public void setPesquisaNumero(String pesquisaNumero) {
        this.pesquisaNumero = pesquisaNumero;
    }

    public String getPesquisaTipo() {
        return pesquisaTipo;
    }

    public void setPesquisaTipo(String pesquisaTipo) {
        this.pesquisaTipo = pesquisaTipo;
    }

    public String getPesquisaSomenteData() {
        return pesquisaSomenteData;
    }

    public void setPesquisaSomenteData(String pesquisaSomenteData) {
        this.pesquisaSomenteData = pesquisaSomenteData;
    }

    public Clientes getNovoCliente() {
        return novoCliente;
    }

    public void setNovoCliente(Clientes novoCliente) {
        this.novoCliente = novoCliente;
    }

    public List<RamosAtiv> getRamosAtiv() {
        return ramosAtiv;
    }

    public void setRamosAtiv(List<RamosAtiv> ramosAtiv) {
        this.ramosAtiv = ramosAtiv;
    }

    public RamosAtiv getRamoAtiv() {
        return ramoAtiv;
    }

    public void setRamoAtiv(RamosAtiv ramoAtiv) {
        this.ramoAtiv = ramoAtiv;
    }

    public MapModel getMapaCliente() {
        return mapaCliente;
    }

    public void setMapaCliente(MapModel mapaCliente) {
        this.mapaCliente = mapaCliente;
    }

    public List<Regiao> getRegioes() {
        return regioes;
    }

    public void setRegioes(List<Regiao> regioes) {
        this.regioes = regioes;
    }

    public Regiao getRegiao() {
        return regiao;
    }

    public void setRegiao(Regiao regiao) {
        this.regiao = regiao;
    }

    public Regiao getNovaRegiao() {
        return novaRegiao;
    }

    public void setNovaRegiao(Regiao novaRegiao) {
        this.novaRegiao = novaRegiao;
    }

    public String getEscolha() {
        return escolha;
    }

    public void setEscolha(String escolha) {
        this.escolha = escolha;
    }

    public String getCpfcnpj() {
        return cpfcnpj;
    }

    public void setCpfcnpj(String cpfcnpj) {
        this.cpfcnpj = cpfcnpj;
    }

    public String getIerg() {
        return ierg;
    }

    public void setIerg(String ierg) {
        this.ierg = ierg;
    }

    public String getCentroMapa() {
        return centroMapa;
    }

    public void setCentroMapa(String centroMapa) {
        this.centroMapa = centroMapa;
    }

    public String getQtdeCacambas() {
        return qtdeCacambas;
    }

    public void setQtdeCacambas(String qtdeCacambas) {
        this.qtdeCacambas = qtdeCacambas;
    }

    public String getQtdeOrcamento() {
        return qtdeOrcamento;
    }

    public void setQtdeOrcamento(String qtdeOrcamento) {
        this.qtdeOrcamento = qtdeOrcamento;
    }
}

package br.com.sasw.managedBeans.tesouraria.TesEntrada;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Controller.CxForte.CustodiaSatMobWeb;
import Controller.DashBoard.DashBoard;
import Controller.Guias.GuiasSatWeb;
import Controller.OS_Vig.OS_VigSatMobWeb;
import Controller.Pedidos.PedidosSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Controller.Tesouraria.TesourariaController;
import Dados.Persistencia;
import SasBeans.*;
import SasBeansCompostas.TesConta;
import SasDaos.TesEntradaDao;
import br.com.sasw.lazydatamodels.TesEntradaLazyList;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.utils.Mascaras.Moeda;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import org.omnifaces.cdi.Push;
import org.omnifaces.cdi.PushContext;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
@Named(value = "tesEntrada")
@ViewScoped
public class TesEntradaMB implements Serializable {

    private Persistencia persistencia;
    private final String caminho;
    private final String banco;
    private final String codPessoa;
    private String codFil, codTesouraria, estatisticaQtdeLabel, estatisticaQtde, estatisticaValorLabel, estatisticaValor, estatisticaHoraLabel, estatisticaHora, estatisticaHoraValorLabel, estatisticaHoraValor, estatisticaCores1, estatisticaCores2, estatisticaCores3, estatisticaCores4,
            dashResumoTotalQtde, dashResumoTotalHoras, dashResumoTotalConferentes, dashResumoTotalValor,
            estatisticaDiaQtdeLabel, estatisticaDiaQtde, estatisticaDiaValorLabel, estatisticaDiaValor;
    private final String operador;
    private final ArquivoLog logerro;
    private String log, dataTela, data1, data2, dataRef;
    private Filiais filialTela;
    private List<SasPWFill> filiais;
    private final TesourariaController tesourariaController;
    private final CustodiaSatMobWeb custodiaController;
    private final OS_VigSatMobWeb os_VigSatMobWeb;
    private final TesEntradaDao tesEntradaDao;
    private final ClientesSatMobWeb clientesSatMobWeb;
    private final Map<String, Object> filters = new HashMap<>();
    private LazyDataModel<TesEntrada> lazyTesEntradas;
    private TesEntrada numerarioClick;
    private boolean apenasPendentes = true, corporativo;
    private String chavePesquisa = "guia", valorPesquisa;
    // Cadastros:
    private String codFilEdicao;
    private TesEntrada numerarioEdicao = new TesEntrada();
    private SasPWFill filialSelecionada;
    private List<CxFGuias> listaGuias;
    private CxFGuias guiaEdicao;
    private List<CxFGuiasVol> cxfGuiasVolLista;
    private boolean editandoEntrada = false;
    // Clientes:
    private Clientes clienteSelecionado, tesourariaSelecionada;
    private List<Clientes> listaClientes, listaTesouraria;
    // OS:
    private OS_Vig os_vigSelecionado;
    private final Map<String, String> filtersOS = new HashMap<String, String>();
    private List<OS_Vig> listaOS;
    // Conta:
    private TesConta contaSelecionada;
    private List<TesConta> listaContas;
    // Lotes:
    private TesConta loteSelecionado;
    private List<TesConta> listaLotes;
    private List<CxForte> cxForteLista;
    private final PedidosSatMobWeb pedidosSatMobWeb;
    private GuiasSatWeb guiasSatWeb;

    private List<Filiais> listaFiliais;

    private List<Date> datasSelecionadas;
    private Date dataInicio, dataFim;

    private List<Funcion> listaFuncionarios;
    private BigDecimal funcionarioSelecionado;

    private List<TesEntrada> listaDashGride, listaProdutividade;

    public TesEntradaMB() throws Exception {
        FacesContext fc = FacesContext.getCurrentInstance();
        LoginMB login = fc.getApplication().evaluateExpressionGet(fc, "#{login}", LoginMB.class);
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = ((BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa")).toBigInteger().toString();
        log = "";
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa + ".txt";
        logerro = new ArquivoLog();
        dataTela = DataAtual.getDataAtual("SQL");

        // persistência e serviços
        RotasSatWeb rotassatweb = new RotasSatWeb();
        guiasSatWeb = new GuiasSatWeb();

        try {
            persistencia = login.getPp();
            if (persistencia == null) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
            }

            filialTela = rotassatweb.buscaInfoFilial(codFil, persistencia);
            filiais = login.getFiliais();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                displayFatal(ex.getMessage());
            }
        }

        pedidosSatMobWeb = new PedidosSatMobWeb();
        custodiaController = new CustodiaSatMobWeb();
        os_VigSatMobWeb = new OS_VigSatMobWeb();
        tesourariaController = new TesourariaController(persistencia);
        tesEntradaDao = new TesEntradaDao(persistencia);
        clientesSatMobWeb = new ClientesSatMobWeb();
        cxForteLista = this.pedidosSatMobWeb.listarCaixasForte(this.codFil, this.persistencia);
        buscarListaLotes();
        buscarListaContas();
        corporativo = false;
        numerarioEdicao.setTesLote(new TesConta());
        numerarioEdicao.setConta(new TesConta());
        listaFiliais = new ArrayList<>();
        listaTesouraria = new ArrayList<>();
        listaFuncionarios = new ArrayList<>();
        listaDashGride = new ArrayList<>();
        listaProdutividade = new ArrayList<>();
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        dataFim = c.getTime();
        dataInicio = c.getTime();
        data1 = dataFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        data2 = dataFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(dataInicio); // data inicial
        datasSelecionadas.add(dataFim); // data final

        for (SasPWFill item : filiais) {
            if (item.getCodfilAc().equals(this.codFil)) {
                filialSelecionada = item;
            }
        }
        setFilters();
    }

    @PostConstruct
    private void init() {
        carregarNumerarios();
    }

    private void displayInfo(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayWarn(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayError(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayFatal(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_FATAL, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void logaErro(final Exception e, final String methodName) {
        displayError(e.getMessage());
        log = this.getClass().getSimpleName() + "\r\n"
                + methodName + "\r\n"
                + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    public void carregarFiliais(boolean carregaDash) throws Exception {
        listaFiliais = tesEntradaDao.listaFiliais(this.persistencia);

        if (carregaDash) {
            carregarDashboard();
        }
    }

    public void carregarTesouraria(boolean carregaDash) throws Exception {
        listaTesouraria = tesEntradaDao.listaTesourarias(this.codFil, this.persistencia);

        if (carregaDash) {
            carregarDashboard();
        }
    }

    public void carregarTesourariaFuncionario(boolean carregaDash) throws Exception {
        carregarTesouraria(false);
        carregarFuncionarios(carregaDash);

        if (carregaDash) {
            carregarDashboard();
        }
    }

    public void carregarFuncionarios(boolean carregaDash) throws Exception {
        listaFuncionarios = tesEntradaDao.listaFuncionarios(this.codFil, this.codTesouraria, this.data1, this.data2, this.persistencia);

        if (carregaDash) {
            carregarDashboard();
        }
    }

    public void carregarDashboard() throws Exception {
        carregarEstatisticaPorQtdeValorHora();
        carregarEstatisticaPorDia();

        this.listaDashGride = tesEntradaDao.analiseDetalhadoGride(this.codFil, this.codTesouraria, null != this.funcionarioSelecionado ? this.funcionarioSelecionado.toPlainString().replace(".0", "") : "", this.data1, this.data2, this.persistencia);
    }

    public void selecionarDatas(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                carregamentoDashboard();
                //PrimeFaces.current().ajax().update("main:tabela");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarDatasProdutividade(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                carregarProdutividade();
                //PrimeFaces.current().ajax().update("main:tabela");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }
    
    public void carregamentoDashboard() throws Exception {
        carregarFiliais(false);
        carregarTesouraria(false);
        carregarFuncionarios(false);
        carregarDashboard();

        //PrimeFaces.current().ajax().update("top:pnlFiltros");
    }

    public void carregarEstatisticaPorDia() throws Exception {
        dataRef = data1;

        if (data1.equals(data2)) {
            Calendar c = Calendar.getInstance();
            c.setTime(dataFim);
            c.add(Calendar.DATE, -30);

            dataRef = c.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }

        List<TesEntrada> listaAnalisePorDia = tesEntradaDao.analisePorDia(this.codFil, this.codTesouraria, null != this.funcionarioSelecionado ? this.funcionarioSelecionado.toPlainString().replace(".0", "") : "", this.dataRef, this.data2, this.persistencia);

        this.estatisticaDiaQtdeLabel = "";
        this.estatisticaDiaQtde = "0";
        this.estatisticaDiaValorLabel = "0";
        this.estatisticaDiaValor = "";

        String Qde = "", TotalValores = "", Labels = "", Cores = "";
        TesEntrada dashItem = new TesEntrada();

        if (listaAnalisePorDia.size() > 0) {
            for (int i = 0; i < listaAnalisePorDia.size(); i++) {
                dashItem = listaAnalisePorDia.get(i);

                Labels += !Labels.equals("") ? ",'" + dashItem.getData() + "'" : "'" + dashItem.getData() + "'";
                Qde += !Qde.equals("") ? "," + dashItem.getQtde().toPlainString().replace(".0", "") : dashItem.getQtde().toPlainString().replace(".0", "");
                Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";

                TotalValores += !TotalValores.equals("") ? "," + dashItem.getValorRec() : dashItem.getValorRec();
            }

            this.estatisticaDiaQtdeLabel = "[" + Labels + "]";
            this.estatisticaDiaValorLabel = "[" + Labels + "]";

            this.estatisticaDiaQtde = "[" + Qde + "]";
            this.estatisticaDiaValor = "[" + TotalValores + "]";
        }
    }

    public void carregarEstatisticaPorQtdeValorHora() throws Exception {
        this.estatisticaQtdeLabel = "";
        this.estatisticaQtde = "0";
        this.estatisticaValorLabel = "";
        this.estatisticaValor = "0";
        this.estatisticaHoraLabel = "";
        this.estatisticaHora = "0";
        this.estatisticaHoraValorLabel = "";
        this.estatisticaHoraValor = "0";
        this.estatisticaCores1 = "";
        this.estatisticaCores2 = "";
        this.estatisticaCores3 = "";
        this.estatisticaCores4 = "";

        this.dashResumoTotalQtde = "0";
        this.dashResumoTotalHoras = "00:00";
        this.dashResumoTotalConferentes = "0";
        this.dashResumoTotalValor = "0";

        // Análise por Qtde
        List<TesEntrada> listaQtdeValor = tesEntradaDao.analiseQtdeValor(this.codFil, this.codTesouraria, null != this.funcionarioSelecionado ? this.funcionarioSelecionado.toPlainString().replace(".0", "") : "", this.data1, this.data2, this.persistencia);
        this.dashResumoTotalHoras = tesEntradaDao.analiseTempoTotal(this.codFil, this.codTesouraria, null != this.funcionarioSelecionado ? this.funcionarioSelecionado.toPlainString().replace(".0", "") : "", this.data1, this.data2, this.persistencia).getTempoHora();

        String Labels = "", Cores = "", TotalValores = "";
        TesEntrada dashItem = new TesEntrada();

        if (listaQtdeValor.size() > 0) {
            for (int i = 0; i < listaQtdeValor.size(); i++) {
                dashItem = listaQtdeValor.get(i);

                if (null != dashItem.getQtde()) {
                    Labels += !Labels.equals("") ? ",'" + dashItem.getNomeConferente() + "'" : "'" + dashItem.getNomeConferente() + "'";
                    TotalValores += !TotalValores.equals("") ? "," + dashItem.getQtde() : dashItem.getQtde();
                    Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";

                    dashResumoTotalQtde = Integer.toString(Integer.parseInt(dashResumoTotalQtde) + Integer.parseInt(dashItem.getQtde().toPlainString().replace(".0", "")));
                    dashResumoTotalConferentes = Integer.toString(Integer.parseInt(dashResumoTotalConferentes) + 1);
                }
            }

            this.estatisticaQtdeLabel = "[" + Labels + "]";
            this.estatisticaCores1 = "[" + Cores + "]";
            this.estatisticaQtde = "[" + TotalValores + "]";
        }

        // Análise Por Valor
        Labels = "";
        Cores = "";
        TotalValores = "";
        dashItem = new TesEntrada();

        if (listaQtdeValor.size() > 0) {
            for (int i = 0; i < listaQtdeValor.size(); i++) {
                dashItem = listaQtdeValor.get(i);

                if (null != dashItem.getQtde()) {
                    Labels += !Labels.equals("") ? ",'" + dashItem.getNomeConferente() + "'" : "'" + dashItem.getNomeConferente() + "'";
                    TotalValores += !TotalValores.equals("") ? "," + dashItem.getValor().toPlainString() : dashItem.getValor().toPlainString();
                    Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";

                    dashResumoTotalValor = Double.toString((Double.parseDouble(dashResumoTotalValor) + Double.parseDouble(dashItem.getValor().toPlainString())));
                }
            }

            this.estatisticaValorLabel = "[" + Labels + "]";
            this.estatisticaCores2 = "[" + Cores + "]";
            this.estatisticaValor = "[" + TotalValores + "]";
        }

        dashResumoTotalValor = Moeda(dashResumoTotalValor);

        // Análise por Hora - Qtde
        Labels = "";
        Cores = "";
        TotalValores = "";
        dashItem = new TesEntrada();

        if (listaQtdeValor.size() > 0) {
            for (int i = 0; i < listaQtdeValor.size(); i++) {
                dashItem = listaQtdeValor.get(i);

                if (null != dashItem.getQtde()) {
                    Labels += !Labels.equals("") ? ",'" + dashItem.getNomeConferente() + "'" : "'" + dashItem.getNomeConferente() + "'";
                    TotalValores += !TotalValores.equals("") ? "," + dashItem.getQtdeHora().toPlainString() : dashItem.getQtdeHora().toPlainString();
                    Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";
                }
            }

            this.estatisticaHoraLabel = "[" + Labels + "]";
            this.estatisticaCores3 = "[" + Cores + "]";
            this.estatisticaHora = "[" + TotalValores + "]";
        }

        // Análise por Hora - Valor
        Labels = "";
        Cores = "";
        TotalValores = "";
        dashItem = new TesEntrada();

        if (listaQtdeValor.size() > 0) {
            for (int i = 0; i < listaQtdeValor.size(); i++) {
                dashItem = listaQtdeValor.get(i);
                if (null != dashItem.getQtde()) {
                    Labels += !Labels.equals("") ? ",'" + dashItem.getNomeConferente() + "'" : "'" + dashItem.getNomeConferente() + "'";
                    TotalValores += !TotalValores.equals("") ? "," + dashItem.getValorHora().toPlainString() : dashItem.getValorHora().toPlainString();
                    Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";
                }
            }

            this.estatisticaHoraValorLabel = "[" + Labels + "]";
            this.estatisticaCores4 = "[" + Cores + "]";
            this.estatisticaHoraValor = "[" + TotalValores + "]";
        }
    }

    public void carregarProdutividade() {
        try {
            this.listaProdutividade = tesEntradaDao.relatorioProdutividade(corporativo ? "" : this.codFil, this.data1, this.data2, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private void setFilters() {
        filters.put("codFil", filialSelecionada.getCodfilAc());
        filters.put("data", dataTela);
        filters.put("somentePendentes", apenasPendentes);
        filters.put("guia", "");
        filters.put("cliente1", "");
        filters.put("cliente2", "");

        filtersOS.put(" OS_Vig.codfil = ? ", filialSelecionada.getCodfilAc());
        filtersOS.put(" OS_Vig.codfil in (select filiais.codfil"
                + "                          from saspw"
                + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                + "                          where saspw.codpessoa = ? and paramet.path = '" + banco + "')", codPessoa);
        filtersOS.put(" OS_Vig.OS = ? ", "");
        filtersOS.put(" OS_Vig.Situacao = ? ", "A");
        filtersOS.put(" OS_Vig.DtInicio <= ? ", dataTela);
        filtersOS.put(" OS_Vig.DtFim >= ? ", dataTela);
    }

    private void mudarDia(int incremento) {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, incremento);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            filters.replace("data", dataTela);
            carregarNumerarios();
        } catch (ParseException e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void voltarDia() {
        mudarDia(-1);
    }

    public void avancarDia() {
        mudarDia(1);
    }

    public void selecionarData(SelectEvent data) {
        dataTela = (String) data.getObject();
        filters.replace("data", dataTela);
        carregarNumerarios();
    }

    public void carregarNumerarios() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            lazyTesEntradas = new TesEntradaLazyList(persistencia);
        } catch (Exception e) {
            //logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void ativarModalCadastro() {
        numerarioClick = null;
        PrimeFaces.current().resetInputs("formContrato:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        numerarioEdicao = new TesEntrada();
        //prepararCadastro();
    }

    public void ativarModalEdicao() {
        if (numerarioClick == null) {
            displayInfo("SelecioneNumerario");
            return;
        }
        PrimeFaces.current().resetInputs("formContrato:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        prepararCadastro();
    }

    public void ativarModalPesquisa() {
        PrimeFaces.current().resetInputs("formPesquisa:pesquisar");
        PrimeFaces.current().executeScript("PF('dlgPesquisa').show();");
    }

    public void mostrarApenasPendentes() {
        filters.replace("somentePendentes", apenasPendentes);
        carregarNumerarios();
    }

    private void limparFilters() {
        filters.replace("guia", "");
        filters.replace("cliente1", "");
        filters.replace("cliente2", "");
    }

    public void pesquisar() {
        limparFilters();
        filters.replace(chavePesquisa, valorPesquisa);
        carregarNumerarios();
    }

    public void limparPesquisa() {
        limparFilters();
        carregarNumerarios();
    }

    ////////////////////////////////
    // Cadastro:
    public void prepararCadastro() {
        if (numerarioClick == null) {
            editandoEntrada = false;
            filialSelecionada = filiais.stream().filter(e -> e.getCodfilAc().equals(codFil)).findFirst().get();
            numerarioEdicao = new TesEntrada();
        } else {
            editandoEntrada = true;
            numerarioEdicao = numerarioClick;

            // TESTE
//            clienteSelecionado =
            // volumes
            try {
                cxfGuiasVolLista = tesourariaController.buscarVolumes(filialSelecionada.getCodfilAc(), numerarioEdicao.getGuia().toString(), numerarioEdicao.getSerie());
            } catch (Exception e) {
                logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            }
        }

        buscarCliente("");
    }

    private void esconderModalCadastro() {
        displayInfo("EdicaoSucesso");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
    }

    public void gravarEntrada() {
        try {
            numerarioEdicao.setCodFil(filialSelecionada.getCodfilAc());
            numerarioEdicao.setOS(numerarioEdicao.getOs_vig().getOS());
            numerarioEdicao.setContaTes(numerarioEdicao.getConta().getCodigo());
            numerarioEdicao.setLote(numerarioEdicao.getTesLote().getTipoSrvCodigo());
            numerarioEdicao.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            numerarioEdicao.setHr_Alter(DataAtual.getDataAtual("HORA"));
            numerarioEdicao.setDt_Alter(LocalDate.now().toString());

            // TODO novos
            numerarioEdicao.setOS(os_vigSelecionado.getOS());
            numerarioEdicao.setContaTes(contaSelecionada.getCodigo());
            numerarioEdicao.setLote(loteSelecionado.getCodigo());

            tesEntradaDao.gravarTesEntrada(numerarioEdicao);
            esconderModalCadastro();
            carregarNumerarios();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void analisarSerie() {
        String serie = numerarioEdicao.getSerie();
        if (serie.equals("")) {
            return;
        }

        try {
            boolean existe = tesourariaController.validaSerieGuia(serie);
            if (!existe) {
                displayInfo("SerieNaoCadastrada");
                return;
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            return;
        }

        preencherCampos();
    }

    public void preencherCampos() {
        String codFilNum = filialSelecionada != null ? filialSelecionada.getCodfilAc() : null;
        BigDecimal guiaNum = numerarioEdicao.getGuia();
        String guia = guiaNum != null ? guiaNum.toString() : "";
        String serie = numerarioEdicao.getSerie();
        numerarioEdicao.setValor("0");
        numerarioEdicao.setData("");

        if (codFilNum == null || codFilNum.equals("") || guia.equals("") || serie.equals("")) {
            return;
        }

        try {
            //TesEntrada numerario = tesourariaController.obterTesentrada(codFilNum, guia, serie, codPessoa, banco);
            List<Rt_Guias> guias = guiasSatWeb.dadosGuia(guia, serie, persistencia);

            if (guias.size() > 0) {
                //numerarioEdicao = numerario;
                Rt_Guias guiaDados = guias.get(0);
                TesEntrada numerario = new TesEntrada();

                numerarioEdicao.setCodCli1(guiaDados.getCodCli1());
                numerarioEdicao.setCodCli2(guiaDados.getCodCli2());

                Map<String, String> filtros = new HashMap<>();
                filtros.put("Codigo = ? ", guiaDados.getCodCli1());
                filtros.put("CodFil = ? ", codFilNum);

                numerarioEdicao.setClienteOrigem(clientesSatMobWeb.listagemPaginada(0, 25, filtros, persistencia).get(0));
                clienteSelecionado = numerarioEdicao.getClienteOrigem();

                filtros = new HashMap<>();
                filtros.put("Codigo = ? ", guiaDados.getCodCli2());
                filtros.put("CodFil = ? ", codFilNum);

                numerarioEdicao.setClienteDestino(clientesSatMobWeb.listagemPaginada(0, 25, filtros, persistencia).get(0));
                tesourariaSelecionada = numerarioEdicao.getClienteDestino();

                LocalDate localDate = guiaDados.getDt_Alter();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                String dataFormatada = localDate.format(formatter);

                numerarioEdicao.setData(dataFormatada);

                consultarOS();
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        try {
            cxfGuiasVolLista = guiasSatWeb.listarLacres(guia, serie, persistencia);

            Double Total = 0.0;

            if (cxfGuiasVolLista.size() > 0) {
                for (CxFGuiasVol item : cxfGuiasVolLista) {
                    Total += Double.valueOf(item.getValor().toPlainString());
                }
            }

            numerarioEdicao.setValor(String.valueOf(Total));

        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public List<CxFGuias> buscarGuias(String input) {
        Map<String, String> filtroGuia = new HashMap<String, String>();
        filtroGuia.put(" CxFGuias.CodFil = ? ", filialSelecionada.getCodfilAc());
        filtroGuia.put(" Guia = ? ", input);

        try {
            listaGuias = custodiaController.listagemPaginada(0, 50, filtroGuia, persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return listaGuias;
    }

    ////////////////////////////////////////////////////////////////////////////
    // Clientes:
    public List<Clientes> buscarCliente(String input) {
        Map<String, String> filtros = new HashMap<>();
        filtros.put("NRed LIKE ? ", "%" + input + "%");

        try {
            listaClientes = clientesSatMobWeb.listagemPaginada(0, 25, filtros, persistencia);
        } catch (Exception e) {
            displayError("TODO: nao foi possiel obter resultados...");
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return listaClientes;
    }

    public void selecionarCliente() {
        numerarioEdicao.setCodCli1(clienteSelecionado.getCodCli());
        numerarioEdicao.setClienteOrigem(clienteSelecionado);

        consultarOS();
    }

    ////////////////////////////////////////////////////////////////////////////
    // Tesouraria Destino:
    public List<Clientes> buscarTesouraria(String input) {
        Map<String, String> filtros = new HashMap<>();
        filtros.put("NRed   LIKE ? ", "%" + input + "%");
        filtros.put("Codigo LIKE ? ", "9997%");
        filtros.put("Situacao = ?", "A");
        filtros.put("CodFil = ?", filialSelecionada.getCodfilAc());

        try {
            listaClientes = clientesSatMobWeb.listagemPaginada(0, 25, filtros, persistencia);
        } catch (Exception e) {
            displayError("TODO: nao foi possiel obter resultados...");
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return listaClientes;
    }

    public void selecionarTesouraria() {
        numerarioEdicao.setCodCli2(tesourariaSelecionada.getCodCli());
        numerarioEdicao.setClienteDestino(tesourariaSelecionada);

        consultarOS();
    }

    public void consultarOS() {
        os_vigSelecionado = null;

        if (null != tesourariaSelecionada
                && null != clienteSelecionado
                && null != tesourariaSelecionada.getCodCli()
                && null != clienteSelecionado.getCodCli()) {
            try {
                String CodigoCaixaforte = "";

                for (CxForte cxForteLista1 : this.cxForteLista) {
                    CodigoCaixaforte = cxForteLista1.getCodCli();
                    break;
                }

                os_vigSelecionado = this.pedidosSatMobWeb.buscarOS_Pedido(clienteSelecionado.getCodigo(),
                        tesourariaSelecionada.getCodigo(),
                        filialSelecionada.getCodfilAc(),
                        CodigoCaixaforte,
                        this.persistencia);

                if (os_vigSelecionado == null) {
                    numerarioEdicao.setOs_vig(new OS_Vig());
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("NaoExisteOS"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                } else {
                    numerarioEdicao.setOs_vig(os_vigSelecionado);
                }
            } catch (Exception e) {
                numerarioEdicao.setOs_vig(new OS_Vig());

                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                this.log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(this.log, this.caminho);
            }
        } else {
            numerarioEdicao.setOs_vig(new OS_Vig());
        }
    }

    public List<OS_Vig> getListaOS() {
        return listaOS;
    }

    ////////////////////////////////////////////////////////////////////////////
    // OS:
    public List<OS_Vig> buscarListaOS(String input) {
        filtersOS.replace(" OS_Vig.codfil = ? ", filialSelecionada.getCodfilAc());
        filtersOS.replace(" OS_Vig.codfil in (select filiais.codfil"
                + "    from saspw"
                + "    inner join saspwfil on saspwfil.nome = saspw.nome"
                + "    inner join filiais on filiais.codfil = saspwfil.codfilac"
                + "    inner join paramet on paramet.filial_pdr = filiais.codfil"
                + "    where saspw.codpessoa = ? and paramet.path = '" + banco + "')", codPessoa);
        filtersOS.replace(" OS_Vig.OS = ? ", input == null ? "" : input);

        try {
            listaOS = os_VigSatMobWeb.listaPaginada(0, 20, filtersOS, persistencia);
        } catch (Exception e) {
            displayError(e.getMessage());
            listaOS = new ArrayList<>();

        }
        return listaOS;
    }

    public void selecionarOS() {
        numerarioEdicao.setOs_vig(os_vigSelecionado);
        PrimeFaces.current().executeScript("PF('dlgModalOS').hide();");
    }

    // Contas:
    public List<TesConta> buscarListaContas() {
        try {
            listaContas = tesEntradaDao.getTesConta();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return listaContas;
    }

    public void buscarConta(String input) {
        try {
            TesConta conta = tesourariaController.getTesContaById(input);
            if (conta != null) {
                contaSelecionada = conta;
            } else {
                contaSelecionada = new TesConta();
                displayWarn("TODO: Nao foi possível achar Conta");
            }
            numerarioEdicao.setConta(contaSelecionada);
        } catch (Exception e) {
            displayError("TODO erro database");
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarConta() {
        numerarioEdicao.setConta(contaSelecionada);
        PrimeFaces.current().executeScript("PF('dlgTesConta').hide();");
    }

    public void limparFiltrosConta() {
        buscarListaContas();
    }

    // Lotes:
    public List<TesConta> buscarListaLotes() {
        try {
            listaLotes = tesEntradaDao.allTiposrvcli();
            numerarioEdicao.setTesLote(new TesConta());
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return listaLotes;
    }

    public void selecionarLote() {
        numerarioEdicao.setTesLote(loteSelecionado);
        PrimeFaces.current().executeScript("PF('dlgTesLote').hide();");
    }

    public void limparFiltrosLotes() {
        buscarListaLotes();
    }

    ////////////////////////////////////////////////////////////////////////////
    // getters & setters:
    public LazyDataModel<TesEntrada> getLazyTesEntradas() {
        return lazyTesEntradas;
    }

    public void setLazyTesEntradas(LazyDataModel<TesEntrada> lazyTesEntradas) {
        this.lazyTesEntradas = lazyTesEntradas;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFilialTela() {
        return filialTela;
    }

    public TesEntrada getNumerarioClick() {
        return numerarioClick;
    }

    public void setNumerarioClick(TesEntrada numerarioClick) {
        this.numerarioClick = numerarioClick;
    }

    public boolean isApenasPendentes() {
        return apenasPendentes;
    }

    public void setApenasPendentes(boolean apenasPendentes) {
        this.apenasPendentes = apenasPendentes;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public TesEntrada getNumerarioEdicao() {
        return numerarioEdicao;
    }

    public void setNumerarioEdicao(TesEntrada numerarioEdicao) {
        this.numerarioEdicao = numerarioEdicao;
    }

    public boolean isEditandoEntrada() {
        return editandoEntrada;
    }

    public String getCodFilEdicao() {
        return codFilEdicao;
    }

    public void setCodFilEdicao(String codFilEdicao) {
        this.codFilEdicao = codFilEdicao;
    }

    public List<CxFGuias> getListaGuias() {
        return listaGuias;
    }

    public void setListaGuias(List<CxFGuias> listaGuias) {
        this.listaGuias = listaGuias;
    }

    public CxFGuias getGuiaEdicao() {
        return guiaEdicao;
    }

    public void setGuiaEdicao(CxFGuias guiaEdicao) {
        this.guiaEdicao = guiaEdicao;
    }

    public List<TesConta> getListaContas() {
        return listaContas;
    }

    public void setListaContas(List<TesConta> listaContas) {
        this.listaContas = listaContas;
    }

    public SasPWFill getFilialSelecionada() {
        return filialSelecionada;
    }

    public void setFilialSelecionada(SasPWFill filialSelecionada) {
        this.filialSelecionada = filialSelecionada;
    }

    public List<CxFGuiasVol> getCxfGuiasVolLista() {
        return cxfGuiasVolLista;
    }

    public OS_Vig getOs_vigSelecionado() {
        return os_vigSelecionado;
    }

    public void setOs_vigSelecionado(OS_Vig os_vigSelecionado) {
        this.os_vigSelecionado = os_vigSelecionado;
    }

    public TesConta getContaSelecionada() {
        return contaSelecionada;
    }

    public void setContaSelecionada(TesConta contaSelecionada) {
        this.contaSelecionada = contaSelecionada;
    }

    public TesConta getLoteSelecionado() {
        return loteSelecionado;
    }

    public void setLoteSelecionado(TesConta loteSelecionado) {
        this.loteSelecionado = loteSelecionado;
    }

    public List<TesConta> getListaLotes() {
        return listaLotes;
    }

    public void setListaLotes(List<TesConta> listaLotes) {
        this.listaLotes = listaLotes;
    }

    public List<Clientes> getListaClientes() {
        return listaClientes;
    }

    public Clientes getClienteSelecionado() {
        return clienteSelecionado;
    }

    public void setClienteSelecionado(Clientes clienteSelecionado) {
        this.clienteSelecionado = clienteSelecionado;
    }

    public Clientes getTesourariaSelecionada() {
        return tesourariaSelecionada;
    }

    public void setTesourariaSelecionada(Clientes tesourariaSelecionada) {
        this.tesourariaSelecionada = tesourariaSelecionada;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public List<Clientes> getListaTesouraria() {
        return listaTesouraria;
    }

    public void setListaTesouraria(List<Clientes> listaTesouraria) {
        this.listaTesouraria = listaTesouraria;
    }

    public List<Filiais> getListaFiliais() {
        return listaFiliais;
    }

    public void setListaFiliais(List<Filiais> listaFiliais) {
        this.listaFiliais = listaFiliais;
    }

    public String getCodTesouraria() {
        return codTesouraria;
    }

    public void setCodTesouraria(String codTesouraria) {
        this.codTesouraria = codTesouraria;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public List<Funcion> getListaFuncionarios() {
        return listaFuncionarios;
    }

    public void setListaFuncionarios(List<Funcion> listaFuncionarios) {
        this.listaFuncionarios = listaFuncionarios;
    }

    public BigDecimal getFuncionarioSelecionado() {
        return funcionarioSelecionado;
    }

    public void setFuncionarioSelecionado(BigDecimal funcionarioSelecionado) {
        this.funcionarioSelecionado = funcionarioSelecionado;
    }

    public String getEstatisticaQtdeLabel() {
        return estatisticaQtdeLabel;
    }

    public void setEstatisticaQtdeLabel(String estatisticaQtdeLabel) {
        this.estatisticaQtdeLabel = estatisticaQtdeLabel;
    }

    public String getEstatisticaQtde() {
        return estatisticaQtde;
    }

    public void setEstatisticaQtde(String estatisticaQtde) {
        this.estatisticaQtde = estatisticaQtde;
    }

    public String getEstatisticaValorLabel() {
        return estatisticaValorLabel;
    }

    public void setEstatisticaValorLabel(String estatisticaValorLabel) {
        this.estatisticaValorLabel = estatisticaValorLabel;
    }

    public String getEstatisticaValor() {
        return estatisticaValor;
    }

    public void setEstatisticaValor(String estatisticaValor) {
        this.estatisticaValor = estatisticaValor;
    }

    public String getEstatisticaHoraLabel() {
        return estatisticaHoraLabel;
    }

    public void setEstatisticaHoraLabel(String estatisticaHoraLabel) {
        this.estatisticaHoraLabel = estatisticaHoraLabel;
    }

    public String getEstatisticaHora() {
        return estatisticaHora;
    }

    public void setEstatisticaHora(String estatisticaHora) {
        this.estatisticaHora = estatisticaHora;
    }

    public String getEstatisticaCores1() {
        return estatisticaCores1;
    }

    public void setEstatisticaCores1(String estatisticaCores1) {
        this.estatisticaCores1 = estatisticaCores1;
    }

    public String getEstatisticaCores2() {
        return estatisticaCores2;
    }

    public void setEstatisticaCores2(String estatisticaCores2) {
        this.estatisticaCores2 = estatisticaCores2;
    }

    public String getEstatisticaCores3() {
        return estatisticaCores3;
    }

    public void setEstatisticaCores3(String estatisticaCores3) {
        this.estatisticaCores3 = estatisticaCores3;
    }

    public String getEstatisticaHoraValorLabel() {
        return estatisticaHoraValorLabel;
    }

    public void setEstatisticaHoraValorLabel(String estatisticaHoraValorLabel) {
        this.estatisticaHoraValorLabel = estatisticaHoraValorLabel;
    }

    public String getEstatisticaHoraValor() {
        return estatisticaHoraValor;
    }

    public void setEstatisticaHoraValor(String estatisticaHoraValor) {
        this.estatisticaHoraValor = estatisticaHoraValor;
    }

    public String getEstatisticaCores4() {
        return estatisticaCores4;
    }

    public void setEstatisticaCores4(String estatisticaCores4) {
        this.estatisticaCores4 = estatisticaCores4;
    }

    public String getDashResumoTotalQtde() {
        return dashResumoTotalQtde;
    }

    public void setDashResumoTotalQtde(String dashResumoTotalQtde) {
        this.dashResumoTotalQtde = dashResumoTotalQtde;
    }

    public String getDashResumoTotalHoras() {
        return dashResumoTotalHoras;
    }

    public void setDashResumoTotalHoras(String dashResumoTotalHoras) {
        this.dashResumoTotalHoras = dashResumoTotalHoras;
    }

    public String getDashResumoTotalConferentes() {
        return dashResumoTotalConferentes;
    }

    public void setDashResumoTotalConferentes(String dashResumoTotalConferentes) {
        this.dashResumoTotalConferentes = dashResumoTotalConferentes;
    }

    public String getDashResumoTotalValor() {
        return dashResumoTotalValor;
    }

    public void setDashResumoTotalValor(String dashResumoTotalValor) {
        this.dashResumoTotalValor = dashResumoTotalValor;
    }

    public String getEstatisticaDiaQtdeLabel() {
        return estatisticaDiaQtdeLabel;
    }

    public void setEstatisticaDiaQtdeLabel(String estatisticaDiaQtdeLabel) {
        this.estatisticaDiaQtdeLabel = estatisticaDiaQtdeLabel;
    }

    public String getEstatisticaDiaQtde() {
        return estatisticaDiaQtde;
    }

    public void setEstatisticaDiaQtde(String estatisticaDiaQtde) {
        this.estatisticaDiaQtde = estatisticaDiaQtde;
    }

    public String getEstatisticaDiaValorLabel() {
        return estatisticaDiaValorLabel;
    }

    public void setEstatisticaDiaValorLabel(String estatisticaDiaValorLabel) {
        this.estatisticaDiaValorLabel = estatisticaDiaValorLabel;
    }

    public String getEstatisticaDiaValor() {
        return estatisticaDiaValor;
    }

    public void setEstatisticaDiaValor(String estatisticaDiaValor) {
        this.estatisticaDiaValor = estatisticaDiaValor;
    }

    public List<TesEntrada> getListaDashGride() {
        return listaDashGride;
    }

    public void setListaDashGride(List<TesEntrada> listaDashGride) {
        this.listaDashGride = listaDashGride;
    }

    public List<TesEntrada> getListaProdutividade() {
        return listaProdutividade;
    }

    public void setListaProdutividade(List<TesEntrada> listaProdutividade) {
        this.listaProdutividade = listaProdutividade;
    }

    public boolean isCorporativo() {
        return corporativo;
    }

    public void setCorporativo(boolean corporativo) {
        this.corporativo = corporativo;
    }

}

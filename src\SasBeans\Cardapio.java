/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Cardapio {

    private String Codigo;
    private String Descricao;
    private String Especificacao;
    private String Operador;
    private String Dt_alter;
    private String Hr_Alter;

    public Cardapio() {
        this.Codigo = "";
        this.Descricao = "";
        this.Especificacao = "";
        this.Operador = "";
        this.Dt_alter = "";
        this.Hr_Alter = "";
    }

    @Override
    public String toString() {
        return Codigo
                + ";" + Descricao
                + ";" + Especificacao
                + ";" + Operador
                + ";" + Dt_alter
                + ";" + Hr_Alter;
    }

    @Override
    public int hashCode() {
        return (int) java.lang.Math.floor(1000 * new Double(this.Codigo));
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Cardapio other = (Cardapio) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getEspecificacao() {
        return Especificacao;
    }

    public void setEspecificacao(String Especificacao) {
        this.Especificacao = Especificacao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

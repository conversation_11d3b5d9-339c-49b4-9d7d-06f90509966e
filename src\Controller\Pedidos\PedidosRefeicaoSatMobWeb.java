/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Pedidos;

import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasDaos.FiliaisDao;
import SasDaos.OS_VigDao;
import SasDaos.PedidoDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PedidosRefeicaoSatMobWeb {

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pedido> listagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<Pedido> retorno;
            PedidoDao pedidoDao = new PedidoDao();
            retorno = pedidoDao.listapaginada(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pedidos.alhageral<message>" + e.getMessage());
        }
    }

    public List<Pedido> listagemPaginadaSemFlag(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<Pedido> retorno;
            PedidoDao pedidoDao = new PedidoDao();
            retorno = pedidoDao.listapaginadaSemFlag(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pedidos.alhageral<message>" + e.getMessage());
        }
    }

    public List<Pedido> listagemPedidosRefeicao(String CodCli, String CodFil, String CodPedido, Persistencia persistencia) throws Exception {
        try {
            List<Pedido> retorno;
            PedidoDao pedidoDao = new PedidoDao();
            retorno = pedidoDao.listagemPedidosRefeicao(CodCli, CodFil, CodPedido, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pedidos.alhageral<message>" + e.getMessage());
        }
    }

    public Integer contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            PedidoDao pedidoDao = new PedidoDao();
            retorno = pedidoDao.totalPedidosMobWeb(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pedidos.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemSemFlag(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            PedidoDao pedidoDao = new PedidoDao();
            retorno = pedidoDao.totalPedidosMobWebSemFlag(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("Pedidos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            pedidoDao.salvarPedido(pedido, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirPedidoRefeicao(Pedido pedido, List<Pedido> pedidoItens, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            pedidoDao.salvarPedidoItens(pedido, pedidoItens, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void editarPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            pedidoDao.editarPedido(pedido, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<OS_Vig> buscarPedidos(String query, boolean tipo, String codigo, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_vigDao = new OS_VigDao();
            return os_vigDao.listarPedido(query, tipo, codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSatWeb.falhageral<message>" + e.getMessage());
        }
    }
}

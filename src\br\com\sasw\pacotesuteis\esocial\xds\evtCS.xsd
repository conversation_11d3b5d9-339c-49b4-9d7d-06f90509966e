﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtCS/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtCS/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtCS">
                    <xs:annotation>
                        <xs:documentation>Evento Contribuicoes Sociais</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do evento de retorno</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="indApuracao">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="perApur">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoCS">
                                <xs:annotation>
                                    <xs:documentation>Informacoes relativas às Contribuicoes Sociais</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="nrRecArqBase">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Recibo do arquivo de origem</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="40"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="indExistInfo">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Existe Informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="infoCPSeg" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes de contribuicao previdenciaria do Segurado</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="vrDescCP">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Valor da Contribuicao Descontada</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:decimal">
                                                                <xs:totalDigits value="14"/>
                                                                <xs:fractionDigits value="2"/>
                                                                <xs:maxInclusive value="999999999999"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="vrCpSeg">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Valor da contribuicao do segurado calculada</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:decimal">
                                                                <xs:totalDigits value="14"/>
                                                                <xs:fractionDigits value="2"/>
                                                                <xs:maxInclusive value="999999999999"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoContrib">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes gerais do contribuinte</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="classTrib">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Informar enquadramento do contribuinte, conforme tabela 8</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:length value="2"/>
                                                                <xs:pattern value="\d{2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="infoPJ" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes exclusivas da PJ</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="indCoop" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo de Cooperativa</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="indConstr">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo de Construtora</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="indSubstPatr" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Substituicao da CP patronal</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="percRedContrib" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Percentual de Reducao da Contribuicao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="5"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="100"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infoAtConc" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes de Atividades Concomitantes</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="fatorMes">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Fator a ser utilizado para calculo da contribuicao incidente sobre a remuneracao mensal</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="5"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="100"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="fator13">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Fator a ser utilizado para calculo da contribuicao incidente sobre a remuneracao relativa ao décimo terceiro salario</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="5"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="100"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="ideEstab" minOccurs="0" maxOccurs="unbounded">
                                            <xs:annotation>
                                                <xs:documentation>Identificacao do estabelecimento/obra</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpInsc">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrInsc">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Número de Inscricao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{8,14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="infoEstab" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do estabelecimento</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="cnaePrep">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo do CNAE</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="aliqRat">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Aliquota RAT</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="fap">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>FAP</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="5"/>
                                                                            <xs:fractionDigits value="4"/>
                                                                            <xs:minInclusive value="0.5"/>
                                                                            <xs:maxInclusive value="2"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="aliqRatAjust">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Aliquota do RAT apOs ajuste pelo FAP</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="5"/>
                                                                            <xs:fractionDigits value="4"/>
                                                                            <xs:minInclusive value="0.5"/>
                                                                            <xs:maxInclusive value="6"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infoComplObra" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes complementares relativas a obras</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="indSubstPatrObra">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Substituicao da CP patronal de Obra</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="ideLotacao" minOccurs="0" maxOccurs="unbounded">
                                                        <xs:annotation>
                                                            <xs:documentation>Identificacao da lotacao tributaria</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="codLotacao">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo da Lotacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="fpas">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo do FPAS</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codTercs">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo de convenios com terceiros, conforme tabela 4</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="4"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codTercsSusp" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo de Terceiros Suspenso</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="4"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infoTercSusp" minOccurs="0" maxOccurs="15">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes de suspensao de contribuicao a Terceiros</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="codTerc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo de Terceiro</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:length value="4"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="infoEmprParcial" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacao complementar de obra de construcao civil</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpInscContrat">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de Inscricao do contratante</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nrInscContrat">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Número de Inscricao do Contratante</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="\d{8,14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="tpInscProp">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de Inscricao do proprietario do CNO</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nrInscProp">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Número de Inscricao do proprietario do CNO</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="\d{8,14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="dadosOpPort" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes relativas ao operador portuario</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="cnpjOpPortuario">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>CNPJ do Operador Portuario</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:length value="14"/>
                                                                                        <xs:pattern value="\d{14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="aliqRat">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Aliquota RAT</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:integer">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="fap">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>FAP</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="5"/>
                                                                                        <xs:fractionDigits value="4"/>
                                                                                        <xs:minInclusive value="0.5"/>
                                                                                        <xs:maxInclusive value="2"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="aliqRatAjust">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Aliquota do RAT apOs ajuste pelo FAP</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="5"/>
                                                                                        <xs:fractionDigits value="4"/>
                                                                                        <xs:minInclusive value="0.5"/>
                                                                                        <xs:maxInclusive value="6"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="basesRemun" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Bases de calculo por categoria</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="indIncid">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de Incidencia</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="codCateg">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo da Categoria do Trabalhador</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:integer">
                                                                                        <xs:pattern value="\d{3}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="basesCp">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Bases, contribuicoes do segurado e deducoes da CP</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="vrBcCp00">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Base de Calculo da Contribuicao Previdenciaria</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrBcCp15">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>BC CP 15</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrBcCp20">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>BC CP 20</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrBcCp25">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>BC CP 25</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrSuspBcCp00">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Valor da BC com incidencia suspensa em decorrencia de processo</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrSuspBcCp15">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Valor CP Suspensa 15 anos</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrSuspBcCp20">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>BC 20 anos, suspensa</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrSuspBcCp25">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Vr CP suspensa 25 anos</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrDescSest">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Desconto Sest</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrCalcSest">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Calc Sest</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrDescSenat">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Desc Senat</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrCalcSenat">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Calculo Senat</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrSalFam">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Vr salario familia total por categoria</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrSalMat">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Sal. maternidade</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="basesAvNPort" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Contratacao de avulsos nao portuarios</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="vrBcCp00">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Base de Calculo da Contribuicao Previdenciaria</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrBcCp15">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>BC CP 15</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrBcCp20">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>BC CP 20</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrBcCp25">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>BC CP 25</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrBcCp13">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>BC CP 13° sal</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrBcFgts">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>BC FGTS</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrDescCP">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor da Contribuicao Descontada</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="infoSubstPatrOpPort" minOccurs="0" maxOccurs="999">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Inf de substituicao prevista na Lei 12546/2011</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="cnpjOpPortuario">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>CNPJ do Operador Portuario</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:length value="14"/>
                                                                                        <xs:pattern value="\d{14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="basesAquis" minOccurs="0" maxOccurs="6">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes sobre aquisicao rural</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="indAquis">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo da Aquisicao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vlrAquis">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor total da aquisicao de producao rural de produtor rural pessoa fisica</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrCPDescPR">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Vr da Contrib. Previd. descontada pelo adquirente</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrCPNRet">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da Contrib. Previd. nao retida em decorrencia de processo</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrRatNRet">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da GILRAT incidente sobre a aquisicao de producao rural de produtor rural pessoa fisica, cuja retencao deixou de ser efetuada em decorrencia de decisao/sentenca judicial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrSenarNRet">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da contribuicao destinada ao SENAR, incidente sobre a aquisicao de producao rural de produtor rural pessoa fisica/segurado especial, e que deixou de ser retida em decorrencia de decisao/sentenca judicial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrCPCalcPR">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor calculado na contribuicao previdenciaria do produtor rural</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrRatDescPR">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da GILRAT incidente sobre a aquisicao de producao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrRatCalcPR">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor calculado do RAT devido pelo Produtor Rural</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrSenarDesc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da contribuicao destinada ao Senar</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrSenarCalc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor calculado da contribuicao devida pelo produtor rural ao Senar</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="basesComerc" minOccurs="0" maxOccurs="5">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes da Comercializacao da producao</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="indComerc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo de Comercializacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrBcComPR">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>BC CP PR</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrCPSusp" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da Contrib. Previd. com exigibilidade suspensa</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrRatSusp" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da contribuicao para Gilrat com exigibilidade suspensa</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrSenarSusp" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da contribuicao para o Senar com exigibilidade suspensa</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="infoCREstab" minOccurs="0" maxOccurs="99">
                                                        <xs:annotation>
                                                            <xs:documentation>COdigos de Receita por Estabelecimento</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpCR">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo de Receita - CR</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrCR">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor relativo ao crédito tributario - CT apurado</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrSuspCR" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Vr Suspenso relativo ao CR</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoCRContrib" minOccurs="0" maxOccurs="99">
                                            <xs:annotation>
                                                <xs:documentation>Totalizador dos CT do contribuinte</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpCR">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo de Receita - CR</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:integer">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="vrCR">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Valor relativo ao crédito tributario - CT apurado</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:decimal">
                                                                <xs:totalDigits value="14"/>
                                                                <xs:fractionDigits value="2"/>
                                                                <xs:maxInclusive value="999999999999"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="vrCRSusp" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Valor do tributo com exigibilidade suspensa</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:decimal">
                                                                <xs:totalDigits value="14"/>
                                                                <xs:fractionDigits value="2"/>
                                                                <xs:maxInclusive value="999999999999"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

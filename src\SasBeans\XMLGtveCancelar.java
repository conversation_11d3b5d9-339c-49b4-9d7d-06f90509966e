/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class XMLGtveCancelar {

    public String xmlns;
    public String versao;
    public XMLGtveCancelarinfEvento infEvento;
    public XMLGtveRetDadosAdic Dados_adic;

    public XMLGtveCancelar() {
        xmlns = "http://www.portalfiscal.inf.br/cte";
        versao = "3.00";
        infEvento = new XMLGtveCancelarinfEvento();
        Dados_adic = new XMLGtveRetDadosAdic();
    }

    public void setXmlns(String xmlns) {
        this.xmlns = xmlns;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }
    
    public void setDados_adic(XMLGtveRetDadosAdic Dados_adic) {
        this.Dados_adic = Dados_adic;
    }

    public void setInfEvento(XMLGtveCancelarinfEvento infEvento) {
        this.infEvento = infEvento;
    }
}

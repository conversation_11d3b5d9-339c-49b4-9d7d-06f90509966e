/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.Siates.SiatesSatWeb;
import Dados.Persistencia;
import SasBeans.Siates;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class SiatesLazyList extends LazyDataModel<Siates> {

    private static final long serialVersionUID = 1L;
    private List<Siates> chamados;
    private Siates chamadoAux;
    private final SiatesSatWeb siatesweb;
    private Persistencia persistencia;

    public SiatesLazyList(SiatesSatWeb siatesweb, Persistencia persistencia) {
        this.siatesweb = siatesweb;
        this.persistencia = persistencia;
    }

    @Override
    public List<Siates> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.chamados = this.siatesweb.listaPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.siatesweb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.chamados;
    }

    @Override
    public Object getRowKey(Siates chamado) {
        return chamado.getIDChamado();
    }

    @Override
    public Siates getRowData(String IDChamado) {
        try {
            chamadoAux = new Siates();
            chamadoAux.setIDChamado(IDChamado);
            return this.chamados.get(this.chamados.indexOf(chamadoAux));
        } catch (Exception e) {
            return null;
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class HT_NF {

    private String CodFil;
    private String Codigo;
    private String Descricao;
    private String CFOP;
    private String CFOPDescr;
    private String Mensagem;
    private String Obs1;
    private String Obs2;
    private String Cod_Conta;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getCFOP() {
        return CFOP;
    }

    public void setCFOP(String CFOP) {
        this.CFOP = CFOP;
    }

    public String getCFOPDescr() {
        return CFOPDescr;
    }

    public void setCFOPDescr(String CFOPDescr) {
        this.CFOPDescr = CFOPDescr;
    }

    public String getMensagem() {
        return Mensagem;
    }

    public void setMensagem(String Mensagem) {
        this.Mensagem = Mensagem;
    }

    public String getObs1() {
        return Obs1;
    }

    public void setObs1(String Obs1) {
        this.Obs1 = Obs1;
    }

    public String getObs2() {
        return Obs2;
    }

    public void setObs2(String Obs2) {
        this.Obs2 = Obs2;
    }

    public String getCod_Conta() {
        return Cod_Conta;
    }

    public void setCod_Conta(String Cod_Conta) {
        this.Cod_Conta = Cod_Conta;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

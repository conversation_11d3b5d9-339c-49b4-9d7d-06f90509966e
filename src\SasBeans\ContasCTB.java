package SasBeans;

import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ContasCTB {

    private String codigo;
    private String descricao;
    private Integer nivel;
    private String contaRef;
    private String operador;
    private LocalDate dt_alter;
    private String hr_alter;

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getNivel() {
        return nivel;
    }

    public void setNivel(Integer nivel) {
        this.nivel = nivel;
    }

    public String getContaRef() {
        return contaRef;
    }

    public void setContaRef(String contaRef) {
        this.contaRef = contaRef;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public LocalDate getDt_alter() {
        return dt_alter;
    }

    public void setDt_alter(LocalDate dt_alter) {
        this.dt_alter = dt_alter;
    }

    public String getHr_alter() {
        return hr_alter;
    }

    public void setHr_alter(String hr_alter) {
        this.hr_alter = hr_alter;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.codigo);
        hash = 97 * hash + Objects.hashCode(this.descricao);
        hash = 97 * hash + Objects.hashCode(this.nivel);
        hash = 97 * hash + Objects.hashCode(this.contaRef);
        hash = 97 * hash + Objects.hashCode(this.operador);
        hash = 97 * hash + Objects.hashCode(this.dt_alter);
        hash = 97 * hash + Objects.hashCode(this.hr_alter);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ContasCTB other = (ContasCTB) obj;
        if (!Objects.equals(this.codigo, other.codigo)) {
            return false;
        }
        return true;
    }

}

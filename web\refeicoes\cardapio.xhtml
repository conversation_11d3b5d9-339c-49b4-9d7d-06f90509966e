<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:p="http://primefaces.org/ui"
      xmlns:cadastros="http://xmlns.jcp.org/jsf/composite/cadastros"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/pessoas.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -1px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid thead tr td:nth-child(1){
                        min-width:70px;
                        width:70px;
                        max-width:70px;
                    }
                    
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid thead tr td:nth-child(6){
                        min-width:100px;
                        width:100px;
                        max-width:100px;
                    }
                    
                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid thead tr td:nth-child(5){
                        min-width:150px;
                        width:150px;
                        max-width:150px;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formPesquisaRapida .ui-radiobutton {
                    background: transparent !important;
                }
               
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{cardapio.Persistencias(login.pp)}"/>
                <f:viewAction action="#{cardapio.allCardapiosCadastro()}"/>
                <f:viewAction action="#{pessoa.Persistencias(login.pp, login.satellite)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <cadastros:header
                    id="cabecalho"
                    titulo="#{localemsgs.Cardapios}"
                    data="#{cardapio.dataTela}"
                    descricao="#{cardapio.filiais.descricao}"
                    endereco="#{cardapio.filiais.endereco}"
                    bairro="#{cardapio.filiais.bairro}"
                    cidade="#{cardapio.filiais.cidade}"
                    imagem="../assets/img/icone_satmob_cardapio1.png"
                    UF="#{cardapio.filiais.UF}"
                    />

                <h:form id="main">
                    <p:hotkey bind="shift+x" oncomplete="PF('dlgExportar').show();" actionListener="#{exportarMB.setTitulo(localemsgs.Pessoas)}"/>
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <!--<p:panel style="overflow:hidden !important; max-width:100% !important; min-height:100% !important; height:100% !important;max-height:100% !important; position:relative;">-->
                                <p:panel style="display: inline">
                                    <p:dataTable
                                        id="tabela"
                                        value="#{cardapio.allCardapiosCadastradosPaginado}"
                                        paginator="true"
                                        rows="50"
                                        lazy="true"
                                        reflow="true"
                                        rowsPerPageTemplate="50"
                                        currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Cardapios}"
                                        paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                        var="lista"
                                        selectionMode="single"
                                        styleClass="tabela"
                                        selection="#{cardapio.cardapioCadastroSelecionado}"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        class="tabela DataGrid"
                                        scrollHeight="100%"
                                        rowKey="#{lista.codigo}"
                                        style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                        >
                                        <p:ajax event="rowDblselect" listener="#{cardapio.dblSelectCadastro}" update="formCadastrar msgs"/>
                                        <p:column headerText="#{localemsgs.Codigo}" class="text-center">
                                            <h:outputText value="#{lista.codigo}" class="text-center" converter="conversor0" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.descricao}" class="text-center">
                                            <h:outputText value="#{lista.descricao}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Especificacao}" class="text-center">
                                            <h:outputText value="#{lista.especificacao}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                            <h:outputText value="#{lista.operador}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                            <h:outputText value="#{lista.dt_alter}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                            <h:outputText value="#{lista.hr_Alter}" converter="conversorHora"/>
                                        </p:column>
                                    </p:dataTable>
                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 110px !important; background: transparent; height:200px !important;" id="botoes">
                        <p:remoteCommand name="execExclusao" partialSubmit="true" 
                                process="@this" 
                                update="msgs main" 
                                actionListener="#{cardapio.excluirCardapio()}" />  
                        
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{cardapio.preCadastroCardapio()}"
                                           update="formCadastrar:cadastrar formCadastrar cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{cardapio.buttonActionCadastro}"
                                           update="msgs formCadastrar:cadastrar cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{cardapio.buttonActionExcluir}"
                                           update="msgs formCadastrar:cadastrar cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisaRapida').show();"
                                           actionListener="#{cardapio.prePesquisaCadastro}" update="formPesquisaRapida:panelPesquisaRapida cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{cardapio.limparFiltrosCadastro}"
                                           update="msgs main:tabela cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>

                </h:form>

                <!--Cadastrar novo-->
                <h:form class="form-inline" id="formCadastrar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute"  focus="descricao" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formCadastrar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_cardapio1.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Cardapio}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; padding-right:0px !important; padding-left:8px !important; margin-top:10px !important" class="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="descricao" value="#{localemsgs.descricao}: "/>
                                <p:inputText id="descricao" value="#{cardapio.cardapioVazio.descricao}" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.descricao}"
                                             label="#{localemsgs.descricao}" style="width: 100%"
                                             maxlength="255">
                                    <p:watermark for="descricao" value="#{localemsgs.descricao}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="especificacao" value="#{localemsgs.Especificacao}: "/>
                                <p:inputText id="especificacao" value="#{cardapio.cardapioVazio.especificacao}" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Especificacao}"
                                             label="#{localemsgs.Especificacao}" style="width: 100%"
                                             maxlength="255">
                                    <p:watermark for="especificacao" value="#{localemsgs.Especificacao}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="margin-top:6px; padding-left:0px !important">
                                <p:commandLink title="#{localemsgs.Salve}" id="cadastrarCardapio" action="#{cardapio.salvarCadastroCardapio()}"
                                               update="main formCadastrar:cadastrar msgs"
                                               style="width:100%">
                                    <label class="btn btn-lg btn-success" style="width:100% !important;margin-left: 0px;"><i class="fa fa-save"></i>&nbsp;#{localemsgs.Salve}</label>
                                </p:commandLink>
                            </p:panelGrid>

                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Pesquisar rápida -->
                <h:form id="formPesquisaRapida">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_cardapio1.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarCardapio}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="panelPesquisaRapida" style="background: transparent;margin-top:0px !important;">

                            <div style="display: flex; flex-direction: row; align-items: center; margin-top:0px !important;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{cardapio.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.descricao}" itemValue="D" />
                                        <f:selectItem itemLabel="#{localemsgs.Especificacao}" itemValue="E" />
                                        <f:selectItem itemLabel="#{localemsgs.Codigo}" itemValue="C" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'D'}" value="#{localemsgs.descricao}: "/>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'E'}" value="#{localemsgs.Especificacao}: "/>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'C'}" value="#{localemsgs.Codigo}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{cardapio.valorPesquisa}"
                                            required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Especificacao}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div style="top:15px !important">
                                <p:commandLink id="botaoPesquisaRapida"
                                               action="#{cardapio.pesquisaRapidaCadastro()}"
                                               update=" :main:tabela :msgs cabecalho"
                                               title="#{localemsgs.Pesquisar}" style="margin-top:10px !important">
                                    <label class="btn btn-lg btn-primary" style="width:100% !important;margin-left: 0px; margin-top:10px !important"><i class="fa fa-search"></i>&nbsp;#{localemsgs.Pesquisar}</label>
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                </script>

            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class GTVeAcesso {
    private String Chave;
    private String Parametro;
    private String CodFil;
    private String CodCli;
    private String CodPessoa;
    private String Oper_Inc;
    private String Dt_Inc;
    private String Hr_Inc;
    private String Dt_Valid;
    private String Flag_excl;
    private String Oper_Excl;
    private String Dt_Excl;
    private String Hr_Excl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    
    private String NomePessoa;
    private String Expirado;
    
    private String Pwweb;

    public String getChave() {
        return Chave;
    }

    public void setChave(String Chave) {
        this.Chave = Chave;
    }

    public String getParametro() {
        return Parametro;
    }

    public void setParametro(String Parametro) {
        this.Parametro = Parametro;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getOper_Inc() {
        return Oper_Inc;
    }

    public void setOper_Inc(String Oper_Inc) {
        this.Oper_Inc = Oper_Inc;
    }

    public String getDt_Inc() {
        return Dt_Inc;
    }

    public void setDt_Inc(String Dt_Inc) {
        this.Dt_Inc = Dt_Inc;
    }

    public String getHr_Inc() {
        return Hr_Inc;
    }

    public void setHr_Inc(String Hr_Inc) {
        this.Hr_Inc = Hr_Inc;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    } 

    public String getNomePessoa() {
        return NomePessoa;
    }

    public void setNomePessoa(String NomePessoa) {
        this.NomePessoa = NomePessoa;
    }

    public String getDt_Valid() {
        return Dt_Valid;
    }

    public void setDt_Valid(String Dt_Valid) {
        this.Dt_Valid = Dt_Valid;
    }

    public String getFlag_excl() {
        return Flag_excl;
    }

    public void setFlag_excl(String Flag_excl) {
        this.Flag_excl = Flag_excl;
    }

    public String getOper_Excl() {
        return Oper_Excl;
    }

    public void setOper_Excl(String Oper_Excl) {
        this.Oper_Excl = Oper_Excl;
    }

    public String getDt_Excl() {
        return Dt_Excl;
    }

    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    public String getHr_Excl() {
        return Hr_Excl;
    }

    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    public String getExpirado() {
        return Expirado;
    }

    public void setExpirado(String Expirado) {
        this.Expirado = Expirado;
    }

    public String getPwweb() {
        return Pwweb;
    }

    public void setPwweb(String Pwweb) {
        this.Pwweb = Pwweb;
    }    
}

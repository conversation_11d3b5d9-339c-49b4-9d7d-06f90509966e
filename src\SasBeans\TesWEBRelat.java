package SasBeans;

import java.math.BigDecimal;
import java.sql.Date;

public class TesWEBRelat {
    private BigDecimal Sequencia;
    private Float CodFil;
    private String CodTes;
    private String CodCli;
    private Date Data;
    private String Descricao;
    private String URL;
    private String Operador;
    private Date Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public float getCodFil() {
        return CodFil;
    }

    public void setCodFil(float CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodTes() {
        return CodTes;
    }

    public void setCodTes(String CodTes) {
        this.CodTes = CodTes;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public Date getData() {
        return Data;
    }

    public void setData(Date Data) {
        this.Data = Data;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getURL() {
        return URL;
    }

    public void setURL(String URL) {
        this.URL = URL;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public Date getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(Date Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

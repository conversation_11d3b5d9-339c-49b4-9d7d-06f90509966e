/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.utils;

import java.io.Serializable;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.SessionScoped;

/**
 *
 * <AUTHOR>
 */
@ManagedBean
@SessionScoped
public class Horas implements Serializable {

    private static Locale sLocale;

    public static Map obterHorarioS() {

        Map map = new LinkedHashMap<>();

        LocalTime time = LocalTime.of(0, 0);
        String padraoHora = getPadraoHoraS();

        for (int i = 0; i < 96; i++) {
            map.put(time.format(DateTimeFormatter.ofPattern(padraoHora)), time.format(DateTimeFormatter.ofPattern("HH:mm")));
            time = time.plusMinutes(15);
        }

        return map;
    }

    private static String getPadraoHoraS() {
        sLocale = LocaleController.getsCurrentLocale();
        switch (sLocale.getLanguage().toUpperCase()) {
            case "PT":
                return "HH:mm";
            case "EN":
                return "hh:mm a";
            default:
                return "HH:mm";
        }
    }

    public Map obterHorario() {

        Map map = new LinkedHashMap<>();

        LocalTime time = LocalTime.of(0, 0);
        String padraoHora = getPadraoHora();

        for (int i = 0; i < 288; i++) {
            map.put(time.format(DateTimeFormatter.ofPattern(padraoHora)), time.format(DateTimeFormatter.ofPattern("HH:mm")));
            time = time.plusMinutes(5);
        }

        return map;
    }

    private String getPadraoHora() {
        sLocale = LocaleController.getsCurrentLocale();
        switch (sLocale.getLanguage().toUpperCase()) {
            case "PT":
                return "HH:mm";
            case "EN":
                return "hh:mm a";
            default:
                return "HH:mm";
        }
    }
}

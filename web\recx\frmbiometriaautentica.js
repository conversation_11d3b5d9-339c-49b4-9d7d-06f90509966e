//SASW Tecnologia//SATMOB
//FrmBiometriaAutentica - ScriptJS
//Autor : Carlos
//Data: 02/03/2022
//
//Atualizações de versão: 


const video = document.getElementById('webCamera');
const editMatr = document.getElementById('editMatr');
//const btnEnviarMatr = document.getElementById('btnEnviarMatr');
const btnAuth = document.getElementById('btnautentica');
const CadBiom = document.getElementById('btncadBio1');
const vImgSaidaBox = document.getElementById('vImgSaidaBox');
const btnSair = document.getElementById('btnSair');

const descriptors = [];
var vNomeFoto = "";
var vNomeFotoCap = "";
var vPathWeb = "";
var vPathLocal = "";
var vCount = 0;
var vContaTempoAtivo = false;
var vContaTempoMsgAtivo = false;
var vIntervalo;
var vIntervaloMsg;
var vSemaforoCompara = false;
var vSemaforoCaptura = false;

Promise.all([
    faceapi.nets.tinyFaceDetector.loadFromUri('models'),
    faceapi.nets.faceLandmark68Net.loadFromUri('models'),
    faceapi.nets.faceRecognitionNet.loadFromUri('models'),
    faceapi.nets.faceExpressionNet.loadFromUri('models'),
    faceapi.nets.ssdMobilenetv1.loadFromUri('models')
  ]);

window.onload = function(){	
    //var vUrl_String = window.location.href;
    //var vUrl = new URL(vUrl_String);
    var vMatrP = sessionStorage.getItem('matrSat');  //vUrl.searchParams.get("matr"); //pega o value

	
	document.getElementById("editMatr").value = vMatrP;
	document.getElementById("editMatr").focus();
	if (vMatrP != null){
	   autenticar();	
	}else{
		var vHTMLorigem =  sessionStorage.getItem('frmbiometriaautentica');
		if (vHTMLorigem == null){
			vHTMLorigem =  "frmpontobatidas.html"
	    }
        window.location.href = vHTMLorigem;
	}		   
}

editSenha.addEventListener("keypress", function(event) {
	if (event.key === "Enter") {
		event.preventDefault();
		document.getElementById("btnEnviar").click();		
	}
});


async function autenticar(){
	var vHeaders = new Headers();
	vHeaders.append("Content-Type", "application/xml");
	//validateForm();

	let vMatrID = document.querySelector('#editMatr').value;
	let vTokID = "B8489FF7205E3217B4E043D70FF9848B";
	var vParam = `matr=${vMatrID}&token=${vTokID}`;
    
	var vRequisicao = {
	  method: 'POST',
	  headers: vHeaders,
	  body: vParam,
	  redirect: 'follow'
	};

	var vWSSAS = "https://mobile.sasw.com.br/SatWebServiceHomolog/api/executasvc/obterFoto";
	//var baseurl = "http://10.1.1.2:8080/SatWebService/api/executasvc/obterFoto";

	fetch(vWSSAS, vRequisicao)
	.then(response => response.text())
	.then(result => autenticarSvc(JSON.parse(result)))
	.catch(error => console.log('error', error));
	//Modificar para colocar apenas uma mensagem simples no console (tirar .catch e colocar try catch la em cima)
}

async function autenticarSvc(jsonObj){
    var vMatr = jsonObj.funcion.matr;
    var imgpath = jsonObj.funcion.faceid;
    var vnome = jsonObj.funcion.nome;
    var vFoto2 = jsonObj.funcion.pathweb+jsonObj.funcion.nfoto2;
    vNomeFoto = jsonObj.funcion.nfoto1;
    vPathWeb = jsonObj.funcion.pathweb;
    vPathLocal = jsonObj.funcion.pathlocal;

    if (vnome == null){
       document.getElementById("editMatr").value = "0";
       document.getElementById("editNome").value = "MATRICULA INEXISTENTE";
       window.location.href = "frmpontobatidas.html";
    }else{
       document.getElementById("editMatr").value = vMatr.replace('.0', '');
       document.getElementById("editNome").value = vnome;
       if (imgpath == "" || imgpath == null || imgpath == "NAO REGISTRADO"){
	   imgpath = "/images/FotoND.jpg";
    	   btncadBio1.removeAttribute('hidden');//Habilitar Botao Captura Biometria
	   document.getElementById("dlg-login").style.display = 'block';
           document.getElementById("editSenha").focus();
           document.getElementById("editModo").value = "Registro/Captura";
	   document.getElementById("editModo").removeAttribute('hidden');		   
		   
	}else{
           document.getElementById("imgSrv1").src = imgpath.replace("'", '');

           if (vFoto2 != "" || vFoto2 != null){
              document.getElementById("imgSrv2").src = vFoto2.replace("'", '');
           }
           document.getElementById("painelPrinc").removeAttribute('hidden');
           document.getElementById("painelDireito").removeAttribute('hidden');
           document.getElementById("border_wrap").style.display = 'flex' ;
           autenticarUsuario();
        }
    }
    btnSair.removeAttribute('hidden');
}

async function validarSenha (){
	var vHeaders = new Headers();
	vHeaders.append("Content-Type", "application/xml");
        //Atribui variaveis
	let matrID = document.querySelector('#editMatr').value;
	let tokID = "B8489FF7205E3217B4E043D70FF9848B"; //document.querySelector('#token').value;
	let vPW = document.querySelector('#editSenha').value;
	let vChaveBio = "";
	var vParam = `matr=${matrID}&token=${tokID}&pw=${vPW}&chavebio=${vChaveBio}&data=`;
	var vRequisicao = {
            method: 'POST',
            headers: vHeaders,
            body: vParam,
            redirect: 'follow'
	};
        var vWSSAS = "https://mobile.sasw.com.br/SatWebServicePreHomolog/api/executasvc/obterFotoPW";
	fetch(vWSSAS, vRequisicao)	
	.then(response => response.text())
	.then(result => validarSenhaSvc(JSON.parse(result)))
	.catch(error => console.log('error', error));
}

async function validarSenhaSvc(jsonObj) {
    if (jsonObj.matr == 0){			    
        document.getElementById("editMensagemdlg").value = jsonObj.nome;
        document.getElementById("painelMensagensdlg").style.display = "flex";				
        tempoMsg();
        //messageDlg(document.getElementById("editMensagem").value = jsonObj.nome);
    }else{
        if (jsonObj.funcion[0].faceid == "NAO REGISTRADO") {
            document.getElementById("imgSrv1").src = jsonObj.funcion[0].fotobatida1
        } else {
            document.getElementById("imgSrv1").src = jsonObj.funcion[0].faceid;
        }	
        document.getElementById("editNome").value = jsonObj.funcion.nome;
        document.getElementById("editNome").removeAttribute("hidden");       
        document.getElementById("dlg-login").style.display = "none";
    }
}


function validateForm(){
    var a = document.querySelector('#editMatr').value;
    var b = "";//document.querySelector('#token').value;

    if (a == null || a == "") {
		alert("MATRICULA E TOKEN NECESSARIOS");
		return false;
    }
}

function chkDetalhesClick(){
   if (document.getElementById("chkDetalhes").checked){
	   document.getElementById("painelDireito").style.display  = 'flex';
	   document.getElementById("painelEsquerdo").style.display = 'flex';
	   
   }else{
	   document.getElementById("painelDireito").style.display = 'none';
	   document.getElementById("painelEsquerdo").style.display = 'none';
   }
}

async function autenticarUsuario(){
	document.getElementById("imgAutentica1").removeAttribute('hidden');
	document.getElementById("imgAutentica2").removeAttribute('hidden');
	document.getElementById("lbSegundo").removeAttribute('hidden'); //Mostra contador
    document.getElementById("editModo").removeAttribute('hidden');
    document.getElementById("editModo").value = "Autenticação facial"
    loadCamera();      
}

function loadCamera(){
    //Captura do elemento de vídeo
	//Habilita
	vSemaforoCaptura = false;
    var video = document.querySelector("#webCamera");
        //As opções abaixo são necessárias para o funcionamento correto no iOS
        video.setAttribute('autoplay', '');
        video.setAttribute('muted', '');
        video.setAttribute('playsinline', '');

    //Verifica se o navegador pode capturar mídia
    if (navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({audio: false, video: {facingMode: 'user'}})
        .then(function(stream){
            //Definir o elemento vídeo a carregar o capturado pela webcam
            video.srcObject = stream;
        })
        .catch(function(error){
            alert("Oooopps.... Não conseguimos carregar sua câmera :(");
        });
    }
}

video.addEventListener('play', () => {
	if (vSemaforoCaptura == false) {
		vSemaforoCaptura = true;
    
       const canvas = faceapi.createCanvasFromMedia(video)
	   const displaySize = { width: video.width, height: video.height }
	   faceapi.matchDimensions(canvas, displaySize)
	   document.body.appendChild(canvas)

	   setInterval(async () => {
           if (document.getElementById("painelMensagens").style.display == "none"){
                const detection = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks().withFaceExpressions()
                var t1 = document.getElementById('imgLocal2').src;
                var t2 = document.getElementById('imgLocal1').src;
                var t3 = document.getElementById('imgAutentica1').src;
                var t4 = document.getElementById('imgAutentica2').src;
                var fSrv1 = document.getElementById('imgSrv1').src;

                if (detection.length > 0){
                    //Call this function to extract and display face
                    //extractFaceFromBox(video, detection[0].detection.box)

                    const resizedDetection = faceapi.resizeResults(detection, displaySize)
                    canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height)
                    //faceapi.draw.drawDetections(canvas, resizedDetection)
                    //faceapi.draw.drawFaceExpressions(canvas, resizedDetection)
                    //faceapi.draw.drawFaceLandmarks(canvas, resizedDetection)


                    if(detection[0].expressions.happy > 0.99){
                        /* Somente para cadastrar
                         if (t1.substring(t1.length-10) == "FotoND.jpg" && fSrv1.substring(fSrv1.length-10) == "FotoND.jpg"){ 
                            extractFaceFromBoxFeliz(video, detection[0].detection.box)
                            pararContador();
                        }
                        */
                        // Captura foto para autenticação
                        if (document.querySelector("#imgAutentica2").hidden === false && t4.substring(t4.length-10) == "FotoND.jpg"){ 
                            extractFaceFromBoxFeliz(video, detection[0].detection.box);
                            pararContador();
                        }
                    }

                    if(detection[0].expressions.neutral > 0.90){   
                        /* Somente para cadastrar
                        if ((t2.substring(t2.length-10) == "FotoND.jpg") || (t3.substring(t3.length-10) == "FotoND.jpg")){  
                           tempo();
                       }
                        if (t2.substring(t2.length-10) == "FotoND.jpg" && fSrv1.substring(fSrv1.length-10) == "FotoND.jpg"){ // Só para autenticar                    
                            extractFaceFromBoxNeutro(video, detection[0].detection.box);			   
                        }
                        */
                       if (document.querySelector("#imgAutentica1").hidden === false && t3.substring(t3.length-10) == "FotoND.jpg"){
                            extractFaceFromBoxNeutro(video, detection[0].detection.box);                           
                        }
                    }

                    // Inserir autenticacao Automatica de usuario
                    if (document.getElementById("painelAnaliseComparacao").style.display === "none"){
                        if ((t3.substring(t3.length-10) != "FotoND.jpg") && (t4.substring(t4.length-10) != "FotoND.jpg")){
							desligaCamera();
							document.getElementById("painelFotoReconhecida").style.display = "flex";
							document.getElementById("border_wrap").style.display = "none" ;							
                            comparaFoto3e4();
                        }
                    }

                }else if((document.getElementById("editModo").hidden === false) && (document.getElementById("editModo").value == "Registro/Captura")){
                   comparaFotoeGrava();
                }

                
				if (t1.substring(t1.length-10) != "FotoND.jpg" && t2.substring(t2.length-10) != "FotoND.jpg"){
                   if (document.getElementById("editModo").value = "Biometria capturada"){
						desligaCamera();
                        document.getElementById("border_wrap").style.display = 'none' ;

                        document.getElementById("editLbAviso").setAttribute("hidden", true);
                        document.getElementById("painelImgPersonagem").setAttribute('hidden', true);
                        document.getElementById("painelPrinc").setAttribute("hidden", true);
                        document.getElementById("btnCompara3e4").removeAttribute("hidden");
						
                    }
                }
				
           }
	   }, 200)
	   document.body.removeChild(canvas);
	   vSemaforoCaptura = false;
	}
})

async function extractFaceFromBoxFeliz(inputImage, box){
    const regionsToExtract = [
        new faceapi.Rect( box.x, box.y-50 , box.width , box.height+40)
    ]    
    let faceImages = await faceapi.extractFaces(inputImage, regionsToExtract)
	

    if(faceImages.length == 0){
	document.getElementById("editMensagem").value = "Rosto não encontrado";
	document.getElementById("painelMensagens").style.display = "flex";
        if (vContaTempoMsgAtivo != true){
	   tempoMsg();
        }
    }else {
		document.getElementById('imgborder_wrap').src = "layout-imgs/background-yellowx.png";
        faceImages.forEach(cnv =>{						
            if (document.querySelector("#imgAutentica2").hidden === false) {  //Modo Autenticacao
                document.getElementById('imgAutentica2').src = cnv.toDataURL('image/jpeg');
                document.getElementById("editLbAviso").setAttribute("hidden", true);
                document.getElementById("painelImgPersonagem").setAttribute("hidden", true);
            }
            //
            //Modo Registro/Captura
            //document.getElementById('imgLocal2').src = cnv.toDataURL('image/jpeg');
            //document.querySelector("#memoBase64Img2").value = cnv.toDataURL('image/jpeg');
        
        })
    }
}

async function extractFaceFromBoxNeutro(inputImage, box){
    const regionsToExtract = [
        new faceapi.Rect( box.x, box.y-50 , box.width , box.height+40)

		
    ]
    const regionsToExtract2 = [
        new faceapi.Rect( box.x-50, box.y-100 , box.width+71+20, box.height+84+55)  // Para foto reconhecida
		//213, 134  -  240, 237
		
		
    ]
    let faceImages = await faceapi.extractFaces(inputImage, regionsToExtract)
    let faceImages2 = await faceapi.extractFaces(inputImage, regionsToExtract2) // Para foto reconhecida

    if(faceImages.length == 0){
     	document.getElementById("editMensagem").value = "Rosto não encontrado";
	document.getElementById("painelMensagens").style.display = "flex";				

    }else{
		 document.getElementById('imgborder_wrap').src = "layout-imgs/background-orangex.png";
         faceImages.forEach(cnv =>{			
            if (document.querySelector("#imgAutentica1").hidden === false) {//Modo Autenticacao
                document.getElementById('imgAutentica1').src = cnv.toDataURL('image/jpeg');	
            }
         });
         faceImages2.forEach(cnv =>{			// Para foto reconhecida
           if (document.querySelector("#imgAutentica1").hidden === false) {//Modo Autenticacao
               document.getElementById('imgLocal1').src = cnv.toDataURL('image/jpeg');
               document.getElementById('imgFotoReconhecida').src = cnv.toDataURL('image/jpeg');

           }
           //{
           //     //Modo Registro/Captura
           //     document.getElementById('imgLocal1').src = cnv.toDataURL('image/jpeg');
           //     document.querySelector("#memoBase64Img1").value = cnv.toDataURL('image/jpeg');
           //     document.getElementById("editLbAviso").innerHTML = "";
           //}
        })
    }
    if (vContaTempoAtivo != true){
       tempo();
    }; 
    
}

function tempo(op){
    vContaTempoAtivo = true;
	var s = 1;
	var m = 0;
	var h = 0;
	vIntervalo = window.setInterval(function() {
		if (s == 25) {
                    m++; 
                    s = 0; 
                    if (document.getElementById("editModo").value != 'Autenticação OK') {
                        fazerNovaAutenticacao(); 
                    } else {
                        pararContador();
                    }
                }
		if (m == 60) { h++; s = 0; m = 0; }
		if (s < 10){document.getElementById("lbSegundo").innerHTML = "0" + s + "";}else{document.getElementById("lbSegundo").innerHTML = s + ""};		
		if (s > 6){ document.getElementById("painelImgPersonagem").removeAttribute("hidden");
                            document.getElementById("editLbAviso").removeAttribute("hidden");}
                if (painelMensagens.style.display == "none") {
	 	   s++;
                }
	},200);	
   if (document.querySelector("#imgAutentica2").hidden === false){
      document.getElementById("lbSegundo").removeAttribute("hidden");//Mostra contador
   }        
}

function tempoMsg(op) {	
    vContaTempoMsgAtivo = true;
    window.clearInterval(vIntervaloMsg);
    var s = 1;	
    vIntervaloMsg = window.setInterval(function() {
    if (s == 55) { }
    if (s > 38) document.getElementById("painelMensagens").style.display = "none";
    if (s > 37) document.getElementById("painelMensagensdlg").style.display = "none";
    //if (s > 50) document.getElementById("editSenha").focus();
    if (s > 40){
       vContaTempoMsgAtivo = false;
       window.clearInterval(vIntervaloMsg);
       if (document.getElementById("editModo").value = "Autenticação falhou"){
           fazerNovaAutenticacao();
       } 
       
    }
     s++;
    },100);
   
}


function pararContador(){
  vContaTempoAtivo = false;
  window.clearInterval(vIntervalo);	
}




async function comparaFoto3e4(){
    if (vSemaforoCompara == false){
	   vSemaforoCompara = true;
        var vSemaforo = false;
        var t3 = document.getElementById('imgAutentica1').src;
        var t4 = document.getElementById('imgAutentica2').src;
        if ((t3.substring(t3.length-10) != "FotoND.jpg") && (t4.substring(t4.length-10) != "FotoND.jpg")){
            try{		
                document.getElementById('imgFotoRec').src = "layout-imgs/background-redx.png";
                //Fase 1 - Foto Neutro Srv		
                let vImgLocalX = document.getElementById('imgSrv1'); //Base de comparacao foto 1		
                const vRostoFaceApiX = await faceapi.detectSingleFace(vImgLocalX).withFaceLandmarks().withFaceDescriptor().withFaceExpressions();

                //Detecção e criação do descriptor da imagem capturada 1		
                let vImgLocal3 = document.getElementById('imgAutentica1');
                const vRostoFaceApi3 = await faceapi.detectSingleFace(vImgLocal3).withFaceLandmarks().withFaceDescriptor().withFaceExpressions();

                document.getElementById('imgFotoRec').src = "layout-imgs/background-orangex.png";				
                //Detecção e criação do descriptor da imagem capturada 2		
                let vImgLocal4 = document.getElementById('imgAutentica2');
                const vRostoFaceApi4 = await faceapi.detectSingleFace(vImgLocal4).withFaceLandmarks().withFaceDescriptor().withFaceExpressions();

                //Estabelece a foto base de comparacao
                const faceMatcher = new faceapi.FaceMatcher(vRostoFaceApiX)

                //Verificando se a imagem capturada combina (dá match) com a img do servidor

                try{
                    const bestMatch1 = faceMatcher.findBestMatch(vRostoFaceApi3.descriptor)
                    const bestMatch2 = faceMatcher.findBestMatch(vRostoFaceApi4.descriptor)
                    document.getElementById('editFaceApiCompara1').value = bestMatch1.distance;
                    document.getElementById('editFaceApiCompara2').value = bestMatch2.distance;
                }
                catch(error){
                    //console.log("FotoSrv1: "+ document.getElementById('imgSrv1')+
                    //            " Foto Autentica1: "+document.getElementById('imgAutentica1')+
                    //            " Foto Autentica2: "+document.getElementById('imgAutentica2'));
                    if (vSemaforo == false){
                       vSemaforo = true;
                       forcarNovaAutenticacao();
                    }
                    console.log(error);            
                }	


                //Fase 2 - Foto Feliz Srv		
                            document.getElementById('imgFotoRec').src = "layout-imgs/background-yellowx.png";
                let vImgLocalX2 = document.getElementById('imgSrv2'); //Base de comparacao foto 2		
                const vRostoFaceApiX2 = await faceapi.detectSingleFace(vImgLocalX2).withFaceLandmarks().withFaceDescriptor().withFaceExpressions();
                const faceMatcher2 = new faceapi.FaceMatcher(vRostoFaceApiX2)

                //Verificando se a imagem capturada combina (dá match) com a img do servidor
                try{
                    const bestMatch3 = faceMatcher2.findBestMatch(vRostoFaceApi3.descriptor)
                    const bestMatch4 = faceMatcher2.findBestMatch(vRostoFaceApi4.descriptor)
                    document.getElementById('editFaceApiCompara3').value = bestMatch3.distance;
                                    document.getElementById('imgFotoRec').src = "layout-imgs/background-greenx.png";
                    document.getElementById('editFaceApiCompara4').value = bestMatch4.distance;
                }
                catch(error){
                    if (vSemaforo == false){
                       vSemaforo = true;
                       forcarNovaAutenticacao();
                    }
                    console.log(error);
                }


                if (document.getElementById('editFaceApiCompara1').value === "" ||
                    document.getElementById('editFaceApiCompara2').value === "" ||
                    document.getElementById('editFaceApiCompara3').value === "" ||
                    document.getElementById('editFaceApiCompara4').value === "" ) {
                   if (vSemaforo == false){
                       vSemaforo = true;
                       forcarNovaAutenticacao();
                   }

                } else {
                    pararContador();                    
                    document.getElementById("painelAnaliseComparacao").style.display = "table";
					document.getElementById("painelResultado").style.display = "flex";
					let vX1 = document.getElementById('editFaceApiCompara1').value;
					let vX2 = document.getElementById('editFaceApiCompara2').value;
					let vX3 = document.getElementById('editFaceApiCompara3').value;
					let vX4 = document.getElementById('editFaceApiCompara4').value;
					if (vX1 <= 0.49 && vX4 <= 0.49){
					   document.getElementById("editModo").value = 'Autenticação OK';
					   document.getElementById("lbResultado").innerHTML = "Identificação positiva";
					   document.getElementById("lbResultado").style.color = "green";
					   document.getElementById('imgFotoRec').src = "layout-imgs/background-greenx.png";
					   buscarChaveBioeFechar()					   
					}else{
					   sessionStorage.setItem('matrAutenticada', '');
					   sessionStorage.setItem('matrSat', '');
					   sessionStorage.setItem('chavebio', '');
					   document.getElementById("editModo").value = 'Rosto não compatível';
					   document.getElementById("lbResultado").innerHTML = "Rosto não compatível";
					   document.getElementById("lbResultado").style.color = "red";
					   document.getElementById('imgFotoRec').src = "layout-imgs/background-redx.png";
					}
					                    
					

                };

            }
            catch(error){
                if (vSemaforo == false){
                    vSemaforo = true;
                    forcarNovaAutenticacao();
                }
                console.log(error);
            }
        }
		vSemaforoCompara = false;
    }
}


function buscarChaveBioeFechar(){
	var vHeaders = new Headers();
	vHeaders.append("Content-Type", "application/xml");
	//validateForm();

	let vMatrID = document.getElementById('editMatr').value;
	let vCodPessoa = "0";
	let vBancoDados = "SatSASEX";
	let vTokID = "B8489FF7205E3217B4E043D70FF9848B";
	let vPW = document.getElementById('editSenha').value;;
	let vFotoBase = document.getElementById("imgLocal1").src;
    let vFotoN1 = document.getElementById("imgAutentica1").src;
	let vFotoS1 = document.getElementById("imgAutentica2").src;
	let vNota1 = document.getElementById("editFaceApiCompara1").value;
	let vNota2 = document.getElementById("editFaceApiCompara2").value;
	let vNota3 = document.getElementById("editFaceApiCompara3").value;
	let vNota4 = document.getElementById("editFaceApiCompara4").value;
	let vChaveBio = "";


	var vParam = `fotobase=${vFotoBase}&foton1=${vFotoN1}&fotos1=${vFotoS1}&codpessoa=${vCodPessoa}&bancodados=${vBancoDados}&matr=${vMatrID}
	             &nota1=${vNota1}&nota2=${vNota2}&nota3=${vNota3}&nota4=${vNota4}&cadbio=${vChaveBio}&token=${vTokID}`;

	var vRequisicao = {
		method: 'POST',
		headers: vHeaders,
		body: vParam,
		redirect: 'follow'
	};

	var vWSSAS = "https://mobile.sasw.com.br/SatWebServicePreHomolog/api/executasvc/faceAutentica";

	fetch(vWSSAS, vRequisicao)
	.then(response => response.text())
	.then(result => buscaChaveBioSvc(JSON.parse(result)))
	.catch(error => console.log('error', error));
}

function buscaChaveBioSvc(jsonObj){
	if (typeof jsonObj.resposta.cadbio != "undefined"){
      var vChaveBio = jsonObj.resposta.cadbio;
      sessionStorage.setItem('matrAutenticada', document.getElementById("editMatr").value);
      sessionStorage.setItem('chavebio', vChaveBio);

    }else{
      sessionStorage.setItem('matrAutenticada', "");
      sessionStorage.setItem('chavebio', "");
	}
}

async function forcarNovaAutenticacao(){    
   pararContador();
   document.getElementById("editMensagem").value = "As fotos capturadas não são compatíveis. Tente novamente.";
   document.getElementById('imgAutentica1').src = "layout-imgs/FotoND.jpg"; 					  
   document.getElementById('imgAutentica2').src = "layout-imgs/FotoND.jpg";                
   document.getElementById("painelMensagens").style.display = "flex";		
   document.getElementById("editModo").value = "Autenticação falhou";
   if (vContaTempoMsgAtivo != true){
      tempoMsg();
   document.getElementById("painelFotoReconhecida").style.display = "none";
   document.getElementById("border_wrap").style.display = "flex" ;							
   document.getElementById("painelFotoReconhecida").style.display = "none";
   document.getElementById("border_wrap").style.display = "flex" ;							
   document.getElementById("imgborder_wrap").src = "layout-imgs/background-bluex.png";
   loadCamera();


  }

 }

async function fazerNovaAutenticacao(){    
   document.getElementById("editLbAviso").setAttribute("hidden", true)
   document.getElementById("painelImgPersonagem").setAttribute('hidden', true);   		
   document.getElementById("painelAnaliseComparacao").style.display = "none"; 
   document.getElementById("editModo").value = "Autenticação facial";
}
   
async function comparaFotoeGrava(){
    var t3 = document.getElementById('imgLocal1').src;
    var t4 = document.getElementById('imgLocal2').src;
    if ((t3.substring(t3.length-10) != "FotoND.jpg") && (t4.substring(t4.length-10) != "FotoND.jpg")) {
        //Fazer match entre as 2 fotos
        try{
            let vImgLocal1 = document.getElementById('imgLocal1');
            let vImgLocal2 = document.getElementById('imgLocal2')
            const vRostoFaceApi1 = await faceapi.detectSingleFace(vImgLocal1).withFaceLandmarks().withFaceDescriptor().withFaceExpressions();
            const faceMatcher = new faceapi.FaceMatcher(vRostoFaceApi1);
            try{
                const vRostoFaceApi2 = await faceapi.detectSingleFace(vImgLocal2).withFaceLandmarks().withFaceDescriptor().withFaceExpressions();
                const bestMatch1 = faceMatcher.findBestMatch(vRostoFaceApi1.descriptor);
                const bestMatch2 = faceMatcher.findBestMatch(vRostoFaceApi2.descriptor);
                //let vIndCompara1 = bestMatch1.toString();// bestMatch1.distance
                //let vIndCompara2 = bestMatch2.toString();
                console.log(bestMatch1.distance);
                console.log(bestMatch2.distance);

                if ((bestMatch1.distance+bestMatch2.distance) < 0.40){
                    gravaFoto("1");
                    gravaFoto("2");
                    //document.getElementById("editModo").value = "Biometria capturada";
                }
                else{
                    forcarNovaCaptura();
                }
            }
            catch(error){                
			document.getElementById("editMensagem").value = "NAO RECONHECEU FACE 2 - Falha na comparacao imagem capturada neutra ";
		        document.getElementById("painelMensagens").style.display = "flex";				
                        if (vContaTempoMsgAtivo != true){
			   tempoMsg();
                        }

                forcarNovaCaptura();
                console.log(error);
            }
        }
        catch(error){            
		document.getElementById("editMensagem").value = "NAO RECONHECIDO. TENTE NOVAMENTE";
		document.getElementById("painelMensagens").style.display = "flex";				
		if (vContaTempoMsgAtivo != true){
                   tempoMsg();
               }
            forcarNovaCaptura();
            console.log(error);
        }
    }
 }

//Função que vai enviar para o servidor as informações do usuário
async function gravaFoto(nfotoX){
	var vHeaders = new Headers();
	vHeaders.append("Content-Type", "application/xml");
	//validateForm();

	let vMatrID = document.querySelector('#editMatr').value;
	let vTokID = "B8489FF7205E3217B4E043D70FF9848B";
	let vPW = document.querySelector('#editSenha').value;;

	var vFotoCap = "";
	if (nfotoX == 1){
	   vFotoCap = document.querySelector("#memoBase64Img1").value;
	}
	else if (nfotoX == 2){
	   vFotoCap = document.querySelector("#memoBase64Img2").value;
    }
	else if (nfotoX == 3){
		vFotoCap = document.querySelector("#memoBase64Img3").value;
	}
	

	var vParam = `matr=${vMatrID}&token=${vTokID}&nfoto=${nfotoX}&fotocap=${vFotoCap}&pw=${vPW}`;

	var vRequisicao = {
		method: 'POST',
		headers: vHeaders,
		body: vParam,
		redirect: 'follow'
	};

	var vWSSAS = "https://mobile.sasw.com.br/SatWebServicePreHomolog/api/executasvc/gravaFoto";
	//var baseurl = "http://10.1.1.2:8080/SatWebService/api/executasvc/gravaFoto";

	fetch(vWSSAS, vRequisicao)
	.then(response => response.text())
	.then(result => retornoGravaFoto(JSON.parse(result)))
	.catch(error => console.log('error', error));
}

async function retornoGravaFoto(jsonObj){
    if(jsonObj.nfoto != 3){
            if(jsonObj.resposta == "SUCESSO"){
               document.getElementById("editMensagem").value = "Usuário cadastrado com sucesso!";
               document.getElementById("painelMensagens").style.display = "flex";				
               if (vContaTempoMsgAtivo != true){
                  tempoMsg();
              }
            }else{	   	    
               document.getElementById("editMensagem").value = "Senha Inválida";
               if (vContaTempoMsgAtivo != true){
                  tempoMsg();
              }

              document.getElementById("painelMensagens").style.display = "flex";				
              document.getElementById("editModo").value = document.getElementById("editMensagem").value;
              forcarNovaCaptura();		   
            }
    }else{
       vNomeFotoCap = jsonObj.fotocap;
       geraDetec();
    }
}


async function forcarNovaCaptura(){
    document.getElementById("editMensagem").value = "As fotos capturadas não são compatíveis. Tente novamente.";
    document.getElementById("painelMensagens").style.display = "flex";				
    if (vContaTempoMsgAtivo != true){
        tempoMsg();	
    }
    document.getElementById('imgLocal1').src = "layout-imgs/FotoND.jpg"; 					  
    document.getElementById('imgLocal2').src = "layout-imgs/FotoND.jpg";
    document.getElementById("editModo").value = "Registro/Captura";
    btncadBio1.removeAttribute('hidden');//Habilitar Botao Captura Biometria
    document.getElementById("btnCompara3e4").setAttribute('hidden', true);	
    pararContador();
    //document.getElementById("editLbAviso").setAttribute('hidden', true); 
    //document.getElementById("painelImgPersonagem").setAttribute('hidden', true);   
    document.getElementById("lbSegundo").setAttribute('hidden', true);//Esconde contador
}

function desligaCamera(){
	const video = document.querySelector('video');
	const mediaStream = video.srcObject;
    const tracks = mediaStream.getTracks();
	tracks[0].stop();	
	vSemaforoCaptura = true;
}

async function atualizar(){		
	window.location.href = "frmpontobatidas.html";
}

async function cadastraFoto1(){
	//Captura do elemento de vídeo
	var video = document.querySelector("#webCamera");

	//Criando um canvas que vai guardar a imagem temporariamente
	var canvas = document.createElement('canvas');
	canvas.width = video.videoWidth;
	canvas.height = video.videoHeight;
	var ctx = canvas.getContext('2d');

	//Desenhando e convertendo as dimensões
	ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

	//var dataURI = canvas.toDataURL('image/jpeg'); //O resultado é um BASE64 de uma imagem.
	var dataURI = document.querySelector('#vImgSaidaBox').src

	//Pegando o base64 da imagem capturada
	document.querySelector("#memoBase64Img3").value = dataURI;
	
	document.getElementById('imgLocal1').src = vImgSaidaBox.src;
	console.log(vImgSaidaBox.src);
	gravaFoto("1");
}

async function cadastraFoto2(){
	//Captura do elemento de vídeo
	var video = document.querySelector("#webCamera");

	//Criando um canvas que vai guardar a imagem temporariamente
	var canvas = document.createElement('canvas');
	canvas.width = video.videoWidth;
	canvas.height = video.videoHeight;
	var ctx = canvas.getContext('2d');

	//Desenhando e convertendo as dimensões
	ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

	//var dataURI = canvas.toDataURL('image/jpeg'); //O resultado é um BASE64 de uma imagem.
	var dataURI = document.querySelector('#vImgSaidaBox').src

	//Pegando o base64 da imagem capturada
	document.querySelector("#memoBase64Img3").value = dataURI;
	
	document.getElementById('imgLocal2').src = vImgSaidaBox.src;
	gravaFoto("2");
}

async function habilitaBotoesFoto(){
	if (document.getElementById("editSenha").value == ""){	  
            document.getElementById("editMensagem").value = "Informe a sua Senha";
            document.getElementById("painelMensagens").style.display = "flex";				
            if (vContaTempoMsgAtivo != true){
                tempoMsg();
            }
       document.getElementById("editSenha").focus();	   
	}else{	
		btncadBio1.setAttribute('hidden', true);   
		document.getElementById("painelPrinc").removeAttribute('hidden');
		document.getElementById("painelDireito").height = document.getElementById("border_wrap").height;
		document.getElementById("painelDireito").marginLeft = document.getElementById("border_wrap").marginLeft;
		document.getElementById("painelEsquerdo").height = document.getElementById("border_wrap").height;
		document.getElementById("painelEsquerdo").marginLeft = document.getElementById("border_wrap").marginLeft;
		document.getElementById("painelImagens").style.display = "flex";
                document.getElementById("border_wrap").style.display = 'flex' ;

		loadCamera();
	}
	btnSair.removeAttribute('hidden');

}



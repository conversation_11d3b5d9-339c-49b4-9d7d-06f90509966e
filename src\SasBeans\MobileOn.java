/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class MobileOn {

    private String Nome;
    private BigDecimal Codfil;
    private BigDecimal CodPessoa;
    private String IMEI;
    private String ApiKEY;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getCodfil() {
        return Codfil;
    }

    public void setCodfil(BigDecimal Codfil) {
        this.Codfil = Codfil;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getIMEI() {
        return IMEI;
    }

    public void setIMEI(String IMEI) {
        this.IMEI = IMEI;
    }

    public String getApiKEY() {
        return ApiKEY;
    }

    public void setApiKEY(String ApiKEY) {
        this.ApiKEY = ApiKEY;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

}

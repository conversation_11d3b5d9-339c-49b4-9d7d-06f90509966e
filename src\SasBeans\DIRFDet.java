package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class DIRFDet {

    private BigDecimal CodFil;
    private String AnoCompet;
    private String CPF;
    private BigDecimal Matr;
    private String TipoReg;
    private BigDecimal Valor01;
    private BigDecimal Valor02;
    private BigDecimal Valor03;
    private BigDecimal Valor04;
    private BigDecimal Valor05;
    private BigDecimal Valor06;
    private BigDecimal Valor07;
    private BigDecimal Valor08;
    private BigDecimal Valor09;
    private BigDecimal Valor10;
    private BigDecimal Valor11;
    private BigDecimal Valor12;
    private BigDecimal Valor13;
    private BigDecimal ValorTotal;

    public DIRFDet() {
        this.CodFil = new BigDecimal("0");
        this.AnoCompet = "";
        this.CPF = "";
        this.Matr = new BigDecimal("0");
        this.TipoReg = "";
        this.Valor01 = new BigDecimal("0");
        this.Valor02 = new BigDecimal("0");
        this.Valor03 = new BigDecimal("0");
        this.Valor04 = new BigDecimal("0");
        this.Valor05 = new BigDecimal("0");
        this.Valor06 = new BigDecimal("0");
        this.Valor07 = new BigDecimal("0");
        this.Valor08 = new BigDecimal("0");
        this.Valor09 = new BigDecimal("0");
        this.Valor10 = new BigDecimal("0");
        this.Valor11 = new BigDecimal("0");
        this.Valor12 = new BigDecimal("0");
        this.Valor13 = new BigDecimal("0");
        this.ValorTotal = new BigDecimal("0");
    }

    public BigDecimal getValor02() {
        return Valor02;
    }

    public BigDecimal getValor03() {
        return Valor03;
    }

    public BigDecimal getValor04() {
        return Valor04;
    }

    public BigDecimal getValor05() {
        return Valor05;
    }

    public BigDecimal getValor06() {
        return Valor06;
    }

    public BigDecimal getValor07() {
        return Valor07;
    }

    public BigDecimal getValor08() {
        return Valor08;
    }

    public BigDecimal getValor09() {
        return Valor09;
    }

    public BigDecimal getValor10() {
        return Valor10;
    }

    public BigDecimal getValor11() {
        return Valor11;
    }

    public BigDecimal getValor12() {
        return Valor12;
    }

    public BigDecimal getValor13() {
        return Valor13;
    }

    public BigDecimal getValorTotal() {
        return ValorTotal;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("1");
        }
    }

    public String getAnoCompet() {
        return AnoCompet;
    }

    public void setAnoCompet(String AnoCompet) {
        this.AnoCompet = AnoCompet;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public String getTipoReg() {
        return TipoReg;
    }

    public void setTipoReg(String TipoReg) {
        this.TipoReg = TipoReg;
    }

    public BigDecimal getValor01() {
        return Valor01;
    }

    public void setValor01(String Valor01) {
        try {
            this.Valor01 = new BigDecimal(Valor01);
        } catch (Exception e) {
            this.Valor01 = new BigDecimal("0");

        }
    }

    public void setValor02(String Valor02) {
        try {
            this.Valor02 = new BigDecimal(Valor02);
        } catch (Exception e) {
            this.Valor02 = new BigDecimal("0");
        }
    }

    public void setValor03(String Valor03) {
        try {
            this.Valor03 = new BigDecimal(Valor03);
        } catch (Exception e) {
            this.Valor03 = new BigDecimal("0");
        }
    }

    public void setValor04(String Valor04) {
        try {
            this.Valor04 = new BigDecimal(Valor04);
        } catch (Exception e) {
            this.Valor04 = new BigDecimal("0");
        }
    }

    public void setValor05(String Valor05) {
        try {
            this.Valor05 = new BigDecimal(Valor05);
        } catch (Exception e) {
            this.Valor05 = new BigDecimal("0");
        }
    }

    public void setValor06(String Valor06) {
        try {
            this.Valor06 = new BigDecimal(Valor06);
        } catch (Exception e) {
            this.Valor06 = new BigDecimal("0");
        }
    }

    public void setValor07(String Valor07) {
        try {
            this.Valor07 = new BigDecimal(Valor07);
        } catch (Exception e) {
            this.Valor07 = new BigDecimal("0");
        }
    }

    public void setValor08(String Valor08) {
        try {
            this.Valor08 = new BigDecimal(Valor08);
        } catch (Exception e) {
            this.Valor08 = new BigDecimal("0");
        }
    }

    public void setValor09(String Valor09) {
        try {
            this.Valor09 = new BigDecimal(Valor09);
        } catch (Exception e) {
            this.Valor09 = new BigDecimal("0");
        }
    }

    public void setValor10(String Valor10) {
        try {
            this.Valor10 = new BigDecimal(Valor10);
        } catch (Exception e) {
            this.Valor10 = new BigDecimal("0");
        }
    }

    public void setValor11(String Valor11) {
        try {
            this.Valor11 = new BigDecimal(Valor11);
        } catch (Exception e) {
            this.Valor11 = new BigDecimal("0");
        }
    }

    public void setValor12(String Valor12) {
        try {
            this.Valor12 = new BigDecimal(Valor12);
        } catch (Exception e) {
            this.Valor12 = new BigDecimal("0");
        }
    }

    public void setValor13(String Valor13) {
        try {
            this.Valor13 = new BigDecimal(Valor13);
        } catch (Exception e) {
            this.Valor13 = new BigDecimal("0");
        }
    }

    public void setValorTotal(String ValorTotal) {
        try {
            this.ValorTotal = new BigDecimal(ValorTotal);
        } catch (Exception e) {
            this.ValorTotal = new BigDecimal("0");
        }
    }
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.SasLogMob;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SasLogMobDao {

    /**
     * Busca o valor máximo de sequencia da tabela
     *
     * @param persistencia - conexão ao banco de dados
     * @return - retorna o último valor gravado
     * @throws Exception
     */
    public BigDecimal getMaxSequencia(Persistencia persistencia) throws Exception {
        String sql = "select MAX(sequencia) maxseq from saslogmob";
        BigDecimal retorno = new BigDecimal("0");
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = new BigDecimal(consult.getString("maxseq"));
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar ultimo numero de sequencia - " + e.getMessage());
        }
    }

    /**
     * Grava registro na tabela de logs do mobile
     *
     * @param log - objeto do tipo SasLogMob
     * @param persistencia - conexão com o banco de dados
     * @throws Exception
     */
    public void InsereLog(SasLogMob log, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into saslogmob "
                    + " (Sequencia,SeqRota,Parada,HrCheg,HrSaida,Guia,Serie,Valor,Versao,Volumes,Historico,Dt_Alter,Hr_Alter,IMEI) values"
                    + " (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(log.getSequencia());
            consulta.setBigDecimal(log.getSeqRota());
            consulta.setInt(log.getParada());
            consulta.setString(log.getHrCheg());
            consulta.setString(log.getHrSaida());
            consulta.setBigDecimal(log.getGuia());
            consulta.setString(log.getSerie());
            consulta.setBigDecimal(log.getValor());
            consulta.setString(log.getVersao());
            consulta.setString(log.getVolumes());
            consulta.setString(log.getHistorico());
            consulta.setDate(Date.valueOf(log.getDt_Alter()));
            consulta.setString(log.getHr_Alter());
            consulta.setString(log.getIMEI());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar SasLogMob - " + e.getMessage());
        }
    }

    /**
     * Busca log de determinada rota
     *
     * @param Seqrota - Sequencia da rota
     * @param persistencia - conexão ao banco de dados
     * @return - Todo o log da rota
     * @throws Exception
     */
    public List<SasLogMob> BuscaPorRota(String Seqrota, Persistencia persistencia) throws Exception {
        List<SasLogMob> retorno = new ArrayList();
        try {
            String sql = "select * from saslogmob where seqrota = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Seqrota);
            consult.select();
            while (consult.Proximo()) {
                SasLogMob logmob = new SasLogMob();
                logmob.setSequencia(consult.getString("Sequencia"));
                logmob.setSeqRota(consult.getString("SeqRota"));
                logmob.setParada(consult.getInt("Parada"));
                logmob.setHrCheg(consult.getString("HrCheg"));
                logmob.setHrSaida(consult.getString("HrSaida"));
                logmob.setGuia(consult.getString("Guia"));
                logmob.setSerie(consult.getString("Serie"));
                logmob.setValor(consult.getString("Valor"));
                logmob.setVolumes(consult.getString("Volumes"));
                retorno.add(logmob);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao busca log da rota - /r/n" + e.getMessage());
        }
    }

    /**
     * Atualiza o registro de log
     *
     * @param log - Objeto com os dados do log que desejamos atualizar
     * @param persistencia - Conexão ao banco de dados.
     * @throws Exception
     */
    public void AtualizaLog(SasLogMob log, Persistencia persistencia) throws Exception {
        try {
            String sql = "update SasLogMob set Valor = ?, Volumes = ?, HrCheg = ?, HrSaida = ?"
                    + " where Sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(log.getValor());
            consulta.setString(log.getVolumes());
            consulta.setString(log.getHrCheg());
            consulta.setString(log.getHrSaida());
            consulta.setBigDecimal(log.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar SasLogMob - \r\n" + e.getMessage());
        }
    }
}

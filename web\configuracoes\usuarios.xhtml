<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/servicos.css" rel="stylesheet"/>
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <script src="../assets/js/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body>

            <f:metadata>
                <f:viewAction action="#{usuarios.Persistencias(login.pp, login.satellite)}" />
            </f:metadata>

            <p:growl id="msgs" />

            <ui:composition template = "../assets/template/page.xhtml">	

                <ui:define name="menu">
                    <ui:include src="../assets/template/menu.xhtml" />
                </ui:define>

                <ui:define name="top-menu">
                    <ui:include src="../assets/template/header.xhtml" />  
                </ui:define>

                <ui:define name="infoFilial">
                    <div class="ui-grid-row">
                        <h:outputText value="#{usuarios.filiais.descricao}" class="negrito"/>
                    </div>
                    <div class="ui-grid-row">
                        #{usuarios.filiais.endereco}
                    </div>
                    <div class="ui-grid-row">
                        #{usuarios.filiais.bairro}<h:outputText value=", " rendered="#{usuarios.filiais.bairro ne null and usuarios.filiais.bairro ne ''}"/>#{usuarios.filiais.cidade}/#{usuarios.filiais.UF}
                    </div>
                </ui:define>

                <ui:define name="body">
                    <div class="box box-primary" style="font-family: 'Helvetica Neue';font-size: 15px">
                        <h:form id="main">
                            <p:panel styleClass="painelCadastro">
                                <p:dataGrid id="tabela" value="#{usuarios.allAcessos}" paginator="true" rows="50" lazy="true"
                                            rowsPerPageTemplate="5,10,15,20,25,50"
                                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Usuarios}"
                                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                            var="lista" emptyMessage="#{localemsgs.SemRegistros}"
                                            columns="1">
                                    <p:panel>
                                        <f:facet name="header">
                                            <i class="fas fa-user"></i>
                                            <h:outputText value="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#{lista.pessoa.nome}" style="color:#000 !important"/>
                                        </f:facet>
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">     
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Filial}: "/>
                                                <h:outputText value="#{lista.saspw.codFil}" title="#{lista.saspw.codFil}" 
                                                              converter="conversorCodFil" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Email}: "/>
                                                <h:outputText value="#{lista.pessoa.email}" title="#{lista.pessoa.email}" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Nivel}: "/>
                                                <h:outputText value="#{lista.saspw.nivelOP}" title="#{lista.saspw.nivelOP}" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Grupo}: "/>
                                                <h:outputText value="#{lista.grupo.descricao}" title="#{lista.grupo.descricao}" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Descricao}: "/>
                                                <h:outputText value="#{lista.saspw.descricao}" title="#{lista.saspw.descricao}" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Situacao}: "/>
                                                <h:outputText value="#{lista.saspw.situacao}" title="#{lista.saspw.situacao}" class="negrito-normal"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-8" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{usuarios.abrirUsuario(lista)}"
                                                           update="formEditar:editar msgs">
                                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                            </p:commandLink>
                                        </p:panelGrid>
                                    </p:panel>
                                </p:dataGrid>
                            </p:panel>

                            <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                                <div style="padding-bottom: 10px">
                                    <p:commandLink title="#{localemsgs.Cadastrar}" update="msgs formEditar"
                                                   action="#{usuarios.novoUsuario}">
                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                    </p:commandLink>
                                </div>
                                <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                            </p:panel>
                        </h:form>
                    </div>

                    <h:form class="form-inline" id="formEditar">
                        <p:dialog widgetVar="dlgEditar" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" 
                                  style="background-image: url('assets/img/menu_fundo.png');
                                  background-size: 750px 430px; background-repeat: no-repeat; ">
                            <script>
                                $(document).ready(function () {
                                    //first unbind the original click event
                                    PF('dlgEditar').closeIcon.unbind('click');

                                    //register your own
                                    PF('dlgEditar').closeIcon.click(function (e) {
                                        $("#formEditar\\:botaoFechar").click();
                                        //should be always called
                                        e.preventDefault();
                                    });


                                })
                            </script>
                            <f:facet name="header">
                                <img src="../assets/img/icone_usuarios.png" height="40" width="40"/> 
                                #{localemsgs.CadastrarUsuario}
                            </f:facet>
                            <p:panel id="editar" styleClass="painelCadastro">
                                <p:commandButton widgetVar="botaoFechar" style="display: none"
                                                 oncomplete="PF('dlgEditar').hide()" id="botaoFechar">
                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                                </p:commandButton>
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                </p:confirmDialog>
                                <p:tabView id="tabs" activeIndex="0" dynamic="true" orientation="left" onTabShow="PF('dlgEditar').initPosition()">
                                    <p:tab id="tabDados" title="#{localemsgs.Identificacao}">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="subFil" value="#{localemsgs.Filial}:"/>
                                            <p:selectOneMenu id="subFil" value="#{usuarios.filial}" converter="omnifaces.SelectItemsConverter"
                                                             filter="true" filterMatchMode="contains" 
                                                             style="width: 100%" disabled="#{usuarios.flag eq 2}">
                                                <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                               itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                                <p:ajax event="itemSelect" listener="#{usuarios.selecionarFilial}" 
                                                        update="formEditar:editar"/>
                                            </p:selectOneMenu>

                                            <p:outputLabel for="pessoa" value="#{localemsgs.Nome}:" rendered="#{!usuarios.cadastroNovaPessoa and usuarios.flag eq 1}"/>
                                            <p:autoComplete id="pessoa" value="#{usuarios.pessoa}" completeMethod="#{usuarios.listarQueryValida}"
                                                            required="true" label="#{localemsgs.Pessoa}"
                                                            rendered="#{!usuarios.cadastroNovaPessoa and usuarios.flag eq 1}"
                                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Pessoa}"  scrollHeight="200"
                                                            inputStyle="width: 100%" placeholder="#{localemsgs.Nome}" forceSelection="true"
                                                            var="ppl" itemLabel="#{ppl.nome}" itemValue="#{ppl}">
                                                <p:column>
                                                    <i class="fas fa-plus" style="#{ppl.codigo eq '-1' ? 'display: inline; font-size: 10px;' : 'display: none'}"></i>
                                                    <h:outputText value=" #{ppl.nome}" style="#{ppl.codigo eq '-1' ? 'font-weight: bold' : ''}"/>
                                                </p:column>
                                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{usuarios.listaPessoa}" />
                                                <p:ajax event="itemSelect" listener="#{usuarios.selecionarPessoa}" update="msgs formEditar:editar"/>
                                            </p:autoComplete>

                                            <p:outputLabel for="nome" value="#{localemsgs.Nome}:" rendered="#{usuarios.cadastroNovaPessoa or usuarios.flag eq 2}"/>
                                            <p:inputText value="#{usuarios.novo.pessoa.nome}" rendered="#{usuarios.cadastroNovaPessoa or usuarios.flag eq 2}" id="nome" style="width: 100%">
                                                <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                            </p:inputText>

                                            <p:outputLabel for="email" value="#{localemsgs.Email}:"/>
                                            <p:inputText value="#{usuarios.novo.pessoa.email}" id="email" style="width: 100%">
                                                <p:watermark for="email" value="#{localemsgs.Email}"/>
                                            </p:inputText>

                                            <p:outputLabel for="cpf" value="#{localemsgs.CPF}:"/>
                                            <p:inputMask id="cpf"  value="#{usuarios.novo.pessoa.CPF}" disabled="#{!usuarios.cadastroNovaPessoa}"
                                                         mask="#{mascaras.mascaraCPF}">
                                                <p:watermark for="cpf" value="#{localemsgs.CPF}"/>
                                            </p:inputMask>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">    
                                            <p:outputLabel for="nivel" value="#{localemsgs.Nivel}:"/>
                                            <p:selectOneMenu value="#{usuarios.novo.saspw.nivelx}" id="nivel"
                                                             required="true" label="#{localemsgs.Nivel}" style="width: 100%"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nivel}">
                                                <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                                <f:selectItems value="#{usuarios.niveis}" />
                                            </p:selectOneMenu>

                                            <p:outputLabel for="grupo" value="#{localemsgs.Grupo}:"/>
                                            <p:selectOneMenu value="#{usuarios.novo.grupo.codigo}" id="grupo" style="width: 100%"
                                                             required="true" label="#{localemsgs.Grupo}" filter="true" filterMatchMode="contains"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Grupo}">
                                                <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                                <f:selectItems value="#{usuarios.grupos}" var="grupo" itemValue="#{grupo.codigo}"
                                                               itemLabel="#{grupo.descricao}" noSelectionValue="Selecione"/>
                                            </p:selectOneMenu>

                                            <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:" />
                                            <p:selectOneMenu value="#{usuarios.novo.saspw.situacao}" id="situacao"
                                                             required="true" label="#{localemsgs.Situacao}" style="width: 100%"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}">
                                                <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                                <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                                <f:selectItem itemLabel="#{localemsgs.Bloqueado}" itemValue="B"/>
                                            </p:selectOneMenu>

                                            <p:outputLabel for="descricao" value="#{localemsgs.Descricao}:"/>
                                            <p:inputText id="descricao" value="#{usuarios.novo.saspw.descricao}"
                                                         label="#{localemsgs.Descricao} " style="width: 100%">
                                                <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                            </p:inputText>

                                            <p:outputLabel for="senha" value="#{localemsgs.Senha}:"/>
                                            <p:password id="senha" value="#{usuarios.novo.pessoa.PWWeb}" required="true" transient="true"
                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}" autocomplete="off"
                                                        label="#{localemsgs.Senha}" feedback="true" redisplay="true" match="confirmacao"
                                                        promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                                        goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                                        style="width: 100%">
                                                <f:validateRegex pattern="^[0-9]{5,20}$" for="senha"/>
                                                <p:watermark for="senha" value="#{localemsgs.Senha}"/>
                                            </p:password>

                                            <p:outputLabel for="confirmacao" value="#{localemsgs.Confirmacao}:" />
                                            <p:password id="confirmacao" value="#{usuarios.novo.pessoa.PWWeb}" redisplay="true"
                                                        label="#{localemsgs.Senha}" style="width: 100%">
                                                <p:watermark for="confirmacao" value="#{localemsgs.Senha}"/>
                                            </p:password>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="motivo" value="#{localemsgs.Motivo}:"/>
                                            <p:inputText id="motivo" value="#{usuarios.novo.saspw.motivo}"
                                                         label="#{localemsgs.Motivo}" style="width: 100%">
                                                <p:watermark for="motivo" value="#{localemsgs.Motivo}"/>
                                            </p:inputText>
                                        </p:panelGrid>
                                    </p:tab>
                                    
                                    <p:tab id="tabClientes" title="#{localemsgs.Clientes}">
                                        <p:panel id="cadastroCliente" style="max-width: 700px;">

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid"
                                                         styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="novoCliente" value="#{localemsgs.CadastrarCliente}:"/>
                                                <p:autoComplete id="novoCliente" value="#{usuarios.clienteAdicionar}" inputStyle="width: 100%"
                                                                completeMethod="#{usuarios.listarClientes}" minQueryLength="3" scrollHeight="200"
                                                                var="cliente" itemLabel="#{cliente.nomeCli}" itemValue="#{cliente}">
                                                    <o:converter converterId="omnifaces.ListIndexConverter" list="#{usuarios.listaClientesBusca}" />
                                                    <p:ajax event="itemSelect" listener="#{usuarios.adicionarCliente}" update="formEditar:tabs:cadastroCliente"/>
                                                </p:autoComplete>
                                            </p:panelGrid>

                                            <p:dataTable id="tabelaClientes" value="#{usuarios.listaClientes}"
                                                         style="width: 100%; min-heigh: 150px" var="cliente" rowKey="#{cliente.codCli}#{cliente.codFil}"
                                                         sortBy="#{cliente.nomeCli}" scrollable="true" scrollHeight="150" 
                                                         styleClass="tabela" resizableColumns="true"
                                                         emptyMessage="#{localemsgs.SemRegistros}"
                                                         rowStyleClass="#{cliente.flag_Excl eq '*' ? 'excluido' : null }">  
                                                <f:facet name="header">
                                                    <h:outputText value="#{localemsgs.ClientesCadastrados}"/>
                                                </f:facet>
                                                <p:column headerText="#{localemsgs.CodFil}" style="width: 50px">
                                                    <h:outputText value="#{cliente.codFil}" converter="conversor0" />
                                                </p:column>
                                                <p:column headerText="#{localemsgs.CodCli}" style="width: 58px">
                                                    <h:outputText value="#{cliente.codCli}" />
                                                </p:column>
                                                <p:column headerText="#{localemsgs.NRed}" style="width: 300px">
                                                    <h:outputText value="#{cliente.nomeCli}" />
                                                </p:column>
                                                <p:column style="width:35px">
                                                    <p:commandLink action="#{usuarios.removerCliente(cliente)}" update="msgs formEditar:tabs:tabelaClientes" 
                                                                   process="@this">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </p:commandLink>
                                                </p:column>
                                                <f:facet name="footer">
                                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-6,ui-grid-col-4,ui-grid-col-2"
                                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                                        <h:outputText id="qtdClientes" value="#{localemsgs.QtdClientes}: #{usuarios.listaClientes.size()}"
                                                                      style="font-size: 12px"/>

                                                        <p:outputLabel for="checkboxCliente" value="#{localemsgs.ExibirExcluidos}: "
                                                                       style="font-size: 12px"/>
                                                        <p:selectBooleanCheckbox id="checkboxCliente"
                                                                                 value="#{usuarios.clientesExcluidos}" style="font-size: 12px">
                                                            <p:ajax update="formEditar:tabs:cadastroCliente" listener="#{usuarios.atualizarListaClientes}" />
                                                        </p:selectBooleanCheckbox>
                                                    </p:panelGrid>
                                                </f:facet>
                                            </p:dataTable>
                                        </p:panel>
                                    </p:tab>
                                </p:tabView>

                                <p:commandButton action="#{usuarios.cadastrar}" update="msgs main:tabela" styleClass="botao"
                                               value="#{localemsgs.Cadastrar}" rendered="#{usuarios.flag eq 1}">
                                </p:commandButton> 

                                <p:commandButton action="#{usuarios.editar}" update="msgs main:tabela" styleClass="botao"
                                               value="#{localemsgs.Editar}" rendered="#{usuarios.flag eq 2}">
                                </p:commandButton>  
                            </p:panel>
                        </p:dialog>

                        <p:dialog header="#{localemsgs.AdicionarFilial}"
                                  widgetVar="dlgServicos" closable="true" resizable="false" width="400" height="50"
                                  hideEffect="fade">
                            <p:outputPanel id="panelServicos">


                            </p:outputPanel>
                        </p:dialog>
                    </h:form>
                </ui:define>
            </ui:composition>
        </h:body>
    </f:view>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FPLancamentos {

    private BigDecimal CodFil;
    private BigDecimal CodMovFP;
    private String TipoFP;
    private BigDecimal Matr;
    private String Verba;
    private String Tipo;
    private String Calculo;
    private BigDecimal Valor;
    private BigDecimal ValorCalc;
    private String TipoPgto;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;
    private String Operador_Exc;
    private LocalDate Dt_Exc;
    private String Hr_Exc;
    private String Flag_Excl;

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the CodMovFP
     */
    public BigDecimal getCodMovFP() {
        return CodMovFP;
    }

    /**
     * @param CodMovFP the CodMovFP to set
     */
    public void setCodMovFP(String CodMovFP) {
        try {
            this.CodMovFP = new BigDecimal(CodMovFP);
        } catch (Exception e) {
            this.CodMovFP = new BigDecimal("0");
        }
    }

    /**
     * @return the TipoFP
     */
    public String getTipoFP() {
        return TipoFP;
    }

    /**
     * @param TipoFP the TipoFP to set
     */
    public void setTipoFP(String TipoFP) {
        this.TipoFP = TipoFP;
    }

    /**
     * @return the Matr
     */
    public BigDecimal getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    /**
     * @return the Verba
     */
    public String getVerba() {
        return Verba;
    }

    /**
     * @param Verba the Verba to set
     */
    public void setVerba(String Verba) {
        this.Verba = Verba;
    }

    /**
     * @return the Tipo
     */
    public String getTipo() {
        return Tipo;
    }

    /**
     * @param Tipo the Tipo to set
     */
    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    /**
     * @return the Calculo
     */
    public String getCalculo() {
        return Calculo;
    }

    /**
     * @param Calculo the Calculo to set
     */
    public void setCalculo(String Calculo) {
        this.Calculo = Calculo;
    }

    /**
     * @return the Valor
     */
    public BigDecimal getValor() {
        return Valor;
    }

    /**
     * @param Valor the Valor to set
     */
    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    /**
     * @return the ValorCalc
     */
    public BigDecimal getValorCalc() {
        return ValorCalc;
    }

    /**
     * @param ValorCalc the ValorCalc to set
     */
    public void setValorCalc(String ValorCalc) {
        try {
            this.ValorCalc = new BigDecimal(ValorCalc);
        } catch (Exception e) {
            this.ValorCalc = new BigDecimal("0");
        }
    }

    /**
     * @return the TipoPgto
     */
    public String getTipoPgto() {
        return TipoPgto;
    }

    /**
     * @param TipoPgto the TipoPgto to set
     */
    public void setTipoPgto(String TipoPgto) {
        this.TipoPgto = TipoPgto;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    /**
     * @return the Operador_Exc
     */
    public String getOperador_Exc() {
        return Operador_Exc;
    }

    /**
     * @param Operador_Exc the Operador_Exc to set
     */
    public void setOperador_Exc(String Operador_Exc) {
        this.Operador_Exc = Operador_Exc;
    }

    /**
     * @return the Dt_Exc
     */
    public LocalDate getDt_Exc() {
        return Dt_Exc;
    }

    /**
     * @param Dt_Exc the Dt_Exc to set
     */
    public void setDt_Exc(LocalDate Dt_Exc) {
        this.Dt_Exc = Dt_Exc;
    }

    /**
     * @return the Hr_Exc
     */
    public String getHr_Exc() {
        return Hr_Exc;
    }

    /**
     * @param Hr_Exc the Hr_Exc to set
     */
    public void setHr_Exc(String Hr_Exc) {
        this.Hr_Exc = Hr_Exc;
    }

    /**
     * @return the Flag_Excl
     */
    public String getFlag_Excl() {
        return Flag_Excl;
    }

    /**
     * @param Flag_Excl the Flag_Excl to set
     */
    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }
}

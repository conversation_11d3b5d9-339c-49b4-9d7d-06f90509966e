/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RHPontoDet;

/**
 *
 * <AUTHOR>
 */
public class RHPontoDetDao {

    /**
     * Insere os detalhes de uma batida
     *
     * @param rHPontoDet
     * @param persistencia
     * @throws Exception
     */
    public void inserirRHPontoDet(RHPontoDet rHPontoDet, Persistencia persistencia) throws Exception {
        try {
            //tratamento Secao em branco Carlos 23/03/2023
            String vMatr = "", vSecao = "", vSQL = "";
            vSecao = rHPontoDet.getSecao();
            vMatr = rHPontoDet.getMatr();
            Consulta consulta, consultafun;
            if (vSecao.equals("")){
                vSQL = "Select Secao from Funcion "+
                       " where Matr = "+ vMatr;
                consultafun = new Consulta(vSQL, persistencia);                
                consultafun.select();
                if (consultafun.Proximo()){
                    vSecao = consultafun.getString("Secao");
                }
            }
            String sql = " INSERT INTO RHPontoDet (Matr, DtCompet, Batida, Secao, Local) values (?, ?, ?, ?, ?) ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(vMatr);
            consulta.setString(rHPontoDet.getDtCompet());
            consulta.setString(rHPontoDet.getBatida());
            consulta.setString(vSecao);
            consulta.setString(rHPontoDet.getLocal());
            consulta.insert();
        } catch (Exception e) {
            throw new Exception("RHPontoDetDao.inserirRHPontoDet - " + e.getMessage() + "\r\n"
                    + "INSERT INTO RHPontoDet (Matr, DtCompet, Batida, Secao, Local) values (" + rHPontoDet.getMatr() + ", "
                    + " " + rHPontoDet.getDtCompet() + ", " + rHPontoDet.getBatida() + ", " + rHPontoDet.getSecao() + ", " + rHPontoDet.getLocal() + ")");
        }
    }
}

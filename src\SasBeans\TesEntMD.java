package SasBeans;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class TesEntMD {

    private BigDecimal Guia;
    private String Serie;
    private BigInteger Codigo;
    private String Docto;
    private BigDecimal Qtde;
    private BigDecimal Valor;
    private String IDK7;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        try {
            this.Guia = new BigDecimal(Guia);
        } catch (Exception e) {
            this.Guia = new BigDecimal("0");
        }
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public BigInteger getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigInteger(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigInteger("0");
        }
    }

    public String getDocto() {
        return Docto;
    }

    public void setDocto(String Docto) {
        this.Docto = Docto;
    }

    public BigDecimal getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        try {
            this.Qtde = new BigDecimal(Qtde);
        } catch (Exception e) {
            this.Qtde = new BigDecimal("0");
        }
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public String getIDK7() {
        return IDK7;
    }

    public void setIDK7(String IDK7) {
        this.IDK7 = IDK7;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class MobilePTT {

    private String Nome;
    private BigDecimal Sequencia;
    private String Data;
    private String Hora;
    private String Paramet;
    private BigDecimal CodPessoa;
    private BigDecimal CodpessoaDest;
    private BigDecimal GrupoDest;
    private int Recebido;
    private BigDecimal CodPessoaRec;
    private BigDecimal SeqChamada;
    private String arquivo;

    public MobilePTT() {
        Nome = "";
        Sequencia = null;
        Data = null;
        Hora = "";
        Paramet = "";
        CodPessoa = null;
        CodpessoaDest = null;
        GrupoDest = null;
        Recebido = 0;
        CodPessoaRec = null;
        SeqChamada = null;
        arquivo = "";
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getParamet() {
        return Paramet;
    }

    public void setParamet(String Paramet) {
        this.Paramet = Paramet;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public BigDecimal getCodpessoaDest() {
        return CodpessoaDest;
    }

    public void setCodpessoaDest(BigDecimal CodpessoaDest) {
        this.CodpessoaDest = CodpessoaDest;
    }

    public BigDecimal getGrupoDest() {
        return GrupoDest;
    }

    public void setGrupoDest(BigDecimal GrupoDest) {
        this.GrupoDest = GrupoDest;
    }

    public int getRecebido() {
        return Recebido;
    }

    public void setRecebido(int Recebido) {
        this.Recebido = Recebido;
    }

    public BigDecimal getCodPessoaRec() {
        return CodPessoaRec;
    }

    public void setCodPessoaRec(BigDecimal CodPessoaRec) {
        this.CodPessoaRec = CodPessoaRec;
    }

    public BigDecimal getSeqChamada() {
        return SeqChamada;
    }

    public void setSeqChamada(BigDecimal SeqChamada) {
        this.SeqChamada = SeqChamada;
    }

    public String getArquivo() {
        return arquivo;
    }

    public void setArquivo(String arquivo) {
        this.arquivo = arquivo;
    }

}

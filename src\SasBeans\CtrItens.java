/*
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CtrItens {

    private String CHFuncion;
    private String CHMensal;
    private String CHSeman;
    private String Cargo;
    private String CodCargo;
    private String CodFil;
    private String CodTipo;
    private String Contrato;
    private String Descricao;
    private String Dt_Alter;
    private String FranquiaAst;
    private String FranquiaEsp;
    private String FranquiaEve;
    private String FranquiaRot;
    private String HEDiurna2;
    private String HEDiurna;
    private String HENoturna2;
    private String HENoturna;
    private String Horas;
    private String Hr_Alter;
    private String HsExtras;
    private String HsNormais;
    private String IndiceHE;
    private String Operador;
    private String QtdeFunc;
    private String QtdePreench;
    private String Reforco;
    private String Salario;
    private String TipoCalc;
    private String TipoPosto;
    private String ValorAst;
    private String ValorEsp;
    private String ValorEve;
    private String ValorPosto;
    private String ValorRot;
    private int InibirReajuste;

    public CtrItens() {
        this.Contrato = "";
        this.CodFil = "0";
        this.TipoPosto = "";
        this.Descricao = "";
        this.Horas = "0";
        this.ValorPosto = "0";
        this.HEDiurna = "0";
        this.HENoturna = "0";
        this.HEDiurna2 = "0";
        this.HENoturna2 = "0";
        this.Reforco = "0";
        this.CodTipo = "0";
        this.Operador = "";
        this.Dt_Alter = null;
        this.Hr_Alter = "";
        this.CHSeman = "0";
        this.CHMensal = "0";
        this.CHFuncion = "0";
        this.QtdeFunc = "0";
        this.Salario = "0";
        this.HsNormais = "0";
        this.HsExtras = "0";
        this.IndiceHE = "0";
        this.TipoCalc = "";
        this.ValorRot = "0";
        this.FranquiaRot = "0";
        this.ValorEve = "0";
        this.FranquiaEve = "0";
        this.ValorEsp = "0";
        this.FranquiaEsp = "0";
        this.ValorAst = "0";
        this.FranquiaAst = "0";
        this.Cargo = "";
        this.CodCargo = "0";
        this.QtdePreench = "0";
        this.InibirReajuste = 0;
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getTipoPosto() {
        return TipoPosto;
    }

    public void setTipoPosto(String TipoPosto) {
        this.TipoPosto = TipoPosto;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getHoras() {
        return Horas;
    }

    public void setHoras(String Horas) {
        this.Horas = Horas;
    }

    public String getValorPosto() {
        return ValorPosto;
    }

    public void setValorPosto(String ValorPosto) {
        this.ValorPosto = ValorPosto;
    }

    public String getHEDiurna() {
        return HEDiurna;
    }

    public void setHEDiurna(String HEDiurna) {
        this.HEDiurna = HEDiurna;
    }

    public String getHENoturna() {
        return HENoturna;
    }

    public void setHENoturna(String HENoturna) {
        this.HENoturna = HENoturna;
    }

    public String getHEDiurna2() {
        return HEDiurna2;
    }

    public void setHEDiurna2(String HEDiurna2) {
        this.HEDiurna2 = HEDiurna2;
    }

    public String getHENoturna2() {
        return HENoturna2;
    }

    public void setHENoturna2(String HENoturna2) {
        this.HENoturna2 = HENoturna2;
    }

    public String getReforco() {
        return Reforco;
    }

    public void setReforco(String Reforco) {
        this.Reforco = Reforco;
    }

    public String getCodTipo() {
        return CodTipo;
    }

    public void setCodTipo(String CodTipo) {
        this.CodTipo = CodTipo;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getCHSeman() {
        return CHSeman;
    }

    public void setCHSeman(String CHSeman) {
        this.CHSeman = CHSeman;
    }

    public String getCHMensal() {
        return CHMensal;
    }

    public void setCHMensal(String CHMensal) {
        this.CHMensal = CHMensal;
    }

    public String getCHFuncion() {
        return CHFuncion;
    }

    public void setCHFuncion(String CHFuncion) {
        this.CHFuncion = CHFuncion;
    }

    public String getQtdeFunc() {
        return QtdeFunc;
    }

    public void setQtdeFunc(String QtdeFunc) {
        this.QtdeFunc = QtdeFunc;
    }

    public String getSalario() {
        return Salario;
    }

    public void setSalario(String Salario) {
        this.Salario = Salario;
    }

    public String getHsNormais() {
        return HsNormais;
    }

    public void setHsNormais(String HsNormais) {
        this.HsNormais = HsNormais;
    }

    public String getHsExtras() {
        return HsExtras;
    }

    public void setHsExtras(String HsExtras) {
        this.HsExtras = HsExtras;
    }

    public String getIndiceHE() {
        return IndiceHE;
    }

    public void setIndiceHE(String IndiceHE) {
        this.IndiceHE = IndiceHE;
    }

    public String getTipoCalc() {
        return TipoCalc;
    }

    public void setTipoCalc(String TipoCalc) {
        this.TipoCalc = TipoCalc;
    }

    public String getValorRot() {
        return ValorRot;
    }

    public void setValorRot(String ValorRot) {
        this.ValorRot = ValorRot;
    }

    public String getFranquiaRot() {
        return FranquiaRot;
    }

    public void setFranquiaRot(String FranquiaRot) {
        this.FranquiaRot = FranquiaRot;
    }

    public String getValorEve() {
        return ValorEve;
    }

    public void setValorEve(String ValorEve) {
        this.ValorEve = ValorEve;
    }

    public String getFranquiaEve() {
        return FranquiaEve;
    }

    public void setFranquiaEve(String FranquiaEve) {
        this.FranquiaEve = FranquiaEve;
    }

    public String getValorEsp() {
        return ValorEsp;
    }

    public void setValorEsp(String ValorEsp) {
        this.ValorEsp = ValorEsp;
    }

    public String getFranquiaEsp() {
        return FranquiaEsp;
    }

    public void setFranquiaEsp(String FranquiaEsp) {
        this.FranquiaEsp = FranquiaEsp;
    }

    public String getValorAst() {
        return ValorAst;
    }

    public void setValorAst(String ValorAst) {
        this.ValorAst = ValorAst;
    }

    public String getFranquiaAst() {
        return FranquiaAst;
    }

    public void setFranquiaAst(String FranquiaAst) {
        this.FranquiaAst = FranquiaAst;
    }

    public String getCargo() {
        return Cargo;
    }

    public void setCargo(String Cargo) {
        this.Cargo = Cargo;
    }

    public String getCodCargo() {
        return CodCargo;
    }

    public void setCodCargo(String CodCargo) {
        this.CodCargo = CodCargo;
    }

    public String getQtdePreench() {
        return QtdePreench;
    }

    public void setQtdePreench(String QtdePreench) {
        this.QtdePreench = QtdePreench;
    }

    public int getInibirReajuste() {
        return InibirReajuste;
    }

    public void setInibirReajuste(int InibirReajuste) {
        this.InibirReajuste = InibirReajuste;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 79 * hash + Objects.hashCode(this.Contrato);
        hash = 79 * hash + Objects.hashCode(this.CodFil);
        hash = 79 * hash + Objects.hashCode(this.TipoPosto);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CtrItens other = (CtrItens) obj;
        if (!Objects.equals(this.Contrato, other.Contrato)) {
            return false;
        }
        if (!Objects.equals(this.TipoPosto, other.TipoPosto)) {
            return false;
        }
        if (!Objects.equals(this.CodFil.replaceAll(".0", ""), other.CodFil.replaceAll(".0", ""))) {
            return false;
        }
        return true;
    }
}

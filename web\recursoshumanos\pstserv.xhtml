<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/pstserv.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />

            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }

                    .tabelaInspecoes [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    /*@media only screen and (max-width: 35em) and (min-width: 10px) {*/

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    /*@media only screen and (max-width: 2000px) and (min-width: 35em) {*/
                    .DataGrid, .tabelaInspecoes{
                        width:100% !important;
                        border: none !important;
                    }

                    .DataGrid tbody tr td , .tabelaInspecoes body tr td{
                        white-space: nowrap;
                        overflow:hidden;
                        text-overflow:ellipsis;
                        text-align: center;
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid tbody tr td:nth-child(1){
                        min-width:70px !important;
                        max-width:70px !important;
                    }

                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2){
                        min-width: 200px !important;
                        max-width: 200px !important;
                    }

                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid tbody tr td:nth-child(3){
                        min-width: 350px !important;
                        max-width: 350px !important;
                        text-align: left;
                    }

                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid tbody tr td:nth-child(4){
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }

                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid tbody tr td:nth-child(5){
                        min-width: 300px !important;
                        max-width: 300px !important;
                        text-align: left;
                    }

                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid tbody tr td:nth-child(6){
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }

                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7){
                        min-width: 150px !important;
                        max-width: 150px !important;
                    }

                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid tbody tr td:nth-child(8){
                        min-width: 150px !important;
                        max-width: 150px !important;
                    }

                    .DataGrid thead tr th:nth-child(9),
                    .DataGrid tbody tr td:nth-child(9){
                        min-width: 200px !important;
                        max-width: 200px !important;
                    }

                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid tbody tr td:nth-child(10){
                        min-width: 150px !important;
                        max-width: 150px !important;
                    }

                    .DataGrid thead tr th:nth-child(11),
                    .DataGrid tbody tr td:nth-child(11){
                        min-width: 150px !important;
                        max-width: 150px !important;
                    }

                    .tabelaInspecoes thead tr th:nth-child(1),
                    .tabelaInspecoes tbody tr td:nth-child(1){
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }

                    .tabelaInspecoes thead tr th:nth-child(2),
                    .tabelaInspecoes tbody tr td:nth-child(2){
                        min-width: 200px !important;
                        max-width: 200px !important;
                    }

                    .tabelaInspecoes thead tr th:nth-child(3),
                    .tabelaInspecoes tbody tr td:nth-child(3){
                        min-width: 250px !important;
                        max-width: 250px !important;
                    }

                    .tabelaInspecoes thead tr th:nth-child(4),
                    .tabelaInspecoes tbody tr td:nth-child(4){
                        min-width: 150px !important;
                        max-width: 150px !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                .ui-radiobutton {
                    background: inherit !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .panelModal {
                    background-color: #EEE;
                }

                [id*="tabDados"],
                [id*="supervisoes"],
                [id*="relatorios"],
                [id*="pstdepen"],
                [id*="pontos"],
                [id*="rondas"],
                [id*="documentos"]{
                    background-color:#FFF !important;
                    padding-right: 0px!important;
                }


                div[id*="pnlHTML"] table{
                    padding:0px !important;
                    border-spacing:0px !important;
                }

                div[id*="pnlHTML"] table tbody tr td img{
                    margin-right:12px;
                }

                div[id*="pnlHTML"] table tbody tr td span,
                div[id*="pnlHTML"] table tbody tr td span strong{
                    font-size:8pt !important;
                    padding:0px !important;
                }

                div[id*="pnlHTML"] table tbody tr{
                    /*min-height:5px !important;
                    height:5px !important;
                    max-height:5px !important;
                    line-height:5px !important;*/
                }

                div[id*="pnlHTML"] table tbody tr td{
                    padding:0px !important;
                    min-height:5px !important;
                    height:5px !important;
                    max-height:5px !important;
                    line-height:5px !important;
                    vertical-align: middle !important;
                }

                div[id*="pnlHTML"] table tbody tr{
                    padding:0px !important;
                    line-height:5px !important;
                    height:5px !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td,
                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr{
                    padding-top: 0px !important;
                    padding-bottom: 0px !important;
                    height:auto !important;
                }

                div[id*="pnlHTML"] table tbody tr td table tbody tr td span{
                    /*width:100px !important;
                    white-space:nowrap;
                    overflow:hidden !important;
                    text-overflow:ellipsis !important;*/
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:first-child td{
                    background-color:#3C8DBC !important;
                    color:#FFF !important;
                    padding-bottom:0px !important;
                    height:auto !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td{
                    padding-top: 6px !important;
                    padding-left: 6px !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table{
                    border:thin solid #BBB !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:nth-child(odd):not(:first-child) td{
                    background-color:whitesmoke !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td:first-child{
                    text-align:right !important;
                    padding-right: 6px !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td span{
                    margin-top: 8px !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table,
                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr,
                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td{
                    vertical-align:middle !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td{
                    height:10px !important;
                    line-height:10px !important;
                    max-height:10px !important;
                }

                div[id*="pnlHTML"][class*="INSP"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td,
                div[id*="pnlHTML"][class*="RO"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td,
                div[id*="pnlHTML"][class*="SUP"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td{
                    display:none !important;
                }

                div[id*="pnlHTML"]{
                    height:100% !important;
                    overflow:hidden !important;
                    overflow-y:auto !important;
                }

                div[id*="pnlHTMLpai"]{
                    height:436px !important;
                }

                #divMapaFull{
                    position:absolute;
                    width:100%;
                    height:100%;
                    top:0px;
                    left:0px;
                    background-color:#FFF;
                    z-index:9999 !important;
                    padding:0px !important;
                }

                [id*="formBoletimTrabalho"] .ui-state-focus{
                    box-shadow: none !important;
                    outline: none !important;
                }

                [id*="formBoletimTrabalho"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color:#EEE !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="formBoletimTrabalho"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color:#FFF !important;
                }
                
                [id*="relatorio"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color: #EEE !important;
                    border-bottom-color: #CCC !important;
                    height: 70px !important;
                }
                
                [id*="relatorio"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color: #FFF !important;
                }
                
                [id*="dlgRelatorio_content"]{
                    background-color: transparent !important;
                }
                
                [id*="formEditar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color: #FFF !important;
                    border-bottom-color: #CCC !important;
                    height: 70px !important;
                }
                
                [id*="formEditar"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color: #EEE !important;
                }
                
                [id*="dlgEditar_content"]{
                    background-color: transparent !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{postoservico.Persistencia(login.pp)}" />
            </f:metadata>
            <p:growl id="msgs" />

            <div id="divMapaFull" style="display:none"></div>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_postosdeservico.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.PstServ}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{postoservico.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">#{postoservico.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                        <label class="FilialEndereco">#{postoservico.filiais.endereco}</label>
                                        <label class="FilialBairroCidade">#{postoservico.filiais.bairro}, #{postoservico.filiais.cidade}/#{postoservico.filiais.UF}</label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="c" update="msgs formEditar" actionListener="#{postoservico.NovoPosto}" oncomplete="PF('dlgEditar').show();"/>
                    <p:hotkey bind="p" update="formPesquisaRapida" oncomplete="PF('dlgPesquisaRapida').show();" actionListener="#{postoservico.PrePesquisa}"/>
                    <p:hotkey bind="e" update="msgs formEditar" actionListener="#{postoservico.buttonAction}"/>
                    <p:hotkey bind="shift+x"  oncomplete="PF('dlgExportar').show();" actionListener="#{exportarMB.setTitulo(localemsgs.PstServ)}"/>

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela"
                                                 value="#{postoservico.allPostos}"
                                                 paginator="true"
                                                 rows="15"
                                                 lazy="true"
                                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.PstServ}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista"
                                                 selectionMode="single"
                                                 reflow="true"
                                                 selection="#{postoservico.selecionado}"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true"
                                                 class="tabela DataGrid"
                                                 scrollHeight="100%"
                                                 style="font-size: 12px; background: white"
                                                 >
                                        <p:ajax event="rowDblselect" listener="#{postoservico.dblSelect}" update="formEditar msgs"/>
                                        <p:column headerText="#{localemsgs.CodFil}" class="text-center" exportable="#{postoservico.eFilial}">
                                            <h:outputText value="#{lista.codFil}" title="#{lista.codFil}">
                                                <f:convertNumber pattern="0000" />
                                            </h:outputText>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Secao}" exportable="#{postoservico.eCodPosto}">
                                            <h:outputText value="#{lista.secao}" title="#{lista.secao}" />
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Local}" exportable="#{postoservico.ePosto}">
                                            <h:outputText value="#{lista.local}" title="#{lista.local}" />
                                        </p:column>

                                        <p:column headerText="#{localemsgs.TipoPosto}" exportable="#{postoservico.eTipo}">
                                            <h:outputText value="#{lista.tipoPosto}" title="#{lista.tipoPosto}" />
                                        </p:column>

                                        <p:column headerText="#{localemsgs.TipoPostoDesc}" exportable="#{postoservico.eDescTipo}">
                                            <h:outputText value="#{lista.tipoPostoDesc}" title="#{lista.tipoPostoDesc}"/>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Situacao}" exportable="#{postoservico.eSituacao}">
                                            <h:outputText value="#{lista.situacao}" title="#{lista.situacao}" />
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Dt_Situacao}" exportable="#{postoservico.eDtSituacao}">
                                            <h:outputText value="#{lista.dt_Situacao}" converter="conversorData" />
                                        </p:column>

                                        <p:column headerText="#{localemsgs.InterfExt}" exportable="#{postoservico.eInterfExt}">
                                            <h:outputText value="#{lista.interfExt}" title="#{lista.interfExt}" />
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Operador}" exportable="#{postoservico.eOperador}">
                                            <h:outputText value="#{lista.operador}" title="#{lista.operador}" />
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Dt_Alter}" exportable="#{postoservico.eDtAlter}">
                                            <h:outputText value="#{lista.dt_Alter}" converter="conversorData"/>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Hr_Alter}" exportable="#{postoservico.eHrAlter}">
                                            <h:outputText value="#{lista.hr_Alter}" title="#{lista.hr_Alter}" converter="conversorHora" />
                                        </p:column>
                                    </p:dataTable>

                                    <script>
                                        // <![CDATA[
                                        (function () {
                                            function responsividade() {
                                                if ($(window).width() <= 640) {
                                                    $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                                } else {
                                                    $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                                }
                                            }

                                            $(document).ready(responsividade);
                                            $(window).resize(responsividade);
                                        })();
                                        // ]]>
                                    </script>

                                </p:panel>
                            </div>
                        </div>
                    </div>
                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 100px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Cadastrar}" update="msgs formEditar"
                                           actionListener="#{postoservico.NovoPosto}"
                                           oncomplete="PF('dlgEditar').show();">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" update="msgs formEditar"
                                           actionListener="#{postoservico.buttonAction}">
                                <p:graphicImage  url="../assets/img/icone_redondo_editar.png" height="40" />
                            </p:commandLink>
                        </div>

                        <div style="display: none; padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" update="formPesquisar"
                                           oncomplete="PF('dlgPesquisar').show();" actionListener="#{postoservico.PrePesquisa}">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40" />
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" update="formPesquisaRapida"
                                           oncomplete="PF('dlgPesquisaRapida').show();" actionListener="#{postoservico.PrePesquisa}">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40" />
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{postoservico.LimparFiltros}"
                                           update="msgs main:tabela cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.exportar}" oncomplete="PF('dlgExportar').show();"
                                           actionListener="#{exportarMB.setTitulo(localemsgs.PstServ)}">
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <!-- Editar Posto de Serviço -->
                <h:form id="formEditar" class="form-inline" style="background-color:#EEE !important;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgEditar').hide()"/>
                    <p:dialog widgetVar="dlgEditar" positionType="absolute"
                              fitViewport="true" draggable="false" modal="true"
                              closable="true" resizable="false" dynamic="true"
                              responsive="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="false" id="dlgEditar"
                              style="max-width:96% !important;
                              border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;
                              border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important;
                              padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgEditar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgEditar').closeIcon.click(function (e) {
                                    $("#formEditar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>

                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgEditar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_postosdeservico.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarPstServ}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel styleClass="panelModal">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="subFil" value="#{localemsgs.Filial}: "/>
                                <p:selectOneMenu id="subFil" value="#{postoservico.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 disabled="#{postoservico.flag eq 2}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                    <p:ajax event="itemSelect" listener="#{postoservico.SelecionarFilial}" resetValues="true"
                                            update="cliente nomeCliente endereco cidadePais
                                            contrato posto tipoPosto"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-1,ui-grid-col-1,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="contrato" value="#{localemsgs.Contrato}: " />
                                <p:autoComplete id="contrato" required="true" styleClass="contrato"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Contrato}"
                                                maxlength="60" scrollHeight="200"
                                                value="#{postoservico.novo.descContrato}" style="width: 100%"
                                                completeMethod="#{postoservico.BuscarContratos}" forceSelection="true" >
                                    <p:ajax event="itemSelect" listener="#{postoservico.SelecionarContrato}"
                                            update="contrato posto"/>
                                    <p:watermark for="contrato" value="#{localemsgs.Contrato}" />
                                </p:autoComplete>

                                <p:outputLabel for="tipoPosto" value="#{localemsgs.TipoPosto}:" />
                                <p:inputText id="tipoPosto" value="#{postoservico.novo.tipoPosto}" required="true"
                                             label="#{localemsgs.TipoPosto}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.tipoPosto}" disabled="true">
                                    <p:watermark for="tipoPosto" value="#{localemsgs.TipoPosto}" />
                                </p:inputText>

                                <p:autoComplete id="posto" required="true" value="#{postoservico.novo.tipoPostoDesc}"
                                                disabled="#{postoservico.novo.descContrato eq ''}" styleClass="posto"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoPostoDesc}"
                                                style="width: 100%" scrollHeight="200"
                                                completeMethod="#{postoservico.ListarTipos}" forceSelection="true"
                                                var="posto" itemLabel="#{posto}" itemValue="#{posto}">
                                    <p:ajax event="itemSelect" listener="#{postoservico.SelecionarTipo}"
                                            update="tipoPosto posto"/>
                                    <p:watermark for="posto" value="#{localemsgs.TipoPostoDesc}" />
                                </p:autoComplete>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="local" value="#{localemsgs.Local}: "/>

                                <p:inputText id="local" value="#{postoservico.novo.local}" required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Local}"
                                             style="width: 100%" maxlength="60" >
                                    <p:watermark for="local" value="#{localemsgs.Local}" />
                                </p:inputText>

                                <p:outputLabel for="codposto" value="#{localemsgs.Codigo}: "/>
                                <p:inputText id="codposto" value="#{postoservico.novo.secao}" disabled="true"
                                             style="width: 100%" maxlength="60">
                                    <p:watermark for="codposto" value="#{localemsgs.Codigo}" />
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-6"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="cliente" value="#{localemsgs.Cliente}:"/>
                                <p:autoComplete id="cliente" value="#{postoservico.clientes}" styleClass="cliente"
                                                style="width: 100%"
                                                completeMethod="#{postoservico.ListarClientes}" required="true" scrollHeight="200"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}" forceSelection="true"
                                                var="cli" itemLabel="#{cli.NRed}" itemValue="#{cli}" converter="conversorCliente">
                                    <p:ajax event="itemSelect" listener="#{postoservico.SelecionarCliente}"
                                            update="endereco cidadePais nomeCliente"/>
                                    <p:watermark for="cliente" value="#{localemsgs.Cliente}" />
                                </p:autoComplete>

                                <p:inputText id="nomeCliente" value="#{postoservico.clientes.nome}" disabled="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}" required="true"
                                             style="width: 100%">
                                    <p:watermark for="nomeCliente" value="#{localemsgs.Cliente}" />
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="endereco" value="#{localemsgs.Endereco}:"/>
                                <p:inputText id="endereco" value="#{postoservico.endereco}" disabled="true" style="width: 100%">
                                    <p:watermark for="endereco" value="#{localemsgs.Endereco}" />
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <div/>
                                <p:inputText id="cidadePais" value="#{postoservico.cidadePais}" disabled="true"
                                             style="width: 100%">
                                    <p:watermark for="cidadePais" value="#{localemsgs.Endereco}" />
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:"/>
                                <p:selectOneRadio id="situacao" value="#{postoservico.novo.situacao}"
                                                  style="width:100%;" >
                                    <f:selectItem  itemLabel="#{localemsgs.Ativo}" itemValue="A" />
                                    <f:selectItem itemLabel="#{localemsgs.Inativo}" itemValue="I" />
                                </p:selectOneRadio>

                                <p:outputLabel for="interfext" value="#{localemsgs.InterfExt}: "/>
                                <p:inputText id="interfext" value="#{postoservico.novo.interfExt}" label="#{localemsgs.InterfExt}"
                                             style="width: 100%">
                                    <p:watermark for="interfext" value="#{localemsgs.InterfExt}" />
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:commandLink id="btnEditar" action="#{postoservico.Editar}" update=":msgs :main:tabela"
                                               title="#{localemsgs.Editar}" rendered="#{postoservico.flag eq 2}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40"/>
                                </p:commandLink>
                                <p:commandLink id="btnCadastrar" action="#{postoservico.Cadastrar}" update=":msgs :main:tabela"
                                               title="#{localemsgs.Cadastrar}" rendered="#{postoservico.flag eq 1}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </p:panelGrid>
                        </p:panel>

                        <p:panel id="editar" style="background-color: transparent" styleClass="cadastrar">
                            <p:tabView id="tabs" activeIndex="0" onTabShow="PF('dlgEditar').initPosition()" dynamic="true" style="background-color:#EEE !important;">
                                <p:tab id="inspecoes" title="#{localemsgs.Inspecoes}" disabled="#{postoservico.flag eq 1}">
                                    <p:panel id="panelInspecoes" style="background: transparent">
                                        <div style="width: 100%">
                                            <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                                        </div>

                                        <div style="width: 100%">
                                            <p:outputLabel for="dataInspecao1" value="#{localemsgs.DataInicial}: "/>
                                            <p:spacer width="10px"/>

                                            <p:inputMask id="dataInspecao1" value="#{postoservico.dataInspecao1}" mask="99/99/9999" style="width: 90px;" converter="conversorData"/>
                                            <p:spacer width="10px"/>

                                            <p:outputLabel for="dataInspecao2" value="#{localemsgs.DataFinal}: "/>
                                            <p:spacer width="10px"/>

                                            <p:inputMask id="dataInspecao2" value="#{postoservico.dataInspecao2}" mask="99/99/9999" style="width: 90px;" converter="conversorData"/>
                                            <p:spacer width="10px"/>

                                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                                           update="tabelaInspecao msgs"
                                                           action="#{postoservico.listarInspecoesPosto}">
                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                            </p:commandLink>
                                        </div>

                                        <div style="width: 100%;">
                                            <p:dataTable id="tabelaInspecao"
                                                         value="#{postoservico.listaLogsSatMobEW}"
                                                         style="font-size: 12px;"
                                                         var="lista"
                                                         rowKey="#{lista.chave}"
                                                         resizableColumns="true" selectionMode="single"
                                                         scrollable="true" scrollWidth="100%" scrollHeight="200"
                                                         class="tabela tabelaInspecoes"
                                                         emptyMessage="#{localemsgs.SemRegistros}"
                                                         selection="#{postoservico.logsSatMobEWSelecionado}">
                                                <p:ajax event="rowDblselect" listener="#{postoservico.mostrarInspecoes}"
                                                        update="relatorio"/>
                                                <p:column headerText="#{localemsgs.Data}">
                                                    <h:outputText value="#{lista.data}" title="#{lista.data}"
                                                                  converter="conversorData"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Posto}">
                                                    <h:outputText value="#{lista.posto}" title="#{lista.posto}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Titulo2}">
                                                    <h:outputText value="#{lista.titulo}" title="#{lista.titulo}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Operador}">
                                                    <h:outputText value="#{lista.funcionario}" title="#{lista.funcionario}"/>
                                                </p:column>
                                            </p:dataTable>
                                        </div>
                                    </p:panel>
                                </p:tab>

                                <p:tab id="supervisoes" title="#{localemsgs.Supervisoes}" disabled="#{postoservico.flag eq 1}">
                                    <p:panel id="panelSupervisoes" style="background: transparent">
                                        <div style="width: 100%">
                                            <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                                        </div>

                                        <div style="width: 100%">
                                            <p:outputLabel for="dataSupervisao1" value="#{localemsgs.DataInicial}: "/>
                                            <p:spacer width="10px"/>

                                            <p:inputMask id="dataSupervisao1" value="#{postoservico.dataSupervisao1}" mask="99/99/9999" style="width: 90px;"/>
                                            <p:spacer width="10px"/>

                                            <p:outputLabel for="dataSupervisao2" value="#{localemsgs.DataFinal}: "/>
                                            <p:spacer width="10px"/>

                                            <p:inputMask id="dataSupervisao2" value="#{postoservico.dataSupervisao2}" mask="99/99/9999" style="width: 90px;"/>
                                            <p:spacer width="10px"/>

                                            <p:commandLink title="#{localemsgs.Pesquisar}" update="tabelaSupervisao msgs" action="#{postoservico.listarSupervisoes}">
                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                            </p:commandLink>
                                        </div>

                                        <div style="width: 100%;">
                                            <p:dataTable id="tabelaSupervisao"
                                                         value="#{postoservico.supervisaoMB.listaSupervisao}"
                                                         style="font-size: 12px;"
                                                         var="lista" rowKey="#{lista.tmktdetpst.sequencia}"
                                                         styleClass="tabela panelTabelaSupervisao"
                                                         resizableColumns="true" selectionMode="single"
                                                         scrollable="true" scrollWidth="100%" scrollHeight="200"
                                                         emptyMessage="#{localemsgs.SemRegistros}"
                                                         selection="#{postoservico.supervisaoMB.supervisaoSelecionado}">
                                                <p:ajax event="rowDblselect" listener="#{postoservico.dlbClickSupervisao}"
                                                        update="formSupervisao:supervisao"/>
                                                <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                                    <h:outputText value="#{lista.tmktdetpst.data}" title="#{lista.tmktdetpst.data}"
                                                                  converter="conversorData"
                                                                  style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hora}" style="width: 75px">
                                                    <h:outputText value="#{lista.tmktdetpst.hora}" title="#{lista.tmktdetpst.hora}"
                                                                  converter="conversorHora"
                                                                  style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Descricao}" style="width: 200px">
                                                    <h:outputText value="#{lista.tmktdetpst.historico}" title="#{lista.tmktdetpst.historico}"
                                                                  style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Detalhes}">
                                                    <h:outputText value="#{lista.tmktdetpst.detalhes}" title="#{lista.tmktdetpst.detalhes}"
                                                                  style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Situacao}" style="width: 60px">
                                                    <h:outputText value="#{lista.tmktdetpst.situacao}" title="#{lista.tmktdetpst.situacao}"
                                                                  style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column style="width: 50px">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{localemsgs.Qtd}" title="#{localemsgs.QtdEntrevistas}" />
                                                    </f:facet>
                                                    <h:outputText value="#{lista.qtdEntrevistas}" title="#{lista.qtdEntrevistas}"
                                                                  style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                            </p:dataTable>
                                        </div>
                                    </p:panel>
                                </p:tab>

                                <p:tab id="relatorios" title="#{localemsgs.Relatorios}" disabled="#{postoservico.flag eq 1}">
                                    <p:panel id="panelRelatorios" style="background: transparent">
                                        <div style="width: 100%">
                                            <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                                        </div>

                                        <div style="width: 100%">
                                            <p:outputLabel for="dataRelatorio1" value="#{localemsgs.DataInicial}: "/>
                                            <p:spacer width="10px"/>
                                            <p:inputMask id="dataRelatorio1" value="#{postoservico.dataRelatorio1}" mask="99/99/9999" style="width: 90px;"/>

                                            <p:spacer width="10px"/>

                                            <p:outputLabel for="dataRelatorio2" value="#{localemsgs.DataFinal}: "/>
                                            <p:spacer width="10px"/>
                                            <p:inputMask id="dataRelatorio2" value="#{postoservico.dataRelatorio2}" mask="99/99/9999" style="width: 90px;"/>

                                            <p:spacer width="10px"/>

                                            <p:commandLink title="#{localemsgs.Pesquisar}" update="tabelaRelatorio msgs" action="#{postoservico.listarRelatorios}">
                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                            </p:commandLink>
                                        </div>

                                        <div style="width: 100%">
                                            <p:dataTable id="tabelaRelatorio"
                                                         value="#{postoservico.relatorios}"
                                                         style="font-size: 12px"
                                                         var="relatorio" rowKey="#{relatorio.tmktdetpst.sequencia}"
                                                         styleClass="tabela panelTabelaSupervisao"
                                                         selectionMode="single" resizableColumns="true"
                                                         scrollHeight="200" scrollable="true" scrollWidth="100%"
                                                         emptyMessage="#{localemsgs.SemRegistros}"
                                                         selection="#{postoservico.relatorio}">
                                                <p:ajax event="rowDblselect" listener="#{postoservico.dbClickRelatorio}"
                                                        update="formRelatorio:relatorio"/>
                                                <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                                    <h:outputText value="#{relatorio.tmktdetpst.data}" title="#{relatorio.tmktdetpst.data}"
                                                                  converter="conversorData"
                                                                  style="#{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hora}" style="width: 75px">
                                                    <h:outputText value="#{relatorio.tmktdetpst.hora}" title="#{relatorio.tmktdetpst.hora}"
                                                                  converter="conversorHora"
                                                                  style="#{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Descricao}" style="width: 200px">
                                                    <h:outputText value="#{relatorio.tmktdetpst.historico}" title="#{relatorio.tmktdetpst.historico}"
                                                                  style="#{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Detalhes}" style="width: 200px">
                                                    <h:outputText value="#{relatorio.tmktdetpst.detalhes}" title="#{relatorio.tmktdetpst.detalhes}"
                                                                  style="white-space: normal; #{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                                                relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Operador}" style="width: 60px">
                                                    <h:outputText value="#{relatorio.tmktdetpst.operador}" title="#{relatorio.tmktdetpst.operador}"
                                                                  style="#{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                           relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                                </p:column>
                                            </p:dataTable>
                                        </div>
                                    </p:panel>
                                </p:tab>

                                <p:tab id="pstdepen" title="#{localemsgs.PstDepen}" disabled="#{postoservico.flag eq 1}">
                                    <p:panel id="panelPstdepen" style="background: transparent">
                                        <p:dataTable id="tabelaPstDepen" value="#{postoservico.dependencias}" var="depen"
                                                     resizableColumns="true" styleClass="tabela panelTabelaSupervisao"
                                                     selectionMode="single" rowKey="#{depen.codigo}"
                                                     selection="#{postoservico.dependencia}" emptyMessage="#{localemsgs.SemRegistros}"
                                                     scrollable="true" scrollWidth="100%" scrollHeight="200"
                                                     style="font-size: 12px;"
                                                     >
                                            <p:column headerText="#{localemsgs.Codigo}" style="width: 65px">
                                                <h:outputText value="#{depen.codigo}" converter="conversor0"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Descricao}">
                                                <h:outputText value="#{depen.descricao}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Latitude}" style="width: 85px">
                                                <h:outputText value="#{depen.latitude}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Longitude}" style="width: 85px">
                                                <h:outputText value="#{depen.longitude}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Tag}" style="width: 120px">
                                                <h:outputText value="#{depen.qrCode}"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </p:tab>

                                <p:tab id="pontos" title="#{localemsgs.Batidas}" disabled="#{postoservico.flag eq 1}">
                                    <p:panel id="panelPontos" style="background: transparent">
                                        <div style="width: 100%">
                                            <h:outputText value="#{localemsgs.BuscarData}: "/>
                                        </div>

                                        <div style="width: 100%">
                                            <p:outputLabel for="data" value="#{localemsgs.Data}: "/>
                                            <p:spacer width="10px"/>
                                            <p:inputMask id="data" value="#{postoservico.dataPonto}" mask="99/99/9999" style="width: 90px;"/>

                                            <p:spacer width="10px"/>

                                            <p:commandLink title="#{localemsgs.Pesquisar}" update="tabelaPontos msgs" action="#{postoservico.listarPontos}">
                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                            </p:commandLink>
                                        </div>

                                        <div style="width: 100%">
                                            <p:dataTable id="tabelaPontos" var="ponto"
                                                         value="#{postoservico.pontos}"
                                                         resizableColumns="true"
                                                         selection="#{postoservico.ponto}" emptyMessage="#{localemsgs.SemRegistros}"
                                                         scrollable="true" scrollWidth="100%" scrollHeight="200"
                                                         styleClass="tabela panelTabelaSupervisao" selectionMode="single"
                                                         rowKey="#{ponto.matr} #{ponto.batida}"
                                                         style="font-size: 12px;"
                                                         >
                                                <p:ajax event="rowDblselect" listener="#{postoservico.dbClickPonto}"
                                                        update="formBatida"/>
                                                <p:column headerText="#{localemsgs.Matr}" style="width: 70px">
                                                    <h:outputText value="#{ponto.matr}">
                                                        <f:convertNumber pattern="0000" />
                                                    </h:outputText>
                                                </p:column>

                                                <p:column headerText="#{localemsgs.Nome}">
                                                    <h:outputText value="#{ponto.funcionario}"/>
                                                </p:column>

                                                <p:column headerText="#{localemsgs.Batida}" style="width: 60px">
                                                    <h:outputText value="#{ponto.batida}">
                                                        <f:convertNumber pattern="00" />
                                                    </h:outputText>
                                                </p:column>

                                                <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                                    <h:outputText value="#{ponto.dtBatida}" converter="conversorData"/>
                                                </p:column>

                                                <p:column headerText="#{localemsgs.Hora}" style="width: 60px">
                                                    <h:outputText value="#{ponto.hora}" converter="conversorHora"/>
                                                </p:column>
                                            </p:dataTable>
                                        </div>
                                    </p:panel>
                                </p:tab>

                                <p:tab id="rondas" title="#{localemsgs.Rondas}" disabled="#{postoservico.flag eq 1}">
                                    <p:panel id="panelRondas" style="background: transparent">
                                        <div style="width: 100%">
                                            <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                                        </div>

                                        <div style="width: 100%">
                                            <p:outputLabel for="data1" value="#{localemsgs.DataInicial}: "/>
                                            <p:spacer width="10px"/>

                                            <p:inputMask id="data1" value="#{postoservico.dataRonda1}" mask="99/99/9999" style="width: 90px;"/>
                                            <p:spacer width="10px"/>

                                            <p:outputLabel for="data2" value="#{localemsgs.DataFinal}: "/>
                                            <p:spacer width="10px"/>

                                            <p:inputMask id="data2" value="#{postoservico.dataRonda2}" mask="99/99/9999" style="width: 90px;"/>
                                            <p:spacer width="10px"/>

                                            <p:commandLink title="#{localemsgs.Pesquisar}" update="tabelaRonda msgs" action="#{postoservico.listarRondas}">
                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                            </p:commandLink>
                                        </div>

                                        <div style="width: 100%">
                                            <p:dataTable id="tabelaRonda" var="ronda"
                                                         value="#{postoservico.rondas}"
                                                         resizableColumns="true" styleClass="tabela panelTabelaSupervisao"
                                                         selectionMode="single" rowKey="#{ronda.sequencia}"
                                                         selection="#{postoservico.ronda}" emptyMessage="#{localemsgs.SemRegistros}"
                                                         scrollable="true" scrollWidth="100%" scrollHeight="200"
                                                         style="font-size: 12px;"
                                                         >
                                                <p:column headerText="#{localemsgs.Dependencia}">
                                                    <h:outputText value="#{ronda.descricao}"/>
                                                </p:column>

                                                <p:column headerText="#{localemsgs.Matr}" style="width: 60px">
                                                    <h:outputText value="#{ronda.matr}" converter="conversor0"/>
                                                </p:column>

                                                <p:column headerText="#{localemsgs.Operador}" style="width: 80px">
                                                    <h:outputText value="#{ronda.operador}"/>
                                                </p:column>

                                                <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 75px">
                                                    <h:outputText value="#{ronda.dt_alter}" converter="conversorData"/>
                                                </p:column>

                                                <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 60px">
                                                    <h:outputText value="#{ronda.hr_alter}" converter="conversorHora"/>
                                                </p:column>
                                            </p:dataTable>
                                        </div>
                                    </p:panel>
                                </p:tab>

                                <p:tab id="documentos" title="#{localemsgs.Documentos}" disabled="#{postoservico.flag eq 1}">
                                    <div style="text-align: justify; width: 100%; padding-bottom: 10px">
                                        <h:outputText value="#{localemsgs.ArrasteArquivo}:"/>
                                    </div>

                                    <div  style="text-align: center; width: 100%; height: 150px">
                                        <p:fileUpload id="upload"
                                                      fileUploadListener="#{postoservico.HandleFileUpload}"
                                                      allowTypes="/(\.|\/)(pdf|jpe?g|xls|xlsx)$/"
                                                      label="#{localemsgs.Pesquisar}" auto="true"
                                                      invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                                      dragDropSupport="true" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                                      update="msgs formEditar:tabs:arquivos" previewWidth="10" skinSimple="true"
                                                      >
                                            <h:outputText value="#{localemsgs.ArrasteAqui}" id="ArrasteAqui"
                                                          style="text-align: justify; color: lightgray; top: 30px; position: relative;"/>
                                        </p:fileUpload>
                                    </div>

                                    <p:panel style="background: transparent; border: 1px solid #E6E6E6 !important">
                                        <p:dataTable value="#{postoservico.listaDocumentos}"
                                                     scrollHeight="200" scrollable="true"
                                                     style="background: transparent"
                                                     styleClass="tabelaArquivos"
                                                     rowKey="#{listaDocumentos.ordem}"
                                                     var="listaDocumentos" id="arquivos"
                                                     >
                                            <p:column headerText="#{localemsgs.Arquivos}">
                                                <p:commandLink actionListener="#{postoservico.HandleFileDownload(listaDocumentos)}" ajax="false"
                                                               value="#{listaDocumentos.descricao}" update="msgs">
                                                    <p:fileDownload value="#{postoservico.download}" />
                                                </p:commandLink>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.ModificadoEm}" style="width: 110px; text-align: center">
                                                <h:outputText value="#{listaDocumentos.dt_Alter}" converter="conversorData"/>
                                            </p:column>

                                            <p:column style="width: 30px">
                                                <p:commandLink actionListener="#{postoservico.excluirDocumento(listaDocumentos)}"
                                                               update="msgs formEditar:tabs:arquivos">
                                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirDocumento}" icon="ui-icon-alert" />
                                                    <p:graphicImage  url="../assets/img/icone_redondo_excluir.png" height="20" />
                                                </p:commandLink>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </p:tab>
                            </p:tabView>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--não usado?-->
                <!--Cadastrar novo-->
                <h:form id="formRelatorio">
                    <p:hotkey bind="esc" oncomplete="PF('dlgRelatorio').hide()"/>
                    <p:dialog widgetVar="dlgRelatorio" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgEditar"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_relatorios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Relatorio}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>
                        <p:panel id="relatorio" style="background-color: transparent" styleClass="cadastrar">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="local" value="#{localemsgs.PstServ}"/>
                                <p:inputText id="local" value="#{postoservico.relatorio.pstserv.local}" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="endereco" value=""/>
                                <p:inputText id="endereco" value="#{postoservico.relatorio.clientes.NRed} - #{postoservico.relatorio.clientes.nome}" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="endereco2" value=""/>
                                <p:inputText id="endereco2" value="#{postoservico.relatorio.clientes.ende} - #{postoservico.relatorio.clientes.bairro}" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="endereco3" value=""/>
                                <p:inputText id="endereco3" value="#{postoservico.enderecoRelatorio}" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="funcionario" value="#{localemsgs.Vigilante}"/>
                                <p:inputText id="funcionario" value="#{postoservico.relatorio.funcionario}" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="ocorrencia" value="#{localemsgs.Ocorrencia}"/>
                                <p:inputText id="ocorrencia" value="#{postoservico.relatorio.tmktdetpst.historico}" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="detalhes" value="#{localemsgs.Detalhes}"/>
                                <p:inputTextarea rows="4" id="detalhes" value="#{postoservico.relatorio.tmktdetpst.detalhes}" scrollHeight="200"
                                                 readonly="true" style="width: 100%" title="#{postoservico.relatorio.tmktdetpst.detalhes}"/>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="data" value="#{localemsgs.Data}"/>
                                <p:inputText id="data" value="#{postoservico.relatorio.tmktdetpst.data}" converter="conversorData" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="hora" value="#{localemsgs.Hora}"/>
                                <p:inputText id="hora" value="#{postoservico.relatorio.tmktdetpst.hora}" converter="conversorHora" readonly="true" style="width: 100%"/>
                            </p:panelGrid>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-8,ui-grid-col-2" layout="grid" styleClass="ui-panelgrid-blank">

                                <h:outputText value="#{localemsgs.Fotos} " rendered="#{postoservico.relatorio.fotos.size() gt 0}"/>

                                <h:outputText value="#{localemsgs.QtdFotos}: #{postoservico.relatorio.fotos.size()}" rendered="#{postoservico.relatorio.fotos.size() gt 0}"/>

                                <h:outputText value=" " rendered="#{postoservico.relatorio.fotos.size() gt 0}"/>

                                <p:commandLink action="#{postoservico.voltarFotoRelatorio}" rendered="#{postoservico.relatorio.fotos.size() gt 1}"
                                               update="formRelatorio:fotoRelatorio msgs">
                                    <p:graphicImage url="../assets/img/botao_anterior.png" height="20" title="#{localemsgs.FotoAnterior}"/>
                                </p:commandLink>
                                <h:outputText value=" " rendered="#{postoservico.relatorio.fotos.size() eq 1}"/>

                                <p:panel style="width: 100%; text-align: center; background: transparent" rendered="#{postoservico.relatorio.fotos.size() gt 0}">
                                    <p:lightBox style=" text-align: center;" id="fotoPosto">
                                        <h:outputLink value="#{postoservico.fotoRelatorio}"
                                                      title="#{postoservico.relatorio.pstserv.local}">
                                            <h:graphicImage value="#{postoservico.fotoRelatorio}" id="fotoRelatorio" style="height: 400px;"/>
                                        </h:outputLink>
                                    </p:lightBox>
                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:commandButton id="rotateLeft" icon="ui-icon-arrowreturnthick-1-w" style="float: right; width: 30px"
                                                         onclick="rotateLeft(event)" rendered="#{not empty postoservico.fotoRelatorio}"/>
                                        <p:commandButton id="rotateRight" icon="ui-icon-arrowreturnthick-1-e" style="float: left; width: 30px"
                                                         onclick="rotateRight(event)" rendered="#{not empty postoservico.fotoRelatorio}"/>
                                        <script type="text/javascript">
                                            rotate = 0;
                                            function rotateLeft(e) {
                                                e.preventDefault();
                                                if (rotate === -360) {
                                                    rotate = 0;
                                                }

                                                rotate = rotate + -90;
                                                var img = document.getElementById("formRelatorio:fotoRelatorio");
                                                var h = img.clientHeight;
                                                var w = img.clientWidth;

                                                img.style.transform = "rotate(" + rotate + "deg)";
                                                img.height = w;
                                                img.width = h;
                                            }
                                            ;

                                            function rotateRight(e) {
                                                e.preventDefault();
                                                if (rotate === 360) {
                                                    rotate = 0;
                                                }

                                                rotate = rotate + 90;
                                                var img = document.getElementById("formRelatorio:fotoRelatorio");
                                                var h = img.clientHeight;
                                                var w = img.clientWidth;

                                                img.style.transform = "rotate(" + rotate + "deg)";
                                                img.height = w;
                                                img.width = h;
                                            }
                                            ;
                                        </script>
                                    </p:panelGrid>
                                </p:panel>

                                <p:commandLink action="#{postoservico.avancarFotoRelatorio}" rendered="#{postoservico.relatorio.fotos.size() gt 1}"
                                               update="formRelatorio:fotoRelatorio msgs">
                                    <p:graphicImage url="../assets/img/botao_proximo.png" height="20" title="#{localemsgs.ProximaFoto}"/>
                                </p:commandLink>
                                <h:outputText value=" " rendered="#{postoservico.relatorio.fotos.size() eq 1}"/>

                                <h:outputText value="#{localemsgs.Mapa}"/>

                            </p:panelGrid>
                            <div class="ui-grid-row">
                                <p:gmap center="#{postoservico.coordenadas}" zoom="15" model="#{postoservico.pin}"
                                        type="ROADMAP" style="width:100%;height:210px;"/>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formBatida">
                    <p:dialog widgetVar="dlgBatida" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgEditar"
                              style="background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_relatorios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Batida}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="batida" style="background-color: transparent" styleClass="cadastrar">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="local" value="#{localemsgs.PstServ}"/>
                                <p:inputText id="local" value="#{postoservico.novo.local}" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="funcionario" value="#{localemsgs.Funcion}"/>
                                <p:inputText id="funcionario" value="#{postoservico.ponto.funcionario}" readonly="true" style="width: 100%"/>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="data" value="#{localemsgs.Data}"/>
                                <p:inputText id="data" value="#{postoservico.ponto.dtBatida}" converter="conversorData" readonly="true" style="width: 100%"/>

                                <p:outputLabel for="hora" value="#{localemsgs.Hora}"/>
                                <p:inputText id="hora" value="#{postoservico.ponto.hora}" converter="conversorHora" readonly="true" style="width: 100%"/>
                            </p:panelGrid>

                            <p:panelGrid columns="1" columnClasses="ui-grid-col-12" layout="grid" styleClass="ui-panelgrid-blank">

                                <p:panel style="width: 100%; text-align: center; background: transparent">
                                    <p:lightBox style=" text-align: center;" id="fotoPosto">
                                        <h:outputLink value="#{postoservico.ponto.foto}"
                                                      title="#{postoservico.ponto.funcionario}">
                                            <h:graphicImage value="#{postoservico.ponto.foto}" id="fotoBatida" style="height: 400px;"/>
                                        </h:outputLink>
                                    </p:lightBox>
                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:commandButton id="rotateLeft" icon="ui-icon-arrowreturnthick-1-w" style="float: right; width: 30px"
                                                         onclick="rotateLeft(event)"/>
                                        <p:commandButton id="rotateRight" icon="ui-icon-arrowreturnthick-1-e" style="float: left; width: 30px"
                                                         onclick="rotateRight(event)"/>
                                        <script type="text/javascript">
                                            rotate = 0;
                                            function rotateLeft(e) {
                                                e.preventDefault();
                                                if (rotate === -360) {
                                                    rotate = 0;
                                                }

                                                rotate = rotate + -90;
                                                var img = document.getElementById("formBatida:fotoBatida");
                                                var h = img.clientHeight;
                                                var w = img.clientWidth;

                                                img.style.transform = "rotate(" + rotate + "deg)";
                                                img.height = w;
                                                img.width = h;
                                            }
                                            ;

                                            function rotateRight(e) {
                                                e.preventDefault();
                                                if (rotate === 360) {
                                                    rotate = 0;
                                                }

                                                rotate = rotate + 90;
                                                var img = document.getElementById("formBatida:fotoBatida");
                                                var h = img.clientHeight;
                                                var w = img.clientWidth;

                                                img.style.transform = "rotate(" + rotate + "deg)";
                                                img.height = w;
                                                img.width = h;
                                            }
                                            ;
                                        </script>
                                    </p:panelGrid>
                                </p:panel>

                                <h:outputText value="#{localemsgs.Mapa}"/>
                                <p:gmap center="#{postoservico.coordenadas}" zoom="15" model="#{postoservico.pin}"
                                        type="ROADMAP" style="width:100%;height:210px;"/>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formSupervisao" class="ui-fluid">
                    <p:dialog widgetVar="dlgListarSupervisoes" positionType="absolute" responsive="true"
                              draggable="false" resizable="false" dynamic="true" closable="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dialogosupervisao"
                              style="background-image: url('../assets/img/bk.jpg')">
                        <f:facet name="header">
                            <img src="../assets/img/icone_supervisao.png" height="40" width="40"/>
                            <h:outputText value="#{localemsgs.Supervisoes}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="supervisao" styleClass="panelTelaSupervisao">
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row cabecalhoSupervisao" style="background-color: white">
                                    <div class="ui-grid-col-5" style="align-self: center;">
                                        #{localemsgs.Filial}: #{postoservico.supervisaoMB.filial.descricao}
                                    </div>

                                    <div class="ui-grid-col-4" style="align-self: center;">
                                        #{localemsgs.Operador}: #{postoservico.supervisaoMB.supervisaoSelecionado.tmktdetpst.operador}
                                    </div>

                                    <div class="ui-grid-col-3" style="align-self: center;">
                                        <p:commandLink title="#{localemsgs.SupervisaoAnterior}" action="#{postoservico.SupervisaoAnterior}" process="@this"
                                                       update="formSupervisao:supervisao msgs">
                                            <p:graphicImage url="../assets/img/botao_anterior.png" height="20" />
                                        </p:commandLink>
                                        #{localemsgs.Data}:
                                        <h:outputText value="#{postoservico.supervisaoMB.supervisaoSelecionado.tmktdetpst.data}" converter="conversorData" />
                                        <p:spacer width="5px"/>
                                        <h:outputText value="#{postoservico.supervisaoMB.supervisaoSelecionado.tmktdetpst.hora}" converter="conversorHora" />
                                        <p:commandLink title="#{localemsgs.SupervisaoPosterior}" action="#{postoservico.ProximaSupervisao}" process="@this"
                                                       update="formSupervisao:supervisao msgs">
                                            <p:graphicImage url="../assets/img/botao_proximo.png" height="20" />
                                        </p:commandLink>
                                    </div>
                                </div>
                                <div class="ui-grid-row detalhesSupervisao">
                                    <div class="ui-grid-col-6" >
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float: left;">
                                                <h:outputText value="#{localemsgs.Posto}: " style="color: black; font-size: 12px;"/>
                                            </div>
                                            <div style="width: 60% !important; float: left;">
                                                <h:outputText value="#{postoservico.supervisaoMB.supervisaoSelecionado.pstserv.local}"
                                                              style="color: black; font-size: 16px;"/>
                                                <div style="color: black; font-size: 14px;">
                                                    #{postoservico.supervisaoMB.cliente.NRed} - #{postoservico.supervisaoMB.cliente.nome}
                                                </div>
                                                <div style="color: black; font-size: 12px;">
                                                    #{postoservico.supervisaoMB.cliente.ende} - #{postoservico.supervisaoMB.cliente.bairro}
                                                </div>
                                                <div style="color: black; font-size: 12px;">
                                                    #{postoservico.supervisaoMB.cliente.cidade}/#{postoservico.supervisaoMB.cliente.estado} -
                                                    #{localemsgs.CEP}: <h:outputText value="#{postoservico.supervisaoMB.cliente.CEP}" converter="conversorCEP"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div class="ui-grid-col-6" >
                                                <h:outputText value="#{localemsgs.FotosLocal}: " style="color: black; font-size: 12px;"/>
                                            </div>
                                            <div class="ui-grid-col-6">
                                                <h:outputText value="#{localemsgs.QtdFotos}: " style="color: black; font-size: 12px; text-align: center"/>
                                                <h:outputText value="#{postoservico.supervisaoMB.supervisaoSelecionado.fotos.size()}" style="color: black; font-size: 12px;"/>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div style="width: 10% !important; float: left; text-align: center">
                                                <p:commandLink action="#{postoservico.supervisaoMB.VoltarFotoPosto}"
                                                               rendered="#{postoservico.supervisaoMB.supervisaoSelecionado.fotos.size() gt 0}"
                                                               update="formSupervisao:fotoPosto msgs" styleClass="botao">
                                                    <p:graphicImage url="../assets/img/botao_anterior.png" height="20" title="#{localemsgs.FotoAnterior}"/>
                                                </p:commandLink>
                                            </div>
                                            <div style="width: 80% !important; float: left; text-align: center">
                                                <p:lightBox styleClass="fotoGrande" style=" text-align: center;" id="fotoPosto">
                                                    <h:outputLink value="#{postoservico.supervisaoMB.fotoPosto}"
                                                                  rendered="#{postoservico.supervisaoMB.supervisaoSelecionado.fotos.size() gt 0}"
                                                                  title="#{postoservico.supervisaoMB.supervisaoSelecionado.pstserv.local}">
                                                        <h:graphicImage value="#{postoservico.supervisaoMB.fotoPosto}" id="panelFotoQuestionario"
                                                                        style="height: 200px"/>
                                                    </h:outputLink>
                                                </p:lightBox>
                                            </div>
                                            <div style="width: 10% !important; float: left; text-align: center">
                                                <p:commandLink action="#{postoservico.supervisaoMB.AvancarFotoPosto}"
                                                               rendered="#{postoservico.supervisaoMB.supervisaoSelecionado.fotos.size() gt 0}"
                                                               update="formSupervisao:fotoPosto msgs" styleClass="botao">
                                                    <p:graphicImage url="../assets/img/botao_proximo.png" height="20" title="#{localemsgs.ProximaFoto}"/>
                                                </p:commandLink>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ui-grid-col-6" >
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float:left">
                                                <h:outputText value="#{localemsgs.Tipo}:" style="color:black;font-size: 12px; "/>
                                            </div>
                                            <div style="width: 80% !important; float:left">
                                                <h:outputText value="#{postoservico.selecionado.tipoPosto} " style="color:black; font-size: 16px"/>
                                                <h:outputText value="- #{postoservico.selecionado.tipoPostoDesc}" style="color:black;font-size: 16px"/>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float:left">
                                                <h:outputText value="#{localemsgs.Situacao}:" style="color:black;font-size: 12px;"/>
                                            </div>
                                            <div style="width: 40% !important; float:left">
                                                <h:outputText id="situacao1" value="#{postoservico.supervisaoMB.supervisaoSelecionado.tmktdetpst.situacao} " style="color: black"/>
                                                <p:commandLink oncomplete="PF('dlgSituacao').show()" >
                                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="20"/>
                                                </p:commandLink>
                                                <p:inputText value="#{postoservico.supervisaoMB.situacoes.get(postoservico.supervisaoMB.supervisaoSelecionado.tmktdetpst.situacao)}"
                                                             id="situacao2" style="font-size: 12px; width: 100px" disabled="true"/>
                                                <p:dialog widgetVar="dlgSituacao"
                                                          resizable="false" dynamic="true" closable="false"
                                                          width="400" showEffect="drop" hideEffect="drop"
                                                          style="background-image: url('../assets/img/bk.jpg');">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{localemsgs.Situacao}" style="color:black"/>
                                                    </f:facet>
                                                    <p:panel id="listarSituacoes" style="background-color: transparent;  padding-left: 13px ">
                                                        <p:dataTable id="niveis" value="#{postoservico.supervisaoMB.listaSituacoes}"
                                                                     var="lista" rowKey="#{lista}" resizableColumns="true" emptyMessage="#{localemsgs.SemRegistros}"
                                                                     style="font-size: 12px" selectionMode="single" styleClass="tabela"
                                                                     selection="#{postoservico.supervisaoMB.situacao}">
                                                            <p:ajax event="rowSelect" listener="#{postoservico.supervisaoMB.SelecionarSituacao}" />
                                                            <p:column headerText="#{localemsgs.Situacao}">
                                                                <h:outputText value="#{lista}" title="#{lista}"/>
                                                            </p:column>
                                                            <p:column headerText="#{localemsgs.Descricao}" >
                                                                <h:outputText value="#{postoservico.supervisaoMB.situacoes.get(lista)}" title="#{postoservico.supervisaoMB.situacoes.get(lista)}"/>
                                                            </p:column>
                                                        </p:dataTable>

                                                        <div class="form-inline">
                                                            <p:commandLink oncomplete="PF('dlgSituacao').hide()"
                                                                           action="#{postoservico.supervisaoMB.AtualizarSituacaoPosto(postoservico.selecionado, postoservico.dataSupervisao1, postoservico.dataSupervisao2)}"
                                                                           title="#{localemsgs.Selecionar}" update="formDetalhes:situacao1 formDetalhes:situacao2" process="@this">
                                                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                                            </p:commandLink>
                                                            <p:commandLink oncomplete="PF('dlgSituacao').hide()"
                                                                           title="#{localemsgs.Voltar}">
                                                                <p:graphicImage url="../assets/img/icone_voltar.png" width="40" height="40" />
                                                            </p:commandLink>
                                                        </div>
                                                    </p:panel>
                                                </p:dialog>
                                            </div>
                                            <div style="width: 40% !important; float:left">
                                                <h:outputText value="#{postoservico.selecionado.secao} " style="color:black; font-size: 11px;"/>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float:left">
                                                <h:outputText value="#{localemsgs.Historico}:" style="color:black;font-size: 12px;"/>
                                            </div>
                                            <div style="width: 80% !important; float:left">
                                                <h:outputText value="#{postoservico.supervisaoMB.supervisaoSelecionado.tmktdetpst.historico} " style="color:black;"/>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float:left">
                                                <h:outputText value="#{localemsgs.Mapa}:" style="color:black;font-size: 12px;"/>
                                            </div>
                                            <div style="width: 80% !important; float:left">
                                                <h:outputText value="#{localemsgs.Distancia} (m): " style="color:black; text-align: right" />
                                                <h:outputText value="#{postoservico.supervisaoMB.distPstSup}" style="color: black; text-align: right">
                                                    <f:convertNumber maxFractionDigits="2" />
                                                </h:outputText>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <p:gmap center="#{postoservico.supervisaoMB.coordenadas}" zoom="15" model="#{postoservico.supervisaoMB.pin}"
                                                    type="ROADMAP" style="width:100%;height:210px;"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="ui-grid-row detalhesSupervisao">
                                    <div class="ui-grid-col-1">
                                        <p:outputLabel for="detalhes" value="#{localemsgs.Detalhes}: " style="color: black"/>
                                    </div>
                                    <div class="ui-grid-col-11">
                                        <p:inputText style="width: 100%" disabled="true" id="detalhes"
                                                     value="#{postoservico.supervisaoMB.supervisaoSelecionado.tmktdetpst.detalhes}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row detalhesSupervisao">
                                    <div class="ui-grid-col-3" style="text-align: center; padding-right: 5px">
                                        <h:outputText rendered="#{not empty postoservico.supervisaoMB.funcionarios}"
                                                      style="font-size:14px; color: black; font-weight: normal"
                                                      value="#{localemsgs.Entrevistas}" />
                                        <p:dataTable id="tabelaFuncion" value="#{postoservico.supervisaoMB.funcionarios}" emptyMessage="#{localemsgs.SemRegistros}"
                                                     styleClass="tabela" var="lista" rowKey="#{lista.funcion.matr}" resizableColumns="true"
                                                     scrollable="true" scrollHeight="130" rendered="#{not empty postoservico.supervisaoMB.funcionarios}"
                                                     selectionMode="single" selection="#{postoservico.supervisaoMB.questionarioSelecionado}"
                                                     style="font-size: 12px; width: 100%">
                                            <p:ajax event="rowSelect" listener="#{postoservico.supervisaoMB.SelecionarQuestionario}"
                                                    update="formSupervisao:panelQuestoes formSupervisao:panelFotoFuncion" />
                                            <p:column headerText="#{localemsgs.Matr}" style="width: 65px">
                                                <h:outputText value="#{lista.funcion.matr}" title="#{lista.funcion.matr}">
                                                    <f:convertNumber pattern="0" />
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Nome}">
                                                <h:outputText value="#{lista.funcion.nome}" title="#{lista.funcion.nome}"/>
                                            </p:column>
                                        </p:dataTable>
                                        <p:graphicImage url="../assets/img/funcionarios.png" style="width:100%;"
                                                        rendered="#{empty postoservico.supervisaoMB.funcionarios}" />
                                    </div>
                                    <div class="ui-grid-col-5" style="text-align: center">
                                        <p:panel id="panelQuestoes">
                                            <h:outputText style="font-size:14px; font-weight: normal; color: black"
                                                          value="#{localemsgs.Checklist}"
                                                          rendered="#{postoservico.supervisaoMB.questoes.size() ge 1}"/>
                                            <p:panel rendered="#{not empty postoservico.supervisaoMB.questoes}">
                                                <p:inputTextarea style="font-size:12px; font-weight: normal; text-align: left; width: 100%; height: 40px"
                                                                 rows="1" scrollHeight="20" autoResize="false" readonly="true" disabled="true"
                                                                 value="#{postoservico.supervisaoMB.detalhesQuestoes}"
                                                                 title="#{postoservico.supervisaoMB.detalhesQuestoes}"/>
                                                <p:dataTable id="tabelaDetalhesQuestionario" value="#{postoservico.supervisaoMB.questoes}"
                                                             styleClass="tabela" var="lista" rowKey="#{lista.psthstqst.codQuestao}" emptyMessage="#{localemsgs.SemRegistros}"
                                                             resizableColumns="true" rendered="#{postoservico.supervisaoMB.questoes.size() gt 1}"
                                                             scrollable="true" scrollHeight="100"
                                                             style="font-size: 11px;">
                                                    <p:column headerText="#{localemsgs.Qst}" style="width: 210px">
                                                        <h:outputText value="#{lista.tbval.descricao}" title="#{lista.tbval.descricao}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Resp}" style="width: 70px">
                                                        <h:outputText value="#{postoservico.supervisaoMB.respostas.get(lista.psthstqst.resposta)}"
                                                                      title="#{postoservico.supervisaoMB.respostas.get(lista.psthstqst.resposta)}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Detalhes}">
                                                        <h:outputText value="#{lista.psthstqst.detalhes}" title="#{lista.psthstqst.detalhes}"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </p:panel>
                                            <p:graphicImage url="../assets/img/questionario.png" style="max-height:200px;"
                                                            rendered="#{empty postoservico.supervisaoMB.questoes}" />
                                        </p:panel>
                                    </div>
                                    <div class="ui-grid-col-4" style="text-align: center; padding-left: 5px">
                                        <p:panel id="panelFotoFuncion">
                                            <div class="ui-grid-row">
                                                <div class="ui-grid-col-12" style="text-align: center">
                                                    <h:outputText style="font-size:14px; font-weight: normal; color: white; text-shadow: 1px 1px black;
                                                                  text-align: center" value="#{localemsgs.Galeria}"
                                                                  rendered="#{not empty postoservico.supervisaoMB.questionarioSelecionado.endfotos}"/>
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div class="ui-grid-col-12" style="text-align: left">
                                                    <h:outputText value="#{localemsgs.Foto} #{postoservico.supervisaoMB.posFotoFuncion + 1} #{localemsgs.De}
                                                                  #{postoservico.supervisaoMB.questionarioSelecionado.qtdefotos}:"
                                                                  style="font-size:12px; font-weight: normal; color: #E6E6E6; text-shadow: 1px 1px black;"
                                                                  rendered="#{not empty postoservico.supervisaoMB.questionarioSelecionado.endfotos}" />
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div style="width: 20% !important; float: left; text-align: center">
                                                    <p:commandLink action="#{postoservico.supervisaoMB.VoltarFotoFuncion}"
                                                                   rendered="#{not empty postoservico.supervisaoMB.questionarioSelecionado.endfotos}"
                                                                   update="formSupervisao:panelFotoFuncion msgs">
                                                        <p:graphicImage url="../assets/img/botao_anterior.png" height="20" title="#{localemsgs.FotoAnterior}"/>
                                                    </p:commandLink>
                                                </div>
                                                <div style="width: 60% !important; float: left; text-align: center">
                                                    <p:lightBox styleClass="fotoGrande" id="fotoFuncion" rendered="#{not empty postoservico.supervisaoMB.questionarioSelecionado.endfotos}">
                                                        <h:outputLink value="#{postoservico.supervisaoMB.fotoFuncion}" title="#{postoservico.supervisaoMB.questionarioSelecionado.funcion.nome}">
                                                            <h:graphicImage value="#{postoservico.supervisaoMB.fotoFuncion}" id="fotoFuncionario" style="height: 150px;"/>
                                                        </h:outputLink>
                                                    </p:lightBox>
                                                </div>
                                                <div style="width: 20% !important; float: left; text-align: center">
                                                    <p:commandLink action="#{postoservico.supervisaoMB.AvancarFotoFuncion}"
                                                                   rendered="#{not empty postoservico.supervisaoMB.questionarioSelecionado.endfotos}"
                                                                   update="formSupervisao:panelFotoFuncion msgs">
                                                        <p:graphicImage url="../assets/img/botao_proximo.png" height="20" title="#{localemsgs.ProximaFoto}"/>
                                                    </p:commandLink>
                                                </div>
                                            </div>
                                            <p:graphicImage url="../assets/img/galeria.png" style="height:200px" rendered="#{empty postoservico.supervisaoMB.questionarioSelecionado.endfotos}"/>
                                        </p:panel>
                                    </div>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Pesquisar Posto de Serviço -->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_postosdeservico.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarPstServ}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 30%; float: left">
                                    <p:outputLabel for="subFil" value="#{localemsgs.Filial}: "/>
                                </div>
                                <div style="width: 70%; float: left">
                                    <p:selectOneMenu
                                        id="subFil"
                                        value="#{postoservico.novo.codFil}"
                                        converter="omnifaces.SelectItemsConverter"
                                        filter="true"
                                        filterMatchMode="contains"
                                        style="width: 100%"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}"/>
                                        <p:ajax event="itemSelect" listener="#{postoservico.SelecionarFilial}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 30%; float: left">
                                    <p:outputLabel for="local" value="#{localemsgs.Local}:"/>
                                </div>
                                <div style="width: 70%; float: left">
                                    <p:inputText id="local" value="#{postoservico.novo.local}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="local" value="#{localemsgs.Local}" />
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 30%; float: left">
                                    <p:outputLabel for="secao" value="#{localemsgs.Secao}:"/>
                                </div>
                                <div style="width: 70%; float: left">
                                    <p:inputText id="secao" value="#{postoservico.novo.secao}"
                                                 style="width: 100%">
                                        <p:watermark for="secao" value="#{localemsgs.Secao}" />
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 30%; float: left">
                                    <p:outputLabel for="cliente" value="#{localemsgs.Cliente}:"/>
                                </div>
                                <div style="width: 70%; float: left">
                                    <p:autoComplete id="cliente" value="#{postoservico.clientes}" styleClass="cliente"
                                                    style="width: 100%"
                                                    completeMethod="#{postoservico.ListarClientes}" forceSelection="true"
                                                    var="cli" itemLabel="#{cli.NRed}" itemValue="#{cli}"
                                                    converter="conversorCliente" >
                                        <p:ajax event="itemSelect" listener="#{postoservico.SelecionarCliente}"
                                                update="formPesquisar:cliente"/>
                                        <p:watermark for="cliente" value="#{localemsgs.Cliente}" />
                                    </p:autoComplete>

                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 30%; float: left">
                                    <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:"/>
                                </div>
                                <div style="width: 70%; float: left">
                                    <p:selectOneRadio id="situacao" value="#{postoservico.novo.situacao}"
                                                      style="width: 100%">
                                        <f:selectItem  itemLabel="#{localemsgs.Ativo}" itemValue="A" />
                                        <p:spacer width="10px"/>
                                        <f:selectItem itemLabel="#{localemsgs.Inativo}" itemValue="I" />
                                    </p:selectOneRadio>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 30%; float: left">
                                    <p:outputLabel for="tipoposto" value="#{localemsgs.TipoPosto}:"/>
                                </div>
                                <div style="width: 70%; float: left">
                                    <p:inputText id="tipoposto" value="#{postoservico.novo.tipoPosto}"
                                                 style="width: 100%">
                                        <p:watermark for="tipoposto" value="#{localemsgs.TipoPosto}" />
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 30%; float: left">
                                    <p:outputLabel for="descricao" value="#{localemsgs.TipoPostoDesc}:"/>
                                </div>
                                <div style="width: 70%; float: left">
                                    <p:autoComplete id="descricao" value="#{postoservico.novo.tipoPostoDesc}"
                                                    style="width: 100%" completeMethod="#{postoservico.ListarDescTipos}"
                                                    scrollHeight="200" styleClass="posto"
                                                    var="ctritem" itemLabel="#{ctritem}" itemValue="#{ctritem}">
                                        <p:ajax event="itemSelect" listener="#{postoservico.SelecionarDescTipo}"
                                                update="formPesquisar:descricao"/>
                                        <p:watermark for="descricao" value="#{localemsgs.TipoPostoDesc}" />
                                    </p:autoComplete>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 30%; float: left">
                                    <p:outputLabel for="contrato" value="#{localemsgs.Contrato}:"/>
                                </div>
                                <div style="width: 70%; float: left">
                                    <p:autoComplete id="contrato" styleClass="posto"
                                                    style="width: 100%" value="#{postoservico.novo.descContrato}"
                                                    completeMethod="#{postoservico.BuscarContratos}" forceSelection="true" >
                                        <p:ajax event="itemSelect" listener="#{postoservico.SelecionarContrato}"
                                                update="formPesquisar:contrato"/>
                                        <p:watermark for="contrato" value="#{localemsgs.Contrato}" />
                                    </p:autoComplete>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnLogar" action="#{postoservico.PesquisaPaginada}"
                                               update=":msgs :main:tabela formPesquisar cabecalho corporativo"
                                               title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisar').hide();">
                                    <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Pesquisa Rápida Posto de Serviço -->
                <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_postosdeservico.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarPstServ}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="panelPesquisaRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{postoservico.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Numero}" itemValue="SECAO" />
                                        <f:selectItem itemLabel="#{localemsgs.Local}" itemValue="LOCAL" />
                                        <f:selectItem itemLabel="#{localemsgs.Contrato}" itemValue="CONTRATO" />
                                        <f:selectItem itemLabel="#{localemsgs.OS}" itemValue="OS" />
                                        <f:selectItem itemLabel="#{localemsgs.CCusto}" itemValue="POSTO" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{postoservico.chavePesquisa eq 'SECAO'}" value="#{localemsgs.Numero}: "/>
                                        <p:outputLabel for="opcao" rendered="#{postoservico.chavePesquisa eq 'LOCAL'}" value="#{localemsgs.Local}: "/>
                                        <p:outputLabel for="opcao" rendered="#{postoservico.chavePesquisa eq 'CONTRATO'}" value="#{localemsgs.Contrato}: "/>
                                        <p:outputLabel for="opcao" rendered="#{postoservico.chavePesquisa eq 'OS'}" value="#{localemsgs.OS}: "/>
                                        <p:outputLabel for="opcao" rendered="#{postoservico.chavePesquisa eq 'POSTO'}" value="#{localemsgs.CCusto}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{postoservico.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="botaoPesquisaRapida"
                                               action="#{postoservico.pesquisarUnico()}"
                                               update=" :main:tabela :msgs cabecalho"
                                               oncomplete="PF('dlgPesquisaRapida').hide()"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                </script>

                <!-- Exportar Posto de Serviço -->
                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" width="400"
                          style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_postosdeservico.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                    </f:facet>
                    <h:form class="form-inline">
                        <p:hotkey bind="esc" oncomplete="PF('dlgExportar').hide()"/>
                        <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                        <p:separator />
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="filial" value="#{postoservico.eFilial}">
                                    <p:ajax update="labelFilial"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{postoservico.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="secao" value="#{postoservico.eCodPosto}">
                                    <p:ajax update="labelSecao"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSecao" value="#{localemsgs.Secao}" style="#{postoservico.eCodPosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="local" value="#{postoservico.ePosto}">
                                    <p:ajax update="labelLocal"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelLocal" value="#{localemsgs.Posto}" style="#{postoservico.ePosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="eTipo" value="#{postoservico.eTipo}">
                                    <p:ajax update="labelTipo"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelTipo" value="#{localemsgs.TipoPosto}" style="#{postoservico.eTipo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="eDescTipo" value="#{postoservico.eDescTipo}">
                                    <p:ajax update="labelTipoDesc"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelTipoDesc" value="#{localemsgs.TipoPostoDesc}" style="#{postoservico.eDescTipo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="sit" value="#{postoservico.eSituacao}">
                                    <p:ajax update="labelSit"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSit" value="#{localemsgs.Situacao}" style="#{postoservico.eSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="eDtSituacao" value="#{postoservico.eDtSituacao}">
                                    <p:ajax update="labelSituacao"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSituacao" value="#{localemsgs.Dt_Situac}" style="#{postoservico.eDtSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="InterfExt" value="#{postoservico.eInterfExt}">
                                    <p:ajax update="eInterfExt"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eInterfExt" value="#{localemsgs.InterfExt}" style="#{postoservico.eInterfExt eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="hralter" value="#{postoservico.eHrAlter}">
                                    <p:ajax update="labelHrAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{postoservico.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="dtalter" value="#{postoservico.eDtAlter}">
                                    <p:ajax update="labelDtAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{postoservico.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="operador" value="#{postoservico.eOperador}">
                                    <p:ajax update="labelOperador"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{postoservico.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <p:separator />
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#EEE;">
                            <p:panel style="text-align: center; background-color:#EEE;">
                                <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                                <h:commandLink id="pdf" actionListener="#{postoservico.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_pdf.png" style="height:40px"/>
                                    <p:dataExporter target="main:tabela" type="pdf" fileName="#{localemsgs.Postos}"
                                                    preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                                </h:commandLink>
                            </p:panel>

                            <p:panel style="text-align: center;background-color:#EEE;">
                                <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                                <h:commandLink id="xlsx" actionListener="#{postoservico.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_xls.png" style="height:40px"/>
                                    <p:dataExporter target="main:tabela" type="xlsx" fileName="#{localemsgs.Postos}"/>
                                </h:commandLink>
                            </p:panel>
                        </p:panelGrid>
                    </h:form>
                </p:dialog>

                <h:form id="relatorio" style="overflow:hidden !important;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgRelatorio').hide()"/>
                    <p:dialog widgetVar="dlgRelatorio" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgRelatorio"
                              style="height:98% !important; min-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color: #FFF !important;">
                        <f:facet name="header">
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-1,ui-grid-col-9,ui-grid-col-1,ui-grid-col-1"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="max-height:20px !important;margin:0px !important; width:100% !important; height:100% !important;">
                                <img src="../assets/img/icones_satmob_insp_postos_G.png" height="40" width="40"/>

                                <h:outputText
                                    value="#{postoservico.titulo}"
                                    style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px;" />

                                <p:commandLink title="#{localemsgs.Editar}" update="relatorio msgs" id="btnEditar"
                                               ajax="false" rendered="false"
                                               actionListener="#{postoservico.gerarRelatorioDownload}">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                    <p:fileDownload value="#{postoservico.arquivoRelatorio}" />
                                </p:commandLink>
                                <div style="position:absolute; right:16px;top:26px; z-index: 10 !important; white-space:nowrap !important; width:400px !important;text-align:right !important; display:#{postoservico.logsSatMobEWSelecionado.tipo eq '2' and postoservico.supervisor?'block':'none'}">
                                    <label for="filtroWeb"
                                           style="color:maroon; white-space:nowrap !important; font-weight:bold; text-align:right !important; padding-right:32px; font-size:8pt !important;">
                                        #{localemsgs.RelatorioPublico}:&nbsp;
                                    </label>

                                    <p:selectBooleanCheckbox id="filtroWeb" value="#{postoservico.filtroWeb}" rendered="#{postoservico.logsSatMobEWSelecionado.tipo eq '2' and postoservico.supervisor}" style="position:absolute; top:28px; right:26px !important;">
                                        <p:ajax update="msgs" listener="#{postoservico.atualizarFiltroWeb}" />
                                    </p:selectBooleanCheckbox>

                                </div>

                                <p:commandLink title="#{localemsgs.Editar}" update="relatorio msgs" id="btnEditar2"
                                               actionListener="#{postoservico.buttonAction}"
                                               rendered="false">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>
                            </p:panelGrid>
                        </f:facet>
                        <p:panel styleClass="cadastrar" id="cadastrar" style="overflow:hidden !important; max-width: 100% !important;padding:0px !important; height: calc(100vh - 175px); background-color: #FFF !important">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100vh - 177px); overflow:hidden !important; overflow-y: auto!important;  padding:0px !important; background-color: #FFF !important;">
                                <div id="pnlHTMLpai" class="col-md-7 col-sm-7 col-xs-12 #{localemsgs.INSPEÇÃO}"
                                     style="overflow:hidden !important; padding:0px 8px 6px 4px !important; min-height:100%;background-color: #FFF !important">
                                    <label id="btExpandirMapa" style="position:absolute; width:50px; height: 50px; border-radius:50%; background-color:#022a48; top:22px; z-index:9999 !important; right:20px; cursor:pointer; text-align:center"><img src="../assets/img/icone_abrirmapa.png" style="max-width:70%; max-height:70%; margin-top:8px; margin-left:1px;" /></label>

                                    <p:panel id="pnlHTML" style="min-height:100% !important; height:100% !important; max-height:100% !important; background: #FFF !important; padding:0px 10px 0px 10px !important; border:thin solid #DDD !important; box-shadow: 2px 2px 3px #CCC; min-height:100% !important;">
                                        <div id="DadosHTML"  class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; margin: 0px !important">
                                            <h:outputText value="#{postoservico.html}" escape="false" />
                                        </div>
                                        <p:gmap id="gmap" center="#{postoservico.centroMapa}" zoom="#{postoservico.zoomMapa}" type="TERRAIN"
                                                style="margin-top:6px; height: calc(100vh - 460px); width:100% !important; border:thin solid #BBB;" model="#{postoservico.mapa}" rendered="#{postoservico.mapa ne null}"/>
                                    </p:panel>
                                </div>

                                <div id="divConteudo" class="col-md-5 col-sm-5 col-xs-12 #{localemsgs.INSPEÇÃO}"
                                     style=" padding:0px 8px 6px 4px !important; position:relative;">
                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:commandButton id="rotateLeft" icon="ui-icon-arrowreturnthick-1-w" style="position:absolute; right:8px !important; height:calc(100% - 10px); top:2px; border:none !important;width: 30px;border-radius:0px 4px 4px 0px !important;outline:none !important; box-shadow:none !important; width:20px !important; background-color:#BBB !important;"
                                                         onclick="rotateLeft(event)" rendered="#{not empty postoservico.fotoRelatorio}"/>
                                        <p:commandButton id="rotateRight" icon="ui-icon-arrowreturnthick-1-e" style="position:absolute; left:6px !important; height:calc(100% - 10px); top:2px; border:none !important; width: 30px; border-radius: 4px 0px 0px 4px !important;outline:none !important; box-shadow:none !important; width:20px !important; background-color:#BBB !important;"
                                                         onclick="rotateRight(event)" rendered="#{not empty postoservico.fotoRelatorio}"/>
                                    </p:panelGrid>

                                    <p:chart type="bar" model="#{postoservico.graficoRonda}" style="height: 100px" rendered="#{!postoservico.rondas.isEmpty()}"/>

                                    <p:panel id="panelInspecoesDetalhes" style="height: calc(100vh - 183px); background-color: transparent; border: thin solid #DDD !important; position:relative; border-radius:5px !important; background-color:#FFF !important;box-shadow:2px 2px 3px #CCC !important; padding: 3px !important; overflow: auto" styleClass="col-md-12"
                                             rendered="#{!postoservico.pstInspecaoSelecionadaDetalhes.isEmpty()}">
                                        <ui:repeat value="#{postoservico.pstInspecaoSelecionadaDetalhes}" var="pstInspecao">
                                            <p:panel style="margin-bottom:0px; padding: 0px !important;">
                                                <label style="font-size: 14pt; font-weight: 500; width: 100%; color: steelblue; padding: 8px 0px 5px 10px; background-color: #EEE; text-align: left; margin: 0px 0px 5px 0px !important; text-shadow: 1px 1px #FFF; border: thin solid #DDD"><i class="fa fa-check-square-o" aria-hidden="true" style="font-size: 12pt !important;"></i>&nbsp;&nbsp;#{pstInspecao.pstInspecao.pergunta}</label>
                                                <label style="display: #{pstInspecao.fotos.size() eq 0 ? '': 'none'}; font-size: 10pt; color: #666; width: 100%; text-align: left; padding: 0px 0px 5px 0px; margin: 3px 0px 0px 0px !important;"><span style="float: left; font-weight: 600;width: 45%; text-align: left; padding-left: 8px; color: #303030 !important; text-transform: uppercase !important">#{pstInspecao.pstInspecao.resposta}</span></label>

                                                <p:panel id="panelMedia" style="background-color: transparent; padding-right:0px !important;" rendered="#{pstInspecao.fotos.size() gt 0}">
                                                    <ui:repeat value="#{pstInspecao.fotos}" var="pstInspecaoFotoArray">
                                                        <div class="col-md-3 col-sm-3 col-xs-4" style="padding: 3px !important; margin-bottom: 4px !important">
                                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px !important; border: thin solid #CCC;">
                                                                <img src="#{pstInspecaoFotoArray}" style="max-width: 100%;" onclick="AbrirFoto('#{pstInspecaoFotoArray}')" />
                                                            </div>
                                                        </div>
                                                    </ui:repeat>
                                                </p:panel>

                                                <p:panel id="panelMediaVideo" style="background-color: transparent; padding-right:0px !important; text-align:center !important;"
                                                         rendered="#{pstInspecao.videos.size() gt 0}">
                                                    <div class="col-md-3 col-sm-3 col-xs-4" style="padding: 4px !important">
                                                        <table summary="sasw" cellspacing="0" cellpadding="0" border="0" style="background-color: transparent; border-radius: 3px !important;margin-bottom:10px !important; border:thin solid #CCC !important; width:100%">
                                                            <tbody>
                                                                <tr>
                                                                    <td style="padding:.75pt .75pt .75pt .75pt">
                                                                        <p:commandButton icon=" ui-icon-triangle-1-w" style="width: 30px; float: left;"
                                                                                         action="#{pstInspecao.voltarVideo}" rendered="#{pstInspecao.videos.size() gt 1}"
                                                                                         update="@parent msgs">
                                                                        </p:commandButton>
                                                                    </td>
                                                                    <td style="padding:.75pt .75pt .75pt .75pt; width: 100%">
                                                                        <p:panel id="panelVideo" style="background: transparent">
                                                                            <p:media value="#{pstInspecao.video}" style="max-width: 100% !important; vertical-align: middle;">
                                                                                <f:param name="autoPlay" value="false" />
                                                                            </p:media>
                                                                        </p:panel>
                                                                    </td>
                                                                    <td style="padding:.75pt .75pt .75pt .75pt">
                                                                        <p:commandButton icon=" ui-icon-triangle-1-e" style="width: 30px;"
                                                                                         action="#{pstInspecao.avancarVideo}" rendered="#{pstInspecao.videos.size() gt 1}"
                                                                                         update="@parent msgs">
                                                                        </p:commandButton>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </p:panel>
                                            </p:panel>
                                        </ui:repeat>
                                    </p:panel>

                                    <p:panel style="height:430px  !important; max-height:430px  !important; background-color: transparent; border: thin solid #DDD !important; position:relative; border-radius:5px !important; background-color:#FFF !important;box-shadow:2px 2px 3px #CCC !important; padding:10px !important;" styleClass="col-md-12"
                                             rendered="#{!postoservico.rondas.isEmpty()}">
                                        <p:dataTable id="tabela" value="#{postoservico.rondas}" rendered="#{!postoservico.rondas.isEmpty()}"
                                                     var="ronda" rowKey="#{ronda.sequencia}" sortBy="#{ronda.hr_alter}"
                                                     resizableColumns="true" styleClass="tabela" emptyMessage="#{localemsgs.SemRegistros}"
                                                     rowIndexVar="ordem" style="font-size: 12px">
                                            <p:column headerText="#{localemsgs.NomeTag}">
                                                <h:outputText value="#{ronda.descricao}" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OrdemLeitura}" style="width: 140px; text-align: center">
                                                <h:outputText value="#{ordem+1}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TagTour}" style="width: 140px; text-align: center">
                                                <h:outputText value="#{ronda.codDepen}" converter="conversor0"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora}" style="width: 70px; text-align: center">
                                                <h:outputText value="#{ronda.hr_alter}" converter="conversorHora"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </div>
                            </div>
                        </p:panel>
                        <script type="text/javascript">
                            // <![CDATA[
                            setTimeout(function () {
                                AlinhaObjetos();
                            }, 0);
                            function AlinhaObjetos() {
                                if (!$('div[id*="divConteudo"] div div').html() &&
                                        !$('div[id*="divConteudo"] div table').html() &&
                                        !$('div[id*="divConteudo"]').find('[id*="panelInspecoesDetalhes"]').html() {

                                    $('div[id*="divConteudo"]').css('display', 'none');
                                    $('div[id*="pnlHTMLpai"]').css('min-width', '100%');
                                    if ($(document).width() > 700) {
                                        $('[id*="DadosHTML"]').css('min-width', '49.5%').css('width', '49.5%').css('max-width', '49.5%').css('float', 'left');
                                        $('div[id*="gmap"]').css('min-width', '49.5%').css('width', '49.5%').css('max-width', '49.5%').css('float', 'right').css('min-height', '400px');
                                    } else {
                                        $('[id*="DadosHTML"]').css('min-width', '100%').css('width', '100%').css('max-width', '100%').css('float', 'initial');
                                        $('div[id*="gmap"]').css('min-width', '100%').css('width', '100%').css('max-width', '100%').css('float', 'initial').css('min-height', '0px');
                                    }
                                }
                            }

                            $(window).resize(function () {
                                AlinhaObjetos();
                            });
                            // ]]>
                        </script>
                    </p:dialog>
                </h:form>

                <h:form id="formBoletimTrabalho" class="form-inline">
                    <p:dialog widgetVar="dlgBoletimTrabalho"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important; width: calc(100vh - 150px) !important; min-height: calc(100vh - 150px) !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_cadastros.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.ResumoTrabalho}" style="color:#022a48"/>
                        </f:facet>      

                        <p:panel id="pnlBoletim" style="background-color: #FFF; max-width: 100% !important; display: block !important; margin-top:-10px !important; z-index:999 !important; min-height:calc(100% - 20px) !important; height:calc(100% - 20px) !important; max-height:calc(100% - 20px) !important" class="cadastrar">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="height: 80px; background-color: #EEE; border: thin solid #CCC; margin-top: 10px; padding: 2px 4px 2px 4px !important; border-radius: 4px; box-shadow: 2px 2px 4px #DDD;">
                                <div class="col-md-9 col-sm-9 col-xs-9" style="padding: 6px 3px 6px 6px !important">
                                    <label style="font-weight: bold; text-shadow: 1px 1px #FFF">#{localemsgs.Operador}</label>
                                    <p:selectOneMenu id="operador" value="#{postoservico.pessoaSelecionada}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%; outline: none !important" >
                                        <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}" itemValue=""></f:selectItem>
                                        <f:selectItems value="#{postoservico.pessoas}" var="operador" itemValue="#{operador}"
                                                       itemLabel="#{operador.nome}"/>
                                        <p:ajax event="itemSelect" listener="#{postoservico.selecionarPessoa}" update="msgs pnlBoletim"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-3" style="padding: 6px 6px 6px 3px !important">
                                    <label style="font-weight: bold; text-shadow: 1px 1px #FFF; width: 100% !Important; text-align: center !important; color: orangered">#{localemsgs.Visitas}</label>
                                    <label id="lblQtdeVisitas" style="background-color: lightyellow; border: thin solid orangered; color: orangered; width: 100%; height: 34px !important; border-radius: 3px; text-align: left; font-weight: bold; font-size: 16pt !important; padding-left: 8px !Important; padding-top: 1px !important;text-align: center !important;"><i class="fa fa-cog fa-spin fa-fw"></i></label>
                                </div>
                            </div>

                            <div class="col-md-5 col-sm-5 col-xs-12" style="height: calc(100vh - 308px); border: thin solid #DDD; margin-top: 15px !important;  box-shadow: 2px 2px 4px #DDD; padding: 3px !important">
                                <div id="mapGoogle" style="min-width:100% !important;width:100% !important;max-width:100% !important;"><i class="fa fa-cog fa-spin fa-fw" style="position: absolute; top:0;right:0;bottom:0;left:0;margin:auto; font-size: 32pt; width: 45px; height:45px;"></i></div>
                            </div>

                            <div class="col-md-7 col-sm-7 col-xs-12" style="padding:15px 0px 0px 10px !important;height: calc(100vh - 293px);">
                                <div id="divItensHistorico" class="col-md-12 col-sm-12 col-xs-12" style="height: 100% !important; overflow: auto !important;border: thin solid #DDD; padding: 3px !important;  box-shadow: 2px 2px 4px #DDD;"><i class="fa fa-cog fa-spin fa-fw" style="position: absolute; top:0;right:0;bottom:0;left:0;margin:auto; font-size: 32pt; width: 45px; height:45px;"></i></div>
                            </div> 
                            <script type="text/javascript">
                                // <![CDATA[
                                var map;
                                var ArrayBoletimTrabalho = new Array();

                                #{postoservico.listaTrabalhos}

                                function inicioMapaServico() {
                                    let Altura = $('#mapGoogle').parent('div').height() - 0;

                                    $('#mapGoogle').css('min-height', Altura + 'px');

                                    map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                        zoom: 11,
                                        center: #{postoservico.centro},
                                        gestureHandling: 'cooperative'
                                    });

                                #{postoservico.markers}
                                }

                                function CarregarListaTrabalho(inQtdeVisitas) {
                                    let UltimoGrupo = '', HTML = '', Pergunta = '';

                                    for (I = 0; I < ArrayBoletimTrabalho.length; I++) {
                                        Pergunta = ArrayBoletimTrabalho[I].Pergunta.toString().indexOf('-') > -1 ? ArrayBoletimTrabalho[I].Pergunta.toString().split('-')[1] : ArrayBoletimTrabalho[I].Pergunta.toString();

                                        if (UltimoGrupo !== ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim())
                                            HTML += '<label style="font-size: 14pt; font-weight: 500; width: 100%; color: steelblue; padding: 8px 0px 5px 10px; background-color: #EEE; text-align: left; margin: 0px 0px 5px 0px !important; text-shadow: 1px 1px #FFF; border: thin solid #DDD"><i class="fa fa-check-square-o" aria-hidden="true" style="font-size: 12pt !important;"></i>&nbsp;&nbsp;' + ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim() + '</label>';

                                        HTML += '<label style="font-size: 10pt; color: #666; width: 100%; text-align: left; padding: 0px 0px 5px 0px; border-bottom: 1px dashed #DDD; margin: 0px !important;"><span style="float: left; width: 55%; text-align: right">' + Pergunta + ':</span><span style="float: right; font-weight: 600;width: 45%; text-align: left; padding-left: 8px; color: #303030 !important">' + ArrayBoletimTrabalho[I].Resposta + '</span></label>';

                                        UltimoGrupo = ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim();
                                    }

                                    $('#divItensHistorico').html(HTML);
                                    $('#lblQtdeVisitas').html(inQtdeVisitas);
                                }

                                $(document).ready(function () {
                                    inicioMapaServico();
                                });
                                // ]]>
                            </script>   
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText styleClass="corporativo-label" value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{postoservico.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{postoservico.MostrarFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText styleClass="corporativo-label" value="#{localemsgs.SomenteAtivos}: " /></label>
                                <p:selectBooleanCheckbox value="#{postoservico.somenteAtivos}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{postoservico.SomenteAtivos}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>

                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" action="#{localeController.getLocales}">
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25"/>
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <script>
                // <![CDATA[
                var options = {
                    enableHighAccuracy: true,
                    timeout: 5000,
                    maximumAge: 0
                };
                var LatitudeLogon;
                var LongitudeLogon;
                $(document).ready(function () {
                    if (navigator.geolocation)
                        navigator.geolocation.getCurrentPosition(SucessoCapturaPosicao, ErroCapturaPosicao, options);
                })
                        .on('click', '.FotoPendente', function () {
                            if (navigator.geolocation)
                                navigator.geolocation.getCurrentPosition(SucessoCapturaPosicao, ErroCapturaPosicao, options);
                            $this = $(this);
                            $('[id*="uploadFotosRelatorio_input"]').click();
                        })
                        .on('click', '#btExpandirMapa', function () {
                            $('#divMapaFull').html('<label id="lblTeste"></label>');
                            $('div[id*="gmap"]').insertAfter($('#lblTeste'));
                            $('#divMapaFull').append('<label id="btRecolherMapa" style="position:absolute; top:10px; right:10px; width:60px; height:60px; border-radius:50%; z-index:99999999 !important; cursor:pointer; background-color:#022a48;"><img src="../assets/img/icone_fecharmapa.png" style="max-width:55%; max-height:55%; margin-top:13px; margin-left:14px;" /></label>');
                            $('#divMapaFull').fadeIn();
                            setTimeout(function () {
                                $('div[id*="gmap"]').css({
                                    'position': 'absolute',
                                    'top': '0px',
                                    'height': '100%',
                                    'margin-top': '0px'
                                });
                            }, 500);
                        })
                        .on('click', '#btRecolherMapa', function () {
                            $('div[id*="gmap"]').insertAfter($('#DadosHTML'));
                            $('#divMapaFull').html('');
                            $('div[id*="gmap"]').css({
                                'position': 'relative',
                                'top': '0px',
                                'height': '215px',
                                'margin-top': '6px'
                            });
                            $('#divMapaFull').fadeOut();
                        })
                        ;
                function SucessoCapturaPosicao(position) {
                    LatitudeLogon = position.coords.latitude;
                    LongitudeLogon = position.coords.longitude;
                }

                function AtribuirValorObj() {
                    $('[id*="txtLatitude"]').val(LatitudeLogon);
                    $('[id*="txtLongitude"]').val(LongitudeLogon);
                }

                function ErroCapturaPosicao(error) {

                }

                function ConsultarEndereco() {
                    $('.SemPosto').parents('div[ref="posicao"]').each(function () {
                        $.ajax({
                            url: 'https://maps.googleapis.com/maps/api/geocode/json?key=#{login.googleApiOper}&latlng=' + $(this).attr('posicao').split('|')[0] + ',' + $(this).attr('posicao').split('|')[1],
                            method: 'get'
                        })
                                .done(function (response) {
                                    $('.SemPosto:eq(0)').attr('class', 'ComPosto').text(response.results[0].formatted_address);
                                });
                    });
                }
                // ]]>
            </script>
        </h:body>
    </f:view>
</html>
body {
    background-image: linear-gradient(to right,rgba(136, 125, 125, 0.438), rgb(255, 255, 255),rgba(136, 125, 125, 0.438));
	
}

.frmBiometriaAutentica{
	display: flex;
	justify-content: center;
	align-items: center;

}
	
.frmBiometriaAutentica-corpo{
	
	max-width: 500px;
	
}

label{
    	font-family: Arial, Helvetica, sans-serif;
	font-size: 14px;
	font-weight: normal;
}

.btn{
	background-color: rgb(33, 156,70);
	padding 5px 10px 5px 10px;
	border: 2px solid rgb(4, 92, 30);
	cursor: pointer;
	transition: background 0.3s;
	border-radius: 5px;
	color: #fff;
        margin-top: 1%;
        margin-bottom: 1%;
}
.btn:hover{
	background-color: rgb(4, 92, 30);
	padding 5px 10px 5px 10px;
	border: 2px solid rgb(4, 92, 30);
	border-radius: 5px;
	color: #fff;
}
.btn:active{
	background-color: rgb(129, 255, 166);	
	color: rgb(4, 92, 30);
}
.modal{
	background-color: rgba(0, 0, 0, .8); 
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: none;        
	
}
.modal-content{
   margin: 0 auto;
   margin-top: 5%;
   max-width: 400px;
   background-color: #eee;
   padding: 0;
   box-shadow: 0 0 2px #fff;
   border-radius: 10px;
   
}

.modal-body{
    margin-bottom: 5%;
}

.modal-header, .modal-body, .modal.footer{
	padding: 5px;
}
.modal-header{
    display: flex;
	background-color: #ccc;
   border-radius: 5px;
   align-content: center;
   justify-content: center;
}
.modal-header label{
	font-weight: bold;
	text-align: center;

}
.modal-footer{
    background-color: #ccc;
    display : flex;
    border-radius: 5px;
    justify-content: center;
    align-items : center;
}


#painelMenu{
    background-color: #002172;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}


#editlogo{
    position: relative;
    margin-top: 1%;
    margin-bottom: 1%;
    justify-content: center;
    align-items: center;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

#painelLogin{
    display: flex;
    align-items: center;
    width: 50%;
    padding: 1% 25%;
    justify-content: center;
    flex-direction: column;
}





#painelImagens{
    justify-content: center;
    align-items: center;
    display: flex;
}

#imgSrv1{
    margin-right: 0.5%;
}
#imgSrv2{
    margin-right: 0.5%;
}
#imgLocal1{
    margin-right: 0.5%;
}
#imgLocal2{
    margin-right: 0.5%;
}
#imgAutentica1{
    margin-right: 0.5%;
}
#imgAutentica2{
    margin-right: 0.5%;
}

#btnAutentica{
    justify-content: center;
    display: flex;
    align-items: center;
    
}


  #control {
    margin-top: 3px;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    background-color: #f2f2f2;
    padding: 10px;
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    min-width: 400px;
  }
  
  
#webCamera{
    
    width: 100%;
    position: relative;
    min-width: 360px;
    min-height: 230px;
    margin-left: -215px;


  }
  canvas {
    position: absolute;
    top: 200px;
    height: auto;
    display: flex;
    margin: auto;
  }

  #border_wrap {
    margin: 20px;
    -width: 330px;
    -height: 420px;
    padding: 0px;
	
    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
    -webkit-transform: rotate(0.000001deg); 
    -webkit-border-radius: 100%;  
    -moz-border-radius: 100%;
  }
  
#painelFotoReconhecida {
    margin: 20px;
    -width: 330px;
    -height: 420px;
    padding: 0px;
		
    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
    -webkit-transform: rotate(0.000001deg); 
    -webkit-border-radius: 100%; 
    -moz-border-radius: 100%;
  }
@media only screen and (max-width: 440px){

    .acesso{
        margin-top: 2%;
    
    }
	.btn{
	background-color: rgb(33, 156,70);
	padding 1px 2px 1px 2px;
	border: 2px solid rgb(4, 92, 30);
	cursor: pointer;
	transition: background 0.3s;
	border-radius: 10px;
	color: #fff;
        margin-top: 1%;
        margin-bottom: 1%;
	height: 50%;
	width: 50px;
}

  }

  @media (max-width:1024px) {

    nav #container-top{
      flex-direction: column;
        text-align: center;
    }
  
    nav ul  {
        flex-wrap: wrap;
        justify-content: center;
      }
     nav ul li a input{
        margin-top: 10px;
    }
    nav ul li a #token{
      align-items: center;
      justify-content: center;
      margin-bottom: 10%;
    }

    header.cards section.card {
      width: 100%;
      height: 100%;
      max-height: 468px;
      margin-left: 32px;
  }

  header.cards section.card.video{
    width: 150%;
  }

  #border_wrap {
    width: 100%;
    min-width: 240px;
  }

  #painelWebCamera{
    width: 100%;
    min-width: 700px;
    min-height: 460px;
    margin-left: -215px;

  }
    
}
@media screen and (max-width: 720px) {
  header.cards {
      flex-direction: column;
  }

  header.cards section.card {
      margin-left: 0;
      margin-bottom: 32px;
  }

  header.cards section.card:last-child {
      margin-bottom: 0;
  }

  header.cards section.card {
    width: 100%;
    height: 100%;
    max-height: 468px;
}

header.cards section.card.video{
  width: 100%;
}



}
@media only screen and (max-width: 440px){
  
  #imgSrv1{
    float: left;
    margin-left: 10%;
    width: 70px;
    height: 70px;
  }

  #imgSrv2{
    float: left;
    margin-left: 10%;
    width: 70px;
    height: 70px;
  }

  #imgLocal1{
    float: left;
    margin-left: 10%;
    width: 70px;
    height: 70px;
  }

  #imgLocal2{
    float: left;
    margin-left: 10%;
    width: 70px;
    height: 70px;
  }

  #imgAutentica1{
    float: left;
    margin-left: 10%;
    width: 70px;
    height: 70px;
  }

  #imgAutentica2{
    float: left;
    margin-left: 10%;
    width: 70px;
    height: 70px;
  }

  #border_wrap {
    width: 100%;
    min-width: 240px;
    min-height: 240px;
    padding: 2px;
  }

  #painelWebCamera{
    width: 100%;
    min-width: 240px;
    min-height: 240px;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
  }

  body{
    background-size: calc(100vh + 0px + 180px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #103ccc;
    font-family:  'Trebuchet MS', sans-serif;
  }

  .navbar.navbar{
    width: 100%;
  }

  input {
    padding: 5px 20px;
    border-radius: 12px;
    border-width: 0px;
    min-width: 20px;
  }

  
  #btnEnviarMatr_{
    /*margin-left: 100%;
    margin-top: 50%;*/
    padding: 0px 0px;
    margin-left: 105% !important;
    margin-top: 35%;
  }

  #editMatr{
    min-width: auto;
    width: 80%;
    margin-top: 1%;
  }

  #editNome{
    min-width: auto;
    max-width: 80%;
    margin-top: 2%;
  }


  canvas{
    width: 100%;
    min-width: 240px;
    min-height: 240px;
  }

  button{
    width: 150px !important;
    height: 80px !important;
  }

}
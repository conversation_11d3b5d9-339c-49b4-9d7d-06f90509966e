package br.com.sasw.pacotesuteis.sasdaos.compostas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Funcion;
import SasBeans.Saspwac;
import SasBeansCompostas.FuncSaspwSaspwac;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FuncSaspwSaspwacDao {

    String sql;

    /**
     * Retorna dados da função
     *
     * @param sMatricula - código da matrícula
     * @param sPwportal - Código
     * @param sSituacao - Situação
     * @param persistencia - conexão com o banco
     * @return - retorna dados da função
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<FuncSaspwSaspwac> getDadosFunc(String sMatricula, String sPwportal, String sSituacao, Persistencia persistencia) throws Exception {
        List<FuncSaspwSaspwac> lFuncSaspwSaspwac = new ArrayList();
        FuncSaspwSaspwac oFuncSaspwSaspwac = null;
        sql = "select Funcion.Nome_Guer, Funcion.CodFil, "
                + " saspwac.inclusao, saspwac.alteracao, saspwac.exclusao, Funcion.Matr, Funcion.sexo"
                + " from Pessoa as Pessoa "
                + " left join Funcion as Funcion on Funcion.Matr = Pessoa.Matr"
                + " left join aspw on saspw.codpessoa = pessoa.codigo"
                + " left join saspwac on saspwac.nome=saspw.nome and saspwac.sistema=040191"
                + " where Pessoa.Matr = ?"
                + " and Pessoa.PWPortal = ?"
                + " and Funcion.Situacao <> 'D'";
        try {
            Consulta cFuncSaspwSaspwac = new Consulta(sql, persistencia);
            cFuncSaspwSaspwac.setString(sMatricula);
            cFuncSaspwSaspwac.setString(sPwportal);
            cFuncSaspwSaspwac.setString(sSituacao);
            cFuncSaspwSaspwac.select();

            while (cFuncSaspwSaspwac.Proximo()) {
                oFuncSaspwSaspwac = new FuncSaspwSaspwac();
                Funcion oFuncion = new Funcion();
                Saspwac oSaspwac = new Saspwac();
                oFuncion.setNome_Guer(cFuncSaspwSaspwac.getString("Nome_Guer"));
                oFuncion.setCodFil(cFuncSaspwSaspwac.getString("CodFil"));
                oFuncion.setMatr(cFuncSaspwSaspwac.getString("Matr"));
                oFuncion.setSexo(cFuncSaspwSaspwac.getString("sexo"));
                oSaspwac.setInclusao(Integer.parseInt(cFuncSaspwSaspwac.getString("inclusao")));
                oSaspwac.setAlteracao(Integer.parseInt(cFuncSaspwSaspwac.getString("alteracao")));
                oSaspwac.setExclusao(Integer.parseInt(cFuncSaspwSaspwac.getString("exclusao")));
                oFuncSaspwSaspwac.setFuncion(oFuncion);
                oFuncSaspwSaspwac.setSaspwac(oSaspwac);
            }
            cFuncSaspwSaspwac.Close();
            lFuncSaspwSaspwac.add(oFuncSaspwSaspwac);
            return lFuncSaspwSaspwac;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar Funções - " + e.getMessage());
        }

    }
}

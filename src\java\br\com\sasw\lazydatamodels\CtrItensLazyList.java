/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.OS_Vig.OS_VigSatMobWeb;
import Dados.Persistencia;
import SasBeans.CtrItens;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class CtrItensLazyList extends LazyDataModel<CtrItens> {

    private static final long serialVersionUID = 1L;
    private List<CtrItens> itens = null;
    private final Persistencia persistencia;
    private final OS_VigSatMobWeb osVigController;
    private final String idContrato;

    public CtrItensLazyList(String idContrato, Persistencia pst) {
        this.idContrato = idContrato;
        this.persistencia = pst;
        osVigController = new OS_VigSatMobWeb();
    }

    @Override
    public List<CtrItens> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            itens = osVigController.listarItensPaginada(first, pageSize, idContrato, filters, persistencia);

            // set the total of players
            setRowCount(osVigController.contagemItens(idContrato, filters, persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return itens;
    }

    @Override
    public Object getRowKey(CtrItens contrVig) {
        if (null == contrVig.getCodFil()
                || null == contrVig.getContrato()
                || null == contrVig.getTipoPosto()) {
            return null;
        }
        return contrVig.getCodFil().replace(".0", "")
                + ";" + contrVig.getContrato()
                + ";" + contrVig.getTipoPosto();
    }

    @Override
    public CtrItens getRowData(String rowKey) {
        String[] codFilContratoArray = rowKey.split(";");
        if (codFilContratoArray.length != 3) {
            return null;
        }
        for (CtrItens item : itens) {
            if (codFilContratoArray[0].equals(item.getCodFil().replace(".0", ""))
                    && codFilContratoArray[1].equals(item.getContrato())
                    && codFilContratoArray[2].equals(item.getTipoPosto())) {
                return item;
            }
        }
        return null;
    }
}

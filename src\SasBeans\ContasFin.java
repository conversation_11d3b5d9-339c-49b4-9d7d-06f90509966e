package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class ContasFin {

    private Integer Codigo;
    private String Invest;
    private String Descricao;
    private Integer Natureza;
    private String Conta_Ctb;
    private Integer Grupo;
    private Integer GrupoFlx;
    private String DFin;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public ContasFin() {
        this.Codigo = 0;
        this.Invest = "";
        this.Descricao = "";
        this.Natureza = 0;
        this.Conta_Ctb = "";
        this.Grupo = 0;
        this.GrupoFlx = 0;
        this.DFin = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
    }

    public Integer getCodigo() {
        return Codigo;
    }

    public void setCodigo(Integer Codigo) {
        this.Codigo = Codigo;
    }

    public String getInvest() {
        return Invest;
    }

    public void setInvest(String Invest) {
        this.Invest = Invest;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public Integer getNatureza() {
        return Natureza;
    }

    public void setNatureza(Integer Natureza) {
        this.Natureza = Natureza;
    }

    public String getConta_Ctb() {
        return Conta_Ctb;
    }

    public void setConta_Ctb(String Conta_Ctb) {
        this.Conta_Ctb = Conta_Ctb;
    }

    public Integer getGrupo() {
        return Grupo;
    }

    public void setGrupo(Integer Grupo) {
        this.Grupo = Grupo;
    }

    public Integer getGrupoFlx() {
        return GrupoFlx;
    }

    public void setGrupoFlx(Integer GrupoFlx) {
        this.GrupoFlx = GrupoFlx;
    }

    public String getDFin() {
        return DFin;
    }

    public void setDFin(String DFin) {
        this.DFin = DFin;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

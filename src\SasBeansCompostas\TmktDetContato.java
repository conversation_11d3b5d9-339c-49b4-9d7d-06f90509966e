/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Contatos;
import SasBeans.TmktDet;

/**
 *
 * <AUTHOR>
 */
public class TmktDetContato {

    private TmktDet tmktDet;
    private Contatos contato;

    public TmktDet getTmktDet() {
        return tmktDet;
    }

    public void setTmktDet(TmktDet tmktDet) {
        this.tmktDet = tmktDet;
    }

    public Contatos getContato() {
        return contato;
    }

    public void setContato(Contatos contato) {
        this.contato = contato;
    }

}

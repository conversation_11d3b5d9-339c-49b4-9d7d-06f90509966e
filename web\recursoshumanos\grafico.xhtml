<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/graficos.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet"  media="print"/>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script type="text/javascript">
                function chartExtender() {
                    this.cfg.axes.yaxis.tickOptions = {
                        textColor: 'white'
                    };
                    this.cfg.axes.xaxis.tickOptions = {
                        textColor: 'white'
                    };
                    this.cfg.axes.yaxis.labelOptions = {
                        textColor: 'white'
                    };
                    this.cfg.axes.xaxis.labelOptions = {
                        textColor: 'white'
                    };
                }
                function selectSideBarLink(link) {
                    jQuery("#menu").find(".ui-state-highlight").removeClass("ui-state-highlight");
                    if (link) {
                        jQuery(link).addClass("ui-state-highlight");
                    }
                }
            </script>
        </h:head>
        <h:body id="graficosH"> 
            <div id="top">
                <f:metadata>
                    <f:viewAction action="#{(graficomb.Persistencia(login.pp))}"/>
                </f:metadata>
                <p:growl id="msgs"/>
                <header>
                    <h:form id="cabecalho" >
                        <div class="ui-grid ui-grid-responsive" >
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-5">
                                    <img src="../assets/img/icone_satmob_graficosG.png" height="40" width="40"/>
                                    #{localemsgs.Grafico}
                                </div>
                                <div id="periodo" class="ui-grid-col-4" style="align-self: center; width: 30%; color: black; text-align: center;">
                                    <h:outputText value="#{localemsgs.Periodo}: "/>
                                    <h:outputText value="#{graficomb.dataInicio}" converter="conversorData" />
                                    <h:outputText value=" - "/>
                                    <h:outputText value="#{graficomb.dataFim}" converter="conversorData"/>
                                </div>
                                <div class="ui-grid-col-3" style="align-self: center; text-align: center;">
                                    <p:commandLink oncomplete="PF('dlgCalendarios').show();" styleClass="botao" update="cabecalho">
                                        <p:graphicImage url="../assets/img/icone_escaladodia_40.png" style="align-self: center;height: 40px"/>
                                    </p:commandLink>
                                </div>
                                <div style=" top: 0px; right: 5px;">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   action="#{login.voltar}">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial" style="background: transparent">
                                    <div class="ui-grid-col-4" style="color: #000000">
                                        #{localemsgs.Filial}: #{clientes.filialDesc}
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>
            </div>
            <div class="centro">
                <div class="leftmenu" id="left" >
                    <h:form id="menu">
                        <img id="logo" src="../assets/img/icone_logo_graficos.png"/>
                        <p:menu style="background-color: transparent; width: 40px">
                            <p:menuitem id="btnHoraExtra"  onclick="selectSideBarLink(this)" icon="ui-icon-clock" style="color: #FFF;" value="#{localemsgs.HorasExtras}" action="#{graficomb.mostraHoraUltimo}" update="graficos" ></p:menuitem>
                            <p:menuitem id="btnFaltas" onclick="selectSideBarLink(this)" style="color: #FFF" icon="ui-icon-circle-close" value="#{localemsgs.Faltas}" action="#{graficomb.mostraFaltas}" update="graficos" ></p:menuitem>
                            <p:menuitem id="btnSuspensao" onclick="selectSideBarLink(this)" style="color: #FFF" icon="ui-icon-alert" value="#{localemsgs.Suspensao}" action="#{graficomb.mostraSuspensoes}" update="graficos" ></p:menuitem>
                            <p:menuitem id="btnAtestados" onclick="selectSideBarLink(this)" style="color: #FFF" icon="ui-icon-contact" value="#{localemsgs.Atestado}" action="#{graficomb.mostraAtestado}" update="graficos" ></p:menuitem>
                        </p:menu>
                    </h:form>
                </div>
                <div id="content" class="left_content" >
                    <h:form id="graficos" >
                        <p:panel id="HorasExtras" class="btnhe" style="background: transparent;" rendered="#{graficomb.menuEscolha eq 'HoraExtra'}" >
                            <h:panelGrid columns="2" style="margin-left: 5px; width:100%; background: transparent; " >
                                <p:chart type="pie"  model="#{graficomb.pieModelHE50}" rendered="#{graficomb.pieModelHE50 ne null}" style= " height: 330px; width:400px ;color: black; font-size: 13px" responsive="true"/> 
                                <p:chart type="line" model="#{graficomb.lineModelHEs}" rendered="#{graficomb.lineModelHEs ne null}" style=" height: 330px; width:600px ;color: black;font-size: 13px" responsive="true" /> 
                                <p:chart type="pie" model="#{graficomb.pieModelHE100}" rendered="#{graficomb.pieModelHE100 ne null}" style="height: 330px; width:400px ;color: black; font-size: 13px" responsive="true" />
                            </h:panelGrid>
                        </p:panel>
                        <p:panel style="background: transparent " rendered="#{graficomb.menuEscolha eq 'FaltasUltimo'}">
                            <h:panelGrid id="Faltas" columns="4" style="margin-left: 5px; width:100%" >
                                <p:chart type="pie" model="#{graficomb.pieModelUltimoMes}" rendered="#{graficomb.pieModelUltimoMes ne null}" style="height: 330px; width:400px ; color: black; font-size: 13px" responsive="true" />                     
                                <p:chart type="line" model="#{graficomb.lineModelEvoFaltas}" rendered="#{graficomb.lineModelEvoFaltas ne null}" style="height: 330px; width:600px ; color: black; font-size: 13px" responsive="true" /> 
                            </h:panelGrid>
                        </p:panel> 
                        <p:panel style="background: transparent" rendered="#{graficomb.menuEscolha eq 'Suspencao'}">
                            <h:panelGrid id="Supensao" columns="4" style="margin-left: 5px; width:100%" >
                                <p:chart type="pie" model="#{graficomb.pieModelSuspUltimo}" rendered="#{graficomb.pieModelSuspUltimo ne null}" style="height: 330px; width:400px ; color: black; font-size: 13px" responsive="true"  /> 
                                <p:chart type="line" model="#{graficomb.lineModelEvoSuspensoes}" rendered="#{graficomb.lineModelEvoSuspensoes ne null}" style="height: 330px; width:600px ; color: black; font-size: 13px" responsive="true" /> 
                            </h:panelGrid>
                        </p:panel> 
                        <p:panel style="background: transparent" rendered="#{graficomb.menuEscolha eq 'Atestado'}">
                            <h:panelGrid id="Atestados" columns="4" style="margin-left: 5px; width:100%" >
                                <p:chart type="pie" model="#{graficomb.pieModelAtUltimoMes}" rendered="#{graficomb.pieModelAtUltimoMes ne null}" style="height: 330px; width:400px ; color: black; font-size: 13px" responsive="true"  /> 
                                <p:chart type="line" model="#{graficomb.lineModelEvoAtMedicos}" rendered="#{graficomb.lineModelEvoAtMedicos ne null}" style="height: 330px; width:600px ; color: black; font-size: 13px" responsive="true"/> 
                            </h:panelGrid>
                        </p:panel>
                    </h:form>
                </div>
            </div>
            <h:form id="main">
                <p:dialog widgetVar="dlgCalendarios"  positionType="absolute" responsive="true" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" styleClass="dialogo"
                          hideEffect="drop" closeOnEscape="false">
                    <div class="ui-grid-row ui-grid-responsive">
                        <div>
                            <p:panel style="width: 100%; background: transparent">    
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2, ui-grid-col-10" styleClass="ui-panelgrid-blank" style="width: 100%;">
                                    <p:panelGrid columns="1">
                                        <p:outputLabel for="codfil" style="color: black;" value="#{localemsgs.Filial}: "/>
                                    </p:panelGrid>
                                    <p:panelGrid columns="1" >
                                        <p:selectCheckboxMenu class="ui-selectcheckboxmenu-header" id="codfil" value="#{graficomb.selecao}" converter="omnifaces.SelectItemsConverter"
                                                               styleClass="checkBoxMenuClass" panelStyle="width:430px" filterMatchMode="startsWith" label="#{localemsgs.SelecioneFilial}" 
                                                               filter="true" multiple="true">
                                            <f:selectItems value="#{login.listaFiliais}" var="filial" itemValue="#{filial.codfilAc.toString()}"
                                                           itemLabel="#{filial.descricao}"/>
                                        </p:selectCheckboxMenu>
                                    </p:panelGrid>
                                </p:panelGrid>
                            </p:panel>   
                            <p:spacer width="5px"/>
                        </div>
                        <div class="ui-grid-col-6">
                            <div class="ui-grid-row">
                                <div style="width: 50%; float: left">
                                    <p:outputLabel for="call" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}"/>
                                </div>
                                <div style="width: 50%; float: left;">
                                    <p:inputText id="empty" style="width: 0px; z-index: -1; position: fixed"/>
                                    <p:inputMask id="call" mask="99/99/9999" style="width: 85px" value="#{graficomb.dt_Ini}" converter="conversorData">
                                        <p:ajax event="blur" listener="#{graficomb.escreverData1}" update="calendario1"/>
                                    </p:inputMask>
                                </div>
                            </div>
                            <p:calendar id="calendario1" styleClass="calendario" mode="inline" title="#{localemsgs.DataInicial}"
                                        label="#{localemsgs.DataInicial}" pattern="yyyyMMdd" value="#{graficomb.dataSelecionada1}"
                                        locale="#{localeController.getCurrentLocale()}" >
                                <p:ajax event="dateSelect" listener="#{graficomb.selectionarData1}" update="call"/>
                            </p:calendar>
                        </div>

                        <div class="ui-grid-col-6">
                            <div class="ui-grid-row">
                                <div style="width: 50%; float: left">
                                    <p:outputLabel for="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}"/>
                                </div>
                                <div style="width: 50%; float: left;">
                                    <p:inputMask id="cal2" mask="99/99/9999" style="width: 85px" value="#{graficomb.dt_Fim}" converter="conversorData">
                                        <p:ajax event="blur" listener="#{graficomb.escreverData2}" update="calendario2"/>
                                    </p:inputMask>
                                </div>
                            </div>
                            <p:calendar id="calendario2" styleClass="calendario" mode="inline" title="#{localemsgs.DataInicial}"
                                        label="#{localemsgs.DataInicial}" value="#{graficomb.dataSelecionada2}"  pattern="yyyyMMdd"
                                        locale="#{localeController.getCurrentLocale()}">
                                <p:ajax event="dateSelect" listener="#{graficomb.selectionarData2}" update="cal2" />
                            </p:calendar>
                        </div>
                    </div>
                    <div style="text-align: right; float: right">
                        <p:commandLink style="float: right" action="#{graficomb.selecionarData()}" oncomplete="PF('dlgCalendarios').hide();"
                                       update="graficos">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" height="40"/>
                        </p:commandLink>
                    </div>
                </p:dialog>    
            </h:form>    
            <p:growl id="growl" sticky="true" showDetail="true" />
            
            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
            <div id="bottom">
                <footer>
                    <div class="footer-toggler">
                        <a  href="#footer-toggle" id="footer-toggle" >
                            <i class="fa fa-bars" style="font-size: 18px"></i>
                        </a>
                    </div>
                    <div class="footer-body" id="footer-body">
                        <div>
                            <h:form id="corporativo">
                                <h:outputText value="#{localemsgs.Corporativo}: " />
                                <p:selectBooleanCheckbox value="#{graficomb.mostrarFiliais}">
                                    <p:ajax update="graficos" listener="#{graficomb.MostrarFiliais}"/>
                                </p:selectBooleanCheckbox>

                            </h:form>
                        </div>
                        <div class="container">
                            <div class="col-sm-3">
                                <table class="footer-time">
                                    <tr>
                                        <td>
                                            <p:clock pattern="HH:mm:ss" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <h:outputText value="#{localeController.mostraData}" />
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-sm-6">
                                <table class="footer-user">
                                    <tr>
                                        <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                        <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-sm-3">
                                <table class="footer-logos">
                                    <tr>
                                        <td><img src="../assets/img/logo_satweb.png" /></td>
                                        <td>
                                            <h:form>
                                                <h:commandLink 
                                                    action="#{graficomb.listar()}">
                                                    <f:actionListener binding="#{localeController.increment()}"/>
                                                    <f:actionListener binding="#{localeController.getLocales()}"/>
                                                    <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                                </h:commandLink>
                                            </h:form>   
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>   
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>
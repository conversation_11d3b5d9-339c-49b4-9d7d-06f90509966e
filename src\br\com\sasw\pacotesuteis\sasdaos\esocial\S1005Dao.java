/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1005;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1005Dao {

    public List<S1005> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select Filiais.CNAE dadosEstab_cnaePrep, Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, Filiais.RAT aliqGilrat_aliqRat, Filiais.FAP aliqGilrat_fap, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1005' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1005' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Filiais.CNPJ "
                    + "             and z.evento = 'S-1005' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " from Filiais "
                    + " Where Filiais.codFil = ? "
                    + " ORDER BY sucesso asc, Filiais.CNPJ asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.select();
            List<S1005> retorno = new ArrayList<>();
            S1005 s1005;
            while (consulta.Proximo()) {
                s1005 = new S1005();
                s1005.setSucesso(consulta.getInt("sucesso"));
                s1005.setIdeEvento_procEmi("1");
                s1005.setIdeEvento_verProc("Satellite eSocial");
                s1005.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1005.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s1005.setIdeEstab_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1005.setIdeEstab_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s1005.setDadosEstab_cnaePrep(consulta.getString("dadosEstab_cnaePrep"));
                s1005.setAliqGilrat_aliqRat(consulta.getString("aliqGilrat_aliqRat"));
                s1005.setAliqGilrat_fap(consulta.getString("aliqGilrat_fap"));
                s1005.setRegPt_regPt("1");
                s1005.setContApr_contApr("0");
                retorno.add(s1005);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1005Dao.get - " + e.getMessage() + "\r\n"
                    + " Select Filiais.CNAE dadosEstab_cnaePrep, Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, Filiais.RAT aliqGilrat_aliqRat, Filiais.FAP aliqGilrat_fap, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "         and z.evento = 'S-1005' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "         and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "         and z.evento = 'S-1005' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "         and z.evento = 'S-1005' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " from Filiais "
                    + " Where Filiais.codFil = " + codFil
                    + " ORDER BY sucesso asc, substring(Filiais.CNPJ,1,8) asc ");
        }
    }
}

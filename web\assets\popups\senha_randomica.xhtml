<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <ui:fragment rendered="#{solicitacaoSenhaMB.temPermissao}">
        <!--Este fragmento já assume que o Google Maps esteja carregado externamente-->

        <link type="text/css" href="../assets/css/animate.css" rel="stylesheet"/>
        <h:outputScript name="js/solicitacao_senha_mobile.js"/>

        <style>
            .botoesDataTable {
                width: 40px;
                margin-top: 8px;
                position: absolute;
                right: -15px; top: 50%;
                transform: translateY(-50%);
            }

            /* Remove padding de header de diálogo e de header de datatable */
            #dialogSenhaRandomica.ui-dialog .ui-dialog-titlebar,
            #dialogSenhaRandomica .DataGrid thead tr th{
                padding-top: 0 !important;
                padding-bottom: 0 !important;
            }

            td.centeredColumnContent{
                text-align: center;
            }

            #mapGoogleSolicitacaoSenha {
                height: 200px;
                width: 500px;
                border:thin solid #CCC;
                box-shadow:3px 3px 4px #DDD;
                padding:0px !important;
            }

            .modalSolicitacao {
                width: 500px !important;
            }

            @media only screen and (max-width: 700px) and (min-width: 10px) {
                #mapGoogleSolicitacaoSenha {
                    width: 300px;
                }

                .modalSolicitacao {
                    width: 300px !important;
                }
            }
        </style>

        <p:dialog header="#{localemsgs.SolicitacaoSenhaMobile} - #{solicitacaoSenhaMB.codFil}"
                  widgetVar="dlgSolicitacaoSenha"
                  minHeight="30"
                  modal="true"
                  closable="true"
                  id="dialogSenhaRandomica"
                  onHide="fetchPanicStatus();"
                  resizable="false">

            <h:form id="solicitacaoSenhaMobileForm">
                <p:remoteCommand
                    name="atualizarSolicitacaoSenha"
                    actionListener="#{solicitacaoSenhaMB.atualizar()}"
                    update="msgs @form:tabela @form:omitir @form:distancia"
                    global="false"
                    />

                <p:panel
                    id="panel"
                    class="modalSolicitacao"
                    >
                    <div style="position: relative; margin-bottom: 10px;">
                        <p:panel style="width: calc(100% - 30px);">
                            <p:dataTable
                                id="tabela"
                                value="#{solicitacaoSenhaMB.solicitacoes}"
                                var="solicitacao"
                                rowKey="#{solicitacao.rota}-#{solicitacao.parada}-#{solicitacao.sequencia}-#{solicitacao.tipoOperacao}"
                                selection="#{solicitacaoSenhaMB.solicitacaoSelecionada}"
                                selectionMode="single"
                                class="DataGrid"
                                resizableColumns="true"
                                reflow="true"
                                scrollable="true"
                                scrollWidth="100%"
                                scrollHeight="125"
                                emptyMessage="#{localemsgs.SemRegistros}"
                                >
                                <p:ajax event="rowSelect"
                                        update="msgs @form:panel:distancia"
                                        listener="#{solicitacaoSenhaMB.onRowSelected()}"
                                        />
                                <p:column headerText="#{localemsgs.Rota}" styleClass="centeredColumnContent">
                                    <h:outputText value="#{solicitacao.rota}" title="#{localemsgs.Rota}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Parada}">
                                    <h:outputText value="#{solicitacao.parada}" title="#{localemsgs.Parada}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Sequencia}">
                                    <h:outputText value="#{solicitacao.sequencia}" title="#{localemsgs.Sequencia}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.CodFil}">
                                    <h:outputText value="#{solicitacao.codFil}" title="#{localemsgs.CodFil}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TipoOperacao}" styleClass="centeredColumnContent">
                                    <h:outputText value="#{solicitacao.tipoOperacao}" title="#{localemsgs.TipoOperacao}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.LocalSolic}">
                                    <h:outputText value="#{solicitacao.localSolic}" title="#{localemsgs.LocalSolic}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HrSolic}" styleClass="centeredColumnContent">
                                    <h:outputText value="#{solicitacao.hrSolic}" title="#{localemsgs.HrSolic}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ChEquipe}">
                                    <h:outputText value="#{solicitacao.chEquipe}" title="#{localemsgs.ChEquipe}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.ChaveSrv}">
                                    <h:outputText value="#{solicitacao.chaveSrv}" title="#{localemsgs.ChaveSrv}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.CodPessoa}">
                                    <h:outputText value="#{solicitacao.codPessoa}" title="#{localemsgs.CodPessoa}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.FechIdentif}">
                                    <h:outputText value="#{solicitacao.fechIdentif}" title="#{localemsgs.FechIdentif}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Comando_Ref}">
                                    <h:outputText value="#{solicitacao.comando_Ref}" title="#{localemsgs.Comando_Ref}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Senha}">
                                    <h:outputText value="#{solicitacao.senha}" title="#{localemsgs.Senha}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Data}">
                                    <h:outputText value="#{solicitacao.data}" title="#{localemsgs.Data}"  converter="conversorData"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.SrvOK}">
                                    <h:outputText value="#{solicitacao.srvOK}" title="#{localemsgs.SrvOK}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.PW}">
                                    <h:outputText value="#{solicitacao.PW}" title="#{localemsgs.PW}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.MatrChe}">
                                    <h:outputText value="#{solicitacao.matrChe}" title="#{localemsgs.MatrChe}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.PWVig1}">
                                    <h:outputText value="#{solicitacao.PWVig1}" title="#{localemsgs.PWVig1}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Comando_Crt}">
                                    <h:outputText value="#{solicitacao.comando_Crt}" title="#{localemsgs.Comando_Crt}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.MatrVig1}" styleClass="centeredColumnContent">
                                    <h:outputText value="#{solicitacao.matrVig1}" title="#{localemsgs.MatrVig1}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.SeqRota}">
                                    <h:outputText value="#{solicitacao.seqRota}" title="#{localemsgs.SeqRota}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Veiculo}" styleClass="centeredColumnContent">
                                    <h:outputText value="#{solicitacao.veiculo ? localemsgs.Sim : localemsgs.Nao}"
                                                  title="#{localemsgs.Veiculo}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Latitude}">
                                    <h:outputText value="#{solicitacao.latitude}" title="#{localemsgs.Latitude}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Longitude}">
                                    <h:outputText value="#{solicitacao.longitude}" title="#{localemsgs.Longitude}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.CodCli1}" styleClass="centeredColumnContent">
                                    <h:outputText value="#{solicitacao.codCli1}" title="#{localemsgs.CodCli1}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Hora1}">
                                    <h:outputText value="#{solicitacao.hora1}" title="#{localemsgs.Hora1}" converter="conversorHora"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Placa}" styleClass="centeredColumnContent">
                                    <h:outputText value="#{solicitacao.placa}" title="#{localemsgs.Placa}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Comando_Aceito}">
                                    <h:outputText value="#{solicitacao.comando_Aceito}" title="#{localemsgs.Comando_Aceito}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.TpVeic}">
                                    <h:outputText value="#{solicitacao.tpVeic}" title="#{localemsgs.TpVeic}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.TipoFechVei}">
                                    <h:outputText value="#{solicitacao.tipoFechVei}" title="#{localemsgs.TipoFechVei}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.TipoFechCli}">
                                    <h:outputText value="#{solicitacao.tipoFechCli}" title="#{localemsgs.TipoFechCli}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.StatusFechCli}">
                                    <h:outputText value="#{solicitacao.statusFechCli}" title="#{localemsgs.StatusFechCli}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.StatusFechVei}">
                                    <h:outputText value="#{solicitacao.statusFechVei}" title="#{localemsgs.StatusFechVei}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.CodigoFechCli}">
                                    <h:outputText value="#{solicitacao.codigoFechCli}" title="#{localemsgs.CodigoFechCli}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.CodigoFechVei}">
                                    <h:outputText value="#{solicitacao.codigoFechVei}" title="#{localemsgs.CodigoFechVei}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.SenhaUsr1FechCli}">
                                    <h:outputText value="#{solicitacao.senhaUsr1FechCli}" title="#{localemsgs.SenhaUsr1FechCli}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.SenhaUsr1FechVei}">
                                    <h:outputText value="#{solicitacao.senhaUsr1FechVei}" title="#{localemsgs.SenhaUsr1FechVei}"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.MACFechCli}">
                                    <h:outputText value="#{solicitacao.MACFechCli}" title="#{localemsgs.MACFechCli}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.MACFechVei}">
                                    <h:outputText value="#{solicitacao.MACFechVei}" title="#{localemsgs.MACFechVei}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column rendered="false" headerText="#{localemsgs.Cheque}">
                                    <h:outputText value="#{solicitacao.cheque}" title="#{localemsgs.Cheque}"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>

                        <div class="botoesDataTable">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                <p:commandLink title="#{localemsgs.RecarregarSenhas}"
                                               actionListener="#{solicitacaoSenhaMB.carregarLista()}"
                                               process="@form"
                                               update="msgs @form:panel:tabela">
                                    <p:graphicImage url="/assets/img/icone_atualizar.png" height="40"/>
                                </p:commandLink>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                <p:commandLink title="#{localemsgs.AprovarSenha}"
                                               actionListener="#{solicitacaoSenhaMB.aprovarSelecionado()}"
                                               process="@form"
                                               update="msgs @form:panel:tabela">
                                    <p:graphicImage url="/assets/img/icone_adicionar.png" height="40"/>
                                </p:commandLink>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                <p:commandLink title="#{localemsgs.ReprovarSenha}"
                                               actionListener="#{solicitacaoSenhaMB.reprovarSelecionado()}"
                                               process="@form"
                                               update="msgs @form:panel:tabela">
                                    <p:graphicImage url="/assets/img/icone_fechar.png" height="40"/>
                                </p:commandLink>
                            </div>
                        </div>
                    </div>

                    <div style="margin: 8px auto;">
                        <p:selectBooleanCheckbox
                            id="omitir"
                            value="#{solicitacaoSenhaMB.omitirNaoConfirmado}"
                            itemLabel="#{localemsgs.OmitirSolicitacoes}"
                            style="padding-right: 15px;"
                            >
                            <p:ajax
                                event="change"
                                listener="#{solicitacaoSenhaMB.carregarLista()}"
                                partialSubmit="true"
                                update="msgs @form:tabela senhaForm:panel"
                                process="@this"
                                />
                        </p:selectBooleanCheckbox>
                    </div>

                    <div>
                        <div id="mapGoogleSolicitacaoSenha"
                             class="col-md-12 col-sm-12 col-xs-12"/>
                    </div>

                    <p:panel id="distancia"
                             style="visibility: #{empty solicitacaoSenhaMB.solicitacaoSelecionada ? 'hidden' : 'visible'}">
                        <p:outputLabel value="#{localemsgs.Distancia}:&#160;"/>
                        <h:outputText value="#{solicitacaoSenhaMB.solicitacaoSelecionada.distanciaAteCliente} km"
                                      style="width: 100%;"/>
                    </p:panel>
                </p:panel>
            </h:form>
        </p:dialog>

        <p:dialog header="#{localemsgs.SolicitacaoSenhaMobile} - #{solicitacaoSenhaMB.codFil}"
                  widgetVar="dlgPedirSenha"
                  minHeight="40"
                  resizable="false"
                  modal="true"
                  >
            <h:form id="senhaForm">
                <f:facet name="header">
                    <h:outputText value="#{localemsgs.FechaduraDesconectada}" style="color:black" />
                </f:facet>

                <p:panel
                    id="panel"
                    class="modalSolicitacao"
                    >
                    <p:outputLabel for="senha" value="#{localemsgs.InformeSenhaGerada}"/>

                    <p:inputText value="#{solicitacaoSenhaMB.senha}"
                                 placeholder="#{localemsgs.Senha}"
                                 id="senha"
                                 style="width: 100%;">
                        <f:convertNumber pattern="000"/>
                    </p:inputText>

                    <p:commandButton
                        action="#{solicitacaoSenhaMB.atribuirSenha()}"
                        update="msgs"
                        styleClass="botao"
                        value="#{localemsgs.Cadastrar}"
                        />

                    <p:commandButton
                        widgetVar="dlgCancelarSenha"
                        style="display: none;"
                        action="#{solicitacaoSenhaMB.cancelarUpdate()}"
                        update="@form">
                        <p:confirm
                            header="#{localemsgs.Confirmacao}"
                            message="#{localemsgs.CancelarFechadura}"
                            icon="pi pi-exclamation-triangle"/>
                    </p:commandButton>

                    <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                        <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="pi pi-check" />
                        <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="pi pi-times" />
                    </p:confirmDialog>
                </p:panel>
            </h:form>
        </p:dialog>

        <p:dialog header="#{localemsgs.SolicitacaoSenhaMobile} - #{solicitacaoSenhaMB.codFil} - FECHADURA TODO"
                  widgetVar="dlgFechadura"
                  minHeight="40"
                  resizable="false"
                  modal="true"
                  >
            <h:form id="fechaduraForm">
                <f:facet name="header">
                    <h:outputText value="#{localemsgs.FechaduraDesconectada}" style="color:black" />
                </f:facet>

                <p:panel
                    id="panel"
                    class="modalSolicitacao"
                    >
                    <span>Area under construction...</span>
                </p:panel>
            </h:form>
        </p:dialog>

    </ui:fragment>
</ui:composition>
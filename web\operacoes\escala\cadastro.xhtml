<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <h:form id="cadastroEscala" class="form-inline">
        <p:dialog
            widgetVar="dlgCadastrar"
            positionType="absolute"
            responsive="true"
            draggable="false"
            modal="true"
            closable="true"
            resizable="false"
            dynamic="true"
            showEffect="drop"
            hideEffect="drop"
            closeOnEscape="false"
            style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;"
            >

            <script>
                $(document).ready(function () {
                    //first unbind the original click event
                    PF('dlgCadastrar').closeIcon.unbind('click');

                    //register your own
                    PF('dlgCadastrar').closeIcon.click(function (e) {
                        $("#cadastroEscala\\:fecharFormContrato").click();
                        //should be always called
                        e.preventDefault();
                    });
                });
            </script>

            <p:commandButton
                id="fecharFormContrato"
                style="display: none"
                oncomplete="PF('dlgCadastrar').hide()">
                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
            </p:commandButton>

            <f:facet name="header">
                <img src="../assets/img/icone_tesouraria_entradas.png" height="40" width="40"/>
                <p:spacer width="5px"/>
                <h:outputText value="#{localemsgs.CadastrarNumerario}" style="color:#022a48" />
            </f:facet>

            <p:panel id="cadastrar" style="background-color: transparent;" styleClass="cadastrar">
                <p:confirmDialog global="true">
                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                </p:confirmDialog>


                <div class="form-inline">
                    <p:commandLink
                        id="cadastro"
                        action="#{escalaRotaMB.gravarEntrada}"
                        update="msgs main cabecalho"
                        title="#{escalaRotaMB.editandoEntrada ? localemsgs.editar : localemsgs.Cadastrar}">
                        <p:graphicImage
                            url="#{escalaRotaMB.editandoEntrada
                                   ? '../assets/img/icone_adicionar.png'
                                   : '../assets/img/icone_adicionar.png'}"
                            width="40" height="40" />
                    </p:commandLink>
                </div>
            </p:panel>

            <p:confirmDialog message="#{localemsgs.AlteracaoClienteContratante}" header="#{localemsgs.Confirmacao}"
                             showEffect="drop" width="300" widgetVar="cdglClienteContratante"
                             hideEffect="drop">
                <p:commandButton value="#{localemsgs.Sim}"
                                 styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                 action="#{escalaRotaMB.editarContrato}"
                                 oncomplete="PF('cdglClienteContratante').hide()"
                                 update="msgs main cabecalho"/>
                <p:commandButton value="#{localemsgs.Nao}" action="#{acessos.cadastrar}"
                                 styleClass="ui-confirmdialog-no" icon="ui-icon-close"
                                 oncomplete="PF('cdglClienteContratante').hide()"/>
            </p:confirmDialog>
        </p:dialog>
    </h:form>

</ui:composition>
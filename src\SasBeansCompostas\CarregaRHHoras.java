/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Cargos;
import SasBeans.Clientes;
import SasBeans.CtrOperV;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.RHPonto;
import SasBeans.Rastrear;
import SasBeans.Rh_Horas;

/**
 *
 * <AUTHOR>
 */
public class CarregaRHHoras {

    private Rh_Horas rH_Horas;
    private Funcion funcion;
    private Cargos cargos;
    private RHPonto rHPonto;
    private Rastrear rastrear;
    private Clientes clientes;
    private PstServ pstServ;
    private CtrOperV ctrOperV;
    private boolean supervisionado;

    public Rh_Horas getrH_Horas() {
        return rH_Horas;
    }

    public void setrH_Horas(Rh_Horas rH_Horas) {
        this.rH_Horas = rH_Horas;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public Cargos getCargos() {
        return cargos;
    }

    public void setCargos(Cargos cargos) {
        this.cargos = cargos;
    }

    public RHPonto getrHPonto() {
        return rHPonto;
    }

    public void setrHPonto(RHPonto rHPonto) {
        this.rHPonto = rHPonto;
    }

    public Rastrear getRastrear() {
        return rastrear;
    }

    public void setRastrear(Rastrear rastrear) {
        this.rastrear = rastrear;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    public PstServ getPstServ() {
        return pstServ;
    }

    public void setPstServ(PstServ pstServ) {
        this.pstServ = pstServ;
    }

    public CtrOperV getCtrOperV() {
        return ctrOperV;
    }

    public void setCtrOperV(CtrOperV ctrOperV) {
        this.ctrOperV = ctrOperV;
    }

    public boolean isSupervisionado() {
        return supervisionado;
    }

    public void setSupervisionado(boolean supervisionado) {
        this.supervisionado = supervisionado;
    }

}

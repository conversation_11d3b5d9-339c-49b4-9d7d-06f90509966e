package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.GraficoBean;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class GraficoDao {

    // Lista Hora 50% Ultimo Mes
    //Grafico pizza
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> lista(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "Select \n"
                    + "Filiais.Descricao, RH_Ctrl.Dt_Ini, RH_Ctrl.Dt_Fim, Sum(RH_Ctrl.HE50) HE50\n"
                    + "from RH_Ctrl \n"
                    + "Left Join <PERSON>liais  on Filiais.CodFil = Rh_Ctrl.CodFil \n"
                    + "  where ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }

            sql += "RH_Ctrl.CodFil is not null "
                    + "Group by Filiais.Descricao, RH_Ctrl.Dt_Ini, RH_Ctrl.Dt_Fim\n"
                    + "Having Sum(RH_Ctrl.HE50) > 0 ";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }

            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {

                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Ini(consult.getString("Dt_Ini"));
                graficoBean.setDt_Fim(consult.getString("Dt_Fim"));
                graficoBean.setHE50(consult.getBigDecimal("HE50"));
                retorno.add(graficoBean);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    //Horas 100% ultimo Mês
    //Grafico pizza
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> listaHe100(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "Select \n"
                    + "Filiais.Descricao, RH_Ctrl.Dt_Ini, RH_Ctrl.Dt_Fim, Sum(RH_Ctrl.HE100) HE100\n"
                    + "from RH_Ctrl \n"
                    + "Left Join Filiais  on Filiais.CodFil = Rh_Ctrl.CodFil\n"
                    + " where ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }

            sql += " RH_Ctrl.codFil is not null "
                    + "Group by Filiais.Descricao, Dt_Ini, Dt_Fim\n"
                    + "Having Sum(RH_Ctrl.HE100) > 0";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }

            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {

                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Ini(consult.getString("Dt_Ini"));
                graficoBean.setDt_Fim(consult.getString("Dt_Fim"));
                graficoBean.setHE100(consult.getBigDecimal("HE100"));
                retorno.add(graficoBean);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    //Lista Horas Extras
    //Grafico linha
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> listaHEs(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "Select \n"
                    + "Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6) Compet, Sum(RH_Ctrl.HE50)+Sum(RH_Ctrl.HE100) HEs\n"
                    + "from RH_Ctrl \n"
                    + "Left Join Filiais  on Filiais.CodFil = Rh_Ctrl.CodFil \n"
                    + " where ";
//                    + "where Dt_Ini in (Select top 06 Dt_Ini from RH_Ctrl Group by Dt_Ini Order by Dt_Ini Desc)\n";

            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }

            // acrescentando Group by e Order By
            sql += " RH_Ctrl.codFil is not null "
                    + "Group by Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6)\n"
                    + "Having Sum(RH_Ctrl.HE50)+Sum(RH_Ctrl.HE100) > 0\n"
                    + "Order by Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6) ";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }

            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {

                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Fim(consult.getString("Compet"));
                graficoBean.setHE100(consult.getBigDecimal("HEs"));
                retorno.add(graficoBean);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    //Faltas ultimo Mês
    //Grafico pizza
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> faltasUltimo(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "Select \n"
                    + "Filiais.Descricao, Dt_Ini, Dt_Fim, Sum(RH_Ctrl.Faltas) Faltas\n"
                    + "from RH_Ctrl \n"
                    + "Left Join Filiais  on Filiais.CodFil = Rh_Ctrl.CodFil\n"
                    + "where ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }

            sql += " Rh_Ctrl.CodFil is not null "
                    + "Group by Filiais.Descricao, Dt_Ini, Dt_Fim\n"
                    + "Having Sum(RH_Ctrl.Faltas) > 0";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }
            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {

                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Ini(consult.getString("Dt_Ini"));
                graficoBean.setDt_Fim(consult.getString("Dt_Fim"));
                graficoBean.setFaltas(consult.getBigDecimal("Faltas"));
                retorno.add(graficoBean);

            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    //Lista Evolução Faltas
    //Grafico linha
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> listaEvolucaoFaltas(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "Select \n"
                    + "Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6) Compet, Sum(RH_Ctrl.Faltas) Faltas\n"
                    + "from RH_Ctrl \n"
                    + "Left Join Filiais  on Filiais.CodFil = Rh_Ctrl.CodFil\n"
                    + " where ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql += " Rh_Ctrl.CodFil is not null "
                    + " Group by Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6) \n"
                    + " Having Sum(RH_Ctrl.Faltas) > 0\n"
                    + " Order by Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6) ";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }

            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {

                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Fim(consult.getString("Compet"));
                graficoBean.setFaltas(consult.getBigDecimal("Faltas"));
                retorno.add(graficoBean);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    // Suspenção Ultimo Mes
    //Grafico pizza
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> listaSuspencaoUltimo(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "select Filiais.Descricao, Dt_Ini, Dt_Fim, Sum(RH_Ctrl.Suspensao) Suspensao from RH_Ctrl \n"
                    + "Left Join Filiais  on Filiais.CodFil = Rh_Ctrl.CodFil\n"
                    + "where ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql += " Rh_Ctrl.CodFil is not null "
                    + "Group by Filiais.Descricao, RH_Ctrl.Dt_Ini, RH_Ctrl.Dt_Fim\n"
                    + "Having Sum(RH_Ctrl.Suspensao) > 0";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }
            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {
                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Ini(consult.getString("Dt_Ini"));
                graficoBean.setDt_Fim(consult.getString("Dt_Fim"));
                graficoBean.setSuspensao(consult.getBigDecimal("Suspensao"));
                retorno.add(graficoBean);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    //Evolução Suspensões
    //Grafico linha
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> evolucaoSuspensoes(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "Select \n"
                    + "Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6) Compet, Sum(RH_Ctrl.Suspensao) Suspensoes\n"
                    + "from RH_Ctrl \n"
                    + "Left Join Filiais  on Filiais.CodFil = Rh_Ctrl.CodFil\n"
                    + " where ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql += " Rh_Ctrl.CodFil is not null "
                    + "Group by Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6)\n"
                    + "Having Sum(RH_Ctrl.Suspensao) > 0\n"
                    + "Order by Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6)";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }

            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {
                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Fim(consult.getString("Compet"));
                graficoBean.setSuspensao(consult.getBigDecimal("Suspensoes"));
                retorno.add(graficoBean);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    // Atestados médicos ultimo Mês
    //Grafico pizza
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> atestadosMedicosUltimo(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "Select \n"
                    + "Filiais.Descricao, Dt_Ini, Dt_Fim, Sum(Isnull(RH_Ctrl.AtMedico,0)) AtMedico\n"
                    + "from RH_Ctrl \n"
                    + "Left Join Filiais  on Filiais.CodFil = Rh_Ctrl.CodFil\n"
                    + "where ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql += " Rh_Ctrl.CodFil is not null "
                    + "Group by Filiais.Descricao, RH_Ctrl.Dt_Ini, RH_Ctrl.Dt_Fim\n"
                    + "Having Sum(Isnull(RH_Ctrl.AtMedico,0)) > 0";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }

            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {
                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Ini(consult.getString("Dt_Ini"));
                graficoBean.setDt_Fim(consult.getString("Dt_Fim"));
                graficoBean.setAtMedico(consult.getBigDecimal("AtMedico"));
                retorno.add(graficoBean);

            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    //Evolução Atestados Médicos
    //Grafico linha
    /**
     *
     * @param filtros
     * @param Dt_Ini
     * @param Dt_Fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<GraficoBean> evolucaoAtestadosMedicos(Map filtros, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();
        try {
            String sql = "Select \n"
                    + "Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6) Compet, Sum(RH_Ctrl.AtMedico) AtMedicos\n"
                    + "from RH_Ctrl \n"
                    + "Left Join Filiais  on Filiais.CodFil = Rh_Ctrl.CodFil\n"
                    + " where ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql += " Rh_Ctrl.CodFil is not null "
                    + "Group by Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6)\n"
                    + "Having Sum(RH_Ctrl.AtMedico) > 0\n"
                    + "Order by Filiais.Descricao, substring(Convert(Varchar,Dt_Fim,112),1,6) ";

            Consulta consult = new Consulta(sql, persistencia);

            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        for (String entry : entries) {
                            consult.setString(entry);
                        }
                    }
                }
            }

            consult.select();

            GraficoBean graficoBean;

            while (consult.Proximo()) {
                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Fim(consult.getString("Compet"));
                graficoBean.setAtMedico(consult.getBigDecimal("AtMedicos"));
                retorno.add(graficoBean);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    public List<GraficoBean> listaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<GraficoBean> retorno = new ArrayList<>();

        try {
            String sql = "SELECT  *  FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY codfil ) AS RowNum, * "
                    + "  FROM      Rh_Ctrl  WHERE Rh_Ctrl.CodFil in (select filiais.codfil "
                    + "  from saspw "
                    + "  left join saspwfil on saspwfil.nome = saspw.nome "
                    + "  left join filiais on filiais.codfil = saspwfil.codfilac "
                    + "  left join paramet on paramet.filial_pdr = filiais.codfil "
                    + "  where saspw.codpessoa = ? and paramet.path = ?) AND ";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "Numero IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ? "
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
                consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            GraficoBean graficoBean;

            while (consult.Proximo()) {
                graficoBean = new GraficoBean();
                graficoBean.setDescricao(consult.getString("Descricao"));
                graficoBean.setDt_Ini(consult.getString("Dt_Ini"));
                graficoBean.setDt_Fim(consult.getString("Dt_Fim"));
                graficoBean.setAtMedico(consult.getBigDecimal("AtMedico"));
                retorno.add(graficoBean);

            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("GraficoDao.listaPaginada - " + e.getMessage());
        }
    }
}

package Controller.Pessoas;

import Dados.Persistencia;
import SasBeans.*;
import SasDaos.*;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;

/**
 *
 * <AUTHOR>
 */
public class PessoasSatMobWeb {

    public List<Pe_Cargo> removerCargoPretendido(Pe_Cargo pe_cargo, String codigo, Persistencia persistencia) throws Exception {
        try {
            Pe_CargoDao pe_CargoDao = new Pe_CargoDao();
            pe_CargoDao.removerPe_Cargo(pe_cargo, persistencia);
            return pe_CargoDao.listarCargosPretendidosPessoa(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pe_Cargo> adicionarCargoPretendido(Cargos cargo, String codigo, String operador, Persistencia persistencia) throws Exception {
        try {
            Pe_CargoDao pe_CargoDao = new Pe_CargoDao();
            if (!pe_CargoDao.existePe_Cargo(codigo, cargo.getCargo(), persistencia)) {
                Pe_Cargo pe_cargo = new Pe_Cargo();

                pe_cargo.setCodigo(codigo);
                pe_cargo.setCod_Cargo(cargo.getCargo());
                pe_cargo.setOperador(RecortaAteEspaço(operador, 0, 10));
                pe_cargo.setDt_Alter(getDataAtual("SQL"));
                pe_cargo.setHr_Alter(getDataAtual("HORA"));

                pe_CargoDao.inserirPe_Cargo(pe_cargo, persistencia);
            }

            return pe_CargoDao.listarCargosPretendidosPessoa(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirListaCargoPretendido(List<Cargos> lista, String codigo, String operador, Persistencia persistencia) throws Exception {
        Pe_CargoDao pe_CargoDao = new Pe_CargoDao();
        operador = RecortaAteEspaço(operador, 0, 10);

        List<Pe_Cargo> peCargoList = lista.stream().map(cargo -> {
            Pe_Cargo peCargo = new Pe_Cargo();
            peCargo.setCod_Cargo(cargo.getCargo());
            return peCargo;
        }).collect(Collectors.toList());

        pe_CargoDao.mergeListaCargosDePessoa(peCargoList, codigo, operador, persistencia);
    }

    public List<Pe_Cargo> listarCargosPretendidos(String codigo, Persistencia persistencia) throws Exception {
        try {
            Pe_CargoDao pe_CargoDao = new Pe_CargoDao();
            return pe_CargoDao.listarCargosPretendidosPessoa(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public List<Cargos> listarCargos(Persistencia persistencia) throws Exception {
        try {
            CargosDao cargosDao = new CargosDao();
            return cargosDao.listarCargos(persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de pessoas
     *
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> Listagem(Persistencia persistencia) throws Exception {

        try {
            List<Pessoa> retorno;
            PessoaDao pessoadao = new PessoaDao();
            retorno = pessoadao.ListaPessoaMobWeb(persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Inserção de Pessoas: verifica a existência da pessoa pelo CPF na base
     * central do Satellite. Se a pessoa não estiver cadastrada, insere na base
     * central.
     *
     * @param pessoa - objeto pessoa Dados necessário - nome, email, cpf, rg
     * @param persistenciaLocal - persistencia local
     * @param persistenciaSat - persistencia base central
     * @throws Exception
     */
    public void Inserir(Pessoa pessoa, Persistencia persistenciaLocal, Persistencia persistenciaSat) throws Exception {
        try {
            // buscar pessoa base satellite
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            PessoaDao pessoadao = new PessoaDao();
            Pessoa p;
            if (pessoa.getCPF().equals("")) {
                p = new Pessoa();
            } else {
                p = pessoadao.buscarPessoaCPF(pessoa.getCPF(), persistenciaSat);
            }
            if (null == p.getNome()) {
                p = pessoa;
                p.setCodigo(pessoadao.getCodigo(persistenciaSat));
                p = (Pessoa) FuncoesString.removeAcentoObjeto(p);
                pessoadao.InserirPessoaSatMobWeb(p, persistenciaSat);
            }
            BigDecimal codigo = p.getCodigo();
            Pessoa p2;
            if (pessoa.getCPF().equals("")) {
                p2 = new Pessoa();
            } else {
                p2 = pessoadao.buscarPessoaCPF(pessoa.getCPF(), persistenciaLocal);
            }
            if (null == p2.getNome()) {
                pessoa.setCodigo(pessoadao.getCodigo(persistenciaLocal));
                pessoa.setCodPessoaWEB(codigo);
                pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
                pessoadao.InserirPessoaSatMobWeb(pessoa, persistenciaLocal);
            } else {
                throw new Exception("CPF já cadastrado");
            }
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public void atualizarPessoaDieta(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            pessoaDao.atualizarPessoaDieta(pessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Grava registros que deseja gravar na base de dados
     *
     * @param pessoa objeto pessoa - campos: codigo, nome, cpf, rg, email
     * @param persistencia Conexão com a base de dados local
     * @param persistSat Conexão com a base central
     * @throws Exception
     */
    public void gravar(Pessoa pessoa, Persistencia persistencia, Persistencia persistSat) throws Exception {
        try {
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            PessoaDao pessoaDao = new PessoaDao();
            FuncionDao funciondao = new FuncionDao();
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            pessoaDao.atualizaPessoaSatMob(pessoa, persistencia);
            Pessoa p = pessoaDao.BuscarPessoaCPFCentral(pessoa.getCPF(), persistSat);
            if (((p.getCPF().equals("-1") || null == pessoa.getCPF() || "".equals(pessoa.getCPF())) &&
               (null == pessoa.getCodPessoaWEB() || pessoa.getCodPessoaWEB() == BigDecimal.ZERO)) ||
                  (null != pessoa.getCPF() && !pessoa.getCPF().equals("") && !pessoa.getCPF().equals("-1") && (null == pessoa.getCodPessoaWEB() || pessoa.getCodPessoaWEB().toPlainString().replace(".0","").equals("0")))){
                
                
                p.setCPF(pessoa.getCPF());
                p.setRG(pessoa.getRG());
                p.setRGOrgEmis(pessoa.getRGOrgEmis());
                p.setNome(pessoa.getNome());
                p.setEmail(pessoa.getEmail());
                p.setFone1(pessoa.getFone1());
                p.setFone2(pessoa.getFone2());
                p.setCEP(pessoa.getCEP());
                p.setEndereco(pessoa.getEndereco());
                p.setBairro(pessoa.getBairro());
                p.setUF(pessoa.getUF());
                p.setCidade(pessoa.getCidade());
                p.setObs(pessoa.getObs());
                p.setFuncao(pessoa.getFuncao());
                p.setSituacao(pessoa.getSituacao());
                p.setAltura(pessoa.getAltura());
                p.setPeso(pessoa.getPeso());
                p.setSexo(pessoa.getSexo());
                p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                p.setHr_Alter(DataAtual.getDataAtual("HORA"));
                p.setCodigo(pessoaDao.getCodigo(persistSat));
                p.setCodPessoaWEB(pessoa.getCodigo());
                p.setOperador(pessoa.getOperador());
                p.setIndicacao(pessoa.getIndicacao());
                p.setDt_FormIni(pessoa.getDt_FormIni());
                p.setDt_FormFim(pessoa.getDt_FormFim());
                p.setLocalForm(pessoa.getLocalForm());
                p.setCertific(pessoa.getCertific());
                p.setDt_Recicl(pessoa.getDt_Recicl());
                p.setDt_VenCurs(pessoa.getDt_VenCurs());
                p.setReg_PF(pessoa.getReg_PF());
                p.setReg_PFUF(pessoa.getReg_PFUF());
                p.setReg_PFDt(pessoa.getReg_PFDt());
                p.setCNH(pessoa.getCNH());
                p.setCNHDtVenc(pessoa.getCNHDtVenc());
                p.setExtTV(pessoa.getExtTV());
                p.setExtSPP(pessoa.getExtSPP());
                p.setExtEscolta(pessoa.getExtEscolta());
                //p.setDt_nasc(pessoa.getDt_nasc());
                p.setCarNacVig(pessoa.getCarNacVig());
                //p.setDtValCNV(pessoa.getDtValCNV());
                p.setMae(pessoa.getMae());
                p.setPIS(pessoa.getPIS());
                p.setComplemento(pessoa.getComplemento());
                p.setCodCli(pessoa.getCodCli());

                p = (Pessoa) FuncoesString.removeAcentoObjeto(p);
                pessoaDao.InserirPessoaSatMobWeb(p, persistSat);
                pessoa.setCodPessoaWEB(p.getCodigo());
                pessoaDao.atualizaPessoaSatMob(pessoa, persistencia);
            } else {
                BigDecimal CodigoInterno = pessoa.getCodigo();
                pessoa.setCodigo(pessoa.getCodPessoaWEB());
                pessoa.setCodPessoaWEB(CodigoInterno);
                pessoaDao.atualizaPessoaSatMob(pessoa, persistSat);
            }

            List<Funcion> funcion = funciondao.BuscaPessoa(pessoa, persistencia);
            if (!funcion.isEmpty()) {
                for (Funcion f : funcion) {
                    f.setNome(pessoa.getNome());
                    f.setRG(pessoa.getRG());
                    f.setOrgEmis(pessoa.getRGOrgEmis());
                    f.setEmail(pessoa.getEmail());
                    f.setFone1(pessoa.getFone1());
                    f.setFone2(pessoa.getFone2());
                    f.setCEP(pessoa.getCEP());
                    f.setEndereco(pessoa.getEndereco());
                    f.setBairro(pessoa.getBairro());
                    f.setCidade(pessoa.getCidade());
                    f.setUF(pessoa.getUF());
                    f.setObs(pessoa.getObs());
                    f.setAltura(pessoa.getAltura().toPlainString());
                    f.setPeso(pessoa.getPeso().toPlainString());
                    f.setSexo(pessoa.getSexo());
                    //f.setDt_Nasc(pessoa.getDt_nasc());
                    f.setCNH(pessoa.getCNH());
                    f.setDt_VenCNH(pessoa.getCNHDtVenc());
                    f.setMae(pessoa.getMae());
                    f.setPIS(pessoa.getPIS());

                    f = (Funcion) FuncoesString.removeAcentoObjeto(f);
                    funciondao.GravaFuncionSatMobWeb(f, persistencia);
                }
            }

        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Grava registros que deseja gravar na base de dados
     *
     * @param pessoa       objeto pessoa - campos: codigo, nome, cpf, rg, email
     * @param persistencia Conexão com a base de dados local
     * @param persistSat   Conexão com a base central
     * @throws Exception
     */
    public void gravarCandidato(Pessoa pessoa, Persistencia persistencia, Persistencia persistSat) throws Exception {
        try {
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            PessoaDao pessoaDao = new PessoaDao();
            FuncionDao funciondao = new FuncionDao();
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            pessoaDao.atualizaCandidatoSatMob(pessoa, persistencia, false);
            Pessoa p = pessoaDao.BuscarPessoaCPFCentral(pessoa.getCPF(), persistSat);
            if (p.getCPF().equals("-1") || null == pessoa.getCPF() || "".equals(pessoa.getCPF())) {
                p.setCPF(pessoa.getCPF());
                p.setRG(pessoa.getRG());
                p.setRGOrgEmis(pessoa.getRGOrgEmis());
                p.setNome(pessoa.getNome());
                p.setEmail(pessoa.getEmail());
                p.setFone1(pessoa.getFone1());
                p.setFone2(pessoa.getFone2());
                p.setCEP(pessoa.getCEP());
                p.setEndereco(pessoa.getEndereco());
                p.setBairro(pessoa.getBairro());
                p.setUF(pessoa.getUF());
                p.setCidade(pessoa.getCidade());
                p.setObs(pessoa.getObs());
                p.setFuncao(pessoa.getFuncao());
                p.setSituacao(pessoa.getSituacao());
                p.setAltura(pessoa.getAltura());
                p.setPeso(pessoa.getPeso());
                p.setSexo(pessoa.getSexo());
                p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                p.setHr_Alter(DataAtual.getDataAtual("HORA"));
                p.setCodigo(pessoaDao.getCodigo(persistSat));
                p.setCodPessoaWEB(pessoa.getCodigo());
                p.setOperador(pessoa.getOperador());
                p.setIndicacao(pessoa.getIndicacao());
                p.setDt_FormIni(pessoa.getDt_FormIni());
                p.setDt_FormFim(pessoa.getDt_FormFim());
                p.setLocalForm(pessoa.getLocalForm());
                p.setCertific(pessoa.getCertific());
                p.setDt_Recicl(pessoa.getDt_Recicl());
                p.setDt_VenCurs(pessoa.getDt_VenCurs());
                p.setReg_PF(pessoa.getReg_PF());
                p.setReg_PFUF(pessoa.getReg_PFUF());
                p.setReg_PFDt(pessoa.getReg_PFDt());
                p.setCNH(pessoa.getCNH());
                p.setCNHDtVenc(pessoa.getCNHDtVenc());
                p.setExtTV(pessoa.getExtTV());
                p.setExtSPP(pessoa.getExtSPP());
                p.setExtEscolta(pessoa.getExtEscolta());
                //p.setDt_nasc(pessoa.getDt_nasc());
                p.setCarNacVig(pessoa.getCarNacVig());
                //p.setDtValCNV(pessoa.getDtValCNV());
                p.setMae(pessoa.getMae());
                p.setPIS(pessoa.getPIS());
                p.setComplemento(pessoa.getComplemento());
                p.setCodCli(pessoa.getCodCli());

                p = (Pessoa) FuncoesString.removeAcentoObjeto(p);
                pessoaDao.InserirPessoaSatMobWeb(p, persistSat);
                pessoa.setCodPessoaWEB(p.getCodigo());
                pessoaDao.atualizaCandidatoSatMob(pessoa, persistencia, false);
            } else {
                pessoa.setCodigo(pessoa.getCodPessoaWEB());
                pessoaDao.atualizaCandidatoSatMob(pessoa, persistSat, true);
            }

            List<Funcion> funcion = funciondao.BuscaPessoa(pessoa, persistencia);
            if (!funcion.isEmpty()) {
                for (Funcion f : funcion) {
                    f.setNome(pessoa.getNome());
                    f.setRG(pessoa.getRG());
                    f.setOrgEmis(pessoa.getRGOrgEmis());
                    f.setEmail(pessoa.getEmail());
                    f.setFone1(pessoa.getFone1());
                    f.setFone2(pessoa.getFone2());
                    f.setCEP(pessoa.getCEP());
                    f.setEndereco(pessoa.getEndereco());
                    f.setBairro(pessoa.getBairro());
                    f.setCidade(pessoa.getCidade());
                    f.setUF(pessoa.getUF());
                    f.setObs(pessoa.getObs());
                    f.setAltura(pessoa.getAltura().toPlainString());
                    f.setPeso(pessoa.getPeso().toPlainString());
                    f.setSexo(pessoa.getSexo());
                    //f.setDt_Nasc(pessoa.getDt_nasc());
                    f.setCNH(pessoa.getCNH());
                    f.setDt_VenCNH(pessoa.getCNHDtVenc());
                    f.setMae(pessoa.getMae());
                    f.setPIS(pessoa.getPIS());

                    f = (Funcion) FuncoesString.removeAcentoObjeto(f);
                    funciondao.GravaFuncionSatMobWeb(f, persistencia);
                }
            }

        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal gerarCodPessoaWeb(Pessoa pessoa, Persistencia persistencia, Persistencia persistSat) throws Exception {
        try {
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            PessoaDao pessoaDao = new PessoaDao();
            FuncionDao funciondao = new FuncionDao();
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            pessoaDao.atualizaPessoaSatMob(pessoa, persistencia);
            Pessoa p = pessoaDao.BuscarPessoaCPFCentral(pessoa.getCPF(), persistSat);
            if (p.getCPF().equals("-1") || null == pessoa.getCPF() || "".equals(pessoa.getCPF())) {
                p.setCPF(pessoa.getCPF());
                p.setRG(pessoa.getRG());
                p.setRGOrgEmis(pessoa.getRGOrgEmis());
                p.setNome(pessoa.getNome());
                p.setEmail(pessoa.getEmail());
                p.setFone1(pessoa.getFone1());
                p.setFone2(pessoa.getFone2());
                p.setCEP(pessoa.getCEP());
                p.setEndereco(pessoa.getEndereco());
                p.setBairro(pessoa.getBairro());
                p.setUF(pessoa.getUF());
                p.setCidade(pessoa.getCidade());
                p.setObs(pessoa.getObs());
                p.setFuncao(pessoa.getFuncao());
                p.setSituacao(pessoa.getSituacao());
                p.setAltura(pessoa.getAltura());
                p.setPeso(pessoa.getPeso());
                p.setSexo(pessoa.getSexo());
                p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                p.setHr_Alter(DataAtual.getDataAtual("HORA"));
                p.setCodigo(pessoaDao.getCodigo(persistSat));
                p.setCodPessoaWEB(pessoa.getCodigo());
                p.setOperador(pessoa.getOperador());
                p = (Pessoa) FuncoesString.removeAcentoObjeto(p);
                pessoaDao.InserirPessoaSatMobWeb(p, persistSat);
                pessoa.setCodPessoaWEB(p.getCodigo());
                pessoaDao.atualizaPessoaSatMob(pessoa, persistencia);
            } else {
                pessoa.setCodigo(pessoa.getCodPessoaWEB());
                pessoaDao.atualizaPessoaSatMob(pessoa, persistSat);
            }

            List<Funcion> funcion = funciondao.BuscaPessoa(pessoa, persistencia);
            if (!funcion.isEmpty()) {
                for (Funcion f : funcion) {
                    f.setNome(pessoa.getNome());
                    f.setRG(pessoa.getRG());
                    f.setOrgEmis(pessoa.getRGOrgEmis());
                    f.setEmail(pessoa.getEmail());
                    f.setFone1(pessoa.getFone1());
                    f.setFone2(pessoa.getFone2());
                    f.setCEP(pessoa.getCEP());
                    f.setEndereco(pessoa.getEndereco());
                    f.setBairro(pessoa.getBairro());
                    f.setCidade(pessoa.getCidade());
                    f.setUF(pessoa.getUF());
                    f.setObs(pessoa.getObs());
                    f.setAltura(pessoa.getAltura().toPlainString());
                    f.setPeso(pessoa.getPeso().toPlainString());
                    f.setSexo(pessoa.getSexo());
                    f = (Funcion) FuncoesString.removeAcentoObjeto(f);
                    funciondao.GravaFuncionSatMobWeb(f, persistencia);
                }
            }
            return pessoa.getCodPessoaWEB();

        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pessoa> ListaPessoa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList<>();

        try {
            PessoaDao pessoaDao = new PessoaDao();
            if (pessoa.getCodigo().equals(new BigDecimal("-1"))) {
                return null;
            }
            retorno = pessoaDao.BuscaPessoa(pessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
        return retorno;
    }

    public List<Pessoa> ListaPessoaQuery(String query, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList<>();

        try {
            PessoaDao pessoaDao = new PessoaDao();
            retorno = pessoaDao.listagemPessoaQuery(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    public List<Pessoa> ListaPessoaQueryValida(String query, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList<>();

        try {
            PessoaDao pessoaDao = new PessoaDao();
            retorno = pessoaDao.ListagemPessoaQueryValida(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    /**
     * Lista os municípios com base na query
     *
     * <AUTHOR>
     * @param query - string com a busca
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> ListaMunicipios(String query, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.BuscarMunicipio(query, persistencia);
        } catch (Exception e) {
            throw new Exception("cliente.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base no estado e cidade
     *
     * <AUTHOR>
     * @param estado
     * @param cidade
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> ListaMunicipios(String estado, String cidade, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.ValidarMunicipio(estado, cidade, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa buscarPessoa(String CPF, Persistencia persistencia) throws Exception {
        Pessoa retorno = new Pessoa();
        try {
            PessoaDao pessoaDao = new PessoaDao();
            retorno = pessoaDao.buscarPessoaCPF(CPF, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    public Pessoa buscarPessoaCPFAndEmail(String CPF, String email, Persistencia persistencia) throws Exception {
        Pessoa pessoa;
        try {
            PessoaDao pessoaDao = new PessoaDao();
            pessoa = pessoaDao.buscarPessoaCPFAndEmail(CPF, email, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }

        if (pessoa == null) {
            pessoa = new Pessoa();
            pessoa.setCPF(CPF);
            pessoa.setEmail(email);
            pessoa.setSituacao("C");
            pessoa.setExtTV("N");
            pessoa.setExtSPP("N");
            pessoa.setExtEscolta("N");

            return pessoa;
        } else {
            return pessoa;
        }
    }

    public Pessoa buscaCandidato(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            Pessoa candidato = pessoaDao.getInfoCandidato(pessoa.getCodigo().toString(), persistencia);
            pessoa.setIndicacao(candidato.getIndicacao());
            pessoa.setDt_FormIni(candidato.getDt_FormIni());
            pessoa.setDt_FormFim(candidato.getDt_FormFim());
            pessoa.setLocalForm(candidato.getLocalForm());
            pessoa.setCertific(candidato.getCertific());
            pessoa.setDt_Recicl(candidato.getDt_Recicl());
            pessoa.setDt_VenCurs(candidato.getDt_VenCurs());
            pessoa.setReg_PF(candidato.getReg_PF());
            pessoa.setReg_PFUF(candidato.getReg_PFUF());
            pessoa.setReg_PFDt(candidato.getReg_PFDt());
            pessoa.setCNH(candidato.getCNH());
            pessoa.setCNHDtVenc(candidato.getCNHDtVenc());
            pessoa.setExtTV(candidato.getExtTV());
            pessoa.setExtSPP(candidato.getExtSPP());
            pessoa.setExtEscolta(candidato.getExtEscolta());
            return pessoa;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public Boolean Situacao(Pessoa pessoa, Persistencia persistencia) {
        FuncionDao funcionDao = new FuncionDao();
        try {
            return !funcionDao.BuscaCPF(pessoa.getCPF(), persistencia).get(0).getSituacao().equals("D");
        } catch (Exception e) {
            return true;
        }
    }

    public List<Pessoa> PesquisaPessoas(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            return pessoadao.pesquisaPessoaSatMobWeb(pessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de pessoas
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            PessoaDao pessoadao = new PessoaDao();
            retorno = pessoadao.TotalPessoaMobWeb(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de pessoas
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno;
            PessoaDao pessoadao = new PessoaDao();
            retorno = pessoadao.listaPaginada(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Contagem do cadastro de pessoas
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagemQuick(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            PessoaDao pessoadao = new PessoaDao();
            retorno = pessoadao.totalPessoaMobWebQuick(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de pessoas
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> listaPaginadaQuick(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno;
            PessoaDao pessoadao = new PessoaDao();
            retorno = pessoadao.listaPaginadaQuick(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public List<MobileContatos> ListaContatos(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            MobileContatosDao mobilecontatosdao = new MobileContatosDao();
            return mobilecontatosdao.listaDeContatosSatMobWeb(codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Adiciona um contato: verifica a existencia do contato antes de inserir
     *
     * @param contato
     * @param persistencia
     * @throws Exception
     */
    public void AdicionarContato(MobileContatos contato, Persistencia persistencia) throws Exception {
        try {
            MobileContatosDao mobilecontatosdao = new MobileContatosDao();
            if (mobilecontatosdao.existeContato(contato, persistencia)) {
                mobilecontatosdao.atualizaContato(contato, persistencia);
            } else {
                mobilecontatosdao.insereMobileContatos(contato, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Muda a situação do contato de Ativo - 1 para inativo - 0
     *
     * @param contato
     * @param persistencia
     * @throws Exception
     */
    public void DeletarContato(MobileContatos contato, Persistencia persistencia) throws Exception {
        try {
            MobileContatosDao mobilecontatosdao = new MobileContatosDao();
            mobilecontatosdao.atualizaContato(contato, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public List<MobileContatos> BuscarContatos(String query, Persistencia persistencia) throws Exception {
        try {
            MobileContatosDao mobilecontatosdao = new MobileContatosDao();
            return mobilecontatosdao.BuscaContatosMobile(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa buscarMatricula(String matr, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            return pessoadao.buscaMatr(matr, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os documentos de uma pessoa pelo seu código
     *
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pe_Doctos> listarDoctos(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            Pe_DoctosDao pe_DoctosDao = new Pe_DoctosDao();
            return pe_DoctosDao.listarDoctos(codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Insere um documento verificando a existencia antes, e em caso positivo,
     * atualiza o arquivo em vez de inserir.
     *
     * @param pe_Doctos
     * @param persistencia
     * @return retorna o arquivo com a ordem
     * @throws Exception
     */
    public Pe_Doctos inserirDocumento(Pe_Doctos pe_Doctos, Persistencia persistencia) throws Exception {
        try {
            Pe_DoctosDao pe_DoctosDao = new Pe_DoctosDao();
            String ordem = pe_DoctosDao.existeArquivo(pe_Doctos.getCodigo(), pe_Doctos.getDescricao(), persistencia);
            if (null == ordem) {
                ordem = pe_DoctosDao.maxOrdem(pe_Doctos.getCodigo(), persistencia);
                pe_Doctos.setOrdem(ordem);
                pe_DoctosDao.inserirDocumento(pe_Doctos, persistencia);
            } else {
                pe_Doctos.setOrdem(ordem);
                pe_DoctosDao.atualizaDocumentos(pe_Doctos, persistencia);
            }
            return pe_Doctos;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Exclui um documento do banco de dados
     *
     * @param pe_Doctos
     * @param persistencia
     * @throws Exception
     */
    public void deletarDocumento(Pe_Doctos pe_Doctos, Persistencia persistencia) throws Exception {
        try {
            Pe_DoctosDao pe_DoctosDao = new Pe_DoctosDao();
            pe_DoctosDao.excluirDocumento(pe_Doctos, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }
}

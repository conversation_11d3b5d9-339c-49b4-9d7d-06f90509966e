<!DOCTYPE html>
<html lang="pt-br">
  <head>

    <title>Relatório Apuração de Ponto</title>

    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css" integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
   
    <!-- Personalizações -->

    <!-- JS personalizado -->
    <script type="text/javascript" src="relapuraponto.js"></script>
   
    <!-- CSS Personalizado -->
    <link rel="stylesheet" type="text/css" href="relapuraponto.css"/>

    </head>

  <body onload="exibirTodosDados()">


  <div class = "container" id="preConteudo">
    <div class="row alert alert-warning">
      <div class="col-sm-12">
       <p class="text text-center">Aguarde! Os dados estão sendo carregados... </p>
      </div>
    </div>
  </div>

<div class="container" id="todoConteudo"> 

  <!-- Inicio dados do cabeçalho -->
 <div class="row" id="cabecalho">
    <div class="col-sm-2" id="logoE">
      
    </div>
  
    <div class="col-sm-10" id="dados-cabecalho">
      <h5 id="nomeE" class="text text-left"></h5>
      <p id="enderecoE" class="text text-sm-left h6"> </p>
      <p id="cnpjE" class="text-sm-left"></p>
    </div>
  </div>
  <!-- Fim dados do cabeçalho -->
  <div class="row">
    <div class="col-sm-12">
     <h6 class="text text-center">Apuração de Ponto</h6> 
     <hr> 
    </div> 
  </div> 

  <!-- Inicio dados do funcionário -->
  <div class="row">
    <div class="col-sm-12"> 
      <div class="col-sm-12"> 
        <span class="italic">Matrícula: </span><span id="lbMatriculaF"></span> 
        <span class="italic">Filial: </span><span id=lbFilialF> </span>
        <span class="italic">Nome: </span><span id="lbNomeF"> </span> 
        <span class="italic">Função: </span><span id="lbFuncaoF"></span>
      </div>
    
    <div class="col-sm-12"><span class="italic">Posto: </span><span id="lbPostoF"></span>
       <span class="textoreduzido" id="lbRazaoSocial"> </span>
    </div>

    <div class="col-sm-12 ">
      <span class="italic">Horário de Trabalho: </span><span id="lbHorarioTrabalhoF"> </span> 
       <span class="italic">Período: </span> <span id="lbperiodoInicial"></span> <span> a </span> <span id="lbperiodoFinal"></span> 
      </div>
    </div>
    
  </div>
  <!-- fim dados do funcionário -->

  <div class="row"> <!--Início tabela de Apuração -->
      <div class="col-sm-12">
        <table class=" table table-responsive-sm table-bordered table-hover" id="tabela-dados-ponto">
            <thead class="text text-center">
              <tr>
                <th class="thbg" rowspan="2">Data</th>
                <th class="thbg" rowspan="2">Dia Sem</th>
                <th class="thbg" rowspan="2" >Entrada</th>
                <th class="thbg" colspan="2" class="text-center" >Intervalo </th>
                <th class="thbg" rowspan="2" >Saída</th>
                <th class="thbg" colspan="2" class="text-center" >Extra</th>
                <th class="thbg" rowspan="2" >Observação</th>
                <th class="thbgsecundario" rowspan="2" >Hs Diu</th>
                <th class="thbgsecundario" rowspan="2" >Hs Not</th>
                <th class="thbgsecundario" rowspan="2" >IntraJ</th>
                <th class="thbgsecundario"  rowspan="2">HE 1</th>
                <th class="thbgsecundario" rowspan="2" >HE 2</th>
                <th class="thbgsecundario" rowspan="2" >HE 1i</th>
                <th class="thbgsecundario" rowspan="2" >HE 2i</th>
              </tr>
               <!--Segunda Linha do Cabeçalho da tabela--> 
              <tr>
                <th class="thbg thsub">Saída</th>
                <th class="thbg thsub">Entrada</th>
                <th class="thbg thsub">Entrada</th>
                <th class="thbg thsub">Saída</th>
              </tr>
            </thead>
            
            <tbody id="resultadoTabela">              
              <!-- corpo da tabela com dados recebidos via JS  -->
              
            </tbody>

        </table>
      </div>
    </div> <!-- fim da tabela -->

  <div class="row">
    <div class="col-sm-4">
     <table class="table table-responsive-sm table-borderless table-sm" id="tabela-resumo">
      <thead>
        <th  colspan="12">Resumo </th>
      </thead>
      <tbody>
        <tr>
          <td>Hs Trab:  </td>
          <td class="alinhamentodireita"><span id="resumoHrsTrabF"></span></td>
          <td rowspan="8"></td>
          <td>Dias Trab.: </td>
          <td class="alinhamentodireita"><span id="resumoDiasTrabF"></span></td>
          <td rowspan="8"></td>
          <td>Dias CH Sup:   </td>
          <td class="alinhamentodireita"><span id="resumoDiasCHSupF"></span></td>
        </tr>
        <tr>
          <td colspan="2"></td>  
          <td>Folga:</td>
          <td class="alinhamentodireita"><span id="resumoFolgasF"></span></td>

         <td>Vale Refeição: </td>
         <td class="alinhamentodireita"><span id="resumoValeRF"></span></td>
        </tr>
        <tr>
          <td >Hs projeção:  </td>
          <td class="alinhamentodireita"><span id="resumoHsProjF"></span></td>
          <td>Férias:</td>
          <td class="alinhamentodireita"><span id="resumoFeriasF"></span></td>
          <td>Vale Transporte: </td>
          <td class="alinhamentodireita"><span id="resumoValeTF"></span></td>
        </tr>
        <tr>
          <td>HE tipo 1:  </td>
         <td class="alinhamentodireita"><span id="resumoHE1F"></span></td>
          <td>Faltas Just.:  </td>
          <td class="alinhamentodireita"><span id="resumoFaltaJustificadaF"></span></td>
          <td colspan="2"></td>  
              
        </tr>
        <tr>
          <td>HE tipo 2:  </td>
          <td class="alinhamentodireita"><span id="resumoHE2F"></span></td>
          <td>At. Médico.:  </td>
          <td class="alinhamentodireita"><span id="resumoAtMedicoF"></span></td>
          <td>Saldo Anterior:  </td>
          <td class="alinhamentodireita"><span id="resumoSaldoAnteriorF"></span></td>
        </tr>
        <tr>
          <td>Hs Noturnas: </td>
          <td class="alinhamentodireita"><span id="resumoHsNoturnaF"></span></td>
          <td>Faltas:</td>
         <td class="alinhamentodireita"><span id="resumoFaltasF"></span></td>
          <td colspan="2"></td>  
        </tr>
         <tr>
          <td>Trab Feriado: </td>
          <td class="alinhamentodireita"><span id="resumoTrabFeriadoF"></span></td>
          <td>Desc DSR: </td>
          <td class="alinhamentodireita"><span id="resumoDescDSRF"></span></td>
          <td colspan="2"></td>  
        </tr>
         <tr>
          <td>Hs IntraJ: </td>
          <td class="alinhamentodireita"><span id="resumoHsIntraJF"></span></td>
          <td>Faltas Comp.: </td>
          <td class="alinhamentodireita"><span id="resumoFaltasCompF"></span></td>
         <td colspan="2"></td>  
        </tr>
         
      </tbody>

    </table>
    </div>
 </div>

 <div class="row">
  <div class="col-sm-12">
    <div class="text text-center">
      <p id="paragrafoRegistro">REGISTROS ASSINADOS ELETRONICAMENTE POR SENHA E BIOMETRIA</p>
      <P id="linhaPontilhada">--------------------------------------------</P>
      <span  id='nomeFuncionario'></span>
      <P></P>
    </div>  
  </div>
</div>

   

<div class="row">
  <div class="col-sm-12">
    <div id="noprint" class="text text-center">
      
      
   
  <a href="" onclick="window.print();"><i class="fa fa-print" title="Imprimir" style="width:50px;height: 50px;text-align: center;font-size: 16pt;color: #FFFD;right: 10px;bottom: 0px;padding-top: 11px !important;cursor: pointer;background-color: red;border-radius: 50%;z-index: 99;border: 2px solid darkred">  </i></a>

<p></p>
    </div>  
  </div>
</div>

 
  </div> <!-- fim Container -->

    <!-- JavaScript (Opcional) -->
    <!-- jQuery primeiro, depois Popper.js, depois Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
  


  </body>
</html>
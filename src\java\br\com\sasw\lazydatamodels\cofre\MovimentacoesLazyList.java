/*
 */
package br.com.sasw.lazydatamodels.cofre;

import Controller.CofreInteligente.DashBoardsSatMobWeb;
import Dados.Persistencia;
import SasBeans.TesCofresMov;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class MovimentacoesLazyList extends LazyDataModel<TesCofresMov> {

    private static final long serialVersionUID = 1L;
    private List<TesCofresMov> movimentacoes;
    private final DashBoardsSatMobWeb dashBoardsSatMobWeb;
    private Persistencia persistencia;

    public MovimentacoesLazyList(Persistencia pst) {
        this.dashBoardsSatMobWeb = new DashBoardsSatMobWeb();
        this.persistencia = pst;
    }

    @Override
    public List<TesCofresMov> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.movimentacoes = this.dashBoardsSatMobWeb.detalhesMovimentacaoPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.dashBoardsSatMobWeb.totalMovimentacaoPaginada(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.movimentacoes;
    }

    @Override
    public Object getRowKey(TesCofresMov movimentacao) {
        return movimentacao.getId().toBigInteger().toString();
    }

    @Override
    public TesCofresMov getRowData(String id) {
        BigDecimal cc = new BigDecimal(id);
        for (TesCofresMov movimentacao : this.movimentacoes) {
            if (cc.compareTo(movimentacao.getId()) == 0) {
                return movimentacao;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

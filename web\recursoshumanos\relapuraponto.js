﻿
      //variaveis globais - empresa

      var nomeE = '';
      var enderecoE = '';
      var cnpjE = '';
      var logoE = '';

      //variaveis globais - funcionário
      var lbMatriculaF ='';
      var lbNomeF ='';
      var lbFilialF ='';
      var lbFuncaoF ='';
      var lbPostoF='';
      var lbSecaoF ='';
      var lbRazaoSocial ='';
      var lbHorarioTrabalhoF = '';
      var lbperiodoInicial ='';
      var lbperiodoFinal ='';

      //variaveis globais - quadro resumo

      var resumoHE1F = '';
      var resumoHE2F = '';
      var resumoHrsTrabF = '';
      var resumoHsProjF ='' ;
      var resumoHsIntraJF ='' ;
      var resumoHsNoturnaF = '';
      var resumoDiasTrabF ='' ;
      var resumoDiasCHSupF = '';
      var resumoDescDSRF ='';
      var resumoAtMedicoF = '';
      var resumoFaltasCompF = '';
      var resumoFaltaJustificadaF = '';
      var resumoFaltasF = '';
      var resumoFeriasF ='' ;
      var resumoFolgasF = '';
      var resumoValeRF ='';
      var resumoValeTF = '';
      var resumoTrabFeriadoF = '';
      var resumoSaldoAnteriorF = '';

      //variaveis globais - tabela 
      var qtregistros='';
      var data ='';
      var diasem ='';
      var entrada1 ='';
      var saida1 ='';
      var entrada2 = '';
      var saida2 ='';
      var entrada3 ='';
      var saida3 ='';
      var observacao ='';
      var hsdiu ='';
      var hsnot='';
      var intraj ='';
      var he1='';
      var he2='';
      var he1i ='';
      var he2i ='';


      var qtregistros ='';  //  usado para popoular tabela

      //dados do periodo - global 
      var vdtini ='';
      var vdtfim='';

    //função para retirar o zero
    function numInteiro(numero) {
      var  posicao = numero.indexOf('.');
      if (posicao >= 0) {
      numero = numero.substring(0, posicao);
        }
        return numero
   }

      //função para ver o dia da semana
    function formatarDiaSem(dataformatada){
    
      var semana = ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"];
      
        var dataformatada = dataformatada;
        var arr = dataformatada.split("/").reverse();
        var diasemana = new Date(arr[0], arr[1] - 1, arr[2]);
        var dia = diasemana.getDay();
        return (semana[dia]);
      
    } 
    // formata a visualização do CNPJ

    function formatarCNPJ(cnpj){
      cnpj = cnpj;

      let cnpjformatado= cnpj.substr(0,2)+"."+cnpj.substr(2,3)+"."+cnpj.substr(5,3)+"/"+cnpj.substr(8,4)+"-"+cnpj.substr(9,2);
      return cnpjformatado;
    }
    // formata a visualização da data para o padrão visual.
    function formatarData(data){
    data = data;
    dataformatada= '';
    
      //if(!data.search('-')) {
            if(data.length==8) {
      let dtdia = data.substring(6,8);
      let dtmes = data.substring(4,6);
      let dtano = data.substring(0,4);
      dataformatada = dtdia+"/"+dtmes+"/"+dtano;
      
      } 

      else {
      
      let dtdia = data.substring(8,10);
      let dtmes = data.substring(5,7);
      let dtano = data.substring(0,4);
      dataformatada = dtdia+"/"+dtmes+"/"+dtano;
      
     
      }
      return dataformatada;
    }
      
    //busca no servidor das informações
      function autenticar (){

      var vHeaders = new Headers();
      vHeaders.append("Content-Type", "application/xml");
            //Atribui variaveis



      // urlParms para pegar as datas 
      const urlParams = new URLSearchParams(window.location.href);
     
      // location href para pegar matrID e toID
      let matrID = location.href.split("matrID=").pop();// Matricula funconário
      let tokID =  location.href.split("tokID=").pop();// token de autenticação
      let vdtini = urlParams.get('vdtini');// data inicial
      let vdtfim = urlParams.get('vdtfim');// data fin
       
      //var vParam = `matr=${matrID}&token=${tokID}&dataini=${vdtini}&datafim=${vdtfim}`;
      var vParam = `matr=8000126&token=5A8BFBC18A784651B24058660CBFB8A5&dataini=20230501&datafim=20230531`;
      var vRequisicao = {
                method: 'POST',
                headers: vHeaders,
                body: vParam,
                redirect: 'follow'
      };
            var vWSSAS ="https://mobile.sasw.com.br/SatWebServiceHomologG/api/executasvc/consultaPonto";
            
      fetch(vWSSAS, vRequisicao)  
      .then(response => response.text())
      .then(result => autenticarSvc(JSON.parse(result)))
      .catch(error => console.log('error', error));
   

      //define as variáveis do período.
      lbperiodoInicial = vdtini;
      lbperiodoFinal = vdtfim;

      }

      function autenticarSvc(jsonObj) {
     
  

      console.log(jsonObj)
    
        if (jsonObj.funcion[0].matr == "NAO REGISTRADO") {
           alert('Matrícula não localizada para o período informado');
        } else {
          
          document.getElementById("preConteudo").style.display = "none";
          document.getElementById("todoConteudo").style.display = "block";
            
        } 

        //populando as variáveis com os dados.
        nomeE = jsonObj.funcion[0].filialdesc; 
        logoE = "<img src='"+jsonObj.funcion[0].arqlogo+"'>";
        
        enderecoE = jsonObj.funcion[0].enderecofilial;
        cnpjE = jsonObj.funcion[0].cnpjfil;

        lbMatriculaF = jsonObj.funcion[0].matr.replace('.0', '');
        lbFilialF = jsonObj.funcion[0].codfil;
        lbNomeF = jsonObj.funcion[0].nome;
        lbFuncaoF = jsonObj.funcion[0].cargodescricao;
        lbPostoF = jsonObj.funcion[0].posto;
        lbSecaoF = jsonObj.funcion[0].secao;
        lbRazaoSocial = jsonObj.funcion[0].razaosocial;
        lbHorarioTrabalhoF = jsonObj.funcion[0].deschorario; 
        //utilizado para ser o count da tabela
        qtregistros =  jsonObj.funcion.length;

        //variaveis do quadro resumo com json
        resumoHsProjF = jsonObj.funcion[0].hsprojecao;
        resumoHrsTrabF = jsonObj.funcion[0].horastrab;
        resumoHE1F = jsonObj.funcion[0].he50;
        resumoHE2F = jsonObj.funcion[0].he100;
        resumoHsNoturnaF = jsonObj.funcion[0].adnot;
        resumoTrabFeriadoF = jsonObj.funcion[0].diasfertrab;
        resumoHsIntraJF = jsonObj.funcion[0].intraj;
        resumoFolgasF = jsonObj.funcion[0].diasfolga;
        resumoDiasTrabF = jsonObj.funcion[0].diastrab;
        resumoFeriasF = jsonObj.funcion[0].diasferias;
        resumoFaltaJustificadaF = jsonObj.funcion[0].faltasjust;
        resumoAtMedicoF = jsonObj.funcion[0].atmedico;
        resumoFaltasF = jsonObj.funcion[0].faltas;
        resumoDescDSRF = jsonObj.funcion[0].descdsr;
        resumoFaltasCompF = jsonObj.funcion[0].faltasabon;
        resumoDiasCHSupF = jsonObj.funcion[0].diaschsup;
        resumoValeRF = 0;
        resumoValeTF =0;
        resumoSaldoAnteriorF =0;
       // resumoHsProjF = json.funcion[0].hsprojecao;

        
        // gera as linhas da tabela.
        var  trdatabela = '';
          // inicio gerar tabela
           
          for(z=0;z<qtregistros;z++){

                  data = jsonObj.funcion[z].data;
                  hora1 = jsonObj.funcion[z].hora1.replace(",",":");
                  hora2 = jsonObj.funcion[z].hora2.replace(",",":");
                  hora3 = jsonObj.funcion[z].hora3.replace(",",":");
                  hora4 = jsonObj.funcion[z].hora4.replace(",",":");
                  hora5 ='';  
                  hora6 ='';
                  observacao = jsonObj.funcion[z].situacao;
                  hsdiu = jsonObj.funcion[z].hsdiurnas;
                  hsnot = jsonObj.funcion[z].hsnoturnas;
                  intraj = jsonObj.funcion[z].hsintraj;
                  he1= jsonObj.funcion[z].he50d;
                  he2 = jsonObj.funcion[z].he100d;
                  he1li = jsonObj.funcion[z].he50i;
                  he2li =  jsonObj.funcion[z].he100i;              
                
                   // tratamento visual doscampos HS Not HE1 He2 H1i HE2i observação


                   //Verificação se funcionário tem horário de almoço.
                   if(hora3=='' & hora4==''){
                    hora4 = hora2;
                    hora2 = '';
                    hora3 = ''
                    }

                   if(observacao=="MOBILE"){
                    observacao ='';
                   } 

                   if(hsdiu == '0.0') {
                     hsdiu ='';
                   }

                   if(hsnot == '0.0') {
                     hsnot ='';
                   }

                   if(he1 =='0.0'){
                    he1='';
                   }

                   if(he2 =='0.0'){
                    he2='';
                   }

                   if(he1li =='0.0'){
                    he1li='';
                   }

                   if(he2li =='0.0'){
                    he2li='';
                   }

                trdatabela +=  trdatabela = "<tr>"
                +"<td  name='data'>"+formatarData(data)+"</td>"
                +"<td name='diasem'>"+formatarDiaSem(formatarData(data))+"</td>"
                +"<td  name='entrada1'>"+hora1+"</td>"
                +"<td  name='saida1'>"+hora2+"</td>"
                +"<td name='entrada2'>"+hora3+"</td>"
                +"<td  name='saida2'>"+hora4+"</td>"
                +"<td  name='entrada3'>"+hora5+"</td>"
                +"<td  name='saida3'>"+hora6+"</td>"
                +"<td  name=''observacao'>"+observacao+"</td>"
                +"<td  name='hsdiu'>"+hsdiu+"</td><"
                +"td  name='hsnot'>"+hsnot+"</td>"
                +"<td  name='intraj'>"+intraj+"</td>"
                +"<td  name='he1'>"+he1+"</td>"
                +"<td  name='he2'>"+he2+"</td>"
                +"<td  name='he1li'>"+he1li+"</td>"
                +"<td  name='he2li'>"+he2li+"</td>"
                +"</tr>";    
        }
        //exibe na tabela
        document.getElementById("resultadoTabela").innerHTML = trdatabela;
  
        // fim gerar tabela
        new exibirDadosEmpresa();
        new exibirDadosFuncionario();
        new exibirResumoFolhaF();
       
   }

      // receber dados via JSON e tratar

      function exibirTodosDados(){
        new autenticar();
      }
    
      // Dados do Cabeçalho -> dados da empresa
      function exibirDadosEmpresa(){
         
        document.getElementById('logoE').innerHTML = logoE;
        document.getElementById("nomeE").innerHTML = nomeE;
        document.getElementById("enderecoE").innerHTML = enderecoE;
        document.getElementById("cnpjE").innerHTML = "CNPJ: " + formatarCNPJ(cnpjE) ;
      }

      // Dados do Funcionário
      function exibirDadosFuncionario() {

        document.getElementById("lbMatriculaF").innerHTML = lbMatriculaF;
        document.getElementById("lbFilialF").innerHTML = lbFilialF;
        document.getElementById("lbNomeF").innerHTML = lbNomeF;
        document.getElementById("lbFuncaoF").innerHTML = lbFuncaoF;
        document.getElementById("lbPostoF").innerHTML = lbSecaoF +" - "+ lbPostoF;
        document.getElementById("lbRazaoSocial").innerHTML = lbRazaoSocial;
        document.getElementById("lbHorarioTrabalhoF").innerHTML = lbHorarioTrabalhoF;
        document.getElementById("lbperiodoInicial").innerHTML = formatarData(lbperiodoInicial);
        document.getElementById("lbperiodoFinal").innerHTML = formatarData(lbperiodoFinal);
        document.getElementById("nomeFuncionario").innerHTML =lbNomeF;
      }

      function exibirResumoFolhaF() {
        
         document.getElementById("resumoHrsTrabF").innerHTML = resumoHrsTrabF;
         document.getElementById("resumoDiasTrabF").innerHTML= numInteiro(resumoDiasTrabF);
         document.getElementById("resumoDiasCHSupF").innerHTML= numInteiro(resumoDiasCHSupF);
         document.getElementById("resumoHsProjF").innerHTML= resumoHsProjF;
         document.getElementById("resumoValeRF").innerHTML= resumoValeRF;
         document.getElementById("resumoFeriasF").innerHTML= numInteiro(resumoFeriasF);
         document.getElementById("resumoValeTF").innerHTML= resumoValeTF;
         document.getElementById("resumoHE1F").innerHTML= resumoHE1F;
         document.getElementById("resumoFaltaJustificadaF").innerHTML= numInteiro(resumoFaltaJustificadaF);
         document.getElementById("resumoHE2F").innerHTML= resumoHE2F;
         document.getElementById("resumoAtMedicoF").innerHTML= numInteiro(resumoAtMedicoF);
         document.getElementById("resumoSaldoAnteriorF").innerHTML= resumoSaldoAnteriorF;
         document.getElementById("resumoTrabFeriadoF").innerHTML= resumoTrabFeriadoF;
         document.getElementById("resumoDescDSRF").innerHTML= numInteiro(resumoDescDSRF);
         document.getElementById("resumoHsIntraJF").innerHTML= resumoHsIntraJF;
         document.getElementById("resumoFaltasCompF").innerHTML= numInteiro(resumoFaltasCompF);
         document.getElementById("resumoFolgasF").innerHTML =numInteiro(resumoFolgasF);
         document.getElementById("resumoHsNoturnaF").innerHTML =resumoHsNoturnaF;
         document.getElementById("resumoFaltasF").innerHTML =numInteiro(resumoFaltasF);

      }
     

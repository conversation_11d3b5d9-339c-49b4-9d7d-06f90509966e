package SasBeans;

import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class SasPWFill implements Serializable {

    private String Nome;
    private String CodfilAc;
    private String CodFil;
    private String Codigo;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Descricao;
    private String Matr;
    private String CodGrupo;

    public SasPWFill() {
        Nome = "";
        CodfilAc = "";
        CodFil = "";
        Codigo = "";
        Operador = "";
        Dt_Alter = "";
        Hr_Alter = "";
        Descricao = "";
        Matr = "";
        CodGrupo = "";
    }

    public SasPWFill(SasPWFill original) {
        Nome = original.getNome();
        CodfilAc = original.getCodfilAc();
        CodFil = original.getCodFil();
        Codigo = original.getCodigo();
        Operador = original.getOperador();
        Dt_Alter = original.getDt_Alter();
        Hr_Alter = original.getHr_Alter();
        Descricao = original.getDescricao();
        Matr = original.getMatr();
        CodGrupo = original.getCodGrupo();
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getCodfilAc() {
        return CodfilAc;
    }

    public void setCodfilAc(String CodfilAc) {
        this.CodfilAc = CodfilAc;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public String getCodGrupo() {
        return CodGrupo;
    }

    public void setCodGrupo(String CodGrupo) {
        this.CodGrupo = CodGrupo;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.CodfilAc);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SasPWFill other = (SasPWFill) obj;
//        if (!Objects.equals(this.Nome, other.Nome)) {
//            return false;
//        }
//        if (!Objects.equals(this.Descricao, other.Descricao)) {
//            return false;
//        }
        if (!this.CodfilAc.isEmpty() && !other.CodfilAc.isEmpty() && Integer.parseInt(this.CodfilAc) != Integer.parseInt(other.CodfilAc)) {
            return false;
        }
//        if (!Objects.equals(this.Matr, other.Matr)) {
//            return false;
//        }
        return true;
    }

    @Override
    public String toString() {
        try {
            if (getCodfilAc() == null) {
                return "";
            }
            return String.format("%s[id=%s]", getClass().getSimpleName(), getCodfilAc());
//            return String.format("%04d", Integer.parseInt(this.CodfilAc.toPlainString()))+" - "+this.Descricao;
        } catch (Exception e) {
            try {
                return String.format("%04d", Integer.parseInt(this.CodFil)) + " - " + this.Descricao;
            } catch (Exception e2) {
                return null;
            }
        }
    }
}

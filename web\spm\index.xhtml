<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/faviconSPM.png" />
            <title>#{localemsgs.SPM} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/index.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <style>           
                .DataGrid tbody tr td{
                    text-align: center !important;
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {                                      
                    .rodape-links img{
                        width:10px !important;
                        height:10px !important;
                        margin-left:-14px !important;
                        position:absolute


                    }
                    .rodape-links li{
                        font-size: 8pt !important;
                        text-align:center !important;
                        padding-left:15px !important;
                    }

                    .rodape-links li:nth-child(3){
                        display:none;
                    }

                    .rodape-links{
                        text-align:center !important;
                        white-space:nowrap !important;
                    }

                    #login{
                        margin-top:-70px !important;
                    }
                }
                
            </style>    
        </h:head>
        <h:body id="h" style="overflow:hidden !important; background: #FFF !important;">
            <f:metadata>
                <f:viewParam name="empresa" value="#{login.cli}"/>
                <f:viewAction action="#{login.onRefresh}"/>
            </f:metadata>

            <script>
                var sheets = document.styleSheets;
                console.log(document.styleSheets[0]);
                var sheet = document.styleSheets[6];

                function gup(name)
                {
                    name = name.replace(/[\[]/, "\\\[").replace(/[\]]/, "\\\]");
                    var regexS = "[\\?&amp;]" + name + "=([^&amp;#]*)";
                    var regex = new RegExp(regexS);
                    var results = regex.exec(window.location.href);
                    if (results == null)
                        return "";
                    else if (results[1] == 'SessaoExpirada')
                    {
                        if (navigator.language == 'en-US' || navigator.language == 'en')
                            alert("For security reasons, your session has expired. Please log in again.");
                        else
                            alert("Por motivos de segurança, sua sessão expirou. Por favor, faça login novamente.");

                        top.location.href = 'index.xhtml'
                    } else if (results[1] == 'CadastroSucesso')
                    {
                        alert("Senha cadastrada com sucesso. Efetue login digitando empresa@matricula e a senha cadastrada anteriormente.");
                    } else if (results[1] == 'ErroValidacao')
                    {
                        alert("Erro de validação. Tente novamente.");
                    } else
                        return "_" + results[1];
                }
                var sessao = gup('msg');
            </script>
            <div id="h-wrapper" style="background: #FFF !important;">
                <p:growl id="msgs" widgetVar="msgs" />
                <h:form class="form-inline" id="login" style="overflow:hidden !important;">
                    <div class="col-md-12 col-sm-12 col-xs-12" style="width: 90%; max-width: 400px; height:420px; position: absolute; top:0;right:0;bottom:0;left:0;margin:auto;">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: center">
                            <img src="../assets/img/logo_SPMMONETA.png" />    
                        </div>
                        <div class="mt col-md-12 col-sm-12 col-xs-12" style="text-align: left; padding-top: 12px !important">
                            <h:outputText value="#{localemsgs.AcessoSistema}" rendered="#{!login.exibirEmpresas}" style="color: #000 !important" />
                            <h:outputText value="#{localemsgs.EmpresaServico}" rendered="#{login.exibirEmpresas}" style="color: #000 !important" />
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 5px !important; display:#{login.exibirEmpresas?'none':''}">
                            <h:inputHidden id="txtUrlAtual" value="#{login.enderecoNavegador}"></h:inputHidden>
                            <p:inputText id="email" value="#{login.email}" rendered="#{!login.exibirEmpresas}"
                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"
                                         style="width: 100%; height: 40px;"/>
                            <p:watermark for="email" value="#{login.cli eq null ? localemsgs.Email : localemsgs.Matricula}" 
                                         rendered="#{!login.exibirEmpresas}"/>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 5px !important; display:#{login.exibirEmpresas?'':'none'}">
                            <p:selectOneMenu id="param" value="#{login.empresa}" converter="omnifaces.SelectItemsConverter"
                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Empresa}"
                                             style="width: 100%; height: 40px;"
                                             filter="true" filterMatchMode="contains" rendered="#{login.exibirEmpresas}"  >
                                <f:selectItems value="#{login.usuarios.pessoaLogin}" var="empresas" itemValue="#{empresas}"
                                               itemLabel="#{login.NomeEmpresa(empresas.bancoDados)}"  noSelectionValue="Selecione"/>
                            </p:selectOneMenu>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 12px !important; display:#{login.exibirEmpresas?'none':''}">
                            <p:password id="pw" value="#{login.pwweb}" styleClass="form-control" required="true" 
                                        redisplay="true" rendered="#{!login.exibirEmpresas}"  style="width: 100%; height: 40px;"
                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}">
                                <p:watermark for="pw" value="#{login.cli eq null ? localemsgs.Senha : localemsgs.CPF}" />
                            </p:password>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 12px !important; display:#{login.exibirEmpresas?'':'none'}">
                            <p:selectOneMenu id="servico" value="#{login.pessoaPortalSrv}" converter="omnifaces.SelectItemsConverter" 
                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Servico}"
                                             style="width: 100%; height: 40px;" 
                                             filter="true" filterMatchMode="contains" rendered="#{login.exibirEmpresas}"  >
                                <f:selectItems value="#{login.usuarios.pessoaPortalSrv}" var="servicos" itemValue="#{servicos}"
                                               itemLabel="#{servicos.descricao}"  noSelectionValue="Selecione"/>
                            </p:selectOneMenu>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 12px !important">
                            <p:commandButton class="custom-button" value="#{localemsgs.Entrar}" id="btnLogar" 
                                             action="#{login.Logar}" style="width: calc(100% - 50px) !important; float: left; font-size: 14pt !important; background-color: #F36C25 !important; border: none !important; color: #FFF !important" rendered="#{!login.exibirEmpresas}"
                                             update="msgs login"/>
                            
                            <p:commandButton class="custom-button" value="#{localemsgs.Acessar}" id="btnAcessar" 
                                             action="#{login.acessarServico}" style="width: calc(100% - 50px) !important; float: left;  font-size: 14pt !important; background-color: #F36C25 !important; border: none !important; color: #FFF !important" rendered="#{login.exibirEmpresas}"
                                             update="msgs login"/>
                            
                            <h:commandLink actionListener="#{localeController.increment}" 
                                            action="#{localeController.getLocales}">
                                 <p:graphicImage url="../assets/img/#{localeController.number}.png" style="float: right; width: 40px; background: #E9E9E9; padding: 2px; border: thin solid #CCC; border-radius: 4px;" />
                             </h:commandLink>
                        </div>

                        <script src="../assets/scripts/capson.js"></script>
                        <script>
        $(document).ready(function () {
            $('[id*="txtUrlAtual"]').val(window.location.href);

            $(window).capslockstate();

            $(window).bind("capsOn", function (event) {
                if ($("#login\\:pw:focus").length > 0) {
                    PF('msgs').renderMessage({"summary": "Tecla CapsLk pressionada.", "severity": "info"});
                }
            });
            $("#login\\:pw").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    PF('msgs').renderMessage({"summary": "Tecla CapsLk pressionada.", "severity": "info"});
                }
            });
        });
                        </script>
                    </div>
                </h:form>
            </div>
            <div>
                <h:form id="validarCC">
                    <p:dialog widgetVar="dlgValidarCC" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" width="440"
                              showEffect="drop" hideEffect="drop" style="max-width:90% !important; border-top:4px solid #3C8DBC !important">
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_contracheque_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.ValidarCC}" style="color:#3C8DBC" /> 
                        </f:facet>
                        <p:panel>
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-4" style="align-self: flex-start">
                                        <p:outputLabel for="codigo" value="#{localemsgs.Codigo}: "/>
                                    </div>
                                    <div class="ui-grid-col-8" style="align-self: flex-start">
                                        <p:inputText id="codigo" value="#{login.validadorCC}"
                                                     label="#{localemsgs.Codigo}" styleClass="form-control"/>
                                        <p:watermark for="codigo" value="#{localemsgs.Codigo}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row" style="text-align:right !important">
                                    <p:commandLink id="cadastro"
                                                   update="msgs" action="#{login.CodigoValida}"
                                                   title="#{localemsgs.Concluido}" ajax="false">
                                        <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="primeiroacesso">
                    <p:dialog widgetVar="dlgPrimeiroAcesso" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" width="440"
                              showEffect="drop" hideEffect="drop" style="max-width:90% !important; border-top:4px solid #3C8DBC !important">
                        <f:facet name="header">
                            <img src="assets/img/icone_cadastros.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PrimeiroAcesso}" style="color:#3C8DBC;" /> 
                        </f:facet>
                        <p:panel>
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-4" style="align-self: flex-start">
                                        <p:outputLabel for="matricula" value="#{localemsgs.Matricula}: "/>
                                    </div>
                                    <div class="ui-grid-col-8" style="align-self: flex-start">
                                        <p:inputText id="matricula" value="#{login.matricula}"
                                                     label="#{localemsgs.Matricula}" styleClass="form-control"/>
                                        <p:watermark for="matricula" value="#{localemsgs.Matricula}"/>
                                    </div>
                                </div>

                                <div class="ui-grid-row">
                                    <div class="ui-grid-col-4" style="align-self: flex-start">
                                        <p:outputLabel for="empresa" value="#{localemsgs.Empresa}: "/>
                                    </div>
                                    <div class="ui-grid-col-8" style="align-self: flex-start">
                                        <p:inputText id="empresa" value="#{login.param}"
                                                     label="#{localemsgs.Empresa}" styleClass="form-control"/>
                                        <p:watermark for="empresa" value="#{localemsgs.Empresa}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row" style="text-align:right !important; padding-top: 8px !important;">
                                    <p:commandLink id="cadastro" action="#{login.PrimeiroAcesso}" update="msgs"
                                                   title="#{localemsgs.Concluido}">
                                        <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="cliente">
                    <p:dialog widgetVar="dlgSelecionarCliente" positionType="absolute" id="dlgClientes"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">  
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="tabelaClientes" style="width: 500px; background: transparent">
                            <div class="form-inline">
                                <p:dataTable id="tabela" value="#{login.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="cli" resizableColumns="true" selection="#{login.selecionado}" rowKey="#{cli.codCli}"
                                             scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabela"
                                             style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                             styleClass="tabela DataGrid" filteredValue="#{login.clientesFiltrados}">
                                    <f:facet name="footer">                                       
                                        <div class="ui-grid-row ui-grid-responsive">
                                            <div class="container col-md-12 row" style="white-space:nowrap">
                                                <div class="col-md-6" style="color:black; text-align: left; font-weight: normal; white-space:nowrap; padding-left:0px !important;">
                                                    <h:outputText value="#{localemsgs.VerTodos}: "/>
                                                    <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                                </div>
                                                <div class="col-md-6" style="color:black; text-align: right; font-weight: normal; white-space:nowrap; padding-left:0px !important;">
                                                    <h:outputText value="#{localemsgs.Buscar}: " />
                                                    <p:inputText id="globalFilter" onkeypress="if (event.keyCode == 13) {
                                                                PF('tabela').filter();
                                                                return false;
                                                            }"
                                                                 style="width:150px;"/>
                                                </div>
                                            </div>
                                        </div>
                                    </f:facet>
                                    <p:ajax event="rowDblselect" listener="#{login.dblSelectGTVIndex}" update="msgs"/>
                                    <p:column rendered="#{!login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.Empresa}" style="width: 145px" filterBy="#{cli.operador}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.operador}" title="#{cli.operador}">
                                        </h:outputText>
                                    </p:column>
                                    <p:column rendered="#{login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.Pref}" style="width: 60px" filterBy="#{cli.agencia}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.agencia}" title="#{cli.agencia}">
                                        </h:outputText>
                                    </p:column>
                                    <p:column rendered="#{login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.NSOP}" style="width: 60px" filterBy="#{cli.subAgencia}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.subAgencia}" title="#{cli.subAgencia}">
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{login.pp.empresa.contains('CONFEDERAL') ? localemsgs.Agencia: localemsgs.Cliente}" filterBy="#{cli.nomeCli}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.nomeCli}" title="#{cli.nomeCli}" />
                                    </p:column>
                                </p:dataTable>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteGTVIndex}"
                                               title="#{localemsgs.Selecionar}">
                                    <p:graphicImage url="assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="esqueciSenha">
                    <p:dialog widgetVar="dlgEsqueciSenha" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" width="440"
                              showEffect="drop" hideEffect="drop" style="max-width:90% !important; border-top:4px solid #3C8DBC !important">
                        <f:facet name="header">
                            <img src="assets/img/icone_configuracoes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#3C8DBC" /> 
                        </f:facet>
                        <p:panel id="trocarSenha">
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="email" value="#{localemsgs.Email}: " rendered="#{login.naoPossuoSenhaDia eq false}"/>
                                        <p:outputLabel for="email" value="#{localemsgs.EmpresaMatricula}"  rendered="#{login.naoPossuoSenhaDia}"/>
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="email" value="#{login.email}" styleClass="form-control"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"/>
                                        <p:watermark for="email" value="#{localemsgs.Email}"  rendered="#{login.naoPossuoSenhaDia eq false}"/>
                                        <p:watermark for="email" value="#{localemsgs.EmpresaMatricula}"  rendered="#{login.naoPossuoSenhaDia}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="senhadia" value="#{localemsgs.SenhaDia}: "/>
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="senhadia" value="#{login.senhaDia}" disabled="#{login.naoPossuoSenhaDia}"
                                                     label="#{localemsgs.SenhaDia}" styleClass="form-control"/>
                                        <p:watermark for="senhadia" value="#{localemsgs.SenhaDia}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-11" style="text-align: right">
                                        <p:outputLabel for="senhadia" value="#{localemsgs.NaoPossuoSenhaDia}"/>
                                    </div>
                                    <div class="ui-grid-col-1" style="text-align: right">
                                        <p:selectBooleanCheckbox value="#{login.naoPossuoSenhaDia}">
                                            <p:ajax update="trocarSenha" listener="#{login.naoPossuo}"/>
                                        </p:selectBooleanCheckbox>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="campo1" rendered="#{login.rand eq 1}" value="#{localemsgs.RGn}: " />
                                        <p:outputLabel for="campo1" rendered="#{login.rand eq 2}" value="#{localemsgs.CidadeRes}: " />
                                        <p:outputLabel for="campo1" rendered="#{login.rand eq 3}" value="#{localemsgs.CidadeRes}: " />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="campo1" style="width: 100%" value="#{login.validacao1}" rendered="#{login.rand ne 0}"/>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="campo2" rendered="#{login.rand eq 1}" value="#{localemsgs.CPFn}: " />
                                        <p:outputLabel for="campo2" rendered="#{login.rand eq 2}" value="#{localemsgs.CPFn}: " />
                                        <p:outputLabel for="campo2" rendered="#{login.rand eq 3}" value="#{localemsgs.Dt_Nascn}: " />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="campo2" style="width: 100%" value="#{login.validacao2}" rendered="#{login.rand ne 0}"/>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="novaSenhaTexto" rendered="#{login.senhaEsquecida ne null}"
                                                       value="#{localemsgs.NovaSenha}: " style="color: #022a48"/>
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="novaSenhaTexto" style="width: 100%; font-weight: bold; opacity: 0.5;"
                                                     value="#{login.senhaEsquecida}" rendered="#{login.senhaEsquecida ne null}"/>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 5px; text-align:right !important; width:100% !important">

                                    <p:commandLink id="cadastro" action="#{login.trocarSenha}" update="msgs esqueciSenha:trocarSenha"
                                                   title="#{localemsgs.TrocarSenha}">
                                        <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>


                                    <p:commandLink id="fechar" oncomplete="PF('dlgEsqueciSenha').hide();"
                                                   title="#{localemsgs.Fechar}" style="margin-left:8px !important;">
                                        <p:graphicImage url="assets/img/icone_fechar.png" width="40" height="40" />
                                    </p:commandLink>

                                </div>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="clienteEW">
                    <p:dialog widgetVar="dlgSelecionarClienteEW" positionType="absolute" id="dlgClienteEW"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">  
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="tabelaClientesEW" style="width: 500px; background: transparent">
                            <div class="form-inline">
                                <p:dataTable id="tabelaClienteEW" value="#{login.postos}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="cli" resizableColumns="true" selection="#{login.postoSelecionado}" rowKey="#{cli.secao}"
                                             scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabelaClienteEW"
                                             style="font-size: 12px; float: left" styleClass="tabela" filteredValue="#{login.postosFiltrados}">
                                    <f:facet name="footer">
                                        <div class="ui-grid-row ui-grid-responsive">
                                            <div class="ui-grid-col-6" style="color:black; text-align: left; font-weight: normal">
                                                <h:outputText value="#{localemsgs.VerTodos}: "/>
                                                <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                            </div>
                                            <div class="ui-grid-col-6" style="color:black; text-align: right; font-weight: normal">
                                                <h:outputText value="#{localemsgs.Buscar}: " />
                                                <p:inputText id="globalFilter" onkeyup="PF('tabelaClienteEW').filter()" style="width:150px;"/>
                                            </div>
                                        </div>
                                    </f:facet>
                                    <p:ajax event="rowDblselect" listener="#{login.dblSelectEWIndex}" update="msgs"/>
                                    <p:column headerText="#{localemsgs.Codigo}" style="width: 150px" filterBy="#{cli.secao}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.secao}" title="#{cli.secao}" >
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cliente}" filterBy="#{cli.local}" style="width: 285px"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.local}" title="#{cli.local}" converter="tradutor"/>
                                    </p:column>
                                </p:dataTable>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteEWIndex}"
                                               title="#{localemsgs.Selecionar}">
                                    <p:graphicImage url="assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="clienteCofre">
                    <p:dialog widgetVar="dlgClienteCofre" positionType="absolute" id="dlgClienteCofre"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">  
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="tabelaClientesCofre" style="width: 500px; background: transparent">
                            <div class="form-inline">
                                <p:dataTable id="tabelaClienteCofre" value="#{login.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="cli" resizableColumns="true" selection="#{login.selecionado}" rowKey="#{cli.codCli}"
                                             scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabelaClienteCofre"
                                             style="font-size: 12px; float: left" styleClass="tabela" filteredValue="#{login.clientesFiltrados}">
                                    <f:facet name="footer">
                                        <div class="ui-grid-row ui-grid-responsive">
                                            <div class="ui-grid-col-6" style="color:black; text-align: left; font-weight: normal">
                                                <h:outputText value="#{localemsgs.VerTodos}: "/>
                                                <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                            </div>
                                            <div class="ui-grid-col-6" style="color:black; text-align: right; font-weight: normal">
                                                <h:outputText value="#{localemsgs.Buscar}: " />
                                                <p:inputText id="globalFilter" onkeyup="PF('tabelaClienteCofre').filter()" style="width:150px;"/>
                                            </div>
                                        </div>
                                    </f:facet>
                                    <p:ajax event="rowDblselect" listener="#{login.dblSelectCofreIndex}" update="msgs"/>
                                    <p:column headerText="#{localemsgs.Codigo}" style="width: 150px" filterBy="#{cli.codCli}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.codCli}" title="#{cli.codCli}" >
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cliente}" filterBy="#{cli.nomeCli}" style="width: 285px"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.nomeCli}" title="#{cli.nomeCli}" converter="tradutor"/>
                                    </p:column>
                                </p:dataTable>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteCofreIndex}"
                                               title="#{localemsgs.Selecionar}">
                                    <p:graphicImage url="assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package ClassesJson.BI;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *
 * <AUTHOR>
 */
public class ReceberXRecebidosSomaValores {

    private String CompetPg;
    private BigDecimal Valor;
    private BigDecimal ValorPago;
    private String DtPagto;

    public String getDtPagto() {
        return DtPagto;
    }

    public void setDtPagto(String DtPagto) {
        this.DtPagto = DtPagto;
    }

    public BigDecimal getValor() {
        return Valor.setScale(2, RoundingMode.FLOOR);
    }

    public void setValor(BigDecimal Valor) {
        Valor.setScale(2, RoundingMode.FLOOR);
        this.Valor = Valor;
    }

    public BigDecimal getValorPago() {
        return ValorPago.setScale(2, RoundingMode.FLOOR);
    }

    public void setValorPago(BigDecimal ValorPago) {
        ValorPago.setScale(2, RoundingMode.FLOOR);
        this.ValorPago = ValorPago;
    }

    public String getCompetPg() {
        return CompetPg;
    }

    public void setCompetPg(String CompetPg) {
        this.CompetPg = CompetPg;
    }

}

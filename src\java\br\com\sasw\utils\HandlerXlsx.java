/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.utils;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Controller.Filiais.FiliaisSatMobWeb;
import Controller.Funcion.FuncionSatMobWeb;
import Controller.Pessoas.PessoasSatMobWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.Pessoa;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.primefaces.PrimeFaces;

/**
 *
 * <AUTHOR>
 */
public class HandlerXlsx {

    private String operador, banco, caminholog, log, sucesso;
    private ArquivoLog logerro;
    private Persistencia persistencia;
    private Persistencia central;
    private Pessoa pessoa;
    private Funcion funcion;
    private Clientes clientes;
    private Filiais filiais;
    private final PessoasSatMobWeb pessoasSatMobWeb;
    private final FuncionSatMobWeb funcionSatMobWeb;
    private final ClientesSatMobWeb clientesSatMobWeb;
    private final FiliaisSatMobWeb filiaisSatMobWeb;
    private List<String> erros;
    private int total;

    public HandlerXlsx(String op) {
        operador = op;
        banco = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("banco");
        caminholog = FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator) + "msgerros_" + banco + ".txt";
        logerro = new ArquivoLog();
        pessoasSatMobWeb = new PessoasSatMobWeb();
        funcionSatMobWeb = new FuncionSatMobWeb();
        clientesSatMobWeb = new ClientesSatMobWeb();
        filiaisSatMobWeb = new FiliaisSatMobWeb();
    }

    public void Persistencia(Persistencia pp, Persistencia local) {
        try {
            this.persistencia = pp;
            if (this.persistencia == null) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.central = local;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()) + " " + banco, null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminholog);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void DefineSaida(InputStream inputStream, int parametro) {
        try {
            this.total = 0;
            switch (parametro) {
                case 1:
                    LeArqPessoa(inputStream);
                    this.sucesso = this.total + " " + Messages.getMessageS("Pessoas") + " " + Messages.getMessageS("ImportadasSucesso");
                    break;
                case 2:
                    LeArqFuncionario(inputStream);
                    this.sucesso = this.total + " " + Messages.getMessageS("Funcion") + " " + Messages.getMessageS("ImportadosSucesso");
                    break;
                case 3:
                    LeArqClientes(inputStream);
                    this.sucesso = this.total + " " + Messages.getMessageS("Clientes") + " " + Messages.getMessageS("ImportadosSucesso");
                    break;
                case 4:
                    LeArqFiliais(inputStream);
                    this.sucesso = this.total + " " + Messages.getMessageS("Filiais") + " " + Messages.getMessageS("ImportadasSucesso");
                    break;
                case 5:
                    LeArqFiliais(inputStream);
                    this.sucesso = this.total + " " + Messages.getMessageS("Filiais") + " " + Messages.getMessageS("ImportadasSucesso");
                    break;
            }
            PrimeFaces.current().executeScript("PF('dlgArqUp').hide();");
            FacesMessage message;
            if (!this.erros.isEmpty()) {
                PrimeFaces.current().ajax().update("formErrosImportacao");
                PrimeFaces.current().executeScript("PF('dlgErrosImportacao').show();");
                message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ImportacaoCompleta"), Messages.getMessageS("ImportacaoCompletaComErros"));
            } else {
                message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ImportacaoCompleta"), sucesso);
            }
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            PrimeFaces.current().ajax().update("uploadForm:upload");
            FacesMessage message;
            if (e.getMessage().equals(("ArquivoInvalido"))) {
                message = new FacesMessage(FacesMessage.SEVERITY_FATAL, Messages.getMessageS(e.getMessage()), Messages.getMessageS("DicaDownload"));
            } else {
                message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            }
            FacesContext.getCurrentInstance().addMessage(null, message);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminholog);
        }
    }

    private void LeArqPessoa(InputStream inputStream) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = workbook.getSheetAt(0);
        this.erros = new ArrayList<>();
        if (!sheet.getSheetName().equals("Pessoas")) {
            throw new Exception("ArquivoInvalido");
        }
        Iterator linhas = sheet.rowIterator();
        while (linhas.hasNext()) {
            try {
                this.pessoa = new Pessoa();
                XSSFRow linha = (XSSFRow) linhas.next();
                Iterator celulas = linha.cellIterator();
                if (linha.getRowNum() != 0) {
                    while (celulas.hasNext()) {
                        XSSFCell celula = (XSSFCell) celulas.next();
                        switch (celula.getColumnIndex()) {
                            case 0:
                                this.pessoa.setEmail(celula.toString());
                                break;
                            case 1:
                                this.pessoa.setNome(celula.toString());
                                break;
                            case 2:
                                this.pessoa.setRG(celula.getRawValue());
                                break;
                            case 3:
                                this.pessoa.setRGOrgEmis(celula.toString());
                                break;
                            case 4:
                                this.pessoa.setCPF(celula.getRawValue());
                                break;
                            case 5:
                                this.pessoa.setFone1(celula.getRawValue());
                                break;
                            case 6:
                                this.pessoa.setFone2(celula.getRawValue());
                                break;
                            case 7:
                                this.pessoa.setEndereco(celula.toString());
                                break;
                            case 8:
                                this.pessoa.setBairro(celula.toString());
                                break;
                            case 9:
                                this.pessoa.setCidade(celula.toString());
                                break;
                            case 10:
                                this.pessoa.setUF(celula.toString());
                                break;
                            case 11:
                                this.pessoa.setCEP(celula.getRawValue());
                                break;
                            case 12:
                                this.pessoa.setSituacao(celula.toString());
                                break;
                            case 13:
                                this.pessoa.setFuncao(celula.toString());
                                break;
                            case 14:
                                this.pessoa.setSexo(celula.toString());
                                break;
                            case 15:
                                this.pessoa.setAltura(celula.toString());
                                break;
                            case 16:
                                this.pessoa.setPeso(celula.toString());
                                break;
                        }
                    }
                    this.pessoa.setObs(Messages.getMessageS("ViaImportacao"));
                    this.pessoa.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                    this.pessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
                    this.pessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
                    this.pessoasSatMobWeb.Inserir(this.pessoa, this.persistencia, this.central);
                    this.total++;
                }
            } catch (Exception e) {
                this.erros.add(Messages.getMessageS(e.getMessage()));
                log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminholog);
            }
        }
    }

    private void LeArqFuncionario(InputStream inputStream) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = workbook.getSheetAt(0);
        this.erros = new ArrayList<>();
        if (!sheet.getSheetName().equals("Funcionários")) {
            throw new Exception("ArquivoInvalido");
        }
        Iterator linhas = sheet.rowIterator();
        while (linhas.hasNext()) {
            try {
                XSSFRow linha = (XSSFRow) linhas.next();
                Iterator celulas = linha.cellIterator();
                if (linha.getRowNum() != 0) {
                    this.funcion = new Funcion();
                    while (celulas.hasNext()) {
                        XSSFCell celula = (XSSFCell) celulas.next();
                        switch (celula.getColumnIndex()) {
                            case 0:
                                this.funcion.setCodFil(celula.toString());
                                break;
                            case 1:
                                this.funcion.setMatr(celula.getRawValue());
                                break;
                            case 2:
                                this.funcion.setNome(celula.toString());
                                break;
                            case 3:
                                this.funcion.setCPF(celula.getRawValue());
                                break;
                            case 4:
                                this.funcion.setEmail(celula.toString());
                                break;
                            case 5:
                                this.funcion.setRG(celula.getRawValue());
                                break;
                            case 6:
                                this.funcion.setOrgEmis(celula.toString());
                                break;
                            case 7:
                                this.funcion.setFone1(celula.getRawValue());
                                break;
                            case 8:
                                this.funcion.setFone2(celula.getRawValue());
                                break;
                            case 9:
                                this.funcion.setCEP(celula.getRawValue());
                                break;
                            case 10:
                                this.funcion.setEndereco(celula.toString());
                                break;
                            case 11:
                                this.funcion.setBairro(celula.toString());
                                break;
                            case 12:
                                this.funcion.setCidade(celula.toString());
                                break;
                            case 13:
                                this.funcion.setObs(celula.toString());
                                break;
                            case 14:
                                this.funcion.setAltura(celula.toString());
                                break;
                            case 15:
                                this.funcion.setPeso(celula.toString());
                                break;
                            case 16:
                                this.funcion.setSexo(celula.toString());
                                break;
                            case 17:
                                this.funcion.setNome_Guer(celula.toString());
                                break;
                            case 18:
                                this.funcion.setSituacao(celula.toString());
                                break;
                            case 19:
                                this.funcion.setSecao(celula.toString());
                                break;
                        }
                    }
                    this.pessoa = this.pessoasSatMobWeb.buscarPessoa(this.funcion.getCPF(), this.central);
                    this.funcion.setObs(Messages.getMessageS("ViaImportacao"));
                    this.funcion.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                    this.funcion.setHr_Alter(DataAtual.getDataAtual("HORA"));
                    this.funcion.setDt_Alter(DataAtual.getDataAtual("SQL"));
                    this.funcionSatMobWeb.Inserir(this.funcion, this.pessoa, this.persistencia, this.central);
                    this.total++;
                }
            } catch (Exception e) {
                this.erros.add(Messages.getMessageS(e.getMessage()));
                log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminholog);
            }
        }
    }

    private void LeArqClientes(InputStream inputStream) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = workbook.getSheetAt(0);
        this.erros = new ArrayList<>();
        if (!sheet.getSheetName().equals("Clientes")) {
            throw new Exception("ArquivoInvalido");
        }
        Iterator linhas = sheet.rowIterator();
        while (linhas.hasNext()) {
            try {
                XSSFRow linha = (XSSFRow) linhas.next();
                Iterator celulas = linha.cellIterator();
                if (linha.getRowNum() != 0) {
                    this.clientes = new Clientes();
                    while (celulas.hasNext()) {
                        XSSFCell celula = (XSSFCell) celulas.next();
                        switch (celula.getColumnIndex()) {
                            case 0:
                                this.clientes.setCodFil(celula.toString());
                                break;
                            case 1:
                                this.clientes.setNome(celula.toString());
                                break;
                            case 2:
                                this.clientes.setNRed(celula.toString());
                                break;
                            case 3:
                                this.clientes.setCEP(celula.getRawValue());
                                break;
                            case 4:
                                this.clientes.setEnde(celula.toString());
                                break;
                            case 5:
                                this.clientes.setBairro(celula.toString());
                                break;
                            case 6:
                                this.clientes.setCidade(celula.toString());
                                break;
                            case 7:
                                this.clientes.setEstado(celula.toString());
                                break;
                            case 8:
                                this.clientes.setFone1(celula.getRawValue());
                                break;
                            case 9:
                                this.clientes.setFone2(celula.getRawValue());
                                break;
                            case 10:
                                this.clientes.setEmail(celula.toString());
                                break;
                            case 11:
                                this.clientes.setCGC(celula.getRawValue());
                                break;
                            case 12:
                                this.clientes.setInsc_Munic(celula.getRawValue());
                                break;
                            case 13:
                                this.clientes.setIE(celula.getRawValue());
                                break;
                            case 14:
                                this.clientes.setCPF(celula.getRawValue());
                                break;
                            case 15:
                                this.clientes.setRG(celula.getRawValue());
                                break;
                        }
                    }
                    this.clientes.setObs(Messages.getMessageS("ViaImportacao"));
                    this.clientes.setOper_Inc(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                    this.clientes.setOper_Alt(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                    this.clientes.setHr_Alter(DataAtual.getDataAtual("HORA"));
                    this.clientes.setDt_Alter(LocalDate.now());
                    this.clientesSatMobWeb.GravaNovoCliente(this.clientes, this.persistencia);
                    this.total++;
                }
            } catch (Exception e) {
                this.erros.add(Messages.getMessageS(e.getMessage()));
                log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminholog);
            }
        }
    }

    private void LeArqFiliais(InputStream inputStream) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = workbook.getSheetAt(0);
        this.erros = new ArrayList<>();
        if (!sheet.getSheetName().equals("Filiais")) {
            throw new Exception("ArquivoInvalido");
        }
        Iterator linhas = sheet.rowIterator();
        while (linhas.hasNext()) {
            try {
                XSSFRow linha = (XSSFRow) linhas.next();
                Iterator celulas = linha.cellIterator();
                if (linha.getRowNum() != 0) {
                    this.filiais = new Filiais();
                    while (celulas.hasNext()) {
                        XSSFCell celula = (XSSFCell) celulas.next();
                        switch (celula.getColumnIndex()) {
                            case 0:
                                this.filiais.setCodFil(celula.toString());
                                if (this.filiais.getCodFil().equals(BigDecimal.ZERO)) {
                                    throw new Exception("FilialInvalida");
                                }
                                break;
                            case 1:
                                this.filiais.setDescricao(celula.toString());
                                break;
                            case 2:
                                this.filiais.setRazaoSocial(celula.toString());
                                break;
                        }
                    }
                    this.filiais.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                    this.filiais.setHr_Alter(DataAtual.getDataAtual("HORA"));
                    this.filiais.setDt_Alter(DataAtual.getDataAtual("SQL"));
                    this.filiaisSatMobWeb.InserirFilial(this.filiais, this.persistencia);
                    this.total++;
                }
            } catch (Exception e) {
                this.erros.add(Messages.getMessageS(e.getMessage()));
                log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminholog);
            }
        }
    }

    public List<String> getErros() {
        return erros;
    }

    public void setErros(List<String> erros) {
        this.erros = erros;
    }

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }
}

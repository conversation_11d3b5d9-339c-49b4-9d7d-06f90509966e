/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Saspwac;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SaspwAcDao {

    public void insereSasPwAc(Saspwac saspWac, boolean incl, boolean alte, boolean excl, boolean sml, Persistencia persistencia) throws Exception {
        String sql = " insert into saspwac(nome, sistema, inclusao, alteracao, exclusao, operador, dt_alter, hr_alter) "
                + "values(?,?,?,?,?,?,?,?) ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(saspWac.getNome());
            consulta.setBigDecimal(saspWac.getSistema());
            if (incl) {
                //consulta.setInt( saspWac.getInclusao()); 
                consulta.setInt(1);
            } else {
                consulta.setInt(0);
            }
            if (alte) {
                // consulta.setInt( saspWac.getAlteracao());   
                consulta.setInt(1);
            } else {
                consulta.setInt(0);
            }
            if (excl) {
                //consulta.setInt( saspWac.getExclusao());     
                consulta.setInt(1);
            } else {
                consulta.setInt(0);
            }
            consulta.setString(saspWac.getOperador());

            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));

            consulta.insert();
            consulta.close();
        } catch (SQLException e) {
            throw new RuntimeException("Falha ao inserir Acessos (saspwAc) " + e.getMessage());
        }
    }

    public List<Saspwac> verificaSasPWAc(Saspwac saspwAc, Persistencia persistencia) throws Exception {
        List<Saspwac> lSaspwac = new ArrayList<>();
        String sql = "select nome, sistema from saspwac where nome = ? and sistema=?";
        try {
            Consulta consult = new Consulta(sql, persistencia);

            consult.setString(saspwAc.getNome());
            consult.setBigDecimal(saspwAc.getSistema());
            consult.select();
            while (consult.Proximo()) {
                Saspwac pl = new Saspwac();
                pl.setSistema(consult.getString("sistema"));
                lSaspwac.add(pl);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao desassociar pessoa à empresas autorizadas - " + e.getMessage());
        }
        return lSaspwac;
    }

    public static List<Saspwac> getPermissaoAcesso(String sCodPessoa, String sTipo, Persistencia persistencia) throws Exception {
        List<Saspwac> lSaspwAc = new ArrayList<>();

        String sql = "select saspwac.sistema, pessoa.codpessoaweb, pessoa.nome, pessoa.codigo, saspw.codpessoa, "
                + "saspw.nomecompleto, saspw.nome, saspwac.nome "
                + "from pessoa "
                + "inner join saspw on pessoa.codigo = saspw.codpessoa "
                + "inner join saspwac on saspw.nome = saspwac.nome "
                + "where pessoa.codpessoaweb = ? and sistema = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCodPessoa);
            consult.setString(sTipo);
            consult.select();
            while (consult.Proximo()) {
                Saspwac pl = new Saspwac();
                pl.setSistema(consult.getString("sistema"));
                pl.setNome(consult.getString("nome"));
                lSaspwAc.add(pl);
            }
            consult.Close();
            return lSaspwAc;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar controles - " + e.getMessage());
        }
    }

    public List<Saspwac> getSasPwAc(String sNome, Persistencia persistencia) throws Exception {
        List<Saspwac> lSaspwAc = new ArrayList<>();

        String sql = "select saspwac.sistema, sysdef.subsistema, saspwac.inclusao, saspwac.alteracao "
                + ", saspwac.exclusao, saspwac.operador, saspwac.dt_alter, saspwac.hr_alter "
                + "from saspwac "
                + "inner join sysdef on saspwac.sistema = sysdef.codigo "
                + "where saspwac.nome=? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sNome);
            consult.select();
            while (consult.Proximo()) {
                Saspwac pl = new Saspwac();
                pl.setSistema(consult.getString("sistema").replace(".0", ""));
                pl.setNome(consult.getString("subsistema"));
                pl.setInclusao(consult.getInt("inclusao"));
                pl.setAlteracao(consult.getInt("alteracao"));
                pl.setExclusao(consult.getInt("exclusao"));
                pl.setOperador(consult.getString("operador"));
                pl.setDt_Alter(consult.getString("dt_alter"));
                pl.setHr_Alter(consult.getString("hr_alter"));
                lSaspwAc.add(pl);
            }
            consult.Close();
            return lSaspwAc;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar controles - " + e.getMessage());
        }
    }

    /**
     * Carrega lista de acessos
     *
     * @param CodPessoa -
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Saspwac> getSasPwAc(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        List<Saspwac> lSaspwAc = new ArrayList<>();

        String sql = "select saspwac.sistema, sysdef.subsistema, saspwac.inclusao, saspwac.alteracao, "
                + " saspwac.exclusao"
                + " from saspwac "
                + " inner join sysdef on saspwac.sistema = sysdef.codigo "
                + " inner join saspw on saspw.nome = saspwac.nome"
                + " where saspw.codpessoa = ? and saspw.situacao = 'A'";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.select();
            while (consult.Proximo()) {
                Saspwac pl = new Saspwac();
                pl.setSistema(consult.getString("sistema").replace(".0", ""));
                pl.setNome(consult.getString("subsistema"));
                pl.setInclusao(consult.getInt("inclusao"));
                pl.setAlteracao(consult.getInt("alteracao"));
                pl.setExclusao(consult.getInt("exclusao"));
                lSaspwAc.add(pl);
            }
            consult.Close();
            return lSaspwAc;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar controles - " + e.getMessage());
        }
    }

    public Boolean deletaAcesso(Saspwac saspwac, Persistencia persistencia) throws Exception {
        Boolean resultado = true;
        String sql = "delete from saspwac where nome = ? and sistema = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(saspwac.getNome());
            consulta.setBigDecimal(saspwac.getSistema());
            resultado = consulta.delete() > 0;
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao desassociar pessoa à empresas autorizadas - " + e.getMessage());
        }
        return resultado;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class CardapioDia {

    private String Sequencia;
    private String CodCardapio;
    private String Data;
    private String CodDieta;
    private String Especificacao;
    private String Operador;
    private String Dt_alter;
    private String Hr_Alter;
    private String CodCli;
    private String Periodo;

    public CardapioDia() {
        this.Sequencia = "0";
        this.CodCardapio = null;
        this.CodDieta = null;
        this.Data = "";
        this.Especificacao = "";
        this.Operador = "";
        this.Dt_alter = "";
        this.Hr_Alter = "";
        this.CodCli = "";
        this.Periodo = "";
    }

    public String getEspecificacao() {
        return Especificacao;
    }

    public void setEspecificacao(String Especificacao) {
        this.Especificacao = Especificacao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodCardapio() {
        return CodCardapio;
    }

    public void setCodCardapio(String CodCardapio) {
        this.CodCardapio = CodCardapio;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getCodDieta() {
        return CodDieta;
    }

    public void setCodDieta(String CodDieta) {
        this.CodDieta = CodDieta;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getPeriodo() {
        return Periodo;
    }

    public void setPeriodo(String Periodo) {
        this.Periodo = Periodo;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.operacoes;

import Arquivo.ArquivoLog;
import Controller.Login.LoginSatMobWeb;
import Controller.Pedidos.PedidosSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import Decoder.BASE64Encoder;
import SasBeans.Clientes;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.EmailsEnviar;
import SasBeans.Escala;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.OS_Vig;
import SasBeans.Paramet;
import SasBeans.Pedido;
import SasBeans.Pessoa;
import SasBeans.Rastrear;
import SasBeans.Rotas;
import SasBeans.Rt_Guias;
import SasBeans.Rt_Hist;
import SasBeans.Rt_Modelo;
import SasBeans.Rt_Perc;
import SasBeans.SasPWFill;
import SasBeans.Veiculos;
import SasBeansCompostas.EGtv;
import SasBeansCompostas.PedidoRefeicaoItens;
import SasBeansCompostas.PreOrderManifesto;
import SasDaos.ClientesDao;
import SasDaos.EGtvDao;
import SasDaos.RotasDao;
import SasDaos.Rt_PercDao;
import br.com.sasw.lazydatamodels.RotasTvLazyList;
import br.com.sasw.managedBeans.guia.GuiasClienteMB;
import br.com.sasw.pacotesuteis.controller.rotas.RotasSPM;
import br.com.sasw.pacotesuteis.sasbeans.CtrOperEquip;
import br.com.sasw.pacotesuteis.sasbeans.Rt_GuiasFat;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.MovimentacaoContainer;
import br.com.sasw.pacotesuteis.sasdaos.PedidoRefeicaoItensDao;
import br.com.sasw.pacotesuteis.sasdaos.Rt_GuiasFatDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.PreencheEsquerda;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.pacotesuteis.utilidades.LerArquivo;
import br.com.sasw.pacotesuteis.utilidades.Logos;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogoAnexo;
import br.com.sasw.utils.LocaleController;
import static br.com.sasw.utils.Mapas.*;
import br.com.sasw.utils.Mascaras;
import static br.com.sasw.utils.Mascaras.CEP;
import static br.com.sasw.utils.Mascaras.CNPJ;
import static br.com.sasw.utils.Mascaras.Data;
import static br.com.sasw.utils.Mascaras.Fone;
import static br.com.sasw.utils.Mascaras.Hora;
import static br.com.sasw.utils.Mascaras.Moeda;
import static br.com.sasw.utils.Mascaras.removeMascaraData;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.awt.Point;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.imageio.ImageIO;
import javax.inject.Named;
import org.krysalis.barcode4j.impl.code128.Code128Bean;
import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleEvent;
import org.primefaces.event.map.OverlaySelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.Visibility;
import org.primefaces.model.map.DefaultMapModel;
import org.primefaces.model.map.LatLng;
import org.primefaces.model.map.MapModel;
import org.primefaces.model.map.Marker;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@Named(value = "rotasescala")
@ViewScoped
public class RotasEscalaMB implements Serializable {

    private Persistencia persistencia, satellite;
    private List<Rotas> rotasSelecao;
    private Rotas novaRota, rotaSelecionada;
    private List<Rt_Perc> trajetos, trajetosSugeridos, posicoesClientes, posicoesClientesMapa, listaPedidos, listaRoteirizacao;
    private Rt_Perc trajetoSelecionado, trajetoValidacao, trajetoSugeridoSelecionado, trajetoSugestaoRoteirizacao;
    private Paramet parametros;
    private List<PreOrderManifesto> manifesto;
    private List<Pessoa> listaPessoa;
    private Pessoa pessoa, motorista, chEquipe, vigilante1, vigilante2, vigilante3;
    private List<Funcion> folgas;
    private OS_Vig osSelecionada;
    private List<Veiculos> veiculos;
    private Veiculos veiculo;
    private BigDecimal sequencia, parada, codpessoa, valorTotal;
    private Pedido dadosPedido, roteirizacaoSelecionado;
    private String operador, numeroPedido, tipoCarregamentoSugestao, codCliReaproveitaOrigem, codCliReaproveitaDestino;
    private String banco, Hora1Ref, Hora2Ref, LatRef, LonRef, nomeNovoModelo;
    private String dataModelo, dataModeloFinal, pinMapaPedido;
    private int qtdePedidos, tolerancia, qtdeRotasMapa;
    private String filialDesc, codfil, rota, tipoMapa, dataTela, horarioMapa, dataRota, caminho, log, centroMapa, horaAtual, msgVeiculo, html, relatorio,
            tabela, coluna, linha, span, dataContainer1, dataContainer2, nomeArquivo, pesquisaRota,
            directions, directionsTrajetoAtual, directionsTrajetoFuturo, directionsTrajetoProximo, centro, markers, contentStrings, infoWindows, legend,
            sobrePosicaoVariaveis, sobrePosicaoDirectionsService, sobrePosicaoDirectionsDisplaySet, sobrePosicaoDirectionsCalculateDisplay, sobrePosicaoSwitch, carregamentoSerie, carregamentoGuia,
            extPersistencia, codNivel, chefeEquipe, ultimaPosicao, ultimaPosicaoData, tipoModelo;
    private List<Rt_Modelo> listaRotaModelos;
    private Rt_Modelo rotaModeloSelecionada;

    private List<String> matrs;
    private List<CxFGuiasVol> containers, containersFiltrado;
    private CxFGuiasVol containerSelecionado;
    private List<MovimentacaoContainer> historicoMovimentacao;
    private ArquivoLog logerro;
    private static Date ultimoDia;
    private Clientes cliOri, cliDst, clienteManifest;
    private List<Clientes> listaClientes, clientesManifest, listaClientesDestino;
    private boolean mostrarFiliais, somenteAtivos, senha, exclFlag,
            infoChEquipe, permissaoRota, apenasModelosAtivos, roteirizaAuto, relatorioDuasDatas;
    private final LoginSatMobWeb loginSatMobWeb;
    private SasPWFill filial;
    private final RotasSatWeb rotassatweb;
    private RotasSPM rotasSPM;
    private int flag, flagTrajeto, flagEscala, total;
    private Escala escala;
    private List<Rt_Hist> historico;
    private Rt_Hist historicoSelecionado;
    private final EGtvDao eGtvDao;
    private List<EGtv> guias;
    private Rt_Guias guia;
    private EGtv guiaSelecionada;
    private Calendar hora1, hora2, hora3, hora4, hora1O, hora1D;
    private LazyDataModel<Rotas> rotas = null;
    private List<Rotas> rotasDet;
    private Map filters, filtersGuia, tiposRelatorios;
    private MapModel posicaoRotas;
    private final PedidosSatMobWeb pedidosSatMobWeb;
    private EmailsEnviar email;
    private Double valor;
    private StreamedContent arquivoDownload;
    private Rastrear posicaoSelecionada, posicaoCarroRastreador, posicoesTrajetoDia;
    private List<CtrOperEquip> posicaoContainers;
    private CtrOperEquip posicaoContainer;
    private List<Rastrear> posicoesRotas, posicoesRastreador, posicoesRastreadorMapa;
    private Filiais filiais;
    private List<Rotas> rotasImportadas;
    private List<Rt_Perc> paradasImportadas;
    private List<CxForte> cxForteLista;

    private final Rt_GuiasFatDao rt_guiasFatDao;

    private TiposRelatorio relatorioSelecionado;

    private enum TiposRelatorio {
        RESUMO_ROTAS,
        RESUMO_ROTAS_PERIODO
    }

    public RotasEscalaMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        codNivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
        trajetos = new ArrayList<>();
        cxForteLista = new ArrayList<>();
        pedidosSatMobWeb = new PedidosSatMobWeb();
        dataTela = getDataAtual("SQL");
        dataModelo = dataTela;
        ultimoDiadoMes();
        loginSatMobWeb = new LoginSatMobWeb();
        rotassatweb = new RotasSatWeb();

        escala = new Escala();
        hora1 = Calendar.getInstance();
        hora2 = Calendar.getInstance();
        hora3 = Calendar.getInstance();
        hora4 = Calendar.getInstance();
        hora1O = Calendar.getInstance();
        hora1D = Calendar.getInstance();
        somenteAtivos = true;
        qtdePedidos = 0;
        tolerancia = 0;
        exclFlag = false;
        mostrarFiliais = false;
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
        rotasSPM = new RotasSPM();
        osSelecionada = new OS_Vig();
        apenasModelosAtivos = true;
        tipoModelo = "1";
        trajetoSugestaoRoteirizacao = null;
        Hora1Ref = "";
        Hora2Ref = "";

        rt_guiasFatDao = new Rt_GuiasFatDao();
        eGtvDao = new EGtvDao();
    }

    public void Persistencia(Persistencia pp, Persistencia ss) {
        try {
            this.persistencia = pp;
            this.satellite = ss;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            if (null == this.satellite) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            if (null != this.sequencia
                    && !this.sequencia.equals("")) {
                RotasSPM DadosRota = new RotasSPM();
                trajetos = DadosRota.listarTrajetos(this.sequencia, true, this.persistencia);
                Rt_Perc ItemDadosRota = trajetos.get(0);

                dataTela = ItemDadosRota.getData();
            }
            filters = new HashMap();
            filters.put(" Rotas.Flag_excl <> ? ", "*");
            filters.put(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", "*");
            filters.put(" Rotas.CodFil = ? ", codfil);
            filters.put(" CONVERT(BigInt, Rotas.Sequencia) = ? ", "");
            filters.put(" Rotas.Data = ? ", dataTela);
            filters.put(" CONVERT(BigInt, Rotas.Rota) = ? ", "");

            total = rotassatweb.contagemValores(filters, codpessoa, persistencia);
            valorTotal = rotassatweb.somaValoresSemCodPessoa(filters, persistencia);
            filiais = rotassatweb.buscaInfoFilial(codfil, persistencia);
            cxForteLista = this.pedidosSatMobWeb.listarCaixasForte(this.codfil, this.persistencia);
            rotasImportadas = new ArrayList<>();
            paradasImportadas = new ArrayList<>();
            listaRotaModelos = rotassatweb.listarModelosRota(codfil, !apenasModelosAtivos, persistencia);

            if (LocalTime.now().isBefore(LocalTime.NOON)) {
                horarioMapa = "manha";
            } else {
                horarioMapa = "tarde";
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void consultarPedidoBaseOrigem() throws Exception {
        this.trajetoSugestaoRoteirizacao = null;
        Hora1Ref = this.dadosPedido.getHora1O();
        Hora2Ref = this.dadosPedido.getHora2O();
        LatRef = this.dadosPedido.getCli1Lat();
        LonRef = this.dadosPedido.getCli1Lon();
        this.listaRoteirizacao = this.rotasSPM.listaRoteirizacao(this.dadosPedido.getData(), this.dadosPedido.getCodFil().toPlainString(), this.Hora1Ref, this.Hora2Ref, this.LatRef, this.LonRef, this.persistencia);
        tipoCarregamentoSugestao = "O";
        PrimeFaces.current().executeScript("$('#divPrincipal').css('display',''); $('#divEscolha').css('display','none')");
    }

    public void consultarPedidoBaseDestino() throws Exception {
        this.trajetoSugestaoRoteirizacao = null;
        Hora1Ref = this.dadosPedido.getHora1D();
        Hora2Ref = this.dadosPedido.getHora2D();
        LatRef = this.dadosPedido.getCli2Lat();
        LonRef = this.dadosPedido.getCli2Lon();
        this.listaRoteirizacao = this.rotasSPM.listaRoteirizacao(this.dadosPedido.getData(), this.dadosPedido.getCodFil().toPlainString(), this.Hora1Ref, this.Hora2Ref, this.LatRef, this.LonRef, this.persistencia);
        tipoCarregamentoSugestao = "D";
        PrimeFaces.current().executeScript("$('#divPrincipal').css('display',''); $('#divEscolha').css('display','none')");
    }

    public void consultarPedidoChange() throws Exception {
        if (this.tipoCarregamentoSugestao.equals("O")) {
            consultarPedidoBaseOrigem();
        } else {
            consultarPedidoBaseDestino();
        }
    }

    public void consultarPedido() throws Exception {
        this.trajetoSugestaoRoteirizacao = null;
        this.dadosPedido = this.rotasSPM.consultarPedidos(this.codfil, this.numeroPedido, this.persistencia);

        if (this.dadosPedido.getCodCli1().equals("9990001")) {
            Hora1Ref = this.dadosPedido.getHora1D();
            Hora2Ref = this.dadosPedido.getHora2D();
            LatRef = this.dadosPedido.getCli2Lat();
            LonRef = this.dadosPedido.getCli2Lon();
            this.listaRoteirizacao = this.rotasSPM.listaRoteirizacao(this.dadosPedido.getData(), this.dadosPedido.getCodFil().toPlainString(), this.Hora1Ref, this.Hora2Ref, this.LatRef, this.LonRef, this.persistencia);
            tipoCarregamentoSugestao = "D";
        } else if (!Hora1Ref.equals("")
                && !Hora2Ref.equals("")
                && !LatRef.equals("")
                && !LonRef.equals("")) {
            this.listaRoteirizacao = this.rotasSPM.listaRoteirizacao(this.dadosPedido.getData(), this.dadosPedido.getCodFil().toPlainString(), this.Hora1Ref, this.Hora2Ref, this.LatRef, this.LonRef, this.persistencia);
        } else {
            PrimeFaces.current().executeScript("$('#divPrincipal').css('display','none'); $('#divEscolha').css('display','')");
        }
    }

    public void carregarQtdePedidos() throws Exception {
        this.listaPedidos = this.rotasSPM.listarPedidos(this.codfil, this.dataTela, this.persistencia);
        this.qtdePedidos = this.listaPedidos.size();
    }

    public void onRowSelectRoteirizacao(SelectEvent event) throws Exception {
        this.markers = "";
        String origem = "", destino = "";

        // Sugestão de roteirização selecionada
        this.trajetoSelecionado = (Rt_Perc) event.getObject();

        // Consultar dados de trajeto da Rota escolhida
        this.trajetoSugestaoRoteirizacao = this.rotasSPM.mapaSugestaoRoteirizacao(this.trajetoSelecionado.getSequencia().toPlainString(), this.codfil, this.trajetoSelecionado.getHora1(), this.persistencia);

        // Dados de localização do Pedido
        this.trajetoSugestaoRoteirizacao.setLatitude(this.trajetoSelecionado.getLatitude());
        this.trajetoSugestaoRoteirizacao.setLongitude(this.trajetoSelecionado.getLongitude());
        this.trajetoSugestaoRoteirizacao.setHora1(this.trajetoSelecionado.getHora1());

        // Marcadores
        if (null != this.trajetoSugestaoRoteirizacao.getLatAnterior()
                && null != this.trajetoSugestaoRoteirizacao.getLonAnterior()
                && null != this.trajetoSugestaoRoteirizacao.getHora1ParadaAnterior()
                && !this.trajetoSugestaoRoteirizacao.getLatAnterior().equals("")
                && !this.trajetoSugestaoRoteirizacao.getLonAnterior().equals("")
                && !this.trajetoSugestaoRoteirizacao.getHora1ParadaAnterior().equals("")) {
            this.markers += MARCADOR.replace("@indice", "pontoAnterior")
                    .replace("@lat", this.trajetoSugestaoRoteirizacao.getLatAnterior()).replace("@lng", this.trajetoSugestaoRoteirizacao.getLonAnterior())
                    .replace("@title", getMessageS("Hora1") + ": " + this.trajetoSugestaoRoteirizacao.getHora1ParadaAnterior())
                    .replace("@icon", "https://mobile.sasw.com.br:9091/satmobile/pins/pin_vermelho_sombra.png");

            // Dados para Polyline
            origem = ORIGEM_MAPA.replace("@lat", this.trajetoSugestaoRoteirizacao.getLatAnterior()).replace("@lon", this.trajetoSugestaoRoteirizacao.getLonAnterior());
            destino = DESTINO_MAPA.replace("@lat", this.trajetoSugestaoRoteirizacao.getLatAtual()).replace("@lon", this.trajetoSugestaoRoteirizacao.getLonAtual());
            this.directionsTrajetoAtual = origem + destino;
        }

        this.markers += MARCADOR.replace("@indice", "pontoAtual")
                .replace("@lat", this.trajetoSugestaoRoteirizacao.getLatAtual()).replace("@lng", this.trajetoSugestaoRoteirizacao.getLonAtual())
                .replace("@title", getMessageS("Hora1") + ": " + this.trajetoSugestaoRoteirizacao.getHora1ParadaAtual())
                .replace("@icon", "https://mobile.sasw.com.br:9091/satmobile/pins/pin_azul_caminhao.png");

        if (null != this.trajetoSugestaoRoteirizacao.getLatSeguinte()
                && null != this.trajetoSugestaoRoteirizacao.getLonSeguinte()
                && null != this.trajetoSugestaoRoteirizacao.getHora1ParadaSeguinte()
                && !this.trajetoSugestaoRoteirizacao.getLatSeguinte().equals("")
                && !this.trajetoSugestaoRoteirizacao.getLonSeguinte().equals("")
                && !this.trajetoSugestaoRoteirizacao.getHora1ParadaSeguinte().equals("")) {
            this.markers += MARCADOR.replace("@indice", "pontoSeguinte")
                    .replace("@lat", this.trajetoSugestaoRoteirizacao.getLatSeguinte()).replace("@lng", this.trajetoSugestaoRoteirizacao.getLonSeguinte())
                    .replace("@title", getMessageS("Hora1") + ": " + this.trajetoSugestaoRoteirizacao.getHora1ParadaSeguinte())
                    .replace("@icon", "https://mobile.sasw.com.br:9091/satmobile/pins/pin_verde_sombra.png");

            /* Directions Trajeto Atual */
            origem = ORIGEM_MAPA.replace("@lat", this.trajetoSugestaoRoteirizacao.getLatAtual()).replace("@lon", this.trajetoSugestaoRoteirizacao.getLonAtual());
            destino = DESTINO_MAPA.replace("@lat", this.trajetoSugestaoRoteirizacao.getLatSeguinte()).replace("@lon", this.trajetoSugestaoRoteirizacao.getLonSeguinte());
            this.directionsTrajetoProximo = origem + destino;
        }

        this.markers += MARCADOR.replace("@indice", "pontoPedido")
                .replace("@lat", this.trajetoSugestaoRoteirizacao.getLatitude()).replace("@lng", this.trajetoSugestaoRoteirizacao.getLongitude())
                .replace("@title", getMessageS("Hora1") + ": " + this.trajetoSugestaoRoteirizacao.getHora1());

        if ((Integer.valueOf(this.trajetoSugestaoRoteirizacao.getHora1().substring(0, 2)) >= 7) && (Integer.valueOf(this.trajetoSugestaoRoteirizacao.getHora1().substring(0, 2)) < 12)) {
            // MANHÃ
            this.markers = this.markers.replace("@icon", PIN_PEDIDOS_MANHA);
            this.pinMapaPedido = PIN_PEDIDOS_MANHA;

        } else if ((Integer.valueOf(this.trajetoSugestaoRoteirizacao.getHora1().substring(0, 2)) >= 12)
                && (Integer.valueOf(this.trajetoSugestaoRoteirizacao.getHora1().substring(0, 2)) < 18)) {
            // TARDE
            this.markers = this.markers.replace("@icon", PIN_PEDIDOS_TARDE);
            this.pinMapaPedido = PIN_PEDIDOS_TARDE;
        } else {
            // NOITE
            this.markers = this.markers.replace("@icon", PIN_PEDIDOS_NOITE);
            this.pinMapaPedido = PIN_PEDIDOS_MANHA;
        }

        // Centralização do Mapa
        this.centro = "{ lat: " + this.trajetoSugestaoRoteirizacao.getLatitude() + ", lng: " + this.trajetoSugestaoRoteirizacao.getLongitude() + " }";
    }

    public void listarTrajetoExecucaoGuia() throws Exception {
        if (!this.sequencia.toString().equals("0")) {
            this.tipoMapa = "1";
            String origem, destino;
            StringBuilder marcadores = new StringBuilder();

            this.rotasSPM = new RotasSPM();
            this.posicaoCarroRastreador = this.rotasSPM.posicao(this.sequencia.toString(), this.codfil, persistencia);
            this.rota = this.posicaoCarroRastreador.getRota();

            // PIN do veículo
            String pin;
            if (this.posicaoCarroRastreador.getAtendimento().equals("0")) {
                pin = PIN_VEICULO_MOVIMENTO;
            } else {
                pin = PIN_VEICULO_PARADO;
            }

            // Marcador do Veículo
            marcadores.append(MARCADOR.replace("@indice", "Veiculo")
                    .replace("@lat", this.posicaoCarroRastreador.getLatitude()).replace("@lng", this.posicaoCarroRastreador.getLongitude())
                    .replace("@title", getMessageS("Veiculo") + ": " + this.posicaoCarroRastreador.getVeiculo().replace(".0", "")
                            + " - " + getMessageS("HorarioPosicao") + ": " + this.posicaoCarroRastreador.getHora())
                    .replace("@icon", pin));

            /* Tratamento de Polylines */
            this.filters.put(" Rt_Perc.Sequencia <> ? ", this.sequencia);
            this.posicoesTrajetoDia = this.rotasSPM.listarTrajetoDia(this.sequencia, getDataAtual("SQL"), this.persistencia);

            /* Directions Trajeto Atual */
            origem = ORIGEM_MAPA.replace("@lat", this.posicaoCarroRastreador.getLatitude()).replace("@lon", this.posicaoCarroRastreador.getLongitude());
            destino = DESTINO_MAPA.replace("@lat", posicoesTrajetoDia.getTrajetoDiaPontoProxLat()).replace("@lon", posicoesTrajetoDia.getTrajetoDiaPontoProxLon());
            this.directionsTrajetoProximo = origem + destino;

            // PIN destino
            marcadores.append(MARCADOR.replace("@indice", "Destino")
                    .replace("@lat", this.posicoesTrajetoDia.getTrajetoDiaPontoProxLat()).replace("@lng", this.posicoesTrajetoDia.getTrajetoDiaPontoProxLon())
                    .replace("@title", getMessageS("CliDst"))
                    .replace("@icon", PIN_BASE));

            this.markers = marcadores.toString();
            this.centro = "{ lat: " + this.posicaoCarroRastreador.getLatitude() + ", lng: " + this.posicaoCarroRastreador.getLongitude() + " }";
            this.chefeEquipe = this.posicaoCarroRastreador.getNome();
            this.ultimaPosicaoData = this.posicaoCarroRastreador.getData().toString();
            this.ultimaPosicao = this.posicaoCarroRastreador.getHora();
            PrimeFaces.current().executeScript("PF('dlgMapa').show(); PreReloadMap();");
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SequenciaInvalida"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void listaTrajetosModal() {
        try {
            this.rotasSPM = new RotasSPM();
            this.novaRota = this.rotasSPM.listagemValoresPaginadaModal(this.sequencia, this.persistencia);
            this.novaRota.setTrajetos(this.rotasSPM.listarTrajetos(this.novaRota.getSequencia(), this.exclFlag, this.persistencia));
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listarTrajetosClientes() {
        try {
            this.tipoMapa = "1";
            String origem = "", destino = "";

            /* INICIO - TRATAMENTO DE POLYLINES */
            this.posicoesTrajetoDia = this.rotasSPM.listarTrajetoDia(this.sequencia, this.dataTela, this.persistencia);
            if (posicoesTrajetoDia.getTrajetoDiaPontoProxLat() != null
                    && !posicoesTrajetoDia.getTrajetoDiaPontoProxLat().equals("")
                    && !posicoesTrajetoDia.getTrajetoDiaPontoProxLat().equals("null")
                    && posicoesTrajetoDia.getTrajetoDiaPontoAntLat() != null
                    && !posicoesTrajetoDia.getTrajetoDiaPontoAntLat().equals("")
                    && !posicoesTrajetoDia.getTrajetoDiaPontoAntLat().equals("null")
                    && this.dataTela.equals(getDataAtual("SQL-L"))) {
                /* Directions Trajeto Anterior */
                origem = ORIGEM_MAPA.replace("@lat", posicoesTrajetoDia.getTrajetoDiaPontoAntLat()).replace("@lon", posicoesTrajetoDia.getTrajetoDiaPontoAntLon());
                destino = DESTINO_MAPA.replace("@lat", posicoesTrajetoDia.getTrajetoDiaPontoUltComLat()).replace("@lon", posicoesTrajetoDia.getTrajetoDiaPontoUltComLon());
                this.directionsTrajetoAtual = origem + destino;

                /* Directions Trajeto Atual */
                origem = ORIGEM_MAPA.replace("@lat", posicoesTrajetoDia.getTrajetoDiaPontoUltComLat()).replace("@lon", posicoesTrajetoDia.getTrajetoDiaPontoUltComLon());
                destino = DESTINO_MAPA.replace("@lat", posicoesTrajetoDia.getTrajetoDiaPontoProxLat()).replace("@lon", posicoesTrajetoDia.getTrajetoDiaPontoProxLon());
                this.directionsTrajetoProximo = origem + destino;
            }
            /* FIM */

            // TRAJETOS JÁ EXECUTADOS
            this.posicoesClientes = this.rotasSPM.listarTrajetos(this.sequencia, false, this.persistencia);
            this.posicaoCarroRastreador = this.rotasSPM.posicao(this.sequencia.toString(), this.codfil, persistencia);
            this.rota = this.posicaoCarroRastreador.getRota();

            this.posicoesClientesMapa = new ArrayList<>();
            if (this.posicoesClientes.size() > 0) {
                if (this.posicoesClientes.size() < 25) {
                    this.posicoesClientesMapa.addAll(this.posicoesClientes);
                } else {
                    switch (this.horarioMapa) {
                        case "manha":
                            for (int i = 0; i < 24 && Integer.parseInt(this.posicoesClientes.get(i).getHora1().substring(0, 2)) < 13; i++) {
                                this.posicoesClientesMapa.add(this.posicoesClientes.get(i));
                            }
                            break;
                        case "tarde":
                            for (int i = 24; i > 0; i--) {
                                this.posicoesClientesMapa.add(this.posicoesClientes.get(this.posicoesClientes.size() - i));
                            }
                            break;
                    }
                }
            }

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void listarTrajetosClientesTodasRotas() {
        try {
            this.tipoMapa = "1";
            String origem = "", destino = "";

            // TRAJETOS JÁ EXECUTADOS
            this.posicoesClientes = this.rotasSPM.listarTrajetosTodasRotas(this.sequencia, false, this.persistencia);
            this.rota = this.posicaoCarroRastreador.getRota();

            this.posicoesClientesMapa = new ArrayList<>();
            if (this.posicoesClientes.size() > 0) {
                if (this.posicoesClientes.size() < 25) {
                    this.posicoesClientesMapa.addAll(this.posicoesClientes);
                } else {
                    switch (this.horarioMapa) {
                        case "manha":
                            for (int i = 0; i < 24 && Integer.parseInt(this.posicoesClientes.get(i).getHora1().substring(0, 2)) < 13; i++) {
                                this.posicoesClientesMapa.add(this.posicoesClientes.get(i));
                            }
                            break;
                        case "tarde":
                            for (int i = 24; i > 0; i--) {
                                this.posicoesClientesMapa.add(this.posicoesClientes.get(this.posicoesClientes.size() - i));
                            }
                            break;
                    }
                }
            }

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void dblSelectContainer(SelectEvent event) {
        this.containerSelecionado = (CxFGuiasVol) event.getObject();
        listarMovimentacaoContainers();
    }

    public void listarMovimentacaoContainers() {
        try {
            if (this.containerSelecionado == null) {
                throw new Exception(getMessageS("SelecioneContainer"));
            } else {
                this.historicoMovimentacao = this.rotassatweb.listarMovimentacaoContainer(this.containerSelecionado.getLacre(),
                        this.containerSelecionado.getCodFil(), this.dataContainer1, this.dataContainer2, this.persistencia);
                PrimeFaces.current().ajax().update("formContainers:tabelaMovimentacaoContainer");
                PrimeFaces.current().executeScript("PF('dlgMovimentacaoContainer').show();");
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void listarContainers() {
        try {
            this.containers = this.rotassatweb.listarContainers(this.codfil, this.persistencia);

            Calendar c = Calendar.getInstance();
            c.setTime(Date.from(Instant.now()));
            c.set(Calendar.DAY_OF_MONTH, 1);

            this.dataContainer1 = c.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
            this.dataContainer2 = c.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            PrimeFaces.current().ajax().update("formContainers");
            PrimeFaces.current().executeScript("PF('dlgContainers').show();");
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void listarParadasPreOrder(ActionEvent actionEvent) {
        try {
            if (null == this.rotaSelecionada) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneRota"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.trajetosSugeridos = this.rotassatweb.listarParadasPreOrder(this.rotaSelecionada.getSequencia().toString(), this.persistencia);
                if (this.trajetosSugeridos.isEmpty()) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("SemManifestos"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                } else {
                    PrimeFaces.current().resetInputs("paradasPreOrder");
                    PrimeFaces.current().executeScript("PF('dlgParadasPreOrder').show();");
                }
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void listarManifestoPreOrder() {
        try {
            this.manifesto = this.rotassatweb.listarManifestosPreOrder(this.rotaSelecionada.getSequencia().toString(),
                    this.trajetoSugeridoSelecionado.getParada(), this.persistencia);
            this.filial = this.loginSatMobWeb.BuscaFilial(this.rotaSelecionada.getCodFil().toPlainString(), this.codpessoa, this.persistencia);

            Filiais infoFilial = this.rotassatweb.buscaInfoFilial(this.rotaSelecionada.getCodFil().toString(), this.persistencia);

            this.relatorio = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/relatorio.html"));
            this.tabela = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/tabela.html"));
            this.linha = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/linha.html"));
            this.coluna = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/coluna.html"));
            this.span = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/span.html"));

            this.nomeArquivo = "manifesto" + this.rotaSelecionada.getData() + ".pdf";

            StringBuilder guiaImpressa = new StringBuilder();
            StringBuilder guiaImpressaAuxTextoTabela, guiaImpressaAuxTextoLinha, guiaImpressaAuxTextoColuna;

            guiaImpressaAuxTextoLinha = new StringBuilder();
            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoTabela = new StringBuilder();

            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "4").replace("@StyleTD", "")
                    .replace("@WidthTD", "").replace("@ClassTD", "")
                    .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.rotaSelecionada.getCodFil().toPlainString())
                            + "\" height=\"47px\" width=\"59px\"/>"));
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                    .replace("@WidthTD", "").replace("@ClassTD", "")
                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                            .replace("@TextoSpan", infoFilial.getRazaoSocial())));
            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                    .replace("@WidthTD", "").replace("@ClassTD", "")
                    .replace("@TextoTD", infoFilial.getEndereco()));
            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                    .replace("@TextoTD", infoFilial.getCidade() + "&nbsp;-&nbsp;"
                            + CEP(infoFilial.getCEP())));

            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                            + ": &nbsp;" + CNPJ(infoFilial.getCNPJ())
                            + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Telefone"))
                            + ": &nbsp;" + Fone(infoFilial.getFone())));
            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "4").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                    .replace("@TextoTD",
                            this.span.replace("@StyleSpan", "font-weight: bold; font-size: 18px;").replace("@ClassSpan", "")
                                    .replace("@TextoSpan", "M A N I F E S T O")));
            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoTabela.append(this.tabela
                    .replace("@StyleTabela", "border-collapse: collapse; text-align: left; background: #fff !important;"
                            + " overflow: hidden; width: 500px;  font-size: 16px; padding: 3px 3px; color: #000000; font-weight: normal;")
                    .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                    .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

//            guiaImpressa.append(guiaImpressaAuxTextoTabela);
            BigDecimal valorT = BigDecimal.ZERO;
            for (PreOrderManifesto preOrderManifesto : this.manifesto) {
                guiaImpressaAuxTextoLinha = new StringBuilder();

                guiaImpressaAuxTextoColuna = new StringBuilder();
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                .replace("@TextoSpan", "<hr>")));

                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                guiaImpressaAuxTextoColuna = new StringBuilder();
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", getMessageS("Destino") + ": "));
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                .replace("@TextoSpan", preOrderManifesto.getDestino())));

                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                guiaImpressaAuxTextoColuna = new StringBuilder();
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", getMessageS("Valor") + ": "));
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                .replace("@TextoSpan", Moeda(preOrderManifesto.getValor()))));

                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                guiaImpressaAuxTextoColuna = new StringBuilder();
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", getMessageS("Lacre") + ": "));
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                .replace("@TextoSpan", preOrderManifesto.getLacre())));

                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                guiaImpressaAuxTextoColuna = new StringBuilder();
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", getMessageS("Guia") + ": "));
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                .replace("@TextoSpan", preOrderManifesto.getGuia() + " - " + preOrderManifesto.getSerie())));

                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                guiaImpressaAuxTextoColuna = new StringBuilder();
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", getMessageS("AssinadoPor") + ": "));
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                .replace("@TextoSpan", preOrderManifesto.getCodPessoaAut() + " " + preOrderManifesto.getNome())));

                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                guiaImpressaAuxTextoColuna = new StringBuilder();
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", getMessageS("Rota") + ": "));
                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                        .replace("@StyleTD", "").replace("@WidthTD", "")
                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                .replace("@TextoSpan", preOrderManifesto.getRota())));

                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@IdTabela", "").replace("@ClassTabela", "")
                        .replace("@StyleTabela", " background: #fff !important; width: 500px;")
                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                valorT = valorT.add(new BigDecimal(preOrderManifesto.getValor()));
            }

            guiaImpressaAuxTextoLinha = new StringBuilder();

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "").replace("@WidthTD", "")
                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                            .replace("@TextoSpan", "<hr>")));

            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "").replace("@WidthTD", "")
                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold")
                            .replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Total").toUpperCase())));
            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center; font-size: 16px")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "").replace("@WidthTD", "")
                    .replace("@TextoTD", getMessageS("Quantidade") + ": "));
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "").replace("@WidthTD", "")
                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                            .replace("@TextoSpan", this.manifesto.size() + "")));

            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "").replace("@WidthTD", "")
                    .replace("@TextoTD", getMessageS("Valor") + ": "));
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "").replace("@WidthTD", "")
                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                            .replace("@TextoSpan", Moeda(valorT.toString()))));

            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoColuna = new StringBuilder();
            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                    .replace("@StyleTD", "").replace("@WidthTD", "")
                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                            .replace("@TextoSpan", Messages.getValorExtensoS(valorT.toString()))));

            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

            guiaImpressaAuxTextoTabela.append(this.tabela.replace("@IdTabela", "").replace("@ClassTabela", "")
                    .replace("@StyleTabela", " background: #fff !important; width: 500px;")
                    .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

            guiaImpressa.append(guiaImpressaAuxTextoTabela);

            this.html = guiaImpressa.toString();
            PrimeFaces.current().executeScript("PF('dlgImprimir').show();");
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void gerarGuiaDownload() throws Exception {
        InputStream stream = new ByteArrayInputStream(this.html.replace("https://mobile.sasw.com.br:9091", "http://localhost:9080").getBytes());

        ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
        ITextRenderer renderer = new ITextRenderer();
        Tidy tidy = new Tidy();
        tidy.setShowWarnings(false);
        Document doc = tidy.parseDOM(stream, null);
        renderer.setDocument(doc, null);
        renderer.layout();
        renderer.createPDF(osPdf);

        InputStream inputPDF = new ByteArrayInputStream(osPdf.toByteArray());

        this.arquivoDownload = new DefaultStreamedContent(inputPDF, "pdf", this.nomeArquivo);
        osPdf.close();
        stream.close();
        inputPDF.close();
    }

    public void buscarEscala() {
        try {
            if (null == this.rotaSelecionada) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneRota"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                //this.escala = this.rotassatweb.SelecionaEscala(banco, sequencia, persistencia)
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void mapasTrajeto() {
        if (null == this.rotaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneRota"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.exclFlag = false;
                this.novaRota = this.rotaSelecionada;
                this.posicaoRotas = new DefaultMapModel();
                LatLng pos;
                double lat, lon;
                List<Point.Double> coordenadas = new ArrayList<>();
                Point.Double xy;
                double centroLat = 0, centroLon = 0, cont = 0;
                this.novaRota.setTrajetos(this.rotassatweb.listarTrajetos(this.novaRota.getSequencia(), this.exclFlag, this.persistencia));
                for (Rt_Perc trajeto : this.novaRota.getTrajetos()) {
                    try {
                        lat = Double.parseDouble(trajeto.getLatitude());
                        lon = Double.parseDouble(trajeto.getLongitude());
                        pos = new LatLng(lat, lon);
                        if (lat != 0 && lon != 0) {
                            if (null == trajeto.getHrBaixa() || trajeto.getHrBaixa().equals("")) {
                                this.posicaoRotas.addOverlay(new Marker(pos, trajeto.getNRed() + " - " + trajeto.getHora1().substring(0, 2) + ":" + trajeto.getHora1().substring(2), trajeto, "https://mobile.sasw.com.br:9091/satmobile/img/pin_vermelho_sombra.png"));
                            } else {
                                this.posicaoRotas.addOverlay(new Marker(pos, trajeto.getNRed() + " - " + trajeto.getHora1().substring(0, 2) + ":" + trajeto.getHora1().substring(2), trajeto, "https://mobile.sasw.com.br:9091/satmobile/img/pin_verde_sombra.png"));
                            }
                            xy = new Point.Double(lat, lon);
                            coordenadas.add(xy);
                        }
                    } catch (Exception ex) {
                    }
                }
                for (Point.Double ponto : coordenadas) {
                    centroLat += ponto.getX();
                    centroLon += ponto.getY();
                }
                centroLat = centroLat / coordenadas.size();
                centroLon = centroLon / coordenadas.size();
                //this.centroMapa = centroLat+","+centroLon;
                Rastrear posicao = this.rotassatweb.posicao(this.novaRota.getSequencia().toPlainString(), this.codfil, this.persistencia);
                //this.trajetos = this.rotassatweb.listarTrajetos(this.novaRota.getSequencia(),this.exclFlag, this.persistencia);
                try {
                    lat = Double.parseDouble(posicao.getLatitude());
                    lon = Double.parseDouble(posicao.getLongitude());
                    pos = new LatLng(lat, lon);
                    this.posicaoRotas.addOverlay(new Marker(pos, Messages.getMessageS("Rota") + " " + posicao.getRota() + " - " + posicao.getHora(), posicao, "https://mobile.sasw.com.br:9091/satmobile/img/pin_azul_sombra.png"));
                    this.centroMapa = lat + "," + lon;
                } catch (Exception ex) {
                    for (Rt_Perc trajeto : this.novaRota.getTrajetos()) {
                        try {
                            lat = Double.parseDouble(trajeto.getLatitude());
                            lon = Double.parseDouble(trajeto.getLongitude());
                            this.centroMapa = lat + "," + lon;
                            break;
                        } catch (Exception ex2) {

                        }
                    }
                }
                PrimeFaces.current().executeScript("PF('dlgMapaTrajetos').show();");
            } catch (Exception e) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void mapaContainers2() {
        try {
            this.posicaoContainers = this.rotassatweb.listarPosicoesContainers(this.codfil, this.codNivel, this.codpessoa.toString(), this.persistencia);
            this.posicaoRotas = new DefaultMapModel();

            LatLng pos;
            double lat, lon;
            List<Point.Double> coordenadas = new ArrayList<>();
            Point.Double xy;
            double centroLat = 0, centroLon = 0;
            String icone = "";

            for (CtrOperEquip ctrOperEquip : this.posicaoContainers) {
                try {
                    LocalDate limite = LocalDate.now().minusDays(Integer.parseInt(ctrOperEquip.getLimite().replace(".0", ""))), dtUltMov;
                    LocalDate dataAtual = LocalDate.now();

                    lat = Double.parseDouble(ctrOperEquip.getLatitude());
                    lon = Double.parseDouble(ctrOperEquip.getLongitude());
                    pos = new LatLng(lat, lon);
                    if (lat != 0 && lon != 0) {
                        dtUltMov = LocalDate.parse(ctrOperEquip.getDtUltMov().split(" ")[0]);
                        if (Integer.parseInt(ctrOperEquip.getLimiteLocal().replace(".0", "")) == 999) {
                            if (Double.parseDouble(ctrOperEquip.getTempoDias()) >= 1 && Double.parseDouble(ctrOperEquip.getTempoDias()) <= 20) {
                                icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_branca" + ctrOperEquip.getTempoDias().replace(".0", "") + ".png";
                            } else {
                                if (Double.parseDouble(ctrOperEquip.getTempoDias()) > 0) {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_branca_maior20.png";
                                } else {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_branca.png";
                                }
                            }
                        } else if (limite.equals(dataAtual)) {
                            icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_amarela.png";
                        } else if (dtUltMov.isAfter(limite)) {
                            if (Double.parseDouble(ctrOperEquip.getTempoDias()) >= 1 && Double.parseDouble(ctrOperEquip.getTempoDias()) <= 15) {
                                icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_verde" + ctrOperEquip.getTempoDias().replace(".0", "") + ".png";
                            } else {
                                if (Double.parseDouble(ctrOperEquip.getTempoDias()) > 0) {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_verde_maior15.png";
                                } else {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_verde.png";
                                }
                            }
                        } else {
                            if (Double.parseDouble(ctrOperEquip.getTempoDias()) >= 6 && Double.parseDouble(ctrOperEquip.getTempoDias()) <= 20) {
                                icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_vermelha" + ctrOperEquip.getTempoDias().replace(".0", "") + ".png";
                            } else {
                                if (Double.parseDouble(ctrOperEquip.getTempoDias()) > 20) {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_vermelha_maior20.png";
                                } else {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_vermelha.png";
                                }
                            }
                        }

                        this.posicaoRotas.addOverlay(new Marker(pos, ctrOperEquip.getIDEquip() + " - " + ctrOperEquip.getNred() + "\n" + getMessageS("Cliente") + ": " + ctrOperEquip.getNredFat() + "\n" + getMessageS("ClienteServico") + ": " + ctrOperEquip.getNred() + "\n" + getMessageS("DataLocacao") + ": " + dtUltMov.format(DateTimeFormatter.ofPattern(Mascaras.getPadraoDataS())) + "\n" + getMessageS("TempoDias") + ": " + ctrOperEquip.getTempoDias() + "\n" + getMessageS("Bairro") + ": " + ctrOperEquip.getBairro() + "\n" + getMessageS("Solicitante") + ": " + ctrOperEquip.getSolicitante(),
                                ctrOperEquip, icone));

                        xy = new Point.Double(lat, lon);
                        coordenadas.add(xy);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            for (Point.Double ponto : coordenadas) {
                centroLat += ponto.getX();
                centroLon += ponto.getY();
            }
            centroLat = centroLat / coordenadas.size();
            centroLon = centroLon / coordenadas.size();
            this.centroMapa = centroLat + "," + centroLon;

//            PrimeFaces.current().ajax().update("main:painelCadastro");
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void mapaContainers() {
        try {

            this.posicaoContainers = this.rotassatweb.listarPosicoesContainers(this.codfil, this.codNivel, this.codpessoa.toString(), this.persistencia);
            this.posicaoRotas = new DefaultMapModel();

            LatLng pos;
            double lat, lon;
            List<Point.Double> coordenadas = new ArrayList<>();
            Point.Double xy;
            double centroLat = 0, centroLon = 0;
            String icone = "";

            for (CtrOperEquip ctrOperEquip : this.posicaoContainers) {
                try {
                    LocalDate limite = LocalDate.now().minusDays(Integer.parseInt(ctrOperEquip.getLimite().replace(".0", ""))), dtUltMov;
                    LocalDate dataAtual = LocalDate.now();

                    lat = Double.parseDouble(ctrOperEquip.getLatitude());
                    lon = Double.parseDouble(ctrOperEquip.getLongitude());
                    pos = new LatLng(lat, lon);
                    if (lat != 0 && lon != 0) {
                        dtUltMov = LocalDate.parse(ctrOperEquip.getDtUltMov().split(" ")[0]);
                        if (Integer.parseInt(ctrOperEquip.getLimiteLocal().replace(".0", "")) == 999) {
                            if (Double.parseDouble(ctrOperEquip.getTempoDias()) >= 1 && Double.parseDouble(ctrOperEquip.getTempoDias()) <= 20) {
                                icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_branca" + ctrOperEquip.getTempoDias().replace(".0", "") + ".png";
                            } else {
                                if (Double.parseDouble(ctrOperEquip.getTempoDias()) > 0) {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_branca_maior20.png";
                                } else {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_branca.png";
                                }
                            }
                        } else if (limite.equals(dataAtual)) {
                            icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_amarela.png";
                        } else if (dtUltMov.isAfter(limite)) {
                            if (Double.parseDouble(ctrOperEquip.getTempoDias()) >= 1 && Double.parseDouble(ctrOperEquip.getTempoDias()) <= 15) {
                                icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_verde" + ctrOperEquip.getTempoDias().replace(".0", "") + ".png";
                            } else {
                                if (Double.parseDouble(ctrOperEquip.getTempoDias()) > 0) {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_verde_maior15.png";
                                } else {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_verde.png";
                                }
                            }
                        } else {
                            if (Double.parseDouble(ctrOperEquip.getTempoDias()) >= 6 && Double.parseDouble(ctrOperEquip.getTempoDias()) <= 20) {
                                icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_vermelha" + ctrOperEquip.getTempoDias().replace(".0", "") + ".png";
                            } else {
                                if (Double.parseDouble(ctrOperEquip.getTempoDias()) > 20) {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_vermelha_maior20.png";
                                } else {
                                    icone = "https://mobile.sasw.com.br:9091/satmobile/img/icone_cacamba_vermelha.png";
                                }
                            }
                        }

                        this.posicaoRotas.addOverlay(new Marker(pos, ctrOperEquip.getIDEquip() + " - " + ctrOperEquip.getNred() + "\n" + Messages.getMessageS("Cliente") + ": " + ctrOperEquip.getNredFat() + "\nData de Locação: " + dtUltMov.format(DateTimeFormatter.ofPattern(Mascaras.getPadraoDataS())) + "\nTempo em Dias: " + ctrOperEquip.getTempoDias() + "\nBairro: " + ctrOperEquip.getBairro(),
                                ctrOperEquip, icone));

                        xy = new Point.Double(lat, lon);
                        coordenadas.add(xy);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            for (Point.Double ponto : coordenadas) {
                centroLat += ponto.getX();
                centroLon += ponto.getY();
            }
            centroLat = centroLat / coordenadas.size();
            centroLon = centroLon / coordenadas.size();
            this.centroMapa = centroLat + "," + centroLon;

            PrimeFaces.current().executeScript("PF('dlgMaps').show();");
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void selecionarPinoTrajeto(OverlaySelectEvent event) {
        this.trajetoSelecionado = (Rt_Perc) event.getOverlay().getData();
    }

    public void selecionarPinoRota(OverlaySelectEvent event) {
        this.posicaoSelecionada = (Rastrear) event.getOverlay().getData();
    }

    public void selecionarPinoConteiner(OverlaySelectEvent event) {
        this.posicaoContainer = (CtrOperEquip) event.getOverlay().getData();
    }

    public void MostrarFiliais() {
        if (mostrarFiliais) {
            filters.replace(" Rotas.CodFil = ? ", Arrays.asList());
        } else {
            filters.replace(" Rotas.CodFil = ? ", Arrays.asList(codfil));
        }
        getAllRotas();
    }

    public void verSomenteAtivos() {
        if (somenteAtivos) {
            filters.replace(" Rotas.Flag_excl <> ? ", Arrays.asList("*"));
            filters.replace(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", Arrays.asList("*"));
        } else {
            filters.replace(" Rotas.Flag_excl <> ? ", Arrays.asList());
            filters.replace(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", Arrays.asList());
        }
        getAllRotas();
    }

    public void verModelosAtivos() {
        try {
            listaRotaModelos = rotassatweb.listarModelosRota(codfil, !apenasModelosAtivos, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void LimparFiltros() {
        filters.replace(" Rotas.Flag_excl <> ? ", Arrays.asList("*"));
        filters.replace("(Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", Arrays.asList("*"));
        filters.replace(" Rotas.CodFil = ? ", Arrays.asList(codfil));
        filters.replace(" Rotas.Data = ? ", Arrays.asList(dataTela));
        setMostrarFiliais(false);
        setSomenteAtivos(true);
        getAllRotas();
    }

    public void SelecionarData(SelectEvent data) {
        dataTela = (String) data.getObject();
        filters.put(" Rotas.Data = ? ", Arrays.asList(dataTela));
        getAllRotas();
    }

    public void selecionarDataModelo(SelectEvent data) {
        dataModelo = (String) data.getObject();
    }

    public void selecionarDataModeloFinal(SelectEvent data) {
        dataModeloFinal = (String) data.getObject();
    }

    public void SelecionarDataMapa(SelectEvent data) {
        dataTela = (String) data.getObject();
        filters.put(" Rotas.Data = ? ", dataTela);
        sequencia = BigDecimal.ZERO;
        getAllRotas();
    }

    public void SelecionarDataMapaTodasRotas(SelectEvent data) throws Exception {
        this.dataTela = (String) data.getObject();
        this.filters.put(" Rotas.Data = ? ", this.dataTela);
    }

    /**
     * Tranforma Date em String
     *
     * @param date Data a ser formatada
     * @return String no formato yyyyMMdd
     */
    public String Date2String(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * Procura o último dia do mês atual
     */
    public static void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONTH) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            RotasEscalaMB.ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void novoTrajeto() throws Exception {
        if (null == this.filial) {
            this.filial = this.loginSatMobWeb.BuscaFilial(this.codfil, this.codpessoa, this.persistencia);
        }

        this.trajetoSelecionado = new Rt_Perc();
        this.trajetoSelecionado.setSequencia(this.novaRota.getSequencia().toString());
        this.trajetoSelecionado.setCodFil(this.novaRota.getCodFil().toPlainString());
        if (this.trajetos.isEmpty()) {
            this.trajetoSelecionado.setHora1(this.novaRota.getHrLargada());
        } else {
            this.trajetoSelecionado.setHora1("00:00");
        }
        this.trajetoSelecionado.setER("E");
        this.trajetoSelecionado.setOperador(RecortaAteEspaço(this.operador, 0, 10));
        this.trajetoSelecionado.setOperIncl(RecortaAteEspaço(this.operador, 0, 10));
        this.flagTrajeto = 1;

        this.cliOri = new Clientes();
        this.cliDst = new Clientes();
        this.listaClientes = new ArrayList<>();
        this.listaClientesDestino = new ArrayList<>();
        this.listaClientes.add(this.cliOri);
        this.listaClientes.add(this.cliDst);
        this.listaClientesDestino.add(this.cliOri);
        this.listaClientesDestino.add(this.cliDst);
        this.valor = new Double(0);

        PrimeFaces.current().ajax().update(new String[]{"main:tabela", "cadastroTrajeto"});
        PrimeFaces.current().executeScript("PF('dlgCadastrarTrajetos').show();");
    }

    public void cadastrar() {
        try {
            verificaHoraIntervalo();
            this.novaRota.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.novaRota.setHr_Alter(getDataAtual("HORA"));
            this.novaRota.setDt_Alter(LocalDate.now());
            this.novaRota.setData(removeMascaraData(this.novaRota.getData()));

            this.novaRota.setSequencia(String.valueOf(this.rotassatweb.criarRotaTransporte(this.novaRota, this.persistencia)));

            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);

            this.trajetos = new ArrayList<>();
            this.flag = 2;
            PrimeFaces.current().ajax().update(new String[]{"cadastroRota"});
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            novoTrajeto();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editar() {
        try {
            verificaHoraIntervalo();
            this.novaRota.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.novaRota.setHr_Alter(getDataAtual("HORA"));
            this.novaRota.setDt_Alter(LocalDate.now());
            this.novaRota.setData(removeMascaraData(this.novaRota.getData()));

            this.rotassatweb.editarRota(this.novaRota, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrarRotaModelo() {
        try {
            if (this.tipoModelo.equals("1")) {
                // ROTA POR MODELO
                if (null == rotaModeloSelecionada) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneModelo"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                    return;
                }

                if (rotassatweb.verificaRotaModelo(codfil, dataModelo, persistencia)) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("RotaJaGerada"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                    return;
                }

                rotassatweb.inserirRotaModelo(dataModelo, rotaModeloSelecionada.getModelo(), RecortaAteEspaço(operador, 0, 10),
                        LocalDate.now().toString(), getDataAtual("HORA"), persistencia);
            } else if (this.tipoModelo.equals("2")) {
                // MODELO POR ROTA
                if (null == this.nomeNovoModelo
                        || this.nomeNovoModelo.equals("")) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("InformeNomeModelo"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                    return;
                }

                if (!rotassatweb.validarModeloRotaExistente(dataModelo, codfil, persistencia)) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("RotaModeloInexistente"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                    return;
                }

                if (!rotassatweb.validarModeloExistente(this.nomeNovoModelo, codfil, persistencia)) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ModeloJaGerado"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                    return;
                }

                rotassatweb.inserirModeloRota(this.dataModelo, this.codfil, this.nomeNovoModelo, RecortaAteEspaço(operador, 0, 10),
                        LocalDate.now().toString(), getDataAtual("HORA"), persistencia);
            } else if (this.tipoModelo.equals("3")) {
                // MODELO POR ROTA
                rotassatweb.gerarPedidoUsandoFrequencia(this.dataModelo, this.codfil, RecortaAteEspaço(operador, 0, 10),
                        LocalDate.now().toString(), getDataAtual("HORA"), Integer.toString(tolerancia), this.persistencia);
            }

            listaRotaModelos = rotassatweb.listarModelosRota(codfil, !apenasModelosAtivos, persistencia);
            this.nomeNovoModelo = "";
            this.tolerancia = 0;

            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            getAllRotas();
            PrimeFaces.current().executeScript("PF('dialogCriarRotaModelo').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void gerarSaidasAutomaticas() {
        try {
            rotassatweb.gerarSaidasAutomaticas(this.dataTela, this.codfil, RecortaAteEspaço(this.operador, 0, 10),
                    getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void Excluir() {
        try {
            if (null == this.rotaSelecionada) {
                throw new Exception("SelecioneRota");
            }
            this.novaRota = this.rotaSelecionada;
            this.novaRota.setOperFech(RecortaAteEspaço(operador, 0, 10));
            this.novaRota.setDt_Fech(LocalDate.now());
            this.novaRota.setHr_Fech(getDataAtual("HORA"));
            this.rotassatweb.excluirRota(this.novaRota, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void prepararCadastro(ActionEvent event) {
        rotaSelecionada = new Rotas();
        novaRota = new Rotas();
        novaRota.setHrIntIni("00:00");
        novaRota.setHrIntFim("00:00");
        novaRota.setObservacao("");
        novaRota.setHrLargada("08:00");
        novaRota.setHrChegada("18:00");
        novaRota.setHsTotal("10.00");
        novaRota.setTpVeic("F");
        novaRota.setAeroporto("N");
        novaRota.setATM("N");
        novaRota.setBACEN("N");
        novaRota.setViagem("N");
        novaRota.setData(dataTela);

        flag = 1;
        try {
            filial = loginSatMobWeb.BuscaFilial(codfil, codpessoa, persistencia);
            novaRota.setCodFil(filial.getCodfilAc());
            novaRota.setRota(rotassatweb.proximaRotaTransporte(codfil, dataTela, persistencia));
            PrimeFaces.current().ajax().update("cadastroRota");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void SelecionarFilial(SelectEvent event) {
        try {
            this.filial = (SasPWFill) event.getObject();
            this.novaRota.setCodFil(this.filial.getCodfilAc());
            this.novaRota.setRota(this.rotassatweb.proximaRotaTransporte(this.novaRota.getCodFil().toString(), this.dataTela, this.persistencia));
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void RotaAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
            getAllRotas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void RotaPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
            getAllRotas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void MapaDiaAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.sequencia = BigDecimal.ZERO;
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
            getAllRotas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void MapaDiaPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
            this.sequencia = BigDecimal.ZERO;
            getAllRotas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void MapaDiaAnteriorTodasRotas() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void MapaDiaPosteriorTodasRotas() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private void mudarDia(int incremento) {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, incremento);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
//            this.sequencia = BigDecimal.ZERO;
            filters.put(" Rotas.Data = ? ", Arrays.asList(dataTela));
            getAllRotas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void voltarDia() {
        mudarDia(-1);
    }

    public void avancarDia() {
        mudarDia(1);
    }

    public void criarRotaPorModelo() {
        try {
            PrimeFaces.current().ajax().update("formRotasModelos");
            PrimeFaces.current().executeScript("PF('dialogCriarRotaModelo').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void onRowToggle(ToggleEvent event) {
        this.novaRota = (Rotas) event.getData();
        if (event.getVisibility() != Visibility.HIDDEN) {
            try {
                this.novaRota.setTrajetos(this.rotassatweb.listarTrajetosSemCoordenada(this.novaRota.getSequencia(), this.exclFlag, this.persistencia));
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void abrirEscala(ActionEvent actionEvent) {
        /*if (null == this.rotaSelecionada) {
            try {
                this.escala = new Escala();
                this.escala.setData(this.dataTela);
                this.flagEscala = 1;
                this.filial = this.loginSatMobWeb.BuscaFilial(this.codfil, this.codpessoa, this.persistencia);
                this.escala.setCodFil(this.filial.getCodfilAc().toString());
                this.parametros = this.rotassatweb.buscarParamet(this.escala.getCodFil().toString(), this.persistencia);
                this.veiculos = this.rotassatweb.listarVeiculos(this.escala.getCodFil().toString(), this.persistencia);
                this.veiculos.add(0, new Veiculos());
                this.veiculo = new Veiculos();

                this.rotasSelecao = this.rotassatweb.listarRotasData(this.escala.getCodFil().toString(),
                        this.escala.getData(), this.persistencia);
                this.rotasSelecao.add(0, new Rotas());
                this.rotaSelecionada = new Rotas();
                this.motorista = new Pessoa();
                this.chEquipe = new Pessoa();
                this.permissaoRota = false;
                this.vigilante1 = new Pessoa();
                this.vigilante2 = new Pessoa();
                this.vigilante3 = new Pessoa();

                this.infoChEquipe = false;

                PrimeFaces.current().resetInputs("cadastroEscala");
                PrimeFaces.current().executeScript("PF('dlgEscala').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        } else {*/

        if (null == rotaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneRota"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.escala = this.rotassatweb.selecionaEscala(this.rotaSelecionada.getSequencia(), this.persistencia);
                if (null != this.escala.getSeqRota() && this.escala.getSeqRota().compareTo(BigDecimal.ZERO) != 0) {
                    this.flagEscala = 2;
                    this.filial = this.loginSatMobWeb.BuscaFilial(this.rotaSelecionada.getCodFil().toString(), this.codpessoa, this.persistencia);
                    this.parametros = this.rotassatweb.buscarParamet(this.escala.getCodFil().toString(), this.persistencia);
                    this.veiculos = this.rotassatweb.listarVeiculos(this.escala.getCodFil().toString(), this.persistencia);
                    this.veiculos.add(0, new Veiculos());
                    this.rotasSelecao = this.rotassatweb.listarRotasData(this.filial.getCodfilAc(), this.dataTela, this.persistencia);
                    this.rotasSelecao.add(0, new Rotas());
                    this.motorista = this.rotassatweb.buscarMatricula(this.escala.getMatrMot().toBigInteger().toString(), this.persistencia);
                    this.chEquipe = this.rotassatweb.buscarMatricula(this.escala.getMatrChe().toBigInteger().toString(), this.persistencia);
                    this.permissaoRota = this.chEquipe != null
                            && this.rotassatweb.buscarPermissaoRotas(this.chEquipe.getCodigo(), this.persistencia);
                    this.vigilante1 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig1().toBigInteger().toString(), this.persistencia);
                    this.vigilante2 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig2().toBigInteger().toString(), this.persistencia);
                    this.vigilante3 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig3().toBigInteger().toString(), this.persistencia);
                    this.veiculo = this.rotassatweb.buscarVeiculo(this.escala.getVeiculo().intValue(), this.persistencia);

                    this.infoChEquipe = false;
                } else {
                    this.flagEscala = 1;
                    this.filial = this.loginSatMobWeb.BuscaFilial(this.rotaSelecionada.getCodFil().toString(), this.codpessoa, this.persistencia);
                    this.escala.setCodFil(this.filial.getCodfilAc().toString());
                    this.escala.setSeqRota(this.rotaSelecionada.getSequencia().toString());
                    this.escala.setRota(this.rotaSelecionada.getRota());
                    this.escala.setData(this.rotaSelecionada.getData().split(" ")[0].replace("-", ""));
                    this.escala.setHora1(this.rotaSelecionada.getHrLargada());
                    this.escala.setHora2(this.rotaSelecionada.getHrIntIni());
                    this.escala.setHora3(this.rotaSelecionada.getHrIntFim());
                    this.escala.setHora4(this.rotaSelecionada.getHrChegada());

                    this.parametros = this.rotassatweb.buscarParamet(this.escala.getCodFil().toString(), this.persistencia);
                    this.veiculos = this.rotassatweb.listarVeiculos(this.escala.getCodFil().toString(), this.persistencia);
                    this.veiculos.add(0, new Veiculos());
                    this.veiculo = new Veiculos();
                    this.rotasSelecao = this.rotassatweb.listarRotasData(this.filial.getCodfilAc().toString(), this.dataTela, this.persistencia);
                    this.rotasSelecao.add(0, new Rotas());
                    this.listaPessoa = new ArrayList<>();
                    this.motorista = new Pessoa();
                    this.chEquipe = new Pessoa();
                    this.permissaoRota = false;
                    this.vigilante1 = new Pessoa();
                    this.vigilante2 = new Pessoa();
                    this.vigilante3 = new Pessoa();

                    this.infoChEquipe = false;
                }
                PrimeFaces.current().resetInputs("cadastroEscala");
                PrimeFaces.current().executeScript("PF('dlgEscala').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void abrirEscalaModal() {
        try {
            this.filtersGuia = new HashMap<>();
            this.filtersGuia.put(" Rotas.Sequencia = ? ", this.sequencia.toPlainString());
            this.rotaSelecionada = this.rotasSPM.detalhesRota(this.filtersGuia, this.codpessoa, this.persistencia).get(0);
            this.escala = this.rotassatweb.selecionaEscala(this.sequencia, this.persistencia);

            if (null != this.escala.getSeqRota() && this.escala.getSeqRota().compareTo(BigDecimal.ZERO) != 0) {
                this.flagEscala = 2;
                this.filial = this.loginSatMobWeb.BuscaFilial(this.codfil, this.codpessoa, this.persistencia);
                this.parametros = this.rotassatweb.buscarParamet(this.escala.getCodFil().toString(), this.persistencia);
                this.veiculos = this.rotassatweb.listarVeiculos(this.escala.getCodFil().toString(), this.persistencia);
                this.veiculos.add(0, new Veiculos());
                this.rotasSelecao = this.rotassatweb.listarRotasData(this.filial.getCodfilAc(), this.dataTela, this.persistencia);
                this.rotasSelecao.add(0, new Rotas());
                this.motorista = this.rotassatweb.buscarMatricula(this.escala.getMatrMot().toBigInteger().toString(), this.persistencia);
                this.chEquipe = this.rotassatweb.buscarMatricula(this.escala.getMatrChe().toBigInteger().toString(), this.persistencia);
                this.permissaoRota = this.chEquipe != null
                        && this.rotassatweb.buscarPermissaoRotas(this.chEquipe.getCodigo(), this.persistencia);
                this.vigilante1 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig1().toBigInteger().toString(), this.persistencia);
                this.vigilante2 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig2().toBigInteger().toString(), this.persistencia);
                this.vigilante3 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig3().toBigInteger().toString(), this.persistencia);
                this.veiculo = this.rotassatweb.buscarVeiculo(this.escala.getVeiculo().intValue(), this.persistencia);

                this.infoChEquipe = false;
            } else {
                this.flagEscala = 1;
                this.filial = this.loginSatMobWeb.BuscaFilial(this.rotaSelecionada.getCodFil().toString(), this.codpessoa, this.persistencia);
                this.escala.setCodFil(this.filial.getCodfilAc().toString());
                this.escala.setSeqRota(this.rotaSelecionada.getSequencia().toString());
                this.escala.setRota(this.rotaSelecionada.getRota());
                this.escala.setData(this.rotaSelecionada.getData().split(" ")[0].replace("-", ""));
                this.escala.setHora1(this.rotaSelecionada.getHrLargada());
                this.escala.setHora2(this.rotaSelecionada.getHrIntIni());
                this.escala.setHora3(this.rotaSelecionada.getHrIntFim());
                this.escala.setHora4(this.rotaSelecionada.getHrChegada());

                this.parametros = this.rotassatweb.buscarParamet(this.escala.getCodFil().toString(), this.persistencia);
                this.veiculos = this.rotassatweb.listarVeiculos(this.escala.getCodFil().toString(), this.persistencia);
                this.veiculos.add(0, new Veiculos());
                this.veiculo = new Veiculos();
                this.rotasSelecao = this.rotassatweb.listarRotasData(this.filial.getCodfilAc().toString(), this.dataTela, this.persistencia);
                this.rotasSelecao.add(0, new Rotas());
                this.listaPessoa = new ArrayList<>();
                this.motorista = new Pessoa();
                this.chEquipe = new Pessoa();
                this.permissaoRota = false;
                this.vigilante1 = new Pessoa();
                this.vigilante2 = new Pessoa();
                this.vigilante3 = new Pessoa();

                this.infoChEquipe = false;
            }
            PrimeFaces.current().resetInputs("cadastroEscala");
            PrimeFaces.current().executeScript("PF('dlgEscala').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarFilialEscala(SelectEvent event) {
        try {
            this.filial = this.loginSatMobWeb.BuscaFilial(this.codfil, this.codpessoa, this.persistencia);
            this.escala.setCodFil(this.filial.getCodfilAc().toString());
            //this.escala.setCodFil(codFil);
            this.parametros = this.rotassatweb.buscarParamet(this.escala.getCodFil().toString(), this.persistencia);
            this.rotasSelecao = this.rotassatweb.listarRotasData(this.escala.getCodFil().toString(), this.dataTela, this.persistencia);
            this.rotasSelecao.add(0, new Rotas());
            this.rotaSelecionada = new Rotas();
            this.veiculo = new Veiculos();
            this.motorista = new Pessoa();
            this.chEquipe = new Pessoa();
            this.permissaoRota = false;
            this.vigilante1 = new Pessoa();
            this.vigilante2 = new Pessoa();
            this.vigilante3 = new Pessoa();

            this.infoChEquipe = false;

            PrimeFaces.current().resetInputs("cadastroEscala");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarRotaEscala(SelectEvent event) {
        try {
            if (null == this.rotaSelecionada.getSequencia() || this.rotaSelecionada.getSequencia().equals(BigDecimal.ZERO)) {
                this.escala = new Escala();
                this.escala.setData(this.dataTela);
                this.motorista = new Pessoa();
                this.chEquipe = new Pessoa();
                this.permissaoRota = false;
                this.vigilante1 = new Pessoa();
                this.vigilante2 = new Pessoa();
                this.vigilante3 = new Pessoa();
                this.veiculo = new Veiculos();

                this.infoChEquipe = false;
                throw new Exception("SelecioneRotaValida");
            } else {
                this.escala = this.rotassatweb.selecionaEscala(this.rotaSelecionada.getSequencia(), this.persistencia);
                if (null == this.escala.getSeqRota() || this.escala.getSeqRota().compareTo(BigDecimal.ZERO) == 0) {
                    this.flagEscala = 1;
                    this.escala.setSeqRota(this.rotaSelecionada.getSequencia().toString());
                    this.escala.setCodFil(this.rotaSelecionada.getCodFil().toString());
                    this.escala.setRota(this.rotaSelecionada.getRota());
                    this.escala.setData(this.rotaSelecionada.getData().split(" ")[0].replace("-", ""));
                    this.escala.setHora1(this.rotaSelecionada.getHrLargada());
                    this.escala.setHora2(this.rotaSelecionada.getHrIntIni());
                    this.escala.setHora3(this.rotaSelecionada.getHrIntFim());
                    this.escala.setHora4(this.rotaSelecionada.getHrChegada());
                } else {
                    this.flagEscala = 2;
                }

                this.veiculo = this.rotassatweb.buscarVeiculo(this.escala.getVeiculo().intValue(), this.persistencia);
                this.motorista = this.rotassatweb.buscarMatricula(this.escala.getMatrMot().toBigInteger().toString(), this.persistencia);
                this.chEquipe = this.rotassatweb.buscarMatricula(this.escala.getMatrChe().toBigInteger().toString(), this.persistencia);
                this.permissaoRota = this.chEquipe != null
                        && this.rotassatweb.buscarPermissaoRotas(this.chEquipe.getCodigo(), this.persistencia);
                this.vigilante1 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig1().toBigInteger().toString(), this.persistencia);
                this.vigilante2 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig2().toBigInteger().toString(), this.persistencia);
                this.vigilante3 = this.rotassatweb.buscarMatricula(this.escala.getMatrVig3().toBigInteger().toString(), this.persistencia);
                this.veiculo = this.rotassatweb.buscarVeiculo(this.escala.getVeiculo().intValue(), this.persistencia);

                this.infoChEquipe = false;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void novaListaPessoas() {
        this.listaPessoa = new ArrayList<>();
        if (null != this.motorista) {
            this.listaPessoa.add(this.motorista);
        }
        if (null != this.chEquipe) {
            this.listaPessoa.add(this.chEquipe);
        }
        if (null != this.vigilante1) {
            this.listaPessoa.add(this.vigilante1);
        }
        if (null != this.vigilante2) {
            this.listaPessoa.add(this.vigilante2);
        }
        if (null != this.vigilante3) {
            this.listaPessoa.add(this.vigilante3);
        }
    }

    public void selecionarMotorista(SelectEvent event) {
        try {
            novaListaPessoas();
            this.motorista = (Pessoa) event.getObject();
            if (this.motorista.getFuncao().equals("M") || this.motorista.getFuncao().equals("T")) {
                if (this.rotassatweb.existeEscala(this.escala.getCodFil().toString(),
                        this.escala.getData(),
                        this.motorista.getMatr().toString(),
                        this.escala.getSeqRota().toBigInteger().toString(), this.persistencia)) {
                    this.motorista = new Pessoa();
                    throw new Exception("PessoaJaEscalada");
                }
                if (!validarEscalaFuncionario(this.motorista.getCodigo())) {
                    throw new Exception("PessoaJaEscalada");
                }
                if (null == this.veiculo
                        || null == this.veiculo.getMatr_Mot()
                        || this.veiculo.getMatr_Mot().compareTo(BigDecimal.ZERO) == 0
                        || this.veiculo.getMatr_Mot().compareTo(this.motorista.getMatr()) == 0) {
                    this.msgVeiculo = Messages.getMessageS("MotoristaDiferenteVeiculo");
                } else {
                    this.msgVeiculo = "";
                }

                this.escala.setMatrMot(this.motorista.getMatr().toString());
                this.escala.setHrMot(LocalTime.parse(this.escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                        .minusMinutes(this.parametros.getEscTolerMot()).toString());
            } else {
                this.motorista = new Pessoa();
                throw new Exception("PessoaNaoMotorista");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarChEquipe(SelectEvent event) {
        try {
            novaListaPessoas();
            this.chEquipe = (Pessoa) event.getObject();
            if (this.chEquipe.getFuncao().equals("C") || this.chEquipe.getFuncao().equals("T")) {
                if (this.rotassatweb.existeEscala(this.escala.getCodFil().toString(),
                        this.escala.getData(),
                        this.chEquipe.getMatr().toString(),
                        this.escala.getSeqRota().toBigInteger().toString(), this.persistencia)) {
                    this.chEquipe = new Pessoa();
                    throw new Exception("PessoaJaEscalada");
                }
                if (!validarEscalaFuncionario(this.chEquipe.getCodigo())) {
                    throw new Exception("PessoaJaEscalada");
                }
                this.permissaoRota = this.rotassatweb.buscarPermissaoRotas(this.chEquipe.getCodigo(), this.persistencia);
                this.escala.setMatrChe(this.chEquipe.getMatr().toString());
                this.escala.setHrChe(LocalTime.parse(this.escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                        .minusMinutes(this.parametros.getEscTolerChe()).toString());
            } else {
                this.chEquipe = new Pessoa();
                throw new Exception("PessoaNaoChEquipe");
            }
            this.listaPessoa = new ArrayList<>();
            if (null != this.motorista) {
                this.listaPessoa.add(this.motorista);
            }
            if (null != this.chEquipe) {
                this.listaPessoa.add(this.chEquipe);
            }
            if (null != this.vigilante1) {
                this.listaPessoa.add(this.vigilante1);
            }
            if (null != this.vigilante2) {
                this.listaPessoa.add(this.vigilante2);
            }
            if (null != this.vigilante3) {
                this.listaPessoa.add(this.vigilante3);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarVigilante1(SelectEvent event) {
        try {
            novaListaPessoas();
            this.vigilante1 = (Pessoa) event.getObject();
            if (!validarEscalaFuncionario(this.vigilante1.getCodigo())) {
                throw new Exception("PessoaJaEscalada");
            }
            this.escala.setMatrVig1(this.vigilante1.getMatr().toString());
            this.escala.setHrVig1(LocalTime.parse(this.escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                    .minusMinutes(this.parametros.getEscTolerVig()).toString());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarVigilante2(SelectEvent event) {
        try {
            novaListaPessoas();
            this.vigilante2 = (Pessoa) event.getObject();
            if (!validarEscalaFuncionario(this.vigilante2.getCodigo())) {
                throw new Exception("PessoaJaEscalada");
            }
            this.escala.setMatrVig2(this.vigilante2.getMatr().toString());
            this.escala.setHrVig2(LocalTime.parse(this.escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                    .minusMinutes(this.parametros.getEscTolerVig()).toString());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarVigilante3(SelectEvent event) {
        try {
            novaListaPessoas();
            this.vigilante3 = (Pessoa) event.getObject();
            if (!validarEscalaFuncionario(this.vigilante3.getCodigo())) {
                throw new Exception("PessoaJaEscalada");
            }
            this.escala.setMatrVig3(this.vigilante3.getMatr().toString());
            this.escala.setHrVig3(LocalTime.parse(this.escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                    .minusMinutes(this.parametros.getEscTolerVig()).toString());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarVeiculo(SelectEvent event) {
        try {
            //this.veiculo = (Veiculos) event.getObject();
            if (null == this.motorista
                    || null == this.motorista.getMatr()
                    || this.motorista.getMatr().compareTo(BigDecimal.ZERO) == 0
                    || this.veiculo.getMatr_Mot().compareTo(this.motorista.getMatr()) == 0) {
                this.msgVeiculo = Messages.getMessageS("MotoristaDiferenteVeiculo");
            } else {
                this.msgVeiculo = "";
            }
            if (null != this.veiculo) {
                Escala escalaVeiculo = this.rotassatweb.existeEscalaVeiculo(this.escala.getData(),
                        String.valueOf(this.veiculo.getNumero()), this.persistencia);

                if (escalaVeiculo != null
                        && LocalTime.parse(escalaVeiculo.getHora4(), DateTimeFormatter.ofPattern("HH:mm"))
                                .isAfter(LocalTime.parse(this.escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm")))
                        && escalaVeiculo.getSeqRota().compareTo(this.escala.getSeqRota()) != 0) {
                    throw new Exception("VeiculoJaEscalado");

                }

                this.escala.setVeiculo(String.valueOf(this.veiculo.getNumero()));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrarEscala(boolean prosseguir) {
        try {
            this.matrs = new ArrayList<>();
            if (!this.escala.getMatrMot().equals(BigDecimal.ZERO)) {
                this.matrs.add(this.escala.getMatrMot().toBigInteger().toString());
            }
            this.folgas = this.rotassatweb.funcionariosFolga(this.matrs, this.escala.getData(), this.persistencia);
            if (!this.folgas.isEmpty() && !prosseguir) {
                PrimeFaces.current().ajax().update("panelFuncionarioFolga");
                PrimeFaces.current().executeScript("PF('dlgFuncionarioFolga').show();");
            } else {
                // validar data
//                if (!this.escala.getData().equals(getDataAtual("SQL"))) {
//                    PrimeFaces.current().executeScript("PF('dlgEscala').hide();");
//                    throw new Exception("DataAnteriorEdicaoBloqueada");
//                }

                if (null == this.rotaSelecionada.getSequencia() || this.rotaSelecionada.getSequencia().equals(BigDecimal.ZERO)) {
                    throw new Exception("SelecioneRotaValida");
                }

                if (null != this.motorista && !validarEscalaFuncionario(this.motorista.getCodigo())) {
                    throw new Exception("MotoristaJaEscalado");
                }
                if (null != this.chEquipe && !validarEscalaFuncionario(this.chEquipe.getCodigo())) {
                    throw new Exception("ChEquipeJaEscalado");
                }
                if (null != this.vigilante1 && !validarEscalaFuncionario(this.vigilante1.getCodigo())) {
                    throw new Exception("Vigilante1JaEscalado");
                }
                if (null != this.vigilante2 && !validarEscalaFuncionario(this.vigilante2.getCodigo())) {
                    throw new Exception("Vigilante2JaEscalado");
                }
                if (null != this.vigilante3 && !validarEscalaFuncionario(this.vigilante3.getCodigo())) {
                    throw new Exception("Vigilante3JaEscalado");
                }

                if (null != this.motorista && null != this.motorista.getCodigo() && !this.motorista.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == this.escala.getHrMot() || this.escala.getHrMot().equals("")) {
                        throw new Exception("HrMotInvalida");
                    }
                    if (this.motorista.getFuncao().equals("M") || this.motorista.getFuncao().equals("T")) {
                        if (this.rotassatweb.existeEscala(this.escala.getCodFil().toString(),
                                this.escala.getData(),
                                this.motorista.getMatr().toString(),
                                this.escala.getSeqRota().toBigInteger().toString(), this.persistencia)) {
                            this.motorista = new Pessoa();
                            throw new Exception("MotoristaJaEscalado");
                        }
                        this.escala.setMatrMot(this.motorista.getMatr().toString());
                    } else {
                        this.motorista = new Pessoa();
                        throw new Exception("PessoaNaoMotorista");
                    }
                }

                if (null != this.chEquipe && null != this.chEquipe.getCodigo() && !this.chEquipe.getCodigo().equals(BigDecimal.ZERO)) {
                    if (this.chEquipe.getFuncao().equals("C") || this.motorista.getFuncao().equals("T")) {
                        if (this.rotassatweb.existeEscala(this.escala.getCodFil().toString(),
                                this.escala.getData(),
                                this.chEquipe.getMatr().toString(),
                                this.escala.getSeqRota().toBigInteger().toString(), this.persistencia)) {
                            this.chEquipe = new Pessoa();
                            throw new Exception("ChEquipeJaEscalado");
                        }
                        this.permissaoRota = this.rotassatweb.buscarPermissaoRotas(this.chEquipe.getCodigo(), this.persistencia);
                        this.escala.setMatrChe(this.chEquipe.getMatr().toString());
                    } else {
                        this.chEquipe = new Pessoa();
                        throw new Exception("PessoaNaoChEquipe");
                    }
                }

                if (null != this.veiculo && this.veiculo.getNumero() != 0) {
                    Escala escalaVeiculo = this.rotassatweb.existeEscalaVeiculo(this.escala.getData(),
                            String.valueOf(this.veiculo.getNumero()), this.persistencia);

                    if (escalaVeiculo != null
                            && LocalTime.parse(escalaVeiculo.getHora4(), DateTimeFormatter.ofPattern("HH:mm"))
                                    .isAfter(LocalTime.parse(this.escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm")))) {
                        if (escalaVeiculo.getSeqRota().compareTo(this.escala.getSeqRota()) != 0) {
                            throw new Exception("VeiculoJaEscalado");
                        }
                    }
                }

                verificaHoraIntervaloEscala();
                this.escala.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.rotassatweb.inserirEscala(this.escala, this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgEscala').hide();");
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editarEscala(boolean prosseguir) {
        try {
            this.matrs = new ArrayList<>();
            if (!this.escala.getMatrMot().equals(BigDecimal.ZERO)) {
                this.matrs.add(this.escala.getMatrMot().toBigInteger().toString());
            }
            this.folgas = this.rotassatweb.funcionariosFolga(this.matrs, this.escala.getData(), this.persistencia);
            if (!this.folgas.isEmpty() && !prosseguir) {
                PrimeFaces.current().ajax().update("panelFuncionarioFolga");
                PrimeFaces.current().executeScript("PF('dlgFuncionarioFolga').show();");
            } else {
                // validar data
                if (!this.escala.getData().equals(getDataAtual("SQL"))) {
                    PrimeFaces.current().executeScript("PF('dlgEscala').hide();");
                    throw new Exception("DataAnteriorEdicaoBloqueada");
                }

                if (null == this.rotaSelecionada.getSequencia() || this.rotaSelecionada.getSequencia().equals(BigDecimal.ZERO)) {
                    throw new Exception("SelecioneRotaValida");
                }

                if (this.motorista != null && !validarEscalaFuncionario(this.motorista.getCodigo())) {
                    throw new Exception("MotoristaJaEscalado");
                }
                if (this.chEquipe != null && !validarEscalaFuncionario(this.chEquipe.getCodigo())) {
                    throw new Exception("ChEquipeJaEscalado");
                }
                if (this.vigilante1 != null && !validarEscalaFuncionario(this.vigilante1.getCodigo())) {
                    throw new Exception("Vigilante1JaEscalado");
                }
                if (this.vigilante2 != null && !validarEscalaFuncionario(this.vigilante2.getCodigo())) {
                    throw new Exception("Vigilante2JaEscalado");
                }
                if (this.vigilante3 != null && !validarEscalaFuncionario(this.vigilante3.getCodigo())) {
                    throw new Exception("Vigilante3JaEscalado");
                }

                if (null != this.motorista && null != this.motorista.getCodigo() && !this.motorista.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == this.escala.getHrMot() || this.escala.getHrMot().equals("")) {
                        throw new Exception("HrMotInvalida");
                    }
                    if (this.motorista.getFuncao().equals("M") || this.motorista.getFuncao().equals("T")) {
                        if (this.rotassatweb.existeEscala(this.escala.getCodFil().toString(),
                                this.escala.getData(),
                                this.motorista.getMatr().toString(),
                                this.escala.getSeqRota().toBigInteger().toString(), this.persistencia)) {
                            this.motorista = new Pessoa();
                            throw new Exception("MotoristaJaEscalado");
                        }
                        this.escala.setMatrMot(this.motorista.getMatr().toString());
                    } else {
                        this.motorista = new Pessoa();
                        throw new Exception("PessoaNaoMotorista");
                    }
                }

                if (null != this.chEquipe && null != this.chEquipe.getCodigo() && !this.chEquipe.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == this.escala.getHrChe() || this.escala.getHrChe().equals("")) {
                        throw new Exception("HrCheInvalida");
                    }
                    if (this.chEquipe.getFuncao().equals("C") || this.motorista.getFuncao().equals("T")) {
                        if (this.rotassatweb.existeEscala(this.escala.getCodFil().toString(),
                                this.escala.getData(),
                                this.chEquipe.getMatr().toString(),
                                this.escala.getSeqRota().toBigInteger().toString(), this.persistencia)) {
                            this.chEquipe = new Pessoa();
                            throw new Exception("ChEquipeJaEscalado");
                        }
                        this.permissaoRota = this.rotassatweb.buscarPermissaoRotas(this.chEquipe.getCodigo(), this.persistencia);
                        this.escala.setMatrChe(this.chEquipe.getMatr().toString());
                    } else {
                        this.chEquipe = new Pessoa();
                        throw new Exception("PessoaNaoChEquipe");
                    }
                }

                if (null != this.vigilante1 && null != this.vigilante1.getCodigo() && !this.vigilante1.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == this.escala.getHrVig1() || this.escala.getHrVig1().equals("")) {
                        throw new Exception("HrVig1Invalida");
                    }
                }

                if (null != this.vigilante2 && null != this.vigilante2.getCodigo() && !this.vigilante2.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == this.escala.getHrVig2() || this.escala.getHrVig2().equals("")) {
                        throw new Exception("HrVig2Invalida");
                    }
                }

                if (null != this.vigilante3 && null != this.vigilante3.getCodigo() && !this.vigilante3.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == this.escala.getHrVig3() || this.escala.getHrVig3().equals("")) {
                        throw new Exception("HrVig3Invalida");
                    }
                }

                if (null != this.veiculo && this.veiculo.getNumero() != 0) {
                    Escala escalaVeiculo = this.rotassatweb.existeEscalaVeiculo(this.escala.getData(),
                            String.valueOf(this.veiculo.getNumero()), this.persistencia);

                    if (escalaVeiculo != null
                            && LocalTime.parse(escalaVeiculo.getHora4(), DateTimeFormatter.ofPattern("HH:mm"))
                                    .isAfter(LocalTime.parse(this.escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm")))) {
                        if (escalaVeiculo.getSeqRota().compareTo(this.escala.getSeqRota()) != 0) {
                            throw new Exception("VeiculoJaEscalado");
                        }
                    }
                }

                verificaHoraIntervaloEscala();
                this.escala.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.rotassatweb.atualizarEscala(this.escala, this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgEscala').hide();");
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public boolean validarEscalaFuncionario(BigDecimal codigo) {
        int cont = 0;
        if (null != this.motorista
                && null != this.motorista.getCodigo()
                && !this.motorista.getCodigo().equals(BigDecimal.ZERO)
                && this.motorista.getCodigo().equals(codigo)) {
            cont++;
        }
        if (null != this.chEquipe
                && null != this.chEquipe.getCodigo()
                && !this.chEquipe.getCodigo().equals(BigDecimal.ZERO)
                && this.chEquipe.getCodigo().equals(codigo)) {
            cont++;
        }
        if (null != this.vigilante1
                && null != this.vigilante1.getCodigo()
                && !this.vigilante1.getCodigo().equals(BigDecimal.ZERO)
                && this.vigilante1.getCodigo().equals(codigo)) {
            cont++;
        }
        if (null != this.vigilante2
                && null != this.vigilante2.getCodigo()
                && !this.vigilante2.getCodigo().equals(BigDecimal.ZERO)
                && this.vigilante2.getCodigo().equals(codigo)) {
            cont++;
        }
        if (null != this.vigilante3
                && null != this.vigilante3.getCodigo()
                && !this.vigilante3.getCodigo().equals(BigDecimal.ZERO)
                && this.vigilante3.getCodigo().equals(codigo)) {
            cont++;
        }
        return cont <= 1;
    }

    public void onRowSelect(SelectEvent event) {
        this.rotaSelecionada = (Rotas) event.getObject();
        buttonAction(null);
    }

    public void onModeloRowSelect(SelectEvent event) {
        rotaModeloSelecionada = (Rt_Modelo) event.getObject();
    }

    public void buttonAction(ActionEvent actionEvent) {
        if (null == rotaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneRota"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                //Rotas, Escala e Rt_Escala
                novaRota = rotaSelecionada;
                filial = loginSatMobWeb.BuscaFilial(novaRota.getCodFil().toString(), codpessoa, persistencia);
                dataRota = novaRota.getData();
                exclFlag = !novaRota.getFlag_Excl().isEmpty();

                trajetos = rotassatweb.listarTrajetosSemCoordenada(novaRota.getSequencia(), exclFlag, persistencia);

                flag = 2;
                PrimeFaces.current().resetInputs("cadastroRota");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                logerro.Grava(log, caminho);
            }
        }
    }

    public void buttonActionExclusao(ActionEvent actionEvent) {
        if (null == rotaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("SelecioneRota"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {

                if (dataTela.equals(getDataAtual("SQL"))) {
                    Rotas rotaExclusao = new Rotas();
                    rotaExclusao.setSequencia(rotaSelecionada.getSequencia().toPlainString().replace(".0", ""));
                    rotaExclusao.setOperador(RecortaAteEspaço(this.operador, 0, 10));
                    rotaExclusao.setHr_Alter(getDataAtual("HORA"));
                    rotaExclusao.setDt_Alter(LocalDate.now());

                    RotasDao rotasDao = new RotasDao();
                    rotasDao.excluirCompleto(rotaExclusao, persistencia);

                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExcluidoSucesso"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                } else {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("ExclusaoRotaDia"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }

            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                logerro.Grava(log, caminho);
            }
        }
    }

    public void listaTrajetos() {
        try {
            trajetos = rotassatweb.listarTrajetos(novaRota.getSequencia(), exclFlag, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void listaTrajetosSemCoordenada() {
        try {
            trajetos = rotassatweb.listarTrajetosSemCoordenada(novaRota.getSequencia(), exclFlag, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void cadastrarTrajeto() {
        try {
            if (!consularOSexistente()) {
                return;
            }

            if (null == this.trajetoSelecionado.getCodCli1() || this.trajetoSelecionado.getCodCli1().equals("")) {
                throw new Exception(Messages.getMessageS("SelecioneCliOri"));
            }
            if (this.trajetoSelecionado.getER().equals("R") || this.trajetoSelecionado.getER().equals("ER")) {
                if (null == this.trajetoSelecionado.getCodCli2() || this.trajetoSelecionado.getCodCli2().equals("")) {
                    throw new Exception(Messages.getMessageS("SelecioneCliDst"));
                }
            }

            try {
                validarHorarioTrajeto(false);
            } catch (ParseException e) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("HoraInvalida"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
                return;
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
                return;
            }

            if (this.trajetoSelecionado.getER().equals("E")
                    && (null == this.trajetoSelecionado.getHora1D()
                    || this.trajetoSelecionado.getHora1D().equals(""))) {
                this.trajetoSelecionado.setHora1D(this.trajetoSelecionado.getHora1());
            }

            BigDecimal valorDst = BigDecimal.ZERO;
            boolean aproveitamentoRota = false, paradaDestino = false;
            // Verifica se já não existe serviço nesse horário.
            this.trajetoValidacao = this.rotassatweb.validarHorario(this.trajetoSelecionado.getSequencia().toString(),
                    this.trajetoSelecionado.getHora1(), this.persistencia);

            if (this.trajetoValidacao != null) {
                if (this.trajetoValidacao.getCodCli1().equals(this.trajetoSelecionado.getCodCli1())) {
                    valorDst = new BigDecimal(this.trajetoValidacao.getValor());
                    aproveitamentoRota = true;
                } else {
                    throw new Exception(Messages.getMessageS("ExisteTrajetoMesmoHorario")
                            + ": " + this.trajetoValidacao.getNRed() + ",\n "
                            + Messages.getMessageS("Parada") + ": " + this.trajetoValidacao.getParada());
                }
            }

            if (this.trajetoSelecionado.getER().equals("R") || this.trajetoSelecionado.getER().equals("ER")) {
                paradaDestino = true;
                this.trajetoValidacao = this.rotassatweb.validarHorario(this.trajetoSelecionado.getSequencia().toString(),
                        this.trajetoSelecionado.getHora1D(), this.persistencia);
                if (this.trajetoValidacao != null) {
                    if (!this.trajetoValidacao.getCodCli1().equals(this.trajetoSelecionado.getCodCli2())) {
                        throw new Exception(Messages.getMessageS("ExisteTrajetoMesmoHorarioEntrega")
                                + ": " + this.trajetoValidacao.getNRed() + ",\n "
                                + Messages.getMessageS("Parada") + ": " + this.trajetoValidacao.getParada());
                    } else {
                        this.trajetoSelecionado.setDPar(this.trajetoValidacao.getParada());
                        paradaDestino = false;
                    }
                }
            }

            validarHorarioTrajeto(this.trajetoSelecionado.getER().equals("R") || this.trajetoSelecionado.getER().equals("ER"));

            this.trajetoSelecionado.setValor(this.valor.toString());
            this.trajetoSelecionado.setOperFech("");
            this.trajetoSelecionado.setHrBaixa("");
            this.trajetoSelecionado.setHr_Alter(getDataAtual("HORA"));
            this.trajetoSelecionado.setFlag_Excl("");
            this.trajetoSelecionado.setObserv(RecortaAteEspaço(this.trajetoSelecionado.getObserv(), 0, 20));
            int parada = this.rotassatweb.criarTrajetos(this.trajetoSelecionado, this.persistencia);
            this.trajetoSelecionado.setParada(parada);
            if ((this.trajetoSelecionado.getER().equals("R") || this.trajetoSelecionado.getER().equals("ER")) && paradaDestino) {
//            insere trajeto com destino
                String cxForte = this.rotassatweb.cxForte(this.trajetoSelecionado.getCodFil(), this.persistencia);
                try {
                    if (cxForte.equals(this.trajetoSelecionado.getCodCli1())) {
                        valorDst = new BigDecimal(this.trajetoSelecionado.getValor());
                    }
                } catch (Exception e) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("NaoExisteCaixaForte"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                }
                this.trajetoValidacao = new Rt_Perc();
                this.trajetoValidacao.setSequencia(this.trajetoSelecionado.getSequencia().toString());
                this.trajetoValidacao.setHora1(this.trajetoSelecionado.getHora1D());
                this.trajetoValidacao.setParada(this.trajetoSelecionado.getDPar());
                this.trajetoValidacao.setCodFil(this.trajetoSelecionado.getCodFil().toString());
                this.trajetoValidacao.setER("E");
                this.trajetoValidacao.setTipoSrv(this.trajetoSelecionado.getTipoSrv());
                this.trajetoValidacao.setCodCli1(this.trajetoSelecionado.getCodCli2());
                this.trajetoValidacao.setNRed(this.trajetoSelecionado.getNRedDst());
                this.trajetoValidacao.setRegiao(this.trajetoSelecionado.getRegiaoDst());
                this.trajetoValidacao.setValor(valorDst.toString());
                this.trajetoValidacao.setObserv(RecortaAteEspaço(this.trajetoSelecionado.getObserv(), 0, 20));
                this.trajetoValidacao.setOperIncl(RecortaAteEspaço(this.operador, 0, 10));
                this.trajetoValidacao.setOperador(RecortaAteEspaço(this.operador, 0, 10));
                this.trajetoValidacao.setFlag_Excl("");
                this.trajetoValidacao.setOperFech("");
                this.trajetoValidacao.setHrBaixa("");
                int dpar = this.rotassatweb.criarTrajetos(this.trajetoValidacao, this.persistencia);
                this.trajetoSelecionado.setDPar(dpar);
            }

            if (aproveitamentoRota) {
//            update no valor da parada
                this.rotassatweb.atualizarValorParada(this.trajetoSelecionado, valorDst, this.persistencia);
            } else {
//            update com um monte de info
                this.rotassatweb.atualizarTrajeto(this.trajetoSelecionado, this.persistencia);

                FacesContext fc = FacesContext.getCurrentInstance();
                if ((Boolean) fc.getExternalContext().getSessionMap().get("transpCacamba")) {
                    // Se for empresa de caçamba, verifica se existe para sem OS e preenche - Correção para SATTAGUA
                    Rt_PercDao rtPercDao = new Rt_PercDao();
                    rtPercDao.atualizarOSEmpresaCacamba(this.trajetoSelecionado, this.persistencia);
                }
            }

            listaTrajetosSemCoordenada();
            PrimeFaces.current().ajax().update(new String[]{"cadastroRota"});
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            novoTrajeto();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void excluirTrajeto() {
        try {
            if (null == this.trajetoSelecionado) {
                throw new Exception("SelecioneTrajeto");
            }
            if (this.trajetoSelecionado.getFlag_Excl().equals("*")) {
                throw new Exception("trajetoExcluido");
            }
            this.trajetoSelecionado.setOperExcl(RecortaAteEspaço(this.operador, 0, 10));
            this.trajetoSelecionado.setHr_Excl(getDataAtual("HORA"));
            this.trajetoSelecionado.setDt_Excl(LocalDate.now());
            this.rotassatweb.FlagExcl(this.trajetoSelecionado, this.persistencia);

            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    //Realiza o tramento para autenticacao
    private String tratarAutenticacao(String cnpj, String guia, String serie, String seqRota, String parada) {
        StringBuilder autTemp = new StringBuilder();
        autTemp = autTemp.append(cnpj.replace(".", "").replace("-", "").replace("/", ""))
                .append(PreencheEsquerda(guia, 14, "0"))
                .append(PreencheEsquerda(serie, 4, "0"))
                .append(PreencheEsquerda(seqRota, 8, "0"))
                .append(PreencheEsquerda(parada, 4, "0"));

        StringBuilder sb = new StringBuilder();

        float i = 0;
        for (char s : autTemp.toString().toCharArray()) {
            if (i % 4 == 0 && i != 0 && i != 24) {
                sb.append(' ');
            }
            if (i % 24 == 0 && i != 0) {
                sb.append("</br>");
            }
            sb = sb.append(s);

            i++;
        }
        return sb.toString();
    }

    public void imprimir() {
        if (null == this.guiaSelecionada) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneGuia"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.relatorio = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/relatorio.html"));
                this.tabela = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/tabela.html"));
                this.linha = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/linha.html"));
                this.coluna = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/coluna.html"));
                this.span = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/span.html"));

                if (this.persistencia.getEmpresa().equals("SATMAXIMA")) {
                    this.guiaSelecionada.setGuia(PreencheEsquerda(this.guiaSelecionada.getSequencia().replace(".0", ""), 8, "0")
                            + " " + PreencheEsquerda(this.guiaSelecionada.getParada().replace(".0", ""), 3, "0"));

                    this.guia = new Rt_Guias();
                    this.guia.setGuia(this.guiaSelecionada.getGuia());
                    this.guia.setCodBarras(PreencheEsquerda(this.guiaSelecionada.getSequencia().replace(".0", ""), 8, "0")
                            + " " + PreencheEsquerda(this.guiaSelecionada.getParada().replace(".0", ""), 3, "0"));
                } else {
                    this.guia = this.rotassatweb.obterInfoGuia(this.guiaSelecionada, this.persistencia);

                    this.guia.setAssRemetente(this.guiaSelecionada.getAssinatura());
                    this.guia.setAssDestinatario(this.guiaSelecionada.getAssinaturaDestino());
                    this.guia.setCodBarras(PreencheEsquerda(this.guia.getGuia().toBigInteger().toString(), 8, "0") + " " + this.guia.getSerie());
                    this.guia.setValorExtenso(Messages.getValorExtensoS(this.guia.getValor().toPlainString()));
                    this.guia.setAutenticacao(tratarAutenticacao(this.guia.getCnpjFilial(), this.guia.getGuia().toBigInteger().toString(),
                            this.guia.getSerie(), this.guia.getSequencia().toBigInteger().toString(), String.valueOf(this.guia.getParada())));
                    this.guia.setLacres(this.rotassatweb.listarLacres(this.guia.getGuia().toBigInteger().toString(), this.guia.getSerie(), this.persistencia));
                }

                this.nomeArquivo = this.guiaSelecionada.getGuia().replace(".0", "") + ".pdf";

                StringBuilder guiaImpressa = new StringBuilder();
                StringBuilder guiaImpressaAuxTextoTabela, guiaImpressaAuxTextoLinha, guiaImpressaAuxTextoColuna;

                guiaImpressaAuxTextoTabela = new StringBuilder();

                Code128Bean bean = new Code128Bean();
                bean.setHeight(10d);
                bean.setModuleWidth(0.35);
                bean.doQuietZone(false);
                BitmapCanvasProvider provider = new BitmapCanvasProvider(110, BufferedImage.TYPE_BYTE_GRAY, false, 0);
                bean.generateBarcode(provider, this.guia.getCodBarras());
                provider.finish();
                BufferedImage imagem = provider.getBufferedImage();
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(imagem, "png", os);

                String codigoBarras = new BASE64Encoder().encode(os.toByteArray());
                FacesContext fc = FacesContext.getCurrentInstance();
                String NomeRef = !(Boolean) fc.getExternalContext().getSessionMap().get("transpCacamba") ? this.persistencia.getEmpresa() : "CACAMBA";

                switch (NomeRef) {
                    case "SATMAXIMA":
                        //cabecalho
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "4").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.guiaSelecionada.getCodFil())
                                        + "\" height=\"47px\" width=\"59px\"/>"));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                        .replace("@ClassSpan", "").replace("@TextoSpan", this.guiaSelecionada.getRazaoSocial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guiaSelecionada.getEnderecoFilial() + "," + this.guiaSelecionada.getBairroFilial()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guiaSelecionada.getCidadeFilial() + "/" + this.guiaSelecionada.getUfFilial() + "&nbsp;-&nbsp;"
                                        + CEP(this.guiaSelecionada.getCepFilial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;")
                                        .replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                        + ": &nbsp;" + CNPJ(this.guiaSelecionada.getCnpjFilial())
                                        + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("Telefone"))
                                        + ": &nbsp;" + Fone(this.guiaSelecionada.getFoneFilial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 500px !important; "
                                        + "background: #fff !important; overflow: hidden; width: 335px;"
                                        + "font-size: 16px; padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //filialOS
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                        .replace("@ClassSpan", "").replace("@TextoSpan", "TERMO DE ENTREGA DE REFEIÇÃO")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "width: 100%;")
                                .replace("@WidthTD", "100%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Filial"))
                                        + ": &nbsp;" + PreencheEsquerda(this.guiaSelecionada.getCodFil().replace(".0", ""), 4, "0")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "2").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<img src=\"data:image/png;base64," + codigoBarras + "\" width=\"250\" height=\"60\""
                                        + "alt=\"embedded folder icon\" style=\"min-width:250px\">"));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("OS"))
                                        + ": &nbsp;" + this.guiaSelecionada.getOS().replace(".0", "")));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%; "
                                        + "background: #fff !important; overflow: hidden; width: 335px; "
                                        + " font-size: 16px; padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                .replace("@IdTabela", "filialOS").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //cliente
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("ClienteP") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "").replace("@TextoSpan", this.guiaSelecionada.getnRedFat())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "italic").replace("@TextoSpan", getMessageS("Endereco"))
                                        + ": &nbsp;" + this.guiaSelecionada.getEndFat()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guiaSelecionada.getBairroFat() + ", " + this.guiaSelecionada.getCidadeFat() + "/" + this.guiaSelecionada.getEstadoFat()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                        + ": &nbsp;" + CNPJ(this.guiaSelecionada.getCgcFat())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("InscEstadual"))
                                        + ": &nbsp;" + this.guiaSelecionada.getIeFat()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;border-right: 1px solid black;"
                                        + "border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "cliente").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //dadosDestino
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("Entrega") + ": ")
                                        + "&nbsp;" + Data(this.guiaSelecionada.getDtEntrega())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                        + "&nbsp;" + this.guiaSelecionada.getVeiculoDst().replace(".0", "")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("Rota") + ": ")
                                        + "&nbsp;" + this.guiaSelecionada.getRota().replace(".0", "")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                        + Hora(this.guiaSelecionada.getHrCheg_E())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Said") + ": ")
                                        + Hora(this.guiaSelecionada.getHrCheg_S())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;"
                                        + "border-right: 1px solid black; border-left: 1px solid black; border-bottom: 1px solid black; font-size: 14px;")
                                .replace("@IdTabela", "dadosDestino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        PedidoRefeicaoItensDao pedidoRefeicaoItensDao = new PedidoRefeicaoItensDao();
                        PedidoRefeicaoItens itens = pedidoRefeicaoItensDao.listarItensPedido(this.guiaSelecionada.getSequencia(),
                                this.guiaSelecionada.getParada(), this.guiaSelecionada.getCodFil(), this.persistencia);
                        BigInteger total = BigInteger.ZERO;

                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        if (!itens.getQtdeCafe().equals("0")) {
                            total = total.add(new BigInteger(itens.getQtdeCafe()));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Turno de serviço:\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", "Café")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Quantidade:\t\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", itens.getQtdeCafe())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "</br>")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        }

                        if (!itens.getQtdeAlmoco().equals("0")) {
                            total = total.add(new BigInteger(itens.getQtdeAlmoco()));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Turno de serviço:\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", "Almoço")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Quantidade:\t\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", itens.getQtdeAlmoco())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "</br>")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        }

                        if (!itens.getQtdeJantar().equals("0")) {
                            total = total.add(new BigInteger(itens.getQtdeJantar()));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Turno de serviço:\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", "Jantar")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Quantidade:\t\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", itens.getQtdeJantar())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "</br>")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        }

                        if (!itens.getQtdeCeia().equals("0")) {
                            total = total.add(new BigInteger(itens.getQtdeCeia()));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Turno de serviço:\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", "Ceia")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Quantidade:\t\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", itens.getQtdeCeia())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "</br>")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        }

                        if (!total.equals(BigInteger.ZERO)) {
                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", " ")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "Quantidade Total:\t")
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", total.toString())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        }

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;"
                                        + "border-right: 1px solid black; border-left: 1px solid black; border-bottom: 1px solid black; font-size: 14px;")
                                .replace("@IdTabela", "dadosDestino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        // Solicitante
                        guiaImpressaAuxTextoLinha = new StringBuilder();

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-right: 1px solid black; border-bottom: 1px solid black; vertical-align: baseline;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("Solicitante") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "vertical-align: baseline;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<br>"));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "vertical-align: baseline;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guiaSelecionada.getOrigem()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "vertical-align: baseline;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<br>"));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;"
                                        + "border-right: 1px solid black; border-left: 1px solid black; font-size: 14px;")
                                .replace("@IdTabela", "dadosDestino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        // Assinatura
                        guiaImpressaAuxTextoLinha = new StringBuilder();

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; vertical-align: baseline;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("Assinatura") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "vertical-align: baseline;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<br>"));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "vertical-align: baseline;text-align: center;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD",
                                        "<img src=\"https://mobile.sasw.com.br:9091/satellite/assinaturas/" + this.persistencia.getEmpresa()
                                        + "/" + this.guiaSelecionada.getSequencia().replace(".0", "") + "/" + this.guiaSelecionada.getParada()
                                        + ".png\" height=\"47px\" width=\"59px\" alt=\"" + this.guiaSelecionada.getAssinaturaDestino() + "\"/>"));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;"
                                        + "border-right: 1px solid black; border-left: 1px solid black; border-bottom: 1px solid black; font-size: 14px;")
                                .replace("@IdTabela", "dadosDestino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));
                        break;
                    case "CACAMBA":
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "5").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.guia.getCodFil().toPlainString())
                                        + "\" height=\"47px\" width=\"59px\"/>"));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", "ECO VISÃO")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getRazaoSocial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getEnderecoFilial() + "," + this.guia.getBairroFilial()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getCidadeFilial() + "/" + this.guia.getUfFilial() + "&nbsp;-&nbsp;"
                                        + CEP(this.guia.getCepFilial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                        + ": &nbsp;" + CNPJ(this.guia.getCnpjFilial())
                                        + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Telefone"))
                                        + ": &nbsp;" + Fone(this.guia.getFoneFilial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; "
                                + "text-align: left; width: 100%; background: #fff; overflow: hidden; width: 335px;  font-size: 16px; "
                                + "padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //filialOS
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        if (this.guia.getEr().equals("R")) {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "TERMO DE COLETA DE CONTAINER")));
                        } else {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "TERMO DE ENTREGA DE CONTAINER")));
                        }
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "width: 100%;")
                                .replace("@WidthTD", "100%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Filial"))
                                        + ": &nbsp;" + PreencheEsquerda(this.guia.getCodFil().toString().replace(".0", ""), 4, "0")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "2").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<img src=\"data:image/png;base64," + codigoBarras + "\" width=\"250\" height=\"60\" alt=\"embedded folder icon\">"));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("OS"))
                                        + ": &nbsp;" + this.guia.getOS().toString().replace(".0", "")));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left;"
                                + " width: 100%; background: #fff; overflow: hidden; width: 335px;  font-size: 16px; padding: 3px 3px; "
                                + "color: #000000; font-size: 16px; font-weight: normal;")
                                .replace("@IdTabela", "filialOS").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //origem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("DadosCliente"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "border-top: 1px solid black; border-bottom: 1px solid black;")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        if (this.guia.getEr().equals("R")) {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center;border-top: 1px solid black;")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", this.guia.getNomeOri())));
                        } else {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center;border-top: 1px solid black;")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", this.guia.getNomeDst())));
                        }
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        if (this.guia.getEr().equals("R")) {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center;")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Remetente") + ": ")
                                            + " " + this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", this.guia.getnRedOri())));
                        } else {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center;")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Remetente") + ": ")
                                            + " " + this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", this.guia.getnRedDst())));
                        }
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        if (this.guia.getEr().equals("R")) {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Registro") + ": ")
                                            + " "
                                            + (this.guia.getRegistroOri().length() == 14 ? Mascaras.CNPJ(this.guia.getRegistroOri())
                                            : (this.guia.getRegistroOri().length() == 11 ? Mascaras.CPF(this.guia.getRegistroOri()) : this.guia.getRegistroOri()))));
                        } else {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Registro") + ": ")
                                            + " "
                                            + (this.guia.getRegistroDst().length() == 14 ? Mascaras.CNPJ(this.guia.getRegistroDst())
                                            : (this.guia.getRegistroDst().length() == 11 ? Mascaras.CPF(this.guia.getRegistroDst()) : this.guia.getRegistroDst()))));
                        }
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        if (this.guia.getEr().equals("R")) {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": ")
                                            + " " + this.guia.getEndOri()));
                        } else {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": ")
                                            + " " + this.guia.getEndDst()));
                        }
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 335px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "origem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //enderecoOrigem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        if (this.guia.getEr().equals("R")) {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.guia.getBairroOri() + ", " + this.guia.getCidadeOri() + "/" + this.guia.getEstadoOri()));
                        } else {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.guia.getBairroDst() + ", " + this.guia.getCidadeDst() + "/" + this.guia.getEstadoDst()));
                        }
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 335px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "enderecoOrigem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //dadosOrigem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        if (this.guia.getEr().equals("R")) {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Coleta") + ": ")
                                            + "&nbsp;"
                                            + Data(this.guia.getColetaOri())));
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").
                                    replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                            + "&nbsp;"
                                            + this.guia.getVeiculoOri().replace(".0", "")));
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Rota") + ": ")
                                            + "&nbsp;"
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                                    .replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getRotaOri().replace(".0", ""))));
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                            + Hora(this.guia.getHora1())));
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Said") + ": ")
                                            + Hora(this.guia.getHora2())));
                        } else {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Entrega") + ": ")
                                            + "&nbsp;"
                                            + Data(this.guia.getColetaDst())));
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").
                                    replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                            + "&nbsp;"
                                            + this.guia.getVeiculoDst().replace(".0", "")));
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Rota") + ": ")
                                            + "&nbsp;"
                                            + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                                    .replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getRotaDst().replace(".0", ""))));
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                            + Hora(this.guia.getHoraChegada())));
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Said") + ": ")
                                            + Hora(this.guia.getHoraSaida())));
                        }
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 335px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "dadosOrigem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //detalhes
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("IdentificacaoContainer"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan",
                                                (this.guia.getLacres().size() == 1 ? getMessageS("Container") : getMessageS("Containers"))
                                                + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        for (CxFGuiasVol lacre : this.guia.getLacres()) {
                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", lacre.getLacre())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        }

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: justify; padding-right: 4px !important;border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", "Ao receber este documento, concordo com os seguintes termos:"
                                                + "</br>"
                                                + "</br> * É necessário que seja feita a reserva da vaga para colocação da caçamba no local desejado. O espaço a ser reservado equivale a vaga de um carro."
                                                + "</br> * Não é permitido a colocação de caçambas em pontos de ônibus, táxi, Pne ou cobrindo bueiros. "
                                                + "</br> * Não é permitido a mudança de local da caçamba por parte do contratante."
                                                + "</br> * Não é permitido colocar resíduo hospitalar, químico, orgânico, industrial e lixo doméstico dentro da caçamba."
                                                + "</br> * Não é permitido colocar fogo no conteúdo da caçamba. "
                                                + "</br> * É proibido colocar entulho ultrapassando a borda da caçamba."
                                                + "</br> * Fica o locatário responsável pela caçamba recebida, durante o período de permanência da mesma no local entregue."                                                
                                                + "</br> * Não ultrapasse o limite da borda do container. Evite ser multado.")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; border-right: 1px solid black ;vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("AssRemetente") + ": ")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("AssDestinatario") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-right: 1px solid black;vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getAssRemetente().replace("Ch.Eq: ", "Motorista: ")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "vertical-align: baseline;text-align: center;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD",
                                        "<img src=\"https://mobile.sasw.com.br:9091/satellite/assinaturas/" + this.persistencia.getEmpresa()
                                        + "/" + this.guia.getSequencia().toBigInteger().toString() + "/" + this.guia.getParada()
                                        + ".png\" height=\"47px\" width=\"59px\" alt=\"" + this.guia.getAssDestinatario().replace("Ch.Eq: ", "Motorista: ") + "\"/>"));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        // Forma de pagamento
                        Rt_GuiasFat rt_GuiasFat = this.rt_guiasFatDao.obterRt_GuiasFatGuia(this.guia.getGuia().toString(), this.guia.getSerie(), this.persistencia);
                        if (rt_GuiasFat != null) {
                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "border-top: 1px solid black;")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("Valor")
                                                    + ": ")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", Moeda(rt_GuiasFat.getValorTot()))));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "border-bottom: 1px solid black;")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", Messages.getValorExtensoS(rt_GuiasFat.getValorTot()))));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "border-top: 1px solid black;")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", getMessageS("FormaPagamento")
                                                    + ": ")));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "border-bottom: 1px solid black;")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", rt_GuiasFat.getFormaPgtoDescricao())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        }

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("Autenticacao"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", this.guia.getAutenticacao()) + "&nbsp;"));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; border-bottom: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Obs") + ": ")
                                        + "&nbsp;"
                                        + this.guia.getObserv()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; "
                                + "width: 100%;background: #fff;overflow: hidden; width: 335px;border-right: 1px solid black;"
                                + "border-left: 1px solid black;font-size: 14px; margin-bottom: 10px;")
                                .replace("@IdTabela", "detalhes").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));
                        break;
                    default:
                        //cabecalho
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "4").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.guia.getCodFil().toPlainString())
                                        + "\" height=\"47px\" width=\"59px\"/>"));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                        .replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getRazaoSocial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getEnderecoFilial() + "," + this.guia.getBairroFilial()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getCidadeFilial() + "/" + this.guia.getUfFilial() + "&nbsp;-&nbsp;"
                                        + CEP(this.guia.getCepFilial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;")
                                        .replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                        + ": &nbsp;" + CNPJ(this.guia.getCnpjFilial())
                                        + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("Telefone"))
                                        + ": &nbsp;" + Fone(this.guia.getFoneFilial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 500px !important; "
                                        + "background: #fff !important; overflow: hidden; width: 335px;"
                                        + "font-size: 16px; padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //filialOS
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("GETV"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "width: 100%;")
                                .replace("@WidthTD", "100%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Filial"))
                                        + ": &nbsp;" + PreencheEsquerda(this.guia.getCodFil().toString().replace(".0", ""), 4, "0")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "2").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<img src=\"data:image/png;base64," + codigoBarras + "\" width=\"250\" height=\"60\" alt=\"embedded folder icon\">"));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("OS"))
                                        + ": &nbsp;" + this.guia.getOS().toString().replace(".0", "")));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%; "
                                        + "background: #fff !important; overflow: hidden; width: 335px; "
                                        + " font-size: 16px; padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                .replace("@IdTabela", "filialOS").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //cliente
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("ClienteP") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getnRedFat())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "italic").replace("@TextoSpan", getMessageS("Endereco"))
                                        + ": &nbsp;" + this.guia.getEndFat()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getBairroFat() + ", " + this.guia.getCidadeFat() + "/" + this.guia.getEstadoFat()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                        + ": &nbsp;" + CNPJ(this.guia.getCgcFat())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("InscEstadual"))
                                        + ": &nbsp;" + this.guia.getIeFat()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;border-right: 1px solid black;"
                                        + "border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "cliente").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //origem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("DadosOrigem")) + " " + this.guia.getAgencia()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Remetente") + ": ")
                                        + " " + this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", this.guia.getAgenciaOri() + " " + this.guia.getSubAgenciaOri() + " " + this.guia.getnRedOri())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": ")
                                        + " " + this.guia.getEndOri()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;"
                                        + "border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "origem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //enderecoOrigem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getBairroOri() + ", " + this.guia.getCidadeOri() + "/" + this.guia.getEstadoOri()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff;background: #fff !important;overflow: hidden;"
                                        + " width: 335px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "enderecoOrigem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //dadosOrigem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Data") + ": ")
                                        + "&nbsp;"
                                        + Data(this.guia.getColetaOri())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").
                                replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                        + "&nbsp;"
                                        + this.guia.getVeiculoOri().replace(".0", "")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Rota") + ": ")
                                        + "&nbsp;"
                                        + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getRotaOri().replace(".0", ""))));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                        + Hora(this.guia.getHoraChegada())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Said") + ": ")
                                        + Hora(this.guia.getHoraSaida())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;"
                                        + "border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "dadosOrigem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //destino
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("DadosDestino"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Destinatario") + ": ")
                                        + this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", this.guia.getAgenciaDst() + " " + this.guia.getSubAgenciaDst() + " " + this.guia.getnRedDst())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": " + this.guia.getEndDst())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;"
                                        + "border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "destino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //enderecoDestino
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getBairroDst() + ", " + this.guia.getCidadeDst() + "/" + this.guia.getEstadoDst()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;border-right: 1px solid black;"
                                        + "border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "enderecoDestino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //dadosDestino
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Data") + ": ")
                                        + "&nbsp;" + Data(this.guia.getColetaDst())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                        + "&nbsp;" + this.guia.getVeiculoDst().replace(".0", "")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                        + Hora(this.guia.getHora1())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Said") + ": ")
                                        + Hora(this.guia.getHora2())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;overflow: hidden; width: 335px;"
                                        + "border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "dadosDestino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //detalhes
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("DiscriminacaoValorIdentificacao"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("ValorDeclarado") + ": ")
                                        + "&nbsp;"
                                        + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", Moeda(this.guia.getValor().toPlainString()))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "preencheLinha")
                                .replace("@TextoTD", this.guia.getValorExtenso()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("IdentificacaoMalote"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Lacres") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        for (CxFGuiasVol lacre : this.guia.getLacres()) {
                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", lacre.getLacre())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        }

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: justify; padding-right: 4px !important;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", "&emsp; " + getMessageS("TermoAssGuia"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; border-right: 1px solid black ;vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("AssRemetente") + ": ")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("AssDestinatario") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.guia.getAssRemetente().replace("Chefe Equipe: ", getMessageS("ChefeEquipe") + ": ")));
                        if (this.persistencia.getEmpresa().contains("SATGLOVAL") || this.persistencia.getEmpresa().contains("SPM")) {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "vertical-align: baseline;text-align: center;")
                                    .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                    .replace("@TextoTD",
                                            "<img src=\"https://mobile.sasw.com.br:9091/satellite/assinaturas/" + this.persistencia.getEmpresa()
                                            + "/" + this.guia.getSequencia().toString().replace(".0", "") + "/" + this.guia.getParada()
                                            + ".png\" height=\"47px\" width=\"59px\" alt=\"" + this.guia.getAssDestinatario() + "\"/>"));
                        } else {
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                    .replace("@StyleTD", "vertical-align: baseline;text-align: center;")
                                    .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.guia.getAssDestinatario()));
                        }
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Autenticacao"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getAutenticacao()) + "&nbsp;"));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; border-bottom: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Obs") + ": ")
                                        + "&nbsp;"
                                        + this.guia.getObserv()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;"
                                        + "background: #fff !important;"
                                        + "overflow: hidden; width: 335px;border-right: 1px solid black;"
                                        + "border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "detalhes").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                }

                guiaImpressa.append(guiaImpressaAuxTextoTabela);
//                this.html = guiaImpressa.toString();
                this.html = this.relatorio.replace("@TitleRelatorio", this.guia.getGuia().toBigInteger().toString())
                        .replace("@RelatorioHTML", guiaImpressa.toString());
                PrimeFaces.current().executeScript("PF('dlgImprimir').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void selecionarTrajetoSimples(SelectEvent event) {
        this.trajetoSelecionado = (Rt_Perc) event.getObject();
//        abrirTrajeto(null);
    }

    public void selecionarTrajeto(SelectEvent event) {
        abrirTrajetoEdicao((Rt_Perc) event.getObject());
    }

    public void abrirTrajeto() {
        abrirTrajeto(null);
    }

    public void abrirTrajeto(ActionEvent actionEvent) {
        if (null == this.trajetoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneTrajeto"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {

                this.filial = this.loginSatMobWeb.BuscaFilial(this.trajetoSelecionado.getCodFil().toString(), this.codpessoa, this.persistencia);
                this.cliOri = this.rotassatweb.obterClienteTrajeto(this.trajetoSelecionado.getCodFil().toString(), this.trajetoSelecionado.getCodCli1(), this.persistencia);
                if (!this.trajetoSelecionado.getCodCli2().equals("")) {
                    this.cliDst = this.rotassatweb.obterClienteTrajeto(this.trajetoSelecionado.getCodFil().toString(), this.trajetoSelecionado.getCodCli2(), this.persistencia);
                }
                this.listaClientes = new ArrayList<>();
                this.listaClientesDestino = new ArrayList<>();

                this.flag = 2;
                this.flagTrajeto = 2;

                this.historico = this.rotassatweb.listarHistorico(this.trajetoSelecionado.getSequencia(), this.trajetoSelecionado.getParada(), this.persistencia);

                this.filtersGuia = new HashMap<>();
                this.filtersGuia.put(" Rt_Guias.Sequencia = ? ", Arrays.asList(this.trajetoSelecionado.getSequencia().toPlainString()));
                this.filtersGuia.put(" Rt_Guias.Parada = ? ", Arrays.asList(String.valueOf(this.trajetoSelecionado.getParada())));
                this.guias = this.rotassatweb.listarGuias(this.filtersGuia, this.persistencia);
                if (this.guias.isEmpty()) {
                    this.guias = this.eGtvDao.listaGuiasRefeicao(this.trajetoSelecionado.getSequencia().toPlainString(),
                            String.valueOf(this.trajetoSelecionado.getParada()), this.persistencia);
                }

                this.clienteManifest = new Clientes();
                this.clientesManifest = new ArrayList<>();

                this.valor = new Double(this.trajetoSelecionado.getValor());

                PrimeFaces.current().ajax().update("cadastroTrajeto");
                PrimeFaces.current().executeScript("PF('dlgCadastrarTrajetos').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void abrirTrajetoEdicao(Rt_Perc rt_Perc) {
        try {
            this.trajetoSelecionado = rt_Perc;
            this.filial = this.loginSatMobWeb.BuscaFilial(this.trajetoSelecionado.getCodFil().toString(), this.codpessoa, this.persistencia);
            this.listaClientes = new ArrayList<>();
            this.listaClientesDestino = new ArrayList<>();
            this.cliOri = this.rotasSPM.obterClienteTrajeto(this.trajetoSelecionado.getCodFil().toString(), this.trajetoSelecionado.getCodCli1(), this.persistencia);
            this.listaClientes.add(this.cliOri);
            this.listaClientesDestino.add(this.cliOri);
            if (!this.trajetoSelecionado.getCodCli2().equals("")) {
                this.cliDst = this.rotasSPM.obterClienteTrajeto(this.trajetoSelecionado.getCodFil().toString(), this.trajetoSelecionado.getCodCli2(), this.persistencia);
                this.listaClientes.add(this.cliDst);
                this.listaClientesDestino.add(this.cliDst);
            }

            this.filtersGuia = new HashMap<>();
            this.filtersGuia.put(" Rt_Guias.Sequencia = ? ", Arrays.asList(this.trajetoSelecionado.getSequencia().toPlainString()));
            this.filtersGuia.put(" Rt_Guias.Parada = ? ", Arrays.asList(String.valueOf(this.trajetoSelecionado.getParada())));
            this.guias = this.rotassatweb.listarGuias(this.filtersGuia, this.persistencia);

            if (this.guias.isEmpty()) {
                this.guias = this.eGtvDao.listaGuiasRefeicao(this.trajetoSelecionado.getSequencia().toPlainString(),
                        String.valueOf(this.trajetoSelecionado.getParada()), this.persistencia);
            }

            this.flag = 2;
            this.flagTrajeto = 2;

            try {
                this.valor = new Double(this.trajetoSelecionado.getValor());
            } catch (Exception va) {
                this.valor = 0.0;
            }

            PrimeFaces.current().ajax().update("cadastroTrajeto");
            PrimeFaces.current().executeScript("PF('dlgCadastrarTrajetos').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirGuias(Rt_Perc rt_Perc) {
        try {
            this.trajetoSelecionado = rt_Perc;
            this.filtersGuia = new HashMap<>();
            this.filtersGuia.put(" Rt_Guias.Sequencia = ? ", Arrays.asList(this.trajetoSelecionado.getSequencia().toPlainString()));
            this.filtersGuia.put(" Rt_Guias.Parada = ? ", Arrays.asList(String.valueOf(this.trajetoSelecionado.getParada())));
            this.guias = this.rotasSPM.listarGuias(this.filtersGuia, this.persistencia);
            if (this.guias.isEmpty()) {
                throw new Exception("SemGuias");
            } else if (this.guias.size() == 1) {
                this.guiaSelecionada = this.guias.get(0);
                imprimir();
            } else {
                PrimeFaces.current().executeScript("PF('dlgGuiasSelecao').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirGuiasModal() {
        try {
            this.rotasSPM = new RotasSPM();
            this.filtersGuia = new HashMap<>();
            this.filtersGuia.put(" Rt_Guias.Sequencia = ? ", Arrays.asList(this.sequencia.toPlainString()));
            this.filtersGuia.put(" Rt_Guias.Parada = ? ", Arrays.asList(this.parada.toPlainString()));

            this.guias = this.rotasSPM.listarGuias(this.filtersGuia, this.persistencia);
            if (this.guias.isEmpty()) {
                throw new Exception("SemGuias");
            } else if (this.guias.size() == 1) {
                this.guiaSelecionada = this.guias.get(0);
                imprimir();
            } else {
                PrimeFaces.current().executeScript("PF('dlgGuiasSelecao').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirGuiasPagina() {
        try {
            this.rotasSPM = new RotasSPM();
            this.filtersGuia = new HashMap<>();
            this.filtersGuia.put(" Rt_Guias.Sequencia = ? ", Arrays.asList(this.sequencia.toPlainString()));
            this.filtersGuia.put(" Rt_Guias.Parada = ? ", Arrays.asList(this.parada.toPlainString()));
            this.filtersGuia.put(" RPV.Guia = ? ", Arrays.asList(this.carregamentoGuia));
            this.filtersGuia.put(" RPV.Serie = ? ", Arrays.asList(this.carregamentoSerie));

            this.guias = this.rotasSPM.listarGuias(this.filtersGuia, this.persistencia);
            if (this.guias.isEmpty()) {
                throw new Exception("SemGuias");
            } else if (this.guias.size() >= 1) {
                this.guiaSelecionada = this.guias.get(0);
                imprimir();
            } else {
                PrimeFaces.current().executeScript("PF('dlgGuiasSelecao').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirGuia(EGtv eGtv) {
        try {
            this.guiaSelecionada = eGtv;
            imprimir();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editarTrajeto() {
        try {

            BigDecimal valorDst = BigDecimal.ZERO;
            boolean paradaDestino = false;

            if (this.trajetoSelecionado.getER().equals("R") || this.trajetoSelecionado.getER().equals("ER")) {
                paradaDestino = true;
                this.trajetoValidacao = this.rotassatweb.validarHorario(this.trajetoSelecionado.getSequencia().toString(),
                        this.trajetoSelecionado.getHora1D(), this.persistencia);
                if (this.trajetoValidacao != null) {
                    if (!this.trajetoValidacao.getCodCli1().equals(this.trajetoSelecionado.getCodCli2())) {
                        throw new Exception(Messages.getMessageS("ExisteTrajetoMesmoHorarioEntrega")
                                + ": " + this.trajetoValidacao.getNRed() + ",\n "
                                + Messages.getMessageS("Parada") + ": " + this.trajetoValidacao.getParada());
                    } else {
                        this.trajetoSelecionado.setDPar(this.trajetoValidacao.getParada());
                        paradaDestino = false;
                    }
                }
            }

            this.trajetoSelecionado.setValor(this.valor.toString());
            if ((this.trajetoSelecionado.getER().equals("R") || this.trajetoSelecionado.getER().equals("ER")) && paradaDestino) {
                String cxForte = this.rotassatweb.cxForte(this.trajetoSelecionado.getCodFil(), this.persistencia);
                if (cxForte.equals(this.trajetoSelecionado.getCodCli1())) {
                    valorDst = new BigDecimal(this.trajetoSelecionado.getValor());
                }
                this.trajetoValidacao = new Rt_Perc();
                this.trajetoValidacao.setSequencia(this.trajetoSelecionado.getSequencia().toString());
                this.trajetoValidacao.setHora1(this.trajetoSelecionado.getHora1D());
                this.trajetoValidacao.setParada(this.trajetoSelecionado.getDPar());
                this.trajetoValidacao.setCodFil(this.trajetoSelecionado.getCodFil().toString());
                this.trajetoValidacao.setER("E");
                this.trajetoValidacao.setTipoSrv(this.trajetoSelecionado.getTipoSrv());
                this.trajetoValidacao.setCodCli1(this.trajetoSelecionado.getCodCli2());
                this.trajetoValidacao.setNRed(this.trajetoSelecionado.getNRedDst());
                this.trajetoValidacao.setRegiao(this.trajetoSelecionado.getRegiaoDst());
                this.trajetoValidacao.setValor(valorDst.toString());
                this.trajetoValidacao.setObserv(RecortaAteEspaço(this.trajetoSelecionado.getObserv(), 0, 20));
                this.trajetoValidacao.setOperIncl(RecortaAteEspaço(this.operador, 0, 10));
                this.trajetoValidacao.setOperador(RecortaAteEspaço(this.operador, 0, 10));
                this.trajetoValidacao.setFlag_Excl("");
                this.trajetoValidacao.setOperFech("");
                this.trajetoValidacao.setHrBaixa("");
                int dpar = this.rotassatweb.criarTrajetos(this.trajetoValidacao, this.persistencia);
                this.trajetoSelecionado.setDPar(dpar);
            }

            this.trajetoSelecionado.setDt_Alter(LocalDate.now());
            this.trajetoSelecionado.setHr_Alter(getDataAtual("HORA"));
            this.trajetoSelecionado.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.rotassatweb.atualizarTrajeto(this.trajetoSelecionado, this.persistencia);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            atualizaListaTrajetos();
            PrimeFaces.current().executeScript("PF('dlgCadastrarTrajetos').hide();");

            //            novoTrajeto();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

    }

    public void atualizaListaTrajetos() {
        try {
            this.novaRota.setTrajetos(this.rotasSPM.listarTrajetos(this.novaRota.getSequencia(), this.exclFlag, this.persistencia));
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void calculaHorasTrabalhadas() {
        try {
            verificaHoraIntervalo();
        } catch (ParseException e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("HoraInvalida"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void calculaHorasTrabalhadasEscala() {
        try {
            verificaHoraIntervaloEscala();
        } catch (ParseException e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("HoraInvalida"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void reaproveitaClienteOrigem() throws Exception {
        ClientesDao clientesDao = new ClientesDao();

        this.cliOri = clientesDao.consultaSimplesCliente(this.codCliReaproveitaOrigem, this.persistencia);
        this.trajetoSelecionado.setCodCli1(this.cliOri.getCodigo());
        this.trajetoSelecionado.setNRed(this.cliOri.getNRed());
        this.trajetoSelecionado.setRegiao(this.cliOri.getRegiao());
    }

    public void reaproveitaClienteDestino() throws Exception {
        ClientesDao clientesDao = new ClientesDao();

        this.cliDst = clientesDao.consultaSimplesCliente(this.codCliReaproveitaDestino, this.persistencia);
        this.trajetoSelecionado.setCodCli2(this.cliDst.getCodigo());
        this.trajetoSelecionado.setNRedDst(this.cliDst.getNRed());
        this.trajetoSelecionado.setRegiaoDst(this.cliDst.getRegiao());

        if (this.trajetoSelecionado.getHora1() != null && !this.trajetoSelecionado.getHora1().equals("")) {
            this.trajetosSugeridos = this.rotassatweb.listarEntregas(this.trajetoSelecionado.getSequencia(), this.trajetoSelecionado.getCodCli2(),
                    this.trajetoSelecionado.getHora1(), this.persistencia);

            if (!this.trajetosSugeridos.isEmpty()) {
                PrimeFaces.current().executeScript("PF('dlgTrajetosEntrega').show();");
                //PrimeFaces.current().ajax().update("panelTrajetosEntrega"); 
            }

        } else {
            this.trajetosSugeridos = new ArrayList<>();
        }
    }

    /**
     * Busca os clientes pelo nome ou nred para cadastro de Rt_Perc
     *
     * @param query
     * @return
     */
    public List<Clientes> listarClientes(String query) {
        this.listaClientes = new ArrayList<>();
        try {
            List<Clientes> retorno = this.rotassatweb.listarClientes(this.trajetoSelecionado.getCodFil().toString(), query, this.persistencia);

            for (Clientes c : retorno) {
                if (c.getNRed().toUpperCase().contains(query.toUpperCase())
                        || c.getNome().toUpperCase().contains(query.toUpperCase())) {
                    c.setNome(c.getNRed() + " - " + c.getNome());
                    this.listaClientes.add(c);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.listaClientes;
    }

    /**
     * Busca os clientes pelo nome ou nred para cadastro de Rt_Perc
     *
     * @param query
     * @return
     */
    public List<Clientes> listarClientesDst(String query) {
        this.listaClientesDestino = new ArrayList<>();
        try {
            List<Clientes> retorno = this.rotassatweb.listarClientes(this.trajetoSelecionado.getCodFil().toString(), query, this.persistencia);
            for (Clientes c : retorno) {
                if (c.getNRed().toUpperCase().contains(query.toUpperCase())
                        || c.getNome().toUpperCase().contains(query.toUpperCase())) {
                    c.setNome(c.getNRed() + " - " + c.getNome());
                    this.listaClientesDestino.add(c);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.listaClientesDestino;
    }

    /**
     * Seleção do cliente de origem de Rt_Perc
     *
     * @param event
     */
    public void selecionarCliOri(SelectEvent event) {
        try {
            this.cliOri = ((Clientes) event.getObject());
            this.trajetoSelecionado.setCodCli1(this.cliOri.getCodigo());
            this.trajetoSelecionado.setNRed(this.cliOri.getNRed());
            this.trajetoSelecionado.setRegiao(this.cliOri.getRegiao());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Seleção do cliente de destino de Rt_Perc quando necessário
     *
     * @param event
     */
    public void selecionarCliDst(SelectEvent event) {
        try {
            this.cliDst = ((Clientes) event.getObject());
            this.trajetoSelecionado.setCodCli2(this.cliDst.getCodigo());
            this.trajetoSelecionado.setNRedDst(this.cliDst.getNRed());
            this.trajetoSelecionado.setRegiaoDst(this.cliDst.getRegiao());

            if (this.trajetoSelecionado.getHora1() != null && !this.trajetoSelecionado.getHora1().equals("")) {
                String Hora = this.trajetoSelecionado.getHora1();
                String CodigoCli = "";

                if (this.trajetoSelecionado.getCodCli1().substring(0, 3).equals("999")) {
                    //Hora = this.trajetoSelecionado.getHora1();
                    CodigoCli = this.trajetoSelecionado.getCodCli1();
                } else {
                    //Hora = this.trajetoSelecionado.getHora1D();
                    CodigoCli = this.trajetoSelecionado.getCodCli2();
                }

                this.trajetosSugeridos = this.rotassatweb.listarEntregas(this.trajetoSelecionado.getSequencia(), CodigoCli, Hora, this.persistencia);

                if (!this.trajetosSugeridos.isEmpty()) {
                    PrimeFaces.current().executeScript("PF('dlgTrajetosEntrega').show();");
                    //PrimeFaces.current().ajax().update("panelTrajetosEntrega"); 
                }

                consularOSexistente();
            } else {
                this.trajetosSugeridos = new ArrayList<>();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private boolean consularOSexistente() {
        if (null != this.trajetoSelecionado.getCodCli1()
                && !this.trajetoSelecionado.getCodCli1().equals("")
                && null != this.trajetoSelecionado.getCodCli2()
                && !this.trajetoSelecionado.getCodCli2().equals("")) {
            try {
                String CodigoCaixaforte = "";

                for (CxForte cxForteLista1 : this.cxForteLista) {
                    CodigoCaixaforte = cxForteLista1.getCodCli();
                    break;
                }

                this.osSelecionada = this.pedidosSatMobWeb.buscarOS_Pedido(this.trajetoSelecionado.getCodCli1(),
                        this.trajetoSelecionado.getCodCli2(), this.trajetoSelecionado.getCodFil().toString(), CodigoCaixaforte, this.persistencia);
                if (this.osSelecionada == null) {
                    this.trajetoSelecionado.setOS("0");
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("NaoExisteOS"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return false;
                } else {
                    this.trajetoSelecionado.setOS(this.osSelecionada.getOS());
                    return true;
                }
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                this.log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(this.log, this.caminho);
                return false;
            }
        } else {
            return true;
        }
    }

    public void abrirTrajetosModal() {
        try {
            if (null != this.cliDst
                    && null != this.cliDst.getCodigo()
                    && !this.cliDst.getCodigo().equals("")
                    && null != this.cliOri
                    && null != this.cliOri.getCodigo()
                    && !this.cliOri.getCodigo().equals("")) {

                if (this.trajetoSelecionado.getHora1() != null && !this.trajetoSelecionado.getHora1().equals("")) {
                    this.trajetosSugeridos = this.rotassatweb.listarEntregas(this.trajetoSelecionado.getSequencia(), this.trajetoSelecionado.getCodCli2(),
                            this.trajetoSelecionado.getHora1(), this.persistencia);

                    if (!this.trajetosSugeridos.isEmpty()) {
                        PrimeFaces.current().executeScript("PF('dlgTrajetosEntrega').show();");
                    }
                } else {
                    this.trajetosSugeridos = new ArrayList<>();
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarParadaEntrega(SelectEvent event) {
        try {
            this.trajetoSugeridoSelecionado = (Rt_Perc) event.getObject();
            this.trajetoSelecionado.setHora1D(this.trajetoSugeridoSelecionado.getHora1());
            this.trajetoSelecionado.setDPar(this.trajetoSugeridoSelecionado.getParada());
            PrimeFaces.current().executeScript("PF('dlgTrajetosEntrega').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void validaHoraEntrega() {
        try {
            validarHorarioTrajeto(false);
        } catch (ParseException e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("HoraInvalida"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void validaHoraRecolhimento() {
        try {
            validarHorarioTrajeto(true);
        } catch (ParseException e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("HoraInvalida"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void validarHorarioTrajeto(boolean recolhimento) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        sdf.setLenient(true);
        LocalDate dia = LocalDate.parse(removeMascaraData(this.novaRota.getData()), DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate diaSeguinte = dia.plusDays(1);

        this.hora1.setTime(sdf.parse(this.novaRota.getHrLargada()));
        this.hora1.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora1.get(Calendar.HOUR_OF_DAY), this.hora1.get(Calendar.MINUTE));
        this.hora2.setTime(sdf.parse(this.novaRota.getHrIntIni()));
        this.hora2.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
        this.hora3.setTime(sdf.parse(this.novaRota.getHrIntFim()));
        this.hora3.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
        this.hora4.setTime(sdf.parse(this.novaRota.getHrChegada()));
        this.hora4.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));

        // Se inicio da rota depois do final, adiciona-se um dia ao final.
        if (this.hora1.after(this.hora4)) {
            this.hora4.set(diaSeguinte.getYear(), diaSeguinte.getMonthValue(), diaSeguinte.getDayOfMonth(),
                    this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
        }

        // Se exisitr horário de intervalo, deve-se fazer o mesmo tratamento acima, além de algumas validações.
        if (!this.novaRota.getHrIntFim().equals(this.novaRota.getHrIntIni())) {
            // Se o começo do intervalo for antes do início da rota, adiciona-se um dia ao intervalo.
            if (this.hora2.before(this.hora1)) {
                this.hora2.set(diaSeguinte.getYear(), diaSeguinte.getMonthValue(), diaSeguinte.getDayOfMonth(),
                        this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
                this.hora3.set(diaSeguinte.getYear(), diaSeguinte.getMonthValue(), diaSeguinte.getDayOfMonth(),
                        this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
            }
        }

        // Teoricamente, os horários já estão válidos porque a rota já foi cadastrada.
        // Validando horários das paradas
        this.hora1O.setTime(sdf.parse(this.trajetoSelecionado.getHora1().toUpperCase()));
        this.hora1O.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora1O.get(Calendar.HOUR_OF_DAY), this.hora1O.get(Calendar.MINUTE));

        // Se o horário da parada for menor que o do início da rota, adiciona-se um dia a ele.
        if (this.hora1O.before(this.hora1)) {
            this.hora1O.set(diaSeguinte.getYear(), diaSeguinte.getMonthValue(), diaSeguinte.getDayOfMonth(),
                    this.hora1O.get(Calendar.HOUR_OF_DAY), this.hora1O.get(Calendar.MINUTE));
        }

        if (this.hora1O.before(this.hora1)) {
            throw new Exception("HorarioForaRota");
        }

        if (this.hora1O.after(this.hora4)) {
            throw new Exception("HorarioForaRota");
        }

        if (!this.novaRota.getHrIntFim().equals(this.novaRota.getHrIntIni()) && this.hora1O.after(this.hora2) && this.hora1O.before(this.hora3)) {
            throw new Exception("HorarioDentroIntervalo");
        }

        if (recolhimento) {
            this.hora1D.setTime(sdf.parse(this.trajetoSelecionado.getHora1D()));
            this.hora1D.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                    this.hora1D.get(Calendar.HOUR_OF_DAY), this.hora1D.get(Calendar.MINUTE));

            if (this.hora1D.before(this.hora1)) {
                this.hora1D.set(diaSeguinte.getYear(), diaSeguinte.getMonthValue(), diaSeguinte.getDayOfMonth(),
                        this.hora1D.get(Calendar.HOUR_OF_DAY), this.hora1D.get(Calendar.MINUTE));
            }

            if (this.hora1D.before(this.hora1) || this.hora1D.after(this.hora4)) {
                throw new Exception("HorarioForaRota");
            }
            if (!this.novaRota.getHrIntFim().equals(this.novaRota.getHrIntIni()) && this.hora1D.after(this.hora2) && this.hora1D.before(this.hora3)) {
                throw new Exception("HorarioDentroIntervalo");
            }
            if (this.hora1D.before(this.hora1O)) {
                throw new Exception("RecolhimentoAnteriorEntrega");
            }
        }
    }

    public void verificaHoraIntervaloEscala() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        sdf.setLenient(true);
        LocalDate dia = LocalDate.parse(removeMascaraData(this.escala.getData()), DateTimeFormatter.ofPattern("yyyyMMdd"));

        this.hora1.setTime(sdf.parse(this.escala.getHora1()));
        this.hora1.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora1.get(Calendar.HOUR_OF_DAY), this.hora1.get(Calendar.MINUTE));
        this.hora2.setTime(sdf.parse(this.escala.getHora2()));
        this.hora2.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
        this.hora3.setTime(sdf.parse(this.escala.getHora3()));
        this.hora3.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
        this.hora4.setTime(sdf.parse(this.escala.getHora4()));
        this.hora4.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));

        long diferenca, minutos;
        float tot;

        if (!this.escala.getHora2().equals(this.escala.getHora3())) {
            if (this.hora1.before(this.hora4)) {
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                this.hora4.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            this.escala.setHsTot(Float.toString(tot / 60));
            if (this.hora2.before(this.hora1)) {
                LocalDate dia3 = dia.plusDays(1);
                this.hora2.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
            }
            if (this.hora2.after(this.hora4)) {
                throw new Exception("IntervaloInvalido");
            }
            if (this.hora3.before(this.hora1)) {
                LocalDate dia3 = dia.plusDays(1);
                this.hora3.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
            }
            if (this.hora3.after(this.hora4)) {
                throw new Exception("IntervaloInvalido");
            }
            if (this.hora2.before(this.hora3)) {
                diferenca = this.hora3.getTime().getTime() - this.hora2.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else if (this.hora2.after(this.hora3)) {
                throw new Exception("IntervaloInvalido");
            }
            tot = tot - minutos;
            if (tot > 0) {
                this.escala.setHsTot(Float.toString(tot / 60));
            }
            if (tot < 0) {
                throw new Exception("IntervaloErrado");
            }
            this.hora1.add(Calendar.MINUTE, -15);
        } else {
            if (this.hora1.before(this.hora4)) {
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                this.hora4.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            this.escala.setHsTot(Float.toString(tot / 60));
            this.hora1.add(Calendar.MINUTE, -15);
        }
    }

    /**
     * Verifica se a hora do intervalo ultrapassa a hora de trabalho
     *
     * @throws java.lang.Exception
     */
    public void verificaHoraIntervalo() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        sdf.setLenient(true);
        LocalDate dia = LocalDate.parse(removeMascaraData(this.novaRota.getData()), DateTimeFormatter.ofPattern("yyyyMMdd"));

        this.hora1.setTime(sdf.parse(this.novaRota.getHrLargada()));
        this.hora1.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora1.get(Calendar.HOUR_OF_DAY), this.hora1.get(Calendar.MINUTE));
        this.hora2.setTime(sdf.parse(this.novaRota.getHrIntIni()));
        this.hora2.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
        this.hora3.setTime(sdf.parse(this.novaRota.getHrIntFim()));
        this.hora3.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
        this.hora4.setTime(sdf.parse(this.novaRota.getHrChegada()));
        this.hora4.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));

        long diferenca, minutos;
        float tot;

        if (!this.novaRota.getHrIntFim().equals(this.novaRota.getHrIntIni())) {
            if (this.hora1.before(this.hora4)) {
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                this.hora4.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            this.novaRota.setHsTotal(Float.toString(tot / 60));
            if (this.hora2.before(this.hora1)) {
                LocalDate dia3 = dia.plusDays(1);
                this.hora2.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
            }
            if (this.hora2.after(this.hora4)) {
                throw new Exception("IntervaloInvalido");
            }
            if (this.hora3.before(this.hora1)) {
                LocalDate dia3 = dia.plusDays(1);
                this.hora3.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
            }
            if (this.hora3.after(this.hora4)) {
                throw new Exception("IntervaloInvalido");
            }
            if (this.hora2.before(this.hora3)) {
                diferenca = this.hora3.getTime().getTime() - this.hora2.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else if (this.hora2.after(this.hora3)) {
                throw new Exception("IntervaloInvalido");
            }
            tot = tot - minutos;
            if (tot > 0) {
                this.novaRota.setHsTotal(Float.toString(tot / 60));
            }
            if (tot < 0) {
                throw new Exception("IntervaloErrado");
            }
            this.hora1.add(Calendar.MINUTE, -15);
        } else {
            if (this.hora1.before(this.hora4)) {
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                this.hora4.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            this.novaRota.setHsTotal(Float.toString(tot / 60));
            this.hora1.add(Calendar.MINUTE, -15);
        }
    }

    /**
     * Busca uma lista de pessoas
     *
     * @param query String para comparar na lista de pessoa
     * @return Lista de pessoa
     */
    public List<Pessoa> buscarPessoas(String query) {
        this.listaPessoa = new ArrayList<>();
        try {
            this.listaPessoa = this.rotassatweb.buscarPessoaEscala(query, this.filial.getCodfilAc(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return listaPessoa;
    }

    public void buscarFuncionario() {
        try {
//            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("os", this.pedidoSelecionado.getOS());
            FacesContext.getCurrentInstance().getExternalContext().redirect("../recursoshumanos/funcion.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public List<Pessoa> buscarPessoas(String query, String funcao) {
        this.listaPessoa = new ArrayList<>();
        try {
            this.listaPessoa = this.rotassatweb.buscarPessoaEscalaFuncao(query, funcao, this.filial.getCodfilAc(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return listaPessoa;
    }

    public List<Pessoa> buscarMotorista(String query) {
        return buscarPessoas(query, "M");
    }

    public List<Pessoa> buscarChEquip(String query) {
        return buscarPessoas(query, "C");
    }

    public List<Pessoa> buscarVigilante(String query) {
        return buscarPessoas(query, "V");
    }

    public void PesquisaFilial() {
        List<SasPWFill> list = new ArrayList<>();
        try {
            for (SasPWFill sasPWFill : list) {
                if (sasPWFill.getDescricao().equals(this.filial.getDescricao())) {
                    this.filial.setCodFil(sasPWFill.getCodFil());
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Transforma Localdate para String no formato yyyy-MM-dd
     *
     * @param date Data em LocalDate
     * @return Data em LocalDate
     */
    public String LocalDate2String(LocalDate date) {
        //LocalDate yyyy-MM-dd
        String data = date.toString();
        return data.substring(0, 4) + data.substring(5, 7) + data.substring(8, 10);
    }

    public void selecionaUltimaRota() {
        try {
            this.rotaSelecionada = this.novaRota;

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listarValoresRotas() {
        try {
            // TESTE
            filters.replace(" Rotas.Flag_excl <> ? ", "*");
            filters.replace(" CONVERT(BigInt, Rotas.Sequencia) = ? ", "");
            filters.replace(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", "*");
            filters.replace(" Rotas.CodFil = ? ", codfil);
            filters.replace(" Rotas.Data = ? ", dataTela);
            filters.replace(" CONVERT(BigInt, Rotas.Rota) = ? ", "");
            filters.replace(" Escala.matrche = ? ", "");
            filters.replace(" Funcion.nome = ? ", "");
            filters.replace(" Escala.matrmot = ? ", "");
            filters.replace(" mot.nome like ? ", "");
            filters.replace(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", "");
            filters.replace(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", "");

            rotasDet = rotassatweb.detalhesRota(filters, codpessoa, persistencia);

            // FIXME
            filters.replace(" Rotas.Flag_excl <> ? ", Arrays.asList("*"));
            filters.replace(" CONVERT(BigInt, Rotas.Sequencia) = ? ", Arrays.asList());
            filters.replace(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", Arrays.asList("*"));
            filters.replace(" Rotas.CodFil = ? ", Arrays.asList(codfil));
            filters.replace(" Rotas.Data = ? ", Arrays.asList(dataTela));
            filters.replace(" CONVERT(BigInt, Rotas.Rota) = ? ", Arrays.asList());
            filters.replace(" Escala.matrche = ? ", Arrays.asList());
            filters.replace(" Funcion.nome = ? ", Arrays.asList());
            filters.replace(" Escala.matrmot = ? ", Arrays.asList());
            filters.replace(" mot.nome like ? ", Arrays.asList());
            filters.replace(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
            filters.replace(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());

            PrimeFaces.current().ajax().update("cabecalho2:tabela2");
            PrimeFaces.current().executeScript("PF('dlgDetalhesValores').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<Rotas> getAllRotas() {
        if (rotas == null) {
            filters.replace(" Rotas.Flag_excl <> ? ", Arrays.asList("*"));
            filters.replace(" CONVERT(BigInt, Rotas.Sequencia) = ? ", Arrays.asList());
            filters.replace(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", Arrays.asList("*"));
            filters.replace(" Rotas.CodFil = ? ", Arrays.asList(codfil));
            filters.replace(" Rotas.Data = ? ", Arrays.asList(dataTela));
            filters.replace(" CONVERT(BigInt, Rotas.Rota) = ? ", Arrays.asList());
            filters.replace(" Escala.matrche = ? ", Arrays.asList());
            filters.replace(" Funcion.nome = ? ", Arrays.asList());
            filters.replace(" Escala.matrmot = ? ", Arrays.asList());
            filters.replace(" mot.nome like ? ", Arrays.asList());
            filters.replace(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
            filters.replace(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());

            rotas = new RotasTvLazyList(persistencia, filters);
        } else {
            ((RotasTvLazyList) rotas).setFilters(filters);
        }
        try {
            total = rotasSPM.contagemValores(filters, persistencia);

            // FIXME:
            Map filtersStringOldFormat = new HashMap();
            filtersStringOldFormat.put(" Rotas.Flag_excl <> ? ", "*");
            filtersStringOldFormat.put(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", "*");
            filtersStringOldFormat.put(" Rotas.CodFil = ? ", codfil);
            filtersStringOldFormat.put(" Rotas.Data = ? ", dataTela);
            filtersStringOldFormat.put(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", "");

            valorTotal = rotassatweb.somaValoresSemCodPessoa(filtersStringOldFormat, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return rotas;
    }

    public void prepararManifest() {
        try {
            if (this.guias.isEmpty()) {
                throw new Exception("SemGuiasParaManifest");
            }
            this.clientesManifest = this.rotassatweb.clientesFaturar(this.trajetoSelecionado.getCodFil().toPlainString(),
                    this.trajetoSelecionado.getSequencia().toPlainString(), this.trajetoSelecionado.getParada(), this.persistencia);
            if (this.clientesManifest.isEmpty()) {
                throw new Exception("SemInfoCliFat");
            } else if (this.clientesManifest.size() == 1) {
                this.clienteManifest = this.clientesManifest.get(0);
                gerarManifest();
            } else {
                PrimeFaces.current().ajax().update("formClientesManifest");
                PrimeFaces.current().executeScript("PF('dlgClientesManifest').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void gerarManifest() {
        try {

            this.email = new EmailsEnviar();
            // Preparação do arquivo html
            File file = new File(this.getClass().getResource("guias_entrega.html").getPath().replace("%20", " "));
            FileInputStream fis = new FileInputStream(file);
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            fis.close();
            String html = new String(data, "UTF-8");
            this.email = this.rotassatweb.geraManifest(this.trajetoSelecionado.getCodFil().toPlainString(),
                    this.trajetoSelecionado.getSequencia().toPlainString(), this.trajetoSelecionado.getParada(), getDataAtual("SQL"),
                    getDataAtual("HORA"), html, LocaleController.getsCurrentLocale().getLanguage(), this.clienteManifest.getCodigo(),
                    this.persistencia);
            PrimeFaces.current().ajax().update("formEmail");
            PrimeFaces.current().executeScript("PF('dlgEmail').show();");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void enviarManifest() {
        try {
            this.rotassatweb.enviarEmailManifest(this.email, this.satellite);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("EmailFilaEnvio"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public boolean isDataAtual() {
        return this.dataTela.equals(getDataAtual("SQL"));
    }

    /*:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::*/
 /*::    Graus para Radianos                                         :*/
 /*:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::*/
    private static double deg2rad(double deg) {
        return (deg * Math.PI / 180.0);
    }

    /*:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::*/
 /*::    Radianos para Graus                                         :*/
 /*:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::*/
    private static double rad2deg(double rad) {
        return (rad * 180 / Math.PI);
    }

    private static double distance(double lat1, double lon1, double lat2, double lon2) {
        double theta = lon1 - lon2;
        double dist = Math.sin(deg2rad(lat1)) * Math.sin(deg2rad(lat2)) + Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.cos(deg2rad(theta));
        dist = Math.acos(dist);
        dist = rad2deg(dist);
        dist = dist * 60 * 1.1515;
        //Convertendo para KM
        dist = dist * 1.609344;

        return (dist);
    }

    private Map gerarTiposRelatorios() {
        Map map = new HashMap<>();
        map.put(getMessageS("RESUMO_ROTAS"), TiposRelatorio.RESUMO_ROTAS);
        map.put(getMessageS("RESUMO_ROTAS_PERIODO"), TiposRelatorio.RESUMO_ROTAS_PERIODO);
        return map;
    }

    public void relatoriosDisponiveis() {
        this.relatorio = null;
        this.dataModelo = getDataAtual("SQL");
        this.tiposRelatorios = gerarTiposRelatorios();
        this.relatorioSelecionado = null;
        this.relatorioDuasDatas = false;

        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        this.dataModeloFinal = sdf.format(c.getTime());
        PrimeFaces.current().resetInputs("formRelatoriosDisponiveis");
        PrimeFaces.current().executeScript("PF('dlgRelatorios').show();");
    }

    public void selecionarRelatorio() {
        try {
            if (this.relatorioSelecionado != null) {
                switch (this.relatorioSelecionado) {
                    case RESUMO_ROTAS:
                        this.relatorioDuasDatas = false;
                        break;
                    case RESUMO_ROTAS_PERIODO:
                        this.relatorioDuasDatas = true;
                        break;
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void gerarRelatorio() {
        try {
            if (this.relatorio == null) {
                carregarRelatorio();
            }
            InputStream stream = new ByteArrayInputStream(this.relatorio.replace("https://mobile.sasw.com.br:9091", "http://localhost:9080").getBytes());

            ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
            ITextRenderer renderer = new ITextRenderer();
            Tidy tidy = new Tidy();
            tidy.setShowWarnings(false);
            org.w3c.dom.Document doc = tidy.parseDOM(stream, null);
            renderer.setDocument(doc, null);
            renderer.layout();
            renderer.createPDF(osPdf);

            InputStream inputPDF = new ByteArrayInputStream(osPdf.toByteArray());

            this.arquivoDownload = new DefaultStreamedContent(inputPDF, "pdf", this.nomeArquivo + ".pdf");
            osPdf.close();
            stream.close();
            inputPDF.close();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarRelatorio() {

        StringBuilder relatorioBuilder = new StringBuilder();

        /**
         * CABEÇALHO
         */
        relatorioBuilder.append("<table border=\"0\" cellpadding=\"1\" cellspacing=\"1\" class=\"cabecalho\">");
        relatorioBuilder.append("<tbody>");
        relatorioBuilder.append("<tr>");
        relatorioBuilder.append("<td rowspan=\"3\" style=\"width: 20px;\">");
        relatorioBuilder.append("<img src=\"").append(getLogoAnexo(this.persistencia.getEmpresa(), this.codfil)).append("\" style=\"width: 200px;height: 85px;\" /></td>");
        relatorioBuilder.append("<td><strong>").append(this.filiais.getRazaoSocial()).append("</strong></td>");
        relatorioBuilder.append("</tr>");
        relatorioBuilder.append("<tr>");
        relatorioBuilder.append("<td>").append(this.filiais.getEndereco()).append("</td>");
        relatorioBuilder.append("</tr>");
        relatorioBuilder.append("<tr>");
        relatorioBuilder.append("<td>").append(this.filiais.getCidade()).append("/").append(this.filiais.getUF()).append(", ").append(CEP(this.filiais.getCEP())).append("</td>");
        relatorioBuilder.append("</tr>");
        relatorioBuilder.append("</tbody>");
        relatorioBuilder.append("</table>");

        try {
            switch (this.relatorioSelecionado) {
                case RESUMO_ROTAS_PERIODO:
                    this.nomeArquivo = getMessageS("RESUMO_ROTAS_PERIODO") + "-" + this.dataModelo + "-" + this.dataModeloFinal;

                    relatorioBuilder.append("<br>").append("<div style=\"text-align: center\">").append("<H3>")
                            .append(getMessageS("RESUMO_ROTAS_PERIODO").toUpperCase())
                            .append("</H3>").append("</div>").append("<br>").append("<br>");

                    relatorioBuilder.append(getMessageS("Periodo")).append(": ")
                            .append(Data(this.dataModelo)).append(" - ").append(Data(this.dataModeloFinal));

                    relatorioBuilder.append("<br>").append("<hr>").append("<br>");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<H3>")
                            .append(getMessageS("Rotas").toUpperCase())
                            .append("</H3>").append("</div>")
                            .append("<br>");

                    relatorioBuilder.append("<hr>").append("<br>").append("<table style=\"width: 100%;\">");
                    relatorioBuilder.append("<thead>").append("<tr>")
                            .append("<th>").append(getMessageS("Rota")).append("</th>")
                            .append("<th>").append(getMessageS("Motorista")).append("</th>")
                            .append("<th>").append(getMessageS("Entregas")).append("</th>")
                            .append("<th>").append(getMessageS("Recolhimentos")).append("</th>")
                            .append("</thead>").append("<tbody>");

                    List<Rotas> rrp = this.rotassatweb.relatorioResumoRotas(this.codfil, this.dataModelo, this.dataModeloFinal, this.persistencia);
                    int totalEntOkp = 0,
                     totalRecOkp = 0,
                     parcialEntOkp = 0,
                     parcialRecOk = 0;
                    String dataAux = this.dataModelo;

                    relatorioBuilder
                            .append("<tr>")
                            .append("<td colspan=\"4\">").append(Data(dataAux)).append("</td>")
                            .append("</tr>");
                    for (Rotas r : rrp) {
                        if (!dataAux.equals(r.getData())) {
                            relatorioBuilder
                                    .append("<tr>")
                                    .append("<td colspan=\"2\">").append(getMessageS("Total")).append("</td>")
                                    .append("<td>").append(parcialEntOkp).append("</td>")
                                    .append("<td>").append(parcialRecOk).append("</td>")
                                    .append("</tr>");

                            relatorioBuilder
                                    .append("<tr>")
                                    .append("<td style=\"border-top: 1px solid black;\" colspan=\"4\"></H3></td>")
                                    .append("</tr>");

                            parcialEntOkp = 0;
                            parcialRecOk = 0;
                            dataAux = r.getData();
                            relatorioBuilder
                                    .append("<tr>")
                                    .append("<td colspan=\"4\">").append(Data(dataAux)).append("</td>")
                                    .append("</tr>");
                        }

                        totalEntOkp = totalEntOkp + Integer.valueOf(r.getEntOk());
                        totalRecOkp = totalRecOkp + Integer.valueOf(r.getRecOk());
                        parcialEntOkp = parcialEntOkp + Integer.valueOf(r.getEntOk());
                        parcialRecOk = parcialRecOk + Integer.valueOf(r.getRecOk());

                        relatorioBuilder
                                .append("<tr>")
                                .append("<td>").append(r.getRota()).append("</td>")
                                .append("<td>").append(r.getNomeMotorista()).append("</td>")
                                .append("<td>").append(r.getEntOk()).append("</td>")
                                .append("<td>").append(r.getRecOk()).append("</td>")
                                .append("</tr>");
                    }

                    relatorioBuilder
                            .append("<tr>")
                            .append("<td colspan=\"2\">").append(getMessageS("Total")).append("</td>")
                            .append("<td>").append(parcialEntOkp).append("</td>")
                            .append("<td>").append(parcialRecOk).append("</td>")
                            .append("</tr>");

                    relatorioBuilder
                            .append("<tr>")
                            .append("<td style=\"border-top: 1px solid black;\" colspan=\"4\"></H3></td>")
                            .append("</tr>");

                    relatorioBuilder.append("</tbody>")
                            .append("<tfoot>")
                            .append("<tr>")
                            .append("<td colspan=\"2\">").append(getMessageS("Total")).append("</td>")
                            .append("<td>").append(totalEntOkp).append("</td>")
                            .append("<td>").append(totalRecOkp).append("</td>")
                            .append("</tr>");

                    relatorioBuilder.append("</tfoot></table>").append("<hr>").append("<br>");
                    break;
                case RESUMO_ROTAS:
                    this.nomeArquivo = getMessageS("RESUMO_ROTAS") + "-" + this.dataModelo;

                    relatorioBuilder.append("<br>").append("<div style=\"text-align: center\">").append("<H3>")
                            .append(getMessageS("RESUMO_ROTAS").toUpperCase())
                            .append("</H3>").append("</div>").append("<br>").append("<br>");

                    relatorioBuilder.append(getMessageS("Periodo")).append(": ")
                            .append(Data(this.dataModelo));

                    relatorioBuilder.append("<br>").append("<hr>").append("<br>");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<H3>")
                            .append(getMessageS("Rotas").toUpperCase())
                            .append("</H3>").append("</div>")
                            .append("<br>");

                    relatorioBuilder.append("<hr>").append("<br>").append("<table style=\"width: 100%;\">");
                    relatorioBuilder.append("<thead>").append("<tr>")
                            .append("<th>").append(getMessageS("Rota")).append("</th>")
                            .append("<th>").append(getMessageS("Motorista")).append("</th>")
                            .append("<th>").append(getMessageS("Entregas")).append("</th>")
                            .append("<th>").append(getMessageS("Recolhimentos")).append("</th>")
                            .append("</thead>").append("<tbody>");

                    List<Rotas> rr = this.rotassatweb.relatorioResumoRotas(this.codfil, this.dataModelo, this.persistencia);
                    int totalEntOk = 0,
                     totalRecOk = 0;
                    for (Rotas r : rr) {
                        totalEntOk = totalEntOk + Integer.valueOf(r.getEntOk());
                        totalRecOk = totalRecOk + Integer.valueOf(r.getRecOk());
                        relatorioBuilder
                                .append("<tr>")
                                .append("<td>").append(r.getRota()).append("</td>")
                                .append("<td>").append(r.getNomeMotorista()).append("</td>")
                                .append("<td>").append(r.getEntOk()).append("</td>")
                                .append("<td>").append(r.getRecOk()).append("</td>")
                                .append("</tr>");
                    }

                    relatorioBuilder.append("</tbody>")
                            .append("<tfoot>")
                            .append("<tr>")
                            .append("<td colspan=\"2\">").append(getMessageS("Total")).append("</td>")
                            .append("<td>").append(totalEntOk).append("</td>")
                            .append("<td>").append(totalRecOk).append("</td>")
                            .append("</tr>");

                    relatorioBuilder.append("</tfoot></table>").append("<hr>").append("<br>");
                    break;
            }
            this.relatorio = relatorioBuilder.toString();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public Rotas getRotaSelecionada() {
        return rotaSelecionada;
    }

    public void setRotaSelecionada(Rotas rotaSelecionada) {
        this.rotaSelecionada = rotaSelecionada;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Rotas getNovaRota() {
        return novaRota;
    }

    public void setNovaRota(Rotas novaRota) {
        this.novaRota = novaRota;
    }

    public BigDecimal getSequencia() {
        return sequencia;
    }

    public void setSequencia(BigDecimal sequencia) {
        this.sequencia = sequencia;
    }

    public BigDecimal getParada() {
        return parada;
    }

    public void setParada(BigDecimal parada) {
        this.parada = parada;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public boolean isSenha() {
        return senha;
    }

    public void setSenha(boolean senha) {
        this.senha = senha;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public boolean isSomenteAtivos() {
        return somenteAtivos;
    }

    public void setSomenteAtivos(boolean somenteAtivos) {
        this.somenteAtivos = somenteAtivos;
    }

    public List<Rt_Perc> getTrajetos() {
        return trajetos;
    }

    public void setTrajetos(List<Rt_Perc> trajetos) {
        this.trajetos = trajetos;
    }

    public String getDataRota() {
        return dataRota;
    }

    public void setDataRota(String dataRota) {
        this.dataRota = dataRota;
    }

    public boolean isExclFlag() {
        return exclFlag;
    }

    public void setExclFlag(boolean exclFlag) {
        this.exclFlag = exclFlag;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public LazyDataModel<Rotas> getRotas() {
        return rotas;
    }

    public void setRotas(LazyDataModel<Rotas> rotas) {
        this.rotas = rotas;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public MapModel getPosicaoRotas() {
        return posicaoRotas;
    }

    public void setPosicaoRotas(MapModel posicaoRotas) {
        this.posicaoRotas = posicaoRotas;
    }

    public String getCentroMapa() {
        return centroMapa;
    }

    public void setCentroMapa(String centroMapa) {
        this.centroMapa = centroMapa;
    }

    public Rt_Perc getTrajetoSelecionado() {
        return trajetoSelecionado;
    }

    public void setTrajetoSelecionado(Rt_Perc trajetoSelecionado) {
        this.trajetoSelecionado = trajetoSelecionado;
    }

    public String getHoraAtual() {
        return getDataAtual("HORA");
    }

    public void setHoraAtual(String horaAtual) {
        this.horaAtual = horaAtual;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }

    public List<Rotas> getRotasDet() {
        return rotasDet;
    }

    public void setRotasDet(List<Rotas> rotasDet) {
        this.rotasDet = rotasDet;
    }

    public int getFlagTrajeto() {
        return flagTrajeto;
    }

    public void setFlagTrajeto(int flagTrajeto) {
        this.flagTrajeto = flagTrajeto;
    }

    public Clientes getCliOri() {
        return cliOri;
    }

    public void setCliOri(Clientes cliOri) {
        this.cliOri = cliOri;
    }

    public List<Clientes> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<Clientes> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public Clientes getCliDst() {
        return cliDst;
    }

    public void setCliDst(Clientes cliDst) {
        this.cliDst = cliDst;
    }

    public List<Rt_Perc> getTrajetosSugeridos() {
        return trajetosSugeridos;
    }

    public void setTrajetosSugeridos(List<Rt_Perc> trajetosSugeridos) {
        this.trajetosSugeridos = trajetosSugeridos;
    }

    public Rt_Perc getTrajetoSugeridoSelecionado() {
        return trajetoSugeridoSelecionado;
    }

    public void setTrajetoSugeridoSelecionado(Rt_Perc trajetoSugeridoSelecionado) {
        this.trajetoSugeridoSelecionado = trajetoSugeridoSelecionado;
    }

    public List<Rt_Hist> getHistorico() {
        return historico;
    }

    public void setHistorico(List<Rt_Hist> historico) {
        this.historico = historico;
    }

    public EGtv getGuiaSelecionada() {
        return guiaSelecionada;
    }

    public void setGuiaSelecionada(EGtv guiaSelecionada) {
        this.guiaSelecionada = guiaSelecionada;
    }

    public Rt_Hist getHistoricoSelecionado() {
        return historicoSelecionado;
    }

    public void setHistoricoSelecionado(Rt_Hist historicoSelecionado) {
        this.historicoSelecionado = historicoSelecionado;
    }

    public Clientes getClienteManifest() {
        return clienteManifest;
    }

    public void setClienteManifest(Clientes clienteManifest) {
        this.clienteManifest = clienteManifest;
    }

    public List<Clientes> getClientesManifest() {
        return clientesManifest;
    }

    public void setClientesManifest(List<Clientes> clientesManifest) {
        this.clientesManifest = clientesManifest;
    }

    public EmailsEnviar getEmail() {
        return email;
    }

    public void setEmail(EmailsEnviar email) {
        this.email = email;
    }

    public int getFlagEscala() {
        return flagEscala;
    }

    public void setFlagEscala(int flagEscala) {
        this.flagEscala = flagEscala;
    }

    public List<Rotas> getRotasSelecao() {
        return rotasSelecao;
    }

    public void setRotasSelecao(List<Rotas> rotasSelecao) {
        this.rotasSelecao = rotasSelecao;
    }

    public Pessoa getMotorista() {
        return motorista;
    }

    public void setMotorista(Pessoa motorista) {
        this.motorista = motorista;
    }

    public List<Pessoa> getListaPessoa() {
        if (listaPessoa == null) {
            listaPessoa = new ArrayList<>();
            if (null != this.motorista) {
                listaPessoa.add(this.motorista);
            }
            if (null != this.chEquipe) {
                listaPessoa.add(this.chEquipe);
            }
            if (null != this.vigilante1) {
                listaPessoa.add(this.vigilante1);
            }
            if (null != this.vigilante2) {
                listaPessoa.add(this.vigilante2);
            }
            if (null != this.vigilante3) {
                listaPessoa.add(this.vigilante3);
            }
        }
        return listaPessoa;
    }

    public void setListaPessoa(List<Pessoa> listaPessoa) {
        this.listaPessoa = listaPessoa;
    }

    public Pessoa gete() {
        return chEquipe;
    }

    public void setChEquipe(Pessoa chEquipe) {
        this.chEquipe = chEquipe;
    }

    public Pessoa getVigilante1() {
        return vigilante1;
    }

    public void setVigilante1(Pessoa vigilante1) {
        this.vigilante1 = vigilante1;
    }

    public Pessoa getVigilante2() {
        return vigilante2;
    }

    public void setVigilante2(Pessoa vigilante2) {
        this.vigilante2 = vigilante2;
    }

    public Pessoa getVigilante3() {
        return vigilante3;
    }

    public void setVigilante3(Pessoa vigilante3) {
        this.vigilante3 = vigilante3;
    }

    public Escala getEscala() {
        return escala;
    }

    public void setEscala(Escala escala) {
        this.escala = escala;
    }

    public boolean isInfoChEquipe() {
        return infoChEquipe;
    }

    public void setInfoChEquipe(boolean infoChEquipe) {
        this.infoChEquipe = infoChEquipe;
    }

    public boolean isPermissaoRota() {
        return permissaoRota;
    }

    public void setPermissaoRota(boolean permissaoRota) {
        this.permissaoRota = permissaoRota;
    }

    public List<Funcion> getFolgas() {
        return folgas;
    }

    public void setFolgas(List<Funcion> folgas) {
        this.folgas = folgas;
    }

    public List<Veiculos> getVeiculos() {
        return veiculos;
    }

    public void setVeiculos(List<Veiculos> veiculos) {
        this.veiculos = veiculos;
    }

    public Veiculos getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(Veiculos veiculo) {
        this.veiculo = veiculo;
    }

    public String getMsgVeiculo() {
        return msgVeiculo;
    }

    public void setMsgVeiculo(String msgVeiculo) {
        this.msgVeiculo = msgVeiculo;
    }

    public List<CxFGuiasVol> getContainers() {
        return containers;
    }

    public void setContainers(List<CxFGuiasVol> containers) {
        this.containers = containers;
    }

    public List<CxFGuiasVol> getContainersFiltrado() {
        return containersFiltrado;
    }

    public void setContainersFiltrado(List<CxFGuiasVol> containersFiltrado) {
        this.containersFiltrado = containersFiltrado;
    }

    public CxFGuiasVol getContainerSelecionado() {
        return containerSelecionado;
    }

    public void setContainerSelecionado(CxFGuiasVol containerSelecionado) {
        this.containerSelecionado = containerSelecionado;
    }

    public List<MovimentacaoContainer> getHistoricoMovimentacao() {
        return historicoMovimentacao;
    }

    public void setHistoricoMovimentacao(List<MovimentacaoContainer> historicoMovimentacao) {
        this.historicoMovimentacao = historicoMovimentacao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public List<EGtv> getGuias() {
        return guias;
    }

    public void setGuias(List<EGtv> guias) {
        this.guias = guias;
    }

    public String getDataContainer1() {
        return dataContainer1;
    }

    public void setDataContainer1(String dataContainer1) {
        this.dataContainer1 = dataContainer1;
    }

    public String getDataContainer2() {
        return dataContainer2;
    }

    public void setDataContainer2(String dataContainer2) {
        this.dataContainer2 = dataContainer2;
    }

    public StreamedContent getArquivoDownload() {
        return arquivoDownload;
    }

    public void setArquivoDownload(StreamedContent arquivoDownload) {
        this.arquivoDownload = arquivoDownload;
    }

    public Rastrear getPosicaoSelecionada() {
        return posicaoSelecionada;
    }

    public void setPosicaoSelecionada(Rastrear posicaoSelecionada) {
        this.posicaoSelecionada = posicaoSelecionada;
    }

    public CtrOperEquip getPosicaoContainer() {
        return posicaoContainer;
    }

    public void setPosicaoContainer(CtrOperEquip posicaoContainer) {
        this.posicaoContainer = posicaoContainer;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getPesquisaRota() {
        return pesquisaRota;
    }

    public void setPesquisaRota(String pesquisaRota) {
        this.pesquisaRota = pesquisaRota;
    }

    public String getDirections() {
        return directions;
    }

    public void setDirections(String directions) {
        this.directions = directions;
    }

    public String getDirectionsTrajetoAtual() {
        return directionsTrajetoAtual;
    }

    public void setDirectionsTrajetoAtual(String directionsTrajetoAtual) {
        this.directionsTrajetoAtual = directionsTrajetoAtual;
    }

    public String getDirectionsTrajetoFuturo() {
        return directionsTrajetoFuturo;
    }

    public void setDirectionsTrajetoFuturo(String directionsTrajetoFuturo) {
        this.directionsTrajetoFuturo = directionsTrajetoFuturo;
    }

    public String getDirectionsTrajetoProximo() {
        return directionsTrajetoProximo;
    }

    public void setDirectionsTrajetoProximo(String directionsTrajetoProximo) {
        this.directionsTrajetoProximo = directionsTrajetoProximo;
    }

    public String getCentro() {
        return centro;
    }

    public void setCentro(String centro) {
        this.centro = centro;
    }

    public String getMarkers() {
        return markers;
    }

    public void setMarkers(String markers) {
        this.markers = markers;
    }

    public String getInfoWindows() {
        return infoWindows;
    }

    public void setInfoWindows(String infoWindows) {
        this.infoWindows = infoWindows;
    }

    public String getLegend() {
        return legend;
    }

    public void setLegend(String legend) {
        this.legend = legend;
    }

    public String getContentStrings() {
        return contentStrings;
    }

    public void setContentStrings(String contentStrings) {
        this.contentStrings = contentStrings;
    }

    public String getTipoMapa() {
        return tipoMapa;
    }

    public void setTipoMapa(String tipoMapa) {
        this.tipoMapa = tipoMapa;
    }

    public String getRota() {
        return rota;
    }

    public void setRota(String rota) {
        this.rota = rota;
    }

    public List<Rt_Perc> getPosicoesClientes() {
        return posicoesClientes;
    }

    public void setPosicoesClientes(List<Rt_Perc> posicoesClientes) {
        this.posicoesClientes = posicoesClientes;
    }

    public List<Rt_Perc> getPosicoesClientesMapa() {
        return posicoesClientesMapa;
    }

    public void setPosicoesClientesMapa(List<Rt_Perc> posicoesClientesMapa) {
        this.posicoesClientesMapa = posicoesClientesMapa;
    }

    public String getHorarioMapa() {
        return horarioMapa;
    }

    public void setHorarioMapa(String horarioMapa) {
        this.horarioMapa = horarioMapa;
    }

    public List<Rotas> getRotasImportadas() {
        return rotasImportadas;
    }

    public void setRotasImportadas(List<Rotas> rotasImportadas) {
        this.rotasImportadas = rotasImportadas;
    }

    public List<Rastrear> getPosicoesRastreador() {
        return posicoesRastreador;
    }

    public void setPosicoesRastreador(List<Rastrear> posicoesRastreador) {
        this.posicoesRastreador = posicoesRastreador;
    }

    public List<Rastrear> getPosicoesRastreadorMapa() {
        return posicoesRastreadorMapa;
    }

    public void setPosicoesRastreadorMapa(List<Rastrear> posicoesRastreadorMapa) {
        this.posicoesRastreadorMapa = posicoesRastreadorMapa;
    }

    public String getSobrePosicaoVariaveis() {
        return sobrePosicaoVariaveis;
    }

    public void setSobrePosicaoVariaveis(String sobrePosicaoVariaveis) {
        this.sobrePosicaoVariaveis = sobrePosicaoVariaveis;
    }

    public String getSobrePosicaoDirectionsService() {
        return sobrePosicaoDirectionsService;
    }

    public void setSobrePosicaoDirectionsService(String sobrePosicaoDirectionsService) {
        this.sobrePosicaoDirectionsService = sobrePosicaoDirectionsService;
    }

    public String getSobrePosicaoDirectionsDisplaySet() {
        return sobrePosicaoDirectionsDisplaySet;
    }

    public void setSobrePosicaoDirectionsDisplaySet(String sobrePosicaoDirectionsDisplaySet) {
        this.sobrePosicaoDirectionsDisplaySet = sobrePosicaoDirectionsDisplaySet;
    }

    public String getSobrePosicaoDirectionsCalculateDisplay() {
        return sobrePosicaoDirectionsCalculateDisplay;
    }

    public void setSobrePosicaoDirectionsCalculateDisplay(String sobrePosicaoDirectionsCalculateDisplay) {
        this.sobrePosicaoDirectionsCalculateDisplay = sobrePosicaoDirectionsCalculateDisplay;
    }

    public String getSobrePosicaoSwitch() {
        return sobrePosicaoSwitch;
    }

    public void setSobrePosicaoSwitch(String sobrePosicaoSwitch) {
        this.sobrePosicaoSwitch = sobrePosicaoSwitch;
    }

    public String getCarregamentoSerie() {
        return carregamentoSerie;
    }

    public void setCarregamentoSerie(String carregamentoSerie) {
        this.carregamentoSerie = carregamentoSerie;
    }

    public String getCarregamentoGuia() {
        return carregamentoGuia;
    }

    public void setCarregamentoGuia(String carregamentoGuia) {
        this.carregamentoGuia = carregamentoGuia;
    }

    public List<Rt_Perc> getListaPedidos() {
        return listaPedidos;
    }

    public void setListaPedidos(List<Rt_Perc> listaPedidos) {
        this.listaPedidos = listaPedidos;
    }

    public String getExtPersistencia() {
        return extPersistencia;
    }

    public void setExtPersistencia(String extPersistencia) {
        this.extPersistencia = extPersistencia;
    }

    public String getCodNivel() {
        return codNivel;
    }

    public void setCodNivel(String codNivel) {
        this.codNivel = codNivel;
    }

    public String getChefeEquipe() {
        return chefeEquipe;
    }

    public void setChefeEquipe(String chefeEquipe) {
        this.chefeEquipe = chefeEquipe;
    }

    public String getUltimaPosicao() {
        return ultimaPosicao;
    }

    public void setUltimaPosicao(String ultimaPosicao) {
        this.ultimaPosicao = ultimaPosicao;
    }

    public String getUltimaPosicaoData() {
        return ultimaPosicaoData;
    }

    public void setUltimaPosicaoData(String ultimaPosicaoData) {
        this.ultimaPosicaoData = ultimaPosicaoData;
    }

    public Pessoa getChEquipe() {
        return chEquipe;
    }

    public List<Rt_Modelo> getListaRotaModelos() {
        return listaRotaModelos;
    }

    public void setListaRotaModelos(List<Rt_Modelo> listaRotaModelos) {
        this.listaRotaModelos = listaRotaModelos;
    }

    public Rt_Modelo getRotaModeloSelecionada() {
        return rotaModeloSelecionada;
    }

    public void setRotaModeloSelecionada(Rt_Modelo rotaModeloSelecionada) {
        this.rotaModeloSelecionada = rotaModeloSelecionada;
    }

    public String getTipoModelo() {
        return tipoModelo;
    }

    public void setTipoModelo(String tipoModelo) {
        this.tipoModelo = tipoModelo;
    }

    public List<Rt_Perc> getParadasImportadas() {
        return paradasImportadas;
    }

    public void setParadasImportadas(List<Rt_Perc> paradasImportadas) {
        this.paradasImportadas = paradasImportadas;
    }

    public String getDataModelo() {
        return dataModelo;
    }

    public void setDataModelo(String dataModelo) {
        this.dataModelo = dataModelo;
    }

    public boolean isApenasModelosAtivos() {
        return apenasModelosAtivos;
    }

    public void setApenasModelosAtivos(boolean apenasModelosAtivos) {
        this.apenasModelosAtivos = apenasModelosAtivos;
    }

    public boolean isRoteirizaAuto() {
        return roteirizaAuto;
    }

    public void setRoteirizaAuto(boolean roteirizaAuto) {
        this.roteirizaAuto = roteirizaAuto;
    }

    public int getQtdePedidos() {
        return qtdePedidos;
    }

    public void setQtdePedidos(int qtdePedidos) {
        this.qtdePedidos = qtdePedidos;
    }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public Pedido getDadosPedido() {
        return dadosPedido;
    }

    public void setDadosPedido(Pedido dadosPedido) {
        this.dadosPedido = dadosPedido;
    }

    public List<Rt_Perc> getListaRoteirizacao() {
        return listaRoteirizacao;
    }

    public void setListaRoteirizacao(List<Rt_Perc> listaRoteirizacao) {
        this.listaRoteirizacao = listaRoteirizacao;
    }

    public Pedido getRoteirizacaoSelecionado() {
        return roteirizacaoSelecionado;
    }

    public void setRoteirizacaoSelecionado(Pedido roteirizacaoSelecionado) {
        this.roteirizacaoSelecionado = roteirizacaoSelecionado;
    }

    public Rt_Perc getTrajetoSugestaoRoteirizacao() {
        return trajetoSugestaoRoteirizacao;
    }

    public void setTrajetoSugestaoRoteirizacao(Rt_Perc trajetoSugestaoRoteirizacao) {
        this.trajetoSugestaoRoteirizacao = trajetoSugestaoRoteirizacao;
    }

    public String getPinMapaPedido() {
        return pinMapaPedido;
    }

    public void setPinMapaPedido(String pinMapaPedido) {
        this.pinMapaPedido = pinMapaPedido;
    }

    public String getTipoCarregamentoSugestao() {
        return tipoCarregamentoSugestao;
    }

    public void setTipoCarregamentoSugestao(String tipoCarregamentoSugestao) {
        this.tipoCarregamentoSugestao = tipoCarregamentoSugestao;
    }

    public Map getTiposRelatorios() {
        return tiposRelatorios;
    }

    public void setTiposRelatorios(Map tiposRelatorios) {
        this.tiposRelatorios = tiposRelatorios;
    }

    public TiposRelatorio getRelatorioSelecionado() {
        return relatorioSelecionado;
    }

    public void setRelatorioSelecionado(TiposRelatorio relatorioSelecionado) {
        this.relatorioSelecionado = relatorioSelecionado;
    }

    public String getNomeNovoModelo() {
        return nomeNovoModelo;
    }

    public void setNomeNovoModelo(String nomeNovoModelo) {
        this.nomeNovoModelo = nomeNovoModelo;
    }

    public int getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(int tolerancia) {
        this.tolerancia = tolerancia;
    }

    public List<Clientes> getListaClientesDestino() {
        return listaClientesDestino;
    }

    public void setListaClientesDestino(List<Clientes> listaClientesDestino) {
        this.listaClientesDestino = listaClientesDestino;
    }

    public int getQtdeRotasMapa() {
        return qtdeRotasMapa;
    }

    public void setQtdeRotasMapa(int qtdeRotasMapa) {
        this.qtdeRotasMapa = qtdeRotasMapa;
    }

    public String getCodCliReaproveitaOrigem() {
        return codCliReaproveitaOrigem;
    }

    public void setCodCliReaproveitaOrigem(String codCliReaproveitaOrigem) {
        this.codCliReaproveitaOrigem = codCliReaproveitaOrigem;
    }

    public String getCodCliReaproveitaDestino() {
        return codCliReaproveitaDestino;
    }

    public void setCodCliReaproveitaDestino(String codCliReaproveitaDestino) {
        this.codCliReaproveitaDestino = codCliReaproveitaDestino;
    }

    public String getDataModeloFinal() {
        return dataModeloFinal;
    }

    public void setDataModeloFinal(String dataModeloFinal) {
        this.dataModeloFinal = dataModeloFinal;
    }

    public boolean isRelatorioDuasDatas() {
        return relatorioDuasDatas;
    }

    public void setRelatorioDuasDatas(boolean relatorioDuasDatas) {
        this.relatorioDuasDatas = relatorioDuasDatas;
    }
}

/*
 */
package br.com.sasw.lazydatamodels.containers;

import Controller.Clientes.ClientesSatMobWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import static br.com.sasw.utils.Messages.getMessageS;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ClientesLazyList extends LazyDataModel<Clientes> {

    private static final long serialVersionUID = 1L;
    private List<Clientes> clientes;
    private final ClientesSatMobWeb clientesmobweb;
    private final Persistencia persistencia;
    private Map filters;
    private final BigDecimal codPessoa;

    public ClientesLazyList(Persistencia pst, BigDecimal codPessoa, Map filters) {
        this.clientesmobweb = new ClientesSatMobWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
        this.filters = filters;
    }

    @Override
    public List<Clientes> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.clientes = this.clientesmobweb.listagemPaginadaCliAut(this.codPessoa, first, pageSize, this.filters, this.persistencia);

            // set the total of players
            setRowCount(this.clientesmobweb.contagemPessoaCliAut(this.filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.clientes;
    }

    @Override
    public Object getRowKey(Clientes cliente) {
        return cliente.getCodFil() + "__" + cliente.getCodigo();
    }

    @Override
    public Clientes getRowData(String codigo) {
        try {
            if (codigo.split("__").length != 2) {
                return null;
            }
            String codfil = codigo.split("__")[0];
            String codcli = codigo.split("__")[1];
            for (Clientes cliente : this.clientes) {
                if (codcli.equals(cliente.getCodigo()) && (new BigDecimal(codfil)).compareTo(cliente.getCodFil()) == 0) {
                    return cliente;
                }
            }
            return null;
        } catch (Exception e) {
            System.out.println("Codigo: " + codigo + "ERRO: " + e.getMessage());
            return null;
        }
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

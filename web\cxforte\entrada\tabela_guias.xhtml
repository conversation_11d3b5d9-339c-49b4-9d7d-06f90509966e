<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <h:form id="guiaForm" style="color: black;">
        <p:panel id="origem" header="#{localemsgs.GuiasEmCxForte}">
            <div class="row flexBottom" style="margin-top: 10px; margin-bottom: 10px;">
                <div class="col-md-1">
                    <p:outputLabel for="origemCod"
                                   value="#{localemsgs.Origem}: "/>
                </div>
                <div class="col-md-2">
                    <p:inputText id="origemCod"
                                 class="primefacesInputFix"
                                 value="#{cxForteEntrada.entradaSelecionada.codCli1}"
                                 disabled="true"/>
                </div>
                <div class="col-md-3">
                    <p:inputText id="origemNred"
                                 class="primefacesInputFix"
                                 value="#{cxForteEntrada.entradaSelecionada.NRed}"
                                 disabled="true"/>
                </div>
            </div>
        </p:panel>

        <div style="position: relative; overflow: visible;">
            <div style="overflow: hidden !important; position: relative; width: 100%; height: 90%;">
                <p:dataTable
                    id="tabela"
                    value="#{cxForteEntrada.guias}"
                    currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Guias}"
                    var="item"
                    selection="#{cxForteEntrada.guiaSelecionada}"
                    selectionMode="single"
                    rowKey="#{item.guia};#{item.serie}"
                    emptyMessage="#{localemsgs.SemRegistros}"
                    paginator="true"
                    rows="25"
                    reflow="true"
                    rowsPerPageTemplate="5,10,15,20,25,50"
                    paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                    styleClass="tabelaReformulada"
                    class="grelha tabelaReformulada tabelaGuias"
                    scrollable="true"
                    scrollWidth="100%"
                    paginatorPosition="top"
                    >
                    <p:ajax
                        event="rowDblselect"
                        listener="#{cxForteEntrada.dialogVolumesGTV()}"
                        update="msgs guiaForm:origem guiaForm:tabela"/>
                    <p:column headerText="#{localemsgs.Guia}">
                        <h:outputText value="#{item.guia}" converter="conversor0"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.Serie}">
                        <h:outputText value="#{item.serie}"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.Valor}">
                        <h:outputText value="#{item.valor}" converter="conversormoeda"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.OperEnt}">
                        <h:outputText value="#{item.operEnt}"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.DtEnt}">
                        <h:outputText value="#{item.dtEnt}" converter="conversorData"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.HrEnt}">
                        <h:outputText value="#{item.hrEnt}" converter="conversorHora"/>
                    </p:column>
                </p:dataTable>
            </div>

            <div style="position: absolute; top: 50%; right: 0; transform: translateX(33%);">
                <p:commandLink
                    action="#{cxForteEntrada.deletarGuia()}"
                    update="msgs @form main:tabela"
                    rendered="#{not empty cxForteEntrada.guias}"
                    title="#{localemsgs.Deletar}"
                    >
                    <p:graphicImage
                        url="../assets/img/icone_redondo_excluir.png"
                        width="40" height="40" />
                </p:commandLink>
            </div>
        </div>
    </h:form>

</ui:composition>
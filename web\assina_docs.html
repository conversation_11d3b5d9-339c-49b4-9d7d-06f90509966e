<!DOCTYPE html>
<html lang="pt-br">
<head>
    <link rel="icon" href="assets/img/favicon.png" />
    <title>SatMOB</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <link type="text/css" href="assets/css/bootstrap.css" rel="stylesheet" />
    <meta name="theme-color" content="#002172" />
    <meta name="msapplication-navbutton-color" content="#002172" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#002172" />

    <style>
        .wrapper {
            position: relative;
            width: 228px;
            height: 200px;
            -moz-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin-left: 4px;
            padding: 0px !important;
        }
        img {
            position: absolute;
            left: 0;
            top: 0;
        }

        .signature-pad {
            position: absolute;
            left: 0;
            top: 0;
            width: 228px;
            height: 200px;
        }
        
        a{
            width: 100%;
        }
    </style>
</head>
<body>
<div class="wrapper">
    <center>
    <img src="assets/images/ci_1.png" width=228 height=200 style="border: thin solid #CCC" />
    <canvas id="signature-pad" class="signature-pad" width=228 height=200></canvas>
    </center>
</div>
<div style="width: 231px; padding: 0px; margin-top: 10px;">
    <div class="col-md-6 col-sm-6 col-xs-6" style="padding-right: 4px;padding-left: 4px;" >
        <a id="save" href="javascript:void(0)" class="btn btn-success">Salvar</a>
    </div>
    <div class="col-md-6 col-sm-6 col-xs-6" style="padding-left: 4px;padding-right: 0px;">
        <a id="clear" href="javascript:void(0)" class="btn btn-warning">Refazer</a>
    </div>    
</div>
    
<textarea name="imageCheck" id="imageCheck" cols="30" rows="10" style="margin-top: 100px; display: none;"></textarea>

</body>
<script src="https://cdn.jsdelivr.net/npm/signature_pad@3.0.0-beta.3/dist/signature_pad.min.js"></script>
<script src="https://code.jquery.com/jquery-3.4.1.slim.min.js" integrity="sha256-pasqAKBDmFT4eHoN2ndd6lN370kFiGUFyTiUHWhU7k8=" crossorigin="anonymous"></script>

<script src="assets/scripts/south-1.0.0.js" type="text/javascript"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
<script>
    function download(dataURL, filename) {
        if (navigator.userAgent.indexOf("Safari") > -1 && navigator.userAgent.indexOf("Chrome") === -1) {
            window.open(dataURL);
        } else {
            var blob = dataURLToBlob(dataURL);
            var url = window.URL.createObjectURL(blob);

            var a = document.createElement("a");
            a.style = "display: none";
            a.href = url;
            a.download = filename;

            document.body.appendChild(a);
            a.click();

            window.URL.revokeObjectURL(url);
        }
    }

    function dataURLToBlob(dataURL) {
        // Code taken from https://github.com/ebidel/filer.js
        var parts = dataURL.split(';base64,');
        var contentType = parts[0].split(":")[1];
        var raw = window.atob(parts[1]);
        var rawLength = raw.length;
        var uInt8Array = new Uint8Array(rawLength);

        for (var i = 0; i < rawLength; ++i) {
            uInt8Array[i] = raw.charCodeAt(i);
        }

        return new Blob([uInt8Array], { type: contentType });
    }


    var signaturePad = new SignaturePad(document.getElementById('signature-pad'), {
        backgroundColor: 'rgba(255, 255, 255, 0)',
        penColor: 'rgb(0, 0, 0)'
    });


    var saveButton = document.getElementById('save');
    var cancelButton = document.getElementById('clear');

    saveButton.addEventListener("click", function(event) {
        if (signaturePad.isEmpty()) {
            $.alert({
            icon: 'fa fa-info-circle fa-lg',
            theme: 'modern',
            type: 'blue',
            title: 'Aten&ccedil;&atilde;o',
            content: 'Escreva sua Assinatura',
            buttons: {
                ok: {
                    text: 'Ok',
                    btnClass: 'btn-blue'
                }
            }
        });
        } else {
            var dataURL = signaturePad.toDataURL();
            //download(dataURL, "signature.png");
            //alert(dataURL);
            $("#imageCheck").val(dataURL);
            window.parent.AssinaturaCapturada($("#imageCheck").val());
        }
    });

    cancelButton.addEventListener('click', function(event) {
        signaturePad.clear();
    });

</script>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class S2399 {

    private int sucesso;
    private String evtTSVTermino_Id;

    public String getEvtTSVTermino_Id() {
        return evtTSVTermino_Id;
    }

    public void setEvtTSVTermino_Id(String evtTSVTermino_Id) {
        this.evtTSVTermino_Id = evtTSVTermino_Id;
    }
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideTrabSemVinculo_cpfTrab;
    private String ideTrabSemVinculo_nisTrab;
    private String ideTrabSemVinculo_matricula;
    private String ideTrabSemVinculo_codCateg;
    private String infoTSVTermino_dtTerm;
    private String infoTSVTermino_mtvDesligTSV;
    private String dmDev_ideDmDev;
    private String ideEstabLot_tpInsc;
    private String ideEstabLot_nrInsc;
    private String ideEstabLot_codLotacao;
//    private String detVerbas_codRubr;
//    private String detVerbas_ideTabRubr;
//    private String detVerbas_qtdRubr;
//    private String detVerbas_fatorRubr;
//    private String detVerbas_vrUnit;
//    private String detVerbas_vrRubr;
    private String cnpjOper;
    private String detOper_regANS;
    private String detOper_vrPgTit;
    private String detPlano_tpDep;
    private String detPlano_cpfDep;
    private String detPlano_nmDep;
    private String detPlano_dtNascto;
    private String detPlano_vlrPgDep;
    private String infoAgNocivo_grauExp;
    private String infoSimples_indSimples;
    private String procJudTrab_tpTrib;
    private String procJudTrab_nrProcJud;
    private String procJudTrab_codSusp;
    private String infoMV_indMV;
    private String remunOutrEmpr_tpInsc;
    private String remunOutrEmpr_nrInsc;
    private String remunOutrEmpr_codCateg;
    private String remunOutrEmpr_vlrRemunOE;
    private String quarentena_dtFimQuar;
    private String ideEvento_compet;

    public String getIdeEvento_compet() {
        return ideEvento_compet;
    }

    public void setIdeEvento_compet(String ideEvento_compet) {
        this.ideEvento_compet = ideEvento_compet;
    }
    private InfoPerApur infoPerApur_ideEstabLot;

    public InfoPerApur getInfoPerApur_ideEstabLot() {
        return infoPerApur_ideEstabLot;
    }

    public void setInfoPerApur_ideEstabLot(InfoPerApur infoPerApur_ideEstabLot) {
        this.infoPerApur_ideEstabLot = infoPerApur_ideEstabLot;
    }

    public static class InfoPerApur {

        private List<DetVerbas> ideEstabLot_detVerbas;
        private List<DetOper> infoSaudeColet_detOper;

        public List<DetVerbas> getIdeEstabLot_detVerbas() {
            return ideEstabLot_detVerbas;
        }

        public void setIdeEstabLot_detVerbas(List<DetVerbas> ideEstabLot_detVerbas) {
            this.ideEstabLot_detVerbas = ideEstabLot_detVerbas;
        }

        public List<DetOper> getInfoSaudeColet_detOper() {
            return infoSaudeColet_detOper;
        }

        public void setInfoSaudeColet_detOper(List<DetOper> infoSaudeColet_detOper) {
            this.infoSaudeColet_detOper = infoSaudeColet_detOper;
        }
    }
//private S2299.InfoPerApur infoPerApur_ideEstabLot;

    public static class DetVerbas {

        private String detVerbas_codRubr;
        private String detVerbas_ideTabRubr;
        private String detVerbas_qtdRubr;
        private String detVerbas_vrUnit;
        private String detVerbas_vrRubr;

        public String getDetVerbas_codRubr() {
            return detVerbas_codRubr == null ? "" : detVerbas_codRubr;
        }

        public void setDetVerbas_codRubr(String detVerbas_codRubr) {
            this.detVerbas_codRubr = detVerbas_codRubr;
        }

        public String getDetVerbas_ideTabRubr() {
            return detVerbas_ideTabRubr == null ? "" : detVerbas_ideTabRubr;
        }

        public void setDetVerbas_ideTabRubr(String detVerbas_ideTabRubr) {
            this.detVerbas_ideTabRubr = detVerbas_ideTabRubr;
        }

        public String getDetVerbas_qtdRubr() {
            return detVerbas_qtdRubr == null ? "" : detVerbas_qtdRubr;
        }

        public void setDetVerbas_qtdRubr(String detVerbas_qtdRubr) {
            this.detVerbas_qtdRubr = detVerbas_qtdRubr;
        }

        public String getDetVerbas_vrUnit() {
            return detVerbas_vrUnit == null ? "" : detVerbas_vrUnit;
        }

        public void setDetVerbas_vrUnit(String detVerbas_vrUnit) {
            this.detVerbas_vrUnit = detVerbas_vrUnit;
        }

        public String getDetVerbas_vrRubr() {
            return detVerbas_vrRubr == null ? "" : detVerbas_vrRubr;
        }

        public void setDetVerbas_vrRubr(String detVerbas_vrRubr) {
            this.detVerbas_vrRubr = detVerbas_vrRubr;
        }
    }

    public static class DetPlano {

        private String detPlano_tpDep;
        private String detPlano_cpfDep;
        private String detPlano_nmDep;
        private String detPlano_dtNascto;
        private String detPlano_vlrPgDep;

        public String getDetPlano_tpDep() {
            return detPlano_tpDep == null ? "" : detPlano_tpDep;
        }

        public void setDetPlano_tpDep(String detPlano_tpDep) {
            this.detPlano_tpDep = detPlano_tpDep;
        }

        public String getDetPlano_cpfDep() {
            return detPlano_cpfDep == null ? "" : detPlano_cpfDep;
        }

        public void setDetPlano_cpfDep(String detPlano_cpfDep) {
            this.detPlano_cpfDep = detPlano_cpfDep;
        }

        public String getDetPlano_nmDep() {
            return detPlano_nmDep == null ? "" : detPlano_nmDep;
        }

        public void setDetPlano_nmDep(String detPlano_nmDep) {
            this.detPlano_nmDep = detPlano_nmDep;
        }

        public String getDetPlano_dtNascto() {
            return detPlano_dtNascto == null ? "" : detPlano_dtNascto;
        }

        public void setDetPlano_dtNascto(String detPlano_dtNascto) {
            this.detPlano_dtNascto = detPlano_dtNascto;
        }

        public String getDetPlano_vlrPgDep() {
            return detPlano_vlrPgDep == null ? "" : detPlano_vlrPgDep;
        }

        public void setDetPlano_vlrPgDep(String detPlano_vlrPgDep) {
            this.detPlano_vlrPgDep = detPlano_vlrPgDep;
        }
    }

    public static class DetOper {

        private String detOper_cnpjOper;
        private String detOper_regANS;
        private String detOper_vrPgTit;
        private List<DetPlano> detOper_detPlano;

        public String getDetOper_cnpjOper() {
            return detOper_cnpjOper == null ? "" : detOper_cnpjOper;
        }

        public void setDetOper_cnpjOper(String detOper_cnpjOper) {
            this.detOper_cnpjOper = detOper_cnpjOper;
        }

        public String getDetOper_regANS() {
            return detOper_regANS == null ? "" : detOper_regANS;
        }

        public void setDetOper_regANS(String detOper_regANS) {
            this.detOper_regANS = detOper_regANS;
        }

        public String getDetOper_vrPgTit() {
            return detOper_vrPgTit == null ? "" : detOper_vrPgTit;
        }

        public void setDetOper_vrPgTit(String detOper_vrPgTit) {
            this.detOper_vrPgTit = detOper_vrPgTit;
        }

        public List<DetPlano> getDetOper_detPlano() {
            return detOper_detPlano;
        }

        public void setDetOper_detPlano(List<DetPlano> detOper_detPlano) {
            this.detOper_detPlano = detOper_detPlano;
        }

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 97 * hash + Objects.hashCode(this.detOper_regANS);
            hash = 97 * hash + Objects.hashCode(this.detOper_vrPgTit);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final DetOper other = (DetOper) obj;
            if (!Objects.equals(this.detOper_regANS, other.detOper_regANS)) {
                return false;
            }
            return true;
        }
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeTrabSemVinculo_cpfTrab() {
        return ideTrabSemVinculo_cpfTrab;
    }

    public void setIdeTrabSemVinculo_cpfTrab(String ideTrabSemVinculo_cpfTrab) {
        this.ideTrabSemVinculo_cpfTrab = ideTrabSemVinculo_cpfTrab;
    }

    public String getIdeTrabSemVinculo_nisTrab() {
        return ideTrabSemVinculo_nisTrab;
    }

    public void setIdeTrabSemVinculo_nisTrab(String ideTrabSemVinculo_nisTrab) {
        this.ideTrabSemVinculo_nisTrab = ideTrabSemVinculo_nisTrab;
    }

    public String getIdeTrabSemVinculo_codCateg() {
        return ideTrabSemVinculo_codCateg;
    }

    public void setIdeTrabSemVinculo_codCateg(String ideTrabSemVinculo_codCateg) {
        this.ideTrabSemVinculo_codCateg = ideTrabSemVinculo_codCateg;
    }

    public String getInfoTSVTermino_dtTerm() {
        return infoTSVTermino_dtTerm;
    }

    public void setInfoTSVTermino_dtTerm(String infoTSVTermino_dtTerm) {
        this.infoTSVTermino_dtTerm = infoTSVTermino_dtTerm;
    }

    public String getInfoTSVTermino_mtvDesligTSV() {
        return infoTSVTermino_mtvDesligTSV;
    }

    public void setInfoTSVTermino_mtvDesligTSV(String infoTSVTermino_mtvDesligTSV) {
        this.infoTSVTermino_mtvDesligTSV = infoTSVTermino_mtvDesligTSV;
    }

    public String getDmDev_ideDmDev() {
        return dmDev_ideDmDev == null ? "" : dmDev_ideDmDev;
    }

    public void setDmDev_ideDmDev(String dmDev_ideDmDev) {
        this.dmDev_ideDmDev = dmDev_ideDmDev;
    }

    public String getIdeEstabLot_tpInsc() {
        return ideEstabLot_tpInsc;
    }

    public void setIdeEstabLot_tpInsc(String ideEstabLot_tpInsc) {
        this.ideEstabLot_tpInsc = ideEstabLot_tpInsc;
    }

    public String getIdeEstabLot_nrInsc() {
        return ideEstabLot_nrInsc;
    }

    public void setIdeEstabLot_nrInsc(String ideEstabLot_nrInsc) {
        this.ideEstabLot_nrInsc = ideEstabLot_nrInsc;
    }

    public String getIdeEstabLot_codLotacao() {
        return ideEstabLot_codLotacao;
    }

    public void setIdeEstabLot_codLotacao(String ideEstabLot_codLotacao) {
        this.ideEstabLot_codLotacao = ideEstabLot_codLotacao;
    }

//    public String getDetVerbas_codRubr() {
//        return detVerbas_codRubr;
//    }
//
//    public void setDetVerbas_codRubr(String detVerbas_codRubr) {
//        this.detVerbas_codRubr = detVerbas_codRubr;
//    }
//
//    public String getDetVerbas_ideTabRubr() {
//        return detVerbas_ideTabRubr;
//    }
//
//    public void setDetVerbas_ideTabRubr(String detVerbas_ideTabRubr) {
//        this.detVerbas_ideTabRubr = detVerbas_ideTabRubr;
//    }
//
//    public String getDetVerbas_qtdRubr() {
//        return detVerbas_qtdRubr;
//    }
//
//    public void setDetVerbas_qtdRubr(String detVerbas_qtdRubr) {
//        this.detVerbas_qtdRubr = detVerbas_qtdRubr;
//    }
//
//    public String getDetVerbas_fatorRubr() {
//        return detVerbas_fatorRubr;
//    }
//
//    public void setDetVerbas_fatorRubr(String detVerbas_fatorRubr) {
//        this.detVerbas_fatorRubr = detVerbas_fatorRubr;
//    }
//
//    public String getDetVerbas_vrUnit() {
//        return detVerbas_vrUnit;
//    }
//
//    public void setDetVerbas_vrUnit(String detVerbas_vrUnit) {
//        this.detVerbas_vrUnit = detVerbas_vrUnit;
//    }
//
//    public String getDetVerbas_vrRubr() {
//        return detVerbas_vrRubr;
//    }
//
//    public void setDetVerbas_vrRubr(String detVerbas_vrRubr) {
//        this.detVerbas_vrRubr = detVerbas_vrRubr;
//    }
    public String getCnpjOper() {
        return cnpjOper;
    }

    public void setCnpjOper(String cnpjOper) {
        this.cnpjOper = cnpjOper;
    }

    public String getDetOper_regANS() {
        return detOper_regANS;
    }

    public void setDetOper_regANS(String detOper_regANS) {
        this.detOper_regANS = detOper_regANS;
    }

    public String getDetOper_vrPgTit() {
        return detOper_vrPgTit;
    }

    public void setDetOper_vrPgTit(String detOper_vrPgTit) {
        this.detOper_vrPgTit = detOper_vrPgTit;
    }

    public String getDetPlano_tpDep() {
        return detPlano_tpDep;
    }

    public void setDetPlano_tpDep(String detPlano_tpDep) {
        this.detPlano_tpDep = detPlano_tpDep;
    }

    public String getDetPlano_cpfDep() {
        return detPlano_cpfDep;
    }

    public void setDetPlano_cpfDep(String detPlano_cpfDep) {
        this.detPlano_cpfDep = detPlano_cpfDep;
    }

    public String getDetPlano_nmDep() {
        return detPlano_nmDep;
    }

    public void setDetPlano_nmDep(String detPlano_nmDep) {
        this.detPlano_nmDep = detPlano_nmDep;
    }

    public String getDetPlano_dtNascto() {
        return detPlano_dtNascto;
    }

    public void setDetPlano_dtNascto(String detPlano_dtNascto) {
        this.detPlano_dtNascto = detPlano_dtNascto;
    }

    public String getDetPlano_vlrPgDep() {
        return detPlano_vlrPgDep;
    }

    public void setDetPlano_vlrPgDep(String detPlano_vlrPgDep) {
        this.detPlano_vlrPgDep = detPlano_vlrPgDep;
    }

    public String getInfoAgNocivo_grauExp() {
        return infoAgNocivo_grauExp;
    }

    public void setInfoAgNocivo_grauExp(String infoAgNocivo_grauExp) {
        this.infoAgNocivo_grauExp = infoAgNocivo_grauExp;
    }

    public String getInfoSimples_indSimples() {
        return infoSimples_indSimples;
    }

    public void setInfoSimples_indSimples(String infoSimples_indSimples) {
        this.infoSimples_indSimples = infoSimples_indSimples;
    }

    public String getProcJudTrab_tpTrib() {
        return procJudTrab_tpTrib;
    }

    public void setProcJudTrab_tpTrib(String procJudTrab_tpTrib) {
        this.procJudTrab_tpTrib = procJudTrab_tpTrib;
    }

    public String getProcJudTrab_nrProcJud() {
        return procJudTrab_nrProcJud;
    }

    public void setProcJudTrab_nrProcJud(String procJudTrab_nrProcJud) {
        this.procJudTrab_nrProcJud = procJudTrab_nrProcJud;
    }

    public String getProcJudTrab_codSusp() {
        return procJudTrab_codSusp;
    }

    public void setProcJudTrab_codSusp(String procJudTrab_codSusp) {
        this.procJudTrab_codSusp = procJudTrab_codSusp;
    }

    public String getInfoMV_indMV() {
        return infoMV_indMV;
    }

    public void setInfoMV_indMV(String infoMV_indMV) {
        this.infoMV_indMV = infoMV_indMV;
    }

    public String getRemunOutrEmpr_tpInsc() {
        return remunOutrEmpr_tpInsc;
    }

    public void setRemunOutrEmpr_tpInsc(String remunOutrEmpr_tpInsc) {
        this.remunOutrEmpr_tpInsc = remunOutrEmpr_tpInsc;
    }

    public String getRemunOutrEmpr_nrInsc() {
        return remunOutrEmpr_nrInsc;
    }

    public void setRemunOutrEmpr_nrInsc(String remunOutrEmpr_nrInsc) {
        this.remunOutrEmpr_nrInsc = remunOutrEmpr_nrInsc;
    }

    public String getRemunOutrEmpr_codCateg() {
        return remunOutrEmpr_codCateg;
    }

    public void setRemunOutrEmpr_codCateg(String remunOutrEmpr_codCateg) {
        this.remunOutrEmpr_codCateg = remunOutrEmpr_codCateg;
    }

    public String getRemunOutrEmpr_vlrRemunOE() {
        return remunOutrEmpr_vlrRemunOE;
    }

    public void setRemunOutrEmpr_vlrRemunOE(String remunOutrEmpr_vlrRemunOE) {
        this.remunOutrEmpr_vlrRemunOE = remunOutrEmpr_vlrRemunOE;
    }

    public String getQuarentena_dtFimQuar() {
        return quarentena_dtFimQuar;
    }

    public void setQuarentena_dtFimQuar(String quarentena_dtFimQuar) {
        this.quarentena_dtFimQuar = quarentena_dtFimQuar;
    }

    public String getIdeTrabSemVinculo_matricula() {
        return ideTrabSemVinculo_matricula;
    }

    public void setIdeTrabSemVinculo_matricula(String ideTrabSemVinculo_matricula) {
        this.ideTrabSemVinculo_matricula = ideTrabSemVinculo_matricula;
    }

}

package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CxFGuiasVol {

    private String CodFil;
    private String CodCli;
    private String Guia;
    private String Serie;
    private String Ordem;
    private String Qtde;
    private String Lacre;
    private String Tipo;
    private BigDecimal Valor;
    private String Obs;

    public CxFGuiasVol() {
        this.CodFil = "0";
        this.CodCli = "";
        this.Guia = "0";
        this.Serie = "";
        this.Ordem = "0";
        this.Qtde = "0";
        this.Lacre = "";
        this.Tipo = "";
        this.Valor = new BigDecimal("0");
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getOrdem() {
        return Ordem;
    }

    public void setOrdem(String Ordem) {
        this.Ordem = Ordem;
    }

    public String getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        this.Qtde = Qtde;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CxFGuiasVol other = (CxFGuiasVol) obj;
        if (!Objects.equals(this.Lacre, other.Lacre)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        return true;
    }
}

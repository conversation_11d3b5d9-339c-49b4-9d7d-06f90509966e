/*
 */
package br.com.sasw.managedBeans;

import Arquivo.ArquivoLog;
import Controller.ContraCheque.ContraChequeSatWeb;
import Controller.Filiais.FiliaisSatMobWeb;
import Controller.Funcion.FuncionSatMobWeb;
import Controller.Login.LoginSatMobWeb;
import Controller.PortalRH.PortalRHSatMobWeb;
import Dados.OLD.Persistencia_OLD;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.AvisoPortal;
import SasBeans.FPMensal;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.GTVeAcesso;
import SasBeans.Paramet;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaLogin;
import SasBeans.PessoaPortalSrv;
import SasBeans.PstServ;
import SasBeans.Rotas;
import SasBeans.SasPWFill;
import SasBeans.Saspwac;
import SasBeans.TesWEBRelat;
import SasBeansCompostas.ContraCheque;
import SasBeansCompostas.Login;
import SasBeansCompostas.SaspwacSysdef;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasBeansCompostas.UsuarioSatMobWebServicos;
import SasDaos.AcessosDao;
import SasDaos.LoginDao;
import SasDaos.ParametDao;
import SasDaos.PessoaCliAutDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.PessoaPortalSrvDao;
import SasDaos.RotasDao;
import SasDaos.TesWEBRelatDao;
import br.com.sasw.arquivos.PDF;
import br.com.sasw.pacotesuteis.controller.acessos.AcessosSatMobWeb;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.KMPrestador;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.LerArquivo;
import br.com.sasw.utils.LocaleController;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.enterprise.context.SessionScoped;
import javax.faces.application.FacesMessage;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.inject.Named;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.omnifaces.util.Faces;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.primefaces.model.menu.DefaultMenuItem;
import org.primefaces.model.menu.DefaultSubMenu;
import org.primefaces.model.menu.DynamicMenuModel;
import org.primefaces.model.menu.MenuModel;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@Named(value = "login")
@SessionScoped
public class LoginMB implements Serializable {

    private final SasPoolPersistencia pool;
    private static SasPoolPersistencia poolStatic;
    private String email, pwweb, senhaDia, origem, caminho, log, nivel, ano, matricula, param,
            validacao1, validacao2, validacao3, validadorCC, senhaEsquecida, cli, dataTela,
            webPontoDescricao, webPontoLocal, webPontoEscala, webPontoEnde, webPontoMatr, webPontoFuncao, webPontoNomeGuer, webPontoCodFil, webPontoSecao, webPontoCodPessoa, webPontoPw, webPontoImagem, webPontoLogo, googleApiMob, googleApiOper,
            loginSatDesktopData, loginSatDesktopBase, loginSatDesktopCodPessoa, loginSatDesktopSeqRota, portalLogin, portalEmail, portalSenha, tipoLogin;
    private final ArquivoLog logerro;
    private List<PessoaLogin> empresas;
    private List<PessoaPortalSrv> servicos;
    private final PessoaPortalSrvDao pessoaPortalSrvDao;
    private PessoaLogin empresa;
    private List<SasPWFill> filiais, listaFiliais;
    private Filiais infoFilial;
    private SasPWFill filial;
    private List<Saspwac> permissoes;
    private List<PstServ> postos, postosFiltrados;
    private PstServ postoSelecionado;
    private PessoaCliAut acesso, selecionado;
    private String codFil, enderecoNavegador;
    private BigDecimal codgrupo;
    private Pessoa usuario, primeiroAcesso;
    private int rand;
    private List<PessoaCliAut> clientes, clientesFiltrados;
    private Boolean verTodos, naoPossuoSenhaDia, exibirEmpresas, acessoPorLogin, Permissao10207, Permissao10221, Permissao10311, Permissao10201, Permissao10211, Permissao10101, utilizaGTVe, transpCacamba, firmaGTV;
    private Persistencia pp, satellite;
    private Persistencia_OLD satellite_old;
    private static Persistencia satelliteStatic;
    private AvisoPortal avisoportal;
    private final LoginSatMobWeb login;
    private final AcessosSatMobWeb permissao;
    private static String CaminhoStatic;
    private UsuarioSatMobWeb funcion;
    private final PortalRHSatMobWeb portalrh;
    private final FuncionSatMobWeb funcionsatmobweb;
    private final FiliaisSatMobWeb filiaissatmobweb;
    private FPMensal fp;
    private MenuModel menu;
    private UsuarioSatMobWebServicos usuarios;
    private PessoaPortalSrv pessoaPortalSrv;
    private LocaleController locale;
    private GTVeAcesso gtveAcesso;
    private String nomeArq, token, empresaBD;

    private UploadedFile uploadedFile;

    public LoginMB() {
        permissao = new AcessosSatMobWeb();
        acesso = new PessoaCliAut();
        login = new LoginSatMobWeb();
        empresas = new ArrayList<>();
        filiais = new ArrayList<>();
        pool = new SasPoolPersistencia();
        poolStatic = new SasPoolPersistencia();
//        pool.setTamanhoPool(20);
        pool.setCaminho(this.getClass().getResource("mapconect_dev.txt").getPath().replace("%20", " "));
        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("CaminhoMap", this.getClass().getResource("mapconect_dev.txt").getPath().replace("%20", " "));
        poolStatic.setCaminho(this.getClass().getResource("mapconect_dev.txt").getPath().replace("%20", " "));
        CaminhoStatic = this.getClass().getResource("mapconect_dev.txt").getPath().replace("%20", " ");
        FacesContext fc = FacesContext.getCurrentInstance();
        locale = fc.getApplication().evaluateExpressionGet(fc, "#{localeController}", LocaleController.class);
        email = null;
        pwweb = null;
        verTodos = false;
        portalrh = new PortalRHSatMobWeb();
        funcionsatmobweb = new FuncionSatMobWeb();
        filiaissatmobweb = new FiliaisSatMobWeb();
        origem = "";
        log = new String();
        logerro = new ArquivoLog(this.getClass().getSimpleName());
        rand = 0;
        naoPossuoSenhaDia = false;
        dataTela = getDataAtual("SQL");
        pessoaPortalSrvDao = new PessoaPortalSrvDao();
        servicos = new ArrayList<>();
        exibirEmpresas = false;
        acessoPorLogin = false;
        webPontoImagem = "";
        googleApiMob = "";
        googleApiOper = "";
        transpCacamba = false;
        utilizaGTVe = false;
        Permissao10207 = false;
        Permissao10221 = false;
        Permissao10311 = false;
        Permissao10201 = false;
        Permissao10211 = false;
        Permissao10101 = false;
        nomeArq = "";
    }

    public void acessarRecurso(String codigoTela, String paginaDestino) {
        try {
            boolean temPermissao = retornaPermissao(codigoTela);
            if (temPermissao) {
                FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(
                        FacesMessage.SEVERITY_INFO, 
                        "Você acessou o recurso: " + codigoTela, 
                        "Você acessou o recurso: " + codigoTela));
                FacesContext facesContext = FacesContext.getCurrentInstance();
                try {
                    facesContext.getExternalContext().redirect(paginaDestino);
                } catch (IOException e) {
                    FacesContext.getCurrentInstance().addMessage(null, 
                            new FacesMessage(FacesMessage.SEVERITY_FATAL, 
                                    "Erro de Navegação!", 
                                    "Não foi possível redirecionar para a página."));
                }
            } else {
                FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(
                        FacesMessage.SEVERITY_ERROR, 
                        "Você não tem permissão para acessar: " + codigoTela, 
                        "Você não tem permissão para acessar: " + codigoTela));
            }
        } catch (Exception ex) {
            Logger.getLogger(LoginMB.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private boolean verificarPermissaoNoServidor(String codigoTela) {
        // Implemente sua lógica de permissão aqui.
        // EX: chamar um serviço de segurança, consultar roles do usuário, etc.
        return "relatorio".equals(codigoTela); // Exemplo: apenas permite acesso a "relatorio"
    } 
    public void carregarDadosWebPonto() throws IOException {
        try {
            FacesContext fc = FacesContext.getCurrentInstance();
            this.webPontoEscala = (String) fc.getExternalContext().getSessionMap().get("Escala");
            this.webPontoDescricao = (String) fc.getExternalContext().getSessionMap().get("Descricao");
            this.webPontoLocal = (String) fc.getExternalContext().getSessionMap().get("local");
            this.webPontoEnde = (String) fc.getExternalContext().getSessionMap().get("ende");
            this.webPontoMatr = (String) fc.getExternalContext().getSessionMap().get("Matr");
            this.webPontoPw = (String) fc.getExternalContext().getSessionMap().get("PwWeb");
            this.webPontoFuncao = (String) fc.getExternalContext().getSessionMap().get("funcao");
            this.webPontoNomeGuer = (String) fc.getExternalContext().getSessionMap().get("Nome_Guer");
            this.webPontoCodFil = (String) fc.getExternalContext().getSessionMap().get("CodFil");
            this.webPontoSecao = (String) fc.getExternalContext().getSessionMap().get("Secao");
            this.webPontoCodPessoa = (String) fc.getExternalContext().getSessionMap().get("cod_pessoa");

            /*if (null == this.webPontoCodFil
                    && this.webPontoCodFil.equals("")
                    && null == this.webPontoNomeGuer
                    && this.webPontoNomeGuer.equals("")) {
                FacesContext.getCurrentInstance().getExternalContext().redirect("index.xhtml");
            }*/
        } catch (Exception e) {
            //FacesContext.getCurrentInstance().getExternalContext().redirect("index.xhtml");
        }
    }

    public void logarPontoWeb() {
        if (null != this.matricula
                && !this.matricula.equals("")
                && null != this.pwweb
                && !this.pwweb.equals("")) {
            try {
                this.pp = this.pool.getConexao(this.param, this.enderecoNavegador);
                this.codFil = "1";

                List<Login> loginList = new ArrayList<>();
                LoginDao loginDao = new LoginDao();

                loginList = loginDao.LoginWebPonto(this.matricula, this.pwweb, this.codFil, this.dataTela, this.pp);

                if (loginList.size() > 0) {
                    // Chave para utilizar no Google Maps
                    this.satellite = this.pool.getConexao("SATELLITE", this.enderecoNavegador);
                    satelliteStatic = this.pool.getConexao("SATELLITE", this.enderecoNavegador);
                    ParametDao parametDao = new ParametDao();
                    Paramet parametGoogle = parametDao.getParametGoogleApi(this.pp.getEmpresa(), this.satellite);

                    this.googleApiMob = parametGoogle.getGoogleApiMob();
                    this.googleApiOper = parametGoogle.getGoogleApiOper();
                    this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
                    this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
                    this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("Situacao", loginList.get(0).getSaspw().getSituacao());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("Matr", loginList.get(0).getFuncion().getMatr().toPlainString().replace(".0", ""));
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("Nome_Guer", loginList.get(0).getFuncion().getNome_Guer());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("CodFil", loginList.get(0).getFuncion().getCodFil().toPlainString().replace(".0", ""));
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("Secao", loginList.get(0).getFuncion().getSecao());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cargo", loginList.get(0).getFuncion().getCargo());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("sexo", loginList.get(0).getFuncion().getSexo());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("Escala", loginList.get(0).getFuncion().getEscala());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("Descricao", loginList.get(0).getSaspw().getDescricao());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("funcao", loginList.get(0).getPessoa().getFuncao());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("PwWeb", loginList.get(0).getPessoa().getPWWeb());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("pw", loginList.get(0).getPessoa().getPW());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cod_pessoa", loginList.get(0).getPessoa().getCodigo().toPlainString().replace(".0", ""));
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("local", loginList.get(0).getCliente().getNRed());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("latitude", loginList.get(0).getCliente().getLatitude());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("longitude", loginList.get(0).getCliente().getLongitude());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("ende", loginList.get(0).getCliente().getEnde());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("ultimaBatidaData", loginList.get(0).getRhPonto().getDtBatida());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("ultimaBatidaHora", loginList.get(0).getRhPonto().getHora());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("sistema", loginList.get(0).getSaspwac().getSistema());

                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

                    FacesContext.getCurrentInstance().getExternalContext().redirect("ponto_mob.xhtml");
                } else {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("UsuarioSenhaIncorreta"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                }
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("VerifiqueParam"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = Thread.currentThread().getStackTrace()[1].getMethodName() + "\r\nMATRICULA: " + this.matricula + "\r\nSENHA: " + this.pwweb;
                this.logerro.Grava(log, caminho);
            }
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("InformeMatriculaSenha"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    /**
     * Cria conexão com a base central; Tenta fazer login no portal RH, em caso
     * de erro, tenta fazer login no módulo principal; Faz a verificação do
     * nível do usuário no sistema.
     */
    public void Logar() {
        try {
            this.exibirEmpresas = false;
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\login\\"
                    + DataAtual.getDataAtual("SQL") + ".txt";
            gerarLog("Criando persistência central");
            this.satellite = this.pool.getConexao("SATELLITE", this.enderecoNavegador);

            /*SasPoolPersistencia_OLD pool2 =  new SasPoolPersistencia_OLD();
            pool2.setTamanhoPool(1);
            pool2.setCaminho(this.getClass().getResource("mapconect_dev.txt").getPath().replace("%20", " "));
            this.satellite_old = pool2.getConexao("SATELLITE");*/
            gerarLog("Persistência central - OK");
            if (null == this.satellite) {
                this.satellite = this.pool.getConexao("SATELLITE", this.enderecoNavegador);

                if (null == this.satellite) {
                    throw new Exception("ImpossivelConectarSatellite");
                }
            }
            try {
                if (null != this.cli && (this.cli.toUpperCase().contains("SERVITE") || this.cli.toUpperCase().contains("INTERFORT")
                        || this.cli.toUpperCase().contains("TRANSPORTER"))) {
                    this.param = this.cli;
                    this.matricula = this.email;
                } else {
                    String[] dadosFuncionario = new String[2];

                    if (null != this.cli && !this.cli.equals("") && !this.email.contains("@")) {
                        dadosFuncionario[0] = this.cli.toUpperCase();
                        dadosFuncionario[1] = this.email;
                    } else {
                        dadosFuncionario = this.email.split("@");
                    }

                    this.param = dadosFuncionario[0];
                    this.matricula = dadosFuncionario[1];
                }
                this.pp = this.pool.getConexao(getParametro(this.param), this.enderecoNavegador);
                if (null == this.pp) {

                    this.pp = this.pool.getConexao(getParametro(this.param.toUpperCase().replace("SAT", "")), this.enderecoNavegador);
                    if (null == this.pp) {
                        if (null != this.tipoLogin && this.tipoLogin.equals("PC")) {
                            throw new Exception("login.usuarioerrado");
                        } else {
                            throw new Exception("EmpresaErrada");
                        }
                    }
                }
                this.funcion = this.login.loginRH(this.matricula, this.pwweb, this.pp, this.satellite);
                if (null != this.funcion) {
                    this.avisoportal = this.portalrh.buscaMensagem(this.funcion.getSaspw().getCodFil(), this.pp);
                    this.ano = this.portalrh.RendimentosAno(this.funcion.getPessoa().getMatr().toPlainString(), this.pp);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", getParametro(this.param));
                    this.filial = this.permissao.buscarFilial(this.funcion.getSaspw().getCodFil(), this.pp);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.filial.getCodfilAc());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.funcion.getPessoa().getCodigo());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.funcion.getSaspw().getNivelx());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.funcion.getPessoa().getNome());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", this.param);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula", this.funcion.getPessoa().getMatr().toPlainString());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.funcion.getSaspw().getCodGrupo());
                    this.portalrh.atualizaAcesso(this.funcion.getPessoa().getCodigo().toPlainString(), this.pp);
                    FacesContext.getCurrentInstance().getExternalContext().redirect("recursoshumanos/portalrh.xhtml");
                } else {
                    throw new Exception("SemAcessoRH");
                }
            } catch (Exception e) {
                if (e.getMessage().contains("login.senhaerrada") || e.getMessage().contains("login.metodoerrado") || e.getMessage().contains("login.usuarioerrado")) {
                    if (!e.getMessage().contains("login.usuarioerrado")) {
                        throw new Exception(e.getMessage());
                    } else {
                        throw new Exception(Messages.getMessageS("login.falhageral<message>login.usuarioerrado"));
                    }
                }
                gerarLog("Carregando empresas");
                this.empresas = this.login.Login(this.email, this.pwweb, this.satellite);
                this.usuario = this.login.BuscaPessoa(this.empresas.get(0).getCodigo(), this.satellite);
                this.servicos = this.pessoaPortalSrvDao.listarServicosUsuario(this.empresas.get(0).getCodigo(), this.satellite);
                this.usuarios = this.login.buscarUsuarios(this.email, this.pwweb, this.satellite);
                if ((this.usuarios.getPessoaPortalSrv().size() == 1 && this.usuarios.getPessoaLogin().size() <= 1)
                        && (this.usuarios.getPessoaPortalSrv().get(0).getServico().equals("0")
                        || this.usuarios.getPessoaPortalSrv().get(0).getServico().equals("0.0"))) {
                    this.filial = new SasPWFill();
                    Boolean logado = false;
                    for (PessoaLogin emp : this.empresas) {
                        switch (emp.getNivel()) {
                            case "4":
                                PortalRH(emp);
                                logado = true;
                                this.empresa = emp;
                                break;
                            case "5":
                                GTV(emp);
                                logado = true;
                                this.empresa = emp;
                                break;
                            case "6":
                                Cofre(emp);
                                logado = true;
                                this.empresa = emp;
                                break;
                            case "8":
                                PortalSatMobEW(emp);
                                logado = true;
                                this.empresa = emp;
                                break;
                            case "10":
                                PortalSatMobEW(emp);
                                logado = true;
                                this.empresa = emp;
                                break;
                            case "7":
                                throw new Exception(Messages.getMessageS("UsuarioPermissaoContateResponsavelSistema"));
                            default:
                                break;
                        }
                    }
                    if (!logado) {
                        this.acessoPorLogin = true;
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                        getLogo(this.empresas.get(0).getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("param.xhtml");
                    }
                } else {
                    if (this.usuarios.getPessoaLogin().size() == 1 && this.usuarios.getPessoaPortalSrv().size() == 1) {
                        this.empresa = this.usuarios.getPessoaLogin().get(0);
                        this.pessoaPortalSrv = this.usuarios.getPessoaPortalSrv().get(0);
                        acessarServico();
                    } else {
                        this.exibirEmpresas = true;
                        
                        if (null != this.cli && !this.cli.equals("")) {
                            for (PessoaLogin emp : this.empresas) {
                                if (emp.getBancoDados().toUpperCase().contains(this.cli.toUpperCase())) {
                                    this.empresa = emp;
                                }
                            }
                        }
                        
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneEmpresaServico"), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    }
                }

            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nEMAIL: " + this.email + "\r\nSENHA: " + this.pwweb + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void trocarOrigem() throws IOException {
        FacesContext fc = FacesContext.getCurrentInstance();
        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("origem", "portal_cliente");

        //PrimeFaces.current().executeScript("ValidarAcesso('10101', 'guia/guiasclientes.xhtml?faces-redirect=true', '')");
        FacesContext.getCurrentInstance().getExternalContext().redirect("guia/guiasclientes.xhtml?faces-redirect=true");
    }

    public boolean retornaPermissao(String codigoTela) throws Exception {
        boolean retorno = false;
        this.permissoes = this.login.ListarPermissoes(this.empresa.getCodPessoaBD(), this.pp);

        try {
            for (Saspwac item : this.permissoes) {
                if (item.getSistema().toPlainString().replace(".0", "").equals(codigoTela)) {
                    retorno = true;
                    return true;
                }
            }
        } catch (Exception ex) {

        }

        return retorno;
    }

    public void msgSemPermissao(String codigoTela) {
        PrimeFaces.current().executeScript("ValidarAcesso('" + codigoTela + "','','N');");
    }

    public void logarSatelliteDesktop() {
        try {
            if (null == this.loginSatDesktopData
                    || this.loginSatDesktopData.equals("")) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("InformeDataParaAcesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
            } else if (!this.loginSatDesktopData.equals(this.dataTela)) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("DataInvalidaParaAcesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
            } else if (null == this.loginSatDesktopBase
                    || this.loginSatDesktopBase.equals("")) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("InformeBaseDadosAcesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
            } else if (null == this.loginSatDesktopCodPessoa
                    || this.loginSatDesktopCodPessoa.equals("")) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("InformeCodPessoaAcesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
            } else if (null == this.loginSatDesktopSeqRota
                    || this.loginSatDesktopSeqRota.equals("")) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("InformeSeqRotaAcesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
            } else {
                this.exibirEmpresas = false;
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\login\\"
                        + DataAtual.getDataAtual("SQL") + ".txt";
                gerarLog("Criando persistência central");
                this.satellite = this.pool.getConexao("SATELLITE", this.enderecoNavegador);
                gerarLog("Persistência central - OK");

                if (null == this.satellite) {
                    this.satellite = this.pool.getConexao("SATELLITE", this.enderecoNavegador);

                    if (null == this.satellite) {
                        PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
                        throw new Exception("ImpossivelConectarSatellite");
                    }
                }

                try {
                    // Cria Conexão
                    this.pp = this.pool.getConexao(getParametro(this.loginSatDesktopBase), this.enderecoNavegador);

                    // Procurar CodPessoaWeb em BD Local
                    PessoaDao pessoaDao = new PessoaDao();
                    Pessoa pessoa = pessoaDao.ListaPessoaMobWebFromGTVe(this.loginSatDesktopCodPessoa.replace(".0", ""), this.pp);

                    if (null != pessoa
                            && null != pessoa.getCodPessoaWEB()
                            && BigDecimal.ZERO != pessoa.getCodPessoaWEB()) {

                        this.empresas = this.login.LoginCod(pessoa.getCodPessoaWEB().toPlainString().replace(".0", ""), this.satellite);
                        this.empresas.get(0).setCodPessoaBD(this.loginSatDesktopCodPessoa.replace(".0", ""));

                        this.usuario = this.login.BuscaPessoa(this.empresas.get(0).getCodigo(), this.satellite);
                        this.servicos = this.pessoaPortalSrvDao.listarServicosUsuario(this.empresas.get(0).getCodigo(), this.satellite);

                        if (this.empresas.size() > 0) {
                            // Selecionar Empresa
                            this.filial = new SasPWFill();
                            this.empresa = this.empresas.get(0);

                            for (int I = 0; I < this.empresas.size(); I++) {
                                if (this.empresas.get(I).getBancoDados().equals(this.pp.getEmpresa())) {
                                    this.empresa = this.empresas.get(I);
                                    break;
                                }
                            }

                            this.acessoPorLogin = true;
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                            getLogo(this.empresa.getBancoDados());

                            RotasDao rotasDao = new RotasDao();
                            Rotas rota = rotasDao.buscarDadosSeqRota(this.loginSatDesktopSeqRota, this.pp);

                            // Selecionar filial
                            try {
                                this.codFil = rota.getCodFil().toPlainString().replace(".0", "");
                                this.filiais = this.login.SelecionaEmpresa(BigDecimal.valueOf(Double.valueOf(this.loginSatDesktopCodPessoa)), this.pp);

                                for (int I = 0; I < filiais.size(); I++) {
                                    if (filiais.get(I).getCodfilAc().equals(this.codFil)) {
                                        this.filial = this.filiais.get(I);
                                        break;
                                    }
                                }

                                this.infoFilial = this.login.buscaInfoFilial(this.codFil, this.pp);
                                this.nivel = String.valueOf(this.empresa.getNivel());
                                this.permissoes = this.login.ListarPermissoes(BigDecimal.valueOf(Double.valueOf(this.loginSatDesktopCodPessoa)), this.pp);

                                AcessosDao acessosDao = new AcessosDao();
                                List<SaspwacSysdef> permissaoRotas = acessosDao.listaPermissoesRotas(BigDecimal.valueOf(Double.valueOf(this.loginSatDesktopCodPessoa)), this.pp);

                                // Permissoes de Operacoes
                                for (SaspwacSysdef permissaoItem : permissaoRotas) {
                                    switch (permissaoItem.getSaspwac().getSistema().toPlainString().replace(".0", "")) {
                                        case "10207":
                                            this.Permissao10207 = true;
                                            break;

                                        case "10221":
                                            this.Permissao10221 = true;
                                            break;

                                        case "10311":
                                            this.Permissao10311 = true;
                                            break;

                                        case "10201":
                                            this.Permissao10201 = true;
                                            break;

                                        case "10211":
                                            this.Permissao10211 = true;
                                            break;

                                        case "10101":
                                            this.Permissao10101 = true;
                                            break;
                                    }
                                }

                                // Chave para utilizar no Google Maps
                                ParametDao parametDao = new ParametDao();
                                Paramet parametGoogle = parametDao.getParametGoogleApi(this.empresa.getBancoDados(), this.satellite);
                                this.googleApiMob = parametGoogle.getGoogleApiMob();
                                this.googleApiOper = parametGoogle.getGoogleApiOper();

                                this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
                                this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
                                this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

                                getLogo(this.pp.getEmpresa(), this.codFil);

                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", BigDecimal.valueOf(Double.valueOf(this.loginSatDesktopCodPessoa)));
                                try {
                                    if (Integer.valueOf(this.empresa.getNivel()) > 3 && Integer.valueOf(this.empresa.getNivel()) < 9) {
                                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", "1");
                                    } else {
                                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                                    }
                                } catch (Exception e) {
                                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                                }
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                        this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());

                                FacesContext.getCurrentInstance().getExternalContext().redirect("operacoes/mapa_direccion.xhtml?faces-redirect=true&seqRota=" + rota.getSequencia().toPlainString().replace(".0", "") + "&codfil=" + rota.getCodFil().toPlainString().replace(".0", ""));

                            } catch (Exception e) {
                                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                                log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
                                this.logerro.Grava(log, caminho);
                                PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
                            }
                        } else {
                            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemPermissaoAcesso"), null);
                            FacesContext.getCurrentInstance().addMessage(null, mensagem);
                            PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
                        }
                    } else {
                        // Pessoa nao encontrada em BD central
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("NaoEncontradoCentral"), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                        PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
                    }
                } catch (Exception ex) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
                }
            }
        } catch (Exception ex) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            PrimeFaces.current().executeScript("$('#imgLoad').css('display','none');");
        }
    }

    public void LogarGTVePortalExclusivo(String caminho) {
        LogarGTVePortal(caminho);
    }

    public void LogarGTVe() {
        LogarGTVePortal("");
    }

    public void LogarGTVePortal(String portalExclusivo) {
        try {
            this.exibirEmpresas = false;
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\login\\"
                    + DataAtual.getDataAtual("SQL") + ".txt";
            gerarLog("Criando persistência central");
            this.satellite = this.pool.getConexao("SATELLITE", this.enderecoNavegador);
            gerarLog("Persistência central - OK");

            if (null == this.satellite) {
                this.satellite = this.pool.getConexao("SATELLITE", this.enderecoNavegador);

                if (null == this.satellite) {
                    throw new Exception("ImpossivelConectarSatellite");
                }
            }
            try {
                PessoaLoginDao pessoaLoginDao = new PessoaLoginDao();
                gtveAcesso = pessoaLoginDao.getPessoaGTVe(this.email, this.satellite);

                if (null != gtveAcesso
                        && null != gtveAcesso.getCodPessoa()
                        && !gtveAcesso.getCodPessoa().equals("")) {

                    // Verifica se chave está excluída
                    if (null != gtveAcesso.getFlag_excl()
                            && gtveAcesso.getFlag_excl().equals("*")) {
                        throw new Exception("ChaveExcluida");
                    }

                    // Verifica se chave está vencida
                    if (null != gtveAcesso.getExpirado()
                            && !gtveAcesso.getExpirado().equals("")
                            && gtveAcesso.getExpirado().equals("S")) {
                        throw new Exception("ChaveExpirada");
                    }

                    // Cria Conexão
                    this.pp = this.pool.getConexao(getParametro(gtveAcesso.getParametro()), this.enderecoNavegador);

                    if (null == this.pp) {
                        throw new Exception("EmpresaErrada");
                    }

                    // Procurar CodPessoaWeb em BD Local
                    PessoaDao pessoaDao = new PessoaDao();
                    Pessoa pessoa = pessoaDao.ListaPessoaMobWebFromGTVe(gtveAcesso.getCodPessoa().replace(".0", ""), this.pp);

                    if (null != pessoa
                            && null != pessoa.getCodPessoaWEB()
                            && BigDecimal.ZERO != pessoa.getCodPessoaWEB()) {

                        this.empresas = this.login.LoginCod(pessoa.getCodPessoaWEB().toPlainString().replace(".0", ""), this.satellite);
                        this.empresas.get(0).setCodPessoaBD(gtveAcesso.getCodPessoa().replace(".0", ""));

                        if (null != this.empresas
                                && this.empresas.size() > 0) {
                            // Acessar pagina de guias
                            gtveAcesso.setNomePessoa(pessoa.getNome());

                            if (null != pessoa.getPWWeb()
                                    && pessoa.getPWWeb().equals(this.senhaDia)) {
                                // Senha correta - acessa GTV
                                GTV(this.empresas.get(0), portalExclusivo);
                                this.empresa = this.empresas.get(0);
                            } else {
                                // Senha Inválida
                                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SenhaIncorreta"), null);
                                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                            }
                        } else {
                            // Não encontrou dados com o codPessoaWeb
                            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("NaoEncontradoCentral"), null);
                            FacesContext.getCurrentInstance().addMessage(null, mensagem);
                        }
                    } else {
                        // Pessoa nao encontrada em BD central
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("NaoEncontradoCentral"), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    }
                } else {
                    // Chave não encontrada
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ChaveNaoEncontrada"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                }

            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nEMAIL: " + this.email + "\r\nSENHA: " + this.pwweb + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void acessarServico() {
        try {
            this.acessoPorLogin = true;
            this.pp = this.pool.getConexao(this.empresa.getBancoDados(), this.enderecoNavegador);
            this.filiais = this.login.SelecionaEmpresa(this.empresa.getCodPessoaBD(), this.pp);
            locale.setParam(this.empresa.getCodPessoaBD(), this.pp);

            // **********************************************************************************
            // Capturar Idioma do Usuario
            // **********************************************************************************
            String idioma = this.login.consultarIdioma(this.empresa.getCodPessoaBD(), this.pp);
            String idiomaCookie = Faces.getRequestCookie("idioma");

            if (null == idioma
                    || idioma.equals("")) {
                idioma = idiomaCookie;
            }

            if (null != idioma
                    && !idioma.equals("")) {
                Faces.addResponseCookie("idioma", idioma, 315360000);

                switch (idioma.toLowerCase()) {
                    case "pt":
                    case "porbr":
                        locale.getLocales(1);
                        break;

                    case "es":
                    case "espes":
                        locale.getLocales(2);
                        break;

                    case "en":
                    case "enguk":
                        locale.getLocales(3);
                        break;
                }
            }
            // **********************************************************************************

            // Chave para utilizar no Google Maps
            ParametDao parametDao = new ParametDao();
            Paramet parametGoogle = parametDao.getParametGoogleApi(this.empresa.getBancoDados(), this.satellite);
            this.googleApiMob = parametGoogle.getGoogleApiMob();
            this.googleApiOper = parametGoogle.getGoogleApiOper();

            this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
            this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
            this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

            switch (this.pessoaPortalSrv.getServico().replace(".0", "")) {
                case "0":
                case "101":
                case "221":
                case "301":
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                    getLogo(this.empresas.get(0).getBancoDados());
                    FacesContext.getCurrentInstance().getExternalContext().redirect("param.xhtml");
                    break;
                case "401":
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuarios.getPessoa().getNome());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                    //            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", );
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codServico", this.pessoaPortalSrv.getServico().replace(".0", ""));
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "1");
                    FacesContext.getCurrentInstance().getExternalContext().redirect("containers/containers.xhtml");

                    break;
                case "402":
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuarios.getPessoa().getNome());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codServico", this.pessoaPortalSrv.getServico().replace(".0", ""));
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "1");
                    FacesContext.getCurrentInstance().getExternalContext().redirect("containers/containers_marketing.xhtml");
                    break;
                case "131":
                    GTV(this.empresa);

                    break;
                default:
                    throw new Exception("ServicoNaoDisponivel");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nEMAIL: " + this.email + "\r\nSENHA: " + this.pwweb + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Lista todas as filiais de uma empresa que o usuário tem acesso.
     *
     * @throws Exception
     */
    public void ListarFiliais() throws Exception {
        try {
            if (null == this.empresa) {
                this.empresa = this.empresas.get(0);
            }
            gerarLog("Criando persistencia local - " + this.empresa.getBancoDados());
            float startTime = (float) System.nanoTime();
            this.pp = this.pool.getConexao(this.empresa.getBancoDados(), this.enderecoNavegador);
            float endTime = (float) System.nanoTime();
            float duration = (endTime - startTime) / 1000000000;

            gerarLog("Persistencia " + this.empresa.getBancoDados() + " - OK: " + duration + "s");
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.empresa.getBancoDados() + "\\" + DataAtual.getDataAtual("SQL") + "\\" + this.empresa.getCodPessoaBD().toBigInteger() + ".txt";
//            this.logerro.Grava(this.empresa.getBancoDados()+": "+duration+"ms", FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator)+"tempo_conexao.txt");
            if (null == this.pp) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.filiais = this.login.SelecionaEmpresa(this.empresa.getCodPessoaBD(), this.pp);
            this.listaFiliais = this.login.SelecionaEmpresa(this.empresa.getCodPessoaBD(), this.pp);
            getLogo(this.pp.getEmpresa());
            if (this.acessoPorLogin) {
                this.acessoPorLogin = false;
                if (this.empresas.size() == 1 && this.filiais.size() == 1) {
                    this.filial = this.filiais.get(0);
                    SelecionarFilial();
                }
            }
        } catch (Exception e) {
            this.filiais = new ArrayList<>();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        } finally {
//            this.filiais.add(0, this.filial);
        }
    }

    public void abrirSelecaoFilialRotas() {
        try {
            this.pp = this.pool.getConexao(this.empresa.getBancoDados(), this.enderecoNavegador);
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.empresa.getBancoDados() + "\\" + DataAtual.getDataAtual("SQL") + "\\" + this.empresa.getCodPessoaBD().toBigInteger() + ".txt";

            this.filiais = this.login.SelecionaEmpresa(this.empresa.getCodPessoaBD(), this.pp);
            getLogo(this.pp.getEmpresa());
        } catch (Exception e) {
            this.filiais = new ArrayList<>();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Filtro para busca de filiais de uma empresa
     *
     * @param query - busca do usuário
     * @return lista de filiais
     */
    public List<SasPWFill> ListarQuery(String query) {
        try {
            List<SasPWFill> retorno = new ArrayList<>();
            for (SasPWFill f : this.filiais) {
                if (null != f && null != f.getDescricao() && f.getDescricao().toUpperCase().contains(query.toUpperCase())) {
                    retorno.add(f);
                }
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    /**
     * Seleciona a filial e coloca em sessão os atributos necessários para a
     * navegação no sisitema;
     */
    public void SelecionarFilial() {
        try {
            if (null == this.filial) {
                throw new Exception("SelecioneFilial");
            }
            Boolean valida = false;
            for (SasPWFill fil : this.filiais) {
                if (fil.equals(this.filial)) {
                    valida = true;
                    break;
                }
            }
            if (!valida) {
                throw new Exception("FilialInvalida");
            }
            if (this.login.SituacaoUsuario(this.filial.getCodfilAc(), this.email, this.pp).equals("B")) {
                throw new Exception("UsuarioBloqueado");
            }
            this.codFil = this.filial.getCodfilAc();
            this.infoFilial = this.login.buscaInfoFilial(this.filial.getCodfilAc(), this.pp);
            this.nivel = String.valueOf(this.empresa.getNivel());
            this.permissoes = this.login.ListarPermissoes(this.empresa.getCodPessoaBD(), this.pp);

            AcessosDao acessosDao = new AcessosDao();
            List<SaspwacSysdef> permissaoRotas = acessosDao.listaPermissoesRotas(this.empresa.getCodPessoaBD(), this.pp);

            // Permissoes de Operacoes
            for (SaspwacSysdef permissaoItem : permissaoRotas) {
                switch (permissaoItem.getSaspwac().getSistema().toPlainString().replace(".0", "")) {
                    case "10207":
                        this.Permissao10207 = true;
                        break;

                    case "10221":
                        this.Permissao10221 = true;
                        break;

                    case "10311":
                        this.Permissao10311 = true;
                        break;

                    case "10201":
                        this.Permissao10201 = true;
                        break;

                    case "10211":
                        this.Permissao10211 = true;
                        break;

                    case "10101":
                        this.Permissao10101 = true;
                        break;
                }
            }

            this.ano = this.portalrh.RendimentosAno(this.filial.getMatr(), this.pp);

            // Chave para utilizar no Google Maps
            ParametDao parametDao = new ParametDao();
            Paramet parametGoogle = parametDao.getParametGoogleApi(this.empresa.getBancoDados(), this.satellite);
            this.googleApiMob = parametGoogle.getGoogleApiMob();
            this.googleApiOper = parametGoogle.getGoogleApiOper();

            this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
            this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
            this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

            getLogo(this.pp.getEmpresa(), this.codFil);
            if (this.pessoaPortalSrv != null) {
                switch (this.pessoaPortalSrv.getServico().replace(".0", "")) {
                    case "0":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        try {
                            if (Integer.valueOf(this.empresa.getNivel()) > 3 && Integer.valueOf(this.empresa.getNivel()) < 9) {
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", "1");
                            } else {
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                            }
                        } catch (Exception e) {
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        }
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("menu.xhtml");
                        break;
                    case "101":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("operacoes/rotas.xhtml");
                        break;
                    case "221":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("relatorio/portal.xhtml");
                        break;
                    case "301":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                        //                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
                        this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("menu", false);
                        FacesContext.getCurrentInstance().getExternalContext().redirect("cofre/cofre.xhtml");
                        break;
                    default:
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("menu.xhtml");
                }
            } else {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                        this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                FacesContext.getCurrentInstance().getExternalContext().redirect("menu.xhtml");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void verificarPermissaoAtualizada(String CodigoPermissao, String Link) throws Exception {
        AcessosDao acessosDao = new AcessosDao();
        List<SaspwacSysdef> permissaoRotas = acessosDao.listaPermissoesRotas(this.empresa.getCodPessoaBD(), this.pp);

        // Permissoes de Operacoes
        for (SaspwacSysdef permissaoItem : permissaoRotas) {
            switch (permissaoItem.getSaspwac().getSistema().toPlainString().replace(".0", "")) {
                case "10207":
                    this.Permissao10207 = true;
                    break;

                case "10221":
                    this.Permissao10221 = true;
                    break;

                case "10311":
                    this.Permissao10311 = true;
                    break;

                case "10201":
                    this.Permissao10201 = true;
                    break;

                case "10211":
                    this.Permissao10211 = true;
                    break;

                case "10101":
                    this.Permissao10101 = true;
                    break;
            }
        }

        // Retorna permissao atualizada
        switch (CodigoPermissao) {
            case "10207":
                if (this.Permissao10207) {
                    FacesContext.getCurrentInstance().getExternalContext().redirect(Link);
                } else {
                    PrimeFaces.current().executeScript("NaoPermitido('" + CodigoPermissao + "')");
                }

                break;

            case "10221":
                if (this.Permissao10221) {
                    FacesContext.getCurrentInstance().getExternalContext().redirect(Link);
                } else {
                    PrimeFaces.current().executeScript("NaoPermitido('" + CodigoPermissao + "')");
                }

                break;

            case "10311":
                if (this.Permissao10311) {
                    FacesContext.getCurrentInstance().getExternalContext().redirect(Link);
                } else {
                    PrimeFaces.current().executeScript("NaoPermitido('" + CodigoPermissao + "')");
                }

                break;

            case "10201":
                if (this.Permissao10201) {
                    FacesContext.getCurrentInstance().getExternalContext().redirect(Link);
                } else {
                    PrimeFaces.current().executeScript("NaoPermitido('" + CodigoPermissao + "')");
                }

                break;

            case "10211":
                if (this.Permissao10211) {
                    FacesContext.getCurrentInstance().getExternalContext().redirect(Link);
                } else {
                    PrimeFaces.current().executeScript("NaoPermitido('" + CodigoPermissao + "')");
                }

                break;

            case "10101":
                if (this.Permissao10101) {
                    FacesContext.getCurrentInstance().getExternalContext().redirect(Link);
                } else {
                    PrimeFaces.current().executeScript("NaoPermitido('" + CodigoPermissao + "')");
                }

                break;
        }
    }

    public void SelecionarFilialSPM() {
        try {
            if (null == this.filial) {
                throw new Exception("SelecioneFilial");
            }
            Boolean valida = false;
            for (SasPWFill fil : this.filiais) {
                if (fil.equals(this.filial)) {
                    valida = true;
                    break;
                }
            }
            if (!valida) {
                throw new Exception("FilialInvalida");
            }
            if (this.login.SituacaoUsuario(this.email, this.pp).equals("B")) {
                throw new Exception("UsuarioBloqueado");
            }
            this.codFil = this.filial.getCodfilAc();
            this.infoFilial = this.login.buscaInfoFilial(this.filial.getCodfilAc(), this.pp);
            this.nivel = String.valueOf(this.empresa.getNivel());
            this.permissoes = this.login.ListarPermissoes(this.empresa.getCodPessoaBD(), this.pp);

            AcessosDao acessosDao = new AcessosDao();
            List<SaspwacSysdef> permissaoRotas = acessosDao.listaPermissoes(this.empresa.getCodPessoaBD().toPlainString().replace(".0", ""), this.pp);

            // Permissoes de Operacoes
            for (SaspwacSysdef permissaoItem : permissaoRotas) {
                switch (permissaoItem.getSaspwac().getSistema().toPlainString().replace(".0", "")) {
                    case "10207":
                        this.Permissao10207 = true;
                        break;

                    case "10221":
                        this.Permissao10221 = true;
                        break;

                    case "10311":
                        this.Permissao10311 = true;
                        break;

                    case "10201":
                        this.Permissao10201 = true;
                        break;

                    case "10211":
                        this.Permissao10211 = true;
                        break;

                    case "10101":
                        this.Permissao10101 = true;
                        break;
                }
            }

            this.ano = this.portalrh.RendimentosAno(this.filial.getMatr(), this.pp);

            // Chave para utilizar no Google Maps
            ParametDao parametDao = new ParametDao();
            Paramet parametGoogle = parametDao.getParametGoogleApi(this.empresa.getBancoDados(), this.satellite);
            this.googleApiMob = parametGoogle.getGoogleApiMob();
            this.googleApiOper = parametGoogle.getGoogleApiOper();

            this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
            this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
            this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

            getLogo(this.pp.getEmpresa());
            if (this.pessoaPortalSrv != null) {
                switch (this.pessoaPortalSrv.getServico().replace(".0", "")) {
                    case "0":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        try {
                            if (Integer.valueOf(this.empresa.getNivel()) > 3 && Integer.valueOf(this.empresa.getNivel()) < 9) {
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", "1");
                            } else {
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                            }
                        } catch (Exception e) {
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        }
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("indexSPM.xhtml");
                        break;
                    case "101":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("../operacoes/rotas.xhtml");
                        break;
                    case "221":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("../relatorio/portal.xhtml");
                        break;
                    case "301":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                        //                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
                        this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("menu", false);
                        FacesContext.getCurrentInstance().getExternalContext().redirect("../cofre/cofre.xhtml");
                        break;
                    default:
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("indexSPM.xhtml");
                }
            } else {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                        this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                FacesContext.getCurrentInstance().getExternalContext().redirect("indexSPM.xhtml");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void SelecionarFilialMSL() {
        try {
            if (null == this.filial) {
                throw new Exception("SelecioneFilial");
            }
            Boolean valida = false;
            for (SasPWFill fil : this.filiais) {
                if (fil.equals(this.filial)) {
                    valida = true;
                    break;
                }
            }
            if (!valida) {
                throw new Exception("FilialInvalida");
            }
            if (this.login.SituacaoUsuario(this.email, this.pp).equals("B")) {
                throw new Exception("UsuarioBloqueado");
            }
            this.codFil = this.filial.getCodfilAc();
            this.infoFilial = this.login.buscaInfoFilial(this.filial.getCodfilAc(), this.pp);
            this.nivel = String.valueOf(this.empresa.getNivel());
            this.permissoes = this.login.ListarPermissoes(this.empresa.getCodPessoaBD(), this.pp);

            AcessosDao acessosDao = new AcessosDao();
            List<SaspwacSysdef> permissaoRotas = acessosDao.listaPermissoes(this.empresa.getCodPessoaBD().toPlainString().replace(".0", ""), this.pp);

            // Permissoes de Operacoes
            for (SaspwacSysdef permissaoItem : permissaoRotas) {
                switch (permissaoItem.getSaspwac().getSistema().toPlainString().replace(".0", "")) {
                    case "10207":
                        this.Permissao10207 = true;
                        break;

                    case "10221":
                        this.Permissao10221 = true;
                        break;

                    case "10311":
                        this.Permissao10311 = true;
                        break;

                    case "10201":
                        this.Permissao10201 = true;
                        break;

                    case "10211":
                        this.Permissao10211 = true;
                        break;

                    case "10101":
                        this.Permissao10101 = true;
                        break;
                }
            }

            this.ano = this.portalrh.RendimentosAno(this.filial.getMatr(), this.pp);

            // Chave para utilizar no Google Maps
            ParametDao parametDao = new ParametDao();
            Paramet parametGoogle = parametDao.getParametGoogleApi(this.empresa.getBancoDados(), this.satellite);
            this.googleApiMob = parametGoogle.getGoogleApiMob();
            this.googleApiOper = parametGoogle.getGoogleApiOper();

            this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
            this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
            this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

            getLogo(this.pp.getEmpresa());
            if (this.pessoaPortalSrv != null) {
                switch (this.pessoaPortalSrv.getServico().replace(".0", "")) {
                    case "0":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        try {
                            if (Integer.valueOf(this.empresa.getNivel()) > 3 && Integer.valueOf(this.empresa.getNivel()) < 9) {
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", "1");
                            } else {
                                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                            }
                        } catch (Exception e) {
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        }
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("index_moneta.xhtml");
                        break;
                    case "101":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("../operacoes/rotas.xhtml");
                        break;
                    case "221":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("../relatorio/portal.xhtml");
                        break;
                    case "301":
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                        //                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
                        this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("menu", false);
                        FacesContext.getCurrentInstance().getExternalContext().redirect("../cofre/cofre.xhtml");
                        break;
                    default:
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                                this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("index_moneta.xhtml");
                }
            } else {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.codFil);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.empresa.getCodPessoaBD());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.filial.getCodGrupo().replace(".0", ""));
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(this.empresa.getBancoDados()));
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula",
                        this.filial.getMatr().contains(".0") ? this.filial.getMatr().replace(".0", "") : this.filial.getMatr());
                FacesContext.getCurrentInstance().getExternalContext().redirect("index_moneta.xhtml");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Faz a verificação dos parâmetros digitados pelo usuário, e gera um número
     * para validação;
     */
    public void PrimeiroAcesso() {
        try {
            this.pp = this.pool.getConexao(getParametro(this.param), this.enderecoNavegador);
            if (null == this.pp) {
                throw new Exception(Messages.getMessageS("EmpresaInvalida"));
            }
            this.primeiroAcesso = this.portalrh.PrimeiroAcesso(this.matricula, this.pp);
            if (null == this.primeiroAcesso) {
                throw new Exception(Messages.getMessageS("FuncionarioNaoEncontrado"));
            }
            if (null != this.primeiroAcesso.getDt_UltAcPortal() && !this.primeiroAcesso.getDt_UltAcPortal().equals("")) {
                throw new Exception(Messages.getMessageS("MatriculaAcessouSistema"));
            }
            this.rand = (int) Math.floor(Math.random() * 3 + 1);
            FacesContext.getCurrentInstance().getExternalContext().redirect("recursoshumanos/primeiroacesso.xhtml");
            this.validacao1 = new String();
            this.validacao2 = new String();
            this.validacao3 = new String();
            PrimeFaces.current().ajax().update("main");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nEMPRESA: " + this.param + "\r\nOPERADOR: " + this.matricula + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Valida os dados fornecidos pelo usuário com os dados salvos na base local
     * da empresa;
     *
     * @throws IOException - em caso de erro, redireciona para a tela inicial
     */
    public void Validar() throws IOException {
        try {
            switch (this.rand) {
                case 1: //RG e CPF
                    if (this.validacao1.toUpperCase().equals(this.primeiroAcesso.getRG().toUpperCase())
                            && this.validacao2.toUpperCase().equals(this.primeiroAcesso.getCPF().toUpperCase())) {
                        PrimeFaces.current().executeScript("PF('dlgDigiteSenha').show();");
                    } else {
                        throw new Exception("ErroValidacao");
                    }
                    break;
                case 2: //cidade e CPF
                    if (this.validacao1.toUpperCase().equals(this.primeiroAcesso.getCidade().toUpperCase())
                            && this.validacao2.toUpperCase().equals(this.primeiroAcesso.getCPF().toUpperCase())) {
                        PrimeFaces.current().executeScript("PF('dlgDigiteSenha').show();");
                    } else {
                        throw new Exception("ErroValidacao");
                    }
                    break;
                case 3: //cidade e data nascimento
                    if (this.validacao1.toUpperCase().equals(this.primeiroAcesso.getCidade().toUpperCase())
                            && Mascaras.Data(this.validacao2.toUpperCase()).equals(Mascaras.Data(this.primeiroAcesso.getDt_nasc()))) {
                        PrimeFaces.current().executeScript("PF('dlgDigiteSenha').show();");
                    } else {
                        throw new Exception("ErroValidacao");
                    }
                    break;
                default:
                    throw new Exception("ErroValidacao");
            }
        } catch (Exception e) {
            this.rand = 0;
            FacesContext.getCurrentInstance().getExternalContext().redirect("../index.xhtml?msg=ErroValidacao");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "EMPRESA: " + this.param + "\r\nOPERADOR: " + this.matricula + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void Validar2() {
        try {
            // INSERIR ENTRADA NA TABELA PESSOA SATELLITE COPIA DA PESSOA LOCAL
            // ATUALIZAR PESSOA LOCAL PWPORTAL E DT_ULTACPORTAL
            // INSERIR ENTRADA NA TABELA PESSOALOGIN SATELLITE NIVEL 4
            this.primeiroAcesso.setPWPortal(this.validacao3);
            this.portalrh.CadastroRH(this.primeiroAcesso, this.pp, this.pool.getConexao("SATELLITE", this.enderecoNavegador));
            PrimeFaces.current().executeScript("PF('dlgDigiteSenha').hide();");
            FacesContext.getCurrentInstance().getExternalContext().redirect("../index.xhtml?msg=CadastroSucesso");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ErroPrimeiroAcesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nEMPRESA: " + this.param + "\r\nOPERADOR: " + this.matricula + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    private void PortalRH(PessoaLogin emp) {
        try {
            this.pp = this.pool.getConexao(emp.getBancoDados(), this.enderecoNavegador);
            if (this.login.SituacaoUsuario(this.email, this.pp).equals("B")) {
                throw new Exception("UsuarioBloqueado");
            }
            Funcion funcionario = this.funcionsatmobweb.BuscaFuncisonCodpessoa(emp.getCodPessoaBD(), this.pp);
            this.avisoportal = this.portalrh.buscaMensagem(funcionario.getCodFil().toPlainString(), this.pp);
            this.ano = this.portalrh.RendimentosAno(funcionario.getMatr().toPlainString(), this.pp);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
            this.filial = this.permissao.buscarFilial(funcionario.getCodFil().toString(), this.pp);
            this.funcion = new UsuarioSatMobWeb();
            this.funcion.getPessoa().setNome(funcionario.getNome());
            this.funcion.getPessoa().setSexo(funcionario.getSexo());
            this.param = Messages.getMessageS(emp.getBancoDados());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.filial.getCodfilAc());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", funcionario.getCodPessoaWeb());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", funcionario.getNome());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", Messages.getMessageS(emp.getBancoDados()));
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula", funcionario.getMatr().toPlainString());
            this.portalrh.atualizaAcesso(funcionario.getCodPessoaWeb().toBigInteger().toString(), this.pp);
            FacesContext.getCurrentInstance().getExternalContext().redirect("recursoshumanos/portalrh.xhtml");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Sair do sistema
     */
    public void trocarFilial() {
        try {
            this.filial = new SasPWFill();
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", null);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", null);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", null);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", null);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", null);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", null);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresa", null);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula", null);
            FacesContext.getCurrentInstance().getExternalContext().redirect("param.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void onMSL() throws Exception {
        Faces.addResponseCookie("idioma", "2", 315360000);
        locale.getLocales(2);
    }

    public void limparLogo() {
        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("LogoSessao", "");
    }

    public void limparSession() {
        this.cli = "";
        this.tipoLogin = "";
    }

    public void onRefresh() {
        try {
            if (null == this.tipoLogin || this.tipoLogin.equals("")) {
                this.tipoLogin = "SC";
            }
            // **********************************************************************************
            // Capturar Idioma do Usuario
            // **********************************************************************************
            String idioma = Faces.getRequestCookie("idioma");

            if (null != idioma
                    && !idioma.equals("")) {
                switch (idioma.toLowerCase()) {
                    case "pt":
                    case "porbr":
                        locale.getLocales(1);
                        break;

                    case "es":
                    case "espes":
                        locale.getLocales(2);
                        break;

                    case "en":
                    case "enguk":
                        locale.getLocales(3);
                        break;
                }
            }
            // **********************************************************************************

            this.exibirEmpresas = false;
            this.pessoaPortalSrv = null;
            this.empresa = null;

            if (null != this.portalLogin && !portalLogin.equals("")) {
                this.email = this.portalEmail;
                this.pwweb = this.portalSenha;
                Logar();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void logOut() {
        try {
            this.pp.FechaConexao();
            this.pp = null;
            this.pessoaPortalSrv = null;
            this.empresa = null;
            FacesContext fc = FacesContext.getCurrentInstance();
            FacesContext.getCurrentInstance().getExternalContext()
                    .redirect((null == this.cli || this.cli.equals(""))
                            ? "index.xhtml"
                            : "index.xhtml?empresa=" + this.cli);
            HttpSession session = (HttpSession) fc.getExternalContext().getSession(false);
            session.invalidate();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void logOutRH() {
        try {
            this.pp.FechaConexao();
            this.pp = null;
            this.pessoaPortalSrv = null;
            this.empresa = null;
            HttpSession session = (HttpSession) FacesContext.getCurrentInstance().getExternalContext().getSession(false);
            session.invalidate();

            if (null == gtveAcesso || null == gtveAcesso.getCodCli() || gtveAcesso.getCodCli().equals("")) {
                FacesContext.getCurrentInstance().getExternalContext()
                        .redirect((null == this.cli || this.cli.equals(""))
                                ? "../index.xhtml"
                                : "../index.xhtml?empresa=" + this.cli);
            } else {
                if (null != gtveAcesso.getParametro() && !gtveAcesso.getParametro().toUpperCase().contains("BRASIFORT")) {
                    FacesContext.getCurrentInstance().getExternalContext().redirect("../gtve/index.xhtml");
                } else {
                    FacesContext.getCurrentInstance().getExternalContext().redirect("../gtve/brasifort/index.xhtml");
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Voltar para o menu principal;
     */
    public void voltar() {
        try {
            FacesContext.getCurrentInstance().getExternalContext()
                    .redirect((null == this.cli || this.cli.equals(""))
                            ? "../menu.xhtml?faces-redirect=true"
                            : "../menu.xhtml?faces-redirect=true&empresa=" + this.cli);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Voltar para a url desejada
     */
    public void voltar(String url) {
        try {
            FacesContext.getCurrentInstance().getExternalContext().redirect(url);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    // GTV //
    /**
     * Login para usuários de nível GTV-e; Se houver apenas um cliente
     * autorizado, o login é automático.
     *
     * @param emp - pessoalogin com codigo da pessoa, parametro e nivel
     */
    private void GTV(PessoaLogin emp) {
        try {
            // Chave para utilizar no Google Maps
            ParametDao parametDao = new ParametDao();
            Paramet parametGoogle = parametDao.getParametGoogleApi(emp.getBancoDados(), this.satellite);
            this.googleApiMob = parametGoogle.getGoogleApiMob();
            this.googleApiOper = parametGoogle.getGoogleApiOper();

            this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
            this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
            this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

            getLogo(emp.getBancoDados());

            gerarLog("Criando persistencia local - " + emp.getBancoDados());

            float startTime = (float) System.nanoTime();
            if (null == this.pp) {
                this.pp = this.pool.getConexao(emp.getBancoDados(), this.enderecoNavegador);
            }
            float endTime = (float) System.nanoTime();
            float duration = (endTime - startTime) / 1000000000;

            gerarLog("Persistencia " + emp.getBancoDados() + " - OK: " + duration + "s");

            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + emp.getBancoDados() + "\\" + DataAtual.getDataAtual("SQL") + "\\" + emp.getCodPessoaBD().toBigInteger() + ".txt";

            gerarLog("Validando siatuação do usuário");
            if (this.login.SituacaoUsuario(this.email, this.pp).equals("B")) {
                throw new Exception("UsuarioBloqueado");
            }
            this.acesso.setCodigo(emp.getCodPessoaBD().toPlainString());

            gerarLog("Listando Clientes - PessoaCliAut");

            if (null == this.gtveAcesso
                    || null == this.gtveAcesso.getCodCli()
                    || gtveAcesso.getCodCli().equals("")) {
                this.clientes = this.login.ListarClientes(this.acesso, false, this.pp);
            } else {
                PessoaCliAutDao pessoaCliAutDao = new PessoaCliAutDao();
                this.clientes = pessoaCliAutDao.listarClientesGTVe(this.gtveAcesso.getCodCli(), false, this.pp);
            }

            if (this.clientes.isEmpty()) {
                throw new Exception("UsuarioSemClientes");
            } else if (this.clientes.size() == 1 || this.pp.getEmpresa().equals("SPM")) {
                try {
                    if (this.pp.getEmpresa().equals("SPM")) {
                        gerarLog("Cliente selecionado - Todos");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.clientes.get(0).getCodFil());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
                        gerarLog("Listando Permissoes - Saspwac");

                        this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("guia/guiasclientes.xhtml");
                    } else {
                        this.selecionado = this.clientes.get(0);
                        gerarLog("Listando Permissoes - Saspwac");
                        this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", this.selecionado.getAgencia());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", this.selecionado.getSubAgencia());
                        if (null == this.gtveAcesso
                                || null == this.gtveAcesso.getCodCli()
                                || gtveAcesso.getCodCli().equals("")) {
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("formalogin", "tradicional");
                        } else {
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", gtveAcesso.getNomePessoa());
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("formalogin", "gtveAcesso");
                        }
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.selecionado.getCodFil());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.selecionado.getNomeCli());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.selecionado.getOperador());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.selecionado.getNomeCli());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.selecionado.getCodGrupo());
                        if (null == this.gtveAcesso
                                || null == this.gtveAcesso.getCodCli()
                                || gtveAcesso.getCodCli().equals("")) {
                            FacesContext.getCurrentInstance().getExternalContext().redirect("menu_cliente.xhtml");
                        } else {
                            FacesContext.getCurrentInstance().getExternalContext().redirect("../menu_cliente.xhtml");
                        }
                    }
                } catch (Exception e) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                }
            } else {
                this.selecionado = this.clientes.get(0);
                gerarLog("Listando Permissoes - Saspwac");
                this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", this.selecionado.getAgencia());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", this.selecionado.getSubAgencia());
                if (null == this.gtveAcesso
                        || null == this.gtveAcesso.getCodCli()
                        || gtveAcesso.getCodCli().equals("")) {
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("formalogin", "tradicional");
                } else {
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", gtveAcesso.getNomePessoa());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("formalogin", "gtveAcesso");
                }
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.selecionado.getCodFil());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.selecionado.getNomeCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.selecionado.getOperador());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.selecionado.getNomeCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.selecionado.getCodGrupo());
                if (null == this.gtveAcesso
                        || null == this.gtveAcesso.getCodCli()
                        || gtveAcesso.getCodCli().equals("")) {
                    FacesContext.getCurrentInstance().getExternalContext().redirect("menu_cliente.xhtml");
                } else {
                    FacesContext.getCurrentInstance().getExternalContext().redirect("../menu_cliente.xhtml");
                }

                /*PrimeFaces.current().ajax().update("cliente");
                PrimeFaces.current().executeScript("PF('dlgSelecionarCliente').show();");*/
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    private void GTV(PessoaLogin emp, String portalExclusivo) {
        try {
            // Chave para utilizar no Google Maps
            ParametDao parametDao = new ParametDao();
            Paramet parametGoogle = parametDao.getParametGoogleApi(emp.getBancoDados(), this.satellite);
            this.googleApiMob = parametGoogle.getGoogleApiMob();
            this.googleApiOper = parametGoogle.getGoogleApiOper();

            this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
            this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
            this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

            getLogo(emp.getBancoDados());

            gerarLog("Criando persistencia local - " + emp.getBancoDados());

            float startTime = (float) System.nanoTime();
            if (null == this.pp) {
                this.pp = this.pool.getConexao(emp.getBancoDados(), this.enderecoNavegador);
            }
            float endTime = (float) System.nanoTime();
            float duration = (endTime - startTime) / 1000000000;

            gerarLog("Persistencia " + emp.getBancoDados() + " - OK: " + duration + "s");

            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + emp.getBancoDados() + "\\" + DataAtual.getDataAtual("SQL") + "\\" + emp.getCodPessoaBD().toBigInteger() + ".txt";

            gerarLog("Validando siatuação do usuário");
            if (this.login.SituacaoUsuario(this.email, this.pp).equals("B")) {
                throw new Exception("UsuarioBloqueado");
            }
            this.acesso.setCodigo(emp.getCodPessoaBD().toPlainString());

            gerarLog("Listando Clientes - PessoaCliAut");

            if (null == this.gtveAcesso
                    || null == this.gtveAcesso.getCodCli()
                    || gtveAcesso.getCodCli().equals("")) {
                this.clientes = this.login.ListarClientes(this.acesso, false, this.pp);
            } else {
                PessoaCliAutDao pessoaCliAutDao = new PessoaCliAutDao();
                this.clientes = pessoaCliAutDao.listarClientesGTVeChave(this.gtveAcesso.getCodCli(), this.gtveAcesso.getCodFil(), false, this.pp);
            }

            if (this.clientes.isEmpty()) {
                throw new Exception("UsuarioSemClientes");
            } else if (this.clientes.size() == 1 || this.pp.getEmpresa().equals("SPM")) {
                try {
                    if (this.pp.getEmpresa().equals("SPM")) {
                        gerarLog("Cliente selecionado - Todos");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.clientes.get(0).getCodFil());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", "");
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
                        gerarLog("Listando Permissoes - Saspwac");

                        this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                        FacesContext.getCurrentInstance().getExternalContext().redirect("guia/guiasclientes.xhtml");
                    } else {
                        this.selecionado = this.clientes.get(0);
                        gerarLog("Listando Permissoes - Saspwac");
                        this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", this.selecionado.getAgencia());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", this.selecionado.getSubAgencia());
                        if (null == this.gtveAcesso
                                || null == this.gtveAcesso.getCodCli()
                                || gtveAcesso.getCodCli().equals("")) {
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("formalogin", "tradicional");
                        } else {
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", gtveAcesso.getNomePessoa());
                            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("formalogin", "gtveAcesso");
                        }
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.selecionado.getCodFil());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.selecionado.getNomeCli());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.selecionado.getOperador());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.selecionado.getNomeCli());
                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.selecionado.getCodGrupo());
                        if (null == this.gtveAcesso
                                || null == this.gtveAcesso.getCodCli()
                                || gtveAcesso.getCodCli().equals("")) {
                            FacesContext.getCurrentInstance().getExternalContext().redirect("guia/guiasclientes.xhtml");
                        } else {
                            if (portalExclusivo.equals("")) {
                                FacesContext.getCurrentInstance().getExternalContext().redirect("../guia/guiasclientes.xhtml");
                            } else {
                                FacesContext.getCurrentInstance().getExternalContext().redirect("../../guia/guiasclientes.xhtml");
                            }
                        }
                    }
                } catch (Exception e) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                }
            } else {
                PrimeFaces.current().ajax().update("cliente");
                PrimeFaces.current().executeScript("PF('dlgSelecionarCliente').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Duplo clique na tabela de seleção de clientes para usuários do nível
     * GTV-e
     *
     * @param event
     */
    public void dblSelectGTVIndex(SelectEvent event) {
        this.selecionado = (PessoaCliAut) event.getObject();
        this.verTodos = false;
        selecionarClienteGTVIndex();
    }

    /**
     * Seleciona o cliente para login de usuários do nivel GTV-e
     */
    public void selecionarClienteGTVIndex() {
        try {
            // Chave para utilizar no Google Maps
            ParametDao parametDao = new ParametDao();
            Paramet parametGoogle = parametDao.getParametGoogleApi(this.empresa.getBancoDados(), this.satellite);
            this.googleApiMob = parametGoogle.getGoogleApiMob();
            this.googleApiOper = parametGoogle.getGoogleApiOper();

            this.transpCacamba = parametGoogle.getTranspCacamba().equals("0") ? false : true;
            this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("0") ? false : true;
            this.firmaGTV = parametGoogle.getFirmaGtv().equals("0") ? false : true;

            getLogo(this.empresa.getBancoDados());

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("transpCacamba", this.transpCacamba);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("firmaGTV", this.firmaGTV);

            if (null == this.selecionado || this.verTodos) {
                gerarLog("Cliente selecionado - Todos");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "0");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
            } else {
                gerarLog("Cliente selecionado - " + this.selecionado.getCodCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", this.selecionado.getAgencia());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", this.selecionado.getSubAgencia());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.selecionado.getNomeCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.selecionado.getCodFil());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.selecionado.getOperador());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.selecionado.getCodGrupo());
            }
            gerarLog("Listando Permissoes - Saspwac");
            this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
            FacesContext.getCurrentInstance().getExternalContext().redirect("guia/guiasclientes.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Duplo clique na tabela de seleção de clientes para usuários do nível
     * GTV-e
     *
     * @param event
     */
    public void dblSelectGTV(SelectEvent event) {
        this.selecionado = (PessoaCliAut) event.getObject();
        this.verTodos = false;
        selecionarClienteGTV();
    }

    /**
     * Seleciona o cliente para login de usuários do nivel GTV-e
     */
    public void selecionarClienteGTV() {
        try {
            if (null == this.selecionado || this.verTodos) {
                gerarLog("Cliente selecionado - Todos");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "0");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
            } else {
                gerarLog("Cliente selecionado - " + this.selecionado.getCodCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.selecionado.getNomeCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", this.selecionado.getAgencia());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", this.selecionado.getSubAgencia());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.selecionado.getCodFil());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.selecionado.getOperador());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.selecionado.getCodGrupo());
            }
            gerarLog("Listando Permissoes - Saspwac");
            this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
            FacesContext.getCurrentInstance().getExternalContext().redirect("guiasclientes.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    // RELATÓRIOS EW //
    /**
     * Login para usuários de nível GTV-e; Se houver apenas um cliente
     * autorizado, o login é automático.
     *
     * @param emp - pessoalogin com codigo da pessoa, parametro e nivel
     */
    private void PortalSatMobEW(PessoaLogin emp) {
        try {
            this.pp = this.pool.getConexao(emp.getBancoDados(), this.enderecoNavegador);
            if (this.login.SituacaoUsuario(this.email, this.pp).equals("B")) {
                throw new Exception("UsuarioBloqueado");
            }
            this.usuario = this.login.BuscaPessoa(emp.getCodPessoaBD(), this.pp);
            this.acesso.setCodigo(emp.getCodPessoaBD().toPlainString());
            this.postos = this.login.listarPostosCliente(this.acesso, false, this.pp);
            if (this.postos.isEmpty()) {
                throw new Exception("UsuarioSemClientes");
            } else if (this.postos.size() == 1) {
                try {
                    this.postoSelecionado = this.postos.get(0);
                    this.filial = this.permissao.buscarFilial(this.postoSelecionado.getCodFil().toString(), this.pp);
                    this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("posto", this.postoSelecionado.getSecao());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula", this.usuario.getMatr().toBigInteger().toString());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.postoSelecionado.getCodFil().toString());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.postoSelecionado.getCodGrupo());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.postoSelecionado.getOperador());
                    FacesContext.getCurrentInstance().getExternalContext().redirect("relatorio/portal.xhtml");
                } catch (Exception e) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                }
            } else {
                PrimeFaces.current().ajax().update("cliente");
                PrimeFaces.current().executeScript("PF('dlgSelecionarClienteEW').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Duplo clique na tabela de seleção de clientes para usuários do nível
     * Cofre inteligente
     *
     * @param event
     */
    public void dblSelectEW(SelectEvent event) {
        this.postoSelecionado = (PstServ) event.getObject();
        this.verTodos = false;
        selecionarClienteEW();
    }

    /**
     * Seleciona o cliente para login de usuários do portal EW
     */
    public void selecionarClienteEW() {
        try {
            if (null == this.postoSelecionado || this.verTodos) {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("posto", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "0");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.postos.get(0).getCodGrupo());
            } else {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("posto", this.postoSelecionado.getSecao());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.postoSelecionado.getCodFil().toString());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.postoSelecionado.getOperador());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.postoSelecionado.getCodGrupo());
            }
            this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula", this.usuario.getMatr().toBigInteger().toString());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
            FacesContext.getCurrentInstance().getExternalContext().redirect("portal.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Duplo clique na tabela de seleção de clientes para usuários do nível
     * Cofre inteligente
     *
     * @param event
     */
    public void dblSelectEWIndex(SelectEvent event) {
        this.postoSelecionado = (PstServ) event.getObject();
        this.verTodos = false;
        selecionarClienteEWIndex();
    }

    /**
     * Seleciona o cliente para login de usuários do portal EW
     */
    public void selecionarClienteEWIndex() {
        try {
            if (null == this.postoSelecionado || this.verTodos) {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("posto", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "0");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.postos.get(0).getCodGrupo());
            } else {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("posto", this.postoSelecionado.getSecao());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.postoSelecionado.getCodFil().toString());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.postoSelecionado.getOperador());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.postoSelecionado.getCodGrupo());
            }
            this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("matricula", this.usuario.getMatr().toBigInteger().toString());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
            FacesContext.getCurrentInstance().getExternalContext().redirect("relatorio/portal.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    // COFRE INTELIGENTE //
    /**
     * Login para usuários de nível cofre inteligente; Se houver apenas um
     * cliente autorizado, o login é automático.
     *
     * @param emp - pessoalogin com codigo da pessoa, parametro e nivel
     */
    private void Cofre(PessoaLogin emp) {
        try {
            this.pp = this.pool.getConexao(emp.getBancoDados(), this.enderecoNavegador);
            if (this.login.SituacaoUsuario(this.email, this.pp).equals("B")) {
                throw new Exception("UsuarioBloqueado");
            }
            this.acesso.setCodigo(emp.getCodPessoaBD().toPlainString());
//            this.clientes = this.login.ListarClientes(this.acesso, false, this.pp);
//            if (this.clientes.isEmpty()) {
//                throw new Exception("UsuarioSemClientes");
//            } else {
            try {
//                    this.selecionado = this.clientes.get(0);
//                    this.filial = this.permissao.buscarFilial(this.selecionado.getCodFil(), this.pp);
                this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("menu", false);
                FacesContext.getCurrentInstance().getExternalContext().redirect("cofre/cofre.xhtml");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
//            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }
//    private void Cofre(PessoaLogin emp) {
//        try {
//            this.pp = this.pool.getConexao(emp.getBancoDados());
//            if (this.login.SituacaoUsuario(this.email, this.pp).equals("B")) {
//                throw new Exception("UsuarioBloqueado");
//            }
//            this.acesso.setCodigo(emp.getCodPessoaBD().toPlainString());
//            this.clientes = this.login.ListarClientes(this.acesso, false, this.pp);
//            if (this.clientes.isEmpty()) {
//                throw new Exception("UsuarioSemClientes");
//            } else if (this.clientes.size() == 1) {
//                try {
//                    this.selecionado = this.clientes.get(0);
//                    this.filial = this.permissao.buscarFilial(this.selecionado.getCodFil(), this.pp);
//                    this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", emp.getNivel());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", emp.getBancoDados());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.selecionado.getCodFil());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.selecionado.getCodGrupo());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.selecionado.getOperador());
//                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("menu", false);
//                    FacesContext.getCurrentInstance().getExternalContext().redirect("cofre/cofre.xhtml");
//                } catch (Exception e) {
//                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
//                }
//            } else {
//                PrimeFaces.current().ajax().update("clienteCofre");
//                PrimeFaces.current().executeScript("PF('dlgClienteCofre').show();");
//            }
//        } catch (Exception e) {
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
//            this.logerro.Grava(log, caminho);
//        }
//    }

    /**
     * Duplo clique na tabela de seleção de clientes para usuários do nível
     * Cofre inteligente
     *
     * @param event
     */
    public void dblSelectCofreIndex(SelectEvent event) {
        this.selecionado = (PessoaCliAut) event.getObject();
        this.verTodos = false;
        selecionarClienteCofreIndex();
    }

    /**
     * Seleciona o cliente para login de usuários do cofre inteligente na página
     * index
     */
    public void selecionarClienteCofreIndex() {
        try {
            if (null == this.selecionado || this.verTodos) {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "0");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
//                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
                this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                FacesContext.getCurrentInstance().getExternalContext().redirect("cofre/cofres.xhtml");
            } else {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.selecionado.getCodFil());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.selecionado.getOperador());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.selecionado.getCodGrupo());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codcofre", this.selecionado.getCodCofre());
                this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                FacesContext.getCurrentInstance().getExternalContext().redirect("cofre/movimentacao.xhtml");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Seleciona o cliente para login de usuários do cofre inteligente na página
     * index
     */
    public void abrirCofreMenu() {
        try {
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("menu", true);
            //FacesContext.getCurrentInstance().getExternalContext().redirect("cofre/cofre.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }


    public void abrirCofreMovMenu() {
        try {
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("menu", true);
            //FacesContext.getCurrentInstance().getExternalContext().redirect("cofre/cofre.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Seleciona o cliente para login de usuários do cofre inteligente na página
     * index
     */
    public void abrirDashboardCofreMenu() {
        try {
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("menu", true);
            //FacesContext.getCurrentInstance().getExternalContext().redirect("cofre/dashboard_geral.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void dblSelectCofre(SelectEvent event) {
        this.selecionado = (PessoaCliAut) event.getObject();
        this.verTodos = false;
        selecionarClienteCofre();
    }

    /**
     * Seleciona o cliente para login de usuários do cofre inteligente
     */
    public void selecionarClienteCofre() {
        try {
            if (null == this.selecionado || this.verTodos) {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", "0");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", " ");
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.clientes.get(0).getCodGrupo());
                this.permissoes = this.login.ListarPermissoes(this.acesso.getCodigo(), this.pp);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                FacesContext.getCurrentInstance().getExternalContext().redirect("cofres.xhtml");
            } else {
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.selecionado.getCodFil());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.selecionado.getOperador());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codgrupo", this.selecionado.getCodGrupo());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codcofre", this.selecionado.getCodCofre());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nome", this.usuario.getNome());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("empresas", this.empresas);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nivel", this.empresa.getNivel());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("codpessoa", this.acesso.getCodigo());
                FacesContext.getCurrentInstance().getExternalContext().redirect("movimentacao.xhtml");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Relatório quilometragem transporter
     */
    private String dataRelatorioKMIni, dataRelatorioKMFim;
    private List<Pessoa> prestadoresRelatoriokM;
    private Pessoa prestadorRelatorio;
    private boolean existeDataRelatorioKMFim, existePessoaRelatorioKMm;
    private List<KMPrestador> trajetoPercorrido;
    private StreamedContent arquivoDownload;

    public void novoRelatorioKM() {
        this.existeDataRelatorioKMFim = false;
        this.existePessoaRelatorioKMm = false;
        this.dataRelatorioKMIni = null;
        this.dataRelatorioKMFim = null;
        this.prestadorRelatorio = null;
        this.prestadoresRelatoriokM = new ArrayList<>();
        this.trajetoPercorrido = new ArrayList<>();
        PrimeFaces.current().resetInputs("formRelatorios");
    }

    public void selecionarDataFimRelatorioKM() {
        try {
            this.prestadoresRelatoriokM = this.login.listarPrestadoresServicoDatas(this.dataRelatorioKMIni, this.dataRelatorioKMFim, this.pp);
            if (this.prestadoresRelatoriokM.isEmpty()) {
                throw new Exception("SemPrestadorIntervalo");
            } else {
                this.existeDataRelatorioKMFim = true;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("DataInvalida"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.logerro.Grava(e.getMessage(), "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.pp.getEmpresa() + "\\" + DataAtual.getDataAtual("SQL") + "_" + this.empresa.getCodPessoaBD().toBigInteger() + ".txt");
        }
    }

    public List<Pessoa> listaPrestadoresRelatorioKM(String query) {
        List<Pessoa> prestadores = new ArrayList<>();
        try {
            this.prestadoresRelatoriokM.stream().filter((p) -> (p.getNome().contains(query.toUpperCase()))).forEachOrdered((p) -> {
                prestadores.add(p);
            });
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.logerro.Grava(e.getMessage(), "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.pp.getEmpresa() + "\\" + DataAtual.getDataAtual("SQL") + "_" + this.empresa.getCodPessoaBD().toBigInteger() + ".txt");
        }
        return prestadores;
    }

    public void selecionarPrestadoresRelatorioKM(SelectEvent event) {
        this.prestadorRelatorio = (Pessoa) event.getObject();
        try {
            this.trajetoPercorrido = this.login.obterRelatorioKMPrestador(this.prestadorRelatorio.getCodigo().toString(), this.dataRelatorioKMIni, this.dataRelatorioKMFim, this.pp);
            this.existePessoaRelatorioKMm = true;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.logerro.Grava(e.getMessage(), "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.pp.getEmpresa() + "\\" + DataAtual.getDataAtual("SQL") + "_" + this.empresa.getCodPessoaBD().toBigInteger() + ".txt");
        }
    }

    public void gerarRelatorioKMPrestador() {
        try {
            String relatorio = LerArquivo.obterConteudo(LoginMB.class
                    .getResourceAsStream("relatorios/kmprestador/relatorio.html"));
            String linha = LerArquivo.obterConteudo(LoginMB.class
                    .getResourceAsStream("relatorios/kmprestador/linha.html"));
            String coluna = LerArquivo.obterConteudo(LoginMB.class
                    .getResourceAsStream("relatorios/kmprestador/coluna.html"));

            StringBuilder tBody = new StringBuilder();
            StringBuilder TR;

            BigDecimal distTotal = BigDecimal.ZERO;
            for (KMPrestador kMPrestador : this.trajetoPercorrido) {
                TR = new StringBuilder();
                TR.append(coluna.replace("@TextoTD", Mascaras.Data(kMPrestador.getData()))
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center; min-width: 70px").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", kMPrestador.getOrigem())
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", kMPrestador.getDestino())
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                distTotal = distTotal.add(new BigDecimal(kMPrestador.getKmPercorrido()));
                TR.append(coluna.replace("@TextoTD", String.format("%.2f", new BigDecimal(kMPrestador.getKmPercorrido())))
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                TR.append(coluna.replace("@TextoTD", "")
                        .replace("@ColspanTD", "").replace("@RowspanTD", "")
                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "")
                        .replace("@ClassTD", ""));
                tBody.append(linha.replace("@ClassTR", "").replace("@StyleTR", "")
                        .replace("@TextoTR", TR));

            }

            TR = new StringBuilder();
            TR.append(coluna.replace("@TextoTD", "Total")
                    .replace("@ColspanTD", "5").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", String.format("%.2f", distTotal))
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", "")
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", "")
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", "")
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", "")
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", "")
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", "")
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", "")
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 1px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            TR.append(coluna.replace("@TextoTD", "")
                    .replace("@ColspanTD", "").replace("@RowspanTD", "")
                    .replace("@StyleTD", "font-size: 12px; background: #f2f2f2; border-top: 2px solid black; border-right: 2px solid black; text-align: center;")
                    .replace("@WidthTD", "").replace("@ClassTD", ""));
            tBody.append(linha.replace("@ClassTR", "").replace("@StyleTR", "font-weight: bold")
                    .replace("@TextoTR", TR));

            relatorio = relatorio.replace("@NomeColaborador", this.prestadorRelatorio.getNome())
                    .replace("@CPFColaborador", Mascaras.CPF(this.prestadorRelatorio.getCPF()))
                    .replace("@CargoColaborador", this.prestadorRelatorio.getFuncao())
                    .replace("@EndeColaborador", this.prestadorRelatorio.getEndereco())
                    .replace("@MatriculaColaborador", "")
                    .replace("@VeiculoColaborador", "")
                    .replace("@PlacaColaborador", "")
                    .replace("@MesRefColaborador", Mascaras.Data(this.dataRelatorioKMIni) + " - " + Mascaras.Data(this.dataRelatorioKMFim))
                    .replace("@BancoColaborador", "")
                    .replace("@AgenciaColaborador", "")
                    .replace("@ContaColaborador", "")
                    .replace("@ProjetoColaborador", "")
                    .replace("@FilialColaborador", this.filial.getDescricao())
                    .replace("@GestorColaborador", "")
                    .replace("@RegiaoColaborador", "")
                    .replace("@TBody", tBody)
                    .replace("@AdiantamentoDespesaViagem", "")
                    .replace("@RealizadoDespesaViagem", "")
                    .replace("@SaldoDespesaViagem", "")
                    .replace("@SituacaoDespesaViagem", "")
                    .replace("@PlanejadoDeslocamentoKM", "")
                    .replace("@RealizadoDeslocamentoKM", String.format("%.2f", distTotal))
                    .replace("@SaldoDeslocamentoKM", "")
                    .replace("@SituacaoDeslocamentoKM", "")
                    .replace("@AdiantamentoDeslocamentosRS", "")
                    .replace("@RealizadoDeslocamentosRS", "")
                    .replace("@SaldoDeslocamentosRS", "")
                    .replace("@SituacaoDeslocamentosRS", "</br>");

            InputStream stream = new ByteArrayInputStream(relatorio.getBytes());
            String nomeArquivo = getMessageS("RelatorioKMPrestador") + ".pdf";

            ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
            ITextRenderer renderer = new ITextRenderer();
            Tidy tidy = new Tidy();
            tidy.setShowWarnings(false);
            Document doc = tidy.parseDOM(stream, null);
            renderer.setDocument(doc, null);
            renderer.layout();
            renderer.createPDF(osPdf);

            InputStream inputPDF = new ByteArrayInputStream(osPdf.toByteArray());

            this.arquivoDownload = new DefaultStreamedContent(inputPDF, "pdf", nomeArquivo);
            osPdf.close();
            stream.close();
            inputPDF.close();

//            File newHtmlFile = new File("C:\\Users\\<USER>\\Desktop\\New Text Document.html");
//            FileUtils.writeStringToFile(newHtmlFile, relatorio, StandardCharsets.UTF_8);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.logerro.Grava(e.getMessage(), "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.pp.getEmpresa() + "\\" + DataAtual.getDataAtual("SQL") + "_" + this.empresa.getCodPessoaBD().toBigInteger() + ".txt");
        }
    }

    public boolean isExistePessoaRelatorioKMm() {
        return existePessoaRelatorioKMm;
    }

    public void setExistePessoaRelatorioKMm(boolean existePessoaRelatorioKMm) {
        this.existePessoaRelatorioKMm = existePessoaRelatorioKMm;
    }

    public String getDataRelatorioKMIni() {
        return dataRelatorioKMIni;
    }

    public void setDataRelatorioKMIni(String dataRelatorioKMIni) {
        this.dataRelatorioKMIni = dataRelatorioKMIni;
    }

    public String getDataRelatorioKMFim() {
        return dataRelatorioKMFim;
    }

    public void setDataRelatorioKMFim(String dataRelatorioKMFim) {
        this.dataRelatorioKMFim = dataRelatorioKMFim;
    }

    public List<Pessoa> getPrestadoresRelatoriokM() {
        return prestadoresRelatoriokM;
    }

    public void setPrestadoresRelatoriokM(List<Pessoa> prestadoresRelatoriokM) {
        this.prestadoresRelatoriokM = prestadoresRelatoriokM;
    }

    public Pessoa getPrestadorRelatorio() {
        return prestadorRelatorio;
    }

    public void setPrestadorRelatorio(Pessoa prestadorRelatorio) {
        this.prestadorRelatorio = prestadorRelatorio;
    }

    public boolean isExisteDataRelatorioKMFim() {
        return existeDataRelatorioKMFim;
    }

    public void setExisteDataRelatorioKMFim(boolean existeDataRelatorioKMFim) {
        this.existeDataRelatorioKMFim = existeDataRelatorioKMFim;
    }

    public StreamedContent getArquivoDownload() {
        return arquivoDownload;
    }

    public void setArquivoDownload(StreamedContent arquivoDownload) {
        this.arquivoDownload = arquivoDownload;
    }

    // TROCA DE SENHA //
    public void naoPossuo() {
        if (this.naoPossuoSenhaDia) {
            try {
                this.rand = (int) Math.floor(Math.random() * 3 + 1);
                this.validacao1 = new String();
                this.validacao2 = new String();
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.email + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        } else {
            this.rand = 0;
        }
    }

    public void preTrocarSenha() {
        this.senhaEsquecida = null;
        this.naoPossuoSenhaDia = false;
        this.email = new String();
        this.rand = 0;
        PrimeFaces.current().resetInputs("esqueciSenha");
        PrimeFaces.current().ajax().update("esqueciSenha");
    }

    public void trocarSenha() {
        try {
            if (this.naoPossuoSenhaDia) {
                String[] dadosFuncionario = this.email.split("@");
                this.param = dadosFuncionario[0];
                this.matricula = dadosFuncionario[1];
                this.pp = this.pool.getConexao(getParametro(this.param), this.enderecoNavegador);
                if (null == this.pp) {
                    throw new Exception("EmpresaErrada");
                }
                if (this.param.toUpperCase().contains("CORPVS")) {
                    throw new Exception("SenhaIndisponivelProcureRH");
                }
                Pessoa recuperaSenha = this.portalrh.PrimeiroAcesso(this.matricula, this.pp);
                if (null == recuperaSenha) {
                    throw new Exception("MatriculaInvalida");
                }
                switch (this.rand) {
                    case 1: //RG e CPF
                        if (!this.validacao1.toUpperCase().equals(recuperaSenha.getRG().toUpperCase())
                                || !this.validacao2.toUpperCase().equals(recuperaSenha.getCPF().toUpperCase())) {
                            throw new Exception("ErroValidacao");
                        }
                        break;
                    case 2: //cidade e CPF
                        if (!this.validacao1.toUpperCase().equals(recuperaSenha.getCidade().toUpperCase())
                                || !this.validacao2.toUpperCase().equals(recuperaSenha.getCPF().toUpperCase())) {
                            throw new Exception("ErroValidacao");
                        }
                        break;
                    case 3: //cidade e data nascimento
                        if (!this.validacao1.toUpperCase().equals(recuperaSenha.getCidade().toUpperCase())
                                || !this.validacao2.toUpperCase().equals(recuperaSenha.getDt_nasc())) {
                            throw new Exception("ErroValidacao");
                        }
                        break;
                    default:
                        throw new Exception("ErroValidacao");
                }
                this.senhaEsquecida = this.login.esqueciSenha(recuperaSenha, this.pp);
            } else {
                this.satellite = this.pool.getConexao("SATELLITE", this.enderecoNavegador);
                this.login.senhaDia(this.senhaDia, this.email, this.getClass().getResource("mapconect.txt").getPath(), this.satellite);
                PrimeFaces.current().executeScript("PF('dlgEsqueciSenha').hide()");
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SenhaSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void manual() {
        try {
            String Arquivo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("/WEB-INF/Arquivos/Login.pdf");
            File file = new File(Arquivo);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int tamanho = fis.available();
            byte[] buffer = new byte[tamanho];
            int bytesRead;
            while ((bytesRead = fis.read(buffer, 0, tamanho)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            fis.close();
            byte[] arquivo = baos.toByteArray();
            FacesContext.getCurrentInstance().getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
            response.setContentLength(arquivo.length);
            response.setHeader("Content-disposition", "inline; filename=\"manual.pdf\"");
            OutputStream ouputStream = response.getOutputStream();
            ouputStream.write(arquivo, 0, arquivo.length);
            ouputStream.flush();
            ouputStream.close();
            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void atualizaMensagem() {
        try {
            this.avisoportal = this.portalrh.buscaMensagem(this.filial.getCodfilAc(), this.pp);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void funcaoIndisponivel() {
        FacesContext context = FacesContext.getCurrentInstance();
        context.addMessage(null, new FacesMessage(Messages.getMessageS("FuncaoIndisponivel")));
    }

    public void CodigoValida() {
        this.fp = new FPMensal();
        try {
            StringTokenizer tok = new StringTokenizer(this.validadorCC, ".");
            String parametro, codMovFP;
            int num = tok.countTokens();
            if (num < 7) {
                throw new Exception("CodigoInvalido");
            } else {
                this.matricula = tok.nextToken();
                this.codFil = tok.nextToken();
                codMovFP = tok.nextToken();
                if (codMovFP.equals("0")) {
                    this.fp.setCodMovFP(tok.nextToken());
                } else {
                    this.fp.setCodMovFP(codMovFP);
                }
                this.fp.setTipoFP(tok.nextToken());
                parametro = tok.nextToken();
                this.pp = this.pool.getConexao(getParametro(parametro), this.enderecoNavegador);
                if (null == this.pp) {
                    throw new Exception("CodigoInvalido");
                }
                if (!tok.nextToken().equals(this.fp.getCodMovFP().toPlainString().substring(2, 4) + "/20" + this.fp.getCodMovFP().toPlainString().substring(0, 2))) {
                    throw new Exception("CodigoInvalido");
                }
                if (!"VCC".equals(tok.nextToken())) {
                    throw new Exception("CodigoInvalido");
                }

                PDF pdf = new PDF("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                        + this.pp.getEmpresa() + "\\Contracheques\\Validacao", this.matricula + ".pdf");
                String composicao = this.fp.getCodMovFP().toPlainString().substring(2, 4) + "/20" + this.fp.getCodMovFP().toPlainString().substring(0, 2);

                ContraChequeSatWeb contrachequemobweb = new ContraChequeSatWeb();
                List<ContraCheque> contracheque = contrachequemobweb.getCabecalhoCC(this.fp.getCodMovFP().toPlainString(), this.matricula, this.fp.getTipoFP(), this.pp);
                pdf.CabecalhoContraCheque(FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator),
                        this.pp.getEmpresa(), composicao, contracheque.get(0), this.fp.getTipoFP());
                pdf.CorpoContraChequeTitulo();

                contracheque = contrachequemobweb.getComposicaoCC(this.fp.getCodMovFP().toPlainString(), this.matricula, this.fp.getTipoFP(), this.pp);
                BigDecimal Acumula_liquido = new BigDecimal("0.00");
                BigDecimal Acumula_desconto = new BigDecimal("0.00");

                for (ContraCheque cc : contracheque) {
                    pdf.CorpoContraChequeLinhas(composicao, cc, 0);
                    if (cc.getfPLancamentos().getTipo().equals("V")) {
                        Acumula_liquido = Acumula_liquido.add(cc.getfPLancamentos().getValorCalc());
                    } else {
                        Acumula_desconto = Acumula_desconto.add(cc.getfPLancamentos().getValorCalc());
                    }
                }
                pdf.CorpoContraChequeLinhas("", new ContraCheque(), 1);
                contracheque = contrachequemobweb.getBaseCC(this.fp.getCodMovFP().toPlainString(), this.matricula, this.fp.getTipoFP(), this.pp);
                contracheque.get(0).getfPMensal().setProventos(Acumula_liquido.toString());
                contracheque.get(0).getfPMensal().setDescontos(Acumula_desconto.toString());
                contracheque.get(0).getfPMensal().setLiquido(Acumula_liquido.subtract(Acumula_desconto).toString());
                String codigo = this.matricula + "." + this.codFil
                        + "." + this.fp.getCodMovFP().toPlainString().substring(0, 4)
                        + "." + this.fp.getTipoFP() + "." + parametro + "." + composicao + ".VCC";
                pdf.RodapeContraCheque(contracheque.get(0), codigo);

                contrachequemobweb.geraLogContraCheque(this.matricula, this.codFil, composicao, this.fp.getTipoFP(), this.pp);
                pdf.FechaPdf();
                pdf.service();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("CodigoInvalido"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nCodigo: " + this.validadorCC + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    private String novaEmpresa;
    private SasPWFill novaFilial;
    private List<SasPWFill> novasFils;

    public void selecionarNovaFilial(SelectEvent event) {
        try {
            this.novaFilial = this.filial;
            this.filial = (SasPWFill) event.getObject();
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("banco", this.empresa.getBancoDados());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("filial", this.filial.getCodfilAc());
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeFilial", this.filial.getDescricao());
            FacesContext.getCurrentInstance().getExternalContext().redirect("configuracoes/acessos.xhtml?faces-redirect=true");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public List<SasPWFill> listarFils(String query) {
        try {
            List<SasPWFill> retorno = new ArrayList<>();
            for (SasPWFill f : this.novasFils) {
                if (null != f.getDescricao() && f.getDescricao().toUpperCase().contains(query.toUpperCase())) {
                    retorno.add(f);
                }
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public void tratarToken() throws Exception {
        try {
            this.nomeArq = "";

            if (null == this.token || this.token.equals("")) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("TokenNaoInformado"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                // Extrair Token
                criarPersistenciaSatellite();
                TOKENSDao tokensDao = new TOKENSDao();
                TOKENS token = tokensDao.obterToken(this.token, this.satellite);

                if (null == token || null == token.getBancoDados() || token.getBancoDados().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("TokenNaoEncontrado"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                } else {
                    // Criar Persistencia
                    HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance()
                            .getExternalContext()
                            .getRequest();

                    enderecoNavegador = request.getRequestURL().toString();
                    pp = pool.getConexao(token.getBancoDados(), enderecoNavegador);

                    carregarArrayDoctos();
                }
            }
        } catch (Exception ex) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, ex.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }
    
    public void conectarEmpresa() throws Exception {
        try {
            if (null == this.empresaBD || this.empresaBD.equals("")) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, 
                        Messages.getMessageS("TokenNaoInformado"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.empresaBD + "\\" + getDataAtual("SQL") + "\\" + "0.txt";
                HttpServletRequest request = (HttpServletRequest) FacesContext
                        .getCurrentInstance().getExternalContext().getRequest();
                enderecoNavegador = request.getRequestURL().toString();

                // Criar persistência
                criarPersistenciaSatellite();
                pp = pool.getConexao(empresaBD, enderecoNavegador);     
                
                ParametDao parametDao = new ParametDao();
                Paramet parametGoogle = parametDao.getParametGoogleApi(
                        this.pp.getEmpresa(), this.satellite);

                this.googleApiMob = parametGoogle.getGoogleApiMob();
                this.googleApiOper = parametGoogle.getGoogleApiOper();
                this.transpCacamba = parametGoogle.getTranspCacamba().equals("1");
                this.utilizaGTVe = parametGoogle.getUtilizaGTVe().equals("1");
                this.firmaGTV = parametGoogle.getFirmaGtv().equals("1");

                ExternalContext ec = FacesContext.getCurrentInstance().getExternalContext();
                ec.getSessionMap().put("transpCacamba", this.transpCacamba);
                ec.getSessionMap().put("utilizaGTVe", this.utilizaGTVe);
                ec.getSessionMap().put("firmaGTV", this.firmaGTV);                
                ec.getSessionMap().put("CodFil", this.codFil);                
            }
        } catch (Exception ex) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                    ex.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void HandleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                if (null == this.nomeArq || this.nomeArq.equals("")) {
                    this.nomeArq = "Nao Identificado";
                }

                // Carregar arquivo
                this.uploadedFile = fileUploadEvent.getFile();
                int Tamanho = (this.uploadedFile.getFileName().toString().length() - 4);

                String NomeArquivo = this.uploadedFile.getFileName().toString(),
                        ExtensaoArquivo = NomeArquivo.replace(NomeArquivo.substring(0, Tamanho), "");

                new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + pp.getEmpresa() + "\\Doctos").mkdirs();

                if (ExtensaoArquivo.indexOf(".") == -1) {
                    ExtensaoArquivo = "." + ExtensaoArquivo;
                }

                String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + pp.getEmpresa() + "\\Doctos\\" + this.nomeArq + ExtensaoArquivo;

                File validFile = new File(arquivo);

                if (validFile.exists()) {
                    arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + pp.getEmpresa() + "\\Doctos\\" + this.nomeArq + "_" + DataAtual.getDataAtual("SQL") + ExtensaoArquivo;
                }

                byte[] conteudo = fileUploadEvent.getFile().getContents();
                FileOutputStream fos = new FileOutputStream(arquivo);
                fos.write(conteudo);
                fos.close();

                // Atualiza em Página
                carregarArrayDoctos();

                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("ArquivoCarregadoComSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarArrayDoctos() {
        // Listar Arquivos
        File file = new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + pp.getEmpresa() + "\\Doctos");
        File[] arquivos = file.listFiles();
        String Arquivos = "";

        for (File fileTmp : arquivos) {
            int Tamanho = (fileTmp.getName().length() - 4);
            String ExtensaoArquivo = fileTmp.getName().replace(fileTmp.getName().substring(0, Tamanho), "");

            if (ExtensaoArquivo.indexOf(".") == -1) {
                ExtensaoArquivo = "." + ExtensaoArquivo;
            }

            if (!Arquivos.equals("")) {
                Arquivos += "=*=";
            }

            Arquivos += fileTmp.getName() + "|*|" + ExtensaoArquivo.toLowerCase() + "|*|" + "https://mobile.sasw.com.br:9091/satellite/documentos/" + pp.getEmpresa() + "/Doctos/" + fileTmp.getName();
        }

        PrimeFaces.current().executeScript("CarregarArquivos('" + Arquivos + "');");
    }

    public void carregarMapaTesouraria() throws Exception {
        // Listar Arquivos
        String Arquivos = "", Nivel = "", ExtensaoArquivo = "", LocalArmazenamento = "C:\\xampp\\htdocs\\satmobile\\documentos\\doctos\\" + pp.getEmpresa() + "\\MapaTesouraria";
        int Tamanho = 0;

        // Criar (se náo houver) e "setar"Local de Armazenamento
        new File(LocalArmazenamento).mkdirs();
        File[] listArquivos = new File(LocalArmazenamento).listFiles();

        // Consultar clientes permitidos
        try {
            FacesContext fc = FacesContext.getCurrentInstance();
            Nivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
            if (Nivel == null) {
                Nivel = "0";
            }
        } catch (Exception e) {
            Nivel = "0";
        }

        PessoaCliAut pessoa = new PessoaCliAut();
        pessoa.setCodigo(this.empresa.getCodPessoaBD().toPlainString());

        PessoaCliAutDao pessoaCliAutDao = new PessoaCliAutDao();
        List<PessoaCliAut> listaClientes = pessoaCliAutDao.listarClientes(pessoa, true, this.pp);
        
        TesWEBRelatDao tesWEBRelatDao = new TesWEBRelatDao();
        //float codigoFilial = Float.parseFloat(this.codFil);
        float codigoFilial = 0;
        List<TesWEBRelat> listaRelatorio;
        if (selecionado != null){
          listaRelatorio =  tesWEBRelatDao.listarRelatorios(
                  selecionado.getCodCli(), codigoFilial, this.pp);
            TesWEBRelat relatorio = new TesWEBRelat();
            relatorio.setDescricao("27-10-2024.pdf");
            //relatorio.setURL("https://mobile.sasw.com.br:9091/satmobile/documentos/doctos/SATFEDERAL/MapaTesouraria/27-10-2024-9997007.pdf");        
            relatorio.setURL("https://saswsatellite.blob.core.windows.net/imagem/27-10-2024.pdf");        
            listaRelatorio.add(relatorio);

            relatorio = new TesWEBRelat();
            relatorio.setDescricao("28-10-2024.pdf");
            relatorio.setURL("https://saswsatellite.blob.core.windows.net/imagem/28-10-2024.pdf");        
            listaRelatorio.add(relatorio);

            for (TesWEBRelat itemRelatorio : listaRelatorio) {
                if (!Arquivos.equals("")) {
                    Arquivos += "=*=";
                }
                Arquivos += itemRelatorio.getDescricao()+ "|*|" + "pdf" + 
                        "|*|" + itemRelatorio.getURL();
            }            
        }        
        String vCodTes = this.selecionado.getCliDst();        
        
        // Percorrer Arquivos        
//        for (File fileTmp : listArquivos) {;
//            // Validar se usuário pode visualizar arquivo pelo Cód. Cliente ou Nível
//            try {
//                Tamanho = (fileTmp.getName().length() - 4);
//                ExtensaoArquivo = fileTmp.getName().replace(fileTmp.getName().substring(0, Tamanho), "");
//
//                if (ExtensaoArquivo.indexOf(".") == -1) {
//                    ExtensaoArquivo = "." + ExtensaoArquivo;
//                }
//
//                if (!Arquivos.equals("")) {
//                    Arquivos += "=*=";
//                }
//
//                if (fileTmp.getName().contains(vCodTes)) {
//                    Arquivos += fileTmp.getName() + "|*|" + ExtensaoArquivo.toLowerCase()
//                            + "|*|" + "https://mobile.sasw.com.br:9091/satmobile/documentos/doctos/"
//                            + pp.getEmpresa() + "/MapaTesouraria/" + fileTmp.getName();
//                }
//                //}
//            } catch (Exception ex) {
//            }
//        }

        PrimeFaces.current().executeScript("CarregarArquivos('" + Arquivos + "');");
    }

    public void selecionarNovaEmpresa(SelectEvent event) {
        try {
            this.pp = this.pool.getConexao((String) event.getObject(), this.enderecoNavegador);
            List<Filiais> ff = this.filiaissatmobweb.ListaFiliais(this.pp);
            this.novasFils = new ArrayList<>();
            for (Filiais f : ff) {
                SasPWFill fff = new SasPWFill();
                fff.setCodfilAc(f.getCodFil().toBigInteger().toString());
                fff.setDescricao(f.getDescricao());
                fff.setMatr("0");
                this.novasFils.add(fff);
            }
            this.filiais = this.novasFils;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.email + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    // PERMISSÕES
    public boolean existePermissao(String codigo) {
        if (null == this.permissoes || this.permissoes.isEmpty()) {
            return false;
        }

        Saspwac saspwac = new Saspwac();
        saspwac.setSistema(codigo);

        return this.permissoes.indexOf(saspwac) >= 0;
    }

    public boolean isPermissaoControleAcessos() {
        return existePermissao("60113");
    }

    public boolean isPermissaoFechadurasAbertura() {
        return existePermissao("10104");
    }

    public boolean isPermissaoSolicitacaoSenhaMobile() {
        return existePermissao("10104");
        // FIXME: 10109
    }

    public MenuModel getMenu() {
        int i = 0;
        this.menu = new DynamicMenuModel();
        try {
            DefaultSubMenu subMenu, subMenu1;
            DefaultMenuItem item;

            subMenu = new DefaultSubMenu(this.usuario.getNome(), "iconeHome fas fa-user");
            subMenu.setStyleClass("menuCabecalho");
            //        subMenu.setId(String.valueOf(i++));

            if (this.filiais.size() >= 1) {
                subMenu1 = new DefaultSubMenu(getMessageS("Filial"), "iconeHome fas fa-building");
                subMenu1.setId(String.valueOf(i++));
                for (SasPWFill f : this.filiais) {
                    item = new DefaultMenuItem(f.getDescricao());
                    item.setStyleClass("itemMenuCabecalho");
                    item.setParam(f.getCodfilAc(), f);
                    item.setCommand("#{login.alterarFilial(" + f.getCodfilAc() + ")}");
                    //                item.setId(String.valueOf(i++));
                    subMenu1.addElement(item);
                }
                subMenu.addElement(subMenu1);
            }

            subMenu1 = new DefaultSubMenu(getMessageS("Idioma"), "iconeHome fas fa-language");
            //        subMenu1.setId(String.valueOf(i++));
            item = new DefaultMenuItem(getMessageS("Espanhol"));
            item.setAjax(false);
            item.setCommand("#{localeController.setNumberAndUpdate(2)}");
            item.setIcon("iconeHome flag-icon flag-icon-es");
            //        item.setId(String.valueOf(i++));
            subMenu1.addElement(item);
            item = new DefaultMenuItem(getMessageS("Ingles"));
            item.setAjax(false);
            item.setCommand("#{localeController.setNumberAndUpdate(3)}");
            item.setIcon("iconeHome flag-icon flag-icon-us");
            //        item.setId(String.valueOf(i++));
            subMenu1.addElement(item);
            if (this.pp != null && !this.pp.getEmpresa().equals("SPM")) {
                item = new DefaultMenuItem(getMessageS("Portugues"));
                item.setAjax(false);
                item.setCommand("#{localeController.setNumberAndUpdate(1)}");
                item.setIcon("iconeHome flag-icon flag-icon-br");
                //        item.setId(String.valueOf(i++));
                subMenu1.addElement(item);
            }

            subMenu.addElement(subMenu1);

            item = new DefaultMenuItem(getMessageS("Configuracoes"));
            item.setIcon("iconeHome fas fa-cog");
            //        item.setId(String.valueOf(i++));
            subMenu.addElement(item);

            item = new DefaultMenuItem(getMessageS("Sair"));
            item.setIcon("iconeHome fas fa-sign-out-alt");
            item.setCommand("#{login.logOutRH}");
            //        item.setId(String.valueOf(i++));
            subMenu.addElement(item);

            this.menu.addElement(subMenu);
            this.menu.generateUniqueIds();
        } catch (Exception e) {

        }
        return menu;
    }

    public void criarPersistenciaSatellite() {
        try {
            HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance()
                    .getExternalContext()
                    .getRequest();
            enderecoNavegador = request.getRequestURL().toString();
            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\login\\"
                    + DataAtual.getDataAtual("SQL") + ".txt";
            gerarLog("Criando persistência central");
            satellite = pool.getConexao("SATELLITE", enderecoNavegador);
            gerarLog("Persistência central - OK");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nEMAIL: " + this.email + "\r\nSENHA: " + this.pwweb + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void criarPersistenciaEmpresa() {
        try {
            HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance()
                    .getExternalContext()
                    .getRequest();
            enderecoNavegador = request.getRequestURL().toString();
            String empresa = request.getParameter("empresa");
            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\login\\"
                    + DataAtual.getDataAtual("SQL") + ".txt";
            gerarLog("Criando persistência central");
            pp = pool.getConexao(empresa, enderecoNavegador);
            gerarLog("Persistência central - OK");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nEMAIL: " + this.email + "\r\nSENHA: " + this.pwweb + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void setMenu(MenuModel menu) {
        this.menu = menu;
    }

    public List<String> listaEmpresas(String query) {
        List<String> emps = new ArrayList<>();
        if ("SATFIDELYS".contains(query.toUpperCase())) {
            emps.add("SATFIDELYS");
        }
        if ("SATFIDELYS1".contains(query.toUpperCase())) {
            emps.add("SATFIDELYS1");
        }
        if ("SATINVLRO".contains(query.toUpperCase())) {
            emps.add("SATINVLRO");
        }
        if ("SATPISCINAFACIL".contains(query.toUpperCase())) {
            emps.add("SATPISCINAFACIL");
        }
        if ("SATBIMBO".contains(query.toUpperCase())) {
            emps.add("SATBIMBO");
        }
        if ("SATTRANSPORTER".contains(query.toUpperCase())) {
            emps.add("SATTRANSPORTER");
        }
        if ("SATASC".contains(query.toUpperCase())) {
            emps.add("SATASC");
        }
        if ("SATSERVITE".contains(query.toUpperCase())) {
            emps.add("SATSERVITE");
        }
        if ("SATPROSECUR".contains(query.toUpperCase())) {
            emps.add("SATPROSECUR");
        }
        if ("TECNOSEG".contains(query.toUpperCase())) {
            emps.add("TECNOSEG");
        }
        if ("CONFEDERAL".contains(query.toUpperCase())) {
            emps.add("CONFEDERAL");
        }
        if ("CONFEDERALGO".contains(query.toUpperCase())) {
            emps.add("CONFEDERALGO");
        }
        if ("SATCONFEDERALBSB".contains(query.toUpperCase())) {
            emps.add("SATCONFEDERALBSB");
        }
        if ("SATCONFEDERALGO".contains(query.toUpperCase())) {
            emps.add("SATCONFEDERALGO");
        }
        if ("SATCORPVS".contains(query.toUpperCase())) {
            emps.add("SATCORPVS");
        }
        if ("SATCORPVSPE".contains(query.toUpperCase())) {
            emps.add("SATCORPVSPE");
        }
        if ("SATTRANSVIP".contains(query.toUpperCase())) {
            emps.add("SATTRANSVIP");
        }
        if ("SATPRESERVE".contains(query.toUpperCase())) {
            emps.add("SATPRESERVE");
        }
        if ("VSG".contains(query.toUpperCase())) {
            emps.add("VSG");
        }
        if ("SATAGIL".contains(query.toUpperCase())) {
            emps.add("SATAGIL");
        }
        if ("SATAGILVIG".contains(query.toUpperCase())) {
            emps.add("SATAGILVIG");
        }
        if ("SATAGILCOND".contains(query.toUpperCase())) {
            emps.add("SATAGILCOND");
        }
        if ("SATLOYAL".contains(query.toUpperCase())) {
            emps.add("SATLOYAL");
        }
        if ("SATTRANSVIG".contains(query.toUpperCase())) {
            emps.add("SATTRANSVIG");
        }
        if ("SATINVLMT".contains(query.toUpperCase())) {
            emps.add("SATINVLMT");
        }
        if ("SATINVLRS".contains(query.toUpperCase())) {
            emps.add("SATINVLRS");
        }
        if ("SASEX".contains(query.toUpperCase())) {
            emps.add("SASEX");
        }
        if ("SATGSI".contains(query.toUpperCase())) {
            emps.add("SATGSI");
        }
        if ("SATEXCEL".contains(query.toUpperCase())) {
            emps.add("SATEXCEL");
        }

        if ("SATTRANSEXCEL".contains(query.toUpperCase())) {
            emps.add("SATTRANSEXCEL");
        }
        if ("SATRODOB".contains(query.toUpperCase())) {
            emps.add("SATRODOB");
        }
        if ("SATRODOBAN".contains(query.toUpperCase())) {
            emps.add("SATRODOBAN");
        }
        if ("SATTAMEME".contains(query.toUpperCase())) {
            emps.add("SATTAMEME");
        }
        if ("SATCOMETRA".contains(query.toUpperCase())) {
            emps.add("SATCOMETRA");
        }
        if ("SATSASEX".contains(query.toUpperCase())) {
            emps.add("SATSASEX");
        }
        if ("EAGSATI".contains(query.toUpperCase())) {
            emps.add("EAGSATI");
        }
        if ("EAGSAS".contains(query.toUpperCase())) {
            emps.add("EAGSAS");
        }
        if ("SAS".contains(query.toUpperCase())) {
            emps.add("SAS");
        }
        if ("SASW".contains(query.toUpperCase())) {
            emps.add("SASW");
        }
        if ("TESTE".contains(query.toUpperCase())) {
            emps.add("TESTE");
        }
        if ("SATELLITE".contains(query.toUpperCase())) {
            emps.add("SATELLITE");
        }
        if ("SATTSEG".contains(query.toUpperCase())) {
            emps.add("SATTSEG");
        }
        if ("CORPVSFOLHA".contains(query.toUpperCase())) {
            emps.add("CORPVSFOLHA");
        }
        if ("SATQUALIFOCO".contains(query.toUpperCase())) {
            emps.add("SATQUALIFOCO");
        }
        if ("SATCETSEG".contains(query.toUpperCase())) {
            emps.add("SATCETSEG");
        }
        if ("SATGOMETRIX".contains(query.toUpperCase())) {
            emps.add("SATGOMETRIX");
        }
        if ("SATGLOVAL".contains(query.toUpperCase())) {
            emps.add("SATGLOVAL");
        }
        if ("SATCOGAR".contains(query.toUpperCase())) {
            emps.add("SATCOGAR");
        }
        if ("SATINTERFORT".contains(query.toUpperCase())) {
            emps.add("SATINTERFORT");
        }
        if ("SATIBL".contains(query.toUpperCase())) {
            emps.add("SATIBL");
        }
        if ("SATGETLOCK".contains(query.toUpperCase())) {
            emps.add("SATGETLOCK");
        }
        if ("SATFENIXX".contains(query.toUpperCase())) {
            emps.add("SATFENIXX");
        }
        if ("SATVANTEC".contains(query.toUpperCase())) {
            emps.add("SATVANTEC");
        }
        if ("SATNORSERV".contains(query.toUpperCase())) {
            emps.add("SATNORSERV");
        }
        if ("SATBRASIFORT".contains(query.toUpperCase())) {
            emps.add("SATBRASIFORT");
        }
        if ("SATCIT".contains(query.toUpperCase())) {
            emps.add("SATCIT");
        }
        if ("SATSHALOM".contains(query.toUpperCase())) {
            emps.add("SATSHALOM");
        }
        if ("SATMAXIMA".contains(query.toUpperCase())) {
            emps.add("SATMAXIMA");
        }
        if ("SATASO".contains(query.toUpperCase())) {
            emps.add("SATASO");
        }
        /*if ("SATGRIFFO".contains(query.toUpperCase())) {
            emps.add("SATGRIFFO");
        }*/
        if ("SATSEFIX".contains(query.toUpperCase())) {
            emps.add("SATSEFIX");
        }
        if ("SATROMMA".contains(query.toUpperCase())) {
            emps.add("SATROMMA");
        }
        if ("SATCEFOR".contains(query.toUpperCase())) {
            emps.add("SATCEFOR");
        }
        if ("SATTAGUA".contains(query.toUpperCase())) {
            emps.add("SATTAGUA");
        }
        if ("SATTECBAN".contains(query.toUpperCase())) {
            emps.add("SATTECBAN");
        }
        if ("MSL".contains(query.toUpperCase())) {
            emps.add("MSL");
        }
        if ("G5".contains(query.toUpperCase())) {
            emps.add("SATG5ESTRELAS");
        }
        if ("SATGLOBAL".contains(query.toUpperCase())) {
            emps.add("SATGLOBAL");
        }
        if ("SATNSF".contains(query.toUpperCase())) {
            emps.add("SATNSF");
        }
        if ("SATPETGOLDEN".contains(query.toUpperCase())) {
            emps.add("SATPETGOLDEN");
        }
        if ("SATDELTACORP".contains(query.toUpperCase())) {
            emps.add("SATDELTACORP");
        }
        if ("SATFORCAALERTA".contains(query.toUpperCase())) {
            emps.add("SATFORCAALERTA");
        }       
        if ("SATFEDERAL".contains(query.toUpperCase())) {
            emps.add("SATFEDERAL");
        }    
        if ("SATBRINKSDEMO".contains(query.toUpperCase())) {
            emps.add("SATBRINKSDEMO");
        }
        if ("SATBRINKS".contains(query.toUpperCase())) {
            emps.add("SATBRINKS");
        }
        return emps;
    }

    public List<String> getListaEmpresas() {
        List<String> emps = new ArrayList<>();
        emps.add("SATFIDELYS1");
        emps.add("SATFIDELYS");
        emps.add("SATASO");
        emps.add("SATINVLRO");
        emps.add("SATPISCINAFACIL");
        emps.add("SATBIMBO");
        emps.add("SATECOVISAO");
        emps.add("SATTRANSPORTER");
        emps.add("SATASC");
        emps.add("SATSERVITE");
        emps.add("SATPROSECUR");
        emps.add("TECNOSEG");
        emps.add("CONFEDERAL");
        emps.add("CONFEDERALGO");
        emps.add("SATCONFEDERALBSB");
        emps.add("SATCONFEDERALGO");
        emps.add("SATCORPVS");
        emps.add("SATCORPVSPE");
        emps.add("SATTRANSVIP");
        emps.add("SATPRESERVE");
        emps.add("VSG");
        emps.add("SATAGIL");
        emps.add("SATAGILVIG");
        emps.add("SATAGILCOND");
        emps.add("SATLOYAL");
        emps.add("SATTRANSVIG");
        emps.add("SATINVLMT");
        emps.add("SATINVLRS");
        emps.add("SASEX");
        emps.add("SATGSI");
        emps.add("SATTRANSEXCEL");
        emps.add("SATEXCEL");
        emps.add("SATRODOB");
        emps.add("SATRODOBAN");
        emps.add("SATTAMEME");
        emps.add("SATCOMETRA");
        emps.add("SATSASEX");
        emps.add("EAGSATI");
        emps.add("EAGSAS");
        emps.add("SAS");
        emps.add("SASW");
        emps.add("TESTE");
        emps.add("SATELLITE");
        emps.add("SATTSEG");
        emps.add("CORPVSFOLHA");
        emps.add("SATQUALIFOCO");
        emps.add("SATCETSEG");
        emps.add("SATGOMETRIX");
        emps.add("SATGLOVAL");
        emps.add("SATCOGAR");
        emps.add("SATINTERFORT");
        emps.add("SATIBL");
        emps.add("SATGETLOCK");
        emps.add("SATFENIXX");
        emps.add("SATVANTEC");
        emps.add("SATNORSERV");
        emps.add("SATBRASIFORT");
        emps.add("SATCIT");
        emps.add("SATSHALOM");
        emps.add("SATMAXIMA");
        emps.add("MSL");
        emps.add("SATG5ESTRELAS");
        //emps.add("SATGRIFFO");
        emps.add("SATROMMA");
        emps.add("SATCEFOR");
        emps.add("SATTAGUA");
        emps.add("SATSEFIX");
        emps.add("SATGLOBAL");
        emps.add("SATNSF");
        emps.add("SATTECBAN");
        emps.add("SATPETGOLDEN");
        emps.add("SATDELTACORP");
        emps.add("SATFORCAALERTA");
        emps.add("SATBRINKSDEMO");
        emps.add("SATFEDERAL");

        return emps;
    }

    public Persistencia obterNovaPersistencia(String empresa) {
        try {
            return this.pool.getConexao(empresa, this.enderecoNavegador);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Traduz o parâmetro em nome de empresa
     *
     * @param nome
     * @return
     */
    public String NomeEmpresa(String nome) {
        return Messages.getMessageS(nome);
    }

    public String getParametro(String empresa) {
        String parametro;
        this.origem = "";
        switch (empresa.toUpperCase()) {
            case "FIDELYS":
            case "SATFIDELYS":
                this.origem = "_fidelys";
                parametro = "SATFIDELYS";
                break;
            case "FIDELYS1":
            case "SATFIDELYS1":
                this.origem = "_fidelys";
                parametro = "SATFIDELYS1";
                break;
            case "INVLRO":
            case "SATINVLRO":
                this.origem = "_invlro";
                parametro = "SATINVLRO";
                break;
            case "BIMBO":
            case "SATBIMBO":
                parametro = "SATBIMBO";
                break;
            case "PISCINAFACIL":
            case "SATPISCINAFACIL":
                this.origem = "_piscina";
                parametro = "SATPISCINAFACIL";
                break;
            case "ASC":
            case "SATASC":
                parametro = "SATASC";
                break;
            case "INTERFORT":
            case "SATINTERFORT":
                this.origem = "_interfort";
                parametro = "SATINTERFORT";
                break;
            case "SERVITE":
            case "SATSERVITE":
                this.origem = "_servite";
                parametro = "SATSERVITE";
                break;
            case "PROSECUR":
            case "SATPROSECUR":
                this.origem = "_prosecur";
                parametro = "SATPROSECUR";
                break;
            case "CONFEDERALGO":
            case "SATCONFEDERALGO":
                parametro = "SATCONFEDERALGO";
                this.origem = "_confederal";
                break;
            case "CONFEDERAL":
            case "SATCONFEDERAL":
            case "SATCONFEDERALBSB":
            case "CONFEDERALBSB":
                this.origem = "_confederal";
                parametro = "SATCONFEDERALBSB";
                break;
            case "TRANSFEDERAL":
            case "SATTRANSFEDERAL":
                this.origem = "_transfederal";
                parametro = "SATCONFEDERALBSB";
                break;
            case "CORPVS":
            case "SATCORPVS":
                parametro = "SATCORPVS";
                break;
            case "CORPVSPE":
            case "SATCORPVSPE":
                parametro = "SATCORPVSPE";
                break;
            case "TRANSVIP":
            case "SATTRANSVIP":
                parametro = "SATTRANSVIP";
                break;
            case "PRESERVE":
            case "SATPRESERVE":
                parametro = "SATPRESERVE";
                break;
            case "VSG":
            case "SATVSG":
                parametro = "VSG";
                break;
            case "TSEG":
            case "SATTSEG":
                parametro = "SATTSEG";
                break;
            case "AGIL":
            case "SATAGIL":
                this.origem = "_agil";
                parametro = "SATAGIL";
                break;
            case "AGILSERV":
            case "SATAGILSERV":
                this.origem = "_agil";
                parametro = "AGILSERV";
                break;
            case "AGILVIG":
            case "SATAGILVIG":
                this.origem = "_agil";
                parametro = "SATAGILVIG";
                break;
            case "AGILCOND":
            case "SATAGILCOND":
                parametro = "SATAGILCOND";
                break;
            case "LOYAL":
            case "SATLOYAL":
                parametro = "SATLOYAL";
                break;
            case "TRANSVIG":
            case "SATTRANSVIG":
                parametro = "SATTRANSVIG";
                break;
            case "INVLMT":
            case "SATINVLMT":
                parametro = "SATINVLMT";
                this.origem = "_invlmt";
                break;
            case "INVLRS":
            case "SATINVLRS":
                parametro = "SATINVLRS";
                break;
            case "GSI":
            case "SATGSI":
                parametro = "SATGSI";
                break;
            case "EXCEL":
            case "SATEXCEL":
                parametro = "SATEXCEL";
                break;
            case "TRANSEXCEL":
            case "SATTRANSEXCEL":
                parametro = "SATTRANSEXCEL";
                break;
            case "RODOB":
            case "SATRODOB":
                parametro = "SATRODOB";
                break;
            case "RODOBAN":
            case "SATRODOBAN":
                parametro = "SATRODOBAN";
                break;
            case "TAMEME":
            case "SATTAMEME":
                parametro = "SATTAMEME";
                break;
            case "COMETRA":
            case "SATCOMETRA":
                parametro = "SATCOMETRA";
                break;
            case "SAS":
            case "SATSAS":
                parametro = "SAS";
                break;
            case "SASEX":
            case "SATSASEX":
                parametro = "SATSASEX";
                break;
            case "SASW":
                parametro = "SASW";
                break;
            case "SATELLITE":
                parametro = "SATELLITE";
                break;
            case "QUALIFOCO":
                this.origem = "_qualifoco";
                parametro = "SATQUALIFOCO";
                break;
            case "IBL":
            case "SATIBL":
                this.origem = "_ibl";
                parametro = "SATIBL";
                break;
            case "TRANSPORTER":
            case "SATTRANSPORTER":
                this.origem = "_transporter";
                parametro = "SATTRANSPORTER";
                break;
            case "ECOVISAO":
            case "SATECOVISAO":
                this.origem = "_ecovisao";
                parametro = "SATECOVISAO";
                break;
            case "GETLOCK":
            case "SATGETLOCK":
                this.origem = "_getlock";
                parametro = "SATGETLOCK";
                break;
            case "VANTEC":
            case "SATVANTEC":
                this.origem = "_VANTEC";
                parametro = "SATVANTEC";
                break;
            case "NORSERV":
            case "SATNORSERV":
                this.origem = "_NORSERV";
                parametro = "SATNORSERV";
                break;
            case "CIT":
            case "SATCIT":
                this.origem = "_CIT";
                parametro = "SATCIT";
                break;
            case "SHALOM":
            case "SATSHALOM":
                this.origem = "_SHALOM";
                parametro = "SATSHALOM";
                break;
            case "SATMAXIMA":
            case "MAXIMA":
                this.origem = "_maxima";
                parametro = "SATMAXIMA";
                break;
            case "ASO":
            case "SATASO":
                parametro = "SATASO";
                break;
            case "TECBAN":
            case "SATTECBAN":
                parametro = "SATTECBAN";
                break;
            case "MSL":
                parametro = "MSL";
                break;
            case "SATG5ESTRELAS":
            case "G5ESTRELAS":
                parametro = "SATG5ESTRELAS";
                break;
            /*case "SATGRIFFO":
            case "GRIFFO":
                parametro = "SATGRIFFO";
                break;*/
            case "SATROMMA":
            case "ROMMA":
                parametro = "SATROMMA";
                break;
            case "SATCEFOR":
            case "CEFOR":
                parametro = "SATCEFOR";
                break;
            case "SATTAGUA":
            case "TAGUA":
                parametro = "SATTAGUA";
                break;
            case "SATBRASIFORT":
            case "BRASIFORT":
                parametro = "SATBRASIFORT";
                break;
            case "SATSEFIX":
            case "SEFIX":
                parametro = "SATSEFIX";
                break;
            case "GLOBAL":
            case "SATGLOBAL":
                parametro = "SATGLOBAL";
                break;
            case "SATNSF":
            case "NSF":
                parametro = "SATNSF";
                break;
            case "SATDELTACORP":
                parametro = "SATDELTACORP";
                break;                
            case "SATFORCAALERTA":
                parametro = "SATFORCAALERTA";
                break;        
            case "SATFEDERAL":
            case "FEDERAL":
                parametro = "SATFEDERAL";
                break;           
            case "SATPETGOLDEN":
            case "PETGOLDEN":
                parametro = "SATPETGOLDEN";
                break;                
            case "SATFORTEARARUAMA":
            case "FORTE ARARUAMA":
                parametro = "SATFORTEARARUAMA";
                break;                                
            case "SATBRINKSDEMO":
            case "BRINKS DEMO":
                parametro = "SATBRINKSDEMO";
                break;
            case "SATBRINKS":
            case "BRINKS":
                parametro = "SATBRINKS";
                break;    
            default:
                parametro = "";
        }
        return parametro;
    }

    public static String getLogoS(String banco) throws Exception {
        FacesContext fc = FacesContext.getCurrentInstance();
        String LogoSessao = (String) fc.getExternalContext().getSessionMap().get("LogoSessao");
        String Logo = "";

        if (null == LogoSessao || LogoSessao.equals("")) {
            ParametDao parametDao = new ParametDao();

            if (null == satelliteStatic) {
                poolStatic = new SasPoolPersistencia();
                if (null == CaminhoStatic) {
                    CaminhoStatic = (String) fc.getExternalContext().getSessionMap().get("CaminhoMap");
                }
                poolStatic.setCaminho(CaminhoStatic);
                satelliteStatic = poolStatic.getConexao(banco, "");
            }

            Logo = parametDao.getLogo(banco, satelliteStatic);

            if (!Logo.equals("")) {
                Logo = "https://mobile.sasw.com.br:9091/satellite/logos/" + Logo;
            } else {
                Logo = "https://mobile.sasw.com.br:9091/satellite/logos/logo.png";
            }

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("LogoSessao", Logo);
        } else {
            Logo = LogoSessao;
        }

        return Logo;

        /*String url;
        switch (banco) {
            case "SATFIDELYS":
            case "SATFIDELYS1":
                url = "../assets/logos/logo_Fidelys_satelliteWeb.png";
                break;
            case "SATINVLRO":
                url = "../assets/logos/logoinviseg.png";
                break;
            case "SATBIMBO":
                url = "../assets/logos/logo_Bimbo.png";
                break;
            case "SATPISCINAFACIL":
                url = "../assets/logos/logo-piscina.png";
                break;
            case "SATPROSECUR":
                url = "/assets/logos/LogoProsecur.jpg";
                break;
            case "SATIBL":
                url = "/assets/logos/logo_ibl.jpg";
                break;
            case "SATSERVITE":
                url = "/assets/logos/logo_SATSERVITE.jpg";
                break;
            case "SATINTERFORT":
                url = "/assets/logos/logo_interfort.jpg";
                break;
            case "SATGLOVAL":
                url = "/assets/logos/logo_gloval.png";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                try {
                FacesContext fc = FacesContext.getCurrentInstance();
                String CodigoFilial = (String) fc.getExternalContext().getSessionMap().get("filial");
                if (!CodigoFilial.equals("3001")) {
                    url = "/assets/logos/logo_confederal.jpg";
                } else {
                    url = "/assets/logos/logo_confere.jpg";
                }
            } catch (Exception ex) {
                url = "/assets/logos/logo_confederal.jpg";
            }

            break;
            case "SATCORPVS":
            case "SATCORPVSPE":
            case "SATNORSERV":
            case "SATVANTEC":
                url = "/assets/logos/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "/assets/logos/LogoTVip.jpg";
                break;
            case "SATPRESERVE":
                url = "/assets/logos/logo_preserve.jpg";
                break;
            case "VSG":
                url = "/assets/logos/logo_VSG.jpg";
                break;
            case "SATTSEG":
                url = "/assets/logos/logo_tecnoseg.jpg";
                break;
            case "SATLOYAL":
                url = "/assets/logos/logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
                url = "/assets/logos/logo_transvig.jpg";
                break;
            case "SATINVLMT":
            case "SATINVLRS":
                url = "/assets/logos/logo_invioseg.jpg";
                break;
            case "SATGSI":
                url = "/assets/logos/logo_GSI.jpg";
                break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "/assets/logos/LogoRDB.jpg";
                break;
            case "SATTAMEME":
                url = "/assets/logos/logo_tameme.jpg";
                break;
            case "SATCOMETRA":
                url = "/assets/logos/logo_GSI.jpg";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "/assets/logos/LogoSASEX.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "/assets/logos/eagle.jpg";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "/assets/logos/logosas.jpg";
                break;
            case "SATQUALIFOCO":
                url = "/assets/logos/logo_qualifoco.jpg";
                break;
            case "SATTRANSPORTER":
                url = "/assets/logos/Logo_Transporter.png";
                break;
            case "SATECOVISAO":
                url = "/assets/logos/logo_ecovisao.jpg";
                break;
            case "SATGETLOCK":
                url = "/assets/logos/logo_getlock.png";
                break;
            case "SATBRASIFORT":
                url = "/assets/logos/Logo_Brasifort.png";
                break;
            case "SATCIT":
                url = "/assets/img/logo_citcompany.png";
                break;
            case "SATSHALOM":
                url = "/assets/img/logo_shalom.png";
                break;
            case "SATMAXIMA":
                url = "/assets/img/logo_maxima.png";
                break;
            case "SATASO":
                url = "/assets/img/logo_sataso.png";
                break;
            default:
                url = "/assets/logos/logo.png";
        }
        return url;*/
    }

    public String getLogo(String banco) throws Exception {
        if (banco == "") {
            return "";
        } else {
            ParametDao parametDao = new ParametDao();
            FacesContext fc = FacesContext.getCurrentInstance();
            String LogoSessao = (String) fc.getExternalContext().getSessionMap().get("LogoSessao");
            String Logo = "";

            if (null != LogoSessao && !LogoSessao.equals("")) {
                Logo = LogoSessao;
            } else if (null == this.pp) {
                this.pp = this.pool.getConexao(banco, "");

                Logo = parametDao.getLogo(banco, this.pp);

                if (!Logo.equals("")) {
                    Logo = "https://mobile.sasw.com.br:9091/satellite/logos/" + Logo;
                } else {
                    Logo = "https://mobile.sasw.com.br:9091/satellite/logos/logo.png";
                }
            } else {
                Logo = parametDao.getLogo(banco, this.pp);

                if (!Logo.equals("")) {
                    Logo = "https://mobile.sasw.com.br:9091/satellite/logos/" + Logo;
                } else {
                    Logo = "https://mobile.sasw.com.br:9091/satellite/logos/logo.png";
                }
            }
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("LogoSessao", Logo);

            return Logo;
        }

        /*String url;
        this.origem = "";
        switch (banco) {
            case "SATFIDELYS":
            case "SATFIDELYS1":
                this.origem = "_fidelys";
                url = "../assets/logos/logo_Fidelys_satelliteWeb.png";
                break;
            case "SATINVLRO":
                this.origem = "_invlro";
                url = "../assets/logos/logoinviseg.png";
                break;
            case "SATBIMBO":
                url = "../assets/logos/logo_Bimbo.png";
                break;
            case "SATPISCINAFACIL":
                this.origem = "_piscina";
                url = "../assets/logos/logo-piscina.png";
                break;
            case "SATPROSECUR":
                this.origem = "_prosecur";
                url = "../assets/logos/LogoProsecur.jpg";
                break;
            case "SATIBL":
                this.origem = "_ibl";
                url = "../assets/logos/logo_ibl.jpg";
                break;
            case "SATSERVITE":
                this.origem = "_servite";
                url = "../assets/logos/logo_SATSERVITE.jpg";
                break;
            case "SATINTERFORT":
                this.origem = "_interfort";
                url = "../assets/logos/logo_interfort.jpg";
                break;
            case "SATGLOVAL":
                this.origem = "_gloval";
                url = "../assets/logos/logo_gloval.png";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                this.origem = "_confederal";
                try {
                    FacesContext fc = FacesContext.getCurrentInstance();
                    String CodigoFilial = (String) fc.getExternalContext().getSessionMap().get("filial");
                    if (!CodigoFilial.equals("3001")) {
                        url = "../assets/logos/logo_confederal.jpg";
                    } else {
                        url = "../assets/logos/logo_confere.jpg";
                    }
                } catch (Exception ex) {
                    url = "../assets/logos/logo_confederal.jpg";
                }
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
            case "SATNORSERV":
            case "SATVANTEC":
                url = "../assets/logos/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "../assets/logos/LogoTVip.jpg";
                break;
            case "SATPRESERVE":
                url = "../assets/logos/logo_preserve.jpg";
                break;
            case "VSG":
                url = "../assets/logos/logo_VSG.jpg";
                break;
            case "SATTSEG":
                url = "../assets/logos/logo_tecnoseg.jpg";
                break;
            case "SATASC":
                url = "../assets/logos/logoasc.jpg";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
                this.origem = "_agil";
                url = "../assets/logos/logo_AGIL.jpg";
                break;
            case "SATLOYAL":
                url = "../assets/logos/logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
                url = "../assets/logos/logo_transvig.jpg";
                break;
            case "SATINVLMT":
                this.origem = "_invlmt";
            case "SATINVLRS":
                url = "../assets/logos/logo_invioseg.jpg";
                break;
            case "SATGSI":
                url = "../assets/logos/logo_GSI.jpg";
                break;
            case "SATTRANSEXCEL":
                try {
                switch (this.filial.getCodfilAc()) {
                    case "1":
                        url = "../assets/logos/logotransexcel.jpg";
                        break;
                    case "2001":
                        url = "../assets/logos/logoDepa.jpg";
                        break;
                    case "3001":
                        url = "../assets/logos/logoExcel.jpg";
                        break;
                    default:
                        url = "../assets/logos/logotransexcel.jpg";
                }
            } catch (Exception e) {
                url = "../assets/logos/logotransexcel.jpg";
            }
            break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "../assets/logos/LogoRDB.jpg";
                break;
            case "SATTAMEME":
                url = "../assets/logos/logo_tameme.jpg";
                break;
            case "SATCOMETRA":
                url = "../assets/logos/logo_GSI.jpg";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "../assets/logos/LogoSASEX.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "../assets/logos/eagle.jpg";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "../assets/logos/logosas.jpg";
                break;
            case "SATQUALIFOCO":
                this.origem = "_qualifoco";
                url = "../assets/logos/logo_qualifoco.jpg";
                break;
            case "SATTRANSPORTER":
                url = "../assets/logos/Logo_Transporter.png";
                break;
            case "SATECOVISAO":
                url = "../assets/logos/logo_ecovisao.jpg";
                break;
            case "SATGETLOCK":
                url = "../assets/logos/logo_getlock.png";
                break;
            case "SATBRASIFORT":
                url = "../assets/logos/Logo_Brasifort.png";
                break;
            case "SATCIT":
                url = "../assets/logos/logo_citcompany.png";
                break;
            case "SATSHALOM":
                url = "../assets/logos/logo_shalom.png";
                break;
            case "SATMAXIMA":
                url = "../assets/logos/logo_maxima.png";
                this.origem = "_maxima";
                break;
            case "SATASO":
                url = "../assets/logos/logo_sataso.png";
                //this.origem = "_sataso";
                break;
            default:
                url = "../assets/logos/logo.png";
        }
        return url;*/
    }

    public void getLogoPonto() throws Exception {
        ParametDao parametDao = new ParametDao();
        String banco = "";
        try {
            String Logo = "";
            banco = Faces.getRequestCookie("paramSAS");

            this.pp = this.pool.getConexao(banco, "");

            Logo = parametDao.getLogo(banco, this.pp);

            if (!Logo.equals("")) {
                Logo = "https://mobile.sasw.com.br:9091/satellite/logos/" + Logo;
            } else {
                Logo = "https://mobile.sasw.com.br:9091/satellite/logos/logo.png";
            }

            this.webPontoLogo = Logo;
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("LogoSessao", Logo);
        } catch (Exception ex) {
            this.webPontoLogo = "https://mobile.sasw.com.br:9091/satellite/logos/logo.png";

            if (null != banco && !banco.equals("")) {
                PrimeFaces.current().executeScript("ParamInvalido()");
            }
        }
    }

    public void selecionarCliente() {
        FacesContext fc = FacesContext.getCurrentInstance();
        if (null == this.selecionado || this.selecionado.equals("")) {
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", "");
        } else {
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.selecionado.getCodCli());
        }
    }

    public String getLogo(String banco, String codFil) throws Exception {
        ParametDao parametDao = new ParametDao();

        if (null == this.pp) {
            this.pp = this.pool.getConexao(banco, "");
        }

        String Logo = parametDao.getLogo(banco, codFil, this.pp);

        if (!Logo.equals("")) {
            Logo = "https://mobile.sasw.com.br:9091/satellite/logos/" + Logo;
        } else {
            Logo = "https://mobile.sasw.com.br:9091/satellite/logos/logo.png";
        }

        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("LogoSessao", Logo);

        return Logo;

        /*String url;
        this.origem = "";
        switch (banco) {
            case "SATFIDELYS":
            case "SATFIDELYS1":
                this.origem = "_fidelys";
                url = "../assets/logos/logo_Fidelys_satelliteWeb.png";
                break;
            case "SATINVLRO":
                this.origem = "_invlro";
                url = "../assets/logos/logoinviseg.png";
                break;
            case "SATBIMBO":
                url = "../assets/logos/logo_Bimbo.png";
                break;
            case "SATPISCINAFACIL":
                this.origem = "_piscina";
                url = "../assets/logos/logo-piscina.png";
                break;
            case "SATPROSECUR":
                this.origem = "_prosecur";
                url = "../assets/logos/LogoProsecur.jpg";
                break;
            case "SATIBL":
                this.origem = "_ibl";
                url = "../assets/logos/logo_ibl.jpg";
                break;
            case "SATSERVITE":
                this.origem = "_servite";
                url = "../assets/logos/logo_SATSERVITE.jpg";
                break;
            case "SATINTERFORT":
                this.origem = "_interfort";
                url = "../assets/logos/logo_interfort.jpg";
                break;
            case "SATGLOVAL":
                this.origem = "_gloval";
                url = "../assets/logos/logo_gloval.png";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                this.origem = "_confederal";
                try {
                    FacesContext fc = FacesContext.getCurrentInstance();
                    String CodigoFilial = (String) fc.getExternalContext().getSessionMap().get("filial");
                    if (!CodigoFilial.equals("3001")) {
                        url = "../assets/logos/logo_confederal.jpg";
                    } else {
                        url = "../assets/logos/logo_confere.jpg";
                    }
                } catch (Exception ex) {
                    url = "../assets/logos/logo_confederal.jpg";
                }
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
            case "SATNORSERV":
            case "SATVANTEC":
                url = "../assets/logos/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "../assets/logos/LogoTVip.jpg";
                break;
            case "SATPRESERVE":
                url = "../assets/logos/logo_preserve.jpg";
                break;
            case "VSG":
                url = "../assets/logos/logo_VSG.jpg";
                break;
            case "SATTSEG":
                url = "../assets/logos/logo_tecnoseg.jpg";
                break;
            case "SATASC":
                url = "../assets/logos/logoasc.jpg";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
                this.origem = "_agil";
                url = "../assets/logos/logo_AGIL.jpg";
                break;
            case "SATLOYAL":
                url = "../assets/logos/logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
                url = "../assets/logos/logo_transvig.jpg";
                break;
            case "SATINVLMT":
                this.origem = "_invlmt";
            case "SATINVLRS":
                url = "../assets/logos/logo_invioseg.jpg";
                break;
            case "SATGSI":
                url = "../assets/logos/logo_GSI.jpg";
                break;
            case "SATTRANSEXCEL":
                try {
                switch (this.filial.getCodfilAc()) {
                    case "1":
                        url = "../assets/logos/logotransexcel.jpg";
                        break;
                    case "2001":
                        url = "../assets/logos/logoDepa.jpg";
                        break;
                    case "3001":
                        url = "../assets/logos/logoExcel.jpg";
                        break;
                    default:
                        url = "../assets/logos/logotransexcel.jpg";
                }
            } catch (Exception e) {
                url = "../assets/logos/logotransexcel.jpg";
            }
            break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "../assets/logos/LogoRDB.jpg";
                break;
            case "SATTAMEME":
                url = "../assets/logos/logo_tameme.jpg";
                break;
            case "SATCOMETRA":
                url = "../assets/logos/logo_GSI.jpg";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "../assets/logos/LogoSASEX.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "../assets/logos/eagle.jpg";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "../assets/logos/logosas.jpg";
                break;
            case "SATQUALIFOCO":
                this.origem = "_qualifoco";
                url = "../assets/logos/logo_qualifoco.jpg";
                break;
            case "SATTRANSPORTER":
                url = "../assets/logos/Logo_Transporter.png";
                break;
            case "SATECOVISAO":
                url = "../assets/logos/logo_ecovisao.jpg";
                break;
            case "SATGETLOCK":
                url = "../assets/logos/logo_getlock.png";
                break;
            case "SATBRASIFORT":
                url = "../assets/logos/Logo_Brasifort.png";
                break;
            case "SATCIT":
                url = "../assets/logos/logo_citcompany.png";
                break;
            case "SATSHALOM":
                url = "../assets/logos/logo_shalom.png";
                break;
            case "SATMAXIMA":
                url = "../assets/logos/logo_maxima.png";
                this.origem = "_maxima";
                break;
            case "SATASO":
                url = "../assets/logos/logo_sataso.png";
                //this.origem = "_sataso";
                break;
            default:
                url = "../assets/logos/logo.png";
        }
        return url;*/
    }

    /*   public void getLogoPonto() {
        try {
            String url = getLogo(Faces.getRequestCookie("paramSAS"));

            if (null == url || url.equals("")) {
                url = "../assets/logos/logo_SASW.jpg";
            }

            this.webPontoLogo = url;
        } catch (Exception e) {
            this.webPontoLogo = "../assets/logos/logo_SASW.jpg";
        }
    }*/
    public String getLogoRH(String banco) throws Exception {
        ParametDao parametDao = new ParametDao();
        SasPoolPersistencia poolStatic = new SasPoolPersistencia();
        poolStatic.setCaminho(CaminhoStatic);

        if (null == this.pp) {
            this.pp = poolStatic.getConexao(banco, "");
        }

        String Logo = parametDao.getLogo(banco, pp);

        if (!Logo.equals("")) {
            Logo = "https://mobile.sasw.com.br:9091/satellite/logos/" + Logo;
        } else {
            Logo = "https://mobile.sasw.com.br:9091/satellite/logos/logo.png";
        }

        return Logo;

        /*String url = "";
        switch (banco) {
            case "SATFIDELYS":
            case "SATFIDELYS1":
                url = "../assets/logos/logo_Fidelys_satelliteWeb.png";
                break;
            case "SATINVLRO":
                url = "../assets/logos/logoinviseg.png";
                break;
            case "SATBIMBO":
                url = "../assets/logos/logo_Bimbo.png";
                break;
            case "SATPISCINAFACIL":
                url = "../assets/logos/logo-piscina.png";
                break;
            case "SATPROSECUR":
                url = "../assets/logos/LogoProsecur.jpg";
                break;
            case "SATIBL":
                url = "../assets/logos/logo_ibl.jpg";
                break;
            case "SATINTERFORT":
                url = "../assets/logos/logo_interfort.jpg";
                break;
            case "SATSERVITE":
                url = "../assets/logos/logo_SATSERVITE.jpg";
                break;
            case "SATGLOVAL":
                url = "../assets/logos/logo_gloval.png";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                url = "../assets/img/logo.png";
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
            case "SATNORSERV":
            case "SATVANTEC":
                url = "../assets/img/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "../assets/img/TRANSVIP.gif";
                break;
            case "SATPRESERVE":
                url = "../assets/img/logo.png";
                break;
            case "VSG":
                url = "../assets/img/VSG.gif";
                break;
            case "SATASC":
                url = "../assets/logos/logoasc.jpg";
                break;
            case "SATTSEG":
                url = "../assets/img/TECNOSEG.gif";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
                url = "../assets/img/AGIL.gif";
                break;
            case "SATLOYAL":
                url = "../assets/img/LOYAL.gif";
                break;
            case "SATTRANSVIG":
                url = "../assets/img/TRANSVIG.gif";
                break;
            case "SATINVLMT":
                this.origem = "_invlmt";
                url = "../assets/img/INVLMT.gif";
                break;
            case "SATINVLRS":
                url = "../assets/img/INVLRS.gif";
                break;
            case "SATGSI":
                url = "../assets/img/GSI.gif";
                break;
            case "SATTRANSEXCEL":
                try {
                switch (this.filial.getCodfilAc()) {
                    case "1":
                        url = "../assets/logos/logotransexcel.jpg";
                        break;
                    case "2001":
                        url = "../assets/logos/logoDepa.jpg";
                        break;
                    case "3001":
                        url = "../assets/logos/logoExcel.jpg";
                        break;
                    default:
                        url = "../assets/logos/logotransexcel.jpg";
                }
            } catch (Exception e) {
                url = "../assets/logos/logotransexcel.jpg";
            }
            break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "../assets/img/logo.png";
                break;
            case "SATTAMEME":
                url = "../assets/img/logo.png";
                break;
            case "SATCOMETRA":
                url = "../assets/img/logo.png";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "../assets/img/logo.png";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "../assets/img/logo.png";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "../assets/img/logo.png";
                break;
            case "SATTRANSPORTER":
                url = "../assets/img/Logo_Transporter.png";
                break;
            case "SATECOVISAO":
                url = "../assets/logos/logo_ecovisao.jpg";
                break;
            case "SATGETLOCK":
                url = "../assets/logos/logo_getlock.png";
                break;
            case "SATBRASIFORT":
                url = "../assets/logos/Logo_Brasifort.png";
                break;
            case "SATCIT":
                url = "../assets/logos/logo_citcompany.png";
                break;
            case "SATSHALOM":
                url = "../assets/logos/logo_shalom.png";
                break;
            case "SATMAXIMA":
//                url = "../assets/logos/logo_maxima.png";
                break;
            case "SATASO":
                //url = "../assets/logos/logo_sataso.png";
                break;
            default:
                url = "../assets/logos/logo.png";
                break;
        }
        return url;*/
    }

    public String getNovaEmpresa() {
        return novaEmpresa;
    }

    public void setNovaEmpresa(String novaEmpresa) {
        this.novaEmpresa = novaEmpresa;
    }

    public SasPWFill getNovaFilial() {
        return novaFilial;
    }

    public void setNovaFilial(SasPWFill novaFilial) {
        this.novaFilial = novaFilial;
    }

    public List<SasPWFill> getNovasFils() {
        return novasFils;
    }

    public void setNovasFils(List<SasPWFill> novasFils) {
        this.novasFils = novasFils;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPwweb() {
        return pwweb;
    }

    public void setPwweb(String pwweb) {
        if (null != pwweb && !pwweb.equals("")) {
            this.pwweb = pwweb;
        }
    }

    public List<PessoaLogin> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<PessoaLogin> empresas) {
        this.empresas = empresas;
    }

    public PessoaLogin getEmpresa() {
        return empresa;
    }

    public void setEmpresa(PessoaLogin empresa) {
        this.empresa = empresa;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public List<SasPWFill> getFiliais() {
        return filiais;
    }

    public void setFiliais(List<SasPWFill> filiais) {
        this.filiais = filiais;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public Pessoa getUsuario() {
        return usuario;
    }

    public void setUsuario(Pessoa usuario) {
        this.usuario = usuario;
    }

    public List<PessoaCliAut> getClientes() {
        return clientes;
    }

    public void setClientes(List<PessoaCliAut> clientes) {
        this.clientes = clientes;
    }

    public PessoaCliAut getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(PessoaCliAut selecionado) {
        this.selecionado = selecionado;
    }

    public Boolean getVerTodos() {
        return verTodos;
    }

    public void setVerTodos(Boolean verTodos) {
        this.verTodos = verTodos;
    }

    public List<PessoaCliAut> getClientesFiltrados() {
        return clientesFiltrados;
    }

    public void setClientesFiltrados(List<PessoaCliAut> clientesFiltrados) {
        this.clientesFiltrados = clientesFiltrados;
    }

    public Persistencia getPp() {
        return pp;
    }

    public void setPp(Persistencia pp) {
        this.pp = pp;
    }

    public Persistencia getSatellite() {
        return satellite;
    }

    public void setSatellite(Persistencia satellite) {
        this.satellite = satellite;
    }

    public void setSenhaDia(String senhaDia) {
        this.senhaDia = senhaDia;
    }

    public String getSenhaDia() {
        return senhaDia;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getAno() {
        return ano;
    }

    public void setAno(String ano) {
        this.ano = ano;
    }

    public AvisoPortal getAvisoportal() {
        return avisoportal;
    }

    public void setAvisoportal(AvisoPortal avisoportal) {
        this.avisoportal = avisoportal;
    }

    public UsuarioSatMobWeb getFuncion() {
        return funcion;
    }

    public void setFuncion(UsuarioSatMobWeb funcion) {
        this.funcion = funcion;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getValidacao1() {
        return validacao1;
    }

    public void setValidacao1(String validacao1) {
        this.validacao1 = validacao1;
    }

    public String getValidacao2() {
        return validacao2;
    }

    public void setValidacao2(String validacao2) {
        this.validacao2 = validacao2;
    }

    public int getRand() {
        return rand;
    }

    public String getValidacao3() {
        return validacao3;
    }

    public void setValidacao3(String validacao3) {
        this.validacao3 = validacao3;
    }

    public String getValidadorCC() {
        return validadorCC;
    }

    public void setValidadorCC(String validadorCC) {
        this.validadorCC = validadorCC;
    }

    public SasPoolPersistencia getPool() {
        return pool;
    }

    public Boolean getNaoPossuoSenhaDia() {
        return naoPossuoSenhaDia;
    }

    public void setNaoPossuoSenhaDia(Boolean naoPossuoSenhaDia) {
        this.naoPossuoSenhaDia = naoPossuoSenhaDia;
    }

    public String getSenhaEsquecida() {
        return senhaEsquecida;
    }

    public void setSenhaEsquecida(String senhaEsquecida) {
        this.senhaEsquecida = senhaEsquecida;
    }

    public String getCli() {
        return cli;
    }

    public void setCli(String cli) throws Exception {
        this.cli = cli;
        getLogo(getParametro(this.cli));
    }

    public List<PstServ> getPostos() {
        return postos;
    }

    public void setPostos(List<PstServ> postos) {
        this.postos = postos;
    }

    public PstServ getPostoSelecionado() {
        return postoSelecionado;
    }

    public void setPostoSelecionado(PstServ postoSelecionado) {
        this.postoSelecionado = postoSelecionado;
    }

    public List<PstServ> getPostosFiltrados() {
        return postosFiltrados;
    }

    public void setPostosFiltrados(List<PstServ> postosFiltrados) {
        this.postosFiltrados = postosFiltrados;
    }

    public List<SasPWFill> getListaFiliais() {
        return listaFiliais;
    }

    public void setListaFiliais(List<SasPWFill> listaFiliais) {
        this.listaFiliais = listaFiliais;
    }

    public void gerarLog(String texto) {
        this.logerro.GravaMetodos(texto, this.caminho);
    }

    public Filiais getInfoFilial() {
        return infoFilial;
    }

    public void setInfoFilial(Filiais infoFilial) {
        this.infoFilial = infoFilial;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Boolean getExibirEmpresas() {
        return exibirEmpresas;
    }

    public void setExibirEmpresas(Boolean exibirEmpresas) {
        this.exibirEmpresas = exibirEmpresas;
    }

    public UsuarioSatMobWebServicos getUsuarios() {
        return usuarios;
    }

    public void setUsuarios(UsuarioSatMobWebServicos usuarios) {
        this.usuarios = usuarios;
    }

    public PessoaPortalSrv getPessoaPortalSrv() {
        return pessoaPortalSrv;
    }

    public void setPessoaPortalSrv(PessoaPortalSrv pessoaPortalSrv) {
        this.pessoaPortalSrv = pessoaPortalSrv;
    }

    public Persistencia_OLD getSatellite_old() {
        return satellite_old;
    }

    public String getEnderecoNavegador() {
        return enderecoNavegador;
    }

    public void setEnderecoNavegador(String enderecoNavegador) {
        this.enderecoNavegador = enderecoNavegador;
    }

    public String getWebPontoDescricao() {
        return webPontoDescricao;
    }

    public void setWebPontoDescricao(String webPontoDescricao) {
        this.webPontoDescricao = webPontoDescricao;
    }

    public String getWebPontoLocal() {
        return webPontoLocal;
    }

    public void setWebPontoLocal(String webPontoLocal) {
        this.webPontoLocal = webPontoLocal;
    }

    public String getWebPontoEscala() {
        return webPontoEscala;
    }

    public void setWebPontoEscala(String webPontoEscala) {
        this.webPontoEscala = webPontoEscala;
    }

    public String getWebPontoEnde() {
        return webPontoEnde;
    }

    public void setWebPontoEnde(String webPontoEnde) {
        this.webPontoEnde = webPontoEnde;
    }

    public String getWebPontoMatr() {
        return webPontoMatr;
    }

    public void setWebPontoMatr(String webPontoMatr) {
        this.webPontoMatr = webPontoMatr;
    }

    public String getWebPontoFuncao() {
        return webPontoFuncao;
    }

    public void setWebPontoFuncao(String webPontoFuncao) {
        this.webPontoFuncao = webPontoFuncao;
    }

    public String getWebPontoNomeGuer() {
        return webPontoNomeGuer;
    }

    public void setWebPontoNomeGuer(String webPontoNomeGuer) {
        this.webPontoNomeGuer = webPontoNomeGuer;
    }

    public String getWebPontoCodFil() {
        return webPontoCodFil;
    }

    public void setWebPontoCodFil(String webPontoCodFil) {
        this.webPontoCodFil = webPontoCodFil;
    }

    public String getWebPontoSecao() {
        return webPontoSecao;
    }

    public void setWebPontoSecao(String webPontoSecao) {
        this.webPontoSecao = webPontoSecao;
    }

    public String getWebPontoCodPessoa() {
        return webPontoCodPessoa;
    }

    public void setWebPontoCodPessoa(String webPontoCodPessoa) {
        this.webPontoCodPessoa = webPontoCodPessoa;
    }

    public String getWebPontoLogo() {
        return webPontoLogo;
    }

    public void setWebPontoLogo(String webPontoLogo) {
        this.webPontoLogo = webPontoLogo;
    }

    public String getGoogleApiMob() {
        return googleApiMob;
    }

    public void setGoogleApiMob(String googleApiMob) {
        this.googleApiMob = googleApiMob;
    }

    public String getGoogleApiOper() {
        return googleApiOper;
    }

    public void setGoogleApiOper(String googleApiOper) {
        this.googleApiOper = googleApiOper;
    }

    public Boolean getPermissao10207() {
        return Permissao10207;
    }

    public void setPermissao10207(Boolean Permissao10207) {
        this.Permissao10207 = Permissao10207;
    }

    public Boolean getPermissao10221() {
        return Permissao10221;
    }

    public void setPermissao10221(Boolean Permissao10221) {
        this.Permissao10221 = Permissao10221;
    }

    public Boolean getPermissao10311() {
        return Permissao10311;
    }

    public void setPermissao10311(Boolean Permissao10311) {
        this.Permissao10311 = Permissao10311;
    }

    public Boolean getPermissao10201() {
        return Permissao10201;
    }

    public void setPermissao10201(Boolean Permissao10201) {
        this.Permissao10201 = Permissao10201;
    }

    public Boolean getPermissao10101() {
        return Permissao10101;
    }

    public void setPermissao10101(Boolean Permissao10101) {
        this.Permissao10101 = Permissao10101;
    }

    public Boolean getPermissao10211() {
        return Permissao10211;
    }

    public void setPermissao10211(Boolean Permissao10211) {
        this.Permissao10211 = Permissao10211;
    }

    public Boolean getUtilizaGTVe() {
        return utilizaGTVe;
    }

    public void setUtilizaGTVe(Boolean utilizaGTVe) {
        this.utilizaGTVe = utilizaGTVe;
    }

    public Boolean getTranspCacamba() {
        return transpCacamba;
    }

    public void setTranspCacamba(Boolean transpCacamba) {
        this.transpCacamba = transpCacamba;
    }

    public String getLoginSatDesktopData() {
        return loginSatDesktopData;
    }

    public void setLoginSatDesktopData(String loginSatDesktopData) {
        this.loginSatDesktopData = loginSatDesktopData;
    }

    public String getLoginSatDesktopBase() {
        return loginSatDesktopBase;
    }

    public void setLoginSatDesktopBase(String loginSatDesktopBase) {
        this.loginSatDesktopBase = loginSatDesktopBase;
    }

    public String getLoginSatDesktopCodPessoa() {
        return loginSatDesktopCodPessoa;
    }

    public void setLoginSatDesktopCodPessoa(String loginSatDesktopCodPessoa) {
        this.loginSatDesktopCodPessoa = loginSatDesktopCodPessoa;
    }

    public String getLoginSatDesktopSeqRota() {
        return loginSatDesktopSeqRota;
    }

    public void setLoginSatDesktopSeqRota(String loginSatDesktopSeqRota) {
        this.loginSatDesktopSeqRota = loginSatDesktopSeqRota;
    }

    public String getNomeArq() {
        return nomeArq;
    }

    public void setNomeArq(String nomeArq) {
        this.nomeArq = nomeArq;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public List<Saspwac> getPermissoes() {
        return permissoes;
    }

    public void setPermissoes(List<Saspwac> permissoes) {
        this.permissoes = permissoes;
    }

    public String getPortalLogin() {
        return portalLogin;
    }

    public void setPortalLogin(String portalLogin) {
        this.portalLogin = portalLogin;
    }

    public String getPortalEmail() {
        return portalEmail;
    }

    public void setPortalEmail(String portalEmail) {
        this.portalEmail = portalEmail;
    }

    public String getPortalSenha() {
        return portalSenha;
    }

    public void setPortalSenha(String portalSenha) {
        this.portalSenha = portalSenha;
    }

    public String getWebPontoPw() {
        return webPontoPw;
    }

    public void setWebPontoPw(String webPontoPw) {
        this.webPontoPw = webPontoPw;
    }

    public String getTipoLogin() {
        return tipoLogin;
    }

    public void setTipoLogin(String tipoLogin) {
        this.tipoLogin = tipoLogin;
    }

    public String getEmpresaBD() {
        return empresaBD;
    }

    public void setEmpresaBD(String empresaBD) {
        this.empresaBD = empresaBD;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.operacoes;

import Arquivo.ArquivoLog;
import Controller.Login.LoginSatMobWeb;
import Controller.Pedidos.PedidosRefeicaoSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.PstServ;
import SasBeans.SasPWFill;
import SasBeansCompostas.PedidoRefeicao;
import SasBeansCompostas.PedidoRefeicaoItens;
import SasDaos.OS_VigDao;
import SasDaos.PedidoRefeicaoDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.managedBeans.comercial.ClientesMB;
import br.com.sasw.managedBeans.recursoshumanos.PostoServicoMB;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;

/**
 *
 * <AUTHOR>
 */
//@ManagedBean
@Named(value = "pedidosMB")
@ViewScoped
public class PedidosRefeicaoMB implements Serializable {

    private Persistencia persistencia;
    private String codFil, log, caminho, nomeFilial, banco, operador, d1, d2, coordenadas,
            numero, dataOrigem, dataDestino, hora1Origem, hora1Destino, dataTela;
    private BigDecimal valor;
    private Date dataSelecionada1, dataSelecionada2;
    private LocalDate data1, data2;
    private BigDecimal codPessoa;
    private ClientesMB clientesMB;
    private ArquivoLog logerro;
    private SasPWFill filial;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private Pedido pedidoSelecionado, pedido;
    private PedidoRefeicao pedidoRefeicao, pedidoRefeicaoSelecionado, pedidoRefeicaoSelecionadoGride;
    private PostoServicoMB postoServicoMB;
    private List<PstServ> listaPostoServico;
    private Map filters;
    private LoginSatMobWeb loginsatmobweb;
    private PedidosRefeicaoSatMobWeb pedidosController;
    private List<PedidoRefeicao> pedidos;
    private boolean mostrarFiliais, limparFiltros;
    private List<OS_Vig> listaOsVig, listaOsVigDestino;
    private OS_Vig osVigOrigem, osVigDestino;
    private Clientes clienteOrigem, clienteDestino;
    private List<Pedido> listaPedidoRefeicao;
    private PedidoRefeicaoItens pedidoRefeicaoItem;
    private List<PedidoRefeicaoItens> listaPedidoRefeicaoItens;
    private PedidoRefeicaoDao pedidoRefeicaoDao;

    public PedidosRefeicaoMB() {
        filial = new SasPWFill();
        numero = new String();
        mostrarFiliais = false;
        limparFiltros = false;
        pedidoSelecionado = new Pedido();
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        log = new String();
        logerro = new ArquivoLog();
        pedidosController = new PedidosRefeicaoSatMobWeb();
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, 1);
        dataSelecionada1 = new Date();//c.getTime();
        data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d1 = DataAtual.getDataAtual("SQL");//dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        c.set(Calendar.DAY_OF_MONTH, 2);
        dataSelecionada2 = new Date();//c.getTime();
        data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d2 = DataAtual.getDataAtual("SQL");//dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        loginsatmobweb = new LoginSatMobWeb();
        pedido = new Pedido();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");
        osVigOrigem = new OS_Vig();
        listaOsVig = new ArrayList<>();
        listaOsVig.add(osVigOrigem);
        osVigDestino = new OS_Vig();
        listaOsVigDestino = new ArrayList<>();
        listaOsVigDestino.add(osVigDestino);
        clienteOrigem = new Clientes();
        clienteDestino = new Clientes();
        listaPostoServico = new ArrayList<>();
        listaPedidoRefeicao = new ArrayList<>();
        pedidoRefeicaoDao = new PedidoRefeicaoDao();
    }

    public void persistencia(Persistencia pp) {
        try {
            persistencia = pp;
            if (null == persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            filters = new HashMap();
            filters.put(" pedido.codfil = ? ", Arrays.asList(codFil));
            filters.put(" pedido.data between ? and ? ", Arrays.asList(d1, d2));
            filiais = rotassatweb.buscaInfoFilial(codFil, persistencia);
            clientesMB = new ClientesMB(pp);
            postoServicoMB = new PostoServicoMB(pp);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
            System.out.println("Erro contagem - " + e.getMessage());
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);

            }
        }
    }

    public void dataAnterior() {
        try {
            if (null == data1) {
                Calendar c = Calendar.getInstance();
                c.setTime(Date.from(Instant.now()));
                c.set(Calendar.DAY_OF_MONTH, 1);
                dataSelecionada1 = c.getTime();
                data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                this.d1 = data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
                dataSelecionada2 = c.getTime();
                data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                d2 = data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            } else {
                Calendar c = Calendar.getInstance(), d = Calendar.getInstance();
                c.setTime(dataSelecionada1);
                c.add(Calendar.MONTH, -1);
                dataSelecionada1 = c.getTime();
                d.setTime(dataSelecionada2);
                d.add(Calendar.MONTH, -1);
                d.set(Calendar.DAY_OF_MONTH, d.getActualMaximum(Calendar.DAY_OF_MONTH));
                dataSelecionada2 = d.getTime();
                data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                d1 = data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                d2 = data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            filters.replace(" pedido.data between ? and ? ", Arrays.asList(d1, d2));
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            allPedidos();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void dataPosterior() {
        try {
            if (null == data1) {
                Calendar c = Calendar.getInstance();
                c.setTime(Date.from(Instant.now()));
                c.set(Calendar.DAY_OF_MONTH, 1);
                dataSelecionada1 = c.getTime();
                data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                d1 = data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
                dataSelecionada2 = c.getTime();
                data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                d2 = data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            } else {
                Calendar c = Calendar.getInstance(), d = Calendar.getInstance();
                c.setTime(dataSelecionada1);
                c.add(Calendar.MONTH, 1);
                d.setTime(dataSelecionada2);
                d.add(Calendar.MONTH, 1);
                d.set(Calendar.DAY_OF_MONTH, d.getActualMaximum(Calendar.DAY_OF_MONTH));
                dataSelecionada1 = c.getTime();
                dataSelecionada2 = d.getTime();
                data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                d1 = data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                d2 = data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            filters.replace(" pedido.data between ? and ? ", Arrays.asList(d1, d2));
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            allPedidos();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void escreverData1() {
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            dataSelecionada1 = df.parse(d1);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void escreverData2() {
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            dataSelecionada2 = df.parse(d2);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void selecionarData1(SelectEvent data) {
        dataSelecionada1 = (Date) data.getObject();
        data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d1 = data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        try {
            DateFormat format = new SimpleDateFormat("dd/MM/yyyy");

            Calendar dataInicio = new GregorianCalendar();
            Calendar dataFim = new GregorianCalendar();

            dataInicio.setTime(format.parse(d1.substring(6, 8) + "/" + d1.substring(4, 6) + "/" + d1.substring(0, 4)));
            dataFim.setTime(format.parse(d2.substring(6, 8) + "/" + d2.substring(4, 6) + "/" + d2.substring(0, 4)));

            if ((dataInicio.getTimeInMillis() > dataFim.getTimeInMillis())) {
                d2 = d1;
                data2 = data1;
                dataSelecionada2 = dataSelecionada1;
            }
        } catch (Exception e) {

        }
    }

    public void selecionarData2(SelectEvent data) {
        dataSelecionada2 = (Date) data.getObject();
        data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d2 = data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        try {
            DateFormat format = new SimpleDateFormat("dd/MM/yyyy");

            Calendar dataInicio = new GregorianCalendar();
            Calendar dataFim = new GregorianCalendar();

            dataInicio.setTime(format.parse(d1.substring(6, 8) + "/" + d1.substring(4, 6) + "/" + d1.substring(0, 4)));
            dataFim.setTime(format.parse(d2.substring(6, 8) + "/" + d2.substring(4, 6) + "/" + d2.substring(0, 4)));

            if ((dataInicio.getTimeInMillis() > dataFim.getTimeInMillis())) {
                d1 = d2;
                data1 = data2;
                dataSelecionada1 = dataSelecionada2;
            }
        } catch (Exception e) {

        }
    }

    public void selecionarData() {
        try {
            data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            d1 = data1.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            d2 = data2.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (dataSelecionada1.after(dataSelecionada2)) {
                throw new Exception("IntervaloInvalido");
            }
            filters.replace(" pedido.codfil = ? ", Arrays.asList(codFil));
            filters.replace(" pedido.data between ? and ? ", Arrays.asList(d1, d2));
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            allPedidos();
            dt.setFirst(0);

            PrimeFaces.current().ajax().update("msgs");
            PrimeFaces.current().ajax().update("main");
            PrimeFaces.current().ajax().update("cabecalho");
        } catch (Exception e) {
            //PrimeFaces.current().ajax().update("msgs");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    //public LazyDataModel<Pedido> allPedidos() {
    public void allPedidos() throws Exception {
        listaPedidoRefeicao = new ArrayList<>();
        pedidoRefeicaoSelecionadoGride = new PedidoRefeicao();

        this.pedidos = pedidoRefeicaoDao.listagemPedidos(this.codFil, d1, d2, this.persistencia);
        /* try {
            if (pedidos == null) {
                DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
                filters.replace(" pedido.codfil = ? ", Arrays.asList(codFil));
                filters.replace(" pedido.data between ? and ? ", Arrays.asList(d1, d2));
                dt.setFilters(filters);
                pedidos = new PedidosLazyList(persistencia, codPessoa);
            }
        } catch (Exception e) {

            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
            System.out.println("br.com.sasw.managedBeans.operacoes.PedidosMB.allPedidos()" + e.getMessage());
        }
        return pedidos;*/
    }

    public void preEdicao(ActionEvent actionEvent) {
        if (null == pedidoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePedido"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            pedido = pedidoSelecionado;
            PrimeFaces.current().resetInputs("formDetalhes");
            PrimeFaces.current().executeScript("PF('dlgEdicaoPedido').show();");
        }
    }

    public void dblSelect(SelectEvent event) {
        pedidoSelecionado = (Pedido) event.getObject();
        buscarPedidos(pedidoSelecionado.getNRed1());
        buscarPedidosDestino(pedidoSelecionado.getNRed2());
        preEdicao(null);
    }

    public void dblSelectRefeicao(SelectEvent event) throws Exception {
        preEdicaoRefeicao();
    }

    public void mostrarFiliais() throws Exception {
        if (mostrarFiliais) {
            filters.replace(" pedido.codfil = ? ", Arrays.asList());
        } else {
            filters.replace(" pedido.codfil = ? ", Arrays.asList(codFil));
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(filters);
        allPedidos();
        dt.setFirst(0);
    }

    public void limpaFiltros() throws Exception {
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, 1);
        dataSelecionada1 = c.getTime();
        data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataSelecionada2 = c.getTime();
        data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        d2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        filters = new HashMap();
        filters.put(" pedido.codfil = ? ", Arrays.asList(codFil));
        filters.put(" pedido.data between ? and ? ", Arrays.asList(d1, d2));
        mostrarFiliais = false;
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(filters);
        allPedidos();
        dt.setFirst(0);
        limparFiltros = false;
        mostrarFiliais = false;
    }

    public void pesquisaPaginada() throws Exception {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        pedidoSelecionado = pedido;
        pedidoSelecionado.setNumero(numero);

        if (null != pedidoSelecionado.getCodFil() && !pedidoSelecionado.getCodFil().equals(BigDecimal.ZERO)) {
            filters.replace(" pedido.codfil = ? ", Arrays.asList(pedidoSelecionado.getCodFil().toPlainString()));
        } else {
            filters.replace(" pedido.codfil = ? ", Arrays.asList());
        }
        if (!numero.equals("")) {
            filters.put(" pedido.numero like ? ", Arrays.asList("%" + pedidoSelecionado.getNumero().toPlainString() + "%"));
        }
        if (!pedidoSelecionado.getNRed1().equals("")) {
            filters.put(" pedido.nred1 like ? ", Arrays.asList("%" + pedidoSelecionado.getNRed1() + "%"));
        }
        if (!pedidoSelecionado.getNRed2().equals("")) {
            filters.put(" pedido.nred2 like ? ", Arrays.asList("%" + pedidoSelecionado.getNRed2() + "%"));
        }
        dt.setFilters(filters);
        allPedidos();
        dt.setFirst(0);

        PrimeFaces.current().ajax().update("msgs", "main", "cabecalho", "corporativo");
    }

    public void prePesquisa() {
        pedido = new Pedido();
        numero = new String();
    }

    public void preCadastro() {
        osVigOrigem = new OS_Vig();
        listaOsVig = new ArrayList<>();
        listaOsVig.add(osVigOrigem);
        osVigDestino = new OS_Vig();
        listaOsVigDestino = new ArrayList<>();
        listaOsVigDestino.add(osVigDestino);
        pedido = new Pedido();
        pedido.setDt_Incl(this.dataTela);
        clienteOrigem = new Clientes();
        clienteDestino = new Clientes();

        /*if (this.persistencia.getEmpresa().equals("SATMAXIMA")) {
            this.clientesMB.setCodigo("7770007");
            this.clientesMB.BuscarCliente();
            this.clienteOrigem = this.clientesMB.getLista().get(0);
        }*/
    }

    public void excluirPedidoRefeicao() {
        try {
            if (null != this.pedidoRefeicaoSelecionadoGride) {
                pedidoRefeicaoDao.excluirPedido(this.pedidoRefeicaoSelecionadoGride.getSequencia(), this.persistencia);
                allPedidos();
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExcluidoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void preCadastroRefeicao() {
        pedido = new Pedido();
        pedido.setDt_Incl(this.dataTela);
        clienteOrigem = new Clientes();
        clienteDestino = new Clientes();
        this.pedidoRefeicaoSelecionado = new PedidoRefeicao();
        listaPedidoRefeicao = new ArrayList<>();
        pedidoRefeicaoSelecionadoGride = new PedidoRefeicao();
    }

    public void preExclusaoRefeicao() {
        if (null != this.pedidoRefeicaoSelecionadoGride) {
            if (null == this.pedidoRefeicaoSelecionadoGride.getSituacao()
                    || this.pedidoRefeicaoSelecionadoGride.getSituacao().equals("")
                    || this.pedidoRefeicaoSelecionadoGride.getSituacao().equals("PD")) {
                PrimeFaces.current().executeScript("$.MsgBoxVerdeSimNao('" + Messages.getMessageS("Atencao") + "','"
                        + Messages.getMessageS("PerguntaPedido") + " " + this.pedidoRefeicaoSelecionadoGride.getSequencia().toPlainString() + " ?','"
                        + Messages.getMessageS("Sim") + "','"
                        + Messages.getMessageS("Nao") + "',"
                        + "function(){ execExclusao();});");
            }
            else{
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoPedidoNaoPermitida"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            PrimeFaces.current().ajax().update("msgs");
        }
    }

    public void preEdicaoRefeicao() throws Exception {
        if (null != this.pedidoRefeicaoSelecionadoGride) {
            if (null == this.pedidoRefeicaoSelecionadoGride.getSituacao()
                    || this.pedidoRefeicaoSelecionadoGride.getSituacao().equals("")
                    || this.pedidoRefeicaoSelecionadoGride.getSituacao().equals("PD")) {
                this.pedidoRefeicaoSelecionado = new PedidoRefeicao();
                this.pedidoRefeicaoSelecionado.setData(this.pedidoRefeicaoSelecionadoGride.getData());
                this.pedidoRefeicaoSelecionado.setSolicitante(this.pedidoRefeicaoSelecionadoGride.getSolicitante());
                this.pedidoRefeicaoSelecionado.setObs(this.pedidoRefeicaoSelecionadoGride.getObs());

                this.clientesMB.setCodigo(this.pedidoRefeicaoSelecionadoGride.getCodcli());
                this.clientesMB.setCodfil(this.pedidoRefeicaoSelecionadoGride.getCodFil());
                this.clientesMB.BuscarCliente();
                this.clienteDestino = this.clientesMB.getLista().get(0);

                this.listaPedidoRefeicao = new ArrayList<>();
                Pedido pedidoItem;

                List<PedidoRefeicaoItens> Itens = pedidoRefeicaoDao.listagemPedidosItens(this.pedidoRefeicaoSelecionadoGride.getSequencia(), this.persistencia);

                for (int i = 0; i < Itens.size(); i++) {
                    pedidoItem = new Pedido();

                    pedidoItem.setQtdeAlmoco(Itens.get(i).getQtdeAlmoco());
                    pedidoItem.setQtdeCafe(Itens.get(i).getQtdeCafe());
                    pedidoItem.setQtdeCeia(Itens.get(i).getQtdeCeia());
                    pedidoItem.setQtdeJanta(Itens.get(i).getQtdeJantar());
                    pedidoItem.setSecao(Itens.get(i).getSecao());
                    pedidoItem.setCli2Nred(Itens.get(i).getLocal());

                    this.listaPedidoRefeicao.add(pedidoItem);
                }

                PrimeFaces.current().executeScript("PF('dlgPedido').show();");
                PrimeFaces.current().executeScript("setTimeout(function(){ CalcularTotais(); }, 500);");
            }
            else{
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoPedidoNaoPermitida"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }

        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void selecionarOrigem(SelectEvent event) {
        Clientes cli = ((Clientes) event.getObject());
        this.clientesMB.setCodigo(cli.getCodigo());
        this.clientesMB.BuscarCliente();
        this.clienteOrigem = this.clientesMB.getLista().get(0);
    }

    public void selecionarDest(SelectEvent event) throws Exception {
        Clientes cli = ((Clientes) event.getObject());
        this.clientesMB.setCodigo(cli.getCodigo());
        this.clientesMB.BuscarCliente();
        this.clienteDestino = this.clientesMB.getLista().get(0);

        this.listaPostoServico = postoServicoMB.listarPostos(cli.getCodigo());
    }

    public void selecionarDestRefeicao(SelectEvent event) throws Exception {
        Clientes cli = ((Clientes) event.getObject());
        this.clientesMB.setCodigo(cli.getCodigo());
        this.clientesMB.BuscarCliente();
        this.clienteDestino = this.clientesMB.getLista().get(0);

        this.listaPedidoRefeicao = this.pedidosController.listagemPedidosRefeicao(cli.getCodigo(), this.codFil, "0", this.persistencia);
    }

    private boolean validarInput() {
        return !(null == this.pedido.getData()
                || this.pedido.getData().equals("")
                || this.filial == null
                || null == this.osVigDestino
                || null == this.osVigOrigem
                || null == this.pedido.getDt_Incl()
                || this.pedido.getDt_Incl().equals("")
                || null == this.pedido.getHora1O()
                || this.pedido.getHora1O().equals("")
                || null == this.pedido.getHora1D()
                || this.pedido.getHora1D().equals(""));
    }

    public void cadastrarPedidoMaxima() {
        try {
            if (null == this.pedidoRefeicaoSelecionado.getData()
                    || this.pedidoRefeicaoSelecionado.getData().isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("Data"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else if (null == this.pedidoRefeicaoSelecionado.getSolicitante()
                    || this.pedidoRefeicaoSelecionado.getSolicitante().isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("Solicitante"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else if (null == this.getClienteDestino().getCodigo()
                    || this.getClienteDestino().getCodigo().isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("Cliente"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else if (this.listaPedidoRefeicao.size() == 0) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneClienteSecaoCadastrado"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                PedidoRefeicaoItens pedidoItens;
                int Count = 1;

                if (null != this.pedidoRefeicaoSelecionadoGride
                        && this.pedidoRefeicaoSelecionadoGride.getSequencia() != BigDecimal.ZERO) {
                    this.pedidoRefeicaoSelecionado.setSequencia(this.pedidoRefeicaoSelecionadoGride.getSequencia());
                }

                this.pedidoRefeicaoSelecionado.setCodcli(this.getClienteDestino().getCodigo());
                this.pedidoRefeicaoSelecionado.setCodFil(this.codFil);
                this.pedidoRefeicaoSelecionado.setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.pedidoRefeicaoSelecionado.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.pedidoRefeicaoSelecionado.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));

                this.pedidoRefeicaoSelecionado.setPedidoRefeicaoItens(new ArrayList<>());

                for (Pedido listaPedidoRefeicao1 : this.listaPedidoRefeicao) {
                    pedidoItens = new PedidoRefeicaoItens();
                    pedidoItens.setDt_Alter(this.pedidoRefeicaoSelecionado.getDt_Alter());
                    pedidoItens.setHr_Alter(this.pedidoRefeicaoSelecionado.getHr_Alter());
                    pedidoItens.setOperador(this.pedidoRefeicaoSelecionado.getOperador());
                    pedidoItens.setOrdem(Integer.toString(Count));
                    pedidoItens.setQtdeAlmoco(listaPedidoRefeicao1.getQtdeAlmoco());
                    pedidoItens.setQtdeCafe(listaPedidoRefeicao1.getQtdeCafe());
                    pedidoItens.setQtdeCeia(listaPedidoRefeicao1.getQtdeCeia());
                    pedidoItens.setQtdeJantar(listaPedidoRefeicao1.getQtdeJanta());
                    pedidoItens.setSecao(listaPedidoRefeicao1.getSecao());
                    this.pedidoRefeicaoSelecionado.getPedidoRefeicaoItens().add(pedidoItens);

                    Count++;
                }

                this.pedidoRefeicaoDao.inserirPedido(this.pedidoRefeicaoSelecionado, this.persistencia);

                PrimeFaces.current().executeScript("PF('dlgPedido').hide();");
                allPedidos();
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);

            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void cadastrarPedido() {
        try {
            SimpleDateFormat formatoHora1 = new SimpleDateFormat("HH:mm");
            Calendar calendarioOrigem = Calendar.getInstance();
            Calendar calendarioDestino = Calendar.getInstance();

            calendarioOrigem.setTime(formatoHora1.parse(this.pedido.getHora1O()));
            calendarioOrigem.add(Calendar.MINUTE, +60);

            calendarioDestino.setTime(formatoHora1.parse(this.pedido.getHora1D()));
            calendarioDestino.add(Calendar.MINUTE, +60);

            pedido.setSolicitante(pedido.getSolicitante().toUpperCase());
            pedido.setSituacao("PD");
            pedido.setTipo("T");
            pedido.setNRed1(osVigOrigem.getNRed());
            pedido.setNRed2(osVigDestino.getNRed());
            pedido.setCodFil(this.filial.getCodfilAc());
            pedido.setCodCli1(osVigOrigem.getCliente());
            pedido.setCodCli2(osVigDestino.getCliente());
            pedido.setFlag_Excl("");
            pedido.setHr_Incl(getDataAtual("HORA"));

            pedido.setOperIncl(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            pedido.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));

            pedido.setPedidoCliente(pedido.getPedidoCliente().toUpperCase());
            pedido.setObs(pedido.getObs().toUpperCase());

            if (pedido.getDt_Incl().equals(pedido.getData())) {
                pedido.setClassifSrv("E");
            } else {
                pedido.setClassifSrv("V");
            }

            pedidosController.inserirPedido(pedido, persistencia);

            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);

            PrimeFaces.current().executeScript("PF('dlgPedido').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);

            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void editarPedido() {
        try {
            if (!validarInput()) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Obrigatorio"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                SimpleDateFormat formatoHora1 = new SimpleDateFormat("HH:mm");
                Calendar calendarioOrigem = Calendar.getInstance();
                Calendar calendarioDestino = Calendar.getInstance();

                calendarioOrigem.setTime(formatoHora1.parse(this.pedido.getHora1O()));
                calendarioOrigem.add(Calendar.MINUTE, +60);

                calendarioDestino.setTime(formatoHora1.parse(this.pedido.getHora1D()));
                calendarioDestino.add(Calendar.MINUTE, +60);

                pedido.setSolicitante(pedido.getSolicitante().toUpperCase());
                pedido.setPedidoCliente(pedido.getPedidoCliente().toUpperCase());
                pedido.setObs(pedido.getObs().toUpperCase());

                pedido.setSituacao("PD");
                pedido.setTipo("T");
                pedido.setNRed1(osVigOrigem.getNRed());
                pedido.setNRed2(osVigDestino.getNRed());
                pedido.setCodFil(filial.getCodfilAc());
                pedido.setCodCli1(osVigOrigem.getCliente());
                pedido.setCodCli2(osVigDestino.getCliente());
                pedido.setFlag_Excl("");
                pedido.setHr_Incl(getDataAtual("HORA"));

                pedido.setOperIncl(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                pedido.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));

                if (pedido.getDt_Incl().equals(pedido.getData())) {
                    pedido.setClassifSrv("E");
                } else {
                    pedido.setClassifSrv("V");
                }

                pedidosController.editarPedido(pedido, persistencia);
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);

                PrimeFaces.current().executeScript("PF('dlgEdicaoPedido').hide();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void excluirPedido() {
        try {
            pedido.setTipo("T");
            pedido.setCodFil(codFil);
            pedido.setFlag_Excl("*");
            pedido.setDt_Excl(getDataAtual("SQL"));
            pedido.setHr_Excl(getDataAtual("HORA"));
            pedido.setOperExcl(RecortaAteEspaço(operador, 0, 10));

            FacesMessage message;
            if (pedido.getSituacao().equals("PD")) {
                rotassatweb.editarPedido(pedido, persistencia);

                OS_VigDao osvigdao = new OS_VigDao();
                osvigdao.acrescentarGtvQtde(pedido.getOS().toString(), pedido.getCodFil().toString(), persistencia);

                message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("ExclusaoSucesso"),
                        getMessageS("Pedido") + ": " + pedido.getNumero().toBigInteger());
                FacesContext.getCurrentInstance().addMessage(null, message);
                PrimeFaces.current().executeScript("PF('dlgEdicaoPedido').hide();");
            } else {
                message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("ExclusaoPedidoNaoPermitida"),
                        getMessageS("Pedido") + ": " + pedido.getNumero().toBigInteger());
                FacesContext.getCurrentInstance().addMessage(null, message);
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public List<OS_Vig> buscarPedidos(String query) {
        try {
            listaOsVig = pedidosController.buscarPedidos(query, true, codPessoa.toString(), persistencia);

            if (listaOsVig.isEmpty()) {
                osVigOrigem = new OS_Vig();
                listaOsVig = new ArrayList<>();
                listaOsVig.add(osVigOrigem);
            } else {
                osVigOrigem = listaOsVig.get(0);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return listaOsVig;
    }

    public List<OS_Vig> buscarPedidosDestino(String query) {
        try {
            listaOsVigDestino = pedidosController.buscarPedidos(query, true, codPessoa.toString(), persistencia);

            if (listaOsVigDestino.isEmpty()) {
                osVigDestino = new OS_Vig();
                listaOsVigDestino = new ArrayList<>();
                listaOsVigDestino.add(osVigDestino);
            } else {
                osVigDestino = listaOsVigDestino.get(0);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return listaOsVigDestino;
    }

    public Pedido getPedido() {
        return pedido;
    }

    public void setPedido(Pedido pedido) {
        this.pedido = pedido;
    }

    public LocalDate getData1() {
        return data1;
    }

    public void setData1(LocalDate data1) {
        this.data1 = data1;
    }

    public LocalDate getData2() {
        return data2;
    }

    public void setData2(LocalDate data2) {
        this.data2 = data2;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public Pedido getPedidoSelecionado() {
        return pedidoSelecionado;
    }

    public void setPedidoSelecionado(Pedido pedidoSelecionado) {
        this.pedidoSelecionado = pedidoSelecionado;
    }

    public String getD1() {
        return d1;
    }

    public void setD1(String d1) {
        this.d1 = d1;
    }

    public String getD2() {
        return d2;
    }

    public void setD2(String d2) {
        this.d2 = d2;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataSelecionada2() {
        return dataSelecionada2;
    }

    public void setDataSelecionada2(Date dataSelecionada2) {
        this.dataSelecionada2 = dataSelecionada2;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public boolean isLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    /*public LazyDataModel<Pedido> getPedidos() {
        if (pedidos == null) {
            allPedidos();
        }

        return pedidos;
    }*/
    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public String getCaminho() {
        return caminho;
    }

    public void setCaminho(String caminho) {
        this.caminho = caminho;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getCoordenadas() {
        return coordenadas;
    }

    public void setCoordenadas(String coordenadas) {
        this.coordenadas = coordenadas;
    }

    public String getDataDestino() {
        return dataDestino;
    }

    public void setDataDestino(String dataDestino) {
        this.dataDestino = dataDestino;
    }

    public String getHora1Origem() {
        return hora1Origem;
    }

    public void setHora1Origem(String hora1Origem) {
        this.hora1Origem = hora1Origem;
    }

    public String getHora1Destino() {
        return hora1Destino;
    }

    public void setHora1Destino(String hora1Destino) {
        this.hora1Destino = hora1Destino;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public BigDecimal getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(BigDecimal codPessoa) {
        this.codPessoa = codPessoa;
    }

    public ArquivoLog getLogerro() {
        return logerro;
    }

    public void setLogerro(ArquivoLog logerro) {
        this.logerro = logerro;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }

    public LoginSatMobWeb getLoginsatmobweb() {
        return loginsatmobweb;
    }

    public void setLoginsatmobweb(LoginSatMobWeb loginsatmobweb) {
        this.loginsatmobweb = loginsatmobweb;
    }

    public PedidosRefeicaoSatMobWeb getPedidosController() {
        return pedidosController;
    }

    public void setPedidosController(PedidosRefeicaoSatMobWeb pedidosController) {
        this.pedidosController = pedidosController;
    }

    public OS_Vig getOsVigOrigem() {
        return osVigOrigem;
    }

    public void setOsVigOrigem(OS_Vig osVigOrigem) {
        this.osVigOrigem = osVigOrigem;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public List<OS_Vig> getListaOsVig() {
        return listaOsVig;
    }

    public void setListaOsVig(List<OS_Vig> listaOsVig) {
        this.listaOsVig = listaOsVig;
    }

    public String getDataOrigem() {
        return dataOrigem;
    }

    public void setDataOrigem(String dataOrigem) {
        this.dataOrigem = dataOrigem;
    }

    public List<OS_Vig> getListaOsVigDestino() {
        return listaOsVigDestino;
    }

    public void setListaOsVigDestino(List<OS_Vig> listaOsVigDestino) {
        this.listaOsVigDestino = listaOsVigDestino;
    }

    public OS_Vig getOsVigDestino() {
        return osVigDestino;
    }

    public void setOsVigDestino(OS_Vig osVigDestino) {
        this.osVigDestino = osVigDestino;
    }

    public Clientes getClienteOrigem() {
        return clienteOrigem;
    }

    public void setClienteOrigem(Clientes clienteOrigem) {
        this.clienteOrigem = clienteOrigem;
    }

    public Clientes getClienteDestino() {
        return clienteDestino;
    }

    public void setClienteDestino(Clientes clienteDestino) {
        this.clienteDestino = clienteDestino;
    }

    public List<PstServ> getListaPostoServico() {
        return listaPostoServico;
    }

    public void setListaPostoServico(List<PstServ> listaPostoServico) {
        this.listaPostoServico = listaPostoServico;
    }

    public List<Pedido> getListaPedidoRefeicao() {
        return listaPedidoRefeicao;
    }

    public void setListaPedidoRefeicao(List<Pedido> listaPedidoRefeicao) {
        this.listaPedidoRefeicao = listaPedidoRefeicao;
    }

    public PedidoRefeicaoItens getPedidoRefeicaoItem() {
        return pedidoRefeicaoItem;
    }

    public void setPedidoRefeicaoItem(PedidoRefeicaoItens pedidoRefeicaoItem) {
        this.pedidoRefeicaoItem = pedidoRefeicaoItem;
    }

    public List<PedidoRefeicaoItens> getListaPedidoRefeicaoItens() {
        return listaPedidoRefeicaoItens;
    }

    public void setListaPedidoRefeicaoItens(List<PedidoRefeicaoItens> listaPedidoRefeicaoItens) {
        this.listaPedidoRefeicaoItens = listaPedidoRefeicaoItens;
    }

    public PedidoRefeicao getPedidoRefeicao() {
        return pedidoRefeicao;
    }

    public void setPedidoRefeicao(PedidoRefeicao pedidoRefeicao) {
        this.pedidoRefeicao = pedidoRefeicao;
    }

    public PedidoRefeicao getPedidoRefeicaoSelecionado() {
        return pedidoRefeicaoSelecionado;
    }

    public void setPedidoRefeicaoSelecionado(PedidoRefeicao pedidoRefeicaoSelecionado) {
        this.pedidoRefeicaoSelecionado = pedidoRefeicaoSelecionado;
    }

    public List<PedidoRefeicao> getPedidos() {
        return pedidos;
    }

    public void setPedidos(List<PedidoRefeicao> pedidos) {
        this.pedidos = pedidos;
    }

    public PedidoRefeicao getPedidoRefeicaoSelecionadoGride() {
        return pedidoRefeicaoSelecionadoGride;
    }

    public void setPedidoRefeicaoSelecionadoGride(PedidoRefeicao pedidoRefeicaoSelecionadoGride) {
        this.pedidoRefeicaoSelecionadoGride = pedidoRefeicaoSelecionadoGride;
    }
}

/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class PropServ {

    private Float Numero;
    private BigDecimal CodFil;
    private BigDecimal Ordem;
    private BigDecimal CodFilAtd;
    private String Descricao;
    private BigDecimal CodContato1;
    private BigDecimal CodContato2;
    private String ViaCxF;
    private Integer DiasCst;
    private String EntregaSab;
    private String EntregaDom;
    private String EntregaFer;
    private String FormaFat;
    private BigDecimal CodFreq;
    private BigDecimal Valor;
    private String CodOperacoes;
    private String CodComercial;
    private String CodAnalise;
    private BigDecimal KM;
    private String Detalhes;
    private String Justificativa;
    private String JustCml;
    private String OperIncl;
    private LocalDate Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String OperAnalise;
    private LocalDate DtAnalise;
    private String HrAnalise;
    private String OperAnCml;
    private LocalDate DtAnCml;
    private String HrAnCml;
    private Integer Aprovado;
    private LocalDate DtAprovacao;
    private String OperAprov;
    private BigDecimal OS;
    private LocalDate DtInicio;
    private LocalDate DtFim;

    public Float getNumero() {
        return Numero;
    }

    public void setNumero(Float Numero) {
        this.Numero = Numero;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getOrdem() {
        return Ordem;
    }

    public void setOrdem(BigDecimal Ordem) {
        this.Ordem = Ordem;
    }

    public BigDecimal getCodFilAtd() {
        return CodFilAtd;
    }

    public void setCodFilAtd(BigDecimal CodFilAtd) {
        this.CodFilAtd = CodFilAtd;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public BigDecimal getCodContato1() {
        return CodContato1;
    }

    public void setCodContato1(BigDecimal CodContato1) {
        this.CodContato1 = CodContato1;
    }

    public BigDecimal getCodContato2() {
        return CodContato2;
    }

    public void setCodContato2(BigDecimal CodContato2) {
        this.CodContato2 = CodContato2;
    }

    public String getViaCxF() {
        return ViaCxF;
    }

    public void setViaCxF(String ViaCxF) {
        this.ViaCxF = ViaCxF;
    }

    public Integer getDiasCst() {
        return DiasCst;
    }

    public void setDiasCst(Integer DiasCst) {
        this.DiasCst = DiasCst;
    }

    public String getEntregaSab() {
        return EntregaSab;
    }

    public void setEntregaSab(String EntregaSab) {
        this.EntregaSab = EntregaSab;
    }

    public String getEntregaDom() {
        return EntregaDom;
    }

    public void setEntregaDom(String EntregaDom) {
        this.EntregaDom = EntregaDom;
    }

    public String getEntregaFer() {
        return EntregaFer;
    }

    public void setEntregaFer(String EntregaFer) {
        this.EntregaFer = EntregaFer;
    }

    public String getFormaFat() {
        return FormaFat;
    }

    public void setFormaFat(String FormaFat) {
        this.FormaFat = FormaFat;
    }

    public BigDecimal getCodFreq() {
        return CodFreq;
    }

    public void setCodFreq(BigDecimal CodFreq) {
        try {
            this.CodFreq = CodFreq;
        } catch (Exception e) {
            this.CodFreq = BigDecimal.ZERO;
        }
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(BigDecimal Valor) {
        this.Valor = Valor;
    }

    public String getCodOperacoes() {
        return CodOperacoes;
    }

    public void setCodOperacoes(String CodOperacoes) {
        this.CodOperacoes = CodOperacoes;
    }

    public String getCodComercial() {
        return CodComercial;
    }

    public void setCodComercial(String CodComercial) {
        this.CodComercial = CodComercial;
    }

    public String getCodAnalise() {
        return CodAnalise;
    }

    public void setCodAnalise(String CodAnalise) {
        this.CodAnalise = CodAnalise;
    }

    public BigDecimal getKM() {
        return KM;
    }

    public void setKM(BigDecimal KM) {
        this.KM = KM;
    }

    public String getDetalhes() {
        return Detalhes;
    }

    public void setDetalhes(String Detalhes) {
        this.Detalhes = Detalhes;
    }

    public String getJustificativa() {
        return Justificativa;
    }

    public void setJustificativa(String Justificativa) {
        this.Justificativa = Justificativa;
    }

    public String getJustCml() {
        return JustCml;
    }

    public void setJustCml(String JustCml) {
        this.JustCml = JustCml;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public LocalDate getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(LocalDate Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOperAnalise() {
        return OperAnalise;
    }

    public void setOperAnalise(String OperAnalise) {
        this.OperAnalise = OperAnalise;
    }

    public LocalDate getDtAnalise() {
        return DtAnalise;
    }

    public void setDtAnalise(LocalDate DtAnalise) {
        this.DtAnalise = DtAnalise;
    }

    public String getHrAnalise() {
        return HrAnalise;
    }

    public void setHrAnalise(String HrAnalise) {
        this.HrAnalise = HrAnalise;
    }

    public String getOperAnCml() {
        return OperAnCml;
    }

    public void setOperAnCml(String OperAnCml) {
        this.OperAnCml = OperAnCml;
    }

    public LocalDate getDtAnCml() {
        return DtAnCml;
    }

    public void setDtAnCml(LocalDate DtAnCml) {
        this.DtAnCml = DtAnCml;
    }

    public String getHrAnCml() {
        return HrAnCml;
    }

    public void setHrAnCml(String HrAnCml) {
        this.HrAnCml = HrAnCml;
    }

    public Integer getAprovado() {
        return Aprovado;
    }

    public void setAprovado(Integer Aprovado) {
        this.Aprovado = Aprovado;
    }

    public LocalDate getDtAprovacao() {
        return DtAprovacao;
    }

    public void setDtAprovacao(LocalDate DtAprovacao) {
        this.DtAprovacao = DtAprovacao;
    }

    public String getOperAprov() {
        return OperAprov;
    }

    public void setOperAprov(String OperAprov) {
        this.OperAprov = OperAprov;
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(BigDecimal OS) {
        this.OS = OS;
    }

    public LocalDate getDtInicio() {
        return DtInicio;
    }

    public void setDtInicio(LocalDate DtInicio) {
        this.DtInicio = DtInicio;
    }

    public LocalDate getDtFim() {
        return DtFim;
    }

    public void setDtFim(LocalDate DtFim) {
        this.DtFim = DtFim;
    }
}

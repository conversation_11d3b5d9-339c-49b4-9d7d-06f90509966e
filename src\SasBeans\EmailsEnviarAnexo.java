package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class EmailsEnviarAnexo {

    private BigDecimal Sequencia;
    private int Ordem;
    private String EndAnexo;
    private String DescAnexo;
    private String NomeAnexo;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public int getOrdem() {
        return Ordem;
    }

    public void setOrdem(int Ordem) {
        this.Ordem = Ordem;
    }

    public String getEndAnexo() {
        return EndAnexo;
    }

    public void setEndAnexo(String EndAnexo) {
        this.EndAnexo = EndAnexo;
    }

    public String getDescAnexo() {
        return DescAnexo;
    }

    public void setDescAnexo(String DescAnexo) {
        this.DescAnexo = DescAnexo;
    }

    public String getNomeAnexo() {
        return NomeAnexo;
    }

    public void setNomeAnexo(String NomeAnexo) {
        this.NomeAnexo = NomeAnexo;
    }
}

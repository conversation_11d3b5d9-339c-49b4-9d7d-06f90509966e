<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:cadastros="http://xmlns.jcp.org/jsf/composite/cadastros"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <style>
                .ui-radiobutton{
                    background-color: #EEE !important;
                }
                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                .lblItem{
                    background-color: #FFFFFF !important;
                    border: thin solid #CCC !important;
                    color: #000;
                    font-size: 10pt !important;
                    font-weight: 500 !important;
                    width: 100% !important;
                    padding: 3px 6px 3px 6px !important;
                    text-shadow:1px 1px #FFF !important;
                }

                input, textarea{
                    display:block;
                    width:100% !important;
                    padding:4px !important;
                    top:-5px !important;
                }

            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewParam name="numero" value="#{valores.numeroPedido}" />
                <f:viewParam name="codfil" value="#{valores.codfil}" />
                <f:viewAction action="#{valores.consultarPedido()}"/>
            </f:metadata>

            <h:form id="main" style="padding:0px !important">
                <div id="divEscolha" style="height: calc(100vh - 0px) !important; background-color: #DDD; width:100% !important; padding:30px 10px 10px 10px !important; overflow:hidden; display:none">
                    <div class="col-md-12" style="background-color:#FFF;position:absolute;top:0;right:0;bottom:0;left:0;margin:auto; width:40% !important; height:40% !important; border-radius:10px; box-shadow:2px 2px 3px #BBB">
                        <i class="fa fa-question-circle-o" style="color:#CCC !important; font-size:70pt; width:100%; text-align:center; margin-top:40px"></i>
                        <label style="font-size:16pt; color:#000;margin-bottom:40px; font-weight:500; display:block; margin-top:40px; width:100%; text-align:center">#{localemsgs.QualReferenciaRotas}</label>
                        <div class="col-md-6">
                            <h:commandButton class="btn btn-primary btn-large" value="#{localemsgs.Origem}" action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%;height:60px;font-size:16pt;padding-top:0px !important;"></h:commandButton>
                        </div>
                        <div class="col-md-6">
                            <h:commandButton class="btn btn-success btn-large" value="#{localemsgs.Destino}" action="#{valores.consultarPedidoBaseDestino()}" style="width:100%;height:60px;font-size:16pt;padding-top:0px !important;"></h:commandButton>
                        </div>
                    </div>
                </div>
                <div id="divPrincipal" style="height: calc(100vh - 0px) !important; background-color: #FFF; width:100% !important; padding:10px !important; overflow:hidden !important;">
                    <div class="col-md-12" style="height:calc(100vh - 60px) !important; padding:0px !important;overflow:auto !important;">
                        <div class="col-md-12" style="padding:10px 10px 4px 10px; border:thin solid #CCC; background-color:#EEE; box-shadow:2px 2px 4px #DDD;">
                            <label style="background-color: orangered; color:#FFF; width:100%; padding:1px 8px 1px 8px;">#{localemsgs.Origem}</label>
                            <div class="col-md-12" style="padding:0px !important">
                                <div class="col-md-3" style="padding:2px 6px 2px 0px">
                                    <label class="lblItem" style="font-size: 12pt !important; font-weight:600 !important;">#{valores.dadosPedido.codCli1.subSequence(0, 3)}.#{valores.dadosPedido.codCli1.substring(3).subSequence(0, 1)}.#{valores.dadosPedido.codCli1.substring(4)}</label>
                                </div>
                                <div class="col-md-5" style="padding:2px 6px 2px 0px">
                                    <label class="lblItem" style="font-size: 12pt !important; font-weight:600 !important;">#{valores.dadosPedido.cli1Nred}</label>
                                </div>
                                <div class="col-md-4" style="padding:2px 0px 2px 0px">
                                    <div class="col-md-6" style="padding:0px 6px 0px 0px; text-align:right !important;">
                                        <label style="font-size:10pt !important; color:#000; font-weight:600 !important; margin-top: 5px !important">#{localemsgs.ColetarEntre}:</label>
                                    </div>    
                                    <div class="col-md-3" style="padding:0px 6px 0px 0px; text-align:right !important;">
                                        <label style="background-color:lightgoldenrodyellow; border:thin solid #CCC; min-width:100% !important;text-align:center !important;padding:4px !important; font-weight:bold !important;"><h:outputText value="#{valores.dadosPedido.hora1O}" converter="conversorHora" style="color:#000 !important;" /></label>
                                    </div>
                                    <div class="col-md-3" style="padding:0px 0px 0px 0px; text-align:right !important;">
                                        <label style="background-color:lightgoldenrodyellow; border:thin solid #CCC; min-width:100% !important;text-align:center !important;padding:4px !important; font-weight:bold !important;"><h:outputText value="#{valores.dadosPedido.hora2O}" converter="conversorHora" style="color:#000 !important;" /></label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8" style="padding:2px 6px 2px 0px">
                                <label class="lblItem" style="background-color:transparent !important">#{valores.dadosPedido.cli1Ende}</label>
                            </div>
                            <div class="col-md-1" style="padding:2px 0px 2px 0px">
                                <label class="lblItem" style="text-align:center !important;background-color:transparent !important">#{valores.dadosPedido.regiao1}</label>
                            </div>
                            <div class="col-md-12" style="padding:0px !important">
                                <div class="col-md-3" style="padding:2px 6px 2px 0px">
                                    <label class="lblItem" style="background-color:transparent !important">#{valores.dadosPedido.cli1Bairro}</label>
                                </div>
                                <div class="col-md-5" style="padding:2px 6px 2px 0px">
                                    <label class="lblItem" style="background-color:transparent !important">#{valores.dadosPedido.cli1Cidade}</label>
                                </div>
                                <div class="col-md-4" style="padding:2px 0px 2px 0px">
                                    <label class="lblItem" style="background-color:transparent !important">#{valores.dadosPedido.regiaoDesc1}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12" style="padding:10px 10px 4px 10px; border:thin solid #CCC; background-color:#EEE; margin-top:14px; box-shadow:2px 2px 4px #DDD;">
                            <label style="background-color:forestgreen; border:thin solid darkgreen; color:#FFF; width:100%; padding:1px 8px 1px 8px;">#{localemsgs.Destino}</label>
                            <div class="col-md-12" style="padding:0px !important">
                                <div class="col-md-3" style="padding:1px 6px 1px 0px">
                                    <label class="lblItem" style="font-size: 12pt !important; font-weight:600 !important;">#{valores.dadosPedido.codCli2.subSequence(0, 3)}.#{valores.dadosPedido.codCli2.substring(3).subSequence(0, 1)}.#{valores.dadosPedido.codCli2.substring(4)}</label>
                                </div>
                                <div class="col-md-5" style="padding:1px 6px 1px 0px">
                                    <label class="lblItem" style="font-size: 12pt !important; font-weight:600 !important;">#{valores.dadosPedido.cli2Nred}</label>
                                </div>
                                <div class="col-md-4" style="padding:2px 0px 2px 0px">
                                    <div class="col-md-6" style="padding:0px 6px 0px 0px; text-align:right !important;">
                                        <label style="font-size:10pt !important; color:#000; font-weight:600 !important; margin-top: 5px !important">#{localemsgs.EntregarEntre}:</label>
                                    </div>    
                                    <div class="col-md-3" style="padding:0px 6px 0px 0px; text-align:right !important;">
                                        <label style="background-color:lightgoldenrodyellow; border:thin solid #CCC; min-width:100% !important;text-align:center !important;padding:4px !important; font-weight:bold !important;"><h:outputText value="#{valores.dadosPedido.hora1D}" converter="conversorHora" style="color:#000 !important;" /></label>
                                    </div>
                                    <div class="col-md-3" style="padding:0px 0px 0px 0px; text-align:right !important;">
                                        <label style="background-color:lightgoldenrodyellow; border:thin solid #CCC; min-width:100% !important;text-align:center !important;padding:4px !important; font-weight:bold !important;"><h:outputText value="#{valores.dadosPedido.hora2D}" converter="conversorHora" style="color:#000 !important;" /></label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8" style="padding:1px 6px 1px 0px">
                                <label class="lblItem" style="background-color:transparent !important">#{valores.dadosPedido.cli2Ende}</label>
                            </div>
                            <div class="col-md-1" style="padding:1px 0px 1px 0px">
                                <label class="lblItem" style="text-align:center !important;background-color:transparent !important">#{valores.dadosPedido.regiao2}</label>
                            </div>
                            <div class="col-md-12" style="padding:0px !important">
                                <div class="col-md-3" style="padding:2px 6px 2px 0px">
                                    <label class="lblItem" style="background-color:transparent !important">#{valores.dadosPedido.cli2Bairro}</label>
                                </div>
                                <div class="col-md-5" style="padding:1px 6px 1px 0px">
                                    <label class="lblItem" style="background-color:transparent !important">#{valores.dadosPedido.cli2Cidade}</label>
                                </div>
                                <div class="col-md-4" style="padding:1px 0px 1px 0px">
                                    <label class="lblItem" style="background-color:transparent !important">#{valores.dadosPedido.regiaoDesc2}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4" style="padding:0px 10px 4px 0px; margin-top:8px;">
                            <label style="font-size:10pt !important; color:#666;">#{localemsgs.Solicitante}</label>
                            <input type="text" readonly="readonly" style="background-color:whitesmoke; border:thin solid #CCC;font-weight:bold !important;color:#000 !important" value="#{valores.dadosPedido.solicitante}" />
                        </div>
                        <div class="col-md-2" style="padding:0px 10px 4px 0px; margin-top:8px;">
                            <label style="font-size:10pt !important; color:#666; text-align:center !important; width:100%">#{localemsgs.Valor}</label>
                            <label style="background-color:whitesmoke; border:thin solid #CCC; min-width:100% !important;text-align:center !important;padding:4px !important; font-weight:bold !important;"><h:outputText value="#{valores.dadosPedido.valor}" converter="conversormoeda" style="color:#000 !important;" /></label>
                        </div>
                        <div class="col-md-6" style="padding:0px 0px 4px 0px; margin-top:8px;">
                            <label style="font-size:10pt !important; color:#666;">#{localemsgs.OBS}</label>
                            <input type="text" readonly="readonly" style="background-color:whitesmoke; border:thin solid #CCC;color:#000 !important" value="#{valores.dadosPedido.obs}" />
                        </div>
                        <div class="col-md-12" style="padding:0px !important;">
                            <div class="col-md-4" style="padding:0px 10px 4px 0px; margin-top:0px;">
                                <label style="font-size:10pt !important; color:#666;">#{localemsgs.classificacao}</label>
                                <p:selectOneMenu id="slcClassificacao"  value="#{valores.dadosPedido.classifSrv}" style="color:#000 !important;">
                                    <f:selectItem itemLabel="#{localemsgs.Rotineiro}" itemValue="R" />
                                    <f:selectItem itemLabel="#{localemsgs.Eventual}" itemValue="V" />
                                    <f:selectItem itemLabel="#{localemsgs.Especial}" itemValue="E" />
                                    <f:selectItem itemLabel="#{localemsgs.AssistTecnica}" itemValue="A" />
                                    <f:selectItem itemLabel="#{localemsgs.Intermediaria}" itemValue="I" />
                                    <f:selectItem itemLabel="#{localemsgs.Preliminar}" itemValue="P" />
                                    <f:selectItem itemLabel="#{localemsgs.ProjetoEspecial}" itemValue="J" />
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-2" style="padding:0px 10px 4px 0px; margin-top:0px;">
                                <label style="color:#666;">#{localemsgs.Hora}</label>
                                <p:selectOneMenu id="hora" editable="true" 
                                                 converter="conversorHora" validator="ValidadorHora"
                                                 required="true"
                                                 filter="true" filterMatchMode="contains" value="#{valores.dadosPedido.horaEnt}">
                                    <f:selectItems value="#{horas.obterHorario()}" />
                                </p:selectOneMenu>

                            </div>
                        </div>    
                        <div class="col-md-12" style="padding:0px 0px 10px 0px !important;min-height:300px !important;height:calc(100% - 460px);max-height:calc(100% - 460px);">
                            <div class="col-md-6" style="min-height:calc(100% - 0px);height:calc(100% - 0px);max-height:calc(100% - 0px); margin-top:8px !important; padding:0px 8px 0px 0px !important;">
                                <label style="font-size:16pt !important; font-weight:bold !important; color:steelblue !important"><i class="fa fa-map"></i>&nbsp;#{localemsgs.Rotas}</label>
                                <div style="width:300px; position: absolute; height:30px; top:-2px; right:10px; background-color:#EEE; border-radius:60px; border:thin solid #DDD;">
                                    <h:panelGrid columns="1" style="margin:2px 0px 0px 0px !important;">
                                        <center>
                                            <p:selectOneRadio id="console" value="#{valores.tipoCarregamentoSugestao}" style="background-color:#EEE !important; color:#000 !important; width:210px !important; margin-left: 60px">
                                                <f:selectItem itemLabel="#{localemsgs.Origem}" itemValue="O"  />
                                                <f:selectItem itemLabel="#{localemsgs.Destino}" itemValue="D" />

                                                <p:ajax listener="#{valores.consultarPedidoChange}" partialSubmit="true" process="@this" update="main:tabelaCxForte main:pnlMapaRoteirizacao" />
                                            </p:selectOneRadio>
                                        </center>
                                    </h:panelGrid>
                                </div>
                                <div style="padding:0px !important; overflow:auto !important; max-height:calc(100% - 35px) !important;">
                                    <p:dataTable
                                        id="tabelaCxForte"
                                        value="#{valores.listaRoteirizacao}"
                                        rowKey="#{lista.rota}_#{lista.parada}"
                                        paginator="false"
                                        paginatorTemplate="false"
                                        lazy="true"
                                        reflow="true"
                                        var="lista"
                                        selection="#{valores.trajetoSelecionado}"
                                        styleClass="tabela"
                                        selectionMode="single"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        scrollable="true"
                                        class="tabela DataGrid"
                                        scrollWidth="100%"
                                        style="display: flex; flex-direction: column; font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important; background-color:#FFF !important;margin-top:-10px !important"
                                        >
                                        <p:ajax listener="#{valores.onRowSelectRoteirizacao}" event="rowSelect" update="main:pnlMapaRoteirizacao" />

                                        <p:column headerText="#{localemsgs.ROTA}" class="text-center">
                                            <h:outputText value="#{lista.rota}" class="text-center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora1}" class="text-center">
                                            <h:outputText value="#{lista.hora1}" converter="conversorHora" class="text-center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Parada}"  class="text-center">
                                            <h:outputText value="#{lista.parada}"  class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ER}" class="text-center">
                                            <h:outputText value="#{lista.ER}"  class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Distancia}" class="text-center">
                                            <h:outputText value="#{lista.distancia}"  class="text-center" converter="conversorCodFil"/>
                                        </p:column>
                                    </p:dataTable>
                                </div>
                            </div>
                            <div class="col-md-6" style="min-height:calc(100% - 0px);height:calc(100% - 0px);max-height:calc(100% - 0px); margin-top:8px !important; padding:0px 0px 0px 0px !important; border:thin solid #CCC">
                                <p:panel id="pnlMapaRoteirizacao" style="width:100% !important; height:100% !important;"> 
                                    <p:panel id="pnlMapa" style="width:100% !important; height:100% !important;" rendered="#{valores.trajetoSugestaoRoteirizacao ne null}">
                                        <div id="mapGoogle" style="min-width:100% !important;width:100% !important;max-width:100% !important;"></div>
                                        <div style="height:80px !important; width:100%; margin-top:0px !important; padding: 6px 6px 0px 6px !important;">
                                            <table style="width: 100%">
                                                <tr>
                                                    <td style="width:50px !important; text-align: center !important; border-bottom:thin solid #DDD !important; padding-bottom:5px !important; background-color:mistyrose"><img src="https://mobile.sasw.com.br:9091/satmobile/pins/pin_vermelho_sombra.png" style="height: 32px; margin-top:5px;" /></td>
                                                    <td style="width:calc(50% - 50px) !important; text-align: left !important;border-bottom:thin solid #DDD !important; padding-bottom:5px !important; font-weight:bold;text-shadow:1px 1px #FFF !important; color:red; background-color:mistyrose">#{localemsgs.OrigemRota}</td>
                                                    <td style="width:50px !important; text-align: center !important;border-bottom:thin solid #DDD !important; padding-bottom:5px !important; background-color:#EEE"><img src="https://mobile.sasw.com.br:9091/satmobile/pins/pin_azul_caminhao.png" style="height: 32px; margin-top:5px;" /></td>
                                                    <td style="width:calc(50% - 50px) !important; text-align: left !important;border-bottom:thin solid #DDD !important; padding-bottom:5px !important; font-weight:bold;text-shadow:1px 1px #FFF !important; color:black; background-color:#EEE">#{localemsgs.LocalizacaoRota}</td>
                                                </tr>
                                                <tr>
                                                    <td style="width:50px !important; text-align: center !important; padding-top:5px !important; background-color:honeydew"><img src="https://mobile.sasw.com.br:9091/satmobile/pins/pin_verde_sombra.png" style="height: 32px; margin-top:5px;" /></td>
                                                    <td style="width:calc(50% - 50px) !important; text-align: left !important;padding-top:5px !important; font-weight:bold; color:forestgreen; background-color:honeydew;text-shadow:1px 1px #FFF !important;">#{localemsgs.DestinoRota}</td>
                                                    <td style="width:50px !important; text-align: center !important; padding-right: 16px !important;padding-top:1px !important; vertical-align:top !important; background-color:azure"><img src="#{valores.pinMapaPedido}" style="width: 25px; margin-right:5px" /></td>
                                                    <td style="width:calc(50% - 50px) !important; text-align: left !important;padding-top:5px !important; font-weight:bold; color:steelblue; background-color:azure;text-shadow:1px 1px #FFF !important;">#{localemsgs.LocalizacaoPedido}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <script type="text/javascript">
                                            // <![CDATA[
                                            var map;
                                            var directionsDisplayNext;
                                            var directionsDisplayBack;
                                            function initMap() {
                                            let Altura = $('[id*="pnlMapaRoteirizacao"]').parent('div').height() - 96;
                                            $('#mapGoogle').css('min-height', Altura + 'px');
                                            map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                            zoom: 12,
                                                    center: #{valores.centro},
                                                    gestureHandling: 'cooperative'
                                            });
                                            var directionsServiceNext = new google.maps.DirectionsService;
                                            directionsDisplayNext = new google.maps.DirectionsRenderer({ polylineOptions: { strokeColor: "#006633", strokeOpacity: 0.9, strokeWeight: 5 },
                                                    suppressMarkers: true });
                                            var directionsServiceBack = new google.maps.DirectionsService;
                                            directionsDisplayBack = new google.maps.DirectionsRenderer({ polylineOptions: { strokeColor: "#F00", strokeOpacity: 0.8, strokeWeight: 7 },
                                                    suppressMarkers: true });
                                            directionsDisplayBack.setMap(map);
                                            directionsDisplayNext.setMap(map);
                                            var onChangeHandler = function () {
                                            calculateAndDisplayRouteNext(directionsServiceNext, directionsDisplayNext);
                                            setTimeout(function(){
                                            calculateAndDisplayRouteBack(directionsServiceBack, directionsDisplayBack);
                                            }, 300);
                                            }

                                            onChangeHandler();
                                            function calculateAndDisplayRouteBack(directionsService, directionsDisplay) {
                                            directionsService.route({
                                            #{valores.directionsTrajetoAtual}
                                            travelMode: 'DRIVING'
                                            }, function (response, status) {
                                            if (status === 'OK') {
                                            directionsDisplay.setDirections(response);
                                            } else {

                                            }
                                            console.log(response);
                                            });
                                            }

                                            function calculateAndDisplayRouteNext(directionsService, directionsDisplay) {
                                            directionsService.route({
                                            #{valores.directionsTrajetoProximo}
                                            travelMode: 'DRIVING'
                                            }, function (response, status) {
                                            if (status === 'OK') {
                                            directionsDisplay.setDirections(response);
                                            } else {

                                            }
                                            console.log(response);
                                            });
                                            }


                                            #{valores.markers}
                                            }

                                            // ]]>
                                        </script>
                                        <script src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}&amp;callback=initMap"></script>
                                    </p:panel>
                                    <p:panel id="pnlLabel" style="width:100% !important; height:100% !important;" rendered="#{valores.trajetoSugestaoRoteirizacao eq null}">
                                        <label style="position:absolute; top:0;right:0;bottom:0;left:0;margin:auto; font-size:16pt;color:#CCC; max-height:150px; width:100%;text-align:center">
                                            <i class="fa fa-map-marker" style="font-size:80pt !important"></i>
                                            <span style="display:block">#{localemsgs.SelecioneRotaMapa}</span>
                                        </label>
                                    </p:panel>
                                </p:panel>
                            </div>
                        </div>

                    </div>
                    <div class="col-md-12" style="padding:5px 0px 0px 0px !important; text-align: right;">
                        <a href="javascript:void(0);" id="btCancelarRoteirizacao" class="btn btn-danger" style="margin-right:8px;"><i class="fa fa-ban" style="margin-right: 4px"></i>#{localemsgs.Cancelar}</a>
                        <a href="javascript:void(0);" id="btSalvarRoteirizacao" class="btn btn-primary"><i class="fa fa-save" style="margin-right: 4px"></i>#{localemsgs.Salve}</a>
                    </div>
                </div>
                <script type="text/javascript">
                    // <![CDATA[
                    var Operador = '#{valores.operador}';
                    var Solicitante = '#{login.pp.empresa}';
                    var CodFil = '#{valores.codfil}';
                    var NumeroPedido = #{valores.dadosPedido.numero}

                    $.extend({
                        MsgBoxAzulOk: function (Texto, callbackFnk) {
                            $.alert({
                                icon: 'fa fa-info-circle fa-lg',
                                    theme: 'modern',
                                    type: 'blue',
                                    title: '#{localemsgs.Aviso}',
                                    content: Texto,
                                    buttons: {
                                    ok: {
                                    text: 'Ok',
                                            btnClass: 'btn-blue',
                                            action: function () {
                                            // now we are calling our own callback function
                                                if (typeof callbackFnk == 'function') {
                                                callbackFnk.call();
                                                }
                                            }
                                    }
                                }
                            });
                        },
                        MsgBoxVermelholOk: function (Texto, callbackFnk) {
                            $.alert({
                                icon: 'fa fa-exclamation-triangle fa-lg',
                                    theme: 'modern',
                                    type: 'red',
                                    title: '#{localemsgs.Aviso}',
                                    content: Texto,
                                    buttons: {
                                    ok: {
                                    text: 'Ok',
                                            btnClass: 'btn-red',
                                            action: function () {
                                            // now we are calling our own callback function
                                            if (typeof callbackFnk == 'function') {
                                            callbackFnk.call();
                                            }
                                            }
                                    }
                                }
                            });
                        },
                        MsgBoxLaranjalOk: function (Texto, callbackFnk) {
                            $.alert({
                                icon: 'fa fa-exclamation-triangle fa-lg',
                                    theme: 'modern',
                                    type: 'orange',
                                    title: '#{localemsgs.Aviso}',
                                    content: Texto,
                                    buttons: {
                                    ok: {
                                    text: 'Ok',
                                            btnClass: 'btn-orange',
                                            action: function () {
                                            // now we are calling our own callback function
                                            if (typeof callbackFnk == 'function') {
                                            callbackFnk.call();
                                            }
                                            }
                                    }
                                }
                            });
                        }
                    });
                    
                    function Roteirizar() {
                        let arrayJson = new Array();

                        arrayJson.push({
                        'Numero': NumeroPedido,
                                'Rota': $('[id$="tabelaCxForte"]').find('.ui-state-highlight').find('td:nth-child(1)').text().trim().toUpperCase().replace('RUTA', '').replace('ROTA', '').replace('ROUTE', ''),
                                'Hora1O': $('[name*="hora_editableInput"]').val(),
                                'CodFil': CodFil,
                                'Solicitante': Solicitante,
                                'Operador': Operador
                        });
                        
                        const UrlService = 'https://mobile.sasw.com.br/SatWebService/api/ws-rotas/roteirizacao/roteirizar';
                        //const UrlService = 'http://localhost:8080/SatWebService/api/ws-rotas/roteirizacao/roteirizar';
                        
                    $.ajax({
                        url: UrlService,
                                method: 'POST',
                                data: JSON.stringify(arrayJson)
                        })
                        .done(function (response) {
                            if (response.status === 'ok'){
                                $.MsgBoxAzulOk('#{localemsgs.DadosSalvosSucesso}', function(){
                                    setTimeout(function(){
                                        location.href = 'mapa_total.xhtml?codfil=' + CodFil + '&pedidos=S';
                                        $('#btCancelarRoteirizacao').click();
                                    }, 400);
                                });
                            }
                            else {
                                $.MsgBoxVermelholOk('#{localemsgs.ErroSalvarBD}')
                                //  alert('Error: ' + JSON.stringify(response));
                            }
                        })
                        .fail(function (fail) {
                            $.MsgBoxVermelholOk('#{localemsgs.ErroSalvarBD}')
                            //alert('Error: ' + JSON.stringify(fail));
                        });
                    }

                    function ValidarCamposRoteiro(){
                        if ($('[name$="hora_editableInput"]').val().trim() == ''){
                            $.MsgBoxLaranjalOk('#{localemsgs.InformeHora}', function(){
                                setTimeout(function(){
                                    $('[name$="hora_editableInput"]').focus();
                                }, 400);
                            });
                            
                            return false;
                        }
                        else if ($('[id$="tabelaCxForte"]').find('.ui-state-highlight').length == 0) {
                            $.MsgBoxLaranjalOk('#{localemsgs.SelecioneRota}');
                            return false;
                        }
                        else return true;
                    }

                    $(document).ready(function(){

                    })
                    .on('click', '#btCancelarRoteirizacao', function(){
                        $('#btFecharDetalhePedido', window.parent.document).click();
                    })
                    .on('click', '#btSalvarRoteirizacao', function(){
                        if (ValidarCamposRoteiro()) Roteirizar();
                    })
                    ;
                    // ]]>
                </script>
            </h:form>   
        </h:body>
    </f:view>
</html>
/*
 */
package br.com.sasw.utils;

import java.lang.reflect.Field;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Logger {

    public static String objeto2String(Object objeto) {
        String respostaFormatada = "----------------------------------------";
        try {
            Field[] fields = objeto.getClass().getDeclaredFields();
            respostaFormatada += "\n" + objeto.getClass().getCanonicalName() + "\n\n";
            for (Field f : fields) {
                f.setAccessible(true);
                if (null != f.get(objeto)) {
                    respostaFormatada += f.getName() + " - [" + f.get(objeto).toString() + "]\n";
                }
            }
            respostaFormatada += "\n----------------------------------------\n";
        } catch (Exception e) {
            respostaFormatada += "\n" + e.getMessage() + "\n";
        } finally {
            return respostaFormatada;
        }
    }

    public static String listaObjeto2String(List<?> objeto) {
        String respostaFormatada = "----------------------------------------";
        try {
            for (Object o : objeto) {
                Field[] fields = o.getClass().getDeclaredFields();
                respostaFormatada += "\n" + o.getClass().getCanonicalName() + "\n\n";
                for (Field f : fields) {
                    f.setAccessible(true);
                    if (null != f.get(o)) {
                        respostaFormatada += f.getName() + " - [" + f.get(o).toString() + "]\n";
                    }
                }
                respostaFormatada += "\n-------------------\n";
            }
            respostaFormatada += "\n----------------------------------------\n";
        } catch (Exception e) {
            respostaFormatada += "\n" + e.getMessage() + "\n";
        } finally {
            return respostaFormatada;
        }
    }

    public static String listaObjetosCompostos2String(List<?> objeto) {
        String respostaFormatada = "----------------------------------------";
        try {
            for (Object o : objeto) {
                Field[] fields = o.getClass().getDeclaredFields();
                respostaFormatada += "\n" + o.getClass().getCanonicalName() + "\n\n";
                for (Field f : fields) {
                    f.setAccessible(true);
                    if (null != f.get(o)) {
                        Object o2 = f.get(o);
                        Field[] fields2 = o2.getClass().getDeclaredFields();
                        respostaFormatada += "\n" + o2.getClass().getCanonicalName() + "\n\n";
                        for (Field f2 : fields2) {
                            f2.setAccessible(true);
                            if (null != f2.get(o2)) {
                                respostaFormatada += f2.getName() + " - [" + f2.get(o2).toString() + "]\n";
                            }
                        }
                        respostaFormatada += "\n";
                    }
                }
                respostaFormatada += "\n-------------------\n";
            }
            respostaFormatada += "\n----------------------------------------\n";
        } catch (Exception e) {
            respostaFormatada += "\n" + e.getMessage() + "\n";
        } finally {
            return respostaFormatada;
        }
    }
}

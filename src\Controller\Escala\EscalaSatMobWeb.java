/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Escala;

import Dados.Persistencia;
import SasBeans.Escala;
import SasBeans.Municipios;
import SasBeans.Rotas;
import SasBeans.Rt_Escala;
import SasBeans.SasPWFill;
import SasBeans.Veiculos;
import SasBeansCompostas.CarregaEscala;
import SasBeansCompostas.EscalaPessoaDTO;
import SasBeansCompostas.Login;
import SasDaos.EscalaDao;
import SasDaos.LoginDao;
import SasDaos.MunicipiosDao;
import SasDaos.RotasDao;
import SasDaos.Rt_EscalaDao;
import SasDaos.SasPwFilDao;
import SasDaos.VeiculosDao;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class EscalaSatMobWeb {

    private EscalaDao escalaDao;

    public EscalaSatMobWeb() {
    }

    public EscalaSatMobWeb(Persistencia persistencia) {
        escalaDao = new EscalaDao(persistencia);
    }

    public List<Rotas> listarRotasData(String codfil, String data, Boolean excl, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.listarRotasData(codfil, data, excl, codpessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Rt_Escala selecionaRt_Escala(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            Rt_EscalaDao rt_EscalaDao = new Rt_EscalaDao();
            return rt_EscalaDao.SelecionaRt_EscalaSatMobWeb(sequencia, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Rotas buscaRota(String codfil, String rotas, String data, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.buscaRota(codfil, rotas, data, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public SasPWFill buscaFilial(String CodFil, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFillLogin(CodFil, CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CarregaEscala> ListarEscalas(String codfil, String data, BigDecimal codpessoa, Boolean excl, Persistencia persistencia) throws Exception {
        List<CarregaEscala> listaEscala = new ArrayList<>();
        EscalaDao escalaDao = new EscalaDao();
        try {
            LocalDate date = String2LocalDate(data);
            listaEscala = escalaDao.ListaEscalaSatMobWeb(codfil, date, codpessoa, excl, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }

        return listaEscala;
    }

    /**
     * Lista o veículo por filial
     *
     * @param codfil Código da filial
     * @param persistencia Conexão com o código
     * @return Lista de veículos
     * @throws Exception
     */
    public List<Veiculos> ListaVeiculos(String codfil, Persistencia persistencia) throws Exception {
        List<Veiculos> retorno = new ArrayList<>();
        VeiculosDao veiculosDao = new VeiculosDao();

        try {
            retorno = veiculosDao.ListaVeiculo(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    public Veiculos BuscaVeiculoNumero(int numero, Persistencia persistencia) throws Exception {
        Veiculos veiculos = new Veiculos();
        VeiculosDao veiculosDao = new VeiculosDao();

        try {
            veiculos = veiculosDao.BuscaVeiculoNumero(numero, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
        return veiculos;
    }

    public Veiculos BuscaVeiculoPlaca(String placa, Persistencia persistencia) throws Exception {
        Veiculos veiculos = new Veiculos();
        VeiculosDao veiculosDao = new VeiculosDao();

        try {
            veiculos = veiculosDao.BuscaVeiculoPlaca(placa, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
        return veiculos;
    }

    public void AtualizaEscala(Escala escala, Rotas rotas, Rt_Escala rt_Escala, Persistencia persistencia) throws Exception {
        EscalaDao escalaDao = new EscalaDao();
        RotasDao rotasDao = new RotasDao();
        Rt_EscalaDao rt_EscalaDao = new Rt_EscalaDao();

        try {
            escalaDao.AtualizaEscalaSatMobWeb(escala, persistencia);

            rotas.setHrLargada(escala.getHora1());
            rotas.setHrIntIni(escala.getHora2());
            rotas.setHrIntFim(escala.getHora3());
            rotas.setHrChegada(escala.getHora4());
            rotas.setHsTotal(escala.getHsTot().toPlainString());
            //rotas.setOperador(escala.getOperador());
            //rotas.setDt_Alter(escala.getDt_Alter());
            //rotas.setHr_Alter(escala.getHr_Alter());
            rotasDao.atualizarRotasSupervisaoSatMob(rotas, persistencia);

            rt_Escala.setHora(escala.getHrChe());
            rt_Escala.setMatr(escala.getMatrChe());
            rt_Escala.setOperador(escala.getOperador());
            rt_Escala.setDt_alter(tratarValores(escala.getDt_Alter().toString()));
            rt_Escala.setHr_alter(escala.getHr_Alter());
            rt_EscalaDao.atualizarEscala(rt_Escala, persistencia);

        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Login BuscaPermissao(BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        Login login = new Login();
        LoginDao loginDao = new LoginDao();
        try {
            login = loginDao.PermissaoPessoaRota(codpessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }

        return login;
    }

    /**
     * Lista os municípios da base de dados
     *
     * @param persistencia Conexão com a base dados
     * @return Retorna todos os municípios da base de dados
     * @throws Exception
     */
    public List<Municipios> ListaMunicipios(Persistencia persistencia) throws Exception {
        List<Municipios> retorno = new ArrayList<>();
        MunicipiosDao municipiosDao = new MunicipiosDao();
        try {
            retorno = municipiosDao.ListaCodMunUf(persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    public Municipios BuscaMunicipio(BigDecimal codigo, Persistencia persistencia) throws Exception {
        Municipios retorno = new Municipios();
        MunicipiosDao municipiosDao = new MunicipiosDao();

        try {
            retorno = municipiosDao.obtemMuncipio(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    /**
     * Busca o próximo número do veículo disponível
     *
     * @param persistencia Conexão com o banco de dados
     * @return Retorna o número disponível para veículo
     * @throws Exception
     */
    public int BuscaNumeroVeic(Persistencia persistencia) throws Exception {
        int retorno = 0;
        VeiculosDao veiculosDao = new VeiculosDao();

        try {
            retorno = veiculosDao.MaxNumero(persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    public void InsereVeiculo(Veiculos veiculos, Persistencia persistencia) throws Exception {
        VeiculosDao veiculosDao = new VeiculosDao();
        try {
            veiculosDao.insereVeiculoSatMobWeb(veiculos, persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void ExcluirEscala(Escala escala, Persistencia persistencia) throws Exception {
        EscalaDao escalaDao = new EscalaDao();
        try {
            escalaDao.ExcluirEscalaSatMobWeb(escala.getSeqRota(), persistencia);
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void InserirEscala(Escala escala, Rotas rotas, Rt_Escala rt_Escala, Persistencia persistencia) throws Exception {
        EscalaDao escalaDao = new EscalaDao();
        RotasDao rotasDao = new RotasDao();
        Rt_EscalaDao rt_EscalaDao = new Rt_EscalaDao();
        try {

            escalaDao.inserirEscala(escala, persistencia);

            rotas.setHrLargada(escala.getHora1());
            rotas.setHrIntIni(escala.getHora2());
            rotas.setHrIntFim(escala.getHora3());
            rotas.setHrChegada(escala.getHora4());
            rotas.setHsTotal(escala.getHsTot().toPlainString());
//            rotas.setOperador(escala.getOperador());
//            rotas.setDt_Alter(escala.getDt_Alter());
//            rotas.setHr_Alter(escala.getHr_Alter());
            rotasDao.atualizarRotasSupervisaoSatMob(rotas, persistencia);

            rt_Escala.setHora(escala.getHrChe());
            rt_Escala.setMatr(escala.getMatrChe());
            rt_Escala.setOperador(escala.getOperador());
            rt_Escala.setDt_alter(tratarValores(escala.getDt_Alter().toString()));
            rt_Escala.setHr_alter(escala.getHr_Alter());
            rt_EscalaDao.atualizarEscala(rt_Escala, persistencia);

        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Transforma String para LocalDate no formato yyyyMMdd
     *
     * @param dataS Data em String
     * @return Data em String
     */
    private LocalDate String2LocalDate(String dataS) {
        //String dd/MM/yyyy
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate date = LocalDate.parse(dataS, formatter);

        return date;
    }

    //Realiza o tratamento de valores
    private String tratarValores(String valores) {
        if (valores.contains("-")) {
            valores = valores.replace("-", "");
        }
        return valores;
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de escalas
     *
     * @param filtros - filtros para pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            EscalaDao escalasdao = new EscalaDao();
            retorno = escalasdao.TotalEscalasMobWeb(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de escalas
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<CarregaEscala> ListagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<CarregaEscala> retorno;
            EscalaDao escalasdao = new EscalaDao();
            retorno = escalasdao.ListaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("EscalaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<EscalaPessoaDTO> listaPaginadaEscala(
            int primeiro,
            int linhas,
            String codFil,
            String data,
            boolean excluded,
            String rota,
            String nome,
            String nred
    ) throws Exception {
        try {
            codFil = codFil.equals("") ? null : codFil;
            rota = rota.equals("") ? null : rota;
            nome = nome.equals("") ? null : nome;
            nred = nred.equals("") ? null : nred;
            return escalaDao.listaPaginadaEscala(primeiro, linhas, codFil, data, excluded, rota, nome, nred);
        } catch (Exception e) {
            // TODO
            throw new Exception("ErroLerBD");
        }
    }

    public int contagemEscalaPessoa(
            String codFil,
            String data,
            boolean excluded,
            String rota,
            String nome,
            String nred) throws Exception {
        try {
            codFil = codFil.equals("") ? null : codFil;
            rota = rota.equals("") ? null : rota;
            nome = nome.equals("") ? null : nome;
            nred = nred.equals("") ? null : nred;
            return escalaDao.contagemEscalaPessoa(codFil, data, excluded, rota, nome, nred);
        } catch (Exception e) {
            // TODO
            throw new Exception("ErroLerBD");
        }
    }
}

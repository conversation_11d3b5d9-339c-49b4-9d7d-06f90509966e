/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Objeto2Map {

    public static Map conversor(Object objeto) throws Exception {
        try {
            Map retorno = new HashMap();
            Object object;
            Class classDef = Class.forName(objeto.getClass().getCanonicalName());
            object = classDef.newInstance();
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field f : fields) {
                f.setAccessible(true);
                retorno.put(f.getName(), f.get(objeto));
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao converter objeto\r\n" + e.getMessage());
        }
    }

    /**
     * Ignora campos null e strings vazias
     *
     * @param objeto
     * @return
     * @throws Exception
     */
    public static Map conversorSemNull(Object objeto) throws Exception {
        try {
            Map retorno = new HashMap();
            Object object;
            Class classDef = Class.forName(objeto.getClass().getCanonicalName());
            object = classDef.newInstance();
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field f : fields) {
                f.setAccessible(true);
                String value = null;
                try {
                    value = (String) f.get(objeto);
                } catch (Exception e) {
                    value = f.get(objeto).toString();
                }
                if (null != value && !value.equals("")) {
                    retorno.put(f.getName(), f.get(objeto));
                }
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao converter objeto\r\n" + e.getMessage());
        }
    }

    /**
     * Ignora campos null e strings vazias
     *
     * @param objeto
     * @param lista indicador de lista
     * @return
     * @throws Exception
     */
    public static Map conversorESocial(Object objeto, final int lista) throws Exception {
        try {
            Map retorno = new HashMap();
            Object object;
//            Class classDef = Class.forName(objeto.getClass().getCanonicalName());  
            Class classDef = Class.forName(objeto.getClass().getTypeName());
            object = classDef.newInstance();
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field f : fields) {
                f.setAccessible(true);
                Object value = f.get(objeto);
                try {
                    if (null != value) {
                        if (value instanceof String) {
                            value = (String) f.get(objeto);
                            if (null != value && !value.equals("") && !f.getName().equals("sucesso")) {
                                if (lista >= 0) {
                                    retorno.put(f.getName().replace("_", " ") + "[" + lista + "]", f.get(objeto));
                                } else {
                                    retorno.put(f.getName().replace("_", " "), f.get(objeto));
                                }
                            }
                        } else if (value instanceof Number) {
                            value = f.get(objeto).toString();
                            if (null != value && !value.equals("") && !f.getName().equals("sucesso")) {
                                if (lista >= 0) {
                                    retorno.put(f.getName().replace("_", " ") + "[" + lista + "]", f.get(objeto));
                                } else {
                                    retorno.put(f.getName().replace("_", " "), f.get(objeto));
                                }
                            }
                        } else if (value instanceof ArrayList) {
                            List aux = (List) value;
                            for (int i = 0; i < aux.size(); i++) {
                                retorno.putAll(conversorESocial(aux.get(i), i));
                            }
                        } else {
                            retorno.putAll(conversorESocial(value, -1));
                        }
                    }
                } catch (Exception e2) {

                }
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao converter objeto\r\n" + e.getMessage());
        }
    }
}

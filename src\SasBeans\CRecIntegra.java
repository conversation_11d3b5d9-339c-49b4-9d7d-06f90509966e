/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class CRecIntegra {
    private String Sequencia;
    private String Id;
    private String Created;
    private String Tipo_pagamento;
    private String Bandeira;
    private String Tipo_pagamento_id;
    private String Tipo_pagamento_tit;
    private Float Total;
    private Float Taxa;
    private Float Liquido;
    private Float Taxatotal;
    private String Nome;
    private String Nome_titular;
    private String Documento;
    private String Parcelas;
    private String DataRecebimento;
    private String ZoopTransactionId;
    private String Representante;
    private String SeqRota;
    private String Parada;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getId() {
        return Id;
    }

    public void setId(String Id) {
        this.Id = Id;
    }

    public String getCreated() {
        return Created;
    }

    public void setCreated(String Created) {
        this.Created = Created;
    }

    public String getTipo_pagamento() {
        return Tipo_pagamento;
    }

    public void setTipo_pagamento(String Tipo_pagamento) {
        this.Tipo_pagamento = Tipo_pagamento;
    }

    public String getBandeira() {
        return Bandeira;
    }

    public void setBandeira(String Bandeira) {
        this.Bandeira = Bandeira;
    }

    public String getTipo_pagamento_id() {
        return Tipo_pagamento_id;
    }

    public void setTipo_pagamento_id(String Tipo_pagamento_id) {
        this.Tipo_pagamento_id = Tipo_pagamento_id;
    }

    public String getTipo_pagamento_tit() {
        return Tipo_pagamento_tit;
    }

    public void setTipo_pagamento_tit(String Tipo_pagamento_tit) {
        this.Tipo_pagamento_tit = Tipo_pagamento_tit;
    }

    public Float getTotal() {
        return Total;
    }

    public void setTotal(Float Total) {
        this.Total = Total;
    }

    public Float getTaxa() {
        return Taxa;
    }

    public void setTaxa(Float Taxa) {
        this.Taxa = Taxa;
    }

    public Float getLiquido() {
        return Liquido;
    }

    public void setLiquido(Float Liquido) {
        this.Liquido = Liquido;
    }

    public Float getTaxatotal() {
        return Taxatotal;
    }

    public void setTaxatotal(Float Taxatotal) {
        this.Taxatotal = Taxatotal;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getNome_titular() {
        return Nome_titular;
    }

    public void setNome_titular(String Nome_titular) {
        this.Nome_titular = Nome_titular;
    }

    public String getDocumento() {
        return Documento;
    }

    public void setDocumento(String Documento) {
        this.Documento = Documento;
    }

    public String getParcelas() {
        return Parcelas;
    }

    public void setParcelas(String Parcelas) {
        this.Parcelas = Parcelas;
    }

    public String getDataRecebimento() {
        return DataRecebimento;
    }

    public void setDataRecebimento(String DataRecebimento) {
        this.DataRecebimento = DataRecebimento;
    }

    public String getZoopTransactionId() {
        return ZoopTransactionId;
    }

    public void setZoopTransactionId(String ZoopTransactionId) {
        this.ZoopTransactionId = ZoopTransactionId;
    }

    public String getRepresentante() {
        return Representante;
    }

    public void setRepresentante(String Representante) {
        this.Representante = Representante;
    }

    public String getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(String SeqRota) {
        this.SeqRota = SeqRota;
    }

    public String getParada() {
        return Parada;
    }

    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }   
}

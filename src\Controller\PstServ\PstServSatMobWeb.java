/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.PstServ;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.ContrVig;
import SasBeans.CtrItens;
import SasBeans.PstDepen;
import SasBeans.PstInspecao;
import SasBeans.PstServ;
import SasBeans.PstServDoctos;
import SasBeans.RHPonto;
import SasBeans.Rondas;
import SasBeansCompostas.LogsSatMobEW;
import SasBeansCompostas.TmktDetPstPstServClientes;
import SasDaos.ClientesDao;
import SasDaos.ContrVigDao;
import SasDaos.CtrItensDao;
import SasDaos.PstDepenDao;
import SasDaos.PstInspecaoDao;
import SasDaos.PstServDao;
import SasDaos.PstServDoctosDao;
import SasDaos.RHPontoDao;
import SasDaos.RondasDao;
import SasLibrary.Supervisao;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PstServSatMobWeb {

    PstServDoctosDao pstServDoctosDao;

    /**
     * controller de inclusão de posto
     *
     * @param pstserv
     * @param persistencia
     * @throws Exception
     */
    public void Incluir(PstServ pstserv, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstservdao = new PstServDao();

            //Obtendo código do banco
            String banco = pstserv.getCodCli().substring(0, 3);
            String tipoPosto = pstserv.getTipoPosto();
            //Gerando sequencial
            String codigo = "0" + pstserv.getCodCli().substring(5);
            String sequencial = "";
            boolean pararLaco = false;
            while (!pararLaco) {
                sequencial = pstservdao.getSequencial(banco, pstserv.getCodFil().toPlainString(), Integer.parseInt(codigo), tipoPosto, persistencia);
                if (sequencial.equals("999")) {
                    codigo = trataCodigo(Integer.parseInt(codigo) + 1);
                } else {
                    pararLaco = true;
                }
            }

            //Seção do novo posto e incluindo na base de dados
            String secao = banco + "." + codigo + "." + String.format("%03d", Integer.parseInt(sequencial)) + "" + tipoPosto.substring(0, 1);
            pstserv.setSecao(secao);
            pstserv = (PstServ) FuncoesString.removeAcentoObjeto(pstserv);
            pstservdao.adicionaPstServ(pstserv, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem postos conforme parametros passados caso de secao e local em
     * branco, trará a listagem completa
     *
     * @param codfil - codigo de filial - obrigatório
     * @param secao - código da secao
     * @param local - nome do local
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PstServ> ListagemSecao(BigDecimal codfil, String secao, String local, Persistencia persistencia) throws Exception {
        List<PstServ> retorno;
        PstServDao pstservdao = new PstServDao();
        try {
            if (!"".equals(secao)) {
                retorno = pstservdao.listagemPostosSecaoSatMobWeb(codfil, secao, persistencia);
            } else if (!"".equals(local)) {
                retorno = pstservdao.listagemPostosLocalSatMobWeb(codfil, local, persistencia);
            } else { //local
                retorno = pstservdao.listagemPostosSatMobWeb(codfil, persistencia);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<PstServ> PesquisaPosto(PstServ posto, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        List<PstServ> retorno;
        PstServDao pstservdao = new PstServDao();
        try {
            return pstservdao.pesquisaPostosSatMobWeb(posto, CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<PstServ> BuscaPosto(PstServ posto, Persistencia persistencia) throws Exception {
        List<PstServ> retorno;
        PstServDao pstservdao = new PstServDao();
        try {
            return pstservdao.BuscaPostosSatMobWeb(posto, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista as dependências de um posto
     *
     * @param pstServ
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstDepen> getPstDepenPosto(PstServ pstServ, Persistencia persistencia) throws Exception {
        try {
            PstDepenDao pstDepenDao = new PstDepenDao();
            return pstDepenDao.getPstDepen(pstServ.getSecao(), pstServ.getCodFil().toPlainString(), persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista as batidas de ponto de um posto na data específica
     *
     * @param secao
     * @param data
     * @param codFil
     * @param persistenca
     * @return
     * @throws Exception
     */
    public List<RHPonto> getPontosPosto(String secao, String data, String codFil, Persistencia persistenca) throws Exception {
        try {
            RHPontoDao rhPontoDao = new RHPontoDao();
            return rhPontoDao.listaPontosPosto(secao, data, codFil, persistenca);
        } catch (Exception e) {
            throw new Exception("postoservico.falhalistarpontos<message>" + e.getMessage());
        }
    }

    /**
     * Busca os relatórios de um posto em uma data específica
     *
     * @param codfil
     * @param secao
     * @param data1
     * @param data2
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> getRelatoriosPosto(String codfil, String secao, String data1, String data2, Persistencia persistencia) throws Exception {
        try {
            Supervisao supervisao = new Supervisao();
            return supervisao.listaRelatoriosPosto(codfil, secao, data1, data2, persistencia);
        } catch (Exception e) {
            throw new Exception("listasupervisaodia.falha<message>" + e.getMessage());
        }
    }

    /**
     * Lista as rondas de um posto no período seleionado
     *
     * @param dataInicio
     * @param dataFim
     * @param pstServ
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> getRondasPosto(String dataInicio, String dataFim, PstServ pstServ, Persistencia persistencia) throws Exception {
        try {
            RondasDao rondasDao = new RondasDao();
            return rondasDao.getRondasPosto(dataInicio, dataFim, pstServ, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Gravação de alterações
     *
     * @param pstserv - objeto pstserv
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void GravaPstServ(PstServ pstserv, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstservdao = new PstServDao();
            pstserv = (PstServ) FuncoesString.removeAcentoObjeto(pstserv);
            pstservdao.GravaPstServ(pstserv, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    //Acrescenta zero para deixar no padrão
    private String trataCodigo(int codigo) {
        String tratado = "";

        if (codigo < 10) {
            tratado = "0000" + codigo;
        } else if (codigo < 100) {
            tratado = "000" + codigo;
        } else if (codigo < 1000) {
            tratado = "00" + codigo;
        } else if (codigo < 10000) {
            tratado = "0" + codigo;
        } else {
            tratado = "" + codigo;
        }

        return tratado;
    }

    public List<ContrVig> ListarContratos(BigDecimal CodFil, String query, Persistencia persistencia) throws Exception {
        try {
            ContrVigDao contrvigdao = new ContrVigDao();
            return contrvigdao.ListarContratos(CodFil, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<CtrItens> ListarTipos(BigDecimal CodFil, String contrato, String query,
            Persistencia persistencia) throws Exception {
        try {
            CtrItensDao ctritensdao = new CtrItensDao();
            return ctritensdao.BuscarContratos(CodFil, contrato, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<CtrItens> ListarTodosTipos(BigDecimal CodFil, String query,
            Persistencia persistencia) throws Exception {
        try {
            CtrItensDao ctritensdao = new CtrItensDao();
            return ctritensdao.BuscarTodosContratos(CodFil, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> ListarClientes(String CodFil, String query, Persistencia persistencia)
            throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.ListagemSimplesCliente(CodFil, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de postos
     *
     * @param filtros - filtros de pesquisa
     * @param codPessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            PstServDao pstservdao = new PstServDao();
            retorno = pstservdao.totalListaPaginada(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pstserv.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de postos
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo da pessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PstServ> ListagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<PstServ> retorno;
            PstServDao pstservdao = new PstServDao();
            retorno = pstservdao.ListaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("pstserv.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Inclusão de documentos
     *
     * @param pstServDoctos
     * @param persistencia
     * @return - o documento inserido com possíveis alterações
     * @throws Exception
     */
    public PstServDoctos incluirDocumentos(PstServDoctos pstServDoctos, Persistencia persistencia) throws Exception {
        try {
            BigDecimal ordem = getPstServDoctosDao().getMaxOrdem(pstServDoctos, persistencia);
            if (getPstServDoctosDao().existDescricao(pstServDoctos, persistencia)) {
                String nome = pstServDoctos.getDescricao().substring(0, pstServDoctos.getDescricao().lastIndexOf("."));
                String tipo = pstServDoctos.getDescricao().split("\\.")[pstServDoctos.getDescricao().split("\\.").length - 1];
                pstServDoctos.setDescricao(nome + "_" + ordem.toBigInteger().toString() + "." + tipo);
            }
            pstServDoctos.setOrdem(ordem);
            getPstServDoctosDao().adicionaPstServDoctos(pstServDoctos, persistencia);
            return pstServDoctos;
        } catch (Exception e) {
            throw new Exception("pstserv.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem de documentos por Secao, CodFil e Ordem
     *
     * @param pstServDoctos
     * @param persistencia
     * @return lPstServDoctos
     * @throws Exception
     */
    public List<PstServDoctos> listarDocumentos(PstServDoctos pstServDoctos, Persistencia persistencia) throws Exception {
        try {
            return getPstServDoctosDao().getPostos(pstServDoctos, persistencia);
        } catch (Exception e) {
            throw new Exception("pstserv.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Deleta um documento por Secao, CodFil e Descricao
     *
     * @param pstServDoctos
     * @param persistencia
     * @throws Exception
     */
    public void excluirDocumento(PstServDoctos pstServDoctos, Persistencia persistencia) throws Exception {
        try {
            getPstServDoctosDao().excluirDocumento(pstServDoctos, persistencia);
        } catch (Exception e) {
            throw new Exception("pstserv.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * PstServDoctosDao singleton
     *
     * @return pstServDoctosDao
     */
    public PstServDoctosDao getPstServDoctosDao() {
        if (pstServDoctosDao == null) {
            pstServDoctosDao = new PstServDoctosDao();
        }
        return pstServDoctosDao;
    }

    public List<PstServ> listarPostos(String query, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstServDao = new PstServDao();
            return pstServDao.buscarPostos(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pstserv.falhageral<message>" + e.getMessage());
        }
    }

    public List<LogsSatMobEW> listaPorSecaoIntervalo(String codFil, String secao, String dataIni, String dataFim, Persistencia persistencia) throws Exception {
        PstInspecaoDao dao = new PstInspecaoDao();
        return dao.listaPorSecaoIntervalo(codFil, secao, dataIni, dataFim, persistencia);
    }

}

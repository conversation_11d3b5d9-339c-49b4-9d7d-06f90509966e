<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/usuarios.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                .UserInativo, .UserInativo td{
                    color: red;
                }
            </style>
        </h:head>
        <h:body id="h" style="overflow:hidden !important;max-height:100% !important;">
            <f:metadata>
                <f:viewAction action="#{acessos.Persistencias(login.pp, login.satellite)}" />
            </f:metadata>
            <p:growl id="msgs" />
            <div id="body" style="overflow:hidden !important;max-height:100% !important;">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_usuarios.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Usuarios}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{acessos.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">#{acessos.filiaisCabecalho.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                        <label class="FilialEndereco">#{acessos.filiaisCabecalho.endereco}</label>
                                        <label class="FilialBairroCidade">#{acessos.filiaisCabecalho.bairro}, #{acessos.filiaisCabecalho.cidade}/#{acessos.filiaisCabecalho.UF}</label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>
                <h:form id="main">
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; overflow-y:auto !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela" value="#{acessos.allAcessos}" paginator="true" rows="15" lazy="true" rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Usuarios}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista" selection="#{acessos.selecionado}" selectionMode="single" styleClass="tabela DataGrid"
                                                 emptyMessage="#{localemsgs.SemRegistros}" scrollable="false" scrollHeight="100%" reflow="true"
                                                 rowStyleClass="#{lista.saspw.situacao eq 'A'?'':'UserInativo'}"  >
                                        <p:ajax event="rowDblselect" listener="#{acessos.dblSelect}" update="formEditar msgs"/>
                                        <p:column headerText="#{localemsgs.Filial}" class="text-center descricao">
                                            <h:outputText value="#{lista.saspw.codFil}" title="#{lista.saspw.codFil}"
                                                          converter="conversorCodFil" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nome}" class="text-center descricao">
                                            <h:outputText value="#{lista.pessoa.nome}"
                                                          title="#{lista.pessoa.nome}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Email}" class="text-center descricao">
                                            <h:outputText value="#{lista.pessoa.email}" title="#{lista.pessoa.email}"/>
                                        </p:column>
                                    </p:dataTable>

                                    <script>
                                        // <![CDATA[
                                        (function () {
                                            function responsividade() {
                                                if ($(window).width() <= 640) {
                                                    $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                                } else {
                                                    $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                                }
                                            }

                                            $(document).ready(responsividade);
                                            $(window).resize(responsividade);
                                        })();
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 250px !important; background: transparent; height:200px !important;" id="botoes">
                        <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                        
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Cadastrar}" update="formEditar:editar msgs formEditar"
                                           action="#{acessos.novoUsuarioPessoaAcesso}">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{acessos.buttonAction}"
                                           update="formEditar:editar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        
                       
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" update="pesquisar" actionListener="#{acessos.PrePesquisar}"
                                           oncomplete="PF('dlgPesquisar').show();">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}"
                                           update="msgs main:tabela cabecalho corporativo" actionListener="#{acessos.LimparFiltros}">

                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>


                    </p:panel>
                </h:form>

                <h:form id="formGrupos">
                    <p:dialog widgetVar="dlgGrupos" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgGrupos').closeIcon.unbind('click');

                                //register your own
                                PF('dlgGrupos').closeIcon.click(function (e) {
                                    $("#formGrupos\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>
                        <f:facet name="header">
                            <img src="../assets/img/icone_usuarios.png" height="40" width="40"/>
                            #{localemsgs.CadastrarGrupos}
                        </f:facet>
                        <p:panel id="editar" style="background-color: transparent;" class="tabs">
                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                             oncomplete="PF('dlgGrupos').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-9,ui-grid-col-1"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}:"/>
                                <p:inputText value="#{acessos.novoGrupo.descricao}" id="descricao" style="width: 100%">
                                    <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                </p:inputText>

                            </p:panelGrid>

                            <p:panelGrid columns="2">
                                <p:panel class="panelTabela">
                                    <p:dataTable id="grupos" value="#{acessos.grupos}" scrollable="true" emptyMessage="#{localemsgs.SemRegistros}"
                                                 var="grupo" rowKey="#{grupo.codigo.toString()}" scrollHeight="200"
                                                 sortBy="#{grupo.descricao}" resizableColumns="true"
                                                 selectionMode="single" selection="#{acessos.grupoEdicao}" styleClass="tabela"
                                                 style="font-size: 12px">
                                        <p:ajax event="rowSelect" listener="#{acessos.selecionarGrupo}" process="@this"/>
                                        <p:ajax event="rowDblselect" listener="#{acessos.dblSelecionarGrupo}" process="@this"/>
                                        <p:column headerText="#{localemsgs.Descricao}">
                                            <p:outputLabel value="#{grupo.descricao}" title="#{grupo.descricao}"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panel>
                                <p:panel style="width: 30px; padding-right: 0px; height: 200px">
                                    <p:commandLink title="#{localemsgs.Adicionar}" id="adicionarGrupo" process="@this"
                                                   action="#{acessos.detalhesGrupo}"
                                                   update="msgs formDetalhesGrupo">
                                        <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="30" height="30" />
                                    </p:commandLink>
                                </p:panel>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formDetalhesGrupo">
                    <p:dialog widgetVar="dlgDetalhesGrupo" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgDetalhesGrupo').closeIcon.unbind('click');

                                //register your own
                                PF('dlgDetalhesGrupo').closeIcon.click(function (e) {
                                    $("#formDetalhesGrupo\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });

                            });
                        </script>
                        <f:facet name="header">
                            <img src="../assets/img/icone_usuarios.png" height="40" width="40"/>
                            #{localemsgs.DetalhesGrupo}
                        </f:facet>
                        <p:panel id="editar" style="background-color: transparent;" class="tabs">
                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                             oncomplete="PF('dlgDetalhesGrupo').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-9,ui-grid-col-1"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}:"/>
                                <p:inputText value="#{acessos.grupoEdicao.descricao}" id="descricao" style="width: 100%">
                                    <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                </p:inputText>

                                <p:commandLink id="btnCadastrar" update=":msgs :formGrupos:grupos"
                                               action="#{acessos.editarGrupo}" class="right"
                                               title="#{localemsgs.Cadastrar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30" />
                                </p:commandLink>
                            </p:panelGrid>

                            <span>
                                Usuários do grupos
                            </span>
                            <p:dataTable id="usuarios" value="#{acessos.usuariosGrupo}" scrollable="true"
                                         emptyMessage="#{localemsgs.SemRegistros}" scrollWidth="100%"
                                         var="usuario" rowKey="#{usuario.nome}" scrollHeight="150"
                                         sortBy="#{usuario.nomeCompleto}" resizableColumns="true" styleClass="tabela"
                                         style="font-size: 12px">
                                <p:column headerText="#{localemsgs.Nome}" style="width: 180px">
                                    <p:outputLabel value="#{usuario.nomeCompleto}" title="#{usuario.nomeCompleto}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Email}" style="width: 180px">
                                    <p:outputLabel value="#{usuario.email}" title="#{usuario.email}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Adm}" style="width: 70px">
                                    <p:outputLabel value="#{usuario.motivo}" title="#{usuario.motivo}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Situacao}" style="width: 70px">
                                    <p:outputLabel value="#{usuario.situacao}" title="#{usuario.situacao}"/>
                                </p:column>
                            </p:dataTable>

                            <span>
                                clientes que algum usuário do grupo tem acesso
                            </span>
                            <p:dataTable id="clientes" value="#{acessos.clientesGrupo}" scrollable="true" emptyMessage="#{localemsgs.SemRegistros}"
                                         var="cliente" rowKey="#{cliente.codigo}" scrollHeight="150" scrollWidth="100%"
                                         sortBy="#{cliente.NRed}" resizableColumns="true" styleClass="tabela"
                                         style="font-size: 12px">
                                <p:column headerText="#{localemsgs.CodFil}" style="width: 40px">
                                    <p:outputLabel value="#{cliente.codFil}" title="#{cliente.codFil}" converter="conversorCodFil"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Codigo}" style="width: 65px">
                                    <p:outputLabel value="#{cliente.codigo}" title="#{cliente.codigo}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.NRed}" style="width: 150px">
                                    <p:outputLabel value="#{cliente.NRed}" title="#{cliente.NRed}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Ende}" style="width: 150px">
                                    <p:outputLabel value="#{cliente.ende}" title="#{cliente.ende}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Usuarios}" style="width: 70px">
                                    <p:outputLabel value="#{cliente.lote}" title="#{cliente.lote}"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form class="form-inline" id="formEditar">
                    <p:dialog widgetVar="dlgEditar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" focus="formEditar:nome"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; padding-bottom:20px !important;
                              border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important;
                              padding:0px 0px 20px 0px !important; overflow-y: auto !important">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgEditar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgEditar').closeIcon.click(function (e) {
                                    $("#formEditar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });


                            })
                        </script>
                        <f:facet name="header">
                            <img src="../assets/img/icone_usuarios.png" height="40" width="40"/>
                            #{localemsgs.CadastrarUsuario}
                        </f:facet>
                        <p:panel id="editar" style="background-color: transparent;">
                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                             oncomplete="PF('dlgEditar').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                            <p:focus context="formEditar:nome"/>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="subFil" value="#{localemsgs.Filial}:" rendered="#{!login.nivel.equals(8)}"/>
                                <p:selectOneMenu id="subFil" value="#{acessos.filial}" converter="omnifaces.SelectItemsConverter"
                                                 filter="true" filterMatchMode="contains"  disabled="#{acessos.flag eq 2}"
                                                 style="width: 100%" rendered="#{!login.nivel.equals(8)}">
                                    <f:selectItems value="#{acessos.todasFiliais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}"/>
                                    <p:ajax event="itemSelect" listener="#{acessos.SelecionarFilialUsuario}" update="formEditar:editar"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="nome" value="#{localemsgs.Pessoa}:" />
                                <p:inputText value="#{acessos.novo.pessoa.nome}" 
                                             id="nome" style="width: 100%">
                                    <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                </p:inputText>
                                <p:outputLabel for="email" value="#{localemsgs.Email}:"/>
                                <p:inputText value="#{acessos.novo.pessoa.email}" rendered="#{!login.nivel.equals(8)}" id="email" style="width: 100%">
                                    <p:watermark for="email" value="#{localemsgs.Email}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">


                                <p:outputLabel for="senha" value="#{localemsgs.Senha}:"/>
                                <p:password id="senha" value="#{acessos.novo.pessoa.PWWeb}" required="true" transient="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                            label="#{localemsgs.Senha}" feedback="true" redisplay="true" match="confirmacao"
                                            promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                            goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                            style="width: 100%">
                                    <f:validateRegex pattern="^[0-9]{5,20}$" for="senha"/>
                                    <p:watermark for="senha" value="#{localemsgs.Senha}"/>
                                </p:password>

                                <p:outputLabel for="confirmacao" value="#{localemsgs.Confirmacao}:" />
                                <p:password id="confirmacao" value="#{acessos.novo.pessoa.PWWeb}" redisplay="true"
                                            label="#{localemsgs.Senha}" style="width: 100%">
                                    <p:watermark for="confirmacao" value="#{localemsgs.Senha}"/>
                                </p:password>
                            </p:panelGrid>


                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:commandLink id="btnCadastrar" update=":msgs :main:tabela cabecalho formEditar:editar"
                                               action="#{acessos.cadastrarPessoaAcesso}"
                                               title="#{localemsgs.Cadastrar}" rendered="#{acessos.flag eq 1 and !login.nivel.equals(8)}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink id="btnEditarPessoaAcesso" update=":msgs :main:tabela cabecalho formEditar:editar"
                                               action="#{acessos.editarPessoaAcesso}"
                                               title="#{localemsgs.Editar}" rendered="#{acessos.flag eq 2 and !login.nivel.equals(8)}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:confirmDialog message="#{acessos.grupoSelecionado}" header="#{localemsgs.Confirmacao}"
                                                 showEffect="drop" appendTo="@(body)" width="300"
                                                 hideEffect="drop" widgetVar="permissaoGrupo">
                                    <p:commandButton value="#{localemsgs.Sim}" process="@this"
                                                     styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                     onclick="PF('dlgOk').show()"
                                                     update="msgs"/>
                                    <p:commandButton value="#{localemsgs.Nao}" action="#{acessos.cadastrar}" process="formEditar" partialSubmit="true"
                                                     styleClass="ui-confirmdialog-no" icon="ui-icon-close" update="msgs main:tabela"
                                                     oncomplete="PF('permissaoGrupo').hide()"/>
                                </p:confirmDialog>

                                <p:dialog header="#{localemsgs.Permissoes}" widgetVar="dlgOk" resizable="false" appendTo="@(body)"
                                          draggable="false" closable="true" width="300">
                                    <p:panel id="permissoesDeGrupo" style="height: 150px">
                                        <div class="form-inline">
                                            <h:outputText value="#{localemsgs.Permissoes}:" style="position: absolute; float: left"/>
                                        </div>

                                        <p:spacer height="30px"/>

                                        <div class="form-inline">
                                            <p:outputLabel for="inclusao" value="#{localemsgs.Inclusao}:" style="position: absolute; float: left"/>
                                            <p:selectBooleanCheckbox id="inclusao" value="#{acessos.inclusao}"
                                                                     style="position: absolute; float: left; left: 100px"/>
                                        </div>

                                        <p:spacer height="30px"/>

                                        <div class="form-inline">
                                            <p:outputLabel for="alteracao" value="#{localemsgs.Alteracao}:" style="position: absolute; float: left"/>
                                            <p:selectBooleanCheckbox id="alteracao" value="#{acessos.alteracao}"
                                                                     style="position: absolute; float: left; left: 100px"/>
                                        </div>

                                        <p:spacer height="30px"/>

                                        <div class="form-inline">
                                            <p:outputLabel for="exclusao" value="#{localemsgs.Exclusao}:" style="position: absolute; float: left"/>
                                            <p:selectBooleanCheckbox id="exclusao" value="#{acessos.exclusao}"
                                                                     style="position: absolute; float: left; left: 100px"/>
                                        </div>

                                        <p:spacer height="30px"/>

                                        <div class="form-inline">
                                            <p:commandLink id="btnPermGrupos" update=":msgs :main:tabela cabecalho formEditar:editar"
                                                           action="#{acessos.AdicionarPermissoes}" partialSubmit="true"
                                                           process="formEditar:permissoesDeGrupo"
                                                           title="#{localemsgs.Cadastrar}" style="float:left; left:15px; position: absolute">
                                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                            </p:commandLink>
                                        </div>
                                    </p:panel>
                                </p:dialog>
                            </p:panelGrid>

                            
                        </p:panel>
                    </p:dialog>

                    <p:dialog header="#{localemsgs.AdicionarFilial}" widgetVar="dlgFiliais"
                              closable="true" resizable="false" width="400" hideEffect="fade"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; padding-bottom:20px !important;
                              border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important;
                              padding:0px 0px 20px 0px !important;">
                        <p:panel id="panelFiliais" style="background-color: transparent; height: 40px;">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="addFil" value="#{localemsgs.Filial}:"/>
                                <p:column>
                                    <p:selectOneMenu id="addFil" value="#{acessos.novaFilial}"
                                                     converter="omnifaces.SelectItemsConverter"
                                                     style="width: calc(100% - 30px); float: left"
                                                     filterMatchMode="contains" filter="true">
                                        <f:selectItems value="#{acessos.todasFiliais}" var="fil" itemValue="#{fil}"
                                                       itemLabel="#{fil.codfilAc} - #{fil.descricao}"/>
                                        <p:ajax event="itemSelect" listener="#{acessos.SelecionarNovaFilial}"/>
                                        <p:watermark for="addFil" value="#{localemsgs.Filial}"/>
                                    </p:selectOneMenu>

                                    
                                </p:column>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>

                    <p:dialog widgetVar="dlgAdicionarPermissoes" header="#{localemsgs.Permissoes}"
                              closable="true" resizable="false" width="400" hideEffect="fade"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; padding-bottom:20px !important;
                              border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important;
                              padding:0px 0px 20px 0px !important;">
                        <p:panel id="adicionarPermissoes" style="background-color: transparent;">
                            <div class="form-inline">
                                <p:outputLabel for="addPermissao" value="#{localemsgs.Sistema}:"
                                               style="position: absolute; float: left"/>
                                <p:selectOneMenu id="addPermissao" value="#{acessos.novaPermissao.sysdef}"
                                                 converter="omnifaces.SelectItemsConverter"
                                                 style="position: absolute; float: left; left: 100px; width: 250px;"
                                                 filterMatchMode="contains" filter="true">
                                    <f:selectItems value="#{acessos.todasPermissoes}" var="permissoes" itemValue="#{permissoes}"
                                                   itemLabel="#{permissoes.codigo} - #{permissoes.subSistema}"/>
                                </p:selectOneMenu>
                                <p:watermark for="addPermissao" value="#{localemsgs.Permissao}"/>

                            </div>

                            <p:spacer height="40px"/>

                            <div class="form-inline">
                                <p:outputLabel for="inclusaoAdd" value="#{localemsgs.Inclusao}:" style="position: absolute; float: left"/>
                                <p:selectBooleanCheckbox id="inclusaoAdd" value="#{acessos.inclusao}"
                                                         style="position: absolute; float: left; left: 100px"/>
                            </div>

                            <p:spacer height="30px"/>

                            <div class="form-inline">
                                <p:outputLabel for="alteracaoAdd" value="#{localemsgs.Alteracao}:" style="position: absolute; float: left"/>
                                <p:selectBooleanCheckbox id="alteracaoAdd" value="#{acessos.alteracao}"
                                                         style="position: absolute; float: left; left: 100px"/>
                            </div>

                            <p:spacer height="30px"/>

                            <div class="form-inline">
                                <p:outputLabel for="exclusaoAdd" value="#{localemsgs.Exclusao}:" style="position: absolute; float: left"/>
                                <p:selectBooleanCheckbox id="exclusaoAdd" value="#{acessos.exclusao}"
                                                         style="position: absolute; float: left; left: 100px"/>
                            </div>

                            <p:spacer height="30px"/>

                            <div class="form-inline">
                                <p:commandLink oncomplete="PF('dlgAdicionarPermissoes').hide()"
                                               action="#{acessos.AdicionarPermissao}" process="adicionarPermissoes"
                                               title="#{localemsgs.Selecionar}"
                                               update="adicionarPermissoes ">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>

                    <p:dialog widgetVar="dlgEditarPermissoes"
                              closable="true" resizable="false" width="400" hideEffect="fade"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; padding-bottom:20px !important;
                              border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important;
                              padding:0px 0px 20px 0px !important;">
                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.Permissoes}"/>
                        </f:facet>
                        <p:panel id="editarPermissoes" style="background-color: transparent;">
                            <div class="form-inline">
                                <p:outputLabel for="sistema" value="#{localemsgs.Sistema}:" style="position: absolute; float: left"/>
                                <p:inputText id="sistema" value="#{acessos.permissaoSelecionada.saspwac.sistema}" disabled="true"
                                             required="true" label="#{localemsgs.Sistema}"
                                             style="position: absolute; float: left; left: 100px; width: 250px;"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Sistema}">
                                    <f:convertNumber pattern="0"/>
                                </p:inputText>
                            </div>

                            <p:spacer height="40px"/>

                            <div class="form-inline">
                                <p:outputLabel for="inclusaoEditar" value="#{localemsgs.Inclusao}:" style="position: absolute; float: left"/>
                                <p:selectBooleanCheckbox id="inclusaoEditar" value="#{acessos.inclusao}"
                                                         style="position: absolute; float: left; left: 100px"/>
                            </div>

                            <p:spacer height="30px"/>

                            <div class="form-inline">
                                <p:outputLabel for="alteracaoEditar" value="#{localemsgs.Alteracao}:" style="position: absolute; float: left"/>
                                <p:selectBooleanCheckbox id="alteracaoEditar" value="#{acessos.alteracao}"
                                                         style="position: absolute; float: left; left: 100px"/>
                            </div>

                            <p:spacer height="30px"/>

                            <div class="form-inline">
                                <p:outputLabel for="exclusaoEditar" value="#{localemsgs.Exclusao}:" style="position: absolute; float: left"/>
                                <p:selectBooleanCheckbox id="exclusaoEditar" value="#{acessos.exclusao}"
                                                         style="position: absolute; float: left; left: 100px"/>
                            </div>

                            <p:spacer height="30px"/>

                            <div class="form-inline">
                                <p:commandLink oncomplete="PF('dlgEditarPermissoes').hide()"
                                               action="#{acessos.EditarPermissao}" process="editarPermissoes"
                                               title="#{localemsgs.Selecionar}" update="">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
                
                <h:form id="formCadastrarCliente">
                    <p:dialog widgetVar="dlgAdicionarClientes" header="#{localemsgs.AdicionarCliente}"
                              closable="true" resizable="false" width="400" hideEffect="fade" draggable="false" modal="true"
                              styleClass="noscroll"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;
                              border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important;">
                        <p:panel id="adicionarClientes" style="background-color: transparent; overflow: hidden">

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="addCliente" value="#{localemsgs.Cliente}:"/>
                                <p:column>
                                    <p:inputText id="addCliente" value="#{acessos.nome}" style="width: calc(100% - 30px); float: left;"/>
                                    <p:watermark for="addCliente" value="#{localemsgs.Cliente}" />
                                    <p:commandLink title="#{localemsgs.Pesquisar}" style="float: left;" actionListener="#{acessos.listarClientes}"
                                                   update="adicionarClientes msgs" oncomplete="PF('dlgAdicionarClientes').initPosition();">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                    </p:commandLink>
                                </p:column>
                            </p:panelGrid>

                            <p:scrollPanel mode="native" style="height:200px" rendered="#{acessos.todosClientes.size() gt 0}" >
                                <p:dataGrid id="clientes" value="#{acessos.todosClientes}" rendered="#{acessos.todosClientes.size() gt 0}" 
                                            emptyMessage="#{localemsgs.SemRegistros}"
                                            var="listaClientes" columns="1" style="max-height: 200px">
                                    <p:panel styleClass="#{listaClientes.selecionado ? 'selecionado' : ''}"
                                             style="background-color: #F0F0F0; border: 1px solid #DDD !important;
                                             border-radius: 6px; padding: 0px 0px 15px 0px !important; box-shadow:1px 1px 5px #CCC">
                                        <f:facet name="header">
                                            <p:selectBooleanCheckbox value="#{listaClientes.selecionado}" style="margin-right:15px !important;">
                                                <p:ajax update="msgs clientes " listener="#{acessos.selecionarClienteCadastro(listaClientes)}"/>
                                            </p:selectBooleanCheckbox>
                                            <h:outputText value="#{listaClientes.codFil}" converter="conversor0"/>
                                            <h:outputText value=" #{listaClientes.NRed}"/>
                                        </f:facet>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                                     layout="grid" styleClass="ui-panelgrid-blank"
                                                     style="padding:10px 15px 10px 15px !important; font-size:10pt !important;font-family:'Open Sans', sans-serif !important; 
                                                     background-color: #FFF !important; border-radius: 5px; border:thin solid #DDD !important; box-shadow:2px 2px 3px #DDD; 
                                                     margin-left:15px !important; width:calc(100% - 30px) !important;">
                                            <h:outputText value="#{localemsgs.Nome}:" style="float:right"/>
                                            <h:outputText value="#{listaClientes.nome}" styleClass="negrito azul"/>

                                            <h:outputText value="#{localemsgs.Endereco}:" style="float:right"/>
                                            <h:outputText value="#{listaClientes.ende}" styleClass="negrito azul"/>

                                            <h:outputText value="#{localemsgs.Email}:" style="float:right"/>
                                            <h:outputText value="#{listaClientes.email}" styleClass="negrito azul"/>

                                            <h:outputText value="#{localemsgs.Fone}:" style="float:right"/>
                                            <h:outputText value="#{listaClientes.fone1}" styleClass="negrito azul"/>
                                        </p:panelGrid>

                                    </p:panel>
                                </p:dataGrid>

                            </p:scrollPanel>

                            <div class="form-inline">
                                <p:commandLink oncomplete="PF('dlgAdicionarClientes').hide()"
                                               title="#{localemsgs.Fechar}"
                                               update="msgs">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form class="form-inline" id="formBancos">
                    <p:dialog header="#{localemsgs.AdicionarBanco}"
                              widgetVar="dlgAdicionarBanco" closable="true" resizable="false" width="400"
                              hideEffect="fade"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; padding-bottom:20px !important;
                              border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important;
                              padding:0px 0px 20px 0px !important;">
                        <p:outputPanel id="panelBancos">
                            <div class="form-inline">
                                <p:panelGrid columns="1" columnClasses="ui-grid-col-12" layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:selectOneMenu id="banco" value="#{acessos.novoBanco}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="form-control" required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Banco}"
                                                     style="width: 100%; height: 40px;"
                                                     filter="true" filterMatchMode="contains" >
                                        <f:selectItems value="#{login.listaEmpresas}" var="banco" itemValue="#{banco}"
                                                       itemLabel="#{banco}"  noSelectionValue="Selecione"/>
                                        <p:ajax listener="#{acessos.selecionarBanco(login.obterNovaPersistencia(acessos.novoBanco))}"
                                                update="panelBancos msgs" oncomplete="PF('dlgAdicionarBanco').initPosition()"/>
                                    </p:selectOneMenu>

                                    <p:outputLabel for="bancoFilial" value="#{localemsgs.SelecioneFilial}" rendered="#{acessos.pessoaLoginPersistencia ne null}"/>
                                    <p:selectOneMenu id="bancoFilial" value="#{acessos.pessoaLoginFilial}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains" rendered="#{acessos.pessoaLoginPersistencia ne null}"
                                                     style="width: 100%">
                                        <f:selectItems value="#{acessos.pessoaLoginFiliais}" var="filial" itemValue="#{filial}"
                                                       itemLabel="#{filial.descricao}"/>
                                    </p:selectOneMenu>

                                    <p:outputLabel for="nivel" value="#{localemsgs.Nivel}:" rendered="#{acessos.pessoaLoginPersistencia ne null}"/>
                                    <p:selectOneMenu value="#{acessos.pessoaLoginUsuario.saspw.nivelx}" id="nivel" rendered="#{acessos.pessoaLoginPersistencia ne null}"
                                                     required="true" label="#{localemsgs.Nivel}" style="width: 100%"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nivel}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                        <f:selectItems value="#{acessos.niveis}" />
                                    </p:selectOneMenu>

                                    <p:outputLabel for="grupo" value="#{localemsgs.Grupo}:" rendered="#{acessos.pessoaLoginPersistencia ne null}"/>
                                    <p:selectOneMenu value="#{acessos.pessoaLoginUsuario.grupo.codigo}" id="grupo" style="width: 100%"
                                                     rendered="#{acessos.pessoaLoginPersistencia ne null}"
                                                     required="true" label="#{localemsgs.Grupo}" filter="true" filterMatchMode="contains"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Grupo}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItems value="#{acessos.grupos}" var="grupos" itemValue="#{grupos.codigo}"
                                                       itemLabel="#{grupos.descricao}" noSelectionValue="Selecione"/>
                                    </p:selectOneMenu>
                                </p:panelGrid>
                            </div>
                            <div class="form-inline">
                                <p:commandLink oncomplete="PF('dlgAdicionarBanco').hide()"
                                               action="#{acessos.adicionarBanco}"
                                               title="#{localemsgs.Selecionar}"
                                               update="msgs">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:outputPanel>
                    </p:dialog>
                </h:form>

                <h:form class="form-inline" id="formServicos">
                    <p:dialog header="#{localemsgs.AdicionarServico}"
                              widgetVar="dlgServicos" closable="true" resizable="false" width="400"
                              hideEffect="fade"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                        <p:outputPanel id="panelServicos">
                            <div class="form-inline">
                                <p:panelGrid columns="1" columnClasses="ui-grid-col-12" layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:selectOneMenu id="servico" value="#{acessos.servico}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="form-control" required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Servico}"
                                                     style="width: 100%; height: 40px;"
                                                     filter="true" filterMatchMode="contains" >
                                        <f:selectItems value="#{acessos.todosServicos}" var="serv" itemValue="#{serv}"
                                                       itemLabel="#{serv.descricao}"  noSelectionValue="Selecione"/>
                                    </p:selectOneMenu>
                                </p:panelGrid>
                            </div>
                            <div class="form-inline">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="ordem" value="#{localemsgs.Ordem}: "/>
                                    <p:inputText id="ordem" value="#{acessos.novoServico.ordem}"
                                                 required="true" label="#{localemsgs.Ordem}"
                                                 style="width: 100%; height: 40px;"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Ordem}">
                                        <f:convertNumber pattern="0"/>
                                    </p:inputText>
                                </p:panelGrid>
                            </div>
                            <div class="form-inline">
                                <p:commandLink oncomplete="PF('dlgServicos').hide()"
                                               action="#{acessos.adicionarServico}"
                                               title="#{localemsgs.Selecionar}"
                                               update="msgs">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:outputPanel>
                    </p:dialog>
                </h:form>

                <!--Cadastrar novo-->
                <h:form class="form-inline" id="formCadastrar">
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute"  focus="cpf" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" styleClass="dialogo"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarPessoa}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent" class="cadastrar2">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="cpf" value="#{localemsgs.CPF}: " />
                                <p:inputMask id="cpf" value="#{acessos.pessoas.novaPessoa.CPF}" disabled="#{acessos.pessoas.flag eq 2}"
                                             maxlength="11" mask="#{mascaras.mascaraCPF}">
                                    <p:watermark for="cpf" value="#{localemsgs.CPF}"/>
                                    <p:ajax event="blur" listener="#{acessos.pessoas.buscarPessoaCofre}"
                                            update="msgs"/>
                                </p:inputMask>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="nome" value="#{localemsgs.Nome}: " indicateRequired="false"/>
                                <p:inputText id="nome" value="#{acessos.pessoas.novaPessoa.nome}" style="width: 100%"
                                             required="true" label="#{localemsgs.Nome}" disabled="#{acessos.pessoas.edicao}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                             maxlength="50">
                                    <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                </p:inputText>

                                <p:outputLabel for="email" value="#{localemsgs.Email}: " indicateRequired="false"/>
                                <p:inputText id="email" value="#{acessos.pessoas.novaPessoa.email}" style="width: 100%"
                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"
                                             maxlength="50" disabled="#{acessos.pessoas.edicao}">
                                    <p:watermark for="email" value="#{localemsgs.Email}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:commandLink id="cadastro" action="#{acessos.pessoas.cadastroSimples}" update="formCadastrar :main:tabela :msgs cabecalho"
                                               title="#{localemsgs.Cadastrar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Pesquisar -->
                <p:dialog header="#{localemsgs.Pesquisar}" widgetVar="dlgPesquisar" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true"
                          width="440" showEffect="drop" hideEffect="drop"
                          style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_usuarios.png" height="40" width="40"/>
                        #{localemsgs.PesquisarUsuario}
                    </f:facet>
                    <p:panel id="pesquisar">
                        <h:form id="formPesquisar" class="form-inline">
                            <div class="form-inline">
                                <p:outputLabel for="subFil" value="#{localemsgs.Filial}:" style="float: left;position:absolute;"/>
                                <p:selectOneMenu id="subFil" value="#{acessos.filial}" converter="omnifaces.SelectItemsConverter"
                                                 filter="true" filterMatchMode="contains"
                                                 style="float: left;left:120px;position:absolute; width: 280px">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItems value="#{acessos.todasFiliais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}"/>
                                    <p:ajax event="itemSelect" listener="#{acessos.SelecionarFilialUsuario}"/>
                                </p:selectOneMenu>
                            </div>

                            <p:spacer height="40px"/>

                            <div class="form-inline">
                                <p:outputLabel for="nome" value="#{localemsgs.Nome}:" style="float: left;position:absolute;"/>
                                <p:inputText id="nome" value="#{acessos.selecionado.pessoa.nome}"
                                             style="float: left;left:120px;position:absolute; width: 280px"/>
                            </div>

                            <p:spacer height="40px"/>

                            <div class="form-inline">
                                <p:commandLink oncomplete="PF('dlgPesquisar').hide()"
                                               action="#{acessos.PesquisaPaginadaPessoaAcesso}"
                                               title="#{localemsgs.Pesquisar}" update="main:tabela msgs cabecalho corporativo">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </h:form>
                    </p:panel>
                </p:dialog>

                <!-- Relatorio Usuarios -->
                <h:form id="formRelatorioUsuario" class="form-inline">
                    <p:dialog widgetVar="dlgRelatorioUsuario" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                        <f:facet name="header">
                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-8,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">

                                <img src="../assets/img/icone_usuarios.png" height="40" width="40"/>

                                <h:outputText value="#{localemsgs.RelatorioAuditoria}"/>

                                <p:panel style="text-align: center">
                                    <p:commandLink id="btnImprimir" action="#{acessos.gerarRelatorio}"
                                                   target="_blank" ajax="false"
                                                   update="msgs main:tabela"
                                                   title="#{localemsgs.Imprimir}">
                                        <p:graphicImage url="../assets/img/icone_redondo_impressao.png" width="40" height="40" />
                                    </p:commandLink>
                                </p:panel>
                            </p:panelGrid>
                        </f:facet>
                        <p:panel id="editar" style="background-color: transparent;" styleClass="cadastrar2">
                            <p:dataTable value="#{acessos.usuariosGrupo}" var="usuario" sortBy="#{usuario.cliente}"
                                         styleClass="relatorio" scrollWidth="100%" scrollHeight="400" scrollable="true"
                                         expandableRowGroups="true" id="tabelaRelatorio" resizableColumns="true">
                                <p:headerRow>
                                    <p:column colspan="4">
                                        <h:outputText value="#{usuario.cliente} -
                                                      #{usuario.subAgencia eq '' ? usuario.agencia : usuario.agencia.concat('/').concat(usuario.subAgencia)}"/>
                                    </p:column>
                                </p:headerRow>
                                <p:column headerText="#{localemsgs.Usuario}">
                                    <h:outputText value="#{usuario.nomeCompleto}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Email}" style="width: 220px">
                                    <h:outputText value="#{usuario.email}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Situacao}" style="width: 70px">
                                    <h:outputText value="#{usuario.situacao}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ControleUsuarios}"  style="width: 100px">
                                    <h:outputText value="#{usuario.motivo}"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>


            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{acessos.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{acessos.MostrarFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.SomenteAtivos}: " /></label>
                                <p:selectBooleanCheckbox value="#{acessos.flagSomenteAtivos}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{acessos.SomenteAtivos}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important; margin-top:5px !important;">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

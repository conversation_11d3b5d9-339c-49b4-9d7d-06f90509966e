package br.com.sasw.managedBeans.relatorio;

import Arquivo.ArquivoLog;
import br.com.sasw.managedBeans.LoginMB;
import Controller.QueueFech.QueueFechController;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeansCompostas.QueueFechPessoaDTO;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;

/**
 *
 * <AUTHOR>
 */
@Named(value = "centralAlerta")
@ViewScoped
public class CentralAlertaMB implements Serializable {

    private Persistencia persistencia;
    protected ArquivoLog logerro;
    private String log;
    private String caminho;
    private RotasSatWeb rotassatweb;
    private QueueFechController queueController;
    private String operador;
    private String codFil;
    private Filiais filialTela;
    private List<QueueFechPessoaDTO> listaQueueFechPessoa = new ArrayList();
    private QueueFechPessoaDTO queueFechPessoaSelecionado, queueFechPessoaEdicao;
    private LocalDate dataInicio, dataFim;

    public CentralAlertaMB() throws Exception {
        String LOG_BASE_PATH = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\";
        FacesContext fc = FacesContext.getCurrentInstance();
        LoginMB login = fc.getApplication().evaluateExpressionGet(fc, "#{login}", LoginMB.class);
        Map<String, Object> session = fc.getExternalContext().getSessionMap();
        codFil = (String) session.get("filial");
        String banco = (String) session.get("banco");
        operador = (String) session.get("nome");
        BigDecimal codPessoa = (BigDecimal) session.get("codpessoa");

        log = new String();
        String dataTela = DataAtual.getDataAtual("SQL");
        caminho = LOG_BASE_PATH + banco + "\\" + dataTela + "\\" + codPessoa + ".txt";
        logerro = new ArquivoLog();

        LocalDate now = LocalDate.now();
        dataInicio = now.withDayOfMonth(1);
        dataFim = now.withDayOfMonth(now.lengthOfMonth());

        rotassatweb = new RotasSatWeb();
        persistencia = login.getPp();
        if (persistencia == null) {
            FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
        }

        filialTela = rotassatweb.buscaInfoFilial(codFil, persistencia);
        queueController = new QueueFechController(persistencia);
    }

    protected void logaErro(Exception e, String methodName) {
        log = this.getClass().getSimpleName() + "\r\n"
                + methodName + "\r\n"
                + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    private void displayInfo(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayError(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    @PostConstruct
    public void init() {
        try {
            listarCentralAlertaPorDatas();
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void listarCentralAlertaPorDatas() throws Exception {
        try {
            listaQueueFechPessoa = queueController.listarCentralAlertaPorDatas(codFil, dataInicio, dataFim);
        } catch (Exception e) {
            throw e;
        }
    }

    public void selecionarQueue() {
        queueFechPessoaEdicao = new QueueFechPessoaDTO(queueFechPessoaSelecionado);
        PrimeFaces.current().executeScript("PF('dlgEdicao').show();");
    }

    public void mudarMes(int increment) {
        LocalDate month = dataInicio.plusMonths(increment);
        dataInicio = month.withDayOfMonth(1);
        dataFim = month.withDayOfMonth(month.lengthOfMonth());
        init();
    }

    public void selecionarData() {
        init();
        PrimeFaces.current().ajax().update("msgs", "main", "cabecalho");
        PrimeFaces.current().executeScript("PF('oCalendarios').hide()");
    }

    public void actionListenerEditar(ActionEvent actionEvent) {
        if (queueFechPessoaSelecionado == null) {
            displayInfo("SelecioneQueue");
        } else {
            selecionarQueue();
        }
    }

    public void editarQueue() {
        try {
            if (queueFechPessoaEdicao == null || queueFechPessoaEdicao.getQueueFech().getSequencia() == null) {
                throw new Exception("SelecioneQueue");
            }

            queueController.updatePathSrv(queueFechPessoaEdicao);
            displayInfo("EdicaoSucesso");
            init();
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public LocalDate getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(LocalDate dataInicio) {
        this.dataInicio = dataInicio;
    }

    public LocalDate getDataFim() {
        return dataFim;
    }

    public List<QueueFechPessoaDTO> getListaQueueFechPessoa() {
        return listaQueueFechPessoa;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public QueueFechPessoaDTO getQueueFechPessoaSelecionado() {
        return queueFechPessoaSelecionado;
    }

    public void setQueueFechPessoaSelecionado(QueueFechPessoaDTO queueFechPessoaSelecionado) {
        this.queueFechPessoaSelecionado = queueFechPessoaSelecionado;
    }

    public Filiais getFilialTela() {
        return filialTela;
    }

    public QueueFechPessoaDTO getQueueFechPessoaEdicao() {
        return queueFechPessoaEdicao;
    }

    public void setQueueFechPessoaEdicao(QueueFechPessoaDTO queueFechPessoaEdicao) {
        this.queueFechPessoaEdicao = queueFechPessoaEdicao;
    }

    public void setDataFim(LocalDate dataFim) {
        this.dataFim = dataFim;
    }
}

<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.PreFaturaCliente} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />

            <link type="text/css" href="../assets/css/menu.css" rel="stylesheet" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/cofres.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/charts.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -3px !important;
                        position: relative !important;
                    }
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{prefaturaCliFat.Persistencia(login.pp)}" />
                <f:viewParam name="portal" value="#{prefaturaCliFat.portal}" />
            </f:metadata>
            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_Dashboard_cliente.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.PreFaturaCliente}</label>
                                    <label class="TituloDataHora" style="margin-top:4px !important;">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText value="#{prefaturaCliFat.data1}" converter="conversorData" />
                                            <h:outputText value=" - "/>
                                            <h:outputText value="#{prefaturaCliFat.data2}" converter="conversorData"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6">
                                    <label class="FilialNome">#{prefaturaCliFat.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{prefaturaCliFat.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{prefaturaCliFat.filiais.bairro}, #{prefaturaCliFat.filiais.cidade}/#{trajeto.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:commandLink action="#{prefaturaCliFat.dataAnterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                                    </p:commandLink>

                                    <p:commandLink id="calendar" oncomplete="PF('oCalendarios').loadContents();"
                                                   styleClass="botao">
                                        <p:graphicImage url="../assets/img/icone_escaladodia_40.png" style="align-self: center;height: 40px"/>
                                    </p:commandLink>

                                    <p:commandLink action="#{prefaturaCliFat.dataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>

                    <p:overlayPanel id="calendarios" for="cabecalho:calendar" hideEffect="fade" dynamic="true" dismissable="false"
                                    style="font-size: 14px;" widgetVar="oCalendarios" my="top" at="bottom" class="overlay">
                        <h:form id="panelCals">
                            <div class="ui-grid-row ui-grid-responsive">
                                <div class="ui-grid-col-6">
                                    <div class="ui-grid-row" style="margin: 5px">
                                        <h:outputText id="cal1" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}"/>
                                    </div>
                                    <div class="ui-grid-row" style="margin: 5px">
                                        <p:calendar id="calendario1" styleClass="calendario"
                                                    value="#{prefaturaCliFat.dataSelecionada1}" mask="true"
                                                    title="#{localemsgs.DataInicial}" label="#{localemsgs.DataInicial}"
                                                    pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-col-6">
                                    <div class="ui-grid-row" style="margin: 5px">
                                        <h:outputText id="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}"/>
                                    </div>
                                    <div class="ui-grid-row" style="margin: 5px">
                                        <p:calendar id="calendario2" styleClass="calendario"
                                                    value="#{prefaturaCliFat.dataSelecionada2}" mask="true"
                                                    title="#{localemsgs.DataFinal}" label="#{localemsgs.DataFinal}"
                                                    pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                                    </div>
                                </div>
                            </div>
                            <div style="text-align: right; float: right">
                                <p:commandLink action="#{prefaturaCliFat.selecionarData()}" style="float: right"
                                               update="main cabecalho msgs">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" height="40" />
                                </p:commandLink>
                            </div>
                        </h:form>
                    </p:overlayPanel>



                </header>
                <h:form id="main">

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela" var="trajeto"
                                                 value="#{prefaturaCliFat.trajetos}"
                                                 sortBy="#{trajeto.NRed}"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true"
                                                 class="tabela DataGrid"
                                                 scrollHeight="100%"  >
                                        <f:facet name="header">
                                            #{localemsgs.PreFaturaCliente}
                                        </f:facet>
                                        <p:column headerText="#{localemsgs.Cliente}" style="text-align: center">
                                            <h:outputText value="#{trajeto.NRed}" style="text-align: center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Data}" style="text-align: center">
                                            <h:outputText value="#{trajeto.data}" style="text-align: center" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Rota}" style="text-align: center">
                                            <h:outputText value="#{trajeto.rota}" style="text-align: center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Parada}" style="text-align: center">
                                            <h:outputText value="#{trajeto.parada}" style="text-align: center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.TipoSrv}" style="text-align: center">
                                            <h:outputText value="#{trajeto.tipoSrv}" style="text-align: center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ER}" style="text-align: center">
                                            <h:outputText value="#{trajeto.ER}" style="text-align: center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Guias}" style="text-align: center">
                                            <h:outputText value="#{trajeto.guia}" style="text-align: center"/>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Montante}" style="text-align: center">
                                            <h:outputText value="#{trajeto.montante}" converter="conversormoeda" style="text-align: center" />
                                        </p:column>

                                        <p:column headerText="#{localemsgs.ValorEmbarque}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorEmb}" converter="conversormoeda" style="text-align: center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorADV}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorAdv}" converter="conversormoeda" style="text-align: center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorTE}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorTE}" converter="conversormoeda" style="text-align: center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorProcDN}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorProcDN}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorProcMD}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorProcMD}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorCustodia}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorCustodia}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorTotal}" style="text-align: center">
                                            <h:outputText value="#{trajeto.valorEmb + trajeto.valorAdv + trajeto.valorTE + trajeto.valorProcDN + trajeto.valorProcMD + trajeto.valorCustodia}" converter="conversormoeda" />
                                        </p:column>
                                        <p:summaryRow>
                                            <p:column colspan="1" style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{localemsgs.Total}" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="" style="text-align: center;color:#FFF"/>
                                            </p:column>

                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalMontante}" converter="conversormoeda" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalValorEmb}" converter="conversormoeda" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalValorAdv}" converter="conversormoeda" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalValorTE}" converter="conversormoeda" style="text-align: center;color:#FFF"/>
                                            </p:column>

                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalProcDN}" converter="conversormoeda" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalProcMD}" converter="conversormoeda" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalCustodia}" converter="conversormoeda" style="text-align: center;color:#FFF"/>
                                            </p:column>

                                            <p:column style="text-align: center;background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                                <h:outputText value="#{trajeto.totalValorEmb + trajeto.totalValorAdv + trajeto.totalValorTE +trajeto.totalProcDN + trajeto.totalProcMD + trajeto.totalCustodia }" converter="conversormoeda" style="text-align: center;color:#FFF"/>
                                            </p:column>
                                        </p:summaryRow>
                                    </p:dataTable>
                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>


<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <ui:fragment rendered="#{panicoMB.temPermissao}">
        <link type="text/css" href="../assets/css/animate.css" rel="stylesheet" />

        <script type="text/javascript">
            var panicHandle;

            function fetchPanicStatus() {
                window.botaoPanicoAberto = false;
                panicHandle = setInterval(function () {
                    if (window.botaoPanicoAberto) {
                        clearInterval(panicHandle);
                        console.warn('Abrindo janela de pânico!', window.botaoPanicoAberto, panicHandle);
                    } else {
                        console.log('polling por pânico:', window.botaoPanicoAberto, panicHandle);
                        atualizarPanico();
                    }
                }, 15000);
            }

            fetchPanicStatus();
        </script>

        <h:form id="panicoForm">
            <p:remoteCommand
                name="atualizarPanico"
                actionListener="#{panicoMB.atualizar()}"
                update="panicoForm msgs"
                global="false"
                />

            <p:panel id="panel" rendered="#{panicoMB.panicoDetectado ne null and panicoMB.panicoDetectado.comando_Ref eq 'PANIC' }"
                     style="width:100%; height: 100%; padding:0px; margin:0px; position: fixed; z-index: 99999 !important; 
                     background-color:transparent !important">
                <div style="position:absolute; width:100%; height:100%; background-color:rgba(0,0,0,0.6) !important; z-index:99999"></div>
                <div style="position:absolute; width:95%; height:80%; background-color:#FFF; z-index:999999; top:0;right:0;bottom:0;left:0;
                     margin:auto; border-radius:8px; box-shadow:3px 3px 4px #000; border-top:6px solid red; border-bottom:6px solid red">
                    <div class="col-md-4 col-sm-5 col-xs-12" style="height:calc(100% - 0px); overflow: auto; background-color:mistyrose; padding:20px !important;">
                        <label style="background-color:red !important; padding:10px; border-radius:40px; color:mistyrose; width:100%; 
                               text-align:center; font-size:18pt; animation: flash 2s infinite">#{localemsgs.BotaoPanico}</label>

                        <div class="col-ms-12" style="border-bottom: thin solid red; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">
                                <h:outputText value="#{localemsgs.Rota}" rendered="#{panicoMB.panicoDetectado.rota ne null and panicoMB.panicoDetectado.rota ne ''}"/>
                            </label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:red; text-shadow:1px 1px #FFF">
                                <h:outputText value="#{localemsgs.Rota}" rendered="#{panicoMB.panicoDetectado.rota ne null and panicoMB.panicoDetectado.rota ne ''}"/>
                                <h:outputText value="#{panicoMB.panicoDetectado.rota}" rendered="#{panicoMB.panicoDetectado.rota ne null and panicoMB.panicoDetectado.rota ne ''}"/>
                            </label>
                        </div>
                        <div class="col-ms-12" style="border-bottom: thin solid red; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">    
                                <h:outputText value="#{localemsgs.NumeroVeiculo}" rendered="#{panicoMB.panicoDetectado.veiculo ne null and panicoMB.panicoDetectado.veiculo ne ''}"/>
                                
                                <h:outputText value="#{localemsgs.Local}" rendered="#{panicoMB.panicoDetectado.local ne null and panicoMB.panicoDetectado.local ne ''}"/>
                            </label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:red; text-shadow:1px 1px #FFF">
                                <h:outputText value="#{panicoMB.panicoDetectado.veiculo}" rendered="#{panicoMB.panicoDetectado.veiculo ne null and panicoMB.panicoDetectado.veiculo ne ''}"/>

                                <h:outputText value="#{panicoMB.panicoDetectado.local}" rendered="#{panicoMB.panicoDetectado.local ne null and panicoMB.panicoDetectado.local ne ''}"/>
                            </label>
                        </div>
                        <div class="col-ms-12" style="border-bottom: thin solid red; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">
                                <h:outputText value="#{localemsgs.Operador}" rendered="#{panicoMB.panicoDetectado.local ne null and panicoMB.panicoDetectado.local ne ''}"/>
                                <h:outputText value="#{localemsgs.ChefeEquipe}" rendered="#{panicoMB.panicoDetectado.local eq null or panicoMB.panicoDetectado.local eq ''}"/>

                            </label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:red; text-shadow:1px 1px #FFF">#{panicoMB.panicoDetectado.nome_Guer}</label>
                        </div>
                        <div class="col-ms-12" style="border-bottom: thin solid red; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">#{localemsgs.Latitude}</label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:red; text-shadow:1px 1px #FFF">#{panicoMB.panicoDetectado.latitude}</label>
                        </div>
                        <div class="col-ms-12" style="border-bottom: thin solid red; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">#{localemsgs.Longitude}</label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:red; text-shadow:1px 1px #FFF">#{panicoMB.panicoDetectado.longitude}</label>
                        </div>
                        <!--<div class="col-ms-12" style="border-bottom: thin solid red; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">Endereço Aproximado</label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:red; text-shadow:1px 1px #FFF">Eixo Monumental, Brasília, Distrito Federal</label>
                        </div>-->
                        <div class="col-ms-12" style="border-bottom: thin solid red; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">#{localemsgs.DataHora}</label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:red; text-shadow:1px 1px #FFF"><h:outputText value="#{panicoMB.panicoDetectado.dataHoraComando}" converter="conversorData" /><h:outputText value=" #{panicoMB.panicoDetectado.horaComando}" converter="conversorHora" /></label>
                        </div>
                        <div class="col-ms-12" style="padding:6px 0px 6px 0px !important; margin-top:30px !important">
                            <p:commandButton
                                action="#{panicoMB.update()}"
                                value="#{localemsgs.ConfirmarRecebimento}"
                                update="msgs panicoForm"
                                oncomplete="fetchPanicStatus();"
                                class="btn btn-success btn-large"
                                style="width:100%;height:60px;font-size:16pt;padding-top:6px !important;box-shadow:2px 2px 3px #BBB"
                                />
                        </div>

                    </div>
                    <div class="col-md-8 col-sm-7 col-xs-12" style="height:100%; padding:20px !important;">
                        <div id="mapGooglePanico" class="col-md-12 col-sm-12 col-xs-12" style="height:100%; border:thin solid #CCC; box-shadow:3px 3px 4px #DDD; padding:0px !important;"></div>
                    </div>
                    <script type="text/javascript">
                        var markers;
                        var map;
                        window.botaoPanicoAberto = true;

                        function initMap() {
                            map = new google.maps.Map(document.getElementById('mapGooglePanico'), {
                                zoom: 12,
                                center: {lat: parseFloat(#{panicoMB.panicoDetectado.latitude}), lng: parseFloat(#{panicoMB.panicoDetectado.longitude})},
                                gestureHandling: 'cooperative'
                            });

                            setTimeout(function () {
                                markers = new google.maps.Marker({
                                    position: {lat: parseFloat(#{panicoMB.panicoDetectado.latitude}), lng: parseFloat(#{panicoMB.panicoDetectado.longitude})},
                                    icon: 'https://mobile.sasw.com.br:9091/satmobile/pins/pin_azul_caminhao.png',
                                    map: map
                                });
                            }, 1000);
                        }
                    </script>
                    <script src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}&amp;callback=initMap"></script>
                </div>
            </p:panel>

            <p:panel id="panelMensagem" rendered="#{panicoMB.panicoDetectado ne null and panicoMB.panicoDetectado.comando_Ref eq 'MENSAGEM' }"
                     style="width:100%; height: 100%; padding:0px; margin:0px; position: fixed; z-index: 99999 !important; 
                     background-color:transparent !important">
                <div style="position:absolute; width:100%; height:100%; background-color:rgba(0,0,0,0.6) !important; z-index:99999"></div>
                <div style="position:absolute; width:95%; height:80%; background-color:#FFF; z-index:999999; top:0;right:0;bottom:0;left:0;
                     margin:auto; border-radius:8px; box-shadow:3px 3px 4px #000; border-top:6px solid darkgreen; border-bottom:6px solid darkgreen">
                    <div class="col-md-4 col-sm-5 col-xs-12" style="height:calc(100% - 0px); overflow: auto; background-color:gainsboro; padding:20px !important;">
                        <label style="background-color:darkgreen !important; padding:10px; border-radius:40px; color:gainsboro; width:100%; 
                               text-align:center; font-size:18pt; animation: flash 2s infinite">#{localemsgs.MensagemRecebida}</label>

                        <div class="col-ms-12" style="border-bottom: thin solid darkgreen; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">
                                <h:outputText value="#{localemsgs.Mensagem}"/>
                            </label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:darkgreen; text-shadow:1px 1px #FFF">
                                <h:outputText value="#{panicoMB.panicoDetectado.mensagem}"/>
                            </label>
                        </div>
                        <div class="col-ms-12" style="border-bottom: thin solid darkgreen; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">    
                                <h:outputText value="#{localemsgs.Local}" rendered="#{panicoMB.panicoDetectado.local ne null and panicoMB.panicoDetectado.local ne ''}"/>
                            </label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:darkgreen; text-shadow:1px 1px #FFF">
                                 <h:outputText value="#{panicoMB.panicoDetectado.local}" rendered="#{panicoMB.panicoDetectado.local ne null and panicoMB.panicoDetectado.local ne ''}"/>
                            </label>
                        </div>
                        <div class="col-ms-12" style="border-bottom: thin solid darkgreen; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">
                                <h:outputText value="#{localemsgs.Operador}" rendered="#{panicoMB.panicoDetectado.local ne null and panicoMB.panicoDetectado.local ne ''}"/>
                                <h:outputText value="#{localemsgs.ChefeEquipe}" rendered="#{panicoMB.panicoDetectado.local eq null or panicoMB.panicoDetectado.local eq ''}"/>

                            </label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:darkgreen; text-shadow:1px 1px #FFF">#{panicoMB.panicoDetectado.nome_Guer}</label>
                        </div>
                        <div class="col-ms-12" style="border-bottom: thin solid darkgreen; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">#{localemsgs.Latitude}</label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:darkgreen; text-shadow:1px 1px #FFF">#{panicoMB.panicoDetectado.latitude}</label>
                        </div>
                        <div class="col-ms-12" style="border-bottom: thin solid darkgreen; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">#{localemsgs.Longitude}</label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:darkgreen; text-shadow:1px 1px #FFF">#{panicoMB.panicoDetectado.longitude}</label>
                        </div>
                        <!--<div class="col-ms-12" style="border-bottom: thin solid darkgreen; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">Endereço Aproximado</label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:darkgreen; text-shadow:1px 1px #FFF">Eixo Monumental, Brasília, Distrito Federal</label>
                        </div>-->
                        <div class="col-ms-12" style="border-bottom: thin solid darkgreen; padding:6px 0px 6px 0px !important">
                            <label style="font-size:11pt; color:#000; width:100%;margin:0px; text-transform:uppercase; height:10px !important; font-weight:500">#{localemsgs.DataHora}</label>
                            <label style="font-size:18pt; width:100%; margin:0px; color:darkgreen; text-shadow:1px 1px #FFF"><h:outputText value="#{panicoMB.panicoDetectado.dataHoraComando}" converter="conversorData" /><h:outputText value=" #{panicoMB.panicoDetectado.horaComando}" converter="conversorHora" /></label>
                        </div>
                        <div class="col-ms-12" style="padding:6px 0px 6px 0px !important; margin-top:30px !important">
                            <p:commandButton
                                action="#{panicoMB.update()}"
                                value="#{localemsgs.ConfirmarRecebimento}"
                                update="msgs panicoForm"
                                oncomplete="fetchPanicStatus();"
                                class="btn btn-success btn-large"
                                style="width:100%;height:60px;font-size:16pt;padding-top:6px !important;box-shadow:2px 2px 3px #BBB"
                                />
                        </div>

                    </div>
                    <div class="col-md-8 col-sm-7 col-xs-12" style="height:100%; padding:20px !important;">
                        <div id="mapGooglePanico" class="col-md-12 col-sm-12 col-xs-12" style="height:100%; border:thin solid #CCC; box-shadow:3px 3px 4px #DDD; padding:0px !important;"></div>
                    </div>
                    <script type="text/javascript">
                        var markers;
                        var map;
                        window.botaoPanicoAberto = true;

                        function initMap() {
                            map = new google.maps.Map(document.getElementById('mapGooglePanico'), {
                                zoom: 12,
                                center: {lat: parseFloat(#{panicoMB.panicoDetectado.latitude}), lng: parseFloat(#{panicoMB.panicoDetectado.longitude})},
                                gestureHandling: 'cooperative'
                            });

                            setTimeout(function () {
                                markers = new google.maps.Marker({
                                    position: {lat: parseFloat(#{panicoMB.panicoDetectado.latitude}), lng: parseFloat(#{panicoMB.panicoDetectado.longitude})},
                                    icon: 'https://mobile.sasw.com.br:9091/satmobile/pins/icone_mobile_supervisor.png',
                                    map: map
                                });
                            }, 1000);
                        }
                    </script>
                    <script src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}&amp;callback=initMap"></script>
                </div>
            </p:panel>
        </h:form>
    </ui:fragment>

</ui:composition>
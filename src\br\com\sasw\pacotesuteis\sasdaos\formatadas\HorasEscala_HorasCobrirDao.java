/*
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.SatWebService.HorasEscala_HorasCobrir;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class HorasEscala_HorasCobrirDao {

    public List<HorasEscala_HorasCobrir> horasEscalaHorasCobrirGeralPorCliente(String dataInicio, String dataFim, Persistencia persistencia)
            throws Exception {
        List<HorasEscala_HorasCobrir> retorno = new ArrayList<>();
        try {
            String sql = "Select  Clientes.NRed, Max(OS_VItens.Qtde)*Max(CtrItens.QtdeFunc) QtdeFunc, Sum(QtdeEscala*HsTrab) QtdeEscala, "
                    + " Round(Sum(QtdeEscala*HsTrab)/(Max(OS_VItens.Qtde)*Max(CtrItens.QtdeFunc)), 2) CHDia, Sum(QtdeCobrir*HsTrab) QtdeCobrir, "
                    + " Sum(QtdePresenca*HsTrab) QtdePresenca from OperacoesServ "
                    + " left join PstServ on  PstServ.Secao  = OperacoesServ.Secao "
                    + "                   and PstServ.CodFil = OperacoesServ.CodFil "
                    + " left join OS_Vig on OS_Vig.OS      = PstServ.OS "
                    + "                  and OS_Vig.CodFil = PstServ.Codfil "
                    + "                  and OS_Vig.DtFim >= ? "
                    + "                  and OS_Vig.DtINicio < ? "
                    + " left join OS_VItens on  OS_Vitens.OS = PstServ.OS "
                    + "                     and OS_Vitens.CodFil = PstServ.CodFIl "
                    + "                     and OS_VItens.TipoPosto = PstServ.TipoPosto "
                    + "                     and OS_VItens.DtFim >= ? "
                    + "                     and OS_VItens.DtINicio < ? "
                    + " left join CtrItens on CtrItens.COntrato = OS_Vig.Contrato "
                    + "                    and CtrItens.CodFil  = OS_Vig.CodFil  "
                    + "                    and CtrItens.TipoPosto = PStServ.TIpoPosto "
                    + " left join clientes on Clientes.Codigo  = OS_Vig.CliFat "
                    + "                    and Clientes.CodFil = OS_Vig.CodFIl "
                    + " where data >= ? "
                    + "   and data <= ? "
                    + "   and QtdeCobrir >= 0 "
                    + "   and OperacoesServ.Secao in (Select Secao from PstMonitorados) "
                    + " group by Clientes.Nred "
                    + " order by QtdeCobrir Desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataInicio);
            consulta.setString(dataInicio);
            consulta.setString(dataInicio);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            HorasEscala_HorasCobrir resposta;
            while (consulta.Proximo()) {
                resposta = new HorasEscala_HorasCobrir();
                resposta.setnRed(consulta.getString("nRed"));
                resposta.setQtdeFunc(consulta.getBigDecimal("qtdeFunc"));
                resposta.setQtdeEscala(consulta.getBigDecimal("qtdeEscala"));
                resposta.setcHDia(consulta.getBigDecimal("cHDia"));
                resposta.setQtdeCobrir(consulta.getBigDecimal("qtdeCobrir"));
                resposta.setQtdePresenca(consulta.getBigDecimal("qtdePresenca"));
                retorno.add(resposta);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao gerar relatório - " + e.getMessage());
        }
    }
}

<wsdl:definitions name="ConsultasReinf" targetNamespace="http://sped.fazenda.gov.br/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://sped.fazenda.gov.br/" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<wsp:Policy Id="WsReceberLoteEventos_policy">
		<wsp:ExactlyOne>
			<wsp:All>
				<sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<wsp:Policy>
						<sp:TransportToken>
							<wsp:Policy>
								<sp:HttpsToken RequireClientCertificate="true"/>
							</wsp:Policy>
						</sp:TransportToken>
						<sp:AlgorithmSuite>
							<wsp:Policy>
								<sp:Basic256/>
							</wsp:Policy>
						</sp:AlgorithmSuite>
						<sp:Layout>
							<wsp:Policy>
								<sp:Strict/>
							</wsp:Policy>
						</sp:Layout>
					</wsp:Policy>
				</sp:TransportBinding>
			</wsp:All>
		</wsp:ExactlyOne>
	</wsp:Policy>
	<wsdl:types>
		<xs:schema elementFormDefault="qualified" targetNamespace="http://sped.fazenda.gov.br/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
			<xs:element name="ConsultaInformacoesConsolidadas">
				<xs:complexType>
					<xs:sequence>
						<xs:element maxOccurs="1" minOccurs="1" name="tipoInscricaoContribuinte" type="xs:unsignedByte"/>
						<xs:element maxOccurs="1" minOccurs="0" name="numeroInscricaoContribuinte" type="xs:string"/>
						<xs:element maxOccurs="1" minOccurs="0" name="numeroProtocoloFechamento" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ConsultaInformacoesConsolidadasResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element maxOccurs="1" minOccurs="0" name="ConsultaInformacoesConsolidadasResult">
							<xs:complexType>
								<xs:sequence>
									<xs:any/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:schema>
	</wsdl:types>
	<wsdl:message name="ConsultasReinf_ConsultaInformacoesConsolidadas_InputMessage">
		<wsdl:part element="tns:ConsultaInformacoesConsolidadas" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="ConsultasReinf_ConsultaInformacoesConsolidadas_OutputMessage">
		<wsdl:part element="tns:ConsultaInformacoesConsolidadasResponse" name="parameters"/>
	</wsdl:message>
	<wsdl:portType name="ConsultasReinf">
		<wsdl:operation name="ConsultaInformacoesConsolidadas">
			<wsdl:input message="tns:ConsultasReinf_ConsultaInformacoesConsolidadas_InputMessage" wsaw:Action="http://sped.fazenda.gov.br/ConsultasReinf/ConsultaInformacoesConsolidadas"/>
			<wsdl:output message="tns:ConsultasReinf_ConsultaInformacoesConsolidadas_OutputMessage" wsaw:Action="http://sped.fazenda.gov.br/ConsultasReinf/ConsultaInformacoesConsolidadasResponse"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="BasicHttpBinding_ConsultasReinf" type="tns:ConsultasReinf">
		<soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsp:PolicyReference URI="#WsReceberLoteEventos_policy"/>
		<wsdl:operation name="ConsultaInformacoesConsolidadas">
			<soap:operation soapAction="http://sped.fazenda.gov.br/ConsultasReinf/ConsultaInformacoesConsolidadas" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="ConsultasReinf">
		<wsdl:port binding="tns:BasicHttpBinding_ConsultasReinf" name="BasicHttpBinding_ConsultasReinf">
			<soap:address location="https://reinf.receita.fazenda.gov.br/WsREINF/ConsultasReinf.svc"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
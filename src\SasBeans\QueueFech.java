/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import SasBeansCompostas.QueueFechDTO;

/**
 *
 * <AUTHOR>
 */
public class QueueFech {

    private String Sequencia;
    private String CodFil;
    private String TipoOperacao;
    private String CodFech;
    private String CodPessoa;
    private String FechIdentif;
    private String Bottom;
    private String PathSrv;
    private String ChaveSrv;
    private String ContraSenha;
    private String Senha;
    private String Operador;
    private String Data;
    private String Hora;
    private String Pk_Fechadura;
    private String DataHoraUltAtual;
    private String DataHoraComando;
    private String SeqRota;
    private String Parada;
    private String HoraComando;
    private String Comando_Org;
    private String Comando_Ref;
    private String Comando_Crt;
    private String Senha_Fecha;
    private String Comando_Aceito;
    private String SrvOK;
    private String Latitude;
    private String Longitude;
    private String Precisao;
    private String Matr;
    private String ModTecban;

    public QueueFech() {
    }

    public QueueFech(QueueFech original) {
        Sequencia = original.getSequencia();
        CodFil = original.getCodFil();
        TipoOperacao = original.getTipoOperacao();
        CodFech = original.getCodFech();
        CodPessoa = original.getCodPessoa();
        FechIdentif = original.getFechIdentif();
        Bottom = original.getBottom();
        PathSrv = original.getPathSrv();
        ChaveSrv = original.getChaveSrv();
        ContraSenha = original.getContraSenha();
        Senha = original.getSenha();
        Operador = original.getOperador();
        Data = original.getData();
        Hora = original.getHora();
        Pk_Fechadura = original.getPk_Fechadura();
        DataHoraUltAtual = original.getDataHoraUltAtual();
        DataHoraComando = original.getDataHoraComando();
        SeqRota = original.getSeqRota();
        Parada = original.getParada();
        HoraComando = original.getHoraComando();
        Comando_Org = original.getComando_Org();
        Comando_Ref = original.getComando_Ref();
        Comando_Crt = original.getComando_Crt();
        Senha_Fecha = original.getSenha_Fecha();
        Comando_Aceito = original.getComando_Aceito();
        SrvOK = original.getSrvOK();
        Latitude = original.getLatitude();
        Longitude = original.getLongitude();
        Precisao = original.getPrecisao();
        Matr = original.getMatr();
        ModTecban = original.getModTecban();
    }

    public QueueFech(QueueFechDTO dto) {
        Sequencia = dto.getSequencia().toString();
        DataHoraComando = dto.getDataHoraComando().toString();
        HoraComando = dto.getHoraComando();
        Latitude = dto.getLatitude();
        Longitude = dto.getLongitude();
    }

    public QueueFech(QueueFech original, QueueFechDTO dto) {
        CodFil = original.getCodFil();
        TipoOperacao = original.getTipoOperacao();
        CodFech = original.getCodFech();
        CodPessoa = original.getCodPessoa();
        FechIdentif = original.getFechIdentif();
        Bottom = original.getBottom();
        PathSrv = original.getPathSrv();
        ChaveSrv = original.getChaveSrv();
        ContraSenha = original.getContraSenha();
        Senha = original.getSenha();
        Operador = original.getOperador();
        Data = original.getData();
        Hora = original.getHora();
        Pk_Fechadura = original.getPk_Fechadura();
        DataHoraUltAtual = original.getDataHoraUltAtual();
        SeqRota = original.getSeqRota();
        Parada = original.getParada();
        Comando_Org = original.getComando_Org();
        Comando_Ref = original.getComando_Ref();
        Comando_Crt = original.getComando_Crt();
        Senha_Fecha = original.getSenha_Fecha();
        Comando_Aceito = original.getComando_Aceito();
        SrvOK = original.getSrvOK();
        Precisao = original.getPrecisao();
        Matr = original.getMatr();
        ModTecban = original.getModTecban();

        Sequencia = dto.getSequencia().toString();
        DataHoraComando = dto.getDataHoraComando().toString();
        HoraComando = dto.getHoraComando();
        Latitude = dto.getLatitude();
        Longitude = dto.getLongitude();
    }

    /**
     * @return the Sequencia
     */
    public String getSequencia() {
        return Sequencia;
    }

    /**
     * @param Sequencia the Sequencia to set
     */
    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    /**
     * @return the CodFil
     */
    public String getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    /**
     * @return the TipoOperacao
     */
    public String getTipoOperacao() {
        return TipoOperacao;
    }

    /**
     * @param TipoOperacao the TipoOperacao to set
     */
    public void setTipoOperacao(String TipoOperacao) {
        this.TipoOperacao = TipoOperacao;
    }

    /**
     * @return the CodFech
     */
    public String getCodFech() {
        return CodFech;
    }

    /**
     * @param CodFech the CodFech to set
     */
    public void setCodFech(String CodFech) {
        this.CodFech = CodFech;
    }

    /**
     * @return the CodPessoa
     */
    public String getCodPessoa() {
        return CodPessoa;
    }

    /**
     * @param CodPessoa the CodPessoa to set
     */
    public void setCodPessoa(String CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    /**
     * @return the FechIdentif
     */
    public String getFechIdentif() {
        return FechIdentif;
    }

    /**
     * @param FechIdentif the FechIdentif to set
     */
    public void setFechIdentif(String FechIdentif) {
        this.FechIdentif = FechIdentif;
    }

    /**
     * @return the Bottom
     */
    public String getBottom() {
        return Bottom;
    }

    /**
     * @param Bottom the Bottom to set
     */
    public void setBottom(String Bottom) {
        this.Bottom = Bottom;
    }

    /**
     * @return the PathSrv
     */
    public String getPathSrv() {
        return PathSrv;
    }

    /**
     * @param PathSrv the PathSrv to set
     */
    public void setPathSrv(String PathSrv) {
        this.PathSrv = PathSrv;
    }

    /**
     * @return the ChaveSrv
     */
    public String getChaveSrv() {
        return ChaveSrv;
    }

    /**
     * @param ChaveSrv the ChaveSrv to set
     */
    public void setChaveSrv(String ChaveSrv) {
        this.ChaveSrv = ChaveSrv;
    }

    /**
     * @return the ContraSenha
     */
    public String getContraSenha() {
        return ContraSenha;
    }

    /**
     * @param ContraSenha the ContraSenha to set
     */
    public void setContraSenha(String ContraSenha) {
        this.ContraSenha = ContraSenha;
    }

    /**
     * @return the Senha
     */
    public String getSenha() {
        return Senha;
    }

    /**
     * @param Senha the Senha to set
     */
    public void setSenha(String Senha) {
        this.Senha = Senha;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Data
     */
    public String getData() {
        return Data;
    }

    /**
     * @param Data the Data to set
     */
    public void setData(String Data) {
        this.Data = Data;
    }

    /**
     * @return the Hora
     */
    public String getHora() {
        return Hora;
    }

    /**
     * @param Hora the Hora to set
     */
    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    /**
     * @return the Pk_Fechadura
     */
    public String getPk_Fechadura() {
        return Pk_Fechadura;
    }

    /**
     * @param Pk_Fechadura the Pk_Fechadura to set
     */
    public void setPk_Fechadura(String Pk_Fechadura) {
        this.Pk_Fechadura = Pk_Fechadura;
    }

    /**
     * @return the DataHoraUltAtual
     */
    public String getDataHoraUltAtual() {
        return DataHoraUltAtual;
    }

    /**
     * @param DataHoraUltAtual the DataHoraUltAtual to set
     */
    public void setDataHoraUltAtual(String DataHoraUltAtual) {
        this.DataHoraUltAtual = DataHoraUltAtual;
    }

    /**
     * @return the DataHoraComando
     */
    public String getDataHoraComando() {
        return DataHoraComando;
    }

    /**
     * @param DataHoraComando the DataHoraComando to set
     */
    public void setDataHoraComando(String DataHoraComando) {
        this.DataHoraComando = DataHoraComando;
    }

    /**
     * @return the SeqRota
     */
    public String getSeqRota() {
        return SeqRota;
    }

    /**
     * @param SeqRota the SeqRota to set
     */
    public void setSeqRota(String SeqRota) {
        this.SeqRota = SeqRota;
    }

    /**
     * @return the Parada
     */
    public String getParada() {
        return Parada;
    }

    /**
     * @param Parada the Parada to set
     */
    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    /**
     * @return the HoraComando
     */
    public String getHoraComando() {
        return HoraComando;
    }

    /**
     * @param HoraComando the HoraComando to set
     */
    public void setHoraComando(String HoraComando) {
        this.HoraComando = HoraComando;
    }

    /**
     * @return the Comando_Org
     */
    public String getComando_Org() {
        return Comando_Org;
    }

    /**
     * @param Comando_Org the Comando_Org to set
     */
    public void setComando_Org(String Comando_Org) {
        this.Comando_Org = Comando_Org;
    }

    /**
     * @return the Comando_Ref
     */
    public String getComando_Ref() {
        return Comando_Ref;
    }

    /**
     * @param Comando_Ref the Comando_Ref to set
     */
    public void setComando_Ref(String Comando_Ref) {
        this.Comando_Ref = Comando_Ref;
    }

    /**
     * @return the Comando_Crt
     */
    public String getComando_Crt() {
        return Comando_Crt;
    }

    /**
     * @param Comando_Crt the Comando_Crt to set
     */
    public void setComando_Crt(String Comando_Crt) {
        this.Comando_Crt = Comando_Crt;
    }

    /**
     * @return the Senha_Fecha
     */
    public String getSenha_Fecha() {
        return Senha_Fecha;
    }

    /**
     * @param Senha_Fecha the Senha_Fecha to set
     */
    public void setSenha_Fecha(String Senha_Fecha) {
        this.Senha_Fecha = Senha_Fecha;
    }

    /**
     * @return the Comando_Aceito
     */
    public String getComando_Aceito() {
        return Comando_Aceito;
    }

    /**
     * @param Comando_Aceito the Comando_Aceito to set
     */
    public void setComando_Aceito(String Comando_Aceito) {
        this.Comando_Aceito = Comando_Aceito;
    }

    /**
     * @return the SrvOK
     */
    public String getSrvOK() {
        return SrvOK;
    }

    /**
     * @param SrvOK the SrvOK to set
     */
    public void setSrvOK(String SrvOK) {
        this.SrvOK = SrvOK;
    }

    /**
     * @return the Latitude
     */
    public String getLatitude() {
        return Latitude;
    }

    /**
     * @param Latitude the Latitude to set
     */
    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    /**
     * @return the Longitude
     */
    public String getLongitude() {
        return Longitude;
    }

    /**
     * @param Longitude the Longitude to set
     */
    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    /**
     * @return the Precisao
     */
    public String getPrecisao() {
        return Precisao;
    }

    /**
     * @param Precisao the Precisao to set
     */
    public void setPrecisao(String Precisao) {
        this.Precisao = Precisao;
    }

    /**
     * @return the Matr
     */
    public String getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        this.Matr = Matr;
    }
    

    /**
     * @return the ModTecban
     */
    public String getModTecban() {
        return ModTecban;
    }

    /**
     * @param ModTecban the ModTecban to set
     */
    public void setModTecban(String ModTecban) {
        this.ModTecban = ModTecban;
    }
            

    
}

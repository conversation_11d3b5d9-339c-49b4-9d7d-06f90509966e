<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"   
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">
    <h:body>
    	<ui:composition>
            <p:ajaxStatus onstart="PF('pfBlock').show();" oncomplete="PF('pfBlock').hide();">
                <p:dialog  widgetVar="pfBlock" header="#{localemsgs.ProcessandoAguarde}" modal="true"
                           draggable="false" closable="false" resizable="false" width="300">  
                    <p:progressBar widgetVar="progressoIndeterminado" id="progressoIndeterminado"
                                   styleClass="progresso" mode="indeterminate"/>
                </p:dialog>
            </p:ajaxStatus>	
        </ui:composition>
    </h:body>
</html>
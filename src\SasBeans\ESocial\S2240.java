/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class S2240 {

    private int sucesso; //
    private String evtExpRisco_Id;

    private String ideEvento_indRetif;//
    private String ideEvento_nrRecibo;//

    private String ideEvento_tpAmb;//

    private String ideEvento_procEmi;//
    private String ideEvento_verProc;//

    private String ideEmpregador_tpInsc; //
    private String ideEmpregador_nrInsc; //

    private String ideVinculo_cpfTrab;//
    private String ideVinculo_matricula;//
    private String ideVinculo_matr;//
    private String ideVinculo_codCateg;//
    
    private String infoExpRisco_dtIniCondicao;
    private int infoExpRisco_localAmb;
    private String infoExpRisco_dscSetor;
    private String infoExpRisco_nrInsc;
    
    private String infoExpRisco_dscAtivDes;
    private String infoExpRisco_codAgNoc;
    private String infoExpRisco_dscAgNoc;
    private int infoExpRisco_tpAval;
    private int infoExpRisco_intConc;
    private int infoExpRisco_limTol;
    private int infoExpRisco_unMed;
    private String infoExpRisco_tecMedicao;
    private int infoExpRisco_utilizEPC;
    private String infoExpRisco_eficEpc;
    private int infoExpRisco_utilizEPI;
    private String infoExpRisco_eficEpi;
    private String infoExpRisco_docAval;
    private String infoExpRisco_dscEPI;
    private String infoExpRisco_medProtecao;
    private String infoExpRisco_condFuncto;
    private String infoExpRisco_usoInint;
    private String infoExpRisco_przValid;
    private String infoExpRisco_periodicTroca;
    private String infoExpRisco_higienizacao;
    private String infoExpRisco_cpfResp;
    private String infoExpRisco_desOC;
    private int infoExpRisco_ideOC;
    private String infoExpRisco_nrOC;
    private String infoExpRisco_ufOC;
    private String infoExpRisco_obsCompl;

    
    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    
    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeVinculo_cpfTrab() {
        return ideVinculo_cpfTrab;
    }

    public void setIdeVinculo_cpfTrab(String ideVinculo_cpfTrab) {
        this.ideVinculo_cpfTrab = ideVinculo_cpfTrab;
    }

    public String getIdeVinculo_matricula() {
        return ideVinculo_matricula;
    }

    public void setIdeVinculo_matricula(String ideVinculo_matricula) {
        this.ideVinculo_matricula = ideVinculo_matricula;
    }

    public String getIdeVinculo_codCateg() {
        return ideVinculo_codCateg;
    }

    public void setIdeVinculo_codCateg(String ideVinculo_codCateg) {
        this.ideVinculo_codCateg = ideVinculo_codCateg;
    }

    
    public String getIdeVinculo_matr() {
        return ideVinculo_matr;
    }
    
    

    public void setIdeVinculo_matr(String ideVinculo_matr) {
        this.ideVinculo_matr = ideVinculo_matr;
    }
    
    /**
     * @return the evtExpRisco_Id
     */
    public String getEvtExpRisco_Id() {
        return evtExpRisco_Id;
    }

    /**
     * @param evtExpRisco_Id the evtExpRisco_Id to set
     */
    public void setEvtExpRisco_Id(String evtExpRisco_Id) {
        this.evtExpRisco_Id = evtExpRisco_Id;
    }

    /**
     * @return the infoExpRisco_dtIniCondicao
     */
    public String getInfoExpRisco_dtIniCondicao() {
        return infoExpRisco_dtIniCondicao;
    }

    /**
     * @param infoExpRisco_dtIniCondicao the infoExpRisco_dtIniCondicao to set
     */
    public void setInfoExpRisco_dtIniCondicao(String infoExpRisco_dtIniCondicao) {
        this.infoExpRisco_dtIniCondicao = infoExpRisco_dtIniCondicao;
    }

    /**
     * @return the infoExpRisco_localAmb
     */
    public int getInfoExpRisco_localAmb() {
        return infoExpRisco_localAmb;
    }

    /**
     * @param infoExpRisco_localAmb the infoExpRisco_localAmb to set
     */
    public void setInfoExpRisco_localAmb(int infoExpRisco_localAmb) {
        this.infoExpRisco_localAmb = infoExpRisco_localAmb;
    }

    /**
     * @return the infoExpRisco_dscSetor
     */
    public String getInfoExpRisco_dscSetor() {
        return infoExpRisco_dscSetor;
    }

    /**
     * @param infoExpRisco_dscSetor the infoExpRisco_dscSetor to set
     */
    public void setInfoExpRisco_dscSetor(String infoExpRisco_dscSetor) {
        this.infoExpRisco_dscSetor = infoExpRisco_dscSetor;
    }

    /**
     * @return the infoExpRisco_dscAtivDes
     */
    public String getInfoExpRisco_dscAtivDes() {
        return infoExpRisco_dscAtivDes;
    }

    /**
     * @param infoExpRisco_dscAtivDes the infoExpRisco_dscAtivDes to set
     */
    public void setInfoExpRisco_dscAtivDes(String infoExpRisco_dscAtivDes) {
        this.infoExpRisco_dscAtivDes = infoExpRisco_dscAtivDes;
    }

    /**
     * @return the infoExpRisco_codAgNoc
     */
    public String getInfoExpRisco_codAgNoc() {
        return infoExpRisco_codAgNoc;
    }

    /**
     * @param infoExpRisco_codAgNoc the infoExpRisco_codAgNoc to set
     */
    public void setInfoExpRisco_codAgNoc(String infoExpRisco_codAgNoc) {
        this.infoExpRisco_codAgNoc = infoExpRisco_codAgNoc;
    }

    /**
     * @return the infoExpRisco_dscAgNoc
     */
    public String getInfoExpRisco_dscAgNoc() {
        return infoExpRisco_dscAgNoc;
    }

    /**
     * @param infoExpRisco_dscAgNoc the infoExpRisco_dscAgNoc to set
     */
    public void setInfoExpRisco_dscAgNoc(String infoExpRisco_dscAgNoc) {
        this.infoExpRisco_dscAgNoc = infoExpRisco_dscAgNoc;
    }

    /**
     * @return the infoExpRisco_tpAval
     */
    public int getInfoExpRisco_tpAval() {
        return infoExpRisco_tpAval;
    }

    /**
     * @param infoExpRisco_tpAval the infoExpRisco_tpAval to set
     */
    public void setInfoExpRisco_tpAval(int infoExpRisco_tpAval) {
        this.infoExpRisco_tpAval = infoExpRisco_tpAval;
    }

    /**
     * @return the infoExpRisco_intConc
     */
    public int getInfoExpRisco_intConc() {
        return infoExpRisco_intConc;
    }

    /**
     * @param infoExpRisco_intConc the infoExpRisco_intConc to set
     */
    public void setInfoExpRisco_intConc(int infoExpRisco_intConc) {
        this.infoExpRisco_intConc = infoExpRisco_intConc;
    }

    /**
     * @return the infoExpRisco_unMed
     */
    public int getInfoExpRisco_unMed() {
        return infoExpRisco_unMed;
    }

    /**
     * @param infoExpRisco_unMed the infoExpRisco_unMed to set
     */
    public void setInfoExpRisco_unMed(int infoExpRisco_unMed) {
        this.infoExpRisco_unMed = infoExpRisco_unMed;
    }

    /**
     * @return the infoExpRisco_tecMedicao
     */
    public String getInfoExpRisco_tecMedicao() {
        return infoExpRisco_tecMedicao;
    }

    /**
     * @param infoExpRisco_tecMedicao the infoExpRisco_tecMedicao to set
     */
    public void setInfoExpRisco_tecMedicao(String infoExpRisco_tecMedicao) {
        this.infoExpRisco_tecMedicao = infoExpRisco_tecMedicao;
    }

    /**
     * @return the infoExpRisco_utilizEPC
     */
    public int getInfoExpRisco_utilizEPC() {
        return infoExpRisco_utilizEPC;
    }

    /**
     * @param infoExpRisco_utilizEPC the infoExpRisco_utilizEPC to set
     */
    public void setInfoExpRisco_utilizEPC(int infoExpRisco_utilizEPC) {
        this.infoExpRisco_utilizEPC = infoExpRisco_utilizEPC;
    }

    /**
     * @return the infoExpRisco_utilizEPI
     */
    public int getInfoExpRisco_utilizEPI() {
        return infoExpRisco_utilizEPI;
    }

    /**
     * @param infoExpRisco_utilizEPI the infoExpRisco_utilizEPI to set
     */
    public void setInfoExpRisco_utilizEPI(int infoExpRisco_utilizEPI) {
        this.infoExpRisco_utilizEPI = infoExpRisco_utilizEPI;
    }

    /**
     * @return the infoExpRisco_eficEpi
     */
    public String getInfoExpRisco_eficEpi() {
        return infoExpRisco_eficEpi;
    }

    /**
     * @param infoExpRisco_eficEpi the infoExpRisco_eficEpi to set
     */
    public void setInfoExpRisco_eficEpi(String infoExpRisco_eficEpi) {
        this.infoExpRisco_eficEpi = infoExpRisco_eficEpi;
    }

    /**
     * @return the infoExpRisco_docAval
     */
    public String getInfoExpRisco_docAval() {
        return infoExpRisco_docAval;
    }

    /**
     * @param infoExpRisco_docAval the infoExpRisco_docAval to set
     */
    public void setInfoExpRisco_docAval(String infoExpRisco_docAval) {
        this.infoExpRisco_docAval = infoExpRisco_docAval;
    }

    /**
     * @return the infoExpRisco_medProtecao
     */
    public String getInfoExpRisco_medProtecao() {
        return infoExpRisco_medProtecao;
    }

    /**
     * @param infoExpRisco_medProtecao the infoExpRisco_medProtecao to set
     */
    public void setInfoExpRisco_medProtecao(String infoExpRisco_medProtecao) {
        this.infoExpRisco_medProtecao = infoExpRisco_medProtecao;
    }

    /**
     * @return the infoExpRisco_condFuncto
     */
    public String getInfoExpRisco_condFuncto() {
        return infoExpRisco_condFuncto;
    }

    /**
     * @param infoExpRisco_condFuncto the infoExpRisco_condFuncto to set
     */
    public void setInfoExpRisco_condFuncto(String infoExpRisco_condFuncto) {
        this.infoExpRisco_condFuncto = infoExpRisco_condFuncto;
    }

    /**
     * @return the infoExpRisco_usoInint
     */
    public String getInfoExpRisco_usoInint() {
        return infoExpRisco_usoInint;
    }

    /**
     * @param infoExpRisco_usoInint the infoExpRisco_usoInint to set
     */
    public void setInfoExpRisco_usoInint(String infoExpRisco_usoInint) {
        this.infoExpRisco_usoInint = infoExpRisco_usoInint;
    }

    /**
     * @return the infoExpRisco_przValid
     */
    public String getInfoExpRisco_przValid() {
        return infoExpRisco_przValid;
    }

    /**
     * @param infoExpRisco_przValid the infoExpRisco_przValid to set
     */
    public void setInfoExpRisco_przValid(String infoExpRisco_przValid) {
        this.infoExpRisco_przValid = infoExpRisco_przValid;
    }

    /**
     * @return the infoExpRisco_periodicTroca
     */
    public String getInfoExpRisco_periodicTroca() {
        return infoExpRisco_periodicTroca;
    }

    /**
     * @param infoExpRisco_periodicTroca the infoExpRisco_periodicTroca to set
     */
    public void setInfoExpRisco_periodicTroca(String infoExpRisco_periodicTroca) {
        this.infoExpRisco_periodicTroca = infoExpRisco_periodicTroca;
    }

    /**
     * @return the infoExpRisco_higienizacao
     */
    public String getInfoExpRisco_higienizacao() {
        return infoExpRisco_higienizacao;
    }

    /**
     * @param infoExpRisco_higienizacao the infoExpRisco_higienizacao to set
     */
    public void setInfoExpRisco_higienizacao(String infoExpRisco_higienizacao) {
        this.infoExpRisco_higienizacao = infoExpRisco_higienizacao;
    }

    /**
     * @return the infoExpRisco_cpfResp
     */
    public String getInfoExpRisco_cpfResp() {
        return infoExpRisco_cpfResp;
    }

    /**
     * @param infoExpRisco_cpfResp the infoExpRisco_cpfResp to set
     */
    public void setInfoExpRisco_cpfResp(String infoExpRisco_cpfResp) {
        this.infoExpRisco_cpfResp = infoExpRisco_cpfResp;
    }

    /**
     * @return the infoExpRisco_ideOC
     */
    public int getInfoExpRisco_ideOC() {
        return infoExpRisco_ideOC;
    }

    /**
     * @param infoExpRisco_ideOC the infoExpRisco_ideOC to set
     */
    public void setInfoExpRisco_ideOC(int infoExpRisco_ideOC) {
        this.infoExpRisco_ideOC = infoExpRisco_ideOC;
    }

    /**
     * @return the infoExpRisco_nrOC
     */
    public String getInfoExpRisco_nrOC() {
        return infoExpRisco_nrOC;
    }

    /**
     * @param infoExpRisco_nrOC the infoExpRisco_nrOC to set
     */
    public void setInfoExpRisco_nrOC(String infoExpRisco_nrOC) {
        this.infoExpRisco_nrOC = infoExpRisco_nrOC;
    }

    /**
     * @return the infoExpRisco_ufOC
     */
    public String getInfoExpRisco_ufOC() {
        return infoExpRisco_ufOC;
    }

    /**
     * @param infoExpRisco_ufOC the infoExpRisco_ufOC to set
     */
    public void setInfoExpRisco_ufOC(String infoExpRisco_ufOC) {
        this.infoExpRisco_ufOC = infoExpRisco_ufOC;
    }    
    
     @Override
    public int hashCode() {
        int hash = 5;
        hash = 29 * hash + Objects.hashCode(this.ideVinculo_cpfTrab);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final S2240 other = (S2240) obj;
        if (!Objects.equals(this.ideVinculo_cpfTrab, other.ideVinculo_cpfTrab)) {
            return false;
        }
        return true;
    }

    /**
     * @return the infoExpRisco_limTol
     */
    public int getInfoExpRisco_limTol() {
        return infoExpRisco_limTol;
    }

    /**
     * @param infoExpRisco_limTol the infoExpRisco_limTol to set
     */
    public void setInfoExpRisco_limTol(int infoExpRisco_limTol) {
        this.infoExpRisco_limTol = infoExpRisco_limTol;
    }

    /**
     * @return the infoExpRisco_eficEpc
     */
    public String getInfoExpRisco_eficEpc() {
        return infoExpRisco_eficEpc;
    }

    /**
     * @param infoExpRisco_eficEpc the infoExpRisco_eficEpc to set
     */
    public void setInfoExpRisco_eficEpc(String infoExpRisco_eficEpc) {
        this.infoExpRisco_eficEpc = infoExpRisco_eficEpc;
    }

    /**
     * @return the infoExpRisco_dscEPI
     */
    public String getInfoExpRisco_dscEPI() {
        return infoExpRisco_dscEPI;
    }

    /**
     * @param infoExpRisco_dscEPI the infoExpRisco_dscEPI to set
     */
    public void setInfoExpRisco_dscEPI(String infoExpRisco_dscEPI) {
        this.infoExpRisco_dscEPI = infoExpRisco_dscEPI;
    }

    /**
     * @return the infoExpRisco_desOC
     */
    public String getInfoExpRisco_desOC() {
        return infoExpRisco_desOC;
    }

    /**
     * @param infoExpRisco_desOC the infoExpRisco_desOC to set
     */
    public void setInfoExpRisco_desOC(String infoExpRisco_desOC) {
        this.infoExpRisco_desOC = infoExpRisco_desOC;
    }

    /**
     * @return the infoExpRisco_obsCompl
     */
    public String getInfoExpRisco_obsCompl() {
        return infoExpRisco_obsCompl;
    }

    /**
     * @param infoExpRisco_obsCompl the infoExpRisco_obsCompl to set
     */
    public void setInfoExpRisco_obsCompl(String infoExpRisco_obsCompl) {
        this.infoExpRisco_obsCompl = infoExpRisco_obsCompl;
    }

    /**
     * @return the infoExpRisco_nrInsc
     */
    public String getInfoExpRisco_nrInsc() {
        return infoExpRisco_nrInsc;
    }

    /**
     * @param infoExpRisco_nrInsc the infoExpRisco_nrInsc to set
     */
    public void setInfoExpRisco_nrInsc(String infoExpRisco_nrInsc) {
        this.infoExpRisco_nrInsc = infoExpRisco_nrInsc;
    }
}

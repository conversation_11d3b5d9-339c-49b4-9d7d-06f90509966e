package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RastrearStat;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.Sqls;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class RastrearStatDao {

    /**
     * Inserir dados na RastrearStat
     *
     * @param rastrearstat - Dados da RastrearStat
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void Inserir(RastrearStat rastrearstat, Persistencia persistencia) throws Exception {
        try {
            String sql = Sqls.montaInsert(rastrearstat);
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rastrearstat.getSequencia());
            consulta.setBigDecimal(rastrearstat.getCodFil());
            consulta.setBigDecimal(rastrearstat.getSeqRota());
            consulta.setBigDecimal(rastrearstat.getCodPessoa());
            consulta.setDate(DataAtual.LC2Date(rastrearstat.getDataPOS()));
            consulta.setString(rastrearstat.getHoraPOS());
            consulta.setDate(DataAtual.LC2Date(rastrearstat.getData()));
            consulta.setString(rastrearstat.getHora());
            consulta.setString(rastrearstat.getPlaca());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao adiconar RastrearStat \r\n" + e.getMessage());
        }
    }

    public RastrearStat buscarLocalizacao(String seqRota, BigDecimal codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " select * from RastrearStat "
                    + " where SeqRota = ? "
                    + "    and codFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setBigDecimal(codFil);
            consulta.select();
            RastrearStat loc = new RastrearStat();
            while (consulta.Proximo()) {
                loc.setCodFil(consulta.getBigDecimal("codFil"));
                loc.setCodPessoa(consulta.getBigDecimal("codPessoa"));
                loc.setData(consulta.getLocalDate("data"));
                loc.setDataPOS(consulta.getLocalDate("dataPOS"));
                loc.setHora(consulta.getString("hora"));
                loc.setHoraPOS(consulta.getString("horaPOS"));
                loc.setPlaca(consulta.getString("placa"));
                loc.setSeqRota(consulta.getBigDecimal("seqRota"));
                loc.setSequencia(consulta.getBigDecimal("sequencia"));
            }
            consulta.Close();
            return loc;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização - " + e.getMessage());
        }
    }
}

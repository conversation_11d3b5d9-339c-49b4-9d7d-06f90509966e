package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.VeiculosAbast;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class VeiculosAbastDao {

    public List<VeiculosAbast> ultimoKM(int Numero, Persistencia persistencia) throws Exception {
        List<VeiculosAbast> listaVeiculosAbast;
        String sql = "Select Top 1 KM from VeiculosAbast "
                + "where Numero = ? "
                + "and Data <= ? "
                + "order by Data Desc, KM desc";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setInt(Numero);
            consult.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consult.select();
            listaVeiculosAbast = new ArrayList();
            while (consult.Proximo()) {
                VeiculosAbast veiculosabast = new VeiculosAbast();
                veiculosabast.setKM(consult.getString("KM"));
                listaVeiculosAbast.add(veiculosabast);
            }
            consult.Close();
            return listaVeiculosAbast;
        } catch (Exception e) {
            throw new Exception("Falha ao executar buscaVeiculosAbast" + e.getMessage());
        }
    }

    public VeiculosAbast ultimoKM(int Numero, String dataAtual, Persistencia persistencia) throws Exception {
        VeiculosAbast veiculosabast = null;
        String sql = "Select Top 1 KM from VeiculosAbast "
                + "where Numero = ? "
                + "and Data <= ? "
                + "order by Data Desc, KM desc";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setInt(Numero);
            consult.setString(dataAtual);
            consult.select();
            if (consult.Proximo()) {
                veiculosabast = new VeiculosAbast();
                veiculosabast.setKM(consult.getString("KM"));
            }
            consult.Close();
            return veiculosabast;
        } catch (Exception e) {
            throw new Exception("VeiculosAbastDao.ultimoKM - " + e.getMessage()+"\r\n"
                    + "Select Top 1 KM from VeiculosAbast "
                + "where Numero = "+Numero
                + "and Data <= "+dataAtual
                + "order by Data Desc, KM desc");
        }
    }

    public VeiculosAbast getSequencia(Persistencia persistencia) throws Exception {
        String sql = "select MAX(Sequencia) Sequencia from VeiculosAbast";
        VeiculosAbast veiculo = new VeiculosAbast();
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                veiculo.setSequencia(consult.getString("Sequencia"));
            }
            consult.Close();
            return veiculo;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar sequencia - " + e.getMessage());
        }
    }

    public void insereVeiculosAbast(VeiculosAbast veiculosabast, Persistencia persistencia) throws Exception {
        String sql = "Insert into VeiculosAbast (CodFil,Numero,CodPessoa,"
                + " KM, KMRodados, CodProd, Data, Qtde,"
                + "CustoUn, ValorTotal, Media, CCusto, Obs,"
                + "Operador, Dt_alter, Hr_alter,Sequencia) "
                + "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(veiculosabast.getCodFil());
            consulta.setInt(veiculosabast.getNumero());
            consulta.setBigDecimal(veiculosabast.getCodPessoa());
            consulta.setBigDecimal(veiculosabast.getKM());
            consulta.setBigDecimal(veiculosabast.getKMRodados());
            consulta.setBigDecimal(veiculosabast.getCodProd());
            consulta.setString(veiculosabast.getData().toString());
            consulta.setBigDecimal(veiculosabast.getQtde());
            consulta.setBigDecimal(veiculosabast.getCustoUn());
            consulta.setBigDecimal(veiculosabast.getValorTotal());
            consulta.setBigDecimal(veiculosabast.getMedia());
            consulta.setString(veiculosabast.getCCusto());
            consulta.setString(veiculosabast.getObs());
            consulta.setString(veiculosabast.getOperador());
            consulta.setString(veiculosabast.getDt_alter().toString());
//            consulta.setDate(java.sql.Date.valueOf(veiculosabast.getDt_alter()));
            consulta.setString(veiculosabast.getHr_alter());
            consulta.setString(veiculosabast.getSequencia().toString());
            consulta.insert();
            consulta.close();
        } catch (SQLException e) {
            throw new RuntimeException("Falha ao executar atualizarVeiculosAbast" + e.getMessage());
        }
    }

}

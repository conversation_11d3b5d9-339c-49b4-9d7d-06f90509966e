/*
 */
package br.com.sasw.managedBeans.comercial;

import Arquivo.ArquivoLog;
import Controller.Produtos.ProdutosSatMobWeb;
import Controller.Propostas.PropostasSatMobWeb;
import Dados.Persistencia;
import SasBeans.Contatos;
import SasBeans.Filiais;
import SasBeans.Frequencias;
import SasBeans.Produtos;
import SasBeans.PropCml;
import SasBeans.PropCmlMod;
import SasBeans.PropCondPgto;
import SasBeansCompostas.ProdutosPropProdServ;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.arquivos.PdfProposta;
import br.com.sasw.lazydatamodels.ProdutosLazyList;
import br.com.sasw.lazydatamodels.PropostasLazyList;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.DragDropEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "propostas")
@ViewScoped
public class PropostasMB implements Serializable {

    private Persistencia persistencia;
    private PropostasSatMobWeb propostassatmob;
    private ProdutosSatMobWeb produtossatmob;
    private BigDecimal codPessoa;
    private String codFil, nomeFilial, banco, operador, log, caminho, queryProduto, foneContato, enderecoContato;
    private ArquivoLog logerro;
    private int total, flag;
    private Map filters;
    private LazyDataModel<PropCml> propostas = null;
    private LazyDataModel<Produtos> produtos = null;
    private List<ProdutosPropProdServ> produtosProposta;
    private Produtos produtoSelecionado;
    private ProdutosPropProdServ produtoPropostaSelecionado;
    private boolean mostrarFiliais, limparFiltros;
    private PropCml novaProposta;
    private Contatos contato;
    private List<Contatos> contatos;
    private List<String> referencias;
    private Filiais filial;
    private List<PropCondPgto> condicoes;
    private PropCondPgto condicao;
    private List<PropCmlMod> modelos;
    private PropCmlMod modelo;
    private List<Frequencias> frequencias;

    public PropostasMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
        propostassatmob = new PropostasSatMobWeb();
        produtossatmob = new ProdutosSatMobWeb();

    }

    /**
     * Inicializa a persistencia e os filtros de pesquisa
     *
     * @param pp
     */
    public void Persistencias(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.filters = new HashMap();
            this.filters.put(" propcml.codfil = ? ", this.codFil);
            this.filters.put(" propcml.codfil in (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and "
                    + " paramet.path = '"
//                    + (this.persistencia.getEmpresa().equals("SATCONFEDERALBSB") ? "SatCVB" : this.persistencia.getEmpresa())
                    + (this.persistencia.getEmpresa())
                    + "') ", "");
            this.filters.put(" propcml.numero = ? ", "");
            this.filters.put(" propcml.cliente like ? ", "");
            this.filters.put(" propcml.descricao like ? ", "");
            this.filters.put(" propcml.referencia = ? ", "");
            this.total = this.propostassatmob.contagem(this.filters, this.persistencia);
            this.frequencias = this.propostassatmob.listarFrequencias(this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    /**
     * Pesquisa paginada de propostas
     *
     * @return
     */
    public LazyDataModel<PropCml> getAllPropostas() {
        if (this.propostas == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace(" propcml.codfil = ? ", this.codFil);
            dt.setFilters(this.filters);
            this.propostas = new PropostasLazyList(this.persistencia);
        }
        try {
            this.total = this.propostassatmob.contagem(this.filters, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.propostas;
    }

    /**
     * PEsquisa paginada de produtos
     *
     * @return
     */
    public LazyDataModel<Produtos> getAllProdutos() {
        if (this.produtos == null) {
            this.produtos = new ProdutosLazyList(this.persistencia);
        }
        return this.produtos;
    }

    /**
     * Seleção de um produto na tabela de produtos
     *
     * @param event
     */
    public void selecionarProduto(SelectEvent event) {
        this.produtoSelecionado = (Produtos) event.getObject();
    }

    /**
     * handler da ação de drag and drop de uma tabela a outra
     *
     * @param ddEvent
     */
    public void onSelecionarProduto(DragDropEvent ddEvent) {
        Produtos p = ((Produtos) ddEvent.getData());
        p.setPrecoCusto(p.getPrecoVenda());
        ProdutosPropProdServ produtoProposta = new ProdutosPropProdServ();
        produtoProposta.setProduto(p);
        produtoProposta.setId(new BigDecimal(String.valueOf(this.produtosProposta.size() + 1)));
        this.produtosProposta.add(produtoProposta);
        calculaValorParcialProposta();
    }

    /**
     * handler da seleção de produtos da tabela de produtos selecionados da
     * proposta
     *
     * @param event
     */
    public void selecionarProdutoProposta(SelectEvent event) {
        this.produtoPropostaSelecionado = (ProdutosPropProdServ) event.getObject();
    }

    /**
     * handler botão de adicionar produto (+)
     */
    public void adicionarNovoProduto() {
        if (null == this.produtoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneProduto"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.produtoSelecionado.setPrecoCusto(this.produtoSelecionado.getPrecoVenda());
            ProdutosPropProdServ produtoProposta = new ProdutosPropProdServ();
            produtoProposta.setProduto(this.produtoSelecionado);
            produtoProposta.setId(new BigDecimal(String.valueOf(this.produtosProposta.size() + 1)));
            boolean possui = false;
            for (ProdutosPropProdServ ppps : this.produtosProposta) {
                if (this.produtoSelecionado.getCodigo().compareTo(ppps.getProduto().getCodigo()) == 0) {
                    possui = true;
                    break;
                }
            }
            if (!possui) {
                this.produtosProposta.add(produtoProposta);
                calculaValorParcialProposta();
            }
        }
    }

    /**
     * handler botão de remover um produto da tabela (-)
     */
    public void retirarNovoProduto() {
        if (null == this.produtoPropostaSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneProduto"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.produtosProposta.remove(this.produtoPropostaSelecionado);
            calculaValorParcialProposta();
        }
    }

    /**
     * filtra produtos da tabela pela descrição do produto
     */
    public void buscarProdutos() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:todosProdutos");
        Map filtroProduto = new HashMap();
        filtroProduto.put(" descricao like ? ", "%" + this.queryProduto + "%");
        dt.setFilters(filtroProduto);
        getAllProdutos();
        dt.setFirst(0);
    }

    /**
     * calcula o valor corrente da proposta
     */
    public void calculaValorParcialProposta() {
        this.novaProposta.setValorProd(new BigDecimal("0"));
        for (ProdutosPropProdServ p : this.produtosProposta) {
            if (null != p.getProduto().getPrecoVenda()) {
                this.novaProposta.setValorProd(this.novaProposta.getValorProd().add(p.getProduto().getQtde().multiply(p.getProduto().getPrecoVenda())));
            }
        }
    }

    /**
     * handler botão de adicionar quantidades de um produto (+)
     *
     * @param sequencia
     */
    public void addQtdProduto(BigDecimal sequencia) {
        for (ProdutosPropProdServ p : this.produtosProposta) {
            if (p.getId().equals(sequencia)) {
                p.getProduto().setQtde(p.getProduto().getQtde().add(BigDecimal.ONE));
                calculaValorParcialProposta();
                break;
            }
        }
    }

    /**
     * handler botão de remover quantidades de um produto (-)
     *
     * @param sequencia
     */
    public void subtractQtdProduto(BigDecimal sequencia) {
        for (ProdutosPropProdServ p : this.produtosProposta) {
            if (p.getId().equals(sequencia)) {
                if (p.getProduto().getQtde().compareTo(BigDecimal.ZERO) > 0) {
                    p.getProduto().setQtde(p.getProduto().getQtde().subtract(BigDecimal.ONE));
                }
                calculaValorParcialProposta();
                break;
            }
        }
    }

    /**
     * handler para quando o usuário digitar o preço do produto
     *
     * @param sequencia
     */
    public void digitarPreco(BigDecimal sequencia) {
        for (ProdutosPropProdServ p : this.produtosProposta) {
            if (p.getProduto().getCodigo().equals(sequencia)) {
                if (null == p.getProduto().getPrecoCusto()) {
                    p.getProduto().setPrecoCusto("0");
                }
                if (null == p.getProduto().getPrecoVenda()) {
                    p.getProduto().setPrecoVenda("0");
                }
                if (p.getProduto().getPrecoCusto().compareTo(BigDecimal.ZERO) != 0) {
                    p.getProduto().setMargem(p.getProduto().getPrecoVenda().divide(p.getProduto().getPrecoCusto(), 5, RoundingMode.HALF_UP));
                }
                calculaValorParcialProposta();
                break;
            }
        }
    }

    /**
     * handler quando o usuário diminui a margem (-)
     *
     * @param sequencia
     */
    public void subtractMargem(BigDecimal sequencia) {
        for (ProdutosPropProdServ p : this.produtosProposta) {
            if (p.getProduto().getCodigo().equals(sequencia)) {
                if (p.getProduto().getMargem().compareTo(BigDecimal.ZERO) > 0) {
                    p.getProduto().setMargem(p.getProduto().getMargem().subtract(new BigDecimal("0.1")));
                }
                calculaValorParcialProposta();
                break;
            }
        }
    }

    /**
     * handler quando o usuário aumenta a margem (+)
     *
     * @param sequencia
     */
    public void addMargem(BigDecimal sequencia) {
        for (ProdutosPropProdServ p : this.produtosProposta) {
            if (p.getProduto().getCodigo().equals(sequencia)) {
                if (p.getProduto().getMargem().compareTo(BigDecimal.ZERO) > 0) {
                    p.getProduto().setMargem(p.getProduto().getMargem().add(new BigDecimal("0.1")));
                }
                calculaValorParcialProposta();
                break;
            }
        }
    }

    /**
     * Prepara o cadastro de uma proposta
     */
    public void preCadastro() {
        this.novaProposta = new PropCml();
        this.novaProposta.setCodFil(this.codFil);
        this.novaProposta.setValorProd(new BigDecimal("0"));
        this.novaProposta.setOperIncl(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novaProposta.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novaProposta.setCodPessoa(this.codPessoa);
        this.contato = new Contatos();
        this.contatos = new ArrayList<>();
        this.contatos.add(this.contato);
        this.enderecoContato = new String();
        this.foneContato = new String();
        this.flag = 1;
        getAllProdutos();
        this.produtosProposta = new ArrayList<>();
        this.condicao = new PropCondPgto();
        this.condicoes = new ArrayList<>();
    }

    /**
     * handler adicionar uma nova condição de pagamento
     */
    public void adicionarCondicao() {
        if (this.flag == 1) {
            //Cadastro
            this.condicoes.add(this.condicao);
            this.condicao = new PropCondPgto();
        } else if (this.flag == 2) {
            //Edição
            try {
                this.condicao.setCodFil(this.novaProposta.getCodFil());
                this.condicao.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.condicao.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.condicao.setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.condicao.setNumero(this.novaProposta.getNumero());
                this.propostassatmob.cadastrarCondicao(this.condicao, this.persistencia);
                this.condicoes = this.propostassatmob.getCondicoesPagamentoProposta(this.novaProposta.getNumero(), this.novaProposta.getCodFil(), this.persistencia);
                this.condicao = new PropCondPgto();
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    /**
     * handler de remover condição de pagamento
     *
     * @param cond
     */
    public void removerCondicao(PropCondPgto cond) {
        if (this.flag == 1) {
            //Cadastro
            this.condicoes.remove(cond);
        } else if (this.flag == 2) {
            //Edição
            try {
                this.propostassatmob.removerCondicao(cond, this.persistencia);
                this.condicoes = this.propostassatmob.getCondicoesPagamentoProposta(this.novaProposta.getNumero(), this.novaProposta.getCodFil(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    /**
     * Prepara a edição de uma proposta, carrega
     */
    public void preEdicao() {
        if (null == this.novaProposta) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneProposta"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                if (null == this.modelos) {
                    this.modelos = this.propostassatmob.getModelos(this.persistencia);
                }
                this.modelo = new PropCmlMod();
                this.modelo.setCodigo(this.novaProposta.getCodModelo());
                this.contato = this.propostassatmob.selecionarContato(this.novaProposta.getCodContato(), this.persistencia);
                this.contatos = new ArrayList<>();
                this.contatos.add(this.contato);
                this.condicao = new PropCondPgto();
                this.condicoes = this.propostassatmob.getCondicoesPagamentoProposta(this.novaProposta.getNumero(), this.novaProposta.getCodFil(), this.persistencia);
                this.enderecoContato = this.contato.getEndereco() + ", " + this.contato.getBairro() + ", " + this.contato.getCidade() + " - " + this.contato.getUF();
                try {
                    this.foneContato = (null == this.contato.getFone1() || this.contato.getFone1().equals("") ? ""
                            : FuncoesString.formatarString(this.contato.getFone1(), "(##) ########?#"))
                            + (null == this.contato.getFone2() || this.contato.getFone2().equals("") ? ""
                            : " - " + FuncoesString.formatarString(this.contato.getFone2(), "(##) ########?#"));
                    this.foneContato = this.foneContato.equals("") ? Messages.getMessageS("SemFoneContato") : this.foneContato;
                } catch (Exception e) {
                    this.foneContato = this.contato.getFone1() + (null != this.contato.getFone2() || this.contato.getFone2().equals("") ? ""
                            : " - " + this.contato.getFone2());
                }
                this.produtosProposta = this.propostassatmob.listaProdutosProposta(this.novaProposta.getNumero(), this.novaProposta.getCodFil(), this.persistencia);
                calculaValorParcialProposta();
                this.flag = 2;
                PrimeFaces.current().resetInputs("formCadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void selecionarModelo(SelectEvent select) {
        this.modelo = (PropCmlMod) select.getObject();
        this.novaProposta.setCodModelo(this.modelo.getCodigo());
    }

    /**
     * gera o pdf de uma proposta
     */
    public void gerarProposta() {
        try {
            this.filial = this.propostassatmob.getFilialProposta(this.novaProposta.getCodFil(), this.persistencia);
            PdfProposta pdf = new PdfProposta("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.banco + "\\Propostas\\", this.novaProposta.getNumero().toBigInteger().toString() + ".pdf", this.filial, this.novaProposta, this.banco);
            pdf.contato(this.filial, this.contato, this.novaProposta, this.propostassatmob.getDetalheModeloProposta(this.novaProposta.getCodModelo(), this.persistencia));
            pdf.detalhes(this.propostassatmob.getItensProposta(this.novaProposta.getCodModelo(), this.persistencia), this.produtosProposta, this.novaProposta, this.condicoes);
            pdf.fechaDocumento(this.propostassatmob.getPessoasProposta(this.novaProposta, this.persistencia));
            pdf.service();
            this.propostassatmob.atualizarModeloProposta(this.novaProposta, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrar() {
        try {
            this.novaProposta.setData(LocalDate.now());
            this.novaProposta.setHr_Incl(DataAtual.getDataAtual("HORA"));
            this.novaProposta.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novaProposta.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.novaProposta.setDt_Incl(LocalDate.now());
            this.novaProposta.setSituacao(new BigDecimal("55"));
            this.novaProposta.setCodContato(this.contato.getCodigo());
            this.novaProposta.setContato(this.contato.getContato());
            this.novaProposta.setEmail(this.contato.getEmail());
            //this.propostassatmob.cadastrarProposta(this.novaProposta, this.produtosProposta, this.persistencia);
            this.total = this.propostassatmob.contagem(this.filters, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editar() {
        try {
            this.novaProposta.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novaProposta.setCodPessoa(this.codPessoa);
            this.novaProposta.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novaProposta.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.novaProposta.setCodContato(this.contato.getCodigo());
            this.novaProposta.setContato(this.contato.getContato());
            this.novaProposta.setEmail(this.contato.getEmail());
            //this.propostassatmob.editarProposta(this.novaProposta, this.produtosProposta, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<String> buscarReferencias(String query) {
        try {
            this.referencias = this.propostassatmob.listaReferencias(query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.referencias;
    }

    public List<Contatos> buscarContatos(String query) {
        try {
            this.contatos = this.propostassatmob.buscarContatos(query, this.codPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.contatos;
    }

    public void selecionarContato(SelectEvent event) {
        this.contato = (Contatos) event.getObject();
        this.enderecoContato = this.contato.getEndereco() + ", " + this.contato.getBairro() + ", " + this.contato.getCidade() + " - " + this.contato.getUF();
        try {
            this.foneContato = (null == this.contato.getFone1() || this.contato.getFone1().equals("") ? ""
                    : FuncoesString.formatarString(this.contato.getFone1(), "(##) ########?#"))
                    + (null == this.contato.getFone2() || this.contato.getFone2().equals("") ? ""
                    : " - " + FuncoesString.formatarString(this.contato.getFone2(), "(##) ########?#"));
            this.foneContato = this.foneContato.equals("") ? Messages.getMessageS("SemFoneContato") : this.foneContato;
        } catch (Exception e) {
            this.foneContato = this.contato.getFone1() + (null != this.contato.getFone2() || this.contato.getFone2().equals("") ? ""
                    : " - " + this.contato.getFone2());
        }
    }

    public void mostrarTodasFiliais() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        if (this.mostrarFiliais) {
            this.filters.replace(" propcml.codfil in (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and "
                    + " paramet.path = '"
//                    + (this.persistencia.getEmpresa().equals("SATCONFEDERALBSB") ? "SatCVB" : this.persistencia.getEmpresa())
                    + (this.persistencia.getEmpresa())
                    + "') ", this.codPessoa.toBigInteger().toString());
            this.filters.replace(" propcml.codfil = ? ", "");
        } else {
            this.filters.replace(" propcml.codfil in (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and "
                    + " paramet.path = '"
//                    + (this.persistencia.getEmpresa().equals("SATCONFEDERALBSB") ? "SatCVB" : this.persistencia.getEmpresa())
                    + (this.persistencia.getEmpresa())
                    + "') ", "");
            this.filters.replace(" propcml.codfil = ? ", this.codFil);
        }
        dt.setFilters(this.filters);
        getAllPropostas();
        dt.setFirst(0);
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public boolean isLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public PropCml getNovaProposta() {
        return novaProposta;
    }

    public void setNovaProposta(PropCml novaProposta) {
        this.novaProposta = novaProposta;
    }

    public Contatos getContato() {
        return contato;
    }

    public void setContato(Contatos contato) {
        this.contato = contato;
    }

    public List<Contatos> getContatos() {
        return contatos;
    }

    public void setContatos(List<Contatos> contatos) {
        this.contatos = contatos;
    }

    public List<ProdutosPropProdServ> getProdutosProposta() {
        return produtosProposta;
    }

    public void setProdutosProposta(List<ProdutosPropProdServ> produtosProposta) {
        this.produtosProposta = produtosProposta;
    }

    public Produtos getProdutoSelecionado() {
        return produtoSelecionado;
    }

    public void setProdutoSelecionado(Produtos produtoSelecionado) {
        this.produtoSelecionado = produtoSelecionado;
    }

    public String getQueryProduto() {
        return queryProduto;
    }

    public void setQueryProduto(String queryProduto) {
        this.queryProduto = queryProduto;
    }

    public ProdutosPropProdServ getProdutoPropostaSelecionado() {
        return produtoPropostaSelecionado;
    }

    public void setProdutoPropostaSelecionado(ProdutosPropProdServ produtoPropostaSelecionado) {
        this.produtoPropostaSelecionado = produtoPropostaSelecionado;
    }

    public String getFoneContato() {
        return foneContato;
    }

    public void setFoneContato(String foneContato) {
        this.foneContato = foneContato;
    }

    public String getEnderecoContato() {
        return enderecoContato;
    }

    public void setEnderecoContato(String enderecoContato) {
        this.enderecoContato = enderecoContato;
    }

    public List<String> getReferencias() {
        return referencias;
    }

    public void setReferencias(List<String> referencias) {
        this.referencias = referencias;
    }

    public List<PropCondPgto> getCondicoes() {
        return condicoes;
    }

    public void setCondicoes(List<PropCondPgto> condicoes) {
        this.condicoes = condicoes;
    }

    public PropCondPgto getCondicao() {
        return condicao;
    }

    public void setCondicao(PropCondPgto condicao) {
        this.condicao = condicao;
    }

    public List<PropCmlMod> getModelos() {
        return modelos;
    }

    public void setModelos(List<PropCmlMod> modelos) {
        this.modelos = modelos;
    }

    public PropCmlMod getModelo() {
        return modelo;
    }

    public void setModelo(PropCmlMod modelo) {
        if (null != modelo) {
            this.modelo = modelo;
        }
    }

    public List<Frequencias> getFrequencias() {
        return frequencias;
    }

    public void setFrequencias(List<Frequencias> frequencias) {
        this.frequencias = frequencias;
    }
}

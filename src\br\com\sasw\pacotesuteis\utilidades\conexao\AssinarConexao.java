/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades.conexao;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.util.Enumeration;
import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;

/**
 *
 * <AUTHOR>
 */
public class AssinarConexao {
  
    private static String cert = "cert";
    private static String senha = "123456789";

    public static void assinarConexaoGTVe() {
        try {
            File cacert = new File(File.separator + "Certificados" + File.separator + "cacert.jks");
            
            File cacertPath = new File(File.separator + "Certificados" + File.separator);
            if (!cacert.exists()) {
                cacertPath.mkdirs();
            }

            KeyStore keyStore = KeyStore.getInstance("JKS");
            if (cacert.exists()) {
                // if exists, load
                keyStore.load(new FileInputStream(cacert), "123456".toCharArray());
            } else {
                // if not exists, create
                keyStore.load(null, null);
                keyStore.store(new FileOutputStream(cacert), "123456".toCharArray());

                File cert = new File(File.separator + "Certificados" + File.separator 
                        + AssinarConexao.cert);

                InputStream certIs = new FileInputStream(cert);
                KeyStore ks = KeyStore.getInstance("PKCS12");
                ks.load(certIs, AssinarConexao.senha.toCharArray());

                Enumeration<String> e = ks.aliases();

                // print the enumeration 
                Certificate certs1 = null;
                String alias = null;
                while (e.hasMoreElements()) {
                    alias = e.nextElement();
                    certs1 = ks.getCertificate(alias);
                    if (certs1 != null) {
                        break;
                    }
                }

                // Add the certificate
                keyStore.setCertificateEntry(alias, certs1);

                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                DataInputStream dis = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator 
                        + "svrs.cer"));
                byte[] bytes = new byte[dis.available()];
                dis.readFully(bytes);
                ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
                Certificate certs = cf.generateCertificate(bais);

                // Add the certificate
                keyStore.setCertificateEntry("svrs", certs);

                // Save the new keystore contents
                FileOutputStream out = new FileOutputStream(cacert);
                keyStore.store(out, "123456".toCharArray());
                out.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        assinarConexao(
                File.separator + "Certificados" + File.separator + AssinarConexao.cert,
                AssinarConexao.senha,
                File.separator + "Certificados" + File.separator + "cacert.jks");
    }

    public static void assinarConexao(String keystoreInputPath, String keyStorePassword, String truststoreInputPath) {
        try {
            InputStream keystoreInput = new FileInputStream(keystoreInputPath);
            InputStream truststoreInput = new FileInputStream(truststoreInputPath);
            setSSLFactories(keystoreInput, keyStorePassword, truststoreInput);
            keystoreInput.close();
            truststoreInput.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void setSSLFactories(InputStream keyStream, String keyStorePassword, InputStream trustStream) throws Exception {
        // Get keyStore
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());

        // if your store is password protected then declare it (it can be null however)
        char[] keyPassword = keyStorePassword.toCharArray();

        // load the stream to your store
        keyStore.load(keyStream, keyPassword);

        // initialize a key manager factory with the key store
        KeyManagerFactory keyFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        keyFactory.init(keyStore, keyPassword);

        // get the key managers from the factory
        KeyManager[] keyManagers = keyFactory.getKeyManagers();

        // Now get trustStore
        KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());

        // if your store is password protected then declare it (it can be null however)
        //char[] trustPassword = password.toCharArray();
        // load the stream to your store
        trustStore.load(trustStream, null);

        // initialize a trust manager factory with the trusted store
        TrustManagerFactory trustFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustFactory.init(trustStore);

        // get the trust managers from the factory
        TrustManager[] trustManagers = trustFactory.getTrustManagers();

        // initialize an ssl context to use these managers and set as default
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(keyManagers, trustManagers, null);
        SSLContext.setDefault(sslContext);
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class S1200 {

    private int sucesso;

    private String tipoEnvio;

    private String evtRemun_Id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_indApuracao;
    private String ideEvento_perApur;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;

    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;

    private String ideTrabalhador_cpfTrab;
    private String ideTrabalhador_nisTrab;
    private String ideTrabalhador_infoMV;
    private String infoMV_indMV;
    private String infoMV_remunOutrEmpr;
    private String ideTrabalhador_infoComplem;
    private String infoComplem_nmTrab;
    private String infoComplem_dtNascto;
    private String infoComplem_sucessaoVinc;
    private String sucessaoVinc_cnpjEmpregAnt;
    private String sucessaoVinc_matricAnt;
    private String sucessaoVinc_dtAdm;
    private String sucessaoVinc_observacao;
    private String ideTrabalhador_procJudTrab;
    private String procJudTrab_tpTrib;
    private String procJudTrab_nrProcJud;
    private String procJudTrab_codSusp;

    private String ideTrabalhador_codCateg;
    
    private String CBO;

    private String ideTrabalhador_matr;

    private String infoInterm_qtdDiasInterm;
       

    private List<DmDev> dmDev;

    public static class DmDev {

        private String ideTrabalhador_cpfTrab;
        private String dmDev_ideDmDev;
        private String dmDev_codCateg;
        private String infoPerApur_ideEstabLot_tpInsc;
        private String infoPerApur_ideEstabLot_nrInsc;
        private String infoPerApur_ideEstabLot_codLotacao;
        private String ideEstabLot_qtdDiasAv;
        private String remunPerApur_matricula;
        private String remunPerApur_indSimples;
        private List<ItensRemun> remunPerApur_itensRemun;

        private InfoSaudeColet remunPerApur_infoSaudeColet;

        private String remunPerApur_infoAgNocivo_grauExp;
        private String remunPerApur_infoTrabInterm_codConv;
        private String ideADC_dtAcConv;
        private String ideADC_tpAcConv;
        private String ideADC_compAcConv;
        private String ideADC_dtEfAcConv;
        private String ideADC_dsc;
        private String ideADC_remunSuc;

        private String idePeriodo_perRef;
        private String idePeriodo_ideEstabLot_tpInsc;
        private String idePeriodo_ideEstabLot_nrInsc;
        private String idePeriodo_ideEstabLot_codLotacao;
        private String remunPerAnt_matricula;
        private String remunPerAnt_indSimples;

        private List<ItensRemun> remunPerAnt_itensRemun;
        private String remunPerAnt_infoAgNocivo_grauExp;
        private String remunPerAnt_infoTrabInterm_codConv;

        private String infoComplCont_codCBO;
        private String infoComplCont_natAtividade;
        private String infoComplCont_qtdDiasTrab;

        @Override
        public int hashCode() {
            int hash = 3;
            hash = 43 * hash + Objects.hashCode(this.ideTrabalhador_cpfTrab);
            hash = 43 * hash + Objects.hashCode(this.dmDev_ideDmDev);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final DmDev other = (DmDev) obj;
            if (!Objects.equals(this.ideTrabalhador_cpfTrab, other.ideTrabalhador_cpfTrab)) {
                return false;
            }
            if (!Objects.equals(this.dmDev_ideDmDev, other.dmDev_ideDmDev)) {
                return false;
            }
            return true;
        }

        public InfoSaudeColet getRemunPerApur_infoSaudeColet() {
            return remunPerApur_infoSaudeColet;
        }

        public void setRemunPerApur_infoSaudeColet(InfoSaudeColet remunPerApur_infoSaudeColet) {
            this.remunPerApur_infoSaudeColet = remunPerApur_infoSaudeColet;
        }

        public String getIdeTrabalhador_cpfTrab() {
            return null == ideTrabalhador_cpfTrab ? "" : ideTrabalhador_cpfTrab;
        }

        public void setIdeTrabalhador_cpfTrab(String ideTrabalhador_cpfTrab) {
            this.ideTrabalhador_cpfTrab = ideTrabalhador_cpfTrab;
        }

        public String getDmDev_ideDmDev() {
            return null == dmDev_ideDmDev ? "" : dmDev_ideDmDev;
        }

        public void setDmDev_ideDmDev(String dmDev_ideDmDev) {
            this.dmDev_ideDmDev = dmDev_ideDmDev;
        }

        public String getDmDev_codCateg() {
            return null == dmDev_codCateg ? "" : dmDev_codCateg;
        }

        public void setDmDev_codCateg(String dmDev_codCateg) {
            this.dmDev_codCateg = dmDev_codCateg;
        }

        public String getInfoPerApur_ideEstabLot_tpInsc() {
            return null == infoPerApur_ideEstabLot_tpInsc ? "" : infoPerApur_ideEstabLot_tpInsc;
        }

        public void setInfoPerApur_ideEstabLot_tpInsc(String infoPerApur_ideEstabLot_tpInsc) {
            this.infoPerApur_ideEstabLot_tpInsc = infoPerApur_ideEstabLot_tpInsc;
        }

        public String getInfoPerApur_ideEstabLot_nrInsc() {
            return null == infoPerApur_ideEstabLot_nrInsc ? "" : infoPerApur_ideEstabLot_nrInsc;
        }

        public void setInfoPerApur_ideEstabLot_nrInsc(String infoPerApur_ideEstabLot_nrInsc) {
            this.infoPerApur_ideEstabLot_nrInsc = infoPerApur_ideEstabLot_nrInsc;
        }

        public String getInfoPerApur_ideEstabLot_codLotacao() {
            return null == infoPerApur_ideEstabLot_codLotacao ? "" : infoPerApur_ideEstabLot_codLotacao;
        }

        public void setInfoPerApur_ideEstabLot_codLotacao(String infoPerApur_ideEstabLot_codLotacao) {
            this.infoPerApur_ideEstabLot_codLotacao = infoPerApur_ideEstabLot_codLotacao;
        }

        public String getIdeEstabLot_qtdDiasAv() {
            return null == ideEstabLot_qtdDiasAv ? "" : ideEstabLot_qtdDiasAv;
        }

        public void setIdeEstabLot_qtdDiasAv(String ideEstabLot_qtdDiasAv) {
            this.ideEstabLot_qtdDiasAv = ideEstabLot_qtdDiasAv;
        }

        public String getRemunPerApur_matricula() {
            return null == remunPerApur_matricula ? "" : remunPerApur_matricula;
        }

        public void setRemunPerApur_matricula(String remunPerApur_matricula) {
            this.remunPerApur_matricula = remunPerApur_matricula;
        }

        public String getRemunPerApur_indSimples() {
            return null == remunPerApur_indSimples ? "" : remunPerApur_indSimples;
        }

        public void setRemunPerApur_indSimples(String remunPerApur_indSimples) {
            this.remunPerApur_indSimples = remunPerApur_indSimples;
        }

        public List<ItensRemun> getRemunPerApur_itensRemun() {
            return remunPerApur_itensRemun;
        }

        public void setRemunPerApur_itensRemun(List<ItensRemun> remunPerApur_itensRemun) {
            this.remunPerApur_itensRemun = remunPerApur_itensRemun;
        }

        public String getRemunPerApur_infoAgNocivo_grauExp() {
            return null == remunPerApur_infoAgNocivo_grauExp ? "" : remunPerApur_infoAgNocivo_grauExp;
        }

        public void setRemunPerApur_infoAgNocivo_grauExp(String remunPerApur_infoAgNocivo_grauExp) {
            this.remunPerApur_infoAgNocivo_grauExp = remunPerApur_infoAgNocivo_grauExp;
        }

        public String getRemunPerApur_infoTrabInterm_codConv() {
            return null == remunPerApur_infoTrabInterm_codConv ? "" : remunPerApur_infoTrabInterm_codConv;
        }

        public void setRemunPerApur_infoTrabInterm_codConv(String remunPerApur_infoTrabInterm_codConv) {
            this.remunPerApur_infoTrabInterm_codConv = remunPerApur_infoTrabInterm_codConv;
        }

        public String getIdeADC_dtAcConv() {
            return null == ideADC_dtAcConv ? "" : ideADC_dtAcConv;
        }

        public void setIdeADC_dtAcConv(String ideADC_dtAcConv) {
            this.ideADC_dtAcConv = ideADC_dtAcConv;
        }

        public String getIdeADC_tpAcConv() {
            return null == ideADC_tpAcConv ? "" : ideADC_tpAcConv;
        }

        public void setIdeADC_tpAcConv(String ideADC_tpAcConv) {
            this.ideADC_tpAcConv = ideADC_tpAcConv;
        }

        public String getIdeADC_compAcConv() {
            return null == ideADC_compAcConv ? "" : ideADC_compAcConv;
        }

        public void setIdeADC_compAcConv(String ideADC_compAcConv) {
            this.ideADC_compAcConv = ideADC_compAcConv;
        }

        public String getIdeADC_dtEfAcConv() {
            return null == ideADC_dtEfAcConv ? "" : ideADC_dtEfAcConv;
        }

        public void setIdeADC_dtEfAcConv(String ideADC_dtEfAcConv) {
            this.ideADC_dtEfAcConv = ideADC_dtEfAcConv;
        }

        public String getIdeADC_dsc() {
            return null == ideADC_dsc ? "" : ideADC_dsc;
        }

        public void setIdeADC_dsc(String ideADC_dsc) {
            this.ideADC_dsc = ideADC_dsc;
        }

        public String getIdeADC_remunSuc() {
            return null == ideADC_remunSuc ? "" : ideADC_remunSuc;
        }

        public void setIdeADC_remunSuc(String ideADC_remunSuc) {
            this.ideADC_remunSuc = ideADC_remunSuc;
        }

        public String getIdePeriodo_perRef() {
            return null == idePeriodo_perRef ? "" : idePeriodo_perRef;
        }

        public void setIdePeriodo_perRef(String idePeriodo_perRef) {
            this.idePeriodo_perRef = idePeriodo_perRef;
        }

        public String getIdePeriodo_ideEstabLot_tpInsc() {
            return null == idePeriodo_ideEstabLot_tpInsc ? "" : idePeriodo_ideEstabLot_tpInsc;
        }

        public void setIdePeriodo_ideEstabLot_tpInsc(String idePeriodo_ideEstabLot_tpInsc) {
            this.idePeriodo_ideEstabLot_tpInsc = idePeriodo_ideEstabLot_tpInsc;
        }

        public String getIdePeriodo_ideEstabLot_nrInsc() {
            return null == idePeriodo_ideEstabLot_nrInsc ? "" : idePeriodo_ideEstabLot_nrInsc;
        }

        public void setIdePeriodo_ideEstabLot_nrInsc(String idePeriodo_ideEstabLot_nrInsc) {
            this.idePeriodo_ideEstabLot_nrInsc = idePeriodo_ideEstabLot_nrInsc;
        }

        public String getIdePeriodo_ideEstabLot_codLotacao() {
            return null == idePeriodo_ideEstabLot_codLotacao ? "" : idePeriodo_ideEstabLot_codLotacao;
        }

        public void setIdePeriodo_ideEstabLot_codLotacao(String idePeriodo_ideEstabLot_codLotacao) {
            this.idePeriodo_ideEstabLot_codLotacao = idePeriodo_ideEstabLot_codLotacao;
        }

        public String getRemunPerAnt_matricula() {
            return null == remunPerAnt_matricula ? "" : remunPerAnt_matricula;
        }

        public void setRemunPerAnt_matricula(String remunPerAnt_matricula) {
            this.remunPerAnt_matricula = remunPerAnt_matricula;
        }

        public String getRemunPerAnt_indSimples() {
            return null == remunPerAnt_indSimples ? "" : remunPerAnt_indSimples;
        }

        public void setRemunPerAnt_indSimples(String remunPerAnt_indSimples) {
            this.remunPerAnt_indSimples = remunPerAnt_indSimples;
        }

        public List<ItensRemun> getRemunPerAnt_itensRemun() {
            return remunPerAnt_itensRemun;
        }

        public void setRemunPerAnt_itensRemun(List<ItensRemun> remunPerAnt_itensRemun) {
            this.remunPerAnt_itensRemun = remunPerAnt_itensRemun;
        }

        public String getRemunPerAnt_infoAgNocivo_grauExp() {
            return null == remunPerAnt_infoAgNocivo_grauExp ? "" : remunPerAnt_infoAgNocivo_grauExp;
        }

        public void setRemunPerAnt_infoAgNocivo_grauExp(String remunPerAnt_infoAgNocivo_grauExp) {
            this.remunPerAnt_infoAgNocivo_grauExp = remunPerAnt_infoAgNocivo_grauExp;
        }

        public String getRemunPerAnt_infoTrabInterm_codConv() {
            return null == remunPerAnt_infoTrabInterm_codConv ? "" : remunPerAnt_infoTrabInterm_codConv;
        }

        public void setRemunPerAnt_infoTrabInterm_codConv(String remunPerAnt_infoTrabInterm_codConv) {
            this.remunPerAnt_infoTrabInterm_codConv = remunPerAnt_infoTrabInterm_codConv;
        }

        public String getInfoComplCont_codCBO() {
            return null == infoComplCont_codCBO ? "" : infoComplCont_codCBO;
        }

        public void setInfoComplCont_codCBO(String infoComplCont_codCBO) {
            this.infoComplCont_codCBO = infoComplCont_codCBO;
        }

        public String getInfoComplCont_natAtividade() {
            return null == infoComplCont_natAtividade ? "" : infoComplCont_natAtividade;
        }

        public void setInfoComplCont_natAtividade(String infoComplCont_natAtividade) {
            this.infoComplCont_natAtividade = infoComplCont_natAtividade;
        }

        public String getInfoComplCont_qtdDiasTrab() {
            return null == infoComplCont_qtdDiasTrab ? "" : infoComplCont_qtdDiasTrab;
        }

        public void setInfoComplCont_qtdDiasTrab(String infoComplCont_qtdDiasTrab) {
            this.infoComplCont_qtdDiasTrab = infoComplCont_qtdDiasTrab;
        }
    }

    private List<RemunOutrEmpr> remunOutrEmpr;

    public static class RemunOutrEmpr {

        private String tpInsc;
        private String nrInsc;
        private String codCateg;
        private String vlrRemunOE;
        private String IdeTrabalhador_cpfTrab;

        public String getTpInsc() {
            return tpInsc;
        }

        public void setTpInsc(String tpInsc) {
            this.tpInsc = tpInsc;
        }

        public String getNrInsc() {
            return nrInsc;
        }

        public void setNrInsc(String nrInsc) {
            this.nrInsc = nrInsc;
        }

        public String getCodCateg() {
            return codCateg;
        }

        public void setCodCateg(String codCateg) {
            this.codCateg = codCateg;
        }

        public String getVlrRemunOE() {
            return vlrRemunOE;
        }

        public void setVlrRemunOE(String vlrRemunOE) {
            this.vlrRemunOE = vlrRemunOE;
        }

        public String getIdeTrabalhador_cpfTrab() {
            return IdeTrabalhador_cpfTrab;
        }

        public void setIdeTrabalhador_cpfTrab(String IdeTrabalhador_cpfTrab) {
            this.IdeTrabalhador_cpfTrab = IdeTrabalhador_cpfTrab;
        }

        @Override
        public int hashCode() {
            int hash = 5;
            hash = 41 * hash + Objects.hashCode(this.IdeTrabalhador_cpfTrab);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final RemunOutrEmpr other = (RemunOutrEmpr) obj;
            if (!Objects.equals(this.IdeTrabalhador_cpfTrab, other.IdeTrabalhador_cpfTrab)) {
                return false;
            }
            return true;
        }
    }

    public static class ItensRemun {

        private String itensRemun_codRubr;
        private String itensRemun_ideTabRubr;
        private String itensRemun_qtdRubr;
        private String itensRemun_fatorRubr;
        private String itensRemun_vrUnit;
        private String itensRemun_vrRubr;

        public String getItensRemun_codRubr() {
            return null == itensRemun_codRubr ? "" : itensRemun_codRubr;
        }

        public void setItensRemun_codRubr(String itensRemun_codRubr) {
            this.itensRemun_codRubr = itensRemun_codRubr;
        }

        public String getItensRemun_ideTabRubr() {
            return null == itensRemun_ideTabRubr ? "" : itensRemun_ideTabRubr;
        }

        public void setItensRemun_ideTabRubr(String itensRemun_ideTabRubr) {
            this.itensRemun_ideTabRubr = itensRemun_ideTabRubr;
        }

        public String getItensRemun_qtdRubr() {
            return null == itensRemun_qtdRubr ? "" : itensRemun_qtdRubr;
        }

        public void setItensRemun_qtdRubr(String itensRemun_qtdRubr) {
            this.itensRemun_qtdRubr = itensRemun_qtdRubr;
        }

        public String getItensRemun_fatorRubr() {
            return null == itensRemun_fatorRubr ? "" : itensRemun_fatorRubr;
        }

        public void setItensRemun_fatorRubr(String itensRemun_fatorRubr) {
            this.itensRemun_fatorRubr = itensRemun_fatorRubr;
        }

        public String getItensRemun_vrUnit() {
            return null == itensRemun_vrUnit ? "" : itensRemun_vrUnit;
        }

        public void setItensRemun_vrUnit(String itensRemun_vrUnit) {
            this.itensRemun_vrUnit = itensRemun_vrUnit;
        }

        public String getItensRemun_vrRubr() {
            return null == itensRemun_vrRubr ? "" : itensRemun_vrRubr;
        }

        public void setItensRemun_vrRubr(String itensRemun_vrRubr) {
            this.itensRemun_vrRubr = itensRemun_vrRubr;
        }
    }

    public static class InfoSaudeColet {

        public InfoSaudeColet() {
            this.detOper = new ArrayList<>();
        }
        private List<DetOper> detOper;

        public List<DetOper> getDetOper() {
            return detOper;
        }

        public void setDetOper(List<DetOper> detOper) {
            this.detOper = detOper;
        }
    }

    public static class DetOper {

        public DetOper() {
            this.detPlano = new ArrayList<>();
        }

        private String detOper_cnpjOper;
        private String detOper_regANS;
        private String detOper_vrPgTit;
        private List<DetPlano> detPlano;

        public String getDetOper_cnpjOper() {
            return null == detOper_cnpjOper ? "" : detOper_cnpjOper;
        }

        public void setDetOper_cnpjOper(String detOper_cnpjOper) {
            this.detOper_cnpjOper = detOper_cnpjOper;
        }

        public String getDetOper_regANS() {
            return null == detOper_regANS ? "" : detOper_regANS;
        }

        public void setDetOper_regANS(String detOper_regANS) {
            this.detOper_regANS = detOper_regANS;
        }

        public String getDetOper_vrPgTit() {
            return null == detOper_vrPgTit ? "" : detOper_vrPgTit;
        }

        public void setDetOper_vrPgTit(String detOper_vrPgTit) {
            this.detOper_vrPgTit = detOper_vrPgTit;
        }

        public List<DetPlano> getDetPlano() {
            return detPlano;
        }

        public void setDetPlano(List<DetPlano> detPlano) {
            this.detPlano = detPlano;
        }
    }

    public static class DetPlano {

        private String detPlano_tpDep;
        private String detPlano_cpfDep;
        private String detPlano_nmDep;
        private String detPlano_dtNascto;
        private String detPlano_vlrPgDep;

        public String getDetPlano_tpDep() {
            return null == detPlano_tpDep ? "" : detPlano_tpDep;
        }

        public void setDetPlano_tpDep(String detPlano_tpDep) {
            this.detPlano_tpDep = detPlano_tpDep;
        }

        public String getDetPlano_cpfDep() {
            return null == detPlano_cpfDep ? "" : detPlano_cpfDep;
        }

        public void setDetPlano_cpfDep(String detPlano_cpfDep) {
            this.detPlano_cpfDep = detPlano_cpfDep;
        }

        public String getDetPlano_nmDep() {
            return null == detPlano_nmDep ? "" : detPlano_nmDep;
        }

        public void setDetPlano_nmDep(String detPlano_nmDep) {
            this.detPlano_nmDep = detPlano_nmDep;
        }

        public String getDetPlano_dtNascto() {
            return null == detPlano_dtNascto ? "" : detPlano_dtNascto;
        }

        public void setDetPlano_dtNascto(String detPlano_dtNascto) {
            this.detPlano_dtNascto = detPlano_dtNascto;
        }

        public String getDetPlano_vlrPgDep() {
            return null == detPlano_vlrPgDep ? "" : detPlano_vlrPgDep;
        }

        public void setDetPlano_vlrPgDep(String detPlano_vlrPgDep) {
            this.detPlano_vlrPgDep = detPlano_vlrPgDep;
        }
    }

    public List<DmDev> getDmDev() {
        return dmDev;
    }

    public void setDmDev(List<DmDev> dmDev) {
        this.dmDev = dmDev;
    }

    public List<RemunOutrEmpr> getRemunOutrEmpr() {
        return remunOutrEmpr;
    }

    public void setRemunOutrEmpr(List<RemunOutrEmpr> remunOutrEmpr) {
        this.remunOutrEmpr = remunOutrEmpr;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtRemun_Id() {
        return null == evtRemun_Id ? "" : evtRemun_Id;
    }

    public void setEvtRemun_Id(String evtRemun_Id) {
        this.evtRemun_Id = evtRemun_Id;
    }

    public String getIdeEvento_indRetif() {
        return null == ideEvento_indRetif ? "" : ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return null == ideEvento_nrRecibo ? "" : ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_indApuracao() {
        return null == ideEvento_indApuracao ? "" : ideEvento_indApuracao;
    }

    public void setIdeEvento_indApuracao(String ideEvento_indApuracao) {
        this.ideEvento_indApuracao = ideEvento_indApuracao;
    }

    public String getIdeEvento_perApur() {
        return null == ideEvento_perApur ? "" : ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeTrabalhador_cpfTrab() {
        return null == ideTrabalhador_cpfTrab ? "" : ideTrabalhador_cpfTrab;
    }

    public void setIdeTrabalhador_cpfTrab(String ideTrabalhador_cpfTrab) {
        this.ideTrabalhador_cpfTrab = ideTrabalhador_cpfTrab;
    }

    public String getInfoInterm_qtdDiasInterm() {
        return null == infoInterm_qtdDiasInterm ? "" : infoInterm_qtdDiasInterm;
    }

    public void setInfoInterm_qtdDiasInterm(String infoInterm_qtdDiasInterm) {
        this.infoInterm_qtdDiasInterm = infoInterm_qtdDiasInterm;
    }

    public String getIdeTrabalhador_nisTrab() {
        return null == ideTrabalhador_nisTrab ? "" : ideTrabalhador_nisTrab;
    }

    public void setIdeTrabalhador_nisTrab(String ideTrabalhador_nisTrab) {
        this.ideTrabalhador_nisTrab = ideTrabalhador_nisTrab;
    }

    public String getIdeTrabalhador_infoMV() {
        return null == ideTrabalhador_infoMV ? "" : ideTrabalhador_infoMV;
    }

    public void setIdeTrabalhador_infoMV(String ideTrabalhador_infoMV) {
        this.ideTrabalhador_infoMV = ideTrabalhador_infoMV;
    }

    public String getInfoMV_indMV() {
        return null == infoMV_indMV ? "" : infoMV_indMV;
    }

    public void setInfoMV_indMV(String infoMV_indMV) {
        this.infoMV_indMV = infoMV_indMV;
    }

    public String getInfoMV_remunOutrEmpr() {
        return null == infoMV_remunOutrEmpr ? "" : infoMV_remunOutrEmpr;
    }

    public void setInfoMV_remunOutrEmpr(String infoMV_remunOutrEmpr) {
        this.infoMV_remunOutrEmpr = infoMV_remunOutrEmpr;
    }

    public String getIdeTrabalhador_infoComplem() {
        return null == ideTrabalhador_infoComplem ? "" : ideTrabalhador_infoComplem;
    }

    public void setIdeTrabalhador_infoComplem(String ideTrabalhador_infoComplem) {
        this.ideTrabalhador_infoComplem = ideTrabalhador_infoComplem;
    }

    public String getInfoComplem_nmTrab() {
        return null == infoComplem_nmTrab ? "" : infoComplem_nmTrab;
    }

    public void setInfoComplem_nmTrab(String infoComplem_nmTrab) {
        this.infoComplem_nmTrab = infoComplem_nmTrab;
    }

    public String getInfoComplem_dtNascto() {
        return null == infoComplem_dtNascto ? "" : infoComplem_dtNascto;
    }

    public void setInfoComplem_dtNascto(String infoComplem_dtNascto) {
        this.infoComplem_dtNascto = infoComplem_dtNascto;
    }

    public String getInfoComplem_sucessaoVinc() {
        return null == infoComplem_sucessaoVinc ? "" : infoComplem_sucessaoVinc;
    }

    public void setInfoComplem_sucessaoVinc(String infoComplem_sucessaoVinc) {
        this.infoComplem_sucessaoVinc = infoComplem_sucessaoVinc;
    }

    public String getSucessaoVinc_cnpjEmpregAnt() {
        return null == sucessaoVinc_cnpjEmpregAnt ? "" : sucessaoVinc_cnpjEmpregAnt;
    }

    public void setSucessaoVinc_cnpjEmpregAnt(String sucessaoVinc_cnpjEmpregAnt) {
        this.sucessaoVinc_cnpjEmpregAnt = sucessaoVinc_cnpjEmpregAnt;
    }

    public String getSucessaoVinc_matricAnt() {
        return null == sucessaoVinc_matricAnt ? "" : sucessaoVinc_matricAnt;
    }

    public void setSucessaoVinc_matricAnt(String sucessaoVinc_matricAnt) {
        this.sucessaoVinc_matricAnt = sucessaoVinc_matricAnt;
    }

    public String getSucessaoVinc_dtAdm() {
        return null == sucessaoVinc_dtAdm ? "" : sucessaoVinc_dtAdm;
    }

    public void setSucessaoVinc_dtAdm(String sucessaoVinc_dtAdm) {
        this.sucessaoVinc_dtAdm = sucessaoVinc_dtAdm;
    }

    public String getSucessaoVinc_observacao() {
        return null == sucessaoVinc_observacao ? "" : sucessaoVinc_observacao;
    }

    public void setSucessaoVinc_observacao(String sucessaoVinc_observacao) {
        this.sucessaoVinc_observacao = sucessaoVinc_observacao;
    }

    public String getIdeTrabalhador_procJudTrab() {
        return null == ideTrabalhador_procJudTrab ? "" : ideTrabalhador_procJudTrab;
    }

    public void setIdeTrabalhador_procJudTrab(String ideTrabalhador_procJudTrab) {
        this.ideTrabalhador_procJudTrab = ideTrabalhador_procJudTrab;
    }

    public String getProcJudTrab_tpTrib() {
        return null == procJudTrab_tpTrib ? "" : procJudTrab_tpTrib;
    }

    public void setProcJudTrab_tpTrib(String procJudTrab_tpTrib) {
        this.procJudTrab_tpTrib = procJudTrab_tpTrib;
    }

    public String getProcJudTrab_nrProcJud() {
        return null == procJudTrab_nrProcJud ? "" : procJudTrab_nrProcJud;
    }

    public void setProcJudTrab_nrProcJud(String procJudTrab_nrProcJud) {
        this.procJudTrab_nrProcJud = procJudTrab_nrProcJud;
    }

    public String getProcJudTrab_codSusp() {
        return null == procJudTrab_codSusp ? "" : procJudTrab_codSusp;
    }

    public void setProcJudTrab_codSusp(String procJudTrab_codSusp) {
        this.procJudTrab_codSusp = procJudTrab_codSusp;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 29 * hash + Objects.hashCode(this.ideTrabalhador_cpfTrab);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final S1200 other = (S1200) obj;
        if (!Objects.equals(this.ideTrabalhador_cpfTrab, other.ideTrabalhador_cpfTrab)) {
            return false;
        }
        return true;
    }

    public String getIdeTrabalhador_matr() {
        return ideTrabalhador_matr;
    }

    public void setIdeTrabalhador_matr(String ideTrabalhador_matr) {
        this.ideTrabalhador_matr = ideTrabalhador_matr;
    }

    public String getTipoEnvio() {
        return tipoEnvio;
    }

    public void setTipoEnvio(String tipoEnvio) {
        this.tipoEnvio = tipoEnvio;
    }

    public String getIdeTrabalhador_codCateg() {
        return ideTrabalhador_codCateg;
    }

    public void setIdeTrabalhador_codCateg(String ideTrabalhador_codCateg) {
        this.ideTrabalhador_codCateg = ideTrabalhador_codCateg;
    }

    public String getCBO() {
        return CBO;
    }

    public void setCBO(String CBO) {
        this.CBO = CBO;
    }    
    
}

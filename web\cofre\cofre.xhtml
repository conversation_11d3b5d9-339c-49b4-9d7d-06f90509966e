<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/cofres.css" rel="stylesheet" />
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 4000px) and (min-width: 700px) {
                    [id*="tabelaMov"] thead tr th,
                    [id*="tabelaMov"] thead tr th span{
                        color: #FFF !important;
                    }
                }

                [id*="tabelaMov"] thead tr th{
                    background-color: rgb(60, 141, 188) !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{cofre.Persistencias(login.pp, login.satellite)}"/>
            </f:metadata>
            <p:growl id="msgs"/>

            <header>
                <h:form id="cabecalho">
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row cabecalho">
                            <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                <img src="../assets/img/icones_satmob_cofre.png" height="40" style="margin-top:-6px !important" />
                                <label class="TituloPagina">#{localemsgs.CofreInteligente}</label>
                                <label class="TituloDataHora">
                                    <h:outputText value="#{localemsgs.Data}: "/>
                                    <span><h:outputText id="diaCofre" value="#{cofre.data}" converter="conversorDia"/></span>
                                </label>
                            </div>

                            <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                <p:panel rendered="#{cofre.filiais != null}">
                                    <label class="FilialNome">#{cofre.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{cofre.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{cofre.filiais.bairro}, #{cofre.filiais.cidade}/#{cofre.filiais.UF}</label>
                                </p:panel>
                            </div>

                            <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6">
                                <p:commandLink action="#{cofre.dataAnteriorCofre}" update="main cabecalho">
                                    <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                                </p:commandLink>

                                <p:datePicker id="calendario" value="#{cofre.calendario}" readonlyInput="true"
                                              pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                              converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{cofre.selecionarData}" update="main cabecalho" />
                                </p:datePicker>

                                <p:commandLink action="#{cofre.dataPosteriorCofre}" update="main cabecalho">
                                    <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                                </p:commandLink>
                            </div>

                            <div id="divBotaoVoltar2" class="col-md-1 col-sm-1 col-xs-6">
                                <p:commandLink title="#{localemsgs.Dashboard}" action="dashboard_geral.xhtml?faces-redirect=true"
                                               rendered="#{!cofre.exibirTodos}">
                                    <p:graphicImage url="../assets/img/icone_dashboard.png" height="40"/>
                                </p:commandLink>
                                <p:commandLink title="#{localemsgs.Voltar}" action="#{login.logOutRH}" rendered="#{!cofre.exibirTodos}">
                                    <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                                </p:commandLink>
                                <p:commandLink title="#{localemsgs.Voltar}" rendered="#{cofre.exibirTodos}"
                                               onclick="window.history.back();" action="#">
                                    <p:graphicImage url="../assets/img/icone_home_branco.png" height="40"/>
                                </p:commandLink>
                            </div>
                        </div>
                    </div>
                </h:form>
            </header>

            <h:form id="main">

                <p:confirmDialog global="true">
                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                </p:confirmDialog>

                <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow-y:auto !important;">
                    <div class="ui-grid-row">
                        <div class="ui-grid-col-12" style="padding-right: 14px !important">
                            <p:panel id="painelPesquisa">
                                <p:accordionPanel styleClass="painelCadastro" activeIndex="1" style="margin-bottom: 10px;">
                                    <p:tab title="#{localemsgs.Pesquisar}">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6 pesquisa,ui-grid-col-6 pesquisa"
                                                     layout="grid">
                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaRota" value="#{localemsgs.NomeCliente}: "/>
                                                <p:inputText id="pesquisaRota" value="#{cofre.filtroNomeCofre}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaRota" value="#{localemsgs.NomeCliente}"/>
                                                </p:inputText>
                                            </p:panelGrid>
                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaVeiculo" value="#{localemsgs.NumeroCofre}: "/>
                                                <p:inputText id="pesquisaVeiculo" value="#{cofre.filtroNumeroCofre}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaVeiculo" value="#{localemsgs.NumeroCofre}"/>
                                                </p:inputText>
                                            </p:panelGrid>
                                        </p:panelGrid>
                                        <p:commandButton action="#{cofre.pesquisar}" update="msgs main:tabela main:painelPesquisa" styleClass="botao btn btn-primary" value="#{localemsgs.Pesquisar}">
                                        </p:commandButton>
                                        <p:commandButton action="#{cofre.limparPesquisa}" update="msgs main:tabela main:painelPesquisa" styleClass="botao btn btn-warning" value="#{localemsgs.LimparFiltros}" style="margin-left: 4px;">
                                        </p:commandButton>
                                        <p:commandButton action="#{cofre.abrirMovimentacoes(cequip)}" update="msgs main:tabela main:painelPesquisa" styleClass="botao btn btn-primary" value="#{localemsgs.buscaMovimentacao}">
                                        </p:commandButton>
                                    </p:tab>
                                </p:accordionPanel>
                            </p:panel>

                            <p:panel style="display: inline;">
                                <p:dataGrid  id="tabela" value="#{cofre.cofres}" paginator="true" rows="50" lazy="true"
                                             rowsPerPageTemplate="10,20,30,40,50,100" layout="grid"
                                             currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Cofres}"
                                             paginatorTemplate="{PreviousPageLink} {CurrentPageReport}
                                             {RowsPerPageDropdown} {NextPageLink}"
                                             var="cequip" columns="2" emptyMessage="#{localemsgs.SemRegistros}">
                                    <p:panel style="background-color: #F0F0F0; border: 1px solid #DDD !important;
                                             border-radius: 6px; padding: 0px 0px 15px 0px !important; box-shadow:1px 1px 5px #CCC">
                                        <f:facet name="header">

                                            <p:panelGrid columns="2" columnClasses="ui-g-12 ui-md-6 ui-lg-6,ui-g-12 ui-md-6 ui-lg-6"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="width: 100%;">
                                                <p:column>
                                                    <i class="fa fa-lock" style="font-size: 18px;color:#3479A3 !important; margin-left:0px !important;margin-right:6px !important"></i>
                                                    <h:outputText value="#{localemsgs.Cofre} " style="font-size: 18px; color:#3479A3 !important;"/>
                                                    <h:outputText value="#{cequip.clientes.codCofre}"
                                                                  title="#{cequip.tescofresres.codCofre}" style="font-size: 18px;color: #F00 !important;
                                                                  font-weight:500 !important;">
                                                        <f:convertNumber pattern="0000"/>
                                                    </h:outputText>
                                                </p:column>

                                                <p:column>
                                                    <p:panel  style="background-color: transparent; white-space: nowrap !important; float:right !important;">
                                                        <div style="padding-bottom: 10px; display:inline;">
                                                            <p:commandLink title="#{localemsgs.MovimentacaoDiaria}"
                                                                           update="msgs" actionListener="#{cofre.detalharMovimentacao(cequip)}">
                                                                <p:graphicImage url="../assets/img/icone_historico.png" height="40"/>
                                                            </p:commandLink>
                                                        </div>
                                                        <div style="padding-bottom: 10px; display:inline;">
                                                            <p:commandLink title="#{localemsgs.Status}" update="msgs" rendered="#{login.pp.empresa eq 'SATGETLOCK'}"
                                                                           actionListener="#{cofre.abrirStatus(cequip)}">
                                                                <p:graphicImage url="../assets/img/icone_status_cofre.png" height="40"/>
                                                            </p:commandLink>
                                                        </div>
                                                        <div style="padding-bottom: 10px;display:inline;">
                                                            <p:commandLink title="#{localemsgs.Movimentacoes}" update="msgs movimentos"
                                                                           actionListener="#{cofre.abrirMovimentacoes(cequip)}">
                                                                <p:graphicImage url="../assets/img/icone_info_cofre.png" height="40"/>
                                                            </p:commandLink>
                                                        </div>
                                                        <div style="padding-bottom: 10px;display:inline;">
                                                            <p:commandLink title="#{localemsgs.Usuarios}" update="msgs formUsuarios"
                                                                           actionListener="#{cofre.abrirUsuarios(cequip)}">
                                                                <p:graphicImage url="../assets/img/icone_usuarios_redondo.png" height="40"/>
                                                            </p:commandLink>
                                                        </div>
                                                        <div style="padding-bottom: 10px;display:inline;">
                                                            <p:commandLink title="#{localemsgs.Relatorios}" update="msgs formRelatorio"
                                                                           actionListener="#{cofre.abrirRelatorios(cequip)}">
                                                                <p:graphicImage url="../assets/img/icone_notas_fiscais.png" height="40"/>
                                                            </p:commandLink>
                                                        </div>
                                                    </p:panel>
                                                </p:column>
                                            </p:panelGrid>
                                        </f:facet>

                                        <p:panelGrid id="divPaiGride" columns="2" columnClasses="ui-g-9 ui-md-9 ui-lg-10,ui-g-3 ui-md-3 ui-lg-2"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="position:relative !important;">
                                            <p:column>
                                                <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                                             layout="grid" styleClass="ui-panelgrid-blank" style="padding:8px 10px 8px 15px !important; font-size:10pt !important;font-family:'Open Sans', sans-serif !important;">
                                                    <p:column>
                                                        <h:outputText value="#{cequip.clientes.NRed}" title="#{cequip.clientes.NRed}" styleClass="negrito"/>
                                                    </p:column>

                                                    <p:column>
                                                        <h:outputText value="#{cequip.clientes.ende}, #{cequip.clientes.bairro}"
                                                                      title="#{cequip.clientes.ende}, #{cequip.clientes.bairro}" style="font-size: 8pt !important; display: inline-block;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical; width: 300px !important;max-width: 300px !important;"/>
                                                    </p:column>

                                                    <p:column>
                                                        <h:outputText value="#{cequip.clientes.CEP}" title="#{cequip.clientes.CEP}" converter="conversorCEP" styleClass="fonte-menor"/>
                                                        <h:outputText value=" #{cequip.clientes.cidade}/#{cequip.clientes.estado}"
                                                                      title="#{cequip.clientes.cidade}/#{cequip.clientes.estado}" style="font-size: 8pt !important;white-space:nowrap !important; overflow:hidden !important; text-overflow:ellipsis !important; width: 300px !important;"/>
                                                    </p:column>
                                                    <p:column>
                                                        <h:outputText value="#{cequip.mobileHW.status}"
                                                                      title="#{cequip.mobileHW.status}" styleClass="negrito"/>
                                                    </p:column>
                                                </p:panelGrid>

                                                <p:panelGrid id="divGride" columns="2" columnClasses="ui-grid-col-6, ui-grid-col-6"
                                                             layout="grid" styleClass="ui-panelgrid-blank" 
                                                             style="padding:10px 15px 10px 15px !important; font-size:10pt !important;font-family:'Open Sans', sans-serif !important;
                                                             background-color: #FFF !important; border-radius: 5px; border:thin solid #DDD !important; box-shadow:2px 2px 3px #DDD;
                                                             margin-left:15px !important; width:calc(100% - 30px) !important; position:absolute;">
                                                    <h:outputText value="#{localemsgs.SaldoCofre}: " class="Labels" style="float:right"/>
                                                    <h:outputText value="#{cequip.tescofresres.saldoFisTotal}" title="#{cequip.tescofresres.saldoFisTotal}" converter="conversormoeda"
                                                                  styleClass="negrito azul"/>

                                                    <h:outputText value="#{localemsgs.CreditoDia}: " class="Labels" style="float:right;"/>
                                                    <h:outputText value="#{cequip.tescofresres.vlrTotalCred}"
                                                                  title="#{cequip.tescofresres.vlrTotalCred}" converter="conversormoeda"
                                                                  style="#{cequip.tescofresres.vlrTotalCred.toPlainString().contains('-') ? 'color:red':''}" />

                                                    <h:outputText value="#{localemsgs.DepositosAte16}: " class="Labels" style="float:right"/>
                                                    <h:outputText value="#{cequip.tescofresres.credD0}"
                                                                  title="#{cequip.tescofresres.credD0}" converter="conversormoeda"
                                                                  style="#{cequip.tescofresres.credD0.contains('-') ? 'color:red':''}"/>

                                                    <h:outputText value="#{localemsgs.DepositosApos16}: " class="Labels" style="float:right"/>
                                                    <h:outputText value="#{cequip.tescofresres.credD1}"
                                                                  title="#{cequip.tescofresres.credD1}" converter="conversormoeda"
                                                                  style="#{cequip.tescofresres.credD1.contains('-') ? 'color:red':''}"/>

                                                    <h:outputText value="#{localemsgs.ValorRecolhido}: " class="Labels" style="float:right"/>
                                                    <h:outputText value="#{cequip.tescofresres.vlrTotalRec}" title="#{cequip.tescofresres.vlrTotalRec}" converter="conversormoeda"
                                                                  style="#{cequip.tescofresres.vlrTotalRec.toPlainString().contains('-') ? 'color:red':''}"/>
                                                </p:panelGrid>
                                            </p:column>
                                        </p:panelGrid>
                                        <script type="text/javascript">
                                            // <![CDATA[
                                            if ($(document).width() <= 700)
                                                $('.FundoPagina').css('max-height', ($(document).height() - 199) + 'px').css('height', ($(document).height() - 199) + 'px');
                                            else
                                                $('.FundoPagina').css('max-height', ($(document).height() - 119) + 'px').css('height', ($(document).height() - 119) + 'px');

                                            $(window).resize(function () {
                                                if ($(document).width() <= 700)
                                                    $('.FundoPagina').css('max-height', ($(document).height() - 199) + 'px').css('height', ($(document).height() - 199) + 'px');
                                                else
                                                    $('.FundoPagina').css('max-height', ($(document).height() - 119) + 'px').css('height', ($(document).height() - 119) + 'px');
                                            });

                                            // ]]>
                                        </script>
                                    </p:panel>
                                </p:dataGrid>
                            </p:panel>
                        </div>
                    </div>
                </div>
            </h:form>

            <h:form id="formRelatorio">
                <p:dialog widgetVar="dlgRelatorio" positionType="absolute"  responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dialogo">
                    <f:facet name="header">
                        <label style="position:absolute !important;">
                            <img src="../assets/img/icone_usuarios.png" height="40" width="40" style="margin:0px !important" />
                            <h:outputText value="#{localemsgs.Relatorios}" style="color:#3C8DBC; margin:0px !important;" />
                        </label>
                    </f:facet>
                    <p:panel id="cadastrar" styleClass="cadastrar2">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px 0px 5px 0px !important; padding-right:0px !important;text-align:center !important">
                            <label style="background-color:#000; color:#FFF; border-radius:20px; padding:1px 8px 1px 8px !important; box-shadow:2px 2px 3px #CCC">
                                <h:outputText value="#{localemsgs.Cofre}: " class="negrito fonte-menor"/>
                                <h:outputText value="#{cofre.cofreSelecionado.clientes.codCofre} "
                                              style="font-weight: bold">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                                <h:outputText value="#{cofre.cofreSelecionado.clientes.codigo}
                                              #{cofre.cofreSelecionado.clientes.NRed}" style="font-weight: bold"/>
                            </label>
                        </div>

                        <p:panelGrid columns="4" layout="grid" styleClass="ui-panelgrid-blank"
                                     columnClasses="ui-lg-2 ui-md-2 ui-g-4 colunaDialogoDireita, ui-lg-4 ui-md-4 ui-g-8 colunaDialogoEsquerda, ui-lg-2 ui-md-2 ui-g-4 colunaDialogoDireita, ui-lg-4 ui-md-4 ui-g-8 colunaDialogoEsquerda"
                                     style="padding-left:10px !important; padding-top:6px !important">

                            <h:outputText value="#{localemsgs.IMEI}: " class="negrito fonte-menor"/>
                            <h:outputText value="#{cofre.infoCofre.IMEI}"/>

                            <h:outputText value="#{localemsgs.Tipo}: " class="negrito fonte-menor"/>
                            <h:outputText value="#{cofre.infoCofre.tipoEquip}"/>

                            <h:outputText value="#{localemsgs.Marca}: " class="negrito fonte-menor"/>
                            <h:outputText value="#{cofre.infoCofre.marcaEquip}"/>

                            <h:outputText value="#{localemsgs.Serial}: " class="negrito fonte-menor"/>
                            <h:outputText value="#{cofre.infoCofre.serialEquip}"/>
                        </p:panelGrid>

                        <p:panelGrid columns="3" layout="grid" styleClass="ui-panelgrid-blank"
                                     columnClasses="ui-lg-2 ui-md-2 ui-g-4 colunaDialogoDireita, ui-lg-10 ui-md-10 ui-g-8 colunaDialogoEsquerda"
                                     style="padding-left:10px !important; padding-top:6px !important; padding-right:0px !important">

                            <h:outputText value="#{localemsgs.Relatorio}:"  class="negrito fonte-menor" />
                            <p:panel style="width:100%; padding-right:0px !important; background-color:transparent !important">
                                <p:selectOneMenu id="tipoRelatorio" value="#{cofre.tipoRelatorio}"
                                                 required="true" label="#{localemsgs.Nivel}"
                                                 style="width: 100% !important; background-color:#FFF !important; float:left"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Relatorio}">
                                    <p:ajax event="itemSelect" listener="#{cofre.carregarInformacoesRelatorios}" update="cadastrar msgs"/>
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{cofre.tiposRelatorios}"/>
                                </p:selectOneMenu>
                            </p:panel>
                        </p:panelGrid>

                        <p:panelGrid columns="4" layout="grid" styleClass="ui-panelgrid-blank"
                                     columnClasses="ui-lg-2 ui-md-2 ui-g-4 colunaDialogoDireita, ui-lg-4 ui-md-4 ui-g-8 colunaDialogoEsquerda, ui-lg-2 ui-md-2 ui-g-4 colunaDialogoDireita, ui-lg-4 ui-md-4 ui-g-8 colunaDialogoEsquerda"
                                     style="padding-left:10px !important; padding-top:6px !important">

                            <h:outputText value="#{localemsgs.DataInicial}: " class="negrito fonte-menor"/>
                            <p:datePicker value="#{cofre.calendario1}" readonlyInput="true"
                                          pattern="#{mascaras.padraoData}"
                                          converter="conversorDate" locale="#{localeController.getCurrentLocale()}" >
                                <p:ajax event="dateSelect" listener="#{cofre.carregarInformacoesRelatorios}" update="cadastrar msgs"/>
                            </p:datePicker>

                            <h:outputText value="#{localemsgs.DataFinal}: " class="negrito fonte-menor" rendered="#{cofre.relatorioDuasDatas}"/>
                            <p:datePicker value="#{cofre.calendario2}" readonlyInput="true" rendered="#{cofre.relatorioDuasDatas}"
                                          pattern="#{mascaras.padraoData}"
                                          converter="conversorDate" locale="#{localeController.getCurrentLocale()}" >
                                <p:ajax event="dateSelect" listener="#{cofre.carregarInformacoesRelatorios}" update="cadastrar msgs" />
                            </p:datePicker>

                        </p:panelGrid>

                        <p:panelGrid columns="4" layout="grid" styleClass="ui-panelgrid-blank"
                                     columnClasses="ui-lg-2 ui-md-2 ui-g-4 colunaDialogoDireita, ui-lg-4 ui-md-4 ui-g-8 colunaDialogoEsquerda, ui-lg-2 ui-md-2 ui-g-4 colunaDialogoDireita, ui-lg-4 ui-md-4 ui-g-8 colunaDialogoEsquerda"
                                     style="padding-left:10px !important; padding-top:6px !important">

                            <h:outputText value="#{localemsgs.HoraInicio}: " class="negrito fonte-menor" rendered="#{cofre.relatorioDuasDatas}"/>
                            <p:inputMask value="#{cofre.hora1}" immediate="true" rendered="#{cofre.relatorioDuasDatas}"
                                         converter="conversorHora" mask="#{mascaras.mascaraHora}" >
                                <p:ajax partialSubmit="true" process="@this"  />
                            </p:inputMask>

                            <h:outputText value="#{localemsgs.HoraFim}: " class="negrito fonte-menor" rendered="#{cofre.relatorioDuasDatas}"/>
                            <p:inputMask value="#{cofre.hora2}" rendered="#{cofre.relatorioDuasDatas}"
                                         converter="conversorHora" mask="#{mascaras.mascaraHora}" >
                            </p:inputMask>


                            <h:outputText value="#{localemsgs.Operador}:"  class="negrito fonte-menor" rendered="#{!cofre.listaPessoa.isEmpty()}"/>
                            <p:selectOneMenu id="operador" value="#{cofre.pessoa}" converter="omnifaces.SelectItemsConverter"
                                             label="#{localemsgs.Operador}" rendered="#{!cofre.listaPessoa.isEmpty()}"
                                             style="width: 100%; background-color:#FFF !important">
                                <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                <f:selectItems value="#{cofre.listaPessoa}" var="pp"
                                               itemLabel="#{pp.nome} - #{localemsgs.Movimentacoes}: #{pp.obs}"/>
                            </p:selectOneMenu>

                            <h:outputText value="#{localemsgs.Deposito}:"  class="negrito fonte-menor" rendered="#{!cofre.depositosRelatorio.isEmpty()}"/>
                            <p:selectOneMenu id="deposito" value="#{cofre.depositoRelatorio}" converter="omnifaces.SelectItemsConverter"
                                             label="#{localemsgs.Deposito}" rendered="#{!cofre.depositosRelatorio.isEmpty()}"
                                             style="width: 100%; background-color:#FFF !important" var="dpt">
                                <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                <p:column>
                                    <h:outputText value="#{dpt.hrInicio}" converter="conversorHora"/>
                                    <h:outputText value=", #{dpt.operador}: " />
                                    <h:outputText value="#{dpt.valor}" converter="conversormoeda"/>
                                </p:column>
                                <f:selectItems value="#{cofre.depositosRelatorio}" var="dpt"
                                               itemValue="#{dpt}"  itemLabel="#{dpt.hrInicio}, #{dpt.operador}: #{dpt.valor}"/>
                            </p:selectOneMenu>

                            <h:outputText value="#{localemsgs.Coleta}:"  class="negrito fonte-menor" rendered="#{!cofre.coletasRelatorio.isEmpty()}"/>
                            <p:selectOneMenu id="coleta" value="#{cofre.coletaRelatorio}" converter="omnifaces.SelectItemsConverter"
                                             label="#{localemsgs.Coleta}" rendered="#{!cofre.coletasRelatorio.isEmpty()}"
                                             style="width: 100%; background-color:#FFF !important" var="clt">
                                <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                <p:column>
                                    <h:outputText value="#{clt.hrInicio}" converter="conversorHora"/>
                                    <h:outputText value=", #{clt.operador}: " rendered="#{clt.operador ne ''}"/>
                                    <h:outputText value=": " rendered="#{clt.operador eq ''}"/>
                                    <h:outputText value="#{clt.totalDN}" converter="conversormoeda"/>
                                </p:column>
                                <f:selectItems value="#{cofre.coletasRelatorio}" var="cl"
                                               itemLabel="#{cl.hrInicio}, #{cl.operador}: #{cl.totalDN}"/>
                            </p:selectOneMenu>
                        </p:panelGrid>

                        <p:panelGrid columns="3" layout="grid" styleClass="ui-panelgrid-blank"
                                     columnClasses="ui-lg-4 ui-md-4 ui-g-4 texto-centro,ui-lg-4 ui-md-4 ui-g-4 texto-centro, ui-lg-4 ui-md-4 ui-g-4 texto-centro"
                                     style="padding-left:10px !important; padding-top:6px !important">
                            <p:commandButton value="#{localemsgs.GerarRelatorio}"
                                             update="msgs cadastrar" action="#{cofre.carregarRelatorio}"/>

                            <p:commandButton title="#{localemsgs.DownloadPDF}" update="msgs" value="#{localemsgs.DownloadPDF}"
                                             ajax="false" actionListener="#{cofre.gerarRelatorioDownloadPDF}">
                                <p:fileDownload value="#{cofre.arquivoRelatorio}" />
                            </p:commandButton>

                            <p:commandButton title="#{localemsgs.DownloadCSV}" update="msgs" value="#{localemsgs.DownloadCSV}"
                                             ajax="false" actionListener="#{cofre.gerarRelatorioDownloadCSV}">
                                <p:fileDownload value="#{cofre.arquivoRelatorio}" />
                            </p:commandButton>
                        </p:panelGrid>

                        <p:scrollPanel mode="native" styleClass="scrollPanelDialogo" style="height: 235px !Important;">
                            <h:outputText value="#{cofre.relatorio}" escape="false"/>
                        </p:scrollPanel>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="formUsuarios">
                <p:dialog widgetVar="dlgListarUsuarios" positionType="absolute"  responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="border:thin solid #666 !important; max-height:570px !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <label style="position:absolute !important;">
                            <img src="../assets/img/icone_usuarios.png" height="40" width="40" style="margin:0px !important" />
                            <h:outputText value="#{localemsgs.Usuarios}" style="color:#3C8DBC; margin:0px !important;" />
                        </label>
                        <p:commandLink title="#{localemsgs.Adicionar}" id="adicionarFilial"
                                       oncomplete="PF('dlgCadastrarUsuario').show()" actionListener="#{cofre.novoUsuario}"
                                       update="msgs formCadastrarUsuario" style="position:absolute; left:160px; top:10px !important;">
                            <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40" />
                        </p:commandLink>

                    </f:facet>

                    <p:panel id="cadastrar" style="background-color: #EEE!important; padding:0px !important; margin:10px 0px 0px 0px !important" styleClass="cadastrar2">
                        <p:panel id="tabelaFiliais" style="background-color: #EEE!important; padding:0px !important;width:100% !important; margin:0px !important">
                            <p:scrollPanel mode="native" style="width:100%; height:310px; background-color:#EEE !important; padding:0px !important; width:100% !important; margin:0px !important; border:thin solid #CCC !important;">
                                <p:dataGrid id="tabelaUsuarios" value="#{cofre.usuarios}"
                                            emptyMessage="#{localemsgs.SemRegistros}" var="lista"
                                            styleClass="tabela" style="font-size: 12px; background-color: #EEE !important; padding:0px !important;width:100% !important"
                                            columns="1">
                                    <p:panel style="background-color:#3C8DBC!important; width:100% !important;position:relative">
                                        <f:facet name="header">
                                            <label style="position:absolute; height:25px !important; top:0px !important">
                                                <i class="fas fa-user" style="color:#FFF; float:left"></i>
                                                <h:outputText class="Nomeusuario" value="#{lista.pessoa.nome}" style="color:#FFF !important; float:left; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; text-align:left !important; padding:0px 0px 0px 12px !important;"/>
                                            </label>
                                        </f:facet>
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="padding-left:10px !important; padding-top:6px !important">
                                            <p:column>
                                                <h:outputText value="#{localemsgs.CodigoLogin}: "/>
                                                <h:outputText value="#{lista.pessoa.codigo}" title="#{lista.pessoa.codigo}"
                                                              converter="conversorCodFil" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Email}: "/>
                                                <h:outputText value="#{lista.pessoa.email}" title="#{lista.pessoa.email}" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Nivel}: "/>
                                                <h:outputText value="#{lista.saspw.nivelOP}" title="#{lista.saspw.nivelOP}" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Grupo}: "/>
                                                <h:outputText value="#{lista.grupo.descricao}" title="#{lista.grupo.descricao}" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Situacao}: "/>
                                                <h:outputText value="#{lista.saspw.situacao}" title="#{lista.saspw.situacao}" class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Descricao}: "/>
                                                <h:outputText value="#{lista.saspw.descricao}" title="#{lista.saspw.descricao}" class="negrito-normal"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-g-6 ui-md-3 ui-lg-2,ui-g-6 ui-md-9 ui-lg-10"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{cofre.abrirUsuario(lista)}"
                                                           update="formCadastrarUsuario msgs">
                                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                            </p:commandLink>

                                            <p:commandLink title="#{localemsgs.Remover}" actionListener="#{cofre.excluir(lista)}"
                                                           update="tabelaUsuarios msgs" rendered="#{login.usuario.nome ne lista.pessoa.nome and login.nivel eq '9'}">
                                                <p:confirm header="#{localemsgs.Atencao}"
                                                           message="#{localemsgs.ConfirmaExclusao}"
                                                           icon="pi pi-exclamation-triangle" />
                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="30"/>
                                            </p:commandLink>
                                        </p:panelGrid>
                                    </p:panel>
                                </p:dataGrid>
                            </p:scrollPanel>
                        </p:panel>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form class="form-inline" id="formCadastrarUsuario">
                <p:dialog widgetVar="dlgCadastrarUsuario" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="border:thin solid #666 !important; max-height:575px !important; min-width:350px !important; max-width:96% !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 0px 0px !important; background-color:#EEE !important;">
                    <script>
                        $(document).ready(function () {
                            //first unbind the original click event
                            PF('dlgCadastrarUsuario').closeIcon.unbind('click');

                            //register your own
                            PF('dlgCadastrarUsuario').closeIcon.click(function (e) {
                                $("#formCadastrarUsuario\\:botaoFechar").click();
                                //should be always called
                                e.preventDefault();
                            });
                        });
                    </script>
                    <f:facet name="header">
                        <label style="color:#3C8DBC !important; position:absolute"><img src="../assets/img/icone_usuarios.png" height="40" width="40"/>&nbsp;&nbp;#{localemsgs.CadastrarUsuario}</label>
                    </f:facet>
                    <div style="background-color:#EEE !important;max-height: 405px !important; overflow-x:hidden !important;overflow-y:auto !important; padding:3px 0px 0px 8px !important; margin-top:6px!important">
                        <p:panel id="editar" styleClass="painelCadastro" style="background-color:#EEE !important; padding-right:0px !important;">
                            <p:commandButton widgetVar="botaoFechar" style="display: none;"
                                             oncomplete="PF('dlgCadastrarUsuario').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#EEE !important;">

                                <p:outputLabel for="pessoa" value="#{localemsgs.Nome}:" rendered="#{!cofre.cadastroNovaPessoa and cofre.flagUsuario eq 1}"/>
                                <p:autoComplete id="pessoa" value="#{cofre.usuario.pessoa}" completeMethod="#{cofre.listarQueryValida}"
                                                required="true" label="#{localemsgs.Pessoa}"
                                                rendered="#{!cofre.cadastroNovaPessoa and cofre.flagUsuario eq 1}"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Pessoa}"  scrollHeight="200"
                                                inputStyle="width: 100%" placeholder="#{localemsgs.Nome}" forceSelection="true" style="min-width:100% !important"
                                                var="ppl" itemLabel="#{ppl.nome}" itemValue="#{ppl}">
                                    <p:column>
                                        <i class="fas fa-plus" style="#{ppl.codigo eq '-1' ? 'display: inline; font-size: 10px;' : 'display: none'}"></i>
                                        <h:outputText value=" #{ppl.nome}" style="#{ppl.codigo eq '-1' ? 'font-weight: bold' : ''}"/>
                                    </p:column>
                                    <o:converter converterId="omnifaces.ListIndexConverter" list="#{cofre.listaPessoa}" />
                                    <p:ajax event="itemSelect" listener="#{cofre.selecionarPessoa}" update="msgs formCadastrarUsuario:editar"/>
                                </p:autoComplete>

                                <p:outputLabel for="nome" value="#{localemsgs.Nome}:" rendered="#{cofre.cadastroNovaPessoa or cofre.flagUsuario eq 2}"/>
                                <p:inputText value="#{cofre.usuario.pessoa.nome}" rendered="#{cofre.cadastroNovaPessoa or cofre.flagUsuario eq 2}" id="nome" style="min-width: 100%">
                                    <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                </p:inputText>

                                <p:outputLabel for="email" value="#{localemsgs.Email}:"/>
                                <p:inputText value="#{cofre.usuario.pessoa.email}" id="email" style="width: 100%">
                                    <p:watermark for="email" value="#{localemsgs.Email}"/>
                                </p:inputText>

                                <p:outputLabel for="cpf" value="#{localemsgs.CPF}:"/>
                                <p:inputMask id="cpf"  value="#{cofre.usuario.pessoa.CPF}" disabled="#{!cofre.cadastroNovaPessoa}"
                                             mask="#{mascaras.mascaraCPF}">
                                    <p:watermark for="cpf" value="#{localemsgs.CPF}"/>
                                </p:inputMask>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">

                                <p:outputLabel for="rg" value="#{localemsgs.RG}:"/>
                                <p:inputText value="#{cofre.usuario.pessoa.RG}" id="rg" style="width: 100%">
                                    <p:watermark for="rg" value="#{localemsgs.RG}"/>
                                </p:inputText>

                                <p:outputLabel for="RGOrg" value="#{localemsgs.RGOrg}:"/>
                                <p:inputText value="#{cofre.usuario.pessoa.RGOrgEmis}" id="RGOrg" style="width: 100%">
                                    <p:watermark for="RGOrg" value="#{localemsgs.RGOrg}"/>
                                </p:inputText>

                                <p:outputLabel for="situacaoPessoa" value="#{localemsgs.Tipo}:"/>
                                <p:selectOneMenu value="#{cofre.usuario.pessoa.situacao}" style="width: 100%; background-color:#FFF !important"
                                                 id="situacaoPessoa" >
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Candidato}" itemValue="C"/>
                                    <f:selectItem itemLabel="#{localemsgs.Prestador}" itemValue="P"/>
                                    <f:selectItem itemLabel="#{localemsgs.Autonomo}" itemValue="A"/>
                                    <f:selectItem itemLabel="#{localemsgs.Funcionario}" itemValue="F" itemDisabled="true"/>
                                    <f:selectItem itemLabel="#{localemsgs.Diretor}" itemValue="D"/>
                                    <f:selectItem itemLabel="#{localemsgs.Socio}" itemValue="S"/>
                                    <f:selectItem itemLabel="#{localemsgs.Visitante}" itemValue="V"/>
                                    <f:selectItem itemLabel="#{localemsgs.BBloqueado}" itemValue="B"/>
                                    <f:selectItem itemLabel="#{localemsgs.Visitanteweb}" itemValue="W" />
                                    <f:selectItem itemLabel="#{localemsgs.OOutros}" itemValue="O"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="nivel" value="#{localemsgs.Nivel}:"/>
                                <p:selectOneMenu value="#{cofre.usuario.saspw.nivelx}" id="nivel"
                                                 required="true" label="#{localemsgs.Nivel}" style="width: 100%; background-color:#FFF !important"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nivel}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{cofre.niveis}" />
                                </p:selectOneMenu>

                                <p:outputLabel for="grupo" value="#{localemsgs.Grupo}:"/>
                                <p:selectOneMenu value="#{cofre.usuario.grupo.codigo}" id="grupo" style="width: 100%; background-color:#FFF !important"
                                                 required="true" label="#{localemsgs.Grupo}" filter="true" filterMatchMode="contains"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Grupo}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItems value="#{cofre.grupos}" var="grupo" itemValue="#{grupo.codigo}"
                                                   itemLabel="#{grupo.descricao}" noSelectionValue="Selecione"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:" />
                                <p:selectOneMenu value="#{cofre.usuario.saspw.situacao}" id="situacao"
                                                 required="true" label="#{localemsgs.Situacao}" style="width: 100%; background-color:#FFF !important"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                    <f:selectItem itemLabel="#{localemsgs.Bloqueado}" itemValue="B"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}:"/>
                                <p:inputText id="descricao" value="#{cofre.usuario.saspw.descricao}"
                                             label="#{localemsgs.Descricao} " style="width: 100%">
                                    <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                </p:inputText>

                                <p:outputLabel for="senha" value="#{localemsgs.Senha}:"/>
                                <p:password id="senha" value="#{cofre.usuario.pessoa.PWWeb}" required="true" transient="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}" autocomplete="off"
                                            label="#{localemsgs.Senha}" feedback="true" redisplay="true" match="confirmacao"
                                            promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                            goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                            style="width: 100%">
                                    <f:validateRegex pattern="^[0-9]{5,20}$" for="senha"/>
                                    <p:watermark for="senha" value="#{localemsgs.Senha}"/>
                                </p:password>

                                <p:outputLabel for="confirmacao" value="#{localemsgs.Confirmacao}:" />
                                <p:password id="confirmacao" value="#{cofre.usuario.pessoa.PWWeb}" redisplay="true"
                                            label="#{localemsgs.Senha}" style="width: 100%">
                                    <p:watermark for="confirmacao" value="#{localemsgs.Senha}"/>
                                </p:password>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="motivo" value="#{localemsgs.Motivo}:"/>
                                <p:inputText id="motivo" value="#{cofre.usuario.saspw.motivo}"
                                             label="#{localemsgs.Motivo}" style="width: 100%">
                                    <p:watermark for="motivo" value="#{localemsgs.Motivo}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:commandButton action="#{cofre.cadastrar}" update="msgs formUsuarios:tabelaUsuarios" styleClass="botao btn btn-primary" style="width:calc(100% - 15px) !important; margin-top:10px;"
                                             value="#{localemsgs.Cadastrar}" rendered="#{cofre.flagUsuario eq 1}">
                            </p:commandButton>

                            <p:commandButton action="#{cofre.editar}" update="msgs formUsuarios:tabelaUsuarios" styleClass="botao btn btn-primary" style="width:calc(100% - 15px) !important; margin-top:10px;"
                                             value="#{localemsgs.Gravar}" rendered="#{cofre.flagUsuario eq 2}">
                            </p:commandButton>
                        </p:panel>
                    </div>
                </p:dialog>

                <p:dialog header="#{localemsgs.AdicionarFilial}"
                          widgetVar="dlgServicos" closable="true" resizable="false" width="400" height="50"
                          hideEffect="fade">
                    <p:outputPanel id="panelServicos"></p:outputPanel>
                </p:dialog>
            </h:form>

            <h:form id="movimentos">
                <p:dialog widgetVar="dlgListarMovimentos" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" focus="btPesquisarMov"
                          style="border:thin solid #666 !important; max-height:570px !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Movimentacoes}" style="color:#3C8DBC !important" />
                    </f:facet>

                    <p:panel id="cadastrar" style="background-color: transparent;padding:0px 0px 4px 0px!important; margin-top:-10px !important" styleClass="cadastrar">
                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank"  style="padding:0px !important">
                            <p:column style="padding:0px !important">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px 0px 5px 0px !important; padding-right:0px !important;text-align:center !important">
                                    <label style="background-color:#000; color:#FFF; border-radius:20px; padding:1px 8px 1px 8px !important; box-shadow:2px 2px 3px #CCC">
                                        <h:outputText value="#{localemsgs.Cofre}: " class="negrito fonte-menor"/>
                                        <h:outputText value="#{cofre.cofreSelecionado.clientes.codCofre} "
                                                      style="font-weight: bold">
                                            <f:convertNumber pattern="0000"/>
                                        </h:outputText>
                                        <h:outputText value="#{cofre.cofreSelecionado.clientes.codigo}
                                                      #{cofre.cofreSelecionado.clientes.NRed}" style="font-weight: bold"/>
                                    </label>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right">
                                    <h:outputText value="#{localemsgs.IMEI}: " class="negrito fonte-menor" rendered="#{cofre.infoCofre ne null}"/>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <h:outputText value="#{cofre.infoCofre.IMEI}" rendered="#{cofre.infoCofre ne null}"/>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right">
                                    <h:outputText value="#{localemsgs.Tipo}: " class="negrito fonte-menor" rendered="#{cofre.infoCofre ne null}"/>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important; text-align:left !important; white-space:nowrap !important">
                                    <h:outputText value="#{cofre.infoCofre.tipoEquip}" rendered="#{cofre.infoCofre ne null}"/>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right">
                                    <h:outputText value="#{localemsgs.Marca}: " class="negrito fonte-menor" rendered="#{cofre.infoCofre ne null}"/>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <h:outputText value="#{cofre.infoCofre.marcaEquip}" rendered="#{cofre.infoCofre ne null}"/>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right">
                                    <h:outputText value="#{localemsgs.Serial}: " class="negrito fonte-menor" rendered="#{cofre.infoCofre ne null}"/>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <h:outputText value="#{cofre.infoCofre.serialEquip}" rendered="#{cofre.infoCofre ne null}"/>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;padding-top:8px !important; text-align:right">
                                    <h:outputText value="#{localemsgs.DataInicial}: " class="negrito fonte-menor"/>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-7" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <p:datePicker value="#{cofre.calendario1}" readonlyInput="true"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario2"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right;padding-top:8px !important;">
                                    <h:outputText value="#{localemsgs.DataFinal}: " class="negrito fonte-menor"/>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-7" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <p:datePicker value="#{cofre.calendario2}" readonlyInput="true"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario2"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}" />

                                    <p:commandLink id="btPesquisarMov" title="#{localemsgs.Pesquisar}" update="tabelaMov msgs" action="#{cofre.listarMovimentacoes()}">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30" style="margin-left:6px;top:-3px !important"/>
                                    </p:commandLink>
                                </div>
                            </p:column>
                        </p:panelGrid>

                        <div style="background-color:#FFF !important; border:thin solid #DDD; box-shadow:2px 2px 3px #CCC; width:100% !important; overflow:auto !important; height:250px !important; padding:1px 0px 1px 1px !important;">
                            <p:dataTable id="tabelaMov" value="#{cofre.listaMovimentacao}" var="movimento"
                                         resizableColumns="true" styleClass="tabela" selectionMode="single" rowKey="#{movimento.tescofresres.data}"
                                         selection="#{cofre.movimentacaoSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                         scrollable="false"
                                         style="font-size: 12px; border-radius:4px !important; max-height:100% !important;
                                         width:2000px !important;">
                                <p:ajax event="rowDblselect" listener="#{cofre.detalharMovimentacao}" update="detalhesMovimentacao"/>
                                <p:columnGroup type="header">
                                    <p:row styleClass="agrupador">
                                        <p:column headerText="#{localemsgs.Historicos}" colspan="4" style="background-color:#3C8DBC !important;"/>
                                        <p:column headerText="#{localemsgs.ColetaAntesCorte}" colspan="2" style="background-color:#3C8DBC !important;"/>
                                        <p:column headerText="#{localemsgs.Credito}" colspan="3" style="background-color:#3C8DBC !important;"/>
                                        <p:column headerText="#{localemsgs.RecolhimentoDepoisCorte}" colspan="4" style="background-color:#3C8DBC !important;"/>
                                        <p:column headerText="#{localemsgs.NoCofre}" colspan="2" style="background-color:#3C8DBC !important;"/>
                                        <p:column headerText="#{localemsgs.ProximoDia}" style="background-color:#3C8DBC !important;"/>
                                        <p:column headerText="#{localemsgs.Custodia}" style="background-color:#3C8DBC !important;"/>
                                    </p:row>
                                    <p:row>
                                        <p:column headerText="#{localemsgs.Cofre}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;" />
                                        <p:column headerText="#{localemsgs.NomeCofre}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;" />
                                        <p:column headerText="#{localemsgs.Data}"  style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;" />
                                        <p:column headerText="#{localemsgs.DiaSemana}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;" />
                                        <p:column headerText="#{localemsgs.Feriado}"  style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.ValorRecD0}"  style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.HoraRecD0}"  style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.DepDiaAntAposCorte}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.ValorCorteD0}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.TotalCredDia}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.HrRecDia}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.ValorRecDia}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.ValorRecJaCreditado}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.ValorRecACreditar}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.SaldoCofreTotal}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.DepositoJaCreditado}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.DepositoProxDU}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.SaldoFisCst}" style="background-color:#CCC !important; color:#505050 !important; font-size:8pt !important;"/>
                                    </p:row>
                                </p:columnGroup>

                                <p:column headerText="#{localemsgs.Cofre}" exportable="#{cofre.cofre}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.codCofre}"
                                                  style="text-align:center !important; #{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                         ? 'color:blue' : 'color:black'}">
                                        <f:convertNumber pattern="0000"/>
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{localemsgs.NomeCofre}" exportable="#{cofre.cofreSelecionado.clientes.NRed}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.NRed}"
                                                  style="text-align:center !important; #{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                         ? 'color:blue' : 'color:black'}">
                                        <f:convertNumber pattern="0000"/>
                                    </h:outputText>
                                </p:column>                                                                
                                <p:column headerText="#{localemsgs.Data}" exportable="#{cofre.data}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.dataStr}" converter="conversorData"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DiaSemana}" exportable="#{cofre.diaSeman}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.diaSemT}"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Feriado}" exportable="#{cofre.feriado}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.feriado}"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorRecD0}" styleClass="celula-right" exportable="#{cofre.valorCorteD0}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.vlrDep}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HoraRecD0}" exportable="#{cofre.horaRecD0}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.hrRecDepD0}" converter="conversorHora"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                           ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DepDiaAntAposCorte}" styleClass="celula-right" exportable="#{cofre.depDiaAntAposCorte}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.vlrCredRecD0}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorCorteD0}" styleClass="celula-right" exportable="#{cofre.valorCorteD0}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.vlrCredCorteD0}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TotalCredDia}" styleClass="celula-right" exportable="#{cofre.totalCredDia}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.vlrTotalCred}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                           ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HrRecDia}" exportable="#{cofre.hrRecDia}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.hrRecApos}" converter="conversorHora"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorRecDia}" styleClass="celula-right" exportable="#{cofre.valorRecDia}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.vlrRecApos}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorRecJaCreditado}" styleClass="celula-right" exportable="#{cofre.valorRecJaCreditado}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.vlrD0Apos}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorRecACreditar}" styleClass="celula-right" exportable="#{cofre.valorRecACreditar}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.vlrD1Apos}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.SaldoCofreTotal}" styleClass="celula-right" exportable="#{cofre.saldoCofreTotal}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.saldoFisTotal}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DepositoJaCreditado}" styleClass="celula-right" exportable="#{cofre.depositoJaCreditado}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.saldoFisCred}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DepositoProxDU}" styleClass="celula-right" exportable="#{cofre.depositoProxDU}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.vlrDepProxDU}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                                                        ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.SaldoFisCst}" styleClass="celula-right" exportable="#{cofre.saldoFisCst}" style="text-align:center !important;">
                                    <h:outputText value="#{movimento.tescofresres.saldoFisCst}" converter="conversormoeda"
                                                  style="text-align:center !important;#{movimento.tescofresres.saldoFisCst.toPlainString().contains('-') ? 'color:red':'color:green'}"/>
                                </p:column>
                            </p:dataTable>
                            <br/>
                            <p:commandButton value="Export as XML" ajax="false" >
                                <p:dataExporter type="xml" target="tabelaMov" fileName="movcred"/>
                            </p:commandButton>
                            <p:commandButton value="Export as CSV" ajax="false" >
                                <p:dataExporter type="csv" target="tabelaMov" fileName="movcred"/>
                            </p:commandButton>
                            <p:commandButton value="Export Table as Excel" ajax="false" >
                                <p:dataExporter type="xls" target="tabelaMov" fileName="movcred"/>
                            </p:commandButton>
                            <p:commandButton value="Export Table as PDF" ajax="false" >
                                <p:dataExporter type="pdf" target="tabelaMov" fileName="movcred"/>
                            </p:commandButton>
                        </div>
                    </p:panel>
                </p:dialog>

                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" />
                    </f:facet>

                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink target="_blank" id="pdf" actionListener="#{cofre.atualizaTabela}">
                                <p:graphicImage url="../assets/img/icone_pdf.png"/>
                                <p:dataExporter target="movimentos:tabelaMov" type="pdf"
                                                fileName="RelatorioCofre#{cofre.cofreSelecionado.clientes.codCofre}"
                                                preProcessor="#{cofre.exportarMovimentacao}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" actionListener="#{cofre.atualizaTabela}" target="_blank">
                                <p:graphicImage url="../assets/img/icone_xls.png"/>
                                <p:dataExporter target="movimentos:tabelaMov" type="xlsx" fileName="RelatorioCofre#{cofre.cofreSelecionado.clientes.codCofre}" />
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <h:form id="detalhesMovimentacao" style="overflow:hidden !important">
                <p:dialog widgetVar="dlgListarDetalhesmovimentacao" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" focus="btPesquisar"
                          style="border:thin solid #666 !important; height:500px !important; max-height:500px !important; box-shadow:0px 0px 5px #303030 !important; border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important; overflow:hidden">
                    <f:facet name="header">
                        <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value=" #{localemsgs.MovimentacaoDiaria}" style="color:#3C8DBC !important" />
                    </f:facet>

                    <p:panel id="cadastrar2" style="background-color: transparent; overflow:hidden !important;padding-top:0px !important; margin-top:-10px " styleClass="cadastrar2">

                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="padding-top:0px !important;">
                            <p:column>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top:0px !important; text-align:right;padding-right:0px !important; text-align:center !important;">
                                    <label style="background-color:#000; color:#FFF; border-radius:20px; padding:1px 8px 1px 8px; box-shadow:2px 2px 3px #CCC">
                                        <h:outputText value="#{localemsgs.Cofre}: "/>
                                        <h:outputText value="#{cofre.cofreSelecionado.clientes.codCofre} "
                                                      style="font-weight: bold;">
                                            <f:convertNumber pattern="0000"/>
                                        </h:outputText>
                                        <h:outputText value="#{cofre.movimentacaoSelecionada.clientes.codigo}
                                                      #{cofre.cofreSelecionado.clientes.NRed}" style="font-weight: bold;"/>
                                    </label>
                                </div>
                            </p:column>
                        </p:panelGrid>

                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="padding-bottom:10px !important">
                            <p:column>
                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-right:0px !important; padding-top:6px;text-align:right">
                                    <h:outputText value="#{localemsgs.DataInicial}: " style="font-size:9pt !important" />
                                </div>
                                <div class="col-md-2 col-sm-3 col-xs-8" style="padding-left:8px !important; text-align:left !important;
                                     white-space:nowrap !important">
                                    <p:datePicker value="#{cofre.calendario1}" readonlyInput="true"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario2"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}" />
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-right:0px !important;padding-top:6px;text-align:right">
                                    <h:outputText value="#{localemsgs.DataFinal}: " style="font-size:9pt !important"/>
                                </div>
                                <div class="col-md-2 col-sm-3 col-xs-8" style="padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <p:datePicker value="#{cofre.calendario2}" readonlyInput="true"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario2"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}"/>
                                    <p:commandLink id="btPesquisar" title="#{localemsgs.Pesquisar}" update="tabelaDetMov msgs" action="#{cofre.atualizarListaDetalhesMovmentacao}" style="margin-left:5px;top:-5px;">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                    </p:commandLink>
                                    <p:commandLink title="#{localemsgs.Exportar}" action="#{cofre.preExportarMovimentacao}" style="margin-left:5px;margin-top:-5px;">
                                        <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="30"/>
                                    </p:commandLink>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding-right:0px !important; text-align:right; padding-top:4px; color:blue !important;">
                                    <h:outputText value="#{localemsgs.Depositos} (#{cofre.totalDepositos}): " style="font-size:9pt !important"/>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-8" style="padding-left:8px !important;text-align:left; white-space:nowrap; padding-top:4px;">
                                    <h:outputText value="#{cofre.valorDepositos}" converter="conversormoeda" styleClass="negrito" style="color:blue !important;"/>
                                </div>

                                <div class="col-md-2 col-sm-3 col-xs-4" style="padding-right:0px !important; text-align:right; padding-top:4px;color:red !important;">
                                    <h:outputText value="#{localemsgs.Coletas} (#{cofre.totalColetas}): " style="font-size:9pt !important"/>
                                </div>
                                <div class="col-md-4 col-sm-3 col-xs-8" style="padding-left:8px !important;text-align:left; white-space:nowrap; padding-top:4px;">
                                    <h:outputText value="#{cofre.valorColetas}" converter="conversormoeda" styleClass="negrito" style="color:red !important;"/>
                                </div>
                            </p:column>
                        </p:panelGrid>

                        <p:dataTable id="tabelaDetMovExp" value="#{cofre.detalhesMovimentacao}" var="detMovimento"
                                     style="display:none">
                            <p:column headerText="#{localemsgs.Cofre}" style="width: 40px" exportable="true">
                                <h:outputText value="#{detMovimento.codCofre}" title="#{detMovimento.codCofre}" style="width: 40px">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}" style="width: 73px" exportable="true">
                                <h:outputText value="#{detMovimento.data}" title="#{detMovimento.data}"
                                              style="width: 73px" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora}" style="width: 60px" exportable="true">
                                <h:outputText value="#{detMovimento.hora}" title="#{detMovimento.hora}" style="width: 60px"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.IdUsuario}" style="width: 80px" exportable="true">
                                <h:outputText value="#{detMovimento.idUsuario}" title="#{detMovimento.idUsuario}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Usuario}" style="width: 80px" exportable="true">
                                <h:outputText value="#{detMovimento.nomeUsuario}" title="#{detMovimento.nomeUsuario}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorDeposito}" styleClass="celula-right" exportable="true">
                                <h:outputText value="#{detMovimento.valorDeposito}" title="#{detMovimento.valorDeposito}"
                                              style="width: 80px" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.TipoDeposito}" style="width: 105px" exportable="true">
                                <h:outputText value="#{detMovimento.tipoDeposito}" title="#{detMovimento.tipoDeposito}" style="width: 105px"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Status}" style="width: 70px" exportable="true">
                                <h:outputText value="#{detMovimento.status}" title="#{detMovimento.status}" style="width: 70px"/>
                            </p:column>
                        </p:dataTable>

                        <p:panel id="divGrid" style="width:100%; background-color: #EEE !important; padding-left:0px !important; overflow-y:auto;">
                            <p:dataGrid id="tabelaDetMov" value="#{cofre.detalhesMovimentacao}" var="detMovimento"
                                        emptyMessage="#{localemsgs.SemRegistros}" layout="grid" columns="1" style="background-color:#EEE !important; padding-left:0px !important;">

                                <p:panel id="pnlDadosMovDiaria" style="border: thin solid #DDD !important; background-color:#FFF !important;
                                         border-radius: 4px; padding: 5px 10px 2px 10px !important; box-shadow:2px 2px 3px #CCC">
                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                 layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important; font-size:10pt !important">
                                        <p:column>
                                            <h:outputText value="#{localemsgs.Data}: "/>
                                            <h:outputText value="#{detMovimento.data}" title="#{detMovimento.data}" converter="conversorData"/>
                                        </p:column>
                                        <p:column style="background-color:#FFF !important;">
                                            <h:outputText value="#{localemsgs.Hora}: "/>
                                            <h:outputText value="#{detMovimento.hora}" title="#{detMovimento.hora}"/>
                                        </p:column>

                                        <p:column style="background-color:#FFF !important; ">
                                            <h:outputText value="#{localemsgs.TipoMovimentacao}: "/>
                                            <h:outputText value="#{detMovimento.tipoDeposito}" title="#{detMovimento.tipoDeposito}"
                                                          style="#{detMovimento.tipoDeposito eq 'COLETA' ? 'color: red' : ''}" styleClass="negrito"/>
                                        </p:column>

                                        <p:column style="background-color:#FFF !important;">
                                            <h:outputText value="#{localemsgs.Usuario}: "/>
                                            <h:outputText value="#{detMovimento.nomeUsuario}" title="#{detMovimento.nomeUsuario}"/>
                                        </p:column>
                                        <p:column style="background-color:#FFF !important;">
                                            <h:outputText value="#{localemsgs.Valor}: "/>
                                            <h:outputText value="#{detMovimento.valorDeposito}" title="#{detMovimento.valorDeposito}" converter="conversormoeda"  styleClass="negrito"/>
                                        </p:column>
                                        <p:column style="background-color:#FFF !important;">
                                            <h:outputText value="#{localemsgs.Status}: "/>
                                            <h:outputText value="#{detMovimento.status}" title="#{detMovimento.status}"  styleClass="negrito"/>
                                        </p:column>
                                    </p:panelGrid>
                                </p:panel>
                            </p:dataGrid>
                        </p:panel>
                        <script>
                            Recalcular();

                            function Recalcular() {
                                if ($(document).height() >= 700)
                                    $('div[id*="divGrid"]').css('height', '300px');
                                else
                                    $('div[id*="divGrid"]').css('height', '200px');
                            }

                            $(window).resize(function () {
                                Recalcular();
                            });
                        </script>
                    </p:panel>
                </p:dialog>

                <p:dialog widgetVar="dlgExportarDetMov" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="300" styleClass="dialogo"
                          style="border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}"  style="color:#3C8DBC !important" />
                    </f:facet>

                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center;background-color: #EEE !important;">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink target="_blank" id="pdf" >
                                <p:graphicImage url="../assets/img/icone_pdf.png" style="background-color: #EEE !important; width:40px"/>
                                <p:dataExporter target="detalhesMovimentacao:tabelaDetMovExp" type="pdf" options="#{cofre.pdfOptMovimentacoes}"
                                                fileName="RelatorioAnaliticoCofre#{cofre.cofreSelecionado.clientes.codCofre}"
                                                preProcessor="#{cofre.exportarMovimentacao}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center;background-color: #EEE !important;">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" target="_blank">
                                <p:graphicImage url="../assets/img/icone_xls.png" style="background-color: #EEE !important;width:40px"/>
                                <p:dataExporter target="detalhesMovimentacao:tabelaDetMovExp" type="xlsx"
                                                fileName="RelatorioAnaliticoCofre#{cofre.cofreSelecionado.clientes.codCofre}" />
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <h:form id="formStatus">
                <p:dialog widgetVar="dlgListarStatus" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" focus="btPesquisarMov"
                          style="border:thin solid #666 !important; height:500px !important; max-height:500px !important; box-shadow:0px 0px 5px #303030 !important; border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important; overflow:hidden;">
                    <f:facet name="header">
                        <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Status}" style="color:#3C8DBC" />
                    </f:facet>

                    <p:panel id="cadastrar" style="background-color: transparent;padding:0px 0px 4px 0px!important; margin-top:-10px !important" styleClass="cadastrar">
                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:column style="padding:0px !important">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px 0px 5px 0px !important; padding-right:0px !important;text-align:center !important">
                                    <label style="background-color:#000; color:#FFF; border-radius:20px; padding:1px 8px 1px 8px !important; box-shadow:2px 2px 3px #CCC">
                                        <h:outputText value="#{localemsgs.Cofre}: " class="negrito fonte-menor"/>
                                        <h:outputText value="#{cofre.cofreSelecionado.clientes.codCofre} "
                                                      style="font-weight: bold">
                                            <f:convertNumber pattern="0000"/>
                                        </h:outputText>
                                        <h:outputText value="#{cofre.cofreSelecionado.clientes.codigo}
                                                      #{cofre.cofreSelecionado.clientes.NRed}" style="font-weight: bold"/>
                                    </label>
                                </div>

                                <div class="col-md-2 col-sm-3 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right">
                                    <h:outputText value="#{localemsgs.IMEI}: " class="negrito fonte-menor"/>
                                </div>
                                <div class="col-md-2 col-sm-3 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <h:outputText value="#{cofre.infoCofre.IMEI}"/>
                                </div>


                                <div class="col-md-2 col-sm-2 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right">
                                    <h:outputText value="#{localemsgs.Tipo}: " class="negrito fonte-menor"/>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important; text-align:left !important; white-space:nowrap !important">
                                    <h:outputText value="#{cofre.infoCofre.tipoEquip}"/>
                                </div>

                                <div class="col-md-2 col-sm-2 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right">
                                    <h:outputText value="#{localemsgs.Marca}: " class="negrito fonte-menor"/>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <h:outputText value="#{cofre.infoCofre.marcaEquip}"/>
                                </div>

                                <div class="col-md-2 col-sm-2 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right">
                                    <h:outputText value="#{localemsgs.Serial}: " class="negrito fonte-menor"/>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <h:outputText value="#{cofre.infoCofre.serialEquip}"/>
                                </div>

                                <div class="col-md-2 col-sm-2 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;padding-top:8px !important; text-align:right">
                                    <h:outputText value="#{localemsgs.DataInicial}: " class="negrito fonte-menor"/>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <p:datePicker value="#{cofre.calendario1}" readonlyInput="true"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario2"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}" />
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-4" style="padding-bottom:4px !important;padding-right:0px !important;text-align:right;padding-top:8px !important;">
                                    <h:outputText value="#{localemsgs.DataFinal}: " class="negrito fonte-menor"/>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-8" style="padding-bottom:4px !important;padding-left:8px !important;text-align:left; white-space:nowrap">
                                    <p:datePicker value="#{cofre.calendario2}" readonlyInput="true"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario2"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}" />

                                    <p:commandLink id="btPesquisarMov" title="#{localemsgs.Pesquisar}" update="tabelaStatus msgs" action="#{cofre.listarStatus()}">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                    </p:commandLink>
                                </div>
                                <div class="col-md-12" style="text-align:center; padding-top:2px !important">
                                    <h:outputText value="#{localemsgs.SomenteAlertas}: " style="color:red; #{cofre.somenteAlertas eq true ? 'font-weight: bold'  : ''}"/>
                                    <p:selectBooleanCheckbox value="#{cofre.somenteAlertas}">
                                        <p:ajax update="msgs cadastrar" listener="#{cofre.listarStatus}" />
                                    </p:selectBooleanCheckbox>
                                    <h:outputText value="#{localemsgs.Entradas} (#{cofre.listaStatus.size()})" style="margin-left:10px; color:forestgreen;"/>
                                </div>
                            </p:column>
                        </p:panelGrid>

                        <p:panel id="divGridStatus" style="width:100%; background-color: #FFF !important; padding-left:0px !important; overflow-y:auto; border:thin solid #CCC !important; padding:1px !important">

                            <p:dataTable id="tabelaStatus" value="#{cofre.listaStatus}" var="stts"
                                         styleClass="tabela" emptyMessage="#{localemsgs.SemRegistros}"
                                         scrollable="false"
                                         style="font-size: 12px; background: white; overflow:hidden !important;min-width:750px !important">


                                <p:columnGroup type="header">
                                    <p:row>
                                        <p:column headerText="#{localemsgs.Data}" style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;" />
                                        <p:column headerText="#{localemsgs.Hora}"  style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;" />
                                        <p:column headerText="#{localemsgs.PortaSuperior}" style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;" />
                                        <p:column headerText="#{localemsgs.PortaCofre}"  style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.Cassete}"  style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.Fechadura}"  style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.Validadora}" style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.Impressao}" style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.Latitude}" style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;"/>
                                        <p:column headerText="#{localemsgs.Longitude}" style="background-color:#3C8DBC !important; color:#FFF !important; font-size:8pt !important;"/>
                                    </p:row>
                                </p:columnGroup>


                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.data}" converter="conversorData"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.hora}" converter="conversorHora"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.portaSuperior eq 1 ? localemsgs.Aberta
                                                           : ( stts.portaSuperior eq 0 ? localemsgs.Fechada : localemsgs.Desconectada)}"
                                                  style="#{stts.portaSuperior eq 9 ? 'color: red; font-weight: bold' : ''}"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.portaCofre eq 1 ? localemsgs.Aberta
                                                           : ( stts.portaCofre eq 0 ? localemsgs.Fechada : localemsgs.Desconectada)}"
                                                  style="#{stts.portaCofre eq 9 ? 'color: red; font-weight: bold' : ''}"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.cassete eq 1 ? localemsgs.Aberto
                                                           : ( stts.cassete eq 0 ? localemsgs.Fechada : localemsgs.Desconectado)}"
                                                  style="#{stts.cassete eq 9 ? 'color: red; font-weight: bold' : ''}"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.fechadura eq 1 ? localemsgs.Aberta
                                                           : ( stts.fechadura eq 0 ? localemsgs.Fechada : localemsgs.Desconectada)}"
                                                  style="#{stts.fechadura eq 9 ? 'color: red; font-weight: bold' : ''}"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.validadora eq 1 ? localemsgs.Conectada : localemsgs.Desconectada}"
                                                  style="#{stts.validadora eq 9 ? 'color: red; font-weight: bold' : ''}"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.impressao eq 1 ? localemsgs.Conectada : localemsgs.Desconectada}"
                                                  style="#{stts.impressao eq 9 ? 'color: red; font-weight: bold' : ''}"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.latitude}"/>
                                </p:column>
                                <p:column style="text-align:center;">
                                    <h:outputText value="#{stts.longitude}"/>
                                </p:column>
                            </p:dataTable>

                        </p:panel>
                    </p:panel>
                </p:dialog>

                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#3C8DBC !important" />
                    </f:facet>

                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink target="_blank" id="pdf" actionListener="#{cofre.atualizaTabela}">
                                <p:graphicImage url="../assets/img/icone_pdf.png"/>
                                <p:dataExporter target="formStatus:tabelaStatus" type="pdf"
                                                fileName="RelatorioCofre#{cofre.cofreSelecionado.clientes.codCofre}"
                                                preProcessor="#{cofre.exportarMovimentacao}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" target="_blank">
                                <p:graphicImage url="../assets/img/icone_xls.png"/>
                                <p:dataExporter target="formStatus:tabelaStatus" type="xlsx"
                                                fileName="RelatorioCofre#{cofre.cofreSelecionado.clientes.codCofre}" />
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <h:form id="formInfoCofre">
                <p:dialog widgetVar="dlgInfoCofre" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.InfoCofre}" style="color:#022a48" />
                    </f:facet>
                    <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <h:outputText value="#{localemsgs.Codigo}: "/>
                        <h:outputText value="#{cofre.infoCofre.codEquip}" converter="conversor0"/>

                        <h:outputText value="#{localemsgs.Tipo}: "/>
                        <h:outputText value="#{cofre.infoCofre.tipoEquip}"/>

                        <h:outputText value="#{localemsgs.Marca}: "/>
                        <h:outputText value="#{cofre.infoCofre.marcaEquip}"/>

                        <h:outputText value="#{localemsgs.Serial}: "/>
                        <h:outputText value="#{cofre.infoCofre.serialEquip}"/>

                        <h:outputText value="#{localemsgs.IMEI}: "/>
                        <h:outputText value="#{cofre.infoCofre.IMEI}"/>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <!--<div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>-->
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{cofre.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{cofre.mostrarFiliais()}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.SASGrupos;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SASGruposDao {

    /**
     * Insere um novo grupo de código maior que 2000
     *
     * @param grupo
     * @param persistencia
     * @throws Exception
     */
    public void inserirGrupo(SASGrupos grupo, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO SASGrupos (Codigo, Descricao) "
                    + " VALUES ((select isnull(max(codigo),2000) from sasgrupos where codigo > 2000),?) ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(grupo.getDescricao());
            consult.insert();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("SASGruposDao.inserirGrupo - " + e.getMessage() + "\r\n"
                    + " INSERT INTO SASGrupos (Codigo, Descricao) "
                    + " VALUES ((select isnull(max(codigo),2000) from sasgrupos where codigo > 2000)," + grupo.getDescricao() + ")");
        }
    }

    /**
     * Atualiza a descrição de um grupo
     *
     * @param grupo
     * @param persistencia
     * @throws Exception
     */
    public void editarGrupo(SASGrupos grupo, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE SASGrupos SET Descricao = ? WHERE Codigo = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(grupo.getDescricao());
            consult.setString(grupo.getCodigo());
            consult.update();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("SASGruposDao.inserirGrupo - " + e.getMessage() + "\r\n"
                    + " UPDATE SASGrupos SET Descricao = " + grupo.getDescricao() + " WHERE Codigo = " + grupo.getCodigo());
        }
    }

    /**
     * Lista todos os grupos.
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<SASGrupos> listaSASGrupos(Persistencia persistencia) throws Exception {
        try {
            List<SASGrupos> retorno = new ArrayList<>();
            String sql = "SELECT codigo, descricao "
                    + " FROM sasgrupos";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            SASGrupos sg;
            while (consult.Proximo()) {
                sg = new SASGrupos();
                sg.setCodigo(consult.getString("codigo"));
                sg.setDescricao(consult.getString("descricao"));
                retorno.add(sg);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("SASGruposDao.listaSASGrupos - " + e.getMessage() + "\r\n"
                    + "SELECT codigo, descricao "
                    + " FROM sasgrupos");
        }
    }

    /**
     * Lista a descrição de um grupo.
     *
     * @param grupo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<SASGrupos> listaSASGruposGrupo(String grupo, Persistencia persistencia) throws Exception {
        try {
            List<SASGrupos> retorno = new ArrayList<>();
            String sql = "SELECT codigo, descricao "
                    + " FROM sasgrupos "
                    + " WHERE codigo = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(grupo);
            consult.select();
            SASGrupos sg;
            while (consult.Proximo()) {
                sg = new SASGrupos();
                sg.setCodigo(consult.getString("codigo"));
                sg.setDescricao(consult.getString("descricao"));
                retorno.add(sg);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("SASGruposDao.listaSASGruposGrupo - " + e.getMessage() + "\r\n"
                    + " SELECT codigo, descricao "
                    + " FROM sasgrupos "
                    + " WHERE codigo = ? ");
        }
    }

    /**
     * Atualiza a lista de grupos caso um novo banco tenha sido cadastrado
     *
     * @param persistencia
     * @throws Exception
     */
    public void atualizarListaGrupos(Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO SasGrupos "
                    + " SELECT Banco+1000, Substring(Nome,1,30) FROM Bancos "
                    + " WHERE Banco+1000 NOT IN (SELECT Codigo FROM SasGrupos) ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.insert();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("SASGruposDao.atualizarListaGrupos - " + e.getMessage() + "\r\n"
                    + " INSERT INTO SasGrupos "
                    + " SELECT Banco+1000, Substring(Nome,1,30) FROM Bancos "
                    + " WHERE Banco+1000 NOT IN (SELECT Codigo FROM SasGrupos) ");
        }
    }
}

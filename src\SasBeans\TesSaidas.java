package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TesSaidas {

    private String Guia;
    private String Serie;
    private String CodGTV;
    private String CodFil;
    private String Data;
    private String CodCli1;
    private String TipoMov;
    private String CodCli2;
    private String CodCli3;
    private String TotalDN;
    private String TotalDD;
    private String TotalMoeda;
    private String ChequesQtde;
    private String ChequesValor;
    private String TicketsQtde;
    private String TicketsValor;
    private String KitTrocoQtde;
    private String TotalGeral;
    private String Diferenca;
    private String ContaTes;
    private String TipoSrv;
    private String CodSrv;
    private String Obs;
    private String Situacao;
    private String Pedido;
    private String MatrConf;
    private String HrInicio;
    private String HrFinal;
    private String Baixa;
    private String Retorno;
    private String Dt_Pedido;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private int camera;
    
    private String Agencia;
    private String Destino;
    private String Lacre;
    private String MarcaAtm;
    private String Hora1;
    private String Rota;
    private String TipoSrvRota;
    private String Remessa;
    private String TpCliBrad;
    private String funcionario;

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        try {
            this.Guia = new String(Guia);
        } catch (Exception e) {
            this.Guia = new String("0");
        }
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getCodGTV() {
        return CodGTV;
    }

    public void setCodGTV(String CodGTV) {
        try {
            this.CodGTV = new String(CodGTV);
        } catch (Exception e) {
            this.CodGTV = new String("0");
        }
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new String(CodFil);
        } catch (Exception e) {
            this.CodFil = new String("0");
        }
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getTipoMov() {
        return TipoMov;
    }

    public void setTipoMov(String TipoMov) {
        this.TipoMov = TipoMov;
    }

    public String getCodCli2() {
        return CodCli2;
    }

    public void setCodCli2(String CodCli2) {
        this.CodCli2 = CodCli2;
    }

    public String getCodCli3() {
        return CodCli3;
    }

    public void setCodCli3(String CodCli3) {
        this.CodCli3 = CodCli3;
    }

    public String getTotalDN() {
        return TotalDN;
    }

    public void setTotalDN(String TotalDN) {
        try {
            this.TotalDN = new String(TotalDN);
        } catch (Exception e) {
            this.TotalDN = new String("0");
        }
    }

    public String getTotalDD() {
        return TotalDD;
    }

    public void setTotalDD(String TotalDD) {
        try {
            this.TotalDD = new String(TotalDD);
        } catch (Exception e) {
            this.TotalDD = new String("0");
        }
    }

    public String getTotalMoeda() {
        return TotalMoeda;
    }

    public void setTotalMoeda(String TotalMoeda) {
        try {
            this.TotalMoeda = new String(TotalMoeda);
        } catch (Exception e) {
            this.TotalMoeda = new String("0");
        }
    }

    public String getChequesQtde() {
        return ChequesQtde;
    }

    public void setChequesQtde(String ChequesQtde) {
        try {
            this.ChequesQtde = new String(ChequesQtde);
        } catch (Exception e) {
            this.ChequesQtde = new String("0");
        }
    }

    public String getChequesValor() {
        return ChequesValor;
    }

    public void setChequesValor(String ChequesValor) {
        try {
            this.ChequesValor = new String(ChequesValor);
        } catch (Exception e) {
            this.ChequesValor = new String("0");
        }
    }

    public String getTicketsQtde() {
        return TicketsQtde;
    }

    public void setTicketsQtde(String TicketsQtde) {
        try {
            this.TicketsQtde = new String(TicketsQtde);
        } catch (Exception e) {
            this.TicketsQtde = new String("0");
        }
    }

    public String getTicketsValor() {
        return TicketsValor;
    }

    public void setTicketsValor(String TicketsValor) {
        try {
            this.TicketsValor = new String(TicketsValor);
        } catch (Exception e) {
            this.TicketsValor = new String("0");
        }
    }

    public String getKitTrocoQtde() {
        return KitTrocoQtde;
    }

    public void setKitTrocoQtde(String KitTrocoQtde) {
        try {
            this.KitTrocoQtde = new String(KitTrocoQtde);
        } catch (Exception e) {
            this.KitTrocoQtde = new String("0");
        }
    }

    public String getTotalGeral() {
        return TotalGeral;
    }

    public void setTotalGeral(String TotalGeral) {
        try {
            this.TotalGeral = new String(TotalGeral);
        } catch (Exception e) {
            this.TotalGeral = new String("0");
        }
    }

    public String getDiferenca() {
        return Diferenca;
    }

    public void setDiferenca(String Diferenca) {
        try {
            this.Diferenca = new String(Diferenca);
        } catch (Exception e) {
            this.Diferenca = new String("0");
        }
    }

    public String getContaTes() {
        return ContaTes;
    }

    public void setContaTes(String ContaTes) {
        try {
            this.ContaTes = new String(ContaTes);
        } catch (Exception e) {
            this.ContaTes = new String("0");
        }
    }

    public String getTipoSrv() {
        return TipoSrv;
    }

    public void setTipoSrv(String TipoSrv) {
        this.TipoSrv = TipoSrv;
    }

    public String getCodSrv() {
        return CodSrv;
    }

    public void setCodSrv(String CodSrv) {
        this.CodSrv = CodSrv;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getPedido() {
        return Pedido;
    }

    public void setPedido(String Pedido) {
        try {
            this.Pedido = new String(Pedido);
        } catch (Exception e) {
            this.Pedido = new String("0");
        }
    }

    public String getMatrConf() {
        return MatrConf;
    }

    public void setMatrConf(String MatrConf) {
        try {
            this.MatrConf = new String(MatrConf);
        } catch (Exception e) {
            this.MatrConf = new String("0");
        }
    }

    public String getHrInicio() {
        return HrInicio;
    }

    public void setHrInicio(String HrInicio) {
        this.HrInicio = HrInicio;
    }

    public String getHrFinal() {
        return HrFinal;
    }

    public void setHrFinal(String HrFinal) {
        this.HrFinal = HrFinal;
    }

    public String getBaixa() {
        return Baixa;
    }

    public void setBaixa(String Baixa) {
        this.Baixa = Baixa;
    }

    public String getRetorno() {
        return Retorno;
    }

    public void setRetorno(String Retorno) {
        this.Retorno = Retorno;
    }

    public String getDt_Pedido() {
        return Dt_Pedido;
    }

    public void setDt_Pedido(String Dt_Pedido) {
        try {
            this.Dt_Pedido = new String(Dt_Pedido);
        } catch (Exception e) {
            this.Dt_Pedido = new String("0");
        }
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getDestino() {
        return Destino;
    }

    public void setDestino(String Destino) {
        this.Destino = Destino;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getMarcaAtm() {
        return MarcaAtm;
    }

    public void setMarcaAtm(String MarcaAtm) {
        this.MarcaAtm = MarcaAtm;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getTipoSrvRota() {
        return TipoSrvRota;
    }

    public void setTipoSrvRota(String TipoSrvRota) {
        this.TipoSrvRota = TipoSrvRota;
    }

    public String getRemessa() {
        return Remessa;
    }

    public void setRemessa(String Remessa) {
        this.Remessa = Remessa;
    }

    public String getTpCliBrad() {
        return TpCliBrad;
    }

    public void setTpCliBrad(String TpCliBrad) {
        this.TpCliBrad = TpCliBrad;
    }
    
    

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        try {
            this.Dt_Alter = new String(Dt_Alter);
        } catch (Exception e) {
            this.Dt_Alter = new String("0");
        }
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getFuncionario() {
        return funcionario;
    }

    public void setFuncionario(String funcionario) {
        this.funcionario = funcionario;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TesSaidas other = (TesSaidas) obj;
        if (!Objects.equals(this.Guia, other.Guia)) {
            return false;
        }
        if (!Objects.equals(this.Serie, other.Serie)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "TesSaidas{" + "Guia=" + Guia + ", Serie=" + Serie + '}';
    }

    public int getCamera() {
        return camera;
    }

    public void setCamera(int camera) {
        this.camera = camera;
    }
}

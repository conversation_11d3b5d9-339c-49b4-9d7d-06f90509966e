/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RHEscala;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RHEscalaDao {

    /**
     * Busca a tolerância no horário para determinado funcionário.
     *
     * @param matr
     * @param persistencia
     * @return
     * @throws Exception
     */
    public long getTolerancia(String matr, Persistencia persistencia) throws Exception {
        try {
            long retorno = 0;
            String sql = "select isnull(tolerancia, '0') tolerancia from rhescala "
                    + " left join funcion on funcion.escala = rhescala.codigo "
                    + " where matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getInt("tolerancia");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RHEscalaDao.getTolerancia - " + e.getMessage() + "\r\n"
                    + "select isnull(tolerancia, '0') tolerancia from rhescala "
                    + " left join funcion on funcion.escala = rhescala.codigo "
                    + " where matr = " + matr);
        }
    }

    public List<RHEscala> listarEscalas(String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        List<RHEscala> retorno = new ArrayList<>();

        try {

            sql = "SELECT * FROM RHEscala\n"
                    + "WHERE CodFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);

            consulta.select();
            
            RHEscala rhEscala;

            while (consulta.Proximo()) {
                rhEscala = new RHEscala();

                rhEscala.setCodigo(consulta.getString("Codigo"));
                rhEscala.setDescricao(consulta.getString("Descricao"));
                rhEscala.setCodFil(consulta.getString("CodFil"));

                retorno.add(rhEscala);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("RHEscalaDao.ListarEscalas - " + e.getMessage() + "\r\n" + sql);
        }
    }
}

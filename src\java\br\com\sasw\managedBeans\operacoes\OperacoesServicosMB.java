/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.operacoes;

import Arquivo.ArquivoLog;
import Controller.PstServ.PstServSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.OperacoesServicos;
import SasBeans.PstServ;
import SasBeans.Rt_Perc;
import SasBeans.SasPWFill;
import SasDaos.FuncionDao;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.controller.rotas.RotasSPM;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.Funcionarios;
import br.com.sasw.pacotesuteis.sasdaos.OperacoesServicosDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

/**
 *
 * <AUTHOR>
 */
@Named(value = "operacoesServicos")
@ViewScoped
public class OperacoesServicosMB implements Serializable {

    private Persistencia persistencia, satellite;
    private String caminho, banco, codPessoa, nomeFilial, codFil, operador, dataTela, dataAnterior, dataPosterior, codCli1, data1, data2, tipoMov, htmlEdit, log, tableGuiasProcessadas;
    private final ArquivoLog logerro;
    private final Calendar calendar;
    private Date dataSelecionada1, dataInicio, dataFim;
    private List<Date> datasSelecionadas;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private Map filters = new HashMap();
    private OperacoesServicosDao operacoesServicosDao;
    private OperacoesServicos operacoesServicos, operacoesServicosSelecionado;
    private List<OperacoesServicos> listaOperacoesServicos;
    private boolean mostrarFiliais, exibirExcluidos;
    private List<PstServ> listaPostos;
    private List<Funcion> funcionarios;
    private FuncionDao funcionDao;

    public OperacoesServicosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        LoginMB login = fc.getApplication().evaluateExpressionGet(fc, "#{login}", LoginMB.class);
        this.codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        this.nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        this.banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        this.operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        this.codPessoa = ((BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa")).toBigInteger().toString();
        this.log = new String();
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa + ".txt";
        this.logerro = new ArquivoLog();
        this.dataTela = DataAtual.getDataAtual("SQL");
        this.calendar = Calendar.getInstance();
        this.datasSelecionadas = new ArrayList<>();
        this.datasSelecionadas.add(calendar.getTime()); // data inicial
        this.datasSelecionadas.add(calendar.getTime()); // data final
        this.datasSelecionadas.get(0).setTime(calendar.getTimeInMillis()); // alterando data inicial

        this.data2 = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        this.data1 = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        this.dataInicio = datasSelecionadas.get(0);
        this.dataFim = datasSelecionadas.get(1);
        this.dataSelecionada1 = datasSelecionadas.get(0);
        this.dataInicio = calendar.getTime();
        this.dataFim = calendar.getTime();
        this.rotassatweb = new RotasSatWeb();
        this.dataAnterior = "";
        this.dataPosterior = "";
        this.codCli1 = "";
        this.tipoMov = "0";
        this.htmlEdit = "<i class=\"fa fa-pencil-square-o\" aria-hidden=\"true\"></i>";

        this.operacoesServicosDao = new OperacoesServicosDao();
        this.operacoesServicos = new OperacoesServicos();
        this.operacoesServicosSelecionado = new OperacoesServicos();
        this.listaOperacoesServicos = new ArrayList<>();
        this.mostrarFiliais = false;
        this.funcionDao = new FuncionDao();
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;

            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            filiais = rotassatweb.buscaInfoFilial(codFil, persistencia);
            carregarGrideOperacoesServicos();
            carregarPostosFilial();
            this.funcionarios = funcionDao.selecionarFuncion(this.codFil, this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void carregarPostosFilial() throws Exception {
        PstServSatMobWeb pstservsatmobweb = new PstServSatMobWeb();
        PstServ temp = new PstServ();
        temp.setCodFil(this.codFil);
        temp.setSecao("");
        temp.setLocal("");
        temp.setDescContrato("");
        this.listaPostos = pstservsatmobweb.BuscaPosto(temp, this.persistencia);
    }

    public void gravarMovimentacao() throws Exception {
        try {
            this.operacoesServicos.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.operacoesServicos.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.operacoesServicos.setHr_Alter(DataAtual.getDataAtual("HORA"));

            if (this.operacoesServicos.getOcorrencia().equals("C")) {
                if (null == this.operacoesServicos.getFuncAus() || this.operacoesServicos.getFuncAus().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: FuncionarioAusente"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getHora_Extra() || this.operacoesServicos.getHora_Extra().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: IndicadorHorasExtras"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getHEDiurna() || this.operacoesServicos.getHEDiurna().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: HorasDiurnas"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getHENoturna() || this.operacoesServicos.getHENoturna().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: HorasNoturnas"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getVR() || this.operacoesServicos.getVR().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: RefeicoesSimbolo"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getVT() || this.operacoesServicos.getVT().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: TransporteSimbolo"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getHospedagem() || this.operacoesServicos.getHospedagem().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: HospedagemSimbolo"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getHora1() || this.operacoesServicos.getHora1().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: Horario"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getHora4() || this.operacoesServicos.getHora4().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: Horario"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getPedido() || this.operacoesServicos.getPedido().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: Pedido"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                } else if (null == this.operacoesServicos.getOBS() || this.operacoesServicos.getOBS().equals("")) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio: OBS"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    return;
                }
            }

            boolean valid = this.operacoesServicosDao.validarDataMovimentacao(this.operacoesServicos.getData(), DataAtual.getDataAtual("SQL"), this.persistencia);

            if (valid) {
                this.operacoesServicosDao.gravarMovimentacao(this.operacoesServicos, this.persistencia);
                carregarGrideOperacoesServicos();

                if (this.operacoesServicos.getNumero().equals("0")) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                } else {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }

                this.operacoesServicos = new OperacoesServicos();
                this.operacoesServicosSelecionado = new OperacoesServicos();

                PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("PeriodoFechado"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preEdicao() {
        try {
            if (null != this.operacoesServicosSelecionado
                    && null != this.operacoesServicosSelecionado.getNumero()) {
                this.operacoesServicos = new OperacoesServicos();
                this.operacoesServicos = this.operacoesServicosSelecionado;

                PrimeFaces.current().ajax().update("formCadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SelecioneItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void exibirExcluidos() throws Exception {
        if (!this.exibirExcluidos) {
            carregarGrideOperacoesServicos();
        } else {
            this.listaOperacoesServicos = operacoesServicosDao.grideMovimentacaoOperacional(this.data1, this.data2, this.codFil, this.mostrarFiliais, true, this.persistencia);
        }
    }

    public void preExcluir() {
        if (null == operacoesServicosSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            PrimeFaces.current().executeScript("ConfirmarExclusao(" + this.operacoesServicosSelecionado.getNumero() + ");");
        }
    }

    public void excluir() {
        try {
            this.operacoesServicosSelecionado.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.operacoesServicosSelecionado.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.operacoesServicosSelecionado.setHr_Alter(DataAtual.getDataAtual("HORA"));

            operacoesServicosDao.excluirMovimentacao(this.operacoesServicosSelecionado, this.persistencia);
            carregarGrideOperacoesServicos();

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preCadastro() {
        try {
            this.operacoesServicosSelecionado = new OperacoesServicos();
            this.operacoesServicos = new OperacoesServicos();
            this.operacoesServicos.setData(this.data1);
            this.operacoesServicos.setNumero("0");
            this.operacoesServicos.setCodFil(this.codFil);

            PrimeFaces.current().ajax().update("formCadastrar");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }
    
    public void selecionarPostoFuncionarioAusente() throws Exception{
        String secao = funcionDao.getSecao(this.operacoesServicos.getFuncAus(), this.persistencia);
        this.operacoesServicos.setPosto(secao);
    }

    public void selecionarPostoFuncionarioEscalado() throws Exception{
        String secao = funcionDao.getSecao(this.operacoesServicos.getFuncSubs(), this.persistencia);
        this.operacoesServicos.setPosto(secao);
    }
    
    public void limparSelecaoPostoFuncionario(){
        this.operacoesServicos.setFuncAus("");
        this.operacoesServicos.setFuncSubs("");
        this.operacoesServicos.setPosto("");
    }
    
    public void carregarGrideOperacoesServicos() {
        try {
            this.listaOperacoesServicos = operacoesServicosDao.grideMovimentacaoOperacional(this.data1, this.data2, this.codFil, this.mostrarFiliais, false, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarDatas(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                carregarGrideOperacoesServicos();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private void displayInfo(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayWarn(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayError(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayFatal(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_FATAL, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void logaErro(final Exception e, final String methodName) {
        displayError(e.getMessage());
        log = this.getClass().getSimpleName() + "\r\n"
                + methodName + "\r\n"
                + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getDataAnterior() {
        return dataAnterior;
    }

    public void setDataAnterior(String dataAnterior) {
        this.dataAnterior = dataAnterior;
    }

    public String getDataPosterior() {
        return dataPosterior;
    }

    public void setDataPosterior(String dataPosterior) {
        this.dataPosterior = dataPosterior;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public List<OperacoesServicos> getListaOperacoesServicos() {
        return listaOperacoesServicos;
    }

    public void setListaOperacoesServicos(List<OperacoesServicos> listaOperacoesServicos) {
        this.listaOperacoesServicos = listaOperacoesServicos;
    }

    public OperacoesServicos getOperacoesServicos() {
        return operacoesServicos;
    }

    public void setOperacoesServicos(OperacoesServicos operacoesServicos) {
        this.operacoesServicos = operacoesServicos;
    }

    public OperacoesServicos getOperacoesServicosSelecionado() {
        return operacoesServicosSelecionado;
    }

    public void setOperacoesServicosSelecionado(OperacoesServicos operacoesServicosSelecionado) {
        this.operacoesServicosSelecionado = operacoesServicosSelecionado;
    }

    public List<PstServ> getListaPostos() {
        return listaPostos;
    }

    public void setListaPostos(List<PstServ> listaPostos) {
        this.listaPostos = listaPostos;
    }

    public List<Funcion> getFuncionarios() {
        return funcionarios;
    }

    public void setFuncionarios(List<Funcion> funcionarios) {
        this.funcionarios = funcionarios;
    }

    public boolean isExibirExcluidos() {
        return exibirExcluidos;
    }

    public void setExibirExcluidos(boolean exibirExcluidos) {
        this.exibirExcluidos = exibirExcluidos;
    }

}

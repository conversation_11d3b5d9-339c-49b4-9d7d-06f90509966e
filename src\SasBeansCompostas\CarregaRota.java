package SasBeansCompostas;

import SasBeans.Clientes;
import SasBeans.Escala;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.OS_Vig;
import SasBeans.Pessoa;
import SasBeans.Rotas;
import SasBeans.Rt_Perc;
import SasBeans.Rt_PercSla;

/**
 *
 * <AUTHOR>
 */
public class CarregaRota {

    private Rotas rota;
    private Rt_Perc rt_perc;
    private Rt_PercSla rt_percsla;
    private Pessoa pessoa;
    private Funcion funcion;
    private Escala escala;
    private Clientes CliOri;
    private Clientes CliDst;
    private Clientes CliFat;
    private Filiais filiais;
    private OS_Vig OS;

    public Escala getEscala() {
        return escala;
    }

    public void setEscala(Escala escala) {
        this.escala = escala;
    }

    public Rotas getRota() {
        return rota;
    }

    public void setRota(Rotas rota) {
        this.rota = rota;
    }

    public Rt_Perc getRt_perc() {
        return rt_perc;
    }

    public void setRt_perc(Rt_Perc rt_perc) {
        this.rt_perc = rt_perc;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public Rt_PercSla getRt_percsla() {
        return rt_percsla;
    }

    public void setRt_percsla(Rt_PercSla rt_percsla) {
        this.rt_percsla = rt_percsla;
    }

    /**
     * @return the CliOri
     */
    public Clientes getCliOri() {
        return CliOri;
    }

    /**
     * @param CliOri the CliOri to set
     */
    public void setCliOri(Clientes CliOri) {
        this.CliOri = CliOri;
    }

    /**
     * @return the CliDst
     */
    public Clientes getCliDst() {
        return CliDst;
    }

    /**
     * @param CliDst the CliDst to set
     */
    public void setCliDst(Clientes CliDst) {
        this.CliDst = CliDst;
    }

    public Clientes getCliFat() {
        return CliFat;
    }

    public void setCliFat(Clientes CliFat) {
        this.CliFat = CliFat;
    }

    /**
     * @return the filiais
     */
    public Filiais getFiliais() {
        return filiais;
    }

    /**
     * @param filiais the filiais to set
     */
    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public OS_Vig getOS() {
        return OS;
    }

    public void setOS(OS_Vig OS) {
        this.OS = OS;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Pessoa;
import SasBeans.PessoaLogin;
import SasBeans.PessoaPortalSrv;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class UsuarioSatMobWebServicos {

    private Pessoa pessoa;
    private List<PessoaLogin> pessoaLogin;
    private List<PessoaPortalSrv> pessoaPortalSrv;

    public UsuarioSatMobWebServicos() {
        this.pessoa = new Pessoa();
        this.pessoaLogin = new ArrayList<>();
        this.pessoaPortalSrv = new ArrayList<>();
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public List<PessoaLogin> getPessoaLogin() {
        return pessoaLogin;
    }

    public void setPessoaLogin(List<PessoaLogin> pessoaLogin) {
        this.pessoaLogin = pessoaLogin;
    }

    public List<PessoaPortalSrv> getPessoaPortalSrv() {
        return pessoaPortalSrv;
    }

    public void setPessoaPortalSrv(List<PessoaPortalSrv> pessoaPortalSrv) {
        this.pessoaPortalSrv = pessoaPortalSrv;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 17 * hash + Objects.hashCode(this.pessoa);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final UsuarioSatMobWebServicos other = (UsuarioSatMobWebServicos) obj;
        if (!Objects.equals(this.pessoa, other.pessoa)) {
            return false;
        }
        return true;
    }
}

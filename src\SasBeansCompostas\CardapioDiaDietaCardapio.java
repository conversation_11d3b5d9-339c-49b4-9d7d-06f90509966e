/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Cardapio;
import SasBeans.Clientes;

/**
 *
 * <AUTHOR>
 */
public class CardapioDiaDietaCardapio {

    private String Sequencia;
    private String CodCardapio;
    private String Data;
    private String CodDieta;
    private String dietaDescricao;
    private String Especificacao;
    private String Operador;
    private String Dt_alter;
    private String Hr_Alter;
    private String Periodo;
    private Dieta dieta;
    private Cardapio cardapio;
    private Clientes clientes;

    public CardapioDiaDietaCardapio() {
        this.Sequencia = "";
        this.CodCardapio = null;
        this.CodDieta = null;
        this.Data = "";
        this.Especificacao = "";
        this.Operador = "";
        this.Dt_alter = "";
        this.Hr_Alter = "";
        this.dieta = new Dieta();
        this.cardapio = new Cardapio();
        this.Periodo = "A";
        this.clientes = new Clientes();
    }

    public CardapioDiaDietaCardapio(CardapioDiaDietaCardapio Modelo) {
        this.Sequencia = Modelo.getSequencia();
        this.CodCardapio = Modelo.getCodCardapio();
        this.CodDieta = Modelo.getCodDieta();
        this.Data = Modelo.getData();
        this.Especificacao = Modelo.getEspecificacao();
        this.Operador = Modelo.getOperador();
        this.Dt_alter = Modelo.getDt_alter();
        this.Hr_Alter = Modelo.getHr_Alter();
        this.dieta = Modelo.getDieta();
        this.cardapio = Modelo.getCardapio();
        this.Periodo = Modelo.getPeriodo();
        this.clientes = Modelo.getClientes();
    }

    public String getEspecificacao() {
        return Especificacao;
    }

    public void setEspecificacao(String Especificacao) {
        this.Especificacao = Especificacao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodCardapio() {
        return CodCardapio;
    }

    public void setCodCardapio(String CodCardapio) {
        this.CodCardapio = CodCardapio;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getCodDieta() {
        return CodDieta;
    }

    public void setCodDieta(String CodDieta) {
        this.CodDieta = CodDieta;
    }

    public String getDietaDescricao() {
        return dietaDescricao;
    }

    public void setDietaDescricao(String dietaDescricao) {
        this.dietaDescricao = dietaDescricao;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public Dieta getDieta() {
        return dieta;
    }

    public void setDieta(Dieta dieta) {
        this.dieta = dieta;
    }

    public Cardapio getCardapio() {
        return cardapio;
    }

    public void setCardapio(Cardapio cardapio) {
        this.cardapio = cardapio;
    }

    public String getPeriodo() {
        return Periodo;
    }

    public void setPeriodo(String Periodo) {
        this.Periodo = Periodo;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }
}

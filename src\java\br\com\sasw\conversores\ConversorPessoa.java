/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import SasBeans.Pessoa;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorPessoa")
public class ConversorPessoa implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        Pessoa p = new Pessoa();
        p.setCodigo(value);
        p.setNome("");
        return p;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            return ((Pessoa) value).getCodigo().toPlainString();
        } catch (Exception e) {
            return null;
        }
    }

}

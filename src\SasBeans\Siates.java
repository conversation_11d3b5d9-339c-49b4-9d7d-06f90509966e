/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import SasBeans.XMLSiates.Tipo1;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Siates {

    private String Sequencia;
    private String ID;
    private String WO;
    private String Req;
    private String XML_Envio;
    private String XML_Retorno;
    private String Captura;
    private String Data;
    private String Hora;
    private String IDChamado;
    private String Empresa;
    private String Organizacao;
    private String Suporte;
    private String Status;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private Tipo1 XMLRetorno;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getID() {
        return ID;
    }

    public void setID(String ID) {
        this.ID = ID;
    }

    public String getWO() {
        return WO;
    }

    public void setWO(String WO) {
        this.WO = WO;
    }

    public String getReq() {
        return Req;
    }

    public void setReq(String Req) {
        this.Req = Req;
    }

    public String getXML_Envio() {
        return XML_Envio;
    }

    public void setXML_Envio(String XML_Envio) {
        this.XML_Envio = XML_Envio;
    }

    public String getXML_Retorno() {
        return XML_Retorno;
    }

    public void setXML_Retorno(String XML_Retorno) {
        this.XML_Retorno = XML_Retorno;
    }

    public String getCaptura() {
        return Captura;
    }

    public void setCaptura(String Captura) {
        this.Captura = Captura;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getIDChamado() {
        return IDChamado;
    }

    public void setIDChamado(String IDChamado) {
        this.IDChamado = IDChamado;
    }

    public String getEmpresa() {
        return Empresa;
    }

    public void setEmpresa(String Empresa) {
        this.Empresa = Empresa;
    }

    public String getOrganizacao() {
        return Organizacao;
    }

    public void setOrganizacao(String Organizacao) {
        this.Organizacao = Organizacao;
    }

    public String getSuporte() {
        return Suporte;
    }

    public void setSuporte(String Suporte) {
        this.Suporte = Suporte;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String Status) {
        this.Status = Status;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public Tipo1 getXMLRetorno() {
        return XMLRetorno;
    }

    public void setXMLRetorno(Tipo1 XMLRetorno) {
        this.XMLRetorno = XMLRetorno;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 59 * hash + Objects.hashCode(this.IDChamado);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Siates other = (Siates) obj;
        if (!Objects.equals(this.IDChamado, other.IDChamado)) {
            return false;
        }
        return true;
    }
}

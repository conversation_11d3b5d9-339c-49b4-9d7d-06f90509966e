package Controller.SaswChamados;

import Dados.Persistencia;
import SasBeans.Chamados;
import SasDaos.ChamadosDao;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class ChamadosSasw {

    /**
     * Listagem do cadastro de chamados
     *
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Chamados> Listagem(String codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<Chamados> retorno;
            ChamadosDao chamadosDAO = new ChamadosDao();
            retorno = chamadosDAO.ListaChamados(codPessoa, persistencia);
            return retorno;

        } catch (Exception e) {
        }
//            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        return null;

    }

    public void Inserir(Chamados chamados, Persistencia persistenciaSat) throws Exception {
        try {
//             buscar pessoa base satellite
            chamados = (Chamados) FuncoesString.removeAcentoObjeto(chamados);
            ChamadosDao chamadosdao = new ChamadosDao();
            Chamados c = chamadosdao.BuscarChamadosSequencia(BigDecimal.TEN, persistenciaSat);

        } catch (Exception e) {
//            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public Integer Contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            ChamadosDao chamadosdao = new ChamadosDao();
            retorno = chamadosdao.TotalChamados(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
//            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
        return null;
    }

}

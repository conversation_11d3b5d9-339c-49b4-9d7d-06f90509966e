/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class XMLeSocial {

    private BigDecimal Sequencia;
    private String Identificador;
    private String Tipo;
    private String Ambiente;
    private String CodFil;
    private String Evento;
    private String Compet;
    private String Protocolo_Envio;
    private String XML_Envio;
    private String XML_Retorno;
    private String Assinar;
    private String Dt_Envio;
    private String Hr_Envio;
    private String Dt_Retorno;
    private String Hr_Retorno;

    private String row;

    public String getRow() {
        return row;
    }

    public void setRow(String row) {
        this.row = row;
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getIdentificador() {
        return Identificador;
    }

    public void setIdentificador(String Identificador) {
        this.Identificador = Identificador;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getCodFil() {
        return CodFil;
    }

    public String getAmbiente() {
        return Ambiente;
    }

    public void setAmbiente(String Ambiente) {
        this.Ambiente = Ambiente;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getEvento() {
        return Evento;
    }

    public void setEvento(String Evento) {
        this.Evento = Evento;
    }

    public String getCompet() {
        return Compet;
    }

    public void setCompet(String Compet) {
        this.Compet = Compet;
    }

    public String getXML_Envio() {
        return XML_Envio;
    }

    public void setXML_Envio(String XML_Envio) {
        this.XML_Envio = XML_Envio;
    }

    public String getXML_Retorno() {
        return XML_Retorno;
    }

    public void setXML_Retorno(String XML_Retorno) {
        this.XML_Retorno = XML_Retorno;
    }

    public String getAssinar() {
        return Assinar;
    }

    public void setAssinar(String Assinar) {
        this.Assinar = Assinar;
    }

    public String getDt_Envio() {
        return Dt_Envio;
    }

    public void setDt_Envio(String Dt_Envio) {
        this.Dt_Envio = Dt_Envio;
    }

    public String getHr_Envio() {
        return Hr_Envio;
    }

    public void setHr_Envio(String Hr_Envio) {
        this.Hr_Envio = Hr_Envio;
    }

    public String getDt_Retorno() {
        return Dt_Retorno;
    }

    public void setDt_Retorno(String Dt_Retorno) {
        this.Dt_Retorno = Dt_Retorno;
    }

    public String getHr_Retorno() {
        return Hr_Retorno;
    }

    public void setHr_Retorno(String Hr_Retorno) {
        this.Hr_Retorno = Hr_Retorno;
    }

    public String getProtocolo_Envio() {
        return Protocolo_Envio;
    }

    public void setProtocolo_Envio(String Protocolo_Envio) {
        this.Protocolo_Envio = Protocolo_Envio;
    }

    public byte[] getBytes(String name) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }
}

/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Rotas;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class RotasLazyList extends LazyDataModel<Rotas> {

    private static final long serialVersionUID = 1L;
    private List<Rotas> rotas;
    private final RotasSatWeb rotassatweb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public RotasLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.rotassatweb = new RotasSatWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<Rotas> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.rotas = this.rotassatweb.ListagemPaginada(first, pageSize, filters, this.codPessoa, this.persistencia);

            // set the total of players
            setRowCount(this.rotassatweb.Contagem(filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.rotas;
    }

    @Override
    public Object getRowKey(Rotas rota) {
        return rota.getSequencia().toBigInteger().toString();
    }

    @Override
    public Rotas getRowData(String sequencia) {
        for (Rotas rota : this.rotas) {
            if (sequencia.equals(rota.getSequencia().toBigInteger().toString())) {
                return rota;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.R2020;
import SasBeans.ESocial.R2020.InfoTpServ;
import SasBeans.ESocial.R2020.Nfs;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.PreencheEsquerda;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class R2020Dao {

    public List<R2020> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<R2020> retorno = new ArrayList<>();
            String sql = " Select \n"
                    + " Max(Clientes.NRed) NRed,\n"
                    + "Filiais.CNPJ ideContri_nrInsc,\n"
                    + "Filiais.CNPJ ideEstabPrest_nrInscEstabPrest  ,\n"
                    + "convert(bigint, Clientes.CGC) ideTomador_nrInscTomador,\n"
                    + "'1' ideTomador_tpInscTomador,\n"
                    + "'0' ideTomador_indObra,\n"
                    + " Replace(Substring(Convert(Varchar,Nfiscal.Data,111),1,7),'/','-') ideEvento_perApur, "
                    + " '1' ideEvento_indRetif, \n"
                    + " round(Sum(NFiscal.Valor),2) ideTomador_vlrTotalBruto, \n"
                    + " round(Sum(NFiscal.BaseINSS),2) ideTomador_vlrTotalBaseRet, \n"
                    + " round(Sum(NFiscal.INSS),2) ideTomador_vlrTotalRetPrinc, \n"
                    //+ " '2' ideEvento_indRetif, \n" 
                    //+ " round(Sum(0),2) ideTomador_vlrTotalBruto, \n" 
                    //+ " round(Sum(0),2) ideTomador_vlrTotalBaseRet, \n" 
                    //+ " round(Sum(0),2) ideTomador_vlrTotalRetPrinc, \n" 
                    + "'0'	ideTomador_vlrTotalRetAdic,\n"
                    //                    + "'0' ideTomador_vlrTotalNRetPrinc,\n" 
                    + "'0' ideTomador_vlrTotalNRetAdic, \n"
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = convert(bigint, Clientes.CGC) "
                    + "             and z.evento = 'R-2020' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.CodFil = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%'\n"
                    + "                         or z.XML_Retorno like '%<descRetorno>ERRO</descRetorno>%')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = convert(bigint, Clientes.CGC) "
                    + "             and z.evento = 'R-2020' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.CodFil = ? "
                    + "             and z.Xml_Retorno like '%<cdRetorno>0%')) a) sucesso "
                    + "from NFiscal\n"
                    + "Left join Filiais  on Filiais.CodFil = NFiscal.CodFil\n"
                    + "Left join Clientes  on Clientes.Codigo = NFiscal.CliFat\n"
                    + "                   and Clientes.CodFil = NFiscal.CodFil\n"
                    + "Where Nfiscal.Situacao = 'A' \n"
                    //+  "Where Nfiscal.Numero = 39011 \n" 
                    + "  and Replace(Substring(Convert(Varchar,Nfiscal.Data,111),1,7),'/','-') = ? \n"
                    + "  and NFiscal.CodFil = ? \n"
                    + "  and NFiscal.INSSRet = 'S' \n"
                    + "  and NFiscal.INSS > 0 \n"
                    + "Group by Filiais.CNPJ, Clientes.CGC, Replace(Substring(Convert(Varchar,Nfiscal.Data,111),1,7),'/','-') \n";
            //+  "Having Sum(NFiscal.INSS) > 0";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            R2020 r2020;
            int indice;
            Nfs nfs;
            InfoTpServ infoTpServ;
//            InfoProcRetPr infoProcRetPr;
//            InfoProcRetAd infoProcRetAd;
            while (consulta.Proximo()) {
                r2020 = new R2020();
                r2020.setSucesso(consulta.getInt("sucesso"));
                r2020.setIdeEvento_procEmi("1");
                r2020.setIdeEvento_verProc("Satellite Reinf");
                r2020.setIdeContri_tpInsc("1");
                r2020.setIdeContri_nrInsc(consulta.getString("ideContri_nrInsc"));
                r2020.setIdeEvento_perApur(consulta.getString("ideEvento_perApur"));
                r2020.setIdeEvento_indRetif(consulta.getString("ideEvento_indRetif"));
                r2020.setIdeEstabPrest_tpInscEstabPrest("1");
                r2020.setIdeEstabPrest_nrInscEstabPrest(consulta.getString("ideEstabPrest_nrInscEstabPrest"));
                r2020.setNred(consulta.getString("NRed"));
                r2020.setIdeTomador_nrInscTomador(PreencheEsquerda(consulta.getString("ideTomador_nrInscTomador"), 14, "0"));
                r2020.setIdeTomador_tpInscTomador(consulta.getString("ideTomador_tpInscTomador"));
                r2020.setIdeTomador_indObra(consulta.getString("ideTomador_indObra"));
                r2020.setIdeTomador_vlrTotalBruto(consulta.getString("ideTomador_vlrTotalBruto"));
                r2020.setIdeTomador_vlrTotalBaseRet(consulta.getString("ideTomador_vlrTotalBaseRet"));
                r2020.setIdeTomador_vlrTotalRetPrinc(consulta.getString("ideTomador_vlrTotalRetPrinc"));

                r2020.setIdeTomador_nfs(new ArrayList<>());
                r2020.setIdeTomador_infoProcRetPr(new ArrayList<>());
                r2020.setIdeTomador_infoProcRetAd(new ArrayList<>());

                retorno.add(r2020);
            }

            sql = "Select \n"
                    + "convert(bigint, Clientes.CGC) ideTomador_nrInscTomador,\n"
                    + "NFiscal.Serie nfs_serie,\n"
                    + "Case when NFiscal.NFeRetorno is not null then Convert(BigInt, NFiscal.NFeRetorno) \n"
                    + "     when NFiscal.NFeRetorno <> '' then Convert(BigInt, NFiscal.NFeRetorno) \n"
                    + "else Convert(BigInt, NFiscal.Numero) end nfs_numDocto,\n"
                    + "Replace(Convert(Varchar,NFiscal.Data,111),'/','-') nfs_dtEmissaoNF,\n"
                    + "NFiscal.Valor nfs_vlrBruto,\n"
                    + "Case when NFiscal.CFOP = '1102' then '100000002'\n"
                    + "     when NFiscal.CFOP = '1103' then '100000002'\n"
                    + "       else '100000001' end infoTpServ_tpServico,\n"
                    + "NFiscal.BaseINSS infoTpServ_vlrBaseRet,\n"
                    + "NFiscal.INSS infoTpServ_vlrRetencao\n"
                    + "From NFiscal\n"
                    + "Left join Filiais  on Filiais.CodFil = NFiscal.CodFil\n"
                    + "Left join Clientes  on Clientes.Codigo = NFiscal.CliFat\n"
                    + "                    and Clientes.CodFil = NFiscal.CodFil\n"
                    + "Where Nfiscal.Situacao = 'A'\n"
                    + "  and Replace(Substring(Convert(Varchar,Nfiscal.Data,111),1,7),'/','-') = ?\n"
                    + "  and NFiscal.CodFil = ?\n"
                    + "  and (NFiscal.INSS) > 0\n"
                    + "  and NFiscal.INSSRet = 'S';";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            while (consulta.Proximo()) {
                r2020 = new R2020();
                r2020.setIdeTomador_nrInscTomador(PreencheEsquerda(consulta.getString("ideTomador_nrInscTomador"), 14, "0"));

                indice = retorno.indexOf(r2020);
                if (indice >= 0) {
                    nfs = new Nfs();
                    nfs.setNfs_serie(consulta.getString("nfs_serie"));
                    nfs.setNfs_numDocto(consulta.getString("nfs_numDocto"));
                    nfs.setNfs_dtEmissaoNF(consulta.getString("nfs_dtEmissaoNF"));
                    nfs.setNfs_vlrBruto(consulta.getString("nfs_vlrBruto"));
//                    nfs.setNfs_obs(consulta.getString("nfs_obs"));
                    nfs.setNfs_infoTpserv(new ArrayList<>());

                    infoTpServ = new InfoTpServ();
                    infoTpServ.setInfoTpServ_tpServico(consulta.getString("infoTpServ_tpServico"));
                    infoTpServ.setInfoTpServ_vlrBaseRet(consulta.getString("infoTpServ_vlrBaseRet"));
                    infoTpServ.setInfoTpServ_vlrRetencao(consulta.getString("infoTpServ_vlrRetencao"));
//                    infoTpServ.setInfoTpServ_vlrRetSub(consulta.getString("infoTpServ_vlrRetSub"));
//                    infoTpServ.setInfoTpServ_vlrNRetPrinc(consulta.getString("infoTpServ_vlrNRetPrinc"));
//                    infoTpServ.setInfoTpServ_vlrServicos15(consulta.getString("infoTpServ_vlrServicos15"));
//                    infoTpServ.setInfoTpServ_vlrServicos20(consulta.getString("infoTpServ_vlrServicos20"));
//                    infoTpServ.setInfoTpServ_vlrServicos25(consulta.getString("infoTpServ_vlrServicos25"));
//                    infoTpServ.setInfoTpServ_vlrAdicional(consulta.getString("infoTpServ_vlrAdicional"));
//                    infoTpServ.setInfoTpServ_vlrNRetAdic(consulta.getString("infoTpServ_vlrNRetAdic"));

                    nfs.getNfs_infoTpserv().add(infoTpServ);
                    retorno.get(indice).getIdeTomador_nfs().add(nfs);
                }
            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("R2020Dao.get - " + e.getMessage() + "\r\n"
                    + " Select \n"
                    + "Clientes.NRed,\n"
                    + "Filiais.CNPJ ideContri_nrInsc,\n"
                    + "Filiais.CNPJ ideEstabPrest_tpInscEstabPrest,\n"
                    + "Clientes.CGC ideTomador_nrInscTomador,\n"
                    + "'0' ideTomador_indObra,\n"
                    + "Sum(NFiscal.Valor) ideTomador_vlrTotalBruto,\n"
                    + "Sum(NFiscal.BaseINSS) ideTomador_vlrTotalBaseRet,\n"
                    + "Sum(NFiscal.INSS) ideTomador_vlrTotalRetPrinc,\n"
                    + "'0' ideTomador_vlrTotalRetAdic,\n"
                    + "'0' ideTomador_vlrTotalNRetPrinc,\n"
                    + "'0' ideTomador_vlrTotalNRetAdic, \n"
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Clientes.CGC "
                    + "             and z.evento = 'R-2020' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.CodFil = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Clientes.CGC "
                    + "             and z.evento = 'R-2020' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.CodFil = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Clientes.CGC "
                    + "             and z.evento = 'R-2020' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.CodFil = ? "
                    + "             and z.Xml_Retorno like '%<cdRetorno>0%')) a) sucesso "
                    + "from NFiscal\n"
                    + "Left join Filiais  on Filiais.CodFil = NFiscal.CodFil\n"
                    + "Left join Clientes  on Clientes.Codigo = NFiscal.CliFat\n"
                    + "                   and Clientes.CodFil = NFiscal.CodFil\n"
                    + "Where Nfiscal.Situacao = 'A'\n"
                    + "  and Replace(Substring(Convert(Varchar,Nfiscal.Data,111),1,7),'/','-') = ? \n"
                    + "  and NFiscal.CodFil = ? \n"
                    + "  and NFiscal.INSS > 0 \n"
                    + "Group by Filiais.CNPJ, Clientes.CGC, Clientes.NRED");
            //+  "Having Sum(NFiscal.INSS) > 0");
        }
    }
}

package SasBeansCompostas;

import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.Pessoa;
import SasBeans.PstServ;
import SasBeans.RHPonto;
import SasBeans.Saspw;
import SasBeans.Saspwac;
import SasBeans.TesAutomatiza;

/**
 *
 * <AUTHOR>
 */
public class Login {

    private Funcion funcion;
    private Pessoa pessoa;
    private Saspw saspw;
    private Saspwac saspwac;
    private Clientes cliente;
    private RHPonto rhPonto;
    private TesAutomatiza tesAutomatiza;
    private String rotaM;
    private String rotaP;
    private String data;
    private String hora;
    private String MoedaPdrMobile;
    
    private PstServ pstServ;
    private Filiais filiais;
    
    public String getRotaM() {
        return rotaM;
    }

    public void setRotaM(String rotaM) {
        this.rotaM = rotaM;
    }

    public String getRotaP() {
        return rotaP;
    }

    public void setRotaP(String rotaP) {
        this.rotaP = rotaP;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Saspw getSaspw() {
        return saspw;
    }

    public void setSaspw(Saspw saspw) {
        this.saspw = saspw;
    }

    public Saspwac getSaspwac() {
        return saspwac;
    }

    public void setSaspwac(Saspwac saspwac) {
        this.saspwac = saspwac;
    }

    public Clientes getCliente() {
        return cliente;
    }

    public void setCliente(Clientes cliente) {
        this.cliente = cliente;
    }

    public RHPonto getRhPonto() {
        return rhPonto;
    }

    public void setRhPonto(RHPonto rhPonto) {
        this.rhPonto = rhPonto;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public TesAutomatiza getTesAutomatiza() {
        return tesAutomatiza;
    }

    public void setTesAutomatiza(TesAutomatiza tesAutomatiza) {
        this.tesAutomatiza = tesAutomatiza;
    }

    public String getMoedaPdrMobile() {
        return MoedaPdrMobile;
    }

    public void setMoedaPdrMobile(String MoedaPdrMobile) {
        this.MoedaPdrMobile = MoedaPdrMobile;
    }

    public PstServ getPstServ() {
        return pstServ;
    }

    public void setPstServ(PstServ pstServ) {
        this.pstServ = pstServ;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }    
}

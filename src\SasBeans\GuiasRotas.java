package SasBeans;

/**
 * <AUTHOR>
 */
public class GuiasRotas {

    private int cliente;
    private int guia;
    private int parada;

    private float valor;
    private float OS;
    private float sequencia;
    private float codFil;

    private String endOrigem;
    private String bairroOrigem;
    private String cidadeOrigem;
    private String estadoOrigem;
    private String endDestino;
    private String bairroDestino;
    private String estadoDestino;
    private String cidadeDestino;
    private String codPessoaAuth;
    private String hora1;
    private String serie;
    private String origem;
    private String destino;

    public GuiasRotas() {
        cliente = 0;
        guia = 0;
        parada = 0;

        codFil = 0;
        valor = 0;
        OS = 0;
        sequencia = 0;

        endOrigem = "";
        bairroOrigem = "";
        cidadeOrigem = "";
        estadoOrigem = "";
        endDestino = "";
        bairroDestino = "";
        estadoDestino = "";
        cidadeDestino = "";
        codPessoaAuth = "";
        hora1 = "";
        serie = "";
        origem = "";
        destino = "";
    }

    public String getEndOrigem() {
        return endOrigem;
    }

    public void setEndOrigem(String endOrigem) {
        this.endOrigem = endOrigem;
    }

    public String getBairroOrigem() {
        return bairroOrigem;
    }

    public void setBairroOrigem(String bairroOrigem) {
        this.bairroOrigem = bairroOrigem;
    }

    public String getCidadeOrigem() {
        return cidadeOrigem;
    }

    public void setCidadeOrigem(String cidadeOrigem) {
        this.cidadeOrigem = cidadeOrigem;
    }

    public String getEstadoOrigem() {
        return estadoOrigem;
    }

    public void setEstadoOrigem(String estadoOrigem) {
        this.estadoOrigem = estadoOrigem;
    }

    public String getEndDestino() {
        return endDestino;
    }

    public void setEndDestino(String endDestino) {
        this.endDestino = endDestino;
    }

    public String getBairroDestino() {
        return bairroDestino;
    }

    public void setBairroDestino(String bairroDestino) {
        this.bairroDestino = bairroDestino;
    }

    public String getEstadoDestino() {
        return estadoDestino;
    }

    public void setEstadoDestino(String estadoDestino) {
        this.estadoDestino = estadoDestino;
    }

    public String getCidadeDestino() {
        return cidadeDestino;
    }

    public void setCidadeDestino(String cidadeDestino) {
        this.cidadeDestino = cidadeDestino;
    }

    public String getCodPessoaAuth() {
        return codPessoaAuth;
    }

    public void setCodPessoaAuth(String codPessoaAuth) {
        this.codPessoaAuth = codPessoaAuth;
    }

    public float getCodFil() {
        return codFil;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = hora1;
    }

    public void setCodFil(float codFil) {
        this.codFil = codFil;
    }

    public int getCliente() {
        return cliente;
    }

    public void setCliente(int cliente) {
        this.cliente = cliente;
    }

    public int getGuia() {
        return guia;
    }

    public void setGuia(int guia) {
        this.guia = guia;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public int getParada() {
        return parada;
    }

    public void setParada(int parada) {
        this.parada = parada;
    }

    public float getValor() {
        return valor;
    }

    public void setValor(float valor) {
        this.valor = valor;
    }

    public float getOS() {
        return OS;
    }

    public void setOS(float OS) {
        this.OS = OS;
    }

    public float getSequencia() {
        return sequencia;
    }

    public void setSequencia(float sequencia) {
        this.sequencia = sequencia;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getDestino() {
        return destino;
    }

    public void setDestino(String destino) {
        this.destino = destino;
    }
}

// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3) 
// Source File Name:   LocaleController.java
package br.com.sasw.utils;

import Controller.Login.LoginSatMobWeb;
import Dados.Persistencia;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.util.*;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.SessionScoped;
import javax.faces.component.UIViewRoot;
import javax.faces.context.FacesContext;
import org.omnifaces.util.Faces;

@ManagedBean
@SessionScoped
public class LocaleController implements Serializable {

    private int number;
    private String mostraData;
    private DateFormat dataAtual;
    Calendar c;
    Date data;
    Locale browserLocaleB;
    private String localB;
    private String PaisB;
    private Locale currentLocale;
    private static Locale sCurrentLocale;
    private Persistencia pp;
    private BigDecimal CodPessoa;

    public LocaleController() {
        number = 1;
        c = Calendar.getInstance();
        data = c.getTime();
        browserLocaleB = FacesContext.getCurrentInstance().getExternalContext().getRequestLocale();
        localB = browserLocaleB.getLanguage();
        PaisB = browserLocaleB.getCountry();
        currentLocale = new Locale(localB, PaisB);
        CodPessoa = BigDecimal.ZERO;
        pp = null;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public void setNumberAndUpdate(int number) throws IOException, Exception {
        this.number = number;
        getLocales();
    }

    public String getMostraData() {
        return mostraData;
    }

    public void setMostraData(String mostraData) {
        this.mostraData = mostraData;
    }

    public void getLocales(int inNumber) throws Exception {
        number = inNumber;
        getLocales();
    }

    public void setParam(BigDecimal codPessoa, Persistencia persistencia) {
        CodPessoa = codPessoa;
        pp = persistencia;
    }

    public void getLocales() throws Exception {
        UIViewRoot viewRoot = FacesContext.getCurrentInstance().getViewRoot();
        switch (number) {
            case 1: // '\001'
                currentLocale = new Locale("pt", "BR");
                viewRoot.setLocale(currentLocale);
                sCurrentLocale = currentLocale;
                dataAtual = DateFormat.getDateInstance(0, currentLocale);
                setMostraData(dataAtual.format(data));
                break;

//            case 2: // '\002'
//                currentLocale = new Locale("es", "MX");
//                viewRoot.setLocale(currentLocale);
//                sCurrentLocale = currentLocale;
//                dataAtual = DateFormat.getDateInstance(0, currentLocale);
//                setMostraData(dataAtual.format(data));
//                break;
            case 2: // '\003'
                currentLocale = new Locale("es", "ES");
                viewRoot.setLocale(currentLocale);
                sCurrentLocale = currentLocale;
                dataAtual = DateFormat.getDateInstance(0, currentLocale);
                setMostraData(dataAtual.format(data));
                break;

            case 3: // '\004'   
                currentLocale = Locale.US;
                viewRoot.setLocale(currentLocale);
                sCurrentLocale = currentLocale;
                dataAtual = DateFormat.getDateInstance(0, currentLocale);
                setMostraData(dataAtual.format(data));
                break;
        }

        atualizarIdioma();
    }

    public void increment() {
        number++;
        if (number == 4) {
            number = 1;
        }
    }

    public Locale getCurrentLocale() throws Exception {
        Locale locale = null;
        if (currentLocale.getLanguage() == "pt" && currentLocale.getCountry() == "BR") {
            setNumber(1);
            locale = currentLocale;
            dataAtual = DateFormat.getDateInstance(0, currentLocale);
            setMostraData(dataAtual.format(data));
//        } else if (currentLocale.getLanguage() == "es" && currentLocale.getCountry() == "MX") {
//            setNumber(2);
//            locale = currentLocale;
//            dataAtual = DateFormat.getDateInstance(0, currentLocale);
//            setMostraData(dataAtual.format(data));
        } else if (currentLocale.getLanguage() == "es" && currentLocale.getCountry() == "ES") {
            setNumber(2);
            locale = currentLocale;
            dataAtual = DateFormat.getDateInstance(0, currentLocale);
            setMostraData(dataAtual.format(data));
        } else if (currentLocale.getLanguage() == "en" && currentLocale.getCountry() == "US") {
            setNumber(3);
            locale = currentLocale;
            dataAtual = DateFormat.getDateInstance(0, currentLocale);
            setMostraData(dataAtual.format(data));
        } else {
            setNumber(1);
            currentLocale = new Locale("pt", "BR");
            locale = currentLocale;
            dataAtual = DateFormat.getDateInstance(0, currentLocale);
            setMostraData(dataAtual.format(data));
        }
        sCurrentLocale = locale;
        return locale;
    }

    public void atualizarIdioma() throws Exception {
        if (null != this.pp) {
            LoginSatMobWeb obj = new LoginSatMobWeb();
            obj.atualizarIdioma(this.CodPessoa, currentLocale.getLanguage(), this.pp);

            // **********************************************************************************
            // Guardar idioma no cookie
            // **********************************************************************************
            String idioma = currentLocale.getLanguage();
            String idiomaCookie = Faces.getRequestCookie("idioma");

            if (null == idioma
                    || idioma.equals("")) {
                idioma = idiomaCookie;
            }

            if (null != idioma
                    && !idioma.equals("")) {
                Faces.addResponseCookie("idioma", idioma, 315360000);
            }
            // **********************************************************************************
        }
    }

    public static Locale getsCurrentLocale() {
        return sCurrentLocale;
    }
}

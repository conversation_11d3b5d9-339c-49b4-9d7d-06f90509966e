<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/font-awesome.min.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/kanban.css" rel="stylesheet" />
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewParam name="sequencia" value="#{kanban.sequencia}"/>
                <f:viewAction action="#{kanban.buscaTicket()}"/>
            </f:metadata>
            <p:growl id="msgs" widgetVar="msgs"/>
            <h:form id="login">
                <p:panel id="telaLogin" style="background: -moz-linear-gradient(top,  #000 40%, #db2525 100%);
                         background: -webkit-linear-gradient(top,  #000 40%,#db2525 100%);
                         background: linear-gradient(to bottom, #000 40%,#db2525 100%);
                         min-height: 100vh; vertical-align: middle; display: block; margin: 0 auto;
                         width: 100vw; padding-top: 40px" rendered="#{kanban.logado eq false}">
                    <script type = "text/javascript">
                        var senha = '';
                        function adicionaSenha(char) {
                            senha = senha.concat(char);
                            var botao = document.querySelector("#login\\:senha".concat(senha.length));
                            botao.classList.remove("botoesSenha");
                            botao.classList.add("botoesSenhaPressionado");
                            if(senha.length === 6){
                                var campo = document.querySelector("#login\\:campoSenha");
                                campo.value = senha;
                                jQuery("#login\\:btnLogin").click();
                                senha = '';
                            }
                        };
                        function apagaSenha() {
                            if(senha.length !== 0){
                                var botao = document.querySelector("#login\\:senha".concat(senha.length));
                                botao.classList.remove("botoesSenhaPressionado");
                                botao.classList.add("botoesSenha");
                                senha = senha.substr(0,(senha.length-1));
                            };
                        };
                    </script>
                    
                    <p:panelGrid columns="1" id="digiteSenha" styleClass="panel-botoes" style="margin: 0 auto">
                        <h:outputText value="#{localemsgs.DigiteASenha}"/>
                        <p:inputText value="#{kanban.senha}" id="campoSenha" style="display:none"/>
                        <p:commandButton value="" update="msgs main" id="btnLogin" style="display: none" action="#{kanban.logar}">
                        </p:commandButton>
                    </p:panelGrid>
                    
                    <p:panelGrid columns="6" id="panelSenha" styleClass="panel-botoes" style="margin: 0 auto; margin-bottom: 15px; ">
                        <p:commandButton value="" id="senha1" styleClass="botoesSenha" disabled="true" />
                        <p:commandButton value="" id="senha2" styleClass="botoesSenha" disabled="true" />
                        <p:commandButton value="" id="senha3" styleClass="botoesSenha" disabled="true" />
                        <p:commandButton value="" id="senha4" styleClass="botoesSenha" disabled="true" />
                        <p:commandButton value="" id="senha5" styleClass="botoesSenha" disabled="true" />
                        <p:commandButton value="" id="senha6" styleClass="botoesSenha" disabled="true" />
                    </p:panelGrid>
                    
                    <p:panelGrid columns="3" styleClass="panel-botoes" style="margin: 0 auto; margin-bottom: 15px">
                        <p:commandButton value="1" update="login:b1" id="b1" class="botoes" onclick="adicionaSenha('1')" global="false"/>
                        <p:commandButton value="2" update="login:b2" id="b2" class="botoes" onclick="adicionaSenha('2')" global="false"/>
                        <p:commandButton value="3" update="login:b3" id="b3" class="botoes" onclick="adicionaSenha('3')" global="false"/>
                        <p:commandButton value="4" update="login:b4" id="b4" class="botoes" onclick="adicionaSenha('4')" global="false"/>
                        <p:commandButton value="5" update="login:b5" id="b5" class="botoes" onclick="adicionaSenha('5')" global="false"/>
                        <p:commandButton value="6" update="login:b6" id="b6" class="botoes" onclick="adicionaSenha('6')" global="false"/>
                        <p:commandButton value="7" update="login:b7" id="b7" class="botoes" onclick="adicionaSenha('7')" global="false"/>
                        <p:commandButton value="8" update="login:b8" id="b8" class="botoes" onclick="adicionaSenha('8')" global="false"/>
                        <p:commandButton value="9" update="login:b9" id="b9" class="botoes" onclick="adicionaSenha('9')" global="false"/>
                        <p:commandButton value="" id="bn" class="botoes botaoNulo" disabled="true"/>
                        <p:commandButton value="0" update="login:b0" id="b0" class="botoes" onclick="adicionaSenha('0')" global="false"/>
                        <p:commandButton value="&#8656;" update="login:bx" id="bx" class="botoes" onclick="apagaSenha()" global="false"/>
                        
                        <p:hotkey bind="0" handler="adicionaSenha('0')" update="msgs login:panelSenha" />
                        <p:hotkey bind="1" handler="adicionaSenha('1')" update="msgs login:panelSenha" />
                        <p:hotkey bind="2" handler="adicionaSenha('2')" update="msgs login:panelSenha" />
                        <p:hotkey bind="3" handler="adicionaSenha('3')" update="msgs login:panelSenha" />
                        <p:hotkey bind="4" handler="adicionaSenha('4')" update="msgs login:panelSenha" />
                        <p:hotkey bind="5" handler="adicionaSenha('5')" update="msgs login:panelSenha" />
                        <p:hotkey bind="6" handler="adicionaSenha('6')" update="msgs login:panelSenha" />
                        <p:hotkey bind="7" handler="adicionaSenha('7')" update="msgs login:panelSenha" />
                        <p:hotkey bind="8" handler="adicionaSenha('8')" update="msgs login:panelSenha" />
                        <p:hotkey bind="9" handler="adicionaSenha('9')" update="msgs login:panelSenha" />
                        <p:hotkey bind="backspace" handler="apagaSenha()" update="msgs login:panelSenha" />
                        
                    </p:panelGrid>
                    
                    <p:panelGrid columns="1" id="panelCadastroSenha" styleClass="panel-botoes" style="margin: 0 auto">
                        <p:commandButton value="#{localemsgs.Cadastrar}" action="#{kanban.preCadastroNovaSenha}" 
                                         id="cadastroSenha" styleClass="botaoCadastrarSenha" update="cadastrarNovaSenha:cadastrarSenha"/>
                    </p:panelGrid>                    
                </p:panel>
            </h:form>
            
            <h:form id="cadastrarNovaSenha">
                <p:dialog  widgetVar="dlgCadastrarSenha" positionType="absolute" responsive="true" id="dlgCadastrarSenha"
                           draggable="false" modal="true" closable="false" resizable="false" dynamic="true" 
                           showEffect="drop" hideEffect="drop" closeOnEscape="false"
                           style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="assets/img/icone_satmob_kanban.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Cadastrar}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panel id="cadastrarSenha" class="cadastrarSenha">
                        <h:outputText value="#{localemsgs.SenhaNumerica6Digitos}"/>
                        <p:spacer height="30"/> 
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3 no-background, ui-grid-col-9 no-background" layout="grid">
                            <p:outputLabel for="email" value="#{localemsgs.Email}: "  />
                            <p:inputText id="email" style="width: 100%" value="#{kanban.email}"
                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}">
                                <p:watermark for="email" value="#{localemsgs.Email}"/>
                            </p:inputText>

                            <p:outputLabel for="senha" value="#{localemsgs.NovaSenha}: "  />
                            <p:password id="senha" style="width: 100%" value="#{kanban.novaSenha}" placeholder="SenhaNumerica6Digitos"
                                        validatorMessage="#{localemsgs.Senha} #{localemsgs.DeveConter} 6 #{localemsgs.Digitos}."
                                        required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}">
                                <p:watermark for="senha" value="#{localemsgs.NovaSenha}"/>
                                <f:validateRegex pattern="^[0-9]{6,6}$" for="senha"/>
                            </p:password>
                        </p:panelGrid>

                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 30%; float: left">
                                <p:commandLink action="#{kanban.cadatrarNovaSenha}" update="msgs"
                                               title="#{localemsgs.Cadastrar}">
                                    <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                            <div style="width: 30%; float: left">
                                <p:commandLink oncomplete="PF('dlgCadastrarSenha').hide();"
                                               title="#{localemsgs.Fechar}">
                                    <p:graphicImage url="assets/img/icone_fechar.png" width="40" height="40" />
                                </p:commandLink>  
                            </div>
                        </div>          
                    </p:panel>
                </p:dialog>
            </h:form>
                
            <script>
                function gup( name )
                {
                    name = name.replace(/[\[]/,"\\\[").replace(/[\]]/,"\\\]");
                    var regexS = "[\\?&amp;]"+name+"=([^&amp;#]*)";
                    var regex = new RegExp( regexS );
                    var results = regex.exec( window.location.href );
                    if( results == null )
                        return "";
                    else if ( results[1] == 'SessaoExpirada' )
                    {
                        alert("Por motivos de segurança, sua sessão expirou.");
                    }
                    else if ( results[1] == '2' )
                    {
                        alert("Senha cadastrada com sucesso. Efetue login digitando empresa@matricula e a senha cadastrada anteriormente.");
                    }
                    else if ( results[1] == 'ErroValidacao' )
                    {
                        alert("Erro de validação. Tente novamente.");
                    }
                    else
                        return "_"+results[1];
                }
                var sessao = gup('msg');

            </script>

            <div id="bodyKanban">
                <header>
                    <h:form id="cabecalho">
                        <p:panel rendered="#{kanban.logado eq true}" style="background: transparent">
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row cabecalho">
                                    <div class="ui-grid-col-12">
                                        <img src="assets/img/icone_satmob_kanban.png" height="40" width="40"/>
                                        #{localemsgs.Tickets}
                                    </div>
                                </div>
                            </div>
                        </p:panel>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:panel rendered="#{kanban.logado eq true and kanban.visual eq false}" style="background: transparent;">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-1">
                                        <h:outputText    value="#{localemsgs.Usuario}:"/>
                                    </div>
                                    <div class="ui-grid-col-2">
                                        #{kanban.usuario}
                                    </div>
                                    <div class="ui-grid-col-1">
                                    </div>
                                    <div class="ui-grid-col-8">
                                        #{localemsgs.QtdTickets}: #{kanban.total}
                                    </div>
                                </p:panel>
                            </div>
                            <div class="ui-grid-row">
                                <p:panel id="status2" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-3">
                                        <h:outputText value="Você possui #{kanban.totalPendentes} tickets pendentes." rendered="#{kanban.totalPendentes gt 0}"/>
                                        <h:outputText value="Você não possui tickets pendentes." rendered="#{kanban.totalPendentes eq 0}"/>
                                    </div>
                                    <div class="ui-grid-col-9">
                                        <h:outputText value="Último acesso: "/>
                                        <h:outputText value="#{kanban.ultimoAcesso}" converter="conversorData"/>
                                    </div>
                                </p:panel>
                            </div>
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-12">
                                    <p:panel style="display: inline;">
                                        <p:dataTable id="tabelaKanban" value="#{kanban.allTickets}" paginator="true" rows="15" lazy="true"
                                                     rowsPerPageTemplate="5,10,15, 20, 25"
                                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Tickets}"
                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                     var="lista" rowKey="#{lista.sequencia}" selection="#{kanban.ticket}" selectionMode="single"
                                                     resizableColumns="true" styleClass="tabela"
                                                     emptyMessage="#{localemsgs.SemRegistros}"
                                                     scrollable="true" scrollWidth="100%"
                                                     style="font-size: 12px">
                                            <p:ajax event="rowDblselect" listener="#{kanban.abrirTicket}" update="formCadastrar main:status msgs"/>
                                            <p:column headerText="#{localemsgs.Status}" style="width: 95px">
                                                <h:outputText value="#{lista.fase}" style="#{lista.fase eq 'FEITO' ? 'color: green' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Descricao}" style="width: 220px">
                                                <h:outputText value="#{lista.descricao}" style="#{lista.fase eq 'FEITO' ? 'color: green' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Responsavel}" style="width: 200px">
                                                <h:outputText value="#{lista.nomePessoa}" style="#{lista.fase eq 'FEITO' ? 'color: green' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Sequencia}" style="width: 70px" class="text-right">
                                                <h:outputText value="#{lista.sequencia}" style="#{lista.fase eq 'FEITO' ? 'color: green' : 'color: black' }">
                                                    <f:convertNumber pattern="0000"/>
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Historico}">
                                                <h:outputText value="#{lista.historico}" style="#{lista.fase eq 'FEITO' ? 'color: green' : 'color: black' }"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </div>
                            </div>
                        </div>
                    </p:panel>
                    <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes" rendered="#{kanban.logado eq true}">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" action="#{kanban.preCadastro}"
                                           update="formCadastrar msgs main:status">
                                <p:graphicImage url="assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" action="#{kanban.abrirTicket}"
                                           update="formCadastrar msgs">
                                <p:graphicImage url="assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" action="#{kanban.prePesquisa}"
                                           update="formPesquisar main:status msgs">
                                <p:graphicImage url="assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Tickets}" action="#{kanban.alternarVisualizacao}"
                                           update="main msgs">
                                <p:graphicImage url="assets/img/icone_redondo_ticket.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Atas}" oncomplete="PF('dlgAtas').show();" rendered="false"
                                           update="formAtas msgs">
                                <p:graphicImage url="assets/img/icone_pdf.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style=" top: 0px; right: 5px; position: fixed">
                            <p:commandLink title="#{localemsgs.Voltar}" action="#{kanban.sair}">
                                <p:graphicImage url="assets/img/icone_sair.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                    </p:panel>
                    <p:panel id="ticketsVisuais" rendered="#{kanban.logado eq true and kanban.visual eq true}">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <p:panel id="statusVisuais1" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-1">
                                        <h:outputText value="#{localemsgs.Usuario}:"/>
                                    </div>
                                    <div class="ui-grid-col-2">
                                        #{kanban.usuario}
                                    </div>
                                    <div class="ui-grid-col-9">
                                        #{localemsgs.QtdTickets}: #{kanban.total}
                                    </div>
                                </p:panel>
                            </div>
                            <div class="ui-grid-row">
                                <p:panel id="statusVisuais2" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-3">
                                        <h:outputText value="Você possui #{kanban.totalPendentes} tickets pendentes." rendered="#{kanban.totalPendentes gt 0}"/>
                                        <h:outputText value="Você não possui tickets pendentes." rendered="#{kanban.totalPendentes eq 0}"/>
                                    </div>
                                    <div class="ui-grid-col-9">
                                        <h:outputText value="Último acesso: "/>
                                        <h:outputText value="#{kanban.ultimoAcesso}" converter="conversorData"/>
                                    </div>
                                </p:panel>
                            </div>
                            <div class="ui-grid-row">
                                <p:panel style="width: 16%; text-align: center; border: 1px solid black !important; border-radius: 0px;">Fazer</p:panel>
                                <p:panel style="width: 16%; text-align: center; border: 1px solid black !important; border-radius: 0px;">Desenvolver</p:panel>
                                <p:panel style="width: 16%; text-align: center; border: 1px solid black !important; border-radius: 0px;">Fila P/ Teste</p:panel>
                                <p:panel style="width: 16%; text-align: center; border: 1px solid black !important; border-radius: 0px;">Teste</p:panel>
                                <p:panel style="width: 16%; text-align: center; border: 1px solid black !important; border-radius: 0px;">Implantar</p:panel>
                                <p:panel style="width: 16%; text-align: center; border: 1px solid black !important; border-radius: 0px;">Feito</p:panel>
                            </div>
                              <p:inputText id="painelClicado" value="#{kanban.painelClicado}" style="display:none"/>
                                <p:dashboard id="painelTickets" model="#{kanban.modelPainel}" binding="#{kanban.painel}">
                                    <p:ajax event="reorder" listener="#{kanban.handleReorder}" update="msgs main" />
                                </p:dashboard>

                                <p:dialog positionType="absolute" responsive="true"
                                    draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                    style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;"
                                    header="#{localemsgs.Confirmacao}" widgetVar="dlgOk">
                                    <div class="form-inline">
                                        <h:outputText value="#{localemsgs.ConfirmarEdicaoTicket}" style="text-align: center"/>
                                        <h:panelGrid columns="2" style="text-align: center">
                                            <p:commandLink oncomplete="PF('dlgOk').hide()" update="main msgs" action="#{kanban.confirmarEdicao}">
                                                <p:graphicImage url="assets/img/icone_adicionar.png" height="40"/>
                                            </p:commandLink>
                                            <p:commandLink oncomplete="PF('dlgOk').hide()" update="main msgs" action="#{kanban.cancelarEdicao}">
                                                <p:graphicImage url="assets/img/icone_fechar.png" height="40"/>
                                            </p:commandLink> 
                                        </h:panelGrid>
                                    </div>
                                </p:dialog>      <div class="ui-grid-row">
                        
                            </div>
                            <p:remoteCommand name="click" actionListener="#{kanban.handleClickPanel}"/>
                            <p:remoteCommand name="dbclick" actionListener="#{kanban.handleDbClickPanel}" update="formCadastrar main:status msgs"/>
                            <script>
                                var selecionado = '';
                                $('.postit').click(function(event) {
                                    var id_ = event.target.id.split('_');
                                    var id = id_[0] + '_' + id_[1] + '_' + id_[2];
                                    if(id !== '_undefined_undefined'){
                                        if(selecionado !== id){
                                            document.getElementById('main:painelClicado').value = id;

                                            $("#main\\:"+id.split("main:")[1]).toggleClass("postit-selecionado");   
                                            $("#main\\:"+id.split("main:")[1]).toggleClass("postit");

                                            $("#main\\:"+selecionado.split("main:")[1]).toggleClass("postit-selecionado");   
                                            $("#main\\:"+selecionado.split("main:")[1]).toggleClass("postit");
                                            selecionado = id;
                                            click();
                                        }
                                    }
                                });
                                $('.postit').dblclick(function(event) {
                                    console.log('db click');
                                    var id_ = event.target.id.split('_');
                                    var id = id_[0] + '_' + id_[1] + '_' + id_[2];
                                    if(id !== '_undefined_undefined'){
                                        if(selecionado !== id){
                                            document.getElementById('main:painelClicado').value = id;

                                            $("#main\\:"+id.split("main:")[1]).toggleClass("postit-selecionado");   
                                            $("#main\\:"+id.split("main:")[1]).toggleClass("postit");

                                            $("#main\\:"+selecionado.split("main:")[1]).toggleClass("postit-selecionado");   
                                            $("#main\\:"+selecionado.split("main:")[1]).toggleClass("postit");
                                            selecionado = id;
                                        }
                                        dbclick();
                                    }
                                });
                            </script>
                        </div>
                    </p:panel>
                </h:form>
            </div>
            
                <h:form id="formCadastrar">
                    <p:panel rendered="#{kanban.logado eq true}" style="background: transparent">
                        <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true" visible="#{kanban.flag eq 2}"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false"
                                  style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <script>
                                $(document).ready(function() {
                                    //first unbind the original click event
                                    PF('dlgCadastrar').closeIcon.unbind('click');

                                    //register your own
                                    PF('dlgCadastrar').closeIcon.click(function(e) {
                                        $("#formCadastrar\\:botaoFechar").click();
                                        //should be always called
                                        e.preventDefault();
                                    });

                                });
                            </script>
                            <p:commandButton widgetVar="botaoFechar" style="display: none" 
                                             oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" disabled="#{kanban.onchange}" />
                            </p:commandButton>
                            <f:facet name="header">
                                <img src="assets/img/icone_satmob_kanban_P.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.CadastrarTicket}" style="color:#022a48" rendered="#{kanban.flag eq 1}"/> 
                                <h:outputText value="#{localemsgs.Ticket} ##{kanban.ticket.sequencia.toBigInteger()}" style="color:#022a48" rendered="#{kanban.flag eq 2}"/> 
                            </f:facet>
                            <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                </p:confirmDialog>                        
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="usuario" value="#{localemsgs.Usuario}:" rendered="#{kanban.usuario eq null || kanban.usuario eq 'null'}"/>
                                    <p:selectOneMenu id="usuario" value="#{kanban.usuario}" style="width: 100%" class="usuario" 
                                                     rendered="#{kanban.usuario eq null || kanban.usuario eq 'null'}"
                                                     required="true" requiredMessage="#{localemsgs.SelecioneUsuario}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="null"/>
                                        <f:selectItems value="#{kanban.usuarios}" var="user" itemLabel="#{user.nome}" itemValue="#{user.nome}"/>
                                        <p:ajax event="itemSelect" listener="#{kanban.selecionarUsuario}"/>
                                    </p:selectOneMenu>

                                    <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "  />
                                    <p:inputText id="descricao" maxlength="80" value="#{kanban.ticket.descricao}" style="width: 100%" readonly="#{kanban.flag eq 2}"/>

                                    <p:outputLabel for="historico" value="#{localemsgs.Historico}: "  />
                                    <p:inputTextarea rows="3" scrollHeight="70" id="historico" 
                                                     required="true" requiredMessage="#{localemsgs.HistoricoObrigatorio}"
                                                     value="#{kanban.ticket.historico}" style="width: 100%" readonly="#{kanban.flag eq 2}"/>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-4,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="dono" value="#{localemsgs.Responsavel}: "  />
                                    <p:selectOneMenu id="dono" value="#{kanban.ticket.codPessoa}" style="width: 100%"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Responsavel}">
                                        <p:ajax event="change" listener="#{kanban.setOnchange(false)}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItems value="#{kanban.usuarios}" var="user" itemLabel="#{user.nome}" itemValue="#{user.codigo.toBigInteger().toString()}"/>
                                    </p:selectOneMenu>

                                    <p:outputLabel for="status" value="#{localemsgs.Status}: "  />
                                    <p:selectOneMenu id="status" value="#{kanban.ticket.fase}" style="width: 100%"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Status}">
                                        <p:ajax event="change" listener="#{kanban.setOnchange(false)}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Fazer}" itemValue="FAZER"/>
                                        <f:selectItem itemLabel="#{localemsgs.Desenvolver}" itemValue="DESENVOLVER"/>
                                        <f:selectItem itemLabel="#{localemsgs.FilaPTeste}" itemValue="FILA P/ TESTE"/>
                                        <f:selectItem itemLabel="#{localemsgs.Teste}" itemValue="TESTE"/>
                                        <f:selectItem itemLabel="#{localemsgs.Implantar}" itemValue="IMPLANTAR"/>
                                        <f:selectItem itemLabel="#{localemsgs.Feito}" itemValue="FEITO"/>
                                    </p:selectOneMenu>
                                </p:panelGrid>

                                <div class="ui-grid-row" style="padding-bottom: 3px;">
                                    <div style="width: 15%; float: left">
                                        <p:commandLink rendered="#{kanban.flag eq 1}" id="cadastro" update=":main main:painelTickets :msgs :cabecalho"
                                                   title="#{localemsgs.Cadastrar}" action="#{kanban.cadastrarTicket}">
                                            <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                        </p:commandLink>
                                        <p:commandLink rendered="#{kanban.flag eq 2}" id="edit" update=":msgs main main:painelTickets :cabecalho"
                                                       title="#{localemsgs.Editar}" action="#{kanban.editarTicket}">
                                            <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                        </p:commandLink>
                                    </div>
                                    <div style="width: 15%; float: left">
                                        <p:commandLink oncomplete="PF('dlgCadastrar').hide()" id="btnFecharComImagem">
                                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}"
                                                       icon="ui-icon-alert" disabled="#{kanban.onchange}"/>
                                            <p:graphicImage url="assets/img/icone_fechar.png" width="40" height="40" />
                                        </p:commandLink>

                                        <p:blockUI block="formCadastrar:cadastrar" trigger="formCadastrar:edit"/>  
                                    </div>
                                </div>

                                <p:spacer height="10" rendered="#{kanban.flag eq 2}"/>

                                <p:panelGrid columns="1" columnClasses="ui-grid-col-12" 
                                             layout="grid" styleClass="ui-panelgrid-blank" id="historicoMovimentacoes"
                                             style="background-color: transparent;"
                                             rendered="#{kanban.flag eq 2}">
                                    <p:panel id="cabecalhoMovimentacoes" style="background: transparent">
                                        <img src="assets/img/icone_satmob_fopag_P.png" height="30"
                                             width="30" rendered="#{kanban.flag eq 2}"/>
                                        <p:spacer width="5"/>
                                        <h:outputText value="#{localemsgs.Historico}" style="font-size: 14px; font-weight: bold"/>
                                    </p:panel>

                                    <p:dataTable id="tabelaMovimentacao" value="#{kanban.movimentacao}"
                                                 style="font-size: 12px" var="listaMov"
                                                 styleClass="tabela" scrollHeight="80"
                                                 resizableColumns="true" scrollable="true"
                                                 emptyMessage="#{localemsgs.SemRegistros}" rendered="#{kanban.flag eq 2}">
                                        <p:column headerText="#{localemsgs.Ordem}" style="width: 50px" class="text-right">
                                            <h:outputText value="#{listaMov.ordem}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Status}" style="width: 115px">
                                            <h:outputText value="#{listaMov.fase}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Responsavel}" style="width: 195px">
                                            <h:outputText value="#{listaMov.nomePessoa}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Data}" style="width:70px">
                                            <h:outputText value="#{listaMov.data}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 70px">
                                            <h:outputText value="#{listaMov.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 50px">
                                            <h:outputText value="#{listaMov.hr_Alter}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" style="width: 100px">
                                            <h:outputText value="#{listaMov.operador}"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panelGrid>

                                <p:spacer height="10" rendered="#{kanban.flag eq 2}"/>

                                <p:panel id="cabecalhoComentarios" style="background: transparent" rendered="#{kanban.flag eq 2}">
                                        <img src="assets/img/icone_satmob_propostas_P.png" height="30"
                                             width="30"/>
                                        <p:spacer width="5"/>
                                        <h:outputText value="#{localemsgs.Comentarios}" style="font-size: 14px; font-weight: bold"/>
                                </p:panel>
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-11,ui-grid-col-1"
                                             layout="grid" styleClass="ui-panelgrid-blank" id="comentariosTicket"
                                             style="background-color: transparent;"
                                             rendered="#{kanban.flag eq 2}">
                                    <p:dataTable id="tabelaComentario" value="#{kanban.comentarios}"
                                                 style="font-size: 12px" var="listaCom"
                                                 styleClass="tabela" scrollHeight="80"
                                                 resizableColumns="true" scrollable="true"
                                                 emptyMessage="#{localemsgs.SemRegistros}" rendered="#{kanban.flag eq 2}">
                                        <p:column style="width:16px">
                                            <p:rowToggler />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Descricao}" style="width: 115px">
                                            <h:outputText value="#{listaCom.descricao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Responsavel}" style="width: 145px">
                                            <h:outputText value="#{listaCom.nomePessoa}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" style="width: 100px">
                                            <h:outputText value="#{listaCom.operador}"/>
                                        </p:column>
                                        <p:rowExpansion  class="borderNone">
                                            <p:panel class="borderNone">
                                                <p:panelGrid columnClasses="ui-grid-col-2,ui-grid-col-10" columns="2" style="width: 100%;">
                                                    <h:outputText value="#{localemsgs.Detalhes}:" />
                                                    <h:outputText value="#{listaCom.detalhes}" style="font-weight: bold; white-space: normal"/>
                                                </p:panelGrid>
                                                <p:panelGrid styleClass="borderNone" class="borderNone" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                             columns="4" style="width: 100%;">
                                                    <h:outputText value="#{localemsgs.Dt_Alter}:" />
                                                    <h:outputText value="#{listaCom.dt_Alter}" converter="conversorData" style="font-weight: bold;"/>

                                                    <h:outputText value="#{localemsgs.Hr_Alter}:" />
                                                    <h:outputText value="#{listaCom.hr_Alter}" style="font-weight: bold"/>
                                                </p:panelGrid>
                                            </p:panel>
                                        </p:rowExpansion>
                                    </p:dataTable>

                                    <p:commandLink title="#{localemsgs.Adicionar}" action="#{kanban.preCadastroComentario}"
                                                   update="msgs formCadastrar:panelAddComentario">
                                        <p:graphicImage url="assets/img/icone_redondo_adicionar.png" height="30"/>
                                    </p:commandLink>

                                    <p:dialog widgetVar="dlgAddComentario" header="#{localemsgs.AdicionarComentario}"
                                              closable="true" resizable="false" width="400" modal="true" appendTo="@(body)"
                                              hideEffect="fade">
                                        <p:panelGrid  columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" id="panelAddComentario" style="width: 100%;">
                                            <p:outputLabel for="comentDesc" value="#{localemsgs.Descricao}:"/>
                                            <p:inputText id="comentDesc" value="#{kanban.novoComentario.descricao}" style="width: 100%">
                                                <p:watermark value="#{localemsgs.Descricao}" for="comentDesc"/>
                                            </p:inputText>

                                            <p:outputLabel for="comentDet" value="#{localemsgs.Detalhes}:"/>
                                            <p:inputTextarea id="comentDet" value="#{kanban.novoComentario.detalhes}" style="width: 100%" rows="3">
                                                <p:watermark value="#{localemsgs.Detalhes}" for="comentDet"/>
                                            </p:inputTextarea>

                                            <p:commandLink action="#{kanban.adicionarNovoComentario}" id="btnAdicionarComentario"
                                                           title="#{localemsgs.Selecionar}" update="formCadastrar:tabelaComentario msgs"
                                                           partialSubmit="true" process="panelAddComentario">
                                                <p:graphicImage url="assets/img/icone_adicionar.png" width="30" height="30" />
                                            </p:commandLink>

                                        </p:panelGrid>                                    
                                    </p:dialog>
                                </p:panelGrid>
                            </p:panel>
                        </p:dialog>
                    </p:panel>
                </h:form>

                <!--Pesquisar tickets-->
                <h:form id="formPesquisar">
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_kanban.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarTickets}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "  />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="descricao" value="#{kanban.ticket.descricao}" style="width: 100%">
                                        <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="historico" value="#{localemsgs.Historico}: "  />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="historico" value="#{kanban.ticket.historico}" style="width: 100%"/>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="dono" value="#{localemsgs.Responsavel}: "  />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="dono" value="#{kanban.ticket.codPessoa}" style="width: 100%">
                                       <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                       <f:selectItems value="#{kanban.usuarios}" var="user" itemLabel="#{user.nome}" itemValue="#{user.codigo}"/>
                                   </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="status" value="#{localemsgs.Status}: "  />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="status" value="#{kanban.ticket.fase}" style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Fazer}" itemValue="FAZER"/>
                                        <f:selectItem itemLabel="#{localemsgs.Desenvolver}" itemValue="DESENVOLVER"/>
                                        <f:selectItem itemLabel="#{localemsgs.FilaPTeste}" itemValue="FILA P/ TESTE"/>
                                        <f:selectItem itemLabel="#{localemsgs.Teste}" itemValue="TESTE"/>
                                        <f:selectItem itemLabel="#{localemsgs.Implantar}" itemValue="IMPLANTAR"/>
                                        <f:selectItem itemLabel="#{localemsgs.Feito}" itemValue="FEITO"/>
                                    </p:selectOneMenu>
                                    </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{kanban.pesquisar}" oncomplete="PF('dlgPesquisar').hide()"
                                               update=" :main:tabelaKanban main:status :msgs :cabecalho corporativo"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>            
                        </p:panel>
                    </p:dialog>
                </h:form>


                <h:form id="formAtas">
                    <p:dialog  widgetVar="dlgAtas" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_kanban.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Atas}" style="color:#022a48" /> 
                        </f:facet>
                        <p:tabView id="tabs" activeIndex="0">
                            <p:tab id="listaAtas" title="#{localemsgs.Atas}">
                                <p:panel id="historico" style="background: transparent">
                                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                                        <h:outputText value="#{localemsgs.HistoricoAtas}"/> 
                                    </div>

                                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                                        <p:panel style="background: transparent; border: 1px solid #E6E6E6 !important">
                                            <p:dataTable value="#{kanban.atas}" scrollHeight="200" scrollable="true" style="background: transparent"
                                                         rowKey="#{atas.data}" var="atas" id="tabelaAtas" styleClass="tabelaArquivos">
                                                <p:column headerText="#{localemsgs.Atas}">
                                                    <p:commandLink actionListener="#{postoservico.HandleFileDownload(listaDocumentos)}" ajax="false"
                                                                   value="#{atas.data}" update="msgs">
                                                        <p:fileDownload value="#{postoservico.download}" />
                                                    </p:commandLink>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.ModificadoEm}" style="width: 110px; text-align: center">
                                                    <h:outputText value="#{atas.dt_Alter}" converter="conversorData"/>
                                                </p:column>
                                                <p:column style="width: 30px">
                                                    <p:commandLink actionListener="#{postoservico.excluirDocumento(listaDocumentos)}"
                                                                   update="msgs formAtas:tabs:arquivos">
                                                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirDocumento}" icon="ui-icon-alert" />
                                                        <p:graphicImage  url="assets/img/icone_redondo_excluir.png" height="20" />
                                                    </p:commandLink>
                                                </p:column>
                                            </p:dataTable>
                                        </p:panel>
                                    </div>


                                    <div class="form-inline">
                                        <p:commandLink id="fechar" action="#{kanban.pesquisar}" oncomplete="PF('dlgPesquisar').hide()"
                                                       update=" :main:tabelaKanban main:status :msgs :cabecalho corporativo"
                                                       title="#{localemsgs.Pesquisar}">
                                            <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                        </p:commandLink>
                                    </div>            
                                </p:panel>
                            </p:tab>
                            <p:tab id="novaAta" title="#{localemsgs.InserirAta}">
                                <p:panel id="inserir" style="background: transparent">
                                    <div  style="text-align: justify; width: 100%; padding-bottom: 10px">
                                        <h:outputText value="#{localemsgs.ArrasteArquivo}:"/>
                                    </div>

                                    <div  style="text-align: center; width: 100%; height: 150px">
                                        <p:fileUpload id="upload" fileUploadListener="#{postoservico.HandleFileUpload}"
                                                      allowTypes="/(\.|\/)(pdf|jpe?g|xls|xlsx)$/" label="#{localemsgs.Pesquisar}" auto="true"
                                                      invalidFileMessage="#{localemsgs.ArquivoInvalido}" 
                                                      dragDropSupport="true" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                                      update="msgs formAtas:tabs:tabelaAtas"
                                                      previewWidth="10" skinSimple="true">
                                            <h:outputText value="#{localemsgs.ArrasteAqui}" id="ArrasteAqui"
                                                          style="text-align: justify; color: lightgray; top: 30px; position: relative;"/>
                                        </p:fileUpload>
                                    </div>

                                    <div  style="text-align: justify; width: 100%; padding-bottom: 10px">
                                        <h:outputText value="#{localemsgs.Mensagem}:"/>
                                        <p:inputText id="mensagem" style="width: 100%"/>
                                    </div>

                                    <div class="form-inline">
                                        <p:commandLink id="btnInserir" action="#{kanban.pesquisar}" oncomplete="PF('dlgPesquisar').hide()"
                                                       update=" :main:tabelaKanban main:status :msgs :cabecalho corporativo"
                                                       title="#{localemsgs.Pesquisar}">
                                            <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                        </p:commandLink>
                                    </div>
                                </p:panel>
                            </p:tab>
                        </p:tabView>
                    </p:dialog>
                </h:form>
            
            <ui:insert name="loading" >
                <ui:include src="assets/template/loading.xhtml" />
            </ui:insert>
                
            <h:form id="corporativo">
                <p:panel rendered="#{kanban.logado eq true}" style="background: transparent;" class="rodape">
                    <footer>
                        <div class="footer-toggler">
                            <a href="#footer-toggle" id="footer-toggle" >
                                <i class="fa fa-bars" style="font-size: 18px"></i>
                            </a>

                        </div>
                        <div class="footer-body" id="footer-body">
                            <div class="container">
                            <div class="col-sm-9 ">
                                <h:outputText value="#{localemsgs.Fazer}: "/>
                                <p:selectBooleanCheckbox value="#{kanban.fazer}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.filtrarFase}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="20px"/>

                                <h:outputText value="#{localemsgs.Desenvolver}: " />
                                <p:selectBooleanCheckbox value="#{kanban.desenvolver}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.filtrarFase}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="20px"/>

                                <h:outputText value="#{localemsgs.FilaPTeste}: " />
                                <p:selectBooleanCheckbox value="#{kanban.fila}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.filtrarFase}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="20px"/>

                                <h:outputText value="#{localemsgs.Teste}: " />
                                <p:selectBooleanCheckbox value="#{kanban.teste}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.filtrarFase}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="20px"/>

                                <h:outputText value="#{localemsgs.Implantar}: " />
                                <p:selectBooleanCheckbox value="#{kanban.implantar}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.filtrarFase}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="20px"/>

                                <h:outputText value="#{localemsgs.Feito}: " />
                                <p:selectBooleanCheckbox value="#{kanban.feito}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.filtrarFase}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="20px"/>

                                <h:outputText value="#{localemsgs.MarcarDesmarcar}: " />
                                <p:selectBooleanCheckbox value="#{kanban.marcarDesmarcar}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.desmarcarTodos}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="20px"/>

                                <h:outputText value="#{localemsgs.LimparFiltros}: " />
                                <p:selectBooleanCheckbox value="#{kanban.removerFiltros}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.removerTodos}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="20px"/>

                                <h:outputText value="#{localemsgs.MeusTickets}: " 
                                              style="#{kanban.ticketsUsuario ? 'font-weight: bold' : 'font-weight: normal' }"/>
                                <p:selectBooleanCheckbox value="#{kanban.ticketsUsuario}">
                                    <p:ajax update="msgs main:tabelaKanban main:status corporativo" listener="#{kanban.filtrarTicktesUsuario()}" />
                                </p:selectBooleanCheckbox>
                            </div>
                            <div class="col-sm-3">
                                <table class="footer-logos">
                                    <tr>
                                        <td><img src="assets/img/logo_satweb.png" /></td>
                                        <td>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        </div>
                    </footer>
                    <script>
                        $("#footer-toggle").click(function(e) {
                            e.preventDefault();
                            $("footer").toggleClass("toggled");
                            $(".footer-toggler").toggleClass("toggled");
                            $(".status").toggleClass("toggled");
                            $("#body").toggleClass("toggled");
                            $(".ui-datatable-scrollable-body").toggleClass("toggled");
                        });
                    </script>
                </p:panel>
            </h:form>
        </h:body>
    </f:view>
</html>

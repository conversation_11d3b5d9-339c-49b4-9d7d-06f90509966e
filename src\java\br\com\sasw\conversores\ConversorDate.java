/*
 */
package br.com.sasw.conversores;

import static br.com.sasw.utils.Mascaras.getPadraoDataS;
import static br.com.sasw.utils.Mascaras.removeMascaraData2;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorDate")
public class ConversorDate implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        try {
            return removeMascaraData2(value.replace(" ", ""));
        } catch (Exception e) {
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            Date date = (Date) value;
            return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern(getPadraoDataS()));
        } catch (Exception e) {
        }
        return value.toString();
    }
}

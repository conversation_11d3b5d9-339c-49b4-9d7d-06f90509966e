/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 04-Sep-2018, 09:45:26
    Author     : SASWRichard
*/


.ui-icon-calendar {
    background-image:  url('../img/icone_escaladodia_40.png') !important;
    background-position: center center !important;
    width: 40px !important;
    height: 40px !important;
    left: 0px !important;
    margin-left: 0px !important;
    top: 1px !important;
    margin-top: 0px !important;
}

.cert.ui-fileupload{
    width: 100%;
    height: 100%;
    border: none;
    background: white;
    border-radius: 3px;
}


.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

.cidade .ui-autocomplete-panel {
    width: 100% !important;
}
.cidade .ui-autocomplete-input{
    width: 100% !important;
}
.pstserv .ui-autocomplete-panel {
    width: 100% !important;
}
.pstserv .ui-autocomplete-input{
    width: 100% !important;
}

.pstservPesq .ui-autocomplete-panel {
    width: 230px !important;
}
.pstservPesq .ui-autocomplete-input{
    width: 230px !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.cadastrar{
    width: 90vh;
    min-width: 100%;
}


@media all and (min-width: 768px) {
    .cadastrar{
        width: 700px;
    }
}
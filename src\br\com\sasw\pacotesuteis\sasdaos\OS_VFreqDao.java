/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.OS_VFreq;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class OS_VFreqDao {

    private Persistencia persistencia;

    public OS_VFreqDao() {
    }

    public OS_VFreqDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public List<OS_VFreq> listarOS_VFreq(String OS, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<OS_VFreq> retorno = new ArrayList<>();
            String sql = " SELECT * FROM OS_VFreq \n"
                    + " WHERE OS_VFreq.OS = ? AND OS_VFreq.CodFil = ? \n"
                    + " ORDER BY Tipo, <PERSON>as desc, DU desc, DiaSem, Hora1, Hora2 ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(codFil);
            consulta.select();
            OS_VFreq os_vfreq;
            while (consulta.Proximo()) {
                os_vfreq = new OS_VFreq();
                os_vfreq.setOS(consulta.getString("OS"));
                os_vfreq.setCodFil(consulta.getString("CodFil"));
                os_vfreq.setDiaSem(consulta.getString("DiaSem"));
                os_vfreq.setHora1(consulta.getString("Hora1"));
                os_vfreq.setTipo(consulta.getString("Tipo"));
                os_vfreq.setHora2(consulta.getString("Hora2"));
                os_vfreq.setRotaC(consulta.getString("RotaC"));
                os_vfreq.setDias(consulta.getString("Dias"));
                os_vfreq.setDU(consulta.getString("DU"));
                os_vfreq.setOperador(consulta.getString("Operador"));
                os_vfreq.setDt_Alter(consulta.getString("Dt_Alter"));
                os_vfreq.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(os_vfreq);
            }
            consulta.Close();
            return (retorno);
        } catch (Exception e) {
            throw new Exception("OS_VFreqDao.listarOS_VFreq - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM OS_VFreq \n"
                    + " WHERE OS_VFreq.OS = " + OS + " AND OS_VFreq.CodFil = " + codFil + " \n"
                    + " ORDER BY Tipo, Dias desc, DU desc, DiaSem, Hora1, Hora2 ");
        }
    }

    public OS_VFreq getById(String OS, String codFil, String diaSem, String hora1) throws Exception {
        final String sql = "SELECT * FROM OS_VFreq\n"
                + "    WHERE OS = ? \n"
                + "        AND CodFil = ? \n"
                + "        AND DiaSem = ? \n"
                + "        AND Hora1 = ? \n";

        Consulta consulta = null;
        OS_VFreq frequencia = null;

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(codFil);
            consulta.setString(diaSem);
            consulta.setString(hora1);

            consulta.select();
            if (consulta.Proximo()) {
                frequencia = new OS_VFreq();

                frequencia.setOS(consulta.getString("OS"));
                frequencia.setCodFil(consulta.getString("CodFil"));
                frequencia.setDiaSem(consulta.getString("DiaSem"));
                frequencia.setHora1(consulta.getString("Hora1"));
                frequencia.setTipo(consulta.getString("Tipo"));
                frequencia.setHora2(consulta.getString("Hora2"));
                frequencia.setRotaC(consulta.getString("RotaC"));
                frequencia.setDias(consulta.getString("Dias"));
                frequencia.setDU(consulta.getString("DU"));
                frequencia.setOperador(consulta.getString("Operador"));
                frequencia.setDt_Alter(consulta.getString("Dt_Alter"));
                frequencia.setHr_Alter(consulta.getString("Hr_Alter"));
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (consulta != null) {
                try {
                    consulta.Close();
                } catch (Exception e) {
                    // log
                }
            }
        }
        return frequencia;
    }

    public void insert(OS_VFreq frequencia) throws Exception {
        final String sql = ";INSERT INTO OS_VFreq(\n"
                + "OS, CodFil, DiaSem, Hora1, Tipo, Hora2, RotaC,\n"
                + "Dias, DU, Operador, Dt_Alter, Hr_Alter)\n"
                + "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?,"
                + "GETDATE(), FORMAT (GETDATE(), 'HH:mm'));";

        Consulta consulta = null;

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(frequencia.getOS());
            consulta.setString(frequencia.getCodFil());
            consulta.setString(frequencia.getDiaSem());
            consulta.setString(frequencia.getHora1());
            consulta.setString(frequencia.getTipo());
            consulta.setString(frequencia.getHora2());
            consulta.setString(frequencia.getRotaC());
            consulta.setString(frequencia.getDias());
            consulta.setString(frequencia.getDU());
            consulta.setString(frequencia.getOperador());

            consulta.insert();
        } catch (Exception e) {
            throw e;
        } finally {
            if (consulta != null) {
                try {
                    consulta.Close();
                } catch (Exception e) {
                    // log
                }
            }
        }
    }

    public void update(OS_VFreq antigo, OS_VFreq frequencia) throws Exception {
        final String sql = ";UPDATE OS_VFreq\n"
                + "SET Hora1 = ?, Tipo = ?, Hora2 = ?, RotaC = ?,\n"
                + "Dias = ?, DU = ?, Operador = ?, "
                + "Dt_Alter = GETDATE(), Hr_Alter = FORMAT(GETDATE(), 'HH:mm')\n"
                + "WHERE OS = ? \n"
                + "    AND CodFil = ? \n"
                + "    AND DiaSem = ? \n"
                + "    AND Hora1 = ? ;";

        Consulta consulta = null;

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(frequencia.getHora1());
            consulta.setString(frequencia.getTipo());
            consulta.setString(frequencia.getHora2());
            consulta.setString(frequencia.getRotaC());
            consulta.setString(frequencia.getDias());
            consulta.setString(frequencia.getDU());
            consulta.setString(frequencia.getOperador());

            consulta.setString(antigo.getOS());
            consulta.setString(antigo.getCodFil());
            consulta.setString(antigo.getDiaSem());
            consulta.setString(antigo.getHora1());

            consulta.update();
        } catch (Exception e) {
            throw e;
        } finally {
            if (consulta != null) {
                try {
                    consulta.Close();
                } catch (Exception e) {
                    // log
                }
            }
        }
    }

    public void delete(OS_VFreq frequencia) throws Exception {
        String sql = "Delete from OS_VFreq\n"
                + " Where OS   = ?\n"
                + " and DiaSem = ?\n"
                + " and Hora1  = ?\n"
                + " and CodFil = ?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(frequencia.getOS());
            consulta.setString(frequencia.getDiaSem());
            consulta.setString(frequencia.getHora1());
            consulta.setString(frequencia.getCodFil());

            consulta.delete();
        } catch (Exception e) {
            throw e;
        } finally {

        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.compostas;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.compostas.Rt_PercRotasClientes;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rt_PercRotasClientesDao {

    public List<Rt_PercRotasClientes> listarClientesRota(String query, String codFil, String data, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT Rt_Perc.Sequencia Rt_Perc_Sequencia, Rt_Perc.Parada Rt_Perc_Parada, Rt_Perc.CodFil Rt_Perc_CodFil, Rt_Perc.Hora1 Rt_Perc_Hora1, \n"
                    + "Rt_Perc.ER Rt_Perc_ER, Rt_Perc.CodCli1 Rt_Perc_CodCli1, Rt_Perc.NRed Rt_Perc_NRed, Rt_Perc.CodCli2 Rt_Perc_CodCli2, \n"
                    + "Rt_Perc.Hora1D Rt_Perc_Hora1D, Rt_Perc.TipoSrv Rt_Perc_TipoSrv, Rt_Perc.Chave Rt_Perc_Chave, Rt_Perc.DPar Rt_Perc_DPar, \n"
                    + "Rt_Perc.Regiao Rt_Perc_Regiao, Rt_Perc.Observ Rt_Perc_Observ, Rt_Perc.Valor Rt_Perc_Valor, Rt_Perc.HrSaida Rt_Perc_HrSaida, \n"
                    + "Rt_Perc.HrBaixa Rt_Perc_HrBaixa, Rt_Perc.HrCheg Rt_Perc_HrCheg, Rt_Perc.Atraso Rt_Perc_Atraso, Rt_Perc.TempoEspera Rt_Perc_TempoEspera, \n"
                    + "Rt_Perc.OS Rt_Perc_OS, Rt_Perc.OperIncl Rt_Perc_OperIncl, Rt_Perc.Dt_Incl Rt_Perc_Dt_Incl, Rt_Perc.Hr_Incl Rt_Perc_Hr_Incl, \n"
                    + "Rt_Perc.Operador Rt_Perc_Operador, Rt_Perc.Dt_Alter Rt_Perc_Dt_Alter, Rt_Perc.Hr_Alter Rt_Perc_Hr_Alter, Rt_Perc.OperExcl Rt_Perc_OperExcl, \n"
                    + "Rt_Perc.Dt_Excl Rt_Perc_Dt_Excl, Rt_Perc.Hr_Excl Rt_Perc_Hr_Excl, Rt_Perc.OperFech Rt_Perc_OperFech, Rt_Perc.Dt_Fech Rt_Perc_Dt_Fech, \n"
                    + "Rt_Perc.Hr_Fech Rt_Perc_Hr_Fech, Rt_Perc.CodLan Rt_Perc_CodLan, Rt_Perc.Pedido Rt_Perc_Pedido, Rt_Perc.Flag_Excl Rt_Perc_Flag_Excl,\n"
                    + "Rotas.Sequencia Rotas_Sequencia, Rotas.Rota Rotas_Rota, Rotas.Data Rotas_Data, Rotas.CodFil Rotas_CodFil, Rotas.TpVeic Rotas_TpVeic, \n"
                    + "Rotas.Viagem Rotas_Viagem, Rotas.ATM Rotas_ATM, Rotas.BACEN Rotas_BACEN, Rotas.Aeroporto Rotas_Aeroporto, Rotas.HrLargada Rotas_HrLargada, \n"
                    + "Rotas.HrChegada Rotas_HrChegada, Rotas.HrIntIni Rotas_HrIntIni, Rotas.HrIntFim Rotas_HrIntFim, Rotas.HsTotal Rotas_HsTotal, \n"
                    + "Rotas.Observacao Rotas_Observacao, Rotas.KmSaida Rotas_KmSaida, Rotas.KmChegada Rotas_KmChegada, Rotas.KmTotal Rotas_KmTotal, \n"
                    + "Rotas.HrUrb Rotas_HrUrb, Rotas.HrInter Rotas_HrInter, Rotas.PrdUrb Rotas_PrdUrb, Rotas.PrdInter Rotas_PrdInter, \n"
                    + "Rotas.GuiasUrb Rotas_GuiasUrb, Rotas.GuiasInter Rotas_GuiasInter, Rotas.KmTotConj Rotas_KmTotConj, Rotas.KmTotConj2 Rotas_KmTotConj2, \n"
                    + "Rotas.DtFim Rotas_DtFim, Rotas.Operador Rotas_Operador, Rotas.Dt_Alter Rotas_Dt_Alter, Rotas.Hr_Alter Rotas_Hr_Alter, \n"
                    + "Rotas.OperFech Rotas_OperFech, Rotas.Dt_Fech Rotas_Dt_Fech, Rotas.Hr_Fech Rotas_Hr_Fech, Rotas.Flag_Excl Rotas_Flag_Excl,\n"
                    + "Clientes.CodFil Clientes_CodFil, Clientes.Banco Clientes_Banco, Clientes.TpCli Clientes_TpCli, Clientes.CodCli Clientes_CodCli, \n"
                    + "Clientes.Agencia Clientes_Agencia, Clientes.SubAgencia Clientes_SubAgencia, Clientes.Lote Clientes_Lote, Clientes.NRed Clientes_NRed, \n"
                    + "Clientes.Nome Clientes_Nome, Clientes.Ende Clientes_Ende, Clientes.Bairro Clientes_Bairro, Clientes.CodCidade Clientes_CodCidade, \n"
                    + "Clientes.Cidade Clientes_Cidade, Clientes.Estado Clientes_Estado, Clientes.CEP Clientes_CEP, Clientes.Fone1 Clientes_Fone1, Clientes.Fone2 "
                    + "Clientes_Fone2, Clientes.Fax Clientes_Fax, Clientes.Contato Clientes_Contato, Clientes.Email Clientes_Email, \n"
                    + "Clientes.SenhaWEB Clientes_SenhaWEB, Clientes.RamoAtiv Clientes_RamoAtiv, Clientes.Regiao Clientes_Regiao, \n"
                    + "Clientes.Latitude Clientes_Latitude, Clientes.Longitude Clientes_Longitude, Clientes.PrdApoio Clientes_PrdApoio, \n"
                    + "Clientes.Risco Clientes_Risco, Clientes.Malotes Clientes_Malotes, Clientes.NroChave Clientes_NroChave, Clientes.GrpChave Clientes_GrpChave, \n"
                    + "Clientes.CGC Clientes_CGC, Clientes.IE Clientes_IE, Clientes.Insc_Munic Clientes_Insc_Munic, Clientes.CEI Clientes_CEI, \n"
                    + "Clientes.CPF Clientes_CPF, Clientes.RG Clientes_RG, Clientes.RateioFat Clientes_RateioFat, Clientes.RateioTes Clientes_RateioTes, \n"
                    + "Clientes.DiaFechaFat Clientes_DiaFechaFat, Clientes.DiaVencNF Clientes_DiaVencNF, Clientes.RetencoesFat Clientes_RetencoesFat, \n"
                    + "Clientes.MarcaATM Clientes_MarcaATM, Clientes.Retorno Clientes_Retorno, Clientes.Cheque Clientes_Cheque, Clientes.Envelope Clientes_Envelope, \n"
                    + "Clientes.Vr_A Clientes_Vr_A, Clientes.Ced_A Clientes_Ced_A, Clientes.Ced_AP Clientes_Ced_AP, Clientes.Vr_B Clientes_Vr_B, \n"
                    + "Clientes.Ced_B Clientes_Ced_B, Clientes.Ced_BP Clientes_Ced_BP, Clientes.Vr_C Clientes_Vr_C, Clientes.Ced_C Clientes_Ced_C, \n"
                    + "Clientes.Ced_CP Clientes_Ced_CP, Clientes.Vr_D Clientes_Vr_D, Clientes.Ced_D Clientes_Ced_D, Clientes.Ced_DP Clientes_Ced_DP, \n"
                    + "Clientes.Vr_E Clientes_Vr_E, Clientes.Ced_E Clientes_Ced_E, Clientes.Ced_EP Clientes_Ced_EP, Clientes.EndCob Clientes_EndCob, \n"
                    + "Clientes.CodCidCod Clientes_CodCidCod, Clientes.CidCob Clientes_CidCob, Clientes.UFCob Clientes_UFCob, Clientes.CEPCob Clientes_CEPCob, \n"
                    + "Clientes.EmailCob Clientes_EmailCob, Clientes.Obs Clientes_Obs, Clientes.Situacao Clientes_Situacao, Clientes.InterfExt Clientes_InterfExt, \n"
                    + "Clientes.CodExt Clientes_CodExt, Clientes.CodIntCli Clientes_CodIntCli, Clientes.CodPtoCli Clientes_CodPtoCli, \n"
                    + "Clientes.CercaElet Clientes_CercaElet, Clientes.DtSituacao Clientes_DtSituacao, Clientes.Dt_Cad Clientes_Dt_Cad, \n"
                    + "Clientes.Dt_Alter Clientes_Dt_Alter, Clientes.Hr_Alter Clientes_Hr_Alter, Clientes.Dt_UltMov Clientes_Dt_UltMov, \n"
                    + "Clientes.Oper_Inc Clientes_Oper_Inc, Clientes.Oper_Alt Clientes_Oper_Alt, Clientes.Codigo Clientes_Codigo, Clientes.CCusto Clientes_CCusto, \n"
                    + "Clientes.TipoPagto Clientes_TipoPagto, Clientes.Limite Clientes_Limite, Clientes.LimiteSeguro Clientes_LimiteSeguro, \n"
                    + "Clientes.LimiteColeta Clientes_LimiteColeta, Clientes.Patrimonio Clientes_Patrimonio, Clientes.Proprietario Clientes_Proprietario, \n"
                    + "Clientes.Representante Clientes_Representante, Clientes.AtivEconomica Clientes_AtivEconomica, Clientes.CodCofre Clientes_CodCofre, \n"
                    + "Clientes.GrpRota Clientes_GrpRota\n"
                    + "FROM Rt_Perc\n"
                    + "LEFT JOIN Clientes on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                  AND Clientes.CodFil = Rt_Perc.CodFil\n"
                    + "LEFT JOIN Rotas ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "WHERE (Clientes.Codigo = ? OR Clientes.NRed like ?) AND Clientes.CodFil = ? AND Rotas.DtFim >= ? AND Rt_Perc.Flag_Excl <> '*' ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(query); // Codigo
            consulta.setString("%" + query + "%"); // NRed
            consulta.setString(codFil);
            consulta.setString(data);
            consulta.select();
            List<Rt_PercRotasClientes> retorno = new ArrayList<>();
            Rt_PercRotasClientes rt_PercRotasClientes;
            while (consulta.Proximo()) {
                rt_PercRotasClientes = new Rt_PercRotasClientes();

                rt_PercRotasClientes.getRt_Perc().setSequencia(consulta.getString("Rt_Perc_Sequencia"));
                rt_PercRotasClientes.getRt_Perc().setParada(consulta.getInt("Rt_Perc_Parada"));
                rt_PercRotasClientes.getRt_Perc().setCodFil(consulta.getString("Rt_Perc_CodFil"));
                rt_PercRotasClientes.getRt_Perc().setHora1(consulta.getString("Rt_Perc_Hora1"));
                rt_PercRotasClientes.getRt_Perc().setER(consulta.getString("Rt_Perc_ER"));
                rt_PercRotasClientes.getRt_Perc().setCodCli1(consulta.getString("Rt_Perc_CodCli1"));
                rt_PercRotasClientes.getRt_Perc().setNRed(consulta.getString("Rt_Perc_NRed"));
                rt_PercRotasClientes.getRt_Perc().setCodCli2(consulta.getString("Rt_Perc_CodCli2"));
                rt_PercRotasClientes.getRt_Perc().setHora1D(consulta.getString("Rt_Perc_Hora1D"));
                rt_PercRotasClientes.getRt_Perc().setTipoSrv(consulta.getString("Rt_Perc_TipoSrv"));
                rt_PercRotasClientes.getRt_Perc().setChave(consulta.getString("Rt_Perc_Chave"));
                rt_PercRotasClientes.getRt_Perc().setDPar(consulta.getInt("Rt_Perc_DPar"));
                rt_PercRotasClientes.getRt_Perc().setRegiao(consulta.getString("Rt_Perc_Regiao"));
                rt_PercRotasClientes.getRt_Perc().setObserv(consulta.getString("Rt_Perc_Observ"));
                rt_PercRotasClientes.getRt_Perc().setValor(consulta.getString("Rt_Perc_Valor"));
                rt_PercRotasClientes.getRt_Perc().setHrSaida(consulta.getString("Rt_Perc_HrSaida"));
                rt_PercRotasClientes.getRt_Perc().setHrBaixa(consulta.getString("Rt_Perc_HrBaixa"));
                rt_PercRotasClientes.getRt_Perc().setHrCheg(consulta.getString("Rt_Perc_HrCheg"));
                rt_PercRotasClientes.getRt_Perc().setAtraso(consulta.getString("Rt_Perc_Atraso"));
                rt_PercRotasClientes.getRt_Perc().setTempoEspera(consulta.getString("Rt_Perc_TempoEspera"));
                rt_PercRotasClientes.getRt_Perc().setOS(consulta.getString("Rt_Perc_OS"));
                rt_PercRotasClientes.getRt_Perc().setOperIncl(consulta.getString("Rt_Perc_OperIncl"));
                rt_PercRotasClientes.getRt_Perc().setDt_Incl(consulta.getLocalDate("Rt_Perc_Dt_Incl"));
                rt_PercRotasClientes.getRt_Perc().setHr_Incl(consulta.getString("Rt_Perc_Hr_Incl"));
                rt_PercRotasClientes.getRt_Perc().setOperador(consulta.getString("Rt_Perc_Operador"));
                rt_PercRotasClientes.getRt_Perc().setDt_Alter(consulta.getLocalDate("Rt_Perc_Dt_Alter"));
                rt_PercRotasClientes.getRt_Perc().setHr_Alter(consulta.getString("Rt_Perc_Hr_Alter"));
                rt_PercRotasClientes.getRt_Perc().setOperExcl(consulta.getString("Rt_Perc_OperExcl"));
                rt_PercRotasClientes.getRt_Perc().setDt_Excl(consulta.getLocalDate("Rt_Perc_Dt_Excl"));
                rt_PercRotasClientes.getRt_Perc().setHr_Excl(consulta.getString("Rt_Perc_Hr_Excl"));
                rt_PercRotasClientes.getRt_Perc().setOperFech(consulta.getString("Rt_Perc_OperFech"));
                rt_PercRotasClientes.getRt_Perc().setDt_Fech(consulta.getLocalDate("Rt_Perc_Dt_Fech"));
                rt_PercRotasClientes.getRt_Perc().setHr_Fech(consulta.getString("Rt_Perc_Hr_Fech"));
                rt_PercRotasClientes.getRt_Perc().setCodLan(consulta.getString("Rt_Perc_CodLan"));
                rt_PercRotasClientes.getRt_Perc().setPedido(consulta.getString("Rt_Perc_Pedido"));
                rt_PercRotasClientes.getRt_Perc().setFlag_Excl(consulta.getString("Rt_Perc_Flag_Excl"));
                rt_PercRotasClientes.getRotas().setSequencia(consulta.getString("Rotas_Sequencia"));
                rt_PercRotasClientes.getRotas().setRota(consulta.getString("Rotas_Rota"));
                rt_PercRotasClientes.getRotas().setData(consulta.getString("Rotas_Data"));
                rt_PercRotasClientes.getRotas().setCodFil(consulta.getString("Rotas_CodFil"));
                rt_PercRotasClientes.getRotas().setTpVeic(consulta.getString("Rotas_TpVeic"));
                rt_PercRotasClientes.getRotas().setViagem(consulta.getString("Rotas_Viagem"));
                rt_PercRotasClientes.getRotas().setATM(consulta.getString("Rotas_ATM"));
                rt_PercRotasClientes.getRotas().setBACEN(consulta.getString("Rotas_BACEN"));
                rt_PercRotasClientes.getRotas().setAeroporto(consulta.getString("Rotas_Aeroporto"));
                rt_PercRotasClientes.getRotas().setHrLargada(consulta.getString("Rotas_HrLargada"));
                rt_PercRotasClientes.getRotas().setHrChegada(consulta.getString("Rotas_HrChegada"));
                rt_PercRotasClientes.getRotas().setHrIntIni(consulta.getString("Rotas_HrIntIni"));
                rt_PercRotasClientes.getRotas().setHrIntFim(consulta.getString("Rotas_HrIntFim"));
                rt_PercRotasClientes.getRotas().setHsTotal(consulta.getString("Rotas_HsTotal"));
                rt_PercRotasClientes.getRotas().setObservacao(consulta.getString("Rotas_Observacao"));
                rt_PercRotasClientes.getRotas().setKmSaida(consulta.getString("Rotas_KmSaida"));
                rt_PercRotasClientes.getRotas().setKmChegada(consulta.getString("Rotas_KmChegada"));
                rt_PercRotasClientes.getRotas().setKmTotal(consulta.getString("Rotas_KmTotal"));
                rt_PercRotasClientes.getRotas().setHrUrb(consulta.getString("Rotas_HrUrb"));
                rt_PercRotasClientes.getRotas().setHrInter(consulta.getString("Rotas_HrInter"));
                rt_PercRotasClientes.getRotas().setPrdUrb(consulta.getString("Rotas_PrdUrb"));
                rt_PercRotasClientes.getRotas().setPrdInter(consulta.getString("Rotas_PrdInter"));
                rt_PercRotasClientes.getRotas().setGuiasUrb(consulta.getString("Rotas_GuiasUrb"));
                rt_PercRotasClientes.getRotas().setGuiasInter(consulta.getString("Rotas_GuiasInter"));
                rt_PercRotasClientes.getRotas().setKmTotConj(consulta.getString("Rotas_KmTotConj"));
                rt_PercRotasClientes.getRotas().setKmTotConj2(consulta.getString("Rotas_KmTotConj2"));
                rt_PercRotasClientes.getRotas().setDtFim(consulta.getString("Rotas_DtFim"));
                rt_PercRotasClientes.getRotas().setOperador(consulta.getString("Rotas_Operador"));
                rt_PercRotasClientes.getRotas().setDt_Alter(consulta.getLocalDate("Rotas_Dt_Alter"));
                rt_PercRotasClientes.getRotas().setHr_Alter(consulta.getString("Rotas_Hr_Alter"));
                rt_PercRotasClientes.getRotas().setOperFech(consulta.getString("Rotas_OperFech"));
                rt_PercRotasClientes.getRotas().setDt_Fech(consulta.getLocalDate("Rotas_Dt_Fech"));
                rt_PercRotasClientes.getRotas().setHr_Fech(consulta.getString("Rotas_Hr_Fech"));
                rt_PercRotasClientes.getRotas().setFlag_Excl(consulta.getString("Rotas_Flag_Excl"));
                rt_PercRotasClientes.getClientes().setCodFil(consulta.getString("Clientes_CodFil"));
                rt_PercRotasClientes.getClientes().setBanco(consulta.getString("Clientes_Banco"));
                rt_PercRotasClientes.getClientes().setTpCli(consulta.getString("Clientes_TpCli"));
                rt_PercRotasClientes.getClientes().setCodCli(consulta.getString("Clientes_CodCli"));
                rt_PercRotasClientes.getClientes().setAgencia(consulta.getString("Clientes_Agencia"));
                rt_PercRotasClientes.getClientes().setSubAgencia(consulta.getString("Clientes_SubAgencia"));
                rt_PercRotasClientes.getClientes().setLote(consulta.getString("Clientes_Lote"));
                rt_PercRotasClientes.getClientes().setNRed(consulta.getString("Clientes_NRed"));
                rt_PercRotasClientes.getClientes().setNome(consulta.getString("Clientes_Nome"));
                rt_PercRotasClientes.getClientes().setEnde(consulta.getString("Clientes_Ende"));
                rt_PercRotasClientes.getClientes().setBairro(consulta.getString("Clientes_Bairro"));
                rt_PercRotasClientes.getClientes().setCodCidade(consulta.getString("Clientes_CodCidade"));
                rt_PercRotasClientes.getClientes().setCidade(consulta.getString("Clientes_Cidade"));
                rt_PercRotasClientes.getClientes().setEstado(consulta.getString("Clientes_Estado"));
                rt_PercRotasClientes.getClientes().setCEP(consulta.getString("Clientes_CEP"));
                rt_PercRotasClientes.getClientes().setFone1(consulta.getString("Clientes_Fone1"));
                rt_PercRotasClientes.getClientes().setFone2(consulta.getString("Clientes_Fone2"));
                rt_PercRotasClientes.getClientes().setFax(consulta.getString("Clientes_Fax"));
                rt_PercRotasClientes.getClientes().setContato(consulta.getString("Clientes_Contato"));
                rt_PercRotasClientes.getClientes().setEmail(consulta.getString("Clientes_Email"));
                rt_PercRotasClientes.getClientes().setSenhaWEB(consulta.getString("Clientes_SenhaWEB"));
                rt_PercRotasClientes.getClientes().setRamoAtiv(consulta.getString("Clientes_RamoAtiv"));
                rt_PercRotasClientes.getClientes().setRegiao(consulta.getString("Clientes_Regiao"));
                rt_PercRotasClientes.getClientes().setLatitude(consulta.getString("Clientes_Latitude"));
                rt_PercRotasClientes.getClientes().setLongitude(consulta.getString("Clientes_Longitude"));
                rt_PercRotasClientes.getClientes().setPrdApoio(consulta.getString("Clientes_PrdApoio"));
                rt_PercRotasClientes.getClientes().setRisco(consulta.getString("Clientes_Risco"));
                rt_PercRotasClientes.getClientes().setMalotes(consulta.getInt("Clientes_Malotes"));
                rt_PercRotasClientes.getClientes().setNroChave(consulta.getString("Clientes_NroChave"));
                rt_PercRotasClientes.getClientes().setGrpChave(consulta.getInt("Clientes_GrpChave"));
                rt_PercRotasClientes.getClientes().setCGC(consulta.getString("Clientes_CGC"));
                rt_PercRotasClientes.getClientes().setIE(consulta.getString("Clientes_IE"));
                rt_PercRotasClientes.getClientes().setInsc_Munic(consulta.getString("Clientes_Insc_Munic"));
                rt_PercRotasClientes.getClientes().setCEI(consulta.getString("Clientes_CEI"));
                rt_PercRotasClientes.getClientes().setCPF(consulta.getString("Clientes_CPF"));
                rt_PercRotasClientes.getClientes().setRG(consulta.getString("Clientes_RG"));
                rt_PercRotasClientes.getClientes().setRateioFat(consulta.getString("Clientes_RateioFat"));
                rt_PercRotasClientes.getClientes().setRateioTes(consulta.getString("Clientes_RateioTes"));
                rt_PercRotasClientes.getClientes().setDiaFechaFat(consulta.getInt("Clientes_DiaFechaFat"));
                rt_PercRotasClientes.getClientes().setDiaVencNF(consulta.getInt("Clientes_DiaVencNF"));
                rt_PercRotasClientes.getClientes().setRetencoesFat(consulta.getString("Clientes_RetencoesFat"));
                rt_PercRotasClientes.getClientes().setMarcaATM(consulta.getString("Clientes_MarcaATM"));
                rt_PercRotasClientes.getClientes().setRetorno(consulta.getString("Clientes_Retorno"));
                rt_PercRotasClientes.getClientes().setCheque(consulta.getString("Clientes_Cheque"));
                rt_PercRotasClientes.getClientes().setEnvelope(consulta.getString("Clientes_Envelope"));
                rt_PercRotasClientes.getClientes().setVr_A(consulta.getString("Clientes_Vr_A"));
                rt_PercRotasClientes.getClientes().setCed_A(consulta.getString("Clientes_Ced_A"));
                rt_PercRotasClientes.getClientes().setCed_AP(consulta.getString("Clientes_Ced_AP"));
                rt_PercRotasClientes.getClientes().setVr_B(consulta.getString("Clientes_Vr_B"));
                rt_PercRotasClientes.getClientes().setCed_B(consulta.getString("Clientes_Ced_B"));
                rt_PercRotasClientes.getClientes().setCed_BP(consulta.getString("Clientes_Ced_BP"));
                rt_PercRotasClientes.getClientes().setVr_C(consulta.getString("Clientes_Vr_C"));
                rt_PercRotasClientes.getClientes().setCed_C(consulta.getString("Clientes_Ced_C"));
                rt_PercRotasClientes.getClientes().setCed_CP(consulta.getString("Clientes_Ced_CP"));
                rt_PercRotasClientes.getClientes().setVr_D(consulta.getString("Clientes_Vr_D"));
                rt_PercRotasClientes.getClientes().setCed_D(consulta.getString("Clientes_Ced_D"));
                rt_PercRotasClientes.getClientes().setCed_DP(consulta.getString("Clientes_Ced_DP"));
                rt_PercRotasClientes.getClientes().setVr_E(consulta.getString("Clientes_Vr_E"));
                rt_PercRotasClientes.getClientes().setCed_E(consulta.getString("Clientes_Ced_E"));
                rt_PercRotasClientes.getClientes().setCed_EP(consulta.getString("Clientes_Ced_EP"));
                rt_PercRotasClientes.getClientes().setEndCob(consulta.getString("Clientes_EndCob"));
                rt_PercRotasClientes.getClientes().setCodCidCod(consulta.getString("Clientes_CodCidCod"));
                rt_PercRotasClientes.getClientes().setCidCob(consulta.getString("Clientes_CidCob"));
                rt_PercRotasClientes.getClientes().setUFCob(consulta.getString("Clientes_UFCob"));
                rt_PercRotasClientes.getClientes().setCEPCob(consulta.getString("Clientes_CEPCob"));
                rt_PercRotasClientes.getClientes().setEmailCob(consulta.getString("Clientes_EmailCob"));
                rt_PercRotasClientes.getClientes().setObs(consulta.getString("Clientes_Obs"));
                rt_PercRotasClientes.getClientes().setSituacao(consulta.getString("Clientes_Situacao"));
                rt_PercRotasClientes.getClientes().setInterfExt(consulta.getString("Clientes_InterfExt"));
                rt_PercRotasClientes.getClientes().setCodExt(consulta.getString("Clientes_CodExt"));
                rt_PercRotasClientes.getClientes().setCodIntCli(consulta.getString("Clientes_CodIntCli"));
                rt_PercRotasClientes.getClientes().setCodPtoCli(consulta.getString("Clientes_CodPtoCli"));
                rt_PercRotasClientes.getClientes().setCercaElet(consulta.getString("Clientes_CercaElet"));
                rt_PercRotasClientes.getClientes().setDtSituacao(consulta.getString("Clientes_DtSituacao"));
                rt_PercRotasClientes.getClientes().setDt_Cad(consulta.getLocalDate("Clientes_Dt_Cad"));
                rt_PercRotasClientes.getClientes().setDt_Alter(consulta.getLocalDate("Clientes_Dt_Alter"));
                rt_PercRotasClientes.getClientes().setHr_Alter(consulta.getString("Clientes_Hr_Alter"));
                rt_PercRotasClientes.getClientes().setDt_UltMov(consulta.getLocalDate("Clientes_Dt_UltMov"));
                rt_PercRotasClientes.getClientes().setOper_Inc(consulta.getString("Clientes_Oper_Inc"));
                rt_PercRotasClientes.getClientes().setOper_Alt(consulta.getString("Clientes_Oper_Alt"));
                rt_PercRotasClientes.getClientes().setCodigo(consulta.getString("Clientes_Codigo"));
                rt_PercRotasClientes.getClientes().setCCusto(consulta.getString("Clientes_CCusto"));
                rt_PercRotasClientes.getClientes().setTipoPagto(consulta.getString("Clientes_TipoPagto"));
                rt_PercRotasClientes.getClientes().setLimite(consulta.getString("Clientes_Limite"));
                rt_PercRotasClientes.getClientes().setLimiteSeguro(consulta.getString("Clientes_LimiteSeguro"));
                rt_PercRotasClientes.getClientes().setLimiteColeta(consulta.getString("Clientes_LimiteColeta"));
                rt_PercRotasClientes.getClientes().setPatrimonio(consulta.getString("Clientes_Patrimonio"));
                rt_PercRotasClientes.getClientes().setProprietario(consulta.getString("Clientes_Proprietario"));
                rt_PercRotasClientes.getClientes().setRepresentante(consulta.getString("Clientes_Representante"));
                rt_PercRotasClientes.getClientes().setAtivEconomica(consulta.getString("Clientes_AtivEconomica"));
                rt_PercRotasClientes.getClientes().setCodCofre(consulta.getString("Clientes_CodCofre"));
                rt_PercRotasClientes.getClientes().setGrpRota(consulta.getString("Clientes_GrpRota"));
                retorno.add(rt_PercRotasClientes);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercRotasClientesDao.listarClientesRota - " + e.getMessage());
        }
    }
}

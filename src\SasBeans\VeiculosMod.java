package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class VeiculosMod {

    private int Codigo;
    private String Descricao;
    private BigDecimal KMMedioMin;
    private BigDecimal KMMedioMax;
    private BigDecimal Tanque;
    private String Obs;
    private int Fabricante;
    private String Operador;
    private LocalDate Dt_alter;
    private String Hr_alter;

    public int getCodigo() {
        return Codigo;
    }

    public void setCodigo(int Codigo) {
        this.Codigo = Codigo;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public BigDecimal getKMMedioMin() {
        return KMMedioMin;
    }

    public void setKMMedioMin(BigDecimal KMMedioMin) {
        this.KMMedioMin = KMMedioMin;
    }

    public BigDecimal getKMMedioMax() {
        return KMMedioMax;
    }

    public void setKMMedioMax(BigDecimal KMMedioMax) {
        this.KMMedioMax = KMMedioMax;
    }

    public BigDecimal getTanque() {
        return Tanque;
    }

    public void setTanque(BigDecimal Tanque) {
        this.Tanque = Tanque;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public int getFabricante() {
        return Fabricante;
    }

    public void setFabricante(int Fabricante) {
        this.Fabricante = Fabricante;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(LocalDate Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 29 * hash + this.Codigo;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final VeiculosMod other = (VeiculosMod) obj;
        if (this.Codigo != other.Codigo) {
            return false;
        }
        return true;
    }
}

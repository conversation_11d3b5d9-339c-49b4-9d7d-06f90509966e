/*
 */
package SasBeans.SatWebService;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class WebServiceBean {

    private BigDecimal num_cliente;
    private String tiempo_promedio;
    private BigDecimal num_pds;
    private BigDecimal visitas;
    private BigDecimal efectividad;
    private BigDecimal eficiencia;
    private List<Clientes_Rota_Servicos> rutaServicioCliente;
    private List<Visitas_Dia_Regiao> visitasRegionDia;

    public BigDecimal getNum_cliente() {
        return num_cliente;
    }

    public void setNum_cliente(BigDecimal num_cliente) {
        this.num_cliente = num_cliente;
    }

    public String getTiempo_promedio() {
        return tiempo_promedio;
    }

    public void setTiempo_promedio(String tiempo_promedio) {
        this.tiempo_promedio = tiempo_promedio;
    }

    public BigDecimal getNum_pds() {
        return num_pds;
    }

    public void setNum_pds(BigDecimal num_pds) {
        this.num_pds = num_pds;
    }

    public BigDecimal getVisitas() {
        return visitas;
    }

    public void setVisitas(BigDecimal visitas) {
        this.visitas = visitas;
    }

    public BigDecimal getEfectividad() {
        return efectividad;
    }

    public void setEfectividad(BigDecimal efectividad) {
        this.efectividad = efectividad;
    }

    public BigDecimal getEficiencia() {
        return eficiencia;
    }

    public void setEficiencia(BigDecimal eficiencia) {
        this.eficiencia = eficiencia;
    }

    public List<Clientes_Rota_Servicos> getRutaServicioCliente() {
        return rutaServicioCliente;
    }

    public void setRutaServicioCliente(List<Clientes_Rota_Servicos> rutaServicioCliente) {
        this.rutaServicioCliente = rutaServicioCliente;
    }

    public List<Visitas_Dia_Regiao> getVisitasRegionDia() {
        return visitasRegionDia;
    }

    public void setVisitasRegionDia(List<Visitas_Dia_Regiao> visitasRegionDia) {
        this.visitasRegionDia = visitasRegionDia;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.compostas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeansCompostas.DashboardCharts;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DashboardSecSaudeDao {

    public List<DashboardCharts> imoveisSituacaoAgp(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Replace(Replace(Pergunta,'Número de ',''),' - Total','') Pergunta, sum(Convert(int,Resposta)) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and pergunta in ('NÚMERO DE IMÓVEIS INSPECIONADOS - TOTAL',\n"
                    + "'NÚMERO DE IMÓVEIS RECUSADOS - TOTAL',\n"
                    + "'NÚMERO DE IMÓVEIS FECHADOS - TOTAL')\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? "
                    + "Group by Pergunta \n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Pergunta"));
                dashboard.setData(consulta.getString("Qtd"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.imoveisSituacao - " + e.getMessage());
        }
    }

    public List<DashboardCharts> imoveisSituacaoDia(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Convert(Date,Data) Data, \n"
                    + "(Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE IMÓVEIS INSPECIONADOS - TOTAL') 'IMÓVEIS INSPECIONADOS',\n"
                    + "(Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE IMÓVEIS RECUSADOS - TOTAL') 'IMÓVEIS RECUSADOS',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE IMÓVEIS FECHADOS - TOTAL') 'IMÓVEIS FECHADOS'\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and pergunta in ('NÚMERO DE IMÓVEIS INSPECIONADOS - TOTAL',\n"
                    + "'NÚMERO DE IMÓVEIS RECUSADOS - TOTAL',\n"
                    + "'NÚMERO DE IMÓVEIS FECHADOS - TOTAL')\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "Group by data, CodInspecao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Data"));
                dashboard.setData(consulta.getString("IMÓVEIS INSPECIONADOS"));
                dashboard.setData2(consulta.getString("IMÓVEIS RECUSADOS"));
                dashboard.setData3(consulta.getString("IMÓVEIS FECHADOS"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.imoveisSituacao - " + e.getMessage());
        }
    }

    public List<DashboardCharts> imoveisDiaTrabalho(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select 'IMÓVEIS VISITADOS' Pergunta, Round(sum(Convert(float,Resposta))/Count(Distinct data),2) Media\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and pergunta in ('NÚMERO DE IMÓVEIS INSPECIONADOS - TOTAL',\n"
                    + "'NÚMERO DE IMÓVEIS RECUSADOS - TOTAL',\n"
                    + "'NÚMERO DE IMÓVEIS FECHADOS - TOTAL')\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "union\n"
                    + "Select 'IMÓVEIS FECHADOS' Pergunta, Round(sum(Convert(float,Resposta))/Count(Distinct data),2) Media\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and pergunta ='NÚMERO DE IMÓVEIS INSPECIONADOS - TOTAL'\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Pergunta"));
                dashboard.setData(consulta.getString("Media"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.imoveisDiaTrabalho - " + e.getMessage());
        }
    }

    public List<DashboardCharts> inspecaoTratamentoAgp(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Replace(Pergunta,'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO ','') Pergunta, sum(Convert(int,Resposta)) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? "
                    + "and Pergunta in (\n"
                    + "'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO RESIDÊNCIA',\n"
                    + "'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO COMÉRCIO',\n"
                    + "'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO TB',\n"
                    + "'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO OUTROS')\n"
                    + "Group by Pergunta\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Pergunta"));
                dashboard.setData(consulta.getString("Qtd"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.inspecaoTratamentoAgp - " + e.getMessage());
        }
    }

    public List<DashboardCharts> depositosTipoAgp(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Replace(Pergunta,'NÚMERO DE DEPÓSITOS POTENCIAIS POR ','') Pergunta, sum(Convert(int,Resposta)) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? "
                    + "and Pergunta in (\n"
                    + "'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A1',\n"
                    + "'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A2',\n"
                    + "'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - B',\n"
                    + "'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - C',\n"
                    + "'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D1',\n"
                    + "'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D2',\n"
                    + "'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - E')\n"
                    + "Group by Pergunta\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Pergunta"));
                dashboard.setData(consulta.getString("Qtd"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.depositosTipoAgp - " + e.getMessage());
        }
    }

    public List<DashboardCharts> imoveisInspecaoTratamentoTipo(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Convert(Date,Data) Data, \n"
                    + "(Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO RESIDÊNCIA') 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO RESIDÊNCIA',\n"
                    + "(Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO COMÉRCIO') 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO COMÉRCIO',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO OUTROS') 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO OUTROS',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO TB') 'INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO TB'\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "Group by data, CodInspecao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Data"));
                dashboard.setData(consulta.getString("INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO RESIDÊNCIA"));
                dashboard.setData2(consulta.getString("INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO COMÉRCIO"));
                dashboard.setData3(consulta.getString("INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO OUTROS"));
                dashboard.setData4(consulta.getString("INSPEÇÃO E TRATAMENTO - NÚMERO DE IMÓVEIS TIPO TB"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.imoveisSituacao - " + e.getMessage());
        }
    }

    public List<DashboardCharts> numeroDepositosEvolucao(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Convert(Date,Data) Data, \n"
                    + "(Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS INSPECIONADOS - TOTAL') 'NÚMERO DE DEPÓSITOS INSPECIONADOS - TOTAL',\n"
                    + "(Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS TRATADOS - TOTAL') 'NÚMERO DE DEPÓSITOS TRATADOS - TOTAL',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS ELIMINADOS - TOTAL') 'NÚMERO DE DEPÓSITOS ELIMINADOS - TOTAL'\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "Group by data, CodInspecao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Data"));
                dashboard.setData(consulta.getString("NÚMERO DE DEPÓSITOS INSPECIONADOS - TOTAL"));
                dashboard.setData2(consulta.getString("NÚMERO DE DEPÓSITOS TRATADOS - TOTAL"));
                dashboard.setData3(consulta.getString("NÚMERO DE DEPÓSITOS ELIMINADOS - TOTAL"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.imoveisSituacao - " + e.getMessage());
        }
    }

    public List<DashboardCharts> depositosAcaoAgp(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Replace(Replace(Pergunta,'NÚMERO DE DEPÓSITOS ',''),' - TOTAL','') Pergunta, sum(Convert(int,Resposta)) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? "
                    + "and Pergunta in (\n"
                    + "'NÚMERO DE DEPÓSITOS INSPECIONADOS - TOTAL',\n"
                    + "'NÚMERO DE DEPÓSITOS TRATADOS - TOTAL',\n"
                    + "'NÚMERO DE DEPÓSITOS ELIMINADOS - TOTAL')\n"
                    + "Group by Pergunta\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Pergunta"));
                dashboard.setData(consulta.getString("Qtd"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.depositosAcaoAgp - " + e.getMessage());
        }
    }

    public List<DashboardCharts> evolucaoDepositosTipo(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Convert(Date,Data) Data, \n"
                    + "(Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A1') 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A1',\n"
                    + "(Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A2') 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A2',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - B') 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - B',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - C') 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - C',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D1') 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D1',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D2') 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D2',\n"
                    + "  (Select sum(Convert(int,y.Resposta)) from pstinspecao y \n"
                    + "where y.Data = pstinspecao.Data\n"
                    + "  and y.CodInspecao = pstinspecao.CodInspecao \n"
                    + "  and y.pergunta = 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - E') 'NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - E'\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "Group by data, CodInspecao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Data"));
                dashboard.setData(consulta.getString("NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A1"));
                dashboard.setData2(consulta.getString("NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - A2"));
                dashboard.setData3(consulta.getString("NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - B"));
                dashboard.setData4(consulta.getString("NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - C"));
                dashboard.setData5(consulta.getString("NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D1"));
                dashboard.setData6(consulta.getString("NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - D2"));
                dashboard.setData7(consulta.getString("NÚMERO DE DEPÓSITOS POTENCIAIS POR TIPO - E"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.imoveisSituacao - " + e.getMessage());
        }
    }

    public List<DashboardCharts> amostrasRoedoresAmostras(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Substring(Pergunta,9,100) Pergunta, sum(Convert(int,Resposta)) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 4\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "and Pergunta in (\n"
                    + "'29/41 - N° DE AMOSTRAS COLETADAS INIC/FINAL')\n"
                    + "Group by Pergunta\n"
                    + "Union\n"
                    + "Select Substring(Pergunta,9,100) Pergunta, Count(*) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 4\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "and Pergunta in (\n"
                    + "'42/42 - NOS ÚLTIMOS 3 MESES ALGUÉM DA CASA TEVE DENGUE',\n"
                    + "'37/41 - ROEDORES - TOCA',\n"
                    + "'38/41 - ROEDORES - VESTÍGIO'\n"
                    + ")\n"
                    + "and Resposta = 'Sim'\n"
                    + "Group by Pergunta\n"
                    + "union\n"
                    + "Select 'IMOVEIS INSPECIONADOS' Pergunta, sum(Convert(int,Resposta)) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and pergunta = 'NÚMERO DE IMÓVEIS INSPECIONADOS - TOTAL'\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "Group by Pergunta\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);

            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);

            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Pergunta"));
                dashboard.setData(consulta.getString("Qtd"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.depositosAcaoAgp - " + e.getMessage());
        }
    }

    public List<DashboardCharts> inspecionadosxdengue(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        List<DashboardCharts> retorno = new ArrayList<>();
        DashboardCharts dashboard;

        try {
            String sql = "Select Substring(Pergunta,9,100) Pergunta, Count(*) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 4\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "and Pergunta in (\n"
                    + "'42/42 - NOS ÚLTIMOS 3 MESES ALGUÉM DA CASA TEVE DENGUE'\n"
                    + ")\n"
                    + "and Resposta = 'Sim'\n"
                    + "Group by Pergunta\n"
                    + "union\n"
                    + "Select 'IMOVEIS INSPECIONADOS' Pergunta, sum(Convert(int,Resposta)) Qtd\n"
                    + "from pstinspecao \n"
                    + "where codinspecao = 6\n"
                    + "and pergunta = 'NÚMERO DE IMÓVEIS INSPECIONADOS - TOTAL'\n"
                    + "and Datepart(year,data)= ? \n"
                    + "and Datepart(month,data)= ? \n"
                    + "and CodFil = ? \n"
                    + "Group by Pergunta;\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);
            consulta.setString(ano);
            consulta.setString(mes);
            consulta.setString(codfil);            
            consulta.select();

            while (consulta.Proximo()) {
                dashboard = new DashboardCharts();
                dashboard.setLabel(consulta.getString("Pergunta"));
                dashboard.setData(consulta.getString("Qtd"));
                retorno.add(dashboard);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DasboardSecSaude.depositosAcaoAgp - " + e.getMessage());
        }
    }
}

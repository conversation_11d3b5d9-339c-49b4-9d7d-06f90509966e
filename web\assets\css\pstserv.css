/*
*/
/* 
    Created on : Jan 2, 2017, 4:03:27 PM
    Author     : Richard
*/
.ui-tabs .ui-tabs-nav.ui-widget-header li{
    background: #ccccff;
}
.tabelaArquivos .ui-widget-content{
    background: white;
}
.tabelaArquivos td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    border: none !important;
    background: transparent !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaArquivos .ui-datatable-selectable{
    border: none !important;
    background: transparent !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaArquivos .ui-datatable-scrollable-header-box{
    background: white;
}
.tabelaArquivos .ui-datatable-data .ui-widget-content{
    border: none !important;
}
.tabelaArquivos .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.tabelaArquivos .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

.cadastrar{
    width: 90vh;
    min-width: 100%;
}

.panelTelaSupervisao{
    width: 100%;
}
.panelDetalhesPosto{
    width: 400px;
}

.panelTabelaSupervisao{
    width: 300px;
}

.cabecalhoSupervisao{
    text-align: center;
}

@media all and (min-width: 768px) {
    .cadastrar{
        width: 700px;
    }
    .panelTelaSupervisao{
        width: 1024px;
    }
    .panelDetalhesPosto{
        width: 100%;
    }
    .cabecalhoSupervisao{
        text-align: left;
    }
    .panelTabelaSupervisao{
        width: auto;
    }
}

.cabecalhoSupervisao{
    padding: 0px !important;
}

.botao .ui-panelgrid .ui-panelgrid-cell, .botao .ui-panelgrid-cell{
    padding: 0px !important;
}

.ui-lightbox-nav-left, .ui-lightbox-nav-right{
    display: none !important;
}
.ui-lightbox-caption-text{
    color:white;
}

.dialogo .ui-widget-content{
    background: transparent;
}
.dialogo .ui-panelgrid-cell{
    display: table-cell;
    vertical-align: top;
}

.detalhesSupervisao{
    padding: 5px 5px 5px 5px;
}

.dialogo .ui-dialog.ui-widget-content .ui-dialog-content{
    padding: 0px 15px 15px 0px;
}

.dialogosupervisao .ui-dialog-content{
    padding: 0px 0px 0px 0px !important;
}

.dialogosupervisao .ui-widget-content{
    background: transparent;
}
.contrato .ui-autocomplete-panel {
    width: 100% !important;
}
.contrato .ui-autocomplete-input{
    width: 100% !important;
}

.contratoP .ui-autocomplete-panel {
    width: 280px !important;
}
.contratoP .ui-autocomplete-input{
    width: 280px !important;
}

.posto .ui-autocomplete-panel {
    width: 100% !important;
}
.posto .ui-autocomplete-input{
    width: 100% !important;
}

.postoP .ui-autocomplete-panel {
    width: 280px !important;
}
.postoP .ui-autocomplete-input{
    width: 280px !important;
}

.cliente .ui-autocomplete-panel {
    width: 100% !important;
}
.cliente .ui-autocomplete-input{
    width: 100% !important;
}

.clienteP .ui-autocomplete-panel {
    width: 280px !important;
}
.clienteP .ui-autocomplete-input{
    width: 280px !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}


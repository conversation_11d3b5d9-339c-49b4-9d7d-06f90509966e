<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <h:form id="guiaCadastro" style="color: black;">
        <p:panel id="cadastrar"
                 style="background-color: transparent; width: 100%;"
                 styleClass="cadastrar">
            <div class="content-fluid">
                <div class="row flexBottom">
                    <div class="col-md-4">
                        <p:outputLabel for="guia"
                                       value="#{localemsgs.Guia}: "
                                       indicateRequired="false"/>
                        <p:inputNumber id="guia"
                                       value="#{cxForteEntrada.guia}"
                                       class="primefacesInputFix"
                                       maxValue="999999999"
                                       decimalPlaces="0"
                                       thousandSeparator=""
                                       >
                            <f:validateLength minimum="1" maximum="9" />
                            <p:ajax
                                event="change"
                                update="msgs @form:cadastrar guiaSerieCadastroForm cancelarOperacao confirmarParada confirmarParadaHorario utilizarParada guiaForm main"
                                listener="#{cxForteEntrada.analisarGuiaESerie()}"/>
                        </p:inputNumber>
                    </div>

                    <div class="col-md-4 flexBottom">
                        <div style="margin-right: 8px;">
                            <p:outputLabel for="serie"
                                           value="#{localemsgs.Serie}:"/>
                            <p:inputText id="serie"
                                         value="#{cxForteEntrada.serie}"
                                         onblur="value = value.toUpperCase()"
                                         class="primefacesInputFix"
                                         >
                                <f:validateLength minimum="1" maximum="5" />
                                <p:ajax
                                    event="change"
                                    delay="50"
                                    update="msgs @form:cadastrar guiaSerieCadastroForm cancelarOperacao confirmarParada confirmarParadaHorario utilizarParada guiaForm main"
                                    listener="#{cxForteEntrada.analisarGuiaESerie()}"/>
                            </p:inputText>
                        </div>

                        <h:panelGroup id="gtv" layout="block">
                            <p:commandLink
                                action="#{cxForteEntrada.metodoGTV()}"
                                rendered="#{cxForteEntrada.rotaSelecionada.rota eq cxForteEntrada.ROTA_TESOURARIA}"
                                update="msgs @form:cadastrar"
                                title="#{localemsgs.GuiasCustodia}">
                                <p:graphicImage
                                    url="../assets/img/icone_redondo_pesquisar.png"
                                    width="25" height="25" />
                            </p:commandLink>
                        </h:panelGroup>
                    </div>

                    <div class="col-md-4">
                        <p:outputLabel for="valor"
                                       value="#{localemsgs.Valor}: "/>
                        <p:inputNumber  id="valor"
                                        value="#{cxForteEntrada.valor}"
                                        minValue="0"
                                        maxValue="999999999.99"
                                        decimalPlaces="2"
                                        required="true"
                                        validatorMessage="Not valid Number"/>
                    </div>
                </div>

                <div class="row flexBottom">
                    <div class="col-md-4 flexBottom">
                        <div style="margin-right: 8px; width: 100%;">
                            <p:outputLabel for="tipo"
                                           value="#{localemsgs.Tipo}: "/>

                            <p:selectOneMenu
                                value="#{cxForteEntrada.tipo}"
                                id="tipo"
                                converter="omnifaces.SelectItemsConverter"
                                disabled="false"
                                class="primefacesInputFix"
                                styleClass="filial"
                                style="width: 100%;"
                                >
                                <f:selectItem itemValue="#{null}"
                                              itemLabel="#{localemsgs.Selecionar}"
                                              noSelectionOption="true"/>
                                <f:selectItem itemValue="1"
                                              itemLabel="1 - #{localemsgs.Dinheiro}"/>
                                <f:selectItem itemValue="2"
                                              itemLabel="2 - #{localemsgs.Cheque}"/>
                                <f:selectItem itemValue="3"
                                              itemLabel="3 - #{localemsgs.Moeda}"/>
                                <f:selectItem itemValue="4"
                                              itemLabel="4 - #{localemsgs.MetaisPreciosos}"/>
                                <f:selectItem itemValue="5"
                                              itemLabel="5 - #{localemsgs.MoedaExtrangeira}"/>
                                <f:selectItem itemValue="9"
                                              itemLabel="9 - #{localemsgs.Outros}"/>
                            </p:selectOneMenu>
                        </div>
                    </div>
                </div>

                <div class="row flexBottom">
                    <div class="col-md-4 col-sm-4 col-xs-12">
                        <p:outputLabel for="destino"
                                       value="#{localemsgs.Destino}: "
                                       indicateRequired="false"/>

                        <p:inputText
                            id="destino"
                            disabled="true"
                            value="#{cxForteEntrada.codigoClienteDst}"
                            styleClass="cliente"
                            style="width: 100%"
                            />
                    </div>
                    <div class="col-md-4 col-sm-4 col-xs-12">
                        <p:inputText
                            id="clienteContratanteNome"
                            class="primefacesInputFix"
                            disabled="true"
                            value="#{cxForteEntrada.NRedClienteDst}"/>
                    </div>
                </div>

                <div class="row flexTop">
                    <div class="col-md-2 col-sm-4 col-xs-12">
                        <p:outputLabel for="OS"
                                       value="#{localemsgs.OS}: "
                                       indicateRequired="false"/>
                    </div>
                    <div class="col-md-2 col-sm-4 col-xs-12" style="display: flex; flex-direction: column;">
                        <h:outputText
                            id="OS"
                            value="#{cxForteEntrada.codigoOS}"
                            converter="conversor0"
                            styleClass="cliente"
                            style="width: 100%"
                            />

                        <h:outputText
                            id="OS2"
                            value="#{cxForteEntrada.parada}"
                            styleClass="cliente"
                            style="width: 100%"
                            />
                    </div>
                    <div class="col-md-2 col-sm-4 col-xs-12">
                        <p:outputLabel for="horaSaida"
                                       value="#{localemsgs.PrevisaoHoraSaida}: "
                                       indicateRequired="false"/>
                    </div>
                    <div class="col-md-2 col-sm-4 col-xs-12">
                        <h:outputText
                            id="horaSaida"
                            value="#{cxForteEntrada.hora1}"
                            styleClass="cliente"
                            style="width: 100%"
                            />
                    </div>
                    <div class="col-md-2 col-sm-4 col-xs-12">
                        <h:outputText
                            value="//&#160;&#160;&#160;&#160;#{cxForteEntrada.regiaoCliente}"
                            rendered="#{not cxForteEntrada.regiaoCliente eq null}"
                            styleClass="cliente"
                            style="width: 100%"
                            />
                    </div>
                </div>

                <div class="form-inline">
                    <p:commandLink
                        id="cadastro"
                        action="#{cxForteEntrada.gravarGuia()}"
                        update="msgs @form:cadastrar guiaForm main"
                        title="#{localemsgs.Cadastrar}">
                        <p:graphicImage
                            url="../assets/img/icone_adicionar.png"
                            width="40" height="40" />
                    </p:commandLink>
                </div>
            </div>
        </p:panel>
    </h:form>

</ui:composition>
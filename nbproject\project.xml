<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://www.netbeans.org/ns/project/1">
    <type>org.netbeans.modules.web.project</type>
    <configuration>
        <buildExtensions xmlns="http://www.netbeans.org/ns/ant-build-extender/1"/>
        <data xmlns="http://www.netbeans.org/ns/web-project/3">
            <name>SatMobWeb</name>
            <minimum-ant-version>1.6.5</minimum-ant-version>
            <web-module-libraries>
                <library dirs="200">
                    <file>${reference.PacotesUteis.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.com.lowagie.text-2.1.7.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.poi-ooxml-3.15.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.poi-3.15.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.sqljdbc4.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.bootstrap-1.0.10.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.commons-email-1.4.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.commons-collections4-4.1.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.poi-ooxml-schemas-3.15-beta1.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.xhtmlrenderer-8.7-atlassian-2.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.flying-saucer-core-9.1.7.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.flying-saucer-pdf-9.1.7.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.flying-saucer-pdf-itext5-9.1.7.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.tidy.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.XmlSchema-1.4.3.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis-ssl-1.4.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axiom.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis2-adb-1.7.8.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis2-jaxws-1.7.8.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis2-kernel-1.7.8.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis2-metadata-1.7.8.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis2-saaj-1.7.8.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis2-transport-http-1.7.8.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis2-transport-local-1.7.8.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.axis2-xmlbeans-1.7.8.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.barbecue-1.5-beta1.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.commons-codec-1.3.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.commons-httpclient-3.1.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.httpcore-4.4.6.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.log4j-1.2.15.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.mail.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.neethi-3.0.3.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.sunpkcs11.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.wsdl4j.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.xmlschema-core-2.2.1.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.commons-logging-1.2.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.xmlbeans-2.6.0.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.commons-io-2.4.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.omnifaces-2.7.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.admin-theme-1.0.0.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.sun.misc.BASE64Decoder.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.primefaces-7.0.RC3.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.json-20180130.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.httpclient-4.5.12.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.httpcore-4.4.13.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.gson-2.2.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.core-3.3.0.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.javase-3.3.0.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.barcode4j-2.1.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.itextpdf-5.3.2.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.woden-core-1.0M10.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
                <library dirs="200">
                    <file>${file.reference.activation-1.1.1.jar}</file>
                    <path-in-war>WEB-INF/lib</path-in-war>
                </library>
            </web-module-libraries>
            <web-module-additional-libraries/>
            <source-roots>
                <root id="src.dir" name="Pacotes de Códigos-fonte"/>
            </source-roots>
            <test-roots/>
        </data>
        <references xmlns="http://www.netbeans.org/ns/ant-project-references/1">
            <reference>
                <foreign-project>PacotesUteis</foreign-project>
                <artifact-type>jar</artifact-type>
                <script>build.xml</script>
                <target>jar</target>
                <clean-target>clean</clean-target>
                <id>jar</id>
            </reference>
        </references>
    </configuration>
</project>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels.containers;

import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Rotas;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class RotasLazyList extends LazyDataModel<Rotas> {

    private static final long serialVersionUID = 1L;
    private List<Rotas> rotas;
    private final RotasSatWeb rotassatweb;
    private final Persistencia persistencia;
    private Map filters;

    public RotasLazyList(Persistencia pst, Map filters) {
        this.rotassatweb = new RotasSatWeb();
        this.persistencia = pst;
        this.filters = filters;
    }

    @Override
    public List<Rotas> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.rotas = this.rotassatweb.listagemRotasContainersPaginada(first, pageSize, this.filters, this.persistencia);

            // set the total of players
            setRowCount(this.rotassatweb.contagemRotasContainers(this.filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.rotas;
    }

    @Override
    public Object getRowKey(Rotas rota) {
        return rota.getCodFil() + ";" + rota.getRota();
    }

    @Override
    public Rotas getRowData(String nRota) {
        try {
            String codfil = nRota.split(";")[0];
            String rota = nRota.split(";")[1];
            for (Rotas r : this.rotas) {
                if (rota.equals(r.getRota()) && codfil.equals(r.getCodFil().toString())) {
                    return r;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

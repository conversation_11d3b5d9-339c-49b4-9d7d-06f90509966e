/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.FatISSGrp;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FatISSGrpDao {

    public List<FatISSGrp> listarFatISSGrp(String query, Persistencia persistencia) throws Exception {
        try {
            List<FatISSGrp> retorno = new ArrayList<>();
            String sql = " SELECT Municipios.Nome Municipio, MunicCFOP.AliqISS, FatISSGrp.* \n"
                    + "FROM FatISSGrp \n"
                    + "LEFT JOIN Municipios on Municipios.Codigo = FatISSGrp.CodMunic\n"
                    + "LEFT JOIN MunicCFOP on MunicCFOP.CodMunic = FatISSGrp.CodMunic\n"
                    + "WHERE FatISSGrp.Descricao LIKE ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.select();
            FatISSGrp fatISSGrp;
            while (consulta.Proximo()) {
                fatISSGrp = new FatISSGrp();
                fatISSGrp.setCodigo(consulta.getString("Codigo"));
                fatISSGrp.setTipo(consulta.getString("Tipo"));
                fatISSGrp.setDescricao(consulta.getString("Descricao"));
                fatISSGrp.setCodMunic(consulta.getString("CodMunic"));
                fatISSGrp.setISS(consulta.getString("ISS"));
                fatISSGrp.setAliqICMS(consulta.getString("AliqICMS"));
                fatISSGrp.setICMSRet(consulta.getString("ICMSRet"));
                fatISSGrp.setISSRet(consulta.getString("ISSRet"));
                fatISSGrp.setOperador(consulta.getString("Operador"));
                fatISSGrp.setDt_Alter(consulta.getString("Dt_Alter"));
                fatISSGrp.setHr_Alter(consulta.getString("Hr_Alter"));

                fatISSGrp.setMunicipio(consulta.getString("Municipio"));
                fatISSGrp.setAliqISS(consulta.getString("AliqISS"));
                retorno.add(fatISSGrp);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FatISSGrpDao.buscarFatISSGrp - " + e.getMessage() + "\r\n"
                    + " SELECT Municipios.Nome Municipio, MunicCFOP.AliqISS, FatISSGrp.* \n"
                    + "FROM FatISSGrp \n"
                    + "LEFT JOIN Municipios on Municipios.Codigo = FatISSGrp.CodMunic\n"
                    + "LEFT JOIN MunicCFOP on MunicCFOP.CodMunic = FatISSGrp.CodMunic\n"
                    + "WHERE FatISSGrp.Descricao LIKE %" + query + "%");
        }
    }

    public FatISSGrp buscarFatISSGrp(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT Municipios.Nome Municipio, MunicCFOP.AliqISS, FatISSGrp.* \n"
                    + "FROM FatISSGrp \n"
                    + "LEFT JOIN Municipios on Municipios.Codigo = FatISSGrp.CodMunic\n"
                    + "LEFT JOIN MunicCFOP on MunicCFOP.CodMunic = FatISSGrp.CodMunic\n"
                    + "WHERE FatISSGrp.Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            FatISSGrp fatISSGrp = null;
            if (consulta.Proximo()) {
                fatISSGrp = new FatISSGrp();
                fatISSGrp.setCodigo(consulta.getString("Codigo"));
                fatISSGrp.setTipo(consulta.getString("Tipo"));
                fatISSGrp.setDescricao(consulta.getString("Descricao"));
                fatISSGrp.setCodMunic(consulta.getString("CodMunic"));
                fatISSGrp.setISS(consulta.getString("ISS"));
                fatISSGrp.setAliqICMS(consulta.getString("AliqICMS"));
                fatISSGrp.setICMSRet(consulta.getString("ICMSRet"));
                fatISSGrp.setISSRet(consulta.getString("ISSRet"));
                fatISSGrp.setOperador(consulta.getString("Operador"));
                fatISSGrp.setDt_Alter(consulta.getString("Dt_Alter"));
                fatISSGrp.setHr_Alter(consulta.getString("Hr_Alter"));

                fatISSGrp.setMunicipio(consulta.getString("Municipio"));
                fatISSGrp.setAliqISS(consulta.getString("AliqISS"));
            }
            consulta.close();
            return fatISSGrp;
        } catch (Exception e) {
            throw new Exception("FatISSGrpDao.buscarFatISSGrp - " + e.getMessage() + "\r\n"
                    + " SELECT Municipios.Nome Municipio, MunicCFOP.AliqISS, FatISSGrp.* \n"
                    + "FROM FatISSGrp \n"
                    + "LEFT JOIN Municipios on Municipios.Codigo = FatISSGrp.CodMunic\n"
                    + "LEFT JOIN MunicCFOP on MunicCFOP.CodMunic = FatISSGrp.CodMunic\n"
                    + "WHERE FatISSGrp.Codigo = " + codigo);
        }
    }
}

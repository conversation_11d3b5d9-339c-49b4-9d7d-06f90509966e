/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.compostas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeansCompostas.CofreDashBoardGeral;
import SasBeansCompostas.CofreDashBoardStatus;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CofreDashBoardDao {

    public List<CofreDashBoardStatus> obterEstatisticaPorCofre(Persistencia persistencia) throws Exception {
        try {
            List<CofreDashBoardStatus> retorno = new ArrayList<>();
            String sql = "SELECT CONVERT(BigInt, Clientes.CodCofre) CodCofre, Clientes.NRed,\n"
                    + "(SELECT Versao FROM ParametMobile WHERE Aplicativo = 'GetLock') VersaoAtual, MobileHW.Versao, MobileHW.Bateria,\n"
                    + "(SELECT TOP 1 Data FROM TesCofresMov t WHERE t.CodCofre = Clientes.CodCofre ORDER BY Data DESC, Hora DESC) DataMov,\n"
                    + "(SELECT TOP 1 Hora FROM TesCofresMov t WHERE t.CodCofre = Clientes.CodCofre ORDER BY Data DESC, Hora DESC) HoraMov,\n"
                    + "(SELECT TOP 1 ValorDeposito FROM TesCofresMov t WHERE t.CodCofre = Clientes.CodCofre ORDER BY Data DESC, Hora DESC) ValorMov,\n"
                    + "(SELECT TOP 1 TipoDeposito FROM TesCofresMov t WHERE t.CodCofre = Clientes.CodCofre ORDER BY Data DESC, Hora DESC) TipoDepositoMov,\n"
                    + "ISNULL(MobileHW.Saldo, 0) Saldo,\n"
                    + "(SELECT top 1 Situacao FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) Situacao,\n"
                    + "(SELECT top 1 CONVERT(VarChar, Data, 112) FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) DataSituacao,\n"
                    + "(SELECT top 1 Hora FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) HoraSituacao,\n"
                    + "(SELECT top 1 Latitude FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) LatitudeSituacao,\n"
                    + "(SELECT top 1 Longitude FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) Longitudeituacao,\n"
                    + "MobileHW.IMEI\n"
                    + "FROM Clientes \n"
                    + "LEFT JOIN MobileHW on MobileHW.CodEquip = Clientes.CodCofre\n"
                    + "WHERE Clientes.CodCofre IS NOT NULL AND Clientes.CodCofre <> '0';\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            CofreDashBoardStatus cofreDashBoardStatus;
            while (consulta.Proximo()) {
                cofreDashBoardStatus = new CofreDashBoardStatus();
                cofreDashBoardStatus.setCodCofre(consulta.getString("CodCofre"));
                cofreDashBoardStatus.setNRed(consulta.getString("NRed"));
                cofreDashBoardStatus.setBateria(consulta.getString("Bateria"));
                cofreDashBoardStatus.setVersao(consulta.getString("Versao"));
                cofreDashBoardStatus.setVersaoAtual(consulta.getString("VersaoAtual"));
                cofreDashBoardStatus.setDataMov(consulta.getString("DataMov"));
                cofreDashBoardStatus.setHoraMov(consulta.getString("HoraMov"));
                cofreDashBoardStatus.setValorMov(consulta.getString("ValorMov"));
                cofreDashBoardStatus.setTipoDepositoMov(consulta.getString("TipoDepositoMov"));
                cofreDashBoardStatus.setSaldo(consulta.getString("Saldo"));
                cofreDashBoardStatus.setSituacao(consulta.getString("Situacao"));
                cofreDashBoardStatus.setDataSituacao(consulta.getString("DataSituacao"));
                cofreDashBoardStatus.setHorasituacao(consulta.getString("Horasituacao"));
                cofreDashBoardStatus.setLatitudesituacao(consulta.getString("Latitudesituacao"));
                cofreDashBoardStatus.setLongitudeituacao(consulta.getString("Longitudeituacao"));
                cofreDashBoardStatus.setIMEI(consulta.getString("IMEI"));
                retorno.add(cofreDashBoardStatus);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CofreDashBoardDao.obterEstatisticaPorCofre - " + e.getMessage());
        }
    }

    public List<CofreDashBoardStatus> obterEstatisticaPorCofre(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        try {
            List<CofreDashBoardStatus> retorno = new ArrayList<>();
            String sql = "SELECT CONVERT(BigInt, Clientes.CodCofre) CodCofre, Clientes.NRed,\n"
                    + "(SELECT Versao FROM ParametMobile WHERE Aplicativo = 'GetLock') VersaoAtual, MobileHW.Versao, MobileHW.Bateria,\n"
                    + "(SELECT TOP 1 Data FROM TesCofresMov t WHERE t.CodCofre = Clientes.CodCofre ORDER BY Data DESC, Hora DESC) DataMov,\n"
                    + "(SELECT TOP 1 Hora FROM TesCofresMov t WHERE t.CodCofre = Clientes.CodCofre ORDER BY Data DESC, Hora DESC) HoraMov,\n"
                    + "(SELECT TOP 1 ValorDeposito FROM TesCofresMov t WHERE t.CodCofre = Clientes.CodCofre ORDER BY Data DESC, Hora DESC) ValorMov,\n"
                    + "(SELECT TOP 1 TipoDeposito FROM TesCofresMov t WHERE t.CodCofre = Clientes.CodCofre ORDER BY Data DESC, Hora DESC) TipoDepositoMov,\n"
                    + "ISNULL(MobileHW.Saldo, 0) Saldo,\n"
                    + "(SELECT top 1 Situacao FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) Situacao,\n"
                    + "(SELECT top 1 CONVERT(VarChar, Data, 112) FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) DataSituacao,\n"
                    + "(SELECT top 1 Hora FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) HoraSituacao,\n"
                    + "(SELECT top 1 Latitude FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) LatitudeSituacao,\n"
                    + "(SELECT top 1 Longitude FROM MobileHWSt WHERE MobileHWSt.IMEI = MobileHW.IMEI AND MobileHWSt.CodEquip = MobileHW.CodEquip ORDER BY Sequencia DESC) Longitudeituacao,\n"
                    + "MobileHW.IMEI\n"
                    + "FROM Clientes \n";
            if (!exibirTodos) {
                sql += " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                        + " and PessoaCliAut.CodFil = Clientes.Codfil\n";
            }
            sql += "LEFT JOIN MobileHW on MobileHW.CodEquip = Clientes.CodCofre\n"
                    + "WHERE Clientes.CodCofre IS NOT NULL AND Clientes.CodCofre <> '0'\n";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            CofreDashBoardStatus cofreDashBoardStatus;
            while (consulta.Proximo()) {
                cofreDashBoardStatus = new CofreDashBoardStatus();
                cofreDashBoardStatus.setCodCofre(consulta.getString("CodCofre"));
                cofreDashBoardStatus.setNRed(consulta.getString("NRed"));
                cofreDashBoardStatus.setBateria(consulta.getString("Bateria"));
                cofreDashBoardStatus.setVersao(consulta.getString("Versao"));
                cofreDashBoardStatus.setVersaoAtual(consulta.getString("VersaoAtual"));
                cofreDashBoardStatus.setDataMov(consulta.getString("DataMov"));
                cofreDashBoardStatus.setHoraMov(consulta.getString("HoraMov"));
                cofreDashBoardStatus.setValorMov(consulta.getString("ValorMov"));
                cofreDashBoardStatus.setTipoDepositoMov(consulta.getString("TipoDepositoMov"));
                cofreDashBoardStatus.setSaldo(consulta.getString("Saldo"));
                cofreDashBoardStatus.setSituacao(consulta.getString("Situacao"));
                cofreDashBoardStatus.setDataSituacao(consulta.getString("DataSituacao"));
                cofreDashBoardStatus.setHorasituacao(consulta.getString("Horasituacao"));
                cofreDashBoardStatus.setLatitudesituacao(consulta.getString("Latitudesituacao"));
                cofreDashBoardStatus.setLongitudeituacao(consulta.getString("Longitudeituacao"));
                cofreDashBoardStatus.setIMEI(consulta.getString("IMEI"));
                retorno.add(cofreDashBoardStatus);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CofreDashBoardDao.obterEstatisticaPorCofre - " + e.getMessage());
        }
    }

    public List<CofreDashBoardGeral> obterEstatisticaPorHora(String data, Persistencia persistencia) throws Exception {
        List<CofreDashBoardGeral> retorno = new ArrayList<>();
        CofreDashBoardGeral cofreDashBoardGeral;
        for (int i = 0; i < 24; i++) {
            cofreDashBoardGeral = new CofreDashBoardGeral();
            cofreDashBoardGeral.setData(data);
            cofreDashBoardGeral.setHora(String.valueOf(i));
            cofreDashBoardGeral.setCofresAtivos("0");
            cofreDashBoardGeral.setQtdDepositos("0");
            cofreDashBoardGeral.setQtdColetas("0");
            retorno.add(cofreDashBoardGeral);
        }
        try {
            int indice;
            String sql = "SELECT\n"
                    + "CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2)) Hora,\n"
                    + "(SELECT COUNT(DISTINCT  CodCofre) FROM TesCofresMov t WHERE t.Data = TesCofresMov.Data) CofresAtivos,\n"
                    + "(SELECT COUNT(*) FROM TesCofresMov t WHERE t.TipoDeposito = 'DINHEIRO' AND t.Data = TesCofresMov.Data\n"
                    + "AND CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), t.Hora, 102), '.', '-'),2)) = CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2))) QtdDepositos,\n"
                    + "(SELECT COUNT(*) FROM TesCofresMov t WHERE t.TipoDeposito = 'COLETA' AND t.Data = TesCofresMov.Data\n"
                    + "AND CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), t.Hora, 102), '.', '-'),2)) = CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2))) QtdColetas\n"
                    + "FROM TesCofresMov \n"
                    + "WHERE Data = ? \n"
                    + "GROUP BY LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2), Data\n"
                    + "ORDER BY LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.select();
            while (consulta.Proximo()) {
                cofreDashBoardGeral = new CofreDashBoardGeral();
                cofreDashBoardGeral.setData(data);
                cofreDashBoardGeral.setHora(consulta.getString("Hora"));

                indice = retorno.indexOf(cofreDashBoardGeral);
                if (indice > -1) {
                    retorno.get(indice).setCofresAtivos(consulta.getString("CofresAtivos"));
                    retorno.get(indice).setQtdDepositos(consulta.getString("QtdDepositos"));
                    retorno.get(indice).setQtdColetas(consulta.getString("QtdColetas"));
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CofreDashBoardDao.obterEstatisticaPorHora - " + e.getMessage());
        }
    }

    public List<CofreDashBoardGeral> obterEstatisticaPorHora(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        Map<String, String> filtro = filtros;
        List<CofreDashBoardGeral> retorno = new ArrayList<>();
        CofreDashBoardGeral cofreDashBoardGeral;

        for (int i = 0; i < 24; i++) {
            cofreDashBoardGeral = new CofreDashBoardGeral();
            cofreDashBoardGeral.setData(filtro.get(" TesCofresMov.data = ? "));
            cofreDashBoardGeral.setHora(String.valueOf(i));
            cofreDashBoardGeral.setCofresAtivos("0");
            cofreDashBoardGeral.setQtdDepositos("0");
            cofreDashBoardGeral.setQtdColetas("0");
            retorno.add(cofreDashBoardGeral);
        }
        try {
            int indice;
            String sql = "SELECT\n"
                    + "CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2)) Hora,\n"
                    + "(SELECT COUNT(DISTINCT  t.CodCofre) \n"
                    + "    FROM TesCofresMov t \n";
            if (!exibirTodos) {
                sql += "    LEFT JOIN Clientes ON Clientes.CodCofre = t.CodCofre\n"
                        + "    inner Join PessoaCliAut p on p.CodCli = Clientes.Codigo\n"
                        + "            and p.CodFil = Clientes.Codfil\n"
                        + "    WHERE t.Data = TesCofresMov.Data \n"
                        + "            AND p.codigo = PessoaCliAut.codigo\n"
                        + "        AND p.flag_excl <> '*'\n";
            }
            sql += ") CofresAtivos,\n"
                    + "(SELECT COUNT(*) \n"
                    + "    FROM TesCofresMov t \n";
            if (!exibirTodos) {
                sql += "    LEFT JOIN Clientes ON Clientes.CodCofre = t.CodCofre\n"
                        + "    inner Join PessoaCliAut p on p.CodCli = Clientes.Codigo\n"
                        + "            and p.CodFil = Clientes.Codfil\n";
            }
            sql += "    WHERE t.TipoDeposito = 'DINHEIRO' AND t.Data = TesCofresMov.Data\n"
                    + "        AND CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), t.Hora, 102), '.', '-'),2)) = \n"
                    + "            CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2))\n";
            if (!exibirTodos) {
                sql += "        AND p.codigo = PessoaCliAut.codigo\n"
                        + "        AND p.flag_excl <> '*'\n";
            }
            sql += ") QtdDepositos,\n"
                    + "(SELECT COUNT(*) \n"
                    + "    FROM TesCofresMov t \n";
            if (!exibirTodos) {
                sql += "    LEFT JOIN Clientes ON Clientes.CodCofre = t.CodCofre\n"
                        + "    inner Join PessoaCliAut p on p.CodCli = Clientes.Codigo\n"
                        + "            and p.CodFil = Clientes.Codfil\n";
            }
            sql += "    WHERE t.TipoDeposito = 'COLETA' AND t.Data = TesCofresMov.Data\n"
                    + "        AND CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), t.Hora, 102), '.', '-'),2)) = \n"
                    + "            CONVERT(INT, LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2))\n";
            if (!exibirTodos) {
                sql += "        AND p.codigo = PessoaCliAut.codigo\n"
                        + "        AND p.flag_excl <> '*'\n";
            }
            sql += ") QtdColetas\n"
                    + "    FROM TesCofresMov \n";
            if (!exibirTodos) {
                sql += " LEFT JOIN Clientes ON Clientes.CodCofre = TesCofresMov.CodCofre \n"
                        + "inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                        + " and PessoaCliAut.CodFil = Clientes.Codfil\n"
                        + "        AND PessoaCliAut.flag_excl <> '*'\n";
            }
            sql += "WHERE TesCofresMov.Data IS NOT NULL \n";

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            sql += "GROUP BY LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2), Data\n";
            if (!exibirTodos) {
                sql += "    , pessoacliaut.codigo\n";
            }
            sql += "ORDER BY LEFT(REPLACE(CONVERT(VARCHAR(8), TesCofresMov.Hora, 102), '.', '-'),2)";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();
            while (consulta.Proximo()) {
                cofreDashBoardGeral = new CofreDashBoardGeral();
                cofreDashBoardGeral.setData(filtro.get(" TesCofresMov.data = ? "));
                cofreDashBoardGeral.setHora(consulta.getString("Hora"));

                indice = retorno.indexOf(cofreDashBoardGeral);
                if (indice > -1) {
                    retorno.get(indice).setCofresAtivos(consulta.getString("CofresAtivos"));
                    retorno.get(indice).setQtdDepositos(consulta.getString("QtdDepositos"));
                    retorno.get(indice).setQtdColetas(consulta.getString("QtdColetas"));
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CofreDashBoardDao.obterEstatisticaPorHora - " + e.getMessage());
        }
    }

}

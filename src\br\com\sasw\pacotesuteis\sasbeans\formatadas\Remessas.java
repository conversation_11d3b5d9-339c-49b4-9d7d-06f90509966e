/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.pacotesuteis.sasbeans.formatadas;

/**
 *
 * <AUTHOR>
 */
public class Remessas {
    
    private String CodCli;
    private String Nome;
    private String Endereco;
    private String Rota;
    private String Sequencia;
    private String NroChave;
    private String Data;
    private String ER;
    private String CodCli1;
    private String Hora1;
    private String Parada;
    private String Dpar;
    private String CodCli2;
    private String Hora1D;
    private String TipoSrv;
    private String Chave;
    private String Regiao;
    private String Observ;
    private String Valor;
    private String Moeda;
    private String Guia;
    private String Serie;
    private String MatrChe;
    private String NomeChe;
    private String Veiculo;
    private String CodRemessa;
    private String Lacre;
    private String Qtde;
    private String Remessa;
    private String Oper_Prep;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getMoeda() {
        return Moeda;
    }

    public void setMoeda(String Moeda) {
        this.Moeda = Moeda;
    }

    public String getNroChave() {
        return NroChave;
    }

    public void setNroChave(String NroChave) {
        this.NroChave = NroChave;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getER() {
        return ER;
    }

    public void setER(String ER) {
        this.ER = ER;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getParada() {
        return Parada;
    }

    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    public String getDpar() {
        return Dpar;
    }

    public void setDpar(String Dpar) {
        this.Dpar = Dpar;
    }

    public String getCodCli2() {
        return CodCli2;
    }

    public void setCodCli2(String CodCli2) {
        this.CodCli2 = CodCli2;
    }

    public String getHora1D() {
        return Hora1D;
    }

    public void setHora1D(String Hora1D) {
        this.Hora1D = Hora1D;
    }

    public String getTipoSrv() {
        return TipoSrv;
    }

    public void setTipoSrv(String TipoSrv) {
        this.TipoSrv = TipoSrv;
    }

    public String getChave() {
        return Chave;
    }

    public void setChave(String Chave) {
        this.Chave = Chave;
    }

    public String getRegiao() {
        return Regiao;
    }

    public void setRegiao(String Regiao) {
        this.Regiao = Regiao;
    }

    public String getObserv() {
        return Observ;
    }

    public void setObserv(String Observ) {
        this.Observ = Observ;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getMatrChe() {
        return MatrChe;
    }

    public void setMatrChe(String MatrChe) {
        this.MatrChe = MatrChe;
    }

    public String getNomeChe() {
        return NomeChe;
    }

    public void setNomeChe(String NomeChe) {
        this.NomeChe = NomeChe;
    }

    public String getVeiculo() {
        return Veiculo;
    }

    public void setVeiculo(String Veiculo) {
        this.Veiculo = Veiculo;
    }

    public String getCodRemessa() {
        return CodRemessa;
    }

    public void setCodRemessa(String CodRemessa) {
        this.CodRemessa = CodRemessa;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        this.Qtde = Qtde;
    }

    public String getRemessa() {
        return Remessa;
    }

    public void setRemessa(String Remessa) {
        this.Remessa = Remessa;
    }

    public String getOper_Prep() {
        return Oper_Prep;
    }

    public void setOper_Prep(String Oper_Prep) {
        this.Oper_Prep = Oper_Prep;
    }
}
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.CCusto;
import SasBeans.Clientes;
import SasBeans.ComprasRec;
import SasBeans.Filiais;
import SasBeans.Fornec;
import SasBeans.NFItens;
import SasBeans.NFiscal;
import SasBeans.OCompra;
import SasBeans.OCompraItens;
import SasBeans.Produtos;
import SasBeans.TiposTribut;

/**
 *
 * <AUTHOR>
 */
public class RegistroSaidaNotaFiscal {

    private CCusto ccusto;
    private NFiscal nfiscal;
    private Clientes clientes;
    private NFItens nfitens;
    private Produtos produtos;
    private OCompra ocompra;
    private OCompraItens ocompraitens;
    private TiposTribut tipostribut;
    private ComprasRec comprasrec;
    private Fornec fornec;
    private Filiais filiais;

    public CCusto getCcusto() {
        return ccusto;
    }

    public void setCcusto(CCusto ccusto) {
        this.ccusto = ccusto;
    }

    public NFiscal getNfiscal() {
        return nfiscal;
    }

    public void setNfiscal(NFiscal nfiscal) {
        this.nfiscal = nfiscal;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    public NFItens getNfitens() {
        return nfitens;
    }

    public void setNfitens(NFItens nfitens) {
        this.nfitens = nfitens;
    }

    public Produtos getProdutos() {
        return produtos;
    }

    public void setProdutos(Produtos produtos) {
        this.produtos = produtos;
    }

    public OCompra getOcompra() {
        return ocompra;
    }

    public void setOcompra(OCompra ocompra) {
        this.ocompra = ocompra;
    }

    public OCompraItens getOcompraitens() {
        return ocompraitens;
    }

    public void setOcompraitens(OCompraItens ocompraitens) {
        this.ocompraitens = ocompraitens;
    }

    public TiposTribut getTipostribut() {
        return tipostribut;
    }

    public void setTipostribut(TiposTribut tipostribut) {
        this.tipostribut = tipostribut;
    }

    public ComprasRec getComprasrec() {
        return comprasrec;
    }

    public void setComprasrec(ComprasRec comprasrec) {
        this.comprasrec = comprasrec;
    }

    public Fornec getFornec() {
        return fornec;
    }

    public void setFornec(Fornec fornec) {
        this.fornec = fornec;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

}

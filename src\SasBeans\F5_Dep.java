package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class F5_Dep {

    private BigDecimal Matr;
    private String Tipo;
    private String Sexo;
    private String Nome;
    private LocalDate Dt_Nasc;
    private String Naturalid;
    private BigDecimal CodNaturalid;
    private String CPF;
    private String EstCivil;
    private String DepIR;
    private String DepSF;
    private String DepPS;
    private BigDecimal ValorPS;
    private String VerbaPS;
    private String DepPO;
    private BigDecimal ValorPO;
    private String VerbaPO;
    private String Obs;
    private BigDecimal CodForn;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public F5_Dep() {
        this.Matr = new BigDecimal("0");
        this.Tipo = "";
        this.Sexo = "";
        this.Nome = "";
        this.Dt_Nasc = LocalDate.now();
        this.Naturalid = "";
        this.CodNaturalid = new BigDecimal("0");
        this.CPF = "";
        this.EstCivil = "";
        this.DepIR = "";
        this.DepSF = "";
        this.DepPS = "";
        this.ValorPS = new BigDecimal("0");
        this.VerbaPS = "";
        this.DepPO = "";
        this.ValorPO = new BigDecimal("0");
        this.VerbaPO = "";
        this.Obs = "";
        this.CodForn = new BigDecimal("0");
        this.Operador = "";
        this.Dt_Alter = LocalDate.now();
        this.Hr_Alter = "";
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getSexo() {
        return Sexo;
    }

    public void setSexo(String Sexo) {
        this.Sexo = Sexo;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public LocalDate getDt_Nasc() {
        return Dt_Nasc;
    }

    public void setDt_Nasc(LocalDate Dt_Nasc) {
        this.Dt_Nasc = Dt_Nasc;
    }

    public String getNaturalid() {
        return Naturalid;
    }

    public void setNaturalid(String Naturalid) {
        this.Naturalid = Naturalid;
    }

    public BigDecimal getCodNaturalid() {
        return CodNaturalid;
    }

    public void setCodNaturalid(String CodNaturalid) {
        try {
            this.CodNaturalid = new BigDecimal(CodNaturalid);
        } catch (Exception e) {
            this.CodNaturalid = new BigDecimal("0");
        }
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getEstCivil() {
        return EstCivil;
    }

    public void setEstCivil(String EstCivil) {
        this.EstCivil = EstCivil;
    }

    public String getDepIR() {
        return DepIR;
    }

    public void setDepIR(String DepIR) {
        this.DepIR = DepIR;
    }

    public String getDepSF() {
        return DepSF;
    }

    public void setDepSF(String DepSF) {
        this.DepSF = DepSF;
    }

    public String getDepPS() {
        return DepPS;
    }

    public void setDepPS(String DepPS) {
        this.DepPS = DepPS;
    }

    public BigDecimal getValorPS() {
        return ValorPS;
    }

    public void setValorPS(String ValorPS) {
        try {
            this.ValorPS = new BigDecimal(ValorPS);
        } catch (Exception e) {
            this.ValorPS = new BigDecimal("0");
        }
    }

    public String getVerbaPS() {
        return VerbaPS;
    }

    public void setVerbaPS(String VerbaPS) {
        this.VerbaPS = VerbaPS;
    }

    public String getDepPO() {
        return DepPO;
    }

    public void setDepPO(String DepPO) {
        this.DepPO = DepPO;
    }

    public BigDecimal getValorPO() {
        return ValorPO;
    }

    public void setValorPO(String ValorPO) {
        try {
            this.ValorPO = new BigDecimal(ValorPO);
        } catch (Exception e) {
            this.ValorPO = new BigDecimal("0");
        }
    }

    public String getVerbaPO() {
        return VerbaPO;
    }

    public void setVerbaPO(String VerbaPO) {
        this.VerbaPO = VerbaPO;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public BigDecimal getCodForn() {
        return CodForn;
    }

    public void setCodForn(String CodForn) {
        try {
            this.CodForn = new BigDecimal(CodForn);
        } catch (Exception e) {
            this.CodForn = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

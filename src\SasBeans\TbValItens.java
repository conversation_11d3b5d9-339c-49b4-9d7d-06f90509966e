/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class TbValItens {

    private Integer Tabela;
    private Integer Codigo;
    private Integer Ordem;
    private String Descricao;
    private String Aba;
    private String pergunta;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public String getAba() {
        return Aba;
    }

    public void setAba(String Aba) {
        this.Aba = Aba;
    }

    /**
     * Void Constructor
     */
    public TbValItens() {
        // void constructor
    }

    public Integer getTabela() {
        return this.Tabela;
    }

    public void setTabela(Integer tabela) {
        this.Tabela = tabela;
    }

    public Integer getCodigo() {
        return this.Codigo;
    }

    public void setCodigo(Integer codigo) {
        this.Codigo = codigo;
    }

    public Integer getOrdem() {
        return this.Ordem;
    }

    public void setOrdem(Integer ordem) {
        this.Ordem = ordem;
    }

    public String getDescricao() {
        return this.Descricao;
    }

    public void setDescricao(String descricao) {
        this.Descricao = descricao;
    }

    public String getOperador() {
        return this.Operador;
    }

    public void setOperador(String operador) {
        this.Operador = operador;
    }

    public String getDt_Alter() {
        return this.Dt_Alter;
    }

    public void setDt_Alter(String dt_alter) {
        this.Dt_Alter = dt_alter;
    }

    public String getHr_Alter() {
        return this.Hr_Alter;
    }

    public void setHr_Alter(String hr_alter) {
        this.Hr_Alter = hr_alter;
    }

    public String getPergunta() {
        return pergunta;
    }

    public void setPergunta(String pergunta) {
        this.pergunta = pergunta;
    }

}

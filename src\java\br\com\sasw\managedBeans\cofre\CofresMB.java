/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.cofre;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Controller.CofreInteligente.CofreIntelSatMobWeb;
import Controller.Login.LoginSatMobWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaLogin;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.Saspw;
import SasBeans.Saspwac;
import SasBeans.Sysdef;
import SasBeans.TesCofresMov;
import SasBeans.TesCofresRes;
import SasBeansCompostas.SaspwacSysdef;
import SasBeansCompostas.TesCofresResClientes;
import SasBeansCompostas.UsuarioSatMobWeb;
import br.com.sasw.lazydatamodels.CofresLazyList;
import br.com.sasw.lazydatamodels.cofre.MovimentacaoCofreLazyList;
import br.com.sasw.managedBeans.recursoshumanos.PessoasMB;
import br.com.sasw.pacotesuteis.controller.acessos.AcessosSatMobWeb;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Logger;
import br.com.sasw.utils.Messages;
import com.lowagie.text.Document;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import java.awt.Color;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "cofres")
@javax.faces.view.ViewScoped
public class CofresMB implements Serializable {

    private Date dataCofre, date1, date2;
    private String codFil, dataCofreStr, banco, nomeFilial, codcli, operador, senhaAtual, senhaNova,
            codcofre, caminho, log, data1, data2, img;
    private Clientes cliente;
    private BigDecimal codpessoa, totalCreditoDia, totalValorRecolhido, totalSaldoCustoDia, totalSaldoCofre;
    private ArquivoLog logerro;
    private Persistencia persistencia, central;
    private List<TesCofresResClientes> listaMovimentacao;
    private TesCofresResClientes cofreSelecionado, movimentacaoSelecionado;
    private List<TesCofresMov> detalhesMovimentacao;
    private CofreIntelSatMobWeb cofreSatMobWeb;
    private LoginSatMobWeb loginSatMobWeb;
    private AcessosSatMobWeb acessosSatMobWeb;
    private LazyDataModel<TesCofresResClientes> cofres = null;
    private LazyDataModel<TesCofresMov> movimentacoesPaginada = null;
    private TesCofresMov movimentacaoSelecionada;
    private int total, totalMovimentacoesPaginda;
    private Map filters, filtroMovimentacoes;
    private boolean cofre, data, dataStr, diaSeman, feriado, valorRecD0, horaRecD0, depDiaAntAposCorte,
            valorCorteD0, totalCredDia, hrRecDia, valorRecDia, valorRecJaCreditado, valorRecACreditar,
            saldoCofreTotal, depositoJaCreditado, depositoProxDU, saldoFisCst, hora, nomeUsuario, valorDeposito,
            tipoDeposito, status, WALMART;

    public CofresMB() {
        FacesContext facesContext = FacesContext.getCurrentInstance();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(Date.from(Instant.now()));
        dataCofre = calendar.getTime();
        dataCofreStr = dataCofre.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        banco = (String) facesContext.getExternalContext().getSessionMap().get("banco");
        codFil = (String) facesContext.getExternalContext().getSessionMap().get("filial");
        codcli = (String) facesContext.getExternalContext().getSessionMap().get("cliente");
        codcofre = (String) facesContext.getExternalContext().getSessionMap().get("codcofre");
        operador = (String) facesContext.getExternalContext().getSessionMap().get("nome");
        codpessoa = (BigDecimal) facesContext.getExternalContext().getSessionMap().get("codpessoa");
        nomeFilial = (String) facesContext.getExternalContext().getSessionMap().get("nomeFilial");
        totalCreditoDia = BigDecimal.ZERO;
        totalValorRecolhido = BigDecimal.ZERO;
        totalSaldoCustoDia = BigDecimal.ZERO;
        totalSaldoCofre = BigDecimal.ZERO;
        cofreSelecionado = new TesCofresResClientes();
        cofreSelecionado.setClientes(new Clientes());
        cofreSelecionado.setTescofresres(new TesCofresRes());
        movimentacaoSelecionado = new TesCofresResClientes();
        cofreSatMobWeb = new CofreIntelSatMobWeb();
        acessosSatMobWeb = new AcessosSatMobWeb();
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        loginSatMobWeb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
        cofre = true;
        data = true;
        dataStr = true;
        diaSeman = true;
        feriado = true;
        valorRecD0 = true;
        horaRecD0 = true;
        depDiaAntAposCorte = true;
        valorCorteD0 = true;
        totalCredDia = true;
        hrRecDia = true;
        valorRecDia = true;
        valorRecJaCreditado = true;
        valorRecACreditar = true;
        saldoCofreTotal = true;
        depositoJaCreditado = true;
        depositoProxDU = true;
        saldoFisCst = true;
        hora = true;
        nomeUsuario = true;
        valorDeposito = true;
        tipoDeposito = true;
        status = true;

        /**
         * WALMART
         */
        WALMART = ((BigDecimal) facesContext.getExternalContext().getSessionMap().get("codpessoa")).toBigInteger().toString().equals("17766");
        clientessatmobweb = new ClientesSatMobWeb();
        selecionado = new UsuarioSatMobWeb();
        novo = new UsuarioSatMobWeb();
        filiais = new ArrayList<>();
        filialSelecionada = new SasPWFill();
        novaFilial = new SasPWFill();
        filial = new SasPWFill();
        filial.setCodfilAc(codFil);
        filial.setDescricao(nomeFilial);
        permissoes = new ArrayList<>();
        permissaoSelecionada = new SaspwacSysdef();
        novaPermissao = new SaspwacSysdef();
        sistema = new String();
        subsistema = new String();
        pessoa = new Pessoa();
        inclusao = false;
        exclusao = false;
        alteracao = false;
        flag = 1;
        log = new String();
        logerro = new ArquivoLog();
    }

    public void Persistencia(Persistencia pp, Persistencia local) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.central = local;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }
            this.filters = new HashMap();
            this.filters.put(" TesCofresRes.data = ? ", DataAtual.getDataAtual("SQL"));
            this.filters.put(" pessoacliaut.codigo = ? ", this.codpessoa.toPlainString());
            this.filters.put(" clientes.codigo = ? ", this.codcli);
            this.total = this.cofreSatMobWeb.Contagem(this.filters, this.codpessoa, this.persistencia);
            this.totalCreditoDia = this.cofreSatMobWeb.TotalVlrTotalCred(this.filters, this.codpessoa, this.persistencia);
            this.totalValorRecolhido = this.cofreSatMobWeb.TotalVlrRecolhido(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCustoDia = this.cofreSatMobWeb.TotalSaldoFisCst(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCofre = this.cofreSatMobWeb.TotalSaldoFisTotal(this.filters, this.codpessoa, this.persistencia);

            this.filtroMovimentacoes = new HashMap();
            this.filtroMovimentacoes.put(" TesCofresMov.data = ? ", DataAtual.getDataAtual("SQL"));
            this.filtroMovimentacoes.put(" TesCofresMov.codCofre = ? ", this.codcofre);

            this.cliente = this.cofreSatMobWeb.clienteCofre(this.codcli, this.codFil, this.persistencia);

            this.pessoas = new PessoasMB(this.persistencia, this.central);
            this.novo.setGrupo(new SASGrupos());
            this.novo.setPessoa(new Pessoa());
            this.novo.setPessoalogin(new PessoaLogin());
            this.novo.setSaspw(new Saspw());
            SASGrupos grupo = new SASGrupos();
            grupo.setCodigo("5");
            grupo.setDescricao("Controle de ATM");
            this.grupos = new ArrayList<>();
            this.grupos.add(grupo);
            this.todasFiliais = this.acessosSatMobWeb.listarTodasFiliais(this.persistencia);
            this.todasPermissoes = this.acessosSatMobWeb.listarTodasPermissoes(this.persistencia);
            this.novaPermissao.setSaspwac(new Saspwac());
            this.novaPermissao.setSysdef(new Sysdef());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<TesCofresResClientes> getAllCofres() {
        try {
            if (this.cofres == null) {
                DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
                this.filters.replace(" TesCofresRes.data = ? ", DataAtual.getDataAtual("SQL"));
                this.filters.replace(" pessoacliaut.codigo = ? ", this.codpessoa.toPlainString());
                this.filters.replace(" clientes.codigo = ? ", this.codcli);
                dt.setFilters(this.filters);
                this.cofres = new CofresLazyList(this.persistencia, this.codpessoa);
                this.total = this.cofreSatMobWeb.Contagem(this.filters, this.codpessoa, this.persistencia);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return this.cofres;
    }

    public LazyDataModel<TesCofresMov> getAllMovimentacoesPaginada() {
        try {
            if (this.movimentacoesPaginada == null) {
                DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
                this.filtroMovimentacoes.replace(" TesCofresMov.data = ? ", DataAtual.getDataAtual("SQL"));
                this.filtroMovimentacoes.replace(" TesCofresMov.codCofre = ? ", this.codcofre);
                dt.setFilters(this.filtroMovimentacoes);
                this.movimentacoesPaginada = new MovimentacaoCofreLazyList(this.persistencia);
                this.totalMovimentacoesPaginda = this.cofreSatMobWeb.totalMovimentacaoPaginada(this.filters, this.persistencia);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return this.movimentacoesPaginada;
    }

    public void dataAnteriorMovimentacoes() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.dataCofre);
            calendar.add(Calendar.DAY_OF_WEEK, -1);
            this.dataCofre = calendar.getTime();
            this.dataCofreStr = this.dataCofre.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.filtroMovimentacoes.replace(" TesCofresMov.data = ? ", this.dataCofreStr);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filtroMovimentacoes);
            getAllMovimentacoesPaginada();
            dt.setFirst(0);
            this.totalMovimentacoesPaginda = this.cofreSatMobWeb.totalMovimentacaoPaginada(this.filtroMovimentacoes, this.persistencia);

            this.filters.replace(" TesCofresRes.data = ? ", this.dataCofreStr);
            this.totalCreditoDia = this.cofreSatMobWeb.TotalVlrTotalCred(this.filters, this.codpessoa, this.persistencia);
            this.totalValorRecolhido = this.cofreSatMobWeb.TotalVlrRecolhido(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCustoDia = this.cofreSatMobWeb.TotalSaldoFisCst(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCofre = this.cofreSatMobWeb.TotalSaldoFisTotal(this.filters, this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataPosteriorMovimentacoes() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.dataCofre);
            calendar.add(Calendar.DAY_OF_WEEK, 1);
            this.dataCofre = calendar.getTime();
            this.dataCofreStr = this.dataCofre.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.filtroMovimentacoes.replace(" TesCofresMov.data = ? ", this.dataCofreStr);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filtroMovimentacoes);
            getAllMovimentacoesPaginada();
            dt.setFirst(0);
            this.totalMovimentacoesPaginda = this.cofreSatMobWeb.totalMovimentacaoPaginada(this.filtroMovimentacoes, this.persistencia);

            this.filters.replace(" TesCofresRes.data = ? ", this.dataCofreStr);
            this.totalCreditoDia = this.cofreSatMobWeb.TotalVlrTotalCred(this.filters, this.codpessoa, this.persistencia);
            this.totalValorRecolhido = this.cofreSatMobWeb.TotalVlrRecolhido(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCustoDia = this.cofreSatMobWeb.TotalSaldoFisCst(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCofre = this.cofreSatMobWeb.TotalSaldoFisTotal(this.filters, this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarDataMovimentacoes(SelectEvent event) {
        try {
            this.dataCofre = (Date) event.getObject();
            this.dataCofreStr = this.dataCofre.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.filtroMovimentacoes.replace(" TesCofresMov.data = ? ", this.dataCofreStr);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filtroMovimentacoes);
            getAllMovimentacoesPaginada();
            dt.setFirst(0);
            this.totalMovimentacoesPaginda = this.cofreSatMobWeb.totalMovimentacaoPaginada(this.filtroMovimentacoes, this.persistencia);

            this.filters.replace(" TesCofresRes.data = ? ", this.dataCofreStr);
            this.totalCreditoDia = this.cofreSatMobWeb.TotalVlrTotalCred(this.filters, this.codpessoa, this.persistencia);
            this.totalValorRecolhido = this.cofreSatMobWeb.TotalVlrRecolhido(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCustoDia = this.cofreSatMobWeb.TotalSaldoFisCst(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCofre = this.cofreSatMobWeb.TotalSaldoFisTotal(this.filters, this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void detalhesMovimentacao() {
        try {
            this.cofreSelecionado = new TesCofresResClientes();
            this.cofreSelecionado.setClientes(this.cliente);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.dataCofre);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            this.date1 = calendar.getTime();
            this.data1 = this.date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            this.date2 = calendar.getTime();
            this.data2 = this.date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            ListarMovimentacao();
            this.movimentacaoSelecionado = null;
            PrimeFaces.current().executeScript("PF('dlgListarMovimentos').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void atualizarListaMovimentacoes() {
        this.data1 = this.date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        this.data2 = this.date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        ListarMovimentacao();
    }

    public void SelecionarData(SelectEvent event) {
        try {
            this.dataCofre = (Date) event.getObject();
            this.dataCofreStr = this.dataCofre.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.filters.replace(" TesCofresRes.data = ? ", this.dataCofreStr);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllCofres();
            dt.setFirst(0);
            this.total = this.cofreSatMobWeb.Contagem(this.filters, this.codpessoa, this.persistencia);
            this.totalCreditoDia = this.cofreSatMobWeb.TotalVlrTotalCred(this.filters, this.codpessoa, this.persistencia);
            this.totalValorRecolhido = this.cofreSatMobWeb.TotalVlrRecolhido(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCustoDia = this.cofreSatMobWeb.TotalSaldoFisCst(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCofre = this.cofreSatMobWeb.TotalSaldoFisTotal(this.filters, this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void DataAnteriorCofre() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.dataCofre);
            calendar.add(Calendar.DAY_OF_WEEK, -1);
            this.dataCofre = calendar.getTime();
            this.dataCofreStr = this.dataCofre.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.filters.replace(" TesCofresRes.data = ? ", this.dataCofreStr);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllCofres();
            dt.setFirst(0);
            this.total = this.cofreSatMobWeb.Contagem(this.filters, this.codpessoa, this.persistencia);
            this.totalCreditoDia = this.cofreSatMobWeb.TotalVlrTotalCred(this.filters, this.codpessoa, this.persistencia);
            this.totalValorRecolhido = this.cofreSatMobWeb.TotalVlrRecolhido(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCustoDia = this.cofreSatMobWeb.TotalSaldoFisCst(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCofre = this.cofreSatMobWeb.TotalSaldoFisTotal(this.filters, this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void DataPosteriorCofre() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.dataCofre);
            calendar.add(Calendar.DAY_OF_WEEK, 1);
            this.dataCofre = calendar.getTime();
            this.dataCofreStr = this.dataCofre.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.filters.replace(" TesCofresRes.data = ? ", this.dataCofreStr);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllCofres();
            dt.setFirst(0);
            this.total = this.cofreSatMobWeb.Contagem(this.filters, this.codpessoa, this.persistencia);
            this.totalCreditoDia = this.cofreSatMobWeb.TotalVlrTotalCred(this.filters, this.codpessoa, this.persistencia);
            this.totalValorRecolhido = this.cofreSatMobWeb.TotalVlrRecolhido(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCustoDia = this.cofreSatMobWeb.TotalSaldoFisCst(this.filters, this.codpessoa, this.persistencia);
            this.totalSaldoCofre = this.cofreSatMobWeb.TotalSaldoFisTotal(this.filters, this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void DblSelect(SelectEvent event) {
        this.cofreSelecionado = (TesCofresResClientes) event.getObject();
        buttonAction();
    }

    public void buttonAction() {
        if (this.cofreSelecionado == null) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCofre"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.data1 = "";
                this.data2 = "";
                ListarMovimentacao();
                this.movimentacaoSelecionado = null;
                PrimeFaces.current().executeScript("PF('dlgListarMovimentos').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void detalhesDblSelect(SelectEvent event) {
        this.movimentacaoSelecionado = (TesCofresResClientes) event.getObject();
        detalhesButtonAction();
    }

    public void detalhesButtonAction() {
        if (this.movimentacaoSelecionado == null) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneMovimentacao"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.detalhesMovimentacao = this.cofreSatMobWeb.detalhesMovimentacaoCofre(this.movimentacaoSelecionado.getTescofresres().getCodCofre(),
                        this.movimentacaoSelecionado.getTescofresres().getData().toString(), this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgListarDetalhesmovimentacao').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void TrocaSenha() {
        try {
            Boolean senhaValida = this.loginSatMobWeb.VerificaSenha(this.codpessoa, this.senhaAtual, this.persistencia);
            if (senhaValida) {
                this.acessosSatMobWeb.trocarSenhaCliente(this.codpessoa, this.senhaNova,
                        FuncoesString.RecortaAteEspaço(operador, 0, 10), this.persistencia, this.central);
            } else {
                throw new Exception("SenhaIncorreta");
            }
            PrimeFaces.current().executeScript("PF('dlgTrocarSenha').hide()");
            PrimeFaces.current().executeScript("PF('dlgOk').hide()");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SenhaAlteradaSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void ListarMovimentacao() {
        try {
            String vCodPessoa = this.codpessoa.toString();
            this.listaMovimentacao = this.cofreSatMobWeb.MovimentoCofreCliente(this.cofreSelecionado.getClientes().getCodigo(),
                    this.cofreSelecionado.getClientes().getCodFil().toPlainString(),
                    this.data1, this.data2, vCodPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void TrocarSenha() {
        try {
            Boolean senhaValida = this.loginSatMobWeb.VerificaSenha(this.codpessoa, this.senhaAtual, this.persistencia);
            if (senhaValida) {
                this.acessosSatMobWeb.trocarSenhaCliente(this.codpessoa, this.senhaNova,
                        FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia, this.central);
            } else {
                throw new Exception("SenhaIncorreta");
            }
            PrimeFaces.current().executeScript("PF('dlgTrocarSenha').hide()");
            PrimeFaces.current().executeScript("PF('dlgOk').hide()");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SenhaAlteradaSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void exportarMovimentacao(Object document) {
        try {
            Document pdf = (Document) document;
            pdf.setPageSize(PageSize.A4.rotate());
            pdf.setMargins(0, 0, 18, 18);
            pdf.open();
            float[] columnWidths = {2, 10};
            PdfPTable table = new PdfPTable(columnWidths);
            table.setTotalWidth(pdf.right() - 18);
            table.setSplitLate(false);
            table.setSplitRows(true);
            PdfPCell cell;
            Font negrito;
            String logo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("") + File.separator + "assets" + File.separator + "logos" + File.separator + this.img;
            Image image = Image.getInstance(logo);
            image.scaleToFit(75, 100);
            cell = new PdfPCell();
            cell.addElement(image);
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            negrito = new Font(Font.HELVETICA, 14f, Font.BOLD);
            cell = new PdfPCell(new Phrase(this.cofreSatMobWeb.filialCofre(this.cofreSelecionado.getClientes().getCodFil().toPlainString(), this.persistencia), negrito));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(Messages.getMessageS("Operador") + ":"));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(this.operador));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(Messages.getMessageS("Data") + ":"));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(DataAtual.getDataAtual("TELA") + " " + DataAtual.getDataAtual("HORA")));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(Messages.getMessageS("Cliente") + ":"));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            negrito = new Font(Font.HELVETICA, 12f, Font.BOLD);
            cell = new PdfPCell(new Phrase(this.cofreSelecionado.getClientes().getNRed(), negrito));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);

            if (!data1.equals("") || !data2.equals("")) {
                cell = new PdfPCell(new Phrase(Messages.getMessageS("Período")));
                cell.setBorder(Rectangle.NO_BORDER);
                table.addCell(cell);
                cell = new PdfPCell(new Phrase(this.data1 + " à " + this.data2));
                cell.setBorder(Rectangle.NO_BORDER);
                table.addCell(cell);
            }

            pdf.add(table);

            Paragraph p = new Paragraph("Movimentação Cofre " + this.cofreSelecionado.getClientes().getCodCofre() + "\n\r",
                    new Font(Font.HELVETICA, 18, Font.BOLD, Color.BLACK));
            p.setAlignment(Element.ALIGN_CENTER);
            pdf.add(p);

            pdf.addAuthor(this.operador);
            pdf.addCreationDate();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void atualizaTabela() {
        PrimeFaces.current().ajax().update("main:tabela");
    }

    /**
     * Início dos métodos do Walmart
     */
    private SasPWFill filialSelecionada, novaFilial, filial;
    private int flag;
    private List<SasPWFill> filiais, todasFiliais;
    private UsuarioSatMobWeb selecionado, novo;
    private PessoasMB pessoas;
    private Pessoa pessoa;
    private List<SaspwacSysdef> permissoes;
    private SaspwacSysdef permissaoSelecionada, novaPermissao;
    private List<PessoaCliAut> clientes;
    private Clientes todosClientesSelecao;
    private boolean flag_exclPessoaCliAut, alteracao, inclusao, exclusao;
    private PessoaCliAut novoCliente, clienteSelecionado;
    private String grupoSelecionado, subsistema, sistema;
    private List<SASGrupos> grupos;
    private ClientesSatMobWeb clientessatmobweb;
    private List<Sysdef> todasPermissoes;
    private List<Clientes> todosClientes;

    public void selecionarFilialUsuario(SelectEvent event) {
        this.filial = (SasPWFill) event.getObject();
        this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
        this.selecionado.getSaspw().setCodFil(this.filial.getCodfilAc());
    }

    public void selecionarPessoa(SelectEvent event) {
        try {
            this.pessoa = this.pessoas.ListarParametro(((Pessoa) event.getObject())).get(0);
            this.novo.getSaspw().setCodPessoa(this.pessoa.getCodigo().toPlainString());
            if (null == this.pessoa.getCodPessoaWEB()) {
                this.pessoa = new Pessoa();
                throw new Exception(Messages.getMessageS("PessoaInvalida"));
            }
            UsuarioSatMobWeb usuarioSatMobWeb = this.acessosSatMobWeb.buscarUsuario(this.pessoa.getCodPessoaWEB(), this.persistencia, this.central);
            if (null != usuarioSatMobWeb) {
                this.selecionado = usuarioSatMobWeb;
                buttonActionAcesso(null);
            } else {
                this.novo.setPessoa(this.pessoa);
                if (null != this.novo.getPessoa().getMatr()
                        && this.novo.getPessoa().getMatr().compareTo(new BigDecimal("80000")) == 1
                        && this.novo.getPessoa().getMatr().compareTo(new BigDecimal("90000")) == -1) {
                    this.novo.getSaspw().setNome(this.pessoa.getEndereco());
                } else {
                    this.novo.getSaspw().setNome(this.pessoa.getCodigo().toBigInteger().toString());
                }
                this.novo.getSaspw().setCodPessoa(this.pessoa.getCodigo().toBigInteger().toString());
                this.novo.getSaspw().setCodPessoaWeb(this.pessoa.getCodPessoaWEB().toPlainString());
                this.novo.getSaspw().setNomeCompleto(this.pessoa.getNome());
                this.novo.getSaspw().setCodigo(this.pessoa.getCodigo().toBigInteger().toString());
                this.novo.getPessoalogin().setCodigo(this.pessoa.getCodPessoaWEB());
                this.novo.getPessoalogin().setCodPessoaBD(this.pessoa.getCodigo());
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void buttonActionAcesso(ActionEvent actionEvent) {
        if (null == this.selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                PrimeFaces.current().resetInputs("formEditar");
                this.novo = this.selecionado;
                this.filial = this.acessosSatMobWeb.buscarFilial(this.novo.getSaspw().getCodFil(), this.persistencia);
                prepararEdicao();
                this.flag = 2;
                PrimeFaces.current().executeScript("PF('dlgEditar').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        }
    }

    public void prepararEdicao() {
        try {
            this.pessoa = new Pessoa();
            this.pessoa.setCodigo(this.novo.getPessoalogin().getCodPessoaBD());
            this.pessoa.setNome("");
            this.pessoa = this.pessoas.ListarParametro(this.pessoa).get(0);
            this.filiais = this.acessosSatMobWeb.listarFiliaisPermissao(this.novo.getSaspw().getNome(), this.persistencia);
            this.novaFilial = new SasPWFill();
            this.permissoes = this.acessosSatMobWeb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);
            this.novaPermissao = new SaspwacSysdef();
            this.novaPermissao.setSysdef(new Sysdef());
            this.novaPermissao.setSaspwac(new Saspwac());
            this.flag_exclPessoaCliAut = false;
            this.novoCliente = new PessoaCliAut();
            this.clientes = this.acessosSatMobWeb.listarClientes(this.novo.getPessoa().getCodigo(), this.flag_exclPessoaCliAut, this.persistencia);
            this.todosClientesSelecao = null;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void editarAcesso() {
        try {
            this.novo.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            switch (this.novo.getSaspw().getNivelx()) {
                case "1":
                    this.novo.getSaspw().setNivelOP("Operação");
                    break;
                case "2":
                    this.novo.getSaspw().setNivelOP("Manutenção");
                    break;
                case "3":
                    this.novo.getSaspw().setNivelOP("Gerência");
                    break;
                case "4":
                    this.novo.getSaspw().setNivelOP("Portal RH");
                    break;
                case "5":
                    this.novo.getSaspw().setNivelOP("GTV");
                    break;
                case "6":
                    this.novo.getSaspw().setNivelOP("Cofre Int.");
                    break;
                case "7":
                    this.novo.getSaspw().setNivelOP("Assinar GTV-e");
                    break;
                case "8":
                    this.novo.getSaspw().setNivelOP("SatMobEW");
                    break;
                case "9":
                    this.novo.getSaspw().setNivelOP("Diretoria");
                    break;
                case "10":
                    this.novo.getSaspw().setNivelOP("Chamados");
                    break;
                default:
                    this.novo.getSaspw().setNivelOP("");
                    break;
            }
            this.novo.getSaspw().setNivelOP(this.novo.getSaspw().getNivelOP().toUpperCase());
            this.novo.getSaspw().setDescricao(this.novo.getSaspw().getDescricao().toUpperCase());
            this.novo.getSaspw().setMotivo(this.novo.getSaspw().getMotivo().toUpperCase());
            this.novo.getSaspw().setCodGrupo(Integer.valueOf(this.novo.getGrupo().getCodigo()));
            this.novo.getSaspw().setCodPessoa(this.pessoa.getCodigo().toPlainString().substring(0, this.pessoa.getCodigo().toPlainString().lastIndexOf(".0")));
            this.novo.getSaspw().setCodPessoaWeb(this.pessoa.getCodPessoaWEB().toPlainString());
            this.novo.getSaspw().setNomeCompleto(this.pessoa.getNome());
            this.novo.getSaspw().setCodigo(this.pessoa.getCodigo().toPlainString().substring(0, this.pessoa.getCodigo().toPlainString().lastIndexOf(".0")));
            this.novo.getPessoalogin().setCodigo(this.pessoa.getCodPessoaWEB());
            this.novo.getPessoalogin().setCodPessoaBD(this.pessoa.getCodigo());
            this.novo.getPessoalogin().setNivel(this.novo.getSaspw().getNivelx());

            this.acessosSatMobWeb.editarAcesso(this.novo, this.persistencia, this.central);
            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrar() throws Exception {
        this.novo.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
        switch (this.novo.getSaspw().getNivelx()) {
            case "1":
                this.novo.getSaspw().setNivelOP("Operação");
                break;
            case "2":
                this.novo.getSaspw().setNivelOP("Manutenção");
                break;
            case "3":
                this.novo.getSaspw().setNivelOP("Gerência");
                break;
            case "4":
                this.novo.getSaspw().setNivelOP("Portal RH");
                break;
            case "5":
                this.novo.getSaspw().setNivelOP("GTV");
                break;
            case "6":
                this.novo.getSaspw().setNivelOP("Cofre Int.");
                break;
            case "7":
                this.novo.getSaspw().setNivelOP("Assinar GTV-e");
                break;
            case "8":
                this.novo.getSaspw().setNivelOP("SatMobEW");
                break;
            case "9":
                this.novo.getSaspw().setNivelOP("Diretoria");
                break;
            case "10":
                this.novo.getSaspw().setNivelOP("Chamados");
                break;
            default:
                this.novo.getSaspw().setNivelOP("");
                break;
        }
        try {
            this.novo.getSaspw().setNivelOP(this.novo.getSaspw().getNivelOP().toUpperCase());
            this.novo.getSaspw().setDescricao(this.novo.getSaspw().getDescricao().toUpperCase());
            this.novo.getSaspw().setMotivo(this.novo.getSaspw().getMotivo().toUpperCase());
            this.novo.getPessoalogin().setNivel(this.novo.getSaspw().getNivelx());
            this.novo.getPessoalogin().setBancoDados(this.persistencia.getEmpresa());
            this.novo.getSaspw().setCodGrupo(Integer.valueOf(this.novo.getGrupo().getCodigo()));
            this.acessosSatMobWeb.criarAcesso(this.novo, this.persistencia, this.central);
            for (SaspwacSysdef permissao : this.permissoes) {
                permissao.getSaspwac().setNome(this.novo.getSaspw().getNome());
                this.acessosSatMobWeb.criarPermissoesIndividuais(permissao.getSaspwac(), this.persistencia);
            }
            for (SasPWFill f : this.filiais) {
                f.setNome(this.novo.getSaspw().getNome());
                this.acessosSatMobWeb.inserirFilial(f, this.persistencia);
            }
            for (PessoaCliAut c : this.clientes) {
                c.setCodigo(this.novo.getSaspw().getCodPessoa().toPlainString());
                c.setCodFil(this.novo.getSaspw().getCodFil());

                this.acessosSatMobWeb.inserirCliente(c, this.persistencia);
            }

            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.novo) + "\r\n"
                    + "\r\n" + Logger.listaObjetosCompostos2String(this.permissoes) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.filiais) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void adicionarPermissoes() {
        try {
            List<Sysdef> permissoesGrupo = this.acessosSatMobWeb.listarPermissoesGrupo(new BigDecimal(this.novo.getGrupo().getCodigo()), this.persistencia);
            for (Sysdef permissaogrupo : permissoesGrupo) {
                SaspwacSysdef permissao = new SaspwacSysdef();
                permissao.setSaspwac(new Saspwac());
                permissao.setSysdef(permissaogrupo);
                permissao.getSaspwac().setAlteracao(this.alteracao ? 1 : 0);
                permissao.getSaspwac().setInclusao(this.inclusao ? 1 : 0);
                permissao.getSaspwac().setExclusao(this.exclusao ? 1 : 0);
                permissao.getSaspwac().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                permissao.getSaspwac().setDt_Alter(DataAtual.getDataAtual("SQL"));
                permissao.getSaspwac().setHr_Alter(DataAtual.getDataAtual("HORA"));
                permissao.getSaspwac().setSistema(permissaogrupo.getCodigo());
                Boolean add = true;
                for (SaspwacSysdef permissaopermissao : this.permissoes) {
                    if (permissao.getSaspwac().getSistema().equals(permissaopermissao.getSaspwac().getSistema())) {
                        add = false;
                        break;
                    }
                }
                if (add) {
                    this.permissoes.add(permissao);
                }
            }
            cadastrar();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarFilial(SelectEvent event) {
        this.filialSelecionada = (SasPWFill) event.getObject();
    }

    public void apagarFilial() {
        if (this.flag == 1) {
            for (SasPWFill fil : this.filiais) {
                if (fil.equals(this.filialSelecionada)) {
                    filiais.remove(fil);
                    break;
                }
            }
        } else if (this.flag == 2) {
            try {
                this.acessosSatMobWeb.apagarFilial(this.novo.getSaspw().getNome(), this.filialSelecionada.getCodfilAc(), this.persistencia);
                this.filiais = this.acessosSatMobWeb.listarFiliaisPermissao(this.novo.getSaspw().getNome(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void selecionarNovaFilial(SelectEvent event) {
        this.novaFilial = (SasPWFill) event.getObject();
    }

    public void selecionarPermissao(SelectEvent event) {
        this.permissaoSelecionada = (SaspwacSysdef) event.getObject();
    }

    public void adicionarFilial() throws Exception {
        this.novaFilial.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novaFilial.setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.novaFilial.setHr_Alter(DataAtual.getDataAtual("HORA"));
        if (this.flag == 1) {
            Boolean add = true;
            for (SasPWFill f : this.filiais) {
                if (f.getCodfilAc().equals(this.novaFilial.getCodfilAc())) {
                    add = false;
                }
            }
            if (add) {
                this.filiais.add(this.novaFilial);
            }
        } else if (this.flag == 2) {
            try {
                this.novaFilial.setCodigo(this.novo.getSaspw().getCodPessoa().toPlainString());
                this.novaFilial.setNome(this.novo.getSaspw().getNome());
                this.acessosSatMobWeb.inserirFilial(this.novaFilial, this.persistencia);
                this.filiais = acessosSatMobWeb.listarFiliaisPermissao(this.novo.getSaspw().getNome(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
        this.novaFilial = new SasPWFill();
    }

    public void prepararPermissao() {
        this.novaPermissao = new SaspwacSysdef();
        this.novaPermissao.setSaspwac(new Saspwac());
        this.novaPermissao.setSysdef(new Sysdef());
        this.alteracao = false;
        this.inclusao = false;
        this.exclusao = false;
        this.subsistema = new String();
        this.sistema = new String();
    }

    public void abrirDialogoPermissao() {
        if (null == this.permissaoSelecionada
                || null == this.permissaoSelecionada.getSaspwac()
                || null == this.permissaoSelecionada.getSysdef()) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, "SelecionePermissao", null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.alteracao = false;
            if (this.permissaoSelecionada.getSaspwac().getAlteracao() == 1) {
                this.alteracao = true;
            }
            this.inclusao = false;
            if (this.permissaoSelecionada.getSaspwac().getInclusao() == 1) {
                this.inclusao = true;
            }
            this.exclusao = false;
            if (this.permissaoSelecionada.getSaspwac().getExclusao() == 1) {
                this.exclusao = true;
            }
            PrimeFaces.current().executeScript("PF('dlgEditarPermissoes').show()");
        }
    }

    public void apagarPermissao() {
        if (this.flag == 1) {
            for (SaspwacSysdef permissao : this.permissoes) {
                if (permissao.getSaspwac().getSistema().equals(this.permissaoSelecionada.getSaspwac().getSistema())) {
                    this.permissoes.remove(permissao);
                    break;
                }
            }
        } else if (this.flag == 2) {
            try {
                this.acessosSatMobWeb.apagarPermissoes(this.novo.getSaspw().getNome(), this.permissaoSelecionada.getSaspwac().getSistema().toPlainString(), this.persistencia);
                this.permissoes = this.acessosSatMobWeb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void adicionarPermissao() throws Exception {
        this.novaPermissao.getSaspwac().setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.novaPermissao.getSaspwac().setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.novaPermissao.getSaspwac().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novaPermissao.getSaspwac().setSistema(this.novaPermissao.getSysdef().getCodigo());
        this.novaPermissao.getSaspwac().setAlteracao(this.alteracao ? 1 : 0);
        this.novaPermissao.getSaspwac().setInclusao(this.inclusao ? 1 : 0);
        this.novaPermissao.getSaspwac().setExclusao(this.exclusao ? 1 : 0);
        if (this.flag == 1) {
            Boolean add = true;
            for (SaspwacSysdef permissao : this.permissoes) {
                if (permissao.getSaspwac().getSistema().equals(this.novaPermissao.getSaspwac().getSistema())) {
                    add = false;
                }
            }
            if (add) {
                this.permissoes.add(this.novaPermissao);
                this.novaPermissao = new SaspwacSysdef();
            }
        } else if (this.flag == 2) {
            try {
                this.novaPermissao.getSaspwac().setNome(this.novo.getSaspw().getNome());
                this.acessosSatMobWeb.criarPermissoesIndividuais(this.novaPermissao.getSaspwac(), this.persistencia);
                this.permissoes = acessosSatMobWeb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);
                this.novaPermissao = new SaspwacSysdef();
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
        prepararPermissao();
    }

    public void editarPermissao() throws Exception {
        this.permissaoSelecionada.getSaspwac().setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.permissaoSelecionada.getSaspwac().setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.permissaoSelecionada.getSaspwac().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.permissaoSelecionada.getSaspwac().setAlteracao(this.alteracao ? 1 : 0);
        this.permissaoSelecionada.getSaspwac().setExclusao(this.exclusao ? 1 : 0);
        this.permissaoSelecionada.getSaspwac().setInclusao(this.inclusao ? 1 : 0);
        if (this.flag == 1) {
        } else if (this.flag == 2) {
            try {
                this.permissaoSelecionada.getSaspwac().setNome(this.novo.getSaspw().getNome());
                this.acessosSatMobWeb.editarPermissoesIndividuais(this.permissaoSelecionada.getSaspwac(), this.persistencia);
                this.permissoes = this.acessosSatMobWeb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void selecionarPessoaCliAut(SelectEvent event) {
        this.clienteSelecionado = (PessoaCliAut) event.getObject();
    }

    public void listarPessoaCliAut() {
        try {
            this.clientes = this.acessosSatMobWeb.listarClientes(this.novo.getSaspw().getCodPessoa(), this.flag_exclPessoaCliAut, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void novoCliente() {
        this.todosClientesSelecao = null;
        PrimeFaces.current().executeScript("PF('dlgAdicionarClientes').show()");
    }

    public void apagarCliente() {
        if (this.flag == 1) {
            for (PessoaCliAut p : this.clientes) {
                if (p.getCodCli().equals(this.clienteSelecionado.getCodCli())) {
                    this.clientes.remove(p);
                    break;
                }
            }
        } else if (this.flag == 2) {
            try {
                this.acessosSatMobWeb.apagarCliente(this.clienteSelecionado, this.persistencia);
                this.clientes = this.acessosSatMobWeb.listarClientes(this.novo.getSaspw().getCodPessoa(), this.flag_exclPessoaCliAut, this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void selecionarCliente(SelectEvent event) {
        try {
            Clientes c = ((Clientes) event.getObject());
            c.setCodFil(c.getCodigo().substring(c.getCodigo().indexOf("}") + 1, c.getCodigo().length() - 2));
            c.setCodigo(c.getCodigo().substring(0, c.getCodigo().indexOf("}")));
            this.todosClientesSelecao = this.clientessatmobweb.ListaClientes(c.getCodFil().toString(), c.getCodigo(), "", "", "", this.persistencia).get(0);
            this.novoCliente.setCodCli(c.getCodigo());
            this.novoCliente.setCodFil(c.getCodFil().toPlainString());
            this.novoCliente.setNomeCli(this.todosClientesSelecao.getNRed());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void adicionarCliente() throws Exception {
        this.novoCliente.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.novoCliente.setDt_Alter(DataAtual.getDataAtual("SQL"));
        this.novoCliente.setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.novoCliente.setFlag_Excl("");
        if (this.flag == 1) {
            Boolean add = true;
            for (PessoaCliAut f : this.clientes) {
                if (f.getCodCli().equals(this.novoCliente.getCodCli())) {
                    add = false;
                }
            }
            if (add) {
                this.clientes.add(this.novoCliente);
            }
        } else if (this.flag == 2) {
            try {
                this.novoCliente.setCodigo(this.novo.getPessoa().getCodigo().toPlainString());
                this.acessosSatMobWeb.inserirCliente(this.novoCliente, this.persistencia);
                this.clientes = this.acessosSatMobWeb.listarClientes(this.novo.getSaspw().getCodPessoa(), this.flag_exclPessoaCliAut, this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                        + "\r\n" + Logger.objeto2String(this.novoCliente) + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
        this.novoCliente = new PessoaCliAut();
    }

    public void novoUsuario() {
        try {
            this.novo = new UsuarioSatMobWeb();
            this.selecionado = new UsuarioSatMobWeb();
            this.novo.setGrupo(new SASGrupos());
            this.novo.setPessoa(new Pessoa());
            this.novo.setPessoalogin(new PessoaLogin());
            this.novo.setSaspw(new Saspw());
            this.novo.getSaspw().setSituacao("A");
            this.selecionado.setGrupo(new SASGrupos());
            this.selecionado.setPessoa(new Pessoa());
            this.selecionado.setPessoalogin(new PessoaLogin());
            this.selecionado.setSaspw(new Saspw());
//            this.filial = this.acessosSatMobWeb.BuscarFilial(this.codFil, this.persistencia);
//            this.filial.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
//            this.filial.setDt_Alter(DataAtual.getDataAtual("SQL"));
//            this.filial.setHr_Alter(DataAtual.getDataAtual("HORA"));
//            this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());
            this.filiais = new ArrayList<>();
//            this.filiais.add(this.filial);
            this.permissoes = new ArrayList<>();
            this.novaPermissao = new SaspwacSysdef();
            this.novaPermissao.setSaspwac(new Saspwac());
            this.clientes = new ArrayList<>();
            this.novoCliente = new PessoaCliAut();
            this.clienteSelecionado = new PessoaCliAut();
            this.todosClientesSelecao = null;
            this.pessoa = new Pessoa();
            this.pessoa.setCodigo("-1");
            this.flag = 1;
            PrimeFaces.current().resetInputs("formEditar:editar");
            PrimeFaces.current().executeScript("PF('dlgEditar').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Clientes> listarClientes(String query) {
        this.todosClientes = new ArrayList<>();
        try {
            if (this.flag == 2) {
                List<Clientes> retorno = this.clientessatmobweb.buscaClientesBanco(this.novo.getSaspw().getNome(), query, "894", this.persistencia);
                String f;
                for (Clientes c : retorno) {
                    f = c.getCodFil().toString().substring(0, c.getCodFil().toString().indexOf(".0"));
                    c.setNome(f + ": " + c.getNRed() + " - " + c.getNome());
                    this.todosClientes.add(c);
                }
            } else if (this.flag == 1) {
                List<Clientes> retorno = this.clientessatmobweb.buscaClientesBanco(this.filiais, query, "894", this.persistencia);
                String f;
                for (Clientes c : retorno) {
                    f = c.getCodFil().toString().substring(0, c.getCodFil().toString().indexOf(".0"));
                    c.setNome(f + ": " + c.getNRed() + " - " + c.getNome());
                    this.todosClientes.add(c);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return this.todosClientes;
    }

    public Pessoa getPessoa() {
        try {
            return this.pessoas.ListarParametro(pessoa).get(0);
        } catch (Exception e) {
            return new Pessoa();
        }
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public List<TesCofresResClientes> getListaMovimentacao() {
        return listaMovimentacao;
    }

    public void setListaMovimentacao(List<TesCofresResClientes> listaMovimentacao) {
        this.listaMovimentacao = listaMovimentacao;
    }

    public TesCofresResClientes getCofreSelecionado() {
        return cofreSelecionado;
    }

    public void setCofreSelecionado(TesCofresResClientes cofreSelecionado) {
        this.cofreSelecionado = cofreSelecionado;
    }

    public TesCofresResClientes getMovimentacaoSelecionado() {
        return movimentacaoSelecionado;
    }

    public void setMovimentacaoSelecionado(TesCofresResClientes movimentacaoSelecionado) {
        this.movimentacaoSelecionado = movimentacaoSelecionado;
    }

    public BigDecimal getTotalCreditoDia() {
        return totalCreditoDia;
    }

    public void setTotalCreditoDia(BigDecimal totalCreditoDia) {
        this.totalCreditoDia = totalCreditoDia;
    }

    public BigDecimal getTotalValorRecolhido() {
        return totalValorRecolhido;
    }

    public void setTotalValorRecolhido(BigDecimal totalValorRecolhido) {
        this.totalValorRecolhido = totalValorRecolhido;
    }

    public BigDecimal getTotalSaldoCustoDia() {
        return totalSaldoCustoDia;
    }

    public void setTotalSaldoCustoDia(BigDecimal totalSaldoCustoDia) {
        this.totalSaldoCustoDia = totalSaldoCustoDia;
    }

    public BigDecimal getTotalSaldoCofre() {
        return totalSaldoCofre;
    }

    public void setTotalSaldoCofre(BigDecimal totalSaldoCofre) {
        this.totalSaldoCofre = totalSaldoCofre;
    }

    public Date getDataCofre() {
        return dataCofre;
    }

    public void setDataCofre(Date dataCofre) {
        this.dataCofre = dataCofre;
    }

    public String getDataCofreStr() {
        return dataCofreStr;
    }

    public void setDataCofreStr(String dataCofreStr) {
        this.dataCofreStr = dataCofreStr;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getSenhaAtual() {
        return senhaAtual;
    }

    public void setSenhaAtual(String senhaAtual) {
        this.senhaAtual = senhaAtual;
    }

    public String getNovaSenha() {
        return senhaNova;
    }

    public void setNovaSenha(String senhaNova) {
        this.senhaNova = senhaNova;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        try {
            this.data1 = DataAtual.inverteData2(data1);
        } catch (Exception e) {
            this.data1 = data1;
        }
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        try {
            this.data2 = DataAtual.inverteData2(data2);
        } catch (Exception e) {
            this.data2 = data2;
        }
    }

    public String getCodcli() {
        return codcli;
    }

    public void setCodcli(String codcli) {
        this.codcli = codcli;
    }

    public boolean isData() {
        return data;
    }

    public void setData(boolean data) {
        this.data = data;
    }

    public boolean isDataStr() {
        return dataStr;
    }

    public void setDataStr(boolean dataStr) {
        this.dataStr = dataStr;
    }

    public boolean isDiaSeman() {
        return diaSeman;
    }

    public void setDiaSeman(boolean diaSeman) {
        this.diaSeman = diaSeman;
    }

    public boolean isFeriado() {
        return feriado;
    }

    public void setFeriado(boolean feriado) {
        this.feriado = feriado;
    }

    public boolean isValorRecD0() {
        return valorRecD0;
    }

    public void setValorRecD0(boolean valorRecD0) {
        this.valorRecD0 = valorRecD0;
    }

    public boolean isHoraRecD0() {
        return horaRecD0;
    }

    public void setHoraRecD0(boolean horaRecD0) {
        this.horaRecD0 = horaRecD0;
    }

    public boolean isDepDiaAntAposCorte() {
        return depDiaAntAposCorte;
    }

    public void setDepDiaAntAposCorte(boolean depDiaAntAposCorte) {
        this.depDiaAntAposCorte = depDiaAntAposCorte;
    }

    public boolean isValorCorteD0() {
        return valorCorteD0;
    }

    public void setValorCorteD0(boolean valorCorteD0) {
        this.valorCorteD0 = valorCorteD0;
    }

    public boolean isTotalCredDia() {
        return totalCredDia;
    }

    public void setTotalCredDia(boolean totalCredDia) {
        this.totalCredDia = totalCredDia;
    }

    public boolean isHrRecDia() {
        return hrRecDia;
    }

    public void setHrRecDia(boolean hrRecDia) {
        this.hrRecDia = hrRecDia;
    }

    public boolean isValorRecDia() {
        return valorRecDia;
    }

    public void setValorRecDia(boolean valorRecDia) {
        this.valorRecDia = valorRecDia;
    }

    public boolean isValorRecJaCreditado() {
        return valorRecJaCreditado;
    }

    public void setValorRecJaCreditado(boolean valorRecJaCreditado) {
        this.valorRecJaCreditado = valorRecJaCreditado;
    }

    public boolean isValorRecACreditar() {
        return valorRecACreditar;
    }

    public void setValorRecACreditar(boolean valorRecACreditar) {
        this.valorRecACreditar = valorRecACreditar;
    }

    public boolean isSaldoCofreTotal() {
        return saldoCofreTotal;
    }

    public void setSaldoCofreTotal(boolean saldoCofreTotal) {
        this.saldoCofreTotal = saldoCofreTotal;
    }

    public boolean isDepositoJaCreditado() {
        return depositoJaCreditado;
    }

    public void setDepositoJaCreditado(boolean depositoJaCreditado) {
        this.depositoJaCreditado = depositoJaCreditado;
    }

    public boolean isDepositoProxDU() {
        return depositoProxDU;
    }

    public void setDepositoProxDU(boolean depositoProxDU) {
        this.depositoProxDU = depositoProxDU;
    }

    public boolean isSaldoFisCst() {
        return saldoFisCst;
    }

    public void setSaldoFisCst(boolean saldoFisCst) {
        this.saldoFisCst = saldoFisCst;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
        this.cofre = true;
        this.data = true;
        this.dataStr = true;
        this.diaSeman = true;
        this.feriado = true;
        this.valorRecD0 = true;
        this.horaRecD0 = true;
        this.depDiaAntAposCorte = true;
        this.valorCorteD0 = true;
        this.totalCredDia = true;
        this.hrRecDia = true;
        this.valorRecDia = true;
        this.valorRecJaCreditado = true;
        this.valorRecACreditar = true;
        this.saldoCofreTotal = true;
        this.depositoJaCreditado = true;
        this.depositoProxDU = true;
        this.saldoFisCst = true;
        this.hora = true;
        this.nomeUsuario = true;
        this.valorDeposito = true;
        this.tipoDeposito = true;
        this.status = true;
    }

    public List<TesCofresMov> getDetalhesMovimentacao() {
        return detalhesMovimentacao;
    }

    public void setDetalhesMovimentacao(List<TesCofresMov> detalhesMovimentacao) {
        this.detalhesMovimentacao = detalhesMovimentacao;
    }

    public boolean isCofre() {
        return cofre;
    }

    public void setCofre(boolean cofre) {
        this.cofre = cofre;
    }

    public boolean isHora() {
        return hora;
    }

    public void setHora(boolean hora) {
        this.hora = hora;
    }

    public boolean isNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(boolean nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public boolean isValorDeposito() {
        return valorDeposito;
    }

    public void setValorDeposito(boolean valorDeposito) {
        this.valorDeposito = valorDeposito;
    }

    public boolean isTipoDeposito() {
        return tipoDeposito;
    }

    public void setTipoDeposito(boolean tipoDeposito) {
        this.tipoDeposito = tipoDeposito;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public SasPWFill getNovaFilial() {
        return novaFilial;
    }

    public void setNovaFilial(SasPWFill novaFilial) {
        this.novaFilial = novaFilial;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public List<SasPWFill> getFiliais() {
        return filiais;
    }

    public void setFiliais(List<SasPWFill> filiais) {
        this.filiais = filiais;
    }

    public List<SasPWFill> getTodasFiliais() {
        return todasFiliais;
    }

    public void setTodasFiliais(List<SasPWFill> todasFiliais) {
        this.todasFiliais = todasFiliais;
    }

    public UsuarioSatMobWeb getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(UsuarioSatMobWeb selecionado) {
        this.selecionado = selecionado;
    }

    public UsuarioSatMobWeb getNovo() {
        return novo;
    }

    public void setNovo(UsuarioSatMobWeb novo) {
        this.novo = novo;
    }

    public List<SaspwacSysdef> getPermissoes() {
        return permissoes;
    }

    public void setPermissoes(List<SaspwacSysdef> permissoes) {
        this.permissoes = permissoes;
    }

    public SaspwacSysdef getPermissaoSelecionada() {
        return permissaoSelecionada;
    }

    public void setPermissaoSelecionada(SaspwacSysdef permissaoSelecionada) {
        this.permissaoSelecionada = permissaoSelecionada;
    }

    public SaspwacSysdef getNovaPermissao() {
        return novaPermissao;
    }

    public void setNovaPermissao(SaspwacSysdef novaPermissao) {
        this.novaPermissao = novaPermissao;
    }

    public List<PessoaCliAut> getClientes() {
        return clientes;
    }

    public void setClientes(List<PessoaCliAut> clientes) {
        this.clientes = clientes;
    }

    public Clientes getTodosClientesSelecao() {
        return todosClientesSelecao;
    }

    public void setTodosClientesSelecao(Clientes todosClientesSelecao) {
        this.todosClientesSelecao = todosClientesSelecao;
    }

    public boolean isFlag_exclPessoaCliAut() {
        return flag_exclPessoaCliAut;
    }

    public void setFlag_exclPessoaCliAut(boolean flag_exclPessoaCliAut) {
        this.flag_exclPessoaCliAut = flag_exclPessoaCliAut;
    }

    public boolean isAlteracao() {
        return alteracao;
    }

    public void setAlteracao(boolean alteracao) {
        this.alteracao = alteracao;
    }

    public boolean isInclusao() {
        return inclusao;
    }

    public void setInclusao(boolean inclusao) {
        this.inclusao = inclusao;
    }

    public boolean isExclusao() {
        return exclusao;
    }

    public void setExclusao(boolean exclusao) {
        this.exclusao = exclusao;
    }

    public PessoaCliAut getNovoCliente() {
        return novoCliente;
    }

    public void setNovoCliente(PessoaCliAut novoCliente) {
        this.novoCliente = novoCliente;
    }

    public PessoaCliAut getClienteSelecionado() {
        return clienteSelecionado;
    }

    public void setClienteSelecionado(PessoaCliAut clienteSelecionado) {
        this.clienteSelecionado = clienteSelecionado;
    }

    public String getGrupoSelecionado() {
        return grupoSelecionado;
    }

    public void setGrupoSelecionado(String grupoSelecionado) {
        this.grupoSelecionado = grupoSelecionado;
    }

    public String getSubsistema() {
        return subsistema;
    }

    public void setSubsistema(String subsistema) {
        this.subsistema = subsistema;
    }

    public String getSistema() {
        return sistema;
    }

    public void setSistema(String sistema) {
        this.sistema = sistema;
    }

    public List<SASGrupos> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<SASGrupos> grupos) {
        this.grupos = grupos;
    }

    public SasPWFill getFilialSelecionada() {
        return filialSelecionada;
    }

    public void setFilialSelecionada(SasPWFill filialSelecionada) {
        this.filialSelecionada = filialSelecionada;
    }

    public List<Sysdef> getTodasPermissoes() {
        return todasPermissoes;
    }

    public void setTodasPermissoes(List<Sysdef> todasPermissoes) {
        this.todasPermissoes = todasPermissoes;
    }

    public PessoasMB getPessoas() {
        return pessoas;
    }

    public void setPessoas(PessoasMB pessoas) {
        this.pessoas = pessoas;
    }

    public Boolean getAlteracao() {
        return alteracao;
    }

    public void setAlteracao(Boolean alteracao) {
        this.alteracao = alteracao;
    }

    public Boolean getInclusao() {
        return inclusao;
    }

    public void setInclusao(Boolean inclusao) {
        this.inclusao = inclusao;
    }

    public Boolean getExclusao() {
        return exclusao;
    }

    public void setExclusao(Boolean exclusao) {
        this.exclusao = exclusao;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Boolean getFlag_exclPessoaCliAut() {
        return flag_exclPessoaCliAut;
    }

    public void setFlag_exclPessoaCliAut(Boolean flag_exclPessoaCliAut) {
        this.flag_exclPessoaCliAut = flag_exclPessoaCliAut;
    }

    public List<Clientes> getTodosClientes() {
        return todosClientes;
    }

    public void setTodosClientes(List<Clientes> todosClientes) {
        this.todosClientes = todosClientes;
    }

    public boolean isWALMART() {
        return WALMART;
    }

    public void setWALMART(boolean WALMART) {
        this.WALMART = WALMART;
    }

    public TesCofresMov getMovimentacaoSelecionada() {
        return movimentacaoSelecionada;
    }

    public void setMovimentacaoSelecionada(TesCofresMov movimentacaoSelecionada) {
        this.movimentacaoSelecionada = movimentacaoSelecionada;
    }

    public int getTotalMovimentacoesPaginda() {
        return totalMovimentacoesPaginda;
    }

    public void setTotalMovimentacoesPaginda(int totalMovimentacoesPaginda) {
        this.totalMovimentacoesPaginda = totalMovimentacoesPaginda;
    }

    public Clientes getCliente() {
        return cliente;
    }

    public void setCliente(Clientes cliente) {
        this.cliente = cliente;
    }

    public Date getDate1() {
        return date1;
    }

    public void setDate1(Date date1) {
        this.date1 = date1;
    }

    public Date getDate2() {
        return date2;
    }

    public void setDate2(Date date2) {
        this.date2 = date2;
    }
}

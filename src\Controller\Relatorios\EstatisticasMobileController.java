/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Relatorios;

import Dados.Persistencia;
import SasBeans.StatWeb;
import SasDaos.StatWebDao;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EstatisticasMobileController {

    public List<StatWeb> obterDadosDiario(String data, Persistencia persistencia) {
        try {
            StatWebDao statWebDao = new StatWebDao();
            return statWebDao.dadosDia(data, persistencia);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public List<StatWeb> obterDadosPeriodo(String data1, String data2, Persistencia persistencia) {
        try {
            StatWebDao statWebDao = new StatWebDao();
            return statWebDao.dadosDiarioPeriodo(data1, data2, persistencia);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1200;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1200Dao {

    public List<S1200> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select 1 Tipo, "
                    + " Funcion.Matr,"
                    + " 1 ideEvento_indRetif, "
                    + " Case When FPMensal.TipoFP = '132' then 2 else 1 end ideEvento_indApuracao, "
                    //+ " 1 ideEvento_indApuracao, "
                    + " Case When FPMensal.TipoFP = '132' then '20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2) "
                    + " else Substring(Replace(Convert(varchar,(Convert(Date,'20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPMensal.CodMovFP),3,2)+'-01')),111),'/','-'),1,7) end ideEvento_perApur, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,   "
                    + " Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " Funcion.CPF ideTrabalhador_cpfTrab, "
                    + " Funcion.PIS ideTrabalhador_nisTrab, "
                    + " Funcion.Nome infoComplem_nmTrab, Cargos.CBO, "
                    + " Case when Len(Funcion.CodPonto) > 0 then CONVERT(VARCHAR, Funcion.CodPonto) else CONVERT(VARCHAR, Funcion.Matr) end ideTrabalhador_matrCodPonto,"
                    + " Funcion.Matr ideTrabalhador_matr,"
                    + " Case when Funcion.TrabIntermitente = 'S' then ( "
                    + " Select Count(Distinct CtrOperv.Data) Qtde from CtrOperv  "
                    + " where CtrOperv.FuncSubs = FPMensal.Matr "
                    + "  and CtrOperv.Data between FPPeriodos.DtInicioP and  FPPeriodos.DtFinalP "
                    + "  and CtrOperv.  Flag_Excl <> '*' "
                    + " ) else 0 end infoInterm_qtdDiasInterm, "
                    + " Convert(varchar,FPMensal.TipoFP)+Convert(Varchar,FPMensal.CodMovFP) dmDev_ideDmDev, "
                    + " Case when Funcion.vinculo in ('J','M') then '103' "
                    + "      when Funcion.vinculo = 'D' and Filiais.Descricao not like '%Agil%' then '721' "
                    + "      when Funcion.Vinculo = 'S' and Funcion.TipoADM = 80 then '722' "
                    + "      when Funcion.Vinculo = 'S' and Filiais.Descricao not like '%Agil%' then '723' "
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + "      when Funcion.TipoADM = 25 then 105 "
                    + "      when Funcion.TrabIntermitente = 'S' then '111' "
                    + "      else '101' end dmDev_codCateg, "
                    + " FPMensal.Matr remunPerApur_matricula, "
                    + " 2 remunPerApur_indSimples, "
                    + " Case when Funcion.PgINSS = 'N' then '3' "
                    + "      else '' end infoMV_indMV, "
                    //1 - O declarante aplica a alíquota de desconto do segurado sobre a remuneração por ele informada (o percentual da alíquota será obtido considerando a remuneração total do trabalhador);
                    //2 - O declarante aplica a alíquota de desconto do segurado sobre a diferença entre o limite máximo do salário de contribuição e a remuneração de outra(s) empresa(s) para as quais o trabalhador informou que houve o desconto;
                    //3 - O declarante não realiza desconto do segurado, uma vez que houve desconto sobre o limite máximo de salário de contribuição em outra(s) empresa(s).                    
                    + " (select max(sucesso) from  (  "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  "
                    + "            From XmleSocial z (nolock)  "
                    + "         where z.Identificador = Funcion.CPF  "
                    + "             and z.evento = 'S-1200'  "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = ''))  "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  "
                    + "         From XmleSocial z (nolock)  "
                    + "         where z.Identificador = Funcion.CPF  "
                    + "             and z.evento = 'S-1200'  "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<ocorrencia>%')  "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  "
                    + "         From XmleSocial z (nolock)  "
                    + "         where z.Identificador = Funcion.CPF  "
                    + "             and z.evento = 'S-1200' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' OR z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%'))) a) sucesso  "
                    + " from FPMensal (nolock) "
                    + " Left join Filiais (nolock) on Filiais.CodFil = FPMensal.CodFil "
                    + " Left join Funcion (nolock) on Funcion.Matr   = FPMensal.Matr  "
                    + " Left Join Cargos  on Cargos.Codigo = Funcion.CodCargo "                    
                    + " Left  join FPPeriodos  on FPPeriodos.CodMovFP = FPMensal.CodMovFP ";
            if (compet.contains("-13")) {
                sql += "  JOIN (SELECT MAX(CodMovFP) CodMovFP, FPMensal.Matr "
                        + "       FROM FPMensal"
                        + "       WHERE (Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT(?,5),'-',''),2) + '12'" 
                        + "          or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '09'" 
                        + "          or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '10') " 
                        + "       AND   FPMensal.tipoFP = '132'"
                        + "       GROUP BY FPMensal.Matr) MaiorParcela"
                        + "  ON Funcion.Matr      = MaiorParcela.Matr"
                        + " AND FPMensal.CodMovFP = MaiorParcela.CodMovFP";
            }
            if (compet.contains("-13")) {
                sql = sql + " where (Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT(?,5),'-',''),2) + '12' "
                        + "      or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '09' "
                        + "      or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '10') "
                    //    + "    and FPMensal.CodMovFP = '2112' "
                        + "    and FPMensal.tipoFP = '132' ";
            } else {
                sql = sql + " where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                        + "    and FPMensal.tipoFP in('MEN','EST','AUT') "; 
            }
            sql = sql + "    and FPMensal.CodFil = ?"
                    + "    and FPMensal.Situacao <> 'D' "
                    + "    and FPMensal.Proventos+FPMensal.Descontos > 0 "
                    + "    and Funcion.cpf IS NOT NULL  "
                    + "    and Funcion.cpf <> ''  "
                    + "    and Funcion.cpf NOT LIKE '%X%'  "
                    + "    and Funcion.cpf NOT LIKE '%00000000000%'  "
                    + " Order by Funcion.Matr ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            if (compet.contains("-13")) {
                consulta.setString(compet);
            }
            consulta.setString(compet);
            consulta.setString(codFil);

            /*consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.setString(compet);
            consulta.setString(codFil);*/
            consulta.select();
            List<S1200> retorno = new ArrayList<>();
            S1200 s1200;
            while (consulta.Proximo()) {
                s1200 = new S1200();
                s1200.setIdeEvento_procEmi("1");
                s1200.setSucesso(consulta.getInt("sucesso"));
                s1200.setTipoEnvio(consulta.getString("Tipo"));
                s1200.setIdeEvento_verProc("Satellite eSocial");
                s1200.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1200.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s1200.setIdeEvento_indRetif(consulta.getString("ideEvento_indRetif"));
                s1200.setIdeEvento_indApuracao(consulta.getString("ideEvento_indApuracao"));
                s1200.setIdeEvento_perApur(consulta.getString("ideEvento_perApur"));
                s1200.setIdeTrabalhador_cpfTrab(consulta.getString("ideTrabalhador_cpfTrab"));
                s1200.setIdeTrabalhador_nisTrab(consulta.getString("ideTrabalhador_nisTrab"));
                s1200.setInfoInterm_qtdDiasInterm(consulta.getString("infoInterm_qtdDiasInterm"));
                s1200.setInfoComplem_nmTrab(consulta.getString("infoComplem_nmTrab"));                                
               if ((persistencia.getEmpresa().equals("SATBRASIFORT")) || (persistencia.getEmpresa().equals("SATTRANSPORTER")) || 
                       (consulta.getString("ideTrabalhador_matrCodPonto") != "" )) {
                    s1200.setIdeTrabalhador_matr(consulta.getString("ideTrabalhador_matrCodPonto"));
                } else if (persistencia.getEmpresa().contains("SATAGIL")) {
                    s1200.setIdeTrabalhador_matr(consulta.getString("ideTrabalhador_matrCodPonto"));
                } else {
                    s1200.setIdeTrabalhador_matr(consulta.getString("ideTrabalhador_matr"));
                }
                //s1200.setIdeTrabalhador_matr(consulta.getString("ideTrabalhador_matr"));
                s1200.setInfoMV_indMV(consulta.getString("infoMV_indMV"));
                s1200.setIdeTrabalhador_codCateg(consulta.getString("dmDev_codCateg"));
                if (consulta.getString("dmDev_codCateg").equals("701")){
                   s1200.setCBO(consulta.getString("CBO"));
                }else{
                   s1200.setCBO("");
                }                                
                s1200.setDmDev(new ArrayList<>());
                s1200.setRemunOutrEmpr(new ArrayList<>());
                retorno.add(s1200);
            }
            consulta.Close();

            sql = " Select  "
                    + " Funcion.CPF IdeTrabalhador_cpfTrab, "
                    + " Case when Len(Funcion.CodPonto) > 0 then CONVERT(VARCHAR, Funcion.CodPonto) else convert(varchar,convert(BigInt,Funcion.Matr)) end remunPerApur_matriculaCodPonto, "
                    + " convert(varchar,convert(BigInt,Funcion.Matr)) remunPerApur_matricula, "
                    //                    + " FPMensal.TipoFP, "
                    + " Filiais.TipoPessoa infoPerApur_ideEstabLot_tpInsc, "
                    + " Filiais.CNPJ infoPerApur_ideEstabLot_nrInsc, "
                    + " (Filiais.CodFil*10000)+Filiais.CodFil infoPerApur_ideEstabLot_codLotacao, "
                    + " Convert(varchar,FPMensal.TipoFP)+Convert(Varchar,FPMensal.CodMovFP) dmDev_ideDmDev, Cargos.CBO, "
                    + " Case when Funcion.vinculo in ('J','M') then '103' "
                    + "      when Funcion.vinculo = 'D'  and Filiais.Descricao not like '%Agil%' then '721' "
                    + "      when Funcion.Vinculo = 'S'  and Funcion.TipoADM = 80 then '722' "
                    + "      when Funcion.Vinculo = 'S'  and Filiais.Descricao not like '%Agil%' then '723' "
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + "      when Funcion.TipoADM = 25 then 105 "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + "      when Funcion.TipoADM = 25 then 105 "
                    + "      when Funcion.TrabIntermitente = 'S' then '111' "
                    + "      else '101' end dmDev_codCateg "
                    + " from FPMensal  (nolock) "
                    + " Left join Funcion (nolock) on FPMensal.Matr = Funcion.Matr "
                    + " Left join Cargos (nolock) on Cargos.Codigo = Funcion.CodCargo "                    
                    + " Left Join Filiais (nolock) on Filiais.codFil = FPMensal.codfil ";
            if (compet.contains("-13")) {
                sql += "  JOIN (SELECT MAX(CodMovFP) CodMovFP, FPMensal.Matr "
                        + "       FROM FPMensal"
                        + "       WHERE (Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT(?,5),'-',''),2) + '12' "
                        + "          or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '09' "                        
                        + "          or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '10') "                                                
                        + "       AND   FPMensal.tipoFP = '132'"
                        + "       GROUP BY FPMensal.Matr) MaiorParcela"
                        + "  ON Funcion.Matr      = MaiorParcela.Matr"
                        + " AND FPMensal.CodMovFP = MaiorParcela.CodMovFP";
            }
            if (compet.contains("-13")) {
                sql = sql + " where (Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT(?,5),'-',''),2) + '12' "
                          + "  or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '09' "                        
                          + "  or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '10') "                                                
                      //  + "    and FPMensal.CodMovFP = RIGHT(Convert(Varchar,FPMensal.CodMovFP),2) + '12' "
                        + "    and FPMensal.tipoFP = '132' ";
            } else {
                sql = sql + " Where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                        + "    and FPMensal.TipoFP in ('MEN','EST', 'AUT', 'CPL') ";//Incluido |Validar Carlos 14/01/2021
            }
            sql = sql + "    and FPMensal.CodFil = ? "
                    + "    and Funcion.cpf IS NOT NULL  "
                    + "    and Funcion.cpf <> ''  "
                    + "    and Funcion.cpf NOT LIKE '%X%'  "
                    + "    and Funcion.cpf NOT LIKE '%00000000000%' AND FPMensal.TipoFP <> 'CPL' ORDER BY Funcion.Matr ";

            consulta = new Consulta(sql, persistencia);
            if (compet.contains("-13")) {
                consulta.setString(compet);
            } 
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            S1200.DmDev s1200_dmDev;
            int indice;
            while (consulta.Proximo()) {
                s1200 = new S1200();
                s1200.setIdeTrabalhador_cpfTrab(consulta.getString("IdeTrabalhador_cpfTrab"));
                s1200_dmDev = new S1200.DmDev();
                s1200_dmDev.setDmDev_ideDmDev(consulta.getString("dmDev_ideDmDev"));
                s1200_dmDev.setDmDev_codCateg(consulta.getString("dmDev_codCateg"));
                s1200_dmDev.setIdeTrabalhador_cpfTrab(consulta.getString("IdeTrabalhador_cpfTrab"));
                s1200_dmDev.setInfoPerApur_ideEstabLot_tpInsc(consulta.getString("infoPerApur_ideEstabLot_tpInsc"));
                s1200_dmDev.setInfoPerApur_ideEstabLot_nrInsc(consulta.getString("infoPerApur_ideEstabLot_nrInsc"));
                s1200_dmDev.setInfoPerApur_ideEstabLot_codLotacao(consulta.getString("infoPerApur_ideEstabLot_codLotacao"));
                s1200_dmDev.setRemunPerApur_infoAgNocivo_grauExp("1");
                
                // Autonomo levar CBO para informacoes complementares

                
                if ((persistencia.getEmpresa().equals("SATBRASIFORT")) || (persistencia.getEmpresa().equals("SATTRANSPORTER")) ||
                      (consulta.getString("remunPerApur_matriculaCodPonto") != "" )) {
                    s1200_dmDev.setRemunPerApur_matricula(consulta.getString("remunPerApur_matriculaCodPonto"));
                } else if (persistencia.getEmpresa().contains("SATAGIL")) {
                    s1200_dmDev.setRemunPerApur_matricula(consulta.getString("remunPerApur_matriculaCodPonto"));
                } else {
                    s1200_dmDev.setRemunPerApur_matricula(consulta.getString("remunPerApur_matricula"));
                }

                s1200_dmDev.setRemunPerApur_itensRemun(new ArrayList<>());
                s1200_dmDev.setRemunPerApur_infoSaudeColet(new S1200.InfoSaudeColet());
                indice = retorno.indexOf(s1200);
                if (indice >= 0) {
                    retorno.get(indice).getDmDev().add(s1200_dmDev);
                }
            }
            consulta.Close();

            sql = " Select  \n"
                    + "Funcion.CPF IdeTrabalhador_cpfTrab,  \n"
                    + "Convert(varchar,FPLancamentos.TipoFP)+Convert(Varchar,FPLancamentos.CodMovFP) dmDev_ideDmDev, \n"
                    + "Case when Verbas.Formula = '0002' and FPLancamentos.TipoFP = 'FER' then '9'+Verbas.Verba else Verbas.Verba end itensRemun_codRubr, \n"
                    + "Case when Verbas.Formula = '0002' and FPLancamentos.TipoFP = 'FER' then '9'+Verbas.Verba else Verbas.Verba end itensRemun_ideTabRubr, \n"
                    + "Round(FPLancamentos.ValorCalc,2) itensRemun_vrRubr \n"
                    + "from FPLancamentos (nolock) \n"
                    + "Left join FPMensal (nolock) on FPMensal.CodMovFP = FPLancamentos.CodMovFP\n"
                    + "                   and FPMensal.TipoFP = FPLancamentos.TipoFP\n"
                    + "                   and FPMensal.Matr = FPLancamentos.Matr\n"
                    + "Left join Funcion (nolock) on Funcion.Matr = FPLancamentos.Matr  \n"
                    + "Left join Verbas (nolock) on Verbas.Verba = FPLancamentos.Verba \n";
            if (compet.contains("-13")) {
                sql += "  JOIN (SELECT MAX(CodMovFP) CodMovFP, FPMensal.Matr "
                        + "       FROM FPMensal"
                        + "       WHERE (Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT(?,5),'-',''),2) + '12' "
                        + "        or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '09' "
                        + "        or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '10') "                        
                        + "       AND   FPMensal.tipoFP = '132'"
                        + "       GROUP BY FPMensal.Matr) MaiorParcela"
                        + "  ON Funcion.Matr      = MaiorParcela.Matr"
                        + " AND FPMensal.CodMovFP = MaiorParcela.CodMovFP";
            }
            if (compet.contains("-13")) {
                sql = sql + " where (Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT(?,5),'-',''),2) + '12' "
                        + " or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '09' "
                        + " or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '10') "
                       // + "    and FPMensal.CodMovFP = '2112' "
                        + "    and FPMensal.tipoFP = '132' ";
                //+ "    and Verbas.Formula <> '0002' ";
            } else {
                sql = sql + " Where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                        + "    and FPMensal.TipoFP in ('MEN','EST','AUT', 'CPL') "; //Validar Carlos  14/01/2021
            }
            sql = sql + "   and FPMensal.CodFil = ? and FPMensal.TipoFP <> 'CPL' \n"
                    + "   and FPLancamentos.Flag_Excl <> '*' \n"
                    + "   and FPLancamentos.ValorCalc > 0 \n "
                    //+ "   and Verbas.Formula not in('0221','0222','0223','0225','0226','0003','0013','0023','0033','6051') \n"
                    + "   and Verbas.Formula not in('0221','0222','0223','0225','0226','0013','0023','0033','6051') \n" //Liberado IRRF 19/06/2023
                    + "    and Funcion.cpf IS NOT NULL  "
                    + "    and Funcion.cpf <> ''  "
                    + "    and Funcion.cpf NOT LIKE '%X%'  "
                    + "    and Funcion.cpf NOT LIKE '%00000000000%' ORDER BY Funcion.Matr ";
//                    "   and FPLancamentos.Verba not in (Select z.Verba \n" +
//                    "					from FPLancamentos z (nolock) \n" +
//                    "					left join Verbas x (nolock) on x.Verba = z.Verba \n" +
//                    "					where z.CodMovFP = FPMensal.CodMovFP \n" +
//                    "					  and z.Matr = FPMensal.Matr \n" +
//                    "					  and x.Formula <> '0002' \n" +
//                    "   				  and z.TipoFP <> 'MEN') \n" ;
//                    "union\n" +
//                    "Select  \n" +
//                    "Funcion.CPF IdeTrabalhador_cpfTrab,  \n" +
//                    "Convert(varchar,FPLancamentos.TipoFP)+Convert(Varchar,FPLancamentos.CodMovFP) dmDev_ideDmDev, \n" +
//                    "Case when Verbas.Formula = '0002' and FPLancamentos.TipoFP = 'FER' then '9'+Verbas.Verba else Verbas.Verba end itensRemun_codRubr, \n" +
//                    "Case when Verbas.Formula = '0002' and FPLancamentos.TipoFP = 'FER' then '9'+Verbas.Verba else Verbas.Verba end itensRemun_ideTabRubr, \n" +
//                    "Round(FPLancamentos.ValorCalc,2) itensRemun_vrRubr \n" +
//                    "from FPLancamentos (nolock) \n" +
//                    "Left join FPMensal (nolock) on FPMensal.CodMovFP = FPLancamentos.CodMovFP\n" +
//                    "                   and FPMensal.TipoFP = FPLancamentos.TipoFP\n" +
//                    "                   and FPMensal.Matr = FPLancamentos.Matr\n" +
//                    "Left join Funcion (nolock) on Funcion.Matr = FPLancamentos.Matr  \n" +
//                    "Left join Verbas (nolock) on Verbas.Verba = FPLancamentos.Verba \n" +
//                    "where '20'+Substring(Convert(Varchar,FPLancamentos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPLancamentos.CodMovFP),3,2) = ? \n" +
//                    "   and FPLancamentos.TipoFP in ('FER','RES') \n" +
//                    "   and FPMensal.CodFil = ? \n" +
//                    "   and FPLancamentos.Flag_Excl <> '*' \n" +
//                    "   and Verbas.Formula not in('0221','0222','0223','0225','0226','0003','0013','0023','0033','0297');  ";
            consulta = new Consulta(sql, persistencia);
            if (compet.contains("-13")) {
                consulta.setString(compet);
            }
            consulta.setString(compet);
            consulta.setString(codFil);
//            consulta.setString(compet);
//            consulta.setString(codFil);            
            consulta.select();
            S1200.ItensRemun s1200_itensRemun;
            int indice2;
            DecimalFormat df = new DecimalFormat("#.00");
            while (consulta.Proximo()) {
                s1200 = new S1200();
                s1200.setIdeTrabalhador_cpfTrab(consulta.getString("IdeTrabalhador_cpfTrab"));

                indice = retorno.indexOf(s1200);
                if (indice >= 0) {
                    s1200_dmDev = new S1200.DmDev();
                    s1200_dmDev.setIdeTrabalhador_cpfTrab(consulta.getString("IdeTrabalhador_cpfTrab"));
                    s1200_dmDev.setDmDev_ideDmDev(consulta.getString("dmDev_ideDmDev"));

                    indice2 = retorno.get(indice).getDmDev().indexOf(s1200_dmDev);
                    if (indice2 >= 0) {
                        s1200_itensRemun = new S1200.ItensRemun();
                        s1200_itensRemun.setItensRemun_codRubr(consulta.getString("itensRemun_codRubr"));
                        s1200_itensRemun.setItensRemun_ideTabRubr(consulta.getString("itensRemun_ideTabRubr"));
                        //s1200_itensRemun.setItensRemun_vrRubr(df.format(consulta.getString("itensRemun_vrRubr")));
                        s1200_itensRemun.setItensRemun_vrRubr(consulta.getString("itensRemun_vrRubr"));
                        retorno.get(indice).getDmDev().get(indice2).getRemunPerApur_itensRemun().add(s1200_itensRemun);
                    }
                }
            }
            consulta.Close();

            sql = " Select  "
                    + " Funcion.CPF ideTrabalhador_cpfTrab,  "
                    + " FPLancamentos.TipoFP+Convert(Varchar,FPLancamentos.CodMovFP) dmDev_ideDmDev, "
                    + " Fornec.CNPJ infoSaudeColet_detOper_cnpjOper, "
                    + " Fornec.OBS infoSaudeColet_detOper_regANS, "
                    + " Round(Sum(FPLancamentos.ValorCalc),2) infoSaudeColet_detOper_vrPgTit "
                    + " from FPLancamentos (nolock) "
                    + " Left join FPMensal (nolock) on FPMensal.CodMovFP = FPLancamentos.CodMovFP"
                    + "                    and FPMensal.TipoFP = FPLancamentos.TipoFP"
                    + "                    and FPMensal.Matr = FPLancamentos.Matr"
                    + " Left join Funcion (nolock) on Funcion.Matr = FPLancamentos.Matr  "
                    + " Left join Verbas  (nolock) on Verbas.Verba = FPLancamentos.Verba "
                    + " Left join Fornec  (nolock) on Fornec.Codigo = Verbas.CodForn ";
            if (compet.contains("-13")) {
                sql += "  JOIN (SELECT MAX(CodMovFP) CodMovFP, FPMensal.Matr "
                        + "       FROM FPMensal"
                        + "       WHERE Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                        + "       AND   FPMensal.tipoFP = '132'"
                        + "       GROUP BY FPMensal.Matr) MaiorParcela"
                        + "  ON Funcion.Matr      = MaiorParcela.Matr"
                        + " AND FPMensal.CodMovFP = MaiorParcela.CodMovFP";
            }
            sql += " where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                    + "   and FPMensal.CodFil = ? ";
            if (compet.contains("-13")) {
                sql = sql + " and (Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT(?,5),'-',''),2) + '12'  "
                        + " or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '09'  "
                        + " or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '10')  "
                       // + "    and FPMensal.CodMovFP = '2112' "
                        + "    and FPMensal.tipoFP = '132' ";
            } else {
                sql = sql + " and Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                        + "    and FPMensal.tipoFP in('MEN','EST','AUT', 'CPL') ";//Validar Carlos 14/01/2021
            }
            //+ "    and FPMensal.Matr = 	1001658 "
            sql += " and Verbas.PlanoSaude = 'S' and FPMensal.TipoFP <> 'CPL' "
                    + " and FPLancamentos.Flag_Excl <> '*' "
                    + "    and Funcion.cpf IS NOT NULL  "
                    + "    and Funcion.cpf <> ''  "
                    + "    and Funcion.cpf NOT LIKE '%X%'  "
                    + "    and Funcion.cpf NOT LIKE '%00000000000%'  "
                    + " Group by Funcion.CPF, FPLancamentos.TipoFP,Fornec.CNPJ,Fornec.OBS, FPLancamentos.CodMovFP ";

            consulta = new Consulta(sql, persistencia);
            if (compet.contains("-13")) {
                consulta.setString(compet);
            }
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.select();
            S1200.DetOper s1200_detOper;
            while (consulta.Proximo()) {
                s1200 = new S1200();
                s1200.setIdeTrabalhador_cpfTrab(consulta.getString("IdeTrabalhador_cpfTrab"));

                indice = retorno.indexOf(s1200);
                if (indice >= 0) {
                    s1200_dmDev = new S1200.DmDev();
                    s1200_dmDev.setIdeTrabalhador_cpfTrab(consulta.getString("IdeTrabalhador_cpfTrab"));
                    s1200_dmDev.setDmDev_ideDmDev(consulta.getString("dmDev_ideDmDev"));

                    indice2 = retorno.get(indice).getDmDev().indexOf(s1200_dmDev);
                    if (indice2 >= 0) {
                        s1200_detOper = new S1200.DetOper();
                        s1200_detOper.setDetOper_cnpjOper(consulta.getString("infoSaudeColet_detOper_cnpjOper"));
                        s1200_detOper.setDetOper_regANS(consulta.getString("infoSaudeColet_detOper_regANS"));
                        s1200_detOper.setDetOper_vrPgTit(consulta.getString("infoSaudeColet_detOper_vrPgTit"));
                        retorno.get(indice).getDmDev().get(indice2).getRemunPerApur_infoSaudeColet().getDetOper().add(s1200_detOper);
                    }
                }
            }
            consulta.Close();

            sql = "Select \n"
                    + "Funcion.CPF IdeTrabalhador_cpfTrab,\n"
                    + "Case when Len(Fornec.CNPJ) > 0 then 1 else 2 end remunOutrEmpr_tpInsc, \n"
                    + "Case when Len(Fornec.CNPJ) > 0 then Fornec.CNPJ else Fornec.CPF end remunOutrEmpr_nrInsc, \n"
                    + "'101' remunOutrEmpr_codCateg, \n"
                    + "FuncionVerbas.Valor remunOutrEmpr_vlrRemunOE\n"
                    + "From FPMensal\n"
                    + "Left join Funcion (nolock) on Funcion.Matr = FPMensal.Matr  \n"
                    + "Left join FuncionVerbas  (nolock) on FuncionVerbas.Matr = FPMensal.Matr \n"
                    + "Left join Verbas  on Verbas.Verba = FuncionVerbas.Verba\n"
                    + "Inner join Fornec  (nolock) on Fornec.Codigo = FuncionVerbas.CodForn \n"
                    + "Left join FPPeriodos  on FPPeriodos.CodMovFP = FPMensal.CodMovFP \n";
            if (compet.contains("-13")) {
                sql += "  JOIN (SELECT MAX(CodMovFP) CodMovFP, FPMensal.Matr "
                        + "       FROM FPMensal"
                        + "       WHERE Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                        + "       AND   FPMensal.tipoFP = '132'"
                        + "       GROUP BY FPMensal.Matr) MaiorParcela"
                        + "  ON Funcion.Matr      = MaiorParcela.Matr"
                        + " AND FPMensal.CodMovFP = MaiorParcela.CodMovFP";
            }
            if (compet.contains("-13")) {
                sql = sql + " where (Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT(?,5),'-',''),2) + '12' \n"
                        + " or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '09' \n"
                        + " or Convert(Varchar,FPMensal.CodMovFP) = LEFT(REPLACE(RIGHT('2023-13',5),'-',''),2) + '10') \n"
                       // + "    and FPMensal.CodMovFP = '2112' "
                        + "    and FPMensal.tipoFP = '132' \n";
            } else {
                sql = sql + " where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n"
                          + "  and FPMensal.TipoFP in('MEN', 'CPL')\n";// Validar Carlos 14/01/2021
              //          + "  and FPMensal.TipoFP = 'MEN'\n";
            }
            sql = sql + "  and Verbas.Formula = '6051' and FPMensal.TipoFP <> 'CPL' "
                    + "  and FuncionVerbas.Flag_Excl <> '*' "
                    + "  and FuncionVerbas.DtValidade >= FPPeriodos.DtFinal "
                    + "    and Funcion.cpf IS NOT NULL  "
                    + "    and Funcion.cpf <> ''  "
                    + "    and Funcion.cpf NOT LIKE '%X%'  "
                    + "    and Funcion.cpf NOT LIKE '%00000000000%' ORDER BY Funcion.Matr ";
            consulta = new Consulta(sql, persistencia);
            if (compet.contains("-13")) {
                consulta.setString(compet);
            }
            consulta.setString(compet);
            consulta.select();
            S1200.RemunOutrEmpr s1200_remunOutrEmpr;
            while (consulta.Proximo()) {
                s1200 = new S1200();
                s1200.setIdeTrabalhador_cpfTrab(consulta.getString("IdeTrabalhador_cpfTrab"));

                indice = retorno.indexOf(s1200);
                if (indice >= 0) {
                    s1200_remunOutrEmpr = new S1200.RemunOutrEmpr();
                    s1200_remunOutrEmpr.setIdeTrabalhador_cpfTrab(consulta.getString("IdeTrabalhador_cpfTrab"));
                    s1200_remunOutrEmpr.setCodCateg(consulta.getString("remunOutrEmpr_codCateg"));
                    s1200_remunOutrEmpr.setNrInsc(consulta.getString("remunOutrEmpr_nrInsc"));
                    s1200_remunOutrEmpr.setTpInsc(consulta.getString("remunOutrEmpr_tpInsc"));
                    s1200_remunOutrEmpr.setVlrRemunOE(consulta.getString("remunOutrEmpr_vlrRemunOE"));
                    retorno.get(indice).getRemunOutrEmpr().add(s1200_remunOutrEmpr);
                }
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("S120Dao.get - " + e.getMessage() + "\r\n");
        }
    }
}

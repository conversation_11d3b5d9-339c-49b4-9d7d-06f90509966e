/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.OS_Vig.OS_VigSatMobWeb;
import Dados.Persistencia;
import SasBeans.ContratosReaj;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ContratosReajLazyList extends LazyDataModel<ContratosReaj> {

    private static final long serialVersionUID = 1L;
    private List<ContratosReaj> reajustes = null;
    private final Persistencia persistencia;
    private final OS_VigSatMobWeb osVigController;
    private final String idContrato;

    public ContratosReajLazyList(String idContrato, Persistencia pst) {
        this.idContrato = idContrato;
        this.persistencia = pst;
        osVigController = new OS_VigSatMobWeb();
    }

    @Override
    public List<ContratosReaj> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            reajustes = osVigController.listarReajustesPaginada(first, pageSize, idContrato, filters, persistencia);

            // set the total of players
            setRowCount(osVigController.contagemReajustes(idContrato, filters, persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return reajustes;
    }

    @Override
    public Object getRowKey(ContratosReaj itens) {
        if (null == itens.getDtBase()
                || null == itens.getOrdem()
                || null == itens.getDtBase()
                || null == itens.getOrdem()) {
            return null;
        }
        return itens.getCodFil().replace(".0", "")
                + ";" + itens.getContrato()
                + ";" + itens.getDtBase()
                + ";" + itens.getOrdem();
    }

    @Override
    public ContratosReaj getRowData(String rowKey) {
        String[] keyFields = rowKey.split(";");
        if (keyFields.length != 4) {
            return null;
        }
        for (ContratosReaj reajuste : reajustes) {
            if (keyFields[0].equals(reajuste.getCodFil().replace(".0", ""))
                    && keyFields[1].equals(reajuste.getContrato())
                    && keyFields[2].equals(reajuste.getDtBase())
                    && keyFields[3].equals(reajuste.getOrdem())) {
                return reajuste;
            }
        }
        return null;
    }
}

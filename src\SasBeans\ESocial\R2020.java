/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class R2020 {

    private String evtServPrest_Id;
    private int sucesso;
    private String nred;

    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;

    private String ideEvento_perApur;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideContri_tpInsc;
    private String ideContri_nrInsc;
    private String ideEstabPrest_tpInscEstabPrest;
    private String ideEstabPrest_nrInscEstabPrest;
    private String ideTomador_tpInscTomador;
    private String ideTomador_nrInscTomador;
    private String ideTomador_indObra;
    private String ideTomador_vlrTotalBruto;
    private String ideTomador_vlrTotalBaseRet;
    private String ideTomador_vlrTotalRetPrinc;
    private String ideTomador_vlrTotalRetAdic;
    private String ideTomador_vlrTotalNRetPrinc;
    private String ideTomador_vlrTotalNRetAdic;
    private List<Nfs> ideTomador_nfs;
    private List<InfoProcRetPr> ideTomador_infoProcRetPr;
    private List<InfoProcRetAd> ideTomador_infoProcRetAd;

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 67 * hash + Objects.hashCode(this.ideTomador_nrInscTomador);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final R2020 other = (R2020) obj;
        return Objects.equals(this.ideTomador_nrInscTomador, other.ideTomador_nrInscTomador);
    }

    public static class Nfs {

        private String nfs_serie;
        private String nfs_numDocto;
        private String nfs_dtEmissaoNF;
        private String nfs_vlrBruto;
        private String nfs_obs;
        private List<InfoTpServ> nfs_infoTpserv;

        public String getNfs_serie() {
            return null == nfs_serie ? "" : nfs_serie;
        }

        public void setNfs_serie(String nfs_serie) {
            this.nfs_serie = nfs_serie;
        }

        public String getNfs_numDocto() {
            return null == nfs_numDocto ? "" : nfs_numDocto;
        }

        public void setNfs_numDocto(String nfs_numDocto) {
            this.nfs_numDocto = nfs_numDocto;
        }

        public String getNfs_dtEmissaoNF() {
            return null == nfs_dtEmissaoNF ? "" : nfs_dtEmissaoNF;
        }

        public void setNfs_dtEmissaoNF(String nfs_dtEmissaoNF) {
            this.nfs_dtEmissaoNF = nfs_dtEmissaoNF;
        }

        public String getNfs_vlrBruto() {
            return null == nfs_vlrBruto ? "" : nfs_vlrBruto;
        }

        public void setNfs_vlrBruto(String nfs_vlrBruto) {
            this.nfs_vlrBruto = nfs_vlrBruto;
        }

        public String getNfs_obs() {
            return null == nfs_obs ? "" : nfs_obs;
        }

        public void setNfs_obs(String nfs_obs) {
            this.nfs_obs = nfs_obs;
        }

        public List<InfoTpServ> getNfs_infoTpserv() {
            return nfs_infoTpserv;
        }

        public void setNfs_infoTpserv(List<InfoTpServ> nfs_infoTpserv) {
            this.nfs_infoTpserv = nfs_infoTpserv;
        }
    }

    public static class InfoTpServ {

        private String infoTpServ_tpServico;
        private String infoTpServ_vlrBaseRet;
        private String infoTpServ_vlrRetencao;
        private String infoTpServ_vlrRetSub;
        private String infoTpServ_vlrNRetPrinc;
        private String infoTpServ_vlrServicos15;
        private String infoTpServ_vlrServicos20;
        private String infoTpServ_vlrServicos25;
        private String infoTpServ_vlrAdicional;
        private String infoTpServ_vlrNRetAdic;

        public String getInfoTpServ_tpServico() {
            return null == infoTpServ_tpServico ? "" : infoTpServ_tpServico;
        }

        public void setInfoTpServ_tpServico(String infoTpServ_tpServico) {
            this.infoTpServ_tpServico = infoTpServ_tpServico;
        }

        public String getInfoTpServ_vlrBaseRet() {
            return null == infoTpServ_vlrBaseRet ? "" : infoTpServ_vlrBaseRet;
        }

        public void setInfoTpServ_vlrBaseRet(String infoTpServ_vlrBaseRet) {
            this.infoTpServ_vlrBaseRet = infoTpServ_vlrBaseRet;
        }

        public String getInfoTpServ_vlrRetencao() {
            return null == infoTpServ_vlrRetencao ? "" : infoTpServ_vlrRetencao;
        }

        public void setInfoTpServ_vlrRetencao(String infoTpServ_vlrRetencao) {
            this.infoTpServ_vlrRetencao = infoTpServ_vlrRetencao;
        }

        public String getInfoTpServ_vlrRetSub() {
            return null == infoTpServ_vlrRetSub ? "" : infoTpServ_vlrRetSub;
        }

        public void setInfoTpServ_vlrRetSub(String infoTpServ_vlrRetSub) {
            this.infoTpServ_vlrRetSub = infoTpServ_vlrRetSub;
        }

        public String getInfoTpServ_vlrNRetPrinc() {
            return null == infoTpServ_vlrNRetPrinc ? "" : infoTpServ_vlrNRetPrinc;
        }

        public void setInfoTpServ_vlrNRetPrinc(String infoTpServ_vlrNRetPrinc) {
            this.infoTpServ_vlrNRetPrinc = infoTpServ_vlrNRetPrinc;
        }

        public String getInfoTpServ_vlrServicos15() {
            return null == infoTpServ_vlrServicos15 ? "" : infoTpServ_vlrServicos15;
        }

        public void setInfoTpServ_vlrServicos15(String infoTpServ_vlrServicos15) {
            this.infoTpServ_vlrServicos15 = infoTpServ_vlrServicos15;
        }

        public String getInfoTpServ_vlrServicos20() {
            return null == infoTpServ_vlrServicos20 ? "" : infoTpServ_vlrServicos20;
        }

        public void setInfoTpServ_vlrServicos20(String infoTpServ_vlrServicos20) {
            this.infoTpServ_vlrServicos20 = infoTpServ_vlrServicos20;
        }

        public String getInfoTpServ_vlrServicos25() {
            return null == infoTpServ_vlrServicos25 ? "" : infoTpServ_vlrServicos25;
        }

        public void setInfoTpServ_vlrServicos25(String infoTpServ_vlrServicos25) {
            this.infoTpServ_vlrServicos25 = infoTpServ_vlrServicos25;
        }

        public String getInfoTpServ_vlrAdicional() {
            return null == infoTpServ_vlrAdicional ? "" : infoTpServ_vlrAdicional;
        }

        public void setInfoTpServ_vlrAdicional(String infoTpServ_vlrAdicional) {
            this.infoTpServ_vlrAdicional = infoTpServ_vlrAdicional;
        }

        public String getInfoTpServ_vlrNRetAdic() {
            return null == infoTpServ_vlrNRetAdic ? "" : infoTpServ_vlrNRetAdic;
        }

        public void setInfoTpServ_vlrNRetAdic(String infoTpServ_vlrNRetAdic) {
            this.infoTpServ_vlrNRetAdic = infoTpServ_vlrNRetAdic;
        }
    }

    public static class InfoProcRetPr {

        private String infoProcRetPr_tpProcRetPrinc;
        private String infoProcRetPr_nrProcRetPrinc;
        private String infoProcRetPr_codSuspPrinc;
        private String infoProcRetPr_valorPrinc;

        public String getInfoProcRetPr_tpProcRetPrinc() {
            return null == infoProcRetPr_tpProcRetPrinc ? "" : infoProcRetPr_tpProcRetPrinc;
        }

        public void setInfoProcRetPr_tpProcRetPrinc(String infoProcRetPr_tpProcRetPrinc) {
            this.infoProcRetPr_tpProcRetPrinc = infoProcRetPr_tpProcRetPrinc;
        }

        public String getInfoProcRetPr_nrProcRetPrinc() {
            return null == infoProcRetPr_nrProcRetPrinc ? "" : infoProcRetPr_nrProcRetPrinc;
        }

        public void setInfoProcRetPr_nrProcRetPrinc(String infoProcRetPr_nrProcRetPrinc) {
            this.infoProcRetPr_nrProcRetPrinc = infoProcRetPr_nrProcRetPrinc;
        }

        public String getInfoProcRetPr_codSuspPrinc() {
            return null == infoProcRetPr_codSuspPrinc ? "" : infoProcRetPr_codSuspPrinc;
        }

        public void setInfoProcRetPr_codSuspPrinc(String infoProcRetPr_codSuspPrinc) {
            this.infoProcRetPr_codSuspPrinc = infoProcRetPr_codSuspPrinc;
        }

        public String getInfoProcRetPr_valorPrinc() {
            return null == infoProcRetPr_valorPrinc ? "" : infoProcRetPr_valorPrinc;
        }

        public void setInfoProcRetPr_valorPrinc(String infoProcRetPr_valorPrinc) {
            this.infoProcRetPr_valorPrinc = infoProcRetPr_valorPrinc;
        }
    }

    public static class InfoProcRetAd {

        private String infoProcRetAd_tpProcRetAdic;
        private String infoProcRetAd_nrProcRetAdic;
        private String infoProcRetAd_codSuspAdic;
        private String infoProcRetAd_valorAdic;

        public String getInfoProcRetAd_tpProcRetAdic() {
            return null == infoProcRetAd_tpProcRetAdic ? "" : infoProcRetAd_tpProcRetAdic;
        }

        public void setInfoProcRetAd_tpProcRetAdic(String infoProcRetAd_tpProcRetAdic) {
            this.infoProcRetAd_tpProcRetAdic = infoProcRetAd_tpProcRetAdic;
        }

        public String getInfoProcRetAd_nrProcRetAdic() {
            return null == infoProcRetAd_nrProcRetAdic ? "" : infoProcRetAd_nrProcRetAdic;
        }

        public void setInfoProcRetAd_nrProcRetAdic(String infoProcRetAd_nrProcRetAdic) {
            this.infoProcRetAd_nrProcRetAdic = infoProcRetAd_nrProcRetAdic;
        }

        public String getInfoProcRetAd_codSuspAdic() {
            return null == infoProcRetAd_codSuspAdic ? "" : infoProcRetAd_codSuspAdic;
        }

        public void setInfoProcRetAd_codSuspAdic(String infoProcRetAd_codSuspAdic) {
            this.infoProcRetAd_codSuspAdic = infoProcRetAd_codSuspAdic;
        }

        public String getInfoProcRetAd_valorAdic() {
            return null == infoProcRetAd_valorAdic ? "" : infoProcRetAd_valorAdic;
        }

        public void setInfoProcRetAd_valorAdic(String infoProcRetAd_valorAdic) {
            this.infoProcRetAd_valorAdic = infoProcRetAd_valorAdic;
        }
    }

    public String getEvtServPrest_Id() {
        return evtServPrest_Id;
    }

    public void setEvtServPrest_Id(String evtServPrest_Id) {
        this.evtServPrest_Id = evtServPrest_Id;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getNred() {
        return nred;
    }

    public void setNred(String nred) {
        this.nred = nred;
    }

    public String getIdeEvento_indRetif() {
        return null == ideEvento_indRetif ? "" : ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_perApur() {
        return null == ideEvento_perApur ? "" : ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeContri_tpInsc() {
        return null == ideContri_tpInsc ? "" : ideContri_tpInsc;
    }

    public void setIdeContri_tpInsc(String ideContri_tpInsc) {
        this.ideContri_tpInsc = ideContri_tpInsc;
    }

    public String getIdeContri_nrInsc() {
        return null == ideContri_nrInsc ? "" : ideContri_nrInsc;
    }

    public void setIdeContri_nrInsc(String ideContri_nrInsc) {
        this.ideContri_nrInsc = ideContri_nrInsc;
    }

    public String getIdeEstabPrest_tpInscEstabPrest() {
        return null == ideEstabPrest_tpInscEstabPrest ? "" : ideEstabPrest_tpInscEstabPrest;
    }

    public void setIdeEstabPrest_tpInscEstabPrest(String ideEstabPrest_tpInscEstabPrest) {
        this.ideEstabPrest_tpInscEstabPrest = ideEstabPrest_tpInscEstabPrest;
    }

    public String getIdeEstabPrest_nrInscEstabPrest() {
        return null == ideEstabPrest_nrInscEstabPrest ? "" : ideEstabPrest_nrInscEstabPrest;
    }

    public void setIdeEstabPrest_nrInscEstabPrest(String ideEstabPrest_nrInscEstabPrest) {
        this.ideEstabPrest_nrInscEstabPrest = ideEstabPrest_nrInscEstabPrest;
    }

    public String getIdeTomador_tpInscTomador() {
        return null == ideTomador_tpInscTomador ? "" : ideTomador_tpInscTomador;
    }

    public void setIdeTomador_tpInscTomador(String ideTomador_tpInscTomador) {
        this.ideTomador_tpInscTomador = ideTomador_tpInscTomador;
    }

    public String getIdeTomador_nrInscTomador() {
        return null == ideTomador_nrInscTomador ? "" : ideTomador_nrInscTomador;
    }

    public void setIdeTomador_nrInscTomador(String ideTomador_nrInscTomador) {
        this.ideTomador_nrInscTomador = ideTomador_nrInscTomador;
    }

    public String getIdeTomador_indObra() {
        return null == ideTomador_indObra ? "" : ideTomador_indObra;
    }

    public void setIdeTomador_indObra(String ideTomador_indObra) {
        this.ideTomador_indObra = ideTomador_indObra;
    }

    public String getIdeTomador_vlrTotalBruto() {
        return null == ideTomador_vlrTotalBruto ? "" : ideTomador_vlrTotalBruto;
    }

    public void setIdeTomador_vlrTotalBruto(String ideTomador_vlrTotalBruto) {
        this.ideTomador_vlrTotalBruto = ideTomador_vlrTotalBruto;
    }

    public String getIdeTomador_vlrTotalBaseRet() {
        return null == ideTomador_vlrTotalBaseRet ? "" : ideTomador_vlrTotalBaseRet;
    }

    public void setIdeTomador_vlrTotalBaseRet(String ideTomador_vlrTotalBaseRet) {
        this.ideTomador_vlrTotalBaseRet = ideTomador_vlrTotalBaseRet;
    }

    public String getIdeTomador_vlrTotalRetPrinc() {
        return null == ideTomador_vlrTotalRetPrinc ? "" : ideTomador_vlrTotalRetPrinc;
    }

    public void setIdeTomador_vlrTotalRetPrinc(String ideTomador_vlrTotalRetPrinc) {
        this.ideTomador_vlrTotalRetPrinc = ideTomador_vlrTotalRetPrinc;
    }

    public String getIdeTomador_vlrTotalRetAdic() {
        return null == ideTomador_vlrTotalRetAdic ? "" : ideTomador_vlrTotalRetAdic;
    }

    public void setIdeTomador_vlrTotalRetAdic(String ideTomador_vlrTotalRetAdic) {
        this.ideTomador_vlrTotalRetAdic = ideTomador_vlrTotalRetAdic;
    }

    public String getIdeTomador_vlrTotalNRetPrinc() {
        return null == ideTomador_vlrTotalNRetPrinc ? "" : ideTomador_vlrTotalNRetPrinc;
    }

    public void setIdeTomador_vlrTotalNRetPrinc(String ideTomador_vlrTotalNRetPrinc) {
        this.ideTomador_vlrTotalNRetPrinc = ideTomador_vlrTotalNRetPrinc;
    }

    public String getIdeTomador_vlrTotalNRetAdic() {
        return null == ideTomador_vlrTotalNRetAdic ? "" : ideTomador_vlrTotalNRetAdic;
    }

    public void setIdeTomador_vlrTotalNRetAdic(String ideTomador_vlrTotalNRetAdic) {
        this.ideTomador_vlrTotalNRetAdic = ideTomador_vlrTotalNRetAdic;
    }

    public List<Nfs> getIdeTomador_nfs() {
        return ideTomador_nfs;
    }

    public void setIdeTomador_nfs(List<Nfs> ideTomador_nfs) {
        this.ideTomador_nfs = ideTomador_nfs;
    }

    public List<InfoProcRetPr> getIdeTomador_infoProcRetPr() {
        return ideTomador_infoProcRetPr;
    }

    public void setIdeTomador_infoProcRetPr(List<InfoProcRetPr> ideTomador_infoProcRetPr) {
        this.ideTomador_infoProcRetPr = ideTomador_infoProcRetPr;
    }

    public List<InfoProcRetAd> getIdeTomador_infoProcRetAd() {
        return ideTomador_infoProcRetAd;
    }

    public void setIdeTomador_infoProcRetAd(List<InfoProcRetAd> ideTomador_infoProcRetAd) {
        this.ideTomador_infoProcRetAd = ideTomador_infoProcRetAd;
    }
}

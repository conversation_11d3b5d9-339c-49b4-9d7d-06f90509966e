package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class PessoaHstProf {

    private BigDecimal CodPessoa;
    private Integer Ordem = 0;
    private String Empresa;
    private String Endereco;
    private String Fone;
    private String Dt_Admis;
    private String Dt_Demis;
    private String Cargo;
    private BigDecimal Ult_Salario;
    private String Supervisor;
    private String MotivoSaida;

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("1");
        }
    }

    public Integer getOrdem() {
        return Ordem;
    }

    public void setOrdem(Integer Ordem) {
        this.Ordem = Ordem;
    }

    public String getEmpresa() {
        return Empresa;
    }

    public void setEmpresa(String Empresa) {
        this.Empresa = Empresa;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getFone() {
        return Fone;
    }

    public void setFone(String Fone) {
        this.Fone = Fone;
    }

    public String getDt_Admis() {
        return Dt_Admis;
    }

    public void setDt_Admis(String Dt_Admis) {
        this.Dt_Admis = Dt_Admis;
    }

    public String getDt_Demis() {
        return Dt_Demis;
    }

    public void setDt_Demis(String Dt_Demis) {
        this.Dt_Demis = Dt_Demis;
    }

    public String getCargo() {
        return Cargo;
    }

    public void setCargo(String Cargo) {
        this.Cargo = Cargo;
    }

    public BigDecimal getUlt_Salario() {
        return Ult_Salario;
    }

    public void setUlt_Salario(BigDecimal Ult_Salario) {
        this.Ult_Salario = Ult_Salario;
    }

    public String getSupervisor() {
        return Supervisor;
    }

    public void setSupervisor(String Supervisor) {
        this.Supervisor = Supervisor;
    }

    public String getMotivoSaida() {
        return MotivoSaida;
    }

    public void setMotivoSaida(String MotivoSaida) {
        this.MotivoSaida = MotivoSaida;
    }
}

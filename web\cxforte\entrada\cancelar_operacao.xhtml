<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <p:dialog header="#{localemsgs.ParadaNaoLocalizada}"
              widgetVar="dlgCancelarOperacao"
              minHeight="40"
              width="400px"
              closable="false"
              resizable="false"
              modal="true"
              >
        <h:form id="cancelarOperacao">
            <f:facet name="header">
                <h:outputText value="#{localemsgs.Confirmacao}" style="color:black" />
            </f:facet>

            <p:panel
                id="panel"
                class="modalSolicitacao"
                >
                <div class="row">
                    <div class="col-xs-6">
                        <strong>#{localemsgs.TrajetoPrevistoGTV}</strong>
                    </div>
                    <div class="col-xs-6">
                        <span>#{cxForteEntrada.guiasCxForteAtual.NRed} / #{cxForteEntrada.guiasCxForteAtual.NRedDst}</span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-6">
                        <strong>#{localemsgs.ClienteSelecionado}</strong>
                    </div>
                    <div class="col-xs-6">
                        <span>#{cxForteEntrada.percursoAtual.NRed}</span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-12">
                        <span>#{localemsgs.ClienteGuiaRedefinido}</span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-12">
                        <strong>#{localemsgs.CancelarOperacao}</strong>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-12">
                        <p:commandButton
                            action="#{cxForteEntrada.cancelarOperacao()}"
                            update="msgs @form"
                            styleClass="botao"
                            value="#{localemsgs.Sim}"
                            />
                        <p:commandButton
                            action="#{cxForteEntrada.prosseguirOperacao()}"
                            update="msgs @form"
                            styleClass="botao"
                            value="#{localemsgs.Nao}"
                            />
                    </div>
                </div>
            </p:panel>
        </h:form>
    </p:dialog>

</ui:composition>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.SatWebService;

/**
 *
 * <AUTHOR>
 */
public class EncuestasDeSalida {

    private String supervisor;
    private String punto_servico;
    private String cliente;
    private String codigo_pds;
    private String fecha_hora_checkin;
    private String fecha_hora_checkout;
    private String num_empleado;
    private String presentacion_elemento;
    private String consignas;
    private String actitud;
    private String material;
    private String area_trabajo;
    private String observaciones;

    public String getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(String supervisor) {
        this.supervisor = supervisor;
    }

    public String getPunto_servico() {
        return punto_servico;
    }

    public void setPunto_servico(String punto_servico) {
        this.punto_servico = punto_servico;
    }

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public String getCodigo_pds() {
        return codigo_pds;
    }

    public void setCodigo_pds(String codigo_pds) {
        this.codigo_pds = codigo_pds;
    }

    public String getFecha_hora_checkin() {
        return fecha_hora_checkin;
    }

    public void setFecha_hora_checkin(String fecha_hora_checkin) {
        this.fecha_hora_checkin = fecha_hora_checkin;
    }

    public String getFecha_hora_checkout() {
        return fecha_hora_checkout;
    }

    public void setFecha_hora_checkout(String fecha_hora_checkout) {
        this.fecha_hora_checkout = fecha_hora_checkout;
    }

    public String getNum_empleado() {
        return num_empleado;
    }

    public void setNum_empleado(String num_empleado) {
        this.num_empleado = num_empleado;
    }

    public String getPresentacion_elemento() {
        return presentacion_elemento;
    }

    public void setPresentacion_elemento(String presentacion_elemento) {
        this.presentacion_elemento = presentacion_elemento;
    }

    public String getConsignas() {
        return consignas;
    }

    public void setConsignas(String consignas) {
        this.consignas = consignas;
    }

    public String getActitud() {
        return actitud;
    }

    public void setActitud(String actitud) {
        this.actitud = actitud;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getArea_trabajo() {
        return area_trabajo;
    }

    public void setArea_trabajo(String area_trabajo) {
        this.area_trabajo = area_trabajo;
    }

    public String getObservaciones() {
        return observaciones;
    }

    public void setObservaciones(String observaciones) {
        this.observaciones = observaciones;
    }
}

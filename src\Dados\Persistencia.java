package Dados;

import java.sql.PreparedStatement;

/**
 *
 * <AUTHOR>
 */
public class Persistencia {

    private String empresa;
    private String vRunTraceCarregaRotaVol; 
    private final Conecta conecta;

    public Persistencia() {
        this.conecta = new Conecta();
    }

    /**
     * Construtor da classe
     *
     * @param jndi
     * @throws Exception - excessoes da criacao da classe
     */
    public Persistencia(String jndi) throws Exception {
        this.conecta = new Conecta();
        this.conecta.Abreconexao(jndi);
    }

    /**
     * Construtor da classe
     *
     * @param url
     * @param usuario
     * @param senha
     * @param enderecoUrl
     * @throws Exception - excessoes da criacao da classe
     */
    public Persistencia(String url, String usuario, String senha, String enderecoUrl) throws Exception {
        this.conecta = new Conecta();
        try {
            try {
                 if (enderecoUrl.toUpperCase().contains("LOCALHOST") || url.toUpperCase().contains("C_ONFE")) {
                    url = url.replace("*************", "bd2.sasw.com.br");                
                }else if (enderecoUrl.toUpperCase().contains("LOCALHOST") || enderecoUrl.toUpperCase().contains("SPM.CASHCTR")) {
                    url = url.replace("*************", "bd.sasw.com.br");
                } else if (!enderecoUrl.toUpperCase().contains("SPM.CASHCTR")
                        && !enderecoUrl.toUpperCase().contains("************")) {
                    url = url.replace("bd.sasw.com.br", "*************");
                } else {
                    url = url.replace("*************", "bd.sasw.com.br");
                }
            } catch (Exception e) {
                //Caso não receba o parametro enderecoUrl substitui para rodar no servidor de produção.
                url = url.replace("bd.sasw.com.br", "*************");
            }

            this.conecta.Abreconexao(url, usuario, senha);
        } catch (Exception ex) {
            if (url.contains("*************")) {
                this.conecta.Abreconexao(url.replace("*************", "bd.sasw.com.br"), usuario, senha);
            } else if (url.contains("bd.sasw.com.br")) {
                this.conecta.Abreconexao(url.replace("bd.sasw.com.br", "*************"), usuario, senha);
            }
        }
    }

    /**
     * Use para fechar a conexao com o banco
     *
     * @throws Exception - gera mensagem de erro caso ocorra
     */
    public void FechaConexao() throws Exception {
        conecta.Fechaconexao();
    }

    /**
     * Devolve um PreparedStatement da conexao atual
     *
     * @param sql - string sql que sera usada
     * @return - Prepared Statement da conexao atual
     * @throws Exception - gera a mensagem de erro caso ocorra
     */
    public PreparedStatement getState(String sql) throws Exception {
        return conecta.getState(sql);
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    @Override
    public String toString() {
        return "Persistencia(" + empresa + ")";
    }
}

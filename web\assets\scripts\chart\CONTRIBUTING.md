Contributing
============

New contributions to the library are welcome, just a couple of guidelines:

 * Tabs for indentation, not spaces please.
 * Please ensure you're changing the individual files in /src, not the concatenated output in the Chart.js file in the root of the repo.
 * Please check that your code will pass jshint code standards, gulp jshint will run this for you.
 * Please keep pull requests concise, and document new functionality in the relevant .md file.
 * Consider whether your changes are useful for all users, or if creating a Chart.js extension would be more appropriate.
 * Please avoid committing in the build Chart.js & Chart.min.js file, as it causes conflicts when merging.

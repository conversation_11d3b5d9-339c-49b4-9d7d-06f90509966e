/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

/**
 *
 * <AUTHOR>
 */
public class BBPedidoMalote {

    private String PedidoCliente;
    private String Lacre;
    private String Horario;
    private String Valor;
    private String Obs;

    public String getPedidoCliente() {
        return PedidoCliente;
    }

    public void setPedidoCliente(String PedidoCliente) {
        this.PedidoCliente = PedidoCliente;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getHorario() {
        return Horario;
    }

    public void setHorario(String Horario) {
        this.Horario = Horario;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }
}

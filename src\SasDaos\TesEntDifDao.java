/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesEntDif;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;

/**
 *
 * <AUTHOR>
 */
public class TesEntDifDao {

    /**
     * Retorna o próximo número de sequência disponível
     *
     * @param tesentdif - campos obrigatórios: guia e série
     * @param persistencia
     * @return - o número de sequencia ou 0 em caso de erro
     * @throws Exception
     */
    public int getMaxSequencia(TesEntDif tesentdif, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select"
                    + " vSeq = Isnull(Max(Sequencia),0)+1 "
                    + " from TesEntDif "
                    + " where Guia = ? "
                    + "  and Serie = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(tesentdif.getGuia());
            consult.setString(tesentdif.getSerie());
            consult.select();
            while (consult.Proximo()) {
                return consult.getInt("vSeq");
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar sequencia max tesentdif - " + e.getMessage());
        }
        return 0;
    }

    /**
     * Insere uma nova entrada na tabela TesEntDif
     *
     * @param tesentdif - objeto da tabela TesEntDif
     * @param persistencia
     * @throws Exception
     */
    public void InsereEntrada(TesEntDif tesentdif, Persistencia persistencia) throws Exception {
        try {
            String sql = "Insert into TesEntDif (Guia, Serie, Sequencia) Values (?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(tesentdif.getGuia());
            consulta.setString(tesentdif.getSerie());
            consulta.setInt(tesentdif.getSequencia());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir em TesEntDif - " + e.getMessage());
        }
        try {
            String sql2 = "update TesEntDif set Docto = ?, Sangria = ?, "
                    + " Tipo = ?, CodMotivo = ?, Valor = ?, Agencia = ?, Conta = ?, Camera = ?, Hora = ?, HoraFim = ?, Tempo = ?, "
                    + " Obs = ?, Historico = ?, DtMovto = ?, MatrConf = ?, MatrCoord = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ? "
                    + " where Guia = ? and Serie = ? and Sequencia = ?";
            Consulta consulta = new Consulta(sql2, persistencia);
            consulta.setString(tesentdif.getDocto());
            consulta.setString(tesentdif.getSangria());
            consulta.setString(tesentdif.getTipo());
            consulta.setInt(tesentdif.getCodMotivo());
            consulta.setBigDecimal(tesentdif.getValor());
            consulta.setString(tesentdif.getAgencia());
            consulta.setString(tesentdif.getConta());
            consulta.setInt(tesentdif.getCamera());
            consulta.setString(tesentdif.getHora());
            consulta.setString(tesentdif.getHoraFim());
            consulta.setBigDecimal(tesentdif.getTempo());
            consulta.setString(tesentdif.getObs());
            consulta.setString(tesentdif.getHistorico());
            consulta.setString(tesentdif.getDtMovto());
            consulta.setBigDecimal(tesentdif.getMatrConf());
            consulta.setBigDecimal(tesentdif.getMatrCoord());
            consulta.setString(tesentdif.getOperador());
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setBigDecimal(tesentdif.getGuia());
            consulta.setString(tesentdif.getSerie());
            consulta.setInt(tesentdif.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar TesEntDif - " + e.getMessage());
        }
    }
}

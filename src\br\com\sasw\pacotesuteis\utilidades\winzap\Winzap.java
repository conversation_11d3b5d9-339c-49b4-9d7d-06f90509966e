/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades.winzap;

import Arquivo.ArquivoLogs;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasdaos.EventoMsgWhatsAppDao;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import javax.net.ssl.HttpsURLConnection;
import org.apache.http.client.utils.URIBuilder;

/**
 * Necessário importar os arquivos contidos na pasta Bibliotecas//httpclient//
 * commons-codec-1.11.jar commons-logging-1.2.jar httpclient-4.5.12.jar
 * httpcore-4.4.13.jar
 *
 * <AUTHOR>
 */
public class Winzap {
    public static void enviarMensagemArquivo(String mensagem, String linkArquivo, String numeroEnvio, String codFil, String operador, String local,
            String latitude, String longitude, String param, Persistencia satellite, ArquivoLogs logerro) throws Exception {

        if (!numeroEnvio.startsWith("+55")) {
            numeroEnvio = "+55" + numeroEnvio;
        }
        
        Winzap.enviarMensagem(mensagem, numeroEnvio, codFil, operador, local, latitude, longitude, param, satellite, logerro);
    }

    public static void enviarMensagemLink(String mensagem,
            String link, String thumb, String title, String desc,
            String numeroEnvio, String codFil, String operador, String local,
            String latitude, String longitude, String param, Persistencia satellite, ArquivoLogs logerro) throws Exception {

        if (!numeroEnvio.startsWith("+55")) {
            numeroEnvio = "+55" + numeroEnvio;
        }

        Winzap.enviarLink(link, thumb, title, desc, numeroEnvio, codFil, operador, param, satellite, logerro);
    }

    public static void enviarLink(String link, String thumb, String title, String desc,
            String numeroEnvio, String codFil, String operador, String param, Persistencia satellite, ArquivoLogs logerro) throws Exception {

        if (!numeroEnvio.startsWith("+55")) {
            numeroEnvio = "+55" + numeroEnvio;
        }

        EventoMsgWhatsAppDao eventoMsgWhatsAppDao = new EventoMsgWhatsAppDao();
        String id = "";

        if (null != thumb && !thumb.equals("")) {
            id = eventoMsgWhatsAppDao.inserirMensagem("media", link, thumb, numeroEnvio.replace("+", ""), "@c.us",
                    param, codFil, RecortaAteEspaço(operador, 0, 10), satellite);
        } else {
            id = eventoMsgWhatsAppDao.inserirMensagem("link", link, "", numeroEnvio.replace("+", ""), "@c.us",
                    param, codFil, RecortaAteEspaço(operador, 0, 10), satellite);
        }

        logerro.gerarLog("mensagem id: " + id);
    }

    public static void enviarMensagem(String mensagem, String numeroEnvio, String codFil, String operador, String local,
            String latitude, String longitude, String param, Persistencia satellite, ArquivoLogs logerro) throws Exception {

        if (numeroEnvio.startsWith("55")) {
            numeroEnvio = "+" + numeroEnvio;
        }

        if (!numeroEnvio.startsWith("+55")) {
            numeroEnvio = "+55" + numeroEnvio;
        }

        if (local != null) {
            mensagem = mensagem + "\r\n" + "Local: " + local;
        }
        if (latitude != null && longitude != null) {
            mensagem = mensagem + "\r\n" + "http://maps.google.com/maps?&z=10&q=" + latitude + "+" + longitude + "&ll=" + latitude + "+" + longitude;
        }

        EventoMsgWhatsAppDao eventoMsgWhatsAppDao = new EventoMsgWhatsAppDao();

        String id = eventoMsgWhatsAppDao.inserirMensagem("chat", mensagem, "", numeroEnvio.replace("+", ""), "@c.us",
                param, codFil, RecortaAteEspaço(operador, 0, 10), satellite);

        logerro.gerarLog("mensagem id: " + id);
    }

    public static void enviarMensagemImagem(String mensagem, String linkImagem, String numeroEnvio, String codFil, String operador, String local,
            String latitude, String longitude, String param, Persistencia satellite, ArquivoLogs logerro) throws Exception {

        if (numeroEnvio.startsWith("55")) {
            numeroEnvio = "+" + numeroEnvio;
        }

        if (!numeroEnvio.startsWith("+55")) {
            numeroEnvio = "+55" + numeroEnvio;
        }

        if (local != null) {
            mensagem = mensagem + "\r\n" + "Local: " + local;
        }
        if (latitude != null && longitude != null) {
            mensagem = mensagem + "\r\n" + "http://maps.google.com/maps?&z=10&q=" + latitude + "+" + longitude + "&ll=" + latitude + "+" + longitude;
        }

        EventoMsgWhatsAppDao eventoMsgWhatsAppDao = new EventoMsgWhatsAppDao();
        String id = eventoMsgWhatsAppDao.inserirMensagem("media", mensagem, linkImagem, numeroEnvio.replace("+", ""), "@c.us",
                param, codFil, RecortaAteEspaço(operador, 0, 10), satellite);

        logerro.gerarLog("mensagem id: " + id);
    }
}

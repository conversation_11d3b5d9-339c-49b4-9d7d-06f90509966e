/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 24/01/2017, 14:07:40
    Author     : Richard
*/
.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

.cadastrar{
    width: 90vh;
    min-width: 100%;
}


.rota .ui-autocomplete-panel {
    width: 100% !important;
}
.rota .ui-autocomplete-input{
    width: 100% !important;
}

.placa .ui-autocomplete-panel {
    width: 100% !important;
}
.placa .ui-autocomplete-input{
    width: 100% !important;
}

.supervisor .ui-autocomplete-panel {
    width: 100% !important;
}
.supervisor .ui-autocomplete-input{
    width: 100% !important;
}

@media all and (min-width: 768px) {
    .cadastrar{
        width: 700px;
    }
}

.botao .ui-panelgrid .ui-panelgrid-cell, .botao .ui-panelgrid-cell{
    padding: 0px !important;
}

.ui-icon-calendar {
    background-image:  url('../img/icone_escaladodia_40.png') !important;
    background-position: center center !important;
    width: 40px !important;
    height: 40px !important;
    left: 0px !important;
    margin-left: 0px !important;
    top: 1px !important;
    margin-top: 0px !important;
}

.calendario .ui-datepicker-trigger, .calendario .ui-state-hover{
    background: transparent !important;
    width: 40px;
    height: 40px;
    box-shadow: none;
    border: none;
}

.calendario .ui-inputfield{
    display: none;
}

.calendarios .ui-inputfield{
    width: 90px;
}

.calendarioDia .ui-inputfield{
    width: 190px;
}

.ui-state-disabled, .ui-widget-content .ui-state-disabled{
    opacity: 0.7;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.ui-dialog .ui-dialog-buttonpane {
    text-align: center;
    background-image: none;
    margin: .5em 0 0 0;
    padding: .3em 1em .5em .4em;
}
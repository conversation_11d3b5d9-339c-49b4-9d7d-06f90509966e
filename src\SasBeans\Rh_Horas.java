/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Rh_Horas {

    private BigDecimal Matr;
    private BigDecimal CodFil;
    private BigDecimal Semana;
    private String Data;
    private int DiaSem;
    private String Hora1;
    private String Hora2;
    private String Hora3;
    private String Hora4;
    private String Hora5;
    private String Hora6;
    private BigDecimal HsDiurnas;
    private BigDecimal HsNoturnas;
    private BigDecimal HE50;
    private BigDecimal HE100;
    private BigDecimal HE50i;
    private BigDecimal HE100i;
    private BigDecimal HsDiaMin;
    private BigDecimal HEDia;
    private BigDecimal HsAbono;
    private BigDecimal HsProjecao;
    private BigDecimal HsExceden;
    private BigDecimal HsEscala;
    private BigDecimal HsIntraJ;
    private String Situacao;
    private String Secao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the Matr
     */
    public BigDecimal getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Semana
     */
    public BigDecimal getSemana() {
        return Semana;
    }

    /**
     * @param Semana the Semana to set
     */
    public void setSemana(String Semana) {
        try {
            this.Semana = new BigDecimal(Semana);
        } catch (Exception e) {
            this.Semana = new BigDecimal("0");
        }
    }

    /**
     * @return the Data
     */
    public String getData() {
        return Data;
    }

    /**
     * @param Data the Data to set
     */
    public void setData(String Data) {
        this.Data = Data;
    }

    /**
     * @return the DiaSem
     */
    public int getDiaSem() {
        return DiaSem;
    }

    /**
     * @param DiaSem the DiaSem to set
     */
    public void setDiaSem(int DiaSem) {
        this.DiaSem = DiaSem;
    }

    /**
     * @return the Hora1
     */
    public String getHora1() {
        return Hora1;
    }

    /**
     * @param Hora1 the Hora1 to set
     */
    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    /**
     * @return the Hora2
     */
    public String getHora2() {
        return Hora2;
    }

    /**
     * @param Hora2 the Hora2 to set
     */
    public void setHora2(String Hora2) {
        this.Hora2 = Hora2;
    }

    /**
     * @return the Hora3
     */
    public String getHora3() {
        return Hora3;
    }

    /**
     * @param Hora3 the Hora3 to set
     */
    public void setHora3(String Hora3) {
        this.Hora3 = Hora3;
    }

    /**
     * @return the Hora4
     */
    public String getHora4() {
        return Hora4;
    }

    /**
     * @param Hora4 the Hora4 to set
     */
    public void setHora4(String Hora4) {
        this.Hora4 = Hora4;
    }

    /**
     * @return the Hora5
     */
    public String getHora5() {
        return Hora5;
    }

    /**
     * @param Hora5 the Hora5 to set
     */
    public void setHora5(String Hora5) {
        this.Hora5 = Hora5;
    }

    /**
     * @return the Hora6
     */
    public String getHora6() {
        return Hora6;
    }

    /**
     * @param Hora6 the Hora6 to set
     */
    public void setHora6(String Hora6) {
        this.Hora6 = Hora6;
    }

    /**
     * @return the HsDiurnas
     */
    public BigDecimal getHsDiurnas() {
        return HsDiurnas;
    }

    /**
     * @param HsDiurnas the HsDiurnas to set
     */
    public void setHsDiurnas(String HsDiurnas) {
        try {
            this.HsDiurnas = new BigDecimal(HsDiurnas);
        } catch (Exception e) {
            this.HsDiurnas = new BigDecimal("0");
        }
    }

    /**
     * @return the HsNoturnas
     */
    public BigDecimal getHsNoturnas() {
        return HsNoturnas;
    }

    /**
     * @param HsNoturnas the HsNoturnas to set
     */
    public void setHsNoturnas(String HsNoturnas) {
        try {
            this.HsNoturnas = new BigDecimal(HsNoturnas);
        } catch (Exception e) {
            this.HsNoturnas = new BigDecimal("0");
        }
    }

    /**
     * @return the HE50
     */
    public BigDecimal getHE50() {
        return HE50;
    }

    /**
     * @param HE50 the HE50 to set
     */
    public void setHE50(String HE50) {
        try {
            this.HE50 = new BigDecimal(HE50);
        } catch (Exception e) {
            this.HE50 = new BigDecimal("0");
        }
    }

    /**
     * @return the HE100
     */
    public BigDecimal getHE100() {
        return HE100;
    }

    /**
     * @param HE100 the HE100 to set
     */
    public void setHE100(String HE100) {
        try {
            this.HE100 = new BigDecimal(HE100);
        } catch (Exception e) {
            this.HE100 = new BigDecimal("0");
        }
    }

    /**
     * @return the HE50i
     */
    public BigDecimal getHE50i() {
        return HE50i;
    }

    /**
     * @param HE50i the HE50i to set
     */
    public void setHE50i(String HE50i) {
        try {
            this.HE50i = new BigDecimal(HE50i);
        } catch (Exception e) {
            this.HE50i = new BigDecimal("0");
        }
    }

    /**
     * @return the HE100i
     */
    public BigDecimal getHE100i() {
        return HE100i;
    }

    /**
     * @param HE100i the HE100i to set
     */
    public void setHE100i(String HE100i) {
        try {
            this.HE100i = new BigDecimal(HE100i);
        } catch (Exception e) {
            this.HE100i = new BigDecimal("0");
        }
    }

    /**
     * @return the HsDiaMin
     */
    public BigDecimal getHsDiaMin() {
        return HsDiaMin;
    }

    /**
     * @param HsDiaMin the HsDiaMin to set
     */
    public void setHsDiaMin(String HsDiaMin) {
        try {
            this.HsDiaMin = new BigDecimal(HsDiaMin);
        } catch (Exception e) {
            this.HsDiaMin = new BigDecimal("0");
        }
    }

    /**
     * @return the HEDia
     */
    public BigDecimal getHEDia() {
        return HEDia;
    }

    /**
     * @param HEDia the HEDia to set
     */
    public void setHEDia(String HEDia) {
        try {
            this.HEDia = new BigDecimal(HEDia);
        } catch (Exception e) {
            this.HEDia = new BigDecimal("0");
        }
    }

    /**
     * @return the HsAbono
     */
    public BigDecimal getHsAbono() {
        return HsAbono;
    }

    /**
     * @param HsAbono the HsAbono to set
     */
    public void setHsAbono(String HsAbono) {
        try {
            this.HsAbono = new BigDecimal(HsAbono);
        } catch (Exception e) {
            this.HsAbono = new BigDecimal("0");
        }
    }

    /**
     * @return the HsProjecao
     */
    public BigDecimal getHsProjecao() {
        return HsProjecao;
    }

    /**
     * @param HsProjecao the HsProjecao to set
     */
    public void setHsProjecao(String HsProjecao) {
        try {
            this.HsProjecao = new BigDecimal(HsProjecao);
        } catch (Exception e) {
            this.HsProjecao = new BigDecimal("0");
        }
    }

    /**
     * @return the HsExceden
     */
    public BigDecimal getHsExceden() {
        return HsExceden;
    }

    /**
     * @param HsExceden the HsExceden to set
     */
    public void setHsExceden(String HsExceden) {
        try {
            this.HsExceden = new BigDecimal(HsExceden);
        } catch (Exception e) {
            this.HsExceden = new BigDecimal("0");
        }
    }

    /**
     * @return the HsEscala
     */
    public BigDecimal getHsEscala() {
        return HsEscala;
    }

    /**
     * @param HsEscala the HsEscala to set
     */
    public void setHsEscala(String HsEscala) {
        try {
            this.HsEscala = new BigDecimal(HsEscala);
        } catch (Exception e) {
            this.HsEscala = new BigDecimal("0");
        }
    }

    /**
     * @return the HsIntraJ
     */
    public BigDecimal getHsIntraJ() {
        return HsIntraJ;
    }

    /**
     * @param HsIntraJ the HsIntraJ to set
     */
    public void setHsIntraJ(String HsIntraJ) {
        try {
            this.HsIntraJ = new BigDecimal(HsIntraJ);
        } catch (Exception e) {
            this.HsIntraJ = new BigDecimal("0");
        }
    }

    /**
     * @return the Situacao
     */
    public String getSituacao() {
        return Situacao;
    }

    /**
     * @param Situacao the Situacao to set
     */
    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    /**
     * @return the Secao
     */
    public String getSecao() {
        return Secao;
    }

    /**
     * @param Secao the Secao to set
     */
    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public String getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

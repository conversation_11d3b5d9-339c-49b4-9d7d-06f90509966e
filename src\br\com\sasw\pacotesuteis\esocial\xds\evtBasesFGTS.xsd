﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtBasesFGTS/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtBasesFGTS/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtBasesFGTS">
                    <xs:annotation>
                        <xs:documentation>Evento Bases FGTS por Trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do evento de retorno</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="nrRecArqBase">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Recibo do arquivo de origem</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="40"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="perApur">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideTrabalhador">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do Trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="11"/>
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nisTrab" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>NIS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:maxLength value="11"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoFGTS" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Informacoes relativas ao FGTS</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="dtVenc" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data vencimento FGTS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="ideEstabLot" maxOccurs="unbounded">
                                            <xs:annotation>
                                                <xs:documentation>Identificacao do estabelecimento e lotacao</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpInsc">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrInsc">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Número de Inscricao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{8,14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codLotacao">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo da Lotacao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="infoTrabFGTS" maxOccurs="10">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes relativas à matricula, categoria e incidencias</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="matricula" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Matricula</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codCateg">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo da Categoria</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d{3}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtAdm" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data de admissao do trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtDeslig" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data do Desligamento</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtInicio" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data de Inicio</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="mtvDeslig" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo de Motivo do Desligamento</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="\d{2}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtTerm" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data do Término</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="mtvDesligTSV" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Motivo deslig TSV</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="2"/>
                                                                            <xs:pattern value="\d{2}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infoBaseFGTS" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Bases de calculo do FGTS</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="basePerApur" minOccurs="0" maxOccurs="21">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Bases de calculo, exceto se {tpAcConv} = [E]</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="tpValor">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Tipo de valor</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:byte">
                                                                                                    <xs:pattern value="\d{1,2}"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="remFGTS">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Remuneracao (valor da base de calculo) do FGTS</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                            <xs:element name="infoBasePerAntE" minOccurs="0" maxOccurs="180">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Informacoes sobre bases do FGTS quando {tpAcConv} = [E]</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="perRef">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Periodo de referencia</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="basePerAntE" maxOccurs="11">
                                                                                            <xs:annotation>
                                                                                                <xs:documentation>Bases de calculo quando {tpAcConv} = [E]</xs:documentation>
                                                                                            </xs:annotation>
                                                                                            <xs:complexType>
                                                                                                <xs:sequence>
                                                                                                    <xs:element name="tpValorE">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Tipo de valor que influi na apuracao do FGTS</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:byte">
                                                                                                                <xs:pattern value="\d{2}"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="remFGTSE">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Remuneracao (valor da base de calculo) do FGTS</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:decimal">
                                                                                                                <xs:totalDigits value="14"/>
                                                                                                                <xs:fractionDigits value="2"/>
                                                                                                                <xs:maxInclusive value="999999999999"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                </xs:sequence>
                                                                                            </xs:complexType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoDpsFGTS" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes sobre valores de FGTS</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="infoTrabDps" maxOccurs="10">
                                                        <xs:annotation>
                                                            <xs:documentation>Matricula e categoria do trabalhador</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="matricula" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Matricula</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codCateg">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo da Categoria</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d{3}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dpsPerApur" minOccurs="0" maxOccurs="20">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Valor do FGTS, exceto se {tpAcConv} = [E]</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpDps">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de depOsito</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d{2}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="dpsFGTS">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>DepOsito FGTS</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="infoDpsPerAntE" minOccurs="0" maxOccurs="180">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes sobre FGTS quando {tpAcConv} = [E]</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="perRef">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Periodo de referencia</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="dpsPerAntE" maxOccurs="10">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Valores de FGTS quando {tpAcConv} = [E]</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="tpDpsE">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Tipo de depOsito</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:byte">
                                                                                                    <xs:pattern value="\d{2}"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="dpsFGTSE">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Valor histOrico do FGTS a ser depositado</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

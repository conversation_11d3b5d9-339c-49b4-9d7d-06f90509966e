/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PedidoLog;
import SasBeans.Rt_Guias;
import SasBeans.iblCTeOS;
import SasBeans.iblCTeOS_Cliente;
import SasBeans.iblCTeOS_ClienteEnder;
import SasBeans.iblCTeOS_infGTV;
import SasBeans.iblCTeOS_infGTVnotaFiscal;
import br.com.sasw.pacotesuteis.sasbeans.XMLNFE;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.UFUtils;
import br.inf.portalfiscal.cte.TEndeEmi;
import br.inf.portalfiscal.cte.TEndereco;
import br.inf.portalfiscal.cte.TGTVe;
import br.inf.portalfiscal.cte.TUFSemEX;
import br.inf.portalfiscal.cte.TUf;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GTVeDao {

    public List<TGTVe.InfCte.AutXML> getAutXML(String guia, Persistencia persistencia) throws Exception {
        try {
            List<TGTVe.InfCte.AutXML> retorno = new ArrayList<>();
            TGTVe.InfCte.AutXML autXML = new TGTVe.InfCte.AutXML();
            autXML.setCNPJ("00000000000000");
//            autXML.setCPF("00000000000");
            retorno.add(autXML);
            return retorno;
        } catch (Exception e) {
            throw new Exception("GTVeDao.getAutXML - " + e.getMessage() + "\r\n"
                    + "");
        }
    }

    public TGTVe.InfCte.DetGTV getDetGTV(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            TGTVe.InfCte.DetGTV retorno = new TGTVe.InfCte.DetGTV();
            String sql = "SELECT \n"
                    + "    Qtde detGTVqCarga,\n"
                    + "    CASE WHEN CxFGuiasVol.Tipo = '1' THEN '1'\n"
                    + "    ELSE CASE WHEN CxFGuiasVol.Tipo = '2' THEN '2'\n"
                    + "    ELSE CASE WHEN CxFGuiasVol.Tipo = '3' THEN '3'\n"
                    + "    ELSE '3'\n"
                    + "    END END END detGTVinfEspecietpEspecie,\n"
                    + "    Rt_Guias.Valor detGTVinfEspecievEspecie,\n"
                    + "    CASE WHEN Rt_GuiasMoeda.Moeda = 'BRL' THEN 1\n"
                    + "    ELSE '2' END detGTVinfEspecietpNumerario,\n"
                    + "    ISNULL(Rt_GuiasMoeda.Moeda,'BRL') detGTVinfEspeciexMoedaEstr\n"
                    + "FROM \n"
                    + "    CxFGuiasVol\n"
                    + "LEFT JOIN\n"
                    + "    Rt_Guias ON Rt_Guias.Guia = CxFGuiasVol.Guia AND Rt_Guias.Serie = CxFGuiasVol.Serie\n"
                    + "LEFT JOIN\n"
                    + "    Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada AND Rt_GuiasMoeda.Guia = Rt_Guias.Guia AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "WHERE \n"
                    + "    Rt_Guias.Guia = ? AND Rt_Guias.Serie IN(?, 'TSR') ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            TGTVe.InfCte.DetGTV.InfEspecie infEspecie;
            Integer detGTVqCarga = 0;
            while (consulta.Proximo()) {
                infEspecie = new TGTVe.InfCte.DetGTV.InfEspecie();
                infEspecie.setTpEspecie(consulta.getString("detGTVinfEspecietpEspecie"));
                BigDecimal detGTVinfEspecievEspecie = consulta.getBigDecimal("detGTVinfEspecievEspecie").setScale(2, RoundingMode.CEILING);

                infEspecie.setVEspecie(detGTVinfEspecievEspecie.toString());
                infEspecie.setTpNumerario(consulta.getString("detGTVinfEspecietpNumerario"));
                if (infEspecie.getTpNumerario().equals("2")) {
                    infEspecie.setXMoedaEstr(consulta.getString("detGTVinfEspeciexMoedaEstr"));
                }
                retorno.getInfEspecie().add(infEspecie);

                detGTVqCarga += consulta.getInt("detGTVqCarga");
            }

            if (retorno.getInfEspecie().size() > 0) {
                retorno.setQCarga(String.valueOf(detGTVqCarga));
            }

            consulta.Close();

            sql = " SELECT \n"
                    + "    Veiculos.placa detGTVinfVeiculoplaca,\n"
                    + "    Veiculos.UF_Placa detGTVinfVeiculoUF\n"
                    + "FROM \n"
                    + "    Rt_Guias \n"
                    + "LEFT JOIN\n"
                    + "    Escala ON Escala.SeqRota = Rt_Guias.Sequencia\n"
                    + "LEFT JOIN\n"
                    + "    Veiculos ON Veiculos.Numero = Escala.Veiculo\n"
                    + "WHERE \n"
                    + "    Rt_Guias.Guia = ? AND Rt_Guias.Serie IN(?, 'TSR')";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            TGTVe.InfCte.DetGTV.InfVeiculo infVeiculo;
            while (consulta.Proximo()) {
                infVeiculo = new TGTVe.InfCte.DetGTV.InfVeiculo();
                if (null != consulta.getString("detGTVinfVeiculoplaca") && !consulta.getString("detGTVinfVeiculoplaca").equals("")) {
                    infVeiculo.setPlaca(consulta.getString("detGTVinfVeiculoplaca"));
                    infVeiculo.setUF(TUf.fromValue(consulta.getString("detGTVinfVeiculoUF")));
                    retorno.getInfVeiculo().add(infVeiculo);
                }
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("GTVeDao.getDetGTV - " + e.getMessage() + "\r\n"
                    + "");
        }
    }

    public TGTVe getTGTVe(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = " DECLARE @DataHoraEmiss AS VARCHAR(30);\n"
                    + " DECLARE @DataHoraEmissSimples AS VARCHAR(30);\n"
                    + " SET @DataHoraEmiss = (SELECT CONVERT(CHAR(10), CONVERT(date, GETDATE()))+'T'+ISNULL(LEFT(CONVERT(CHAR(10), CONVERT(time, GETDATE())), 5), '00:00')+':00'); \n"
                    + " SET @DataHoraEmissSimples = (SELECT CONVERT(CHAR(10), CONVERT(date, GETDATE()))+'T'+ISNULL(LEFT(CONVERT(CHAR(10), CONVERT(time, GETDATE())), 5), '00:00') + '00:00'); \n"
                    + " SELECT\n"
                    + "    LEFT(CONVERT(NVARCHAR, GETDATE(), 12), 4) ideIdAnoMes,\n"
                    + "    Paramet.FusoHorarioSEFAZ,\n"
                    + "    Filiais.UF ideCUF, \n"
                    + "    '2606' ideCFOP,\n"
                    + "    'Transporte de Valores' idenatOp,\n"
                    + "    '64' idemod,\n"
                    + "    Rt_Guias.Serie ideserie,\n"
                    + "    CONVERT(bigint, Rt_Guias.guia) idenCT,\n"
                    //                    + "    CONVERT(CHAR(10), Rt_Guias.data, 126)+'T'+Rt_Guias.Hora+':00' idedhEmi,\n"
                    + "    @DataHoraEmiss idedhEmi,\n"
                    + "    '1' idetpImp,\n"
                    + "    '1' idetpEmis,\n"
                    + "    '' idecDV,\n"
                    + "    '1' idetpAmb,\n"
                    + "    '4' idetpCTe,\n"
                    + "    '1' ideverProc,\n"
                    + "    CONVERT(bigint, Clientes.CodCidade) idecMunEnv,\n"
                    + "    Clientes.Cidade idexMunEnv,\n"
                    + "    Clientes.Estado ideUFEnv,\n"
                    + "    '06' idemodal,\n"
                    + "    '9' idetpServ,\n"
                    + "    CASE WHEN Clientes.IE = 'ISENTO' THEN '2'\n"
                    + "    WHEN Clientes.IE = 'NAO CONTRIBUINTE' THEN '9'\n"
                    + "    WHEN Clientes.IE IS NOT NULL AND Clientes.IE <> '' THEN '1'\n"
                    + "    ELSE '9' END ideindIEToma,\n"
                    + "    CASE WHEN Rt_Perc.ER = 'R' THEN \n"
                    + "        CONVERT(CHAR(10), Rotas.data, 126)+'T'+ISNULL(NULLIF(Rt_Perc.HrSaida,''),'00:00')+':00' \n"
                    + "    ELSE \n"
                    + "        CASE WHEN TesSaidas.Dt_Alter IS NOT NULL THEN \n"
                    + "                CONVERT(CHAR(10), TesSaidas.Dt_Alter, 126)+'T'+ISNULL(TesSaidas.Hr_Alter, '00:00')+':00'\n"
                    + "             WHEN CxFGuias.DtSai IS NOT NULL THEN\n"
                    + "                CONVERT(CHAR(10), CxFGuias.DtSai, 126)+'T'+ISNULL(CxFGuias.HrSai, '00:00')+':00'\n"
                    + "             ELSE \n"
                    + "                @DataHoraEmiss\n"
                    + "        END\n"
                    + "    END idedhSaidaOrig,"
                    /*+ "    CASE WHEN Rt_Perc.ER = 'R' THEN \n"
                    + "        CONVERT(CHAR(10), Rotas.data, 126)+'T'+ISNULL(NULLIF(Rt_Perc.HrSaida,''),'00:00')+':00' \n"
                    + "    ELSE \n"
                    + "        CASE WHEN TesSaidas.Dt_Alter IS NOT NULL THEN \n"
                    + "            CONVERT(CHAR(10), TesSaidas.Dt_Alter, 126)+'T'+ISNULL(TesSaidas.Hr_Alter, '00:00')+':00'\n"
                    + "        ELSE \n"
                    + "            CONVERT(CHAR(10), CxFGuias.DtSai, 126)+'T'+ISNULL(CxFGuias.HrSai, '00:00')+':00'\n"
                    + "        END\n"
                    + "    END idedhSaidaOrig,\n"*/
                    + "    CASE WHEN Rt_Perc.ER = 'E' THEN\n"
                    + "        CASE WHEN Rt_Perc.HrCheg IS NOT NULL AND Rotas.data IS NOT NULL THEN CONVERT(CHAR(10), Rotas.data, 126)+'T'+Rt_Perc.HrCheg+':00' ELSE @DataHoraEmiss END \n"
                    + "    ELSE \n"
                    + "        ISNULL((SELECT TOP 1 CASE WHEN RtP.HrCheg IS NOT NULL AND Rt.data IS NOT NULL THEN CONVERT(CHAR(10), Rt.data, 126)+'T'+RtP.HrCheg+':00' ELSE @DataHoraEmiss END FROM Rt_perc RtP LEFT JOIN Rotas Rt ON Rt.Sequencia = RtP.Sequencia WHERE RtP.Sequencia = CxfGuias.SeqRota AND RtP.Hora1 = CxfGuias.Hora1 AND RtP.Flag_Excl <> '*'), @DataHoraEmiss) \n"
                    + "    END idedhChegadaDest,\n"
                    + "    CASE WHEN Rt_Perc.ER = 'R' THEN '0'\n"
                    + "    ELSE '1' END tomatoma, \n"
                    + "    '4' tomaTerceirotoma,\n"
                    + "    TomaTerceiro.CGC tomaTerceiroCNPJ,\n"
                    + "    ISNULL(NULLIF(TomaTerceiro.CPF,''),'00000000000') tomaTerceiroCPF,\n"
                    + "    CASE WHEN TomaTerceiro.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN TomaTerceiro.IE <> 'NAO CONTRIBUINTE' THEN TomaTerceiro.IE\n"
                    + "    ELSE '' END\n"
                    + "    END tomaTerceiroIE,\n"
                    + "    TomaTerceiro.Nome tomaTerceiroxNome,\n"
                    + "    TomaTerceiro.NRed tomaTerceiroxFant,\n"
                    + "    TomaTerceiro.fone1 tomaTerceirofone,\n"
                    + "    TomaTerceiro.ende tomaTerceiroenderTomaxLgr,\n"
                    + "    '' tomaTerceiroenderTomanro,\n"
                    //                    + "    '' tomaTerceiroenderTomaxCpl,\n"
                    + "    TomaTerceiro.bairro tomaTerceiroenderTomaxBairro,\n"
                    + "    CONVERT(bigint, TomaTerceiro.CodCidade) tomaTerceiroenderTomacMun,\n"
                    + "    TomaTerceiro.cidade tomaTerceiroenderTomaxMun,\n"
                    + "    TomaTerceiro.CEP tomaTerceiroenderTomaCEP,\n"
                    + "    TomaTerceiro.Estado tomaTerceiroenderTomaUF,\n"
                    + "    '1058' tomaTerceiroenderTomacPais,\n"
                    + "    'Brasil' tomaTerceiroenderTomaxPais,\n"
                    + "    TomaTerceiro.email tomaTerceiroemail,\n"
                    + "    CONVERT(CHAR(19), getDate(), 126) idedhCont,\n"
                    + "    'Integração realizada logo após emissão' idexJust,\n"
                    + "    ISNULL(GTV.obs,'') complxObs,\n"
                    + "    Filiais.CNPJ emitCNPJ,\n"
                    + "    Filiais.InscEst emitIE,\n"
                    + "    '' emitIEST,\n"
                    + "    Filiais.RazaoSocial emitxNome,\n"
                    + "    Filiais.Descricao emitxFant,\n"
                    + "    Filiais.Endereco emitenderEmitxLgr,\n"
                    + "    '' emitenderEmitnro,\n"
                    //                    + "    '' emitenderEmitxCpl,\n"
                    + "    Filiais.Bairro emitenderEmitxBairro,\n"
                    + "    (SELECT TOP 1 CONVERT(BigInt, Municipios.CodIBGE) FROM Municipios WHERE Municipios.Nome = Filiais.Cidade) emitenderEmitcMun,\n"
                    + "    Filiais.Cidade emitenderEmitxMun,\n"
                    + "    Filiais.CEP emitenderEmitCEP,\n"
                    + "    Filiais.UF emitenderEmitUF,\n"
                    + "    Filiais.Fone emitenderEmitfone,\n"
                    + "    Rem.CGC remCNPJ,\n"
                    + "    Rem.CPF remCPF,\n"
                    + "    CASE WHEN Rem.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN Rem.IE <> 'NAO CONTRIBUINTE' THEN Rem.IE\n"
                    + "    ELSE '' END\n"
                    + "    END remIE,\n"
                    + "    Rem.Nome remxNome,\n"
                    + "    Rem.NRed remxFant,\n"
                    + "    Rem.fone1 remfone,\n"
                    + "    Rem.ende remenderRemexLgr,\n"
                    + "    '' remenderRemenro,\n"
                    //                    + "    '' remenderRemexCpl,\n"
                    + "    Rem.bairro remenderRemexBairro,\n"
                    + "    CONVERT(bigint, Rem.CodCidade) remenderRemecMun,\n"
                    + "    Rem.cidade remenderRemexMun,\n"
                    + "    Rem.CEP remenderRemeCEP,\n"
                    + "    Rem.Estado remenderRemeUF,\n"
                    + "    '1058' remenderRemecPais,\n"
                    + "    'Brasil' remenderRemexPais,\n"
                    + "    Rem.email rememail,\n"
                    + "    Dest.CGC destCNPJ,\n"
                    + "    Dest.CPF destCPF,\n"
                    + "    CASE WHEN Dest.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN Dest.IE <> 'NAO CONTRIBUINTE' THEN Dest.IE\n"
                    + "    ELSE '' END\n"
                    + "    END destIE,\n"
                    + "    Dest.Nome destxNome,\n"
                    + "    Dest.NRed destxFant,\n"
                    + "    Dest.fone1 destfone,\n"
                    + "    '' destISUF,\n"
                    + "    Dest.ende destenderDestxLgr,\n"
                    + "    '' destenderDestnro,\n"
                    //                    + "    '' destenderDestxCpl,\n"
                    + "    Dest.bairro destenderDestxBairro,\n"
                    + "    CONVERT(bigint, Dest.CodCidade) destenderDestcMun,\n"
                    + "    Dest.cidade destenderDestxMun,\n"
                    + "    Dest.CEP destenderDestCEP,\n"
                    + "    Dest.Estado destenderDestUF,\n"
                    + "    '1058' destenderDestcPais,\n"
                    + "    'Brasil' destenderDestxPais,\n"
                    + "    Dest.email destemail,\n"
                    + "    @DataHoraEmissSimples dtHoraEmisSimples\n"
                    + "    \n"
                    + "FROM \n"
                    + "    Rt_Guias\n"
                    + "LEFT JOIN\n"
                    + "    CxfGuias ON CxfGuias.Guia = Rt_Guias.Guia  AND CxfGuias.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    + "    TesSaidas ON TesSaidas.Guia = Rt_Guias.Guia  AND TesSaidas.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    + "    Rotas ON Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "LEFT JOIN\n"
                    + "    Rt_Perc ON Rt_Perc.Sequencia = Rt_Guias.Sequencia AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN\n"
                    + "    Filiais ON Filiais.CodFil = Rotas.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    GTV ON GTV.CodFil = Rotas.CodFil AND  GTV.Guia = Rt_Guias.Guia AND GTV.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    //+ "    OS_Vig ON OS_Vig.OS = GTV.OS AND  OS_Vig.CodFil = GTV.CodFil \n"
                    + "      OS_Vig ON OS_Vig.OS = RT_Guias.OS AND  OS_Vig.CodFil = RT_Guias.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    Clientes ON Clientes.Codigo = OS_Vig.Cliente AND Clientes.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    Clientes TomaTerceiro ON TomaTerceiro.Codigo = OS_Vig.CliFat AND TomaTerceiro.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    Clientes Rem ON Rem.Codigo = CASE WHEN Rt_Perc.ER = 'E' THEN OS_Vig.CliDst -- ent os_vig clidst\n"
                    + "    ELSE Rt_Perc.CodCli1 END AND Rem.CodFil = Rt_Perc.CodFil -- rec codcli1\n"
                    + "LEFT JOIN\n"
                    + "    Clientes Dest ON Dest.Codigo = CASE WHEN Rt_Perc.ER = 'E' THEN Rt_Perc.CodCli1 -- ent codcli1\n"
                    + "    ELSE Rt_Perc.CodCli2 END AND Dest.CodFil = Rt_Perc.CodFil -- rec codcli2\n"
                    + "LEFT JOIN\n"
                    + "    Paramet ON Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "WHERE \n"
                    + "    Rt_Guias.Guia = ? AND Rt_Guias.Serie IN(?, 'TSR') ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            TGTVe retorno = null;
            if (consulta.Proximo()) {
                if (null == retorno) {
                    retorno = new TGTVe();
                    TGTVe.InfCte infCte = new TGTVe.InfCte();

                    String fusoHorario = LocalTime.parse("00:00", DateTimeFormatter.ofPattern("HH:mm")).atOffset(ZoneOffset.ofHours(consulta.getInt("fusoHorarioSEFAZ") - 3)).toString().substring(5);

                    TGTVe.InfCte.Ide ide = new TGTVe.InfCte.Ide();
                    ide.setCUF(UFUtils.codigo(consulta.getString("ideCUF")));

                    if (ide.getCUF().equals("35")) {
                        ide.setTpEmis("8");
                    } else {
                        ide.setTpEmis(consulta.getString("idetpEmis").equals("") ? null : consulta.getString("idetpEmis").trim());
                    }

                    if (ide.getTpEmis().equals("2")) {
                        ide.setDhCont(consulta.getString("idedhCont") + fusoHorario);
                        ide.setXJust("Falta de comunicacao com SVC-SP");
                    } else {
                        //ide.setDhCont(consulta.getString("idedhCont") + fusoHorario);
                        //ide.setXJust(consulta.getString("idexJust").equals("") ? null : consulta.getString("idexJust").trim());
                        ide.setDhCont(null);
                        ide.setXJust(null);
                    }

                    ide.setCFOP(consulta.getString("ideCFOP").equals("") ? null : consulta.getString("ideCFOP").trim());
                    ide.setNatOp(consulta.getString("idenatOp").equals("") ? null : consulta.getString("idenatOp").trim());
                    ide.setMod(consulta.getString("idemod").equals("") ? null : consulta.getString("idemod").trim());
                    if (!consulta.getString("ideserie").equals("TSR")) {
                        ide.setSerie(consulta.getString("ideserie").equals("") ? null : Integer.toString(consulta.getInt("ideserie")));
                    } else {
                        ide.setSerie("53");
                    }
                    ide.setNCT(consulta.getString("idenCT").equals("") ? null : consulta.getString("idenCT").trim());
                    ide.setCCT(FuncoesString.PreencheEsquerda(FuncoesString.RecortaString(consulta.getString("idenCT"), 0, 8), 8, "0"));
                    ide.setDhEmi(consulta.getString("idedhEmi") + fusoHorario);
                    ide.setTpImp(consulta.getString("idetpImp").equals("") ? null : consulta.getString("idetpImp").trim());
                    ide.setCDV(consulta.getString("idecDV").equals("") ? null : consulta.getString("idecDV").trim());
                    ide.setTpAmb(consulta.getString("idetpAmb").equals("") ? null : consulta.getString("idetpAmb").trim());
                    ide.setTpCTe(consulta.getString("idetpCTe").equals("") ? null : consulta.getString("idetpCTe").trim());
                    ide.setVerProc(consulta.getString("ideverProc").equals("") ? null : consulta.getString("ideverProc").trim());
                    ide.setCMunEnv(consulta.getString("idecMunEnv").equals("") ? null : consulta.getString("idecMunEnv").trim());
                    ide.setXMunEnv(consulta.getString("idexMunEnv").equals("") ? null : consulta.getString("idexMunEnv").trim());
                    ide.setUFEnv(TUf.fromValue(consulta.getString("ideUFEnv")));
                    ide.setModal(consulta.getString("idemodal").equals("") ? null : consulta.getString("idemodal").trim());
                    ide.setTpServ(consulta.getString("idetpServ").equals("") ? null : consulta.getString("idetpServ").trim());
                    ide.setIndIEToma(consulta.getString("ideindIEToma").equals("") ? null : consulta.getString("ideindIEToma").trim());

                    ide.setDhSaidaOrig(consulta.getString("idedhEmi") + fusoHorario);
                    ide.setDhChegadaDest(consulta.getString("idedhEmi") + fusoHorario);

                    /*ide.setDhSaidaOrig(consulta.getString("idedhSaidaOrig") + fusoHorario);
                ide.setDhChegadaDest(consulta.getString("idedhChegadaDest") + fusoHorario);*/
                    TGTVe.InfCte.Ide.Toma toma = new TGTVe.InfCte.Ide.Toma();
                    toma.setToma(consulta.getString("tomatoma").equals("") ? null : consulta.getString("tomatoma").trim());
                    ide.setToma(toma);

                    TGTVe.InfCte.Ide.TomaTerceiro tomaTerceiro = new TGTVe.InfCte.Ide.TomaTerceiro();
                    tomaTerceiro.setToma(consulta.getString("tomaTerceirotoma").equals("") ? null : consulta.getString("tomaTerceirotoma").trim());
                    tomaTerceiro.setCNPJ(consulta.getString("tomaTerceiroCNPJ").equals("") ? null : consulta.getString("tomaTerceiroCNPJ").trim());
                    tomaTerceiro.setCPF(consulta.getString("tomaTerceiroCPF").equals("") ? null : consulta.getString("tomaTerceiroCPF").trim());
                    tomaTerceiro.setIE(consulta.getString("tomaTerceiroIE").equals("") ? null : consulta.getString("tomaTerceiroIE").trim().replace(".", "").replace("-", ""));
                    tomaTerceiro.setXNome(consulta.getString("tomaTerceiroxNome").equals("") ? null : consulta.getString("tomaTerceiroxNome").trim());
                    tomaTerceiro.setXFant(consulta.getString("tomaTerceiroxFant").equals("") ? null : consulta.getString("tomaTerceiroxFant").trim());
                    tomaTerceiro.setFone(consulta.getString("tomaTerceirofone").equals("") ? null : consulta.getString("tomaTerceirofone").trim().replace(" ", ""));

                    TEndereco tomaTerceiroenderToma = new TEndereco();
                    tomaTerceiroenderToma.setXLgr(consulta.getString("tomaTerceiroenderTomaxLgr").equals("") ? null : consulta.getString("tomaTerceiroenderTomaxLgr").trim());
                    tomaTerceiroenderToma.setXLgr(tomaTerceiroenderToma.getXLgr().replace("/", " "));
                    if (tomaTerceiroenderToma.getXLgr().contains(",")) {
                        try {
                            tomaTerceiroenderToma.setNro(tomaTerceiroenderToma.getXLgr().substring(tomaTerceiroenderToma.getXLgr().lastIndexOf(",")));
                            tomaTerceiroenderToma.setXLgr(tomaTerceiroenderToma.getXLgr().substring(0, tomaTerceiroenderToma.getXLgr().lastIndexOf(",")));
                            tomaTerceiroenderToma.setNro(Integer.toString(Integer.parseInt(tomaTerceiroenderToma.getNro().replace(",", "").replace(" ", ""))));
                        } catch (Exception en) {
                            tomaTerceiroenderToma.setNro("0");
                        }
                    } else {
                        tomaTerceiroenderToma.setNro("0");
                    }
//                tomaTerceiroenderToma.setXCpl(consulta.getString("tomaTerceiroenderTomaxCpl").equals("") ? null : consulta.getString("tomaTerceiroenderTomaxCpl").trim());
                    tomaTerceiroenderToma.setXBairro(consulta.getString("tomaTerceiroenderTomaxBairro").equals("") ? null : consulta.getString("tomaTerceiroenderTomaxBairro").trim());
                    tomaTerceiroenderToma.setCMun(consulta.getString("tomaTerceiroenderTomacMun").equals("") ? null : consulta.getString("tomaTerceiroenderTomacMun").trim());
                    tomaTerceiroenderToma.setXMun(consulta.getString("tomaTerceiroenderTomaxMun").equals("") ? null : consulta.getString("tomaTerceiroenderTomaxMun").trim());
                    tomaTerceiroenderToma.setCEP(consulta.getString("tomaTerceiroenderTomaCEP").equals("") ? null : consulta.getString("tomaTerceiroenderTomaCEP").trim());
                    tomaTerceiroenderToma.setUF(TUf.fromValue(consulta.getString("tomaTerceiroenderTomaUF")));
                    tomaTerceiroenderToma.setCPais(consulta.getString("tomaTerceiroenderTomacPais").equals("") ? null : consulta.getString("tomaTerceiroenderTomacPais").trim());
                    tomaTerceiroenderToma.setXPais(consulta.getString("tomaTerceiroenderTomaxPais").equals("") ? null : consulta.getString("tomaTerceiroenderTomaxPais").trim());
                    tomaTerceiro.setEnderToma(tomaTerceiroenderToma);

                    tomaTerceiro.setEmail(consulta.getString("tomaTerceiroemail").equals("") ? null : consulta.getString("tomaTerceiroemail").trim());
//                ide.setTomaTerceiro(tomaTerceiro);

                    infCte.setIde(ide);

                    TGTVe.InfCte.Compl compl = new TGTVe.InfCte.Compl();
                    compl.setXObs(consulta.getString("complxObs").equals("") ? null : consulta.getString("complxObs").trim());
                    if (null != compl.getXObs()
                            && !compl.getXObs().trim().equals("")) {
                        infCte.setCompl(compl);
                    }

                    TGTVe.InfCte.Emit emit = new TGTVe.InfCte.Emit();
                    emit.setCNPJ(consulta.getString("emitCNPJ").equals("") ? null : consulta.getString("emitCNPJ").trim());
                    emit.setIE(consulta.getString("emitIE").equals("") ? null : consulta.getString("emitIE").trim().replace(".", "").replace("-", ""));
                    emit.setIEST(consulta.getString("emitIEST").equals("") ? null : consulta.getString("emitIEST").trim());
                    emit.setXNome(consulta.getString("emitxNome").equals("") ? null : consulta.getString("emitxNome").trim());
                    emit.setXFant(consulta.getString("emitxFant").equals("") ? null : consulta.getString("emitxFant").trim());

                    TEndeEmi emitenderEmit = new TEndeEmi();
                    emitenderEmit.setXLgr(consulta.getString("emitenderEmitxLgr").trim());
                    if (emitenderEmit.getXLgr().contains(",")) {
                        try {
                            emitenderEmit.setNro(emitenderEmit.getXLgr().substring(emitenderEmit.getXLgr().lastIndexOf(",")));
                            emitenderEmit.setXLgr(emitenderEmit.getXLgr().substring(0, emitenderEmit.getXLgr().lastIndexOf(",")));
                            emitenderEmit.setNro(Integer.toString(Integer.parseInt(emitenderEmit.getNro().replace(",", "").replace(" ", ""))));
                        } catch (Exception en) {
                            emitenderEmit.setNro("0");
                        }
                    } else {
                        emitenderEmit.setNro("0");
                    }
//                emitenderEmit.setXCpl(consulta.getString("emitenderEmitxCpl").equals("") ? null : consulta.getString("emitenderEmitxCpl").trim());
                    emitenderEmit.setXBairro(consulta.getString("emitenderEmitxBairro").equals("") ? null : consulta.getString("emitenderEmitxBairro").trim());
                    emitenderEmit.setCMun(consulta.getString("emitenderEmitcMun").equals("") ? null : consulta.getString("emitenderEmitcMun").trim());
                    emitenderEmit.setXMun(consulta.getString("emitenderEmitxMun").equals("") ? null : consulta.getString("emitenderEmitxMun").trim());
                    emitenderEmit.setCEP(consulta.getString("emitenderEmitCEP").equals("") ? null : consulta.getString("emitenderEmitCEP").trim());
                    emitenderEmit.setUF(TUFSemEX.fromValue(consulta.getString("emitenderEmitUF")));
                    emitenderEmit.setFone(consulta.getString("emitenderEmitfone").equals("") ? null : consulta.getString("emitenderEmitfone").trim().replace(" ", ""));
                    emit.setEnderEmit(emitenderEmit);
                    infCte.setEmit(emit);

                    TGTVe.InfCte.Rem rem = new TGTVe.InfCte.Rem();
                    rem.setCNPJ(consulta.getString("remCNPJ").equals("") ? null : consulta.getString("remCNPJ").trim());
                    rem.setCPF(consulta.getString("remCPF").equals("") ? null : consulta.getString("remCPF").trim());
                    rem.setIE(consulta.getString("remIE").equals("") ? null : consulta.getString("remIE").trim().replace(".", "").replace("-", ""));
                    rem.setXNome(consulta.getString("remxNome").equals("") ? null : consulta.getString("remxNome").trim());
                    rem.setXFant(consulta.getString("remxFant").equals("") ? null : consulta.getString("remxFant").trim());
                    rem.setFone(consulta.getString("remfone").equals("") ? null : consulta.getString("remfone").trim().replace(" ", ""));

                    TEndereco remenderReme = new TEndereco();
                    remenderReme.setXLgr(consulta.getString("remenderRemexLgr").equals("") ? null : consulta.getString("remenderRemexLgr").trim());
                    if (remenderReme.getXLgr().contains(",")) {
                        try {
                            remenderReme.setNro(remenderReme.getXLgr().substring(remenderReme.getXLgr().lastIndexOf(",")));
                            remenderReme.setXLgr(remenderReme.getXLgr().substring(0, remenderReme.getXLgr().lastIndexOf(",")));
                            remenderReme.setNro(Integer.toString(Integer.parseInt(remenderReme.getNro().replace(",", "").replace(" ", ""))));
                        } catch (Exception en) {
                            remenderReme.setNro("0");
                        }
                    } else {
                        remenderReme.setNro("0");
                    }
//                remenderReme.setXCpl(consulta.getString("remenderRemexCpl").equals("") ? null : consulta.getString("remenderRemexCpl").trim());
                    remenderReme.setXBairro(consulta.getString("remenderRemexBairro").equals("") ? null : consulta.getString("remenderRemexBairro").trim());
                    remenderReme.setCMun(consulta.getString("remenderRemecMun").equals("") ? null : consulta.getString("remenderRemecMun").trim());
                    remenderReme.setXMun(consulta.getString("remenderRemexMun").equals("") ? null : consulta.getString("remenderRemexMun").trim());
                    remenderReme.setCEP(consulta.getString("remenderRemeCEP").equals("") ? null : consulta.getString("remenderRemeCEP").trim());
                    remenderReme.setUF(TUf.fromValue(consulta.getString("remenderRemeUF")));
                    remenderReme.setCPais(consulta.getString("remenderRemecPais").equals("") ? null : consulta.getString("remenderRemecPais").trim());
                    remenderReme.setXPais(consulta.getString("remenderRemexPais").equals("") ? null : consulta.getString("remenderRemexPais").trim());
                    rem.setEnderReme(remenderReme);
                    rem.setEmail(consulta.getString("rememail").equals("") ? null : consulta.getString("rememail").trim());
                    infCte.setRem(rem);

                    TGTVe.InfCte.Dest dest = new TGTVe.InfCte.Dest();
                    dest.setCNPJ(consulta.getString("destCNPJ").equals("") ? null : consulta.getString("destCNPJ").trim());
                    dest.setCPF(consulta.getString("destCPF").equals("") ? null : consulta.getString("destCPF").trim());
                    dest.setIE(consulta.getString("destIE").equals("") ? null : consulta.getString("destIE").trim().replace(".", "").replace("-", ""));
                    dest.setXNome(consulta.getString("destxNome").equals("") ? null : consulta.getString("destxNome").trim().replace("/", ""));
                    dest.setFone(consulta.getString("destfone").equals("") ? null : consulta.getString("destfone").trim().replace(" ", ""));
                    dest.setISUF(consulta.getString("destISUF").equals("") ? null : consulta.getString("destISUF").trim());

                    TEndereco destenderDest = new TEndereco();
                    destenderDest.setXLgr(consulta.getString("destenderDestxLgr").equals("") ? null : consulta.getString("destenderDestxLgr").trim());
                    destenderDest.setXLgr(destenderDest.getXLgr().replace("/", " "));
                    if (null != destenderDest.getXLgr() && destenderDest.getXLgr().contains(",")) {
                        try {
                            destenderDest.setNro(destenderDest.getXLgr().substring(destenderDest.getXLgr().lastIndexOf(",")));
                            destenderDest.setXLgr(destenderDest.getXLgr().substring(0, destenderDest.getXLgr().lastIndexOf(",")).trim());
                            destenderDest.setNro(Integer.toString(Integer.parseInt(destenderDest.getNro().replace(",", "").replace(" ", ""))));
                        } catch (Exception en) {
                            destenderDest.setNro("0");
                        }
                    } else {
                        destenderDest.setNro("0");
                    }
//                destenderDest.setXCpl(consulta.getString("destenderDestxCpl").equals("") ? null : consulta.getString("destenderDestxCpl").trim());
                    destenderDest.setXBairro(consulta.getString("destenderDestxBairro").equals("") ? null : consulta.getString("destenderDestxBairro").trim());
                    destenderDest.setCMun(consulta.getString("destenderDestcMun").equals("") ? null : consulta.getString("destenderDestcMun").trim());
                    destenderDest.setXMun(consulta.getString("destenderDestxMun").equals("") ? null : consulta.getString("destenderDestxMun").trim());
                    destenderDest.setCEP(consulta.getString("destenderDestCEP").equals("") ? null : consulta.getString("destenderDestCEP").trim());
                    destenderDest.setUF(TUf.fromValue(consulta.getString("destenderDestUF")));
                    destenderDest.setCPais(consulta.getString("destenderDestcPais").equals("") ? null : consulta.getString("destenderDestcPais").trim());
                    destenderDest.setXPais(consulta.getString("destenderDestxPais").equals("") ? null : consulta.getString("destenderDestxPais").trim());
                    dest.setEnderDest(destenderDest);
                    dest.setEmail(consulta.getString("destemail").equals("") ? null : consulta.getString("destemail").trim());
                    infCte.setDest(dest);

                    infCte.setId(consulta.getString("ideIdAnoMes"));

                    retorno.setInfCte(infCte);
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("GTVeDao.getTGTVeInfCteIde - " + e.getMessage() + "\r\n"
                    + "SELECT\n"
                    //                    + "    FORMAT(Rt_Guias.data, 'yyMM') ideIdAnoMes,\n"
                    + "    '2009' ideIdAnoMes,\n"
                    + "    Paramet.FusoHorarioSEFAZ,\n"
                    + "    Filiais.UF ideCUF, \n"
                    + "    '2606' ideCFOP,\n"
                    + "    'Transporte de Valores' idenatOp,\n"
                    + "    '64' idemod,\n"
                    + "    Rt_Guias.Serie ideserie,\n"
                    + "    CONVERT(bigint, Rt_Guias.guia) idenCT,\n"
                    //                    + "    CONVERT(CHAR(10), Rt_Guias.data, 126)+'T'+Rt_Guias.Hora+':00' idedhEmi,\n"
                    + "    '2020-09-14T14:39:00' idedhEmi,\n"
                    + "    '1' idetpImp,\n"
                    + "    '1' idetpEmis,\n"
                    + "    '' idecDV,\n"
                    + "    '1' idetpAmb,\n"
                    + "    '4' idetpCTe,\n"
                    + "    '1' ideverProc,\n"
                    + "    CONVERT(bigint, Clientes.CodCidade) idecMunEnv,\n"
                    + "    Clientes.Cidade idexMunEnv,\n"
                    + "    Clientes.Estado ideUFEnv,\n"
                    + "    '06' idemodal,\n"
                    + "    '9' idetpServ,\n"
                    + "    CASE WHEN Clientes.IE = 'ISENTO' THEN '2'\n"
                    + "    WHEN Clientes.IE = 'NAO CONTRIBUINTE' THEN '9'\n"
                    + "    WHEN Clientes.IE IS NOT NULL AND Clientes.IE <> '' THEN '1'\n"
                    + "    ELSE '9' END ideindIEToma,\n"
                    + "    CASE WHEN Rt_Perc.ER = 'R' THEN \n"
                    + "        CONVERT(CHAR(10), Rotas.data, 126)+'T'+ISNULL(NULLIF(Rt_Perc.HrSaida,''),'00:00')+':00' \n"
                    + "    ELSE \n"
                    + "        CASE WHEN TesSaidas.Dt_Alter IS NOT NULL THEN \n"
                    + "            CONVERT(CHAR(10), TesSaidas.Dt_Alter, 126)+'T'+ISNULL(TesSaidas.Hr_Alter, '00:00')+':00'\n"
                    + "        ELSE \n"
                    + "            CONVERT(CHAR(10), CxFGuias.DtSai, 126)+'T'+ISNULL(CxFGuias.HrSai, '00:00')+':00'\n"
                    + "        END\n"
                    + "    END idedhSaidaOrig,\n"
                    + "    CASE WHEN Rt_Perc.ER = 'E' THEN\n"
                    + "        CONVERT(CHAR(10), Rotas.data, 126)+'T'+Rt_Perc.HrCheg+':00' \n"
                    + "    ELSE \n"
                    + "        (SELECT TOP 1 CONVERT(CHAR(10), Rt.data, 126)+'T'+RtP.HrCheg+':00' FROM Rt_perc RtP LEFT JOIN Rotas Rt ON Rt.Sequencia = RtP.Sequencia WHERE RtP.Sequencia = CxfGuias.SeqRota AND RtP.Hora1 = CxfGuias.Hora1 AND RtP.Flag_Excl <> '*') \n"
                    + "    END idedhChegadaDest,\n"
                    + "    CASE WHEN Rt_Perc.ER = 'R' THEN '0'\n"
                    + "    ELSE '1' END tomatoma, \n"
                    + "    '4' tomaTerceirotoma,\n"
                    + "    TomaTerceiro.CGC tomaTerceiroCNPJ,\n"
                    + "    ISNULL(NULLIF(TomaTerceiro.CPF,''),'00000000000') tomaTerceiroCPF,\n"
                    + "    CASE WHEN TomaTerceiro.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN TomaTerceiro.IE <> 'NAO CONTRIBUINTE' THEN TomaTerceiro.IE\n"
                    + "    ELSE '' END\n"
                    + "    END tomaTerceiroIE,\n"
                    + "    TomaTerceiro.Nome tomaTerceiroxNome,\n"
                    + "    TomaTerceiro.NRed tomaTerceiroxFant,\n"
                    + "    TomaTerceiro.fone1 tomaTerceirofone,\n"
                    + "    TomaTerceiro.ende tomaTerceiroenderTomaxLgr,\n"
                    + "    '' tomaTerceiroenderTomanro,\n"
                    //                    + "    '' tomaTerceiroenderTomaxCpl,\n"
                    + "    TomaTerceiro.bairro tomaTerceiroenderTomaxBairro,\n"
                    + "    CONVERT(bigint, TomaTerceiro.CodCidade) tomaTerceiroenderTomacMun,\n"
                    + "    TomaTerceiro.cidade tomaTerceiroenderTomaxMun,\n"
                    + "    TomaTerceiro.CEP tomaTerceiroenderTomaCEP,\n"
                    + "    TomaTerceiro.Estado tomaTerceiroenderTomaUF,\n"
                    + "    '1058' tomaTerceiroenderTomacPais,\n"
                    + "    'Brasil' tomaTerceiroenderTomaxPais,\n"
                    + "    TomaTerceiro.email tomaTerceiroemail,\n"
                    + "    CONVERT(CHAR(19), getDate(), 126) idedhCont,\n"
                    + "    'Integração realizada logo após emissão' idexJust,\n"
                    + "    ISNULL(GTV.obs,'') complxObs,\n"
                    + "    Filiais.CNPJ emitCNPJ,\n"
                    + "    Filiais.InscEst emitIE,\n"
                    + "    '' emitIEST,\n"
                    + "    Filiais.RazaoSocial emitxNome,\n"
                    + "    Filiais.Descricao emitxFant,\n"
                    + "    Filiais.Endereco emitenderEmitxLgr,\n"
                    + "    '' emitenderEmitnro,\n"
                    //                    + "    '' emitenderEmitxCpl,\n"
                    + "    Filiais.Bairro emitenderEmitxBairro,\n"
                    + "    (SELECT TOP 1 CONVERT(BigInt, Municipios.CodIBGE) FROM Municipios WHERE Municipios.Nome = Filiais.Cidade) emitenderEmitcMun,\n"
                    + "    Filiais.Cidade emitenderEmitxMun,\n"
                    + "    Filiais.CEP emitenderEmitCEP,\n"
                    + "    Filiais.UF emitenderEmitUF,\n"
                    + "    Filiais.Fone emitenderEmitfone,\n"
                    + "    Rem.CGC remCNPJ,\n"
                    + "    Rem.CPF remCPF,\n"
                    + "    CASE WHEN Rem.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN Rem.IE <> 'NAO CONTRIBUINTE' THEN Rem.IE\n"
                    + "    ELSE '' END\n"
                    + "    END remIE,\n"
                    + "    Rem.Nome remxNome,\n"
                    + "    Rem.NRed remxFant,\n"
                    + "    Rem.fone1 remfone,\n"
                    + "    Rem.ende remenderRemexLgr,\n"
                    + "    '' remenderRemenro,\n"
                    //                    + "    '' remenderRemexCpl,\n"
                    + "    Rem.bairro remenderRemexBairro,\n"
                    + "    CONVERT(bigint, Rem.CodCidade) remenderRemecMun,\n"
                    + "    Rem.cidade remenderRemexMun,\n"
                    + "    Rem.CEP remenderRemeCEP,\n"
                    + "    Rem.Estado remenderRemeUF,\n"
                    + "    '1058' remenderRemecPais,\n"
                    + "    'Brasil' remenderRemexPais,\n"
                    + "    Rem.email rememail,\n"
                    + "    Dest.CGC destCNPJ,\n"
                    + "    Dest.CPF destCPF,\n"
                    + "    CASE WHEN Dest.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN Dest.IE <> 'NAO CONTRIBUINTE' THEN Dest.IE\n"
                    + "    ELSE '' END\n"
                    + "    END destIE,\n"
                    + "    Dest.Nome destxNome,\n"
                    + "    Dest.NRed destxFant,\n"
                    + "    Dest.fone1 destfone,\n"
                    + "    '' destISUF,\n"
                    + "    Dest.ende destenderDestxLgr,\n"
                    + "    '' destenderDestnro,\n"
                    //                    + "    '' destenderDestxCpl,\n"
                    + "    Dest.bairro destenderDestxBairro,\n"
                    + "    CONVERT(bigint, Dest.CodCidade) destenderDestcMun,\n"
                    + "    Dest.cidade destenderDestxMun,\n"
                    + "    Dest.CEP destenderDestCEP,\n"
                    + "    Dest.Estado destenderDestUF,\n"
                    + "    '1058' destenderDestcPais,\n"
                    + "    'Brasil' destenderDestxPais,\n"
                    + "    Dest.email destemail\n"
                    + "    \n"
                    + "FROM \n"
                    + "    Rt_Guias\n"
                    + "LEFT JOIN\n"
                    + "    CxfGuias ON CxfGuias.Guia = Rt_Guias.Guia  AND CxfGuias.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    + "    TesSaidas ON TesSaidas.Guia = Rt_Guias.Guia  AND TesSaidas.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    + "    Rotas ON Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "LEFT JOIN\n"
                    + "    Rt_Perc ON Rt_Perc.Sequencia = Rt_Guias.Sequencia AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN\n"
                    + "    Filiais ON Filiais.CodFil = Rotas.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    GTV ON GTV.CodFil = Rotas.CodFil AND  GTV.Guia = Rt_Guias.Guia AND GTV.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    + "    OS_Vig ON OS_Vig.OS = GTV.OS AND  OS_Vig.CodFil = GTV.CodFil \n"
                    + "LEFT JOIN\n"
                    + "    Clientes ON Clientes.Codigo = OS_Vig.Cliente AND Clientes.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    Clientes TomaTerceiro ON TomaTerceiro.Codigo  = OS_Vig.CliFat AND TomaTerceiro.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    Clientes Rem ON Rem.Codigo = CASE WHEN Rt_Perc.ER = 'E' THEN OS_Vig.CliDst -- ent os_vig clidst\n"
                    + "    ELSE Rt_Perc.CodCli1 END AND Rem.CodFil = Rt_Perc.CodFil -- rec codcli1\n"
                    + "LEFT JOIN\n"
                    + "    Clientes Dest ON Dest.Codigo = CASE WHEN Rt_Perc.ER = 'E' THEN Rt_Perc.CodCli1 -- ent codcli1\n"
                    + "    ELSE Rt_Perc.CodCli2 END AND Dest.CodFil = Rt_Perc.CodFil -- rec codcli2\n"
                    + "LEFT JOIN\n"
                    + "    Paramet ON Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "WHERE \n"
                    + "    Rt_Guias.Guia = " + guia + " AND Rt_Guias.Serie = " + serie);
        }
    }

    public Rt_Guias getGuiaSerieForRpv(String rpv, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "SELECT CAST(Guia AS BIGINT) Guia, Serie FROM RPV WHERE RPV = ? AND flag_excl <> '*'";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rpv);
            consulta.select();

            Rt_Guias Retorno = new Rt_Guias();

            while (consulta.Proximo()) {
                Retorno = new Rt_Guias();
                Retorno.setGuia(consulta.getString("Guia"));
                Retorno.setSerie(consulta.getString("Serie"));
            }

            return Retorno;

        } catch (Exception e) {
            throw new Exception("GTVeDao.getDetGTV - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public iblCTeOS getDataNfseVr(String guia, String serie, String praca, Persistencia persistencia) throws Exception {
        String sql = "";
        iblCTeOS retorno = new iblCTeOS();

        try {
            sql = "DECLARE @NumeroNota AS INT;\n"
                    + " DECLARE @Praca AS INT;\n"
                    + " SET @NumeroNota = ?;\n"
                    + " SET @Praca      = ?;\n"
                    + "\n"
                    + " Select Nfiscal.*, Clientes.*, XMLNFSE.Dt_Envio, XMLNFSE.Hr_Envio, XMLNFSE.Dt_Retorno, XMLNFSE.Hr_Retorno, XMLNFSE.Serie serieRPS, XMLNFSE.Numero NumeroRPS, \n"
                    + " Filiais.Cidade CidFil, Filiais.CNPJ CNPJFil, Filiais.InscMunic IMFil, Filiais.Endereco EndFil, Filiais.Bairro BairroFil, Filiais.UF UFFil, Filiais.Fone FoneFil, Filiais.RazaoSocial RazaoSocialFil, \n"
                    + " Filiais.Cep CEPFIL, Filiais.Email EmailFil, ht_NF.CFOP TipoPosto, FatCFOP.Descricao DescrItem, NFiscal.Valor Valor_Un, Nfiscal.Valor Valor_Tot, \n"
                    + " HT_NF.CFOP, Substring(HT_NF.mensagem,1,100) Mensagem, Substring(HT_NF.Obs1,1,100) Obs1, Substring(HT_NF.obs2,1,100) Obs2, \n"
                    + " Municipios.CodIBGE, Municipios.CodSRF, Municipios.UF UFMunic, Municipios.Nome NomeMunic,\n"
                    + " STUFF((Select '|' + Descricao \n"
                    + "        from NFItens \n"
                    + "        where NFItens.Praca   = @Praca\n"
                    + "        and   NFItens.Numero  = @NumeroNota \n"
                    + "        and   NFItens.Serie  = ? \n"
                    + "        Order by NFItens.TipoPosto \n"
                    + "        For XML PATH('')),1,1,'') DescricaoServicos,\n"
                    + " STUFF((Select '|' + CFOPDescr \n"
                    + "        from HT_NF \n"
                    + "        where HT_NF.Codigo  = Nfiscal.CodHist\n"
                    + "        and   HT_NF.Codfil  = Nfiscal.CodFil \n"
                    + "        Order by HT_NF.Descricao \n"
                    + "        For XML PATH('')),1,1,'') HistoricoServicos,\n"
                    + " STUFF((Select '|' + Descr_Linh \n"
                    + "        from HTLInhas \n"
                    + "        where HTLInhas.Codigo  = Nfiscal.CodHist\n"
                    + "        and   HTLInhas.Codfil  = Nfiscal.CodFil \n"
                    + "        Order by HTLInhas.Linha \n"
                    + "        For XML PATH('')),1,1,'') DescrLinhas\n"
                    + " From NFiscal \n"
                    + " LEFT JOIN XMLNFSE \n"
                    + "   ON NFiscal.Numero = XMLNFSE.Numero\n"
                    + "  AND NFiscal.Serie  = XMLNFSE.Serie\n"
                    + "  AND NFiscal.Praca  = XMLNFSE.Praca\n"
                    + " Left Join Filiais on Nfiscal.CodFil = Filiais.CodFil \n"
                    + " left Join FatISSGrp  on FatISSGrp.Codigo  = NFiscal.AgrupISS \n"
                    + " left Join Municipios on Municipios.Codigo = FatISSGrp.CodMunic \n"
                    + " Left Join HT_NF on NFiscal.CodHist = Ht_NF.Codigo \n"
                    + "                 and NFiscal.CodFil  = Ht_NF.CodFil \n"
                    + " Left Join FatCFOP   on FatCFOP.Codigo = HT_NF.CFOP \n"
                    + " Left Join Clientes on NFiscal.CliFat  = Clientes.Codigo \n"
                    + "                   and NFiscal.CodFil  = Clientes.CodFil \n"
                    + " where NFiscal.Praca  = @Praca\n"
                    + " and   NFiscal.Numero = @NumeroNota\n"
                    + " and   NFiscal.Serie  = ?\n"
                    + " Order by NFiscal.Praca, NFiscal.Data";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setInt(Integer.parseInt(guia));
            consulta.setInt(Integer.parseInt(praca));
            consulta.setString(serie);
            consulta.setString(serie);

            consulta.select();

            if (consulta.Proximo()) {
                retorno = new iblCTeOS();

                retorno.setNfeRetorno(consulta.getString("NFERetorno"));
                
                // Filial
                iblCTeOS_Cliente cliente = new iblCTeOS_Cliente();
                cliente.setCNPJ(consulta.getString("CNPJFil"));
                cliente.setCPF("");

                iblCTeOS_ClienteEnder endereco = new iblCTeOS_ClienteEnder();
                endereco.setxLgr(consulta.getString("EndFil"));
                endereco.setFone(consulta.getString("FoneFil"));
                endereco.setNro("");
                endereco.setUF(consulta.getString("UFFil"));
                endereco.setxMun(consulta.getString("CidFil"));
                endereco.setxBairro(consulta.getString("BairroFil"));
                endereco.setCEP(consulta.getString("CEPFIL"));

                cliente.setEnder(endereco);

                cliente.setIE(consulta.getString("IMFil"));
                cliente.setxFant(consulta.getString("NRed"));
                cliente.setxNome(consulta.getString("RazaoSocialFil"));

                retorno.setEmit(cliente);

                // Tomador
                iblCTeOS_Cliente clienteToma = new iblCTeOS_Cliente();
                clienteToma.setCNPJ(consulta.getString("CGC"));
                clienteToma.setCPF("");
                clienteToma.setIE(consulta.getString("IE"));

                endereco = new iblCTeOS_ClienteEnder();
                endereco.setxLgr(consulta.getString("Ende"));
                endereco.setFone(consulta.getString("Fone1"));
                endereco.setNro("");
                endereco.setUF(consulta.getString("Estado"));
                endereco.setxMun(consulta.getString("Cidade"));
                endereco.setxBairro(consulta.getString("Bairro"));
                endereco.setCEP(consulta.getString("CEP"));

                clienteToma.setEnder(endereco);

                clienteToma.setxFant(consulta.getString("NRed"));
                clienteToma.setxNome(consulta.getString("Nome"));

                retorno.setToma(clienteToma);

                retorno.setcMunIni(consulta.getString("CidCob"));
                retorno.setcMunFin(consulta.getString("UFCob"));

                retorno.setxObs(consulta.getString("DescricaoServicos"));

                retorno.setCFOP(consulta.getString("CFOP"));
                retorno.setCFOPDescr(consulta.getString("CFOp_Descr"));

                retorno.setValorTotal(consulta.getString("Valor_Un"));
                retorno.setValorUnitario(consulta.getString("Valor_Tot"));

                retorno.setvPIS(consulta.getString("PIS"));
                retorno.setvCOFINS(consulta.getString("COFINS"));
                retorno.setvIR(consulta.getString("IRRF"));
                retorno.setvCSLL(consulta.getString("CSL"));
                retorno.setvINSS(consulta.getString("INSS"));
                retorno.setvISSQN(consulta.getString("ISS"));
                retorno.setAliquota(consulta.getString("AliqISS"));
                retorno.setvOutrasRet("0");
                retorno.setDesconto(consulta.getString("Descontos"));

                retorno.setValor(consulta.getString("Valor"));
                retorno.setValorLiq(consulta.getString("ValorLiq"));
                
                retorno.setHistorico(consulta.getString("HistoricoServicos"));
                retorno.setDescrLinhas(consulta.getString("DescrLinhas"));
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("GTVeDao.getDataNfseVr - " + e.getMessage() + "\r\n"
                    + sql);
        }

    }

    public XMLNFE getDataNfseVrXml(String guia, String serie, String praca, Persistencia persistencia) throws Exception {
        String sql = "";
        XMLNFE retorno = new XMLNFE();

        try {
            sql = "DECLARE @NumeroNota AS INT;\n"
                    + " DECLARE @Praca AS INT;\n"
                    + " SET @NumeroNota = ?;\n"
                    + " SET @Praca      = ?;\n"
                    + "\n"
                    + " Select Nfiscal.*, Clientes.*, XMLNFSE.Dt_Envio, XMLNFSE.Hr_Envio, XMLNFSE.Dt_Retorno, XMLNFSE.Hr_Retorno, XMLNFSE.Serie serieRPS, XMLNFSE.Numero NumeroRPS, \n"
                    + " Filiais.Cidade CidFil, Filiais.CNPJ CNPJFil, Filiais.InscMunic IMFil, Filiais.Endereco EndFil, Filiais.Bairro BairroFil, Filiais.UF UFFil, Filiais.Fone FoneFil, Filiais.RazaoSocial RazaoSocialFil, \n"
                    + " Filiais.Cep CEPFIL, Filiais.Email EmailFil, ht_NF.CFOP TipoPosto, FatCFOP.Descricao DescrItem, NFiscal.Valor Valor_Un, Nfiscal.Valor Valor_Tot, \n"
                    + " HT_NF.CFOP, Substring(HT_NF.mensagem,1,100) Mensagem, Substring(HT_NF.Obs1,1,100) Obs1, Substring(HT_NF.obs2,1,100) Obs2, \n"
                    + " Municipios.CodIBGE, Municipios.CodSRF, Municipios.UF UFMunic, Municipios.Nome NomeMunic,\n"
                    + " STUFF((Select '|' + Descricao \n"
                    + "        from NFItens \n"
                    + "        where NFItens.Praca   = @Praca\n"
                    + "        and   NFItens.Numero  = @NumeroNota \n"
                    + "        and   NFItens.Serie  = ? \n"
                    + "        Order by NFItens.TipoPosto \n"
                    + "        For XML PATH('')),1,1,'') DescricaoServicos\n"
                    + " From NFiscal \n"
                    + " LEFT JOIN XMLNFSE \n"
                    + "   ON NFiscal.Numero = XMLNFSE.Numero\n"
                    + "  AND NFiscal.Serie  = XMLNFSE.Serie\n"
                    + "  AND NFiscal.Praca  = XMLNFSE.Praca\n"
                    + " Left Join Filiais on Nfiscal.CodFil = Filiais.CodFil \n"
                    + " left Join FatISSGrp  on FatISSGrp.Codigo  = NFiscal.AgrupISS \n"
                    + " left Join Municipios on Municipios.Codigo = FatISSGrp.CodMunic \n"
                    + " Left Join HT_NF on NFiscal.CodHist = Ht_NF.Codigo \n"
                    + "                 and NFiscal.CodFil  = Ht_NF.CodFil \n"
                    + " Left Join FatCFOP   on FatCFOP.Codigo = HT_NF.CFOP \n"
                    + " Left Join Clientes on NFiscal.CliFat  = Clientes.Codigo \n"
                    + "                   and NFiscal.CodFil  = Clientes.CodFil \n"
                    + " where NFiscal.Praca  = @Praca\n"
                    + " and   NFiscal.Numero = @NumeroNota\n"
                    + " and   NFiscal.Serie  = ?\n"
                    + " Order by NFiscal.Praca, NFiscal.Data";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setInt(Integer.parseInt(guia));
            consulta.setInt(Integer.parseInt(praca));
            consulta.setString(serie);
            consulta.setString(serie);
            consulta.select();

            iblCTeOS_infGTV infGTV = new iblCTeOS_infGTV();

            if (consulta.Proximo()) {
                retorno = new XMLNFE();

                retorno.setDt_Nota(consulta.getString("Data"));
                retorno.setChaveNFE(consulta.getString("NFEChave"));
                retorno.setDt_Envio(consulta.getString("Dt_Envio"));
                retorno.setHr_Envio(consulta.getString("Hr_Envio"));

                retorno.setDt_Retorno(consulta.getString("Dt_Retorno"));
                retorno.setHr_Retorno(consulta.getString("Hr_Retorno"));

                retorno.setNumero(consulta.getString("NumeroRPS"));
                retorno.setSerie(consulta.getString("serieRPS"));
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("GTVeDao.getDataNfseVr - " + e.getMessage() + "\r\n"
                    + sql);
        }

    }

    public iblCTeOS getGTVeIntegraIBL(String guia, String serie, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = " DECLARE @DataHoraEmiss AS VARCHAR(30);\n"
                    + " DECLARE @DataHoraEmissSimples AS VARCHAR(30);\n"
                    + " SET @DataHoraEmiss = (SELECT CONVERT(CHAR(10), CONVERT(date, GETDATE()))+'T'+ISNULL(LEFT(CONVERT(CHAR(10), CONVERT(time, GETDATE())), 5), '00:00')+':00'); \n"
                    + " SET @DataHoraEmissSimples = (SELECT CONVERT(CHAR(10), CONVERT(date, GETDATE()))+'T'+ISNULL(LEFT(CONVERT(CHAR(10), CONVERT(time, GETDATE())), 5), '00:00') + '00:00'); \n"
                    + " SELECT TOP 1\n"
                    + "    LEFT(CONVERT(NVARCHAR, GETDATE(), 12), 4) ideIdAnoMes,\n"
                    + "    Paramet.FusoHorarioSEFAZ,\n"
                    + "    Filiais.UF ideCUF, \n"
                    + "    '2606' ideCFOP,\n"
                    + "    'Transporte de Valores' idenatOp,\n"
                    + "    '64' idemod,\n"
                    + "    Rt_Guias.Serie ideserie,\n"
                    + "    CAST(Rt_Guias.Guia AS BIGINT) gtvGuia,\n"
                    + "    Rt_Guias.Serie gtvSerie,\n"
                    + "    GTV.DtGeracao AS dEmiGTV,\n"
                    + "    CONVERT(bigint, Rt_Guias.guia) idenCT,\n"
                    //                    + "    CONVERT(CHAR(10), Rt_Guias.data, 126)+'T'+Rt_Guias.Hora+':00' idedhEmi,\n"
                    + "    NFiscal.Data idedhEmi,\n"
                    + "    '1' idetpImp,\n"
                    + "    '1' idetpEmis,\n"
                    + "    '' idecDV,\n"
                    + "    '1' idetpAmb,\n"
                    + "    '4' idetpCTe,\n"
                    + "    '1' ideverProc,\n"
                    + "    CONVERT(bigint, Clientes.CodCidade) idecMunEnv,\n"
                    + "    Clientes.Cidade idexMunEnv,\n"
                    + "    Clientes.Estado ideUFEnv,\n"
                    + "    '06' idemodal,\n"
                    + "    '9' idetpServ,\n"
                    + "    CASE WHEN Clientes.IE = 'ISENTO' THEN '2'\n"
                    + "    WHEN Clientes.IE = 'NAO CONTRIBUINTE' THEN '9'\n"
                    + "    WHEN Clientes.IE IS NOT NULL AND Clientes.IE <> '' THEN '1'\n"
                    + "    ELSE '9' END ideindIEToma,\n"
                    + "    CASE WHEN Rt_Perc.ER = 'R' THEN \n"
                    + "        CONVERT(CHAR(10), Rotas.data, 126)+'T'+ISNULL(NULLIF(Rt_Perc.HrSaida,''),'00:00')+':00' \n"
                    + "    ELSE \n"
                    + "        CASE WHEN TesSaidas.Dt_Alter IS NOT NULL THEN \n"
                    + "                CONVERT(CHAR(10), TesSaidas.Dt_Alter, 126)+'T'+ISNULL(TesSaidas.Hr_Alter, '00:00')+':00'\n"
                    + "             WHEN CxFGuias.DtSai IS NOT NULL THEN\n"
                    + "                CONVERT(CHAR(10), CxFGuias.DtSai, 126)+'T'+ISNULL(CxFGuias.HrSai, '00:00')+':00'\n"
                    + "             ELSE \n"
                    + "                @DataHoraEmiss\n"
                    + "        END\n"
                    + "    END idedhSaidaOrig,"
                    /*+ "    CASE WHEN Rt_Perc.ER = 'R' THEN \n"
                    + "        CONVERT(CHAR(10), Rotas.data, 126)+'T'+ISNULL(NULLIF(Rt_Perc.HrSaida,''),'00:00')+':00' \n"
                    + "    ELSE \n"
                    + "        CASE WHEN TesSaidas.Dt_Alter IS NOT NULL THEN \n"
                    + "            CONVERT(CHAR(10), TesSaidas.Dt_Alter, 126)+'T'+ISNULL(TesSaidas.Hr_Alter, '00:00')+':00'\n"
                    + "        ELSE \n"
                    + "            CONVERT(CHAR(10), CxFGuias.DtSai, 126)+'T'+ISNULL(CxFGuias.HrSai, '00:00')+':00'\n"
                    + "        END\n"
                    + "    END idedhSaidaOrig,\n"*/
                    + "    CASE WHEN Rt_Perc.ER = 'E' THEN\n"
                    + "        CASE WHEN Rt_Perc.HrCheg IS NOT NULL AND Rotas.data IS NOT NULL THEN CONVERT(CHAR(10), Rotas.data, 126)+'T'+Rt_Perc.HrCheg+':00' ELSE @DataHoraEmiss END \n"
                    + "    ELSE \n"
                    + "        ISNULL((SELECT TOP 1 CASE WHEN RtP.HrCheg IS NOT NULL AND Rt.data IS NOT NULL THEN CONVERT(CHAR(10), Rt.data, 126)+'T'+RtP.HrCheg+':00' ELSE @DataHoraEmiss END FROM Rt_perc RtP LEFT JOIN Rotas Rt ON Rt.Sequencia = RtP.Sequencia WHERE RtP.Sequencia = CxfGuias.SeqRota AND RtP.Hora1 = CxfGuias.Hora1 AND RtP.Flag_Excl <> '*'), @DataHoraEmiss) \n"
                    + "    END idedhChegadaDest,\n"
                    + "    CASE WHEN Rt_Perc.ER = 'R' THEN '0'\n"
                    + "    ELSE '1' END tomatoma, \n"
                    + "    '4' tomaTerceirotoma,\n"
                    + "    TomaTerceiro.CGC tomaTerceiroCNPJ,\n"
                    + "    ISNULL(NULLIF(TomaTerceiro.CPF,''),'00000000000') tomaTerceiroCPF,\n"
                    + "    CASE WHEN TomaTerceiro.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN TomaTerceiro.IE <> 'NAO CONTRIBUINTE' THEN TomaTerceiro.IE\n"
                    + "    ELSE '' END\n"
                    + "    END tomaTerceiroIE,\n"
                    + "    TomaTerceiro.Nome tomaTerceiroxNome,\n"
                    + "    TomaTerceiro.NRed tomaTerceiroxFant,\n"
                    + "    TomaTerceiro.fone1 tomaTerceirofone,\n"
                    + "    TomaTerceiro.ende tomaTerceiroenderTomaxLgr,\n"
                    + "    '' tomaTerceiroenderTomanro,\n"
                    //                    + "    '' tomaTerceiroenderTomaxCpl,\n"
                    + "    TomaTerceiro.bairro tomaTerceiroenderTomaxBairro,\n"
                    + "    CONVERT(bigint, TomaTerceiro.CodCidade) tomaTerceiroenderTomacMun,\n"
                    + "    TomaTerceiro.cidade tomaTerceiroenderTomaxMun,\n"
                    + "    TomaTerceiro.CEP tomaTerceiroenderTomaCEP,\n"
                    + "    TomaTerceiro.Estado tomaTerceiroenderTomaUF,\n"
                    + "    '1058' tomaTerceiroenderTomacPais,\n"
                    + "    'Brasil' tomaTerceiroenderTomaxPais,\n"
                    + "    TomaTerceiro.email tomaTerceiroemail,\n"
                    + "    CONVERT(CHAR(19), getDate(), 126) idedhCont,\n"
                    + "    'Integração realizada logo após emissão' idexJust,\n"
                    + "    ISNULL(GTV.obs,'') complxObs,\n"
                    + "    Filiais.CNPJ emitCNPJ,\n"
                    + "    Filiais.InscEst emitIE,\n"
                    + "    '' emitIEST,\n"
                    + "    Filiais.RazaoSocial emitxNome,\n"
                    + "    Filiais.Descricao emitxFant,\n"
                    + "    Filiais.Endereco emitenderEmitxLgr,\n"
                    + "    '' emitenderEmitnro,\n"
                    //                    + "    '' emitenderEmitxCpl,\n"
                    + "    Filiais.Bairro emitenderEmitxBairro,\n"
                    + "    (SELECT TOP 1 CONVERT(BigInt, Municipios.CodIBGE) FROM Municipios WHERE Municipios.Nome = Filiais.Cidade) emitenderEmitcMun,\n"
                    + "    Filiais.Cidade emitenderEmitxMun,\n"
                    + "    Filiais.CEP emitenderEmitCEP,\n"
                    + "    Filiais.UF emitenderEmitUF,\n"
                    + "    Filiais.Fone emitenderEmitfone,\n"
                    + "    Rem.CGC remCNPJ,\n"
                    + "    Rem.CPF remCPF,\n"
                    + "    CASE WHEN Rem.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN Rem.IE <> 'NAO CONTRIBUINTE' THEN Rem.IE\n"
                    + "    ELSE '' END\n"
                    + "    END remIE,\n"
                    + "    Rem.Nome remxNome,\n"
                    + "    Rem.NRed remxFant,\n"
                    + "    Rem.fone1 remfone,\n"
                    + "    Rem.ende remenderRemexLgr,\n"
                    + "    '' remenderRemenro,\n"
                    //                    + "    '' remenderRemexCpl,\n"
                    + "    Rem.bairro remenderRemexBairro,\n"
                    + "    CONVERT(bigint, Rem.CodCidade) remenderRemecMun,\n"
                    + "    Rem.cidade remenderRemexMun,\n"
                    + "    Rem.CEP remenderRemeCEP,\n"
                    + "    Rem.Estado remenderRemeUF,\n"
                    + "    '1058' remenderRemecPais,\n"
                    + "    'Brasil' remenderRemexPais,\n"
                    + "    Rem.email rememail,\n"
                    + "    Dest.CGC destCNPJ,\n"
                    + "    Dest.CPF destCPF,\n"
                    + "    CASE WHEN Dest.IE = 'ISENTO' THEN 'ISENTO'\n"
                    + "    ELSE CASE WHEN Dest.IE <> 'NAO CONTRIBUINTE' THEN Dest.IE\n"
                    + "    ELSE '' END\n"
                    + "    END destIE,\n"
                    + "    Dest.Nome destxNome,\n"
                    + "    Dest.NRed destxFant,\n"
                    + "    Dest.fone1 destfone,\n"
                    + "    '' destISUF,\n"
                    + "    Dest.ende destenderDestxLgr,\n"
                    + "    '' destenderDestnro,\n"
                    //                    + "    '' destenderDestxCpl,\n"
                    + "    Dest.bairro destenderDestxBairro,\n"
                    + "    CONVERT(bigint, Dest.CodCidade) destenderDestcMun,\n"
                    + "    Dest.cidade destenderDestxMun,\n"
                    + "    Dest.CEP destenderDestCEP,\n"
                    + "    Dest.Estado destenderDestUF,\n"
                    + "    '1058' destenderDestcPais,\n"
                    + "    'Brasil' destenderDestxPais,\n"
                    + "    Dest.email destemail,\n"
                    + "    @DataHoraEmissSimples dtHoraEmisSimples,\n"
                    + "    Rotas.CodFil,\n"
                    + "    Rt_perc.Pedido,\n"
                    + "    NFiscal.Valor vTPrest,\n"
                    + "    NFiscal.ValorLiq vRec,\n"
                    + "    NFiscal.Valor vBC,\n"
                    + "    STUFF((SELECT '|' + HTLinhas.Descr_Linh\n"
                    + "                    FROM HtLinhas (nolock)\n"
                    + "                    WHERE HtLinhas.Codigo = NFiscal.CodHist\n"
                    + "                    AND HtLinhas.CodFil   = Rotas.CodFil\n"
                    + "                    ORDER BY HtLinhas.Linha\n"
                    + "                    For XML PATH('')),1,1,'') xObs,\n"
                    + "    NFiscal.AliqICMS pICMS,\n"
                    + "    NFiscal.ICMS vICMS,\n"
                    + "    NFiscal.PIS vPIS,\n"
                    + "    NFiscal.cofins vCOFINS,\n"
                    + "    ISNULL(NFiscal.IRPJ,0) + ISNULL(NFiscal.IRRF,0) vIR,\n"
                    + "    NFiscal.CSL vICSLL,\n"
                    + "    NFiscal.NfeChave\n"
                    + "    \n"
                    + "FROM \n"
                    + "    Rt_Guias\n"
                    + "LEFT JOIN\n"
                    + "    CxfGuias ON CxfGuias.Guia = Rt_Guias.Guia  AND CxfGuias.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    + "    TesSaidas ON TesSaidas.Guia = Rt_Guias.Guia  AND TesSaidas.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    + "    Rotas ON Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "LEFT JOIN\n"
                    + "    Rt_Perc ON Rt_Perc.Sequencia = Rt_Guias.Sequencia AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN\n"
                    + "    Filiais ON Filiais.CodFil = Rotas.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    GTV ON GTV.CodFil = Rotas.CodFil AND  GTV.Guia = Rt_Guias.Guia AND GTV.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN\n"
                    //+ "    OS_Vig ON OS_Vig.OS = GTV.OS AND  OS_Vig.CodFil = GTV.CodFil \n"
                    + "      OS_Vig ON OS_Vig.OS = RT_Guias.OS AND  OS_Vig.CodFil = RT_Guias.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    Clientes ON Clientes.Codigo = OS_Vig.Cliente AND Clientes.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    Clientes TomaTerceiro ON TomaTerceiro.Codigo = OS_Vig.CliFat AND TomaTerceiro.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    Clientes Rem ON Rem.Codigo = CASE WHEN Rt_Perc.ER = 'E' THEN OS_Vig.CliDst -- ent os_vig clidst\n"
                    + "    ELSE Rt_Perc.CodCli1 END AND Rem.CodFil = Rt_Perc.CodFil -- rec codcli1\n"
                    + "LEFT JOIN\n"
                    + "    Clientes Dest ON Dest.Codigo = CASE WHEN Rt_Perc.ER = 'E' THEN Rt_Perc.CodCli1 -- ent codcli1\n"
                    + "    ELSE Rt_Perc.CodCli2 END AND Dest.CodFil = Rt_Perc.CodFil -- rec codcli2\n"
                    + "LEFT JOIN\n"
                    + "    Paramet ON Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "LEFT JOIN\n"
                    + "    FatTvPlan ON FatTvPlan.CodFil = Rotas.CodFil AND  FatTvPlan.Guia = Rt_Guias.Guia AND FatTvPlan.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN \n"
                    + "    FatTVFechaNF ON FatTvPlan.Numero = FatTVFechaNF.Numero AND FatTvPlan.SeqNF = FatTVFechaNF.SeqNF AND FatTvPlan.CodFil = FatTVFechaNF.CodFil\n"
                    + "LEFT JOIN \n"
                    + "     NFiscal ON FatTVFechaNF.NF = NFiscal.Numero AND FatTVFechaNF.Praca = NFiscal.Praca AND FatTVFechaNF.Serie = NFiscal.Serie\n"
                    + "WHERE \n"
                    + "    Rt_Guias.Guia = ? AND Rt_Guias.Serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            iblCTeOS retorno = null;

            iblCTeOS_Cliente cliEmi = new iblCTeOS_Cliente(),
                    cliToma = new iblCTeOS_Cliente(),
                    cliNfRem = new iblCTeOS_Cliente(),
                    cliNfDest = new iblCTeOS_Cliente();

            iblCTeOS_ClienteEnder enderEmit = new iblCTeOS_ClienteEnder(),
                    enderToma = new iblCTeOS_ClienteEnder(),
                    enderNfRem = new iblCTeOS_ClienteEnder(),
                    enderNfDest = new iblCTeOS_ClienteEnder();

            iblCTeOS_infGTV infGTV = new iblCTeOS_infGTV();

            if (consulta.Proximo()) {
                retorno = new iblCTeOS();

                retorno.setCFOP(consulta.getString("ideCFOP"));
                retorno.setChaveCTEOS(consulta.getString("NfeChave"));
                retorno.setDhEmi(consulta.getString("idedhEmi").split(" ")[0]);
                retorno.setNatOp(consulta.getString("idenatOp"));
                retorno.setSerie(consulta.getString("ideserie"));
                retorno.setUFFin(consulta.getString("destenderDestUF"));
                retorno.setUFIni(consulta.getString("remenderRemeUF"));
                retorno.setnCT(consulta.getString("idenCT"));
                retorno.setcMunFin(consulta.getString("destenderDestcMun"));
                retorno.setxMunFin(consulta.getString("destenderDestxMun"));
                retorno.setcMunIni(consulta.getString("remenderRemecMun"));
                retorno.setxMunIni(consulta.getString("remenderRemexMun"));

                retorno.setpICMS(consulta.getString("pICMS"));
                retorno.setvBC(consulta.getString("vBC"));
                retorno.setvCOFINS(consulta.getString("vCOFINS"));
                retorno.setvCSLL(consulta.getString("vICSLL"));
                retorno.setvICMS(consulta.getString("vICMS"));
                retorno.setvIR(consulta.getString("vIR"));
                retorno.setvRec(consulta.getString("vRec"));
                retorno.setvPIS(consulta.getString("vPIS"));
                retorno.setvTPrest(consulta.getString("vTPrest"));
                retorno.setxObs(consulta.getString("xObs"));

                // Cliente Origem
                cliEmi.setCNPJ(consulta.getString("emitCNPJ"));
                cliEmi.setCPF("");
                cliEmi.setIE(consulta.getString("emitIE"));
                cliEmi.setxFant(consulta.getString("emitxFant"));
                cliEmi.setxNome(consulta.getString("emitxNome"));
                // >> Cliente Origem >> Endereço 
                enderEmit.setCEP(consulta.getString("emitenderEmitCEP"));
                enderEmit.setFone(consulta.getString("emitenderEmitfone"));
                enderEmit.setNro(consulta.getString("emitenderEmitnro"));
                enderEmit.setUF(consulta.getString("emitenderEmitUF"));
                enderEmit.setcMun(consulta.getString("emitenderEmitcMun"));
                enderEmit.setxBairro(consulta.getString("emitenderEmitxBairro"));
                enderEmit.setxLgr(consulta.getString("emitenderEmitxLgr"));
                enderEmit.setxMun(consulta.getString("emitenderEmitxMun"));

                cliEmi.setEnder(enderEmit);
                retorno.setEmit(cliEmi);

                // Cliente Toma
                cliToma.setCNPJ(consulta.getString("tomaTerceiroCNPJ"));
                cliToma.setCPF(consulta.getString("tomaTerceiroCPF"));
                cliToma.setIE(consulta.getString("tomaTerceiroIE"));
                cliToma.setxFant(consulta.getString("tomaTerceiroxFant"));
                cliToma.setxNome(consulta.getString("tomaTerceiroxNome"));
                // >> Cliente Origem >> Endereço 
                enderToma.setCEP(consulta.getString("tomaTerceiroenderTomaCEP"));
                enderToma.setFone(consulta.getString("tomaTerceirofone"));
                enderToma.setNro(consulta.getString("tomaTerceiroenderTomanro"));
                enderToma.setUF(consulta.getString("tomaTerceiroenderTomaUF"));
                enderToma.setcMun(consulta.getString("tomaTerceiroenderTomacMun"));
                enderToma.setxBairro(consulta.getString("tomaTerceiroenderTomaxBairro"));
                enderToma.setxLgr(consulta.getString("tomaTerceiroenderTomaxLgr"));
                enderToma.setxMun(consulta.getString("tomaTerceiroenderTomaxMun"));

                cliToma.setEnder(enderToma);
                retorno.setToma(cliToma);

                // Cliente Rem
                cliNfRem.setCNPJ(consulta.getString("remCNPJ"));
                cliNfRem.setCPF(consulta.getString("remCPF"));
                cliNfRem.setIE(consulta.getString("remIE"));
                cliNfRem.setxFant(consulta.getString("remxFant"));
                cliNfRem.setxNome(consulta.getString("remxNome"));
                // >> Cliente Rem >> Endereço 
                enderNfRem.setCEP(consulta.getString("remenderRemeCEP"));
                enderNfRem.setFone(consulta.getString("remfone"));
                enderNfRem.setNro(consulta.getString("remenderRemenro"));
                enderNfRem.setUF(consulta.getString("remenderRemeUF"));
                enderNfRem.setcMun(consulta.getString("remenderRemecMun"));
                enderNfRem.setxBairro(consulta.getString("remenderRemexBairro"));
                enderNfRem.setxLgr(consulta.getString("remenderRemexLgr"));
                enderNfRem.setxMun(consulta.getString("remenderRemexMun"));

                cliNfRem.setEnder(enderNfRem);

                // Cliente Dest
                cliNfDest.setCNPJ(consulta.getString("destCNPJ"));
                cliNfDest.setCPF(consulta.getString("destCPF"));
                cliNfDest.setIE(consulta.getString("destIE"));
                cliNfDest.setxFant(consulta.getString("destxFant"));
                cliNfDest.setxNome(consulta.getString("destxNome"));
                // >> Cliente Dest >> Endereço 
                enderNfDest.setCEP(consulta.getString("destenderDestCEP"));
                enderNfDest.setFone(consulta.getString("destfone"));
                enderNfDest.setNro(consulta.getString("destenderDestnro"));
                enderNfDest.setUF(consulta.getString("destenderDestUF"));
                enderNfDest.setcMun(consulta.getString("destenderDestcMun"));
                enderNfDest.setxBairro(consulta.getString("destenderDestxBairro"));
                enderNfDest.setxLgr(consulta.getString("destenderDestxLgr"));
                enderNfDest.setxMun(consulta.getString("destenderDestxMun"));

                cliNfDest.setEnder(enderNfDest);

                // InfGTV
                TGTVe.InfCte.DetGTV dtgGTV = getDetGTV(guia, serie, persistencia);

                if (dtgGTV.getInfVeiculo().size() > 0) {
                    infGTV.setPlaca(dtgGTV.getInfVeiculo().get(0).getPlaca());
                    infGTV.setUF(dtgGTV.getInfVeiculo().get(0).getUF().value());
                }

                infGTV.setSerie(consulta.getString("gtvSerie"));
                infGTV.setdEmi(consulta.getString("dEmiGTV").split(" ")[0]);
                infGTV.setnGTV(consulta.getString("gtvGuia"));
                infGTV.setqCarga(dtgGTV.getQCarga());

                // Nota Fiscal
                List<iblCTeOS_infGTVnotaFiscal> nFiscalList = new ArrayList<>();
                iblCTeOS_infGTVnotaFiscal nFiscal = new iblCTeOS_infGTVnotaFiscal();
                List<PedidoLog> pedidosLogList = getPedidoLog(consulta.getString("Pedido"), consulta.getString("CodFil"), persistencia);

                if (pedidosLogList.size() > 0) {
                    for (PedidoLog pedidosLogList1 : pedidosLogList) {
                        nFiscal = new iblCTeOS_infGTVnotaFiscal();

                        nFiscal.setDataEmissao(pedidosLogList1.getDt_Alter().split(" ")[0]);
                        nFiscal.setNnf(pedidosLogList1.getNF());
                        nFiscal.setPesoBruto(pedidosLogList1.getPeso());
                        nFiscal.setPesoLiquido(pedidosLogList1.getPeso());
                        nFiscal.setSerieNF(pedidosLogList1.getNFSerie().trim());
                        nFiscal.setChaveNF(pedidosLogList1.getNFeChave());
                        nFiscal.setVlrMerc(pedidosLogList1.getValor());
                        nFiscal.setVolumes(pedidosLogList1.getVolumes());

                        nFiscal.setRem(cliNfRem);
                        nFiscal.setDest(cliNfDest);
                        nFiscalList.add(nFiscal);
                    }
                } else {
                    nFiscal.setDataEmissao("");
                    nFiscal.setNnf("");
                    nFiscal.setPesoBruto("");
                    nFiscal.setPesoLiquido("");
                    nFiscal.setSerieNF("");
                    nFiscal.setChaveNF("");
                    nFiscal.setVlrMerc("");
                    nFiscal.setVolumes("");

                    nFiscal.setRem(cliNfRem);
                    nFiscal.setDest(cliNfDest);
                    nFiscalList.add(nFiscal);
                }

                // SET Lista NFiscal
                infGTV.setNotaFiscal(nFiscalList);

                // SET infGTV
                retorno.setInfGTV(infGTV);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("GTVeDao.getGTVeIntegraIBL - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<PedidoLog> getPedidoLog(String numeroPedido, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<PedidoLog> Retorno = new ArrayList<>();

            sql = "SELECT *, CAST(OCarga AS BIGINT) OCarga2 \n"
                    + "    FROM PedidoLog\n"
                    + "    WHERE Numero = ?\n"
                    + "    AND   CodFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(numeroPedido);
            consulta.setString(codFil.replace(".0", ""));
            consulta.select();

            PedidoLog pedidoLog;

            while (consulta.Proximo()) {
                pedidoLog = new PedidoLog();
                pedidoLog.setCodFil(consulta.getString("CodFil"));
                pedidoLog.setDt_Alter(consulta.getString("Dt_Alter"));
                pedidoLog.setDt_Excl(consulta.getString("Dt_Excl"));
                pedidoLog.setFlag_Excl(consulta.getString("Flag_Excl"));
                pedidoLog.setHr_Alter(consulta.getString("Hr_Alter"));
                pedidoLog.setHr_Excl(consulta.getString("Hr_Excl"));
                pedidoLog.setNF(consulta.getString("NF"));
                pedidoLog.setNFeChave(consulta.getString("NFeChave"));
                pedidoLog.setNFSerie(consulta.getString("NFSerie"));
                pedidoLog.setNumero(consulta.getString("Numero"));
                pedidoLog.setOCarga(consulta.getString("OCarga2"));
                pedidoLog.setOperExcl(consulta.getString("OperExcl"));
                pedidoLog.setOperador(consulta.getString("Operador"));
                pedidoLog.setPeso(consulta.getString("Peso"));
                pedidoLog.setProtocolo(consulta.getString("Protocolo"));
                pedidoLog.setValor(consulta.getString("Valor"));
                pedidoLog.setVolumes(consulta.getString("Volumes"));

                Retorno.add(pedidoLog);
            }

            return Retorno;

        } catch (Exception e) {
            throw new Exception("GTVeDao.getDetGTV - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }
}

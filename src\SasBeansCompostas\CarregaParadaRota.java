package SasBeansCompostas;

import SasBeans.Clientes;
import SasBeans.Rt_Perc;

/**
 *
 * <AUTHOR>
 */
public class CarregaParadaRota {

    private Rt_Perc rt_perc;
    private Clientes cliorigem;
    private Clientes clidestino;

    public Rt_Perc getRt_perc() {
        return rt_perc;
    }

    public void setRt_perc(Rt_Perc rt_perc) {
        this.rt_perc = rt_perc;
    }

    public Clientes getCliorigem() {
        return cliorigem;
    }

    public void setCliorigem(Clientes cliorigem) {
        this.cliorigem = cliorigem;
    }

    public Clientes getClidestino() {
        return clidestino;
    }

    public void setClidestino(Clientes clidestino) {
        this.clidestino = clidestino;
    }

}

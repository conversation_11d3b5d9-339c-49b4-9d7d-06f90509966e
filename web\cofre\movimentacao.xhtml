<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/cofres.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{cofre.Persistencia(login.pp, login.satellite)}"/>
            </f:metadata>
            <p:growl id="msgs"/>
            <header>
                <h:form id="cabecalho">
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-5" style="align-self: center;">
                                <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/> 
                                <h:outputText value="#{localemsgs.CofreInteligente}" class="cabecalho"/>
                            </div>

                            <div class="ui-grid-col-4" style="align-self: center; text-align: center;">
                                <h:outputText value="#{localemsgs.Data}: " class="cabecalho"/>
                                <h:outputText id="diaCofre" value="#{cofre.dataCofreStr}" converter="conversorDia" class="cabecalho"/>
                            </div>

                            <div class="ui-grid-col-3" style="align-self: center; text-align: center;">
                                <p:commandLink action="#{cofre.dataAnteriorMovimentacoes}" update="main:tabela cabecalho totais">
                                    <p:graphicImage url="../assets/img/botao_anterior.png" style="align-self: center;height: 40px"/>
                                </p:commandLink>

                                <p:datePicker id="calendario" styleClass="calendario" class="calendario" showIcon="true" 
                                              pattern="yyyy-MM-dd" locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{cofre.selecionarDataMovimentacoes}" update="main:tabela cabecalho totais" />
                                </p:datePicker>

                                <p:commandLink action="#{cofre.dataPosteriorMovimentacoes}" update="main:tabela cabecalho totais movimentos">
                                    <p:graphicImage url="../assets/img/botao_proximo.png" style="align-self: center;height: 40px"/>
                                </p:commandLink>
                            </div> 
                        </div>

                        <div class="ui-grid-row">
                            <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                <h:outputText value="#{cofre.cliente.codigo} - #{cofre.cliente.NRed}" rendered="#{cofre.cliente ne null}" styleClass="cabecalho" />
                                <div class="ui-grid-col-12">
                                    <h:outputText value="#{cofre.cliente.ende}, #{cofre.cliente.bairro}" rendered="#{cofre.cliente ne null}" styleClass="subcabecalho"/>
                                </div>
                                <div class="ui-grid-col-12">
                                    <h:outputText value="#{cofre.cliente.cidade}/#{cofre.cliente.estado} " rendered="#{cofre.cliente ne null}" styleClass="subcabecalho"/>
                                    <h:outputText value="#{cofre.cliente.CEP}" rendered="#{cofre.cliente ne null}" converter="conversorCEP" styleClass="subcabecalho"/>
                                </div>
                            </p:panel>
                        </div>
                    </div>
                </h:form>
            </header>
            <h:form id="main">
                <p:hotkey bind="e" update="movimentos msgs" actionListener="#{cofre.buttonAction}"/> 
                <div class="ui-grid ui-grid-responsive">
                    <div class="ui-grid-row">
                        <div class="ui-grid-col-12">
                            <p:panel style="display: inline;">
                                <p:dataTable id="tabela" value="#{cofre.allMovimentacoesPaginada}" paginator="true" rows="15" lazy="true"
                                             rowsPerPageTemplate="5,10,15, 20, 25"
                                             currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Movimentacoes}"
                                             paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                             var="detMovimento"
                                             resizableColumns="true" selectionMode="single" styleClass="tabelaMov"
                                             selection="#{cofre.movimentacaoSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:column headerText="#{localemsgs.Cofre}" style="width: 40px" exportable="#{cofre.cofre}">
                                        <h:outputText value="#{detMovimento.codCofre}" title="#{detMovimento.codCofre}">
                                            <f:convertNumber pattern="0000"/>
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Data}" style="width: 73px" exportable="#{cofre.data}">
                                        <h:outputText value="#{detMovimento.data}" title="#{detMovimento.data}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Hora}" style="width: 60px" exportable="#{cofre.hora}">
                                        <h:outputText value="#{detMovimento.hora}" title="#{detMovimento.hora}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Usuario}" style="width: 80px" exportable="#{cofre.nomeUsuario}">
                                        <h:outputText value="#{detMovimento.nomeUsuario}" title="#{detMovimento.nomeUsuario}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.ValorDeposito}" style="width: 200px" styleClass="celula-right" exportable="#{cofre.valorDeposito}">
                                        <h:outputText value="#{detMovimento.valorDeposito}" title="#{detMovimento.valorDeposito}" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.TipoDeposito}" style="width: 105px" exportable="#{cofre.tipoDeposito}">
                                        <h:outputText value="#{detMovimento.tipoDeposito}" title="#{detMovimento.tipoDeposito}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Status}" style="width: 70px" exportable="#{cofre.status}">
                                        <h:outputText value="#{detMovimento.status}" title="#{detMovimento.status}"/>
                                    </p:column>
                                </p:dataTable>
                            </p:panel>

                        </div>

                    </div>
                </div>

                <p:panel style="position: fixed; z-index: 1; right: 5px; top: 150px; background: transparent" id="botoes">
                    <div style="padding-bottom: 10px">
                        <p:commandLink title="#{localemsgs.Editar}" update="movimentos msgs" 
                                       actionListener="#{cofre.detalhesMovimentacao}">
                            <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                        </p:commandLink>
                    </div>
                    <div style=" top: 0px; right: 5px; position: fixed">
                        <p:commandLink title="#{localemsgs.Voltar}" oncomplete="PF('dlgOk').show();">
                            <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                        </p:commandLink>
                    </div>
                </p:panel>
                <p:draggable for="botoes" axis="y" opacity="0.3"/>
            </h:form>

            <h:form id="movimentos">
                <p:dialog widgetVar="dlgListarMovimentos" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.CofreInteligente}" style="color:#022a48" /> 
                    </f:facet>

                    <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                        <div style="float: left; width: 100%">
                            <div style="float: left; width: 20%">
                                <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                            </div>
                            <div style="float: left; width: 20%">
                                <p:outputLabel for="data1" value="#{localemsgs.DataInicial}: "/>
                                <p:spacer width="2px"/>
                                <p:datePicker id="data1" value="#{cofre.date1}" locale="#{localeController.getCurrentLocale()}"
                                              styleClass="calendarioMov"
                                              pattern="#{mascaras.getPadraoDataS()}" style="width: 90px;"/>
                            </div>
                            <div style="float: left; width: 20%">
                                <p:outputLabel for="data2" value="#{localemsgs.DataFinal}: "/>
                                <p:spacer width="2px"/>
                                <p:datePicker id="data2" value="#{cofre.date2}" locale="#{localeController.getCurrentLocale()}"
                                              styleClass="calendarioMov"
                                              pattern="#{mascaras.getPadraoDataS()}" style="width: 90px;"/>

                                <p:spacer width="5"/>

                                <p:commandLink title="#{localemsgs.Pesquisar}" update="tabelaMov msgs" action="#{cofre.atualizarListaMovimentacoes}">
                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>    
                                </p:commandLink>
                            </div>
                        </div>

                        <div style="float: left; width: 100%">
                            <div style="float: left; width: 80%;">
                                <h:outputText value="#{localemsgs.Cofre}: "/>
                                <h:outputText value="#{cofre.cofreSelecionado.clientes.codCofre.toBigInteger()} "
                                              style="font-weight: bold">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                                <h:outputText value="#{cofre.cofreSelecionado.clientes.codigo}
                                              #{cofre.cofreSelecionado.clientes.NRed}" style="font-weight: bold"/>
                            </div>
                            <div style="float: left; width: 20%; text-align: right;">
                                <p:commandLink title="#{localemsgs.Exportar}" action="#{cofre.setImg(exportarMB.getLogo(cofre.banco))}" onclick="PF('dlgExportar').show();">
                                    <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="30"/>
                                </p:commandLink>
                            </div>
                        </div>

                        <p:dataTable id="tabelaMov" value="#{cofre.listaMovimentacao}" var="movimento"
                                     resizableColumns="true" styleClass="tabela" selectionMode="single" rowKey="#{movimento.tescofresres.data}"
                                     selection="#{cofre.movimentacaoSelecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                     scrollable="true" scrollWidth="100%" scrollHeight="350"
                                     style="font-size: 12px; background: white; float: left">
                            <p:ajax event="rowDblselect" listener="#{cofre.detalhesDblSelect}" update="detalhesMovimentacao"/>


                            <p:columnGroup type="header">
                                <p:row styleClass="agrupador">
                                    <p:column headerText="#{localemsgs.Historicos}" colspan="4"/>
                                    <p:column headerText="#{localemsgs.ColetaAntesCorte}" colspan="2"/>
                                    <p:column headerText="#{localemsgs.Credito}" colspan="3"/>
                                    <p:column headerText="#{localemsgs.RecolhimentoDepoisCorte}" colspan="4"/>
                                    <p:column headerText="#{localemsgs.NoCofre}" colspan="2"/>
                                    <p:column headerText="#{localemsgs.ProximoDia}"/>
                                    <p:column headerText="#{localemsgs.Custodia}"/>
                                </p:row>
                                <p:row>
                                    <p:column headerText="#{localemsgs.Cofre}"/>
                                    <p:column headerText="#{localemsgs.Data}"/>
                                    <p:column headerText="#{localemsgs.DiaSemana}"/>
                                    <p:column headerText="#{localemsgs.Feriado}"/>
                                    <p:column headerText="#{localemsgs.ValorRecD0}"/>
                                    <p:column headerText="#{localemsgs.HoraRecD0}"/>
                                    <p:column headerText="#{localemsgs.DepDiaAntAposCorte}"/>
                                    <p:column headerText="#{localemsgs.ValorCorteD0}"/>
                                    <p:column headerText="#{localemsgs.TotalCredDia}"/>
                                    <p:column headerText="#{localemsgs.HrRecDia}"/>
                                    <p:column headerText="#{localemsgs.ValorRecDia}"/>
                                    <p:column headerText="#{localemsgs.ValorRecJaCreditado}"/>
                                    <p:column headerText="#{localemsgs.ValorRecACreditar}"/>
                                    <p:column headerText="#{localemsgs.SaldoCofreTotal}"/>
                                    <p:column headerText="#{localemsgs.DepositoJaCreditado}"/>
                                    <p:column headerText="#{localemsgs.DepositoProxDU}"/>
                                    <p:column headerText="#{localemsgs.SaldoFisCst}"/>
                                </p:row>
                            </p:columnGroup>

                            <p:column style="width: 40px" exportable="#{cofre.cofre}">
                                <h:outputText value="#{movimento.tescofresres.codCofre}"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}" style="width: 77px;" exportable="#{cofre.data}">
                                <h:outputText value="#{movimento.tescofresres.dataStr}" converter="conversorData"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DiaSemana}" style="width: 100px;" exportable="#{cofre.diaSeman}">
                                <h:outputText value="#{movimento.tescofresres.diaSemT}" 
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Feriado}" style="width: 55px;" exportable="#{cofre.feriado}">
                                <h:outputText value="#{movimento.tescofresres.feriado}" 
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column> 
                            <p:column headerText="#{localemsgs.ValorRecD0}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorCorteD0}">
                                <h:outputText value="#{movimento.tescofresres.vlrDep}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.HoraRecD0}" style="width: 95px;" exportable="#{cofre.horaRecD0}">
                                <h:outputText value="#{movimento.tescofresres.hrRecDepD0}" converter="conversorHora"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DepDiaAntAposCorte}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.depDiaAntAposCorte}">
                                <h:outputText value="#{movimento.tescofresres.vlrCredRecD0}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorCorteD0}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorCorteD0}">
                                <h:outputText value="#{movimento.tescofresres.vlrCredCorteD0}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.TotalCredDia}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.totalCredDia}">
                                <h:outputText value="#{movimento.tescofresres.vlrTotalCred}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.HrRecDia}" style="width: 95px;" exportable="#{cofre.hrRecDia}">
                                <h:outputText value="#{movimento.tescofresres.hrRecApos}" converter="conversorHora"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorRecDia}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorRecDia}">
                                <h:outputText value="#{movimento.tescofresres.vlrRecApos}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorRecJaCreditado}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorRecJaCreditado}">
                                <h:outputText value="#{movimento.tescofresres.vlrD0Apos}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorRecACreditar}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorRecACreditar}">
                                <h:outputText value="#{movimento.tescofresres.vlrD1Apos}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SaldoCofreTotal}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.saldoCofreTotal}">
                                <h:outputText value="#{movimento.tescofresres.saldoFisTotal}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DepositoJaCreditado}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.depositoJaCreditado}">
                                <h:outputText value="#{movimento.tescofresres.saldoFisCred}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DepositoProxDU}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.depositoProxDU}">
                                <h:outputText value="#{movimento.tescofresres.vlrDepProxDU}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                       ? 'color:blue' : 'color:black'}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SaldoFisCst}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.saldoFisCst}">
                                <h:outputText value="#{movimento.tescofresres.saldoFisCst}" converter="conversormoeda"
                                              style="#{movimento.tescofresres.saldoFisCst.toPlainString().contains('-') ? 'color:red':'color:green'}"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>
                </p:dialog>

                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" /> 
                    </f:facet>
                    <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                    <p:separator />
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="cofre" value="#{cofre.cofre}">
                                <p:ajax update="labelCofre"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelCofre" value="#{localemsgs.Cofre}" style="#{cofre.cofre eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>    
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="data" value="#{cofre.data}">
                                <p:ajax update="labelData"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelData" value="#{localemsgs.Data}" style="#{cofre.data eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="diaseman" value="#{cofre.diaSeman}">
                                <p:ajax update="labelDiaSeman"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDiaSeman" value="#{localemsgs.DiaSemana}" style="#{cofre.diaSeman eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="feriado" value="#{cofre.feriado}">
                                <p:ajax update="labelFeriado"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelFeriado" value="#{localemsgs.Feriado}" style="#{cofre.feriado eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="valorRecD0" value="#{cofre.valorRecD0}">
                                <p:ajax update="labelValorRecD0"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelValorRecD0" value="#{localemsgs.ValorRecD0}" style="#{cofre.valorRecD0 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>    
                    <div class="ui-grid-row" style="padding-bottom: 3px;">   
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="horaRecD0" value="#{cofre.horaRecD0}">
                                <p:ajax update="labelHoraRecD0"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelHoraRecD0" value="#{localemsgs.HoraRecD0}" style="#{cofre.horaRecD0 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="depDiaAntAposCorte" value="#{cofre.depDiaAntAposCorte}">
                                <p:ajax update="labelDepDiaAntAposCorte"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDepDiaAntAposCorte" value="#{localemsgs.DepDiaAntAposCorte}" style="#{cofre.depDiaAntAposCorte eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="valorCorteD0" value="#{cofre.valorCorteD0}">
                                <p:ajax update="labelValorCorteD0"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelValorCorteD0" value="#{localemsgs.ValorCorteD0}" style="#{cofre.valorCorteD0 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="totalCredDia" value="#{cofre.totalCredDia}">
                                <p:ajax update="labelTotalCredDia"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelTotalCredDia" value="#{localemsgs.TotalCredDia}" style="#{cofre.totalCredDia eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>    
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="hrRecDia" value="#{cofre.hrRecDia}">
                                <p:ajax update="labelHrRecDia"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelHrRecDia" value="#{localemsgs.HrRecDia}" style="#{cofre.hrRecDia eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="valorRecDia" value="#{cofre.valorRecDia}">
                                <p:ajax update="labelValorRecDia"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelValorRecDia" value="#{localemsgs.ValorRecDia}" style="#{cofre.valorRecDia eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="valorRecJaCreditado" value="#{cofre.valorRecJaCreditado}">
                                <p:ajax update="labelValorRecJaCreditado"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelValorRecJaCreditado" value="#{localemsgs.ValorRecJaCreditado}" style="#{cofre.valorRecJaCreditado eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>   
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="valorRecACreditar" value="#{cofre.valorRecACreditar}">
                                <p:ajax update="labelValorRecACreditar"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelValorRecACreditar" value="#{localemsgs.ValorRecACreditar}" style="#{cofre.valorRecACreditar eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>    
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="saldoCofreTotal" value="#{cofre.saldoCofreTotal}">
                                <p:ajax update="labelSaldoCofreTotal"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSaldoCofreTotal" value="#{localemsgs.SaldoCofreTotal}" style="#{cofre.saldoCofreTotal eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>   
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="depositoJaCreditado" value="#{cofre.depositoJaCreditado}">
                                <p:ajax update="labelDepositoJaCreditado"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDepositoJaCreditado" value="#{localemsgs.DepositoJaCreditado}" style="#{cofre.depositoJaCreditado eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div> 
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="depositoProxDU" value="#{cofre.depositoProxDU}">
                                <p:ajax update="labelDepositoProxDU"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDepositoProxDU" value="#{localemsgs.DepositoProxDU}" style="#{cofre.depositoProxDU eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>   
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="saldoFisCst" value="#{cofre.saldoFisCst}">
                                <p:ajax update="labelSaldoFisCst"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSaldoFisCst" value="#{localemsgs.SaldoFisCst}" style="#{cofre.saldoFisCst eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>

                    <p:separator />

                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6" 
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink target="_blank" id="pdf" actionListener="#{cofre.atualizaTabela}">
                                <p:graphicImage url="../assets/img/icone_pdf.png"/>
                                <p:dataExporter target="movimentos:tabelaMov" type="pdf"
                                                fileName="RelatorioCofre#{cofre.cofreSelecionado.clientes.codCofre.toBigInteger()}" 
                                                preProcessor="#{cofre.exportarMovimentacao}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" actionListener="#{cofre.atualizaTabela}" target="_blank">
                                <p:graphicImage url="../assets/img/icone_xls.png"/>
                                <p:dataExporter target="movimentos:tabelaMov" type="xlsx" fileName="RelatorioCofre#{cofre.cofreSelecionado.clientes.codCofre.toBigInteger()}" /> 
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <h:form id="detalhesMovimentacao">
                <p:dialog widgetVar="dlgListarDetalhesmovimentacao" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.CofreInteligente}" style="color:#022a48" /> 
                    </f:facet>

                    <p:panel id="cadastrar2" style="background-color: transparent" styleClass="cadastrar2">                        
                        <div style="float: left; width: 100%">
                            <div style="float: left; width: 45%;">
                                <h:outputText value="#{localemsgs.Cofre}: "/>
                                <h:outputText value="#{cofre.cofreSelecionado.clientes.codCofre.toBigInteger()} "
                                              style="font-weight: bold">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                                <h:outputText value="#{cofre.movimentacaoSelecionado.clientes.codigo}
                                              #{cofre.cofreSelecionado.clientes.NRed}" style="font-weight: bold"/>
                            </div>
                            <div style="float: left; width: 45%;">
                                <h:outputText value="#{localemsgs.DataCompetencia}: "/>
                                <h:outputText value="#{cofre.movimentacaoSelecionado.tescofresres.data}"
                                              style="font-weight: bold" converter="conversorData" />
                            </div>
                            <div style="float: left; width: 5%; text-align: right;">
                                <p:commandLink title="#{localemsgs.Exportar}" action="#{cofre.setImg(exportarMB.getLogo(cofre.banco))}" onclick="PF('dlgExportarDetMov').show();">
                                    <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="30"/>
                                </p:commandLink>
                            </div>
                        </div>
                        <div style="float: left; width: 100%">
                            <div style="float: left; width: 100%;">
                                <h:outputText value="#{localemsgs.Entradas} "/>
                                <h:outputText value="(#{cofre.detalhesMovimentacao.size()})"/>
                            </div>
                        </div>
                        <p:dataTable id="tabelaDetMov" value="#{cofre.detalhesMovimentacao}" var="detMovimento"
                                     resizableColumns="true" styleClass="tabela" 
                                     selection="#{cofre.movimentacaoSelecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                     scrollable="true" scrollWidth="100%" scrollHeight="400"
                                     style="font-size: 12px; background: white; float: left">
                            <p:column headerText="#{localemsgs.Cofre}" style="width: 40px" exportable="#{cofre.cofre}">
                                <h:outputText value="#{detMovimento.codCofre}" title="#{detMovimento.codCofre}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}" style="width: 73px" exportable="#{cofre.data}">
                                <h:outputText value="#{detMovimento.data}" title="#{detMovimento.data}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora}" style="width: 60px" exportable="#{cofre.hora}">
                                <h:outputText value="#{detMovimento.hora}" title="#{detMovimento.hora}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Usuario}" style="width: 80px" exportable="#{cofre.nomeUsuario}">
                                <h:outputText value="#{detMovimento.nomeUsuario}" title="#{detMovimento.nomeUsuario}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorDeposito}" styleClass="celula-right" exportable="#{cofre.valorDeposito}">
                                <h:outputText value="#{detMovimento.valorDeposito}" title="#{detMovimento.valorDeposito}" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.TipoDeposito}" style="width: 105px" exportable="#{cofre.tipoDeposito}">
                                <h:outputText value="#{detMovimento.tipoDeposito}" title="#{detMovimento.tipoDeposito}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Status}" style="width: 70px" exportable="#{cofre.status}">
                                <h:outputText value="#{detMovimento.status}" title="#{detMovimento.status}"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>
                </p:dialog>

                <p:dialog widgetVar="dlgExportarDetMov" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" /> 
                    </f:facet>
                    <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                    <p:separator />
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="cofre" value="#{cofre.cofre}">
                                <p:ajax update="labelCofre"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelCofre" value="#{localemsgs.Cofre}" style="#{cofre.cofre eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>    
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="data" value="#{cofre.data}">
                                <p:ajax update="labelData"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelData" value="#{localemsgs.Data}" style="#{cofre.data eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="hora" value="#{cofre.hora}">
                                <p:ajax update="labelHora"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelHora" value="#{localemsgs.Hora}" style="#{cofre.hora eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="usuario" value="#{cofre.nomeUsuario}">
                                <p:ajax update="labelUsuario"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelUsuario" value="#{localemsgs.Usuario}" style="#{cofre.nomeUsuario eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="valorDeposito" value="#{cofre.valorDeposito}">
                                <p:ajax update="labelValorDeposito"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelValorDeposito" value="#{localemsgs.ValorDeposito}" style="#{cofre.valorDeposito eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>    
                    <div class="ui-grid-row" style="padding-bottom: 3px;">   
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="tipoDeposito" value="#{cofre.tipoDeposito}">
                                <p:ajax update="labelTipoDeposito"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelTipoDeposito" value="#{localemsgs.TipoDeposito}" style="#{cofre.tipoDeposito eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="status" value="#{cofre.status}">
                                <p:ajax update="labelStatus"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelStatus" value="#{localemsgs.Status}" style="#{cofre.status eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>

                    <p:separator />

                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6" 
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink target="_blank" id="pdf" actionListener="#{cofre.atualizaTabela}">
                                <p:graphicImage url="../assets/img/icone_pdf.png"/>
                                <p:dataExporter target="detalhesMovimentacao:tabelaDetMov" type="pdf"
                                                fileName="RelatorioAnaliticoCofre#{cofre.cofreSelecionado.clientes.codCofre.toBigInteger()}" 
                                                preProcessor="#{cofre.exportarMovimentacao}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" actionListener="#{cofre.atualizaTabela}" target="_blank">
                                <p:graphicImage url="../assets/img/icone_xls.png"/>
                                <p:dataExporter target="detalhesMovimentacao:tabelaDetMov" type="xlsx" 
                                                fileName="RelatorioAnaliticoCofre#{cofre.cofreSelecionado.clientes.codCofre.toBigInteger()}" /> 
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <p:dialog positionType="absolute" responsive="true"
                      draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                      style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;"
                      header="#{localemsgs.Opcoes}" widgetVar="dlgOk">
                <h:form>
                    <div class="form-inline">
                        <h:outputText value="#{localemsgs.EscolherOpcao}:" style="text-align: center"/>
                    </div>
                    <p:spacer height="30px"/>
                    <div class="form-inline">
                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px"
                                     rendered="#{login.clientes.size() gt 1}">
                            <p:commandLink oncomplete="PF('dlgSelecionarCliente').show()">
                                <p:graphicImage url="../assets/img/icone_satmob_clientes.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink oncomplete="PF('dlgSelecionarCliente').show()">
                                <h:outputText  value="#{localemsgs.TrocarCliente}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px"
                                     rendered="#{cofre.WALMART}">
                            <p:commandLink action="/configuracoes/acessos.xhtml?faces-redirect=true">
                                <p:graphicImage url="../assets/img/icone_usuarios.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink action="/configuracoes/acessos.xhtml?faces-redirect=true">
                                <h:outputText  value="#{localemsgs.CadastrarUsuario}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px"
                                     rendered="#{login.permissaoControleAcessos}">
                            <p:commandLink action="/configuracoes/acessos.xhtml?faces-redirect=true">
                                <p:graphicImage url="../assets/img/icone_usuarios.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink action="/configuracoes/acessos.xhtml?faces-redirect=true">
                                <h:outputText value="#{localemsgs.Usuarios}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px">
                            <p:commandLink oncomplete="PF('dlgTrocarSenha').show()">
                                <p:graphicImage url="../assets/img/icone_configuracoes.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink oncomplete="PF('dlgTrocarSenha').show()" >
                                <h:outputText  value="#{localemsgs.TrocarSenha}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px">
                            <p:commandLink action="#{login.logOutRH}">
                                <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink action="#{login.logOutRH}">
                                <h:outputText value="#{localemsgs.Sair}"/>
                            </p:commandLink> 
                        </h:panelGrid>
                    </div>
                </h:form>
            </p:dialog>

            <h:form class="form-inline" id="formEditar">
                <p:dialog widgetVar="dlgEditar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" focus="formEditar:codpessoa"
                          style="background-image: url('assets/img/menu_fundo.png');
                          background-size: 750px 430px; background-repeat: no-repeat; ">
                    <script>
                        $(document).ready(function () {
                            //first unbind the original click event
                            PF('dlgEditar').closeIcon.unbind('click');

                            //register your own
                            PF('dlgEditar').closeIcon.click(function (e) {
                                $("#formEditar\\:botaoFechar").click();
                                //should be always called
                                e.preventDefault();
                            });


                        })
                    </script>
                    <f:facet name="header">
                        <img src="../assets/img/icone_usuarios.png" height="40" width="40"/> 
                        #{localemsgs.CadastrarUsuario}
                    </f:facet>
                    <p:panel id="editar" style="background-color: transparent;">
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgEditar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <p:confirmDialog global="true">
                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                        </p:confirmDialog>
                        <p:focus context="formEditar:codpessoa"/>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="subFil" value="#{localemsgs.Filial}:"/>
                            <p:selectOneMenu id="subFil" value="#{cofre.filial}" converter="omnifaces.SelectItemsConverter"
                                             filter="true" filterMatchMode="contains"  disabled="#{cofre.flag eq 2}"
                                             style="width: 100%">
                                <f:selectItems value="#{cofre.todasFiliais}" var="filial" itemValue="#{filial}" 
                                               itemLabel="#{filial.descricao}"/>
                                <p:ajax event="itemSelect" listener="#{cofre.selecionarFilialUsuario}" update="formEditar:editar"/>
                            </p:selectOneMenu>

                            <p:outputLabel for="codpessoa" value="#{localemsgs.Pessoa}:"/>
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-11,ui-grid-col-1" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:autoComplete id="codpessoa" value="#{cofre.pessoa}" completeMethod="#{cofre.pessoas.ListarQueryValida}"
                                                required="true" label="#{localemsgs.Pessoa}"  disabled="#{cofre.flag eq 2}" forceSelection="true"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Pessoa}" scrollHeight="200"
                                                style="width: 100%" styleClass="pessoa"
                                                var="ppl" itemLabel="#{ppl.nome}" itemValue="#{ppl}" converter="conversorPessoa">
                                    <p:ajax event="itemSelect" listener="#{cofre.selecionarPessoa}"
                                            update="formEditar:email msgs 
                                            formEditar:editar
                                            formEditar:codpessoa
                                            formEditar:tabs:panelFiliais
                                            formEditar:tabs:filiais
                                            formEditar:grupo
                                            formEditar:senha
                                            formEditar:nivel
                                            formEditar:confirmacao
                                            formEditar:motivo
                                            formEditar:descricao
                                            formEditar:btnCadastrar
                                            formEditar:btnLogar" />
                                </p:autoComplete>

                                <p:commandLink title="#{localemsgs.Cadastrar}" action="#{cofre.pessoas.PreCadastro}"
                                               process="@this"
                                               update=":msgs :main:tabela cabecalho formCadastrar">
                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30"/>
                                </p:commandLink>
                            </p:panelGrid>

                            <p:outputLabel for="email" value="#{localemsgs.Email}:"/>
                            <p:inputText value="#{cofre.pessoa.email}" readonly="true" id="email" style="width: 100%">
                                <p:watermark for="email" value="#{localemsgs.Email}"/>
                            </p:inputText>
                        </p:panelGrid>

                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">    
                            <p:outputLabel for="nivel" value="#{localemsgs.Nivel}:" />
                            <p:selectOneMenu value="#{cofre.novo.saspw.nivelx}" id="nivel"
                                             required="true" label="#{localemsgs.Nivel}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nivel}">
                                <f:selectItem itemLabel="#{localemsgs.CofreInteligente}" itemValue="6"/>
                            </p:selectOneMenu>

                            <p:outputLabel for="grupo" value="#{localemsgs.Grupo}:" />
                            <p:selectOneMenu value="#{cofre.novo.grupo.codigo}" id="grupo" style="width: 100%"
                                             required="true" label="#{localemsgs.Grupo}" filter="true" filterMatchMode="contains"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Grupo}">
                                <f:selectItems value="#{cofre.grupos}" var="grupos" itemValue="#{grupos.codigo}"
                                               itemLabel="#{grupos.descricao}" noSelectionValue="Selecione"/>
                            </p:selectOneMenu>

                            <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:" />
                            <p:selectOneMenu value="#{cofre.novo.saspw.situacao}" id="situacao"
                                             required="true" label="#{localemsgs.Situacao}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}">
                                <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                <f:selectItem itemLabel="#{localemsgs.Bloqueado}" itemValue="B"/>
                            </p:selectOneMenu>

                            <p:outputLabel for="descricao" value="#{localemsgs.Descricao}:"/>
                            <p:inputText id="descricao" value="#{cofre.novo.saspw.descricao}"
                                         label="#{localemsgs.Descricao} " style="width: 100%">
                                <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                            </p:inputText>

                            <p:outputLabel for="senha" value="#{localemsgs.Senha}:"/>
                            <p:password id="senha" value="#{cofre.novo.pessoa.PWWeb}" required="true" transient="true"
                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                        label="#{localemsgs.Senha}" feedback="true" redisplay="true" match="confirmacao"
                                        promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                        goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                        style="width: 100%">
                                <f:validateRegex pattern="^[0-9]{5,20}$" for="senha"/>
                                <p:watermark for="senha" value="#{localemsgs.Senha}"/>
                            </p:password>

                            <p:outputLabel for="confirmacao" value="#{localemsgs.Confirmacao}:" />
                            <p:password id="confirmacao" value="#{cofre.novo.pessoa.PWWeb}" redisplay="true"
                                        label="#{localemsgs.Senha}" style="width: 100%">
                                <p:watermark for="confirmacao" value="#{localemsgs.Senha}"/>
                            </p:password>
                        </p:panelGrid>


                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="motivo" value="#{localemsgs.Motivo}:"/>
                            <p:inputText id="motivo" value="#{cofre.novo.saspw.motivo}"
                                         label="#{localemsgs.Motivo}" style="width: 100%">
                                <p:watermark for="motivo" value="#{localemsgs.Motivo}"/>
                            </p:inputText>


                            <p:commandLink id="btnCadastrar" update=":msgs :main:tabela cabecalho formEditar:editar"
                                           action="#{cofre.adicionarPermissoes}"
                                           title="#{localemsgs.Cadastrar}" rendered="#{cofre.flag eq 1}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                            <p:commandLink id="btnLogar" update=":msgs :main:tabela cabecalho formEditar:editar"
                                           action="#{cofre.editarAcesso}"
                                           title="#{localemsgs.Editar}" rendered="#{cofre.flag eq 2}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panelGrid>

                        <p:tabView id="tabs" dynamic="true" cache="true" style="height: 210px;" class="tabs">
                            <p:tab class="tabs">
                                <f:facet name="title">
                                    <h:outputText value="#{localemsgs.Filiais}" style="color:black"/>
                                </f:facet>
                                <p:panelGrid columns="2">
                                    <p:panel id="tabelaFiliais" class="panelTabela">
                                        <p:dataTable id="filiais" value="#{cofre.filiais}" scrollable="true" emptyMessage="#{localemsgs.SemRegistros}"
                                                     var="listaFiliais" rowKey="#{listaFiliais.codfilAc}" scrollHeight="100" sortBy="#{listaFiliais.codfilAc}"
                                                     scrollWidth="100%"
                                                     resizableColumns="true" selectionMode="single" selection="#{cofre.filialSelecionada}" styleClass="tabela"
                                                     style="font-size: 12px">
                                            <p:ajax event="rowSelect" listener="#{cofre.selecionarFilial}"/>
                                            <p:column headerText="#{localemsgs.Filiais}" style="width: 35px">
                                                <p:outputLabel value="#{listaFiliais.codfilAc}">
                                                    <f:convertNumber pattern="0000"/>
                                                </p:outputLabel>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Descricao}" style="width: 239px">
                                                <p:outputLabel value="#{listaFiliais.descricao}" title="#{listaFiliais.descricao}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador}" style="width: 122px">
                                                <h:outputText value="#{listaFiliais.operador}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora}" style="width: 49px">
                                                <h:outputText value="#{listaFiliais.hr_Alter}"/>  
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Data}" style="width: 84px">
                                                <h:outputText value="#{listaFiliais.dt_Alter}" title="#{listaFiliais.dt_Alter}" converter="conversorData"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                    <p:panel style="width: 30px; padding-right: 0px">
                                        <p:commandLink title="#{localemsgs.Adicionar}" id="adicionarFilial" process="@this"
                                                       oncomplete="PF('dlgFiliais').show()"
                                                       update="filiais msgs formEditar:tabs:panelFiliais">
                                            <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="30" height="30" />
                                        </p:commandLink>
                                        <p:spacer height="30px"/>
                                        <p:commandLink title="#{localemsgs.Remover}" process="@this" action="#{cofre.apagarFilial}"
                                                       update="filiais msgs" >
                                            <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="30" height="30" />
                                        </p:commandLink>

                                        <p:dialog header="#{localemsgs.AdicionarFilial}"
                                                  widgetVar="dlgFiliais" closable="true" resizable="false" width="400" height="50"
                                                  hideEffect="fade">
                                            <p:outputPanel id="panelFiliais">
                                                <p:outputLabel for="addFil" value="#{localemsgs.Filial}:"
                                                               style="position: absolute; float: left"/>
                                                <p:selectOneMenu id="addFil" value="#{cofre.novaFilial}" 
                                                                 converter="omnifaces.SelectItemsConverter"
                                                                 style="position: absolute; float: left; left: 60px; width: 280px;"
                                                                 filterMatchMode="contains" filter="true">
                                                    <f:selectItems value="#{cofre.todasFiliais}" var="fil" itemValue="#{fil}" 
                                                                   itemLabel="#{fil.codfilAc} - #{fil.descricao}"/>
                                                    <p:ajax event="itemSelect" listener="#{cofre.selecionarNovaFilial}"/>
                                                    <p:watermark for="addFil" value="#{localemsgs.Filial}"/>
                                                </p:selectOneMenu>

                                                <p:commandLink oncomplete="PF('dlgFiliais').hide()" action="#{cofre.adicionarFilial}"
                                                               title="#{localemsgs.Selecionar}" update="filiais msgs"
                                                               partialSubmit="true" process="panelFiliais"
                                                               style="position: absolute; float: left; left: 350px;">
                                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30" />
                                                </p:commandLink>
                                            </p:outputPanel>
                                        </p:dialog>
                                    </p:panel>
                                </p:panelGrid>
                            </p:tab>
                            <p:tab id="tabClientes">
                                <f:facet name="title">
                                    <h:outputText value="#{localemsgs.Clientes}" style="color:black"/>
                                </f:facet>
                                <p:panelGrid columns="2">
                                    <p:panel id="tabelaClientes" class="panelTabela">
                                        <p:dataTable id="clientes" value="#{cofre.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                                     var="listaClientes" rowKey="#{listaClientes.codCli}" scrollWidth="100%"
                                                     resizableColumns="true" scrollable="true" scrollHeight="95" styleClass="tabela"
                                                     style="font-size: 12px" selectionMode="single" 
                                                     selection="#{cofre.clienteSelecionado}">
                                            <p:ajax event="rowSelect" listener="#{cofre.selecionarPessoaCliAut}"/>
                                            <p:column headerText="#{localemsgs.CodCli}" style="width: 58px">
                                                <h:outputText value="#{listaClientes.codCli}" 
                                                              style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.NRed}" style="width: 152px">
                                                <h:outputText value="#{listaClientes.nomeCli}" 
                                                              style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.CodFil}" style="width: 50px">
                                                <h:outputText value="#{listaClientes.codFil}" converter="conversor0" 
                                                              style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Codigo}" style="width: 52px">
                                                <h:outputText value="#{listaClientes.codigo}" converter="conversor0" 
                                                              style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador}" style="width: 70px">
                                                <h:outputText value="#{listaClientes.operador}" 
                                                              style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora}" style="width: 49px">
                                                <h:outputText value="#{listaClientes.hr_Alter}" 
                                                              style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                                <h:outputText value="#{listaClientes.dt_Alter}" title="#{listaClientes.dt_Alter}" 
                                                              converter="conversorData" 
                                                              style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                            </p:column>
                                        </p:dataTable>
                                        <div class="ui-grid ui-grid-responsive">
                                            <div class="ui-grid-row">
                                                <div class="ui-grid-col-6">
                                                    <h:outputText id="qtdClientes" value="#{localemsgs.QtdClientes}: #{cofre.clientes.size()}"
                                                                  style="font-size: 12px"/>
                                                </div>
                                                <div class="ui-grid-col-6">
                                                    <p:outputLabel for="checkboxCliente" value="#{localemsgs.ExibirExcluidos}: "
                                                                   style="font-size: 12px"/>
                                                    <p:selectBooleanCheckbox 
                                                        id="checkboxCliente"
                                                        value="#{cofre.flag_exclPessoaCliAut}" 
                                                        style="font-size: 12px">
                                                        <p:ajax update="clientes adicionarClientes formEditar:tabs:qtdClientes msgs"
                                                                listener="#{cofre.listarPessoaCliAut}" />
                                                    </p:selectBooleanCheckbox>
                                                </div>
                                            </div>
                                        </div>
                                    </p:panel>
                                    <p:panel style="width: 30px">
                                        <p:commandLink title="#{localemsgs.Adicionar}" process="@this" actionListener="#{cofre.novoCliente}"
                                                       update="clientes adicionarClientes msgs formEditar:tabs:qtdClientes"> 
                                            <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="30" height="30" />
                                        </p:commandLink>
                                        <p:spacer height="30px"/>
                                        <p:commandLink title="#{localemsgs.Remover}" process="@this" action="#{cofre.apagarCliente}"
                                                       update="clientes formEditar:tabs:qtdClientes msgs">
                                            <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="30" height="30" />
                                        </p:commandLink>

                                        <p:dialog widgetVar="dlgAdicionarClientes" header="#{localemsgs.AdicionarCliente}"
                                                  resizable="false" dynamic="true" closable="true" 
                                                  width="400" showEffect="drop" hideEffect="drop">
                                            <p:panel id="adicionarClientes" style="background-color: transparent;">
                                                <div class="form-inline">
                                                    <p:outputLabel for="addCliente" value="#{localemsgs.Cliente}:" 
                                                                   style="float: left;position:absolute;"/>
                                                    <p:autoComplete id="addCliente" value="#{cofre.todosClientesSelecao}" styleClass="cliente2"
                                                                    style="float: left;left: 100px;position:absolute; width: 275px"
                                                                    completeMethod="#{cofre.listarClientes}"
                                                                    var="cli" itemLabel="#{cli.nome}" itemValue="#{cli}"
                                                                    converter="conversorCliente" scrollHeight="250">
                                                        <p:ajax event="itemSelect" listener="#{cofre.selecionarCliente}" update="adicionarClientes msgs"/>
                                                    </p:autoComplete>
                                                    <p:watermark for="addCliente" value="#{localemsgs.Cliente}" />
                                                </div>

                                                <p:spacer height="40px"/>

                                                <div class="form-inline">
                                                    <p:outputLabel for="fil" value="#{localemsgs.Filial}:" 
                                                                   style="float: left;position:absolute;" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                    <h:outputText id="fil" value="#{cofre.todosClientesSelecao.codFil}"
                                                                  rendered="#{cofre.todosClientesSelecao ne null}" converter="conversor0"
                                                                  style="float: left;left: 100px;position:absolute; font-weight: bold"/>
                                                    <p:spacer height="30px" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                </div>
                                                <div class="form-inline">
                                                    <p:outputLabel for="nred" value="#{localemsgs.NRed}:" 
                                                                   style="float: left;position:absolute;" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                    <h:outputText id="nred" value="#{cofre.todosClientesSelecao.NRed}"
                                                                  rendered="#{cofre.todosClientesSelecao ne null}"
                                                                  style="float: left;left: 100px;position:absolute; font-weight: bold"/>
                                                    <p:spacer height="30px" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                </div>
                                                <div class="form-inline">
                                                    <p:outputLabel for="ende" value="#{localemsgs.Ende}:" 
                                                                   style="float: left;position:absolute;" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                    <h:outputText id="ende" value="#{cofre.todosClientesSelecao.ende}"
                                                                  style="float: left;left: 100px;position:absolute; font-weight: bold"
                                                                  rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                    <p:spacer height="30px" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                </div>
                                                <div class="form-inline">
                                                    <p:outputLabel for="email" value="#{localemsgs.Email}:" 
                                                                   style="float: left;position:absolute;" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                    <h:outputText id="email" value="#{cofre.todosClientesSelecao.email}"
                                                                  style="float: left;left: 100px;position:absolute; font-weight: bold"
                                                                  rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                    <p:spacer height="30px" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                </div>
                                                <div class="form-inline">
                                                    <p:outputLabel for="fone1" value="#{localemsgs.Fone1}:" 
                                                                   style="float: left;position:absolute;" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                    <h:outputText id="fone1" value="#{cofre.todosClientesSelecao.fone1}" converter="conversorFone"
                                                                  style="float: left;left: 100px;position:absolute; font-weight: bold"
                                                                  rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                    <p:spacer height="30px" rendered="#{cofre.todosClientesSelecao ne null}"/>
                                                </div>

                                                <div class="form-inline">
                                                    <p:commandLink oncomplete="PF('dlgAdicionarClientes').hide()" 
                                                                   action="#{cofre.adicionarCliente}" process="adicionarClientes"
                                                                   title="#{localemsgs.Selecionar}"
                                                                   update="clientes msgs adicionarClientes formEditar:tabs:qtdClientes">
                                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                                    </p:commandLink>
                                                </div>
                                            </p:panel>
                                        </p:dialog>
                                    </p:panel>
                                </p:panelGrid>
                            </p:tab>
                        </p:tabView>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!--Cadastrar novo-->
            <h:form class="form-inline" id="formCadastrar">
                <p:dialog widgetVar="dlgCadastrar" positionType="absolute"  focus="cpf" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" styleClass="dialogo"
                          style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.CadastrarPessoa}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panel id="cadastrar" style="background-color: transparent" class="cadastrar2">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="cpf" value="#{localemsgs.CPF}: " />
                            <p:inputMask id="cpf" value="#{cofre.pessoas.novaPessoa.CPF}" disabled="#{cofre.pessoas.flag eq 2}"
                                         maxlength="11" mask="#{mascaras.mascaraCPF}">
                                <p:watermark for="cpf" value="#{localemsgs.CPF}"/>
                                <p:ajax event="blur" listener="#{cofre.pessoas.buscarPessoaCofre}"
                                        update="msgs"/>
                            </p:inputMask>
                        </p:panelGrid>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="nome" value="#{localemsgs.Nome}: " />
                            <p:inputText id="nome" value="#{cofre.pessoas.novaPessoa.nome}" style="width: 100%"
                                         required="true" label="#{localemsgs.Nome}" disabled="#{cofre.pessoas.edicao}"
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                         maxlength="50">
                                <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                            </p:inputText> 

                            <p:outputLabel for="email" value="#{localemsgs.Email}: "/>
                            <p:inputText id="email" value="#{cofre.pessoas.novaPessoa.email}" style="width: 100%"
                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"
                                         maxlength="50" disabled="#{cofre.pessoas.edicao}">
                                <p:watermark for="email" value="#{localemsgs.Email}"/> 
                            </p:inputText>
                        </p:panelGrid>

                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:commandLink id="cadastro" action="#{cofre.pessoas.cadastroSimples}" update="formCadastrar :main:tabela :msgs cabecalho"
                                           title="#{localemsgs.Cadastrar}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <div>
                <h:form id="cliente">
                    <p:dialog widgetVar="dlgSelecionarCliente" positionType="absolute" id="dlgClientes"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">  
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="tabelaClientes" style="width: 440px; background: transparent">
                            <div class="form-inline">
                                <p:dataTable id="tabela" value="#{login.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="cli" resizableColumns="true" selection="#{login.selecionado}" rowKey="#{cli.codCli}"
                                             scrollable="true" scrollHeight="200" selectionMode="single"
                                             style="font-size: 12px; float: left" styleClass="tabela" >
                                    <p:ajax event="rowDblselect" listener="#{login.dblSelectCofre}" update="msgs"/>
                                    <p:column headerText="#{localemsgs.Codigo}" style="width: 150px">
                                        <h:outputText value="#{cli.codCli}" title="#{cli.codCli}">
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cliente}" >
                                        <h:outputText value="#{cli.nomeCli}" title="#{cli.nomeCli}"/>
                                    </p:column>
                                </p:dataTable>
                            </div>
                            <div class="form-inline">
                                <h:outputText value="#{localemsgs.VerTodos}: "/>
                                <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                <p:spacer height="25px"/>
                            </div>
                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteCofre}" 
                                               title="#{localemsgs.Selecionar}">
                                    <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <div>
                <h:form id="trocarsenha">
                    <p:dialog widgetVar="dlgTrocarSenha" positionType="absolute" id="dlgTrocarSenha"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">  
                        <f:facet name="header">
                            <img src="../assets/img/icone_configuracoes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="panelSenha" style="width: 440px; background: transparent">
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="atual" value="#{localemsgs.SenhaAtual}" />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:password id="atual" value="#{cofre.senhaAtual}" label="#{localemsgs.SenhaAtual}" required="true" 
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.SenhaAtual}"/>
                                    </div>
                                </div>
                            </div>

                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="nova1" value="#{localemsgs.NovaSenha}" />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:password id="nova1" value="#{cofre.novaSenha}" match="nova2"
                                                    label="#{localemsgs.NovaSenha}" required="true" feedback="true"
                                                    promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                                    goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NovaSenha}">
                                            <f:validateRegex pattern="^[0-9]{5,20}$" for="nova1"/>
                                        </p:password>
                                    </div>
                                </div>
                            </div>

                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="nova2" value="#{localemsgs.confirmarNovaSenha}" />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:password id="nova2" value="#{cofre.novaSenha}" label="#{localemsgs.SenhaAtual}" required="true" 
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Confirmacao}"/>
                                    </div>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{cofre.TrocarSenha}" 
                                               title="#{localemsgs.Concluido}" update="msgs">
                                    <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>
                <div class="footer-body" id="footer-body">
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <p:panel id="totais" class="ui-grid-col-12 cabecalhoFilial">
                                <div class="ui-grid-col-3">
                                    <h:outputText value="#{localemsgs.TotalCreditos}: " style="font-weight: bold"/>
                                    <h:outputText value="#{cofre.totalCreditoDia}" converter="conversormoeda" styleClass="cabecalho"/>
                                </div>
                                <div class="ui-grid-col-3">
                                    <h:outputText value="#{localemsgs.TotalVlrRecolhido}: " style="font-weight: bold"/>
                                    <h:outputText value="#{cofre.totalValorRecolhido}" converter="conversormoeda" styleClass="cabecalho"/>
                                </div>
                                <div class="ui-grid-col-3">
                                    <h:outputText value="#{localemsgs.TotalSalCustDia}: " style="font-weight: bold"/>
                                    <h:outputText value="#{cofre.totalSaldoCustoDia}" converter="conversormoeda" styleClass="cabecalho"/>
                                </div>
                                <div class="ui-grid-col-3">
                                    <h:outputText value="#{localemsgs.TotalSaldoCofre}: " style="font-weight: bold"/>
                                    <h:outputText value="#{cofre.totalSaldoCofre}" converter="conversormoeda" styleClass="cabecalho"/>
                                </div>
                            </p:panel>
                        </div>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td align="right">
                                    </td>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

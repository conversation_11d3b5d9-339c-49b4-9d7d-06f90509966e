/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class PessoaDet {

    private String Codigo;
    private String FaceID;
    private String Operador;
    private String Dt_alter;
    private String Hr_Alter;

    private String PWWeb;
    private String Matr;
    private String Nome;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getFaceID() {
        return FaceID;
    }

    public void setFaceID(String FaceID) {
        this.FaceID = FaceID;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getPWWeb() {
        return PWWeb;
    }

    public void setPWWeb(String PWWeb) {
        this.PWWeb = PWWeb;
    }

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TipoSrvCli;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TipoSrvCliDao {

    public int inserirTipoSrvCli(TipoSrvCli tipoSrvCli, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO TipoSrvCli (Codigo, Descricao, Banco, TAtend, TCob, TCar, TipoSrv, ER, \n"
                    + " CodInterf, Aditivo, Exportar, SubCentro, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tipoSrvCli.getCodigo());
            consulta.setString(tipoSrvCli.getDescricao());
            consulta.setString(tipoSrvCli.getBanco());
            consulta.setString(tipoSrvCli.getTAtend());
            consulta.setString(tipoSrvCli.getTCob());
            consulta.setString(tipoSrvCli.getTCar());
            consulta.setString(tipoSrvCli.getTipoSrv());
            consulta.setString(tipoSrvCli.getER());
            consulta.setString(tipoSrvCli.getCodInterf());
            consulta.setString(tipoSrvCli.getAditivo());
            consulta.setString(tipoSrvCli.getExportar());
            consulta.setString(tipoSrvCli.getSubCentro());
            consulta.setString(tipoSrvCli.getOperador());
            consulta.setString(tipoSrvCli.getDt_Alter());
            consulta.setString(tipoSrvCli.getHr_Alter());
            int retorno = consulta.insert();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TipoSrvCliDao.inserirTipoSrvCli - " + e.getMessage() + "\r\n"
                    + " INSERT INTO TipoSrvCli (Codigo, Descricao, Banco, TAtend, TCob, TCar, TipoSrv, ER, \n"
                    + " CodInterf, Aditivo, Exportar, SubCentro, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (" + tipoSrvCli.getCodigo() + "," + tipoSrvCli.getDescricao() + "," + tipoSrvCli.getBanco() + ","
                    + tipoSrvCli.getTAtend() + "," + tipoSrvCli.getTCob() + "," + tipoSrvCli.getTCar() + "," + tipoSrvCli.getTipoSrv() + ","
                    + tipoSrvCli.getER() + "," + tipoSrvCli.getCodInterf() + "," + tipoSrvCli.getAditivo() + "," + tipoSrvCli.getExportar() + ","
                    + tipoSrvCli.getSubCentro() + "," + tipoSrvCli.getOperador() + "," + tipoSrvCli.getDt_Alter() + "," + tipoSrvCli.getHr_Alter() + ")");
        }
    }

    public int atualizarTipoSrvCli(TipoSrvCli tipoSrvCli, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE TipoSrvCli  SET Descricao = ?, Banco = ?, TAtend = ?, TCob = ?, TCar = ?, TipoSrv = ?, ER = ?, \n"
                    + " CodInterf = ?, Aditivo = ?, Exportar = ?, SubCentro = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ? \n"
                    + " WHERE Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tipoSrvCli.getDescricao());
            consulta.setString(tipoSrvCli.getBanco());
            consulta.setString(tipoSrvCli.getTAtend());
            consulta.setString(tipoSrvCli.getTCob());
            consulta.setString(tipoSrvCli.getTCar());
            consulta.setString(tipoSrvCli.getTipoSrv());
            consulta.setString(tipoSrvCli.getER());
            consulta.setString(tipoSrvCli.getCodInterf());
            consulta.setString(tipoSrvCli.getAditivo());
            consulta.setString(tipoSrvCli.getExportar());
            consulta.setString(tipoSrvCli.getSubCentro());
            consulta.setString(tipoSrvCli.getOperador());
            consulta.setString(tipoSrvCli.getDt_Alter());
            consulta.setString(tipoSrvCli.getHr_Alter());
            consulta.setString(tipoSrvCli.getCodigo());
            int retorno = consulta.update();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TipoSrvCliDao.inserirTipoSrvCli - " + e.getMessage() + "\r\n"
                    + " UPDATE TipoSrvCli  SET Descricao = " + tipoSrvCli.getDescricao() + ", Banco = " + tipoSrvCli.getBanco() + ", "
                    + "TAtend = " + tipoSrvCli.getTAtend() + ", TCob = " + tipoSrvCli.getTCob() + ", TCar = " + tipoSrvCli.getTCar() + ", "
                    + "TipoSrv = " + tipoSrvCli.getTipoSrv() + ", ER = " + tipoSrvCli.getER() + ", \nCodInterf = " + tipoSrvCli.getCodInterf() + ", "
                    + "Aditivo = " + tipoSrvCli.getAditivo() + ", Exportar = " + tipoSrvCli.getExportar() + ", "
                    + "SubCentro = " + tipoSrvCli.getSubCentro() + ", Operador = " + tipoSrvCli.getOperador() + ", "
                    + "Dt_Alter = " + tipoSrvCli.getDt_Alter() + ", Hr_Alter = " + tipoSrvCli.getHr_Alter() + ", \n "
                    + " WHERE Codigo = " + tipoSrvCli.getCodigo()
            );
        }
    }

    public List<TipoSrvCli> listarTipoSrvCli(Persistencia persistencia) throws Exception {
        try {
            List<TipoSrvCli> retorno = new ArrayList<>();
            String sql = " SELECT * FROM TipoSrvCli ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            TipoSrvCli tipoSrvCli;
            while (consulta.Proximo()) {
                tipoSrvCli = new TipoSrvCli();

                tipoSrvCli.setCodigo(consulta.getString("Codigo"));
                tipoSrvCli.setDescricao(consulta.getString("Descricao"));
                tipoSrvCli.setBanco(consulta.getString("Banco"));
                tipoSrvCli.setTAtend(consulta.getString("TAtend"));
                tipoSrvCli.setTCob(consulta.getString("TCob"));
                tipoSrvCli.setTCar(consulta.getString("TCar"));
                tipoSrvCli.setTipoSrv(consulta.getString("TipoSrv"));
                tipoSrvCli.setER(consulta.getString("ER"));
                tipoSrvCli.setCodInterf(consulta.getString("CodInterf"));
                tipoSrvCli.setAditivo(consulta.getString("Aditivo"));
                tipoSrvCli.setExportar(consulta.getString("Exportar"));
                tipoSrvCli.setSubCentro(consulta.getString("SubCentro"));
                tipoSrvCli.setOperador(consulta.getString("Operador"));
                tipoSrvCli.setDt_Alter(consulta.getString("Dt_Alter"));
                tipoSrvCli.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tipoSrvCli);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TipoSrvCliDao.listarTipoSrvCli - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM TipoSrvCli ");
        }
    }

    public List<TipoSrvCli> buscarTipoSrvCliList(String query, Persistencia persistencia) throws Exception {
        try {
            List<TipoSrvCli> retorno = new ArrayList<>();
            String sql = " SELECT * FROM TipoSrvCli \n"
                    + " WHERE (TipoSrvCli.Codigo like ? OR TipoSrvCli.Descricao like ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.setString("%" + query + "%");
            consulta.select();

            TipoSrvCli tipoSrvCli;
            while (consulta.Proximo()) {
                tipoSrvCli = new TipoSrvCli();

                tipoSrvCli.setCodigo(consulta.getString("Codigo"));
                tipoSrvCli.setDescricao(consulta.getString("Descricao"));
                tipoSrvCli.setBanco(consulta.getString("Banco"));
                tipoSrvCli.setTAtend(consulta.getString("TAtend"));
                tipoSrvCli.setTCob(consulta.getString("TCob"));
                tipoSrvCli.setTCar(consulta.getString("TCar"));
                tipoSrvCli.setTipoSrv(consulta.getString("TipoSrv"));
                tipoSrvCli.setER(consulta.getString("ER"));
                tipoSrvCli.setCodInterf(consulta.getString("CodInterf"));
                tipoSrvCli.setAditivo(consulta.getString("Aditivo"));
                tipoSrvCli.setExportar(consulta.getString("Exportar"));
                tipoSrvCli.setSubCentro(consulta.getString("SubCentro"));
                tipoSrvCli.setOperador(consulta.getString("Operador"));
                tipoSrvCli.setDt_Alter(consulta.getString("Dt_Alter"));
                tipoSrvCli.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tipoSrvCli);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TipoSrvCliDao.buscarTipoSrvCliList - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM TipoSrvCli \n"
                    + " WHERE (TipoSrvCli.Codigo like %" + query + "% OR TipoSrvCli.Descricao like %" + query + "%) ");
        }
    }

    public TipoSrvCli buscarTipoSrvCli(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM TipoSrvCli \n"
                    + " WHERE TipoSrvCli.Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();

            TipoSrvCli tipoSrvCli = null;
            if (consulta.Proximo()) {
                tipoSrvCli = new TipoSrvCli();

                tipoSrvCli.setCodigo(consulta.getString("Codigo"));
                tipoSrvCli.setDescricao(consulta.getString("Descricao"));
                tipoSrvCli.setBanco(consulta.getString("Banco"));
                tipoSrvCli.setTAtend(consulta.getString("TAtend"));
                tipoSrvCli.setTCob(consulta.getString("TCob"));
                tipoSrvCli.setTCar(consulta.getString("TCar"));
                tipoSrvCli.setTipoSrv(consulta.getString("TipoSrv"));
                tipoSrvCli.setER(consulta.getString("ER"));
                tipoSrvCli.setCodInterf(consulta.getString("CodInterf"));
                tipoSrvCli.setAditivo(consulta.getString("Aditivo"));
                tipoSrvCli.setExportar(consulta.getString("Exportar"));
                tipoSrvCli.setSubCentro(consulta.getString("SubCentro"));
                tipoSrvCli.setOperador(consulta.getString("Operador"));
                tipoSrvCli.setDt_Alter(consulta.getString("Dt_Alter"));
                tipoSrvCli.setHr_Alter(consulta.getString("Hr_Alter"));
            }
            consulta.close();
            return tipoSrvCli;
        } catch (Exception e) {
            throw new Exception("TipoSrvCliDao.buscarTipoSrvCli - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM TipoSrvCli \n"
                    + " WHERE TipoSrvCli.Codigo = " + codigo);
        }
    }
}

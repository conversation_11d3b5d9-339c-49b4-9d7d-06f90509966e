package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Rt_Perc {

    private BigDecimal Sequencia;
    private int Parada;
    private BigDecimal CodFil;
    private String Hora1;
    private String Hora2;
    private String Numero;
    private String ER;
    private String CodCli1;
    private String NRed;
    private String CodCli2;
    private String NRedDst;
    private String Hora1D;
    private String TipoSrv;
    private String Chave;
    private int DPar;
    private String Regiao;
    private String RegiaoDst;
    private String Observ;
    private String Valor;
    private String HrBaixa;
    private String HrCheg;
    private String HrSaida;
    private BigDecimal Atraso;
    private BigDecimal TempoEspera;
    private BigDecimal OS;
    private String OperIncl;
    private LocalDate Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;
    private String OperExcl;
    private LocalDate Dt_Excl;
    private String Hr_Excl;
    private String OperFech;
    private LocalDate Dt_Fech;
    private String Hr_Fech;
    private String CodLan;
    private BigDecimal Pedido;
    private String Flag_Excl;
    private String coletado;
    private String rota;
    private String veiculo;
    private int Impresso;

    private String latitude;
    private String longitude;

    private String Data;
    private String HsServ;
    private String HsImprod;

    private String Entregas;
    private String Recolhimentos;
    private String TempoCliente;
    private String Antecipado;

    private String HrChegVei;
    private String HrSaidaVei;
    private String Guia;
    private String Serie;
    private String Assinatura;

    private String CliOri;
    private String NomeOri;
    private String EnderecoOri;
    private String BairroOri;
    private String CidadeOri;
    private String UfOri;
    private String CepOri;

    private String CliDst;
    private String NomeDst;
    private String EnderecoDst;
    private String BairroDst;
    private String CidadeDst;
    private String UfDst;
    private String CepDst;

    private String CliFat;

    private String MotivCanc;

    private String Banco;

    private String Montante;
    private String ValorEmb;
    private String ValorAdv;
    private String ValorTE;

    private String ValorProcDN;
    private String ValorProcMD;
    private String ValorCustodia;

    private String TotalMontante;
    private String TotalValorEmb;
    private String TotalValorAdv;
    private String TotalValorTE;
    private String TotalParadas;

    private String TotalProcDN;
    private String TotalProcMD;
    private String TotalCustodia;

    private String Solicitante;

    private String Distancia;

    private String LatAtual;
    private String LonAtual;
    private String Hora1ParadaAtual;
    private String LatAnterior;
    private String LonAnterior;
    private String Hora1ParadaAnterior;
    private String LatSeguinte;
    private String LonSeguinte;
    private String Hora1ParadaSeguinte;

    private String Km;
    private String Moeda;
    private String PedidoCliente;

    private String DadosBaixa;
    
    private String GuiasGTVe;

    public Rt_Perc() {
        this.Data = "";
        this.Sequencia = new BigDecimal("0");
        this.Parada = 0;
        this.CodFil = new BigDecimal("0");
        this.Hora1 = "";
        this.Hora2 = "";
        this.ER = "";
        this.CodCli1 = "";
        this.NRed = "";
        this.CodCli2 = "";
        this.Hora1D = "";
        this.TipoSrv = "";
        this.Chave = "";
        this.DPar = 0;
        this.Regiao = "";
        this.Observ = "";
        this.rota = "";
        this.coletado = "";
        this.veiculo = "";
        this.Valor = "0";
        this.HrBaixa = "";
        this.HrCheg = "";
        this.HrSaida = "";
        this.Atraso = new BigDecimal("0");
        this.TempoEspera = new BigDecimal("0");
        this.OS = new BigDecimal("0");
        this.OperIncl = "";
        this.Dt_Incl = null;
        this.Hr_Incl = "";
        this.Operador = "";
        this.Dt_Alter = null;
        this.Hr_Alter = "";
        this.OperExcl = "";
        this.Dt_Excl = null;
        this.Hr_Excl = "";
        this.OperFech = "";
        this.Dt_Fech = null;
        this.Hr_Fech = "";
        this.CodLan = "";
        this.Pedido = new BigDecimal("0");
        this.Flag_Excl = "";
        this.Impresso = 0;
        this.Solicitante = "";
        this.Distancia = "";
        this.LatAtual = "";
        this.LonAtual = "";
        this.Hora1ParadaAtual = "";
        this.LatAnterior = "";
        this.LonAnterior = "";
        this.Hora1ParadaAnterior = "";
        this.LatSeguinte = "";
        this.LonSeguinte = "";
        this.Hora1ParadaSeguinte = "";
        this.Km = "";
        this.DadosBaixa = "";
    }

    public String getValorProcDN() {
        return ValorProcDN;
    }

    public void setValorProcDN(String ValorProcDN) {
        this.ValorProcDN = ValorProcDN;
    }

    public String getValorProcMD() {
        return ValorProcMD;
    }

    public void setValorProcMD(String ValorProcMD) {
        this.ValorProcMD = ValorProcMD;
    }

    public String getValorCustodia() {
        return ValorCustodia;
    }

    public void setValorCustodia(String ValorCustodia) {
        this.ValorCustodia = ValorCustodia;
    }

    public String getTotalProcDN() {
        return TotalProcDN;
    }

    public void setTotalProcDN(String TotalProcDN) {
        this.TotalProcDN = TotalProcDN;
    }

    public String getTotalProcMD() {
        return TotalProcMD;
    }

    public void setTotalProcMD(String TotalProcMD) {
        this.TotalProcMD = TotalProcMD;
    }

    public String getTotalCustodia() {
        return TotalCustodia;
    }

    public void setTotalCustodia(String TotalCustodia) {
        this.TotalCustodia = TotalCustodia;
    }

    public String getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(String veiculo) {
        this.veiculo = veiculo;
    }

    public String getRota() {
        return rota;
    }

    public void setRota(String rota) {
        this.rota = rota;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHsServ() {
        return HsServ;
    }

    public void setHsServ(String HsServ) {
        this.HsServ = HsServ;
    }

    public String getHsImprod() {
        return HsImprod;
    }

    public void setHsImprod(String HsImprod) {
        this.HsImprod = HsImprod;
    }

    public String getColetado() {
        return coletado;
    }

    public void setColetado(String coletado) {
        this.coletado = coletado;
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public int getParada() {
        return Parada;
    }

    public void setParada(int Parada) {
        this.Parada = Parada;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getER() {
        return ER;
    }

    public void setER(String ER) {
        this.ER = ER;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getCodCli2() {
        return CodCli2;
    }

    public void setCodCli2(String CodCli2) {
        this.CodCli2 = CodCli2;
    }

    public String getHora1D() {
        return Hora1D;
    }

    public void setHora1D(String Hora1D) {
        this.Hora1D = Hora1D;
    }

    public String getTipoSrv() {
        return TipoSrv;
    }

    public void setTipoSrv(String TipoSrv) {
        this.TipoSrv = TipoSrv;
    }

    public String getChave() {
        return Chave;
    }

    public void setChave(String Chave) {
        this.Chave = Chave;
    }

    public int getDPar() {
        return DPar;
    }

    public void setDPar(int DPar) {
        this.DPar = DPar;
    }

    public String getRegiao() {
        return Regiao;
    }

    public void setRegiao(String Regiao) {
        this.Regiao = Regiao;
    }

    public String getObserv() {
        return Observ;
    }

    public void setObserv(String Observ) {
        this.Observ = Observ;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getHrBaixa() {
        return HrBaixa;
    }

    public void setHrBaixa(String HrBaixa) {
        this.HrBaixa = HrBaixa;
    }

    public String getHrCheg() {
        return HrCheg;
    }

    public void setHrCheg(String HrCheg) {
        this.HrCheg = HrCheg;
    }

    public String getHrSaida() {
        return HrSaida;
    }

    public void setHrSaida(String HrSaida) {
        this.HrSaida = HrSaida;
    }

    public BigDecimal getAtraso() {
        return Atraso;
    }

    public void setAtraso(String Atraso) {
        try {
            this.Atraso = new BigDecimal(Atraso);
        } catch (Exception e) {
            this.Atraso = new BigDecimal("0");
        }
    }

    public BigDecimal getTempoEspera() {
        return TempoEspera;
    }

    public void setTempoEspera(String TempoEspera) {
        try {
            this.TempoEspera = new BigDecimal(TempoEspera);
        } catch (Exception e) {
            this.TempoEspera = new BigDecimal("0");
        }
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public LocalDate getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(LocalDate Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOperExcl() {
        return OperExcl;
    }

    public void setOperExcl(String OperExcl) {
        this.OperExcl = OperExcl;
    }

    public LocalDate getDt_Excl() {
        return Dt_Excl;
    }

    public void setDt_Excl(LocalDate Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    public String getHr_Excl() {
        return Hr_Excl;
    }

    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    public String getOperFech() {
        return OperFech;
    }

    public void setOperFech(String OperFech) {
        this.OperFech = OperFech;
    }

    public LocalDate getDt_Fech() {
        return Dt_Fech;
    }

    public void setDt_Fech(LocalDate Dt_Fech) {
        this.Dt_Fech = Dt_Fech;
    }

    public String getHr_Fech() {
        return Hr_Fech;
    }

    public void setHr_Fech(String Hr_Fech) {
        this.Hr_Fech = Hr_Fech;
    }

    public String getCodLan() {
        return CodLan;
    }

    public void setCodLan(String CodLan) {
        this.CodLan = CodLan;
    }

    public BigDecimal getPedido() {
        return Pedido;
    }

    public void setPedido(String Pedido) {
        try {
            this.Pedido = new BigDecimal(Pedido);
        } catch (Exception e) {
            this.Pedido = new BigDecimal("0");
        }
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    public int getImpresso() {
        return Impresso;
    }

    public void setImpresso(int Impresso) {
        this.Impresso = Impresso;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getNRedDst() {
        return NRedDst;
    }

    public void setNRedDst(String NRedDst) {
        this.NRedDst = NRedDst;
    }

    public String getRegiaoDst() {
        return RegiaoDst;
    }

    public void setRegiaoDst(String RegiaoDst) {
        this.RegiaoDst = RegiaoDst;
    }

    public String getEntregas() {
        return Entregas;
    }

    public void setEntregas(String Entregas) {
        this.Entregas = Entregas;
    }

    public String getRecolhimentos() {
        return Recolhimentos;
    }

    public void setRecolhimentos(String Recolhimentos) {
        this.Recolhimentos = Recolhimentos;
    }

    public String getTempoCliente() {
        return TempoCliente;
    }

    public void setTempoCliente(String TempoCliente) {
        this.TempoCliente = TempoCliente;
    }

    public String getAntecipado() {
        return Antecipado;
    }

    public void setAntecipado(String Antecipado) {
        this.Antecipado = Antecipado;
    }

    public String getHrChegVei() {
        return HrChegVei;
    }

    public void setHrChegVei(String HrChegVei) {
        this.HrChegVei = HrChegVei;
    }

    public String getHrSaidaVei() {
        return HrSaidaVei;
    }

    public void setHrSaidaVei(String HrSaidaVei) {
        this.HrSaidaVei = HrSaidaVei;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getAssinatura() {
        return Assinatura;
    }

    public void setAssinatura(String Assinatura) {
        this.Assinatura = Assinatura;
    }

    public String getNomeOri() {
        return NomeOri;
    }

    public void setNomeOri(String NomeOri) {
        this.NomeOri = NomeOri;
    }

    public String getEnderecoOri() {
        return EnderecoOri;
    }

    public void setEnderecoOri(String EnderecoOri) {
        this.EnderecoOri = EnderecoOri;
    }

    public String getBairroOri() {
        return BairroOri;
    }

    public void setBairroOri(String BairroOri) {
        this.BairroOri = BairroOri;
    }

    public String getCidadeOri() {
        return CidadeOri;
    }

    public void setCidadeOri(String CidadeOri) {
        this.CidadeOri = CidadeOri;
    }

    public String getUfOri() {
        return UfOri;
    }

    public void setUfOri(String UfOri) {
        this.UfOri = UfOri;
    }

    public String getCepOri() {
        return CepOri;
    }

    public void setCepOri(String CepOri) {
        this.CepOri = CepOri;
    }

    public String getMotivCanc() {
        return MotivCanc;
    }

    public void setMotivCanc(String MotivCanc) {
        this.MotivCanc = MotivCanc;
    }

    public String getHora2() {
        return Hora2;
    }

    public void setHora2(String Hora2) {
        this.Hora2 = Hora2;
    }

    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getMontante() {
        return Montante;
    }

    public void setMontante(String Montante) {
        this.Montante = Montante;
    }

    public String getValorEmb() {
        return ValorEmb;
    }

    public void setValorEmb(String ValorEmb) {
        this.ValorEmb = ValorEmb;
    }

    public String getValorAdv() {
        return ValorAdv;
    }

    public void setValorAdv(String ValorAdv) {
        this.ValorAdv = ValorAdv;
    }

    public String getValorTE() {
        return ValorTE;
    }

    public void setValorTE(String ValorTE) {
        this.ValorTE = ValorTE;
    }

    public String getSolicitante() {
        return Solicitante;
    }

    public void setSolicitante(String Solicitante) {
        this.Solicitante = Solicitante;
    }

    public String getDistancia() {
        return Distancia;
    }

    public void setDistancia(String Distancia) {
        this.Distancia = Distancia;
    }

    public String getLatAtual() {
        return LatAtual;
    }

    public void setLatAtual(String LatAtual) {
        this.LatAtual = LatAtual;
    }

    public String getLonAtual() {
        return LonAtual;
    }

    public void setLonAtual(String LonAtual) {
        this.LonAtual = LonAtual;
    }

    public String getHora1ParadaAtual() {
        return Hora1ParadaAtual;
    }

    public void setHora1ParadaAtual(String Hora1ParadaAtual) {
        this.Hora1ParadaAtual = Hora1ParadaAtual;
    }

    public String getLatAnterior() {
        return LatAnterior;
    }

    public void setLatAnterior(String LatAnterior) {
        this.LatAnterior = LatAnterior;
    }

    public String getLonAnterior() {
        return LonAnterior;
    }

    public void setLonAnterior(String LonAnterior) {
        this.LonAnterior = LonAnterior;
    }

    public String getHora1ParadaAnterior() {
        return Hora1ParadaAnterior;
    }

    public void setHora1ParadaAnterior(String Hora1ParadaAnterior) {
        this.Hora1ParadaAnterior = Hora1ParadaAnterior;
    }

    public String getLatSeguinte() {
        return LatSeguinte;
    }

    public void setLatSeguinte(String LatSeguinte) {
        this.LatSeguinte = LatSeguinte;
    }

    public String getLonSeguinte() {
        return LonSeguinte;
    }

    public void setLonSeguinte(String LonSeguinte) {
        this.LonSeguinte = LonSeguinte;
    }

    public String getHora1ParadaSeguinte() {
        return Hora1ParadaSeguinte;
    }

    public void setHora1ParadaSeguinte(String Hora1ParadaSeguinte) {
        this.Hora1ParadaSeguinte = Hora1ParadaSeguinte;
    }

    public String getTotalMontante() {
        return TotalMontante;
    }

    public void setTotalMontante(String TotalMontante) {
        this.TotalMontante = TotalMontante;
    }

    public String getTotalValorEmb() {
        return TotalValorEmb;
    }

    public void setTotalValorEmb(String TotalValorEmb) {
        this.TotalValorEmb = TotalValorEmb;
    }

    public String getTotalValorAdv() {
        return TotalValorAdv;
    }

    public void setTotalValorAdv(String TotalValorAdv) {
        this.TotalValorAdv = TotalValorAdv;
    }

    public String getTotalValorTE() {
        return TotalValorTE;
    }

    public void setTotalValorTE(String TotalValorTE) {
        this.TotalValorTE = TotalValorTE;
    }

    public String getTotalParadas() {
        return TotalParadas;
    }

    public void setTotalParadas(String TotalParadas) {
        this.TotalParadas = TotalParadas;
    }

    public String getNomeDst() {
        return NomeDst;
    }

    public void setNomeDst(String NomeDst) {
        this.NomeDst = NomeDst;
    }

    public String getEnderecoDst() {
        return EnderecoDst;
    }

    public void setEnderecoDst(String EnderecoDst) {
        this.EnderecoDst = EnderecoDst;
    }

    public String getBairroDst() {
        return BairroDst;
    }

    public void setBairroDst(String BairroDst) {
        this.BairroDst = BairroDst;
    }

    public String getCidadeDst() {
        return CidadeDst;
    }

    public void setCidadeDst(String CidadeDst) {
        this.CidadeDst = CidadeDst;
    }

    public String getUfDst() {
        return UfDst;
    }

    public void setUfDst(String UfDst) {
        this.UfDst = UfDst;
    }

    public String getCepDst() {
        return CepDst;
    }

    public void setCepDst(String CepDst) {
        this.CepDst = CepDst;
    }

    public String getCliOri() {
        return CliOri;
    }

    public void setCliOri(String CliOri) {
        this.CliOri = CliOri;
    }

    public String getCliDst() {
        return CliDst;
    }

    public void setCliDst(String CliDst) {
        this.CliDst = CliDst;
    }

    public String getKm() {
        return Km;
    }

    public void setKm(String Km) {
        this.Km = Km;
    }

    public String getMoeda() {
        return Moeda;
    }

    public void setMoeda(String Moeda) {
        this.Moeda = Moeda;
    }

    public String getDadosBaixa() {
        return DadosBaixa;
    }

    public void setDadosBaixa(String DadosBaixa) {
        this.DadosBaixa = DadosBaixa;
    }

    public String getPedidoCliente() {
        return PedidoCliente;
    }

    public void setPedidoCliente(String PedidoCliente) {
        this.PedidoCliente = PedidoCliente;
    }

    public String getCliFat() {
        return CliFat;
    }

    public void setCliFat(String CliFat) {
        this.CliFat = CliFat;
    }

    public String getGuiasGTVe() {
        return GuiasGTVe;
    }

    public void setGuiasGTVe(String GuiasGTVe) {
        this.GuiasGTVe = GuiasGTVe;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.Sequencia);
        hash = 97 * hash + this.Parada;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Rt_Perc other = (Rt_Perc) obj;
        if (this.Parada != other.Parada) {
            return false;
        }
        if (!Objects.equals(this.Sequencia, other.Sequencia)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        try {
            return NRed + ", " + Hora1.substring(0, 2) + ":" + Hora1.substring(2);
        } catch (Exception e) {
            return NRed + ", " + Hora1;
        }
    }
}

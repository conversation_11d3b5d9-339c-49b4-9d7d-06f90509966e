<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} - #{localemsgs.EscalaDia}</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/rotas.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <ui:fragment rendered="#{solicitacaoSenhaMB.temPermissao}">
                <script
                    src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}">
                </script>
            </ui:fragment>
            <style>
                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -1px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    .tabela .ui-datatable-scrollable-body{
                        height: calc(100% - 6.5em);
                    }
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th {
                        min-width: 100px;
                        max-width: 100px;
                        white-space: normal !important;
                        overflow-wrap: break-word !important;
                    }
                    .DataGrid tbody tr td {
                        min-width: 100px;
                        max-width: 100px;
                        white-space: normal !important;
                        overflow-wrap: break-word !important;
                        word-wrap: break-word !important;
                        -webkit-hyphens: auto !important;
                        -ms-hyphens: auto !important;
                        hyphens: auto !important;
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid tbody tr td:nth-child(1) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }
                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }
                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid tbody tr td:nth-child(3) {
                        min-width: 70px !important;
                        max-width: 70px !important;
                    }
                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid tbody tr td:nth-child(4) {
                        min-width: 70px !important;
                        max-width: 70px !important;
                    }
                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid tbody tr td:nth-child(5) {
                        min-width: 70px !important;
                        max-width: 70px !important;
                    }
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid tbody tr td:nth-child(6) {
                        min-width: 95px !important;
                        max-width: 95px !important;
                    }
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid tbody tr td:nth-child(8) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }
                    .DataGrid thead tr th:nth-child(9),
                    .DataGrid tbody tr td:nth-child(9) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }
                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid tbody tr td:nth-child(10) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }
                    .DataGrid thead tr th:nth-child(11),
                    .DataGrid tbody tr td:nth-child(11) {
                        min-width: 90px !important;
                        max-width: 90px !important;
                    }
                    .DataGrid thead tr th:nth-child(12),
                    .DataGrid tbody tr td:nth-child(12) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .DataGrid thead tr th:nth-child(13),
                    .DataGrid tbody tr td:nth-child(13) {
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }
                    .DataGrid thead tr th:nth-child(14),
                    .DataGrid tbody tr td:nth-child(14) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .DataGrid thead tr th:nth-child(15),
                    .DataGrid tbody tr td:nth-child(15) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }
                    .DataGrid thead tr th:nth-child(16),
                    .DataGrid tbody tr td:nth-child(16) {
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }
                    .DataGrid thead tr th:nth-child(17),
                    .DataGrid tbody tr td:nth-child(17) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .DataGrid thead tr th:nth-child(18),
                    .DataGrid tbody tr td:nth-child(18) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .DataGrid thead tr th:nth-child(19),
                    .DataGrid tbody tr td:nth-child(19) {
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }
                    .DataGrid thead tr th:nth-child(20),
                    .DataGrid tbody tr td:nth-child(20) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .DataGrid thead tr th:nth-child(21),
                    .DataGrid tbody tr td:nth-child(21) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .DataGrid thead tr th:nth-child(22),
                    .DataGrid tbody tr td:nth-child(22) {
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }
                    .DataGrid thead tr th:nth-child(23),
                    .DataGrid tbody tr td:nth-child(23) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .DataGrid thead tr th:nth-child(24),
                    .DataGrid tbody tr td:nth-child(24) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .DataGrid thead tr th:nth-child(25),
                    .DataGrid tbody tr td:nth-child(25) {
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }
                    .DataGrid thead tr th:nth-child(26),
                    .DataGrid tbody tr td:nth-child(26) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .DataGrid thead tr th:nth-child(27),
                    .DataGrid tbody tr td:nth-child(27) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .DataGrid thead tr th:nth-child(28),
                    .DataGrid tbody tr td:nth-child(28) {
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }
                    .DataGrid thead tr th:nth-child(29),
                    .DataGrid tbody tr td:nth-child(29) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .DataGrid thead tr th:nth-child(30),
                    .DataGrid tbody tr td:nth-child(30) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .DataGrid thead tr th:nth-child(31),
                    .DataGrid tbody tr td:nth-child(31) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .DataGrid thead tr th:nth-child(32),
                    .DataGrid tbody tr td:nth-child(32) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .DataGrid thead tr th:nth-child(33),
                    .DataGrid tbody tr td:nth-child(33) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td{
                        text-align: center !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formContrato .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formContrato .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }

                #formPesquisa .ui-radiobutton {
                    background: transparent !important;
                }

                .contratovencidoRow, .DataGrid tbody tr.contratovencidoRow:hover td {
                    color: green !important;
                }

                .tabela .ui-datatable-scrollable-body{
                    height: calc(100% - 9em);
                }

                .botoesDataTable {
                    width: 40px;
                    margin-top: 8px;
                    position: absolute;
                    right: -14px; top: 50%;
                    transform: translateY(-50%);
                }

                .infoSecundaria {
                    color: gray;
                }

                .semPaddingLateral {
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                }

                .debugbox * {
                    border: thin dotted red;
                }

                .inputNumber > input {
                    width: 100px;
                }

                .redColor {
                    color: red !important;
                }

                .blackColor {
                    color: black !important;
                }

                [id*="cadastroEscala"] div[class*="col-md"],
                [id*="formPesquisa"] div[class*="col-md"]{
                    padding: 5px 5px 0px 5px !important;
                }

                [id*="cadastroEscala"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formPesquisa"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }
            </style>
        </h:head>

        <h:body  id="h">
            <p:growl id="msgs"/>

            <div id="body">
                <ui:include src="/botao_panico.xhtml"/>

                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_escaladodia.png" height="40" style="margin-top:-6px !important;" />
                                    <label class="TituloPagina">#{localemsgs.EscalaDia}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{escala.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{escala.filialTela.descricao}<label id="btTrocarFilial">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{escala.filialTela.endereco}</label>
                                    <label class="FilialBairroCidade">#{escala.filialTela.bairro}, #{escala.filialTela.cidade}/#{escala.filialTela.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:commandLink action="#{escala.escalaAnterior}" update="main:tabela cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png"/>
                                    </p:commandLink>

                                    <p:calendar id="calendario"
                                                styleClass="calendario" showOn="button" navigator="true"
                                                converter="conversorData"
                                                pattern="#{mascaras.padraoData}"
                                                value="#{escala.dataTela}"
                                                maxdate="#{escala.ultimoDia}"
                                                locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{escala.selecionarData}" update="main:tabela cabecalho msgs" />
                                    </p:calendar>

                                    <p:commandLink action="#{escala.escalaPosterior}" update="main:tabela cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png"/>
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="e" actionListener="#{escala.buttonAction}" update="cadastroEscala msgs"/>
                    <p:hotkey bind="c" oncomplete="PF('dlgCadastrar').show();" update="cadastroEscala" action="#{escala.novaEscala}"/>
                    <p:hotkey bind="d" action="#{escala.excluir}" update="tabela msgs"/>

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline;">
                                    <p:dataTable
                                        id="tabela"
                                        value="#{escala.escalas}"
                                        var="item"
                                        widgetVar="teste"
                                        selection="#{escala.escalaSelecionada}"
                                        rowStyleClass="#{item.rota.flag_Excl eq '*' ? 'redColor' : 'blackColor'}"
                                        currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Escalas}"
                                        lazy="true"
                                        selectionMode="single"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        paginator="true"
                                        rows="25"
                                        reflow="true"
                                        rowsPerPageTemplate="5,10,15, 20, 25, 50, 100"
                                        paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                        styleClass="tabela"
                                        scrollable="true"
                                        scrollWidth="100%"
                                        paginatorPosition="top"
                                        class="tabela DataGrid"
                                        style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                        >
                                        <p:ajax event="rowDblselect" listener="#{escala.dblSelect}" update="msgs cadastroEscala" />

                                        <p:column headerText="#{localemsgs.CodFil}">
                                            <h:outputText value="#{item.escala.codFil}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Rota}">
                                            <h:outputText value="#{item.escala.rota}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.TpVeic}">
                                            <h:outputText value="#{item.rota.tpVeic}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Veiculo}">
                                            <h:outputText value="#{item.escala.veiculo}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Placa}">
                                            <h:outputText value="#{item.veiculo.placa}" converter="conversorPlaca"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Data}">
                                            <h:outputText value="#{item.escala.data}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora1}" >
                                            <h:outputText value="#{item.escala.hora1}" converter="conversorHora" style="text-align: center"/>
                                        </p:column >
                                        <p:column headerText="#{localemsgs.Hora2}">
                                            <h:outputText value="#{item.escala.hora2}" converter="conversorHora" style="text-align: center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora3}">
                                            <h:outputText value="#{item.escala.hora3}" converter="conversorHora" style="text-align: center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora4}">
                                            <h:outputText value="#{item.escala.hora4}" converter="conversorHora" style="text-align: center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Total}">
                                            <h:outputText value="#{item.escala.hsTot}" converter="conversorHora">
                                                <f:convertNumber type="number" minFractionDigits="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.hs_interv}">
                                            <h:outputText value="#{item.escala.hsInterv}" converter="conversorHora">
                                                <f:convertNumber pattern="00"/>
                                            </h:outputText>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.MatrChe}">
                                            <h:outputText value="#{item.escala.matrChe}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NomeChefe}">
                                            <h:outputText value="#{item.chefeEquipe.nome}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HrChe}">
                                            <h:outputText value="#{item.escala.hrChe}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.MatrMot}">
                                            <h:outputText value="#{item.escala.matrMot}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NomeMot}">
                                            <h:outputText value="#{item.motorista.nome}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HrMot}">
                                            <h:outputText value="#{item.escala.hrMot}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.MatrVig1}">
                                            <h:outputText value="#{item.escala.matrVig1}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NomeVig1}">
                                            <h:outputText value="#{item.vigilante1.nome}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HrVig1}">
                                            <h:outputText value="#{item.escala.hrVig1}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.MatrVig2}">
                                            <h:outputText value="#{item.escala.matrVig2}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NomeVig2}">
                                            <h:outputText value="#{item.vigilante2.nome}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HrVig2}">
                                            <h:outputText value="#{item.escala.hrVig2}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.MatrVig3}">
                                            <h:outputText value="#{item.escala.matrVig3}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NomeVig3}">
                                            <h:outputText value="#{item.vigilante3.nome}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HrVig3}">
                                            <h:outputText value="#{item.escala.hrVig3}">
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Codigo}">
                                            <h:outputText value="#{item.escala.codPessoaSup}">
                                                <f:convertNumber type="number" minFractionDigits="0"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Supervisor}">
                                            <h:outputText value="#{item.motorista.nome}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Horario}">
                                            <h:outputText value="#{item.escala.hrChe}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}">
                                            <h:outputText value="#{item.escala.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}">
                                            <h:outputText value="#{item.escala.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}">
                                            <h:outputText value="#{item.escala.hr_Alter}" converter="conversorHora"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" oncomplete="PF('dlgCadastrar').show();"
                                           update="cadastroEscala" action="#{escala.novaEscala}">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{escala.buttonAction}"
                                           update="cadastroEscala msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Excluir}" action="#{escala.excluir}"
                                           update="tabela msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                                <p:confirm header="#{localemsgs.Confirmacao}"
                                           message="#{localemsgs.ExcluirEscala}"
                                           icon="ui-icon-alert" />
                            </p:commandLink>
                            <p:confirmDialog global="true">
                                <f:facet name="message">
                                    <h:outputLabel value="#{localemsgs.ExcluirEscala}"/>
                                    <br/>
                                    <h:outputLabel value="#{localemsgs.excluirEscala}"/>
                                </f:facet>
                                <p:commandButton value="#{localemsgs.Sim}" type="button"
                                                 styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button"
                                                 styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                           actionListener="#{escala.ativarModalPesquisa()}"
                                           update="msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{escala.limparFiltro()}"
                                           update="msgs :main:tabela corporativo:checkboxes formPesquisa:pesquisar">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <!--NOVA MODAL CADASTRO ESCALAS-->
                <h:form id="cadastroEscala">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog id="dlgCadastrar" widgetVar="dlgCadastrar" positionType="absolute" responsive="true" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="false" class="dialogoGrande" style="overflow: hidden !important;">
                        <div class="col-md-12" style="padding:0px !important; overflow-y: auto; overflow-x: hidden !important; max-height: 70vh;">
                            <f:facet name="header">
                                <img src="../assets/img/icones_satmob_carroforte.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.CadastrarEscala}" style="color:black" />
                            </f:facet>

                            <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                </p:confirmDialog>

                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <p:outputLabel for="filial" value="#{localemsgs.Filial}"  />
                                    <p:selectOneMenu id="filial" value="#{escala.filial}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                     filter="true" filterMatchMode="contains" style="width: 100%" label="">
                                        <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                                       itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" listener="#{escala.selecionarFilial}" />
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="data" value="#{localemsgs.Data}" />
                                    <p:inputMask id="data" value="#{escala.escala.data}" mask="99/99/9999"
                                                 required="true" label="#{localemsgs.Data}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                 style="width: 100%"
                                                 maxlength="8" placeholder="00/00/0000"
                                                 converter="conversorData" disabled="true"/>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="rota" value="#{localemsgs.Rota}"  />
                                    <p:selectOneMenu id="rota" value="#{escala.rotaSelecionada}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Rota}"
                                                     style="width: 100%" label="">
                                        <f:selectItems value="#{escala.listaRotas}" var="rotaSelecao" itemValue="#{rotaSelecao}"
                                                       itemLabel="#{rotaSelecao.rota}"
                                                       />
                                        <p:ajax event="itemSelect" update="cadastroEscala:cadastrar msgs" listener="#{escala.selecionarRota}"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="rota" value="#{localemsgs.Sequencia}"  />
                                    <p:inputText id="rotaSeq" value="#{escala.rotaSelecionada.sequencia}"
                                                 label="#{localemsgs.Rota}" style="width: 100%"
                                                 disabled="true" converter="conversor0">
                                        <p:watermark for="rotaSeq" value="#{localemsgs.Rota}"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="totalhr" value="#{localemsgs.Hr_Total}" escape="false"/>
                                    <p:inputText id="totalhr" value="#{escala.escala.hsTot}"
                                                 maxlength="4" style="width: 100%"
                                                 disabled="true">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrLargada" value="#{localemsgs.Horario}"/>
                                    <p:inputMask id="hrLargada" value="#{escala.escala.hora1}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true" placeholder="00:00"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Largada}"
                                                 maxlength="4" style="width: 100%" >
                                        <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrChegada" value="#{localemsgs.a}" style="width: 100%"/>
                                    <p:inputMask id="hrChegada" value="#{escala.escala.hora4}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true"  placeholder="00:00"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Chegada}"
                                                 maxlength="4" style="width: 100%">
                                        <p:ajax process="@this" update="msgs totalhr" partialSubmit="true"
                                                listener="#{escala.calculaHorasTrabalhadas}" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrIntIni" value="#{localemsgs.Intervalo}"/>
                                    <p:inputMask id="hrIntIni" value="#{escala.escala.hora2}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true" placeholder="00:00" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Intini}"
                                                 maxlength="4" >
                                        <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrIntFim" value="#{localemsgs.a}" style="width: 100%"/>
                                    <p:inputMask id="hrIntFim" value="#{escala.escala.hora3}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true"  placeholder="00:00"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_IntFim}"
                                                 maxlength="4" style="width: 100%">
                                        <p:ajax process="@this" update="msgs totalhr" partialSubmit="true"
                                                listener="#{escala.calculaHorasTrabalhadas}" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="motorista" value="#{localemsgs.Motorista}"/>
                                    <p:inputText id="matrMotorista" value="#{escala.motorista.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>                                
                                <div class="col-md-6 col-sm-6 col-xs-6">
                                    <p:outputLabel for="motorista" value="#{localemsgs.Motorista}" style="opacity: 0"/>
                                    <p:autoComplete id="motorista" value="#{escala.motorista}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{escala.buscarMotorista}"
                                                    var="motoristaSelecao" itemLabel="#{motoristaSelecao.nome}" itemValue="#{motoristaSelecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{escala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{escala.selecionarMotorista}"
                                                update="matrMotorista hrMotorista motorista msgs"/>
                                        <p:watermark for="motorista" value="#{localemsgs.Motorista}" />
                                    </p:autoComplete>
                                </div>                                
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrMotorista" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrMotorista" value="#{escala.escala.hrMot}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>                  
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="chEquipe" value="#{localemsgs.ChEquipe}"/>
                                    <p:inputText id="matrChEquipe" value="#{escala.chEquipe.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="chEquipe" value="#{localemsgs.ChEquipe}" style="opacity:0"/>
                                    <p:autoComplete id="chEquipe" value="#{escala.chEquipe}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{escala.buscarChEquip}"
                                                    var="chEquipeSelecao" itemLabel="#{chEquipeSelecao.nome}" itemValue="#{chEquipeSelecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{escala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{escala.selecionarChEquipe}"
                                                update="matrChEquipe hrChEquipe panelInfoChEquipe chEquipe msgs"/>
                                        <p:watermark for="chEquipe" value="#{localemsgs.ChEquipe}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrChEquipe" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrChEquipe" value="#{escala.escala.hrChe}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>

                                <p:panel id="panelInfoChEquipe" class="col-md-12 col-sm col-xs-12" style="background: lightyellow; border: thin solid orangered; padding:4px 10px 10px 10px !important; margin-top: 6px; margin-bottom: 6px; margin-left: 5px; width:calc(100%  - 10px) !important; display:#{escala.infoChEquipe?'':'none'}">
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="codigoChEquipe" value="#{localemsgs.CodPessoa}: " rendered="#{escala.infoChEquipe}"/>
                                        <p:inputText id="codigoChEquipe" value="#{escala.chEquipe.codigo}" converter="conversor0"
                                                     disabled="true" style="width: 100%" rendered="#{escala.infoChEquipe}"/>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-12">
                                        <p:outputLabel for="senhaChEquipe" value="#{localemsgs.SenhaMobile}: " rendered="#{escala.infoChEquipe}"/>
                                        <p:inputText id="senhaChEquipe" value="#{escala.chEquipe.PWWeb}" rendered="#{escala.infoChEquipe}"
                                                     disabled="true" style="width: 100%"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="permissaoChEquipe" value="#{localemsgs.PermissaoRotas}: " rendered="#{escala.infoChEquipe}"/>
                                        <p:inputText id="permissaoChEquipe" value="#{escala.permissaoRota ? localemsgs.OK :  localemsgs.Nao}" rendered="#{escala.infoChEquipe}"
                                                     disabled="true" style="width: 100%; #{escala.permissaoRota ? 'color:green' : 'color:red'}"/>
                                    </div>
                                </p:panel>

                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante1" value="#{localemsgs.Vigilante1}"/>
                                    <p:inputText id="matrVigilante1" value="#{escala.vigilante1.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="vigilante1" value="#{localemsgs.Vigilante1}" style="opacity: 0"/>
                                    <p:autoComplete id="vigilante1" value="#{escala.vigilante1}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{escala.buscarVigilante}"
                                                    var="vigilante1Selecao" itemLabel="#{vigilante1Selecao.nome}" itemValue="#{vigilante1Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{escala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{escala.selecionarVigilante1}"
                                                update="matrVigilante1 hrVigilante1 msgs"/>
                                        <p:watermark for="vigilante1" value="#{localemsgs.Vigilante1}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante1" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrVigilante1" value="#{escala.escala.hrVig1}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante2" value="#{localemsgs.Vigilante2}"/>
                                    <p:inputText id="matrVigilante2" value="#{escala.vigilante2.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="vigilante2" value="#{localemsgs.Vigilante2}" style="opacity:0"/>
                                    <p:autoComplete id="vigilante2" value="#{escala.vigilante2}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{escala.buscarVigilante}"
                                                    var="vigilante2Selecao" itemLabel="#{vigilante2Selecao.nome}" itemValue="#{vigilante2Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{escala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{escala.selecionarVigilante2}"
                                                update="matrVigilante2 hrVigilante2 msgs"/>
                                        <p:watermark for="vigilante2" value="#{localemsgs.Vigilante2}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante2" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrVigilante2" value="#{escala.escala.hrVig2}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante3" value="#{localemsgs.Vigilante3}"/>
                                    <p:inputText id="matrVigilante3" value="#{escala.vigilante3.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="vigilante3" value="#{localemsgs.Vigilante3}" style="opacity: 0"/>
                                    <p:autoComplete id="vigilante3" value="#{escala.vigilante3}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{escala.buscarVigilante}"
                                                    var="vigilante3Selecao" itemLabel="#{vigilante3Selecao.nome}" itemValue="#{vigilante3Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{escala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{escala.selecionarVigilante3}"
                                                update="matrVigilante3 hrVigilante3 msgs"/>
                                        <p:watermark for="vigilante3" value="#{localemsgs.Vigilante3}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante3" value="#{localemsgs.Hora}: "/>
                                    <p:inputMask id="hrVigilante3" value="#{escala.escala.hrVig3}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="veiculo" value="#{localemsgs.Veiculo}"/>
                                    <p:inputText id="numeroVeiculo" value="#{escala.veiculo.numero}" 
                                                 disabled="true"
                                                 style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="veiculo" value="#{localemsgs.Veiculo}" style="opacity: 0"/>
                                    <p:selectOneMenu id="veiculo" 
                                                     value="#{escala.veiculo}" 
                                                     converter="omnifaces.SelectItemsConverter"
                                                     filter="true" 
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Veiculo}"
                                                     filterMatchMode="contains" style="width: 100%" label="">
                                        <f:selectItems value="#{escala.listaVeiculos}" var="veiculoSelecao" itemValue="#{veiculoSelecao}"
                                                       itemLabel="#{veiculoSelecao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" update="numeroVeiculo msgVeiculo msgs" listener="#{escala.selecionarVeiculo}"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="padding-top: 22px !important">
                                    <h:outputText id="msgVeiculo" value="#{escala.msgVeiculo}"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="text-align: left; padding-top: 8px !important;">
                                    <p:selectBooleanCheckbox value="#{escala.infoChEquipe}" id="senhaMobile"
                                                             style="width: 30px; text-align: left">
                                        <p:ajax update="panelInfoChEquipe" event="change" oncomplete="PF('dlgCadastrar').initPosition()"/>
                                    </p:selectBooleanCheckbox>
                                    <p:outputLabel for="senhaMobile" value="#{localemsgs.senhaMobile}" style="color: orangered" />
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="text-align: right">
                                    <p:commandLink
                                        rendered="#{escala.flagEscala eq 1}"
                                        id="cadastroEscalaAction"
                                        update="main:tabela msgs"
                                        actionListener="#{escala.cadastrarEscala(false)}"
                                        title="#{localemsgs.Cadastrar}" styleClass="btn btn-primary" style="color:#FFF !important">
                                        <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                    </p:commandLink>

                                    <p:commandLink
                                        rendered="#{escala.flagEscala eq 2}"
                                        id="editEscalaAction"
                                        actionListener="#{escala.editarEscala(false)}"
                                        update=":msgs main:tabela cadastrar"
                                        title="#{localemsgs.Editar}" styleClass="btn btn-primary" style="color:#FFF !important">
                                        <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                    </p:commandLink>

                                </div>
                            </p:panel>
                        </div>
                    </p:dialog>

                    <p:dialog widgetVar="dlgFuncionarioFolga" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.FuncionariosFolga}" style="color:black" />
                        </f:facet>
                        <p:panel id="panelFuncionarioFolga" style="background-color: transparent" styleClass="cadastrar2">
                            <h:outputText value="#{escala.folgas.size() > 1 ? localemsgs.AceitarFuncionariosFolga :  localemsgs.AceitarFuncionarioFolga}"/>
                            <p:dataTable id="tabelaFuncionarioFolga" value="#{escala.folgas}"
                                         style="font-size: 12px" var="folgas"
                                         styleClass="tabela" scrollWidth="100%"
                                         resizableColumns="true" scrollable="true" scrollHeight="85" >
                                <p:column headerText="#{localemsgs.Matr}" style="font-size: 12px;">
                                    <h:outputText value="#{folgas.matr}" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Funcion}">
                                    <h:outputText value="#{folgas.nome_Guer}"/>
                                </p:column>
                            </p:dataTable>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:commandLink title="#{localemsgs.Avancar}" rendered="#{escala.flagEscala eq 1}"
                                               update="main:tabela :msgs" actionListener="#{escala.cadastrarEscala(true)}">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>

                                <p:commandLink title="#{localemsgs.Avancar}" rendered="#{escala.flagEscala eq 2}"
                                               update="main:tabela :msgs" actionListener="#{escala.editarEscala(true)}">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>

                                <p:commandLink title="#{localemsgs.Cancelar}" oncomplete="PF('dlgFuncionarioFolga').hide()">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>
                            </p:panelGrid>

                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formPesquisa">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisa').hide()"/>

                    <p:dialog
                        widgetVar="dlgPesquisa"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false" width="400"
                        style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_tesouraria_entradas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText
                                value="#{localemsgs.Pesquisar}"
                                style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"
                                />
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{escala.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Rota}" itemValue="rota" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="nome" />
                                        <f:selectItem itemLabel="#{localemsgs.NRed}" itemValue="nred" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{escala.chavePesquisa eq 'rota'}" value="#{localemsgs.Rota}: "/>
                                        <p:outputLabel for="opcao" rendered="#{escala.chavePesquisa eq 'nome'}" value="#{localemsgs.Nome}: "/>
                                        <p:outputLabel for="opcao" rendered="#{escala.chavePesquisa eq 'nred'}" value="#{localemsgs.NRed}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{escala.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-top: 0px !important; padding-bottom: 0px !important;">
                                <p:commandLink action="#{escala.pesquisar()}"
                                               update="main:tabela msgs"
                                               oncomplete="PF('dlgPesquisa').hide()"
                                               title="#{localemsgs.Pesquisar}" styleClass="btn btn-primary" style="color: #FFF">
                                    <i class="fa fa-search" style="margin-right:8px !important"></i>#{localemsgs.Pesquisar}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--MODAL VEICULOS - NÃO USADA! -->
                <p:dialog widgetVar="dlgVeiculos"
                          id="dlgVeiculos"
                          draggable="false"
                          modal="true"
                          closable="true"
                          resizable="false"
                          dynamic="true"
                          showEffect="drop"
                          hideEffect="drop"
                          closeOnEscape="true"
                          width="480px"
                          style="background-size: 720px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_escaladodia.png" height="40" width="40"/>
                        <p:spacer width="20px"/>
                        <h:outputText value="#{localemsgs.Veiculo}"  />
                    </f:facet>
                    <p:panel id="cadastrarVeiculo" style="background-color: transparent">
                        <h:form id="cadastroVeiculo" class="form-inline">
                            <div class="form-inline">
                                <p:outputLabel value="#{localemsgs.CodFil}:" style="color: black; float:left;position:absolute;" />
                                <p:inputText value="#{escala.filial.descricao}" style="color: black; float:left;left:110px;position:absolute;width: 357px"
                                             disabled="true"/>
                            </div>

                            <p:spacer height="40px"/>

                            <div class="form-inline">
                                <p:outputLabel value="#{localemsgs.Placa}:" style="color: black; float:left;position:absolute;" />
                                <p:inputMask mask="aaa-9999" placeholder="AAA-0000" value="#{escala.novoVeiculo.placa}"
                                             maxlength="7"  style="color: black; float:left;left:110px;position:absolute;text-transform: uppercase">
                                </p:inputMask>

                                <p:outputLabel value="#{localemsgs.numero}:"  style="color: black; float:left;position:absolute;left: 323px"/>
                                <p:inputText id="numVei" value="#{escala.novoVeiculo.numero}"
                                             style="color: black; float:left;position:absolute;left: 387px;width: 81px"
                                             maxlength="5"  disabled="true" placeholder="#{localemsgs.numero}">
                                </p:inputText>
                            </div>

                            <p:spacer height="40px"/>

                            <div class="form-inline">
                                <p:outputLabel value="#{localemsgs.Cidade}:" style="color: black; float:left;position:absolute;"/>
                                <p:autoComplete id="municipio" value="#{escala.municipios}" completeMethod="#{escala.listarMunicipios}"
                                                required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}"
                                                var="municipio" placeholder="#{localemsgs.Cidade}"
                                                itemLabel="#{municipio.nome}" itemValue="#{municipio}" scrollHeight="250"
                                                style="color: black; float:left; left: 110px; position:absolute;width: 244px"
                                                converter="conversorMunicipios">
                                    <p:ajax partialSubmit="true" process="@this" update="msgs ufPlaca"
                                            event="itemSelect" listener="#{escala.selecionaMunicipio}"/>
                                </p:autoComplete>

                                <p:outputLabel value="#{localemsgs.Estado}:" style="color: black; float:left;position:absolute;left: 363px"/>
                                <p:inputText id="ufPlaca" value="#{escala.municipios.UF}" maxlength="2" size="2" placeholder="#{localemsgs.UF}"
                                             style="color: black; float:left;left:415px;position:absolute" disabled="true"/>

                            </div>

                            <p:spacer height="40px"/>

                            <div class="form-inline">
                                <p:commandLink id="insereVeiculo"
                                               update="msgs panelVeiculos " action="#{escala.cadastraVeic}"
                                               title="#{localemsgs.Editar}"
                                               style="float:left; left:14px;position:absolute">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>

                            <p:spacer height="50px"/>

                            <div class="form-inline" style="font-size: 14px; font-weight: bold; color: black; height: 40px;">
                                <p:panel style="background-color: transparent; border:none !important">
                                    <img src="../assets/img/icones_satmob_carroforte.png" height="30" width="30"/>
                                    <p:spacer width="5"/>
                                    <h:outputText value="#{localemsgs.Veiculo}"/>
                                </p:panel>
                            </div>

                            <p:panel id="panelVeiculos">
                                <p:dataTable id="veiculos" value="#{escala.listaVeiculos}" var="listaVeiculos" emptyMessage="#{localemsgs.SemRegistros}"
                                             sortBy="#{listaVeiculos.numero}" rowKey="#{listaVeiculos.numero}"
                                             styleClass="tabela" resizableColumns="true" scrollable="true" scrollHeight="130"
                                             style="font-size: 12px; background-color: transparent; ">
                                    <p:column headerText="#{localemsgs.CodFil}" style="width: 42px;">
                                        <h:outputLabel value="#{listaVeiculos.codFil.toPlainString()}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.numero}" style="width: 50px;">
                                        <h:outputLabel value="#{listaVeiculos.numero}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Placa}" style="width: 60px;">
                                        <h:outputLabel value="#{listaVeiculos.placa}" converter="conversorPlaca"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.UF}" style="width: 15px;">
                                        <h:outputLabel value="#{listaVeiculos.UF_Placa}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cidade}" style="width: 70px;">
                                        <h:outputLabel value="#{listaVeiculos.mun_Placa}"/>
                                    </p:column>
                                </p:dataTable>
                            </p:panel>
                        </h:form>
                    </p:panel>
                </p:dialog>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <p:outputPanel id="checkboxes">
                                <h:outputText value="#{localemsgs.Corporativo}: " />
                                <p:spacer width="5px"/>
                                <p:selectBooleanCheckbox value="#{escala.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela" listener="#{escala.mostrarFiliais()}" />
                                </p:selectBooleanCheckbox>

                                <p:spacer width="15px"/>

                                <h:outputText value="#{localemsgs.SomenteAtivas}: " />
                                <p:spacer width="5px"/>
                                <p:selectBooleanCheckbox value="#{escala.apenasAtivos}">
                                    <p:ajax update="msgs main:tabela" listener="#{escala.mostrarExcluidos()}" />
                                </p:selectBooleanCheckbox>
                            </p:outputPanel>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>
        </h:body>
    </f:view>
</html>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1020 {

    private String evtTabLotacao_Id;
    private int sucesso;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideLotacao_codLotacao;
    private String ideLotacao_iniValid;
    /**
     * Tabela 10
     */
    private String dadosLotacao_tpLotacao;
    /**
     * Preencher com o código correspondente ao tipo de inscrição, conforme
     * tabela 5 Validação: O campo não deve ser preenchido se {tpLotacao} for
     * igual a [01,10, 21, 24, 90, 91]. Nos demais casos, observar conteúdo
     * exigido para o campo {nrInsc}, conforme Tabela 10 - Tipos de Lotação
     * Tributária. Valores Válidos: 1, 2, 4
     */
    private String dadosLotacao_tpInsc;
    /**
     * Preencher com o número de Inscrição (CNPJ, CPF, CNO) ao qual pertence a
     * lotação tributária, conforme indicado na tabela 10 - Tipos de Lotação
     * Tributária. Validação: a) Deve ser preenchido de acordo com o conteúdo
     * exigido, conforme especificado no campo {tpInsc} e na tabela de tipos de
     * Lotação Tributária. b) Deve ser um identificador válido, constante das
     * bases da RFB
     */
    private String dadosLotacao_nrInsc;
    private String dadosLotacao_razaoSocial;
    /**
     * Preencher com o código relativo ao FPAS. Validação: Deve ser um código
     * FPAS válido, conforme tabela 4
     */
    private String fpasLotacao_fpas;
    /**
     * Preencher com o código de Terceiros, conforme tabela 4, já considerando a
     * existência de eventuais convênios para recolhimento direto. Exemplo: Se
     * ocontribuinte está enquadrado com FPAS 507, cujo código cheio de
     * Terceiros é 0079, se possuir convênio com Senai deve informar o código
     * 0075. Validação: O código de terceiros informado deve ser compatível com
     * o código de FPAS informado, conforme tabela 4
     */
    private String fpasLotacao_codTercs;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtTabLotacao_Id() {
        return evtTabLotacao_Id;
    }

    public void setEvtTabLotacao_Id(String evtTabLotacao_Id) {
        this.evtTabLotacao_Id = evtTabLotacao_Id;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeLotacao_codLotacao() {
        return ideLotacao_codLotacao;
    }

    public void setIdeLotacao_codLotacao(String ideLotacao_codLotacao) {
        this.ideLotacao_codLotacao = ideLotacao_codLotacao;
    }

    public String getIdeLotacao_iniValid() {
        return ideLotacao_iniValid;
    }

    public void setIdeLotacao_iniValid(String ideLotacao_iniValid) {
        this.ideLotacao_iniValid = ideLotacao_iniValid;
    }

    public String getDadosLotacao_tpLotacao() {
        return dadosLotacao_tpLotacao;
    }

    public void setDadosLotacao_tpLotacao(String dadosLotacao_tpLotacao) {
        this.dadosLotacao_tpLotacao = dadosLotacao_tpLotacao;
    }

    public String getDadosLotacao_tpInsc() {
        return dadosLotacao_tpInsc;
    }

    public void setDadosLotacao_tpInsc(String dadosLotacao_tpInsc) {
        this.dadosLotacao_tpInsc = dadosLotacao_tpInsc;
    }

    public String getDadosLotacao_nrInsc() {
        return dadosLotacao_nrInsc;
    }

    public void setDadosLotacao_nrInsc(String dadosLotacao_nrInsc) {
        this.dadosLotacao_nrInsc = dadosLotacao_nrInsc;
    }

    public String getDadosLotacao_razaoSocial() {
        return dadosLotacao_razaoSocial;
    }

    public void setDadosLotacao_razaoSocial(String dadosLotacao_razaoSocial) {
        this.dadosLotacao_razaoSocial = dadosLotacao_razaoSocial;
    }

    public String getFpasLotacao_fpas() {
        return fpasLotacao_fpas;
    }

    public void setFpasLotacao_fpas(String fpasLotacao_fpas) {
        this.fpasLotacao_fpas = fpasLotacao_fpas;
    }

    public String getFpasLotacao_codTercs() {
        return fpasLotacao_codTercs;
    }

    public void setFpasLotacao_codTercs(String fpasLotacao_codTercs) {
        this.fpasLotacao_codTercs = fpasLotacao_codTercs;
    }
}

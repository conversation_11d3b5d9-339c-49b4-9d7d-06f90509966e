<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/MonetaIcon.png" />
            <title>MSL</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/param.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <style>
                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    .sate-login-primo{
                        margin-top:150px !important;
                    }
  
                    .btnLogar{
                        position:absolute;
                        right:0px;
                    }

                    .rodape-links{
                        position:absolute;
                        left:0px;
                        width:88px;
                    }
                }
            </style>
        </h:head>
        <h:body id="h" style="background: #FFF !important;">
            <f:metadata>
                <f:viewAction action="#{login.ListarFiliais}" />
            </f:metadata>
            <p:growl id="msgs" />
            <h:form id="formFil" class="form-inline" style="background: #FFF !important;">
                <div class="col-md-12 col-sm-12 col-xs-12" style="width: 90%; max-width: 520px; height:420px; position: absolute; top:0;right:0;bottom:0;left:0;margin:auto; border: thin solid #DDD; border-radius: 4px;; box-shadow: 3px 3px 4px #EEE; background-color: #EFEFEF !important;">
                    <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: left; padding-top: 58px;">
                        <img src="../assets/img/LogoMonetaLogistics.png" style="width: 450px; margin-left: 4px; margin-bottom: 28px" />    
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 5px !important">
                        <div class="mt" style="color:black; font-size: 12pt !important">#{localemsgs.Empresa}</div>
                        <p:selectOneMenu id="param" value="#{login.empresa}" converter="omnifaces.SelectItemsConverter" style="height: 40px;"
                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Empresa}" filter="true"
                                         filterMatchMode="contains" >
                            <f:selectItems value="#{login.empresas}" var="empresas" itemValue="#{empresas}"
                                           itemLabel="#{login.NomeEmpresa(empresas.bancoDados)}"  noSelectionValue="Selecione"/>
                            <p:ajax event="itemSelect" listener="#{login.ListarFiliais}" update="formFil :msgs" />
                        </p:selectOneMenu>
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 12px !important">
                        <p:focus context="formFil:subFil"/>
                        <div class="mt" style="color:black; font-size: 12pt !important">#{localemsgs.Filial}</div>

                        <p:selectOneMenu id="subFil" value="#{login.filial}" converter="omnifaces.SelectItemsConverter" 
                                         required="true" 
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                         style="width: 100%; height: 40px;"
                                         filter="true" filterMatchMode="contains" >
                            <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                           itemLabel="#{filiais.descricao}"  noSelectionValue="Selecione"/>
                        </p:selectOneMenu>
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 12px !important">
                        <p:commandButton class="custom-button" value="#{localemsgs.Entrar}" id="btnLogar" 
                                         action="#{login.SelecionarFilialMSL}" style="width: calc(100% - 50px) !important; float: left; font-size: 14pt !important; background-color: #F36C25 !important; border: none !important; color: #FFF !important"
                                         update=":msgs"/>
                        <h:commandLink actionListener="#{localeController.increment}" 
                                        action="#{localeController.getLocales}">
                             <p:graphicImage url="../assets/img/#{localeController.number}.png" style="float: right; width: 40px; background: #FFF; padding: 2px; border: thin solid #CCC; border-radius: 4px;" />
                         </h:commandLink>
                    </div>
                </div>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view> 
</html>

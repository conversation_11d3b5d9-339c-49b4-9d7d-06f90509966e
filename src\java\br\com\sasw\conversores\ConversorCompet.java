/*
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.LocaleController;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.ResolverStyle;
import java.util.Date;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorCompet")
public class ConversorCompet implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return value;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        DateTimeFormatter data = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter df = DateTimeFormatter.ofPattern("MMMM/yyyy", LocaleController.getsCurrentLocale());
        df.withResolverStyle(ResolverStyle.LENIENT);

        try {
            try {
                LocalDate dia = LocalDate.parse(value.toString() + "-01", data);
                return dia.format(df);
            } catch (Exception ex) {
                LocalDate dia = ((Date) value).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                return dia.format(df);
            }
        } catch (Exception e) {
            LocalDate dia = LocalDate.parse(value.toString(), data);
            return dia.format(df);
        }
    }
}

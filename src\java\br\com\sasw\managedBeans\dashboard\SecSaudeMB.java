/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.dashboard;

import Arquivo.ArquivoLog;
import Controller.CofreInteligente.DashBoardsSatMobWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeansCompostas.DashboardCharts;
import Controller.DashBoard.SecSaude;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Named(value = "dashboardSecSaude")
@ViewScoped
public class SecSaudeMB implements Serializable {

    private Persistencia persistencia;
    private Map filters, filtroMovimentacoes;
    private Filiais filiais;
    private String codfil, caminho, banco, operador, dia, mes, ano;

    private final BigDecimal codpessoa;

    private final ArquivoLog logerro;
    private String log, colors;
    private List<DashboardCharts> imoveisSituacao;
    private final SecSaude secSaude;
    private Persistencia pesistencia;
    DecimalFormat df = new DecimalFormat("#.##");

    private String dataImoveisSitAgp, labelsImoveisSitAgp, dataImoveisSitDia, labelsImoveisSitDia, labelsImoveisdiaTrab,
            dataInspecaoTratAgp, labelInspecaoTratAgp, dataDepositoTipoAgp, labelDepositoTipoAgp, labelsImoveisInspecaoTratamentoTipo,
            labelsNumeroDepositosEvolucao, dataDepositoAcaoAgp, labelDepositoAcaoAgp, labelsEvolucaoDespositosTipo, labelsamostrasRoedoresAmostras,
            dataInspecionadosxDengue, labelInspecionadosxDengue;

    public String getDataInspecionadosxDengue() {
        return dataInspecionadosxDengue;
    }

    public void setDataInspecionadosxDengue(String dataInspecionadosxDengue) {
        this.dataInspecionadosxDengue = dataInspecionadosxDengue;
    }

    public String getLabelInspecionadosxDengue() {
        return labelInspecionadosxDengue;
    }

    public void setLabelInspecionadosxDengue(String labelInspecionadosxDengue) {
        this.labelInspecionadosxDengue = labelInspecionadosxDengue;
    }

    public String getLabelsamostrasRoedoresAmostras() {
        return labelsamostrasRoedoresAmostras;
    }

    public void setLabelsamostrasRoedoresAmostras(String labelsamostrasRoedoresAmostras) {
        this.labelsamostrasRoedoresAmostras = labelsamostrasRoedoresAmostras;
    }

    public SecSaudeMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        secSaude = new SecSaude();
        logerro = new ArquivoLog();

        dia = getDataAtual("TELA").split("/")[0];
        mes = getDataAtual("TELA").split("/")[1];
        ano = getDataAtual("TELA").split("/")[2];
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;

            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            if (this.codfil.equals("")) {
                this.filiais = null;
            } else {
                this.filiais = this.secSaude.buscaInfoFilial(this.codfil, this.persistencia);
            }

            this.filters = new HashMap();

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void carregarGraficos() {
        this.colors = "'#449D44', '#ec971f', '#c9302c', '#1AA6C9', '#C4EAFC', '#118969', '#8798E5',\n"
                + "'#A1EA8F', '#F711A3', '#05AA68', '#0F8E62', '#99F7B5', '#ED2180', '#F2A98E', '#C45F36', '#DAE876', '#A8FF99', '#41A6A8',\n"
                + "'#EAB7F4', '#A6B8EA', '#BF4609', '#EDA171', '#99FCC3', '#FCC2BD', '#F226AB', '#0B8957', '#D308A0', '#D1FFA0', '#57F9A3',\n"
                + "'#EFBC94', '#D677E5', '#F4B897', '#4A72D4', '#71CC4D', '#7DF2E4', '#A3F989', '#F9F348', '#5B94C6', '#A9E3F2', '#14E0C5',\n"
                + "'#E5ED95', '#197D84', '#E6F489', '#FCD8C4', '#EA8435', '#64EAC6', '#D87A5B', '#DFE52D', '#6E7BD3', '#ADD7FF', '#62EF8F'";

        gerarInfImoveisSituacaoAgp();
        gerarInfImoveisSituacaoDia();
        gerarInfImoveisDiaTrab();
        gerarInspecaoTratamentoAgp();
        gerarDepositoTipoAgp();
        gerarImoveisInspecaoTratamentoTipo();
        gerarNumeroDepositosEvolucao();
        gerarDepositosAcaoAgp();
        gerarAmostrasRoedoresAmostras();
        gerarInspecionadosxdengue();
        gerarevolucaoDepositosTipo();
    }

    public void gerarInfImoveisSituacaoAgp() {
        try {
            this.imoveisSituacao = this.secSaude.obterImoveisSituacaoAgp(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label, percent;
            Integer total;
            data = "";
            label = "";
            total = 0;
            percent = "";
            this.dataImoveisSitAgp = "";
            this.labelsImoveisSitAgp = "";

            total = this.imoveisSituacao.stream().map((totDashboard) -> Integer.valueOf(totDashboard.getData())).reduce(total, Integer::sum);

            for (DashboardCharts dashboard : this.imoveisSituacao) {
                percent = df.format((((Double.parseDouble(dashboard.getData())) / total)) * 100);
                data += !data.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                label += !label.equals("") ? ",'" + dashboard.getLabel() + " - " + percent + "%" + "'" : "'" + dashboard.getLabel() + " - " + percent + "%" + "'";
            }

            this.dataImoveisSitAgp = "[" + data + "]";
            this.labelsImoveisSitAgp = "[" + label + "]";
        } catch (Exception ex) {

        }
    }

    public void gerarInfImoveisSituacaoDia() {
        try {
            this.imoveisSituacao = this.secSaude.obterImoveisSituacaoDia(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label1, label2, label3;
            data = "";
            label1 = "";
            label2 = "";
            label3 = "";
            this.labelsImoveisSitDia = "";

            for (DashboardCharts dashboard : this.imoveisSituacao) {

                data += !data.equals("") ? ",'" + dashboard.getLabel() + "'" : "'" + dashboard.getLabel() + "'";
                label1 += !label1.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                label2 += !label2.equals("") ? ",'" + dashboard.getData2() + "'" : "'" + dashboard.getData2() + "'";
                label3 += !label3.equals("") ? ",'" + dashboard.getData3() + "'" : "'" + dashboard.getData3() + "'";
            }

            this.labelsImoveisSitDia += "labels: [" + data + "],";
            this.labelsImoveisSitDia += "datasets: [";

            this.labelsImoveisSitDia += "        {";
            this.labelsImoveisSitDia += "          label: 'IMÓVEIS INSPECIONADOS',";
            this.labelsImoveisSitDia += "          backgroundColor: '#449D44', ";
            this.labelsImoveisSitDia += "          borderColor: '#449D44', ";
            this.labelsImoveisSitDia += "          data: [" + label1 + "],";
            this.labelsImoveisSitDia += "          fill: false, ";
            this.labelsImoveisSitDia += "        }, ";

            this.labelsImoveisSitDia += "        {";
            this.labelsImoveisSitDia += "          label: 'IMÓVEIS RECUSADOS',";
            this.labelsImoveisSitDia += "          backgroundColor: '#ec971f', ";
            this.labelsImoveisSitDia += "          borderColor: '#ec971f', ";
            this.labelsImoveisSitDia += "          data: [" + label2 + "],";
            this.labelsImoveisSitDia += "          fill: false, ";
            this.labelsImoveisSitDia += "        }, ";

            this.labelsImoveisSitDia += "        {";
            this.labelsImoveisSitDia += "          label: 'IMÓVEIS FECHADOS',";
            this.labelsImoveisSitDia += "          backgroundColor: '#c9302c', ";
            this.labelsImoveisSitDia += "          borderColor: '#c9302c', ";
            this.labelsImoveisSitDia += "          data: [" + label3 + "],";
            this.labelsImoveisSitDia += "          fill: false, ";
            this.labelsImoveisSitDia += "        }, ";

            this.labelsImoveisSitDia += "]";
        } catch (Exception ex) {

        }
    }

    public void gerarInfImoveisDiaTrab() {
        try {
            this.imoveisSituacao = this.secSaude.obterImoveisDiaTrabalho(this.ano, this.mes, this.codfil, this.persistencia);
            String color, percent;
            Integer total;
            int i;
            i = 0;
            color = "";
            percent = "";
            total = 0;
            this.labelsImoveisdiaTrab = "";

            this.labelsImoveisdiaTrab += "labels: [],";
            this.labelsImoveisdiaTrab += "datasets: [";
            for (DashboardCharts dashboard : this.imoveisSituacao) {
                percent = df.format((((Double.parseDouble(dashboard.getData())) / total)) * 100);
                switch (i) {
                    case 0:
                        color = "'#449D44'";
                        break;
                    case 1:
                        color = "'#ec971f'";
                        break;
                    case 2:
                        color = "'#c9302c'";
                        break;
                    case 3:
                        color = "'#b94bdd'";
                        break;
                    default:
                        color = "'#D8089A'";
                        break;
                }

                this.labelsImoveisdiaTrab += "        {";
                this.labelsImoveisdiaTrab += "          label: '" + dashboard.getLabel()+"',";
                this.labelsImoveisdiaTrab += "          backgroundColor: " + color + ", ";
                this.labelsImoveisdiaTrab += "          borderColor: " + color + ", ";
                this.labelsImoveisdiaTrab += "          data: [" + dashboard.getData() + "],";
                this.labelsImoveisdiaTrab += "          fill: false, ";
                this.labelsImoveisdiaTrab += "        }, ";
                i++;
            }
            this.labelsImoveisdiaTrab += "]";
        } catch (Exception ex) {

        }
    }

    public void gerarInspecaoTratamentoAgp() {
        try {
            this.imoveisSituacao = this.secSaude.obterInspecaoTratamentoAgp(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label, percent;
            Integer total;
            data = "";
            label = "";
            percent = "";
            total = 0;
            this.dataInspecaoTratAgp = "";
            this.labelInspecaoTratAgp = "";

            total = this.imoveisSituacao.stream().map((totDashboard) -> Integer.valueOf(totDashboard.getData())).reduce(total, Integer::sum);

            for (DashboardCharts dashboard : this.imoveisSituacao) {
                percent = df.format((((Double.parseDouble(dashboard.getData())) / total)) * 100);
                data += !data.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                label += !label.equals("") ? ",'" + dashboard.getLabel() + " - " + percent + "%" + "'" : "'" + dashboard.getLabel() + " - " + percent + "%" + "'";
            }

            this.dataInspecaoTratAgp = "[" + data + "]";
            this.labelInspecaoTratAgp = "[" + label + "]";
        } catch (Exception ex) {

        }
    }

    public void gerarDepositoTipoAgp() {
        try {
            this.imoveisSituacao = this.secSaude.obterDepositosTipoAgp(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label, percent;
            Integer total;
            data = "";
            label = "";
            percent = "";
            total = 0;
            this.dataDepositoTipoAgp = "";
            this.labelDepositoTipoAgp = "";

            total = this.imoveisSituacao.stream().map((totDashboard) -> Integer.valueOf(totDashboard.getData())).reduce(total, Integer::sum);

            for (DashboardCharts dashboard : this.imoveisSituacao) {
                percent = df.format((((Double.parseDouble(dashboard.getData())) / total)) * 100);
                data += !data.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                label += !label.equals("") ? ",'" + dashboard.getLabel() + " - " + percent + "%" + "'" : "'" + dashboard.getLabel() + " - " + percent + "%" + "'";
            }

            this.dataDepositoTipoAgp = "[" + data + "]";
            this.labelDepositoTipoAgp = "[" + label + "]";
        } catch (Exception ex) {

        }
    }

    public void gerarImoveisInspecaoTratamentoTipo() {
        try {
            this.imoveisSituacao = this.secSaude.obterimoveisInspecaoTratamentoTipo(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label1, label2, label3, label4;
            data = "";
            label1 = "";
            label2 = "";
            label3 = "";
            label4 = "";
            this.labelsImoveisInspecaoTratamentoTipo = "";

            for (DashboardCharts dashboard : this.imoveisSituacao) {

                data += !data.equals("") ? ",'" + dashboard.getLabel() + "'" : "'" + dashboard.getLabel() + "'";
                label1 += !label1.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                label2 += !label2.equals("") ? ",'" + dashboard.getData2() + "'" : "'" + dashboard.getData2() + "'";
                label3 += !label3.equals("") ? ",'" + dashboard.getData3() + "'" : "'" + dashboard.getData3() + "'";
                label4 += !label4.equals("") ? ",'" + dashboard.getData4() + "'" : "'" + dashboard.getData4() + "'";
            }

            this.labelsImoveisInspecaoTratamentoTipo += "labels: [" + data + "],";
            this.labelsImoveisInspecaoTratamentoTipo += "datasets: [";

            this.labelsImoveisInspecaoTratamentoTipo += "        {";
            this.labelsImoveisInspecaoTratamentoTipo += "          label: 'RESIDÊNCIA',";
            this.labelsImoveisInspecaoTratamentoTipo += "          backgroundColor: '#449D44', ";
            this.labelsImoveisInspecaoTratamentoTipo += "          borderColor: '#449D44', ";
            this.labelsImoveisInspecaoTratamentoTipo += "          data: [" + label1 + "],";
            this.labelsImoveisInspecaoTratamentoTipo += "          fill: false, ";
            this.labelsImoveisInspecaoTratamentoTipo += "        }, ";

            this.labelsImoveisInspecaoTratamentoTipo += "        {";
            this.labelsImoveisInspecaoTratamentoTipo += "          label: 'COMÉRCIO',";
            this.labelsImoveisInspecaoTratamentoTipo += "          backgroundColor: '#ec971f', ";
            this.labelsImoveisInspecaoTratamentoTipo += "          borderColor: '#ec971f', ";
            this.labelsImoveisInspecaoTratamentoTipo += "          data: [" + label2 + "],";
            this.labelsImoveisInspecaoTratamentoTipo += "          fill: false, ";
            this.labelsImoveisInspecaoTratamentoTipo += "        }, ";

            this.labelsImoveisInspecaoTratamentoTipo += "        {";
            this.labelsImoveisInspecaoTratamentoTipo += "          label: 'OUTROS',";
            this.labelsImoveisInspecaoTratamentoTipo += "          backgroundColor: '#c9302c', ";
            this.labelsImoveisInspecaoTratamentoTipo += "          borderColor: '#c9302c', ";
            this.labelsImoveisInspecaoTratamentoTipo += "          data: [" + label3 + "],";
            this.labelsImoveisInspecaoTratamentoTipo += "          fill: false, ";
            this.labelsImoveisInspecaoTratamentoTipo += "        }, ";

            this.labelsImoveisInspecaoTratamentoTipo += "        {";
            this.labelsImoveisInspecaoTratamentoTipo += "          label: 'TB',";
            this.labelsImoveisInspecaoTratamentoTipo += "          backgroundColor: '#1AA6C9', ";
            this.labelsImoveisInspecaoTratamentoTipo += "          borderColor: '#1AA6C9', ";
            this.labelsImoveisInspecaoTratamentoTipo += "          data: [" + label4 + "],";
            this.labelsImoveisInspecaoTratamentoTipo += "          fill: false, ";
            this.labelsImoveisInspecaoTratamentoTipo += "        }, ";

            this.labelsImoveisInspecaoTratamentoTipo += "]";
        } catch (Exception ex) {

        }
    }

    public void gerarNumeroDepositosEvolucao() {
        try {
            this.imoveisSituacao = this.secSaude.obternumeroDepositosEvolucao(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label1, label2, label3;
            data = "";
            label1 = "";
            label2 = "";
            label3 = "";
            this.labelsNumeroDepositosEvolucao = "";

            for (DashboardCharts dashboard : this.imoveisSituacao) {

                data += !data.equals("") ? ",'" + dashboard.getLabel() + "'" : "'" + dashboard.getLabel() + "'";
                label1 += !label1.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                label2 += !label2.equals("") ? ",'" + dashboard.getData2() + "'" : "'" + dashboard.getData2() + "'";
                label3 += !label3.equals("") ? ",'" + dashboard.getData3() + "'" : "'" + dashboard.getData3() + "'";
            }

            this.labelsNumeroDepositosEvolucao += "labels: [" + data + "],";
            this.labelsNumeroDepositosEvolucao += "datasets: [";

            this.labelsNumeroDepositosEvolucao += "        {";
            this.labelsNumeroDepositosEvolucao += "          label: 'INSPECIONADOS',";
            this.labelsNumeroDepositosEvolucao += "          backgroundColor: '#449D44', ";
            this.labelsNumeroDepositosEvolucao += "          borderColor: '#449D44', ";
            this.labelsNumeroDepositosEvolucao += "          data: [" + label1 + "],";
            this.labelsNumeroDepositosEvolucao += "          fill: false, ";
            this.labelsNumeroDepositosEvolucao += "        }, ";

            this.labelsNumeroDepositosEvolucao += "        {";
            this.labelsNumeroDepositosEvolucao += "          label: 'TRATADOS',";
            this.labelsNumeroDepositosEvolucao += "          backgroundColor: '#ec971f', ";
            this.labelsNumeroDepositosEvolucao += "          borderColor: '#ec971f', ";
            this.labelsNumeroDepositosEvolucao += "          data: [" + label2 + "],";
            this.labelsNumeroDepositosEvolucao += "          fill: false, ";
            this.labelsNumeroDepositosEvolucao += "        }, ";

            this.labelsNumeroDepositosEvolucao += "        {";
            this.labelsNumeroDepositosEvolucao += "          label: 'ELIMINADOS',";
            this.labelsNumeroDepositosEvolucao += "          backgroundColor: '#c9302c', ";
            this.labelsNumeroDepositosEvolucao += "          borderColor: '#c9302c', ";
            this.labelsNumeroDepositosEvolucao += "          data: [" + label3 + "],";
            this.labelsNumeroDepositosEvolucao += "          fill: false, ";
            this.labelsNumeroDepositosEvolucao += "        }, ";

            this.labelsNumeroDepositosEvolucao += "]";
        } catch (Exception ex) {

        }
    }

    public void gerarDepositosAcaoAgp() {
        try {
            this.imoveisSituacao = this.secSaude.obterdepositosAcaoAgp(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label, percent;
            Integer total;
            data = "";
            label = "";
            percent = "";
            total = 0;
            this.dataDepositoAcaoAgp = "";
            this.labelDepositoAcaoAgp = "";

            total = this.imoveisSituacao.stream().map((totDashboard) -> Integer.valueOf(totDashboard.getData())).reduce(total, Integer::sum);

            for (DashboardCharts dashboard : this.imoveisSituacao) {
                percent = df.format((((Double.parseDouble(dashboard.getData())) / total)) * 100);
                data += !data.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                label += !label.equals("") ? ",'" + dashboard.getLabel() + " - " + percent + "%" + "'" : "'" + dashboard.getLabel() + " - " + percent + "%" + "'";
            }

            this.dataDepositoAcaoAgp = "[" + data + "]";
            this.labelDepositoAcaoAgp = "[" + label + "]";
        } catch (Exception ex) {

        }
    }

    public void gerarevolucaoDepositosTipo() {
        try {
            this.imoveisSituacao = this.secSaude.obterevolucaoDepositosTipo(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label1, label2, label3, label4, label5, label6, label7;
            data = "";
            label1 = "";
            label2 = "";
            label3 = "";
            label4 = "";
            label5 = "";
            label6 = "";
            label7 = "";
            this.labelsEvolucaoDespositosTipo = "";

            for (DashboardCharts dashboard : this.imoveisSituacao) {

                data += !data.equals("") ? ",'" + dashboard.getLabel() + "'" : "'" + dashboard.getLabel() + "'";
                label1 += !label1.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                label2 += !label2.equals("") ? ",'" + dashboard.getData2() + "'" : "'" + dashboard.getData2() + "'";
                label3 += !label3.equals("") ? ",'" + dashboard.getData3() + "'" : "'" + dashboard.getData3() + "'";

                label4 += !label4.equals("") ? ",'" + dashboard.getData4() + "'" : "'" + dashboard.getData4() + "'";
                label5 += !label5.equals("") ? ",'" + dashboard.getData5() + "'" : "'" + dashboard.getData5() + "'";
                label6 += !label6.equals("") ? ",'" + dashboard.getData6() + "'" : "'" + dashboard.getData6() + "'";
                label7 += !label7.equals("") ? ",'" + dashboard.getData7() + "'" : "'" + dashboard.getData7() + "'";
            }

            this.labelsEvolucaoDespositosTipo += "labels: [" + data + "],";
            this.labelsEvolucaoDespositosTipo += "datasets: [";

            this.labelsEvolucaoDespositosTipo += "        {";
            this.labelsEvolucaoDespositosTipo += "          label: 'A1',";
            this.labelsEvolucaoDespositosTipo += "          backgroundColor: '#449D44', ";
            this.labelsEvolucaoDespositosTipo += "          borderColor: '#449D44', ";
            this.labelsEvolucaoDespositosTipo += "          data: [" + label1 + "],";
            this.labelsEvolucaoDespositosTipo += "          fill: false, ";
            this.labelsEvolucaoDespositosTipo += "        }, ";

            this.labelsEvolucaoDespositosTipo += "        {";
            this.labelsEvolucaoDespositosTipo += "          label: 'A2',";
            this.labelsEvolucaoDespositosTipo += "          backgroundColor: '#ec971f', ";
            this.labelsEvolucaoDespositosTipo += "          borderColor: '#ec971f', ";
            this.labelsEvolucaoDespositosTipo += "          data: [" + label2 + "],";
            this.labelsEvolucaoDespositosTipo += "          fill: false, ";
            this.labelsEvolucaoDespositosTipo += "        }, ";

            this.labelsEvolucaoDespositosTipo += "        {";
            this.labelsEvolucaoDespositosTipo += "          label: 'B',";
            this.labelsEvolucaoDespositosTipo += "          backgroundColor: '#c9302c', ";
            this.labelsEvolucaoDespositosTipo += "          borderColor: '#c9302c', ";
            this.labelsEvolucaoDespositosTipo += "          data: [" + label3 + "],";
            this.labelsEvolucaoDespositosTipo += "          fill: false, ";
            this.labelsEvolucaoDespositosTipo += "        }, ";

            this.labelsEvolucaoDespositosTipo += "        {";
            this.labelsEvolucaoDespositosTipo += "          label: 'C',";
            this.labelsEvolucaoDespositosTipo += "          backgroundColor: '#1AA6C9', ";
            this.labelsEvolucaoDespositosTipo += "          borderColor: '#1AA6C9', ";
            this.labelsEvolucaoDespositosTipo += "          data: [" + label4 + "],";
            this.labelsEvolucaoDespositosTipo += "          fill: false, ";
            this.labelsEvolucaoDespositosTipo += "        }, ";

            this.labelsEvolucaoDespositosTipo += "        {";
            this.labelsEvolucaoDespositosTipo += "          label: 'D1',";
            this.labelsEvolucaoDespositosTipo += "          backgroundColor: '#C4EAFC', ";
            this.labelsEvolucaoDespositosTipo += "          borderColor: '#C4EAFC', ";
            this.labelsEvolucaoDespositosTipo += "          data: [" + label5 + "],";
            this.labelsEvolucaoDespositosTipo += "          fill: false, ";
            this.labelsEvolucaoDespositosTipo += "        }, ";

            this.labelsEvolucaoDespositosTipo += "        {";
            this.labelsEvolucaoDespositosTipo += "          label: 'D2',";
            this.labelsEvolucaoDespositosTipo += "          backgroundColor: '#118969', ";
            this.labelsEvolucaoDespositosTipo += "          borderColor: '#118969', ";
            this.labelsEvolucaoDespositosTipo += "          data: [" + label6 + "],";
            this.labelsEvolucaoDespositosTipo += "          fill: false, ";
            this.labelsEvolucaoDespositosTipo += "        }, ";

            this.labelsEvolucaoDespositosTipo += "        {";
            this.labelsEvolucaoDespositosTipo += "          label: 'E',";
            this.labelsEvolucaoDespositosTipo += "          backgroundColor: '#8798E5', ";
            this.labelsEvolucaoDespositosTipo += "          borderColor: '#8798E5', ";
            this.labelsEvolucaoDespositosTipo += "          data: [" + label7 + "],";
            this.labelsEvolucaoDespositosTipo += "          fill: false, ";
            this.labelsEvolucaoDespositosTipo += "        }, ";

            this.labelsEvolucaoDespositosTipo += "]";
        } catch (Exception ex) {

        }
    }

    public void gerarAmostrasRoedoresAmostras() {
        try {
            this.imoveisSituacao = this.secSaude.obteramostrasRoedoresAmostras(this.ano, this.mes, this.codfil, this.persistencia);
            String color;
            int i;
            i = 0;
            color = "";
            this.labelsamostrasRoedoresAmostras = "";

            this.labelsamostrasRoedoresAmostras += "labels: [],";
            this.labelsamostrasRoedoresAmostras += "datasets: [";
            for (DashboardCharts dashboard : this.imoveisSituacao) {
                switch (i) {
                    case 0:
                        color = "'#449D44'";
                        break;
                    case 1:
                        color = "'#ec971f'";
                        break;
                    case 2:
                        color = "'#c9302c'";
                        break;
                    case 3:
                        color = "'#b94bdd'";
                        break;
                    default:
                        color = "'#D8089A'";
                        break;
                }

                this.labelsamostrasRoedoresAmostras += "        {";
                this.labelsamostrasRoedoresAmostras += "          label: '" + dashboard.getLabel() + "',";
                this.labelsamostrasRoedoresAmostras += "          backgroundColor: " + color + ", ";
                this.labelsamostrasRoedoresAmostras += "          borderColor: " + color + ", ";
                this.labelsamostrasRoedoresAmostras += "          data: [" + dashboard.getData() + "],";
                this.labelsamostrasRoedoresAmostras += "          fill: false, ";
                this.labelsamostrasRoedoresAmostras += "        }, ";
                i++;
            }
            this.labelsamostrasRoedoresAmostras += "]";
        } catch (Exception ex) {

        }
    }

    public void gerarInspecionadosxdengue() {
        try {
            this.imoveisSituacao = this.secSaude.obterinspecionadosxdengue(this.ano, this.mes, this.codfil, this.persistencia);
            String data, label, percent;
            Integer total;
            data = "";
            label = "";
            percent = "";
            total = 0;
            this.dataInspecionadosxDengue = "";
            this.labelInspecionadosxDengue = "";

            for (DashboardCharts dashboardTotal : this.imoveisSituacao) {
                if (dashboardTotal.getLabel().contains("IMOVEIS INSPECIONADOS")) {
                    total += Integer.valueOf(dashboardTotal.getData());
                }
            }

            for (DashboardCharts dashboard : this.imoveisSituacao) {
                if (!dashboard.getLabel().contains("IMOVEIS INSPECIONADOS")) {
                    percent = df.format((((Double.parseDouble(dashboard.getData())) / total)) * 100);
                    data += !data.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                    label += !label.equals("") ? ",'" + dashboard.getLabel() + " - " + percent + "%" + "'" : "'" + dashboard.getLabel() + " - " + percent + "%" + "'";
                } else {
                    data += !data.equals("") ? ",'" + dashboard.getData() + "'" : "'" + dashboard.getData() + "'";
                    label += !label.equals("") ? ",'" + dashboard.getLabel() + "'" : "'" + dashboard.getLabel() + "'";
                }

            }

            this.dataInspecionadosxDengue = "[" + data + "]";
            this.labelInspecionadosxDengue = "[" + label + "]";
        } catch (Exception ex) {

        }
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }

    public Map getFiltroMovimentacoes() {
        return filtroMovimentacoes;
    }

    public void setFiltroMovimentacoes(Map filtroMovimentacoes) {
        this.filtroMovimentacoes = filtroMovimentacoes;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public List<DashboardCharts> getImoveisSituacao() {
        return imoveisSituacao;
    }

    public void setImoveisSituacao(List<DashboardCharts> imoveisSituacao) {
        this.imoveisSituacao = imoveisSituacao;
    }

    public Persistencia getPesistencia() {
        return pesistencia;
    }

    public void setPesistencia(Persistencia pesistencia) {
        this.pesistencia = pesistencia;
    }

    public String getColors() {
        return colors;
    }

    public void setColors(String colors) {
        this.colors = colors;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getCaminho() {
        return caminho;
    }

    public void setCaminho(String caminho) {
        this.caminho = caminho;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getMes() {
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public String getAno() {
        return ano;
    }

    public void setAno(String ano) {
        this.ano = ano;
    }

    public String getDataImoveisSitAgp() {
        return dataImoveisSitAgp;
    }

    public void setDataImoveisSitAgp(String dataImoveisSitAgp) {
        this.dataImoveisSitAgp = dataImoveisSitAgp;
    }

    public String getLabelsImoveisSitAgp() {
        return labelsImoveisSitAgp;
    }

    public void setLabelsImoveisSitAgp(String labelsImoveisSitAgp) {
        this.labelsImoveisSitAgp = labelsImoveisSitAgp;
    }

    public String getDataImoveisSitDia() {
        return dataImoveisSitDia;
    }

    public void setDataImoveisSitDia(String dataImoveisSitDia) {
        this.dataImoveisSitDia = dataImoveisSitDia;
    }

    public String getLabelsImoveisSitDia() {
        return labelsImoveisSitDia;
    }

    public void setLabelsImoveisSitDia(String labelsImoveisSitDia) {
        this.labelsImoveisSitDia = labelsImoveisSitDia;
    }

    public String getLabelsImoveisdiaTrab() {
        return labelsImoveisdiaTrab;
    }

    public void setLabelsImoveisdiaTrab(String labelsImoveisdiaTrab) {
        this.labelsImoveisdiaTrab = labelsImoveisdiaTrab;
    }

    public String getDataInspecaoTratAgp() {
        return dataInspecaoTratAgp;
    }

    public void setDataInspecaoTratAgp(String dataInspecaoTratAgp) {
        this.dataInspecaoTratAgp = dataInspecaoTratAgp;
    }

    public String getLabelInspecaoTratAgp() {
        return labelInspecaoTratAgp;
    }

    public void setLabelInspecaoTratAgp(String labelInspecaoTratAgp) {
        this.labelInspecaoTratAgp = labelInspecaoTratAgp;
    }

    public String getDataDepositoTipoAgp() {
        return dataDepositoTipoAgp;
    }

    public void setDataDepositoTipoAgp(String dataDepositoTipoAgp) {
        this.dataDepositoTipoAgp = dataDepositoTipoAgp;
    }

    public String getLabelDepositoTipoAgp() {
        return labelDepositoTipoAgp;
    }

    public void setLabelDepositoTipoAgp(String labelDepositoTipoAgp) {
        this.labelDepositoTipoAgp = labelDepositoTipoAgp;
    }

    public String getLabelsImovisInpecaoTratamentoTipo() {
        return labelsImoveisInspecaoTratamentoTipo;
    }

    public void setLabelsImovisInpecaoTratamentoTipo(String labelsImoveisInspecaoTratamentoTipo) {
        this.labelsImoveisInspecaoTratamentoTipo = labelsImoveisInspecaoTratamentoTipo;
    }

    public String getDataDepositoAcaoAgp() {
        return dataDepositoAcaoAgp;
    }

    public void setDataDepositoAcaoAgp(String dataDepositoAcaoAgp) {
        this.dataDepositoAcaoAgp = dataDepositoAcaoAgp;
    }

    public String getLabelDepositoAcaoAgp() {
        return labelDepositoAcaoAgp;
    }

    public void setLabelDepositoAcaoAgp(String labelDepositoAcaoAgp) {
        this.labelDepositoAcaoAgp = labelDepositoAcaoAgp;
    }

    public String getLabelsImoveisInspecaoTratamentoTipo() {
        return labelsImoveisInspecaoTratamentoTipo;
    }

    public void setLabelsImoveisInspecaoTratamentoTipo(String labelsImoveisInspecaoTratamentoTipo) {
        this.labelsImoveisInspecaoTratamentoTipo = labelsImoveisInspecaoTratamentoTipo;
    }

    public String getLabelsNumeroDepositosEvolucao() {
        return labelsNumeroDepositosEvolucao;
    }

    public void setLabelsNumeroDepositosEvolucao(String labelsNumeroDepositosEvolucao) {
        this.labelsNumeroDepositosEvolucao = labelsNumeroDepositosEvolucao;
    }

    public String getLabelsEvolucaoDespositosTipo() {
        return labelsEvolucaoDespositosTipo;
    }

    public void setLabelsEvolucaoDespositosTipo(String labelsEvolucaoDespositosTipo) {
        this.labelsEvolucaoDespositosTipo = labelsEvolucaoDespositosTipo;
    }

}

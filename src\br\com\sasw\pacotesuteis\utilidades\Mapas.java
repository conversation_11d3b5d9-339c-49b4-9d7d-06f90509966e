/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class Mapas {

    private String pin, periodo, tempoParaAtendimento, hora1Pos, horaAtual;

    public String getPin(String empresa, String data, String hora, String horaCheg, String banco) throws ParseException {

        //Calculando tempo até horário programado
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        hora1Pos = hora.substring(0, 2) + ":" + hora.substring(2, 4);
        horaAtual = sdf.format(new Date());

        Calendar hs = Calendar.getInstance();
        Calendar hsFinal = Calendar.getInstance();

        hs.setTime(sdf.parse(data + " " + hora1Pos));
        hsFinal.setTime(sdf.parse(horaAtual));

        long difMin = (hs.getTimeInMillis() - hsFinal.getTimeInMillis()) / 60000;

        if ((Integer.valueOf(hora.substring(0, 2)) >= 7)
                && (Integer.valueOf(hora.substring(0, 2)) < 12)) {
            periodo = "MANHA";
        } else if ((Integer.valueOf(hora.substring(0, 2)) >= 12)
                && (Integer.valueOf(hora.substring(0, 2)) < 18)) {
            periodo = "TARDE";
        } else {
            periodo = "NOITE";
        }

        if (banco.equals("001")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_bb_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_bb_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_bb_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_bb_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_bb_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_bb_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_bb_red.png";
                }
            }
        } else if (banco.equals("033")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_santander_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_santander_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_santander_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_santander_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_santander_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_santander_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_santander_red.png";
                }
            }
        } else if (banco.equals("104")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_caixa_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_caixa_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_caixa_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_caixa_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_caixa_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_caixa_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_caixa_red.png";
                }
            }
        } else if (banco.equals("237")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_bradesco_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_bradesco_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_bradesco_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_bradesco_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_bradesco_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_bradesco_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_bradesco_red.png";
                }
            }
        } else if (banco.equals("341")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_itau_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_itau_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_itau_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_itau_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_itau_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_itau_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_itau_red.png";
                }
            }
        } else if (banco.equals("389")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_mb_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_mb_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_mb_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_mb_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_mb_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_mb_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_mb_red.png";
                }
            }
        } else if (banco.equals("422")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_safra_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_safra_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_safra_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_safra_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_safra_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_safra_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_safra_red.png";
                }
            }
        } else if (empresa.contains("SATCORPVS") && banco.equals("620")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_corpvs_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_corpvs_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_corpvs_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_corpvs_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_corpvs_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_corpvs_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_corpvs_red.png";
                }
            }
        } else if (empresa.equals("SATTRANSVIP") && banco.equals("999")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_transvip_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_transvip_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_transvip_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_transvip_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_transvip_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_transvip_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_transvip_red.png";
                }
            }
        } else if (empresa.equals("SATBRASIFORTE") && banco.equals("999")) {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_manha/icone_brasifort_manha.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_tarde/icone_brasifort_tarde.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_verdes_noite/icone_brasifort_noite.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_manha/icone_brasifort_manha.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_tarde/icone_brasifort_tarde.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_dourados_noite/icone_brasifort_noite.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_vermelhos/icone_brasifort_red.png";
                }
            }
        } else {
            if (horaCheg.length() > 1) {
                if (periodo.equals("MANHA")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconeverde_M.png";
                } else if (periodo.equals("TARDE")) {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconeverde_T.png";
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconeverde_NOITE.png";
                }
            } else {
                if (difMin > 15) {
                    if (periodo.equals("MANHA")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconedourado_M.png";
                    } else if (periodo.equals("TARDE")) {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconedourado_T.png";
                    } else {
                        pin = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconedourado_NOITE.png";
                    }
                } else {
                    pin = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_icone_ATRASO.png";
                }
            }
        }
        return pin;
    }

    public String getPinTroca(String empresa, String hora, String banco) throws ParseException {

        if ((Integer.valueOf(hora.substring(0, 2)) >= 7)
                && (Integer.valueOf(hora.substring(0, 2)) < 12)) {
            periodo = "MANHA";
        } else if ((Integer.valueOf(hora.substring(0, 2)) >= 12)
                && (Integer.valueOf(hora.substring(0, 2)) < 18)) {
            periodo = "TARDE";
        } else {
            periodo = "NOITE";
        }

        if (banco.equals("001")) {
            if (periodo.equals("MANHA")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_manha/icone_bb_manha.png";
            } else if (periodo.equals("TARDE")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_tarde/icone_bb_tarde.png";
            } else {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_noite/icone_bb_noite.png";
            }
        } else if (banco.equals("033")) {
            if (periodo.equals("MANHA")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_manha/icone_santander_manha.png";
            } else if (periodo.equals("TARDE")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_tarde/icone_santander_tarde.png";
            } else {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_noite/icone_santander_noite.png";
            }
        } else if (banco.equals("104")) {
            if (periodo.equals("MANHA")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_manha/icone_caixa_manha.png";
            } else if (periodo.equals("TARDE")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_tarde/icone_caixa_tarde.png";
            } else {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_noite/icone_caixa_noite.png";
            }
        } else if (banco.equals("237")) {
            if (periodo.equals("MANHA")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_manha/icone_bradesco_manha.png";
            } else if (periodo.equals("TARDE")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_tarde/icone_bradesco_tarde.png";
            } else {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_noite/icone_bradesco_noite.png";
            }
        } else if (banco.equals("341")) {
            if (periodo.equals("MANHA")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_manha/icone_itau_manha.png";
            } else if (periodo.equals("TARDE")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_tarde/icone_itau_tarde.png";
            } else {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_noite/icone_itau_noite.png";
            }
        } else if (banco.equals("389")) {
            if (periodo.equals("MANHA")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_manha/icone_mb_manha.png";
            } else if (periodo.equals("TARDE")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_tarde/icone_mb_tarde.png";
            } else {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_noite/icone_mb_noite.png";
            }
        } else if (banco.equals("422")) {
            if (periodo.equals("MANHA")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_manha/icone_safra_manha.png";
            } else if (periodo.equals("TARDE")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_tarde/icone_safra_tarde.png";
            } else {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icones_troca_noite/icone_safra_noite.png";
            }
        } else {
            if (periodo.equals("MANHA")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icone_troca_manha.png";
            } else if (periodo.equals("TARDE")) {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icone_troca_tarde.png";
            } else {
                pin = "https://mobile.sasw.com.br:9091/satmobile/img/icone_troca_noite.png";
            }
        }

        return pin;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getPeriodo() {
        return periodo;
    }

    public void setPeriodo(String periodo) {
        this.periodo = periodo;
    }

    public String getTempoParaAtendimento() {
        return tempoParaAtendimento;
    }

    public void setTempoParaAtendimento(String tempoParaAtendimento) {
        this.tempoParaAtendimento = tempoParaAtendimento;
    }
}

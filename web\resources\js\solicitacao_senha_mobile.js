let google = window.google;
let atualizarSolicitacaoSenha = window.atualizarSolicitacaoSenha;
let panelSolicitacaoAberto;
let mapSenhaMobile;
let markersSenha = [];
let MAP_ID = 'mapGoogleSolicitacaoSenha';
let TIMEOUT = 10000;

function initMapSolicitacaoSenha() {
    element = document.getElementById(MAP_ID);

    if (element == null) {
        console.error('elemento de mapa não existe no DOM');
        return;
    }

    mapSenhaMobile = new google.maps.Map(element, {
        zoom: 2,
        center: {
            lat: 0,
            lng: 0
        },
        gestureHandling: 'cooperative'
    });

    fetchPanicStatus();
}

function fetchPanicStatus() {
    panelSolicitacaoAberto = false;
    setInterval(function () {
        if (!panelSolicitacaoAberto) {
            console.log('polling por solicitação de senha mobile:', panelSolicitacaoAberto);
            atualizarSolicitacaoSenha();
        }
    }, TIMEOUT);
}

function limparMarkers() {
    for (let i = 0; i < markersSenha.length; i++) {
        markersSenha[i].setMap(null);
    }
}

function getIcon(tipo) {
    switch (tipo) {
        case 'caminhao':
            return 'https://mobile.sasw.com.br:9091/satmobile/pins/pin_azul_caminhao.png';
        case 'cliente':
            return 'https://mobile.sasw.com.br:9091/satmobile/pins/icone_cliente_verde.png';
        default:
            return 'https://mobile.sasw.com.br:9091/satmobile/pins/PIN_clientes_googlemaps.png';
    }
}

function addMarker(latitude, longitude, tipo) {
    let marker = new google.maps.Marker({
        position: {
            lat: parseFloat(latitude),
            lng: parseFloat(longitude)
        },
        icon: getIcon(tipo),
        map: mapSenhaMobile
    });

    markersSenha.push(marker);
    return marker;
}

function addCoordinates(list) {
    let bounds = new google.maps.LatLngBounds();

    for (let i = 0; i < list.length; i++) {
        let latitude = list[i].latitude;
        let longitude = list[i].longitude;
        let tipo = list[i].tipo;

        if (typeof latitude === 'number' && typeof longitude === 'number' && typeof tipo === 'string') {
            let marker = addMarker(latitude, longitude, tipo);
            bounds.extend(marker.position);
        } else {
            console.error('Wrong parameters for addCoordinates:', latitude, longitude, tipo);
        }
    }

    mapSenhaMobile.fitBounds(bounds);
}

window.addEventListener('load', function () {
    let MAP_ID = 'mapGoogleSolicitacaoSenha';
    let element = document.getElementById(MAP_ID);

    if (element != null) {
        initMapSolicitacaoSenha();
    }
}, false);
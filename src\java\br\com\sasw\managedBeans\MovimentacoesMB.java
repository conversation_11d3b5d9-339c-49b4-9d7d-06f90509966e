package br.com.sasw.managedBeans;

import Arquivo.ArquivoLog;
import Controller.Login.LoginSatMobWeb;
import Controller.Movimentacoes.MovimentacoesSatMobWeb;
import Dados.Persistencia;
import SasBeans.CtrOperV;
import SasBeans.EmailsEnviar;
import SasBeans.Funcion;
import SasBeans.SasPWFill;
import SasDaos.CtrOpervDao;
import SasDaos.EmailsEnviarDao;
import br.com.sasw.lazydatamodels.MovimentacoesLazyList;
import br.com.sasw.managedBeans.recursoshumanos.FuncionMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.LocaleController;
import br.com.sasw.utils.Messages;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

@ManagedBean
@Named(value = "mbmovimentacoes")
@ViewScoped
public class MovimentacoesMB implements Serializable {

    private Persistencia persistencia;
    private String movimentacao, escolha, data;
    private Map<String, String> movimentacoes;
    private CtrOpervDao ctrOperVDao;
    private CtrOperV novaMovimentacao, ctrSelecionada;
    private List<CtrOperV> listarMov;
    private List<Funcion> listFunc;
    private ArquivoLog logerro;
    private Funcion funcion;
    private FuncionMB funcionmb;
    private Map filters;
    private boolean eNumero, eCodfil, ePosto, eFunc, eData, eObs, eFuncSubs, eMotivo_Aus, eOperador;
    private BigDecimal codPessoa, numero, matr;
    private String FuncAus;
    private String caminho, banco, log, codigo, posto, nome, nome_guer, filialDesc, operador;
    private String destino;
    private String titulo;
    private String mensagem;
    private Boolean mostrarMovimentacoes;
    private EmailsEnviarDao emailEnviarDao;
    private EmailsEnviar emailsEnviar;
    private MovimentacoesSatMobWeb movimentacoesSatMobWeb;
    private SasPWFill filial;
    private int flag, total;
    private final LoginSatMobWeb loginsatmobweb;
    private LazyDataModel<CtrOperV> lazyMovimentacoes = null;
    private StreamedContent download;
    private UploadedFile uploadedFile;
    private InputStream inputStream;
    private String codfil;

    public MovimentacoesMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        FuncAus = (String) fc.getExternalContext().getSessionMap().get("funcAus");
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        novaMovimentacao = new CtrOperV();
        emailsEnviar = new EmailsEnviar();
        movimentacoesSatMobWeb = new MovimentacoesSatMobWeb();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        log = new String();
        logerro = new ArquivoLog();
        loginsatmobweb = new LoginSatMobWeb();
        ctrSelecionada = new CtrOperV();

        eNumero = true;
        eObs = true;
        eData = true;
        eCodfil = true;
        ePosto = true;
        eFunc = true;
        eFuncSubs = true;
        eMotivo_Aus = true;
        eOperador = true;

    }

    public void Persistencias(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
//            this.funcionmb = new FuncionMB(this.persistencia);
            this.filters = new HashMap();
            this.filters.put(" CtrOperV.Data =? ", "");
            this.filters.put(" CtrOperV.Numero =? ", "");
            this.filters.put(" CtrOperV.Codfil = ? ", this.codfil);
            this.filters.put(" CtrOperV.FuncAus = ? ", "");
            this.filters.put(" CtrOperV.Motivo_Aus = ? ", "");
            this.filters.put(" CtrOperV.Posto = ? ", "");
            this.filters.put(" CtrOperV.Operador like ? ", "");
            this.filters.put(" CtrOperV.Hora1 = ? ", "");
            this.filters.put(" CtrOperV.Hora2 = ? ", "");
            this.filters.put(" CtrOperV.Obs = ? ", "");
            //           this.total = this.movimentacoesSatMobWeb.contagem(this.filters, this.codPessoa, this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
                System.out.println(ex.getMessage());
            }
        }
    }

//    public void listar() {
//        try {
//            this.listarMov = this.movimentacoesSatMobWeb.listarMovimentacoes(this.codfil, this.persistencia);
//            this.mostrarMovimentacoes = false;
//
//        } catch (Exception e) {
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            log = this.getClass().getSimpleName() + "\r\n"
//                    + Thread.currentThread().getStackTrace()[1].getMethodName()
//                    + "\r\n" + e.getMessage() + "\r\n";
//            this.logerro.Grava(log, caminho);
//        }
//    }
    public void dblSelect(SelectEvent event) {
        this.ctrSelecionada = (CtrOperV) event.getObject();
        buttonAction();
    }

    public void buttonAction() {
        if (null == this.ctrSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Selecione uma Movimentacao"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.novaMovimentacao = new CtrOperV();
            this.novaMovimentacao = ctrSelecionada;

            this.novaMovimentacao.setFuncAus(this.ctrSelecionada.getFuncAus().toString());
            this.novaMovimentacao.setCodFil(this.ctrSelecionada.getCodFil());
            try {
                this.filial = this.loginsatmobweb.BuscaFilial(this.novaMovimentacao.getCodFil().toString(), this.codPessoa, this.persistencia);

            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
            this.flag = 2;
            PrimeFaces.current().resetInputs("cadastroMovimentacao:idCadastro");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        }
    }

    public void onRowSelect(SelectEvent event) {
        this.ctrSelecionada = (CtrOperV) event.getObject();
        if (this.ctrSelecionada.equals("R")) {
            this.escolha = "R";
        } else if (this.movimentacoes.equals("T")) {
            this.escolha = "T";
        }
    }

    public void cadastrar() {

        try {
            this.novaMovimentacao.setCodFil(this.filial.getCodfilAc());
            this.novaMovimentacao.setData(LocalDate.now().toString());
            this.novaMovimentacao.setFuncAus(this.funcion.getMatr());
//            this.novaMovimentacao.setMotivo_Aus(this.novaMovimentacao.getMotivo_Aus().toUpperCase());
            this.novaMovimentacao.setPosto(this.novaMovimentacao.getPosto().toUpperCase());
//            this.novaMovimentacao.setOperador(this.novaMovimentacao.getOperador().toUpperCase());
            this.novaMovimentacao.setHora1(DataAtual.getDataAtual("HORA"));
            this.novaMovimentacao.setHora2(DataAtual.getDataAtual("HORA"));
            this.novaMovimentacao.setObs(this.novaMovimentacao.getObs().toUpperCase());
//            this.emailsEnviar.setDest_email(this.emailsEnviar.getDest_email().toUpperCase());
            this.movimentacoesSatMobWeb.InserirMovimentacao(this.novaMovimentacao, this.persistencia);

//            this.movimentacoesSatMobWeb.EnviarEmail(emailsEnviar, persistencia);
            Locale locale = LocaleController.getsCurrentLocale();
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void novasMovimentacoes() {
        this.novaMovimentacao = new CtrOperV();
        this.flag = 1;
        try {
            this.filial = this.loginsatmobweb.BuscaFilial(this.codfil, this.codPessoa, this.persistencia);
            this.novaMovimentacao.setCodFil(this.filial.getCodFil());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        PrimeFaces.current().resetInputs("cadastroMovimentacao:dlgCadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        this.codigo = "";
    }

    public void HandleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                this.uploadedFile = fileUploadEvent.getFile();
                this.inputStream = new BufferedInputStream(this.uploadedFile.getInputstream());
                new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\" + this.novaMovimentacao.getCodFil().toBigInteger().toString());
                String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\" + this.novaMovimentacao.getCodFil().toBigInteger().toString();
                File file = new File(arquivo);
                FileOutputStream output = new FileOutputStream(file);
                output.write(this.uploadedFile.getContents());
                output.close();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Funcion> listarFuncionarios(String nome) {
        try {
            return this.movimentacoesSatMobWeb.listarFuncionarios(nome, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public LazyDataModel<CtrOperV> getAllMovimentacoes() {
        if (this.lazyMovimentacoes == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace("CtrOperV.codFil = ? ", this.codfil);
            dt.setFilters(this.filters);
            this.lazyMovimentacoes = new MovimentacoesLazyList(this.persistencia, this.codPessoa);

        }

        try {
            //          this.total = this.movimentacoesSatMobWeb.contagem(this.filters, this.codPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.lazyMovimentacoes;
    }

    public void nomeFunc() {
        try {
            if (!this.funcion.getNome().equals("") && !this.funcion.getNome_Guer().equals("")) {
                this.nome = this.funcion.getNome() + " - " + this.funcion.getNome_Guer();
            } else if ((this.funcion.getNome().equals("") || this.funcion.getNome_Guer().equals(""))
                    && (!this.funcion.getNome().equals("") || !this.funcion.getNome_Guer().equals(""))) {
                this.nome = this.funcion.getNome() + this.funcion.getNome_Guer();
            } else {
                this.nome = Messages.getMessageS("SemFuncionarioCadastrado");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

    }

    public void selecionarFilial(SelectEvent event) {
        this.filial = (SasPWFill) event.getObject();
        this.novaMovimentacao.setCodFil(this.filial.getCodfilAc());
    }

    public void selecionarFunc(SelectEvent event) {
        this.funcion = ((Funcion) event.getObject());
        this.funcion.setNome(this.funcion.getNome());
        this.funcion.setNome_Guer(this.funcion.getNome());
//        this.novaMovimentacao.setFuncAus(this.funcion.getMatr());
//        this.funcionmb.setNome_guer("");

    }

    public void prePesquisar() {
        this.novaMovimentacao = new CtrOperV();
        this.filial = new SasPWFill();
    }

    public void pesquisaPaginada() {

        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        if (null != this.novaMovimentacao.getCodFil()) {
            this.filters.replace(" CtrOperV.Codfil = ? ", this.novaMovimentacao.getCodFil().toPlainString());
        } else {
            this.filters.replace(" CtrOperV.Codfil = ? ", "");
        }

        if (null != this.novaMovimentacao.getFuncAus()) {
            this.filters.replace(" CtrOperV.FuncAus = ? ", this.novaMovimentacao.getFuncAus().toPlainString());
        } else {
            this.filters.replace(" CtrOperV.FuncAus = ? ", "");
        }

        if (!this.novaMovimentacao.getOperador().equals("")) {
            this.filters.replace(" CtrOperV.Operador like ? ", "%" + this.novaMovimentacao.getOperador() + "%");
        } else {
            this.filters.replace(" CtrOperV.Operador like ? ", "");
        }

        dt.setFilters(this.filters);
        getAllMovimentacoes();
        dt.setFirst(0);
    }

    public void editar() {
        try {
            this.ctrSelecionada = this.novaMovimentacao;
            //           this.ctrSelecionada.setCodFil(this.filial.getCodfilAc().toPlainString());
            this.ctrSelecionada.setHora1(DataAtual.getDataAtual("HORA"));
            this.ctrSelecionada.setHora2(DataAtual.getDataAtual("HORA"));
            this.ctrSelecionada.setData(DataAtual.getDataAtual("TELA"));
            this.ctrSelecionada.setPosto(this.novaMovimentacao.getPosto().toUpperCase());
            this.ctrSelecionada.setOperador(this.novaMovimentacao.getOperador().toUpperCase());
            this.ctrSelecionada.setObs(this.novaMovimentacao.getObs());
            this.ctrSelecionada.setFuncAus(this.novaMovimentacao.getFuncAus());
//            this.novaMovimentacao.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.movimentacoesSatMobWeb.gravarMovimentacao(this.ctrSelecionada, this.persistencia);
//            Locale locale = LocaleController.getsCurrentLocale();

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        this.novaMovimentacao = new CtrOperV();
    }

    public void atualizaTabela() {
        PrimeFaces.current().ajax().update("main:tabela");
    }

    public String getMovimentacao() {
        return movimentacao;
    }

    public void setMovimentacao(String movimentacao) {
        this.movimentacao = movimentacao;
    }

    public Map<String, String> getMovimentacoes() {
        return movimentacoes;
    }

    public void setMovimentacoes(Map<String, String> movimentacoes) {
        this.movimentacoes = movimentacoes;
    }

    public CtrOperV getNovaMovimentacao() {
        return novaMovimentacao;
    }

    public void setNovaMovimentacao(CtrOperV novaMovimentacao) {
        this.novaMovimentacao = novaMovimentacao;
    }

    public CtrOperV getCtrSelecionada() {
        return ctrSelecionada;
    }

    public void setCtrSelecionada(CtrOperV ctrSelecionada) {
        this.ctrSelecionada = ctrSelecionada;
    }

    public String getEscolha() {
        return escolha;
    }

    public void setEscolha(String escolha) {
        this.escolha = escolha;
    }

//    public String getSelecao() {
//        return selecao;
//    }
//
//    public void setSelecao(String selecao) {
//        this.selecao = selecao;
//    }
    public EmailsEnviar getEmailsEnviar() {
        return emailsEnviar;
    }

    public void setEmailsEnviar(EmailsEnviar emailsEnviar) {
        this.emailsEnviar = emailsEnviar;
    }

    public boolean iseCodfil() {
        return eCodfil;
    }

    public void seteCodfil(boolean eCodfil) {
        this.eCodfil = eCodfil;
    }

    public boolean iseNumero() {
        return eNumero;
    }

    public void seteNumero(boolean eNumero) {
        this.eNumero = eNumero;
    }

    public boolean isePosto() {
        return ePosto;
    }

    public void setePosto(boolean ePosto) {
        this.ePosto = ePosto;
    }

    public boolean iseFunc() {
        return eFunc;
    }

    public void seteFunc(boolean eFunc) {
        this.eFunc = eFunc;
    }

    public boolean iseData() {
        return eData;
    }

    public void seteData(boolean eData) {
        this.eData = eData;
    }

    public boolean iseObs() {
        return eObs;
    }

    public void seteObs(boolean eObs) {
        this.eObs = eObs;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<CtrOperV> getListarMov() {
        return listarMov;
    }

    public void setListarMov(List<CtrOperV> listarMov) {
        this.listarMov = listarMov;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public LazyDataModel<CtrOperV> getLazyMovimentacoes() {
        return lazyMovimentacoes;
    }

    public void setLazyMovimentacoes(LazyDataModel<CtrOperV> lazyMovimentacoes) {
        this.lazyMovimentacoes = lazyMovimentacoes;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public StreamedContent getDownload() {
        return download;
    }

    public void setDownload(StreamedContent download) {
        this.download = download;
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public boolean iseOperador() {
        return eOperador;
    }

    public void seteOperador(boolean eOperador) {
        this.eOperador = eOperador;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

}

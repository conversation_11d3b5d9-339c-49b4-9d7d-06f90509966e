/*
 */
package br.com.sasw.managedBeans;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import javax.enterprise.context.ApplicationScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.PhaseId;
import javax.imageio.ImageIO;
import javax.inject.Named;
import org.krysalis.barcode4j.impl.code128.Code128Bean;
import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;

/**
 *
 * <AUTHOR>
 */
@Named(value = "barCodeMB")
@ApplicationScoped
public class BarCodeMB {

    public StreamedContent getImage() throws IOException {
        FacesContext context = FacesContext.getCurrentInstance();

        if (context.getCurrentPhaseId() == PhaseId.RENDER_RESPONSE) {
            // So, we're rendering the HTML. Return a stub StreamedContent so that it will generate right URL.
            return new DefaultStreamedContent();
        } else {
            // So, browser is requesting the image. Return a real StreamedContent with the image bytes.
            String barcode = context.getExternalContext().getRequestParameterMap().get("barcode");
            Code128Bean bean = new Code128Bean();
            bean.setHeight(10d);
            bean.setModuleWidth(0.35);
            bean.doQuietZone(false);
            BitmapCanvasProvider provider = new BitmapCanvasProvider(110, BufferedImage.TYPE_BYTE_GRAY, false, 0);
            bean.generateBarcode(provider, barcode);
            provider.finish();
            BufferedImage imagem = provider.getBufferedImage();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(imagem, "png", os);
            return new DefaultStreamedContent(new ByteArrayInputStream(os.toByteArray()), "image/png");
        }
    }

    public static StreamedContent getImageS(String barcode) throws IOException {
        FacesContext context = FacesContext.getCurrentInstance();

        if (context.getCurrentPhaseId() == PhaseId.RENDER_RESPONSE) {
            // So, we're rendering the HTML. Return a stub StreamedContent so that it will generate right URL.
            return new DefaultStreamedContent();
        } else {
            // So, browser is requesting the image. Return a real StreamedContent with the image bytes.
            Code128Bean bean = new Code128Bean();
            bean.setHeight(10d);
            bean.setModuleWidth(0.35);
            bean.doQuietZone(false);
            BitmapCanvasProvider provider = new BitmapCanvasProvider(110, BufferedImage.TYPE_BYTE_GRAY, false, 0);
            bean.generateBarcode(provider, barcode);
            provider.finish();
            BufferedImage imagem = provider.getBufferedImage();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(imagem, "png", os);
            return new DefaultStreamedContent(new ByteArrayInputStream(os.toByteArray()), "image/png");
        }
    }

}

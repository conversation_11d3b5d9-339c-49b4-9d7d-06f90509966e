/*
 */
package Controller.FolhaDePonto;

import Dados.Persistencia;
import SasBeans.Rh_Ctrl;
import SasBeans.Rh_Horas;
import SasBeansCompostas.GeraPdfFolhaPonto;
import SasDaos.LogPortalDao;
import SasDaos.Rh_CtrlDao;
import SasDaos.Rh_HorasDao;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.GeraPdfFolhaPontoDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FolhaDePontoSatMobWeb {

    /**
     * Retorna os últimos seis períodos de folha de ponto aberto a partir da
     * data de hoje
     *
     * @param codFil
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rh_Ctrl> getPeriodos(String codFil, String matricula, Persistencia persistencia) throws Exception {
        try {
            Rh_CtrlDao rhctrldao = new Rh_CtrlDao();
            return rhctrldao.getRHCtrl(codFil, matricula, persistencia);
        } catch (Exception e) {
            throw new Exception("folhadepontos.falhageral<falha buscar pontos> - " + e.getMessage());
        }
    }

    /**
     * Retorna os dados necessários para montar o pdf com a folha de ponto
     *
     * @param matricula
     * @param dataInicial
     * @param dataFinal
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public GeraPdfFolhaPonto getFolhaDePonto(String matricula, String dataInicial,
            String dataFinal, String codFil, Persistencia persistencia) throws Exception {
        try {
            GeraPdfFolhaPontoDao gerapdfdao = new GeraPdfFolhaPontoDao();
            return gerapdfdao.getDadosFunc(matricula, dataInicial, dataFinal, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("folhadepontos.falhageral<falha buscar dados funcionario> - " + e.getMessage());
        }
    }

    /**
     * Busca informações do cabeçalho do pdf da folha de ponto
     *
     * @param matricula
     * @param dataInicial
     * @param dataFinal
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rh_Horas> getCabecalhoPonto(String matricula, String dataInicial,
            String dataFinal, String codFil, Persistencia persistencia) throws Exception {
        try {
            Rh_HorasDao rh_horasdao = new Rh_HorasDao();
            return rh_horasdao.getCabecalhoPonto(matricula, dataInicial, dataFinal, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("folhadepontos.falhageral<falha buscar dados do cabeçalho> - " + e.getMessage());
        }
    }

    /**
     * Salva o log da geração da folha de ponto.
     *
     * @param matricula
     * @param dataInicial
     * @param dataFinal
     * @param codFil
     * @param persistencia
     * @throws Exception
     */
    public void geraLogFolhaDePonto(String matricula, String dataInicial,
            String dataFinal, String codFil, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            logportaldao.insereLog(matricula, codFil, "Folha de Ponto gerada " + dataInicial + " a " + dataFinal, persistencia);
        } catch (Exception e) {
            throw new Exception("folhadepontos.falhageral<falha salvar log> - " + e.getMessage());
        }
    }
}

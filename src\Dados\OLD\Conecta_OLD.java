/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Dados.OLD;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;

/**
 *
 * <AUTHOR>
 */
public class Conecta_OLD {

    private LeArqConect_OLD mapeamento = new LeArqConect_OLD();
    private Connection conexao = null;
    private String ip, login, senha, url;
    private boolean jtds = false;
    private boolean mysql = false;

    /**
     * Abre a conexao do banco com os dados passados no construtor da classe
     * Pode-se passar os dados em branco "" para informar que deseja-se garantir
     * a conexão com os dados lidos anteriormente
     *
     * @param Parametro - parametro de conexao (nome da empresa)
     * @param Caminho - arquivo de conexão
     * @param Opcao - DESK ou WEB
     * @throws java.lang.Exception
     */
    public void Abreconexao(String Parametro, String Caminho, String Opcao) throws Exception {
        this.validaConexao();
        try {
            if (conexao == null) {
                if (jtds) {
                    conexao = this.conecta(Parametro, Caminho, Opcao, "net.sourceforge.jtds.jdbc.Driver");
                } else if (mysql) {
                    conexao = this.conecta(Parametro, Caminho, Opcao, "com.mysql.jdbc.Driver");
                } else {
                    conexao = this.conecta(Parametro, Caminho, Opcao, "com.microsoft.sqlserver.jdbc.SQLServerDriver");
                }
            }
        } catch (Exception ex) {
            throw new Exception("Erro ao conectar - " + ex.getMessage());
        }
    }

    /**
     * Abre uma conexao direta com o banco sem uso da mapconect
     *
     * @param IP - String contendo ip, porta e nome do banco no modelo
     * ***************:2006;databaseName=NomeBanco
     * @param Login - login de acesso ao banco
     * @param Senha - senha de acesso ao banco
     * @throws Exception - pode gerar exception em caso de erro de conexão
     */
    public void ConexaoDireta(String IP, String Login, String Senha) throws Exception {
        ip = IP;
        if (jtds) {
            url = "jdbc:jtds:sqlserver://" + ip.replaceAll(";databaseName=", "/");
        } else if (mysql) {
            url = "jdbc:mysql://" + ip.replaceAll(";databaseName=", "/");
        } else {
            url = "jdbc:sqlserver://" + ip;
        }
        login = Login;
        senha = Senha;
        this.Abreconexao("", "", "");
    }

    /**
     * Fecha a conexao do objeto Sempre tratar com try catch(Exception)
     *
     * @throws java.lang.Exception
     */
    public void Fechaconexao() throws Exception {
        try {
            conexao.close();
        } catch (Exception e) {
            throw new Exception("Não foi possível fechar a conexao - " + e.getMessage());
        }
    }

    /**
     * Inicia uma transacao com o banco
     *
     * @throws Exception - devolve erro
     */
    public void setIniTransacao() throws Exception {
        try {
            conexao.setAutoCommit(false);
        } catch (Exception e) {
            throw new Exception("Problema ao criar transacao");
        }
    }

    /**
     * Executa uma transacao com o banco
     *
     * @throws Exception - devolve erro
     */
    public void ExecutaTransacao() throws Exception {
        try {
            conexao.commit();
        } catch (Exception e) {
            try {
                conexao.rollback();
            } catch (Exception ex) {
                throw new Exception("Falha ao voltar status do banco - " + ex.getMessage());
            }

        }
    }

    /**
     * Finaliza uma transacao com o banco
     *
     * @throws Exception - devolve erro
     */
    public void setFimTransacao() throws Exception {
        try {
            conexao.setAutoCommit(true);
        } catch (Exception e) {
            throw new Exception("Problema ao criar transacao");
        }
    }

    /**
     * Retorna um Statement da conexao Sempre tratar com try catch(Exception)
     *
     * @return
     * @throws java.lang.Exception
     */
    public Statement getStmt() throws Exception {
        try {
            return conexao.createStatement();
        } catch (SQLException ex) {
            throw new Exception("Erro criando Statement - " + ex.getMessage());
        }
    }

    /**
     * Retorna um PreparedStatement do sql passado da conexao atual
     *
     * @param sql que sera usado Sempre tratar com try catch(Exception)
     * @return
     * @throws java.lang.Exception
     */
    public PreparedStatement getState(String sql) throws Exception {
        try {
            return conexao.prepareStatement(sql);
        } catch (SQLException ex) {
            throw new Exception("Erro criando PrepareStatement- " + ex.getMessage());
        }
    }

    /**
     * Ativa uso de jdbc STDL Padrão uso de jdbc microsoft
     *
     * @param value (false) - padrão usado para microsoft (true) - usado para
     * jtds
     */
    public void setJtds(boolean value) {
        this.jtds = value;
    }

    /**
     * Ativa uso de jdbc STDL Padrão uso de jdbc microsoft
     *
     * @param value (false) - padrão usado para microsoft (true) - usado para
     * jtds
     */
    public void setMysql(boolean value) {
        this.mysql = value;
    }

    /**
     * Busca no arquivo os parametros de conexão
     *
     * @param Parametro - parametro da empresa
     * @param Caminho - caminho do arquivo
     * @param Opcao - WEB ou DESK
     * @throws Exception
     */
    private void mapaconexao(String Parametro, String Caminho, String Opcao) throws Exception {
        mapeamento.Mapeia(Parametro, Caminho, Opcao);
        ip = mapeamento.getIp();
        login = mapeamento.getLogin();
        senha = mapeamento.getSenha();
        if (jtds) {
            url = "jdbc:jtds:sqlserver://" + ip.replaceAll(";databaseName=", "/");
        } else if (mysql) {
            url = "jdbc:mysql://" + ip;
        } else {
            url = "jdbc:sqlserver://" + ip;
        }
    }

    /**
     * Verifica se a conexão está aberta e válida
     */
    private void validaConexao() {
        if (conexao != null) {
            boolean valido;
            try {
                if (jtds) {
                    valido = !conexao.isClosed();
                } else {
                    valido = conexao.isValid(3);
                }
            } catch (Exception e) {
                valido = false;
            }
            if (!valido) {
                conexao = null;
            }
        }
    }

    /**
     * Efetua a conexão
     *
     * @param Caminho - caminho do arquivo de conexão
     * @param Parametro - parametro de conexão (nome da empresa)
     * @param Opcao - DESK ou WEB
     * @param Driver - String de conexão do driver
     * @return
     * @throws Exception
     */
    private Connection conecta(String Parametro, String Caminho, String Opcao, String Driver) throws Exception {
        if (!"".equals(Caminho)) {
            this.mapaconexao(Parametro, Caminho, Opcao);
        }
        try {
            Class.forName(Driver);
            return DriverManager.getConnection(url, login, senha);
        } catch (Exception ex) {
            throw new Exception("Conecta.conecta - " + ex.getMessage());
        }
    }

}

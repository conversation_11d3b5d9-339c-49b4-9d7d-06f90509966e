/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.SASPWAcesso;

/**
 *
 * <AUTHOR>
 */
public class SASPWAcessoDao {

    public SASPWAcesso buscarUltimoLoginMobile(String nome, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT TOP 1 Nome, Data, Hora, IMEI, Parametro, Codfil, CodPessoa, DataLog \n"
                    + " FROM SASPWAcesso \n"
                    + " WHERE Nome = ? \n"
                    + " ORDER BY DataLog DESC";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nome);
            consulta.select();
            SASPWAcesso retorno = null;
            if (consulta.Proximo()) {
                retorno = new SASPWAcesso();
                retorno.setNome(consulta.getString("Nome"));
                retorno.setData(consulta.getString("Data"));
                retorno.setHora(consulta.getString("Hora"));
                retorno.setIMEI(consulta.getString("IMEI"));
                retorno.setParametro(consulta.getString("Parametro"));
                retorno.setCodfil(consulta.getString("Codfil"));
                retorno.setCodPessoa(consulta.getString("CodPessoa"));
                retorno.setDataLog(consulta.getString("DataLog"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("SASPWAcessoDao.buscarUltimoLoginMobile - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 Nome, Data, Hora, IMEI, Parametro, Codfil, CodPessoa, DataLog \n"
                    + " FROM SASPWAcesso \n"
                    + " WHERE Nome = " + nome + "\n"
                    + " ORDER BY DataLog DESC");
        }
    }

    /**
     * Insere o login do Mobile
     *
     * @param sasPWAcesso
     * @param persistencia
     * @throws Exception
     */
    public void loginMobile(SASPWAcesso sasPWAcesso, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO SASPWAcesso (Nome, Data, Hora, IMEI, Parametro, Codfil, CodPessoa, DataLog) \n"
                    + "SELECT ?, ?, ?, ?, ?, ?, ?, ? FROM (\n"
                    + "SELECT COUNT(*) Total FROM SASPWAcesso\n"
                    + "WHERE Nome = ? AND Data = ? AND Hora = ?) A\n"
                    + "WHERE A.Total = 0;\n";
            Consulta consulta = new Consulta(sql, persistencia);
            // INSERT
            consulta.setString(sasPWAcesso.getNome());
            consulta.setString(sasPWAcesso.getData());
            consulta.setString(sasPWAcesso.getHora());
            consulta.setString(sasPWAcesso.getIMEI());
            consulta.setString(sasPWAcesso.getParametro());
            consulta.setString(sasPWAcesso.getCodfil());
            consulta.setString(sasPWAcesso.getCodPessoa());
            consulta.setString(sasPWAcesso.getDataLog());
            // SELECT
            consulta.setString(sasPWAcesso.getNome());
            consulta.setString(sasPWAcesso.getData());
            consulta.setString(sasPWAcesso.getHora());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("SASPWAcessoDao.loginMobile - " + e.getMessage() + "\r\n"
                    + " INSERT INTO SASPWAcesso (Nome, Data, Hora, IMEI, Parametro, Codfil, CodPessoa, DataLog) \n"
                    + "SELECT " + sasPWAcesso.getNome() + ", " + sasPWAcesso.getData() + ", " + sasPWAcesso.getHora() + ", " + sasPWAcesso.getIMEI() + ", "
                    + sasPWAcesso.getParametro() + ", " + sasPWAcesso.getCodfil() + ", " + sasPWAcesso.getCodPessoa() + ", " + sasPWAcesso.getDataLog() + " FROM (\n"
                    + "SELECT COUNT(*) Total FROM SASPWAcesso\n"
                    + "WHERE Nome = " + sasPWAcesso.getNome() + " AND Data = " + sasPWAcesso.getData() + " AND Hora = " + sasPWAcesso.getHora() + ") A\n"
                    + "WHERE A.Total = 0;\n");
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1030 {

    private String evtTabCargo_Id;
    private int sucesso;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideCargo_codCargo;
    private String ideCargo_iniValid;
    private String dadosCargo_nmCargo;
    private String dadosCargo_codCBO;

    public String getEvtTabCargo_Id() {
        return null == evtTabCargo_Id ? "" : evtTabCargo_Id;
    }

    public void setEvtTabCargo_Id(String evtTabCargo_Id) {
        this.evtTabCargo_Id = evtTabCargo_Id;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeCargo_codCargo() {
        return null == ideCargo_codCargo ? "" : ideCargo_codCargo;
    }

    public void setIdeCargo_codCargo(String ideCargo_codCargo) {
        this.ideCargo_codCargo = ideCargo_codCargo;
    }

    public String getIdeCargo_iniValid() {
        return null == ideCargo_iniValid ? "" : ideCargo_iniValid;
    }

    public void setIdeCargo_iniValid(String ideCargo_iniValid) {
        this.ideCargo_iniValid = ideCargo_iniValid;
    }

    public String getDadosCargo_nmCargo() {
        return null == dadosCargo_nmCargo ? "" : dadosCargo_nmCargo;
    }

    public void setDadosCargo_nmCargo(String dadosCargo_nmCargo) {
        this.dadosCargo_nmCargo = dadosCargo_nmCargo;
    }

    public String getDadosCargo_codCBO() {
        return null == dadosCargo_codCBO ? "" : dadosCargo_codCBO;
    }

    public void setDadosCargo_codCBO(String dadosCargo_codCBO) {
        this.dadosCargo_codCBO = dadosCargo_codCBO;
    }
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Cargos;
import SasBeans.FPMensal;
import SasBeans.FPRescisoes;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeansCompostas.SeguroDesemprego;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SeguroDesempregoDao {

    public List<SeguroDesemprego> SeguroDesemprego(BigDecimal codfil, String CodMovFP, String DtDemissao, Persistencia persistencia) throws Exception {
        try {
            List<SeguroDesemprego> list_segurodesemprego = new ArrayList();
            Consulta consult = new Consulta("Select filiais.cnpj, funcion.cpf, funcion.nome, funcion.endereco, "
                    + " funcion.numero, funcion.complemento, funcion.cep, funcion.uf, funcion.fone1,"
                    + " funcion.mae, funcion.pis, funcion.ctps_nro, funcion.ctps_serie, funcion.ctps_uf,"
                    + " cargos.cbo, funcion.dt_admis, fprescisoes.tipo, funcion.matr, fprescisoes.DtDemissao,"
                    + " funcion.Jornada, funcion.Sexo, funcion.Instrucao, funcion.dt_nasc"
                    + " from funcion"
                    + " left join cargos on cargos.cargo = funcion.cargo"
                    + " left join filiais on filiais.codfil = funcion.codfil"
                    + " left join fprescisoes on fprescisoes.matr = funcion.matr"
                    + " where fprescisoes.codmovfp = ? "
                    + " and funcion.codfil = ? "
                    + " and fprescisoes.dtdemissao = ? "
                    + " and fprescisoes.motivo not in (40,32) "
                    + " order by fprescisoes.codmovfp asc", persistencia);
            consult.setString(CodMovFP);
            consult.setBigDecimal(codfil);
            consult.setString(DtDemissao);
            consult.select();
            Consulta consultSalario;
            while (consult.Proximo()) {
                consultSalario = new Consulta("select top 3 liquido, proventos "
                        + " from fpmensal "
                        + " where codmovfp < ?"
                        + " and tipofp = ? "
                        + " and matr = ?"
                        + " order by codmovfp desc", persistencia);
                consultSalario.setString(CodMovFP);
                consultSalario.setString("MEN");
                consultSalario.setString(consult.getString("matr"));
                consultSalario.select();
                Funcion funcion = new Funcion();
                Cargos cargo = new Cargos();
                FPMensal fpmensal1 = new FPMensal();
                FPMensal fpmensal2 = new FPMensal();
                FPMensal fpmensal3 = new FPMensal();
                FPRescisoes fprescisoes = new FPRescisoes();
                Filiais filial = new Filiais();
                SeguroDesemprego segurodesemprego = new SeguroDesemprego();
                int i = 1;
                fpmensal1.setLiquido("0");
                fpmensal2.setLiquido("0");
                fpmensal3.setLiquido("0");

                fpmensal1.setProventos("0");
                fpmensal2.setProventos("0");
                fpmensal3.setProventos("0");
                while (consultSalario.Proximo()) {
                    switch (i) {
                        case (1):
                            fpmensal1.setLiquido(consultSalario.getString("liquido"));
                            fpmensal1.setProventos(consultSalario.getString("proventos"));
                            break;
                        case (2):
                            fpmensal2.setLiquido(consultSalario.getString("liquido"));
                            fpmensal2.setProventos(consultSalario.getString("proventos"));
                            break;
                        case (3):
                            fpmensal3.setLiquido(consultSalario.getString("liquido"));
                            fpmensal3.setProventos(consultSalario.getString("proventos"));
                            break;
                    }
                    i++;
                }
                consultSalario.Close();
                filial.setCNPJ(consult.getString("CNPJ"));
                funcion.setCPF(consult.getString("CPF"));
                funcion.setNome(consult.getString("nome"));
                funcion.setEndereco(consult.getString("endereco"));
                funcion.setNumero(consult.getString("numero"));
                funcion.setComplemento(consult.getString("complemento"));
                funcion.setCEP(consult.getString("cep"));
                funcion.setUF(consult.getString("uf"));
                funcion.setFone1(consult.getString("fone1"));
                funcion.setMae(consult.getString("mae"));
                funcion.setPIS(consult.getString("pis"));
                funcion.setCTPS_Nro(consult.getString("ctps_nro"));
                funcion.setCTPS_Serie(consult.getString("ctps_serie"));
                funcion.setCTPS_UF(consult.getString("ctps_uf"));
                funcion.setJornada(consult.getString("Jornada"));
                funcion.setDt_Admis(consult.getString("Dt_Admis"));
                funcion.setSexo(consult.getString("sexo"));
                funcion.setInstrucao(consult.getString("instrucao"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));

                cargo.setCBO(consult.getString("cbo"));
                fprescisoes.setDtDemissao(consult.getDate("DtDemissao").toLocalDate());
                fprescisoes.setTipo(consult.getString("tipo"));

                segurodesemprego.setFuncion(funcion);
                segurodesemprego.setCargos(cargo);
                segurodesemprego.setFpmensal1(fpmensal1);
                segurodesemprego.setFpmensal2(fpmensal2);
                segurodesemprego.setFpmensal3(fpmensal3);
                segurodesemprego.setFprescisoes(fprescisoes);
                segurodesemprego.setFiliais(filial);
                list_segurodesemprego.add(segurodesemprego);
                consultSalario.Close();
            }
            consult.Close();
            return list_segurodesemprego;
        } catch (Exception e) {
            throw new Exception("Falha ao gerar dados SeguroDesemprego - " + e.getMessage());
        }
    }
}

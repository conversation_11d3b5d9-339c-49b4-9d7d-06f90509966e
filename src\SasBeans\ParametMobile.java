package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ParametMobile {

    private BigDecimal Sequencia;
    private String Plataforma;
    private String Versao;
    private String VersaoAceita;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("1");
        }
    }

    public String getPlataforma() {
        return Plataforma;
    }

    public void setPlataforma(String Plataforma) {
        this.Plataforma = Plataforma;
    }

    public String getVersao() {
        return Versao;
    }

    public void setVersao(String Versao) {
        this.Versao = Versao;
    }

    public String getVersaoAceita() {
        return VersaoAceita;
    }

    public void setVersaoAceita(String VersaoAceita) {
        this.VersaoAceita = VersaoAceita;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

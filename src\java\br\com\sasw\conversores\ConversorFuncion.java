package br.com.sasw.conversores;

import SasBeans.Funcion;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorFuncion")
public class ConversorFuncion implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        Funcion f = new Funcion();
        f.setNome(value);
        return f;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            return ((Funcion) value).getNome();
        } catch (Exception e) {
            return null;
        }
    }

}

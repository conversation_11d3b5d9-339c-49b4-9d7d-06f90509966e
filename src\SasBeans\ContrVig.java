/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ContrVig {

    String Contrato;
    BigDecimal CodFil;
    String Descricao;
    String Tipo;
    String Situacao;
    String Identif;
    LocalDate Dt_Inicio;
    LocalDate Dt_Termino;
    String CliFat;
    String IDContrato;
    String ContratoCli;
    String RefArq;
    String OBS;
    String Operador;
    LocalDate Dt_Alter;
    String Hr_Alter;
    String NRed;

    public ContrVig() {
        this.Contrato = "";
        this.CodFil = new BigDecimal("0");
        this.Descricao = "";
        this.Tipo = "";
        this.Situacao = "";
        this.Identif = "";
        this.Dt_Inicio = null;
        this.Dt_Termino = null;
        this.CliFat = "";
        this.IDContrato = "";
        this.ContratoCli = "";
        this.RefArq = "";
        this.OBS = "";
        this.Operador = "";
        this.Dt_Alter = null;
        this.Hr_Alter = "";
        this.NRed = "";
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getIdentif() {
        return Identif;
    }

    public void setIdentif(String Identif) {
        this.Identif = Identif;
    }

    public LocalDate getDt_Inicio() {
        return Dt_Inicio;
    }

    public void setDt_Inicio(LocalDate Dt_Inicio) {
        this.Dt_Inicio = Dt_Inicio;
    }

    public LocalDate getDt_Termino() {
        return Dt_Termino;
    }

    public void setDt_Termino(LocalDate Dt_Termino) {
        this.Dt_Termino = Dt_Termino;
    }

    public String getCliFat() {
        return CliFat;
    }

    public void setCliFat(String CliFat) {
        this.CliFat = CliFat;
    }

    public String getIDContrato() {
        return IDContrato;
    }

    public void setIDContrato(String IDContrato) {
        this.IDContrato = IDContrato;
    }

    public String getContratoCli() {
        return ContratoCli;
    }

    public void setContratoCli(String ContratoCli) {
        this.ContratoCli = ContratoCli;
    }

    public String getRefArq() {
        return RefArq;
    }

    public void setRefArq(String RefArq) {
        this.RefArq = RefArq;
    }

    public String getOBS() {
        return OBS;
    }

    public void setOBS(String OBS) {
        this.OBS = OBS;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }
}

/*
 *
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class OS_VITens {

    private String OS;
    private String CodFil;
    private String TipoPosto;
    private String DtInicio;
    private String DtFim;
    private String Qtde;
    private String Obs;
    private String MsgExtrato;
    private String Valor;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String CHSeman;
    private String CHMensal;
    private String Salario;

    private String TipoPostoDesc;
    private String TipoCalc;

    public OS_VITens() {
        this.OS = "";
        this.CodFil = "";
        this.TipoPosto = "";
        this.DtInicio = "";
        this.DtFim = "";
        this.Qtde = "";
        this.Obs = "";
        this.MsgExtrato = "";
        this.Valor = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
        this.CHSeman = "";
        this.CHMensal = "";
        this.Salario = "";
        this.TipoPostoDesc = "";
        this.TipoCalc = "";
    }

    public OS_VITens(String inOS,
            String inCodFil,
            String inTipoPosto,
            String inDtInicio,
            String inDtFim,
            String inQtde,
            String inObs,
            String inMsgExtrato,
            String inValor,
            String inOperador,
            String inDt_Alter,
            String inHr_Alter,
            String inCHSeman,
            String inCHMensal,
            String inSalario,
            String inTipoPostoDesc,
            String inTipoCalc) {
        this.OS = inOS;
        this.CodFil = inCodFil;
        this.TipoPosto = inTipoPosto;
        this.DtInicio = inDtInicio;
        this.DtFim = inDtFim;
        this.Qtde = inQtde;
        this.Obs = inObs;
        this.MsgExtrato = inMsgExtrato;
        this.Valor = inValor;
        this.Operador = inOperador;
        this.Dt_Alter = inDt_Alter;
        this.Hr_Alter = inHr_Alter;
        this.CHSeman = inCHSeman;
        this.CHMensal = inCHMensal;
        this.Salario = inSalario;
        this.TipoPostoDesc = inTipoPostoDesc;
        this.TipoCalc = inTipoCalc;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getTipoPosto() {
        return TipoPosto;
    }

    public void setTipoPosto(String TipoPosto) {
        this.TipoPosto = TipoPosto;
    }

    public String getDtInicio() {
        return DtInicio;
    }

    public void setDtInicio(String DtInicio) {
        this.DtInicio = DtInicio;
    }

    public String getDtFim() {
        return DtFim;
    }

    public void setDtFim(String DtFim) {
        this.DtFim = DtFim;
    }

    public String getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        this.Qtde = Qtde;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getMsgExtrato() {
        return MsgExtrato;
    }

    public void setMsgExtrato(String MsgExtrato) {
        this.MsgExtrato = MsgExtrato;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getCHSeman() {
        return CHSeman;
    }

    public void setCHSeman(String CHSeman) {
        this.CHSeman = CHSeman;
    }

    public String getCHMensal() {
        return CHMensal;
    }

    public void setCHMensal(String CHMensal) {
        this.CHMensal = CHMensal;
    }

    public String getSalario() {
        return Salario;
    }

    public void setSalario(String Salario) {
        this.Salario = Salario;
    }

    public String getTipoPostoDesc() {
        return TipoPostoDesc;
    }

    public void setTipoPostoDesc(String TipoPostoDesc) {
        this.TipoPostoDesc = TipoPostoDesc;
    }

    public String getTipoCalc() {
        return TipoCalc;
    }

    public void setTipoCalc(String TipoCalc) {
        this.TipoCalc = TipoCalc;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/esocial.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                .calendario input{
                    opacity:0;
                    cursor: initial !important;
                    width:5px !important;
                }
                
                .SetaEsquerda,
                .SetaDireita{
                    display:inline-block !important;
                }
                
                .SetaEsquerda img,
                .SetaDireita img{
                    height:25px !important;
                    margin-bottom:8px !important;
                }
                
                .SetaEsquerda{
                    margin-right:16px !important;
                }
                
                .SetaDireita{
                    margin-left:4px !important;
                }
            </style>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{esocial.persistencia(login.pp)}" /> 
            </f:metadata>
            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-4">
                                    <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                    #{localemsgs.ESocial}
                                </div>

                                <div class="ui-grid-col-4" style="align-self: center; text-align: center;">
                                    <h:outputText value="#{localemsgs.Competencia}: "/>
                                    <h:outputText id="dataDia" value="#{esocial.competTela}" converter="conversorCompet"/>
                                </div>

                                <div class="ui-grid-col-3" style="align-self: center; text-align: center; position:relative !important">
                                    <p:commandLink action="#{esocial.competAnterior}" update="main:tabela cabecalho" class="SetaEsquerda">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" />  
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="yyyy-MM-dd" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{esocial.selecionarCompet}" update="main:tabela cabecalho" />
                                    </p:calendar>

                                    <p:commandLink action="#{esocial.competPosterior}" update="main:tabela cabecalho" class="SetaDireita">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" />  
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{esocial.nomeFilial}
                                    </div>
                                    <div class="ui-grid-col-8">
                                        #{localemsgs.QtdEventos}: #{esocial.total}
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>
                <div id="mainContainer">
                    <h:form id="main">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-12">
                                    <p:panel style="display: inline">
                                        <p:dataTable id="tabela" value="#{esocial.allXmls}" paginator="true" rows="15" lazy="true"
                                                     rowsPerPageTemplate="5,10,15, 20, 25"
                                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Eventos}"
                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                     var="xml" rowKey="#{xml.row}"
                                                     resizableColumns="true" selectionMode="single" styleClass="tabela"
                                                     selection="#{esocial.novoXMLeSocial}" emptyMessage="#{localemsgs.SemRegistros}"
                                                     scrollable="true" scrollWidth="100%"
                                                     style="font-size: 12px; background: white">

                                            <p:ajax event="rowDblselect" listener="#{esocial.rowDblselectTabela}" update="formCadastrar msgs"/>

                                            <p:column headerText="#{localemsgs.CodFil}">
                                                <h:outputText value="#{xml.codFil}" converter="conversorCodFil"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Evento}">
                                                <h:outputText value="#{xml.evento}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.InicioValidade}">
                                                <h:outputText value="#{xml.compet}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Ambiente}">
                                                <h:outputText value="#{xml.ambiente}" converter="ambienteESocial"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Dt_UltimoEnvio}">
                                                <h:outputText value="#{xml.dt_Envio}" converter="conversorData"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </div>
                            </div>
                        </div>

                        <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.Adicionar}" action="#{esocial.novo}"
                                               oncomplete="PF('dlgCadastrar').show();"
                                               update="formCadastrar msgs">
                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.Editar}" id="btnEditar" actionListener="#{esocial.buttonAction}"
                                               update="formCadastrar msgs">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.Ajuda}" target="_blank" ajax="false" action="#{esocial.ajuda}"
                                               update="formCadastrar msgs">
                                    <p:graphicImage url="../assets/img/icone_redondo_ajuda.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.A3}" target="_blank" ajax="false" action="#{esocial.certificadoA3}"
                                               update="formCadastrar msgs">
                                    <p:graphicImage url="../assets/img/icone_A3.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style=" top: 0px; right: 5px; position: fixed">
                                <p:commandLink title="#{localemsgs.Voltar}"
                                               action="#{login.voltar}">
                                    <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                        </p:panel>
                    </h:form>   

                    <!--Cadastrar novo-->
                    <h:form class="form-inline" id="formCadastrar">
                        <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                                  style="background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.CadastrarESocial}" style="color:#022a48;" /> 
                            </f:facet>
                            <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="evento" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="evento" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" 
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                        <p:ajax event="itemSelect" listener="#{esocial.selecionarEvento}"
                                                update="cadastrar"/>
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filial" value="#{localemsgs.Filial}" indicateRequired="false" />
                                    <p:inputText id="filial" value="#{esocial.novoeSocial.filial}" style="width: 100%"
                                                 required="true" >
                                        <p:watermark for="filial" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validade" value="#{localemsgs.InicioValidade}" indicateRequired="false"/>
                                    <p:inputMask id="validade" value="#{esocial.novoeSocial.compet}" required="true" 
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validade" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambiente" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambiente" required="true" value="#{esocial.novoeSocial.ambiente}">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIni" value="#{localemsgs.InicioAfastamento}" id="labelCadIniS2230"
                                                   rendered="#{esocial.novoeSocial.evento == 'S-2230' and esocial.novoeSocial.campoCadIni}"/>
                                    <p:outputLabel for="cadIni" value="#{localemsgs.CadastroInicial}" id="labelCadIni"
                                                   rendered="#{esocial.novoeSocial.evento != 'S-2230' and esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIni" value="#{esocial.novoeSocial.cadIni}" 
                                                      rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="data" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="data" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="data" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="excluir" value="#{localemsgs.EventoExcluir}" 
                                                   rendered="#{esocial.novoeSocial.evento eq 'S-3000'}"/>
                                    <p:selectOneMenu id="excluir" value="#{esocial.eventoExcluir}" rendered="#{esocial.novoeSocial.evento eq 'S-3000'}"
                                                     required="#{esocial.novoeSocial.evento eq 'S-3000'}"
                                                     styleClass="filial" style="width: 100%" filter="true" filterMatchMode="contains" >
                                        <f:selectItems value="#{esocial.eventosExcluir}"/>
                                    </p:selectOneMenu>

                                    <p:outputLabel for="tipo" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipo" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      required="#{esocial.novoeSocial.campoTipo}" styleClass="hideDisabled">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO" class="hideDisabled" itemDisabled="#{esocial.novoeSocial.evento == 'S-2200'}"/>
                                        <f:selectItem itemLabel="Retificação" itemValue="ALTERACAO" class="hideDisabled" itemDisabled="#{esocial.novoeSocial.evento != 'S-2200'}"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <div class="form-inline" style="padding-bottom: 10px;">
                                    <p:commandButton value="#{localemsgs.Prosseguir}" 
                                                     id="cadastro" action="#{esocial.listarEntradasEvento}"
                                                     update="msgs formCadastrar:cadastrar " 
                                                     title="#{localemsgs.Prosseguir}">
                                    </p:commandButton>
                                    <p:blockUI block="formCadastrar:cadastrar" trigger="formCadastrar:cadastro"/>                                    
                                </div>
                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formCertificado">    
                        <p:dialog widgetVar="dlgCertificado" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCertificado"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Certificado}" style="color:#022a48;" /> 
                            </f:facet>
                            <p:panel id="panelCertificado" style="background-color: transparent" styleClass="cadastrar">

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:panel style="text-align: center; width: 100%; height: 100px; background: white; border: 1px solid #aaaaaa !important;
                                             display: flex; justify-content: center; align-items: center;">
                                        <p:outputLabel for="certificado" value="#{localemsgs.Certificado} A1"/>
                                        <p:fileUpload id="certificado" value="#{esocial.certificado}" auto="true"
                                                      update="formCertificado:panelCertificado" class="cert"
                                                      fileUploadListener="#{esocial.carregarCertificado}" mode="advanced" skinSimple="true"
                                                      allowTypes="/(\.|\/)(pfx)$/" previewWidth="10" invalidFileMessage="#{localemsgs.ArquivoInvalido}">
                                        </p:fileUpload>
                                    </p:panel>
                                    <p:panel style="text-align: center; width: 100%; height: 100px; background: white; border: 1px solid #aaaaaa !important;
                                             display: flex; justify-content: center; align-items: center;">
                                        <p:commandButton id="btnCertficadoA3" value="#{localemsgs.Certificado} A3" action="#{esocial.integracaoAssinador}"
                                                         style="font-weight: bold" update="msgs" icon="fa fa-plus" />

                                    </p:panel>
                                </p:panelGrid>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-6" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="senha" value="#{localemsgs.Senha}" rendered="#{esocial.certificadoCarregado}"/>
                                    <p:password id="senha"  value="#{esocial.senha}" rendered="#{esocial.certificadoCarregado}" 
                                                redisplay="true" onfocus="this.value = ''"/>

                                    <h:outputText id="msgCertificado" value="#{esocial.nomeCertificado}"
                                                  style="color: green; font-style: italic;" rendered="#{esocial.certificadoCarregado}"/>
                                </p:panelGrid>

                                <p:commandLink id="btnEnviar2Envio" action="#{esocial.enviarXMLEvento}"
                                               update="msgs main:tabela" rendered="#{esocial.flag eq 1}"
                                               title="#{localemsgs.Enviar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink id="btnEnviar2Consulta" action="#{esocial.consultarPendentes}"
                                               update="msgs main:tabela" rendered="#{esocial.flag eq 2}"
                                               title="#{localemsgs.Enviar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </p:panel>
                        </p:dialog>

                        <p:dialog widgetVar="dlgAssinador" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgAssinador"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Certificado}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel style="background-color: transparent; width: 400px">

                                <h:outputText value="Para utilizar certificados A3 é necessário utilizar nosso assinador.
                                              Caso ainda não tenha baixado, realize o download no botão A3 abaixo."/>
                            </p:panel>

                            <p:commandLink title="#{localemsgs.A3}" target="_blank" ajax="false" action="#{esocial.certificadoA3}"
                                           onclick="PF('dlgAssinador').hide();">
                                <p:graphicImage url="../assets/img/icone_A3.png" width="40" height="40" />
                            </p:commandLink>

                            <p:commandLink title="#{localemsgs.Sim}" oncomplete="PF('dlgAssinador').hide();">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formValidacao">    
                        <p:dialog widgetVar="dlgValidacao" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgValidacao"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Validacao}" style="color:#022a48;" /> 
                            </f:facet>
                            <p:scrollPanel mode="native" id="panelValidacao" style="background-color: transparent; height: 70vh" styleClass="cadastrar">
                                <div  style="text-align: center; width: 100%;">
                                    <table style="text-align: center; width: 100%">
                                        <c:forEach var="eventos" items="#{esocial.eventosValidar}" varStatus="loop">
                                            <p:panel id="validacao_#{esocial.eventosEnviar.get(loop.index).identificador}_#{loop.index}"
                                                     header="#{localemsgs.Identificador}: #{esocial.eventosEnviar.get(loop.index).identificador}" toggleable="true" 
                                                     closable="false" toggleSpeed="500" closeSpeed="500" style="margin-bottom:20px; border: 2px solid #022a48 !important"
                                                     widgetVar="validacao_#{esocial.eventosEnviar.get(loop.index).identificador}_#{loop.index}" >
                                                <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" 
                                                             layout="grid" styleClass="ui-panelgrid-blank">
                                                    <c:forEach var="evento" items="#{eventos}">
                                                        <p:outputLabel value="#{evento.key}"/>
                                                        <p:inputText value="#{evento.value}" style="width: 100%" readonly="true"/>
                                                    </c:forEach>
                                                </p:panelGrid>
                                            </p:panel>
                                        </c:forEach>
                                    </table>
                                </div>


                            </p:scrollPanel>

                            <p:commandLink id="btnEnviar" action="#{esocial.telaCertificado}"
                                           update="msgs main:tabela"
                                           title="#{localemsgs.Enviar}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>

                            <p:commandLink id="btnCancelar" oncomplete="PF('dlgValidacao').hide()"
                                           update="msgs main:tabela"
                                           title="#{localemsgs.Enviar}">
                                <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formProcessamento">    
                        <p:dialog widgetVar="dlgProcessamento" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgProcessamento"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="Detalhes Processamento" style="color:#022a48;" /> 
                            </f:facet>
                            <p:panel id="panelProcessamento" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoProcessamento" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoProcessamento" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialProcessamento" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialProcessamento" value="#{esocial.novoeSocial.filial}" style="width: 100%" disabled="true">
                                        <p:watermark for="filialProcessamento" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeProcessamento" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeProcessamento" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeProcessamento" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteProcessamento" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteProcessamento" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>


                                    <p:outputLabel for="cadIniProcessamento" value="#{localemsgs.CadastroInicial}"
                                                   rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniProcessamento" value="#{esocial.novoeSocial.cadIni}" disabled="true"
                                                      rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataProcessamento" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataProcessamento" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}" disabled="true"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataProcessamento" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoProcessamento" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoProcessamento" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      required="#{esocial.novoeSocial.campoTipo}" disabled="true">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaProcessamento" value="#{esocial.listaProcessamento}" 
                                             var="processamento" rowKey="#{processamento.id}" scrollHeight="300"
                                             resizableColumns="true" styleClass="tabela" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" sortBy="#{processamento.identificador}"
                                             style="font-size: 12px; background: white">
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{processamento.identificador}" converter="identificadorESocial"/>
                                        </p:column>
                                    </p:headerRow>
                                    <p:column style="width:23px">
                                        <p:rowToggler rendered="#{processamento.ocorrencias.size() gt 0}"/>
                                    </p:column>
                                    <p:column headerText="dhProcessamento" style="width: 120px">
                                        <h:outputText value="#{processamento.dhProcessamento}" converter="conversorDataHora"/>
                                    </p:column>
                                    <p:column headerText="protocoloEnvioLote" style="width: 120px" rendered="false">
                                        <h:outputText value="#{processamento.protocolo}"/>
                                    </p:column>
                                    <p:column headerText="cdResposta" style="width: 100px">
                                        <h:outputText value="#{processamento.cdResposta}"/>
                                    </p:column>
                                    <p:column headerText="descResposta">
                                        <h:outputText value="#{processamento.descResposta}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:rowExpansion>
                                        <p:dataTable id="tabelaOcor" value="#{processamento.ocorrencias}" 
                                                     var="ocorrencia"
                                                     resizableColumns="true" styleClass="tabela" emptyMessage="#{localemsgs.SemRegistros}"
                                                     style="font-size: 12px; background: white;">
                                            <p:column headerText="Tipo" style="width: 50px">
                                                <h:outputText value="#{ocorrencia.tipo}"/>
                                            </p:column>
                                            <p:column headerText="Codigo" style="width: 75px">
                                                <h:outputText value="#{ocorrencia.codigo}"/>
                                            </p:column>
                                            <p:column headerText="Descricao">
                                                <h:outputText value="#{ocorrencia.descricao}" style="white-space: pre-line;"/>
                                            </p:column>
                                            <p:column headerText="Localizacao">
                                                <h:outputText value="#{ocorrencia.localizacao}" style="white-space: pre-line;"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:rowExpansion>
                                </p:dataTable>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:commandLink id="btnFechar" oncomplete="PF('dlgProcessamento').hide()"
                                                   title="#{localemsgs.Fechar}">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>

                                    <p:commandLink id="btnImprimir" action="#{esocial.gerarRelatorio}"
                                                   target="_blank" ajax="false" 
                                                   update="msgs main:tabela"
                                                   title="#{localemsgs.Imprimir}">
                                        <p:graphicImage url="../assets/img/icone_redondo_impressao.png" width="40" height="40" />
                                    </p:commandLink>
                                </p:panelGrid>                                
                            </p:panel>
                        </p:dialog>    
                    </h:form>

                    <h:form class="form-inline" id="formS1000">
                        <p:dialog widgetVar="dlgS1000" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1000"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1000 - Informações do Empregador/Contribuinte/Órgão Público" style="color:#022a48;" /> 
                            </f:facet>
                            <p:panel id="panelS1000" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1000" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1000" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1000" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1000" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialS1000" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1000" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1000" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1000" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1000" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1000" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1000" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1000" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1000" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1000" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}" disabled="true" 
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1000" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1000" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1000" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      required="#{esocial.novoeSocial.campoTipo}" disabled="true">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1000" value="#{esocial.s1000}" 
                                             var="s1000" rowKey="#{s1000.id}" scrollHeight="300" sortBy="#{s1000.sucesso}"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true"
                                             selection="#{esocial.s1000selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1000footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1000footer" />
                                    <p:ajax event="rowUnselect" update="s1000footer" />
                                    <p:ajax event="rowSelect" update="s1000footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s1000.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEmpregador nrInsc">
                                        <h:outputText value="#{s1000.ideEmpregador_nrInsc}" style="#{s1000.sucesso eq 1 ? 'color: orange' : 
                                                               (s1000.sucesso eq 0 ? 'color: red' : 
                                                               (s1000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoCadastro nmRazao">
                                        <h:outputText value="#{s1000.infoCadastro_nmRazao}" style="#{s1000.sucesso eq 1 ? 'color: orange' : 
                                                               (s1000.sucesso eq 0 ? 'color: red' : 
                                                               (s1000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="contato nmCtt">
                                        <h:outputText value="#{s1000.contato_nmCtt}" style="#{s1000.sucesso eq 1 ? 'color: orange' : 
                                                               (s1000.sucesso eq 0 ? 'color: red' : 
                                                               (s1000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="contato email">
                                        <h:outputText value="#{s1000.contato_email}" style="#{s1000.sucesso eq 1 ? 'color: orange' : 
                                                               (s1000.sucesso eq 0 ? 'color: red' : 
                                                               (s1000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s1000footer" value="#{esocial.s1000selecionado.size()} de #{esocial.s1000.size()} 
                                                      #{esocial.s1000.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1000selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1000" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1000" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1000" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1005">    
                        <p:dialog widgetVar="dlgS1005" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1005"
                                  style="background-image: url('../assets/img/icone_satmob_esocialG'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1005 - Tabela de Estabelecimentos, Obras ou Unidades de Órgãos Públicos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1005" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1005" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1005" styleClass="pstserv" value="#{esocial.novoeSocial.evento}"
                                                    style="width: 100%" forceSelection="true" placeholder="#{localemsgs.Evento}"
                                                    completeMethod="#{esocial.getEventos}" required="true" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}"
                                                    var="ev" itemLabel="#{ev}" itemValue="#{ev}" scrollHeight="250">
                                        <p:watermark for="eventoS1005" value="#{localemsgs.Evento}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1005" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1005" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialS1005" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1005" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1005" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1005" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1005" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1005" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1005" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1005" value="#{esocial.novoeSocial.cadIni}" 
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1005" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1005" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}" disabled="true" 
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1005" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1005" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1005" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1005" value="#{esocial.s1005}" 
                                             var="s1005" rowKey="#{s1005.evtTabEstab_Id}" scrollHeight="300"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true" sortBy="#{s1005.sucesso}"
                                             selection="#{esocial.s1005selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1005footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1005footer" />
                                    <p:ajax event="rowUnselect" update="s1005footer" />
                                    <p:ajax event="rowSelect" update="s1005footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="6">
                                            <h:outputText value="#{esocial.getSucesso(s1005.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEstab tpInsc" style="width: 105px;">
                                        <h:outputText value="#{s1005.ideEstab_tpInsc}" style="#{s1005.sucesso eq 1 ? 'color: orange' : 
                                                               (s1005.sucesso eq 0 ? 'color: red' : 
                                                               (s1005.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideEstab nrInsc" style="width: 105px;">
                                        <h:outputText value="#{s1005.ideEstab_nrInsc}" style="#{s1005.sucesso eq 1 ? 'color: orange' : 
                                                               (s1005.sucesso eq 0 ? 'color: red' : 
                                                               (s1005.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosEstab cnaePrep" style="width: 145px;">
                                        <h:outputText value="#{s1005.dadosEstab_cnaePrep}" style="#{s1005.sucesso eq 1 ? 'color: orange' : 
                                                               (s1005.sucesso eq 0 ? 'color: red' : 
                                                               (s1005.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="aliqGilrat fap" style="width: 90px;">
                                        <h:outputText value="#{s1005.aliqGilrat_fap}" style="#{s1005.sucesso eq 1 ? 'color: orange' : 
                                                               (s1005.sucesso eq 0 ? 'color: red' : 
                                                               (s1005.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="aliqGilrat aliqRat" style="width: 115px;">
                                        <h:outputText value="#{s1005.aliqGilrat_aliqRat}" style="#{s1005.sucesso eq 1 ? 'color: orange' : 
                                                               (s1005.sucesso eq 0 ? 'color: red' : 
                                                               (s1005.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s1005footer" value="#{esocial.s1005selecionado.size()} de #{esocial.s1005.size()} 
                                                      #{esocial.s1005.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1005selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1005" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1005" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1005" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1010">    
                        <p:dialog widgetVar="dlgS1010" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1010"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1010 - Tabela de Rubricas" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1010" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1010" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1010" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1010" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1010" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialS1010" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1010" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1010" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1010" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1010" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1010" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1010" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1010" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1010" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1010" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1010" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1010" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1010" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1010" value="#{esocial.s1010}" 
                                             var="s1010" rowKey="#{s1010.evtTabRubrica_Id}" scrollHeight="300"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true" sortBy="#{s1010.sucesso}"
                                             selection="#{esocial.s1010selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1010footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1010footer" />
                                    <p:ajax event="rowUnselect" update="s1010footer" />
                                    <p:ajax event="rowSelect" update="s1010footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{esocial.getSucesso(s1010.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideRubrica codRubr">
                                        <h:outputText value="#{s1010.ideRubrica_codRubr}" style="#{s1010.sucesso eq 1 ? 'color: orange' : 
                                                               (s1010.sucesso eq 0 ? 'color: red' : 
                                                               (s1010.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideRubrica ideTabRubr">
                                        <h:outputText value="#{s1010.ideRubrica_ideTabRubr}" style="#{s1010.sucesso eq 1 ? 'color: orange' : 
                                                               (s1010.sucesso eq 0 ? 'color: red' : 
                                                               (s1010.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosRubrica dscRubr">
                                        <h:outputText value="#{s1010.dadosRubrica_dscRubr}" style="#{s1010.sucesso eq 1 ? 'color: orange' : 
                                                               (s1010.sucesso eq 0 ? 'color: red' : 
                                                               (s1010.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>

                                    <f:facet name="footer">
                                        <h:outputText id="s1010footer" value="#{esocial.s1010selecionado.size()} de #{esocial.s1010.size()} 
                                                      #{esocial.s1010.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1010selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1010" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1010" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1010" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1020">    
                        <p:dialog widgetVar="dlgS1020" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1020"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1020 - Tabela de Lotações Tributárias" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1020" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1020" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1020" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1020" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1020" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialS1020" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1020" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1020" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1020" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1020" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1020" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1020" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1020" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1020" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1020" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1020" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1020" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1020" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1020" value="#{esocial.s1020}" 
                                             var="s1020" rowKey="#{s1020.evtTabLotacao_Id}" scrollHeight="300"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s1020.sucesso}"
                                             selection="#{esocial.s1020selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" expandableRowGroups="true"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1020footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1020footer" />
                                    <p:ajax event="rowUnselect" update="s1020footer" />
                                    <p:ajax event="rowSelect" update="s1020footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="3">
                                            <h:outputText value="#{esocial.getSucesso(s1020.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideLotacao codLotacao">
                                        <h:outputText value="#{s1020.ideLotacao_codLotacao}" converter="conversor0"
                                                      style="#{s1020.sucesso eq 1 ? 'color: orange' : 
                                                               (s1020.sucesso eq 0 ? 'color: red' : 
                                                               (s1020.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosLotacao_razaoSocial">
                                        <h:outputText value="#{s1020.dadosLotacao_razaoSocial}"
                                                      style="#{s1020.sucesso eq 1 ? 'color: orange' : 
                                                               (s1020.sucesso eq 0 ? 'color: red' : 
                                                               (s1020.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideEmpregador nrInsc">
                                        <h:outputText value="#{s1020.ideEmpregador_nrInsc}" style="#{s1020.sucesso eq 1 ? 'color: orange' : 
                                                               (s1020.sucesso eq 0 ? 'color: red' : 
                                                               (s1020.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s1020footer" value="#{esocial.s1020selecionado.size()} de #{esocial.s1020.size()} 
                                                      #{esocial.s1020.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1020selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1020" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1020" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1020" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1030">                         
                        <p:dialog widgetVar="dlgS1030" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1030"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1030 - Tabela de Cargos/Empregos Públicos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1030" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1030" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1030" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1030" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1030" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialS1030" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1030" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1030" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1030" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1030" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1030" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1030" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1030" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1030" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1030" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1030" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1030" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1030" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1030" value="#{esocial.s1030}" 
                                             var="s1030" rowKey="#{s1030.evtTabCargo_Id}" sortBy="#{s1030.sucesso}"
                                             resizableColumns="true" styleClass="tabela"  expandableRowGroups="true"
                                             selection="#{esocial.s1030selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1030footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1030footer" />
                                    <p:ajax event="rowUnselect" update="s1030footer" />
                                    <p:ajax event="rowSelect" update="s1030footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{esocial.getSucesso(s1030.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideCargo codCargo">
                                        <h:outputText value="#{s1030.ideCargo_codCargo}" style="#{s1030.sucesso eq 1 ? 'color: orange' : 
                                                               (s1030.sucesso eq 0 ? 'color: red' : 
                                                               (s1030.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosCargo nmCargo">
                                        <h:outputText value="#{s1030.dadosCargo_nmCargo}" style="#{s1030.sucesso eq 1 ? 'color: orange' : 
                                                               (s1030.sucesso eq 0 ? 'color: red' : 
                                                               (s1030.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosCargo codCBO">
                                        <h:outputText value="#{s1030.dadosCargo_codCBO}" style="#{s1030.sucesso eq 1 ? 'color: orange' : 
                                                               (s1030.sucesso eq 0 ? 'color: red' : 
                                                               (s1030.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s1030footer" value="#{esocial.s1030selecionado.size()} de #{esocial.s1030.size()} 
                                                      #{esocial.s1030.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1030selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1030" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1030" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1030" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1040">   
                        <p:dialog widgetVar="dlgS1040" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1040"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1040 - Tabela de Funções/Cargos em Comissão" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1040" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1040" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1040" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1040" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1040" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialS1040" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1040" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1040" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1040" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1040" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1040" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1040" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1040" value="#{esocial.novoeSocial.cadIni}" 
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1040" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1040" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}" disabled="true" 
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1040" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1040" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1040" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1040" value="#{esocial.s1040}" 
                                             var="s1040" rowKey="#{s1040.evtTabFuncao_Id}" sortBy="#{s1040.sucesso}"
                                             resizableColumns="true" styleClass="tabela"  expandableRowGroups="true"
                                             selection="#{esocial.s1040selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1040footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1040footer" />
                                    <p:ajax event="rowUnselect" update="s1040footer" />
                                    <p:ajax event="rowSelect" update="s1040footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{esocial.getSucesso(s1040.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideFuncao codFuncao">
                                        <h:outputText value="#{s1040.ideFuncao_codFuncao}" style="#{s1040.sucesso eq 1 ? 'color: orange' : 
                                                               (s1040.sucesso eq 0 ? 'color: red' : 
                                                               (s1040.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosFuncao dscFuncao">
                                        <h:outputText value="#{s1040.dadosFuncao_dscFuncao}" style="#{s1040.sucesso eq 1 ? 'color: orange' : 
                                                               (s1040.sucesso eq 0 ? 'color: red' : 
                                                               (s1040.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosFuncao codCBO">
                                        <h:outputText value="#{s1040.dadosFuncao_codCBO}" style="#{s1040.sucesso eq 1 ? 'color: orange' : 
                                                               (s1040.sucesso eq 0 ? 'color: red' : 
                                                               (s1040.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s1040footer" value="#{esocial.s1040selecionado.size()} de #{esocial.s1040.size()} 
                                                      #{esocial.s1040.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1040selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1040" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1040" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1040" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1050">     
                        <p:dialog widgetVar="dlgS1050" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1050"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1050 - Tabela de Horários/Turnos de Trabalho" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1050" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1050" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1050" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1050" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1050" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialS1050" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1050" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1050" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1050" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1050" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1050" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1050" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1050" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1050" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1050" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1050" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1050" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1050" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1050" value="#{esocial.s1050}" 
                                             var="s1050" rowKey="#{s1050.evtTabHorTur_Id}"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true"
                                             selection="#{esocial.s1050selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1050footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1050footer" />
                                    <p:ajax event="rowUnselect" update="s1050footer" />
                                    <p:ajax event="toggleSelect" update="s1050footer" />
                                    <p:ajax event="rowSelect" update="s1050footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{esocial.getSucesso(s1050.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideHorContratual codHorContrat">
                                        <h:outputText value="#{s1050.ideHorContratual_codHorContrat}" style="#{s1050.sucesso eq 1 ? 'color: orange' : 
                                                               (s1050.sucesso eq 0 ? 'color: red' : 
                                                               (s1050.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosHorContratual hrEntr">
                                        <h:outputText value="#{s1050.dadosHorContratual_hrEntr}" style="#{s1050.sucesso eq 1 ? 'color: orange' : 
                                                               (s1050.sucesso eq 0 ? 'color: red' : 
                                                               (s1050.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="dadosHorContratual hrSaida">
                                        <h:outputText value="#{s1050.dadosHorContratual_hrSaida}" style="#{s1050.sucesso eq 1 ? 'color: orange' : 
                                                               (s1050.sucesso eq 0 ? 'color: red' : 
                                                               (s1050.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s1050footer" value="#{esocial.s1050selecionado.size()} de #{esocial.s1050.size()} 
                                                      #{esocial.s1050.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1050selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1050" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1050" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1050" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1200">    
                        <p:dialog widgetVar="dlgS1200" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1200"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialP.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1200 - Remuneração de trabalhador vinculado ao Regime Geral de Previd. Social"
                                              style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1200" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1200" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1200" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1200" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1200" value="#{esocial.novoeSocial.filial}"
                                                 disabled="true" style="width: 100%">
                                        <p:watermark for="filialS1200" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1200" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1200" value="#{esocial.novoeSocial.compet}"
                                                 disabled="true" style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1200" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1200" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1200" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1200" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1200" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1200" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1200" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1200" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1200" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1200" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1200" value="#{esocial.s1200}" sortBy="#{s1200.sucesso}"
                                             var="s1200" rowKey="#{s1200.evtRemun_Id}" expandableRowGroups="true"
                                             resizableColumns="true" styleClass="tabela" 
                                             selection="#{esocial.s1200selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1200footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1200footer" />
                                    <p:ajax event="rowUnselect" update="s1200footer" />
                                    <p:ajax event="rowSelect" update="s1200footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="3">
                                            <h:outputText value="#{esocial.getSucesso(s1200.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideTrabalhador cpfTrab">
                                        <h:outputText value="#{s1200.ideTrabalhador_cpfTrab}" style="#{s1200.sucesso eq 1 ? 'color: orange' : 
                                                               (s1200.sucesso eq 0 ? 'color: red' : 
                                                               (s1200.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideTrabalhador matr">
                                        <h:outputText value="#{s1200.ideTrabalhador_matr}" style="#{s1200.sucesso eq 1 ? 'color: orange' : 
                                                               (s1200.sucesso eq 0 ? 'color: red' : 
                                                               (s1200.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoComplem nmTrab">
                                        <h:outputText value="#{s1200.infoComplem_nmTrab}" style="#{s1200.sucesso eq 1 ? 'color: orange' : 
                                                               (s1200.sucesso eq 0 ? 'color: red' : 
                                                               (s1200.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s1200footer" value="#{esocial.s1200selecionado.size()} de #{esocial.s1200.size()} 
                                                      #{esocial.s1200.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1200selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1200" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1200" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1200" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1210">    
                        <p:dialog widgetVar="dlgS1210" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1210"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialP.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1210 - Pagamentos de Rendimentos do Trabalho" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1210" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1210" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1210" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1210" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1210" value="#{esocial.novoeSocial.filial}" style="width: 100%" disabled="true">
                                        <p:watermark for="filialS1210" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1210" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1210" value="#{esocial.novoeSocial.compet}"
                                                 disabled="true" style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1210" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1210" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1210" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1210" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1210" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1210" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1210" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1210" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1210" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1210" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1210" value="#{esocial.s1210}" sortBy="#{s1210.sucesso}"
                                             var="s1210" rowKey="#{s1210.evtPgtos_Id}" expandableRowGroups="true"
                                             resizableColumns="true" styleClass="tabela" 
                                             selection="#{esocial.s1210selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1210footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1210footer" />
                                    <p:ajax event="rowUnselect" update="s1210footer" />
                                    <p:ajax event="rowSelect" update="s1210footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s1210.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideBenef cpfBenef">
                                        <h:outputText value="#{s1210.ideBenef_cpfBenef}" style="#{s1210.sucesso eq 1 ? 'color: orange' : 
                                                               (s1210.sucesso eq 0 ? 'color: red' : 
                                                               (s1210.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="ideBenef matr">
                                        <h:outputText value="#{s1210.ideBenef_matr}" style="#{s1210.sucesso eq 1 ? 'color: orange' : 
                                                               (s1210.sucesso eq 0 ? 'color: red' : 
                                                               (s1210.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="ideBenef nome">
                                        <h:outputText value="#{s1210.ideBenef_nome}" style="#{s1210.sucesso eq 1 ? 'color: orange' : 
                                                               (s1210.sucesso eq 0 ? 'color: red' : 
                                                               (s1210.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>                                    
                                    <f:facet name="footer">
                                        <h:outputText id="s1210footer" value="#{esocial.s1210selecionado.size()} de #{esocial.s1210.size()} 
                                                      #{esocial.s1210.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1210selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1210" action="#{esocial.preparacaoEnvio}" update="msgs panelS1210" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1210" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1210" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>
                    
                    <h:form class="form-inline" id="formS1280">    
                        <p:dialog widgetVar="dlgS1280" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1280"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialP.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1280 - Informações Complementares aos Eventos Periódicos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1280" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1280" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1280" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1280" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1280" value="#{esocial.novoeSocial.filial}" style="width: 100%" disabled="true">
                                        <p:watermark for="filialS1280" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1280" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1280" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1280" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1280" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1280" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1280" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1280" value="#{esocial.novoeSocial.cadIni}" rendered="#{esocial.novoeSocial.campoCadIni}"
                                                      disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1280" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1280" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1280" value="#{esocial.s1280}" sortBy="#{s1280.sucesso}"
                                             var="s1280" rowKey="#{s1280.evtInfoComplPer_id}" expandableRowGroups="true"
                                             resizableColumns="true" styleClass="tabela" 
                                             selection="#{esocial.s1280selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1280footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1280footer" />
                                    <p:ajax event="rowUnselect" update="s1280footer" />
                                    <p:ajax event="rowSelect" update="s1280footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="7">
                                            <h:outputText value="#{esocial.getSucesso(s1280.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEvento perApur">
                                        <h:outputText value="#{s1280.ideEvento_perApur}" style="#{s1280.sucesso eq 1 ? 'color: orange' : 
                                                               (s1280.sucesso eq 0 ? 'color: red' : 
                                                               (s1280.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversor0"/>
                                    </p:column>
                                    <p:column headerText="ideEmpregador nrInsc">
                                        <h:outputText value="#{s1280.ideEmpregador_nrInsc}" style="#{s1280.sucesso eq 1 ? 'color: orange' : 
                                                               (s1280.sucesso eq 0 ? 'color: red' : 
                                                               (s1280.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversor0"/>
                                    </p:column>

                                    <f:facet name="footer">
                                        <h:outputText id="s1280footer" value="#{esocial.s1280selecionado.size()} de #{esocial.s1280.size()} 
                                                      #{esocial.s1280.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1280selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1280" action="#{esocial.preparacaoEnvio}" update="msgs panelS1280" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1280" action="#{esocial.preparacaoConsulta}" update="msgs panelS1280" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1280" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1295">    
                        <p:dialog widgetVar="dlgS1295" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1295"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialP.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1295 - Solicitação de Totalização para Pagamento em Contingência" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1295" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1295" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1295" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1295" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1295" value="#{esocial.novoeSocial.filial}" style="width: 100%" disabled="true">
                                        <p:watermark for="filialS1295" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1295" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1295" value="#{esocial.novoeSocial.compet}"
                                                 disabled="true" style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1295" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1295" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1295" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1295" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1295" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1295" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1295" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1295" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1295" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1295" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1295" value="#{esocial.s1295}" sortBy="#{s1295.sucesso}"
                                             var="s1295" rowKey="#{s1295.evtTotConting_Id}" expandableRowGroups="true"
                                             resizableColumns="true" styleClass="tabela" 
                                             selection="#{esocial.s1295selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1295footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1295footer" />
                                    <p:ajax event="rowUnselect" update="s1295footer" />
                                    <p:ajax event="rowSelect" update="s1295footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s1295.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEmpregador nrInsc">
                                        <h:outputText value="#{s1295.ideEmpregador_nrInsc}" style="#{s1295.sucesso eq 1 ? 'color: orange' : 
                                                               (s1295.sucesso eq 0 ? 'color: red' : 
                                                               (s1295.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="ideRespInf nmResp">
                                        <h:outputText value="#{s1295.ideRespInf_nmResp}" style="#{s1295.sucesso eq 1 ? 'color: orange' : 
                                                               (s1295.sucesso eq 0 ? 'color: red' : 
                                                               (s1295.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="ideRespInf email">
                                        <h:outputText value="#{s1295.ideRespInf_email}" style="#{s1295.sucesso eq 1 ? 'color: orange' : 
                                                               (s1295.sucesso eq 0 ? 'color: red' : 
                                                               (s1295.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>                                    
                                    <f:facet name="footer">
                                        <h:outputText id="s1295footer" value="#{esocial.s1295selecionado.size()} de #{esocial.s1295.size()} 
                                                      #{esocial.s1295.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1295selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1295" action="#{esocial.preparacaoEnvio}" update="msgs panelS1295" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1295" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1295" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1298">    
                        <p:dialog widgetVar="dlgS1298" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1298"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialP.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1298 - Reabertura dos Eventos Periódicos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1298" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1298" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1298" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1298" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1298" value="#{esocial.novoeSocial.filial}" style="width: 100%" disabled="true">
                                        <p:watermark for="filialS1298" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1298" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1298" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1298" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1298" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1298" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1298" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1298" value="#{esocial.novoeSocial.cadIni}" rendered="#{esocial.novoeSocial.campoCadIni}"
                                                      disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1298" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1298" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1298" value="#{esocial.s1298}" sortBy="#{s1298.sucesso}"
                                             var="s1298" rowKey="#{s1298.evtReabreEvPer_Id}" expandableRowGroups="true"
                                             resizableColumns="true" styleClass="tabela" 
                                             selection="#{esocial.s1298selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1298footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1298footer" />
                                    <p:ajax event="rowUnselect" update="s1298footer" />
                                    <p:ajax event="rowSelect" update="s1298footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="7">
                                            <h:outputText value="#{esocial.getSucesso(s1298.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEvento perApur">
                                        <h:outputText value="#{s1298.ideEvento_perApur}" style="#{s1298.sucesso eq 1 ? 'color: orange' : 
                                                               (s1298.sucesso eq 0 ? 'color: red' : 
                                                               (s1298.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversor0"/>
                                    </p:column>
                                    <p:column headerText="ideEmpregador nrInsc">
                                        <h:outputText value="#{s1298.ideEmpregador_nrInsc}" style="#{s1298.sucesso eq 1 ? 'color: orange' : 
                                                               (s1298.sucesso eq 0 ? 'color: red' : 
                                                               (s1298.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversor0"/>
                                    </p:column>

                                    <f:facet name="footer">
                                        <h:outputText id="s1298footer" value="#{esocial.s1298selecionado.size()} de #{esocial.s1298.size()} 
                                                      #{esocial.s1298.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1298selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1298" action="#{esocial.preparacaoEnvio}" update="msgs panelS1298" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1298" action="#{esocial.preparacaoConsulta}" update="msgs panelS1298" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1298" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS1299">    
                        <p:dialog widgetVar="dlgS1299" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS1299"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialP.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-1299 - Fechamento dos Eventos Periódicos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS1299" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS1299" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS1299" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS1299" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS1299" value="#{esocial.novoeSocial.filial}" style="width: 100%" disabled="true">
                                        <p:watermark for="filialS1299" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS1299" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS1299" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS1299" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS1299" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS1299" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS1299" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS1299" value="#{esocial.novoeSocial.cadIni}" rendered="#{esocial.novoeSocial.campoCadIni}"
                                                      disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS1299" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS1299" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}" disabled="true"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS1299" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS1299" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS1299" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS1299" value="#{esocial.s1299}" sortBy="#{s1299.sucesso}"
                                             var="s1299" rowKey="#{s1299.evtFechaEvPer_Id}" expandableRowGroups="true"
                                             resizableColumns="true" styleClass="tabela" 
                                             selection="#{esocial.s1299selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s1299footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s1299footer" />
                                    <p:ajax event="rowUnselect" update="s1299footer" />
                                    <p:ajax event="rowSelect" update="s1299footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="7">
                                            <h:outputText value="#{esocial.getSucesso(s1299.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="evtRemun">
                                        <h:outputText value="#{s1299.infoFech_evtRemun}" style="#{s1299.sucesso eq 1 ? 'color: orange' : 
                                                               (s1299.sucesso eq 0 ? 'color: red' : 
                                                               (s1299.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversor0"/>
                                    </p:column>
                                    <p:column headerText="evtPgtos">
                                        <h:outputText value="#{s1299.infoFech_evtPgtos}" style="#{s1299.sucesso eq 1 ? 'color: orange' : 
                                                               (s1299.sucesso eq 0 ? 'color: red' : 
                                                               (s1299.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="evtAqProd">
                                        <h:outputText value="#{s1299.infoFech_evtAqProd}" style="#{s1299.sucesso eq 1 ? 'color: orange' : 
                                                               (s1299.sucesso eq 0 ? 'color: red' : 
                                                               (s1299.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="evtComProd">
                                        <h:outputText value="#{s1299.infoFech_evtComProd}" style="#{s1299.sucesso eq 1 ? 'color: orange' : 
                                                               (s1299.sucesso eq 0 ? 'color: red' : 
                                                               (s1299.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="evtContratAvNP">
                                        <h:outputText value="#{s1299.infoFech_evtContratAvNP}" style="#{s1299.sucesso eq 1 ? 'color: orange' : 
                                                               (s1299.sucesso eq 0 ? 'color: red' : 
                                                               (s1299.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="evtInfoComplPer">
                                        <h:outputText value="#{s1299.infoFech_evtInfoComplPer}" style="#{s1299.sucesso eq 1 ? 'color: orange' : 
                                                               (s1299.sucesso eq 0 ? 'color: red' : 
                                                               (s1299.sucesso eq 2 ? 'color: green' : 'color: black'))}" converter="conversorData"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s1299footer" value="#{esocial.s1299selecionado.size()} de #{esocial.s1299.size()} 
                                                      #{esocial.s1299.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s1299selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS1299" action="#{esocial.preparacaoEnvio}" update="msgs panelS1299" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS1299" action="#{esocial.preparacaoConsulta}" update="msgs panelS1299" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS1299" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS2190">    
                        <p:dialog widgetVar="dlgS2190" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2190"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-2190 - Admissão de Trabalhador - Registro Preliminar" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2190" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2190" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2190" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2190" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2190" value="#{esocial.novoeSocial.filial}" style="width: 100%"
                                                 disabled="true" >
                                        <p:watermark for="filialS2190" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2190" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2190" value="#{esocial.novoeSocial.compet}"
                                                 disabled="true" style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2190" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2190" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2190" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2190" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2190" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2190" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2190" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2190" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2190" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2190" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2190" value="#{esocial.s2190}" 
                                             var="s2190" rowKey="#{s2190.evtAdmPrelim_Id}"
                                             resizableColumns="true" styleClass="tabela" 
                                             selection="#{esocial.s2190selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2190footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2190footer" />
                                    <p:ajax event="rowUnselect" update="s2190footer" />
                                    <p:ajax event="rowSelect" update="s2190footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:column headerText="infoRegPrelim cpfTrab">
                                        <h:outputText value="#{s2190.infoRegPrelim_cpfTrab}" style="#{s2190.sucesso eq 1 ? 'color: orange' : 
                                                               (s2190.sucesso eq 0 ? 'color: red' : 
                                                               (s2190.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoRegPrelim dtAdm">
                                        <h:outputText value="#{s2190.infoRegPrelim_dtAdm}" style="#{s2190.sucesso eq 1 ? 'color: orange' : 
                                                               (s2190.sucesso eq 0 ? 'color: red' : 
                                                               (s2190.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoRegPrelim dtNascto">
                                        <h:outputText value="#{s2190.infoRegPrelim_dtNascto}" style="#{s2190.sucesso eq 1 ? 'color: orange' : 
                                                               (s2190.sucesso eq 0 ? 'color: red' : 
                                                               (s2190.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2190footer" value="#{esocial.s2190selecionado.size()} de #{esocial.s2190.size()} 
                                                      #{esocial.s2190.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2190selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2190" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2190" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2190" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formS2200">    
                        <p:dialog widgetVar="dlgS2200" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2200"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-2200 - Admissão / Ingresso de Trabalhador" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2200" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2200" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2200" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2200" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2200" value="#{esocial.novoeSocial.filial}"
                                                 disabled="true" style="width: 100%">
                                        <p:watermark for="filialS2200" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2200" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2200" value="#{esocial.novoeSocial.compet}"
                                                 disabled="true" style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2200" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2200" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2200" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2200" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2200" value="#{esocial.novoeSocial.cadIni}"
                                                      rendered="#{esocial.novoeSocial.campoCadIni}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2200" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2200" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}" disabled="true"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2200" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2200" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2200" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Retificação" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2200" value="#{esocial.s2200}"  sortBy="#{s2200.sucesso}"
                                             var="s2200" rowKey="#{s2200.evtAdmissao_Id}" expandableRowGroups="true"
                                             resizableColumns="true" styleClass="tabela" 
                                             selection="#{esocial.s2200selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2200footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2200footer" />
                                    <p:ajax event="rowUnselect" update="s2200footer" />
                                    <p:ajax event="rowSelect" update="s2200footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{esocial.getSucesso(s2200.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="vinculo matricula">
                                        <h:outputText value="#{s2200.vinculo_matricula}" 
                                                      converter="conversor0"
                                                      style="#{s2200.sucesso eq 1 ? 'color: orange' : 
                                                               (s2200.sucesso eq 0 ? 'color: red' : 
                                                               (s2200.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="trabalhador nmTrab">
                                        <h:outputText value="#{s2200.trabalhador_nmTrab}" style="#{s2200.sucesso eq 1 ? 'color: orange' : 
                                                               (s2200.sucesso eq 0 ? 'color: red' : 
                                                               (s2200.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="trabalhador cpfTrab">
                                        <h:outputText value="#{s2200.trabalhador_cpfTrab}" style="#{s2200.sucesso eq 1 ? 'color: orange' : 
                                                               (s2200.sucesso eq 0 ? 'color: red' : 
                                                               (s2200.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2200footer" value="#{esocial.s2200selecionado.size()} de #{esocial.s2200.size()} 
                                                      #{esocial.s2200.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2200selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2200" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2200" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2200" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>
                    <h:form class="form-inline" id="formS2205">    
                        <p:dialog widgetVar="dlgS2205" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2205"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.evento}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2205" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2205" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2205" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2205" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2205" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2205" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2205" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2205" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2205" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2205" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2205" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2205" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2205" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2205" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2205" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2205" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2205" value="#{esocial.s2205}" 
                                             var="s2205" rowKey="#{s2205.evtAltCadastral_Id}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2205.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2205selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2205footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2205footer" />
                                    <p:ajax event="rowUnselect" update="s2205footer" />
                                    <p:ajax event="rowSelect" update="s2205footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2205.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2205.matr}" style="#{s2205.sucesso eq 1 ? 'color: orange' : 
                                                               (s2205.sucesso eq 0 ? 'color: red' : 
                                                               (s2205.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideVinculo cpfTrab">
                                        <h:outputText value="#{s2205.ideTrabalhador_cpfTrab}" style="#{s2205.sucesso eq 1 ? 'color: orange' : 
                                                               (s2205.sucesso eq 0 ? 'color: red' : 
                                                               (s2205.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>=
                                    <f:facet name="footer">
                                        <h:outputText id="s2205footer" value="#{esocial.s2205selecionado.size()} de #{esocial.s2205.size()} 
                                                      #{esocial.s2205.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2205selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2205" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2205" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2205" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>                                    
                    
                    
                    <h:form class="form-inline" id="formS2210">    
                        <p:dialog widgetVar="dlgS2210" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2210"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.evento}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2210" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2210" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2210" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2210" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2210" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2210" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2210" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2210" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2210" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2210" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2210" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2210" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2210" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2210" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2210" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2210" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2210" value="#{esocial.s2210}" 
                                             var="s2210" rowKey="#{s2210.evtCAT_Id}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2210.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2210selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2210footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2210footer" />
                                    <p:ajax event="rowUnselect" update="s2210footer" />
                                    <p:ajax event="rowSelect" update="s2210footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2210.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2210.ideVinculo_matr}" style="#{s2210.sucesso eq 1 ? 'color: orange' : 
                                                               (s2210.sucesso eq 0 ? 'color: red' : 
                                                               (s2210.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideVinculo cpfTrab">
                                        <h:outputText value="#{s2210.ideVinculo_cpfTrab}" style="#{s2210.sucesso eq 1 ? 'color: orange' : 
                                                               (s2210.sucesso eq 0 ? 'color: red' : 
                                                               (s2210.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>=
                                    <f:facet name="footer">
                                        <h:outputText id="s2210footer" value="#{esocial.s2210selecionado.size()} de #{esocial.s2210.size()} 
                                                      #{esocial.s2210.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2210selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2210" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2210" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2210" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>           
                                                            
                    
                    <h:form class="form-inline" id="formS2206">    
                        <p:dialog widgetVar="dlgS2206" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2206"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.evento}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2206" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2206" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2206" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2206" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2206" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2206" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2206" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2206" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2206" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2206" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2206" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2206" value="#{localemsgs.InicioAfastamento}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2206" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2206" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2206" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2206" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2206" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2206" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2206" value="#{esocial.s2206}" 
                                             var="s2206" rowKey="#{s2206.evtAltContratual_Id}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2206.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2206selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2206footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2206footer" />
                                    <p:ajax event="rowUnselect" update="s2206footer" />
                                    <p:ajax event="rowSelect" update="s2206footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2206.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2206.ideVinculo.ideVinculo_matricula}" style="#{s2206.sucesso eq 1 ? 'color: orange' : 
                                                               (s2206.sucesso eq 0 ? 'color: red' : 
                                                               (s2206.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideVinculo cpfTrab">
                                        <h:outputText value="#{s2206.ideVinculo.ideVinculo_cpfTrab}" style="#{s2206.sucesso eq 1 ? 'color: orange' : 
                                                               (s2206.sucesso eq 0 ? 'color: red' : 
                                                               (s2206.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>=
                                    <f:facet name="footer">
                                        <h:outputText id="s2206footer" value="#{esocial.s2206selecionado.size()} de #{esocial.s2206.size()} 
                                                      #{esocial.s2206.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2206selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2206" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2206" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2206" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>           
                    
                    <h:form class="form-inline" id="formS2220">    
                        <p:dialog widgetVar="dlgS2220" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2220"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.evento}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2220" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2220" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2220" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2220" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2220" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2220" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2220" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2220" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2220" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2220" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2220" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2220" value="#{localemsgs.InicioAfastamento}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2220" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2220" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2220" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2220" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2220" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2220" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2220" value="#{esocial.s2220}" 
                                             var="s2220" rowKey="#{s2220.evtMonit_Id}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2220.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2220selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2220footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2220footer" />
                                    <p:ajax event="rowUnselect" update="s2220footer" />
                                    <p:ajax event="rowSelect" update="s2220footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2220.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2220.ideVinculo_matricula}" style="#{s2220.sucesso eq 1 ? 'color: orange' : 
                                                               (s2220.sucesso eq 0 ? 'color: red' : 
                                                               (s2220.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="exMedOcup dtAso">
                                        <h:outputText value="#{s2220.exMedOcup_dtAso}" style="#{s2220.sucesso eq 1 ? 'color: orange' : 
                                                               (s2220.sucesso eq 0 ? 'color: red' : 
                                                               (s2220.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="exMedOcup dtExm">
                                        <h:outputText value="#{s2220.exMedOcup_dtExm}" style="#{s2220.sucesso eq 1 ? 'color: orange' : 
                                                               (s2220.sucesso eq 0 ? 'color: red' : 
                                                               (s2220.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="exMedOcup tpExameOcup">
                                        <h:outputText value="#{s2220.exMedOcup_tpExameOcup}" style="#{s2220.sucesso eq 1 ? 'color: orange' : 
                                                               (s2220.sucesso eq 0 ? 'color: red' : 
                                                               (s2220.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2220footer" value="#{esocial.s2220selecionado.size()} de #{esocial.s2220.size()} 
                                                      #{esocial.s2220.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2220selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2220" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2220" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2220" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>
                    
                    <h:form class="form-inline" id="formS2230">    
                        <p:dialog widgetVar="dlgS2230" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2230"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.evento}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2230" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2230" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2230" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2230" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2230" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2230" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2230" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2230" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2230" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2230" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2230" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2230" value="#{localemsgs.InicioAfastamento}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2230" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2230" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2230" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2230" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2230" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2230" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2230" value="#{esocial.s2230}" 
                                             var="s2230" rowKey="#{s2230.evtAfastTemp_Id}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2230.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2230selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2230footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2230footer" />
                                    <p:ajax event="rowUnselect" update="s2230footer" />
                                    <p:ajax event="rowSelect" update="s2230footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2230.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2230.ideVinculo_matricula}" style="#{s2230.sucesso eq 1 ? 'color: orange' : 
                                                               (s2230.sucesso eq 0 ? 'color: red' : 
                                                               (s2230.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="iniAfastamento dtIniAfast">
                                        <h:outputText value="#{s2230.iniAfastamento_dtIniAfast}" style="#{s2230.sucesso eq 1 ? 'color: orange' : 
                                                               (s2230.sucesso eq 0 ? 'color: red' : 
                                                               (s2230.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="fimAfastamento dtTermAfast">
                                        <h:outputText value="#{s2230.fimAfastamento_dtTermAfast}" style="#{s2230.sucesso eq 1 ? 'color: orange' : 
                                                               (s2230.sucesso eq 0 ? 'color: red' : 
                                                               (s2230.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="iniAfastamento codMotAfast">
                                        <h:outputText value="#{s2230.iniAfastamento_codMotAfast}" style="#{s2230.sucesso eq 1 ? 'color: orange' : 
                                                               (s2230.sucesso eq 0 ? 'color: red' : 
                                                               (s2230.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2230footer" value="#{esocial.s2230selecionado.size()} de #{esocial.s2230.size()} 
                                                      #{esocial.s2230.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2230selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2230" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2230" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2230" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>
                    
                    <h:form class="form-inline" id="formS2240">    
                        <p:dialog widgetVar="dlgS2240" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2240"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.evento}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2240" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2240" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2240" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2240" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2240" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2240" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2240" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2240" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2240" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2240" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2240" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2240" value="#{localemsgs.InicioAfastamento}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2240" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2240" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2240" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2240" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2240" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2240" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2240" value="#{esocial.s2240}" 
                                             var="s2240" rowKey="#{s2240.evtExpRisco_Id}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2240.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2240selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2240footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2240footer" />
                                    <p:ajax event="rowUnselect" update="s2240footer" />
                                    <p:ajax event="rowSelect" update="s2240footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2240.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2240.ideVinculo_matricula}" style="#{s2240.sucesso eq 1 ? 'color: orange' : 
                                                               (s2240.sucesso eq 0 ? 'color: red' : 
                                                               (s2240.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoExpRisco dscSetor">
                                        <h:outputText value="#{s2240.infoExpRisco_dscSetor}" style="#{s2240.sucesso eq 1 ? 'color: orange' : 
                                                               (s2240.sucesso eq 0 ? 'color: red' : 
                                                               (s2240.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="infoExpRisco dscAtivDes">
                                        <h:outputText value="#{s2240.infoExpRisco_dscAtivDes}" style="#{s2240.sucesso eq 1 ? 'color: orange' : 
                                                               (s2240.sucesso eq 0 ? 'color: red' : 
                                                               (s2240.sucesso eq 2 ? 'color: green' : 'color: black'))}"
                                                      converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="infoExpRiscodscAgNoc">
                                        <h:outputText value="#{s2240.infoExpRisco_dscAgNoc}" style="#{s2240.sucesso eq 1 ? 'color: orange' : 
                                                               (s2240.sucesso eq 0 ? 'color: red' : 
                                                               (s2240.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2240footer" value="#{esocial.s2240selecionado.size()} de #{esocial.s2240.size()} 
                                                      #{esocial.s2240.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2240selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2240" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2240" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2240" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>                    
                    
                    <h:form class="form-inline" id="formS2250">    
                        <p:dialog widgetVar="dlgS2250" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2250"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.descricao}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2250" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2250" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2250" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2250" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2250" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2250" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2250" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2250" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2250" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2250" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2250" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2250" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2250" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2250" value="#{esocial.s2250}" 
                                             var="s2250" rowKey="#{s2250.ideVinculo_matricula}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2250.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2250selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2250footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2250footer" />
                                    <p:ajax event="rowUnselect" update="s2250footer" />
                                    <p:ajax event="rowSelect" update="s2250footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2250.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2250.ideVinculo_matricula}" style="#{s2250.sucesso eq 1 ? 'color: orange' : 
                                                               (s2250.sucesso eq 0 ? 'color: red' : 
                                                               (s2250.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="iniAfastamento_dtIniAfast">
                                        <h:outputText value="#{s2250.detAvPrevio_dtAvPrevio}" style="#{s2250.sucesso eq 1 ? 'color: orange' : 
                                                               (s2250.sucesso eq 0 ? 'color: red' : 
                                                               (s2250.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>=
                                    <f:facet name="footer">
                                        <h:outputText id="s2250footer" value="#{esocial.s2250selecionado.size()} de #{esocial.s2250.size()} 
                                                      #{esocial.s2250.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2250selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2250" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2250" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2250" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>             
                    <h:form class="form-inline" id="formS2298">    
                        <p:dialog widgetVar="dlgS2298" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2298"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.descricao}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2298" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2298" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2298" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2298" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2298" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2298" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2298" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2298" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2298" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2298" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2298" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2298" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2298" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2298" value="#{esocial.s2298}" 
                                             var="s2298" rowKey="#{s2298.ideVinculo_matricula}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2298.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2298selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2298footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2298footer" />
                                    <p:ajax event="rowUnselect" update="s2298footer" />
                                    <p:ajax event="rowSelect" update="s2298footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2298.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2298.ideVinculo_matricula}" style="#{s2298.sucesso eq 1 ? 'color: orange' : 
                                                               (s2298.sucesso eq 0 ? 'color: red' : 
                                                               (s2298.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoReintegr dtEfetRetorno">
                                        <h:outputText value="#{s2298.infoReintegr_dtEfetRetorno}" style="#{s2298.sucesso eq 1 ? 'color: orange' : 
                                                               (s2298.sucesso eq 0 ? 'color: red' : 
                                                               (s2298.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2298footer" value="#{esocial.s2298selecionado.size()} de #{esocial.s2298.size()} 
                                                      #{esocial.s2298.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2298selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2298" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2298" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2298" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>                       
                    <h:form class="form-inline" id="formS2299">    
                        <p:dialog widgetVar="dlgS2299" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2299"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.descricao}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2299" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2299" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2299" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2299" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2299" value="#{esocial.novoeSocial.filial}"
                                                 disabled="true" style="width: 100%">
                                        <p:watermark for="filialS2299" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2299" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2299" value="#{esocial.novoeSocial.compet}"
                                                 disabled="true" style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2299" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2299" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2299" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2299" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2299" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2299" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2299" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2299" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2299" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2299" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2299" value="#{esocial.s2299}" 
                                             var="s2299" rowKey="#{s2299.evtDeslig_Id}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2299.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2299selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2299footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2299footer" />
                                    <p:ajax event="rowUnselect" update="s2299footer" />
                                    <p:ajax event="rowSelect" update="s2299footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{esocial.getSucesso(s2299.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo matricula">
                                        <h:outputText value="#{s2299.ideVinculo_matricula}" style="#{s2299.sucesso eq 1 ? 'color: orange' : 
                                                               (s2299.sucesso eq 0 ? 'color: red' : 
                                                               (s2299.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideVinculo cpfTrab">
                                        <h:outputText value="#{s2299.ideVinculo_cpfTrab}" style="#{s2299.sucesso eq 1 ? 'color: orange' : 
                                                               (s2299.sucesso eq 0 ? 'color: red' : 
                                                               (s2299.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="trabalhador cpfTrab">
                                        <h:outputText value="#{s2299.infoDeslig_dtDeslig}" style="#{s2299.sucesso eq 1 ? 'color: orange' : 
                                                               (s2299.sucesso eq 0 ? 'color: red' : 
                                                               (s2299.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2299footer" value="#{esocial.s2299selecionado.size()} de #{esocial.s2299.size()} 
                                                      #{esocial.s2299.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2299selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2299" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2299" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2299" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>
                    <h:form class="form-inline" id="formS2300">    
                        <p:dialog widgetVar="dlgS2300" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2300"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.descricao}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2300" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2300" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2300" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2300" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2300" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2300" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2300" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2300" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2300" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2300" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2300" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2300" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2300" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                </p:panelGrid>


                                <p:dataTable id="tabelaS2300" value="#{esocial.s2300}" 
                                             var="s2300" rowKey="#{s2300.trabalhador_cpfTrab}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2300.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2300selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2300footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2300footer" />
                                    <p:ajax event="rowUnselect" update="s2300footer" />
                                    <p:ajax event="rowSelect" update="s2300footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2300.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="trabalhador cpfTrab">
                                        <h:outputText value="#{s2300.trabalhador_cpfTrab}" style="#{s2300.sucesso eq 1 ? 'color: orange' : 
                                                               (s2300.sucesso eq 0 ? 'color: red' : 
                                                               (s2300.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>

                                    <p:column headerText="trabalhador nmTrab">
                                        <h:outputText value="#{s2300.trabalhador_nmTrab}" style="#{s2300.sucesso eq 1 ? 'color: orange' : 
                                                               (s2300.sucesso eq 0 ? 'color: red' : 
                                                               (s2300.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>=
                                    <f:facet name="footer">
                                        <h:outputText id="s2300footer" value="#{esocial.s2300selecionado.size()} de #{esocial.s2300.size()} 
                                                      #{esocial.s2300.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2300selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2300" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2300" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2300" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>                       
                    <h:form class="form-inline" id="formS2306">    
                        <p:dialog widgetVar="dlgS2306" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2306"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{esocial.novoeSocial.evento}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2306" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2306" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2306" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2306" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2306" value="#{esocial.novoeSocial.filial}" style="width: 100%">
                                        <p:watermark for="filialS2306" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2306" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2306" value="#{esocial.novoeSocial.compet}"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2306" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2306" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2306" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2306" value="#{localemsgs.InicioAfastamento}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2306" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2306" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2306" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2306" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2306" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2306" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2306" value="#{esocial.s2306}" 
                                             var="s2306" rowKey="#{s2306.evtTSVAltContr_Id}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2306.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2306selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2306footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2306footer" />
                                    <p:ajax event="rowUnselect" update="s2306footer" />
                                    <p:ajax event="rowSelect" update="s2306footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(s2306.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideVinculo cpfTrab">
                                        <h:outputText value="#{s2306.ideTrabSemVinculo_cpfTrab}" style="#{s2306.sucesso eq 1 ? 'color: orange' : 
                                                               (s2306.sucesso eq 0 ? 'color: red' : 
                                                               (s2306.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2306footer" value="#{esocial.s2306selecionado.size()} de #{esocial.s2306.size()} 
                                                      #{esocial.s2306.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2306selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2306" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2306" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2306" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>                                    
                    <h:form class="form-inline" id="formS2399">    
                        <p:dialog widgetVar="dlgS2399" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS2399"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-2399 - Desligamento" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS2399" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS2399" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS2399" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS2399" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS2399" value="#{esocial.novoeSocial.filial}"
                                                 disabled="true" style="width: 100%">
                                        <p:watermark for="filialS2399" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS2399" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS2399" value="#{esocial.novoeSocial.compet}"
                                                 disabled="true" style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS2399" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS2399" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS2399" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS2399" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS2399" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS2399" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS2399" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS2399" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoS2399" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS2399" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS2399" value="#{esocial.s2399}" 
                                             var="s2399" rowKey="#{s2399.ideTrabSemVinculo_cpfTrab}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s2399.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s2399selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s2399footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s2399footer" />
                                    <p:ajax event="rowUnselect" update="s2399footer" />
                                    <p:ajax event="rowSelect" update="s2399footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{esocial.getSucesso(s2399.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideTrabSemVinculo matricula">
                                        <h:outputText value="#{s2399.ideTrabSemVinculo_matricula}" style="#{s2399.sucesso eq 1 ? 'color: orange' : 
                                                               (s2399.sucesso eq 0 ? 'color: red' : 
                                                               (s2399.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideTrabSemVinculo cpfTrab">
                                        <h:outputText value="#{s2399.ideTrabSemVinculo_cpfTrab}" style="#{s2399.sucesso eq 1 ? 'color: orange' : 
                                                               (s2399.sucesso eq 0 ? 'color: red' : 
                                                               (s2399.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoTSVTermino dtTerm">
                                        <h:outputText value="#{s2399.infoTSVTermino_dtTerm}" style="#{s2399.sucesso eq 1 ? 'color: orange' : 
                                                               (s2399.sucesso eq 0 ? 'color: red' : 
                                                               (s2399.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s2399footer" value="#{esocial.s2399selecionado.size()} de #{esocial.s2399.size()} 
                                                      #{esocial.s2399.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s2399selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS2399" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS2399" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS2399" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>                    
                    <h:form class="form-inline" id="formS3000">    
                        <p:dialog widgetVar="dlgS3000" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgS3000"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="S-3000 - Exclusão de Eventos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelS3000" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoS3000" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoS3000" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialS3000" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialS3000" value="#{esocial.novoeSocial.filial}" 
                                                 disabled="true" style="width: 100%">
                                        <p:watermark for="filialS3000" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeS3000" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeS3000" value="#{esocial.novoeSocial.compet}"
                                                 disabled="true" style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeS3000" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteS3000" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteS3000" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniS3000" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniS3000" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataS3000" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataS3000" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataS3000" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="excluir" value="#{localemsgs.EventoExcluir}" 
                                                   rendered="#{esocial.novoeSocial.evento eq 'S-3000'}"/>
                                    <p:selectOneMenu id="excluir" value="#{esocial.eventoExcluir}" rendered="#{esocial.novoeSocial.evento eq 'S-3000'}"
                                                     required="#{esocial.novoeSocial.evento eq 'S-3000'}"
                                                     styleClass="filial" style="width: 100%" filter="true" filterMatchMode="contains" >
                                        <p:ajax event="itemSelect" update="formS3000:tabelaS3000 msgs" listener="#{esocial.listarS3000}"/>
                                        <f:selectItem itemValue="#{null}" itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{esocial.eventosExcluir}"/>
                                    </p:selectOneMenu>

                                    <p:outputLabel for="tipoS3000" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoS3000" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaS3000" value="#{esocial.s3000}" 
                                             var="s3000" rowKey="#{s3000.ideEvento_identificador}_#{s3000.infoExclusao_nrRecEvt}"
                                             resizableColumns="true" styleClass="tabela" sortBy="#{s3000.sucesso}" expandableRowGroups="true"
                                             selection="#{esocial.s3000selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%" scrollHeight="300"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="s3000footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="s3000footer" />
                                    <p:ajax event="rowUnselect" update="s3000footer" />
                                    <p:ajax event="rowSelect" update="s3000footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="4">
                                            <h:outputText value="#{esocial.getSucesso(s3000.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="infoExclusao tpEvento">
                                        <h:outputText value="#{s3000.infoExclusao_tpEvento}" style="#{s3000.sucesso eq 1 ? 'color: orange' : 
                                                               (s3000.sucesso eq 0 ? 'color: red' : 
                                                               (s3000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoExclusao identificador">
                                        <h:outputText value="#{s3000.ideEvento_identificador}" style="#{s3000.sucesso eq 1 ? 'color: orange' : 
                                                               (s3000.sucesso eq 0 ? 'color: red' : 
                                                               (s3000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                    
                                    <p:column headerText="infoExclusao nrRecEvt">
                                        <h:outputText value="#{s3000.infoExclusao_nrRecEvt}" style="#{s3000.sucesso eq 1 ? 'color: orange' : 
                                                               (s3000.sucesso eq 0 ? 'color: red' : 
                                                               (s3000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="s3000footer" value="#{esocial.s3000selecionado.size()} de #{esocial.s3000.size()} 
                                                      #{esocial.s3000.size() eq 1 ? ' evento' : ' eventos'} #{esocial.s3000selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarS3000" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaS3000" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaS3000" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formR1000">
                        <p:dialog widgetVar="dlgR1000" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgR1000"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="R-1000 - Informações do Empregador/Contribuinte/Órgão Público" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelR1000" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoR1000" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoR1000" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialR1000" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialR1000" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialR1000" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeR1000" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeR1000" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeR1000" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteR1000" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteR1000" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniR1000" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniR1000" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataR1000" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataR1000" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataR1000" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoR1000" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoR1000" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaR1000" value="#{esocial.r1000}" 
                                             var="r1000" rowKey="#{r1000.id}" scrollHeight="300" sortBy="#{r1000.sucesso}"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true"
                                             selection="#{esocial.r1000selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="r1000footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="r1000footer" />
                                    <p:ajax event="rowUnselect" update="r1000footer" />
                                    <p:ajax event="rowSelect" update="r1000footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(r1000.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEmpregador nrInsc">
                                        <h:outputText value="#{r1000.ideEmpregador_nrInsc}" style="#{r1000.sucesso eq 1 ? 'color: orange' : 
                                                               (r1000.sucesso eq 0 ? 'color: red' : 
                                                               (r1000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="contato nmCtt">
                                        <h:outputText value="#{r1000.contato_nmCtt}" style="#{r1000.sucesso eq 1 ? 'color: orange' : 
                                                               (r1000.sucesso eq 0 ? 'color: red' : 
                                                               (r1000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="contato email">
                                        <h:outputText value="#{r1000.contato_email}" style="#{r1000.sucesso eq 1 ? 'color: orange' : 
                                                               (r1000.sucesso eq 0 ? 'color: red' : 
                                                               (r1000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="r1000footer" value="#{esocial.r1000selecionado.size()} de #{esocial.r1000.size()} 
                                                      #{esocial.r1000.size() eq 1 ? ' evento' : ' eventos'} #{esocial.r1000selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarR1000" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaR1000" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaR1000" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                                <!-- <p:blockUI block="formR1000:panelR1000" trigger="formR1000:btnConsultaR1000"/> -->
                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formR1070">
                        <p:dialog widgetVar="dlgR1070" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgR1070"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="R-1070 - Tabela de Processos Administrativos/Judiciais" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelR1070" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoR1070" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoR1070" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialR1070" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialR1070" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialR1070" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeR1070" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeR1070" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeR1070" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteR1070" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteR1070" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniR1070" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniR1070" value="#{esocial.novoeSocial.cadIni}" 
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataR1070" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataR1070" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataR1070" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoR1070" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoR1070" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaR1070" value="#{esocial.r1070}" 
                                             var="r1070" rowKey="#{r1070.id}" scrollHeight="300" sortBy="#{r1070.sucesso}"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true"
                                             selection="#{esocial.r1070selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="r1070footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="r1070footer" />
                                    <p:ajax event="rowUnselect" update="r1070footer" />
                                    <p:ajax event="rowSelect" update="r1070footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(r1070.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEmpregador nrInsc">
                                        <h:outputText value="#{r1070.dadosProcJud_ufVara}" style="#{r1070.sucesso eq 1 ? 'color: orange' : 
                                                               (r1070.sucesso eq 0 ? 'color: red' : 
                                                               (r1070.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="contato nmCtt">
                                        <h:outputText value="#{r1070.ideProcesso_nrProc}" style="#{r1070.sucesso eq 1 ? 'color: orange' : 
                                                               (r1070.sucesso eq 0 ? 'color: red' : 
                                                               (r1070.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="contato email">
                                        <h:outputText value="#{r1070.infoCadastro_nmRazao}" style="#{r1070.sucesso eq 1 ? 'color: orange' : 
                                                               (r1070.sucesso eq 0 ? 'color: red' : 
                                                               (r1070.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="r1070footer" value="#{esocial.r1070selecionado.size()} de #{esocial.r1070.size()} 
                                                      #{esocial.r1070.size() eq 1 ? ' evento' : ' eventos'} #{esocial.r1070selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarR1070" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaR1070" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaR1070" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                                <!-- <p:blockUI block="formR1070:panelR1070" trigger="formR1070:btnConsultaR1070"/> -->
                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formR2020">
                        <p:dialog widgetVar="dlgR2020" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgR2020"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="R-2020 - Retenção Contribuição Previdenciária - Serviços Prestados" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelR2020" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoR2020" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoR2020" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialR2020" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialR2020" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialR2020" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeR2020" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeR2020" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeR2020" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteR2020" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteR2020" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniR2020" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniR2020" value="#{esocial.novoeSocial.cadIni}"
                                                      disabled="true" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataR2020" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataR2020" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataR2020" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoR2020" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoR2020" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaR2020" value="#{esocial.r2020}" 
                                             var="r2020" rowKey="#{r2020.ideTomador_nrInscTomador}" scrollHeight="300" sortBy="#{r2020.sucesso}"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true"
                                             selection="#{esocial.r2020selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="r2020footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="r2020footer" />
                                    <p:ajax event="rowUnselect" update="r2020footer" />
                                    <p:ajax event="rowSelect" update="r2020footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(r2020.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideTomador nrInscTomador">
                                        <h:outputText value="#{r2020.ideTomador_nrInscTomador}" style="#{r2020.sucesso eq 1 ? 'color: orange' : 
                                                               (r2020.sucesso eq 0 ? 'color: red' : 
                                                               (r2020.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideTomador nRedTomador">
                                        <h:outputText value="#{r2020.nred}" style="#{r2020.sucesso eq 1 ? 'color: orange' : 
                                                               (r2020.sucesso eq 0 ? 'color: red' : 
                                                               (r2020.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="ideTomador vlrTotalBruto">
                                        <h:outputText value="#{r2020.ideTomador_vlrTotalBruto}" style="#{r2020.sucesso eq 1 ? 'color: orange' : 
                                                               (r2020.sucesso eq 0 ? 'color: red' : 
                                                               (r2020.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <h:outputText id="r2020footer" value="#{esocial.r2020selecionado.size()} de #{esocial.r2020.size()} 
                                                      #{esocial.r2020.size() eq 1 ? ' evento' : ' eventos'} #{esocial.r2020selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarR2020" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaR2020" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    
                                    <p:commandButton id="btnVerificaR2020" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formR2098">
                        <p:dialog widgetVar="dlgR2098" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgR2098"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="R-2098 - Reabertura dos Eventos Periódicos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelR2098" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoR2098" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoR2098" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialR2098" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialR2098" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialR2098" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeR2098" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeR2098" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeR2098" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteR2098" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteR2098" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniR2098" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniR2098" value="#{esocial.novoeSocial.cadIni}" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataR2098" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataR2098" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataR2098" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoR2098" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoR2098" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaR2098" value="#{esocial.r2098}" 
                                             var="r2098" rowKey="#{r2098.ideContri_nrInsc}" scrollHeight="300" sortBy="#{r2098.sucesso}"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true"
                                             selection="#{esocial.r2098selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="r2098footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="r2098footer" />
                                    <p:ajax event="rowUnselect" update="r2098footer" />
                                    <p:ajax event="rowSelect" update="r2098footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(r2098.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEvento perApur">
                                        <h:outputText value="#{r2098.ideEvento_perApur}" style="#{r2098.sucesso eq 1 ? 'color: orange' : 
                                                               (r2098.sucesso eq 0 ? 'color: red' : 
                                                               (r2098.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                    
                                    <p:column headerText="ideContri nrInsc">
                                        <h:outputText value="#{r2098.ideContri_nrInsc}" style="#{r2098.sucesso eq 1 ? 'color: orange' : 
                                                               (r2098.sucesso eq 0 ? 'color: red' : 
                                                               (r2098.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                    
                                    <f:facet name="footer">
                                        <h:outputText id="r2098footer" value="#{esocial.r2098selecionado.size()} de #{esocial.r2098.size()} 
                                                      #{esocial.r2098.size() eq 1 ? ' evento' : ' eventos'} #{esocial.r2098selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarR2098" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaR2098" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaR2098" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <h:form class="form-inline" id="formR2099">
                        <p:dialog widgetVar="dlgR2099" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgR2099"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="R-2099 - Fechamento dos Eventos Periódicos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelR2099" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoR2099" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoR2099" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialR2099" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialR2099" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialR2099" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeR2099" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeR2099" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeR2099" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteR2099" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteR2099" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniR2099" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniR2099" value="#{esocial.novoeSocial.cadIni}" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataR2099" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataR2099" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataR2099" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoR2099" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoR2099" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaR2099" value="#{esocial.r2099}" 
                                             var="r2099" rowKey="#{r2099.ideContri_nrInsc}" scrollHeight="300" sortBy="#{r2099.sucesso}"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true"
                                             selection="#{esocial.r2099selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="r2099footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="r2099footer" />
                                    <p:ajax event="rowUnselect" update="r2099footer" />
                                    <p:ajax event="rowSelect" update="r2099footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(r2099.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="infoFech evtServTm">
                                        <h:outputText value="#{r2099.infoFech_evtServTm}" style="#{r2099.sucesso eq 1 ? 'color: orange' : 
                                                               (r2099.sucesso eq 0 ? 'color: red' : 
                                                               (r2099.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                    
                                    <p:column headerText="infoFech evtServPr">
                                        <h:outputText value="#{r2099.infoFech_evtServPr}" style="#{r2099.sucesso eq 1 ? 'color: orange' : 
                                                               (r2099.sucesso eq 0 ? 'color: red' : 
                                                               (r2099.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoFech evtAssDespRec">
                                        <h:outputText value="#{r2099.infoFech_evtAssDespRec}" style="#{r2099.sucesso eq 1 ? 'color: orange' : 
                                                               (r2099.sucesso eq 0 ? 'color: red' : 
                                                               (r2099.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>
                                    <p:column headerText="infoFech evtAssDespRep">
                                        <h:outputText value="#{r2099.infoFech_evtAssDespRep}" style="#{r2099.sucesso eq 1 ? 'color: orange' : 
                                                               (r2099.sucesso eq 0 ? 'color: red' : 
                                                               (r2099.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                    
                                    <p:column headerText="infoFech evtComProd">
                                        <h:outputText value="#{r2099.infoFech_evtComProd}" style="#{r2099.sucesso eq 1 ? 'color: orange' : 
                                                               (r2099.sucesso eq 0 ? 'color: red' : 
                                                               (r2099.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                                                 
                                    <p:column headerText="infoFech evtCPRB">
                                        <h:outputText value="#{r2099.infoFech_evtCPRB}" style="#{r2099.sucesso eq 1 ? 'color: orange' : 
                                                               (r2099.sucesso eq 0 ? 'color: red' : 
                                                               (r2099.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                                                 
                                    <p:column headerText="infoFech evtPgtos">
                                        <h:outputText value="#{r2099.infoFech_evtPgtos}" style="#{r2099.sucesso eq 1 ? 'color: orange' : 
                                                               (r2099.sucesso eq 0 ? 'color: red' : 
                                                               (r2099.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                                                                                     

                                    <f:facet name="footer">
                                        <h:outputText id="r2099footer" value="#{esocial.r2099selecionado.size()} de #{esocial.r2099.size()} 
                                                      #{esocial.r2099.size() eq 1 ? ' evento' : ' eventos'} #{esocial.r2099selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarR2099" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnConsultaR2099" action="#{esocial.preparacaoConsulta}" update="msgs" 
                                                     value="#{localemsgs.ConsultaPendentes}" title="#{localemsgs.ConsultaPendentes}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaR2099" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>


                    <h:form class="form-inline" id="formR9000">
                        <p:dialog widgetVar="dlgR9000" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgR9000"
                                  style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_esocialG.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="R-9000 - Reabertura dos Eventos Periódicos" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelR9000" style="background-color: transparent" styleClass="cadastrar">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="eventoR9000" value="#{localemsgs.Evento}" indicateRequired="false"/>
                                    <p:autoComplete id="eventoR9000" styleClass="pstserv" value="#{esocial.novoeSocial}" completeMethod="#{esocial.getEventos}"
                                                    forceSelection="true" style="width: 100%" disabled="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Evento}" scrollHeight="200"
                                                    var="ev" itemValue="#{ev}" itemLabel="#{ev.descricao}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{esocial.listaEventos}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="filialR9000" value="#{localemsgs.Filial}"/>
                                    <p:inputText id="filialR9000" value="#{esocial.novoeSocial.filial}" disabled="true" style="width: 100%">
                                        <p:watermark for="filialR9000" value="#{localemsgs.Filial}" />
                                    </p:inputText>

                                    <p:outputLabel for="validadeR9000" value="#{localemsgs.InicioValidade}"/>
                                    <p:inputMask id="validadeR9000" value="#{esocial.novoeSocial.compet}" disabled="true"
                                                 style="width: 100%" mask="9999-99" placeholder="AAAA-MM">
                                        <p:watermark for="validadeR9000" value="AAAA-MM" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-2,ui-grid-col-3" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="ambienteR9000" value="#{localemsgs.Ambiente}" indicateRequired="false"/>
                                    <p:selectOneRadio id="ambienteR9000" required="true" value="#{esocial.novoeSocial.ambiente}" disabled="true">
                                        <f:selectItem itemLabel="#{localemsgs.Homologacao}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.Producao}" itemValue="1"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="cadIniR9000" value="#{localemsgs.CadastroInicial}" rendered="#{esocial.novoeSocial.campoCadIni}"/>
                                    <p:selectOneRadio id="cadIniR9000" value="#{esocial.novoeSocial.cadIni}" rendered="#{esocial.novoeSocial.campoCadIni}">
                                        <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N"/>
                                    </p:selectOneRadio>

                                    <p:outputLabel for="dataR9000" value="#{localemsgs.Data}" indicateRequired="#{esocial.novoeSocial.campoData}"
                                                   rendered="#{esocial.novoeSocial.campoData}"/>
                                    <p:inputMask id="dataR9000" value="#{esocial.data}" rendered="#{esocial.novoeSocial.campoData}"
                                                 disabled="true" required="#{esocial.novoeSocial.campoData}"
                                                 style="width: 100%" mask="99/99/9999" placeholder="DD/MM/AAAA">
                                        <p:watermark for="dataR9000" value="DD/MM/AAAA" />
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">

                                    <p:outputLabel for="tipoR9000" value="#{localemsgs.Tipo}" indicateRequired="#{esocial.novoeSocial.campoTipo}"
                                                   rendered="#{esocial.novoeSocial.campoTipo}"/>
                                    <p:selectOneRadio id="tipoR9000" value="#{esocial.novoeSocial.tipo}" rendered="#{esocial.novoeSocial.campoTipo}"
                                                      disabled="true" required="#{esocial.novoeSocial.campoTipo}">
                                        <f:selectItem itemLabel="Inclusão" itemValue="INCLUSAO"/>
                                        <f:selectItem itemLabel="Alteração" itemValue="ALTERACAO"/>
                                    </p:selectOneRadio>
                                </p:panelGrid>

                                <p:dataTable id="tabelaR9000" value="#{esocial.r9000}" 
                                             var="r9000" rowKey="#{r9000.ideContri_nrInsc}" scrollHeight="300" sortBy="#{r9000.sucesso}"
                                             resizableColumns="true" styleClass="tabela" expandableRowGroups="true"
                                             selection="#{esocial.r9000selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">
                                    <p:ajax event="rowSelectCheckbox" update="r9000footer" />
                                    <p:ajax event="rowUnselectCheckbox" update="r9000footer" />
                                    <p:ajax event="rowUnselect" update="r9000footer" />
                                    <p:ajax event="rowSelect" update="r9000footer"/>
                                    <p:column selectionMode="multiple" style="width:30px;text-align:center;"/>
                                    <p:headerRow>
                                        <p:column colspan="5">
                                            <h:outputText value="#{esocial.getSucesso(r9000.sucesso)}" />
                                        </p:column>
                                    </p:headerRow>
                                    <p:column headerText="ideEvento perApur">
                                        <h:outputText value="#{r9000.ideEvento_perApur}" style="#{r9000.sucesso eq 1 ? 'color: orange' : 
                                                               (r9000.sucesso eq 0 ? 'color: red' : 
                                                               (r9000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                    
                                    <p:column headerText="ideContri nrInsc">
                                        <h:outputText value="#{r9000.ideContri_nrInsc}" style="#{r9000.sucesso eq 1 ? 'color: orange' : 
                                                               (r9000.sucesso eq 0 ? 'color: red' : 
                                                               (r9000.sucesso eq 2 ? 'color: green' : 'color: black'))}"/>
                                    </p:column>                                    
                                    <f:facet name="footer">
                                        <h:outputText id="r9000footer" value="#{esocial.r9000selecionado.size()} de #{esocial.r9000.size()} 
                                                      #{esocial.r9000.size() eq 1 ? ' evento' : ' eventos'} #{esocial.r9000selecionado.size() eq 1 ? ' selecionado' : ' selecionados'}"/>
                                    </f:facet>
                                </p:dataTable>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="text-align: center">
                                    <p:commandButton id="btnEnviarR9000" action="#{esocial.preparacaoEnvio}" update="msgs" 
                                                     value="#{localemsgs.Enviar}" title="#{localemsgs.Enviar}">
                                    </p:commandButton>
                                    <p:commandButton id="btnVerificaR9000" action="#{esocial.verificarProcessamento}" update="msgs" 
                                                     value="#{localemsgs.VerificarProcessamento}" title="#{localemsgs.VerificarProcessamento}">
                                    </p:commandButton>
                                </p:panelGrid>

                            </p:panel>
                        </p:dialog>
                    </h:form>


                    <!--Pesquisar-->
                    <h:form id="formPesquisar">
                        <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                                   draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                   showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                                   style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_filiais.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.PesquisarFilial}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="pesquisar" style="background-color: transparent"> 
                                <div class="ui-grid-row" style="padding-bottom: 3px;">
                                    <div style="width: 25%; float: left">
                                        <p:outputLabel for="codfil" value="#{localemsgs.Filial}: "/>
                                    </div>
                                    <div style="width: 75%; float: left">
                                        <p:inputText value="#{filiaisMB.novaFilial.codFil}" id="codfil"
                                                     style="width: 100%">
                                            <p:watermark for="codfil" value="#{localemsgs.Filial}"/>
                                        </p:inputText>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 3px;">
                                    <div style="width: 25%; float: left">
                                        <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "/>
                                    </div>
                                    <div style="width: 75%; float: left">
                                        <p:inputText value="#{filiaisMB.novaFilial.descricao}" id="descricao" 
                                                     style="width: 100%">
                                            <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                        </p:inputText>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 3px;">
                                    <div style="width: 25%; float: left">
                                        <p:outputLabel for="rS" value="#{localemsgs.RazaoSocial}:"/>
                                    </div>
                                    <div style="width: 75%; float: left">
                                        <p:inputText value="#{filiaisMB.novaFilial.razaoSocial}" id="rS"
                                                     style="width: 100%">
                                            <p:watermark for="rS" value="#{localemsgs.RazaoSocial}"/>
                                        </p:inputText>
                                    </div>
                                </div>

                                <div class="form-inline">
                                    <p:commandLink action="#{filiaisMB.PesquisaPaginada}" update="msgs main:tabela cabecalho" 
                                                   title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisar').hide()">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" width="400"
                              style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_filiais.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" /> 
                        </f:facet>
                        <h:form class="form-inline">
                            <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                            <p:separator />
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 5%; float: left;">
                                    <p:selectBooleanCheckbox id="filial" value="#{filiaisMB.eFilial}">
                                        <p:ajax update="labelFilial"/>
                                    </p:selectBooleanCheckbox>
                                </div>
                                <div style="width: 20%; float: left;">
                                    <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{filiaisMB.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>
                                <div style="width: 5%; float: left;">
                                    <p:selectBooleanCheckbox id="desc" value="#{filiaisMB.eDesc}">
                                        <p:ajax update="labelDesc"/>
                                    </p:selectBooleanCheckbox>
                                </div>
                                <div style="width: 20%; float: left;">
                                    <p:outputLabel id="labelDesc" value="#{localemsgs.Descricao}" style="#{filiaisMB.eDesc eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>
                                <div style="width: 5%; float: left;">
                                    <p:selectBooleanCheckbox id="razao" value="#{filiaisMB.eRazao}">
                                        <p:ajax update="labelRazao"/>
                                    </p:selectBooleanCheckbox>
                                </div>
                                <div style="width: 20%; float: left;">
                                    <p:outputLabel id="labelRazao" value="#{localemsgs.RazaoSocial}" style="#{filiaisMB.eRazao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>
                                <div style="width: 5%; float: left;">
                                    <p:selectBooleanCheckbox id="operador" value="#{filiaisMB.eOperador}">
                                        <p:ajax update="labelOperador"/>
                                    </p:selectBooleanCheckbox>
                                </div>
                                <div style="width: 20%; float: left;">
                                    <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{filiaisMB.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>
                            </div>
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 5%; float: left;">
                                    <p:selectBooleanCheckbox id="dtalter" value="#{filiaisMB.eDtAlter}">
                                        <p:ajax update="labelDtAlter"/>
                                    </p:selectBooleanCheckbox>
                                </div>
                                <div style="width: 20%; float: left;">
                                    <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{filiaisMB.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>
                                <div style="width: 5%; float: left;">
                                    <p:selectBooleanCheckbox id="hralter" value="#{filiaisMB.eHrAlter}">
                                        <p:ajax update="labelHrAlter"/>
                                    </p:selectBooleanCheckbox>
                                </div>
                                <div style="width: 20%; float: left;">
                                    <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{filiaisMB.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>
                            </div>
                            <p:separator />
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:panel style="text-align: center">
                                    <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                                    <h:commandLink id="pdf" actionListener="#{filiaisMB.AtualizaTabela}">
                                        <p:graphicImage url="../assets/img/icone_pdf.png"/>
                                        <p:dataExporter target="main:tabela" type="pdf" fileName="#{localemsgs.Filiais}" 
                                                        preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                                    </h:commandLink>
                                </p:panel>

                                <p:panel style="text-align: center">
                                    <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                                    <h:commandLink id="xlsx" actionListener="#{filiaisMB.AtualizaTabela}">
                                        <p:graphicImage url="../assets/img/icone_xls.png"/>
                                        <p:dataExporter target="main:tabela" type="xlsx" fileName="#{localemsgs.Filiais}"/> 
                                    </h:commandLink>
                                </p:panel>
                            </p:panelGrid>
                        </h:form>
                    </p:dialog>
                </div>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>
                <div class="footer-body" id="footer-body">
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <footer>
        <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
            <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                <h:form id="corporativo">
                    <div>
                        <label ref="lblCheck">
                            <h:outputText value="#{localemsgs.SomentePendentes}: "/>
                        </label>
                        <p:selectBooleanCheckbox value="#{tesEntrada.apenasPendentes}">
                            <p:ajax
                                update="msgs main:tabela"
                                listener="#{tesEntrada.mostrarApenasPendentes()}"/>
                        </p:selectBooleanCheckbox>
                    </div>
                </h:form>
            </div>
            <div class="container" style="min-height:10px !important;max-height:40px !important;">
                <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                    <table class="footer-time" style="min-height:10px !important">
                        <tr>
                            <td>
                                <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                    <table class="footer-user" style="min-height:10px !important">
                        <tr>
                            <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                            <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                    <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                        <tr>
                            <td><img src="../assets/img/logo_satweb.png" /></td>
                            <td>
                                <h:form>
                                    <h:commandLink actionListener="#{localeController.increment}"
                                                   action="#{localeController.getLocales}" >
                                        <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                    </h:commandLink>
                                </h:form>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </footer>

</ui:composition>
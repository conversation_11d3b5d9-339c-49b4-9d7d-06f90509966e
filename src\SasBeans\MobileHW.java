/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class MobileHW {

    private String IMEI;
    private String Parametro;
    private String CodEquip;
    private String TipoEquip;
    private String MarcaEquip;
    private String SerialEquip;
    private String CodCli;
    private String CodFil;
    private String Saldo;
    private String Bateria;
    private String Versao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_alter;

    private String Cliente;

    private String Status;

    public String getStatus() {
        return Status;
    }

    public void setStatus(String Status) {
        this.Status = Status;
    }

    public String getIMEI() {
        return IMEI;
    }

    public void setIMEI(String IMEI) {
        this.IMEI = IMEI;
    }

    public String getParametro() {
        return Parametro;
    }

    public void setParametro(String Parametro) {
        this.Parametro = Parametro;
    }

    public String getCodEquip() {
        return CodEquip;
    }

    public void setCodEquip(String CodEquip) {
        this.CodEquip = CodEquip;
    }

    public String getTipoEquip() {
        return TipoEquip;
    }

    public void setTipoEquip(String TipoEquip) {
        this.TipoEquip = TipoEquip;
    }

    public String getMarcaEquip() {
        return MarcaEquip;
    }

    public void setMarcaEquip(String MarcaEquip) {
        this.MarcaEquip = MarcaEquip;
    }

    public String getSerialEquip() {
        return SerialEquip;
    }

    public void setSerialEquip(String SerialEquip) {
        this.SerialEquip = SerialEquip;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getSaldo() {
        return Saldo;
    }

    public void setSaldo(String Saldo) {
        this.Saldo = Saldo;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getCliente() {
        return Cliente;
    }

    public void setCliente(String Cliente) {
        this.Cliente = Cliente;
    }

    public String getBateria() {
        return Bateria;
    }

    public void setBateria(String Bateria) {
        this.Bateria = Bateria;
    }

    public String getVersao() {
        return Versao;
    }

    public void setVersao(String Versao) {
        this.Versao = Versao;
    }
}

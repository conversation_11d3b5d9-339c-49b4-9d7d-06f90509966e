<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:c="http://java.sun.com/jsp/jstl/core"
    >

    <h:form id="main" style="color: black;">
        <div class="ui-grid ui-grid-responsive FundoPagina2"
             style="overflow:hidden !important; padding-right:12px !important; display: flex;flex-direction: column;">
            <div class="row">
                <div class="col-md-2">
                    <p:outputLabel for="rota"
                                   value="#{localemsgs.Rota}: "/>
                </div>
                <div class="col-md-4">
                    <p:selectOneMenu
                        id="rota"
                        value="#{cxForteEntrada.rotaSelecionada}"
                        converter="omnifaces.SelectItemsConverter"
                        required="true"
                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                        styleClass="filial"
                        style="width: 100%"
                        filter="true"
                        filterMatchMode="contains">
                        <f:selectItem itemValue="#{null}"
                                      itemLabel="#{localemsgs.Selecionar}"
                                      noSelectionOption="true"/>
                        <f:selectItems value="#{cxForteEntrada.rotas}"
                                       var="item"
                                       itemValue="#{item}"
                                       itemLabel="#{item.rota}"
                                       noSelectionValue=""/>
                        <p:ajax event="itemSelect"
                                update="msgs @form:tabela guiaCadastro:gtv"
                                process="@this"
                                partialSubmit="true"
                                listener="#{cxForteEntrada.onRotaUpdate()}"/>
                    </p:selectOneMenu>
                </div>
                <div class="col-md-6">
                    <p:selectBooleanCheckbox
                        value="#{cxForteEntrada.somenteDestinoCxForte}"
                        itemLabel="#{localemsgs.SomenteDestinoCxForte}"
                        >
                        <p:ajax listener="#{cxForteEntrada.onSomenteDestinoCxForteUpdate()}"
                                update="msgs @form:tabela" />
                    </p:selectBooleanCheckbox>
                </div>
            </div>

            <div style="overflow:hidden !important; position:relative; width: 100%; flex-grow: 1; flex-shrink: 1;">
                <p:dataTable
                    id="tabela"
                    value="#{cxForteEntrada.entradas}"
                    selection="#{cxForteEntrada.entradaSelecionada}"
                    rowKey="#{item.sequencia};#{item.parada};#{item.codCli1}"
                    rowStyleClass="#{item.flag_Excl eq '*' ? 'entradaExcluida' : (empty item.valor ? null : 'entradaValor')} #{item.ER eq 'E' ? 'entradaEspecial' : null}"
                    currentPageReportTemplate="#{localemsgs.Mostrando} {totalRecords} #{localemsgs.Entradas}"
                    var="item"
                    selectionMode="single"
                    emptyMessage="#{localemsgs.SemRegistros}"
                    reflow="true"
                    styleClass="tabelaReformulada"
                    class="grelha tabelaReformulada"
                    scrollable="true"
                    scrollWidth="100%"
                    >
                    <f:facet name="header">
                        <c:choose>
                            <c:when test="#{cxForteEntrada.entradas eq null}">
                                #{localemsgs.SelecioneRota}
                            </c:when>
                        </c:choose>
                    </f:facet>
                    <p:ajax
                        event="rowSelect"
                        listener="#{cxForteEntrada.selecionarEntrada()}"
                        update="msgs guiaForm:origem guiaForm:tabela"/>
                    <p:column headerText="#{localemsgs.Parada}" sortBy="#{item.parada}" sortFunction="#{cxForteEntrada.sortNumericString}">
                        <h:outputText value="#{item.parada}"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.Hora1}" sortBy="#{item.hora1}" sortFunction="#{cxForteEntrada.sortNumericString}">
                        <h:outputText value="#{item.hora1}"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.ER}">
                        <h:outputText value="#{item.ER}"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.TipoSrv}">
                        <h:outputText value="#{item.tipoSrv}"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.NRed}">
                        <h:outputText value="#{item.NRed}"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.Valor}">
                        <h:outputText value="#{item.valor}" converter="conversormoeda"/>
                    </p:column>
                    <p:column headerText="#{localemsgs.Obs}">
                        <h:outputText value="#{item.observ}"/>
                    </p:column>
                </p:dataTable>
            </div>
        </div>
    </h:form>

</ui:composition>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class iblCTeOS_infGTVnotaFiscal {
    public String Nnf;
    public String serieNF;
    public String chaveNF;
    public String dataEmissao;
    public String pesoBruto;
    public String pesoLiquido;
    public String volumes;
    public String vlrMerc;
    public iblCTeOS_Cliente rem;
    public iblCTeOS_Cliente dest;

    public String getNnf() {
        return Nnf;
    }

    public void setNnf(String Nnf) {
        this.Nnf = Nnf;
    }

    public String getSerieNF() {
        return serieNF;
    }

    public void setSerieNF(String SerieNF) {
        this.serieNF = SerieNF;
    }

    public String getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(String dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getPesoBruto() {
        return pesoBruto;
    }

    public void setPesoBruto(String pesoBruto) {
        this.pesoBruto = pesoBruto;
    }

    public String getPesoLiquido() {
        return pesoLiquido;
    }

    public void setPesoLiquido(String pesoLiquido) {
        this.pesoLiquido = pesoLiquido;
    }

    public String getVolumes() {
        return volumes;
    }

    public void setVolumes(String volumes) {
        this.volumes = volumes;
    }

    public String getVlrMerc() {
        return vlrMerc;
    }

    public void setVlrMerc(String vlrMerc) {
        this.vlrMerc = vlrMerc;
    }

    public iblCTeOS_Cliente getRem() {
        return rem;
    }

    public void setRem(iblCTeOS_Cliente rem) {
        this.rem = rem;
    }

    public iblCTeOS_Cliente getDest() {
        return dest;
    }

    public void setDest(iblCTeOS_Cliente dest) {
        this.dest = dest;
    }

    public String getChaveNF() {
        return chaveNF;
    }

    public void setChaveNF(String chaveNF) {
        this.chaveNF = chaveNF;
    }
}

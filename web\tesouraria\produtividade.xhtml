<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .calendario input {
                    width: 190px !important;
                }
                
                [class*="ui-datepicker"]{
                    left: auto !important;
                    right: 0px !important;
                    z-index: 2 !important;
                }
                
                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid thead tr th,
                    .DataGrid tbody tr td {
                        text-align: center !important;
                    }
                    
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(1),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(1),
                    .DataGrid.gridCorporativo thead tr th:nth-child(1),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(1),
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(2),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(2),
                    .DataGrid.gridCorporativo thead tr th:nth-child(3),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(3),
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(6),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(6),
                    .DataGrid.gridCorporativo thead tr th:nth-child(7),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(7){
                        min-width: 100px !important;
                        width: 100px !important;
                        max-width: 100px !important;
                    }
                    
                    
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(3),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(3),
                    .DataGrid.gridCorporativo thead tr th:nth-child(4),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(4){
                        min-width: 60px !important;
                        width: 60px !important;
                        max-width: 60px !important;
                    }
                    
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(8),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(8),
                    .DataGrid.gridCorporativo thead tr th:nth-child(9),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(9),
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(9),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(9),
                    .DataGrid.gridCorporativo thead tr th:nth-child(10),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(10),
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(10),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(10),
                    .DataGrid.gridCorporativo thead tr th:nth-child(11),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(11),
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(11),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(11),
                    .DataGrid.gridCorporativo thead tr th:nth-child(12),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(12),
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(12),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(12),
                    .DataGrid.gridCorporativo thead tr th:nth-child(13),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(13),
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(13),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(13),
                    .DataGrid.gridCorporativo thead tr th:nth-child(14),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(14){
                        min-width: 110px !important;
                        width: 110px !important;
                        max-width: 110px !important;
                    }
                    
                    .DataGrid:not(.gridCorporativo) thead tr th:nth-child(5),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(5),
                    .DataGrid.gridCorporativo thead tr th:nth-child(6),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(6){
                        min-width: 160px !important;
                        width: 160px !important;
                        max-width: 160px !important;
                    }
                }
                
                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    .calendario input {
                        width: 175px !important;
                        margin-right: 3px !important;
                    }
                    
                    #divCalendario{
                        right: 13px !important;
                    }
                    
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(2),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(3){
                        font-weight: 500 !important;
                    }
                    
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(4),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(7),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(5),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(8){
                        font-weight: bold
                    }
                    
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(2),
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(3),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(3),
                    .DataGrid.gridCorporativo tbody tr td:nth-child(4){
                        color: orangered;
                    }
                    
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(2) span,
                    .DataGrid:not(.gridCorporativo) tbody tr td:nth-child(3) span,
                    .DataGrid.gridCorporativo tbody tr td:nth-child(3) span,
                    .DataGrid.gridCorporativo tbody tr td:nth-child(4) span{
                        color: #000;
                    }
                    
                    .ui-state-highlight td,
                    .ui-state-highlight td span{
                        color: #FFF !important;
                    }
                }
                
            </style>
        </h:head>                      
        <h:body id="h" style="overflow:hidden !important; max-height:100% !important">
            <f:metadata>
                <f:viewAction action="#{tesEntrada.carregarProdutividade}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_area_produtividade.png" height="40" style="margin-top:-6px !important;" />
                                    <label class="TituloPagina">#{localemsgs.Produtividade}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span><h:outputText id="dataDia" value="#{tesEntrada.data1}" converter="conversorData"/> a <h:outputText id="dataDia2" value="#{tesEntrada.data2}" converter="conversorData"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{tesEntrada.filialTela.descricao}<label id="btTrocarFilial">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{tesEntrada.filialTela.endereco}</label>
                                    <label class="FilialBairroCidade">#{tesEntrada.filialTela.bairro}, #{tesEntrada.filialTela.cidade}/#{tesEntrada.filialTela.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                                  value="#{tesEntrada.datasSelecionadas}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{tesEntrada.selecionarDatasProdutividade}" update="msgs main dataDia dataDia2" />
                                    </p:datePicker>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main" style="max-height: calc(100vh - 90px) !important">
                    <div class="ui-grid ui-grid-responsive FundoPagina"
                         style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela"
                                                 value="#{tesEntrada.listaProdutividade}"
                                                 var="produtividade"
                                                 selectionMode="single"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Registros}"
                                                 paginator="true"
                                                 rows="50"
                                                 rowsPerPageTemplate="15, 25, 50, 100,"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 scrollable="true"
                                                 class="tabela DataGrid #{tesEntrada.corporativo?'gridCorporativo':''}"
                                                 reflow="true"
                                                 rowKey="#{produtividade.guia}_#{produtividade.serie}"
                                                 style="font-size: 12px; background: #FFF"
                                                 >
                                        <p:column headerText="#{localemsgs.Data}">
                                            <h:outputText value="#{produtividade.data}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Filial}" rendered="#{tesEntrada.corporativo}">
                                            <h:outputText value="#{produtividade.filialDescricao}" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Guia}">
                                            <h:outputText value="#{produtividade.guia}" converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Serie}">
                                            <h:outputText value="#{produtividade.serie}" converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NRedCli}">
                                            <h:outputText value="#{produtividade.NRed1}" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Valor}">
                                            <h:outputText value="#{produtividade.valor}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.MatrConf}">
                                            <h:outputText value="#{produtividade.matrConf}" converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Conferente}">
                                            <h:outputText value="#{produtividade.nomeConferente}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HoraInicio}">
                                            <h:outputText value="#{produtividade.hrInicio}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HoraFim}">
                                            <h:outputText value="#{produtividade.hrFinal}" converter="conversorHora"/>
                                        </p:column>


                                        <p:column headerText="#{localemsgs.Tempo}">
                                            <h:outputText value="#{produtividade.tempo}" converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.QtdeDH}">
                                            <h:outputText value="#{produtividade.qtdeCed}" converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.MilheirosHr}">
                                            <h:outputText value="#{produtividade.qtdeVol}" converter="conversormoedasemsimbolo" />
                                        </p:column>
                                    </p:dataTable>

                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>
                </h:form>
            </div>
            <ui:insert name="loading" id="loadJava">
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>                                         
            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; top: 6px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: "  /></label>
                                <p:selectBooleanCheckbox value="#{tesEntrada.corporativo}" id="chkCorporativo">
                                    <p:ajax update="msgs main:tabela" listener="#{tesEntrada.carregarProdutividade}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>  
    </f:view>
</html>
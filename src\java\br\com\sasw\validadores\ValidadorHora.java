/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.validadores;

import br.com.sasw.utils.Messages;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.Validator;
import javax.faces.validator.ValidatorException;

/**
 *
 * <AUTHOR>
 */
@FacesValidator("ValidadorHora")
public class ValidadorHora implements Validator {

    @Override
    public void validate(FacesContext fc, UIComponent uic, Object object) throws ValidatorException {

        if (object != null) {
            String hora = object.toString();
            try {
                LocalTime.parse(hora, DateTimeFormatter.ofPattern("HH:mm"));
            } catch (Exception e) {
                throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("HoraInvalida"), null));
            }
        }
    }
}

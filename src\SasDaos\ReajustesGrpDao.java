package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ReajustesGrp;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ReajustesGrpDao {

    public List<ReajustesGrp> obterListaReajustesGrp(Persistencia persistencia) throws Exception {
        try {
            List<ReajustesGrp> retorno = new ArrayList<>();
            String sql = " select * from ReajustesGrp order by descricao ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            ReajustesGrp reajustesGrp;
            while (consulta.Proximo()) {
                reajustesGrp = new ReajustesGrp();
                reajustesGrp.setCodigo(consulta.getString("codigo").replace(".0", ""));
                reajustesGrp.setDescricao(consulta.getString("descricao"));
                reajustesGrp.setDt_alter(consulta.getString("dt_alter"));
                reajustesGrp.setHr_alter(consulta.getString("hr_alter"));
                reajustesGrp.setOperador(consulta.getString("operador"));
                retorno.add(reajustesGrp);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ReajustesGrpDAO.obterListaReajustesGrp - " + e.getMessage() + "\r\n"
                    + " select * from ReajustesGrp order by descricao ");
        }
    }

    /**
     * Obter codigo do grupo de reajustes
     *
     * @param descricao descrição do grupo
     * @param persistencia conexão com o banco de dados
     * @return código do grupo de reajustes
     * @throws Exception
     */
    public BigDecimal obterCodigoGrupoReajustes(String descricao, Persistencia persistencia) throws Exception {
        BigDecimal grupoReajustes = new BigDecimal("0");
        try {
            String sql = "SELECT Codigo FROM ReajustesGrp WHERE Descricao = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(descricao);
            consulta.select();

            while (consulta.Proximo()) {
                grupoReajustes = consulta.getBigDecimal("Codigo");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return grupoReajustes;
    }

    /**
     * Obtem o novo código que será salvo
     *
     * @param descricao A nova descrição
     * @param persistencia Conexão com o banco de dados
     * @return novo codigo
     * @throws Exception
     */
    public BigDecimal obterNovoCodigo(String descricao, Persistencia persistencia) throws Exception {
        BigDecimal novoCodigo = new BigDecimal("0");
        try {
            String sql = "SELECT MAX(Codigo) + 1 novo FROM ReajustesGrp";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                novoCodigo = consulta.getBigDecimal("novo");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return novoCodigo;
    }

    /**
     * Inserir o grupo de reajustes
     *
     * @param grupoReajustes Objeto contendo o registros
     * @param persistencia conexão com o banco de dados
     * @throws Exception
     */
    public void inserirGrupoReajustes(ReajustesGrp grupoReajustes, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO ReajustesGrp (codigo, descricao, operador,"
                    + " hr_alter, dt_alter) VALUES (?,?,?,?,CONVERT(DATE, getDate()))";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(grupoReajustes.getCodigo());
            consulta.setString(grupoReajustes.getDescricao());
            consulta.setString(grupoReajustes.getOperador());
            consulta.setString(grupoReajustes.getHr_alter());
            consulta.insert();
            consulta.close();
        } catch (Exception ex) {
            throw new Exception("ocorreu um erro: " + ex.getMessage());
        }
    }

    public void inserirGrupoReajustes(ReajustesGrp grupoReajustes, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO ReajustesGrp (codigo, descricao, operador,"
                    + " hr_alter, dt_alter) VALUES (?,?,?,?,?))";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(grupoReajustes.getCodigo());
            consulta.setString(grupoReajustes.getDescricao());
            consulta.setString(grupoReajustes.getOperador());
            consulta.setString(grupoReajustes.getHr_alter());
            consulta.setString(dataAtual);
            consulta.insert();
            consulta.close();
        } catch (Exception ex) {
            throw new Exception("ocorreu um erro: " + ex.getMessage());
        }
    }
}

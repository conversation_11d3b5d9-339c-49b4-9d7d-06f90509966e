package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Manifest;
import SasBeans.Rotas;
import SasBeans.Rt_Guias;
import SasBeansCompostas.Rt_GuiasRotas;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rt_GuiasDao {

    public List<Rt_Guias> listaGuiasWeb(BigDecimal sequencia, int parada, Persistencia persistencia) throws Exception {
        List<Rt_Guias> guias = new ArrayList<>();
        try {
            String sql = " Select Rt_Guias.Guia, Rt_Guias.Serie, Rt_Guias.Valor, Rt_Guias.OS, Rt_guias.Operador, "
                    + " Rt_Guias.Dt_alter, Rt_Guias.Hr_Alter "
                    + " from Rt_Guias "
                    + " where Rt_Guias.Sequencia = ? "
                    + "   and Rt_Guias.Parada = ? "
                    + " order by Rt_Guias.Guia";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.setInt(parada);
            consulta.select();
            Rt_Guias guia;
            while (consulta.Proximo()) {
                guia = new Rt_Guias();
                guia.setGuia(consulta.getString("guia"));
                guia.setSerie(consulta.getString("serie"));
                guia.setValor(consulta.getString("valor"));
                guia.setOS(consulta.getString("OS"));
                guia.setOperador(consulta.getString("operador"));
                guia.setDt_Alter(consulta.getLocalDate("dt_alter"));
                guia.setHr_Alter(consulta.getString("hr_alter"));
                guias.add(guia);
            }
            consulta.Close();
            return guias;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.listaGuiasWeb - " + e.getMessage() + "\r\n"
                    + "Select Rt_Guias.Guia, Rt_Guias.Serie, Rt_Guias.Valor, Rt_Guias.OS, Rt_guias.Operador, "
                    + " Rt_Guias.Dt_alter, Rt_Guias.Hr_Alter "
                    + " from Rt_Guias "
                    + " where Rt_Guias.Sequencia = " + sequencia
                    + "   and Rt_Guias.Parada = " + parada
                    + " order by Rt_Guias.Guia");
        }
    }

    /**
     * Lista de guias do banco de dados
     *
     * @param sequencia sequencia da rota
     * @param parada Numero da parada
     * @param persistencia conexao com o banco de dados
     * @return lista de guias do banco de dados
     * @throws Exception
     */
    public List<Rt_Guias> listaGuias(String sequencia, String parada, Persistencia persistencia) throws Exception {
        List<Rt_Guias> guias = new ArrayList<>();
        try {
            /*String sql = "SELECT * FROM Rt_Guias WHERE Sequencia = ? AND parada = ?";
            String sql = "SELECT destino.parada paradaDestino, Rt_Guias.*, CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri,"
                    + " CliOri.Ende EndOri, CliOri.Cidade CidadeOri, CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, "
                    + " CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + " CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + " CliFat.CGC CGCFat,CliFat.IE IEFat, CliDst.email EmailDst, CliOri.email EmailOri,"
                    + " CliOri.Codigo CodCliOri, Rt_Perc.CodCli1 CliOriRota, Rt_Perc.ER "
                    + " from Rt_Guias "
                    + " Left join Rt_Perc  on Rt_Perc.Sequencia = Rt_Guias.Sequencia "
                    + "                       and Rt_Perc.Parada    = Rt_Guias.Parada  "
                    + " left join rt_perc as destino on destino.sequencia = rt_perc.sequencia "
                    + "                       and destino.hora1    = rt_perc.hora1d "
                    + " left JOIN OS_Vig  ON OS_Vig.CodFil = Rt_Guias.CodFil "
                    + "                      AND OS_Vig.OS = Rt_Guias.OS "
                    + " left JOIN Clientes CliOri  ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                               AND OS_Vig.CodFil = CliOri.CodFil "
                    + " left JOIN Clientes CliDst  ON OS_Vig.CliDst = CliDst.Codigo "
                    + "                               AND OS_Vig.CodFil = CliDst.CodFil "
                    + " left JOIN Clientes CliFat  ON OS_Vig.CliFat = CliFat.Codigo "
                    + "                                AND OS_Vig.CodFil = CliFat.CodFil "
                    + " WHERE Rt_Perc.Sequencia = ? and Rt_Perc.Parada = ?";*/
            String sql = " SELECT EscalaDst.Veiculo veiculoOri, EscalaDst.Rota rotaOri, percDst.hrcheg horaChegada, "
                    + "         percDst.hrsaida horaSaida, convert(DATE,percDst.Dt_Fech) coletaOri, "
                    + "         EscalaOri.Veiculo veiculoDst, EscalaOri.Rota rotaDst, percOrigem.hrcheg hora1, "
                    + "         percOrigem.hrsaida hora2, convert(DATE,percOrigem.Dt_Fech) coletaDst, "
                    + "         CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, "
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER, OS_Vig.CCusto CCustoOS "
                    + " FROM Rt_Guias "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia "
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada "
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia "
                    + "                           AND percDst.hora1    = percOrigem.hora1d "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil "
                    + "                  AND OS_Vig.OS    = Rt_Guias.OS "
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia "
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil "
                    + " WHERE percOrigem.Sequencia = ? AND percOrigem.Parada = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            Rt_Guias rt_Guias;
            while (consulta.Proximo()) {
                rt_Guias = new Rt_Guias();
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
                rt_Guias.setKM(consulta.getString("km"));
                rt_Guias.setKMTerra(consulta.getString("KMterra"));
                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setCCustoOS(consulta.getString("CCustoOS"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));
                rt_Guias.setRotaOri(consulta.getString("Rota"));

                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setEr(consulta.getString("er"));
                guias.add(rt_Guias);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.listaGuias - " + e.getMessage() + "\r\n"
                    + "SELECT EscalaDst.Veiculo veiculoOri, EscalaDst.Rota rotaOri, percDst.hrcheg horaChegada, "
                    + "         percDst.hrsaida horaSaida, convert(DATE,percDst.Dt_Fech) coletaOri, "
                    + "         EscalaOri.Veiculo veiculoDst, EscalaOri.Rota rotaDst, percOrigem.hrcheg hora1, "
                    + "         percOrigem.hrsaida hora2, convert(DATE,percOrigem.Dt_Fech) coletaDst, "
                    + "         CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, "
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER "
                    + " FROM Rt_Guias "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia "
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada "
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia "
                    + "                           AND percDst.hora1    = percOrigem.hora1d "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil "
                    + "                  AND OS_Vig.OS    = Rt_Guias.OS "
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia "
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil "
                    + " WHERE percOrigem.Sequencia = " + sequencia + " AND percOrigem.Parada = " + parada);
        }
        return guias;
    }

    public List<Rt_Guias> listaGuias(String sequencia, String parada, boolean pedido, Persistencia persistencia) throws Exception {
        List<Rt_Guias> guias = new ArrayList<>();
        try {
            // Corrigir OS em Rt_Guias
            String sqlUpd = "UPDATE rt_guias\n"
                    + "SET rt_guias.OS = rt_perc.OS\n"
                    + "FROM rt_guias \n"
                    + "JOIN rt_perc \n"
                    + "  ON rt_guias.sequencia =  rt_perc.sequencia \n"
                    + " AND rt_guias.parada = rt_perc.parada\n"
                    + " AND rt_guias.codfil = rt_perc.codfil\n"
                    + "where rt_guias.sequencia = ?\n"
                    + "AND   rt_guias.parada    = ?\n"
                    + "AND   rt_guias.OS        = 0";

            Consulta consulta2 = new Consulta(sqlUpd, persistencia);
            consulta2.setString(sequencia);
            consulta2.setString(parada);
            consulta2.update();
            consulta2.close();

            // Consulta Guia
            String sql = " SELECT Rt_GuiasMoeda.Moeda, EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, \n"
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, \n"
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, \n"
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, \n"
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, \n"
                    + " CliOri.Agencia AgenciaOri, CliOri.SubAgencia SubAgenciaOri,"
                    + "         CliOri.NRed NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, \n"
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, \n"
                    + " CliOri.Nome NomeOri, case when (CliOri.CGC is null or Cliori.CGC = '') then CliOri.CPF else CliOri.CGC end  RegistroOri, \n"
                    + " CliDst.Agencia AgenciaDst, CliDst.SubAgencia SubAgenciaDst, \n"
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, \n"
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, \n"
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER, percOrigem.TipoSrv, percOrigem.Observ,  \n"
                    + " filiais.razaoSocial razaoSocial,  \n"
                    + " filiais.endereco enderecoFilial, \n"
                    + " filiais.bairro bairroFilial, \n"
                    + " filiais.cidade cidadeFilial, \n"
                    + " filiais.UF ufFilial, \n"
                    + " filiais.CEP cepFilial, \n"
                    + " filiais.CNPJ cnpjFilial, \n"
                    + " filiais.fone foneFilial, \n"
                    + " XMLGTVE.ChaveGTVE ChaveGTVE, \n"
                    + " XMLGTVE.Protocolo ProtocoloGTVE, \n"
                    + " XMLGTVE.Link LinkGTVE, \n"
                    + " XMLGTVE.Dt_Retorno DataGTVE, \n"
                    + " XMLGTVE.Hr_Retorno HoraGTVE, \n";

            if (pedido) {
                sql += " Pedido.Solicitante \n";
            } else {
                sql += " '' Solicitante \n";
            }
            sql += " FROM Rt_Guias \n"
                    + " LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                              AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                              AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada \n"
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia \n"
                    + "                           AND percDst.Parada    = percOrigem.Dpar \n"
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia \n"
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia \n";
            if (pedido) {
                sql += " LEFT JOIN Pedido ON Pedido.numero = percOrigem.pedido \n"
                        + "                AND Pedido.SeqRota = percOrigem.Sequencia \n"
                        + "                AND Pedido.CodFil = Rt_Guias.CodFil \n"
                        + "      LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS \n"
                        + "                       AND OS_Vig.codfil = Pedido.CodFil \n";
            } else {
                sql += " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil \n"
                        + "                  AND OS_Vig.OS    = Rt_Guias.OS \n";
            }
            sql += " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil \n"
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil \n"
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil \n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia \n"
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil \n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + " LEFT JOIN XMLGTVE on Rt_Guias.Guia = XMLGTVE.Guia AND Rt_Guias.Serie = XMLGTVE.Serie \n"
                    + " WHERE percOrigem.Sequencia = ? AND percOrigem.Parada = ? AND percOrigem.Flag_Excl != '*'";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            Rt_Guias rt_Guias;
            while (consulta.Proximo()) {
                rt_Guias = new Rt_Guias();
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
                rt_Guias.setKM(consulta.getString("km"));
                rt_Guias.setKMTerra(consulta.getString("KMterra"));
                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setRazaoSocial(consulta.getString("razaoSocial"));
                rt_Guias.setEnderecoFilial(consulta.getString("enderecoFilial"));
                rt_Guias.setBairroFilial(consulta.getString("bairroFilial"));
                rt_Guias.setCidadeFilial(consulta.getString("cidadeFilial"));
                rt_Guias.setUfFilial(consulta.getString("ufFilial"));
                rt_Guias.setCepFilial(consulta.getString("cepFilial"));
                rt_Guias.setCnpjFilial(consulta.getString("cnpjFilial"));
                rt_Guias.setFoneFilial(consulta.getString("foneFilial"));

                rt_Guias.setAgenciaOri(consulta.getString("AgenciaOri"));
                rt_Guias.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setNomeOri(consulta.getString("NomeOri"));
                rt_Guias.setRegistroOri(consulta.getString("RegistroOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setVeiculoOri(consulta.getString("veiculoOri"));
                rt_Guias.setRotaOri(consulta.getString("rotaOri"));
                rt_Guias.setColetaOri(consulta.getString("coletaOri"));

                rt_Guias.setAgenciaDst(consulta.getString("AgenciaDst"));
                rt_Guias.setSubAgenciaDst(consulta.getString("SubAgenciaDst"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setVeiculoDst(consulta.getString("veiculoDst"));
                rt_Guias.setRotaDst(consulta.getString("rotaDst"));
                rt_Guias.setColetaDst(consulta.getString("coletaDst"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setNomeFat(consulta.getString("NomeFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setEr(consulta.getString("er"));
                rt_Guias.setTipoSrv(consulta.getString("tipoSrv"));
                rt_Guias.setObserv(consulta.getString("Observ"));
                rt_Guias.setSolicitante(consulta.getString("Solicitante"));
                rt_Guias.setMoeda(consulta.getString("Moeda"));

                rt_Guias.setGTVeChave(consulta.getString("ChaveGTVE"));
                rt_Guias.setGTVeLink(consulta.getString("LinkGTVE"));
                rt_Guias.setGTVeProtocolo(consulta.getString("ProtocoloGTVE"));

                rt_Guias.setGTVeData(consulta.getString("DataGTVE"));
                rt_Guias.setGTVeHora(consulta.getString("HoraGTVE"));

                guias.add(rt_Guias);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.listaGuias - " + e.getMessage() + "\r\n"
                    + "SELECT EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, "
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, "
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, "
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, "
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, "
                    + "         CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, "
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER "
                    + " FROM Rt_Guias "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia "
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada "
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia "
                    + "                           AND percDst.Parada    = percOrigem.Dpar "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + (pedido ? " LEFT JOIN Pedido ON Pedido.numero = percOrigem.pedido "
                            + "                AND Pedido.SeqRota = percOrigem.Sequencia "
                            + "                AND Pedido.CodFil = Rt_Guias.CodFil "
                            + "      LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS "
                            + "                       AND OS_Vig.codfil = Pedido.CodFil "
                            : " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil "
                            + "                  AND OS_Vig.OS    = Rt_Guias.OS ")
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia "
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil "
                    + " WHERE percOrigem.Sequencia = " + sequencia + " AND percOrigem.Parada = " + parada);
        }
        return guias;
    }

    public List<Rt_Guias> dadosGuia(String guia, String serie, boolean pedido, Persistencia persistencia) throws Exception {
        List<Rt_Guias> guias = new ArrayList<>();
        String sql = "";
        try {
            sql = " SELECT Rt_GuiasMoeda.Moeda, EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, \n"
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, \n"
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, \n"
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, \n"
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, \n"
                    + "(CliOri.Agencia) AgenciaOri, (CliOri.SubAgencia) SubAgenciaOri,"
                    + "         CliOri.NRed NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, \n"
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, \n"
                    + " CliOri.Nome NomeOri, case when (CliOri.CGC is null or Cliori.CGC = '') then CliOri.CPF else CliOri.CGC end  RegistroOri, \n"
                    + "(CliDst.Agencia) AgenciaDst, (CliDst.SubAgencia) SubAgenciaDst, \n"
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, \n"
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, \n"
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER, percOrigem.TipoSrv, percOrigem.Observ,  \n"
                    + " (filiais.razaoSocial) razaoSocial,  \n"
                    + " (filiais.endereco) enderecoFilial, \n"
                    + " (filiais.bairro) bairroFilial, \n"
                    + " (filiais.cidade) cidadeFilial, \n"
                    + " (filiais.UF) ufFilial, \n"
                    + " (filiais.CEP) cepFilial, \n"
                    + " (filiais.CNPJ) cnpjFilial, \n"
                    + " (filiais.fone) foneFilial, percOrigem.Dt_Alter,\n";
            if (pedido) {
                sql += " Pedido.Solicitante \n";
            } else {
                sql += " '' Solicitante \n";
            }
            sql += " FROM Rt_Guias \n"
                    + " LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                              AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                              AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada \n"
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia \n"
                    + "                           AND percDst.Parada    = percOrigem.Dpar \n"
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia \n"
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia \n";
            if (pedido) {
                sql += " LEFT JOIN Pedido ON Pedido.numero = percOrigem.pedido \n"
                        + "                /*AND Pedido.SeqRota = percOrigem.Sequencia*/ \n"
                        + "      LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS \n"
                        + "                       AND OS_Vig.codfil = Pedido.CodFil \n";
            } else {
                sql += " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil \n"
                        + "                  AND OS_Vig.OS    = Rt_Guias.OS \n";
            }
            sql += " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil \n"
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil \n"
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil \n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia \n"
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil \n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + " WHERE Rt_Guias.Guia = ? AND Rt_Guias.Serie = ? AND percOrigem.Flag_Excl != '*'";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            Rt_Guias rt_Guias;
            while (consulta.Proximo()) {
                rt_Guias = new Rt_Guias();
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
                rt_Guias.setKM(consulta.getString("km"));
                rt_Guias.setKMTerra(consulta.getString("KMterra"));
                rt_Guias.setOperador(consulta.getString("operador"));
                rt_Guias.setDt_Alter(consulta.getLocalDate("Dt_Alter"));

                rt_Guias.setRazaoSocial(consulta.getString("razaoSocial"));
                rt_Guias.setEnderecoFilial(consulta.getString("enderecoFilial"));
                rt_Guias.setBairroFilial(consulta.getString("bairroFilial"));
                rt_Guias.setCidadeFilial(consulta.getString("cidadeFilial"));
                rt_Guias.setUfFilial(consulta.getString("ufFilial"));
                rt_Guias.setCepFilial(consulta.getString("cepFilial"));
                rt_Guias.setCnpjFilial(consulta.getString("cnpjFilial"));
                rt_Guias.setFoneFilial(consulta.getString("foneFilial"));

                rt_Guias.setAgenciaOri(consulta.getString("AgenciaOri"));
                rt_Guias.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setNomeOri(consulta.getString("NomeOri"));
                rt_Guias.setRegistroOri(consulta.getString("RegistroOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setVeiculoOri(consulta.getString("veiculoOri"));
                rt_Guias.setRotaOri(consulta.getString("rotaOri"));
                rt_Guias.setColetaOri(consulta.getString("coletaOri"));

                rt_Guias.setAgenciaDst(consulta.getString("AgenciaDst"));
                rt_Guias.setSubAgenciaDst(consulta.getString("SubAgenciaDst"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setVeiculoDst(consulta.getString("veiculoDst"));
                rt_Guias.setRotaDst(consulta.getString("rotaDst"));
                rt_Guias.setColetaDst(consulta.getString("coletaDst"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setNomeFat(consulta.getString("NomeFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setEr(consulta.getString("er"));
                rt_Guias.setTipoSrv(consulta.getString("tipoSrv"));
                rt_Guias.setObserv(consulta.getString("Observ"));
                rt_Guias.setSolicitante(consulta.getString("Solicitante"));
                rt_Guias.setMoeda(consulta.getString("Moeda"));
                guias.add(rt_Guias);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.listaGuias - " + e.getMessage() + "\r\n"
                    + sql);
        }
        return guias;
    }

    public List<Rt_Guias> listaGuiasEntrega(String sequencia, String parada, boolean pedido, Persistencia persistencia) throws Exception {
        List<Rt_Guias> guias = new ArrayList<>();
        try {
            String sql = " SELECT Rt_GuiasMoeda.Moeda, EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg hora1, "
                    + "         percOrigem.hrsaida hora2, convert(DATE,percOrigem.Dt_Fech) coletaOri, "
                    + "         EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg horaChegada, "
                    + "         percDst.hrsaida horaSaida, convert(DATE,percDst.Dt_Fech) coletaDst,"
                    + "         percDst.codcli2 CodCli2, percDst.codcli1 CodCli1, "
                    + "(CliOri.Agencia) AgenciaOri, (CliOri.SubAgencia) SubAgenciaOri,"
                    + "         CliOri.NRed NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri,"
                    + " CliOri.Nome NomeOri, case when (CliOri.CGC is null or Cliori.CGC = '') then CliOri.CPF else CliOri.CGC end  RegistroOri, \n"
                    + "(CliDst.Agencia) AgenciaDst, (CliDst.SubAgencia) SubAgenciaDst, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, "
                    + "         guias.*, Rotas.Rota , percOrigem.ER, percOrigem.TipoSrv, percOrigem.Observ,  "
                    + " (filiais.razaoSocial) razaoSocial,  \n"
                    + " (filiais.endereco) enderecoFilial, \n"
                    + " (filiais.bairro) bairroFilial, \n"
                    + " (filiais.cidade) cidadeFilial, \n"
                    + " (filiais.UF) ufFilial, \n"
                    + " (filiais.CEP) cepFilial, \n"
                    + " (filiais.CNPJ) cnpjFilial, \n"
                    + " (filiais.fone) foneFilial, \n";
            if (pedido) {
                sql += " Pedido.Solicitante \n";
            } else {
                sql += " '' Solicitante \n";
            }
            sql += "  FROM Rt_Perc percDst "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.parada     = percDst.dpar "
                    + "                              AND percOrigem.CodCli1   = percDst.CodCli2 "
                    + "                              AND percOrigem.Sequencia = percDst.Sequencia "
                    + "                              AND percOrigem.HrSaida  != '' "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + " LEFT JOIN Rt_Guias guias ON guias.Sequencia = percDst.Sequencia "
                    + "                          AND guias.Parada   = percDst.Parada \n"
                    + " LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                              AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                              AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n";
            if (pedido) {
                sql += " LEFT JOIN Pedido ON Pedido.numero  = percDst.pedido "
                        + "      LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS "
                        + "                       AND OS_Vig.codfil = Pedido.CodFil ";
            } else {
                sql += " LEFT JOIN OS_Vig ON OS_Vig.CodFil = guias.CodFil "
                        + "                       AND OS_Vig.OS    = guias.OS ";
            }
            sql += " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percDst.Sequencia "
                    + "                 AND Rotas.CodFil   = OS_Vig.Codfil "
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + " WHERE percDst.dpar = ? AND percDst.Sequencia = ? AND percDst.Flag_Excl != '*' "
                    + "                            and percDst.HrBaixa is not null "
                    + "                            and percDst.HrBaixa <> '' "
                    + " ORDER BY CliFat.Codigo DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(parada);
            consulta.setString(sequencia);
            consulta.select();

            Rt_Guias rt_Guias;
            while (consulta.Proximo()) {
                rt_Guias = new Rt_Guias();
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
                rt_Guias.setKM(consulta.getString("km"));
                rt_Guias.setKMTerra(consulta.getString("KMterra"));
                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setRazaoSocial(consulta.getString("razaoSocial"));
                rt_Guias.setEnderecoFilial(consulta.getString("enderecoFilial"));
                rt_Guias.setBairroFilial(consulta.getString("bairroFilial"));
                rt_Guias.setCidadeFilial(consulta.getString("cidadeFilial"));
                rt_Guias.setUfFilial(consulta.getString("ufFilial"));
                rt_Guias.setCepFilial(consulta.getString("cepFilial"));
                rt_Guias.setCnpjFilial(consulta.getString("cnpjFilial"));
                rt_Guias.setFoneFilial(consulta.getString("foneFilial"));

                rt_Guias.setAgenciaOri(consulta.getString("AgenciaOri"));
                rt_Guias.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setNomeOri(consulta.getString("NomeOri"));
                rt_Guias.setRegistroOri(consulta.getString("RegistroOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setVeiculoOri(consulta.getString("veiculoOri"));
                rt_Guias.setRotaOri(consulta.getString("rotaOri"));
                rt_Guias.setColetaOri(consulta.getString("coletaOri"));

                rt_Guias.setAgenciaDst(consulta.getString("AgenciaDst"));
                rt_Guias.setSubAgenciaDst(consulta.getString("SubAgenciaDst"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setVeiculoDst(consulta.getString("veiculoDst"));
                rt_Guias.setRotaDst(consulta.getString("rotaDst"));
                rt_Guias.setColetaDst(consulta.getString("coletaDst"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setNomeFat(consulta.getString("NomeFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setTipoSrv("TipoSrv");
                rt_Guias.setObserv(consulta.getString("Observ"));
                rt_Guias.setEr("E");
                rt_Guias.setSolicitante(consulta.getString("Solicitante"));
                rt_Guias.setMoeda(consulta.getString("Moeda"));
                guias.add(rt_Guias);
            }
            consulta.Close();
        } catch (Exception e) {

        }
        return guias;
    }

    public List<Rt_Guias> listaManifests(String sequencia, String parada, Persistencia persistencia) throws Exception {
        List<Rt_Guias> guias = new ArrayList<>();
        try {
            String sql = " SELECT CxFGuiasVol.Valor ManifestItemValor, ManifestItem.Lacre ManifestItemLacre,\n"
                    + "    Manifest.CodFil ManifestCodFil, \n"
                    + "    Manifest.SeqRota ManifestSeqRota, \n"
                    + "    Manifest.Remessa ManifestRemessa, \n"
                    + "    Manifest.Parada ManifestParada, \n"
                    + "    Manifest.CodRemessa ManifestCodRemessa, \n"
                    + "    Manifest.Qtde ManifestQtde, \n"
                    + "    Manifest.Valor ManifestValor, \n"
                    + "    Manifest.Lacre ManifestLacre, \n"
                    + "    Manifest.Oper_Incl ManifestOper_Incl, \n"
                    + "    Manifest.Dt_Incl ManifestDt_Incl, \n"
                    + "    Manifest.Hr_Incl ManifestHr_Incl, \n"
                    + "    Manifest.Oper_Baixa ManifestOper_Baixa, \n"
                    + "    Manifest.Dt_Baixa ManifestDt_Baixa, \n"
                    + "    Manifest.Hr_Baixa ManifestHr_Baixa, \n"
                    + "EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, \n"
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, \n"
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, \n"
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, \n"
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, \n"
                    + "(CliOri.Agencia) AgenciaOri, (CliOri.SubAgencia) SubAgenciaOri,"
                    + "         CliOri.NRed NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, \n"
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, \n"
                    + " CliOri.Nome NomeOri, case when (CliOri.CGC is null or Cliori.CGC = '') then CliOri.CPF else CliOri.CGC end  RegistroOri, \n"
                    + "(CliDst.Agencia) AgenciaDst, (CliDst.SubAgencia) SubAgenciaDst, \n"
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, \n"
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, \n"
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER, percOrigem.TipoSrv, percOrigem.Observ,  \n"
                    + " (filiais.razaoSocial) razaoSocial,  \n"
                    + " (filiais.endereco) enderecoFilial, \n"
                    + " (filiais.bairro) bairroFilial, \n"
                    + " (filiais.cidade) cidadeFilial, \n"
                    + " (filiais.UF) ufFilial, \n"
                    + " (filiais.CEP) cepFilial, \n"
                    + " (filiais.CNPJ) cnpjFilial, \n"
                    + " (filiais.fone) foneFilial \n"
                    + " FROM Manifest\n"
                    + " LEFT JOIN ManifestItem ON ManifestItem.CodFil = Manifest.CodFil\n"
                    + "                      AND ManifestItem.SeqRota = Manifest.SeqRota\n"
                    + "                      AND ManifestItem.Remessa = Manifest.Remessa\n"
                    + " LEFT JOIN CxFGuiasVol on CxFGuiasVol.Lacre = ManifestItem.Lacre\n"
                    + " LEFT JOIN Rt_Guias ON Rt_Guias.Guia = ManifestItem.Guia\n"
                    + "                  AND Rt_Guias.Serie = ManifestItem.Serie \n"
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada \n"
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia \n"
                    + "                           AND percDst.Parada    = percOrigem.Dpar \n"
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia \n"
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia \n"
                    + " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil \n"
                    + "                  AND OS_Vig.OS    = Rt_Guias.OS \n"
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil \n"
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil \n"
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil \n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia \n"
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil \n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + " WHERE Manifest.SeqRota = ? AND Manifest.Parada = ? AND percOrigem.Flag_Excl != '*' \n"
                    + " ORDER BY ManifestRemessa ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            Rt_Guias rt_Guias;
            Manifest manifest;
            while (consulta.Proximo()) {
                manifest = new Manifest();
                manifest.setCodFil(consulta.getString("ManifestCodFil"));
                manifest.setSeqRota(consulta.getString("ManifestSeqRota"));
                manifest.setRemessa(consulta.getString("ManifestRemessa"));
                manifest.setParada(consulta.getString("ManifestParada"));
                manifest.setCodRemessa(consulta.getString("ManifestCodRemessa"));
                manifest.setQtde(consulta.getString("ManifestQtde"));
                manifest.setValor(consulta.getString("ManifestValor"));
                manifest.setLacre(consulta.getString("ManifestLacre"));
                manifest.setOper_Incl(consulta.getString("ManifestOper_Incl"));
                manifest.setDt_Incl(consulta.getString("ManifestDt_Incl"));
                manifest.setHr_Incl(consulta.getString("ManifestHr_Incl"));
                manifest.setOper_Baixa(consulta.getString("ManifestOper_Baixa"));
                manifest.setDt_Baixa(consulta.getString("ManifestDt_Baixa"));
                manifest.setHr_Baixa(consulta.getString("ManifestHr_Baixa"));

                rt_Guias = new Rt_Guias();
                rt_Guias.setManifest(manifest);
                rt_Guias.setManifestItemLacre(consulta.getString("ManifestItemLacre"));
                rt_Guias.setManifestItemValor(consulta.getString("ManifestItemValor"));

                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
                rt_Guias.setKM(consulta.getString("km"));
                rt_Guias.setKMTerra(consulta.getString("KMterra"));
                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setRazaoSocial(consulta.getString("razaoSocial"));
                rt_Guias.setEnderecoFilial(consulta.getString("enderecoFilial"));
                rt_Guias.setBairroFilial(consulta.getString("bairroFilial"));
                rt_Guias.setCidadeFilial(consulta.getString("cidadeFilial"));
                rt_Guias.setUfFilial(consulta.getString("ufFilial"));
                rt_Guias.setCepFilial(consulta.getString("cepFilial"));
                rt_Guias.setCnpjFilial(consulta.getString("cnpjFilial"));
                rt_Guias.setFoneFilial(consulta.getString("foneFilial"));

                rt_Guias.setAgenciaOri(consulta.getString("AgenciaOri"));
                rt_Guias.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setNomeOri(consulta.getString("NomeOri"));
                rt_Guias.setRegistroOri(consulta.getString("RegistroOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setVeiculoOri(consulta.getString("veiculoOri"));
                rt_Guias.setRotaOri(consulta.getString("rotaOri"));
                rt_Guias.setColetaOri(consulta.getString("coletaOri"));

                rt_Guias.setAgenciaDst(consulta.getString("AgenciaDst"));
                rt_Guias.setSubAgenciaDst(consulta.getString("SubAgenciaDst"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setVeiculoDst(consulta.getString("veiculoDst"));
                rt_Guias.setRotaDst(consulta.getString("rotaDst"));
                rt_Guias.setColetaDst(consulta.getString("coletaDst"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setNomeFat(consulta.getString("NomeFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setEr(consulta.getString("er"));
                rt_Guias.setTipoSrv(consulta.getString("tipoSrv"));
                rt_Guias.setObserv(consulta.getString("Observ"));
                guias.add(rt_Guias);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.listaManifests - " + e.getMessage() + "\r\n"
                    + "SELECT EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, "
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, "
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, "
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, "
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, "
                    + "         CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, "
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER "
                    + " FROM Rt_Guias "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia "
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada "
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia "
                    + "                           AND percDst.Parada    = percOrigem.Dpar "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil "
                    + "                  AND OS_Vig.OS    = Rt_Guias.OS "
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia "
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil "
                    + " WHERE Manifest.SeqRota = " + sequencia + " AND Manifest.Parada = " + parada + " AND percOrigem.Flag_Excl != '*'");
        }
        return guias;
    }

    /**
     * Lista de guias do banco de dados
     *
     * @param sequencia sequencia da rota
     * @param parada Numero da parada
     * @param persistencia conexao com o banco de dados
     * @return lista de guias do banco de dados
     * @throws Exception
     */
    public List<Rt_Guias> listaGuiasEntraga(String sequencia, String parada, Persistencia persistencia) throws Exception {
        List<Rt_Guias> guias = new ArrayList<>();
        try {
            /*String sql = "SELECT guias.* FROM Rt_Perc cliOrigem "
                    + "  JOIN Rt_Perc cliDst ON cliDst.CodCli2 = cliOrigem.CodCli1 "
                    + "    AND cliDst.Sequencia = cliOrigem.Sequencia "
                    + "    AND cliDst.HrSaida != '' "
                    + "  JOIN Rt_Guias guias "
                    + "    ON guias.Sequencia = cliDst.Sequencia "
                    + "  AND guias.Parada = cliDst.Parada "
                    + "WHERE cliOrigem.Parada = ? AND cliOrigem.Sequencia = ?";
            String sql = " SELECT guias.*, CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, "
                        + "            CliOri.Cidade CidadeOri, CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, "
                        + "            CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                        + "            CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                        + "            CliFat.CGC CGCFat,CliFat.IE IEFat, CliDst.email EmailDst, CliDst.Cep cepDst,  CliOri.email EmailOri, CliFat.email EmailFat, "
                        + "            CliOri.Codigo CodCliOri, Rotas.Rota "
                        + "    FROM Rt_Perc percOrigem "
                        + "    LEFT JOIN Rt_Perc percDst ON percDst.CodCli2 = percOrigem.CodCli1 "
                        + "                           AND percDst.Sequencia = percOrigem.Sequencia "
                        + "                            AND percDst.HrSaida != '' "
                        + "    LEFT JOIN Rt_Guias guias ON guias.Sequencia = percDst.Sequencia "
                        + "                               AND guias.Parada = percDst.Parada "
                        + "    LEFT JOIN OS_Vig  ON OS_Vig.CodFil = guias.CodFil "
                        + "                         AND OS_Vig.OS = guias.OS "
                        + "    LEFT JOIN Clientes CliOri  ON OS_Vig.Cliente = CliOri.Codigo "
                        + "                               AND OS_Vig.CodFil = CliOri.CodFil "
                        + "    LEFT JOIN Clientes CliDst  ON OS_Vig.CliDst = CliDst.Codigo "
                        + "                              AND OS_Vig.CodFil = CliDst.CodFil "
                        + "    LEFT JOIN Clientes CliFat  ON OS_Vig.CliFat = CliFat.Codigo "
                        + "                              AND OS_Vig.CodFil = CliFat.CodFil "
                        + "    left Join Rotas on  Rotas.Sequencia = percOrigem.Sequencia "
                        + "                 and Rotas.CodFil = OS_Vig.Codfil "
                        + "    WHERE percOrigem.Parada = ? AND percOrigem.Sequencia = ? "
                        + "    ORDER BY CliFat.Codigo DESC";*/

            String sql = " SELECT EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, "
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, "
                    + "         EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, "
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, "
                    + "         CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, "
                    + "         guias.*, Rotas.Rota , percOrigem.ER "
                    + " FROM Rt_Perc percDst "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.parada     = percDst.dpar "
                    + "                              AND percOrigem.CodCli1   = percDst.CodCli2 "
                    + "                              AND percOrigem.Sequencia = percDst.Sequencia "
                    + "                              AND percOrigem.HrSaida  != '' "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + " LEFT JOIN Rt_Guias guias ON guias.Sequencia = percDst.Sequencia "
                    + "                          AND guias.Parada   = percDst.Parada "
                    + " LEFT JOIN OS_Vig ON OS_Vig.CodFil = guias.CodFil "
                    + "                  AND OS_Vig.OS    = guias.OS "
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percDst.Sequencia "
                    + "                 AND Rotas.CodFil   = OS_Vig.Codfil "
                    + " WHERE percDst.dpar = ? AND percDst.Sequencia = ? "
                    + " ORDER BY CliFat.Codigo DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(parada);
            consulta.setString(sequencia);
            consulta.select();

            Rt_Guias rt_Guias;
            while (consulta.Proximo()) {
                rt_Guias = new Rt_Guias();
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
                rt_Guias.setKM(consulta.getString("km"));
                rt_Guias.setKMTerra(consulta.getString("KMterra"));
                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));
                rt_Guias.setRotaOri(consulta.getString("Rota"));

                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setEr("E");
                guias.add(rt_Guias);
            }
            consulta.Close();
        } catch (Exception e) {

        }
        return guias;
    }

    public String obterSerie(String guia, String parada, String codFil, Persistencia persistencia) throws Exception {
        String serie = "";
        try {
            String sql = "SELECT top 1 Serie FROM Rt_Guias WHERE guia = ? AND parada = ? AND CodFil = ? ORDER BY Dt_Alter DESC";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(parada);
            consulta.setString(codFil);
            consulta.select();

            while (consulta.Proximo()) {
                serie = consulta.getString("Serie");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.obterSerie - " + e.getMessage() + "\r\n"
                    + "SELECT top 1 Serie FROM Rt_Guias WHERE guia = " + guia + " AND parada = " + parada + " AND CodFil = " + codFil + " ORDER BY Dt_Alter DESC");
        }
        return serie;
    }

    /**
     * Faz validação da guia em Rt_Guias
     *
     * @param Guia - Número da Guia a verificar
     * @param Serie - Série da Guia a verificar
     * @param persistencia - Conexão ao banco
     * @return - 1 caso não exista guia 2 caso essa guia tenha acabado de ser
     * digitada, irá sobrescrever 0 caso a guia já exista
     * @throws Exception
     */
    public boolean isGuia(String Guia, String Serie, String codFil, Persistencia persistencia) throws Exception {
        boolean retornar = false; //1 - não existe guia
        String sql = "select top 1 Sequencia,Parada from Rt_Guias"
                + " where Guia=? and Serie=? and codFil = ? ";
        try {
            Consulta consultguia = new Consulta(sql, persistencia);
            consultguia.setString(Guia);
            consultguia.setString(Serie);
            consultguia.setString(codFil);
            consultguia.select();
            while (consultguia.Proximo()) {
                retornar = true; //0 - existe guia
            }
            consultguia.Close();
            return retornar;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.isGuia - " + e.getMessage() + "\r\n"
                    + "select top 1 Sequencia,Parada from Rt_Guias"
                    + " where Guia=" + Guia + " and Serie=" + Serie + " and codFil = " + codFil);
        }
    }

    public List<Rt_Guias> getRt_GuiasEntrega(String sequencia, String parada, Persistencia persistencia) throws Exception {
        List<Rt_Guias> listGuias = new ArrayList();
        try {
            Consulta rsgtv = new Consulta("Select "
                    + " Rt_Guias.Guia, Rt_Guias.Serie, Rt_Guias.Valor, Rt_Guias.OS "
                    + " from Rt_Guias "
                    + " Left Join Rt_Perc on  Rt_Perc.Sequencia = Rt_Guias.Sequencia "
                    + " and Rt_perc.Parada = Rt_Guias.Parada"
                    + " Left Join Rt_Perc Rt_PercDst on Rt_PercDst.Sequencia = Rt_Perc.Sequencia"
                    + " and Rt_PercDst.Parada = Rt_perc.DPar"
                    + " where Rt_PercDst.Sequencia = ?"
                    + " and Rt_PercDst.Parada    = ?", persistencia);
            rsgtv.setString(sequencia);
            rsgtv.setString(parada);
            rsgtv.select();
            while (rsgtv.Proximo()) {
                if ((rsgtv.getFloat("Guia") > 0) && (!rsgtv.getString("Serie").equals(""))) {
                    Rt_Guias temp = new Rt_Guias();
                    temp.setGuia(rsgtv.getString("Guia").substring(0, rsgtv.getString("Guia").indexOf(".")));
                    temp.setSerie(rsgtv.getString("Serie"));
                    temp.setValor(rsgtv.getString("Valor"));
                    temp.setOS(rsgtv.getString("OS"));
                    listGuias.add(temp);
                }
            }
            rsgtv.Close();
            return listGuias;
        } catch (Exception e) {
            return listGuias;
            //throw new Exception("Rt_GuiasDao. - " + e.getMessage());
        }
    }

    public List<Rt_Guias> getGuiasSerieValor(String sequencia, String parada, Persistencia persistencia) throws Exception {

        String sql = "select guia,serie,valor  from rt_guias where "
                + " sequencia=? and parada=?";
        try {
            Consulta consultguia = new Consulta(sql, persistencia);
            consultguia.setString(sequencia);
            consultguia.setString(parada);
            consultguia.select();

            List<Rt_Guias> RtList = new ArrayList<Rt_Guias>();
            while (consultguia.Proximo()) {
                Rt_Guias rtguias = new Rt_Guias();
                rtguias.setGuia(consultguia.getString("Guia"));
                rtguias.setSerie(consultguia.getString("Serie"));
                rtguias.setValor(consultguia.getString("Valor"));
                RtList.add(rtguias);
            }
            consultguia.Close();
            return RtList;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.getGuiasSerieValor - " + e.getMessage() + "\r\n"
                    + "select guia,serie,valor  from rt_guias where "
                    + " sequencia=" + sequencia + " and parada=" + parada);
        }
    }

    public int contaGuiasParada(String Sequencia, String Parada, Persistencia persistencia) throws Exception {
        String sql = "select count(*) qtguias from rt_guias where "
                + " sequencia=? and parada=?";
        int qtguias = 1;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Sequencia);
            consult.setString(Parada);
            consult.select();
            while (consult.Proximo()) {
                qtguias = consult.getInt("qtguias");
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.contaGuiasParada - " + e.getMessage() + "\r\n"
                    + "select count(*) qtguias from rt_guias where "
                    + " sequencia=" + Sequencia + " and parada=" + Parada);
        }
        return qtguias;
    }

    /**
     * apaga a guia em Rt_Guias
     *
     * @param sequencia - Sequência da Rota
     * @param parada - Parada da Rota
     * @param guia - Número da Guia a verificar
     * @param serie - Série da Guia a verificar
     * @param persistencia - Conexão ao banco
     * @throws Exception
     */
    public void apagaGuiaRt(String guia, String serie, String sequencia, String parada, Persistencia persistencia) throws Exception {
        String sql = "delete from rt_guias where "
                + " guia=?"
                + " and serie=?"
                + " and sequencia=?"
                + " and parada=?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.apagaGuiaRt - " + e.getMessage() + "\r\n"
                    + "delete from rt_guias where "
                    + " guia=" + guia
                    + " and serie=" + serie
                    + " and sequencia=" + sequencia
                    + " and parada=" + parada);
        }
    }

    /**
     * Adiciona guia
     *
     * @param persistencia - Conexão ao banco
     * @param sequencia - Seqüência de Rota
     * @param CodFil - Código Filial
     * @param parada - Número da Parada
     * @param guia - Número da guia
     * @param serie - Número série
     * @param valor - Valor
     * @param OS - Número OS
     * @param KM - Km
     * @param KMTerra - KmTerra
     * @param Operador - Operador
     * @param Dt_Alter - Data alteração
     * @param Hr_Alter - Hora alteração
     */
    public void AdicionaRt_Guia(Persistencia persistencia, String CodFil, String sequencia, String parada,
            String guia, String serie, String valor, String OS, String KM, String KMTerra,
            String Operador, String Dt_Alter, String Hr_Alter) throws Exception {
        try {
            /* String sql = "insert into Rt_Guias (Sequencia,CodFil, Parada,Guia,Serie,SerieAnt,"
                + "Valor,OS,KM,KMTerra,Operador,Dt_Alter,Hr_Alter, Chave) "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";*/
            String sql = "insert into Rt_Guias (Sequencia,CodFil, Parada,Guia,Serie,SerieAnt,"
                    + "Valor,OS,KM,KMTerra,Operador,Dt_Alter,Hr_Alter) "
                    + " values (?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(CodFil);
            consulta.setInt(Integer.parseInt(parada));
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(serie);
            consulta.setString(valor);
            consulta.setString(OS);
            consulta.setString(KM);
            consulta.setString(KMTerra);
            consulta.setString(Operador);
            consulta.setString(Dt_Alter);//java.sql.Date.valueOf(Dt_Alter));
            consulta.setString(Hr_Alter);
            //consulta.setString(Chave);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.AdicionaRt_Guia - " + e.getMessage() + "\r\n"
                    + "insert into Rt_Guias (Sequencia,CodFil, Parada,Guia,Serie,SerieAnt,"
                    + "Valor,OS,KM,KMTerra,Operador,Dt_Alter,Hr_Alter) "
                    + " values (" + sequencia + "," + CodFil + "," + parada + "," + guia + "," + serie + "," + serie + "," + valor + "," + OS + "," + KM + "," + KMTerra + ","
                    + Operador + "," + Dt_Alter + "," + Hr_Alter + ")");
        }
    }

    /**
     * Inserção de guias de rota
     *
     * @param Sequencia - Sequencia da rota
     * @param CodFil - Código da filial
     * @param Parada - Número da Parada
     * @param Guia - Número da Guia
     * @param Serie - Série da Guia
     * @param OS - Número da OS do serviço
     * @param Operador - Operador
     * @param Dt_Alter - Data de alteração
     * @param Hr_Alter - Hora de alteração
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void Adiciona_Rt_Guias(String Sequencia, String CodFil, String Parada, String Guia, String Serie,
            String OS, String Operador, String Dt_Alter, String Hr_Alter,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into Rt_Guias (Sequencia,CodFil, Parada,Guia,Serie,SerieAnt,"
                    + "OS, Operador,Dt_Alter,Hr_Alter) "
                    + " values (?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Sequencia);
            consulta.setString(CodFil);
            consulta.setString(Parada);
            consulta.setString(Guia);
            consulta.setString(Serie);
            consulta.setString(Serie);
            consulta.setString(OS);
            consulta.setString(Operador);
            consulta.setString(Dt_Alter);
            consulta.setString(Hr_Alter);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.Adiciona_Rt_Guias - " + e.getMessage() + "\r\n"
                    + "insert into Rt_Guias (Sequencia,CodFil, Parada,Guia,Serie,SerieAnt,"
                    + "OS, Operador,Dt_Alter,Hr_Alter) "
                    + " values (" + Sequencia + "," + CodFil + "," + Parada + "," + Guia + "," + Serie + "," + Serie + "," + OS + "," + Operador + "," + Dt_Alter + "," + Hr_Alter + ")");
        }
    }

    /**
     * Atualiza Rt_Guia
     *
     * @param persistencia - Conexão ao banco
     * @param valor - Valor
     * @param sequencia - Seqüência de Rota
     * @param parada - Número da Parada
     * @param guia - Número da guia
     * @param serie - Número série
     * @param OS - Número OS
     * @param codFil
     * @throws java.lang.Exception - pode gerar exception
     */
    public void AtualizaRt_Guia(Persistencia persistencia, String valor, String sequencia, String parada,
            String guia, String serie, String OS, String codFil) throws Exception {

        String sql = "update Rt_Guias "
                + " set Valor = ?,"
                + " OS = ?"
                + " where sequencia=?"
                + " and parada=?"
                + " and Guia=?"
                + " and Serie=?"
                + " and codFil = ?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(OS);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.AtualizaRt_Guia - " + e.getMessage() + "\r\n"
                    + "update Rt_Guias "
                    + " set Valor = " + valor + ","
                    + " OS = " + OS + ","
                    + " where sequencia=" + sequencia
                    + " and parada=" + parada
                    + " and Guia=" + guia
                    + " and Serie=" + serie
                    + " and codFil=" + codFil);
        }
    }

    public void AtualizaRt_Guia(Persistencia persistencia, String valor, String sequencia, String parada,
            String guia, String serie) throws Exception {

        String sql = "update Rt_Guias "
                + " set Valor = ?"
                + " where sequencia=?"
                + " and parada=?"
                + " and Guia=?"
                + " and Serie=?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.update();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.AtualizaRt_Guia - " + e.getMessage() + "\r\n"
                    + "update Rt_Guias "
                    + " set Valor = " + valor + ","
                    + " where sequencia=" + sequencia
                    + " and parada=" + parada
                    + " and Guia=" + guia
                    + " and Serie=" + serie);
        }
    }

    public int AtualizaRt_Guia(Persistencia persistencia, Rt_Guias rt_guias, String NovaSerie) throws Exception {
        int afetados;
        String sql = "update Rt_Guias set serie = ?, serieant = ?"
                + " from Rt_Guias rtg"
                + " left join rotas on rotas.sequencia = rtg.sequencia"
                + " where rotas.codfil = ?"
                + " and Guia = ?"
                + " and Serie = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(NovaSerie);
            consulta.setString(rt_guias.getSerie());
            consulta.setString(rt_guias.getCodFil().toString());
            consulta.setString(rt_guias.getGuia().toString());
            consulta.setString(rt_guias.getSerie());
            afetados = consulta.update();
            consulta.close();
            return afetados;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.AtualizaRt_Guia - " + e.getMessage() + "\r\n"
                    + "update Rt_Guias set serie = " + NovaSerie + ", serieant = " + rt_guias.getSerie()
                    + " from Rt_Guias rtg"
                    + " left join rotas on rotas.sequencia = rtg.sequencia"
                    + " where rotas.codfil = " + rt_guias.getCodFil()
                    + " and Guia = " + rt_guias.getGuia()
                    + " and Serie = " + rt_guias.getSerie());
        }
    }

    public void deletaRt_Guia(Persistencia persistencia, String CodFil, String sequencia, String guia, String serie) throws Exception {

        String sql = "delete from Rt_Guias "
                + " where CodFil=?"
                + " and sequencia=?"
                + " and Guia=?"
                + " and Serie=?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.setString(sequencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.delete();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.deletaRt_Guia - " + e.getMessage() + "\r\n"
                    + "delete from Rt_Guias "
                    + " where codfil = " + CodFil
                    + " and sequencia=" + sequencia
                    + " and Guia = " + guia
                    + " and Serie = " + serie);
        }
    }

    /**
     * Verifica se a guia já existe e em qual data ele passou pela rota
     *
     * @param Guia - Guia a ser testada
     * @param Serie - Serie a ser testada
     * @param CodFil - Código da filial
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Rt_GuiasRotas getGuia(String Guia, String Serie, String CodFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "select rotas.rota, rotas.data Data, rt_guias.Sequencia, rt_guias.Parada from rt_guias"
                    + " left join rotas on rotas.sequencia = rt_guias.sequencia"
                    + " where rt_guias.guia = ?"
                    + " and rt_guias.serie = ?"
                    + " and rotas.codfil = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Guia);
            consult.setString(Serie);
            consult.setString(CodFil);
            consult.select();
            Rt_GuiasRotas guia = new Rt_GuiasRotas();
            Rotas rota = new Rotas();
            Rt_Guias rt_guia = new Rt_Guias();
            while (consult.Proximo()) {
                rt_guia.setGuia(Guia);
                rt_guia.setSerie(Serie);
                try {
                    rota.setData(consult.getString("Data"));
                    rota.setRota(consult.getString("rota"));
                    rt_guia.setSequencia(consult.getString("Sequencia"));
                    rt_guia.setParada(consult.getInt("Parada"));
                } catch (Exception e) {
                    rota.setData(null);
                    rt_guia.setSequencia(null);
                    rt_guia.setParada(0);
                }
                guia.setRotas(rota);
                guia.setRt_guias(rt_guia);
            }
            consult.Close();
            return guia;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.getGuia - " + e.getMessage() + "\r\n"
                    + "select rotas.rota, rotas.data Data, rt_guias.Sequencia, rt_guias.Parada from rt_guias"
                    + " left join rotas on rotas.sequencia = rt_guias.sequencia"
                    + " where rt_guias.guia = " + Guia
                    + " and rt_guias.serie = " + Serie
                    + " and rotas.codfil = " + CodFil);
        }
    }

    /**
     * Busca maior guia cadastrada para serie GT
     *
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public BigDecimal MaxGuia(Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = new BigDecimal("0");
            String sql = "select MAX(guia) guia from rt_guias where serie = 'GTE'";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = consult.getBigDecimal("guia");
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.MaxGuia - " + e.getMessage() + "\r\n"
                    + "select MAX(guia) guia from rt_guias where serie = 'GTE'");
        }
    }

    /**
     * Busca maior guia cadastrada para serie 53
     *
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public BigDecimal maxGuia53(Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = new BigDecimal("0");
            String sql = "select "
                    + " Case when (max(guia) + 1) > (Select max(Rt_Guias.Guia)+1 "
                    + "                                 from Rt_Guias "
                    + "                                 where Rt_Guias.Serie = '53' "
                    + "                                   and guia >= 100000 "
                    + "                                   and guia <= 2750000) then (max(guia) + 1) "

                    //+ "                                            and guia <= 99999999) then (max(guia) + 1) "
                    + " else (Select max(Rt_Guias.Guia)+1 "
                    + "         from Rt_Guias "
                    + "         where Rt_Guias.Serie = '53' "
                    + "           and guia >= 100000 "
                    + "           and guia <= 2750000) "
                    + " end guia "
                    + " from RPV "
                    + " where serie = '53' "
                    + "   and guia >= 100000 "
                    + "   and guia <= 2750000";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = consult.getBigDecimal("guia");
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.maxGuia53 - " + e.getMessage() + "\r\n"
                    + "select "
                    + " Case when (max(guia) + 1) > (Select max(Rt_Guias.Guia)+1 "
                    + "                                 from Rt_Guias "
                    + "                                 where Rt_Guias.Serie = '53' "
                    + "                                            and guia <= 99999999) then (max(guia) + 1) "
                    + " else (Select max(Rt_Guias.Guia)+1 "
                    + "         from Rt_Guias "
                    + "         where Rt_Guias.Serie = '53' "
                    + "                    and guia <= 99999999) "
                    + " end guia "
                    + " from RPV "
                    + " where serie = '53' "
                    + "   and guia <= 99999999");
        }
    }

    public Rt_Guias infoGuiaEntrega(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT Rotas.Codfil, EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg hora1, \n"
                    + "         percOrigem.hrsaida hora2, convert(DATE,percOrigem.Dt_Fech) coletaOri, \n"
                    + " filiais.razaoSocial,  \n"
                    + " filiais.endereco enderecoFilial, \n"
                    + " filiais.bairro bairroFilial, \n"
                    + " filiais.cidade cidadeFilial, \n"
                    + " filiais.UF ufFilial, \n"
                    + " filiais.CEP cepFilial, \n"
                    + " filiais.CNPJ cnpjFilial, \n"
                    + " filiais.fone foneFilial, \n"
                    + "         EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg horaChegada, \n"
                    + "         percDst.hrsaida horaSaida, convert(DATE,percDst.Dt_Fech) coletaDst,\n"
                    + "         percDst.codcli2 CodCli2, percDst.codcli1 CodCli1, \n"
                    + "         CliOri.NRed NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, CliOri.Agencia AgenciaOri, CliOri.SubAgencia SubAgenciaOri, \n"
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, CliOri.Agencia, \n"
                    + " CliOri.Nome NomeOri, case when (CliOri.CGC is null or Cliori.CGC = '') then CliOri.CPF else CliOri.CGC end  RegistroOri, \n"
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, \n"
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, CliDst.Agencia AgenciaDst, CliDst.SubAgencia SubAgenciaDst, \n"
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "         guias.*, Rotas.Rota , percOrigem.ER, percOrigem.TipoSrv, percOrigem.Observ  \n"
                    + " FROM Rt_Perc percDst \n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percDst.Sequencia  \n"
                    + " LEFT JOIN Pedido ON Pedido.numero  = percDst.pedido  \n"
                    + "                  and Pedido.Codfil = rotas.codfil\n"
                    + " LEFT JOIN Rt_Perc percOrigem ON \n"
                    + "                    Case when pedido.codcli1 <> '' \n"
                    + "                        then percOrigem.CodCli1 \n"
                    + "                        else percorigem.dpar \n"
                    + "                    end = Case when pedido.codcli1 <> '' \n"
                    + "                        then pedido.codcli1 \n"
                    + "                        else percDst.parada\n"
                    + "                    end \n"
                    + "                    and percorigem.hora1 < percdst.hora1 \n"
                    + "                    AND percOrigem.Sequencia = percDst.Sequencia \n"
                    + "                    AND percOrigem.HrSaida  != ''\n"
                    + "                    and percorigem.flag_excl <> '*'\n"
                    + "  LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia \n"
                    + "  LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia\n"
                    + "  LEFT JOIN Rt_Guias guias ON guias.Sequencia = percDst.Sequencia\n"
                    + "                           AND guias.Parada   = percDst.Parada \n"
                    + "     LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS   \n"
                    + "                     AND OS_Vig.codfil = Pedido.CodFil \n"
                    + " LEFT JOIN Clientes CliOri ON CliOri.codigo = Case when percDst.ER = 'E' and OS_Vig.Cliente = percdst.CodCli1 then OS_Vig.CliDst else OS_Vig.Cliente end\n"
                    + "                    AND OS_Vig.CodFil = CliOri.CodFil \n"
                    + " LEFT JOIN Clientes CliDst ON  CliDst.Codigo = Case when percDst.ER = 'E' and OS_Vig.Cliente = percdst.CodCli1 then OS_Vig.Cliente else OS_Vig.Clidst end\n"
                    + "                    AND OS_Vig.CodFil = CliDst.CodFil \n"
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo \n"
                    + "                    AND OS_Vig.CodFil = CliFat.CodFil \n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + " WHERE guias.guia = ? AND guias.serie = ? \n"
                    + " ORDER BY CliFat.Codigo DESC \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            Rt_Guias rt_Guias = new Rt_Guias();
            while (consulta.Proximo()) {
                rt_Guias.setCodFil(consulta.getString("Codfil"));
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
                rt_Guias.setKM(consulta.getString("km"));
                rt_Guias.setKMTerra(consulta.getString("KMterra"));
                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setRazaoSocial(consulta.getString("razaoSocial"));
                rt_Guias.setEnderecoFilial(consulta.getString("enderecoFilial"));
                rt_Guias.setBairroFilial(consulta.getString("bairroFilial"));
                rt_Guias.setCidadeFilial(consulta.getString("cidadeFilial"));
                rt_Guias.setUfFilial(consulta.getString("ufFilial"));
                rt_Guias.setCepFilial(consulta.getString("cepFilial"));
                rt_Guias.setCnpjFilial(consulta.getString("cnpjFilial"));
                rt_Guias.setFoneFilial(consulta.getString("foneFilial"));

                rt_Guias.setAgencia(consulta.getString("Agencia"));
                rt_Guias.setAgenciaOri(consulta.getString("AgenciaOri"));
                rt_Guias.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setNomeOri(consulta.getString("NomeOri"));
                rt_Guias.setRegistroOri(consulta.getString("RegistroOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setVeiculoOri(consulta.getString("veiculoOri"));
                rt_Guias.setRotaOri(consulta.getString("rotaOri"));
                rt_Guias.setColetaOri(consulta.getString("coletaOri"));

                rt_Guias.setAgenciaDst(consulta.getString("AgenciaDst"));
                rt_Guias.setSubAgenciaDst(consulta.getString("SubAgenciaDst"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setVeiculoDst(consulta.getString("veiculoDst"));
                rt_Guias.setRotaDst(consulta.getString("rotaDst"));
                rt_Guias.setColetaDst(consulta.getString("coletaDst"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setNomeFat(consulta.getString("NomeFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setTipoSrv("TipoSrv");
                rt_Guias.setObserv(consulta.getString("Observ"));
                rt_Guias.setEr("E");
            }
            consulta.Close();
            return rt_Guias;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.infoGuiaEntrega - " + e.getMessage());
        }
    }

    public Rt_Guias infoGuia(String guia, String serie, boolean pedido, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, \n"
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, \n"
                    + "         filiais.razaoSocial,  \n"
                    + "         filiais.endereco enderecoFilial, \n"
                    + "         filiais.bairro bairroFilial, \n"
                    + "         filiais.cidade cidadeFilial, \n"
                    + "         filiais.UF ufFilial, \n"
                    + "         filiais.CEP cepFilial, \n"
                    + "         filiais.CNPJ cnpjFilial, \n"
                    + "         filiais.fone foneFilial, \n"
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, \n"
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, \n"
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, \n"
                    + "         CliOri.NRed NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, CliOri.Agencia AgenciaOri, CliOri.SubAgencia SubAgenciaori, \n"
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, CliOri.Agencia, \n"
                    + " CliOri.Nome NomeOri, case when (CliOri.CGC is null or Cliori.CGC = '') then CliOri.CPF else CliOri.CGC end  RegistroOri, \n"
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, \n"
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, CliDst.Agencia AgenciaDst, CliDst.SubAgencia SubAgenciaDst,  \n"
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "         Rt_Guias.*, Rotas.rota, Rotas.CodFil, percOrigem.ER, percOrigem.TipoSrv, percOrigem.Observ \n"
                    + " FROM Rt_Guias \n"
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada \n"
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia \n"
                    + "                           AND percDst.Parada    = percOrigem.Dpar \n"
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia \n"
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia \n";
            if (pedido) {
                sql += " LEFT JOIN Pedido ON Pedido.numero = percOrigem.pedido \n"
                        + "                AND Pedido.SeqRota = percOrigem.Sequencia \n"
                        + "      LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS \n"
                        + "                       AND OS_Vig.codfil = Pedido.CodFil \n";
            } else {
                sql += " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil \n"
                        + "                  AND OS_Vig.OS    = Rt_Guias.OS \n";
            }
            sql += " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil \n"
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil \n"
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil \n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia \n"
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil \n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + " WHERE Rt_Guias.Guia = ? AND Rt_Guias.Serie = ? \n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            Rt_Guias rt_Guias = new Rt_Guias();
            while (consulta.Proximo()) {
                rt_Guias.setCodFil(consulta.getString("Codfil"));
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
                rt_Guias.setKM(consulta.getString("km"));
                rt_Guias.setKMTerra(consulta.getString("KMterra"));
                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setRazaoSocial(consulta.getString("razaoSocial"));
                rt_Guias.setEnderecoFilial(consulta.getString("enderecoFilial"));
                rt_Guias.setBairroFilial(consulta.getString("bairroFilial"));
                rt_Guias.setCidadeFilial(consulta.getString("cidadeFilial"));
                rt_Guias.setUfFilial(consulta.getString("ufFilial"));
                rt_Guias.setCepFilial(consulta.getString("cepFilial"));
                rt_Guias.setCnpjFilial(consulta.getString("cnpjFilial"));
                rt_Guias.setFoneFilial(consulta.getString("foneFilial"));

                rt_Guias.setAgenciaOri(consulta.getString("AgenciaOri"));
                rt_Guias.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setAgencia(consulta.getString("Agencia"));
                rt_Guias.setNomeOri(consulta.getString("NomeOri"));
                rt_Guias.setRegistroOri(consulta.getString("RegistroOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setVeiculoOri(consulta.getString("veiculoOri"));
                rt_Guias.setRotaOri(consulta.getString("rotaOri"));
                rt_Guias.setColetaOri(consulta.getString("coletaOri"));

                rt_Guias.setAgenciaDst(consulta.getString("AgenciaDst"));
                rt_Guias.setSubAgenciaDst(consulta.getString("SubAgenciaDst"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setVeiculoDst(consulta.getString("veiculoDst"));
                rt_Guias.setRotaDst(consulta.getString("rotaDst"));
                rt_Guias.setColetaDst(consulta.getString("coletaDst"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setNomeFat(consulta.getString("NomeFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setEr(consulta.getString("er"));
                rt_Guias.setTipoSrv(consulta.getString("tipoSrv"));
                rt_Guias.setObserv(consulta.getString("Observ"));
            }
            consulta.Close();
            return rt_Guias;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.listaGuias - " + e.getMessage() + "\r\n"
                    + "SELECT EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, "
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, "
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, "
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, "
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, "
                    + "         CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, "
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER "
                    + " FROM Rt_Guias "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia "
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada "
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia "
                    + "                           AND percDst.Parada    = percOrigem.Dpar "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + (pedido ? " LEFT JOIN Pedido ON Pedido.numero = percOrigem.pedido "
                            + "      LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS "
                            + "                       AND OS_Vig.codfil = Pedido.CodFil "
                            : " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil "
                            + "                  AND OS_Vig.OS    = Rt_Guias.OS ")
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia "
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil "
                    + " WHERE percOrigem.Guia = " + guia + " AND percOrigem.Serie= " + serie);
        }
    }

    public Rt_Guias infoGuia(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT RtPRec.Sequencia, RtPRec.Parada, Rotas.Codfil, CONVERT(BigInt,Rt_Guias.Guia) Guia, Rt_Guias.Serie, \n"
                    + "Max(Escala.Veiculo) Veiculo, Max(Escala.Rota) Rota , Max(RtPRec.ER) ER, Max(RtpRec.TipoSrv) TipoSrv, Max(OS_Vig.OS) OS, \n"
                    + "Max(RtPRec.hrcheg) hora1, Max(RtPRec.hrsaida) hora2, Max(convert(DATE,RtPRec.Dt_Fech)) DtColeta, \n"
                    + " Max(filiais.razaoSocial) razaoSocial,  \n"
                    + " Max(filiais.endereco) enderecoFilial, \n"
                    + " Max(filiais.bairro) bairroFilial, \n"
                    + " Max(filiais.cidade) cidadeFilial, \n"
                    + " Max(filiais.UF) ufFilial, \n"
                    + " Max(filiais.CEP) cepFilial, \n"
                    + " Max(filiais.CNPJ) cnpjFilial, \n"
                    + " Max(filiais.fone) foneFilial, \n"
                    + "         Max(CliOri.Agencia) AgenciaOri, Max(CliOri.SubAgencia) SubAgenciaOri, Max(CliOri.NRed) NRedOri, Max(CliOri.Ende) EndOri, Max(CliOri.Cidade) CidadeOri, \n"
                    + "         Max(CliOri.Bairro) BairroOri, Max(CliOri.Estado) EstadoOri, Max(CliOri.email) EmailOri, Max(CliOri.Codigo) CodCliOri, Max(CliOri.Agencia) Agencia, \n"
                    + "MAX(CliOri.Nome) NomeOri, \n"
                    + "case when (MAX(CliOri.CGC) is null or MAX(CliOri.CGC) = '') then MAX(CliOri.CPF) else MAX(CliOri.CGC) end  RegistroOri, \n"
                    + "case when (MAX(CliDst.CGC) is null or MAX(CliDst.CGC) = '') then MAX(CliDst.CPF) else MAX(CliDst.CGC) end  RegistroDst, \n"
                    + "         Max(CliDst.Agencia) AgenciaDst, Max(CliDst.Nome) NomeDst, Max(CliDst.SubAgencia) SubAgenciaDst, Max(CliDst.NRed) NRedDst, Max(CliDst.Ende) EndDst, Max(CliDst.Cidade) CidadeDst, \n"
                    + "         Max(CliDst.Bairro) BairroDst, Max(CliDst.Estado) EstadoDst, Max(CliDst.email) EmailDst, Max(CliDst.Cep) cepDst, Max(CliDst.Codigo) CodCliDst, \n"
                    + "         Max(CliFat.NRed) NRedFat, Max(CliFat.Ende) EndFat, Max(CliFat.Cidade) CidadeFat, Max(CliFat.Bairro) BairroFat, Max(CliFat.Estado) EstadoFat, \n"
                    + "         Max(CliFat.email) EmailFat, Max(CliFat.CGC) CGCFat, Max(CliFat.IE) IEFat, Max(CliFat.Nome) NomeFat, Max(CliFat.Codigo) CodCliFat, \n"
                    + "         Max(RtPRec.Observ) ObsRec, Max(RtPEnt.Observ) ObsEnt, Max(convert(DATE,RtPEnt.Dt_Fech)) DtEntrega, Max(EscalaEnt.Veiculo) VeiculoEnt, \n"
                    + "         Max(EscalaEnt.Rota) RotaEnt, Max(RtPEnt.ER) EREnt, Max(RtpEnt.TipoSrv) TipoSrvEnt, Max(RtPEnt.hrcheg) hora1Ent, Max(RtPEnt.hrsaida) hora2Ent, \n"
                    + "         Max(COALESCE(RPV.Valor, Rt_Guias.Valor)) Valor, case when Max(PessoaRec.Matr) > 0 then 'Ch.Eq: '+Max(PessoaRec.Nome) else Max(PessoaRec.Nome) end NomeRec,\n"
                    + "         case when Max(PessoaEnt.Matr) > 0 then 'Ch.Eq: '+Max(PessoaEnt.Nome) else Max(PessoaEnt.Nome) end NomeEnt,\n"
                    + " XMLGTVE.ChaveGTVE ChaveGTVE, \n"
                    + " XMLGTVE.Protocolo ProtocoloGTVE, \n"
                    + " XMLGTVE.Link LinkGTVE, \n"
                    + " XMLGTVE.Dt_Retorno DataGTVE, \n"
                    + " XMLGTVE.Hr_Retorno HoraGTVE \n"
                    + " FROM Rt_Guias \n"
                    + " left join RPV on Rt_Guias.Guia = RPV.Guia\n"
                    + "                   and Rt_Guias.Serie = RPV.Serie\n"
                    + " LEFT JOIN XMLGTVE on Rt_Guias.Guia = XMLGTVE.Guia AND Rt_Guias.Serie = XMLGTVE.Serie \n"
                    + " left join PreOrder on  Preorder.Guia  = Rt_Guias.Guia\n"
                    + "                    and PreOrder.Serie = Rt_Guias.Serie\n"
                    + "left join Rt_Perc RtPRec on RtPRec.Sequencia = (Select top 1 RPVx.SeqRota from RPV RPVx \n"
                    + "                                                 left join Rt_Perc Rtp on Rtp.Sequencia = RPVX.SeqRota\n"
                    + "                                                                       and RtP.PArada = RPVx.Parada \n"
                    + "                                                 where RPVx.Guia = RPV.Guia and RPVx.Serie = RPV.Serie and ((RtP.ER = 'R') or (RtP.ER = 'E')) order by Data, Hora)\n"
                    + "                          and RtPRec.Parada   = (Select top 1 RPVx.Parada from RPV RPVx \n"
                    + "                                                 left join Rt_Perc Rtp on Rtp.Sequencia = RPVX.SeqRota\n"
                    + "                                                                      and RtP.PArada = RPVx.Parada \n"
                    + "                                                 where RPVx.Guia = RPV.Guia and RPVx.Serie = RPV.Serie and  ((RtP.ER = 'R') or (RtP.ER = 'E')) order by Data, Hora)\n"
                    + "left join Rt_Perc RtPEnt on RtPEnt.Sequencia = (Select top 1 RPVx.SeqRota from RPV RPVx \n"
                    + "                                          left join Rt_Perc Rtp on Rtp.Sequencia = RPVX.SeqRota\n"
                    + "                                                                and RtP.PArada = RPVx.Parada \n"
                    + "                                          where RPVx.Guia = RPV.Guia and RPVx.Serie = RPV.Serie and ((RtPRec.ER = 'R') or (RtPRec.ER = 'E')) order by Data Desc, Hora Desc)\n"
                    + "                         and RtPEnt.Parada   = (Select top 1 RPVx.Parada from RPV RPVx \n"
                    + "                                          left join Rt_Perc Rtp on Rtp.Sequencia = RPVX.SeqRota\n"
                    + "                                                                and RtP.PArada = RPVx.Parada \n"
                    + "                                          where RPVx.Guia = RPV.Guia and RPVx.Serie = RPV.Serie and ((RtPRec.ER = 'R') or (RtPRec.ER = 'E')) order by Data Desc, Hora Desc)\n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + " LEFT JOIN Escala Escala ON Escala.SeqRota = RtPRec.Sequencia \n"
                    + " LEFT JOIN Escala EscalaEnt ON EscalaEnt.SeqRota = RtPEnt.Sequencia \n"
                    + " left join CXFGuias on CxfGuias.Guia = Rt_Guias.Guia\n"
                    + "                    and CxfGuias.Serie = Rt_Guias.Serie\n"
                    + " LEFT JOIN OS_Vig ON OS_Vig.OS      = (Case when Rt_Guias.OS is not null then Rt_Guias.OS when PreOrder.OS is not null then PreOrder.OS \n"
                    + "                                            when CxfGuias.OS is not null then CxfGuias.OS else 0 end)\n"
                    + "                     AND OS_Vig.Codfil = Rotas.CodFil \n"
                    + " LEFT JOIN Clientes CliOri ON  CliOri.codigo = RtPRec.CodCli1\n"
                    + "                           AND CliOri.CodFil = OS_Vig.CodFil \n"
                    + "  LEFT JOIN Clientes CliDst ON  CliDst.Codigo = Case when (RtpEnt.CodCli1 is null and RtpRec.CodCli2 = '9990001') then OS_Vig.CliDst\n"
                    + " when OS_Vig.CliDst  = RtPEnt.CodCli2 then RtPEnt.CodCli1 \n"
                    + "                                                    when OS_Vig.Cliente = RtPEnt.CodCli1 then RtPEnt.CodCli1 else \n"
                    + "                                               Case when RtPEnt.CodCli1 is not null then OS_Vig.CliDst else OS_Vig.Cliente end end\n"
                    + "                           AND CliDst.CodFil = OS_Vig.CodFil  LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo \n"
                    + "                    AND OS_Vig.CodFil = CliFat.CodFil \n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + " left join Pessoa PessoaRec on PessoaRec.Codigo = (Select top 1 RPVx.CodPessoaAut from RPV RPVx \n"
                    + "                                                 left join Rt_Perc Rtp on Rtp.Sequencia = RPVX.SeqRota\n"
                    + "                                                                       and RtP.PArada = RPVx.Parada \n"
                    + "                                                 where RPVx.Guia = RPV.Guia and RPVx.Serie = RPV.Serie and RtP.ER = 'R' order by Data, Hora)\n"
                    + " left join Pessoa PessoaEnt on PessoaEnt.Codigo = (Select top 1 RPVx.CodPessoaAut from RPV RPVx \n"
                    + "                                                 left join Rt_Perc Rtp on Rtp.Sequencia = RPVX.SeqRota\n"
                    + "                                                                       and RtP.PArada = RPVx.Parada \n"
                    + "                                                 where RPVx.Guia = RPV.Guia and RPVx.Serie = RPV.Serie and RtP.ER = 'E' order by Data Desc, Hora Desc)\n"
                    + "\n"
                    + " WHERE Rt_Guias.guia = ? and Rt_Guias.serie = ?\n"
                    + "   and (RtPEnt.TipoSrv <> 'A' OR RtPEnt.TipoSrv IS NULL) \n"
                    + " GROUP BY RtPRec.Sequencia, RtPRec.Parada, Rotas.Codfil, Rt_Guias.Guia, Rt_Guias.Serie,XMLGTVE.ChaveGTVE,XMLGTVE.Protocolo,XMLGTVE.Link,XMLGTVE.Dt_Retorno,XMLGTVE.Hr_Retorno\n"
                    + " ORDER BY Rt_Guias.guia, Rt_Guias.Serie;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            Rt_Guias rt_Guias = new Rt_Guias();
            if (consulta.Proximo()) {
                rt_Guias.setCodFil(consulta.getString("Codfil"));
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getInt("parada"));
//                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
//                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
//                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
//                rt_Guias.setKM(consulta.getString("km"));
//                rt_Guias.setKMTerra(consulta.getString("KMterra"));
//                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setRazaoSocial(consulta.getString("razaoSocial"));
                rt_Guias.setEnderecoFilial(consulta.getString("enderecoFilial"));
                rt_Guias.setBairroFilial(consulta.getString("bairroFilial"));
                rt_Guias.setCidadeFilial(consulta.getString("cidadeFilial"));
                rt_Guias.setUfFilial(consulta.getString("ufFilial"));
                rt_Guias.setCepFilial(consulta.getString("cepFilial"));
                rt_Guias.setCnpjFilial(consulta.getString("cnpjFilial"));
                rt_Guias.setFoneFilial(consulta.getString("foneFilial"));

                rt_Guias.setAgenciaOri(consulta.getString("AgenciaOri"));
                rt_Guias.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setAgencia(consulta.getString("Agencia"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setNomeOri(consulta.getString("NomeOri"));
                rt_Guias.setRegistroOri(consulta.getString("RegistroOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setVeiculoOri(consulta.getString("veiculo"));
                rt_Guias.setRotaOri(consulta.getString("rota"));
                rt_Guias.setColetaOri(consulta.getString("DtColeta"));

                rt_Guias.setAgenciaDst(consulta.getString("AgenciaDst"));
                rt_Guias.setSubAgenciaDst(consulta.getString("SubAgenciaDst"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setNomeDst(consulta.getString("NomeDst"));
                rt_Guias.setRegistroDst(consulta.getString("RegistroDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setVeiculoDst(consulta.getString("veiculoEnt"));
                rt_Guias.setRotaDst(consulta.getString("rotaEnt"));
                rt_Guias.setColetaDst(consulta.getString("DtEntrega"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setNomeFat(consulta.getString("NomeFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                rt_Guias.setHoraChegada(consulta.getString("hora1Ent"));
                rt_Guias.setHoraSaida(consulta.getString("hora2Ent"));
                rt_Guias.setHora1(consulta.getString("hora1"));
                rt_Guias.setHora2(consulta.getString("hora2"));

                rt_Guias.setEr(consulta.getString("er"));
                rt_Guias.setTipoSrv(consulta.getString("tipoSrv"));
                rt_Guias.setObserv(consulta.getString("ObsRec") + "\n" + consulta.getString("ObsEnt"));

                rt_Guias.setGTVeChave(consulta.getString("ChaveGTVE"));
                rt_Guias.setGTVeLink(consulta.getString("LinkGTVE"));
                rt_Guias.setGTVeProtocolo(consulta.getString("ProtocoloGTVE"));

                rt_Guias.setGTVeData(consulta.getString("DataGTVE"));
                rt_Guias.setGTVeHora(consulta.getString("HoraGTVE"));
            }
            consulta.Close();
            return rt_Guias;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.listaGuias - " + e.getMessage() + "\r\n"
                    + "SELECT EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, "
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, "
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, "
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, "
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, "
                    + "         CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, "
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER "
                    + " FROM Rt_Guias "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia "
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada "
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia "
                    + "                           AND percDst.Parada    = percOrigem.Dpar "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia "
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil "
                    + " WHERE percOrigem.Guia = " + guia + " AND percOrigem.Serie= " + serie);
        }
    }

    public String obterValorTotal(String seqRota, String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Sum(Valor) TotalGeral from Rt_guias \n"
                    + " where Sequencia = ? \n"
                    + "    and Guia  = ? \n"
                    + "   and Serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            String valor = null;
            if (consulta.Proximo()) {
                valor = consulta.getString("TotalGeral");
            }
            consulta.Close();
            return valor;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.obterValorTotal - " + e.getMessage() + "\r\n"
                    + "Select Sum(Valor) TotalGeral from Rt_guias \n"
                    + " where Sequencia = " + seqRota + " \n"
                    + "    and Guia  = " + guia + " \n"
                    + "   and Serie = " + serie);
        }
    }

    public int contagemGuiaSerie(String guia, String serie, Persistencia persistencia) throws Exception {
        String sql = "SELECT COUNT(*) AS total\n FROM (\n"
                + "    SELECT Guia, Serie FROM Rt_Guias\n"
                + "    WHERE Guia  = ? \n"
                + "    AND Serie = ? \n"
                + ");";

        int total = 0;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            if (consulta.Proximo()) {
                total = consulta.getInt("total");
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return total;
    }

    public BigDecimal getTotalGeral(String guia, String serie, Persistencia persistencia) throws Exception {
        String sql = "SELECT TOP 1 Sum(Valor) TotalGeral\n"
                + "FROM Rt_guias\n"
                + "WHERE Guia = ? \n"
                + "  AND Serie = ? ;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);

            consulta.select();
            if (consulta.Proximo()) {
                return consulta.getBigDecimal("TotalValor");
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return null;
    }

    public Rt_Guias consultaGuiaSerieAssina(String guia, String serie, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            Rt_Guias retorno = new Rt_Guias();

            sql = "Select \n"
                    + "Rotas.Data,\n"
                    + "Rt_Perc.hrCheg,\n"
                    + "Rt_Perc.Parada,\n"
                    + "Rt_Perc.Sequencia,\n"
                    + "Rt_Perc.HrSaida,\n"
                    + "Rt_guias.Guia, \n"
                    + "Rt_guias.Serie, \n"
                    + "Rt_Perc.ER, \n"
                    + "Rt_Perc.TipoSrv, \n"
                    + "Rt_guias.OS, \n"
                    + "Rt_guias.Valor, \n"
                    + "CliOri.Nred NRedOri, \n"
                    + "CliOri.Ende+' - '+CliOri.Bairro+' - '+CliOri.Cidade+'/'+CliOri.Estado EndOri, \n"
                    + "CliDst.Nred NRedDst, CliDst.Ende+' - '+CliDst.Bairro+' - '+CliDst.Cidade+'/'+CliDst.Estado endDst, \n"
                    + "PessoaOri.Nome NomeOri, \n"
                    + "PessoaOri.Matr MatrOri, \n"
                    + "Pessoadst.Nome NomeDst, \n"
                    + "PessoaDst.Matr MatrDst, \n"
                    + "Rt_guias.operador \n"
                    + "From Rt_Guias\n"
                    + "left join Rotas   on  Rotas.Sequencia   = Rt_guias.Sequencia\n"
                    + "left join Rt_Perc on  Rt_Perc.Sequencia = Rt_guias.Sequencia\n"
                    + "                  and Rt_Perc.Parada    = Rt_guias.Parada \n"
                    + "left join OS_Vig  on  OS_Vig.OS         = Rt_Guias.OS\n"
                    + "                  and OS_Vig.Codfil     = Rotas.CodFil\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = (Case when Rt_Perc.ER like 'E%' then OS_Vig.CliDst else OS_Vig.Cliente end)\n"
                    + "                          and CliOri.CodFil = Rotas.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = (Case when Rt_Perc.ER like 'E%' then OS_Vig.Cliente else OS_Vig.CliDst end)\n"
                    + "                          and CliDst.CodFil = Rotas.CodFil\n"
                    + "left join Pessoa PessoaOri on PessoaOri.Codigo = (Select top 1 CodPessoaAut from RPV \n"
                    + "                                                  left join Rt_Perc RtP on  RtP.Sequencia = RPV.SeqRota \n"
                    + "                                                                        and RtP.Parada    = RPV.Parada\n"
                    + "                                                  where RPV.Guia  = Rt_Guias.Guia \n"
                    + "                                                    and RPV.Serie = Rt_Guias.Serie \n"
                    + "                                                    and RtP.ER Like 'R%') \n"
                    + "left join Pessoa PessoaDst on PessoaDst.Codigo = (Select top 1 CodPessoaAut from RPV \n"
                    + "                                                  left join Rt_Perc RtP on  RtP.Sequencia = RPV.SeqRota \n"
                    + "                                                                        and RtP.Parada    = RPV.Parada\n"
                    + "                                                  where RPV.Guia  = Rt_Guias.Guia \n"
                    + "                                                    and RPV.Serie = Rt_Guias.Serie \n"
                    + "                                                    and RtP.ER Like 'E%') \n"
                    + "\n"
                    + "where Rt_guias.Guia   = ?\n"
                    + "AND   Rt_guias.serie  = ?\n"
                    //+ "AND   Rt_guias.codFil = ?\n"
                    + "order by Rotas.Sequencia, Rt_perc.Parada";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            //consulta.setString(codFil);
            consulta.select();

            while (consulta.Proximo()) {
                retorno.setParada(consulta.getInt("parada"));
                retorno.setSequencia(consulta.getString("Sequencia"));
                retorno.setAssRemetente(consulta.getString("NomeOri"));
                retorno.setAssDestinatario(consulta.getString("NomeDst"));
                retorno.setOS(consulta.getString("OS"));
                retorno.setValor(consulta.getString("Valor"));
                retorno.setEr(consulta.getString("ER"));
                retorno.setOperador(consulta.getString("operador"));
                retorno.setGuia(consulta.getString("Guia"));
                retorno.setGuiaString(consulta.getString("Guia"));
                retorno.setSerie(consulta.getString("Serie"));
                retorno.setTipoSrv(consulta.getString("TipoSrv"));
            }

            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.consultaGuiaSerieAssina - " + e.getMessage() + "\r\n" + sql);
        }
    }
    
    public String buscarEmailClienteOriGuia(String guia, String serie, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            String retorno = "";

            sql = "select cli.email from RPV \n" +
"                    left join Rt_Perc on  Rt_perc.Sequencia = RPV.SeqRota \n" +
"                                    and Rt_Perc.Parada    = RPV.Parada \n" +
"                    left join Rt_Guias  on Rt_Guias.Guia    = RPV.Guia \n" +
"                                     and Rt_Guias.Serie   = RPV.Serie \n" +
"                    left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia \n" +
"                    left join Pessoa on Pessoa.Codigo = RPV.CodPessoaAut \n" +
"                    left join OS_Vig on OS_VIg.OS = Rt_Guias.OS \n" +
"                                    and OS_Vig.codfil = Rotas.codfil \n" +
"                    left join clientes cliori on cliori.codigo = os_vig.cliente left join clientes cli on cli.codigo = Rt_Perc.CodCli1 "
                    + " where RPV.Guia = ? AND RPV.Serie = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);

            consulta.select();

            while (consulta.Proximo()) {
                retorno = consulta.getString("Email");
            }

            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.consultaGuiaSerieAssina - " + e.getMessage() + "\r\n" + sql);
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueueFechSolicitacaoSenhaDTO implements Serializable {

    private String Rota;
    private String Parada;
    private String Sequencia;
    private String CodFil;
    private String TipoOperacao;
    private String LocalSolic;
    private String HrSolic;
    private String ChEquipe;
    private String ChaveSrv;
    private String CodPessoa;
    private String FechIdentif;
    private String Comando_Ref;
    private String Senha;
    private String Data;
    private String SrvOK;
    private String PW;
    private String MatrChe;
    private String PWVig1;
    private String Comando_Crt;
    private String MatrVig1;
    private String SeqRota;
    private String Veiculo;
    private String Latitude;
    private String Longitude;
    private String CodCli1;
    private String Hora1;
    private String Placa;
    private String Comando_Aceito;
    private String TpVeic;
    private String TipoFechVei;
    private String TipoFechCli;
    private String StatusFechCli;
    private String StatusFechVei;
    private String CodigoFechCli;
    private String CodigoFechVei;
    private String SenhaUsr1FechCli;
    private String SenhaUsr1FechVei;
    private String MACFechCli;
    private String MACFechVei;
    private String Cheque;
    private String ClienteLatitude;
    private String ClienteLongitude;
    private String ClienteCercaElet;
    private String distanciaAteCliente;

    public QueueFechSolicitacaoSenhaDTO() {
    }

    public QueueFechSolicitacaoSenhaDTO(QueueFechSolicitacaoSenhaDTO original) {
        Rota = original.getRota();
        Parada = original.getParada();
        Sequencia = original.getSequencia();
        CodFil = original.getCodFil();
        TipoOperacao = original.getTipoOperacao();
        LocalSolic = original.getLocalSolic();
        HrSolic = original.getHrSolic();
        ChEquipe = original.getChEquipe();
        ChaveSrv = original.getChaveSrv();
        CodPessoa = original.getCodPessoa();
        FechIdentif = original.getFechIdentif();
        Comando_Ref = original.getComando_Ref();
        Senha = original.getSenha();
        Data = original.getData();
        SrvOK = original.getSrvOK();
        PW = original.getPW();
        MatrChe = original.getMatrChe();
        PWVig1 = original.getPWVig1();
        Comando_Crt = original.getComando_Crt();
        MatrVig1 = original.getMatrVig1();
        SeqRota = original.getSeqRota();
        Veiculo = original.getVeiculo();
        Latitude = original.getLatitude();
        Longitude = original.getLongitude();
        CodCli1 = original.getCodCli1();
        Hora1 = original.getHora1();
        Placa = original.getPlaca();
        Comando_Aceito = original.getComando_Aceito();
        TpVeic = original.getTpVeic();
        TipoFechVei = original.getTipoFechVei();
        TipoFechCli = original.getTipoFechCli();
        StatusFechCli = original.getStatusFechCli();
        StatusFechVei = original.getStatusFechVei();
        CodigoFechCli = original.getCodigoFechCli();
        CodigoFechVei = original.getCodigoFechVei();
        SenhaUsr1FechCli = original.getSenhaUsr1FechCli();
        SenhaUsr1FechVei = original.getSenhaUsr1FechVei();
        MACFechCli = original.getMACFechCli();
        MACFechVei = original.getMACFechVei();
        Cheque = original.getCheque();
        ClienteLatitude = original.getClienteLatitude();
        ClienteLongitude = original.getClienteLongitude();
        ClienteCercaElet = original.getClienteCercaElet();
        distanciaAteCliente = original.getDistanciaAteCliente();
    }

    public boolean coordenadasExistem() {
        return Latitude != null && !Latitude.equals("")
                && Longitude != null && !Longitude.equals("");
    }

    public boolean coordenadasClienteExistem() {
        return ClienteLatitude != null && !ClienteLatitude.equals("")
                && ClienteLongitude != null && !ClienteLongitude.equals("");
    }

    public boolean isVeiculo() {
        return ChaveSrv.equals("VEICULO");
    }

    public String getTipoFech() {
        return isVeiculo() ? TipoFechVei : TipoFechCli;
    }

    public void setTipoFech(String tipoFech) {
        if (isVeiculo()) {
            this.TipoFechVei = tipoFech;
        } else {
            this.TipoFechCli = tipoFech;
        }
    }

    public String getMACFechadura() {
        return isVeiculo() ? MACFechVei : MACFechCli;
    }

    public String getSenhaUsr1() {
        return isVeiculo() ? SenhaUsr1FechVei : SenhaUsr1FechCli;
    }

    public String getCodFechadura() {
        return isVeiculo() ? CodigoFechVei : CodigoFechCli;
    }

    public void setCodFechadura(String codFechadura) {
        if (isVeiculo()) {
            this.CodigoFechVei = codFechadura;
        } else {
            this.CodigoFechCli = codFechadura;
        }
    }

    public boolean isAtivo() {
        return (isVeiculo() ? StatusFechVei : StatusFechCli).equals("A");
    }

    // getters & setters
    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getParada() {
        return Parada;
    }

    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getTipoOperacao() {
        return TipoOperacao;
    }

    public void setTipoOperacao(String TipoOperacao) {
        this.TipoOperacao = TipoOperacao;
    }

    public String getLocalSolic() {
        return LocalSolic;
    }

    public void setLocalSolic(String LocalSolic) {
        this.LocalSolic = LocalSolic;
    }

    public String getHrSolic() {
        return HrSolic;
    }

    public void setHrSolic(String HrSolic) {
        this.HrSolic = HrSolic;
    }

    public String getChEquipe() {
        return ChEquipe;
    }

    public void setChEquipe(String ChEquipe) {
        this.ChEquipe = ChEquipe;
    }

    public String getChaveSrv() {
        return ChaveSrv;
    }

    public void setChaveSrv(String ChaveSrv) {
        this.ChaveSrv = ChaveSrv;
    }

    public String getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getFechIdentif() {
        return FechIdentif;
    }

    public void setFechIdentif(String FechIdentif) {
        this.FechIdentif = FechIdentif;
    }

    public String getComando_Ref() {
        return Comando_Ref;
    }

    public void setComando_Ref(String Comando_Ref) {
        this.Comando_Ref = Comando_Ref;
    }

    public String getSenha() {
        return Senha;
    }

    public void setSenha(String Senha) {
        this.Senha = Senha;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getSrvOK() {
        return SrvOK;
    }

    public void setSrvOK(String SrvOK) {
        this.SrvOK = SrvOK;
    }

    public String getPW() {
        return PW;
    }

    public void setPW(String PW) {
        this.PW = PW;
    }

    public String getMatrChe() {
        return MatrChe;
    }

    public void setMatrChe(String MatrChe) {
        this.MatrChe = MatrChe;
    }

    public String getPWVig1() {
        return PWVig1;
    }

    public void setPWVig1(String PWVig1) {
        this.PWVig1 = PWVig1;
    }

    public String getComando_Crt() {
        return Comando_Crt;
    }

    public void setComando_Crt(String Comando_Crt) {
        this.Comando_Crt = Comando_Crt;
    }

    public String getMatrVig1() {
        return MatrVig1;
    }

    public void setMatrVig1(String MatrVig1) {
        this.MatrVig1 = MatrVig1;
    }

    public String getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(String SeqRota) {
        this.SeqRota = SeqRota;
    }

    public String getVeiculo() {
        return Veiculo;
    }

    public void setVeiculo(String Veiculo) {
        this.Veiculo = Veiculo;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getPlaca() {
        return Placa;
    }

    public void setPlaca(String Placa) {
        this.Placa = Placa;
    }

    public String getComando_Aceito() {
        return Comando_Aceito;
    }

    public void setComando_Aceito(String Comando_Aceito) {
        this.Comando_Aceito = Comando_Aceito;
    }

    public String getTpVeic() {
        return TpVeic;
    }

    public void setTpVeic(String TpVeic) {
        this.TpVeic = TpVeic;
    }

    public String getTipoFechVei() {
        return TipoFechVei;
    }

    public void setTipoFechVei(String TipoFechVei) {
        this.TipoFechVei = TipoFechVei;
    }

    public String getTipoFechCli() {
        return TipoFechCli;
    }

    public void setTipoFechCli(String TipoFechCli) {
        this.TipoFechCli = TipoFechCli;
    }

    public String getStatusFechCli() {
        return StatusFechCli;
    }

    public void setStatusFechCli(String StatusFechCli) {
        this.StatusFechCli = StatusFechCli;
    }

    public String getStatusFechVei() {
        return StatusFechVei;
    }

    public void setStatusFechVei(String StatusFechVei) {
        this.StatusFechVei = StatusFechVei;
    }

    public String getCodigoFechCli() {
        return CodigoFechCli;
    }

    public void setCodigoFechCli(String CodigoFechCli) {
        this.CodigoFechCli = CodigoFechCli;
    }

    public String getCodigoFechVei() {
        return CodigoFechVei;
    }

    public void setCodigoFechVei(String CodigoFechVei) {
        this.CodigoFechVei = CodigoFechVei;
    }

    public String getSenhaUsr1FechCli() {
        return SenhaUsr1FechCli;
    }

    public void setSenhaUsr1FechCli(String SenhaUsr1FechCli) {
        this.SenhaUsr1FechCli = SenhaUsr1FechCli;
    }

    public String getSenhaUsr1FechVei() {
        return SenhaUsr1FechVei;
    }

    public void setSenhaUsr1FechVei(String SenhaUsr1FechVei) {
        this.SenhaUsr1FechVei = SenhaUsr1FechVei;
    }

    public String getMACFechCli() {
        return MACFechCli;
    }

    public void setMACFechCli(String MACFechCli) {
        this.MACFechCli = MACFechCli;
    }

    public String getMACFechVei() {
        return MACFechVei;
    }

    public void setMACFechVei(String MACFechVei) {
        this.MACFechVei = MACFechVei;
    }

    public String getCheque() {
        return Cheque;
    }

    public void setCheque(String Cheque) {
        this.Cheque = Cheque;
    }

    public String getClienteLatitude() {
        return ClienteLatitude;
    }

    public void setClienteLatitude(String ClienteLatitude) {
        this.ClienteLatitude = ClienteLatitude;
    }

    public String getClienteLongitude() {
        return ClienteLongitude;
    }

    public void setClienteLongitude(String ClienteLongitude) {
        this.ClienteLongitude = ClienteLongitude;
    }

    public String getClienteCercaElet() {
        return ClienteCercaElet;
    }

    public void setClienteCercaElet(String ClienteCercaElet) {
        this.ClienteCercaElet = ClienteCercaElet;
    }

    public String getDistanciaAteCliente() {
        return distanciaAteCliente;
    }

    public void setDistanciaAteCliente(String distanciaAteCliente) {
        this.distanciaAteCliente = distanciaAteCliente;
    }

}

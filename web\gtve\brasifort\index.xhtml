<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      style="overflow:hidden !important; max-height:100% !important;"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <title>#{localemsgs.SatMOB}</title>
            <meta charset="UTF-8" />
            <link rel="icon" href="../../assets/img/favicon.png" />
            <meta name="viewport" content="width=device-width, initial-scale=1"/>
            <!--===============================================================================================-->
            <link rel="stylesheet" type="text/css" href="vendor/bootstrap/css/bootstrap.min.css"/>
            <!--===============================================================================================-->
            <link rel="stylesheet" type="text/css" href="fonts/font-awesome-4.7.0/css/font-awesome.min.css"/>
            <!--===============================================================================================-->
            <link rel="stylesheet" type="text/css" href="fonts/iconic/css/material-design-iconic-font.min.css"/>
            <!--===============================================================================================-->
            <link rel="stylesheet" type="text/css" href="css/util.css"/>
            <link rel="stylesheet" type="text/css" href="css/main.css"/>
            <!--===============================================================================================-->
            <style>
                #j_idt14{
                    top: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    left: 0 !important;
                    height: 95px !important;
                    margin: auto !important;
                }

            </style>
        </h:head>
        <h:body id="h">
            <div class="limiter">
                <div class="container-login100" style="background-image: url('images/bg-01.jpg');">
                    <div class="wrap-login100">

                        <div class="center">
                            <img src="images/logo.png"/>
                            <p:growl id="msgs" widgetVar="msgs" />

                            <h:form class="form-inline" id="login" style="overflow:hidden !important;">

                                <div class="wrap-input100 validate-input" data-validate = "Enter username">
                                    <h:outputText value="#{localemsgs.Chave}" style="font-size: 12pt !important; color: #FFF !important;"/>
                                    <p:inputText id="email" value="#{login.email}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Chave}"
                                                 style="width: 100%; height: 40px;font-size: 10pt !important"/>
                                </div>

                                <div class="wrap-input100 validate-input" data-validate="Enter password">
                                    <h:outputText value="#{localemsgs.Senha}" style="font-size: 12pt !important; color: #FFF !important;"/>
                                    <p:password id="senha" value="#{login.senhaDia}"
                                                required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                                style="width: 100%; height: 40px;font-size: 10pt !important"/>
                                </div>

                                <div class="container-login100-form-btn">
                                    <p:commandButton class="login100-form-btn" value="#{localemsgs.Entrar}" id="btnLogar" 
                                                     action="#{login.LogarGTVePortalExclusivo('brasifort')}" style="width: 100%; border-radius: 50px; color: #000 !important"
                                                     update="msgs"/>
                                </div>

                                <div class="col-md-12 text-center" style="margin-top: 30px">
                                    <center><img src="images/logosatmob.png" width="150" /></center>
                                </div>
                            </h:form>
                        </div>
                    </div>
                </div>

            </div>

            <!--===============================================================================================-->
            <script src="vendor/jquery/jquery-3.2.1.min.js"></script>
            <!--===============================================================================================-->
            <script src="vendor/animsition/js/animsition.min.js"></script>
            <!--===============================================================================================-->
            <script src="vendor/bootstrap/js/popper.js"></script>
            <script src="vendor/bootstrap/js/bootstrap.min.js"></script>
            <!--===============================================================================================-->
            <script src="vendor/select2/select2.min.js"></script>
            <!--===============================================================================================-->
            <script src="vendor/daterangepicker/moment.min.js"></script>
            <script src="vendor/daterangepicker/daterangepicker.js"></script>
            <!--===============================================================================================-->
            <script src="vendor/countdowntime/countdowntime.js"></script>
            <!--===============================================================================================-->
            <script src="js/main.js"></script>

            <ui:insert name="loading" class="loadingPortal">
                <ui:include src="../../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>
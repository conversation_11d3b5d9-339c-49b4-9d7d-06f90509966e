/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.Remessas;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RemessasDao {

    public List<Remessas> obterRemessas(String SeqRota, String CodFil, Persistencia persistencia) throws Exception {
        try {
            List<Remessas> remessas = new ArrayList<>();
            String sql = "Select Pessoa.Nome Oper_Prep, Clientes.Codigo CodCli, Clientes.NRed Nome, Clientes.Ende Endereco, Rotas.Rota, Rotas.Sequencia, Clientes.NroChave, \n"
                    + "CONVERT(VARCHAR, Rotas.Data, 112) Data, Rt_Perc.ER, Rt_Perc.CODCLI1,Rt_Perc.Hora1, Rt_Perc.<PERSON>da, Rt_Perc.Dpar, \n"
                    + "Rt_Perc.CODCLI2, Rt_Perc.Hora1D, Rt_Perc.TipoSrv, Rt_Perc.Chave , Rt_Perc.Regiao, \n"
                    + "Rt_Perc.Observ, CxFGuias.Valor, CONVERT(BIGINT,CxFGuias.Guia) Guia, CxFGuias.Serie, Escala.MatrChe, Funcion.Nome NomeChe, \n"
                    + " Escala.Veiculo, CxFSaidas.CodRemessa, CxfGuiasVol.Lacre, ISNULL(CxfGuiasVol.Qtde,0) Qtde, CxFGuias.Remessa,\n"
                    + "Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "                    		 else Paramet.MoedaPdrMobile end Moeda\n"
                    + "from Rt_Perc\n"
                    + " left join Rotas     on  Rotas.Sequencia     = Rt_Perc.Sequencia \n"
                    + " left join Clientes  on  Clientes.Codigo     = Rt_Perc.CodCli1   \n"
                    + "                     and Clientes.CodFil     = Rotas.CodFil      \n"
                    + " left Join CxFGuias  on  CxFGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                     and CxfGuias.Hora1D     = Rt_Perc.Hora1     \n"
                    + " left join Escala    on  Escala.SeqRota      = Rotas.Sequencia   \n"
                    + " left Join Funcion   on  Funcion.Matr        = Escala.MatrChe    \n"
                    + " left Join CxFSaidas on  CxFSaidas.CodFil    = Rotas.CodFil      \n"
                    + "                     and CxFSaidas.SeqRota   = Rotas.Sequencia   \n"
                    + "                     and CxFSaidas.Remessa   = CxFGuias.Remessa  \n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = CxFGuias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = CxFGuias.Serie\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = CxFGuias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = CxFGuias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = CxFGuias.SeqRotaSai\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "LEFT JOIN SasPW ON SasPW.Nome = CxFSaidas.Oper_Prep\n"
                    + "LEFT JOIN Pessoa ON Pessoa.Codigo = SasPW.CodPessoa\n"
                    + " where Rotas.Sequencia = ?\n"
                    + "   and Rotas.CodFil = ?\n"
                    + "   and Rt_Perc.ER like 'E%'\n"
                    + "   and Rt_Perc.Flag_Excl <> '*'\n"
                    + "   and CxFGuias.Remessa is not null \n"
                    + " order by Rotas.Rota, Rt_Perc.Hora1;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(SeqRota);
            consulta.setString(CodFil);
            consulta.select();
            Remessas remessa;
            while (consulta.Proximo()) {
                remessa = new Remessas();
                remessa.setCodCli(consulta.getString("CodCli"));
                remessa.setNome(consulta.getString("Nome"));
                remessa.setEndereco(consulta.getString("Endereco"));
                remessa.setRota(consulta.getString("Rota"));
                remessa.setSequencia(consulta.getString("Sequencia"));
                remessa.setNroChave(consulta.getString("NroChave"));
                remessa.setData(consulta.getString("Data"));
                remessa.setER(consulta.getString("ER"));
                remessa.setCodCli1(consulta.getString("CodCli1"));
                remessa.setHora1(consulta.getString("Hora1"));
                remessa.setParada(consulta.getString("Parada"));
                remessa.setDpar(consulta.getString("Dpar"));
                remessa.setCodCli2(consulta.getString("CodCli2"));
                remessa.setHora1D(consulta.getString("Hora1D"));
                remessa.setTipoSrv(consulta.getString("TipoSrv"));
                remessa.setChave(consulta.getString("Chave"));
                remessa.setRegiao(consulta.getString("Regiao"));
                remessa.setObserv(consulta.getString("Observ"));
                remessa.setValor(consulta.getString("Valor"));
                remessa.setMoeda(consulta.getString("Moeda"));
                remessa.setGuia(consulta.getString("Guia"));
                remessa.setSerie(consulta.getString("Serie"));
                remessa.setMatrChe(consulta.getString("MatrChe"));
                remessa.setNomeChe(consulta.getString("NomeChe"));
                remessa.setVeiculo(consulta.getString("Veiculo"));
                remessa.setCodRemessa(consulta.getString("CodRemessa"));
                remessa.setLacre(consulta.getString("Lacre"));
                remessa.setQtde(consulta.getString("Qtde"));
                remessa.setRemessa(consulta.getString("Remessa"));
                remessa.setOper_Prep(consulta.getString("Oper_Prep"));
                remessas.add(remessa);
            }
            consulta.close();
            return remessas;
        } catch (Exception e) {
            throw new Exception("RemessasDao.obterRemessas - " + e.getMessage() + "\r\n"
                    + "Select Pessoa.Nome Oper_Prep, Clientes.Codigo CodCli, Clientes.NRed Nome, Clientes.Ende Endereco, Rotas.Rota, Rotas.Sequencia, Clientes.NroChave, \n"
                    + "CONVERT(VARCHAR, Rotas.Data, 112) Data, Rt_Perc.ER, Rt_Perc.CODCLI1,Rt_Perc.Hora1, Rt_Perc.Parada, Rt_Perc.Dpar, \n"
                    + "Rt_Perc.CODCLI2, Rt_Perc.Hora1D, Rt_Perc.TipoSrv, Rt_Perc.Chave , Rt_Perc.Regiao, \n"
                    + "Rt_Perc.Observ, CxFGuias.Valor, CONVERT(BIGINT,CxFGuias.Guia) Guia, CxFGuias.Serie, Escala.MatrChe, Funcion.Nome NomeChe, \n"
                    + " Escala.Veiculo, CxFSaidas.CodRemessa, CxfGuiasVol.Lacre, ISNULL(CxfGuiasVol.Qtde,0) Qtde, CxFGuias.Remessa,\n"
                    + "Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "                    		 else Paramet.MoedaPdrMobile end Moeda\n"
                    + "from Rt_Perc\n"
                    + " left join Rotas     on  Rotas.Sequencia     = Rt_Perc.Sequencia \n"
                    + " left join Clientes  on  Clientes.Codigo     = Rt_Perc.CodCli1   \n"
                    + "                     and Clientes.CodFil     = Rotas.CodFil      \n"
                    + " left Join CxFGuias  on  CxFGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                     and CxfGuias.Hora1D     = Rt_Perc.Hora1     \n"
                    + " left join Escala    on  Escala.SeqRota      = Rotas.Sequencia   \n"
                    + " left Join Funcion   on  Funcion.Matr        = Escala.MatrChe    \n"
                    + " left Join CxFSaidas on  CxFSaidas.CodFil    = Rotas.CodFil      \n"
                    + "                     and CxFSaidas.SeqRota   = Rotas.Sequencia   \n"
                    + "                     and CxFSaidas.Remessa   = CxFGuias.Remessa  \n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = CxFGuias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = CxFGuias.Serie\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = CxFGuias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = CxFGuias.Serie\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + " where Rotas.Sequencia = " + SeqRota + "\n"
                    + "   and Rotas.CodFil = " + CodFil + "\n"
                    + "   and Rt_Perc.ER like 'E%'\n"
                    + "   and Rt_Perc.Flag_Excl <> '*'\n"
                    + "   and CxFGuias.Remessa is not null \n"
                    + " order by Rotas.Rota, Rt_Perc.Hora1;");
        }
    }
}

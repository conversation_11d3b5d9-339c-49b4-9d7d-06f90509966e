/*
 */
package br.com.sasw.pacotesuteis.utilidades;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import javax.net.ssl.HttpsURLConnection;

/**
 *
 * <AUTHOR>
 */
public class BuscarEndereco {

    /**
     * Faz a busca de um endereço a partir de um CEP
     *
     * @param cep - string contendo apenas os algarimos do cep, sem . e -
     * @return objeto JSON convertido para string contendo campos de um
     * endereço, como cidade, bairro, logradouro, estado, etc
     * @throws Exception
     */
    public String BuscarPeloCEP(String cep) throws Exception {
        String urlString = "http://cep.republicavirtual.com.br/web_cep.php?cep=" + cep + "&formato=json";
        StringBuffer newData = new StringBuffer();
        try {
            URL url = new URL(urlString);
            // cria o objeto httpurlconnection
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // seta o metodo
            connection.setRequestProperty("Request-Method", "GET");

            // seta a variavel para ler o resultado
            connection.setDoInput(true);
            connection.setDoOutput(false);

            // conecta com a url destino
            connection.connect();

            // abre a conexão pra input
            BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            // le ate o final
            String s = "";
            while (null != ((s = br.readLine()))) {
                newData.append(s);
            }
            br.close();
        } catch (Exception e) {
            throw new Exception("endereco.falhageral<message>" + e.getMessage());
        }
        return newData.toString();
    }

    /**
     * Faz a busca da latitude/longitude a partir de um endereço
     *
     * @param endereco
     * @return objeto JSON
     * @throws Exception
     */
    public static String BuscarLatLon(String endereco) throws Exception {
        String urlString = "https://maps.googleapis.com/maps/api/geocode/json?address="
                + URLEncoder.encode(endereco, Charset.forName("UTF-8").toString())
                + "&key=AIzaSyA3fIxeYPRGxBm_EVlNGImhZcDx1WvYe_w";
        StringBuilder newData = new StringBuilder();
        try {
            URL url = new URL(urlString);
            // cria o objeto httpurlconnection
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();

            // seta o metodo
            connection.setRequestProperty("Request-Method", "GET");

            // seta a variavel para ler o resultado
            connection.setDoInput(true);
            connection.setDoOutput(false);

            // conecta com a url destino
            connection.connect();

            // abre a conexão pra input
            BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), Charset.forName("UTF-8")));

            // le ate o final
            String s = "";
            while (null != ((s = br.readLine()))) {
                newData.append(s);
            }
            br.close();
        } catch (IOException e) {
            throw new Exception("endereco.falhageral<message>" + e.getMessage());
        }
        return newData.toString();
    }
    
    public static String BuscarLatLon(String endereco, String chaveGoogle) throws Exception {
        String urlString = "https://maps.googleapis.com/maps/api/geocode/json?address="
                + URLEncoder.encode(endereco, Charset.forName("UTF-8").toString())
                + "&key=" + chaveGoogle;
        StringBuilder newData = new StringBuilder();
        try {
            URL url = new URL(urlString);
            // cria o objeto httpurlconnection
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();

            // seta o metodo
            connection.setRequestProperty("Request-Method", "GET");

            // seta a variavel para ler o resultado
            connection.setDoInput(true);
            connection.setDoOutput(false);

            // conecta com a url destino
            connection.connect();

            // abre a conexão pra input
            BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), Charset.forName("UTF-8")));

            // le ate o final
            String s = "";
            while (null != ((s = br.readLine()))) {
                newData.append(s);
            }
            br.close();
        } catch (IOException e) {
            throw new Exception("endereco.falhageral<message>" + e.getMessage());
        }
        return newData.toString();
    }
}

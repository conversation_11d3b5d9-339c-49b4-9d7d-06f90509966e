/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.CxForte;

import Dados.Persistencia;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.Filiais;
import SasBeans.OS_Vig;
import SasDaos.CxFGuiasDao;
import SasDaos.CxFGuiasVolDao;
import SasDaos.CxForteDao;
import SasDaos.FiliaisDao;
import SasDaos.OS_VigDao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustodiaSatMobWeb {

    public OS_Vig buscarOS_Vig(String codFil, String os, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_VigDao = new OS_VigDao();
            return os_VigDao.obterOS(codFil, os, persistencia);
        } catch (Exception e) {
            throw new Exception("CustodiaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxFGuiasVol> listarVolumesGuia(String guia, String serie, String codFil, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfGuiasVolDao = new CxFGuiasVolDao();
            return cxfGuiasVolDao.getLacres(guia, serie, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("CustodiaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("CustodiaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxFGuias> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasDao cxfguiasDao = new CxFGuiasDao();
            return cxfguiasDao.listagemPaginada(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("CustodiaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxForte> listarCaixasForte(String CodFil, Persistencia persistencia) throws Exception {
        try {
            CxForteDao cxfguiasDao = new CxForteDao();
            return cxfguiasDao.listarCaixasForte(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("CustodiaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasDao cxfguiasDao = new CxFGuiasDao();
            return cxfguiasDao.contagemCxFGuias(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("CustodiaSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

}

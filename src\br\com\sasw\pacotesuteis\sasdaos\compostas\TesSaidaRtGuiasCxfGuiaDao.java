package br.com.sasw.pacotesuteis.sasdaos.compostas;

import Arquivo.ArquivoLog;
import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxFGuiasVol;
import SasBeans.GuiasList;
import SasBeans.GuiasRotas;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.ServletContext;

/**
 *
 * <AUTHOR>
 */
public class TesSaidaRtGuiasCxfGuiaDao {

    public List<GuiasList> getTesSaidasRt_GuiasCxfGuiasEntrega(String Codfil, String Hora1, String Sequencia, String Parada, Persistencia persistencia) throws Exception {
        List<GuiasList> lGuias = new ArrayList();
        try {
            Consulta rsgtv = new Consulta("Select clientes.nRed nRedOri, clientes.Nome NomeOri, clientes.ende endeOri, clientes.bairro BairroOri, \n"
                    + " clientes.Cidade CidadeOri, clientes.Estado EstadoOri, clientes.CEP CEPOri, \n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda, \n"
                    + " convert(Bigint,Rt_Guias.Guia) Guia, Rt_Guias.Serie, Rt_Guias.Valor, Rt_Guias.OS, Rt_Perc.Sequencia, Rt_Perc.Parada \n"
                    + " from Rt_Guias \n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = ?\n"
                    + " Left Join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia \n"
                    + "                  and Rt_perc.Parada = Rt_Guias.Parada\n"
                    + " left join clientes on clientes.codigo = rt_perc.codcli1\n"
                    + "                   and clientes.codfil = ?\n"
                    + " Left Join Rt_Perc Rt_PercDst on Rt_PercDst.Sequencia = Rt_Perc.Sequencia\n"
                    + "                             and Rt_PercDst.Parada = Rt_perc.DPar\n"
                    + " where Rt_Guias.Sequencia = ?\n"
                    + " and Rt_PercDst.Parada    = ?\n", persistencia);
            rsgtv.setString(Codfil);
            rsgtv.setString(Codfil);
            rsgtv.setString(Sequencia);
            rsgtv.setString(Parada);
            rsgtv.select();
            GuiasList temp;
            while (rsgtv.Proximo()) {
                if ((rsgtv.getFloat("Guia") > 0) && (!rsgtv.getString("Serie").equals(""))) {
                    temp = new GuiasList();
                    //temp.setGuia(rsgtv.getString("Guia").substring(0, rsgtv.getString("Guia").indexOf(".")));
                    temp.setGuia(rsgtv.getString("Guia"));
                    temp.setSerie(rsgtv.getString("Serie"));
                    temp.setValor(rsgtv.getString("Valor"));
                    temp.setOS(rsgtv.getString("OS"));
                    temp.setSequenciaOri(rsgtv.getBigDecimal("Sequencia"));
                    temp.setParadaOri(rsgtv.getInt("Parada"));

                    temp.setnRedOri(rsgtv.getString("nRedOri"));
                    temp.setNomeOri(rsgtv.getString("nomeOri"));
                    temp.setEndeOri(rsgtv.getString("endeOri"));
                    temp.setBairroOri(rsgtv.getString("bairroOri"));
                    temp.setCidadeOri(rsgtv.getString("cidadeOri"));
                    temp.setEstadoOri(rsgtv.getString("estadoOri"));
                    temp.setCepOri(rsgtv.getString("cepOri"));
                    temp.setMoeda(rsgtv.getString("moeda"));
                    temp.setRtGuias(true);
                    int posicao = this.ExisteGuia(temp.getGuia(), temp.getSerie(), lGuias, 1);
                    if (-1 == posicao) {
                        lGuias.add(temp);
                    } else {
                        lGuias.get(posicao).setRtGuias(true);
                    }
                }
            }
            rsgtv.Close();
        } catch (Exception e) {
            throw new Exception(e);
        }
        try {
            Consulta rsgtv = new Consulta("Select clientes.nRed nRedOri, clientes.Nome NomeOri, clientes.ende endeOri, clientes.bairro BairroOri, "
                    + " clientes.Cidade CidadeOri, clientes.Estado EstadoOri, clientes.CEP CEPOri, "
                    + "  convert(bigint,Guia) Guia,Serie,Valor,OS,CliOri "
                    + " from cxfguias"
                    + " left join clientes on clientes.codigo = cxfguias.cliori"
                    + "                    and clientes.codfil = cxfguias.codfil "
                    + " where seqrotasai = ?"
                    + " and hora1d = ?", persistencia);
            rsgtv.setString(Sequencia);
            rsgtv.setString(Hora1.replace(":", ""));
            rsgtv.select();
            while (rsgtv.Proximo()) {
                if ((rsgtv.getFloat("Guia") > 0) && (!rsgtv.getString("Serie").equals(""))) {
                    GuiasList temp = new GuiasList();
                    temp.setGuia(rsgtv.getString("Guia").replace(".0", ""));
                    temp.setSerie(rsgtv.getString("Serie"));
                    temp.setValor(rsgtv.getString("Valor"));
                    temp.setOS(rsgtv.getString("OS"));
                    temp.setCliori(rsgtv.getString("CliOri"));
                    temp.setnRedOri(rsgtv.getString("nRedOri"));
                    temp.setNomeOri(rsgtv.getString("nomeOri"));
                    temp.setEndeOri(rsgtv.getString("endeOri"));
                    temp.setBairroOri(rsgtv.getString("bairroOri"));
                    temp.setCidadeOri(rsgtv.getString("cidadeOri"));
                    temp.setEstadoOri(rsgtv.getString("estadoOri"));
                    temp.setCepOri(rsgtv.getString("cepOri"));
                    temp.setCxfGuias(true);
                    int posicao = this.ExisteGuia(temp.getGuia(), temp.getSerie(), lGuias, 2);
                    if (-1 == posicao) {
                        lGuias.add(temp);
                    } else {
                        lGuias.get(posicao).setCxfGuias(true);
                    }
                }
            }
            rsgtv.Close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            Consulta rsgtv = new Consulta("Select clientes.nRed nRedOri, clientes.Nome NomeOri, clientes.ende endeOri, clientes.bairro BairroOri, "
                    + " clientes.Cidade CidadeOri, clientes.Estado EstadoOri, clientes.CEP CEPOri, convert(bigint,TesSaidas.Guia) Guia, "
                    + " TesSaidas.Serie, TesSaidas.TotalGeral, "
                    + " Pedido.OS "
                    + " from Pedido as Pedido"
                    + " left join TesSaidas as TesSaidas on  TesSaidas.Pedido  = Pedido.Numero "
                    + "                                 and TesSaidas.Codcli2 = Pedido.Codcli2 "
                    + " left join Clientes on Clientes.codigo = TesSaidas.CodCli2 "
                    + "                     and Clientes.codfil = TesSaidas.CodFil "
                    + "                                 and TesSaidas.CodFil  = Pedido.CodFil  "
                    + " Left Join Rt_Guias as Rt_Guias  on  Rt_Guias.Guia  = TesSaidas.Guia "
                    + "                                and Rt_Guias.Serie = TesSaidas.Serie "
                    + " where Pedido.SeqRota = ?"
                    + "   and Pedido.Parada  = ?"
                    + "   and Pedido.CodFil  = ?"
                    + "   and TesSaidas.TotalGeral >0"
                    + "   and TesSaidas.Guia is not null", persistencia);
            rsgtv.setString(Sequencia);
            rsgtv.setString(Parada);
            rsgtv.setString(Codfil);
            rsgtv.select();
            while (rsgtv.Proximo()) {
                GuiasList temp = new GuiasList();
                temp.setGuia(rsgtv.getString("Guia").replace(".0", ""));
                temp.setSerie(rsgtv.getString("Serie"));
                temp.setValor(rsgtv.getString("TotalGeral"));
                temp.setOS(rsgtv.getString("OS"));
                temp.setnRedOri(rsgtv.getString("nRedOri"));
                temp.setNomeOri(rsgtv.getString("nomeOri"));
                temp.setEndeOri(rsgtv.getString("endeOri"));
                temp.setBairroOri(rsgtv.getString("bairroOri"));
                temp.setCidadeOri(rsgtv.getString("cidadeOri"));
                temp.setEstadoOri(rsgtv.getString("estadoOri"));
                temp.setCepOri(rsgtv.getString("cepOri"));
                temp.setTesSaidas(true);
                int posicao = this.ExisteGuia(temp.getGuia(), temp.getSerie(), lGuias, 3);
                if (-1 == posicao) {
                    lGuias.add(temp);
                } else {
                    lGuias.get(posicao).setTesSaidas(true);
                }
            }
            rsgtv.Close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            List<CxFGuiasVol> volumes;
            String sql = "select *  from cxfguiasvol where codfil = ? and guia = ? and serie = ?";
            Consulta consult;
            CxFGuiasVol volume;
            for (GuiasList lGuia : lGuias) {
                try {
                    volumes = new ArrayList();
                    consult = new Consulta(sql, persistencia);
                    consult.setString(Codfil);
                    consult.setString(lGuia.getGuia());
                    consult.setString(lGuia.getSerie());
                    consult.select();
                    while (consult.Proximo()) {
                        volume = new CxFGuiasVol();
                        volume.setGuia(lGuia.getGuia());
                        volume.setSerie(lGuia.getSerie());
                        volume.setLacre(consult.getString("lacre"));
                        volume.setValor(consult.getString("valor"));
                        volume.setQtde(consult.getString("Qtde"));
                        volumes.add(volume);
                    }
                    lGuia.setVolumes(volumes);
                    consult.Close();
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return lGuias;
    }

    public List<GuiasList> getTesSaidasRt_GuiasCxfGuiasEntrega(ServletContext context, ArquivoLog logerro, String sCodPessoa, boolean gerarLogSubQuery,
            String Codfil, String Hora1, String Sequencia, String Parada, Persistencia persistencia) throws Exception {
        List<GuiasList> lGuias = new ArrayList();
        try {

            if (gerarLogSubQuery) {
                Trace.gerarTrace(context, "", "Buscar guias em Rt_Guias. Codfil: " + Codfil + ", Parada: " + Parada + ", Sequencia: " + Sequencia, sCodPessoa, persistencia.getEmpresa(), logerro);
            }

            Consulta rsgtv = new Consulta("Select clientes.nRed nRedOri, clientes.Nome NomeOri, clientes.ende endeOri, clientes.bairro BairroOri, "
                    + " clientes.Cidade CidadeOri, clientes.Estado EstadoOri, clientes.CEP CEPOri, "
                    + " convert(Bigint,Rt_Guias.Guia) Guia, Rt_Guias.Serie, Rt_Guias.Valor, Rt_Guias.OS, Rt_Perc.Sequencia, Rt_Perc.Parada "
                    + " from Rt_Guias "
                    + " Left Join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia "
                    + "                  and Rt_perc.Parada = Rt_Guias.Parada"
                    + " left join clientes on clientes.codigo = rt_perc.codcli1"
                    + "                   and clientes.codfil = ?"
                    + " Left Join Rt_Perc Rt_PercDst on Rt_PercDst.Sequencia = Rt_Perc.Sequencia"
                    + "                             and Rt_PercDst.Parada = Rt_perc.DPar"
                    + " where Rt_Guias.Sequencia = ?"
                    + " and Rt_PercDst.Parada    = ?", persistencia);
            rsgtv.setString(Codfil);
            rsgtv.setString(Sequencia);
            rsgtv.setString(Parada);
            rsgtv.select();
            GuiasList temp;
            while (rsgtv.Proximo()) {
                if ((rsgtv.getFloat("Guia") > 0) && (!rsgtv.getString("Serie").equals(""))) {
                    temp = new GuiasList();
                    //temp.setGuia(rsgtv.getString("Guia").substring(0, rsgtv.getString("Guia").indexOf(".")));
                    temp.setGuia(rsgtv.getString("Guia"));
                    temp.setSerie(rsgtv.getString("Serie"));
                    temp.setValor(rsgtv.getString("Valor"));
                    temp.setOS(rsgtv.getString("OS"));
                    temp.setSequenciaOri(rsgtv.getBigDecimal("Sequencia"));
                    temp.setParadaOri(rsgtv.getInt("Parada"));

                    /**
                     * Usado para relatório de guias
                     */
                    temp.setnRedOri(rsgtv.getString("nRedOri"));
                    temp.setNomeOri(rsgtv.getString("nomeOri"));
                    temp.setEndeOri(rsgtv.getString("endeOri"));
                    temp.setBairroOri(rsgtv.getString("bairroOri"));
                    temp.setCidadeOri(rsgtv.getString("cidadeOri"));
                    temp.setEstadoOri(rsgtv.getString("estadoOri"));
                    temp.setCepOri(rsgtv.getString("cepOri"));
                    temp.setRtGuias(true);
                    int posicao = this.ExisteGuia(temp.getGuia(), temp.getSerie(), lGuias, 1);
                    if (-1 == posicao) {
                        lGuias.add(temp);
                    } else {
                        lGuias.get(posicao).setRtGuias(true);
                    }
                }
            }
            rsgtv.Close();

            if (gerarLogSubQuery) {
                Trace.gerarTrace(context, "", "Finalização de busca de Rt_Guias", sCodPessoa, persistencia.getEmpresa(), logerro);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            if (gerarLogSubQuery) {
                Trace.gerarTrace(context, "", "Buscar guias em cxfguias. SeqRotaSai: " + Sequencia + ", Hora1D: " + Hora1, sCodPessoa, persistencia.getEmpresa(), logerro);
            }

            Consulta rsgtv = new Consulta("Select clientes.nRed nRedOri, clientes.Nome NomeOri, clientes.ende endeOri, clientes.bairro BairroOri, "
                    + " clientes.Cidade CidadeOri, clientes.Estado EstadoOri, clientes.CEP CEPOri, "
                    + " convert(bigint,Guia) Guia,Serie,Valor,OS,CliOri "
                    + " from cxfguias"
                    + " left join clientes on clientes.codigo = cxfguias.cliori"
                    + "                    and clientes.codfil = cxfguias.codfil "
                    + " where seqrotasai = ?"
                    + " and hora1d = ?", persistencia);
            rsgtv.setString(Sequencia);
            rsgtv.setString(Hora1.replace(":", ""));
            rsgtv.select();
            GuiasList temp;
            while (rsgtv.Proximo()) {
                if ((rsgtv.getFloat("Guia") > 0) && (!rsgtv.getString("Serie").equals(""))) {
                    temp = new GuiasList();
                    temp.setGuia(rsgtv.getString("Guia").replace(".0", ""));
                    temp.setSerie(rsgtv.getString("Serie"));
                    temp.setValor(rsgtv.getString("Valor"));
                    temp.setOS(rsgtv.getString("OS"));

                    /**
                     * Usado para relatório de guias
                     */
                    temp.setCliori(rsgtv.getString("CliOri"));
                    temp.setnRedOri(rsgtv.getString("nRedOri"));
                    temp.setNomeOri(rsgtv.getString("nomeOri"));
                    temp.setEndeOri(rsgtv.getString("endeOri"));
                    temp.setBairroOri(rsgtv.getString("bairroOri"));
                    temp.setCidadeOri(rsgtv.getString("cidadeOri"));
                    temp.setEstadoOri(rsgtv.getString("estadoOri"));
                    temp.setCepOri(rsgtv.getString("cepOri"));
                    temp.setCxfGuias(true);
                    int posicao = this.ExisteGuia(temp.getGuia(), temp.getSerie(), lGuias, 2);
                    if (-1 == posicao) {
                        lGuias.add(temp);
                    } else {
                        lGuias.get(posicao).setCxfGuias(true);
                    }
                }
            }
            rsgtv.Close();
            if (gerarLogSubQuery) {
                Trace.gerarTrace(context, "", "Finalização de busca de cxfguias", sCodPessoa, persistencia.getEmpresa(), logerro);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            if (gerarLogSubQuery) {
                Trace.gerarTrace(context, "", "Buscar pedidos e TesSaidas. SeqRota: " + Sequencia + ", Parada: " + Parada + ", CodFil: " + Codfil,
                        sCodPessoa, persistencia.getEmpresa(), logerro);
            }

            Consulta rsgtv = new Consulta("Select clientes.nRed nRedOri, clientes.Nome NomeOri, clientes.ende endeOri, clientes.bairro BairroOri, "
                    + " clientes.Cidade CidadeOri, clientes.Estado EstadoOri, clientes.CEP CEPOri, convert(bigint,TesSaidas.Guia) Guia, "
                    + " TesSaidas.Serie, TesSaidas.TotalGeral, "
                    + " Pedido.OS "
                    + " from Pedido as Pedido"
                    + " left join TesSaidas as TesSaidas on  TesSaidas.Pedido  = Pedido.Numero "
                    + "                                 and TesSaidas.Codcli2 = Pedido.Codcli2 "
                    + " left join Clientes on Clientes.codigo = TesSaidas.CodCli2 "
                    + "                     and Clientes.codfil = TesSaidas.CodFil "
                    + "                                 and TesSaidas.CodFil  = Pedido.CodFil  "
                    + " Left Join Rt_Guias as Rt_Guias  on  Rt_Guias.Guia  = TesSaidas.Guia "
                    + "                                and Rt_Guias.Serie = TesSaidas.Serie "
                    + " where Pedido.SeqRota = ?"
                    + "   and Pedido.Parada  = ?"
                    + "   and Pedido.CodFil  = ?"
                    + "   and TesSaidas.TotalGeral >0"
                    + "   and TesSaidas.Guia is not null", persistencia);
            rsgtv.setString(Sequencia);
            rsgtv.setString(Parada);
            rsgtv.setString(Codfil);
            rsgtv.select();
            GuiasList temp;
            while (rsgtv.Proximo()) {
                temp = new GuiasList();
                temp.setGuia(rsgtv.getString("Guia").replace(".0", ""));
                temp.setSerie(rsgtv.getString("Serie"));
                temp.setValor(rsgtv.getString("TotalGeral"));
                temp.setOS(rsgtv.getString("OS"));

                /**
                 * Usado para relatório de guias
                 */
                temp.setnRedOri(rsgtv.getString("nRedOri"));
                temp.setNomeOri(rsgtv.getString("nomeOri"));
                temp.setEndeOri(rsgtv.getString("endeOri"));
                temp.setBairroOri(rsgtv.getString("bairroOri"));
                temp.setCidadeOri(rsgtv.getString("cidadeOri"));
                temp.setEstadoOri(rsgtv.getString("estadoOri"));
                temp.setCepOri(rsgtv.getString("cepOri"));
                temp.setTesSaidas(true);
                int posicao = this.ExisteGuia(temp.getGuia(), temp.getSerie(), lGuias, 3);
                if (-1 == posicao) {
                    lGuias.add(temp);
                } else {
                    lGuias.get(posicao).setTesSaidas(true);
                }
            }
            rsgtv.Close();
            if (gerarLogSubQuery) {
                Trace.gerarTrace(context, "", "Finalização de busca pedidos e TesSaidas", sCodPessoa, persistencia.getEmpresa(), logerro);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            List<CxFGuiasVol> volumes;
            String sql = "select *  from cxfguiasvol where codfil = ? and guia = ? and serie = ?";
            Consulta consult;
            CxFGuiasVol volume;
            for (GuiasList lGuia : lGuias) {
                try {
                    volumes = new ArrayList();
                    consult = new Consulta(sql, persistencia);
                    consult.setString(Codfil);
                    consult.setString(lGuia.getGuia());
                    consult.setString(lGuia.getSerie());
                    consult.select();

                    if (gerarLogSubQuery) {
                        Trace.gerarTrace(context, "", "Busca CxfGuiasVol. Codfil: " + Codfil + ", Guia: " + lGuia.getGuia()
                                + ", Serie: " + lGuia.getSerie(), sCodPessoa, persistencia.getEmpresa(), logerro);
                    }

                    while (consult.Proximo()) {
                        volume = new CxFGuiasVol();
                        volume.setGuia(lGuia.getGuia());
                        volume.setSerie(lGuia.getSerie());
                        volume.setLacre(consult.getString("lacre"));
                        volume.setValor(consult.getString("valor"));
                        volume.setQtde(consult.getString("Qtde"));
                        volumes.add(volume);
                    }
                    lGuia.setVolumes(volumes);
                    consult.Close();

                    if (gerarLogSubQuery) {
                        Trace.gerarTrace(context, "", "Fim Busca CxfGuiasVol.", sCodPessoa, persistencia.getEmpresa(), logerro);
                    }

                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return lGuias;
    }

    /**
     * Recupera informações da guia
     *
     * @param sequencia Sequencia da rota
     * @param persistencia Conexão com o banco de dados
     * @return lista de guias
     * @throws Exception
     */
    public List<GuiasRotas> listaGuias(float sequencia, Persistencia persistencia) throws Exception {
        List<GuiasRotas> guias = new ArrayList<>();
        try {
            String sql = "Select Rt_Guias.CodFil, OS_Vig.Cliente,     "
                    + "  Rt_Perc.hora1,    "
                    + "    Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliDst.Nred else CliOri.NRed end Origem, "
                    + "	Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliDst.Ende else CliOri.Ende end EndOrigem, "
                    + "	Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliDst.Bairro else CliOri.Bairro end BairroOrigem, "
                    + "	Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliDst.Cidade else CliOri.Cidade end CidadeOrigem, "
                    + "	Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliDst.Estado else CliOri.Estado end EstadoOrigem, "
                    + "   Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliOri.Nred else CliDst.Nred end Destino,   "
                    + "   Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliOri.Ende else CliDst.Ende end EndDestino,   "
                    + "   Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliOri.Bairro else CliDst.Bairro end BairroDestino,   "
                    + "   Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliOri.Estado else CliDst.Estado end EstadoDestino,   "
                    + "   Case when Rt_Perc.ER = 'E' and CliOri.Codigo = Rt_Perc.CodCli1 then CliOri.Cidade else CliDst.Cidade end CidadeDestino,  "
                    + "   Rt_Guias.Guia, Rt_Guias.Serie, Rt_Guias.Valor, Rt_Guias.OS, Rt_Perc.Sequencia, Rt_Perc.Parada, RPV.CodPessoaAut   "
                    + "   from Rt_Guias     "
                    + "   Left Join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia     "
                    + "                    and Rt_perc.Parada = Rt_Guias.Parada     "
                    + "   Left join Rotas   on Rotas.Sequencia = Rt_Guias.Sequencia     "
                    + "   Left JOIN OS_Vig  ON OS_Vig.OS = Rt_Guias.OS     "
                    + "                    and Os_Vig.CodFil = Rotas.CodFil     "
                    + "   Left join Clientes  CliOri  on CliOri.Codigo = Os_Vig.Cliente     "
                    + "                              and CliOri.CodFil = OS_Vig.CodFil     "
                    + "   Left join Clientes  CliDst  on CliDst.Codigo = OS_Vig.CliDst     "
                    + "                              and CliDst.CodFil = Os_Vig.CodFil   "
                    + "   Left join RPV on RPV.parada = Rt_Perc.parada   "
                    + " 				and RPV.SeqRota = Rt_Perc.Sequencia "
                    + "   where Rt_Guias.Sequencia = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setFloat(sequencia);
            consulta.select();

            GuiasRotas guia = null;
            while (consulta.Proximo()) {
                guia = new GuiasRotas();
                guia.setHora1(consulta.getString("hora1"));
                guia.setCodFil(consulta.getInt("codfil"));
                guia.setCliente(consulta.getInt("cliente"));
                guia.setOrigem(consulta.getString("origem"));
                guia.setEndOrigem(consulta.getString("EndOrigem"));
                guia.setBairroOrigem(consulta.getString("BairroOrigem"));
                guia.setCidadeOrigem(consulta.getString("CidadeOrigem"));
                guia.setEstadoOrigem(consulta.getString("EstadoOrigem"));
                guia.setDestino(consulta.getString("destino"));
                guia.setEndDestino(consulta.getString("EndDestino"));
                guia.setBairroDestino(consulta.getString("BairroDestino"));
                guia.setEstadoDestino(consulta.getString("EstadoDestino"));
                guia.setCidadeDestino(consulta.getString("CidadeDestino"));
                guia.setGuia(consulta.getInt("guia"));
                guia.setSerie(consulta.getString("serie"));
                guia.setValor(consulta.getFloat("valor"));
                guia.setOS(consulta.getFloat("os"));
                guia.setSequencia(consulta.getFloat("sequencia"));
                guia.setParada(consulta.getInt("parada"));
                guia.setCodPessoaAuth(consulta.getString("CodPessoaAut"));
                guias.add(guia);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Ocorreu um erro: " + e.getMessage());
        }
        return guias;
    }

    /**
     * Verifica se uma guia já está na lista
     *
     * @param Guia - número da guia
     * @param Serie - núemero da série
     * @param listGuias - lista de guias a verificar
     * @param tipo 1 Rt_guias (guia de rota) 2 CxFGuias (guia de caixa forte) 3
     * TesSaidas (saida de tesouraria)
     * @return - posição na guia na listagem ou -1 para guia não encontrada
     */
    private int ExisteGuia(String Guia, String Serie, List<GuiasList> listGuias, int tipo) {
        int i = 0;
        while (i < listGuias.size()) {
            if (listGuias.get(i).getGuia().equals(Guia) && listGuias.get(i).getSerie().equals(Serie)) {
                if (tipo == 1) {
                    listGuias.get(i).setRtGuias(true);
                } else if (tipo == 2) {
                    listGuias.get(i).setCxfGuias(true);
                } else {
                    listGuias.get(i).setTesSaidas(true);
                }
                return i;
            } else {
                i++;
            }
        }
        return -1;
    }
}

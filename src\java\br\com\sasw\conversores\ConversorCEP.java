/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import javax.faces.component.UIComponent;
import javax.faces.component.UIViewRoot;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorCEP")
public class ConversorCEP implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        UIViewRoot viewRoot = FacesContext.getCurrentInstance().getViewRoot();
        java.util.Locale locale = viewRoot.getLocale();
        String cep;
        try {
            switch (locale.getLanguage()) {
                case "en":
                    cep = value.toString();
                    if (cep.startsWith("000")) {
                        cep = cep.replace("000", "");
                    }
                    break;
                default:
                    cep = ((String) value).substring(0, 2) + "." + ((String) value).substring(2, 5) + "-" + ((String) value).substring(5, 8);
            }
        } catch (Exception e) {
            cep = (String) value;
        }
        return cep;
    }
}

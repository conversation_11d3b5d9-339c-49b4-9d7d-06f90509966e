/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class PessoaPRH {

    private String Codigo;             //ok
    private String Nome;             //ok
    private String Endereco;        //ok
    private String Bairro;          //ok
    private String Cidade;          //ok
    private String UF;              //ok
    private String CEP;            //ok
    private String Fone1;          //ok
    private String Fone2;          //ok
    private String Email;         //ok
    private String EstCivil;     //ok
    private String Conjuge;      //ok
    private String Pai;          //ok
    private String Mae;          //ok
    private String RG;              //ok
    private String RGOrgEmis;       //ok
    private String RgDtEmis;         //ok
    private String CPF;             //ok
    private String pretSalario;     //ok
    private String TitEleit;       //ok
    private String TitZona;         //ok
    private String TitSecao;        //ok
    private String PIS;            //ok
    private String CNH;            //ok
    private String CNHDtVenc;      //ok
    private String CNHCat;        //ok
    private String Reservista;     //ok
    private String ReservCat;     //ok
    private String CTPS_Nro;          //ok
    private String CTPS_Serie;        //ok
    private String CTPS_UF;           //ok
    private String CTPS_Emis;         //ok
    private String Dt_nasc;        //ok
    private String Naturalid;      //ok
    private Integer Instrucao;     //ok
    private String Sexo;           //ok
    private String Raca;           //ok
    private String GrupoSang;     //ok
    private String Altura;        //ok
    private String Peso;         //ok
    private String Indicacao;      //ok
    private String Dt_FormIni;      //ok
    private String Dt_FormFim;      //ok
    private String LocalForm;     //ok
    private String Certific;     //ok
    private String Reg_PF;        //ok
    private String Reg_PFUF;       //ok
    private String Reg_PFDt;      //ok
    private String CarNacVig;     //ok
    private String DtValCNV;     //ok
    private String Dt_Recicl;      //ok
    private String Dt_VenCurs;       //ok
    private String ExtTV;      //ok
    private String ExtSPP;       //ok
    private String ExtEscolta;    //ok
    private String Obs;            //ok
    private String Funcao;         //ok
    private String FuncaoOutro;
    private Boolean FuncaoTipo;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Naturalid_UF;      //ok
    private String Mae_Prof;          //ok
    private String Mae_Nacion;        //ok
    private String Pai_Prof;         //ok
    private String Pai_Nacion;      //ok
    private String Conjuge_Prof;      //ok
    private String Conjuge_Nasc;      //ok
    private String Olhos;           //ok
    private String Cabelo;     //ok
    private String DefeitosFis;     //ok
    private String Tatuagem;       //ok
    private Integer Camisa;        //ok
    private Integer Sapato;        //ok
    private Integer Calca;        //ok
    private Integer Jaqueta;     //ok
    private String Tipo_Moradia;    //ok
    private String Religiao;         //ok
    private String Religiao_Prat;    //ok
    private String Reservista_RM;    //ok
    private String NomeEscola;     //ok
    private String PorteArma;        //ok
    private String Situacao;
    private String Nacionalidade;
    private String Ct_Banco;
    private String Ct_Agencia;
    private String Ct_Conta;

    private String CodCli;
    private String Complemento;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getFone1() {
        return Fone1;
    }

    public void setFone1(String Fone1) {
        this.Fone1 = Fone1;
    }

    public String getFone2() {
        return Fone2;
    }

    public void setFone2(String Fone2) {
        this.Fone2 = Fone2;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getEstCivil() {
        return EstCivil;
    }

    public void setEstCivil(String EstCivil) {
        this.EstCivil = EstCivil;
    }

    public String getConjuge() {
        return Conjuge;
    }

    public void setConjuge(String Conjuge) {
        this.Conjuge = Conjuge;
    }

    public String getPai() {
        return Pai;
    }

    public void setPai(String Pai) {
        this.Pai = Pai;
    }

    public String getMae() {
        return Mae;
    }

    public void setMae(String Mae) {
        this.Mae = Mae;
    }

    public String getRG() {
        return RG;
    }

    public void setRG(String RG) {
        this.RG = RG;
    }

    public String getRGOrgEmis() {
        return RGOrgEmis;
    }

    public void setRGOrgEmis(String RGOrgEmis) {
        this.RGOrgEmis = RGOrgEmis;
    }

    public String getRgDtEmis() {
        return RgDtEmis;
    }

    public void setRgDtEmis(String RgDtEmis) throws Exception {
        this.RgDtEmis = RgDtEmis;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getPretSalario() {
        return pretSalario;
    }

    public void setPretSalario(String pretSalario) {
        this.pretSalario = pretSalario;
    }

    public String getTitEleit() {
        return TitEleit;
    }

    public void setTitEleit(String TitEleit) {
        this.TitEleit = TitEleit;
    }

    public String getTitZona() {
        return TitZona;
    }

    public void setTitZona(String TitZona) {
        this.TitZona = TitZona;
    }

    public String getTitSecao() {
        return TitSecao;
    }

    public void setTitSecao(String TitSecao) {
        this.TitSecao = TitSecao;
    }

    public String getPIS() {
        return PIS;
    }

    public void setPIS(String PIS) {
        this.PIS = PIS;
    }

    public String getCNH() {
        return CNH;
    }

    public void setCNH(String CNH) {
        this.CNH = CNH;
    }

    public String getCNHDtVenc() {
        return CNHDtVenc;
    }

    public void setCNHDtVenc(String CNHDtVenc) throws Exception {
        this.CNHDtVenc = CNHDtVenc;
    }

    public String getCNHCat() {
        return CNHCat;
    }

    public void setCNHCat(String CNHCat) {
        this.CNHCat = CNHCat;
    }

    public String getReservista() {
        return Reservista;
    }

    public void setReservista(String Reservista) {
        this.Reservista = Reservista;
    }

    public String getReservCat() {
        return ReservCat;
    }

    public void setReservCat(String ReservCat) {
        this.ReservCat = ReservCat;
    }

    public String getCTPS_Nro() {
        return CTPS_Nro;
    }

    public void setCTPS_Nro(String CTPS_Nro) {
        this.CTPS_Nro = CTPS_Nro;
    }

    public String getCTPS_Serie() {
        return CTPS_Serie;
    }

    public void setCTPS_Serie(String CTPS_Serie) {
        this.CTPS_Serie = CTPS_Serie;
    }

    public String getCTPS_UF() {
        return CTPS_UF;
    }

    public void setCTPS_UF(String CTPS_UF) {
        this.CTPS_UF = CTPS_UF;
    }

    public String getCTPS_Emis() {
        return CTPS_Emis;
    }

    public void setCTPS_Emis(String CTPS_Emis) throws Exception {
        this.CTPS_Emis = CTPS_Emis;
    }

    public String getDt_nasc() {
        return Dt_nasc;
    }

    public void setDt_nasc(String Dt_nasc) throws Exception {
        this.Dt_nasc = Dt_nasc;
    }

    public String getNaturalid() {
        return Naturalid;
    }

    public void setNaturalid(String Naturalid) {
        this.Naturalid = Naturalid;
    }

    public Integer getInstrucao() {
        return Instrucao;
    }

    public void setInstrucao(Integer Instrucao) {
        this.Instrucao = Instrucao;
    }

    public String getSexo() {
        return Sexo;
    }

    public void setSexo(String Sexo) {
        this.Sexo = Sexo;
    }

    public String getRaca() {
        return Raca;
    }

    public void setRaca(String Raca) {
        this.Raca = Raca;
    }

    public String getGrupoSang() {
        return GrupoSang;
    }

    public void setGrupoSang(String GrupoSang) {
        this.GrupoSang = GrupoSang;
    }

    public String getAltura() {
        return Altura;
    }

    public void setAltura(String Altura) {
        this.Altura = Altura;
    }

    public String getPeso() {
        return Peso;
    }

    public void setPeso(String Peso) {
        this.Peso = Peso;
    }

    public String getIndicacao() {
        return Indicacao;
    }

    public void setIndicacao(String Indicacao) {
        this.Indicacao = Indicacao;
    }

    public String getDt_FormIni() {
        return Dt_FormIni;
    }

    public void setDt_FormIni(String Dt_FormIni) throws Exception {
        this.Dt_FormIni = Dt_FormIni;
    }

    public String getDt_FormFim() {
        return Dt_FormFim;
    }

    public void setDt_FormFim(String Dt_FormFim) throws Exception {
        this.Dt_FormFim = Dt_FormFim;
    }

    public String getLocalForm() {
        return LocalForm;
    }

    public void setLocalForm(String LocalForm) {
        this.LocalForm = LocalForm;
    }

    public String getCertific() {
        return Certific;
    }

    public void setCertific(String Certific) {
        this.Certific = Certific;
    }

    public String getReg_PF() {
        return Reg_PF;
    }

    public void setReg_PF(String Reg_PF) {
        this.Reg_PF = Reg_PF;
    }

    public String getReg_PFUF() {
        return Reg_PFUF;
    }

    public void setReg_PFUF(String Reg_PFUF) {
        this.Reg_PFUF = Reg_PFUF;
    }

    public String getReg_PFDt() {
        return Reg_PFDt;
    }

    public void setReg_PFDt(String Reg_PFDt) throws Exception {
        this.Reg_PFDt = Reg_PFDt;
    }

    public String getCarNacVig() {
        return CarNacVig;
    }

    public void setCarNacVig(String CarNacVig) {
        this.CarNacVig = CarNacVig;
    }

    public String getDtValCNV() {
        return DtValCNV;
    }

    public void setDtValCNV(String DtValCNV) throws Exception {
        this.DtValCNV = DtValCNV;
    }

    public String getDt_Recicl() {
        return Dt_Recicl;
    }

    public void setDt_Recicl(String Dt_Recicl) throws Exception {
        this.Dt_Recicl = Dt_Recicl;
    }

    public String getDt_VenCurs() {
        return Dt_VenCurs;
    }

    public void setDt_VenCurs(String Dt_VenCurs) throws Exception {
        this.Dt_VenCurs = Dt_VenCurs;
    }

    public String getExtTV() {
        return ExtTV;
    }

    public void setExtTV(String ExtTV) {
        this.ExtTV = ExtTV;
    }

    public String getExtSPP() {
        return ExtSPP;
    }

    public void setExtSPP(String ExtSPP) {
        this.ExtSPP = ExtSPP;
    }

    public String getExtEscolta() {
        return ExtEscolta;
    }

    public void setExtEscolta(String ExtEscolta) {
        this.ExtEscolta = ExtEscolta;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getFuncao() {
        return Funcao;
    }

    public void setFuncao(String Funcao) {
        this.Funcao = Funcao;
    }

    public String getFuncaoOutro() {
        return FuncaoOutro;
    }

    public void setFuncaoOutro(String FuncaoOutro) {
        this.FuncaoOutro = FuncaoOutro;
    }

    public Boolean getFuncaoTipo() {
        return FuncaoTipo;
    }

    public void setFuncaoTipo(Boolean FuncaoTipo) {
        this.FuncaoTipo = FuncaoTipo;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getNaturalid_UF() {
        return Naturalid_UF;
    }

    public void setNaturalid_UF(String Naturalid_UF) {
        this.Naturalid_UF = Naturalid_UF;
    }

    public String getMae_Prof() {
        return Mae_Prof;
    }

    public void setMae_Prof(String Mae_Prof) {
        this.Mae_Prof = Mae_Prof;
    }

    public String getMae_Nacion() {
        return Mae_Nacion;
    }

    public void setMae_Nacion(String Mae_Nacion) {
        this.Mae_Nacion = Mae_Nacion;
    }

    public String getPai_Prof() {
        return Pai_Prof;
    }

    public void setPai_Prof(String Pai_Prof) {
        this.Pai_Prof = Pai_Prof;
    }

    public String getPai_Nacion() {
        return Pai_Nacion;
    }

    public void setPai_Nacion(String Pai_Nacion) {
        this.Pai_Nacion = Pai_Nacion;
    }

    public String getConjuge_Prof() {
        return Conjuge_Prof;
    }

    public void setConjuge_Prof(String Conjuge_Prof) {
        this.Conjuge_Prof = Conjuge_Prof;
    }

    public String getConjuge_Nasc() {
        return Conjuge_Nasc;
    }

    public void setConjuge_Nasc(String Conjuge_Nasc) throws Exception {
        this.Conjuge_Nasc = Conjuge_Nasc;
    }

    public String getOlhos() {
        return Olhos;
    }

    public void setOlhos(String Olhos) {
        this.Olhos = Olhos;
    }

    public String getCabelo() {
        return Cabelo;
    }

    public void setCabelo(String Cabelo) {
        this.Cabelo = Cabelo;
    }

    public String getDefeitosFis() {
        return DefeitosFis;
    }

    public void setDefeitosFis(String DefeitosFis) {
        this.DefeitosFis = DefeitosFis;
    }

    public String getTatuagem() {
        return Tatuagem;
    }

    public void setTatuagem(String Tatuagem) {
        this.Tatuagem = Tatuagem;
    }

    public Integer getCamisa() {
        return Camisa;
    }

    public void setCamisa(Integer Camisa) {
        this.Camisa = Camisa;
    }

    public Integer getSapato() {
        return Sapato;
    }

    public void setSapato(Integer Sapato) {
        this.Sapato = Sapato;
    }

    public Integer getCalca() {
        return Calca;
    }

    public void setCalca(Integer Calca) {
        this.Calca = Calca;
    }

    public Integer getJaqueta() {
        return Jaqueta;
    }

    public void setJaqueta(Integer Jaqueta) {
        this.Jaqueta = Jaqueta;
    }

    public String getTipo_Moradia() {
        return Tipo_Moradia;
    }

    public void setTipo_Moradia(String Tipo_Moradia) {
        this.Tipo_Moradia = Tipo_Moradia;
    }

    public String getReligiao() {
        return Religiao;
    }

    public void setReligiao(String Religiao) {
        this.Religiao = Religiao;
    }

    public String getReligiao_Prat() {
        return Religiao_Prat;
    }

    public void setReligiao_Prat(String Religiao_Prat) {
        this.Religiao_Prat = Religiao_Prat;
    }

    public String getReservista_RM() {
        return Reservista_RM;
    }

    public void setReservista_RM(String Reservista_RM) {
        this.Reservista_RM = Reservista_RM;
    }

    public String getNomeEscola() {
        return NomeEscola;
    }

    public void setNomeEscola(String NomeEscola) {
        this.NomeEscola = NomeEscola;
    }

    public String getPorteArma() {
        return PorteArma;
    }

    public void setPorteArma(String PorteArma) {
        this.PorteArma = PorteArma;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getNacionalidade() {
        return Nacionalidade;
    }

    public void setNacionalidade(String Nacionalidade) {
        this.Nacionalidade = Nacionalidade;
    }

    public String getCt_Banco() {
        return Ct_Banco;
    }

    public void setCt_Banco(String Ct_Banco) {
        this.Ct_Banco = Ct_Banco;
    }

    public String getCt_Agencia() {
        return Ct_Agencia;
    }

    public void setCt_Agencia(String Ct_Agencia) {
        this.Ct_Agencia = Ct_Agencia;
    }

    public String getCt_Conta() {
        return Ct_Conta;
    }

    public void setCt_Conta(String Ct_Conta) {
        this.Ct_Conta = Ct_Conta;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getComplemento() {
        return Complemento;
    }

    public void setComplemento(String Complemento) {
        this.Complemento = Complemento;
    }

}

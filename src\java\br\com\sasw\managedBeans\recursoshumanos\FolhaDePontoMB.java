/*
 */
package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Controller.FolhaDePonto.FolhaDePontoSatMobWeb;
import Dados.Persistencia;
import SasBeans.Rh_Ctrl;
import SasBeans.Rh_Horas;
import SasBeansCompostas.GeraPdfFolhaPonto;
import br.com.sasw.arquivos.PDF;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.awt.Desktop;
import java.io.File;
import java.io.Serializable;
import java.math.BigDecimal;
import java.net.URI;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;

/**
 *
 * <AUTHOR>
 */
@Named(value = "pontos")
@ViewScoped
public class FolhaDePontoMB implements Serializable {

    private Persistencia persistencia;
    private List<Rh_Ctrl> periodos;
    private FolhaDePontoSatMobWeb folhasatweb;
    private GeraPdfFolhaPonto gerapdffolhaponto;
    private BigDecimal codPessoa;
    private String codFil, caminho, banco, matricula, log, operador;
    private ArquivoLog logerro;

    public FolhaDePontoMB() {        
        
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        try {
            matricula = matricula.replace(".0", "");
        } catch (Exception e) {
            matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        }
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\Contracheques\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        folhasatweb = new FolhaDePontoSatMobWeb();
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        log = new String();
    }

    public void buscaPeriodos() {
        try {
            if (this.persistencia.getEmpresa().contains("INTERFORT")
                    || this.persistencia.getEmpresa().contains("SERVITE")
                    || this.persistencia.getEmpresa().contains("INVL")) {
                throw new Exception("UsuarioNaoAutorizado");
            }
            this.periodos = this.folhasatweb.getPeriodos(this.codFil, this.matricula, this.persistencia);
            if (this.periodos.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemFolhasDePonto"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                PrimeFaces.current().executeScript("PF('dlgFolhadePonto').show()");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void imprimirFolhaDePonto(Rh_Ctrl periodo) {
        try {
            PDF pdf;
            if (this.persistencia.getEmpresa().toUpperCase().equals("SATTRANSEXCEL")) {
                pdf = new PDF("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                        + this.banco + "\\Folhadeponto\\", periodo.getCodFil().toBigInteger().toString(), periodo.getMatr().toBigInteger() + ".pdf");
            } else {
                pdf = new PDF("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                        + this.banco + "\\Folhadeponto\\", periodo.getMatr().toBigInteger() + ".pdf");
            }
            this.gerapdffolhaponto = this.folhasatweb.getFolhaDePonto(periodo.getMatr().toBigInteger().toString(),
                    DataAtual.inverteData2(periodo.getDt_Ini()),
                    DataAtual.inverteData2(periodo.getDt_Fim()), periodo.getCodFil().toPlainString(), this.persistencia);
            pdf.CabecalhoPonto(FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator),
                    this.persistencia.getEmpresa(), this.gerapdffolhaponto);
                                               
            List<Rh_Horas> cabecalhoponto = this.folhasatweb.getCabecalhoPonto(periodo.getMatr().toBigInteger().toString(), DataAtual.inverteData2(periodo.getDt_Ini()),
                    DataAtual.inverteData2(periodo.getDt_Fim()), periodo.getCodFil().toBigInteger().toString(), this.persistencia);
            for (Rh_Horas hora : cabecalhoponto) {
                pdf.ItemPonto(hora);
            }

            pdf.FechaPonto(this.gerapdffolhaponto);
            this.folhasatweb.geraLogFolhaDePonto(periodo.getMatr().toBigInteger().toString(), periodo.getDt_Ini(), periodo.getDt_Fim(),
                    periodo.getCodFil().toBigInteger().toString(), this.persistencia);            
            pdf.FechaPdf();
            pdf.service();
            //""
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + e.toString();
            this.logerro.Grava(log, caminho);
        }
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public List<Rh_Ctrl> getPeriodos() {
        return periodos;
    }

    public void setPeriodos(List<Rh_Ctrl> periodos) {
        this.periodos = periodos;
    }
}

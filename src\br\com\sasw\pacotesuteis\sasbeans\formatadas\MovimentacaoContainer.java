/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans.formatadas;

/**
 *
 * <AUTHOR>
 */
public class MovimentacaoContainer {

    private String CodFil;
    private String Container;
    private String Data;
    private String Rota;
    private String HrServico;
    private String Nome;
    private String Nred;
    private String Ende;
    private String Bairro;
    private String Cidade;
    private String Estado;
    private String Motorista;
    private String Placa;
    private String TpServico;

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getContainer() {
        return Container;
    }

    public void setContainer(String Container) {
        this.Container = Container;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getHrServico() {
        return HrServico;
    }

    public void setHrServico(String HrServico) {
        this.HrServico = HrServico;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getNred() {
        return Nred;
    }

    public void setNred(String Nred) {
        this.Nred = Nred;
    }

    public String getEnde() {
        return Ende;
    }

    public void setEnde(String Ende) {
        this.Ende = Ende;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getEstado() {
        return Estado;
    }

    public void setEstado(String Estado) {
        this.Estado = Estado;
    }

    public String getMotorista() {
        return Motorista;
    }

    public void setMotorista(String Motorista) {
        this.Motorista = Motorista;
    }

    public String getPlaca() {
        return Placa;
    }

    public void setPlaca(String Placa) {
        this.Placa = Placa;
    }

    public String getTpServico() {
        return TpServico;
    }

    public void setTpServico(String TpServico) {
        this.TpServico = TpServico;
    }
}

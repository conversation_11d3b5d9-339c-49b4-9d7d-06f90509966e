/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels.cxforte;

import Controller.CxForte.CustodiaSatMobWeb;
import Dados.Persistencia;
import SasBeans.CxFGuias;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class CustodiaLazyList extends LazyDataModel<CxFGuias> {

    private static final long serialVersionUID = 1L;
    private List<CxFGuias> cxFGuiasList;
    private final CustodiaSatMobWeb tesourarialSatMobWeb;
    private final Persistencia persistencia;
    private Map filters;

    public CustodiaLazyList(Persistencia pst, Map filters) {
        this.persistencia = pst;
        this.tesourarialSatMobWeb = new CustodiaSatMobWeb();
        this.filters = filters;
    }

    @Override
    public List<CxFGuias> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.cxFGuiasList = this.tesourarialSatMobWeb.listagemPaginada(first, pageSize, this.filters, this.persistencia);

            setRowCount(this.tesourarialSatMobWeb.contagem(this.filters, this.persistencia));

            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.cxFGuiasList;
    }

    @Override
    public Object getRowKey(CxFGuias cxfguia) {
        return cxfguia.getCodFil().replace(".0", "") + ";"
                + cxfguia.getCodCli().replace(".0", "") + ";"
                + cxfguia.getGuia().replace(".0", "") + ";"
                + cxfguia.getSerie().replace(".0", "");
    }

    @Override
    public CxFGuias getRowData(String cxfguia) {
        try {
            for (CxFGuias c : this.cxFGuiasList) {
                if (c.getCodFil().replace(".0", "").equals(cxfguia.split(";")[0])
                        && c.getCodCli().replace(".0", "").equals(cxfguia.split(";")[1])
                        && c.getGuia().replace(".0", "").equals(cxfguia.split(";")[2])
                        && c.getSerie().replace(".0", "").equals(cxfguia.split(";")[3])) {
                    return c;
                }
            }
            return null;
        } catch (Exception e) {
            System.out.println("CxFGuias: " + cxfguia + "\r\nERRO: " + e.getMessage());
            return null;
        }
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

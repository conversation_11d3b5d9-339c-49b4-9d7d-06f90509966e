/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TmktDet;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class TmktDetDao {

    /**
     * Retorna um relatório salvo em TmktDet
     *
     * @param sequencia
     * @param andamento
     * @param persistencia
     * @return
     * @throws Exception
     */
    public TmktDet getTmktDet(String sequencia, String andamento, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM TmktDet WHERE Sequencia = ? AND Andamento = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(andamento);
            consulta.select();
            TmktDet retorno = null;
            if (consulta.Proximo()) {
                retorno = new TmktDet();
                retorno.setSequencia(consulta.getString("Sequencia"));
                retorno.setAndamento(consulta.getString("Andamento"));
                retorno.setData(consulta.getString("Data"));
                retorno.setHora(consulta.getString("Hora"));
                retorno.setTipoCont(consulta.getString("TipoCont"));
                retorno.setCodPessoa(consulta.getString("CodPessoa"));
                retorno.setCodCont(consulta.getString("CodCont"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setContato(consulta.getString("Contato"));
                retorno.setFone(consulta.getString("Fone"));
                retorno.setFone2(consulta.getString("Fone2"));
                retorno.setHistorico(consulta.getString("Historico"));
                retorno.setDetalhes(consulta.getString("Detalhes"));
                retorno.setCodPrestAtual(consulta.getString("CodPrestAtual"));
                retorno.setQtdeFotos(consulta.getString("QtdeFotos"));
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setPrecisao(consulta.getString("Precisao"));
                retorno.setFiltroWeb(consulta.getString("FiltroWeb"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_alter(consulta.getString("Hr_alter"));
                retorno.setCiente(consulta.getString("Ciente"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setFotos(consulta.getString("Fotos"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TmktDetDao.getTmktDet - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM TmktDet WHERE Sequencia = " + sequencia + " AND Andamento = " + andamento);
        }
    }

    /**
     * Obtém o próximo número de sequência.
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal getSequencia(Persistencia persistencia) throws Exception {
        try {
            Consulta consulta = new Consulta("select IsNull(max(sequencia),0)+1 as sequencia from TmktDet", persistencia);
            consulta.select();
            BigDecimal seq = null;
            while (consulta.Proximo()) {
                seq = new BigDecimal(consulta.getString("sequencia"));
            }
            consulta.Close();
            return seq;
        } catch (Exception e) {
            throw new Exception("TmktDetDao.getSequencia - " + e.getMessage() + "\r\n"
                    + "select IsNull(max(sequencia),0)+1 as sequencia from TmktDet");
        }
    }

    /**
     * Insere uma nova entrada em TmkDet. A data formata no histório é inserida
     * no objeto oTmktDet previamente.
     *
     * @param oTmktDet
     * @param data_atual
     * @param hora_atual
     * @param persistencia
     * @throws Exception
     */
    public void insereTmktDet(TmktDet oTmktDet, String data_atual, String hora_atual, Persistencia persistencia) throws Exception {
        String sql = "insert into TmktDet(Sequencia, Andamento, Data, Hora, TipoCont, CodPessoa, CodCont, CodFil, Historico, Detalhes, "
                + " CodPrestAtual, Operador, Dt_Alter, Hr_alter, Situacao, Longitude, Latitude, Precisao, FiltroWeb) "
                + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(oTmktDet.getSequencia());
            consulta.setInt(0);
            consulta.setString(oTmktDet.getData());
            consulta.setString(oTmktDet.getHora());
            consulta.setString(oTmktDet.getTipoCont());
            consulta.setString(oTmktDet.getCodPessoa());
            consulta.setString(oTmktDet.getCodCont());
            consulta.setBigDecimal(oTmktDet.getCodFil());
            consulta.setString(oTmktDet.getHistorico());
            consulta.setString(oTmktDet.getDetalhes());
            consulta.setString(oTmktDet.getCodPrestAtual());
            consulta.setString(FuncoesString.RecortaString(oTmktDet.getOperador(), 0, 10));
            consulta.setString(data_atual);
            consulta.setString(hora_atual);
            consulta.setString("PD");
            consulta.setString(oTmktDet.getLongitude());
            consulta.setString(oTmktDet.getLatitude());
            consulta.setString(oTmktDet.getPrecisao());
            consulta.setString(oTmktDet.getFiltroWeb());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TmktDetDao.insereTmktDet() - " + e.getMessage() + "\r\n"
                    + "insert into TmktDet(Sequencia, Andamento, Data, Hora, TipoCont, CodPessoa, Secao, CodFil, Historico, Detalhes, "
                    + " Operador, Dt_Alter, Hr_alter, Situacao, Longitude, Latitude, Precisao) "
                    + " values(" + oTmktDet.getSequencia() + "," + 0 + "," + oTmktDet.getData() + "," + oTmktDet.getHora() + "," + "V" + ","
                    + oTmktDet.getCodPessoa() + "," + oTmktDet.getCodCont() + "," + oTmktDet.getCodFil() + "," + oTmktDet.getHistorico() + ","
                    + oTmktDet.getDetalhes() + "," + FuncoesString.RecortaString(oTmktDet.getOperador(), 0, 10) + "," + data_atual + "," + hora_atual + ","
                    + "PD" + "," + oTmktDet.getLongitude() + "," + oTmktDet.getLatitude() + "," + oTmktDet.getPrecisao() + ")");
        }
    }

    /**
     * Atualiza TmktDet
     *
     * @param oTmktDet
     * @param data_atual
     * @param hora_atual
     * @param persistencia
     * @throws Exception
     */
    public void updateTmkt(TmktDet oTmktDet, String data_atual, String hora_atual, Persistencia persistencia) throws Exception {
        String sql = " update TmktDet set Andamento = ?, Data = ?, Hora = ?, TipoCont = ?, CodPessoa = ?, CodCont = ?, "
                + " CodFil = ?, Historico = ?, Detalhes = ?, Operador = ?, Dt_Alter = ?, Hr_alter = ?, Situacao = ?, "
                + " Longitude = ?, Latitude = ?, Precisao = ?, fotos = ?, QtdeFotos = ? where Sequencia = ? ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(0);
            consulta.setString(data_atual);
            consulta.setString(oTmktDet.getHora());
            consulta.setString(oTmktDet.getTipoCont());
            consulta.setBigDecimal(oTmktDet.getCodPessoa());
            consulta.setString(oTmktDet.getCodCont());
            consulta.setBigDecimal(oTmktDet.getCodFil());
            consulta.setString(oTmktDet.getHistorico());
            consulta.setString(oTmktDet.getDetalhes());
            consulta.setString(FuncoesString.RecortaString(oTmktDet.getOperador(), 0, 10));
            consulta.setString(data_atual);
            consulta.setString(hora_atual);
            consulta.setString("PD");
            consulta.setString(oTmktDet.getLongitude());
            consulta.setString(oTmktDet.getLatitude());
            consulta.setString(oTmktDet.getPrecisao());
            consulta.setString(oTmktDet.getFotos());
            consulta.setString(oTmktDet.getQtdeFotos());
            consulta.setBigDecimal(oTmktDet.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TmktDetDao.updateTmkt() - " + e.getMessage() + "\r\n"
                    + " update TmktDet set Andamento = " + 0 + ", Data = " + data_atual + ", Hora = " + oTmktDet.getHora() + ", "
                    + " TipoCont = " + "V" + ", CodPessoa = " + oTmktDet.getCodPessoa() + ", CodCont = " + oTmktDet.getCodCont() + ", "
                    + " CodFil = " + oTmktDet.getCodFil() + ", Historico = " + oTmktDet.getHistorico() + ", Detalhes = " + oTmktDet.getDetalhes() + ", "
                    + " Operador = " + FuncoesString.RecortaString(oTmktDet.getOperador(), 0, 10) + ", Dt_Alter = " + data_atual + ", Hr_alter = " + hora_atual + ", "
                    + " Situacao = " + "PD" + ", Longitude = " + oTmktDet.getLongitude() + ", Latitude = " + oTmktDet.getLatitude() + ","
                    + " Precisao = " + oTmktDet.getPrecisao() + " where Sequencia = " + oTmktDet.getSequencia() + " ");
        }
    }
}

<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/pessoas.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet"/>
            <script src="../assets/scripts/jquery.mask.js" type="text/javascript"></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />

            <script src="../assets/scripts/jquery.qrcode.js" type="text/javascript"></script>
            <script src="../assets/scripts/qrcode.js" type="text/javascript"></script>
            <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>


            <style>
                .objHidden{
                    display:none !important;
                }
                .ui-dialog-content[id*="formDietas"]{
                    padding-top:0px !important;
                }

                [id*="formEnviarQrCode"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="formEnviarQrCode"] .hasDatepicker{
                    text-align: left !important
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid:not([id*="tabelaDietas"]) [role="columnheader"] > span {
                        top: -3px !important;
                        position: relative !important;
                    }

                    .DataGrid[id*="tabelaDietas"] [role="columnheader"] > span {
                        top: -1px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    #divFundoGrid .DataGrid tbody tr,
                    #divFundoGrid .DataGrid tbody tr td{
                        border-bottom: none !important;
                        border-top: none !important;
                        background-color: #F3F3F3;
                    }

                    /*#divFundoGrid .DataGrid tbody tr td:first-child{
                        font-weight: 500 !important;
                    }
                    
                    #divFundoGrid .DataGrid tbody tr td:last-child{
                        font-weight: 500 !important;
                    }*/

                    #divFundoGrid .DataGrid tbody tr td span:nth-child(1){
                        font-weight: 500 !important;
                        min-width: 100px !important;
                        width: 100px !important;
                        max-width: 100px !important;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        text-align: right !important;
                        font-size: 8pt !important;
                    }

                    #divFundoGrid .DataGrid tbody tr td span:nth-child(2){
                        font-weight: bold !important;
                        text-align: left !important;
                        font-size: 8pt !important;
                    }
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    #divFundoGrid .DataGrid tbody tr td:first-child,
                    #divFundoGrid .DataGrid thead tr td:first-child,
                    #divFundoGrid .DataGrid thead tr th:first-child{
                        display: none;
                    }

                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid thead tr td:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2){
                        min-width:70px;
                        width:70px;
                        max-width:70px;
                    }

                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid thead tr td:nth-child(3),
                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid thead tr td:nth-child(4),
                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid thead tr td:nth-child(10),
                    .DataGrid thead tr th:nth-child(16),
                    .DataGrid thead tr td:nth-child(16),
                    .DataGrid thead tr th:nth-child(18),
                    .DataGrid thead tr td:nth-child(18){
                        min-width:300px;
                        width:300px;
                        max-width:300px;
                    }

                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid thead tr td:nth-child(6){
                        min-width:95px;
                        width:95px;
                        max-width:95px;
                    }

                    .DataGrid thead tr th:nth-child(17),
                    .DataGrid thead tr td:nth-child(17),
                    .DataGrid thead tr th:nth-child(22),
                    .DataGrid thead tr td:nth-child(22){
                        min-width:100px;
                        width:100px;
                        max-width:100px;
                    }

                    .DataGrid thead tr th:nth-child(11),
                    .DataGrid thead tr td:nth-child(11),
                    .DataGrid tbody tr td:nth-child(11),
                    .DataGrid thead tr th:nth-child(12),
                    .DataGrid thead tr td:nth-child(12),
                    .DataGrid tbody tr td:nth-child(12),
                    .DataGrid tbody tr td:nth-child(12),
                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid thead tr td:nth-child(10),
                    .DataGrid tbody tr td:nth-child(10){
                        min-width:200px;
                        width:200px;
                        max-width:200px;
                    }

                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid thead tr td:nth-child(5),
                    .DataGrid tbody tr td:nth-child(5),
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid thead tr td:nth-child(6),
                    .DataGrid tbody tr td:nth-child(6),
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid thead tr td:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7),
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid thead tr td:nth-child(8),
                    .DataGrid tbody tr td:nth-child(8),
                    .DataGrid thead tr th:nth-child(9),
                    .DataGrid thead tr td:nth-child(9),
                    .DataGrid tbody tr td:nth-child(9),
                    .DataGrid thead tr th:nth-child(13),
                    .DataGrid thead tr td:nth-child(13),
                    .DataGrid tbody tr td:nth-child(13){
                        min-width:130px;
                        width:130px;
                        max-width:130px;
                    }

                    .DataGrid thead tr th:nth-child(24),
                    .DataGrid thead tr td:nth-child(24),
                    .DataGrid tbody tr td:nth-child(24),
                    .DataGrid thead tr th:nth-child(16),
                    .DataGrid thead tr td:nth-child(16),
                    .DataGrid tbody tr td:nth-child(16){
                        min-width:80px;
                        width:80px;
                        max-width:80px;
                    }

                    .DataGrid thead tr th:nth-child(14),
                    .DataGrid thead tr td:nth-child(14),
                    .DataGrid tbody tr td:nth-child(14),
                    .DataGrid thead tr th:nth-child(15),
                    .DataGrid thead tr td:nth-child(15),
                    .DataGrid tbody tr td:nth-child(15),
                    .DataGrid thead tr th:nth-child(23),
                    .DataGrid thead tr td:nth-child(23),
                    .DataGrid tbody tr td:nth-child(23){
                        min-width:150px;
                        width:150px;
                        max-width:150px;
                    }

                    .DataGrid thead tr th:nth-child(19),
                    .DataGrid thead tr td:nth-child(19),
                    .DataGrid tbody tr td:nth-child(19),
                    .DataGrid thead tr th:nth-child(20),
                    .DataGrid thead tr td:nth-child(20),
                    .DataGrid tbody tr td:nth-child(20),
                    .DataGrid thead tr th:nth-child(21),
                    .DataGrid thead tr td:nth-child(21),
                    .DataGrid tbody tr td:nth-child(21){
                        min-width:90px;
                        width:90px;
                        max-width:90px;
                    }

                    .DataGrid thead tr th:nth-child(24),
                    .DataGrid thead tr td:nth-child(24),
                    .DataGrid tbody tr td:nth-child(24){
                        min-width:150px;
                        width:150px;
                        max-width:150px;
                    }

                    .DataGrid thead tr th:nth-child(17),
                    .DataGrid thead tr td:nth-child(17),
                    .DataGrid tbody tr td:nth-child(17),
                    .DataGrid thead tr th:nth-child(18),
                    .DataGrid thead tr td:nth-child(18),
                    .DataGrid tbody tr td:nth-child(18),
                    .DataGrid thead tr th:nth-child(19),
                    .DataGrid thead tr td:nth-child(19),
                    .DataGrid tbody tr td:nth-child(19){
                        min-width:140px;
                        width:140px;
                        max-width:140px;
                    }

                    .DataGrid thead tr th:nth-child(25),
                    .DataGrid thead tr td:nth-child(25),
                    .DataGrid tbody tr td:nth-child(25){
                        min-width:100px;
                        width:100px;
                        max-width:100px;
                    }

                    .DataGrid[id*="tabelaDietas"] thead tr th:nth-child(1),
                    .DataGrid[id*="tabelaDietas"] tbody tr th:nth-child(1){
                        min-width:50px;
                        width:50px;
                        max-width:50px;
                    }

                    .DataGrid[id*="tabelaDietas"] thead tr th:nth-child(4),
                    .DataGrid[id*="tabelaDietas"] tbody tr th:nth-child(4),
                    .DataGrid[id*="tabelaDietas"] thead tr th:nth-child(5),
                    .DataGrid[id*="tabelaDietas"] tbody tr th:nth-child(5),
                    .DataGrid[id*="tabelaDietas"] thead tr th:nth-child(6),
                    .DataGrid[id*="tabelaDietas"] tbody tr th:nth-child(6){
                        min-width:90px;
                        width:90px;
                        max-width:90px;
                    }

                    .DataGrid thead tr td,
                    .DataGrid tbody tr td,
                    .DataGrid thead tr th{
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formPesquisaRapida .ui-radiobutton {
                    background: transparent !important;
                }

                [id*="tabDadosGerais"],
                [id*="tabDadosPessoais"],
                [id*="tabFormacao"],
                [id*="tabDocumentos"],
                [id*="tabContatos"]{
                    background-color:#FFF !important;
                    padding-right: 20px!important;
                }

                .objHidden{
                    display: none;
                }

                #GerarQrCode #texto {
                    /*white-space: nowrap;
                    font-size: 10pt;
                    color: #666;
                    text-shadow: 1px 1px #FFF;
                    font-weight: bold;
                    margin-bottom: 18px;
                    background-color: #DDD;
                    border: thin solid #CCC;
                    border-radius: 50px;
                    padding: 5px 8px 5px 8px !important;*/
                    padding: 0px !important;
                    margin: 0px !important;
                    width: 100% !important;
                }

                .LogoQr{
                    position: absolute;
                    width:80px;
                    padding: 5px;
                    border-radius: 50px;
                    background-color: #FFF;
                    top:0;
                    right:0;
                    bottom:0;
                    left:0;
                    margin: auto;
                }

                .NomePessoa{
                    position: absolute;
                    width: calc(100% - 0px);
                    font-size: 14pt;
                    font-weight: bold !important;
                    text-transform: Capitalize;
                    left: 0px;
                    top: 0px;
                    background-color: #FFF;
                    z-index: 1;
                    height: 60px;
                    padding-top: 14px;
                    padding-left: 80px;
                    border-bottom: thin solid #DDD !important;
                    border-top: 4px solid #3C8DBC !important;
                    color: #3C8DBC !important;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .IdPessoa{
                    position: absolute;
                    background-color: orangered !important;
                    color: #FFF !important;
                    font-weight: 500 !important;
                    left: 8px;
                    top: 0px;
                    z-index: 2;
                    width: 60px !important;
                    text-align: center;
                    border-radius: 100px;
                    top: 20px;
                    padding-bottom: 2px !important;
                }

                .ui-tabs-nav li:nth-child(4){
                    display: none;
                } 

                [id*="formCadastrar:dlgCadastrar"]{
                    padding-top: 3px !Important
                }

                /*canvas{
                    padding: 20px 0px 0px 0px !important;
                    padding: 0px !important;
                    width: 300px !Important;
                    height: 300px !important;
                    text-align: center !important;
                }*/

                .ui-widget-content[id*="formEnviarQrCode"]{
                    overflow: hidden !important;
                }

                [id*="btGerarChaveAcesso"], .btGerarChaveAcesso{
                    position: absolute !important;
                    padding: 0px 1px 1px 1px !important;
                    font-size: 7pt !important;
                    right: 5px !important;
                    margin-top: -2px !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{pessoa.Persistencias(login.pp, login.satellite)}"/>
                <f:viewAction action="#{pessoa.listarPessoas}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_pessoas.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Pessoas}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{pessoa.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">#{pessoa.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                        <label class="FilialEndereco">#{pessoa.filiais.endereco}</label>
                                        <label class="FilialBairroCidade">#{pessoa.filiais.bairro}, #{pessoa.filiais.cidade}/#{pessoa.filiais.UF}</label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3" style="padding:0px 10px 0px 0px !important; text-align: right !important">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="p" oncomplete="PF('dlgPesquisaRapida').show();" actionListener="#{pessoa.PrePesquisa}" update="formPesquisaRapida"/>
                    <p:hotkey bind="e" actionListener="#{pessoa.buttonAction}" update="msgs formCadastrar:cadastrar"/>
                    <p:hotkey bind="a" actionListener="#{pessoa.PreCadastro}" update="formCadastrar:cadastrar formCadastrar"/>
                    <p:hotkey bind="shift+x" oncomplete="PF('dlgExportar').show();" actionListener="#{exportarMB.setTitulo(localemsgs.Pessoas)}"/>
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <!--<p:panel style="overflow:hidden !important; max-width:100% !important; min-height:100% !important; height:100% !important;max-height:100% !important; position:relative;">-->
                                <p:panel style="display: inline">
                                    <p:dataTable id="tabela"
                                                 value="#{pessoa.pessoasCompleto}"
                                                 paginator="true"
                                                 rows="20"
                                                 lazy="true"
                                                 reflow="true"
                                                 rowsPerPageTemplate="20, 25, 50, 100"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Pessoas}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="listaPessoa"
                                                 selectionMode="single"
                                                 styleClass="tabela"
                                                 selection="#{pessoa.pessoaSelecionada}"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true"
                                                 class="tabela DataGrid"
                                                 scrollHeight="100%"
                                                 rowKey="#{listaPessoa.codigo}"
                                                 style="font-size: 12px; background: white"
                                                 rowStyleClass="#{!pessoa.mostraExcluidos and listaPessoa.situacao eq 'E'?' objHidden':''}"
                                                 >
                                        <p:ajax event="rowDblselect" listener="#{pessoa.dblSelect}" update="formCadastrar msgs"/>

                                        <p:column headerText="">
                                            <label class="NomePessoa">#{listaPessoa.nome}</label>
                                            <label class="IdPessoa">#{localemsgs.Nome}</label>
                                            <i class="fa fa-user" style="width: 60px; height: 60px; border: thin solid #CCC; border-radius: 50%; font-size: 20pt !important; text-align: center; padding-top: 15px; margin: 10px 0px 0px 10px !important"></i>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Codigo}" exportable="#{pessoa.eCodigo}" class="text-center">
                                            <h:outputText value="#{listaPessoa.codigo}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nome}" exportable="#{pessoa.eNome}">
                                            <h:outputText value="#{listaPessoa.nome}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Email}" exportable="#{pessoa.eEmail}" class="text-center">
                                            <h:outputText value="#{listaPessoa.email}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.RG}" exportable="#{pessoa.eRG}" class="text-center">
                                            <h:outputText value="#{listaPessoa.RG}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.RGOrg}" exportable="#{pessoa.eOrgEmis}" class="text-center">
                                            <h:outputText value="#{listaPessoa.RGOrgEmis}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CPF}" exportable="#{pessoa.eCPF}" class="text-center">
                                            <h:outputText value="#{listaPessoa.CPF}" converter="conversorCPF" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Fone1}" exportable="#{pessoa.eFone1}" class="text-center">
                                            <h:outputText value="#{listaPessoa.fone1}" converter="conversorFone" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Fone2}" exportable="#{pessoa.eFone2}" class="text-center">
                                            <h:outputText value="#{listaPessoa.fone2}" converter="conversorFone" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Endereco}" exportable="#{pessoa.eEnde}">
                                            <h:outputText value="#{listaPessoa.endereco}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Bairro}" exportable="#{pessoa.eBairro}" class="text-center">
                                            <h:outputText value="#{listaPessoa.bairro}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Cidade}" exportable="#{pessoa.eCidade}" class="text-center">
                                            <h:outputText value="#{listaPessoa.cidade}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.UF}" exportable="#{pessoa.eUF}" class="text-center">
                                            <h:outputText value="#{listaPessoa.UF}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Complemento}" exportable="#{pessoa.Complemento}" class="text-center">
                                            <h:outputText value="#{listaPessoa.complemento}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CEP}" exportable="#{pessoa.eCEP}" class="text-center">
                                            <h:outputText value="#{listaPessoa.CEP}" converter="conversorCEP" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Tipo}" exportable="#{pessoa.eSituacao}" class="text-center">
                                            <h:outputText value="#{listaPessoa.situacao}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Obs}" exportable="#{pessoa.eObs}">
                                            <h:outputText value="#{listaPessoa.obs}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Matr}" exportable="#{pessoa.eMatr}" class="text-center">
                                            <h:outputText value="#{listaPessoa.matr}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}">
                                                <f:convertNumber pattern="000000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Funcao}" exportable="#{pessoa.eFuncao}">
                                            <h:outputText value="#{listaPessoa.funcao}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Sexo}" exportable="#{pessoa.eSexo}" class="text-center">
                                            <h:outputText value="#{listaPessoa.sexo}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Altura}" exportable="#{pessoa.eAltura}" class="text-center">
                                            <h:outputText value="#{listaPessoa.altura}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}">
                                                <f:convertNumber pattern="000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Peso}" exportable="#{pessoa.ePeso}" class="text-center">
                                            <h:outputText value="#{listaPessoa.peso}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}">
                                                <f:convertNumber pattern="000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" exportable="#{pessoa.eOperador}" class="text-center">
                                            <h:outputText value="#{listaPessoa.operador}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}" exportable="#{pessoa.eDtAlter}" class="text-center">
                                            <h:outputText value="#{listaPessoa.dt_Alter}" converter="conversorData" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}" exportable="#{pessoa.eHrAlter}" class="text-center">
                                            <h:outputText converter="conversorHora" value="#{listaPessoa.hr_Alter}" style="color: #{listaPessoa.situacao eq 'E' ?'red':''}"/>
                                        </p:column>
                                    </p:dataTable>
                                    <script>
                                        // <![CDATA[
                                        let refCalc = 0;

                                        if (ObterParamURL('selecao') &&
                                                ObterParamURL('selecao') === 'S')
                                            refCalc = 54;
                                        else
                                            refCalc = 148;

                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - refCalc) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - refCalc) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 190px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.AutorizacaoAcesso}" actionListener="#{pessoa.abrirQrCode}"
                                           update="msgs">
                                <p:graphicImage url="../assets/img/icone_qr_code.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Usuario}" actionListener="#{pessoa.preUsuario}"
                                           update="msgs formCadastrarUsuario:cadastrar">
                                <p:graphicImage url="../assets/img/icon_usuario_senha.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{pessoa.PreCadastro}"
                                           update="formCadastrar:cadastrar formCadastrar">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{pessoa.buttonAction}"
                                           update="msgs formCadastrar:cadastrar">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="display: none; padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisar').show();"
                                           actionListener="#{pessoa.PrePesquisa}" update="formPesquisar">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisaRapida').show();"
                                           actionListener="#{pessoa.PrePesquisa}" update="formPesquisaRapida">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{pessoa.LimparFiltros}"
                                           update="msgs main:tabela">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Exportar}" oncomplete="PF('dlgExportar').show();"
                                           actionListener="#{exportarMB.setTitulo(localemsgs.Pessoas)}">
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>

                </h:form>

                <!--Dados Usuário-->
                <h:form class="form-inline" id="formCadastrarUsuario">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrarUsuario').hide()"/>
                    <p:dialog widgetVar="dlgCadastrarUsuario" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" focus="nivel"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrarUsuario" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_trocarsenha.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.UsuarioSenha}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; padding: 0px !important;" class="cadastrar">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; padding: 0px 0px 0px 0px !important; margin:0px !important;">
                                <p:outputLabel for="nivel" value="#{localemsgs.Nivel}" style="float: left; margin: 0px !important"/>
                                <p:selectOneMenu value="#{pessoa.pessoaLoginUsuario.saspw.nivelx}" id="nivel"
                                                 required="true" label="#{localemsgs.Nivel}" style="width: 100%" disabled="#{pessoa.pessoaLoginUsuario.saspw.nivelx eq null or pessoa.pessoaLoginUsuario.saspw.nivelx.toString() eq '' ? false: true}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nivel}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{pessoa.niveis}" />
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12" style="text-align: right; padding: 10px 4px 0px 0px !important;">
                                <p:outputLabel for="senhaNova" value="#{localemsgs.Senha}" style="float: left"/>
                                <p:password id="senhaNova" required="true" transient="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                            label="#{localemsgs.Senha}" feedback="true" redisplay="true" match="confirmacao"
                                            promptLabel="#{localemsgs.DigiteSenha}" 
                                            weakLabel="#{localemsgs.SenhaFraca}"
                                            goodLabel="#{localemsgs.SenhaBoa}" 
                                            strongLabel="#{localemsgs.SenhaForte}"
                                            value="#{pessoa.pessoaLoginUsuario.saspw.PW}"
                                            style="width: 100%">
                                    <f:validateRegex pattern="^[0-9]{5,20}$" for="senhaNova"/>
                                </p:password>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="text-align: right; padding: 10px 0px 0px 4px !important;">
                                <p:outputLabel for="confirmacao" value="#{localemsgs.Confirmacao}" style="float: left"/>
                                <p:password id="confirmacao" 
                                            value="#{pessoa.pessoaSelecionada.PWWeb}" redisplay="true"
                                            label="#{localemsgs.Senha}" style="width: 100%">
                                </p:password>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; padding: 10px 0px 0px 0px !important;">
                                <p:commandLink id="cadastro" 
                                               action="#{pessoa.cadastroSenha}" 
                                               update=":main:tabela :msgs"
                                               styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--QR Code por WhatsApp-->
                <h:form class="form-inline" id="formEnviarQrCode">
                    <p:hotkey bind="esc" oncomplete="PF('dlgEnviarQrCode').hide()"/>

                    <p:dialog widgetVar="dlgEnviarQrCode" focus="txtQrData" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgEnviarQrCode" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; width:400px !important; max-width:400px !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important; overflow: hidden !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_qr_code.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.AutorizacaoAcesso}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important;overflow: hidden !important" class="cadastrar">
                            <p:remoteCommand name="rcEnviarQrCode" partialSubmit="true" 
                                             update="msgs"
                                             process="@this"
                                             actionListener="#{pessoa.guardarQrCOde}" /> 
                            <div class="col-md-12 col-sm-12 col-xs-12" style="height: 300px !important; overflow: hidden !important; padding: 0px">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding-left: 0px; padding-right: 0px;">
                                    <label for="txtQrData">#{localemsgs.Data}</label>
                                    <p:calendar id="txtQrData" value="#{pessoa.acessoAut.data}"
                                                required="true"  placeholder="##/##/####"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                locale="#{localeController.getCurrentLocale()}" styleClass="calendario" showOn="button" 
                                                style="width: 100%; top: 0 !important; text-align: left !important">
                                        <p:watermark for="txtQrData" value="##/##/####"/>
                                    </p:calendar>
                                </div>

                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 10px; padding-left: 0px; padding-right: 0px;">
                                    <label for="txtQrData" style="width: 100% !important; text-align: left">#{localemsgs.Horario}</label>
                                    <div class="col-md-6 col-sm-6 col-xs-12" style="padding-left: 0px; width: calc(50% - 20px); padding-right: 0px;">
                                        <p:inputText id="txtQrHora1" value="#{pessoa.segAutorizaArea.hrEntrada}" type="time"
                                                     maxlength="5" style="width: 100%; top: 0 !important;" >
                                            <p:watermark for="txtQrHora1" value="##:##"/>
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-1 col-sm-1 col-xs-1" style="width: 40px; padding: 8px 0px 0px 0px;">
                                        <label style="width: 100%; text-align: center;">#{localemsgs.Ate}</label>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-12"  style="padding-right: 0px; width: calc(50% - 20px); padding-left: 0px;">
                                        <p:inputText id="txtQrHora2" value="#{pessoa.segAutorizaArea.hrSaida}" type="time"
                                                     maxlength="5" style="width: 100%; top: 0 !important;" >
                                            <p:watermark for="txtQrHora2" value="##:##"/>
                                        </p:inputText>
                                    </div>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 10px; padding-left: 0px; padding-right: 0px;">
                                    <label style="width: 100%; text-align: left;">#{localemsgs.Mensagem} (<span id="spnQtdeCaracter">100</span> #{localemsgs.Caracteres})</label>
                                    <p:inputTextarea id="txtQrMensagem" value="#{pessoa.segAutorizaArea.mensagem}"
                                                     rows="3" maxlength="100"
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Mensagem}"
                                                     style="width: 100%; top: 0 !important;">
                                    </p:inputTextarea>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 10px; padding-left: 0px; padding-right: 0px;">
                                    <p:commandLink id="cadastro" action="#{pessoa.enviarQrCode}" update="msgs"
                                                   styleClass="btn btn-primary" style="width: 100% !important">
                                        <i class="fa fa-reply" style="margin-right:8px !important"></i>#{localemsgs.EnviarAutorizacao}
                                    </p:commandLink>
                                </div>
                            </div>

                            <div id="GerarQrCode" style="margin-top: 8px; height: 300px; padding-top: 3px !important; text-align: center !important; padding: 0px !important; position: absolute;top: 340px; width: 100% !important">
                                <div id="divImgQr" style="background-color: #FFF; padding: 0px 5px 5px 0px !important;height: 300px !important; width: 100% !important; margin: 0px !important">

                                </div>
                            </div>
                        </p:panel>
                    </p:dialog>
                    <script>
                        // <![CDATA[
                        $(document).ready(function () {


                        }).on('keyup', '[id*="txtQrMensagem"]', function () {
                            $('#spnQtdeCaracter').text((100 - $(this).val().length).toString());
                        });

                        function gerarHtml() {
                            setTimeout(function () {
                                var isSafari = navigator.vendor && navigator.vendor.indexOf('Apple') > -1 &&
                                        navigator.userAgent &&
                                        navigator.userAgent.indexOf('CriOS') == -1 &&
                                        navigator.userAgent.indexOf('FxiOS') == -1;

                                let MarginQr = isSafari ? '3' : '';

                                let HtmlQr = '<div id="texto" style="text-align: center; width: 260px !important; padding: ' + MarginQr + '0px 0px 0px 0px !important; margin: 0px !important;"></div>' +
                                        '<div id="qr" style="position: relative; width: 200px !important; height: 200px !Important; padding: 0px !important; margin: 0px 0px 0px ' + MarginQr + '0px !important; text-align: center !important;"></div>';

                                if (isSafari) {
                                    $('#divImgQr').html(HtmlQr);
                                } else {
                                    $('#divImgQr').html('<center>' + HtmlQr + '</center>');
                                }
                            }, 2000);
                        }

                        function enviarQrCode(inSequencia, inData, inHorario, inLogo) {
                            GerarQr(inSequencia, inData, inHorario, inLogo);
                        }

                        function GerarQr(inSequencia, inData, inHorario, inLogo, CallBack) {
                            try {
                                const DadosQr = inSequencia.lpad('0', 6) + ' ' + inData + ' ' + inHorario;
                                const LarguraQr = 200;
                                const AlturaQr = 200;

                                $('.LogoQr, #canvasQrClone').remove();
                                $('#GerarQrCode #texto, #GerarQrCode #qr').html('');
                                $('#GerarQrCode').css('display', '');
                                //$('#GerarQrCode #texto').css('max-width', (LarguraQr + 40) + 'px');

                                $('#GerarQrCode #qr').qrcode({
                                    width: LarguraQr,
                                    height: AlturaQr,
                                    text: DadosQr
                                });

                                if (inLogo && inLogo != '')
                                    $('#GerarQrCode #qr').prepend('<img src="' + ReplaceAll(inLogo, 'https://mobile.sasw.com.br:9091/satellite/logos/', '../assets/img/') + '" style="position: absolute;width:80px;padding: 5px;border-radius: 50px;background-color: #FFF;top:0;right:0;bottom:0;left:0;margin: auto;" />');

                                let imgTexto = '../assets/img/';

                                switch ('#{localeController.number}') {
                                    case '2':
                                        imgTexto += 'AutorizacaoEsp.png';
                                        break;

                                    case '3':
                                        imgTexto += 'AutorizacaoIng.png';
                                        break;

                                    case '1':
                                        imgTexto += 'AutorizacaoPt.png';
                                        break;
                                }

                                $('#GerarQrCode #texto').html('<img src="' + imgTexto + '" style="width: 260px !important; margin-left: 0px; margin-top: 30px !important" />');

                                html2canvas(document.querySelector("#divImgQr")).then(canvas => {
                                    canvas.id = 'canvasQrClone';

                                    $('#GerarQrCode').prepend(canvas);

                                    rcEnviarQrCode([{name: 'model', value: canvas.toDataURL('image/jpeg').toString()}]);
                                    if (typeof CallBack == 'function')
                                        CallBack.call();
                                });

                            } catch (e) {
                                alert('Erro ao gerar QR Code.\n\nMensagem de Erro: ' + e.toString())
                            }
                        }
                        // ]]>
                    </script>
                </h:form>




                <!--Cadastrar novo-->
                <h:form class="form-inline" id="formCadastrar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute"  focus="cpf" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <script>
                            /* $(document).ready(function () {
                             //first unbind the original click event
                             PF('dlgCadastrar').closeIcon.unbind('click');
                             
                             //register your own
                             PF('dlgCadastrar').closeIcon.click(function (e) {
                             $("#formCadastrar\\:botaoFechar").click();
                             //should be always called
                             e.preventDefault();
                             });
                             })*/
                        </script>
                        <!--                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                                                 oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                                                </p:commandButton>-->
                        <f:facet name="header">
                            <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarPessoa}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; padding: 0px !important" class="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#EEE !important; padding-top: 0px !important; margin-top: 0px !important">

                                <p:outputLabel for="situacao" value="#{localemsgs.Tipo}: "/>
                                <p:selectOneMenu value="#{pessoa.novaPessoa.situacao}" style="width: 100%" required="true"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}" 
                                                 id="situacao" disabled="#{(pessoa.editaSituacao or pessoa.edicao) and login.pp.empresa != 'SATMAXIMA'}">
                                    <p:ajax event="itemSelect" update="tabs panelCPF panelRG panelItems1 panelItems2 panelItems3"/>
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Autonomo}" itemValue="A"/>
                                    <f:selectItem itemLabel="#{localemsgs.BBloqueado}" itemValue="B"/>
                                    <f:selectItem itemLabel="#{localemsgs.Candidato}" itemValue="C"/>
                                    <f:selectItem itemLabel="#{localemsgs.Diretor}" itemValue="D"/>
                                    <c:if test="#{login.pp.empresa eq 'SATMAXIMA'}">
                                        <f:selectItem itemLabel="E - #{localemsgs.Excluido}" itemValue="E'"/>
                                    </c:if>
                                    <f:selectItem itemLabel="#{localemsgs.Funcionario}" itemValue="F" itemDisabled="#{login.pp.empresa != 'SATMAXIMA'}"/>
                                    <c:if test="#{login.pp.empresa eq 'SATMAXIMA'}">
                                        <f:selectItem itemLabel="#{localemsgs.IInterno}" itemValue="I"/>
                                    </c:if>
                                    <f:selectItem itemLabel="L - #{localemsgs.Fiscal}" itemValue="L" />
                                    <f:selectItem itemLabel="#{localemsgs.OOutros}" itemValue="O"/>
                                    <f:selectItem itemLabel="#{localemsgs.Prestador}" itemValue="P"/>
                                    <f:selectItem itemLabel="#{localemsgs.Socio}" itemValue="S"/>
                                    <f:selectItem itemLabel="#{localemsgs.Visitante}" itemValue="V"/>
                                    <f:selectItem itemLabel="#{localemsgs.Visitanteweb}" itemValue="W" />
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panel id="panelCPF" style="padding: 0px !important; width: 100% !important">

                                <div id="divCPF" class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px 10px 0px 5px !important">
                                    <p:outputLabel for="cpf" value="#{localemsgs.CPF}: " 
                                                   rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}" 
                                                   style="display:#{localeController.number eq 1?'':'none'}" />
                                    <p:inputMask id="cpf" value="#{pessoa.novaPessoa.CPF}" disabled="#{pessoa.flag eq 2 and pessoa.novaPessoa.CPF ne null and pessoa.novaPessoa.CPF ne ''}" 
                                                 style="width: 100%;display:#{localeController.number eq 1?'':'none'}"
                                                 maxlength="11" mask="#{mascaras.mascaraCPF}" 
                                                 rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}">
                                        <p:watermark for="cpf" value="#{localemsgs.CPF}"/>
                                        <p:ajax event="blur" listener="#{pessoa.BuscarPessoaLocal}"
                                                update="formCadastrar:tabs:cep_pesquisa msgs"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px 5px 0px 0px !important">
                                    <p:outputLabel for="matr" value="#{localemsgs.Matr}: "
                                                   rendered="#{pessoa.novaPessoa.situacao eq 'F'}"/>
                                    <p:inputText id="matr" value="#{pessoa.novaPessoa.matr}" disabled="true"
                                                 label="#{localemsgs.Matr}" maxlength="15" style="width: 100%"
                                                 rendered="#{pessoa.novaPessoa.situacao eq 'F'}">
                                        <f:convertNumber pattern="000000"/>
                                        <p:watermark for="matr" value="#{localemsgs.Matr}"/>
                                    </p:inputText>
                                </div>
                            </p:panel>

                            <p:panel id="panelRG" style="padding: 0px 0px 0px 0px !important; width: 100% !important;">
                                <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px 10px 0px 5px !important;margin-top: 5px !important">
                                    <p:outputLabel for="rgorg" value="#{localemsgs.RGOrg}: " rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}" />
                                    <p:inputText id="rgorg" value="#{pessoa.novaPessoa.RGOrgEmis}" disabled="#{pessoa.edicao}"
                                                 required="true" label="#{localemsgs.RGOrg}" style="width: 100%;"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.RGOrg}" 
                                                 rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}"
                                                 maxlength="15">
                                        <p:watermark for="rgorg" value="#{localemsgs.RGOrg}"/>
                                        <p:ajax event="change" process="@this" listener="#{pessoa.GerarRG}" update="rg"></p:ajax>
                                    </p:inputText>
                                </div>

                                <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px 5px 0px 0px !important;margin-top: 5px !important">
                                    <p:outputLabel for="rg" value="#{localemsgs.RG}: " rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}"/>
                                    <p:inputText id="rg" value="#{pessoa.novaPessoa.RG}"
                                                 required="true" label="#{localemsgs.RG}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.RG}" rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}"
                                                 maxlength="20">
                                        <p:watermark for="rg" value="#{localemsgs.RG}"/>
                                    </p:inputText>
                                </div>
                            </p:panel>

                            <p:panelGrid id="panelItems1" columns="1" columnClasses="ui-grid-col-12"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="nome" value="#{localemsgs.Nome}: " />
                                <p:inputText id="nome" value="#{pessoa.novaPessoa.nome}" style="width: 100%"
                                             required="true" label="#{localemsgs.Nome}" disabled="#{pessoa.edicao and pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                             maxlength="50">
                                    <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid id="panelItems2" columns="3" columnClasses="ui-grid-col-2,ui-grid-col-9,ui-grid-col-1"
                                         layout="grid" styleClass="ui-panelgrid-blank" rendered="#{pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}">

                                <p:outputLabel for="dieta" value="#{localemsgs.Dieta}: " rendered="#{pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}"/>

                                <p:selectOneMenu id="dieta" value="#{pessoa.novaPessoa.codDieta}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Dieta}"
                                                 style="width: 100%;"
                                                 filter="true" filterMatchMode="contains"
                                                 rendered="#{pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}">
                                    <f:selectItems value="#{pessoa.listaDietas}" var="dietas" itemValue="#{dietas.sequencia}"
                                                   itemLabel="#{dietas.descricao}"  noSelectionValue="Selecione"/>
                                </p:selectOneMenu>
                                <p:commandLink title="#{localemsgs.CadastrarDieta}"
                                               partialSubmit="true" process="@this" id="btNovaDieta"
                                               update="panelItems2 msgs"
                                               actionListener="#{pessoa.novaDieta()}">
                                    <p:graphicImage url="../assets/img/icone_configuracoes.png" height="36" width="36"/>
                                </p:commandLink>
                            </p:panelGrid>
                            

                            <p:panelGrid id="panelItems3" columns="1" columnClasses="ui-grid-col-12"
                                         layout="grid" styleClass="ui-panelgrid-blank">

                                <p:outputLabel for="sexoInterno" value="#{localemsgs.Sexo}: " rendered="#{pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}"/>
                                <p:selectOneMenu value="#{pessoa.novaPessoa.sexo}" id="sexoInterno" style="width: 100%"
                                                 rendered="#{pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}" 
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Sexo}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Masculino}" itemValue="M"/>
                                    <f:selectItem itemLabel="#{localemsgs.Feminino}" itemValue="F"/>
                                    <f:selectItem itemLabel="#{localemsgs.Outros}" itemValue="O"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="email" value="#{localemsgs.Email}: " rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}"/>
                                <p:inputText id="email" value="#{pessoa.novaPessoa.email}" style="width: 100%"
                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"
                                             maxlength="50" disabled="#{pessoa.edicao}" rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}">
                                    <p:watermark for="email" value="#{localemsgs.Email}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:tabView id="tabs" activeIndex="0" onTabShow="PF('dlgCadastrar').initPosition()" dynamic="true"  style="background-color:#EEE !important;">
                                <!--Dados Gerais-->
                                <p:tab id="tabDadosGerais" title="#{localemsgs.DadosGerais}">
                                    <div style="max-height:160px !important; overflow: auto !important;">
                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;" rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}">
                                            <p:outputLabel for="fone1" value="#{localemsgs.Fone1}: " />
                                            <p:inputText id="fone1" value="#{pessoa.novaPessoa.fone1}" disabled="#{pessoa.edicao}"
                                                         maxlength="11" style="width: 100%">
                                                <p:watermark for="fone1" value="#{localemsgs.Fone1}"/>
                                            </p:inputText>

                                            <p:outputLabel for="fone2" value="#{localemsgs.Fone2}: " style="float: right" />
                                            <p:inputText id="fone2" value="#{pessoa.novaPessoa.fone2}" disabled="#{pessoa.edicao}"
                                                         maxlength="11" style="width: 100%" >
                                                <p:watermark for="fone2" value="#{localemsgs.Fone2}"/>
                                            </p:inputText>
                                        </p:panelGrid>


                                        <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-6"
                                                     layout="grid" styleClass="ui-panelgrid-blank"  style="background-color:#FFF" rendered="#{pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}">
                                            <p:outputLabel for="cliente" value="#{localemsgs.Cliente}:"/>
                                            <p:autoComplete id="cliente" value="#{pessoa.clientes}" styleClass="cliente"
                                                            style="width: 100%"
                                                            completeMethod="#{pessoa.ListarClientes}" required="true" scrollHeight="200"
                                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}" forceSelection="true"
                                                            var="cli" itemLabel="#{cli.NRed}" itemValue="#{cli}" converter="conversorCliente">
                                                <p:ajax event="itemSelect" listener="#{pessoa.SelecionarCliente}"
                                                        update="panelDadosEndereco nomeCliente"/>
                                                <p:watermark for="cliente" value="#{localemsgs.Cliente}" />
                                            </p:autoComplete>

                                            <p:inputText id="nomeCliente" value="#{pessoa.clientes.nome}" disabled="true"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}" required="true"
                                                         style="width: 100%">
                                                <p:watermark for="nomeCliente" value="#{localemsgs.Cliente}" />
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panel id="panelDadosEndereco" style="width:100% !important; padding:0px !important; margin:0px !important">
                                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-1,ui-grid-col-2,ui-grid-col-4"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                                <p:outputLabel for="cep" value="#{localemsgs.CEP}: " />
                                                <p:inputText id="cep" disabled="#{pessoa.edicao or pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}"
                                                             required="true"
                                                             label="#{localemsgs.CEP}" maxlength="8" style="width: 100%" value="#{pessoa.novaPessoa.CEP}" >
                                                    <p:watermark for="cep" value="#{localemsgs.CEP}"/>
                                                </p:inputText>

                                                <p:commandLink title="#{localemsgs.Pesquisar}"
                                                               partialSubmit="true" process="@this formCadastrar:tabs:cep" id="cep_pesquisa" disabled="#{pessoa.edicao}"
                                                               update="formCadastrar:tabs:cep formCadastrar:tabs:ende formCadastrar:tabs:bairro formCadastrar:tabs:cidade formCadastrar:tabs:uf msgs"
                                                               actionListener="#{pessoa.Endereco}" >
                                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30" rendered="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}"/>
                                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.AtualizarCEP}" icon="ui-icon-alert" />
                                                    <p:dialog header="#{localemsgs.Aviso}" widgetVar="dlgOk" resizable="false" 
                                                              draggable="false" closable="true" width="300">
                                                        <div class="form-inline">
                                                            <h:outputText value="#{localemsgs.CompletarEndereco}" style="text-align: center"/>
                                                            <p:spacer height="20px"/>
                                                        </div>
                                                        <div style="text-align:center">
                                                            <p:commandButton value="#{localemsgs.OK}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                                             onclick="PF('dlgOk').hide();" />
                                                        </div>
                                                    </p:dialog>
                                                </p:commandLink>

                                                <p:outputLabel for="bairro" value="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? localemsgs.Bairro: localemsgs.Bairro}: " style="float: right"/>
                                                <p:inputText id="bairro" value="#{pessoa.novaPessoa.bairro}" disabled="#{pessoa.edicao or pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}"
                                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? localemsgs.Bairro: localemsgs.Bloco}"
                                                             maxlength="20" style="width: 100%">
                                                    <p:watermark for="bairro" value="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? localemsgs.Bairro: localemsgs.Bairro}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? '10': '10'}"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                                <p:outputLabel for="ende" value="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? localemsgs.Endereco: localemsgs.Endereco}: "/>
                                                <p:inputText id="ende" value="#{pessoa.novaPessoa.endereco}" disabled="#{pessoa.edicao or pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}"
                                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? localemsgs.Endereco: localemsgs.AlaCela}"
                                                             maxlength="50" style="width: 100%">
                                                    <p:watermark for="ende" value="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? localemsgs.Endereco: localemsgs.Endereco}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                                <p:outputLabel for="cidade" value="#{localemsgs.Cidade}: " />
                                                <p:autoComplete id="cidade" value="#{pessoa.novaPessoa.cidade}" styleClass="cidade" disabled="#{pessoa.edicao or pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}"
                                                                required="true" label="#{localemsgs.Cidade}" completeMethod="#{pessoa.BuscarCidade}"
                                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}" scrollHeight="200"
                                                                forceSelection="true" placeholder="#{localemsgs.Cidade}" style="width: 100%">
                                                    <p:ajax event="itemSelect" listener="#{pessoa.SelecionarCidade}"
                                                            update="formCadastrar:tabs:cidade formCadastrar:tabs:uf"/>
                                                    <p:watermark for="cidade" value="#{localemsgs.Cidade}"/>
                                                </p:autoComplete>

                                                <p:outputLabel for="uf" value="#{localemsgs.UF}: " style="float: right"/>
                                                <p:inputText id="uf" value="#{pessoa.novaPessoa.UF}" disabled="#{pessoa.edicao or pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}"
                                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.UF}"
                                                             label="#{localemsgs.UF}" maxlength="2" style="width: 100%" >
                                                    <p:watermark for="uf" value="#{localemsgs.UF}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;" rendered="#{pessoa.novaPessoa.situacao eq 'I' or pessoa.novaPessoa.situacao eq 'E'}">
                                                <p:outputLabel for="complemento" value="#{localemsgs.Complemento}: "/>
                                                <p:inputText id="complemento" value="#{pessoa.novaPessoa.complemento}"
                                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Complemento}"
                                                             maxlength="255" style="width: 100%">
                                                    <p:watermark for="complemento" value="#{localemsgs.Complemento}"/>
                                                </p:inputText>
                                            </p:panelGrid>
                                        </p:panel>
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="funcao" value="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? localemsgs.Funcao:localemsgs.Prontuario}: "/>
                                            <p:inputText id="funcao" value="#{pessoa.novaPessoa.funcao}" disabled="#{pessoa.edicao and pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}"
                                                         label="#{localemsgs.Funcao}" style="width: 100%" maxlength="40">
                                                <p:watermark for="funcao" value="#{pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'? localemsgs.Funcao:localemsgs.Prontuario}"/>
                                            </p:inputText>

                                            <p:outputLabel for="obs" value="#{localemsgs.Obs}: "/>
                                            <p:inputText id="obs" value="#{pessoa.novaPessoa.obs}" disabled="#{pessoa.edicao and pessoa.novaPessoa.situacao ne 'I' and pessoa.novaPessoa.situacao ne 'E'}"
                                                         label="#{localemsgs.Obs}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                                            </p:inputText>
                                        </p:panelGrid>
                                    </div>
                                </p:tab>

                                <p:tab id="tabDadosPessoais" title="#{localemsgs.DadosPessoais}"
                                       rendered="#{pessoa.novaPessoa.situacao eq 'C' or pessoa.novaPessoa.situacao eq 'F'}">
                                    <div style="max-height:160px !important; overflow: auto !important;">
                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="dt_nasc" value="#{localemsgs.Dt_Nasc}:" />
                                            <p:calendar id="dt_nasc" value="#{pessoa.novaPessoa.dt_nasc}" disabled="#{pessoa.edicao}"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario" showOn="button" 
                                                        style="width: 100%; top: 0 !important;">
                                                <p:watermark for="dt_nasc" value="01/01/1971"/>
                                            </p:calendar>

                                            <p:outputLabel for="altura" value="#{localemsgs.Altura}: "/>
                                            <p:inputText id="altura" value="#{pessoa.novaPessoa.altura}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.Altura}" maxlength="15" style="width: 100%">
                                                <f:convertNumber pattern="000"/>
                                                <p:watermark for="altura" value="#{localemsgs.Altura}"/>
                                            </p:inputText>

                                            <p:outputLabel for="peso" value="#{localemsgs.Peso}: " />
                                            <p:inputText id="peso" value="#{pessoa.novaPessoa.peso}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.Peso}" maxlength="15" style="width: 100%">
                                                <f:convertNumber pattern="000"/>
                                                <p:watermark for="peso" value="#{localemsgs.Peso}"/>
                                            </p:inputText>

                                            <p:outputLabel for="sexo" value="#{localemsgs.Sexo}: "/>
                                            <p:selectOneMenu value="#{pessoa.novaPessoa.sexo}" id="sexo" disabled="#{pessoa.edicao}" style="width: 100%">
                                                <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                                <f:selectItem itemLabel="#{localemsgs.Masculino}" itemValue="M"/>
                                                <f:selectItem itemLabel="#{localemsgs.Feminino}" itemValue="F"/>
                                                <f:selectItem itemLabel="#{localemsgs.Outros}" itemValue="O"/>
                                            </p:selectOneMenu>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="mae" value="#{localemsgs.Mae}: "/>
                                            <p:inputText id="mae" value="#{pessoa.novaPessoa.mae}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.Mae}" style="width: 100%" maxlength="40">
                                                <p:watermark for="mae" value="#{localemsgs.Mae}"/>
                                            </p:inputText>

                                            <p:outputLabel for="pis" value="#{localemsgs.PIS}: "/>
                                            <p:inputText id="pis" value="#{pessoa.novaPessoa.PIS}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.PIS}" style="width: 100%" maxlength="15">
                                                <p:watermark for="pis" value="#{localemsgs.PIS}"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="CNH" value="#{localemsgs.CNH}: "/>
                                            <p:inputText id="CNH" value="#{pessoa.novaPessoa.CNH}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.CNH}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="CNH" value="#{localemsgs.CNH}"/>
                                            </p:inputText>

                                            <p:outputLabel for="CNHDtVenc" value="#{localemsgs.CNHDtVenc}:" />
                                            <p:calendar id="CNHDtVenc" value="#{pessoa.novaPessoa.CNHDtVenc}" disabled="#{pessoa.edicao}"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%">
                                                <p:watermark for="CNHDtVenc" value="01/01/1971"/>
                                            </p:calendar>
                                        </p:panelGrid>
                                    </div>
                                </p:tab>

                                <p:tab id="tabFormacao" title="#{localemsgs.DadosFormacao}" disabled="#{pessoa.edicao}"
                                       rendered="#{pessoa.novaPessoa.situacao eq 'C' or pessoa.novaPessoa.situacao eq 'F'}">
                                    <div style="max-height:160px !important; overflow: auto !important;">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="indicacao" value="#{localemsgs.Indicacao}: "/>
                                            <p:inputText id="indicacao" value="#{pessoa.novaPessoa.indicacao}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.Indicacao}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="indicacao" value="#{localemsgs.Indicacao}"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,
                                                     ui-grid-col-2,ui-grid-col-2,ui-grid-col-2"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="dt_FormIni" value="#{localemsgs.Dt_FormIni}:" />
                                            <p:calendar id="dt_FormIni" value="#{pessoa.novaPessoa.dt_FormIni}" disabled="#{pessoa.edicao}"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%">
                                                <p:watermark for="dt_FormIni" value="01/01/1971"/>
                                            </p:calendar>

                                            <p:outputLabel for="dt_FormFim" value="#{localemsgs.Dt_FormFim}:" />
                                            <p:calendar id="dt_FormFim" value="#{pessoa.novaPessoa.dt_FormFim}" disabled="#{pessoa.edicao}"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%">
                                                <p:watermark for="dt_FormFim" value="01/01/1971"/>
                                            </p:calendar>

                                            <p:outputLabel for="certific" value="#{localemsgs.NCertificado}: "/>
                                            <p:inputText id="certific" value="#{pessoa.novaPessoa.certific}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.NCertificado}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="certific" value="#{localemsgs.NCertificado}"/>
                                            </p:inputText>

                                            <p:outputLabel for="localForm" value="#{localemsgs.LocalForm}: "/>
                                            <p:inputText id="localForm" value="#{pessoa.novaPessoa.localForm}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.LocalForm}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="localForm" value="#{localemsgs.LocalForm}"/>
                                            </p:inputText>

                                            <p:outputLabel for="dt_Recicl" value="#{localemsgs.Dt_Recicl}:" />
                                            <p:calendar id="dt_Recicl" value="#{pessoa.novaPessoa.dt_Recicl}" disabled="#{pessoa.edicao}"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%">
                                                <p:watermark for="dt_Recicl" value="01/01/1971"/>
                                            </p:calendar>

                                            <p:outputLabel for="dt_VenCurs" value="#{localemsgs.Dt_VenCurs}:" />
                                            <p:calendar id="dt_VenCurs" value="#{pessoa.novaPessoa.dt_VenCurs}" disabled="#{pessoa.edicao}"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%">
                                                <p:watermark for="dt_VenCurs" value="01/01/1971"/>
                                            </p:calendar>
                                        </p:panelGrid>

                                        <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-1,ui-grid-col-1,
                                                     ui-grid-col-2,ui-grid-col-4"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="reg_PF" value="#{localemsgs.Reg_PF}: "/>
                                            <p:inputText id="reg_PF" value="#{pessoa.novaPessoa.reg_PF}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.Reg_PF}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="reg_PF" value="#{localemsgs.Reg_PF}"/>
                                            </p:inputText>

                                            <p:outputLabel for="reg_PFUF" value="#{localemsgs.Reg_PFUF}: "/>
                                            <p:inputText id="reg_PFUF" value="#{pessoa.novaPessoa.reg_PFUF}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.Reg_PFUF}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="reg_PFUF" value="#{localemsgs.Reg_PFUF}"/>
                                            </p:inputText>

                                            <p:outputLabel for="reg_PFDt" value="#{localemsgs.Reg_PFDt}:" />
                                            <p:calendar id="reg_PFDt" value="#{pessoa.novaPessoa.reg_PFDt}" disabled="#{pessoa.edicao}"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%">
                                                <p:watermark for="reg_PFDt" value="01/01/1971"/>
                                            </p:calendar>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="carNacVig" value="#{localemsgs.CarNacVig}: "/>
                                            <p:inputText id="carNacVig" value="#{pessoa.novaPessoa.carNacVig}" disabled="#{pessoa.edicao}"
                                                         label="#{localemsgs.Reg_PFUF}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="carNacVig" value="#{localemsgs.CarNacVig}"/>
                                            </p:inputText>

                                            <p:outputLabel for="dtValCNV" value="#{localemsgs.DtValCNV}:" />
                                            <p:calendar id="dtValCNV" value="#{pessoa.novaPessoa.dtValCNV}" disabled="#{pessoa.edicao}"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%">
                                                <p:watermark for="dtValCNV" value="01/01/1971"/>
                                            </p:calendar>
                                        </p:panelGrid>

                                        <p:panelGrid columns="7" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-1,ui-grid-col-3,ui-grid-col-1,ui-grid-col-2,ui-grid-col-1"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel value="#{localemsgs.ExtensoesTV}: "/>

                                            <p:outputLabel for="extTV" value="#{localemsgs.ExtTV}:" />
                                            <p:selectBooleanCheckbox id="extTV" value="#{pessoa.extTV}" disabled="#{pessoa.edicao}"/>

                                            <p:outputLabel for="extSPP" value="#{localemsgs.ExtSPP}:" />
                                            <p:selectBooleanCheckbox id="extSPP" value="#{pessoa.extSPP}" disabled="#{pessoa.edicao}"/>

                                            <p:outputLabel for="extEscolta" value="#{localemsgs.ExtEscolta}:" />
                                            <p:selectBooleanCheckbox id="extEscolta" value="#{pessoa.extEscolta}" disabled="#{pessoa.edicao}"/>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF !important;">
                                            <p:outputLabel for="novoCargoPretendido" value="#{localemsgs.CargoPretendido}:" />
                                            <p:autoComplete id="novoCargoPretendido" completeMethod="#{pessoa.buscarCargo}" value="#{pessoa.cargo}"
                                                            label="#{localemsgs.CargoPretendido}" forceSelection="true" styleClass="cliente"
                                                            disabled="#{pessoa.edicao}" scrollHeight="200" minQueryLength="3" style="width: 100%"
                                                            var="novoCargo" itemValue="#{novoCargo}" itemLabel="#{novoCargo.descricao}">
                                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{pessoa.cargos}"/>
                                                <p:watermark for="novoCargoPretendido" value="#{localemsgs.CargoPretendido}"/>
                                                <p:ajax event="itemSelect" listener="#{pessoa.selecionarCargo}"
                                                        update="msgs tabelaCargosPretendidos novoCargoPretendido"/>
                                            </p:autoComplete>
                                        </p:panelGrid>

                                        <p:panel style="background: #FFF; border: 1px solid #E6E6E6 !important">
                                            <p:dataTable value="#{pessoa.cargosPretendidos}" scrollHeight="200" scrollable="true"
                                                         style="background: transparent" rowKey="#{cargoPretendido.Cod_Cargo}" resizableColumns="true"
                                                         var="cargoPretendido" id="tabelaCargosPretendidos"
                                                         class="tabelaArquivos" styleClass="tabelaArquivos">
                                                <p:column headerText="#{localemsgs.Codigo}" style="width: 65px; text-align: center">
                                                    <h:outputText value="#{cargoPretendido.cod_Cargo}">
                                                        <f:convertNumber pattern="00000"/>
                                                    </h:outputText>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Cargo}" style="text-align: center">
                                                    <h:outputText value="#{cargoPretendido.descricao}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.ModificadoEm}" style="width: 110px; text-align: center">
                                                    <h:outputText value="#{cargoPretendido.dt_Alter}" converter="conversorData"/>
                                                </p:column>
                                                <p:column style="width: 30px">
                                                    <p:commandLink actionListener="#{pessoa.removerCargoPretendido(cargoPretendido)}"
                                                                   update="msgs formCadastrar:tabs:tabelaCargosPretendidos">
                                                        <p:graphicImage  url="../assets/img/icone_redondo_excluir.png" height="20" />
                                                    </p:commandLink>
                                                </p:column>
                                            </p:dataTable>
                                        </p:panel>
                                    </div>
                                </p:tab>

                                <p:tab id="tabDocumentos" title="#{localemsgs.Documentos}" disabled="#{pessoa.edicao}"
                                       rendered="#{pessoa.novaPessoa.situacao eq 'C' or pessoa.novaPessoa.situacao eq 'F'}">
                                    <div style="max-height:160px !important; overflow: auto !important;">
                                        <div  style="text-align: justify; width: 100%; padding-bottom: 10px">
                                            <h:outputText value="#{localemsgs.ArrasteArquivo}:"/>
                                        </div>

                                        <div  style="text-align: center; width: 100%; height: 150px">
                                            <p:fileUpload id="upload" fileUploadListener="#{pessoa.handleFileUpload}"
                                                          allowTypes="/(\.|\/)(pdf|jpe?g|xls|xlsx|doc|docx)$/" label="#{localemsgs.Pesquisar}" auto="true"
                                                          invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                                          dragDropSupport="true" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                                          update="msgs formCadastrar:tabs:tabDocumentos" previewWidth="10" skinSimple="true">
                                                <h:outputText value="#{localemsgs.ArrasteAqui}" id="ArrasteAqui"
                                                              style="text-align: justify; color: lightgray; top: 30px; position: relative;"/>
                                            </p:fileUpload>
                                        </div>
                                        <p:panel style="background: #FFF; border: 1px solid #E6E6E6 !important">
                                            <p:dataTable value="#{pessoa.documentos}" scrollHeight="200" scrollable="true"
                                                         style="background: transparent" rowKey="#{documentos.ordem}"
                                                         var="documentos" id="arquivos" styleClass="tabelaArquivos">
                                                <p:column headerText="#{localemsgs.Arquivos}" style="text-align: center">
                                                    <p:commandLink actionListener="#{pessoa.handleFileDownload(documentos)}" ajax="false"
                                                                   value="#{documentos.descricao}" update="msgs">
                                                        <p:fileDownload value="#{pessoa.download}" />
                                                    </p:commandLink>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.ModificadoEm}" style="width: 110px; text-align: center">
                                                    <h:outputText value="#{documentos.dt_alter}" converter="conversorData"/>
                                                </p:column>
                                                <p:column style="width: 30px">
                                                    <p:commandLink actionListener="#{pessoa.handleFileDelete(documentos)}"
                                                                   update="msgs formCadastrar:tabs:tabDocumentos">
                                                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirDocumento}" icon="ui-icon-alert" />
                                                        <p:graphicImage  url="../assets/img/icone_redondo_excluir.png" height="20" />
                                                    </p:commandLink>
                                                </p:column>
                                            </p:dataTable>
                                        </p:panel>
                                    </div>
                                </p:tab>

                                <p:tab id="tabContatos" title="#{localemsgs.Contatos}" disabled="#{pessoa.flag eq 1}"
                                       rendered="#{(pessoa.novaPessoa.situacao eq 'C' or pessoa.novaPessoa.situacao eq 'F') and login.nivel eq '9'}">
                                    <div style="max-height:160px !important; overflow: auto !important;">
                                        <p:panelGrid columns="2" style="background-color:#FFF !important;">
                                            <p:panel>
                                                <p:dataTable id="contatos" value="#{pessoa.contatos}" var="listaContatos" selection="#{pessoa.contato}"
                                                             selectionMode="single" rowKey="#{listaContatos.codContato}" styleClass="tabela" scrollable="true"
                                                             scrollHeight="200" scrollWidth="100%" resizableColumns="true" sortBy="#{listaContatos.nome}">
                                                    <p:column headerText="#{localemsgs.Nome}" style="width: 250px">
                                                        <h:outputText value="#{listaContatos.nome}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Empresa}">
                                                        <h:outputText value="#{listaContatos.operador}" converter="tradutorEmpresas"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </p:panel>
                                            <p:panel  style="vertical-align: top; height: 200px">
                                                <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{pessoa.NovoContato}"
                                                               update="formCadastrar:addContato msgs">
                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                                </p:commandLink>
                                                <p:spacer height="30px"/>
                                                <p:commandLink title="#{localemsgs.Apagar}" action="#{pessoa.DeletarContato}"
                                                               update="msgs">
                                                    <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                                                </p:commandLink>
                                            </p:panel>
                                        </p:panelGrid>
                                    </div>
                                </p:tab>
                            </p:tabView>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; padding: 10px 0px 0px 0px !important;">
                                <p:commandLink id="cadastro" action="#{pessoa.cadastrar}" update=":main:tabela :msgs"
                                               rendered="#{pessoa.flag eq 1}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>

                                <p:commandLink id="editar" action="#{pessoa.editar}" update=" :main:tabela :msgs"
                                               rendered="#{pessoa.flag eq 2}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>

                    <p:dialog widgetVar="dlgAdicionarContato" closable="true" header="#{localemsgs.AdicionarContato}"
                              resizable="false" width="400" height="50" hideEffect="fade">
                        <p:panel id="addContato">
                            <p:autoComplete id="novoContato" value="#{pessoa.contato}" completeMethod="#{pessoa.BuscarContatos}"
                                            label="#{localemsgs.Contato}" forceSelection="true"  styleClass="cidade"
                                            style="position: absolute; float: left; left: 60px; width: 280px;"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Contato}" scrollHeight="200"
                                            var="cont" itemValue="#{cont}" itemLabel="#{cont.nome}">
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{pessoa.buscaContatos}" />
                            </p:autoComplete>
                            <p:commandLink oncomplete="PF('dlgAdicionarContato').hide()" action="#{pessoa.AdiconarContato}"
                                           title="#{localemsgs.Selecionar}" update="formCadastrar:tabs:contatos msgs"
                                           style="position: absolute; float: left; left: 350px;">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30" />
                            </p:commandLink>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Manutencão de itens-->
                <h:form class="form-inline" id="formDietas">
                    <p:dialog widgetVar="dlgCadastrarDietas" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrarDietas" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; min-width:60% !important; width:auto !important; width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_configuracoes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarDieta}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="cadastrar" style="display:inline-block; background-color: transparent; max-width: 100% !important; height:400px !important; overflow:auto !important; padding:0px !important; margin:0px !important" class="cadastrar">
                            <p:dataTable id="tabelaDietas"
                                         value="#{pessoa.listaDietas}"
                                         var="gridDieta"
                                         selectionMode="single"
                                         selection="#{pessoa.dietaSelecionada}"
                                         emptyMessage="#{localemsgs.SemRegistros}"
                                         scrollable="true"
                                         class="tabela DataGrid"
                                         scrollHeight="100%"
                                         reflow="true"
                                         style="font-size: 12px; background: #EEE"
                                         rowKey="#{gridDieta.sequencia}"
                                         >
                                <p:ajax event="rowDblselect" listener="#{pessoa.dblSelectDieta}" class="tabela DataGrid" update="formDietasCadastro msgs" />

                                <p:column headerText="#{localemsgs.Codigo}" class="text-center">
                                    <h:outputText value="#{gridDieta.sequencia}" title="#{gridDieta.sequencia}">
                                        <f:convertNumber pattern="0000"/>
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{localemsgs.descricao}" class="text-center">
                                    <h:outputText value="#{gridDieta.descricao}" title="#{gridDieta.descricao}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Especificacao}" class="text-center">
                                    <h:outputText value="#{gridDieta.especificacao}" title="#{gridDieta.especificacao}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                    <h:outputText value="#{gridDieta.operador}" title="#{gridDieta.operador}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                    <h:outputText value="#{gridDieta.dt_alter}" title="#{gridDieta.dt_alter}" converter="conversorData"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                    <h:outputText value="#{gridDieta.hr_Alter}" title="#{gridDieta.hr_Alter}" converter="conversorHora"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                        <p:panel id="botoesCadastro" style="width:100% !important; background-color:#EEE; width:100%; text-align:center; margin-top:10px !important">
                            <p:commandLink title="#{localemsgs.Adicionar}"
                                           partialSubmit="true" process="@this"
                                           update="formDietasCadastro:cadastrar msgs"
                                           actionListener="#{pessoa.buttonActionDietaNovo}" >
                                <label class="btn btn-lg btn-primary" style="width:170px !important; margin-right: 4px;"><i class="fa fa-plus"></i>&nbsp;#{localemsgs.Adicionar}</label>
                            </p:commandLink>

                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{pessoa.buttonActionDieta}"
                                           update="formDietasCadastro:cadastrar msgs">
                                <label class="btn btn-lg btn-warning" style="width:170px !important;margin-left: 4px;"><i class="fa fa-edit"></i>&nbsp;#{localemsgs.Editar}</label>
                            </p:commandLink>
                        </p:panel>
                    </p:dialog>
                </h:form>


                <h:form class="form-inline" id="formDietasCadastro">
                    <p:dialog widgetVar="dlgCadastrarDietasForm" positionType="absolute" responsive="true" focus="cadadastroDescricao"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrarDietasForm" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; min-width:400px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">

                        <p:panelGrid id="cadastrar" columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="cadadastroCodigo" value="#{localemsgs.Codigo}" />
                            <p:inputText id="cadadastroCodigo" value="#{pessoa.dietaNova.sequencia}" style="width: 100%"
                                         required="true" label="#{localemsgs.Codigo}" disabled="true"
                                         maxlength="50">
                            </p:inputText>


                            <p:outputLabel for="cadadastroDescricao" value="#{localemsgs.descricao}" />
                            <p:inputText id="cadadastroDescricao" value="#{pessoa.dietaNova.descricao}" style="width: 100%"
                                         required="true" label="#{localemsgs.descricao}"
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.descricao}"
                                         maxlength="255">
                                <p:watermark for="cadadastroDescricao" value="#{localemsgs.descricao}"/>
                            </p:inputText>

                            <p:outputLabel for="cadadastroEspecificacao" value="#{localemsgs.Especificacao}" />
                            <p:inputText id="cadadastroEspecificacao" value="#{pessoa.dietaNova.especificacao}" style="width: 100%"
                                         required="false" label="#{localemsgs.Nome}"
                                         maxlength="255">
                                <p:watermark for="cadadastroEspecificacao" value="#{localemsgs.Especificacao}"/>
                            </p:inputText>
                        </p:panelGrid>
                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="margin-top:8px">
                            <p:commandLink title="#{localemsgs.Salve}" id="cadastrarDieta" action="#{pessoa.salvarDadosDieta}"
                                           update="formDietasCadastro formDietas:tabelaDietas formCadastrar:dieta msgs"
                                           style="width:100%">
                                <label class="btn btn-lg btn-success" style="width:100% !important;margin-left: 4px;"><i class="fa fa-save"></i>&nbsp;#{localemsgs.Salve}</label>
                            </p:commandLink>
                        </p:panelGrid>
                    </p:dialog>
                </h:form>


                <!--Pesquisar funcionário-->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarPessoa}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nome" value="#{localemsgs.Nome}: " />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome" value="#{pessoa.novaPessoa.nome}"
                                                 label="#{localemsgs.Nome}"
                                                 maxlength="50" style="width: 100%">
                                        <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="rg" value="#{localemsgs.RG}: " />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="rg" value="#{pessoa.novaPessoa.RG}"
                                                 label="#{localemsgs.RG}"
                                                 maxlength="50" style="width: 100%">
                                        <p:watermark for="rg" value="#{localemsgs.RG}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="cpf" value="#{localemsgs.CPF}: " />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="cpf" value="#{pessoa.novaPessoa.CPF}"
                                                 label="#{localemsgs.CPF}"
                                                 maxlength="11" style="width: 100%">
                                        <p:watermark for="cpf" value="#{localemsgs.CPF}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="matr" value="#{localemsgs.Matr}: " />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="matr" value="#{pessoa.novaPessoa.matr}"
                                                 label="#{localemsgs.Matr}"
                                                 style="width: 100%">
                                        <p:watermark for="matr" value="#{localemsgs.Matr}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codigo" value="#{localemsgs.Codigo}: " />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="codigo" value="#{pessoa.novaPessoa.codigo}"
                                                 label="#{localemsgs.Codigo}"
                                                 maxlength="50" style="width: 100%">
                                        <p:watermark for="codigo" value="#{localemsgs.Codigo}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="situacao" value="#{localemsgs.Situacao}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu value="#{pessoa.novaPessoa.situacao}" id="situacao"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Candidato}" itemValue="C"/>
                                        <f:selectItem itemLabel="#{localemsgs.Prestador}" itemValue="P"/>
                                        <f:selectItem itemLabel="#{localemsgs.Autonomo}" itemValue="A"/>
                                        <f:selectItem itemLabel="#{localemsgs.Funcionario}" itemValue="F"/>
                                        <f:selectItem itemLabel="#{localemsgs.Diretor}" itemValue="D"/>
                                        <f:selectItem itemLabel="#{localemsgs.Socio}" itemValue="S"/>
                                        <f:selectItem itemLabel="#{localemsgs.Visitante}" itemValue="V"/>
                                        <f:selectItem itemLabel="#{localemsgs.BBloqueado}" itemValue="B"/>
                                        <f:selectItem itemLabel="#{localemsgs.Visitanteweb}" itemValue="W" />
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="cidade" value="#{localemsgs.Cidade}: "/>
                                </div>
                                <div style="width: 55%; float: left">
                                    <p:autoComplete id="cidade" value="#{pessoa.novaPessoa.cidade}" styleClass="cidade"
                                                    completeMethod="#{pessoa.BuscarCidade}" scrollHeight="200"
                                                    maxlength="25" style="width: 100%">
                                        <p:ajax event="itemSelect" listener="#{pessoa.SelecionarCidade}"
                                                update="formPesquisar:cidade"/>
                                        <p:watermark for="cidade" value="#{localemsgs.Cidade}"/>
                                    </p:autoComplete>
                                </div>
                                <div style="width: 10%; float: left">
                                    <p:outputLabel value="#{localemsgs.UF}: "/>
                                </div>
                                <div style="width: 10%; float: left">
                                    <p:inputText id="uf" value="#{pessoa.novaPessoa.UF}"
                                                 style="width: 100%" label="#{localemsgs.UF}" maxlength="2">
                                        <p:watermark for="uf" value="#{localemsgs.UF}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="cadastro" action="#{pessoa.PesquisaPaginada}"
                                               update=" :main:tabela :msgs"
                                               oncomplete="PF('dlgPesquisar').hide()"
                                               title="#{localemsgs.Cadastrar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Pesquisar rápida -->
                <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarPessoa}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="panelPesquisaRapida" style="background: transparent">

                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{pessoa.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.RG}" itemValue="RG" />
                                        <f:selectItem itemLabel="#{localemsgs.CPF}" itemValue="CPF" />
                                        <f:selectItem itemLabel="#{localemsgs.Matr}" itemValue="MATR" />
                                        <f:selectItem itemLabel="#{localemsgs.Codigo}" itemValue="CODIGO" />
                                        <f:selectItem itemLabel="#{localemsgs.Cargo}" itemValue="CARGO" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{pessoa.chavePesquisa eq 'nome'}" value="#{localemsgs.Nome}: "/>
                                        <p:outputLabel for="opcao" rendered="#{pessoa.chavePesquisa eq 'rg'}" value="#{localemsgs.RG}: "/>
                                        <p:outputLabel for="opcao" rendered="#{pessoa.chavePesquisa eq 'cpf'}" value="#{localemsgs.CPF}: "/>
                                        <p:outputLabel for="opcao" rendered="#{pessoa.chavePesquisa eq 'matr'}" value="#{localemsgs.Matr}: "/>
                                        <p:outputLabel for="opcao" rendered="#{pessoa.chavePesquisa eq 'cargo'}" value="#{localemsgs.Cargo}: "/>
                                        <p:outputLabel for="opcao" rendered="#{pessoa.chavePesquisa eq 'codigo'}" value="#{localemsgs.Codigo}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{pessoa.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="botaoPesquisaRapida"
                                               action="#{pessoa.pesquisaRapida()}"
                                               update=" :main:tabela :msgs"
                                               oncomplete="PF('dlgPesquisaRapida').hide()"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });

                    function MascarasJS() {
                        setTimeout(function () {
                            //$('[id*="divCPF"]').find('input').attr('south-type', 'cpf');
                            $('[id*="fone1"]').attr('south-type', 'telefone');
                            $('[id*="fone2"]').attr('south-type', 'telefone');
                            $('[id*="cep"]').attr('south-type', 'cep');

                            CriarAtributos('#{localeController.number}');
                        }, 600);
                    }
                </script>

                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" width="400"
                          style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                    </f:facet>
                    <h:form class="form-inline">
                        <p:hotkey bind="esc" oncomplete ="PF('dlgExportar').hide()"/>
                        <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                        <p:separator />
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="codigo" value="#{pessoa.eCodigo}">
                                    <p:ajax update="labelCodigo"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCodigo" value="#{localemsgs.Codigo}" style="#{pessoa.eCodigo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="nome" value="#{pessoa.eNome}">
                                    <p:ajax update="labelNome"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelNome" value="#{localemsgs.Nome}" style="#{pessoa.eNome eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="email" value="#{pessoa.eEmail}">
                                    <p:ajax update="labelEmail"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelEmail" value="#{localemsgs.Email}" style="#{pessoa.eEmail eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cpf" value="#{pessoa.eCPF}">
                                    <p:ajax update="labelCPF"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCPF" value="#{localemsgs.CPF}" style="#{pessoa.eCPF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="rg" value="#{pessoa.eRG}">
                                    <p:ajax update="labelRG"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelRG" value="#{localemsgs.RG}" style="#{pessoa.eRG ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="orgemis" value="#{pessoa.eOrgEmis}">
                                    <p:ajax update="labelOrgEmis"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelOrgEmis" value="#{localemsgs.RGOrg}" style="#{pessoa.eOrgEmis eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="fon1" value="#{pessoa.eFone1}">
                                    <p:ajax update="labelFon1"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFon1" value="#{localemsgs.Fone1}" style="#{pessoa.eFone1 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="fon2" value="#{pessoa.eFone2}">
                                    <p:ajax update="labelFon2"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFon2" value="#{localemsgs.Fone2}" style="#{pessoa.eFone2 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="ende" value="#{pessoa.eEnde}">
                                    <p:ajax update="labelEnde"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelEnde" value="#{localemsgs.Ende}" style="#{pessoa.eEnde eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="bairro" value="#{pessoa.eBairro}">
                                    <p:ajax event="change" update="labelBairro"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelBairro"  value="#{localemsgs.Bairro}" style="#{pessoa.eBairro eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cidade" value="#{pessoa.eCidade}">
                                    <p:ajax update="labelCidade"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCidade" value="#{localemsgs.Cidade}" style="#{pessoa.eCidade eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="uf" value="#{pessoa.eUF}">
                                    <p:ajax update="labelUF"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelUF" value="#{localemsgs.UF}" style="#{pessoa.eUF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cep" value="#{pessoa.eCEP}">
                                    <p:ajax update="labelCEP"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCEP" value="#{localemsgs.CEP}" style="#{pessoa.eCEP eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="sit" value="#{pessoa.eSituacao}">
                                    <p:ajax update="labelSit"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSit" value="#{localemsgs.Situacao}" style="#{pessoa.eSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="operador" value="#{pessoa.eOperador}">
                                    <p:ajax update="labelOperador"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{pessoa.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="matr" value="#{pessoa.eMatr}">
                                    <p:ajax update="labelMatr"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelMatr" value="#{localemsgs.Matr}" style="#{pessoa.eMatr eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="func" value="#{pessoa.eFuncao}">
                                    <p:ajax update="labelFunc"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFunc" value="#{localemsgs.Funcao}" style="#{pessoa.eFuncao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="sex" value="#{pessoa.eSexo}">
                                    <p:ajax update="labelSex"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSex" value="#{localemsgs.Sexo}" style="#{pessoa.eSexo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="altura" value="#{pessoa.eAltura}">
                                    <p:ajax update="labelAlt"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelAlt" value="#{localemsgs.Altura}" style="#{pessoa.eAltura eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="peso" value="#{pessoa.ePeso}">
                                    <p:ajax update="labelPeso"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelPeso" value="#{localemsgs.Peso}" style="#{pessoa.ePeso eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">

                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="dtalter" value="#{pessoa.eDtAlter}">
                                    <p:ajax update="labelDtAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{pessoa.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="hralter" value="#{pessoa.eHrAlter}">
                                    <p:ajax update="labelHrAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{pessoa.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="obs" value="#{pessoa.eObs}">
                                    <p:ajax update="labelObs"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelObs" value="#{localemsgs.Obs}" style="#{pessoa.eObs eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <p:separator />
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#EEE;">
                            <p:panel style="text-align: center;background-color:#EEE;">
                                <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                                <h:commandLink id="pdf" actionListener="#{pessoa.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_pdf.png" style="height:40px"/>
                                    <p:dataExporter target="main:tabela" type="pdf" fileName="#{localemsgs.Pessoas}"
                                                    preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                                </h:commandLink>
                            </p:panel>

                            <p:panel style="text-align: center;background-color:#EEE;">
                                <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                                <h:commandLink id="xlsx" actionListener="#{pessoa.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_xls.png" style="height:40px"/>
                                    <p:dataExporter target="main:tabela" type="xlsx" fileName="#{localemsgs.Pessoas}"/>
                                </h:commandLink>
                            </p:panel>
                        </p:panelGrid>
                    </h:form>
                </p:dialog>

            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important; display: #{login.pp.empresa ne 'SATMAXIMA'?'none':''}">
                        <h:form id="corporativo">
                            <div style="margin-top: 12px !important">
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Excluido}: " /></label>
                                <p:selectBooleanCheckbox value="#{pessoa.mostraExcluidos}">
                                    <p:ajax update="msgs main:tabela" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                // <![CDATA[
                $(document).ready(function () {
                    if (ObterParamURL('selecao') &&
                            ObterParamURL('selecao') === 'S') {
                        $.MsgBoxLaranjaOk('#{localemsgs.Aviso}', '#{localemsgs.DuploClickPessoa}');
                        $('header, footer').css('display', 'none');
                    } else
                        $('header, footer').css('display', '');
                })
                        .on('dblclick', '[id*="tabela"] tbody tr[role="row"]', function () {
                            if (ObterParamURL('selecao') &&
                                    ObterParamURL('selecao') === 'S') {
                                let CodigoPessoaSelecionada = $(this).attr('data-rk');
                                window.parent.CarregarDadosAproveitamento(CodigoPessoaSelecionada);
                            }
                        })
                        ;

                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });


                // ]]>
            </script>
        </h:body>
    </f:view>
</html>
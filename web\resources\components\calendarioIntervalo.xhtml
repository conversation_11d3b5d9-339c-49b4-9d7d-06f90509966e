<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite = "http://java.sun.com/jsf/composite"
      >
    <composite:interface>
        <composite:attribute name="update" />
        <composite:attribute name="oncomplete" />
        <composite:attribute name="actionVoltar" required="true"
                             method-signature="java.lang.String action()" />
        <composite:attribute name="actionAvancar" required="true"
                             method-signature="java.lang.String action()" />
    </composite:interface>

    <composite:implementation>
        <div
            id="#{cc.id}"
            class="ui-grid-col-3"
            style="align-self: center; text-align: center;"
            >
            <!--Botão voltar-->
            <p:commandLink action="#{cc.attrs.actionVoltar}" update="#{cc.attrs.update}">
                <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 40px"/>  
            </p:commandLink>

            <!--Calendário-->
            <p:commandLink
                id="calendar"
                oncomplete="#{cc.attrs.oncomplete}"
                styleClass="botao"
                update="#{cc.attrs.update}"
                >
                <p:graphicImage 
                    url="../assets/img/icone_escaladodia.png"
                    style="align-self: center;height: 40px"
                    />
            </p:commandLink>

            <!--Botão avançar-->
            <p:commandLink action="#{cc.attrs.actionAvancar}" update="#{cc.attrs.update}">
                <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 40px"/>
            </p:commandLink>
        </div>
    </composite:implementation>
</html>

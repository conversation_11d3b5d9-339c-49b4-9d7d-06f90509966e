package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class OCompraItens {

    private BigDecimal codProd;
    private Integer prodSeq;
    private Integer tipoTribut;
    private BigDecimal qtde;
    private BigDecimal valorUn;
    private BigDecimal valor;
    private Integer codVeiculo;
    private String ccusto;
    private String CST;
    private BigDecimal ICMS;
    private BigDecimal BCICMS;
    private BigDecimal aliqPIS;
    private BigDecimal PIS;
    private String PISCST;
    private BigDecimal aliqCOFINS;
    private BigDecimal COFINS;
    private String COFINSCST;
    private BigDecimal aliqCSL;
    private BigDecimal CSL;
    private String CSLCST;
    private String IPICST;
    private String IPIEnq;
    private BigDecimal aliqIPI;
    private BigDecimal IPI;
    private String CFOPEnt;
    private BigDecimal TotTrib;
    private String operador;
    private String dtAlter;
    private String hrAlter;
    private BigDecimal imposto;

    public OCompraItens() {
        this.codProd = new BigDecimal("0");
        this.prodSeq = 0;
        this.tipoTribut = 0;
        this.qtde = new BigDecimal("0");
        this.valorUn = new BigDecimal("0");
        this.valor = new BigDecimal("0");
        this.codVeiculo = 0;
        this.ccusto = "";
        this.CST = "";
        this.ICMS = new BigDecimal("0");
        this.BCICMS = new BigDecimal("0");
        this.aliqPIS = new BigDecimal("0");
        this.PIS = new BigDecimal("0");
        this.PISCST = "";
        this.aliqCOFINS = new BigDecimal("0");
        this.COFINS = new BigDecimal("0");
        this.COFINSCST = "";
        this.aliqCSL = new BigDecimal("0");
        this.CSL = new BigDecimal("0");
        this.CSLCST = "";
        this.IPICST = "";
        this.IPIEnq = "";
        this.aliqIPI = new BigDecimal("0");
        this.IPI = new BigDecimal("0");
        this.CFOPEnt = "";
        this.TotTrib = new BigDecimal("0");
        this.operador = "";
        this.dtAlter = "";
        this.hrAlter = "";
        this.imposto = new BigDecimal("0");
    }

    public BigDecimal getCodProd() {
        return codProd;
    }

    public void setCodProd(String codProd) {
        try {
            this.codProd = new BigDecimal(codProd);
        } catch (Exception e) {
            this.codProd = new BigDecimal("0");
        }
    }

    public Integer getProdSeq() {
        return prodSeq;
    }

    public void setProdSeq(Integer prodSeq) {
        this.prodSeq = prodSeq;
    }

    public Integer getTipoTribut() {
        return tipoTribut;
    }

    public void setTipoTribut(Integer tipoTribut) {
        this.tipoTribut = tipoTribut;
    }

    public BigDecimal getQtde() {
        return qtde;
    }

    public void setQtde(String qtde) {
        try {
            this.qtde = new BigDecimal(qtde);
        } catch (Exception e) {
            this.qtde = new BigDecimal("0");
        }
    }

    public BigDecimal getValorUn() {
        return valorUn;
    }

    public void setValorUn(String valorUn) {
        try {
            this.valorUn = new BigDecimal(valorUn);
        } catch (Exception e) {
            this.valorUn = new BigDecimal("0");
        }
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(String valor) {
        try {
            this.valor = new BigDecimal(valor);
        } catch (Exception e) {
            this.valor = new BigDecimal("0");
        }
    }

    public Integer getCodVeiculo() {
        return codVeiculo;
    }

    public void setCodVeiculo(Integer codVeiculo) {
        this.codVeiculo = codVeiculo;
    }

    public String getCcusto() {
        return ccusto;
    }

    public void setCcusto(String ccusto) {
        this.ccusto = ccusto;
    }

    public String getCST() {
        return CST;
    }

    public void setCST(String CST) {
        this.CST = CST;
    }

    public BigDecimal getICMS() {
        return ICMS;
    }

    public void setICMS(String ICMS) {
        try {
            this.ICMS = new BigDecimal(ICMS);
        } catch (Exception e) {
            this.ICMS = new BigDecimal("0");
        }
    }

    public BigDecimal getBCICMS() {
        return BCICMS;
    }

    public void setBCICMS(String BCICMS) {
        try {
            this.BCICMS = new BigDecimal(BCICMS);
        } catch (Exception e) {
            this.BCICMS = new BigDecimal("0");
        }
    }

    public BigDecimal getAliqPIS() {
        return aliqPIS;
    }

    public void setAliqPIS(String aliqPIS) {
        try {
            this.aliqPIS = new BigDecimal(aliqPIS);
        } catch (Exception e) {
            this.aliqPIS = new BigDecimal("0");
        }
    }

    public BigDecimal getPIS() {
        return PIS;
    }

    public void setPIS(String PIS) {
        try {
            this.PIS = new BigDecimal(PIS);
        } catch (Exception e) {
            this.PIS = new BigDecimal("0");
        }
    }

    public String getPISCST() {
        return PISCST;
    }

    public void setPISCST(String PISCST) {
        this.PISCST = PISCST;
    }

    public BigDecimal getAliqCOFINS() {
        return aliqCOFINS;
    }

    public void setAliqCOFINS(String aliqCOFINS) {
        try {
            this.aliqCOFINS = new BigDecimal(aliqCOFINS);
        } catch (Exception e) {
            this.aliqCOFINS = new BigDecimal("0");
        }
    }

    public BigDecimal getCOFINS() {
        return COFINS;
    }

    public void setCOFINS(String COFINS) {
        try {
            this.COFINS = new BigDecimal(COFINS);
        } catch (Exception e) {
            this.COFINS = new BigDecimal("0");
        }
    }

    public String getCOFINSCST() {
        return COFINSCST;
    }

    public void setCOFINSCST(String COFINSCST) {
        this.COFINSCST = COFINSCST;
    }

    public BigDecimal getAliqCSL() {
        return aliqCSL;
    }

    public void setAliqCSL(String aliqCSL) {
        try {
            this.aliqCSL = new BigDecimal(aliqCSL);
        } catch (Exception e) {
            this.aliqCSL = new BigDecimal("0");
        }
    }

    public BigDecimal getCSL() {
        return CSL;
    }

    public void setCSL(String CSL) {
        try {
            this.CSL = new BigDecimal(CSL);
        } catch (Exception e) {
            this.CSL = new BigDecimal("0");
        }
    }

    public String getCSLCST() {
        return CSLCST;
    }

    public void setCSLCST(String CSLCST) {
        this.CSLCST = CSLCST;
    }

    public String getIPICST() {
        return IPICST;
    }

    public void setIPICST(String IPICST) {
        this.IPICST = IPICST;
    }

    public String getIPIEnq() {
        return IPIEnq;
    }

    public void setIPIEnq(String IPIEnq) {
        this.IPIEnq = IPIEnq;
    }

    public BigDecimal getAliqIPI() {
        return aliqIPI;
    }

    public void setAliqIPI(String aliqIPI) {
        try {
            this.aliqIPI = new BigDecimal(aliqIPI);
        } catch (Exception e) {
            this.aliqIPI = new BigDecimal("0");
        }
    }

    public BigDecimal getIPI() {
        return IPI;
    }

    public void setIPI(String IPI) {
        try {
            this.IPI = new BigDecimal(IPI);
        } catch (Exception e) {
            this.IPI = new BigDecimal("0");
        }
    }

    public String getCFOPEnt() {
        return CFOPEnt;
    }

    public void setCFOPEnt(String CFOPEnt) {
        this.CFOPEnt = CFOPEnt;
    }

    public BigDecimal getTotTrib() {
        return TotTrib;
    }

    public void setTotTrib(String TotTrib) {
        try {
            this.TotTrib = new BigDecimal(TotTrib);
        } catch (Exception e) {
            this.TotTrib = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDtAlter() {
        return dtAlter;
    }

    public void setDtAlter(String dtAlter) {
        this.dtAlter = dtAlter;
    }

    public String getHrAlter() {
        return hrAlter;
    }

    public void setHrAlter(String hrAlter) {
        this.hrAlter = hrAlter;
    }

    public BigDecimal getImposto() {
        return imposto;
    }

    public void setImposto(String imposto) {
        try {
            this.imposto = new BigDecimal(imposto);
        } catch (Exception e) {
            this.imposto = new BigDecimal("0");
        }
    }

}

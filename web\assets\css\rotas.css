/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 01/03/2017, 11:49:38
    Author     : Richard
*/

.ui-datatable .ui-column-filter{
    display: none;
}

.ui-dialog-title{
    width: calc(100% - 50px);
}

.guiaimpressa{
    width: 530px !important;
}

.supervisor.ui-autocomplete-panel {
    width: 100%;
}
.supervisor .ui-autocomplete-input{
    width: 100%;
}

.trajetosEdicaoRota .ui-panelgrid, .trajetosEdicaoRota .ui-panelgrid-cell{
    padding: 0px !important;
}

.botao .ui-panelgrid .ui-panelgrid-cell, .botao .ui-panelgrid-cell{
    padding: 0px !important;
}

.ui-lightbox-nav-left, .ui-lightbox-nav-right{
    display: none !important;
}
.ui-lightbox-caption-text{
    color:white;
}

.dialogo .ui-widget-content{
    background: transparent;
}
.dialogo .ui-panelgrid-cell{
    display: table-cell;
}
.dialogo .ui-dialog.ui-widget-content .ui-dialog-content{
    padding: 0px 15px 15px 0px;
}

.cliente2 .ui-autocomplete-panel {
    width: 100% !important;
}
.cliente2 .ui-autocomplete-input{
    width: 100% !important;
}

.clienteP .ui-autocomplete-panel {
    width: 280px !important;
}
.clienteP .ui-autocomplete-input{
    width: 280px !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.ui-icon-calendar {
    background-image:  url('../img/icone_escaladodia_40.png') !important;
    background-position: center center !important;
    width: 40px !important;
    height: 40px !important;
    left: 0px !important;
    margin-left: 0px !important;
    top: 1px !important;
    margin-top: 0px !important;
}

.ui-datepicker .ui-state-active, .ui-datepicker .ui-state-highlight{
    background-color: #022a48 !important;
    color: white !important;
}

.calendario .ui-datepicker-trigger{
    background: transparent !important;
    width: 40px;
    height: 40px;
    box-shadow: none;
    border: none;
}

.calendario .ui-inputfield{
    display: none;
}

.calendarios .ui-inputfield{
    width: 90px;
}

.dlgSituacao .ui-dialog{
    width: 400px;
    height: 270px
}

.cadastrar{
    width: 90vw;
    min-width: 100%;
    min-height: 100%;
}

.cadastrar2{
    width: 90vw;
    min-width: 100%;
    min-height: 100%;
}

.panelTabela{
    width: 260px;
}

.panelTabTabela{
    width: 260px;
}

.tabelaSupervisoes{
    width: 260px;
}

.tabs .ui-panelgrid-cell{
    padding-right: 0px !important;
}

.dialogosupervisao .ui-dialog-content{
    padding: 0px 0px 0px 0px !important;
}
.dialogosupervisao .ui-widget-content{
    background: transparent;
}

.panelTelaSupervisao{
    width: 100%;
}

.cabecalhoSupervisao{
    text-align: center;
}

.detalhesSupervisao{
    padding: 5px 5px 5px 5px;
}

@media all and (min-width: 768px) {
    .panelTelaSupervisao{
        width: 1024px;
    }
    .cadastrar{
        width: 700px;
    }
    .cadastrar2{
        width: 400px;
    }
    .panelTabela{
        width: 660px;
    }
    .panelTabTabela{
        width: 620px;
    }
    .tabs{
        width: 700px;
    }
    .tabs .ui-panelgrid-cell{
        padding-right: 15px;
    }
    .tabelaSupervisoes{
        width: 100%;
    }
    .cabecalhoSupervisao{
        text-align: left;
    }
}

.no-padding .ui-panelgrid-cell{
    padding-right: 0px !important;
}

.no-padding .ui-widget-content{
    background: transparent;
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}


.tabelaMenor .ui-widget-content{
    background: white;
}

.tabelaMenor td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaMenor .ui-datatable-selectable{
    border: 1px solid #dddddd !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaMenor .ui-datatable-scrollable-header-box{
    background: white;
}
.tabelaMenor .ui-datatable-data .ui-widget-content{
    border: 1px solid #dddddd !important;
}
.tabelaMenor .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.tabelaMenor .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}
.tabelaMenor .ui-datatable-scrollable-body {
    background: white !important;
}

.tabelaMenor .ui-datatable-scrollable-body{
    height: 300px;
    background:transparent;
}
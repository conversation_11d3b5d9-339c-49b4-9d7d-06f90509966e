package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class DIRFDetPS {

    private BigDecimal CodFil;
    private String AnoCompet;
    private String CPF;
    private String CNPJOPSE;
    private String NomeOPSE;
    private BigDecimal Matr;
    private String Nome;
    private String CPFDep;
    private LocalDate DtNasc;
    private String TipoDep;
    private BigDecimal Valor;

    public DIRFDetPS() {
        this.CodFil = new BigDecimal("0");
        this.AnoCompet = "";
        this.CPF = "";
        this.CNPJOPSE = "";
        this.NomeOPSE = "";
        this.Matr = new BigDecimal("0");
        this.Nome = "";
        this.CPFDep = "";
        this.DtNasc = LocalDate.now();
        this.TipoDep = "";
        this.Valor = new BigDecimal("0");
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("1");
        }
    }

    public String getAnoCompet() {
        return AnoCompet;
    }

    public void setAnoCompet(String AnoCompet) {
        this.AnoCompet = AnoCompet;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getCNPJOPSE() {
        return CNPJOPSE;
    }

    public void setCNPJOPSE(String CNPJOPSE) {
        this.CNPJOPSE = CNPJOPSE;
    }

    public String getNomeOPSE() {
        return NomeOPSE;
    }

    public void setNomeOPSE(String NomeOPSE) {
        this.NomeOPSE = NomeOPSE;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getCPFDep() {
        return CPFDep;
    }

    public void setCPFDep(String CPFDep) {
        this.CPFDep = CPFDep;
    }

    public LocalDate getDtNasc() {
        return DtNasc;
    }

    public void setDtNasc(LocalDate DtNasc) {
        this.DtNasc = DtNasc;
    }

    public String getTipoDep() {
        return TipoDep;
    }

    public void setTipoDep(String TipoDep) {
        this.TipoDep = TipoDep;
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }
}

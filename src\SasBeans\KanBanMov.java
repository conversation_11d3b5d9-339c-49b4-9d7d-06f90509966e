/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class KanBanMov {

    private BigDecimal Sequencia;
    private LocalDate Data;
    private BigDecimal Ordem;
    private String Fase;
    private BigDecimal CodPessoa;
    private String nomePessoa;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public BigDecimal getOrdem() {
        return Ordem;
    }

    public void setOrdem(BigDecimal Ordem) {
        this.Ordem = Ordem;
    }

    public String getFase() {
        return Fase;
    }

    public void setFase(String Fase) {
        this.Fase = Fase;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }
}

/*
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.Mascaras;
import static br.com.sasw.utils.Messages.getMessageS;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Locale;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversormoeda")
public class ConversorMoeda implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
//        return Mascaras.removeMascara(value.replace(Messages.getMessageS("$"), ""));
        String retorno = Mascaras.removeMascaraMoeda(value);
        return retorno;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        Locale locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
        NumberFormat nf = NumberFormat.getNumberInstance(locale);
        String moeda, valor;
        int casas = 0;
        BigDecimal mbd = new BigDecimal("0.00");
        try {
            mbd = mbd.add(new BigDecimal(value.toString().replaceAll(",", ".")));
        } catch (Exception x) {
            mbd = new BigDecimal("0.00");
        }
        valor = mbd.setScale(2, RoundingMode.UP).toString();
        casas = valor.length() - 3;
        moeda = valor.substring(casas).replace(".", ",");
        for (int i = 1; i <= casas; i++) {
            if (i % 3 == 0 && i != casas) {
                moeda = "." + valor.substring(casas - i, casas - (i - 1)) + moeda;
            } else {
                moeda = valor.substring(casas - i, casas - (i - 1)) + moeda;
            }
        }

//        if (moeda.indexOf(".") == 0) {
//            moeda = moeda.substring(1);
//        }
        if (moeda.charAt(0) == '-') {
            if (moeda.charAt(1) == '.') {
                moeda = moeda.charAt(0) + getMessageS("$") + moeda.substring(2);
            } else {
                moeda = moeda.charAt(0) + getMessageS("$") + moeda.substring(1);
            }
        } else {
            moeda = getMessageS("$") + moeda;
        }

        switch (locale.getLanguage()) {
            case "en":
                moeda = moeda.replace(",", "x");
                moeda = moeda.replace(".", ",");
                moeda = moeda.replace("x", ".");
                break;
        }

        return moeda;
    }
}

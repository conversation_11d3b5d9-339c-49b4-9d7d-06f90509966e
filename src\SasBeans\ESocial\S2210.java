/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class S2210 {

    private int sucesso; //
    private String evtCAT_Id;

    private String ideEvento_indRetif;//
    private String ideEvento_nrRecibo;//

    private String ideEvento_tpAmb;//

    private String ideEvento_procEmi;//
    private String ideEvento_verProc;//

    private String ideEmpregador_tpInsc; //
    private String ideEmpregador_nrInsc; //

    private String ideVinculo_cpfTrab;//
    private String ideVinculo_matricula;//
    private String ideVinculo_matr;//
    private String ideVinculo_codCateg;//

    private String cat_dtAcid; //
    private int cat_tpAcid;//
    private String cat_hrAcid;//
    private String cat_hrsTrabAntesAcid;//
    private int cat_tpCat;//
    private String cat_indCatObito;//
    private String cat_dtObito;//
    private String cat_indComunPolicia;//
    private int cat_codSitGeradora;//
    private int cat_iniciatCAT;//
    private String cat_obsCAT;//

    private int localAcidente_tpLocal;//
    private String localAcidente_dscLocal;//
    private String localAcidente_tpLograd;//
    private String localAcidente_dscLograd;//
    private String localAcidente_nrLograd;//
    private String localAcidente_complemento;//
    private String localAcidente_bairro;//
    private String localAcidente_cep;//
    private int localAcidente_codMunic;//
    private String localAcidente_uf;//
    private String localAcidente_pais;//
    private String localAcidente_codPostal;//

    private int ideLocalAcid_tpInsc;//
    private String ideLocalAcid_nrInsc;//

    private int parteAtingida_codParteAting;//
    private int parteAtingida_lateralidade;//

    private int agenteCausador_codAgntCausador;//

    private String atestado_dtAtendimento;//
    private String atestado_hrAtendimento;//
    private String atestado_indInternacao;//
    private int atestado_durTrat;//
    private String atestado_indAfast;//
    private int atestado_dscLesao;//
    private String atestado_dscCompLesao;//
    private String atestado_diagProvavel;//
    private String atestado_codCID;//
    private String atestado_observacao;//
    
    private String emitente_nmEmit;
    private int emitente_ideOC;
    private String emitente_nrOC;
    private String emitente_ufOC;
    
    private String cat_ultDiaTrab;
    private String cat_houveAfast;
    
    private String catOrigem_nrRecCatOrig;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtCAT_Id() {
        return evtCAT_Id;
    }

    public void setEvtCAT_Id(String evtCAT_Id) {
        this.evtCAT_Id = evtCAT_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeVinculo_cpfTrab() {
        return ideVinculo_cpfTrab;
    }

    public void setIdeVinculo_cpfTrab(String ideVinculo_cpfTrab) {
        this.ideVinculo_cpfTrab = ideVinculo_cpfTrab;
    }

    public String getIdeVinculo_matricula() {
        return ideVinculo_matricula;
    }

    public void setIdeVinculo_matricula(String ideVinculo_matricula) {
        this.ideVinculo_matricula = ideVinculo_matricula;
    }

    public String getIdeVinculo_codCateg() {
        return ideVinculo_codCateg;
    }

    public void setIdeVinculo_codCateg(String ideVinculo_codCateg) {
        this.ideVinculo_codCateg = ideVinculo_codCateg;
    }

    public String getCat_dtAcid() {
        return cat_dtAcid;
    }

    public void setCat_dtAcid(String cat_dtAcid) {
        this.cat_dtAcid = cat_dtAcid;
    }

    public int getCat_tpAcid() {
        return cat_tpAcid;
    }

    public void setCat_tpAcid(int cat_tpAcid) {
        this.cat_tpAcid = cat_tpAcid;
    }

    public String getCat_hrAcid() {
        return cat_hrAcid;
    }

    public void setCat_hrAcid(String cat_hrAcid) {
        this.cat_hrAcid = cat_hrAcid;
    }

    public String getCat_hrsTrabAntesAcid() {
        return cat_hrsTrabAntesAcid;
    }

    public void setCat_hrsTrabAntesAcid(String cat_hrsTrabAntesAcid) {
        this.cat_hrsTrabAntesAcid = cat_hrsTrabAntesAcid;
    }

    public int getCat_tpCat() {
        return cat_tpCat;
    }

    public void setCat_tpCat(int cat_tpCat) {
        this.cat_tpCat = cat_tpCat;
    }

    public String getCat_indCatObito() {
        return cat_indCatObito;
    }

    public void setCat_indCatObito(String cat_indCatObito) {
        this.cat_indCatObito = cat_indCatObito;
    }

    public String getCat_dtObito() {
        return "";
    }

    public void setCat_dtObito(String cat_dtObito) {
        this.cat_dtObito = cat_dtObito;
    }

    public String getCat_indComunPolicia() {
        return cat_indComunPolicia;
    }

    public void setCat_indComunPolicia(String cat_indComunPolicia) {
        this.cat_indComunPolicia = cat_indComunPolicia;
    }

    public int getCat_codSitGeradora() {
        return cat_codSitGeradora;
    }

    public void setCat_codSitGeradora(int cat_codSitGeradora) {
        this.cat_codSitGeradora = cat_codSitGeradora;
    }

    public int getCat_iniciatCAT() {
        return cat_iniciatCAT;
    }

    public void setCat_iniciatCAT(int cat_iniciatCAT) {
        this.cat_iniciatCAT = cat_iniciatCAT;
    }

    public String getCat_obsCAT() {
        return cat_obsCAT;
    }

    public void setCat_obsCAT(String cat_obsCAT) {
        this.cat_obsCAT = cat_obsCAT;
    }

    public int getLocalAcidente_tpLocal() {
        return localAcidente_tpLocal;
    }

    public void setLocalAcidente_tpLocal(int localAcidente_tpLocal) {
        this.localAcidente_tpLocal = localAcidente_tpLocal;
    }

    public String getLocalAcidente_dscLocal() {
        return localAcidente_dscLocal;
    }

    public void setLocalAcidente_dscLocal(String localAcidente_dscLocal) {
        this.localAcidente_dscLocal = localAcidente_dscLocal;
    }

    public String getLocalAcidente_tpLograd() {
        return localAcidente_tpLograd;
    }

    public void setLocalAcidente_tpLograd(String localAcidente_tpLograd) {
        this.localAcidente_tpLograd = localAcidente_tpLograd;
    }

    public String getLocalAcidente_dscLograd() {
        return localAcidente_dscLograd;
    }

    public void setLocalAcidente_dscLograd(String localAcidente_dscLograd) {
        this.localAcidente_dscLograd = localAcidente_dscLograd;
    }

    public String getLocalAcidente_nrLograd() {
        return localAcidente_nrLograd;
    }

    public void setLocalAcidente_nrLograd(String localAcidente_nrLograd) {
        this.localAcidente_nrLograd = localAcidente_nrLograd;
    }

    public String getLocalAcidente_complemento() {
        return localAcidente_complemento;
    }

    public void setLocalAcidente_complemento(String localAcidente_complemento) {
        this.localAcidente_complemento = localAcidente_complemento;
    }

    public String getLocalAcidente_bairro() {
        return localAcidente_bairro;
    }

    public void setLocalAcidente_bairro(String localAcidente_bairro) {
        this.localAcidente_bairro = localAcidente_bairro;
    }

    public String getLocalAcidente_cep() {
        return localAcidente_cep;
    }

    public void setLocalAcidente_cep(String localAcidente_cep) {
        this.localAcidente_cep = localAcidente_cep;
    }

    public int getLocalAcidente_codMunic() {
        return localAcidente_codMunic;
    }

    public void setLocalAcidente_codMunic(int localAcidente_codMunic) {
        this.localAcidente_codMunic = localAcidente_codMunic;
    }

    public String getLocalAcidente_uf() {
        return localAcidente_uf;
    }

    public void setLocalAcidente_uf(String localAcidente_uf) {
        this.localAcidente_uf = localAcidente_uf;
    }

    public String getLocalAcidente_pais() {
        return localAcidente_pais;
    }

    public void setLocalAcidente_pais(String localAcidente_pais) {
        this.localAcidente_pais = localAcidente_pais;
    }

    public String getLocalAcidente_codPostal() {
        return "";
    }

    public void setLocalAcidente_codPostal(String localAcidente_codPostal) {
        this.localAcidente_codPostal = localAcidente_codPostal;
    }

    public int getIdeLocalAcid_tpInsc() {
        return 1;
    }

    public void setIdeLocalAcid_tpInsc(int ideLocalAcid_tpInsc) {
        this.ideLocalAcid_tpInsc = ideLocalAcid_tpInsc;
    }

    public String getIdeLocalAcid_nrInsc() {
        return ideLocalAcid_nrInsc;
    }

    public void setIdeLocalAcid_nrInsc(String ideLocalAcid_nrInsc) {
        this.ideLocalAcid_nrInsc = ideLocalAcid_nrInsc;
    }

    public int getParteAtingida_codParteAting() {
        return parteAtingida_codParteAting;
    }

    public void setParteAtingida_codParteAting(int parteAtingida_codParteAting) {
        this.parteAtingida_codParteAting = parteAtingida_codParteAting;
    }

    public int getParteAtingida_lateralidade() {
        return parteAtingida_lateralidade;
    }

    public void setParteAtingida_lateralidade(int parteAtingida_lateralidade) {
        this.parteAtingida_lateralidade = parteAtingida_lateralidade;
    }

    public int getAgenteCausador_codAgntCausador() {
        return agenteCausador_codAgntCausador;
    }

    public void setAgenteCausador_codAgntCausador(int agenteCausador_codAgntCausador) {
        this.agenteCausador_codAgntCausador = agenteCausador_codAgntCausador;
    }

    public String getAtestado_dtAtendimento() {
        return atestado_dtAtendimento;
    }

    public void setAtestado_dtAtendimento(String atestado_dtAtendimento) {
        this.atestado_dtAtendimento = atestado_dtAtendimento;
    }

    public String getAtestado_hrAtendimento() {
        return atestado_hrAtendimento;
    }

    public void setAtestado_hrAtendimento(String atestado_hrAtendimento) {
        this.atestado_hrAtendimento = atestado_hrAtendimento;
    }

    public String getAtestado_indInternacao() {
        return atestado_indInternacao;
    }

    public void setAtestado_indInternacao(String atestado_indInternacao) {
        this.atestado_indInternacao = atestado_indInternacao;
    }

    public int getAtestado_durTrat() {
        return atestado_durTrat;
    }

    public void setAtestado_durTrat(int atestado_durTrat) {
        this.atestado_durTrat = atestado_durTrat;
    }

    public String getAtestado_indAfast() {
        return atestado_indAfast;
    }

    public void setAtestado_indAfast(String atestado_indAfast) {
        this.atestado_indAfast = atestado_indAfast;
    }

    public int getAtestado_dscLesao() {
        return atestado_dscLesao;
    }

    public void setAtestado_dscLesao(int atestado_dscLesao) {
        this.atestado_dscLesao = atestado_dscLesao;
    }

    public String getAtestado_dscCompLesao() {
        return atestado_dscCompLesao;
    }

    public void setAtestado_dscCompLesao(String atestado_dscCompLesao) {
        this.atestado_dscCompLesao = atestado_dscCompLesao;
    }

    public String getAtestado_diagProvavel() {
        return atestado_diagProvavel;
    }

    public void setAtestado_diagProvavel(String atestado_diagProvavel) {
        this.atestado_diagProvavel = atestado_diagProvavel;
    }

    public String getAtestado_codCID() {
        return atestado_codCID;
    }

    public void setAtestado_codCID(String atestado_codCID) {
        this.atestado_codCID = atestado_codCID;
    }

    public String getAtestado_observacao() {
        return atestado_observacao;
    }

    public void setAtestado_observacao(String atestado_observacao) {
        this.atestado_observacao = atestado_observacao;
    }

    public String getEmitente_nmEmit() {
        return emitente_nmEmit; //
    }

    public void setEmitente_nmEmit(String emitente_nmEmit) {
        this.emitente_nmEmit = emitente_nmEmit;
    }

    public int getEmitente_ideOC() {
        return emitente_ideOC;
    }

    public void setEmitente_ideOC(int emitente_ideOC) {
        this.emitente_ideOC = emitente_ideOC;
    }

    public String getEmitente_nrOC() {
        return emitente_nrOC;//;
    }

    public void setEmitente_nrOC(String emitente_nrOC) {
        this.emitente_nrOC = emitente_nrOC;
    }

    public String getEmitente_ufOC() {
        return emitente_ufOC;//;
    }

    public void setEmitente_ufOC(String emitente_ufOC) {
        this.emitente_ufOC = emitente_ufOC;
    }

    public String getCatOrigem_nrRecCatOrig() {
        return catOrigem_nrRecCatOrig;
    }

    public void setCatOrigem_nrRecCatOrig(String catOrigem_nrRecCatOrig) {
        this.catOrigem_nrRecCatOrig = catOrigem_nrRecCatOrig;
    }

    public String getIdeVinculo_matr() {
        return ideVinculo_matr;
    }

    public void setIdeVinculo_matr(String ideVinculo_matr) {
        this.ideVinculo_matr = ideVinculo_matr;
    }
    
     @Override
    public int hashCode() {
        int hash = 5;
        hash = 29 * hash + Objects.hashCode(this.ideVinculo_cpfTrab);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final S2210 other = (S2210) obj;
        if (!Objects.equals(this.ideVinculo_cpfTrab, other.ideVinculo_cpfTrab)) {
            return false;
        }
        return true;
    }

    /**
     * @return the cat_ultDiaTrab
     */
    public String getCat_ultDiaTrab() {
        return cat_ultDiaTrab;
    }

    /**
     * @param cat_ultDiaTrab the cat_ultDiaTrab to set
     */
    public void setCat_ultDiaTrab(String cat_ultDiaTrab) {
        this.cat_ultDiaTrab = cat_ultDiaTrab;
    }

    /**
     * @return the cat_houveAfast
     */
    public String getCat_houveAfast() {
        return cat_houveAfast;
    }

    /**
     * @param cat_houveAfast the cat_houveAfast to set
     */
    public void setCat_houveAfast(String cat_houveAfast) {
        this.cat_houveAfast = cat_houveAfast;
    }
}

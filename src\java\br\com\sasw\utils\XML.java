/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.utils;

import SasBeans.ESocial.Ocorrencia;
import SasBeans.ESocial.Processamento;
import SasBeans.XMLeSocial;
import com.sun.org.apache.xerces.internal.dom.DeferredElementNSImpl;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

/**
 *
 * <AUTHOR>
 */
public class XML {

    public static String buscarTag(String tag, int nivel, String xml) throws Exception {
        try {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document doc = dbf.newDocumentBuilder().parse(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8.name())));
            NodeList elements = doc.getElementsByTagName(tag);
            if (elements != null && elements.getLength() > 0) {
                NodeList subList = elements.item(nivel).getChildNodes();
                if (subList != null && subList.getLength() > 0) {
                    return subList.item(0).getNodeValue();
                }
            }
            throw new Exception(xml);
        } catch (Exception e) {
            throw new Exception("buscarTag erro: " + e.getMessage());
        }
    }

    public static int contarErros(String xml) throws Exception {
        try {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document doc = dbf.newDocumentBuilder().parse(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8.name())));
            NodeList elements = doc.getElementsByTagName("ocorrencia");
            return elements.getLength();
        } catch (Exception e) {
            throw new Exception("contarErros erro: " + e.getMessage());
        }
    }

    public static List<Processamento> interpretadorProcessamento(List<XMLeSocial> listaXMLeSocial) throws Exception {
        try {
            List<Processamento> processamentos = new ArrayList<>();
            Processamento processamento;
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            for (XMLeSocial xmleSocial : listaXMLeSocial) {
                if (xmleSocial.getXML_Retorno().equals("")) {
                    processamento = new Processamento();
                    processamento.setIdentificador(xmleSocial.getIdentificador());

                    processamento.setId("");

                    processamento.setCdResposta("0");
                    processamento.setDescResposta("EVENTO NÃO CONSULTADO");
                    processamento.setVersaoAppProcessamento("");
                    processamento.setDhProcessamento("");
                    processamento.setOcorrencias(new ArrayList<>());

                    processamentos.add(processamento);
                } else {
                    Document doc = dbf.newDocumentBuilder().parse(new ByteArrayInputStream(xmleSocial.getXML_Retorno().getBytes(StandardCharsets.UTF_8.name())));
                    NodeList retornoProcessamentoLoteEventos = doc.getElementsByTagName("status");
                    Element status = (Element) retornoProcessamentoLoteEventos.item(0);
                    if (status.getElementsByTagName("cdResposta").item(0).getTextContent().equals("201")) {
                        NodeList eventos = doc.getElementsByTagName("evento");
                        for (int i = 0; i < eventos.getLength(); i++) {
                            Element evento = (Element) eventos.item(i);
                            processamento = new Processamento();
                            processamento.setIdentificador(xmleSocial.getIdentificador());

                            processamento.setId(evento.getAttribute("Id"));
                            processamento.setProtocolo(evento.getElementsByTagName("protocoloEnvioLote").item(0).getTextContent());

                            processamento.setCdResposta(evento.getElementsByTagName("cdResposta").item(0).getTextContent());
                            processamento.setDescResposta(evento.getElementsByTagName("descResposta").item(0).getTextContent());
                            processamento.setVersaoAppProcessamento(evento.getElementsByTagName("versaoAppProcessamento").item(0).getTextContent());
                            processamento.setDhProcessamento(evento.getElementsByTagName("dhProcessamento").item(0).getTextContent());

                            List<Ocorrencia> ocorrencias = new ArrayList<>();
                            if (evento.getElementsByTagName("ocorrencias").getLength() > 0) {
                                Ocorrencia ocorrencia;
                                NodeList ocorrenciasNL = evento.getElementsByTagName("ocorrencias").item(0).getChildNodes();
                                for (int j = 0; j < ocorrenciasNL.getLength(); j++) {
                                    ocorrencia = new Ocorrencia();
                                    NodeList ocorrenciaNL = ocorrenciasNL.item(j).getChildNodes();
                                    for (int k = 0; k < ocorrenciaNL.getLength(); k++) {
                                        Element ocorrenciaEL = (Element) ocorrenciaNL.item(k);
                                        switch (ocorrenciaEL.getNodeName()) {
                                            case "tipo":
                                                ocorrencia.setTipo(ocorrenciaEL.getTextContent());
                                                break;
                                            case "codigo":
                                                ocorrencia.setCodigo(ocorrenciaEL.getTextContent());
                                                break;
                                            case "descricao":
                                                ocorrencia.setDescricao(ocorrenciaEL.getTextContent());
                                                break;
                                            case "localizacao":
                                                ocorrencia.setLocalizacao(ocorrenciaEL.getTextContent());
                                                break;
                                        }
                                    }
                                    ocorrencias.add(ocorrencia);
                                }
                            }
                            processamento.setOcorrencias(ocorrencias);

                            processamentos.add(processamento);
                        }
                    } else {
                        processamento = new Processamento();
                        processamento.setIdentificador(xmleSocial.getIdentificador());

                        processamento.setId("");
                        processamento.setProtocolo("");

                        processamento.setCdResposta(status.getElementsByTagName("cdResposta").item(0).getTextContent());
                        processamento.setDescResposta(status.getElementsByTagName("descResposta").item(0).getTextContent());
                        processamento.setVersaoAppProcessamento("");
                        processamento.setDhProcessamento("");

                        List<Ocorrencia> ocorrencias = new ArrayList<>();
                        if (status.getElementsByTagName("ocorrencias").getLength() > 0) {
                            Ocorrencia ocorrencia;
                            NodeList ocorrenciasNL = status.getElementsByTagName("ocorrencias").item(0).getChildNodes();
                            for (int j = 0; j < ocorrenciasNL.getLength(); j++) {
                                ocorrencia = new Ocorrencia();
                                NodeList ocorrenciaNL = ocorrenciasNL.item(j).getChildNodes();
                                for (int k = 0; k < ocorrenciaNL.getLength(); k++) {
                                    Element ocorrenciaEL = (Element) ocorrenciaNL.item(k);
                                    switch (ocorrenciaEL.getNodeName()) {
                                        case "tipo":
                                            ocorrencia.setTipo(ocorrenciaEL.getTextContent());
                                            break;
                                        case "codigo":
                                            ocorrencia.setCodigo(ocorrenciaEL.getTextContent());
                                            break;
                                        case "descricao":
                                            ocorrencia.setDescricao(ocorrenciaEL.getTextContent());
                                            break;
                                        case "localizacao":
                                            ocorrencia.setLocalizacao(ocorrenciaEL.getTextContent());
                                            break;
                                    }
                                }
                                ocorrencias.add(ocorrencia);
                            }
                        }
                        processamento.setOcorrencias(ocorrencias);

                        processamentos.add(processamento);
                    }
                }
            }
            return processamentos;
        } catch (Exception e) {
            throw new Exception("listarErros erro: " + e.getMessage());
        }
    }

    public static List<Processamento> interpretadorProcessamentoReinf(List<XMLeSocial> listaXMLeSocial) throws Exception {
        try {
            List<Processamento> processamentos = new ArrayList<>();
            Processamento processamento;
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            for (XMLeSocial xmleSocial : listaXMLeSocial) {
                if (xmleSocial.getXML_Retorno().equals("")) {
                    processamento = new Processamento();
                    processamento.setIdentificador(xmleSocial.getIdentificador());

                    processamento.setId("");

                    processamento.setCdResposta("0");
                    processamento.setDescResposta("EVENTO NÃO CONSULTADO");
                    processamento.setVersaoAppProcessamento("");
                    processamento.setDhProcessamento("");
                    processamento.setOcorrencias(new ArrayList<>());

                    processamentos.add(processamento);
                } else {
                    Document doc = dbf.newDocumentBuilder().parse(new ByteArrayInputStream(xmleSocial.getXML_Retorno().getBytes(StandardCharsets.UTF_8.name())));

                    NodeList eventos = doc.getElementsByTagName("evento");
                    if (eventos.getLength() > 0) {
                        for (int i = 0; i < eventos.getLength(); i++) {
                            Element evento = (Element) eventos.item(i);
                            processamento = new Processamento();
                            processamento.setIdentificador(xmleSocial.getIdentificador());

                            processamento.setId(evento.getAttribute("id"));
                            try {
                                processamento.setProtocolo(evento.getElementsByTagName("nrRecArqBase").item(0).getTextContent());
                            } catch (Exception ex) {
                                processamento.setProtocolo("");
                            }

                            processamento.setCdResposta(evento.getElementsByTagName("cdRetorno").item(0).getTextContent());
                            processamento.setDescResposta(evento.getElementsByTagName("descRetorno").item(0).getTextContent());
                            processamento.setVersaoAppProcessamento(evento.getElementsByTagName("tpEv").item(0).getTextContent());
                            processamento.setDhProcessamento(evento.getElementsByTagName("dhProcess").item(0).getTextContent());
                            //processamento.setProtocolo(evento.getElementsByTagName("nrRecArqBase").item(0).getTextContent()); 

                            if (evento.getElementsByTagName("cdRetorno").item(0).getTextContent().equals("1")) {
                                List<Ocorrencia> ocorrencias = new ArrayList<>();
                                Ocorrencia ocorrencia;
                                if (evento.getElementsByTagName("regOcorrs").getLength() > 0) {
                                    NodeList ocorrenciasNL = evento.getElementsByTagName("regOcorrs").item(0).getChildNodes();
                                    ocorrencia = new Ocorrencia();
                                    for (int j = 0; j < ocorrenciasNL.getLength(); j++) {
                                        DeferredElementNSImpl ocorrenciaEL = (DeferredElementNSImpl) ocorrenciasNL.item(j);
                                        switch (ocorrenciaEL.getNodeName()) {
                                            case "tpOcorr":
                                                ocorrencia.setTipo(ocorrenciaEL.getTextContent());
                                                break;
                                            case "codResp":
                                                ocorrencia.setCodigo(ocorrenciaEL.getTextContent());
                                                break;
                                            case "dscResp":
                                                ocorrencia.setDescricao(ocorrenciaEL.getTextContent());
                                                break;
                                            case "localErroAviso":
                                                ocorrencia.setLocalizacao(ocorrenciaEL.getTextContent());
                                                break;
                                        }

                                    }
                                    ocorrencias.add(ocorrencia);
                                }
                                processamento.setOcorrencias(ocorrencias);
                            }

                            processamentos.add(processamento);
                        }
                    } else {
                        NodeList retornos = doc.getElementsByTagName("evtTotalContrib");
                        for (int i = 0; i < retornos.getLength(); i++) {
                            Element evento = (Element) retornos.item(i);
                            processamento = new Processamento();
                            processamento.setIdentificador(xmleSocial.getIdentificador());

                            processamento.setId(evento.getAttribute("id"));
                            try {
                                processamento.setProtocolo(evento.getElementsByTagName("nrProtEntr").item(0).getTextContent());
                            } catch (Exception ex) {
                                processamento.setProtocolo("");
                            }

                            processamento.setCdResposta(evento.getElementsByTagName("cdRetorno").item(0).getTextContent());
                            processamento.setDescResposta(evento.getElementsByTagName("descRetorno").item(0).getTextContent());
                            processamento.setVersaoAppProcessamento(evento.getElementsByTagName("tpEv").item(0).getTextContent());
                            processamento.setDhProcessamento(evento.getElementsByTagName("dhProcess").item(0).getTextContent());
                            //processamento.setProtocolo(evento.getElementsByTagName("nrRecArqBase").item(0).getTextContent()); 

                            if (evento.getElementsByTagName("cdRetorno").item(0).getTextContent().equals("1")) {
                                List<Ocorrencia> ocorrencias = new ArrayList<>();
                                Ocorrencia ocorrencia;
                                if (evento.getElementsByTagName("regOcorrs").getLength() > 0) {
                                    NodeList ocorrenciasNL = evento.getElementsByTagName("regOcorrs").item(0).getChildNodes();
                                    ocorrencia = new Ocorrencia();
                                    for (int j = 0; j < ocorrenciasNL.getLength(); j++) {
                                        DeferredElementNSImpl ocorrenciaEL = (DeferredElementNSImpl) ocorrenciasNL.item(j);
                                        switch (ocorrenciaEL.getNodeName()) {
                                            case "tpOcorr":
                                                ocorrencia.setTipo(ocorrenciaEL.getTextContent());
                                                break;
                                            case "codResp":
                                                ocorrencia.setCodigo(ocorrenciaEL.getTextContent());
                                                break;
                                            case "dscResp":
                                                ocorrencia.setDescricao(ocorrenciaEL.getTextContent());
                                                break;
                                            case "localErroAviso":
                                                ocorrencia.setLocalizacao(ocorrenciaEL.getTextContent());
                                                break;
                                        }

                                    }
                                    ocorrencias.add(ocorrencia);
                                }
                                processamento.setOcorrencias(ocorrencias);
                            }

                            processamentos.add(processamento);
                        }
                    }
                }
            }
            return processamentos;
        } catch (Exception e) {
            throw new Exception("listarErros erro: " + e.getMessage());
        }
    }
}

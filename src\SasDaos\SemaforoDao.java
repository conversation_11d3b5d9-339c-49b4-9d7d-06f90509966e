/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Semaforo;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.Horaminuto;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SemaforoDao {

    /**
     * Remove registros da tabela do banco de daods
     *
     * @param tabela
     * @param chave chave
     * @param persistencia conexao com o banco de dados
     * @throws Exception
     */
    public void removerBaixaTabela(String tabela, String chave, Persistencia persistencia) throws Exception {
        try {
            String sql = "DELETE FROM semaforo WHERE tabela = ? and chave = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tabela);
            consulta.setString(chave);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("SemaforoDao.removerBaixaTabela - " + e.getMessage() + "\r\n"
                    + "DELETE FROM semaforo WHERE tabela = " + tabela + " and chave = " + chave);
        }
    }

    /**
     * Verifica se o registro já existe na tablea
     *
     * @param tabela nome da tabela
     * @param chave chave para inclusao na tabela
     * @param data
     * @param persistencia conexao com o baoc de dados
     * @return existe ou nao
     * @throws Exception
     */
    public boolean existeSemaforo(String tabela, String chave, String data, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM semaforo WHERE Chave = ? AND tabela = ? ";//AND dt_alter = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(chave);
            consulta.setString(tabela);
//            consulta.setString(data);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }
            consulta.Close();
            if (quantidade > 0) {
                existe = true;
            }
        } catch (Exception e) {
            throw new Exception("SemaforoDao.existeSemafaro - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM semaforo WHERE Chave = " + chave + " AND tabela = " + tabela);
        }
        return existe;
    }

    /**
     * Verifica se o registro já existe na tablea
     *
     * @param tabela nome da tabela
     * @param chave chave para inclusao na tabela
     * @param persistencia conexao com o baoc de dados
     * @return existe ou nao
     * @throws Exception
     */
    public int tempoSemaforo(String tabela, String chave, Persistencia persistencia) throws Exception {
        int tempo;
        try {
            String sql = "SELECT hr_alter FROM semaforo WHERE Chave = ? AND tabela = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(chave);
            consulta.setString(tabela);
            consulta.select();

            String hora = null;
            while (consulta.Proximo()) {
                hora = consulta.getString("hr_alter");
            }
            consulta.Close();
            try {
                Horaminuto dif = new Horaminuto();
                dif.setHora1(getDataAtual("HORA"));
                dif.setHora2(hora);
                tempo = dif.iDifHora1Hora2min();
                if (tempo < 0) {
                    tempo = tempo * -1;
                }
            } catch (Exception e) {
                tempo = 0;
            }
        } catch (Exception e) {
            throw new Exception("SemaforoDao.tempoSemaforo - " + e.getMessage() + "\r\n"
                    + "SELECT hr_alter FROM semaforo WHERE Chave = " + chave + " AND tabela = " + tabela);
        }
        return tempo;
    }

    /**
     * Inserir registros no banco de dados
     *
     * @param operador do sistema
     * @param data da inclusao
     * @param hora da inclusao
     * @param tabela nome da table
     * @param chave chave do registros
     * @param persistencia
     * @throws Exception
     */
    public void inserirRegistro(String operador, String data, String hora, String tabela, String chave, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO semaforo (tabela, chave, dt_alter, hr_alter, operador) VALUES(?,?,?,?,?)";

            if (operador.contains(".0")) {
                operador = operador.replace(".0", "");
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tabela);
            consulta.setString(chave);
            consulta.setString(data.replace("-", ""));
            consulta.setString(hora);
            consulta.setString(operador);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("SemaforoDao.inserirRegistro - " + e.getMessage() + "\r\n"
                    + "INSERT INTO semaforo (tabela, chave, dt_alter, hr_alter, operador) "
                    + "VALUES(" + tabela + "," + chave + "," + data.replace("-", "") + "," + hora + "," + operador + ")");
        }
    }

    /**
     * gravaSemaforo
     *
     * @param semaforo
     * @param persistencia
     * @return
     */
    public boolean gravaSemaforo(Semaforo semaforo, Persistencia persistencia) {
        boolean retorno;
        String sql = "insert into semaforo(Tabela,Chave,Sequencia,Operador,Dt_Alter,Hr_Alter) "
                + "Values (?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(semaforo.getTabela());
            consulta.setString(semaforo.getChave());
            consulta.setBigDecimal(semaforo.getSequencia());
            consulta.setString(semaforo.getOperador());
            consulta.setString(semaforo.getDt_Alter().toString());
            consulta.setString(semaforo.getHr_Alter());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    /**
     * buscaSemaforo
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Semaforo> buscaSemaforo(Persistencia persistencia) throws Exception {
        List<Semaforo> listSemaforo;
        try {
            Semaforo semaforo;
            Consulta consult = new Consulta("select Tabela,Chave,Sequencia,Operador,Dt_Alter,Hr_Alter "
                    + "from semaforo", persistencia);
            consult.select();
            listSemaforo = new ArrayList();
            while (consult.Proximo()) {
                semaforo = new Semaforo();
                semaforo.setTabela(consult.getString("Tabela"));
                semaforo.setChave(consult.getString("Chave"));
                semaforo.setSequencia(consult.getString("Sequencia"));
                semaforo.setOperdor(consult.getString("Operador"));
                semaforo.setDt_Alter(consult.getDate("Dt_Alter").toLocalDate());
                semaforo.setHr_Alter(consult.getString("Hr_Alter"));
                listSemaforo.add(semaforo);
            }
            consult.Close();
        } catch (Exception e) {
            listSemaforo = null;
            throw new Exception("SemaforoDao.buscaSemaforo - " + e.getMessage() + "\r\n"
                    + " select Tabela,Chave,Sequencia,Operador,Dt_Alter,Hr_Alter "
                    + " from semaforo");
        }
        return listSemaforo;
    }

    /**
     * atualizarSemaforo
     *
     * @param semaforo
     * @param persistencia
     * @throws Exception
     */
    public void atualizarSemaforo(Semaforo semaforo, Persistencia persistencia) throws Exception {
        String sql = "update semaforo set Tabela=?,Chave=?,Sequencia=?,Operador=?,Dt_Alter=?,Hr_Alter=? "
                + "where Tabela=? and Chave=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(semaforo.getTabela());
            consulta.setString(semaforo.getChave());
            consulta.setBigDecimal(semaforo.getSequencia());
            consulta.setString(semaforo.getOperador());
            consulta.setString(semaforo.getDt_Alter().toString());
            consulta.setString(semaforo.getHr_Alter());
            consulta.setString(semaforo.getTabela());
            consulta.setString(semaforo.getChave());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("SemaforoDao.atualizarSemaforo - " + e.getMessage() + "\r\n"
                    + " update semaforo set Tabela=" + semaforo.getTabela() + ",Chave=" + semaforo.getChave() + ",Sequencia=" + semaforo.getSequencia() + ","
                    + " Operador=" + semaforo.getOperador() + ",Dt_Alter=" + semaforo.getDt_Alter() + ",Hr_Alter=" + semaforo.getHr_Alter()
                    + " where Tabela=" + semaforo.getTabela() + " and Chave=" + semaforo.getChave());
        }
    }

    /**
     * deleteSemaforo
     *
     * @param semaforo
     * @param persistencia
     * @throws Exception
     */
    public void deleteSemaforo(Semaforo semaforo, Persistencia persistencia) throws Exception {
        String sql = "delete from semaforo "
                + "where Tabela=? and Chave=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(semaforo.getTabela());
            consulta.setString(semaforo.getChave());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("SemaforoDao.deleteSemaforo - " + e.getMessage() + "\r\n"
                    + "delete from semaforo "
                    + "where Tabela=" + semaforo.getTabela() + " and Chave=" + semaforo.getChave());
        }
    }
}

/*
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class InterfaceCI {

    BigDecimal Sequencia;
    String Data;
    String Hora;
    String Mensagem;
    String Processado;

    public InterfaceCI() {
        Sequencia = new BigDecimal("0");
        Data = "";
        Hora = "";
        Mensagem = "";
        Processado = "N";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getMensagem() {
        return Mensagem;
    }

    public void setMensagem(String Mensagem) {
        this.Mensagem = Mensagem;
    }

    public String getProcessado() {
        return Processado;
    }

    public void setProcessado(String Processado) {
        this.Processado = Processado;
    }

}

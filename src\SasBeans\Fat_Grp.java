/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class Fat_Grp {

    private String Codigo;
    private String CodFil;
    private String Descricao;
    private String CodHistFix;
    private String CodHistExt;
    private String CodHist;
    private String AgrupInterf;
    private String TCobran;
    private String AliqIRRF;
    private String AliqISS;
    private String ISSRet;
    private String AliqPIS;
    private String PISRet;
    private String AliqCSL;
    private String CSLRet;
    private String AliqICMS;
    private String ICMSRet;
    private String AliqCOFINS;
    private String COFINSRet;
    private String AliqIRPJ;
    private String IRPJRet;
    private String BaseINSSPerc;
    private String AliqINSS;
    private String INSSRet;
    private String AgrupISS;
    private String Repasse;
    private String IndRepasseCst;
    private String CCusto;
    private String SitFiscal;
    private String ObsFiscal;
    private String OperFiscal;
    private String Dt_Fiscal;
    private String Hr_Fiscal;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Flag_Excl;
    private String OperExcl;
    private String Dt_Excl;
    private String Hr_Excl;

    private String Tipo;
    private String ISSGrpDesc;
    private String Municipio;
    private String UF;
    private String CodMunic;
    private String CFOP;
    private String CFOPDesc;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getCodHistFix() {
        return CodHistFix;
    }

    public void setCodHistFix(String CodHistFix) {
        this.CodHistFix = CodHistFix;
    }

    public String getCodHistExt() {
        return CodHistExt;
    }

    public void setCodHistExt(String CodHistExt) {
        this.CodHistExt = CodHistExt;
    }

    public String getCodHist() {
        return CodHist;
    }

    public void setCodHist(String CodHist) {
        this.CodHist = CodHist;
    }

    public String getAgrupInterf() {
        return AgrupInterf;
    }

    public void setAgrupInterf(String AgrupInterf) {
        this.AgrupInterf = AgrupInterf;
    }

    public String getTCobran() {
        return TCobran;
    }

    public void setTCobran(String TCobran) {
        this.TCobran = TCobran;
    }

    public String getAliqIRRF() {
        return AliqIRRF;
    }

    public void setAliqIRRF(String AliqIRRF) {
        this.AliqIRRF = AliqIRRF;
    }

    public String getAliqISS() {
        return AliqISS;
    }

    public void setAliqISS(String AliqISS) {
        this.AliqISS = AliqISS;
    }

    public String getISSRet() {
        return ISSRet;
    }

    public void setISSRet(String ISSRet) {
        this.ISSRet = ISSRet;
    }

    public String getAliqPIS() {
        return AliqPIS;
    }

    public void setAliqPIS(String AliqPIS) {
        this.AliqPIS = AliqPIS;
    }

    public String getPISRet() {
        return PISRet;
    }

    public void setPISRet(String PISRet) {
        this.PISRet = PISRet;
    }

    public String getAliqCSL() {
        return AliqCSL;
    }

    public void setAliqCSL(String AliqCSL) {
        this.AliqCSL = AliqCSL;
    }

    public String getCSLRet() {
        return CSLRet;
    }

    public void setCSLRet(String CSLRet) {
        this.CSLRet = CSLRet;
    }

    public String getAliqICMS() {
        return AliqICMS;
    }

    public void setAliqICMS(String AliqICMS) {
        this.AliqICMS = AliqICMS;
    }

    public String getICMSRet() {
        return ICMSRet;
    }

    public void setICMSRet(String ICMSRet) {
        this.ICMSRet = ICMSRet;
    }

    public String getAliqCOFINS() {
        return AliqCOFINS;
    }

    public void setAliqCOFINS(String AliqCOFINS) {
        this.AliqCOFINS = AliqCOFINS;
    }

    public String getCOFINSRet() {
        return COFINSRet;
    }

    public void setCOFINSRet(String COFINSRet) {
        this.COFINSRet = COFINSRet;
    }

    public String getAliqIRPJ() {
        return AliqIRPJ;
    }

    public void setAliqIRPJ(String AliqIRPJ) {
        this.AliqIRPJ = AliqIRPJ;
    }

    public String getIRPJRet() {
        return IRPJRet;
    }

    public void setIRPJRet(String IRPJRet) {
        this.IRPJRet = IRPJRet;
    }

    public String getBaseINSSPerc() {
        return BaseINSSPerc;
    }

    public void setBaseINSSPerc(String BaseINSSPerc) {
        this.BaseINSSPerc = BaseINSSPerc;
    }

    public String getAliqINSS() {
        return AliqINSS;
    }

    public void setAliqINSS(String AliqINSS) {
        this.AliqINSS = AliqINSS;
    }

    public String getINSSRet() {
        return INSSRet;
    }

    public void setINSSRet(String INSSRet) {
        this.INSSRet = INSSRet;
    }

    public String getAgrupISS() {
        return AgrupISS;
    }

    public void setAgrupISS(String AgrupISS) {
        this.AgrupISS = AgrupISS;
    }

    public String getRepasse() {
        return Repasse;
    }

    public void setRepasse(String Repasse) {
        this.Repasse = Repasse;
    }

    public String getIndRepasseCst() {
        return IndRepasseCst;
    }

    public void setIndRepasseCst(String IndRepasseCst) {
        this.IndRepasseCst = IndRepasseCst;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public String getSitFiscal() {
        return SitFiscal;
    }

    public void setSitFiscal(String SitFiscal) {
        this.SitFiscal = SitFiscal;
    }

    public String getObsFiscal() {
        return ObsFiscal;
    }

    public void setObsFiscal(String ObsFiscal) {
        this.ObsFiscal = ObsFiscal;
    }

    public String getOperFiscal() {
        return OperFiscal;
    }

    public void setOperFiscal(String OperFiscal) {
        this.OperFiscal = OperFiscal;
    }

    public String getDt_Fiscal() {
        return Dt_Fiscal;
    }

    public void setDt_Fiscal(String Dt_Fiscal) {
        this.Dt_Fiscal = Dt_Fiscal;
    }

    public String getHr_Fiscal() {
        return Hr_Fiscal;
    }

    public void setHr_Fiscal(String Hr_Fiscal) {
        this.Hr_Fiscal = Hr_Fiscal;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    public String getOperExcl() {
        return OperExcl;
    }

    public void setOperExcl(String OperExcl) {
        this.OperExcl = OperExcl;
    }

    public String getDt_Excl() {
        return Dt_Excl;
    }

    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    public String getHr_Excl() {
        return Hr_Excl;
    }

    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getISSGrpDesc() {
        return ISSGrpDesc;
    }

    public void setISSGrpDesc(String ISSGrpDesc) {
        this.ISSGrpDesc = ISSGrpDesc;
    }

    public String getMunicipio() {
        return Municipio;
    }

    public void setMunicipio(String Municipio) {
        this.Municipio = Municipio;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getCodMunic() {
        return CodMunic;
    }

    public void setCodMunic(String CodMunic) {
        this.CodMunic = CodMunic;
    }

    public String getCFOP() {
        return CFOP;
    }

    public void setCFOP(String CFOP) {
        this.CFOP = CFOP;
    }

    public String getCFOPDesc() {
        return CFOPDesc;
    }

    public void setCFOPDesc(String CFOPDesc) {
        this.CFOPDesc = CFOPDesc;
    }
}

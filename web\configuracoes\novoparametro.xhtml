<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <script src="../assets/scripts/jquery.qrcode.js" type="text/javascript"></script>
            <script src="../assets/scripts/qrcode.js" type="text/javascript"></script>
            <link type="text/css" href="../assets/css/animate.css" rel="stylesheet"/>
            <style>
                #main{
                    height: calc(100vh - 86px) !important;
                }
                
                #main{
                    color: #666;
                }
                
                .FundoPagina{
                    padding: 10px 8px 10px 8px !important;
                }
                
                .FundoPagina div{
                    padding: 3px 4px 3px 8px !important;
                }
                
                .ui-inputtext{
                    width: 100%;
                }
                
                [id*="cadastrarParametro"] label{
                    max-width: 200px;
                }
                
                @media only screen and (max-width: 701px) and (min-width: 10px) {
                    #main{
                        height: calc(100vh - 170px) !important;
                    }
                }
                
                @media only screen and (max-width: 500px) and (min-width: 10px) {
                    [id*="cadastrarParametro"] label{
                        max-width: 100% !important;
                    }
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{parametro.Persistencias(login.pp, login.satellite)}" />
                <f:viewAction action="#{parametro.novoParametro}" />
            </f:metadata>
            
            <p:growl id="msgs" />
            
            <div id="body">
                <header style="margin-top: 0px !Important">
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_clientes.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Parametro}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{acessos.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">#{parametro.filiaisCabecalho.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                        <label class="FilialEndereco">#{parametro.filiaisCabecalho.endereco}</label>
                                        <label class="FilialBairroCidade">#{parametro.filiaisCabecalho.bairro}, #{parametro.filiaisCabecalho.cidade}/#{parametro.filiaisCabecalho.UF}</label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>
                
                <h:form id="main">
                    <h:inputHidden id="txtUrlAtual" value="#{parametro.enderecoNavegador}"></h:inputHidden>
                    
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; overflow-y:auto !important; padding-right:12px !important;">
                        <div class="col-md-6 col-sm-6 col-xs-12">
                            <label>#{localemsgs.Empresa}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.nomeEmpresa}" id="txtNomeEmpresa" required="true" requiredMessage="#{localemsgs.Obrigatorio}">
                                <p:ajax event="change" listener="#{parametro.validarParametro}" update="main"></p:ajax>
                            </p:inputText>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-12">
                            <label>#{localemsgs.Filial}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" id="txtNumeroFilial" maxlength="3" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}" value="#{parametro.paramet.filial_PDR}"></p:inputText>
                            <p:watermark value="Ex.: 001" for="txtNumeroFilial"></p:watermark>
                        </div>
                        <div class="col-md-4 col-sm-4 col-xs-12">
                            <label>#{localemsgs.NomeFilial}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" disabled="#{parametro.validaParametro eq 'N'}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" value="#{parametro.paramet.nome_empr}"></p:inputText>
                        </div>
                        <div class="col-md-4 col-sm-4 col-xs-9">
                            <label>#{localemsgs.Cidade}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.cidade_PDR}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-3">
                            <label>#{localemsgs.UF}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" maxlength="2" id="txtUF" required="true" requiredMessage="#{localemsgs.Obrigatorio}" value="#{parametro.paramet.UF_PDR}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                            <p:watermark value="Ex.: DF" for="txtUF"></p:watermark>
                        </div>
                        <div class="col-md-4 col-sm-4 col-xs-12">
                            <label>#{localemsgs.TipoBD}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" disabled="true" value="#{parametro.paramet.tipoBDdescr}"></p:inputText>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-12">
                            <label>#{localemsgs.CodEmpresa}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.codEmpresa}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}" maxlength="4"></p:inputText>
                        </div>
                         <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>#{localemsgs.HostNameLocal}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.hostName}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="true"></p:inputText>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>#{localemsgs.HostNameWeb}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.hostNameWEB}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="true"></p:inputText>
                        </div>
                        
                         <div class="col-md-3 col-sm-3 col-xs-12">
                             <label>#{localemsgs.BancoDados}<font color="red">&nbsp;(*)</font></label>
                             <p:inputText class="form-control" value="#{parametro.paramet.bancoDados}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>#{localemsgs.FonteDados}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.path}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        
                        <div class="col-md-4 col-sm-4 col-xs-12">
                            <label>#{localemsgs.Usuario}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.usuario}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-12">
                            <label>#{localemsgs.Senha}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.senha}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>#{localemsgs.ArquivoLogotipo}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.pathSatelite}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>#{localemsgs.ArquivoLogotipoNfe}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.pathLogoNFE}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-6 col-sm-6 col-xs-12">
                            <label>#{localemsgs.EmailAdm}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.pathEagle}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>#{localemsgs.Fotografias}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.pathFotos}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>#{localemsgs.Documentos}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" value="#{parametro.paramet.pathDoctos}" required="true" requiredMessage="#{localemsgs.Obrigatorio}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6">
                            <label>#{localemsgs.FusoHorario}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" maxlength="2" required="true" requiredMessage="#{localemsgs.Obrigatorio}" value="#{parametro.paramet.fusoHorario}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6">
                            <label>#{localemsgs.FusoSefaz}<font color="red">&nbsp;(*)</font></label>
                            <p:inputText class="form-control" maxlength="2" required="true" requiredMessage="#{localemsgs.Obrigatorio}" value="#{parametro.paramet.fusoHorarioSEFAZ}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputText>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-12">
                            <label>#{localemsgs.Moeda}<font color="red">&nbsp;(*)</font></label>
                            <p:selectOneMenu id="tipoMoeda" 
                                             filter="false" filterMatchMode="contains" value="#{parametro.paramet.moedaPdrMobile}"
                                            required="true" disabled="#{parametro.validaParametro eq 'N'}"
                                            style="width: 100%; color: #000 !important">
                               <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                               <f:selectItem itemLabel="(EUR) #{localemsgs.MoedaEuro}" itemValue="EUR" />
                               <f:selectItem itemLabel="(USD) #{localemsgs.MoedaDolar}" itemValue="USD" />
                               <f:selectItem itemLabel="(GBP) #{localemsgs.MoedaBritaniva}" itemValue="GBP" />
                               <f:selectItem itemLabel="(CLP) #{localemsgs.MoedaPesoChileno}" itemValue="CLP" />
                               <f:selectItem itemLabel="(COP) #{localemsgs.MoedaPesoColombiano}" itemValue="COP" />
                               <f:selectItem itemLabel="(MXN) #{localemsgs.MoedaMexico}" itemValue="MXN" />
                               <f:selectItem itemLabel="(R$) #{localemsgs.MoedaReal}" itemValue="BRL" />
                           </p:selectOneMenu>
                        </div>
                        
                        <div class="col-md-6 col-sm-6 col-xs-12">
                            <label>#{localemsgs.TrocarSenhaMobile}</label>
                            <p:selectOneMenu id="trocaSenhaDiariamente" 
                                            filter="false" filterMatchMode="contains" disabled="#{parametro.validaParametro eq 'N'}"
                                            required="true" value="#{parametro.paramet.trocaSenhaMobile}"
                                            style="width: 100%; color: #000 !important">
                               <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                               <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N" />
                               <f:selectItem itemLabel=" #{localemsgs.Sim}" itemValue="S" />
                           </p:selectOneMenu>
                        </div>
                        
                        
                        
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>UtilizaGTVe</label>
                            <p:selectOneMenu id="utilizaGTVe" 
                                            filter="false" filterMatchMode="contains" disabled="#{parametro.validaParametro eq 'N'}"
                                            required="true" value="#{parametro.paramet.utilizaGTVe}"
                                            style="width: 100%; color: #000 !important">
                               <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                               <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="0" />
                               <f:selectItem itemLabel=" #{localemsgs.Sim}" itemValue="1" />
                           </p:selectOneMenu>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>TranspCacamba</label>
                            <p:selectOneMenu id="transpCacamba" 
                                            filter="false" filterMatchMode="contains" disabled="#{parametro.validaParametro eq 'N'}"
                                            required="true" value="#{parametro.paramet.transpCacamba}"
                                            style="width: 100%; color: #000 !important">
                               <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                               <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="0" />
                               <f:selectItem itemLabel=" #{localemsgs.Sim}" itemValue="1" />
                           </p:selectOneMenu>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>FirmaGTV</label>
                            <p:selectOneMenu id="firmaTtv" 
                                            filter="false" filterMatchMode="contains" disabled="#{parametro.validaParametro eq 'N'}"
                                            required="true" value="#{parametro.paramet.firmaGtv}"
                                            style="width: 100%; color: #000 !important">
                               <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                               <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="0" />
                               <f:selectItem itemLabel=" #{localemsgs.Sim}" itemValue="1" />
                           </p:selectOneMenu>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-12">
                            <label>CapturaValor</label>
                            <p:selectOneMenu id="capturaValor" 
                                            filter="false" filterMatchMode="contains" disabled="#{parametro.validaParametro eq 'N'}"
                                            required="true" value="#{parametro.paramet.capturaValor}"
                                            style="width: 100%; color: #000 !important">
                               <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                               <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="0" />
                               <f:selectItem itemLabel=" #{localemsgs.Sim}" itemValue="1" />
                           </p:selectOneMenu>
                        </div>
                        
                        
                        
                        <div class="col-md-6 col-sm-6 col-xs-12" style="padding-top: 15px !important;">
                            <div class='col-md-12 col-sm-12 col-xs-12' style='border: thin solid #0082C3; padding: 12px 8px 10px 8px !important; border-radius: 3px;'>
                                <label style="background-color: #FFF; color: #0082C3; position: absolute; top: -10px; left: 8px; padding-left: 8px !important; padding-right: 8px !important;">#{localemsgs.ToleranciaEscala}</label>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #0082C3; font-weight: 500 !important;">#{localemsgs.Motorista}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.escTolerMot}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #0082C3; font-weight: 500 !important;">#{localemsgs.ChefeEquipe}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.escTolerChe}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #0082C3; font-weight: 500 !important;">#{localemsgs.Vigilante}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.escTolerVig}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #0082C3; font-weight: 500 !important;">#{localemsgs.OutrosDesc}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.escTolerOutros}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-sm-6 col-xs-12" style="padding-top: 15px !important;">
                            <div class='col-md-12 col-sm-12 col-xs-12' style='border: thin solid #008000; padding: 12px 8px 10px 8px !important; border-radius: 3px;'>
                                <label style="background-color: #FFF; color: #008000; position: absolute; top: -10px; left: 8px; padding-left: 8px !important; padding-right: 8px !important;">#{localemsgs.ToleranciaPonto}</label>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #008000; font-weight: 500 !important;">#{localemsgs.Motorista}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.ptoTolerMot}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #008000; font-weight: 500 !important;">#{localemsgs.ChefeEquipe}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.ptoTolerChe}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #008000; font-weight: 500 !important;">#{localemsgs.Vigilante}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.ptoTolerVig}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #008000; font-weight: 500 !important;">#{localemsgs.OutrosDesc}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.ptoTolerOutros}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-sm-6 col-xs-12" style="padding-top: 15px !important;">
                            <div class='col-md-12 col-sm-12 col-xs-12' style='border: thin solid #6d08c7; padding: 12px 8px 10px 8px !important; border-radius: 3px;'>
                                <label style="background-color: #FFF; color: #6d08c7; position: absolute; top: -10px; left: 8px; padding-left: 8px !important; padding-right: 8px !important;">#{localemsgs.ToleranciaAcesso}</label>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #6d08c7; font-weight: 500 !important;">#{localemsgs.Motorista}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.aceTolerMot}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #6d08c7; font-weight: 500 !important;">#{localemsgs.ChefeEquipe}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.aceTolerChe}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #6d08c7; font-weight: 500 !important;">#{localemsgs.Vigilante}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.aceTolerVig}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-3 col-sm-3 col-xs-6'>
                                    <label style="color: #6d08c7; font-weight: 500 !important;">#{localemsgs.OutrosDesc}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="0" maxlength="2" value="#{parametro.paramet.aceTolerOutros}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                            </div>
                        </div>
                        
                        
                        <div class="col-md-6 col-sm-6 col-xs-12" style="padding-top: 15px !important;">
                            <div class='col-md-12 col-sm-12 col-xs-12' style='border: thin solid #F00; padding: 12px 8px 10px 8px !important; border-radius: 3px;'>
                                <label style="background-color: #FFF; color: #F00; position: absolute; top: -10px; left: 8px; padding-left: 8px !important; padding-right: 8px !important;">#{localemsgs.LimiteSeguro}</label>
                                <div class='col-md-4 col-sm-4 col-xs-12'>
                                    <label style="color: #F00; font-weight: 500 !important;">#{localemsgs.CaixaForte}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="2" maxlength="2" value="#{parametro.paramet.limiteCxf}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-4 col-sm-4 col-xs-12'>
                                    <label style="color: #F00; font-weight: 500 !important;">#{localemsgs.Tesouraria}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" decimalPlaces="2" maxlength="2" value="#{parametro.paramet.limiteTes}" disabled="#{parametro.validaParametro eq 'N'}"></p:inputNumber>
                                </div>
                                <div class='col-md-4 col-sm-4 col-xs-12'>
                                    <label style="color: #F00; font-weight: 500 !important;">#{localemsgs.CarroForte}&nbsp;(*)</label>
                                    <p:inputNumber style='min-width: 100% !important' required="true" requiredMessage="#{localemsgs.Obrigatorio}" value="#{parametro.paramet.limiteSeg}" disabled="#{parametro.validaParametro eq 'N'}" decimalPlaces="2"></p:inputNumber>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 15px !important; text-align: right">
                            <p:commandLink title="#{localemsgs.Salve}" id="cadastrarParametro" action="#{parametro.criarParametro}" update="main msgs"
                                           style="width:100%" rendered="#{parametro.validaParametro ne 'N'}">
                                <label class="btn btn-lg btn-success" style="width:100% !important; margin-left: 0px;"><i class="fa fa-save"></i>&nbsp;#{localemsgs.Salve}</label>
                            </p:commandLink>
                        </div>
                    </div>
                    <script>
                        
                        $(document).ready(function(){
                            $('[id*="txtUrlAtual"]').val(window.location.href);
                        })
                        .on('focus', '.ui-inputtext', function(){
                            $(this).select();
                        })
                                .on('keydown', '[id*="txtNomeEmpresa"]', function(e){
                                if(e.keyCode === 13) $(this).blur();
                        })
    ;
                        
                    </script>
                </h:form>
            </div>
            
            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
            
            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo"></h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important; margin-top:5px !important;">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Veiculos {

    private int Numero;
    private String NumeroFormatado;
    private String Placa;
    private String UF_Placa;
    private String Mun_Placa;
    private int Modelo;
    private String Categoria;
    private BigDecimal CodFil;
    private int Ano;
    private int AnoModelo;
    private String Chassis;
    private String RENAVAN;
    private String Tipo;
    private String Combust;
    private int Passageiros;
    private int Potencia;
    private int VeicTerceiros;
    private String BlindCab;
    private String BlindTeto;
    private String BlindAssoa;
    private String BlindCofre;
    private String BlindVidro;
    private String Viagem;
    private String Bacen;
    private String Aeroporto;
    private String VistoriaPF;
    private String Dt_VisPF;
    private String Dt_VenPF;
    private String Prefixo;
    private String Carroceria;
    private String Apolice;
    private String Dt_SegIni;
    private String Dt_VencSeg;
    private String Seguradora;
    private String Dt_Ipva;
    private int AnoLicen;
    private String VistoriaQualid;
    private String Dt_VisQualid;
    private String Dt_VenQualid;
    private String VistoriaConfor;
    private String Dt_VisConfor;
    private String Dt_VenConfor;
    private String VistoriaFabric;
    private String Dt_VisFabric;
    private String Dt_VenFabric;
    private String Dt_Compra;
    private String Obs;
    private BigDecimal CodCidade;
    private BigDecimal Matr_Mot;
    private String Situacao;
    private String Dt_Situac;
    private String ID_Modulo;
    private String CCusto;
    private String VistoriaQualidII;
    private String Dt_VisQualidII;
    private String Dt_VenQualidII;
    private String VistoriaConforII;
    private String Dt_VisConforII;
    private String Dt_VenConforII;
    private String KMAuditoria;
    private String DtKmAuditoria;
    private String Auditor;
    private String DtAuditoria;
    private String HrAuditoria;
    private String Alvara;
    private String Dt_Alvara;
    private String Dt_VencAlvara;
    private String Tacografo;
    private String Dt_Tacografo;
    private String Dt_VencTacografo;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String DescricaoModelo;

    public Veiculos() {
    }

    public Veiculos(Veiculos original) {
        Numero = original.getNumero();
        Placa = original.getPlaca();
        UF_Placa = original.getUF_Placa();
        Mun_Placa = original.getMun_Placa();
        Modelo = original.getModelo();
        Categoria = original.getCategoria();
        CodFil = original.getCodFil();
        Ano = original.getAno();
        AnoModelo = original.getAnoModelo();
        Chassis = original.getChassis();
        RENAVAN = original.getRENAVAN();
        Tipo = original.getTipo();
        Combust = original.getCombust();
        Passageiros = original.getPassageiros();
        Potencia = original.getPotencia();
        VeicTerceiros = original.getVeicTerceiros();
        BlindCab = original.getBlindCab();
        BlindTeto = original.getBlindTeto();
        BlindAssoa = original.getBlindAssoa();
        BlindCofre = original.getBlindCofre();
        BlindVidro = original.getBlindVidro();
        Viagem = original.getViagem();
        Bacen = original.getBacen();
        Aeroporto = original.getAeroporto();
        VistoriaPF = original.getVistoriaPF();
        Dt_VisPF = original.getDt_VisPF();
        Dt_VenPF = original.getDt_VenPF();
        Prefixo = original.getPrefixo();
        Carroceria = original.getCarroceria();
        Apolice = original.getApolice();
        Dt_SegIni = original.getDt_SegIni();
        Dt_VencSeg = original.getDt_VencSeg();
        Seguradora = original.getSeguradora();
        Dt_Ipva = original.getDt_Ipva();
        AnoLicen = original.getAnoLicen();
        VistoriaQualid = original.getVistoriaQualid();
        Dt_VisQualid = original.getDt_VisQualid();
        Dt_VenQualid = original.getDt_VenQualid();
        VistoriaConfor = original.getVistoriaConfor();
        Dt_VisConfor = original.getDt_VisConfor();
        Dt_VenConfor = original.getDt_VenConfor();
        VistoriaFabric = original.getVistoriaFabric();
        Dt_VisFabric = original.getDt_VisFabric();
        Dt_VenFabric = original.getDt_VenFabric();
        Dt_Compra = original.getDt_Compra();
        Obs = original.getObs();
        CodCidade = original.getCodCidade();
        Matr_Mot = original.getMatr_Mot();
        Situacao = original.getSituacao();
        Dt_Situac = original.getDt_Situac();
        ID_Modulo = original.getID_Modulo();
        CCusto = original.getCCusto();
        VistoriaQualidII = original.getVistoriaQualidII();
        Dt_VisQualidII = original.getDt_VisQualidII();
        Dt_VenQualidII = original.getDt_VenQualidII();
        VistoriaConforII = original.getVistoriaConforII();
        Dt_VisConforII = original.getDt_VisConforII();
        Dt_VenConforII = original.getDt_VenConforII();
        Operador = original.getOperador();
        Dt_Alter = original.getDt_Alter();
        Hr_Alter = original.getHr_Alter();
        DescricaoModelo = original.getDescricaoModelo();
    }

    public int getNumero() {
        return Numero;
    }

    public void setNumero(int Numero) {
        this.Numero = Numero;
    }

    public String getPlaca() {
        return Placa;
    }

    public void setPlaca(String Placa) {
        this.Placa = Placa;
    }

    public String getUF_Placa() {
        return UF_Placa;
    }

    public void setUF_Placa(String UF_Placa) {
        this.UF_Placa = UF_Placa;
    }

    public String getMun_Placa() {
        return Mun_Placa;
    }

    public void setMun_Placa(String Mun_Placa) {
        this.Mun_Placa = Mun_Placa;
    }

    public int getModelo() {
        return Modelo;
    }

    public void setModelo(int Modelo) {
        this.Modelo = Modelo;
    }

    public String getCategoria() {
        return Categoria;
    }

    public void setCategoria(String Categoria) {
        this.Categoria = Categoria;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("1");
        }
    }

    public int getAno() {
        return Ano;
    }

    public void setAno(int Ano) {
        this.Ano = Ano;
    }

    public void setAno(String Ano) {
        try {
            this.Ano = Integer.getInteger(Ano);
        } catch (Exception ex) {
            this.Ano = 0;
        }
    }

    public int getAnoModelo() {
        return AnoModelo;
    }

    public void setAnoModelo(int AnoModelo) {
        this.AnoModelo = AnoModelo;
    }

    public void setAnoModelo(String AnoModelo) {
        try {
            this.AnoModelo = Integer.getInteger(AnoModelo);
        } catch (Exception ex) {
            this.AnoModelo = 0;
        }
    }

    public String getChassis() {
        return Chassis;
    }

    public void setChassis(String Chassis) {
        this.Chassis = Chassis;
    }

    public String getRENAVAN() {
        return RENAVAN;
    }

    public void setRENAVAN(String RENAVAN) {
        this.RENAVAN = RENAVAN;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getCombust() {
        return Combust;
    }

    public void setCombust(String Combust) {
        this.Combust = Combust;
    }

    public int getPassageiros() {
        return Passageiros;
    }

    public void setPassageiros(int Passageiros) {
        this.Passageiros = Passageiros;
    }

    public int getPotencia() {
        return Potencia;
    }

    public void setPotencia(int Potencia) {
        this.Potencia = Potencia;
    }

    public int getVeicTerceiros() {
        return VeicTerceiros;
    }

    public void setVeicTerceiros(int VeicTerceiros) {
        this.VeicTerceiros = VeicTerceiros;
    }

    public String getBlindCab() {
        return BlindCab;
    }

    public void setBlindCab(String BlindCab) {
        this.BlindCab = BlindCab;
    }

    public String getBlindTeto() {
        return BlindTeto;
    }

    public void setBlindTeto(String BlindTeto) {
        this.BlindTeto = BlindTeto;
    }

    public String getBlindAssoa() {
        return BlindAssoa;
    }

    public void setBlindAssoa(String BlindAssoa) {
        this.BlindAssoa = BlindAssoa;
    }

    public String getBlindCofre() {
        return BlindCofre;
    }

    public void setBlindCofre(String BlindCofre) {
        this.BlindCofre = BlindCofre;
    }

    public String getBlindVidro() {
        return BlindVidro;
    }

    public void setBlindVidro(String BlindVidro) {
        this.BlindVidro = BlindVidro;
    }

    public String getViagem() {
        return Viagem;
    }

    public void setViagem(String Viagem) {
        this.Viagem = Viagem;
    }

    public String getBacen() {
        return Bacen;
    }

    public void setBacen(String Bacen) {
        this.Bacen = Bacen;
    }

    public String getAeroporto() {
        return Aeroporto;
    }

    public void setAeroporto(String Aeroporto) {
        this.Aeroporto = Aeroporto;
    }

    public String getVistoriaPF() {
        return VistoriaPF;
    }

    public void setVistoriaPF(String VistoriaPF) {
        this.VistoriaPF = VistoriaPF;
    }

    public String getDt_VisPF() {
        return Dt_VisPF;
    }

    public void setDt_VisPF(String Dt_VisPF) {
        this.Dt_VisPF = Dt_VisPF;
    }

    public String getDt_VenPF() {
        return Dt_VenPF;
    }

    public void setDt_VenPF(String Dt_VenPF) {
        this.Dt_VenPF = Dt_VenPF;
    }

    public String getPrefixo() {
        return Prefixo;
    }

    public void setPrefixo(String Prefixo) {
        this.Prefixo = Prefixo;
    }

    public String getCarroceria() {
        return Carroceria;
    }

    public void setCarroceria(String Carroceria) {
        this.Carroceria = Carroceria;
    }

    public String getApolice() {
        return Apolice;
    }

    public void setApolice(String Apolice) {
        this.Apolice = Apolice;
    }

    public String getDt_SegIni() {
        return Dt_SegIni;
    }

    public void setDt_SegIni(String Dt_SegIni) {
        this.Dt_SegIni = Dt_SegIni;
    }

    public String getDt_VencSeg() {
        return Dt_VencSeg;
    }

    public void setDt_VencSeg(String Dt_VencSeg) {
        this.Dt_VencSeg = Dt_VencSeg;
    }

    public String getSeguradora() {
        return Seguradora;
    }

    public void setSeguradora(String Seguradora) {
        this.Seguradora = Seguradora;
    }

    public String getDt_Ipva() {
        return Dt_Ipva;
    }

    public void setDt_Ipva(String Dt_Ipva) {
        this.Dt_Ipva = Dt_Ipva;
    }

    public int getAnoLicen() {
        return AnoLicen;
    }

    public void setAnoLicen(int AnoLicen) {
        this.AnoLicen = AnoLicen;
    }

    public String getVistoriaQualid() {
        return VistoriaQualid;
    }

    public void setVistoriaQualid(String VistoriaQualid) {
        this.VistoriaQualid = VistoriaQualid;
    }

    public String getDt_VisQualid() {
        return Dt_VisQualid;
    }

    public void setDt_VisQualid(String Dt_VisQualid) {
        this.Dt_VisQualid = Dt_VisQualid;
    }

    public String getDt_VenQualid() {
        return Dt_VenQualid;
    }

    public void setDt_VenQualid(String Dt_VenQualid) {
        this.Dt_VenQualid = Dt_VenQualid;
    }

    public String getVistoriaConfor() {
        return VistoriaConfor;
    }

    public void setVistoriaConfor(String VistoriaConfor) {
        this.VistoriaConfor = VistoriaConfor;
    }

    public String getDt_VisConfor() {
        return Dt_VisConfor;
    }

    public void setDt_VisConfor(String Dt_VisConfor) {
        this.Dt_VisConfor = Dt_VisConfor;
    }

    public String getDt_VenConfor() {
        return Dt_VenConfor;
    }

    public void setDt_VenConfor(String Dt_VenConfor) {
        this.Dt_VenConfor = Dt_VenConfor;
    }

    public String getVistoriaFabric() {
        return VistoriaFabric;
    }

    public void setVistoriaFabric(String VistoriaFabric) {
        this.VistoriaFabric = VistoriaFabric;
    }

    public String getDt_VisFabric() {
        return Dt_VisFabric;
    }

    public void setDt_VisFabric(String Dt_VisFabric) {
        this.Dt_VisFabric = Dt_VisFabric;
    }

    public String getDt_VenFabric() {
        return Dt_VenFabric;
    }

    public void setDt_VenFabric(String Dt_VenFabric) {
        this.Dt_VenFabric = Dt_VenFabric;
    }

    public String getDt_Compra() {
        return Dt_Compra;
    }

    public void setDt_Compra(String Dt_Compra) {
        this.Dt_Compra = Dt_Compra;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public BigDecimal getCodCidade() {
        return CodCidade;
    }

    public void setCodCidade(String CodCidade) {
        try {
            this.CodCidade = new BigDecimal(CodCidade);
        } catch (Exception e) {
            this.CodCidade = new BigDecimal("0");
        }
    }

    public BigDecimal getMatr_Mot() {
        return Matr_Mot;
    }

    public void setMatr_Mot(String Matr_Mot) {
        try {
            this.Matr_Mot = new BigDecimal(Matr_Mot);
        } catch (Exception e) {
            this.Matr_Mot = new BigDecimal("0");
        }
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getDt_Situac() {
        return Dt_Situac;
    }

    public void setDt_Situac(String Dt_Situac) {
        this.Dt_Situac = Dt_Situac;
    }

    public String getID_Modulo() {
        return ID_Modulo;
    }

    public void setID_Modulo(String ID_Modulo) {
        this.ID_Modulo = ID_Modulo;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public String getVistoriaQualidII() {
        return VistoriaQualidII;
    }

    public void setVistoriaQualidII(String VistoriaQualidII) {
        this.VistoriaQualidII = VistoriaQualidII;
    }

    public String getDt_VisQualidII() {
        return Dt_VisQualidII;
    }

    public void setDt_VisQualidII(String Dt_VisQualidII) {
        this.Dt_VisQualidII = Dt_VisQualidII;
    }

    public String getDt_VenQualidII() {
        return Dt_VenQualidII;
    }

    public void setDt_VenQualidII(String Dt_VenQualidII) {
        this.Dt_VenQualidII = Dt_VenQualidII;
    }

    public String getVistoriaConforII() {
        return VistoriaConforII;
    }

    public void setVistoriaConforII(String VistoriaConforII) {
        this.VistoriaConforII = VistoriaConforII;
    }

    public String getDt_VisConforII() {
        return Dt_VisConforII;
    }

    public void setDt_VisConforII(String Dt_VisConforII) {
        this.Dt_VisConforII = Dt_VisConforII;
    }

    public String getDt_VenConforII() {
        return Dt_VenConforII;
    }

    public void setDt_VenConforII(String Dt_VenConforII) {
        this.Dt_VenConforII = Dt_VenConforII;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getDescricaoModelo() {
        return DescricaoModelo;
    }

    public void setDescricaoModelo(String DescricaoModelo) {
        this.DescricaoModelo = DescricaoModelo;
    }

    public String getKMAuditoria() {
        return KMAuditoria;
    }

    public void setKMAuditoria(String KMAuditoria) {
        this.KMAuditoria = KMAuditoria;
    }

    public String getDtKmAuditoria() {
        return DtKmAuditoria;
    }

    public void setDtKmAuditoria(String DtKmAuditoria) {
        this.DtKmAuditoria = DtKmAuditoria;
    }

    public String getAuditor() {
        return Auditor;
    }

    public void setAuditor(String Auditor) {
        this.Auditor = Auditor;
    }

    public String getDtAuditoria() {
        return DtAuditoria;
    }

    public void setDtAuditoria(String DtAuditoria) {
        this.DtAuditoria = DtAuditoria;
    }

    public String getHrAuditoria() {
        return HrAuditoria;
    }

    public void setHrAuditoria(String HrAuditoria) {
        this.HrAuditoria = HrAuditoria;
    }

    public String getAlvara() {
        return Alvara;
    }

    public void setAlvara(String Alvara) {
        this.Alvara = Alvara;
    }

    public String getDt_Alvara() {
        return Dt_Alvara;
    }

    public void setDt_Alvara(String Dt_Alvara) {
        this.Dt_Alvara = Dt_Alvara;
    }

    public String getDt_VencAlvara() {
        return Dt_VencAlvara;
    }

    public void setDt_VencAlvara(String Dt_VencAlvara) {
        this.Dt_VencAlvara = Dt_VencAlvara;
    }

    public String getTacografo() {
        return Tacografo;
    }

    public void setTacografo(String Tacografo) {
        this.Tacografo = Tacografo;
    }

    public String getDt_Tacografo() {
        return Dt_Tacografo;
    }

    public void setDt_Tacografo(String Dt_Tacografo) {
        this.Dt_Tacografo = Dt_Tacografo;
    }

    public String getDt_VencTacografo() {
        return Dt_VencTacografo;
    }

    public void setDt_VencTacografo(String Dt_VencTacografo) {
        this.Dt_VencTacografo = Dt_VencTacografo;
    }

    public String getNumeroFormatado() {
        return NumeroFormatado;
    }

    public void setNumeroFormatado(String NumeroFormatado) {
        this.NumeroFormatado = NumeroFormatado;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 89 * hash + this.Numero;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Veiculos other = (Veiculos) obj;
        if (this.Numero != other.Numero) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return Placa != null && !Placa.equals("") ? (Placa + (Obs != null && !Obs.equals("") ? " - " + Obs : "")) : "";
    }
}

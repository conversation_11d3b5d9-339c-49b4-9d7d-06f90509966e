/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

/**
 *
 * <AUTHOR>
 */
public class CofreDashBoardStatus {

    private String CodCofre;
    private String NRed;
    private String DataMov;
    private String HoraMov;
    private String ValorMov;
    private String TipoDepositoMov;
    private String Saldo;
    private String Situacao;
    private String DataSituacao;
    private String Horasituacao;
    private String Latitudesituacao;
    private String Longitudeituacao;
    private String IMEI;
    private String Bateria;
    private String Versao;
    private String VersaoAtual;

    public String getCodCofre() {
        return CodCofre;
    }

    public void setCodCofre(String CodCofre) {
        this.CodCofre = CodCofre;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getDataMov() {
        return DataMov;
    }

    public void setDataMov(String DataMov) {
        this.DataMov = DataMov;
    }

    public String getHoraMov() {
        return HoraMov;
    }

    public void setHoraMov(String HoraMov) {
        this.HoraMov = HoraMov;
    }

    public String getValorMov() {
        return ValorMov;
    }

    public void setValorMov(String ValorMov) {
        this.ValorMov = ValorMov;
    }

    public String getTipoDepositoMov() {
        return TipoDepositoMov;
    }

    public void setTipoDepositoMov(String TipoDepositoMov) {
        this.TipoDepositoMov = TipoDepositoMov;
    }

    public String getSaldo() {
        return Saldo;
    }

    public void setSaldo(String Saldo) {
        this.Saldo = Saldo;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getDataSituacao() {
        return DataSituacao;
    }

    public void setDataSituacao(String DataSituacao) {
        this.DataSituacao = DataSituacao;
    }

    public String getHorasituacao() {
        return Horasituacao;
    }

    public void setHorasituacao(String Horasituacao) {
        this.Horasituacao = Horasituacao;
    }

    public String getLatitudesituacao() {
        return Latitudesituacao;
    }

    public void setLatitudesituacao(String Latitudesituacao) {
        this.Latitudesituacao = Latitudesituacao;
    }

    public String getLongitudeituacao() {
        return Longitudeituacao;
    }

    public void setLongitudeituacao(String Longitudeituacao) {
        this.Longitudeituacao = Longitudeituacao;
    }

    public String getIMEI() {
        return IMEI;
    }

    public void setIMEI(String IMEI) {
        this.IMEI = IMEI;
    }

    public String getBateria() {
        return Bateria;
    }

    public void setBateria(String Bateria) {
        this.Bateria = Bateria;
    }

    public String getVersao() {
        return Versao;
    }

    public void setVersao(String Versao) {
        this.Versao = Versao;
    }

    public String getVersaoAtual() {
        return VersaoAtual;
    }

    public void setVersaoAtual(String VersaoAtual) {
        this.VersaoAtual = VersaoAtual;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.DashBoard;

import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeansCompostas.DashboardCharts;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.sasdaos.compostas.DashboardSecSaudeDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SecSaude {

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaudeSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<DashboardCharts> obterImoveisSituacaoAgp(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.imoveisSituacaoAgp(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }

    public List<DashboardCharts> obterImoveisSituacaoDia(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.imoveisSituacaoDia(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }

    public List<DashboardCharts> obterImoveisDiaTrabalho(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.imoveisDiaTrabalho(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }

    public List<DashboardCharts> obterInspecaoTratamentoAgp(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.inspecaoTratamentoAgp(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }

    public List<DashboardCharts> obterDepositosTipoAgp(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.depositosTipoAgp(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }
    
    public List<DashboardCharts> obterimoveisInspecaoTratamentoTipo(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.imoveisInspecaoTratamentoTipo(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }    
    
    public List<DashboardCharts> obternumeroDepositosEvolucao(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.numeroDepositosEvolucao(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }        
    
    public List<DashboardCharts> obterdepositosAcaoAgp(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.depositosAcaoAgp(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }        

    public List<DashboardCharts> obterevolucaoDepositosTipo(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.evolucaoDepositosTipo(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }            
    
    public List<DashboardCharts> obteramostrasRoedoresAmostras(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.amostrasRoedoresAmostras(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }          
    
    public List<DashboardCharts> obterinspecionadosxdengue(String ano, String mes, String codfil, Persistencia persistencia) throws Exception {
        try {
            DashboardSecSaudeDao dashboardDao = new DashboardSecSaudeDao();
            return dashboardDao.inspecionadosxdengue(ano, mes, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("SecSaude.falhageral<message>" + e.getMessage());
        }
    }        
    
}

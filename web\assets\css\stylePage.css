/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 30/09/2019, 16:17:08
    Author     : <PERSON><PERSON><PERSON>
*/

/*
    positionType="absolute" responsive="true" draggable="false" 
    modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop" 
    closeOnEscape="false" class="dialogoGrande"
*/
.dialogoPequeno{
    height: auto; 
    max-height:95% !important; 
    max-width: 350px !important; 
    border:thin solid #666 !important; 
    box-shadow:0px 0px 5px #303030 !important;
    overflow: hidden !important;
    overflow-y: auto !important; 
    padding-bottom:20px !important;
    border-top:4px solid #3C8DBC !important;
    border-radius:8px !important;
    font-family:'Open Sans', sans-serif !important;
    padding:0px 0px 20px 0px !important;
    background-color:#EEE !important;
}
.dialogoGrande{
    height: auto;
    max-height:95% !important; 
    max-width:95% !important; 
    border:thin solid #666 !important; 
    box-shadow:0px 0px 5px #303030 !important; 
    overflow: hidden !important; 
    overflow-y: auto !important; 
    padding-bottom:20px !important;
    border-top:4px solid #3C8DBC !important; 
    border-radius:8px !important; 
    font-family:'Open Sans', sans-serif !important; 
    padding:0px 0px 20px 0px !important; 
    background-color:#EEE !important;
}
.dialogoPagina{
    height: auto;
    width:95vw !important; 
    max-height:95% !important; 
    max-width:95% !important; 
    border:thin solid #666 !important; 
    box-shadow:0px 0px 5px #303030 !important; 
    overflow: hidden !important; 
    overflow-y: auto !important; 
    padding-bottom:20px !important;
    border-top:4px solid #3C8DBC !important; 
    border-radius:8px !important; 
    font-family:'Open Sans', sans-serif !important; 
    padding:0px 0px 20px 0px !important; 
    background-color:#EEE !important;
}

#formPesquisaRapida .ui-radiobutton {
    background: transparent !important;
}

body{
    background-color:#ecf0f5;
}

.FundoPagina{
    height: 100% !important;
    background-color: #FFF;
    border: thin solid #CCC;
    padding:10px 0px 10px 15px !important;
    border-radius: 4px !important;
    border-top:4px solid #3C8DBC !important;
    overflow: hidden !important;
}

.ui-selectonemenu-label{
    border:none !important;
    box-shadow:none !important;
}

#main{
    padding:16px 14px 14px 14px !important; 
    overflow:hidden !important; 
    height: calc(100% - 0px)
}

#divTopoTela{
    line-height: 47px !important;
    align-self: center;
    padding-top: 0px !important;
    padding-left: 4px !important;
}

#divTopoTela img{
    margin-right:10px !important;
}

header{
    position:relative !important; 
    box-shadow: -1px 2px 6px #AAA;
    min-height: 10px !important;
    height: 45px !important;
}

footer{
    min-height: 10px !important;
    height: 10px !important;
    max-height: 10px !important;
    line-height:10px !important;
    bottom:0px !important;
    margin-bottom:0px !important;
    padding:0px !important;
    background-color: red !important;
}

#footer-toggle i{
    padding-top:12px !important;
}

.TextoRodape{
    height: 10px !important;
    margin:0px !important;
    padding:0px !important;
}

.calendario2 input{
    width: 100% !important;
    text-align: center !important;
    display:inline-block !important;
}

.calendario input{
    width:150px !important;
    text-align: center !important;
    padding-right:22px !important;
    display:inline-block !important;
}

.calendario{
    font-size:10pt !important;
    top:-2px !important;
}

.TituloPagina{
    font-size:13pt !important;
    font-weight:bold !important;
    line-height: 25px !important;
    display:block;
    position:absolute;
    top:3px; 
    left:40px;
    font-family:'Open Sans', sans-serif !important;
    color:#022B4A !important;
    margin-left:10px !important;
}

.TituloDataHora{
    font-size:8pt !important;
    font-weight:600 !important;
    line-height: 10px !important;
    display:block;
    position:absolute;
    top:28px; 
    left:40px;
    font-family:'Open Sans', sans-serif !important;
    color:#404040 !important;
    margin-left:10px !important;
}

.FilialNome{
    display:block;
    font-size:12pt !important;
    font-weight:bold !important;
    line-height: 12px !important;
    font-family:'Open Sans', sans-serif !important;
    color:#022B4A !important;
    width: 100% !important;
    margin-top:5px !important;
    white-space:nowrap;
    overflow:hidden !important;
    text-overflow:ellipsis;
    padding-top:2px !important;
}

.FilialEndereco,
.FilialBairroCidade{
    display:block;
    font-size:8pt !important;
    font-weight:500 !important;
    line-height: 8px !important;
    font-family:'Open Sans', sans-serif !important;
    color:#404040 !important;
    width: 100% !important;
    max-width: 100% !important;
    white-space:nowrap !important;
    overflow:hidden !important;
    text-overflow: ellipsis !important;
    padding-top:1px !important;
}

#btTrocarFilial{
    background-color:orangered;
    color:#FFF;
    font-weight: bold;
    padding:2px 9px 3px 9px !important;
    font-size: 8pt !important;
    border-radius: 4px !important;
    margin-left: 16px;
    margin-top:-4px;
    position: absolute;
    cursor:pointer;
    white-space:nowrap !important;
    box-shadow:2px 2px 3px #BBB;
}

#divCalendario{
    padding-top:10px !important;
    text-align: right !important
}

#divCalendario img{
    height: 20px;
}

::-webkit-scrollbar {
    width: 8px !important;;
    height: 15px !important;;
    font-weight: 500 !important;
}

::-webkit-scrollbar-track-piece {
    background: #ccc;
    border-radius: 20px;
}

::-webkit-scrollbar-thumb:horizontal {
    width: 10%;
    background: #999;
    border-radius: 20px;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:vertical {
    height: 10px !important;
    background: #999;
    border-radius: 25px;
}

.ui-icon-calendar {
    margin-left:-8px !important;
}

#divBotaoVoltar{
    position:absolute !important;
    width:30px !important;
    top:5px !important;
    right:16px !important;
    padding:0px 10px 0px 0px !important; 
    text-align: right !important
}

#divBotaoVoltar2{
    position:absolute !important;
    top:5px !important;
    right:16px !important;
    padding:0px 10px 0px 0px !important; 
    text-align: right !important
}

.ui-state-highlight td{
    background-color: #88bbea !important;
}

.ui-datatable-scrollable-header-box table, .ui-datatable-scrollable-footer-box table, .ui-datatable-scrollable-body table {
    table-layout: auto !important; 
}

.DataGrid tbody tr:nth-child(even){
    background-color:#f3f6f7;
}

.DataGrid tbody tr 
{
    color:#2a6389;
}

.DataGrid tbody tr:not(.ui-state-highlight):not(.ui-expanded-row-content):hover td{
    background-color:#c7ddf1 !important;
    color:#104e78 !important;
} 

.tabela tbody tr:nth-child(even){
    background-color:#f3f6f7;
}

.tabela tbody tr 
{
    color:#2a6389;
}

.tabela tbody tr:not(.ui-state-highlight):not(.ui-expanded-row-content):hover td{
    background-color:#c7ddf1 !important;
    color:#104e78;
} 

.ui-datatable-scrollable{
    overflow:hidden !important;
    height:100% !important;
}

.tabela_paginator_top,
.tabela_paginator_top div{
    border:none !important
}  

@media only screen and (max-width: 1200px) and (min-width: 10px) {
    .BotaoResumoQuadro{
        margin-top:90px !important;   
    }

    #divFooterTimer{
        display:none !important;
    } 

    .calendario input{
        width:110px !important;
    }

    #divCalendario{
        padding-right:80px !important;
    }
}

@media only screen and (max-width: 640px) and (min-width: 10px) {
    #divTopoTela{
        margin-bottom:0px !important;
        border-bottom:thin solid #CCC !important;
    }        

    #btTrocarFilial {
        top:60px !important;
        width:100px !important;
        bottom: 20px !important;
        left: 0 !important;
        right:0 !important;
        margin:auto !important;
        height:18px !important;
        padding-top:3px !important;
    }

    #divCalendario{
        margin-top:13px !important; 
        padding-right:10px !important;
    }   
}

#divDadosFilial{
    text-align: center !important;
}

#main{
    width: 100% !important
}

@media only screen and (max-width: 700px) and (min-width: 10px) {
    /*@media only screen and (max-width: 35em) and (min-width: 10px) {*/
    #main{
	position: absolute;
    top: 130px;
    height: calc(100% - 105px);
    width: 100% !important
	}
    
    #divBotaoVoltar{
        top:0px !important;
    }

    #divDadosFilial{
        height:80px!important;
        padding-bottom:30px !important;
    }

    .ui-paginator-last,
    .ui-paginator-first{
        display:none !important;
    }

    .ui-paginator-current{
        font-size: 8pt !important;
        margin-right:0px !important;
        margin-left:12px !important;
        padding-left:0px !important;
        padding-right:0px !important;
    }  

    .ui-paginator-top{
        display:inline !important;
        white-space:nowrap !important;
    }

    .ui-paginator-next,
    .ui-paginator-prev{
        margin:0px !important;
    }

    .ui-paginator-next{
        left:-10px !important;
    }

    .ui-paginator-last,
    .ui-paginator-first{
        display:none !important;
    }

    .ui-datatable-scrollable-body{
        border-top:thin solid #CCC !important;
    }

    .DataGrid tbody tr td{
        font-size:9pt !important;
        padding: 2px 6px 2px 6px !important;
    }

    .DataGrid tbody tr td[role="gridcell"]:not(:first-child){
        border-top:thin dashed #DDD !important; 
    }

    .DataGrid tbody tr:nth-child(even) td[role="gridcell"]:not(:first-child){
        border-top:thin dashed #CCC !important; 
    }

    .DataGrid tbody tr td[role="gridcell"]:nth-child(2){
        font-weight:bold;
    }

    .DataGrid tbody tr td[role="gridcell"]:first-child{
        border-top: thin solid #dce6ef !important;
    }

    .DataGrid tbody tr td[role="gridcell"]:last-child{
        border-bottom: thin solid #dce6ef !important;
    }

    .DataGrid tbody tr 
    {
        color:#505050 !important;
    }


    .tabela tbody tr td{
        font-size:9pt !important;
        padding: 2px 6px 2px 6px !important;
    }

    .tabela tbody tr td[role="gridcell"]:not(:first-child){
        border-top:thin dashed #DDD !important; 
    }

    .tabela tbody tr:nth-child(even) td[role="gridcell"]:not(:first-child){
        border-top:thin dashed #CCC !important; 
    }

    .tabela tbody tr td[role="gridcell"]:nth-child(2){
        font-weight:bold;
    }

    .tabela tbody tr td[role="gridcell"]:first-child{
        border-top: thin solid #dce6ef !important;
    }

    .tabela tbody tr td[role="gridcell"]:last-child{
        border-bottom: thin solid #dce6ef !important;
    }

    .tabela tbody tr 
    {
        color:#505050;
    }
    .ui-tabs-header{
        float:left;
        max-width:58px !important;
        padding-left:0px !important;
        padding-bottom:2px !important;
    }

    .ui-tabs-header a{
        font-size:10px !important;
        margin:0px !important;
        float:left;
        max-width:50px !important;
        white-space: pre-wrap !important;
        height: 40px !important;
        text-align:center !important;
    }

    .ui-tabs-header[data-index="3"] a,
    .ui-tabs-header[data-index="4"] a{
        padding-top:15px !important;
    }
    
    /*#main{
        margin-top: 83px !important;
    }*/
    
    #divCorporativo{
        left: 9px !important;
        white-space: nowrap;
        top: 4px !important;
        min-height: 30px !important;
        z-index: 100 !important;
        white-space: nowrap !important;
    }

    .footer-user{
        white-space: nowrap;
        margin-top: 7px !important;
        margin-left: -10px !important;
    }
    
    #corporativo div:nth-child(3){
        display: none;
    }
    
    #corporativo{
        max-width: initial !important;
    }
}

@media only screen and (max-width: 3000px) and (min-width: 641px) {
    .DataGrid{
        width:100% !important;
        border:none !important
    }

    .DataGrid thead tr th,
    .DataGrid thead tr td{
        background: linear-gradient(to bottom, #597d98, #4b708d) !important;
        min-height:46px !important;
        color: #FFF !important; 
        border:thin solid #7397b1 !important;
        font-size: 9pt !important;
    }

    .DataGrid tbody tr td{
        border:thin solid #CCC !important;
        font-size:9pt !important;
        text-transform: uppercase !important;
        padding: 6px !important;
        border-left: 0px solid !important;
        border-right: 0px solid !important;
        border-bottom: thin solid #c7ddf1 !important;
    }
    
    .tabela{
        width:100% !important;
        border:none !important
    }

    .tabela thead tr th,
    .tabela thead tr td{
        background: linear-gradient(to bottom, #597d98, #4b708d) !important;
        min-height:46px !important;
        color: #FFF !important;
        border:thin solid #7397b1 !important;
        font-size: 9pt !important;
    }

    .tabela tbody tr td{
        border:thin solid #CCC !important;
        font-size:9pt !important;
        text-transform: uppercase !important;
        padding: 6px !important;
        border-left: 0px solid !important;
        border-right: 0px solid !important;
        border-bottom: thin solid #c7ddf1 !important;
    }
}

.Rotulo{
    font-size:10pt !important;
    font-weight: bold;
    color:#000; 
    text-shadow:1px 1px 1px #FFF;
    text-align:left;
    width:100%;
    margin:4px 0px 4px 0px !important;
}

.form-control{
    width:100% !important;
}

a { 
    -webkit-transition: color 0.1s linear;
    -moz-transition: color 0.1s linear;
    -o-transition: color 0.1s linear;
    -ms-transition: color 0.1s linear;
    transition: color 0.1s linear;
    cursor: pointer;
    color: #333;
}

a:hover, a:focus {
    text-decoration: none;
    color: #115;
}

.LegendaRota{
    width:100% !important;
    padding:4px 6px 6px 6px !important;
    border:thin solid #CCC;
    border-radius:4px;
    margin-top:3px !important;
    background-color: #F0F0F0 !important;
    box-shadow:2px 2px 4px #DDD;
}

.LegendaRota table{
    width:100% !important;
    padding:0px !important;
}

.LegendaRota table tr td{
    min-width:50% !important;
    width:50% !important;
    max-width:50% !important;
    color:#666 !important;
    font-size:8pt !important;
}

.LegendaRota table tr:not(:last-child) td{
    border-bottom: thin dashed #CCC
}

.LegendaRota table tr td:first-child{
    text-align: right !important;
    padding-right:4px !important;
    font-weight: bold !important;
}

.LegendaRota table tr td:nth-child(2){
    text-align: left !important;
    padding-left:4px !important;
}

#legenda1{
    margin-top:5px !important;
}


.ui-state-error:not(label), .ui-widget-content:not(label) .ui-state-error:not(label), .ui-widget-header:not(label) .ui-state-error:not(label) {
    background-color:snow !important;
    border-color:#E74C3C !important;
}

.form-control-required{
    border-color: red !important;
}

.jconfirm-bg{
    background-color:rgba(0,0,0,1) !important;
}

/*.jconfirm-type-blue .jconfirm-title{
    display:none !important;
}*/

[id*="cabecalho"]{
    background-color: #FFF !important;
}

.iframeCompleto{
    width: 100% !important;
    border: none !important;
    padding: 0px !important;
    height: 100% !important;
    box-shadow: none !important;
    outline: none !important;
    overflow: hidden !important;
}
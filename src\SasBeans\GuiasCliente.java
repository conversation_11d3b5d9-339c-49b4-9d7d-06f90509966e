package SasBeans;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GuiasCliente {

    private String data;
    private String dia;
    private String horaChegada;
    private String horaSaida;
    private String tempo;
    private String guia;
    private String serie;
    private String operacao;
    private String nred;
    private String valor;
    private String volumes;
    private String assinado;
    private String codfil;
    private String origem, destino;
    private String codorigem, coddst, codfat;
    private String OS;
    private String observ;
    private BigDecimal veiculo;
    private String hora1;
    private String codPessoaAut;
    private String rota;

    private String sequencia;
    private String parada;

    private List<CxFGuiasVol> volumesInformacao;

    public GuiasCliente() {
        data = "";
        dia = "";
        horaChegada = "";
        horaSaida = "";
        tempo = "";
        guia = "";
        serie = "";
        operacao = "";
        nred = "";
        valor = "";
        volumes = "";
        assinado = "";
        observ = "";
        volumesInformacao = new ArrayList<>();
        origem = "";
        destino = "";
        veiculo = BigDecimal.ZERO;
        hora1 = "";
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getTempo() {
        return tempo;
    }

    public void setTempo(String tempo) {
        this.tempo = tempo;
    }

    public String getGuia() {
        return guia;
    }

    public void setGuia(String guia) {
        this.guia = guia;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getNred() {
        return nred;
    }

    public void setNred(String nred) {
        this.nred = nred;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getVolumes() {
        return volumes;
    }

    public void setVolumes(String volumes) {
        this.volumes = volumes;
    }

    public String getAssinado() {
        return assinado;
    }

    public void setAssinado(String assinado) {
        this.assinado = assinado;
    }

    public List<CxFGuiasVol> getVolumesInformacao() {
        return volumesInformacao;
    }

    public void setVolumesInformacao(List<CxFGuiasVol> volumesInformacao) {
        this.volumesInformacao = volumesInformacao;
    }

    public String getHoraChegada() {
        return horaChegada;
    }

    public void setHoraChegada(String horaChegada) {
        this.horaChegada = horaChegada;
    }

    public String getHoraSaida() {
        return horaSaida;
    }

    public void setHoraSaida(String horaSaida) {
        this.horaSaida = horaSaida;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getDestino() {
        return destino;
    }

    public void setDestino(String destino) {
        this.destino = destino;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getCodorigem() {
        return codorigem;
    }

    public void setCodorigem(String codorigem) {
        this.codorigem = codorigem;
    }

    public String getCoddst() {
        return coddst;
    }

    public void setCoddst(String coddst) {
        this.coddst = coddst;
    }

    public String getCodfat() {
        return codfat;
    }

    public void setCodfat(String codfat) {
        this.codfat = codfat;
    }

    public String getObserv() {
        return observ;
    }

    public void setObserv(String observ) {
        this.observ = observ;
    }

    public BigDecimal getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(BigDecimal veiculo) {
        this.veiculo = veiculo;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = hora1;
    }

    public String getCodPessoaAut() {
        return codPessoaAut;
    }

    public void setCodPessoaAut(String codPessoaAut) {
        this.codPessoaAut = codPessoaAut;
    }

    public String getRota() {
        return rota;
    }

    public void setRota(String rota) {
        this.rota = rota;
    }

    public String getSequencia() {
        return sequencia;
    }

    public void setSequencia(String sequencia) {
        this.sequencia = sequencia;
    }

    public String getParada() {
        return parada;
    }

    public void setParada(String parada) {
        this.parada = parada;
    }
}

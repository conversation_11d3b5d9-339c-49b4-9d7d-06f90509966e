package Dados;

import Dados.OLD.Persistencia_OLD;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class Consulta {

    private boolean msql = false;
    private PreparedStatement Mstat, mstatCreate, mstatInsert, mstatUpdate, mstatSelect, mstatDelete;
    private ResultSet Rs;
    private String Sql,
            sqlStatCreate = " INSERT INTO StatWeb (Data, Hora, Consulta, Inclusao, Alteracao, Exclusao) VALUES ('" + DataAtual.getDataAtual("SQL") + "', ?, 0, 0, 0, 0) ",
            sqlStatInsert = " UPDATE StatWeb SET Inclusao  = (Inclusao + 1)  WHERE Data = '" + DataAtual.getDataAtual("SQL") + "' AND Hora = " + DataAtual.getDataAtual("HH"),
            sqlStatUpdate = " UPDATE StatWeb SET Alteracao = (Alteracao + 1) WHERE Data = '" + DataAtual.getDataAtual("SQL") + "' AND Hora = " + DataAtual.getDataAtual("HH"),
            sqlStatSelect = " UPDATE StatWeb SET Consulta  = (Consulta + 1)  WHERE Data = '" + DataAtual.getDataAtual("SQL") + "' AND Hora = " + DataAtual.getDataAtual("HH"),
            sqlStatDelete = " UPDATE StatWeb SET Exclusao  = (Exclusao + 1)  WHERE Data = '" + DataAtual.getDataAtual("SQL") + "' AND Hora = " + DataAtual.getDataAtual("HH");
    private int indice_sql = 0, total_sql;
    private float reg_percorrido = 0;

    /**
     * Construtor da classe consulta
     *
     * @param sql - string sql igual a usada com PreparedStatement
     * @param persistencia - passa-se a classe persistencia que contem a conexao
     * com o BD
     * @throws java.lang.Exception
     */
    public Consulta(String sql, Persistencia persistencia) throws Exception {
        Sql = sql;
        Mstat = persistencia.getState(sql);
        mstatCreate = persistencia.getState(sqlStatCreate);
        mstatInsert = persistencia.getState(sqlStatInsert);
        mstatUpdate = persistencia.getState(sqlStatUpdate);
        mstatSelect = persistencia.getState(sqlStatSelect);
        mstatDelete = persistencia.getState(sqlStatDelete);
        int pos = -1;
        int contagem = 0;
        while (true) {
            pos = Sql.indexOf("?", pos + 1);
            if (pos < 0) {
                break;
            }
            contagem++;
        }
        total_sql = contagem;
        reg_percorrido = 0;
    }

    /**
     * Construtor da classe consulta
     *
     * @param sql - string sql igual a usada com PreparedStatement
     * @param persistencia - passa-se a classe persistencia que contem a conexao
     * com o BD
     * @throws java.lang.Exception
     * @deprecated use {@link #Consulta(String sql,Persistencia persistencia)}
     * instead.
     */
    @Deprecated
    public Consulta(String sql, Persistencia_OLD persistencia) throws Exception {
        Sql = sql;
        Mstat = persistencia.getState(sql);
        if (persistencia.isMysql()) {
            mstatCreate = null;
            mstatInsert = null;
            mstatUpdate = null;
            mstatSelect = null;
            mstatDelete = null;
        } else {
            mstatCreate = persistencia.getState(sqlStatCreate);
            mstatInsert = persistencia.getState(sqlStatInsert);
            mstatUpdate = persistencia.getState(sqlStatUpdate);
            mstatSelect = persistencia.getState(sqlStatSelect);
            mstatDelete = persistencia.getState(sqlStatDelete);
        }
        int pos = -1;
        int contagem = 0;
        while (true) {
            pos = Sql.indexOf("?", pos + 1);
            if (pos < 0) {
                break;
            }
            contagem++;
        }
        total_sql = contagem;
        reg_percorrido = 0;
    }

    /**
     * Insere um parametro do tipo string
     *
     * @param param - parametro da pesquisa
     * @throws java.lang.Exception
     */
    public void setString(String param) throws Exception {
        indice_sql++;
        if (indice_sql <= total_sql) {
            try {
                Mstat.setString(indice_sql, param);
            } catch (Exception e) {
                throw new Exception("Query.setString(" + param + ") - " + e.getMessage());
            }
        } else {
            throw new Exception("Query.setString - Parameter '" + param + "' can not be applied, limit exceeded");
        }
    }

    public void setBigDecimal(BigDecimal param) throws Exception {
        indice_sql++;
        if (indice_sql <= total_sql) {
            try {
                Mstat.setBigDecimal(indice_sql, param);
            } catch (Exception e) {
                throw new Exception("Query.setBigDecimal(" + param + ") - " + e.getMessage());
            }
        } else {
            throw new Exception("Query.setBigDecimal - Parameter '" + param + "' can not be applied, limit exceeded");
        }
    }

    /**
     * Insere um parametro do tipo float
     *
     * @param param - parametro da pesquisa
     * @throws java.lang.Exception
     */
    public void setFloat(float param) throws Exception {
        indice_sql++;
        if (indice_sql <= total_sql) {
            try {
                Mstat.setFloat(indice_sql, param);
            } catch (Exception e) {
                throw new Exception("Query.setFloat(" + param + ") - " + e.getMessage());
            }
        } else {
            throw new Exception("Query.setFloat - Parameter '" + param + "' can not be applied, limit exceeded");
        }
    }

    /**
     * Insere um parametro do tipo int
     *
     * @param param - parametro da pesquisa
     * @throws java.lang.Exception
     */
    public void setInt(int param) throws Exception {
        indice_sql++;
        if (indice_sql <= total_sql) {
            try {
                Mstat.setInt(indice_sql, param);
            } catch (Exception e) {
                throw new Exception("Query.setInt(" + param + ") - " + e.getMessage());
            }
        } else {
            throw new Exception("Query.setInt - Parameter '" + param + "' can not be applied, limit exceeded");
        }
    }

    /**
     * Insere um parametro do tipo Date
     *
     * @param param - parametro da pesquisa
     * @throws java.lang.Exception
     */
    public void setDate(Date param) throws Exception {
        indice_sql++;
        if (indice_sql <= total_sql) {
            try {
                Mstat.setDate(indice_sql, param);
            } catch (Exception e) {
                throw new Exception("Query.setDate(" + param + ") - " + e.getMessage());
            }
        } else {
            throw new Exception("Query.setDate - Parameter '" + param + "' can not be applied, limit exceeded");
        }
    }

    /**
     * Executa a consulta
     *
     * @throws java.lang.Exception
     */
    public void select() throws Exception {
        try {
            Rs = Mstat.executeQuery();
            statSelect();
        } catch (Exception e) {
            if (null != Mstat) {
                Mstat.close();
            }
            if (null != Rs) {
                Rs.close();
            }
            throw new Exception("Query.RunSelect - Error executing query - " + e.getMessage());
        }
    }

    /**
     * Executa a consulta
     *
     * @throws java.lang.Exception
     */
    public void contraChequeCORPVS() throws Exception {
        try {
            Rs = Mstat.executeQuery();
        } catch (Exception e) {
            if (null != Mstat) {
                Mstat.close();
            }
            if (null != Rs) {
                Rs.close();
            }
            throw new Exception("Query.RunSelect - Error executing query - " + e.getMessage());
        }
    }

    /**
     * Executa o update
     *
     * @return
     * @throws java.lang.Exception
     */
    public int update() throws Exception {
        try {
            int temp = Mstat.executeUpdate();
            statUpdate();
            return temp;
        } catch (Exception e) {
            if (null != Mstat) {
                Mstat.close();
            }
            throw new Exception("Query.RunUpdate - Error executing update - " + e.getMessage());
        }
    }

    /**
     * Executa o insert
     *
     * @return
     * @throws java.lang.Exception
     */
    public int insert() throws Exception {
        try {
            int temp = Mstat.executeUpdate();
            statInsert();
            return temp;
        } catch (Exception e) {
            if (null != Mstat) {
                Mstat.close();
            }
            throw new Exception("Query.RunInsert - Error executing insert - " + e.getMessage());
        }
    }

    /**
     * Executa o insert
     *
     * @return
     * @throws java.lang.Exception
     */
    public int delete() throws Exception {
        try {
            int temp = Mstat.executeUpdate();
            statDelete();
            return temp;
        } catch (Exception e) {
            if (null != Mstat) {
                Mstat.close();
            }

            throw new Exception("Query.RunDelete - Error executing delete - " + e.getMessage());
        }
    }

    /**
     * Busca o proximo registro devolvido na consulta
     *
     * @return - true para sucesso e false para falha
     * @throws java.lang.Exception
     */
    public boolean Proximo() throws Exception {
        try {
            boolean retorno = Rs.next();
            if (retorno) {
                reg_percorrido++;
            }
            return retorno;
        } catch (Exception e) {
            Rs.close();
            throw new Exception("Query.Next - Closed consultation -  " + e.getMessage());
        }
    }

    /**
     * Devolve uma string da posicao atual na tabela retornada na consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em String
     * @throws java.lang.Exception
     */
    public String getString(int i) throws Exception {
        try {
            if ("null".equals(Rs.getString(i)) || Rs.getString(i) == null) {
                return "";
            } else {
                return Rs.getString(i);
            }
        } catch (Exception e) {
            throw new Exception("Query.getString - " + e.getMessage());
        }
    }

    /**
     * Devolve uma string da posicao atual na tabela retornada na consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em String
     * @throws java.lang.Exception
     */
    public String getString(String i) throws Exception {
        try {
            if ("null".equals(Rs.getString(i)) || Rs.getString(i) == null) {
                return "";
            } else {
                return Rs.getString(i);
            }
        } catch (Exception e) {
            throw new Exception("Query.getString - " + e.getMessage());
        }
    }

    /**
     * Devolve um float da posicao atual na tabela retornada na consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em float
     * @throws java.lang.Exception
     */
    public float getFloat(int i) throws Exception {
        try {
            return Rs.getFloat(i);
        } catch (Exception e) {
            throw new Exception("Query.getFloat - " + e.getMessage());
        }
    }

    /**
     * Devolve um float da posicao atual na tabela retornada na consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em float
     * @throws java.lang.Exception
     */
    public float getFloat(String i) throws Exception {
        try {
            return Rs.getFloat(i);
        } catch (Exception e) {
            throw new Exception("Query.getFloat - " + e.getMessage());
        }
    }

    /**
     * Devolve um inteiro da posicao atual na tabela retornada na consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em inteiro
     * @throws java.lang.Exception
     */
    public int getInt(int i) throws Exception {
        try {
            return Rs.getInt(i);
        } catch (Exception e) {
            throw new Exception("Query.getInt - " + e.getMessage());
        }
    }

    /**
     * Devolve um inteiro da posicao atual na tabela retornada na consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em inteiro
     * @throws java.lang.Exception
     */
    public int getInt(String i) throws Exception {
        try {
            return Rs.getInt(i);
        } catch (Exception e) {
            throw new Exception("Query.getInt - " + e.getMessage());
        }
    }

    /**
     * Devolve uma data da posicao atual na tabela retornada pela consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em Date
     * @throws Exception
     */
    public Date getDate(String i) throws Exception {
        Date I = Rs.getDate(i);
        if (I == null || "null".equals(I)) {
            I = Date.valueOf("1900-01-01");
        }
        return I;
    }

    /**
     * Devolve uma data da posicao atual na tabela retornada pela consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em Date
     * @throws Exception
     */
    public Date getDate(int i) throws Exception {
        try {
            return Rs.getDate(i);
        } catch (Exception e) {
            throw new Exception("Query.getDate - " + e.getMessage());
        }
    }

    /**
     * Devolve uma data da posicao atual na tabela retornada pela consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em LocalDate
     * @throws Exception
     */
    public LocalDate getLocalDate(String i) throws Exception {
        try {
            //return Rs.getDate(i).toLocalDate();
            Date datas = Rs.getDate(i);
            LocalDate ld;
            try {
                ld = datas.toLocalDate();
            } catch (Exception e) {
                ld = null;
            }
            return ld;
        } catch (Exception e) {
            throw new Exception("Query.getLocalDate - " + e.getMessage());
        }
    }

    /**
     * Devolve uma data da posicao atual na tabela retornada pela consulta
     *
     * @param i - coluna da consulta
     * @return - retorno em LocalDate
     * @throws Exception
     */
    public LocalDate getLocalDate(int i) throws Exception {
        try {
            //return Rs.getDate(i).toLocalDate();
            Date datas = Rs.getDate(i);
            LocalDate ld;
            try {
                ld = datas.toLocalDate();
            } catch (Exception e) {
                ld = null;
            }
            return ld;
        } catch (Exception e) {
            throw new Exception("Query.getLocalDate - " + e.getMessage());
        }
    }

    /**
     * Retorna BigDecimal
     *
     * @param i
     * @return
     * @throws Exception
     */
    public BigDecimal getBigDecimal(int i) throws Exception {
        try {
            return Rs.getBigDecimal(i);
        } catch (Exception e) {
            throw new Exception("Query.getBigDecimal - " + e.getMessage());
        }
    }

    /**
     * Retorna BigDecimal
     *
     * @param i
     * @return
     * @throws Exception
     */
    public BigDecimal getBigDecimal(String i) throws Exception {
        try {
            return Rs.getBigDecimal(i);
        } catch (Exception e) {
            throw new Exception("Query.getBigDecimal - " + e.getMessage());
        }
    }

    /**
     * Devolve a quantidade de registros percorridos até o momento
     *
     * @return - float com a quantidade de registros
     */
    public float getRegPercorrido() {
        return reg_percorrido;
    }

    public Object getValue(String coluna, String tipo) throws Exception {
        switch (tipo) {
            case "String": {
                return this.getString(coluna);
            }
            case "Int": {
                return this.getInt(coluna);
            }
            case "BigDecimal": {
                return this.getBigDecimal(coluna);
            }
            case "Date": {
                return this.getDate(coluna);
            }
            case "Float": {
                return this.getFloat(coluna);
            }
            default: {
                throw new Exception("Tipo " + tipo + " incorreto.");
            }
        }
    }

    /**
     * Fecha o resultado da consulta
     *
     * @throws java.lang.Exception
     */
    public void TerminaConsulta() throws Exception {
        try {
            Rs.close();
            this.reg_percorrido = 0;
        } catch (Exception e) {
            throw new Exception("Query.TerminaConsulta - " + e.getMessage());
        }
    }

    /**
     * Finaliza a classe fechando a consulta e os resultados
     *
     * @throws Exception - gera mensagem de erro quando for o caso
     */
    public void Close() throws Exception {
        String erro = "";
        try {
            if (Rs != null) {
                erro = "Rs.close() ";
                Rs.close();
            }
            erro = "Mstat.close() ";
            Mstat.close();
            erro = "mstatCreate.close() ";
            if(mstatCreate != null) mstatCreate.close();
            erro = "mstatInsert.close() ";
            if(mstatInsert != null) mstatInsert.close();
            erro = "mstatUpdate.close() ";
            if(mstatUpdate != null) mstatUpdate.close();
            erro = "mstatSelect.close() ";
            if(mstatSelect != null) mstatSelect.close();
            erro = "mstatDelete.close() ";
            if(mstatDelete != null) mstatDelete.close();
            this.reg_percorrido = 0;
        } catch (Exception e) {
            throw new Exception("Query.Close - " + erro + "\r\n" + e.getMessage());
        }
    }

    public void close() throws Exception {
        Close();
    }

    /**
     * Verifica se o cursor está na primeira posição da consulta
     *
     * @return true - primeira posição false - outras posições
     * @throws Exception
     */
    public boolean isFirst() throws Exception {
        try {
            return Rs.isFirst();
        } catch (Exception e) {
            throw new Exception("Query.isFirst - " + e.getMessage());
        }
    }

    /**
     * Busca lista dos rótulos da consulta
     *
     * @return - retorna lista de rotulos da consulta
     * @throws Exception
     */
    public List<String> getMetadataRotulo() throws Exception {
        try {
            List<String> colunas = new ArrayList();
            ResultSetMetaData mdata;
            mdata = Rs.getMetaData();
            for (int i = 1; i <= mdata.getColumnCount(); i++) {
                colunas.add(mdata.getColumnLabel(i));
            }
            return colunas;
        } catch (Exception e) {
            throw new Exception("Query.getMetadataRotulo - " + e.getMessage());
        }
    }

    /**
     * Busca lista dos tipos da consulta
     *
     * @return - retorna lista dos tipos de dados das colunas da consulta
     * @throws Exception
     */
    public List<String> getMetadataTipo() throws Exception {
        try {
            List<String> colunas = new ArrayList();
            ResultSetMetaData mdata;
            mdata = Rs.getMetaData();
                for (int i = 1; i <= mdata.getColumnCount(); i++) {
                colunas.add(mdata.getColumnTypeName(i));
            }
            return colunas;
        } catch (Exception e) {
            throw new Exception("Query.getMetadataTipo - " + e.getMessage());
        }
    }

    /**
     * Busca lista dos tamanhos da consulta
     *
     * @return - retorna lista dos tamanhos de dados das colunas da consulta
     * @throws Exception
     */
    public List<Integer> getMetadataTamanho() throws Exception {
        try {
            List<Integer> colunas = new ArrayList();
            ResultSetMetaData mdata;
            mdata = Rs.getMetaData();
            for (int i = 1; i <= mdata.getColumnCount(); i++) {
                colunas.add(mdata.getColumnDisplaySize(i));
            }
            return colunas;
        } catch (Exception e) {
            throw new Exception("Query.getMetadataTamanho - " + e.getMessage());
        }
    }

    /**
     * Devolve o ResultSet da Consulta
     *
     * @return
     */
    public ResultSet getRs() {
        return Rs;
    }

    public void setBigDecimal(String param) throws Exception {
        indice_sql++;
        if (indice_sql <= total_sql) {
            try {
                Mstat.setString(indice_sql, param);
            } catch (Exception e) {
                throw new Exception("Query.setBigDecimal(" + param + ") - " + e.getMessage());
            }
        } else {
            throw new Exception("Query.setBigDecimal - Parametro '" + param + "' can not be applied, limit exceeded");
        }
    }

    public void setInt(String s) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    /**
     * Atualiza o campo de inserts da tabela StatWeb
     */
    private void statInsert() {
        if (mstatInsert != null) {
            int updates = 0;
            try {
                updates = mstatInsert.executeUpdate();
            } catch (Exception e) {
            }
            try {
                if (updates == 0) {
                    inicializar();
                    mstatInsert.executeUpdate();
                }
            } catch (Exception e) {
            }
        }
    }

    /**
     * Atualiza o campo de deletes da tabela StatWeb
     */
    private void statDelete() {
        if (mstatDelete != null) {
            int updates = 0;
            try {
                updates = mstatDelete.executeUpdate();
            } catch (Exception e) {
            }
            try {
                if (updates == 0) {
                    inicializar();
                    mstatDelete.executeUpdate();
                }
            } catch (Exception e) {
            }
        }
    }

    /**
     * Atualiza o campo de selects da tabela StatWeb
     */
    private void statSelect() {
        if (mstatSelect != null) {
            int updates = 0;
            try {
                updates = mstatSelect.executeUpdate();
            } catch (Exception e) {
            }
            try {
                if (updates == 0) {
                    inicializar();
                    mstatSelect.executeUpdate();
                }
            } catch (Exception e) {
            }
        }
    }

    /**
     * Atualiza o campo de updates da tabela StatWeb
     */
    private void statUpdate() {
        if (mstatUpdate != null) {
            int updates = 0;
            try {
                updates = mstatUpdate.executeUpdate();
            } catch (Exception e) {
            }
            try {
                if (updates == 0) {
                    inicializar();
                    mstatUpdate.executeUpdate();
                }
            } catch (Exception e) {
            }
        }
    }

    /**
     * Inicializa a tabela StatWeb para a data atual
     */
    private void inicializar() {
        for (int i = 0; i < 24; i++) {
            try {
                mstatCreate.clearParameters();
                mstatCreate.setInt(1, i);
                mstatCreate.executeUpdate();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    
    
}

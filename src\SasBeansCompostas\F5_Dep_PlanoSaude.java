package SasBeansCompostas;

import SasBeans.F5_Dep;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class F5_Dep_PlanoSaude {

    private F5_Dep f5_dep;
    private BigDecimal valoranual;

    public F5_Dep_PlanoSaude() {
        this.f5_dep = new F5_Dep();
        this.valoranual = new BigDecimal("0");
    }

    public F5_Dep getF5_dep() {
        return f5_dep;
    }

    public void setF5_dep(F5_Dep f5_dep) {
        this.f5_dep = f5_dep;
    }

    public BigDecimal getValoranual() {
        return valoranual;
    }

    public void setValoranual(String valoranual) {
        try {
            this.valoranual = new BigDecimal(valoranual);
        } catch (Exception e) {
            this.valoranual = new BigDecimal("0");
        }
    }

}

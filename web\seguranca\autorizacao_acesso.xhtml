<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formPesquisar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formOrdenacaoRapida"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                .calendario input {
                    width: 190px !important;
                }

                [class*="ui-datepicker"]{
                    left: auto !important;
                    right: 0px !important;
                    z-index: 2 !important;
                }

                .titAutorizacao{
                    font-size: 10pt !important;
                    color: #666 !important;
                    text-align: right;
                    padding-right: 8px;
                }

                .valAutorizacao{
                    font-size: 12pt !important;
                    font-weight: bold;
                    color: #222 !important;
                    text-align: left;
                }

                .ui-inputtext{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                [id="main:tabela"]{
                    height: calc(100vh - 150px) !important;
                }

                .dialogoPagina{
                    width: 1000px !important;
                }

                @media(max-width: 510px){
                    #divCalendario{
                        position: absolute;
                        top: 46px !important;
                        right: 0px !important;
                    }

                    .titAutorizacao{
                        zoom: 0.7;
                    }

                    .valAutorizacao{
                        zoom: 0.7;
                    }

                    #divFundoAutorizacao{
                        height: 490px !important;
                    }

                    .calendario .ui-inputfield {
                        width: 174px !important;
                        position: absolute;
                        right: 0px;
                        padding: 0px !important;
                        margin-top: 4px !important;
                        text-align: left !important;
                        padding-left: 6px !important;
                    }

                    .p-datepicker-panel{
                        left: auto !important;
                        right: 0px !important;
                    }

                    [id="cabecalho:j_idt12"]{
                        right: 150px;
                        position: absolute;
                    }

                    body .ui-calendar button.ui-datepicker-trigger>span.ui-icon-calendar {
                        top: 9px !important;
                    }

                    [id$="formCadastrar"] .ui-calendar button.ui-datepicker-trigger>span.ui-icon-calendar{
                        top: 0px !important;
                    }

                    .dialogoPagina{
                        width: 95vw !important;
                    }
                }


                @media only screen and (max-width: 700px) and (min-width: 10px){
                    #corporativo div:nth-child(3) {
                        display: initial !important;
                    }

                    [id$="formCadastrar"] [id$="cadastrar"]{
                        zoom: 0.8;
                    }
                }

                [id$="nomePessoa_input"]{
                    width: 100%;
                }

                [id*="formCadastrar"] .ui-dialog-content.ui-widget-content{
                    padding-right: 6px !important;
                }

                [id*="formCadastrar"] .ui-datatable th[role="columnheader"].ui-state-default, 
                [id*="formCadastrar"] .ui-treetable th[role="columnheader"].ui-state-default{
                    line-height: 7px;
                }

            </style>
        </h:head>                      
        <h:body id="h" style="overflow:hidden !important; max-height:100% !important">
            <f:metadata>
                <f:viewAction action="#{acessoAut.Persistencia(login.pp,login.satellite)}"/>
                <f:viewAction action="#{acessoAut.carregarListaAutorizacoes}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_area_produtividade.png" height="40" style="margin-top:-6px !important;" />
                                    <label class="TituloPagina">#{localemsgs.AutorizacaoAcesso}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span><h:outputText id="dataDia" value="#{acessoAut.data1}" converter="conversorData"/> a <h:outputText id="dataDia2" value="#{acessoAut.data2}" converter="conversorData"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{acessoAut.filialTela.descricao}<label id="btTrocarFilial">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{acessoAut.filialTela.endereco}</label>
                                    <label class="FilialBairroCidade">#{acessoAut.filialTela.bairro}, #{acessoAut.filialTela.cidade}/#{acessoAut.filialTela.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                                  value="#{acessoAut.datasSelecionadas}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{acessoAut.selecionarDatasProdutividade}" update="msgs main dataDia dataDia2" />
                                    </p:datePicker>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main" style="max-height: calc(100vh - 90px) !important">
                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:6px !important;padding-left:6px !important;overflow-y: auto !important; padding-bottom: 15px !important; border-top-color: rgb(210, 214, 222) !important;">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel styleClass="col-md-12 col-sm-12 col-xs-12" id="tabela" style="padding: 0px;">
                                    <label style="width: 100%; text-align: center; color: #AAA; font-size: 16pt; font-weight: 500; display: #{acessoAut.listaAutorizacoes.size() > 0 ?'none':''}">#{localemsgs.SemRegistros}</label>

                                    <p:repeat id="autorizacoeslist" value="#{acessoAut.listaAutorizacoes}" var="lista" rendered="#{acessoAut.listaAutorizacoes.size() > 0}">
                                        <p:panel styleClass="col-md-6 col-sm-6 col-xs-12" style="padding: 8px !important;">
                                            <div id="divFundoAutorizacao" class="col-md-12 col-sm-12 col-xs-12" style="height: 460px; border: thin solid #CCC; border-radius: 10px; padding: 0px; border-top: 5px solid #{lista.situacao eq 'OK'? 'steelblue': 'red'}">

                                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 75px; padding-top: 12px;">
                                                    <img src="../assets/img/icone_satmob_contracheque_funcaoadm_G.png" height="50" style="float: left" />
                                                    <div style="float: left; padding-top: 9px">
                                                        <h:outputText value="#{lista.sequencia2}" style="font-size: 16pt; font-weight: 600; color: #{lista.situacao eq 'OK'? 'steelblue': 'red'}; margin-left: 8px !important">
                                                            <f:convertNumber pattern="000000" />
                                                        </h:outputText>
                                                    </div>
                                                    <div style="float: right; padding-top: 9px; white-space: nowrap;">
                                                        <table style="width: 100%; height: 100%; white-space: nowrap; background-color: #{lista.situacao eq 'OK'? 'steelblue': 'red'}; border-radius: 8px;">
                                                            <tr>
                                                                <td style="font-size: 16pt; padding-left: 8px; font-weight: 600; color: #FFF; margin-left: 8px !important">#{localemsgs.CodFil}: </td>
                                                                <td>
                                                                    <h:outputText value="#{lista.codFil2}" converter="conversorCodFil" style="padding-right: 8px;font-size: 16pt; font-weight: 600; color: #FFF; margin-left: 8px !important"></h:outputText>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 40px; border-top: thin solid #CCC; background-color: #F0F0F0; padding-left: 0px; padding-right: 0px;">
                                                    <table style="width: 100%; height: 100%">
                                                        <tr>
                                                            <td class="titAutorizacao">#{localemsgs.Situacao}:</td>
                                                            <td class="valAutorizacao"><h:outputText value="#{lista.situacao}"  /></td>
                                                            <td class="titAutorizacao">#{localemsgs.DataInicial}:</td>
                                                            <td class="valAutorizacao"><h:outputText value="#{lista.dtInicio}" converter="conversorData" /></td>
                                                            <td class="titAutorizacao">#{localemsgs.DataFinal}:</td>
                                                            <td class="valAutorizacao"><h:outputText value="#{lista.dtFinal}" converter="conversorData" /></td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100% - 115px); border-top: thin solid #CCC; background-color: #F0F0F0; border-radius: 0px 0px 10px 10px">
                                                    <div class="row" style="margin: 8px 0px 0px 0px; padding: 0px;">
                                                        <table style="width: 100%; height: 100%">
                                                            <tr>
                                                                <td class="titAutorizacao" style="text-align: right; width: 15%">#{localemsgs.SegSex}:</td>
                                                                <td class="valAutorizacao" style="text-align: left; width: 10%">  <h:outputText value="#{lista.segSex eq '0'? localemsgs.Nao: localemsgs.Sim}" /></td>
                                                                <td class="titAutorizacao" style="text-align: right; width: 12.5%">#{localemsgs.Sab}:</td>
                                                                <td class="valAutorizacao" style="text-align: left; width: 12.5%">  <h:outputText value="#{lista.sab eq '0'? localemsgs.Nao: localemsgs.Sim}" /></td>
                                                                <td class="titAutorizacao" style="text-align: right; width: 12.5%">#{localemsgs.Dom}:</td>
                                                                <td class="valAutorizacao" style="text-align: left; width: 12.5%">  <h:outputText value="#{lista.dom eq '0'? localemsgs.Nao: localemsgs.Sim}" /></td>
                                                                <td class="titAutorizacao" style="text-align: right; width: 12.5%">#{localemsgs.Fer}:</td>
                                                                <td class="valAutorizacao" style="text-align: left; width: 12.5%">  <h:outputText value="#{lista.fer eq '0'? localemsgs.Nao: localemsgs.Sim}" /></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="row" style="margin: 0px; padding: 0px 0px 8px 0px; border-top: thin solid #DDD; border-bottom: thin solid #DDD; margin-top:8px; background-color: #FFF">
                                                        <div class="col-md-6 col-sm-6 col-xs-12" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right; width: 55px; padding-right: 6px;">#{localemsgs.Nome}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.nome}" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right">#{localemsgs.Situacao}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.PSituacao}" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right">#{localemsgs.RG}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.RG}" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <div class="row" style="margin: 5px 0px 0px 0px; padding: 0px 8px 0px 0px;">
                                                        <table style="width: 100%; height: 100%">
                                                            <tr>
                                                                <td class="titAutorizacao" style="text-align: right; width: 85px;">#{localemsgs.Solicitante}:</td>
                                                                <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.solicitante}" /></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="row" style="margin: 5px 0px 0px 0px; padding: 0px 8px 0px 0px;">
                                                        <table style="width: 100%; height: 100%">
                                                            <tr>
                                                                <td class="titAutorizacao" style="text-align: right; width: 85px;">#{localemsgs.Destino}:</td>
                                                                <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.destino}" /></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="row" style="margin: 5px 0px 0px 0px; padding: 0px 8px 0px 0px;">
                                                        <table style="width: 100%; height: 100%">
                                                            <tr>
                                                                <td class="titAutorizacao" style="text-align: right; width: 85px;">#{localemsgs.Finalidade}:</td>
                                                                <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.finalidade}" /></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="row" style="margin: 5px 0px 0px 0px; padding: 0px 8px 0px 0px;">
                                                        <table style="width: 100%; height: 100%">
                                                            <tr>
                                                                <td class="titAutorizacao" style="text-align: right; width: 85px;">#{localemsgs.Obs}:</td>
                                                                <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.obs}" /></td>
                                                            </tr>
                                                        </table>
                                                    </div>


                                                    <div class="row" style="margin: 8px 0px 0px 0px; padding: 0px; border-top: thin solid #DDD">
                                                        <div class="col-md-6 col-sm-6 col-xs-12" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right; width: 85px; padding-right: 6px;">#{localemsgs.Operador}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.operador}" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right; width: 80px">#{localemsgs.Dt_Alter}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.dt_Alter}" converter="conversorData" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right; width: 80px">#{localemsgs.Hr_Alter}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left"> <h:outputText value="#{lista.hr_Alter}" converter="conversorHora" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <div class="row" style="margin: 0px 0px 0px 0px; padding: 0px;">
                                                        <div class="col-md-6 col-sm-6 col-xs-12" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right; width: 85px; padding-right: 6px;">#{localemsgs.OperAut}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.operAut}" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right; width: 80px">#{localemsgs.Dt_Aut}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left">  <h:outputText value="#{lista.dt_Aut}" converter="conversorData" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 8px 8px 0px 0px;">
                                                            <table style="width: 100%; height: 100%">
                                                                <tr>
                                                                    <td class="titAutorizacao" style="text-align: right; width: 80px">#{localemsgs.Hr_Aut}:</td>
                                                                    <td class="valAutorizacao" style="text-align: left"> <h:outputText value="#{lista.hr_Aut}" converter="conversorHora" /></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <div class="row" style="margin: 0px 0px 0px 0px; padding: 0px; margin-top: 10px;">
                                                        <div class="col-md-4 col-sm-4 col-xs-4" style="padding: 0px; text-align: center;">
                                                            <p:commandLink title="#{localemsgs.Autorizar}" actionListener="#{acessoAut.preAutorizar(lista.sequencia2)}"
                                                                           partialSubmit="true" process="@this"
                                                                           update="formCadastrar msgs">
                                                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" style="margin-top:10px;" />
                                                            </p:commandLink>
                                                        </div>
                                                        <div class="col-md-4 col-sm-4 col-xs-4" style="padding: 0px; text-align: center;">
                                                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{acessoAut.carregarEdicao(lista.sequencia2)}"
                                                                           partialSubmit="true" process="@this"
                                                                           update="formCadastrar msgs">
                                                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="40" height="40" style="margin-top:10px;" />
                                                            </p:commandLink>
                                                        </div>
                                                        <div class="col-md-4 col-sm-4 col-xs-4" style="padding: 0px; text-align: center;">
                                                            <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{acessoAut.preExcluir(lista.sequencia2)}"
                                                                           partialSubmit="true" process="@this"
                                                                           update="msgs">
                                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="40" height="40" style="margin-top:10px;" />
                                                            </p:commandLink>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </p:panel>
                                    </p:repeat>

                                </p:panel>
                            </div>
                        </div>
                    </p:panel>

                    <p:panel style="position: fixed; z-index: 1; right: 5px; bottom: 42px; background: transparent" id="botoes">
                        <p:remoteCommand name="rcExcluirAutorizacao" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs main" 
                                         actionListener="#{acessoAut.excluirAutorizacao}" />   
                        
                        <p:remoteCommand name="rcAutorizar" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs main" 
                                         actionListener="#{acessoAut.autorizar}" />   
                        
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}"
                                           update="formCadastrar" actionListener="#{acessoAut.novaAutorizacao}">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                           oncomplete="PF('dlgPesquisar').show()" update="formPesquisar">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Ordenacao}" process="@this"
                                           oncomplete="PF('dlgOrdenacaoRapida').show()" update="formOrdenacaoRapida">
                                <p:graphicImage url="../assets/img/icone_redondo_ordenar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}"
                                           update="msgs main:tabela corporativo" actionListener="#{acessoAut.limparPesquisa}">

                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <h:form id="formCadastrar" class="form-inline">
                    <p:dialog widgetVar="dlgCadastrar"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important">

                        <f:facet name="header">
                            <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.AutorizacaoAcesso}" style="color:#022a48"/>
                            <h:outputText value=" #{acessoAut.autorizacaoSelecionada.sequencia2.toBigInteger()}" style="color:#022a48" rendered="#{acessoAut.autorizacaoSelecionada.sequencia2 ne null and acessoAut.autorizacaoSelecionada.sequencia2 ne 0}" />
                            <h:outputText value=" - " style="color:#022a48" rendered="#{acessoAut.autorizacaoSelecionada.sequencia2 ne null and acessoAut.autorizacaoSelecionada.sequencia2 ne 0}" />
                            <h:outputText value="#{acessoAut.autorizacaoSelecionada.situacao}" class="#{acessoAut.autorizacaoSelecionada.situacao eq 'OK' ? 'verde' : 'inativo'}" rendered="#{acessoAut.autorizacaoSelecionada.sequencia2 ne null and acessoAut.autorizacaoSelecionada.sequencia2 ne 0}"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; margin-top:-10px !important; z-index:999 !important; height:calc(100% - 20px) !important; padding: 0px !important" class="cadastrar">
                            <div class="col-md-3" style="padding: 0px 8px 0px 0px;">
                                <p:outputLabel for="filial" value="#{localemsgs.Filial}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <p:selectOneMenu id="filial" value="#{acessoAut.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}"/>
                                    <p:ajax event="itemSelect" listener="#{acessoAut.listarCadastroAreas}" update="msgs"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-5" style="padding: 0px 8px 0px 0px;">
                                <p:outputLabel value="#{localemsgs.Periodo}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <div class="row" style="padding: 0px; margin: 0px; width: 100%">
                                    <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px;">
                                        <p:datePicker id="data1" value="#{acessoAut.autorizacaoSelecionada.dtInicio}"
                                                      pattern="#{mascaras.padraoData}" styleClass="calendario2" showIcon="true"
                                                      required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtInicio}"
                                                      monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                      style="width: 100%" converter="conversorData" >
                                        </p:datePicker>   
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px 0px 0px 8px;">
                                        <p:datePicker id="data2" value="#{acessoAut.autorizacaoSelecionada.dtFinal}"
                                                      pattern="#{mascaras.padraoData}" styleClass="calendario2" showIcon="true"
                                                      required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtFim}"
                                                      monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                      style="width: 100%" converter="conversorData" >
                                        </p:datePicker>   
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4" style="padding: 25px 8px 0px 0px">
                                <table style="width: 100%">
                                    <tr>
                                        <td style="width: 25%">
                                            <label for="chkSegSex"><h:outputText value="#{localemsgs.SegSex}: "  /></label>
                                            <p:selectBooleanCheckbox value="#{acessoAut.segSex}" id="chkSegSex" style="margin-left: 8px"></p:selectBooleanCheckbox>
                                        </td>
                                        <td style="width: 25%">
                                            <label for="chkSab"><h:outputText value="#{localemsgs.Sab}: "  /></label>
                                            <p:selectBooleanCheckbox value="#{acessoAut.sab}" id="chkSab" style="margin-left: 8px"></p:selectBooleanCheckbox>
                                        </td>
                                        <td style="width: 25%">
                                            <label for="chkDom"><h:outputText value="#{localemsgs.Dom}: "  /></label>
                                            <p:selectBooleanCheckbox value="#{acessoAut.dom}" id="chkDom" style="margin-left: 8px"></p:selectBooleanCheckbox>
                                        </td>
                                        <td style="width: 25%">
                                            <label for="chkFer"><h:outputText value="#{localemsgs.Fer}: "  /></label>
                                            <p:selectBooleanCheckbox value="#{acessoAut.fer}" id="chkFer" style="margin-left: 8px"></p:selectBooleanCheckbox>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-12" style="margin-top: 8px; padding: 0px 8px 0px 0px">
                                <p:outputLabel value="#{localemsgs.Pessoa}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>


                                <p:autoComplete id="nomePessoa" value="#{acessoAut.pessoa}" completeMethod="#{acessoAut.buscarPessoas}"
                                                label="#{localemsgs.Nome}" forceSelection="true"  styleClass="cidade"
                                                style="width: 100%" minQueryLength="3" scrollHeight="200"
                                                required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Pessoa}"
                                                var="pes" itemValue="#{pes}" itemLabel="#{pes.nome}" >
                                    <p:ajax event="itemSelect" listener="#{acessoAut.buscarFuncion}" update="dadosFuncion2"/>
                                    <o:converter converterId="omnifaces.ListIndexConverter" list="#{acessoAut.listaPessoas}" />
                                </p:autoComplete>
                            </div>
                            <div class="col-md-12" style="padding: 0px 8px 0px 0px; background-color: transparent;">
                                <p:panel id="dadosFuncion2" class="col-md-12" style="padding: 0px; background-color: transparent;">
                                    <p:panel id="dadosFuncion" rendered="#{acessoAut.funcion.matr ne null and acessoAut.funcion.matr ne 0}" class="col-md-12" style="margin-top:8px; background-color: #FFF; border: thin solid #DDD; padding-bottom: 8px !important">
                                        <div class="col-md-7" style="padding-top: 6px">
                                            <table>
                                                <tr>
                                                    <td style="width: 80px; text-align: right; font-weight: bold;">#{localemsgs.Matr}: </td>
                                                    <td style="padding-left: 8px;"><h:outputText value="#{acessoAut.funcion.matr}" converter="conversor0"></h:outputText></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-5" style="padding-top: 6px">
                                            <table>
                                                <tr>
                                                    <td style="width: 80px; text-align: right; font-weight: bold;">#{localemsgs.Cargo}: </td>
                                                    <td style="padding-left: 8px;">#{acessoAut.funcion.cargoDescr}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-7" style="padding-top: 6px">
                                            <table>
                                                <tr>
                                                    <td style="width: 80px; text-align: right; font-weight: bold;">#{localemsgs.Posto}: </td>
                                                    <td style="padding-left: 8px;">#{acessoAut.funcion.localRef}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-5" style="padding-top: 6px">
                                            <table>
                                                <tr>
                                                    <td style="width: 80px; text-align: right; font-weight: bold;">TV: </td>
                                                    <td style="padding-left: 8px;">#{acessoAut.funcion.funcao ne null? acessoAut.funcion.funcao eq 'V'? 'Vigilante': acessoAut.funcion.funcao eq 'M'? 'Motorista': 'Outros':''}</td>
                                                </tr>   
                                            </table>
                                        </div>
                                    </p:panel>
                                </p:panel>
                            </div>
                            <div class="col-md-6" style="padding: 0px 8px 0px 0px; margin-top: 8px">
                                <p:outputLabel value="#{localemsgs.Solicitante}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>

                                <p:inputText value="#{acessoAut.autorizacaoSelecionada.solicitante}" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Solicitante}"></p:inputText>
                            </div>
                            <div class="col-md-6" style="padding: 0px 8px 0px 0px; margin-top: 8px">
                                <p:outputLabel value="#{localemsgs.DeptoDestino}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>

                                <p:inputText value="#{acessoAut.autorizacaoSelecionada.destino}" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Destino}"></p:inputText>
                            </div>
                            <div class="col-md-12" style="padding: 0px 8px 0px 0px; margin-top: 8px">
                                <p:outputLabel value="#{localemsgs.Finalidade}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>

                                <p:inputText value="#{acessoAut.autorizacaoSelecionada.finalidade}" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Finalidade}"></p:inputText>
                            </div>
                            <div class="col-md-12" style="padding: 0px 8px 0px 0px; margin-top: 8px">
                                <p:outputLabel value="#{localemsgs.Obs}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>

                                <p:inputText value="#{acessoAut.autorizacaoSelecionada.obs}"></p:inputText>
                            </div>
                            <div class="col-md-12" style="height: 120px; margin-top: 6px; padding: 0px 8px 0px 0px">
                                <p:outputLabel value="#{localemsgs.AreaSeguranca}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <table style="width: 100%; height: 110px; border: thin solid #DDD">
                                    <tr>
                                        <td>
                                            <div style="height: 110px; overflow: auto">
                                                <p:dataTable id="tabela" 
                                                             var="area" 
                                                             value="#{acessoAut.listaAcessoAreas}" 
                                                             rowKey="#{area.codArea}"
                                                             paginator="false"
                                                             paginatorTemplate="false"
                                                             lazy="true"
                                                             selection="#{acessoAut.acessoAutAreaSelecionado}"
                                                             selectionMode="single"
                                                             emptyMessage="#{localemsgs.SemRegistros}"
                                                             resizableColumns="false"
                                                             style="font-size: 8px; float: left" styleClass="tabela">
                                                    <p:column headerText="#{localemsgs.Codigo}" style="width: 60px" class="text-center">
                                                        <h:outputText value="#{area.codArea}" title="#{area.codArea}" converter="conversor0" class="text-center">
                                                        </h:outputText>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Descricao}">
                                                        <h:outputText value="#{area.descricao}" title="#{area.descricao}" />
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.HrEnt}" class="text-center" style="width: 110px">
                                                        <h:outputText value="#{area.hrEntrada}" title="#{area.hrEntrada}" converter="conversorHora" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.HrSai}" class="text-center" style="width: 110px">
                                                        <h:outputText value="#{area.hrSaida}" title="#{area.hrSaida}" converter="conversorHora" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                                        <h:outputText value="#{area.operador}" title="#{area.operador}" />
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center" style="width: 75px">
                                                        <h:outputText value="#{area.dt_Alter}" title="#{area.dt_Alter}" converter="conversorData" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center" style="width: 75px">
                                                        <h:outputText value="#{area.hr_Alter}" title="#{area.hr_Alter}" converter="conversorHora" class="text-center"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </div>
                                        </td>
                                        <td style="width: 60px; background-color: #FFF; vertical-align: middle;">

                                            <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{acessoAut.novaArea}"
                                                           partialSubmit="true" process="@this"
                                                           update="formAreas:cadastrar msgs">
                                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40" style="display: block; margin-left: 10px;" />
                                            </p:commandLink>
                                            <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{acessoAut.excluirArea}"
                                                           partialSubmit="true" process="@this tabela"
                                                           update="tabela msgs">
                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="40" height="40" style="margin-top:10px; display: block; margin-left: 10px;" />
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <p:panel class="col-md-12" style="background-color:transparent !important; text-align:right; padding:0px 8px 0px 0px !important; margin: 18px 0px 0px 0px !important">
                                <p:commandLink id="cadastro" action="#{acessoAut.salvarAutorizacao}" 
                                               title="#{localemsgs.Cadastrar}" update="main cadastrar msgs" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </p:panel>
                        </p:panel>
                    </p:dialog>
                </h:form>


                <h:form id="formAreas" class="form-inline">
                    <p:dialog widgetVar="dlgAreas"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important">

                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.AreaSeguranca}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; margin-top:-10px !important; z-index:999 !important; height:calc(100% - 20px) !important; padding: 0px !important" class="cadastrar">
                            <div class="col-md-12" style="padding: 0px 8px 0px 0px; margin-top: 8px">
                                <p:outputLabel value="#{localemsgs.Area}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>

                                <p:selectOneMenu id="areas" value="#{acessoAut.area.codArea}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Area}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains">
                                    <f:selectItems value="#{acessoAut.listaCadastroAreas}" var="areas" itemValue="#{areas.codArea}"
                                                   itemLabel="#{areas.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-12" style="padding: 0px 8px 0px 0px; margin-top: 8px">
                                <p:outputLabel value="#{localemsgs.Horario}">
                                    <font style="color:red; font-weight:bold">(*)</font>
                                </p:outputLabel>
                                <table>
                                    <tr>
                                        <td>
                                            <p:selectOneMenu value="#{acessoAut.area.hrEntrada}" editable="true" 
                                                             converter="conversorHora" validator="ValidadorHora"
                                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                                             filter="true" filterMatchMode="contains" style="width: 100%">
                                                <f:selectItems value="#{horas.obterHorario()}" />
                                            </p:selectOneMenu>
                                        </td>
                                        <td style="padding-left: 10px">
                                            <p:selectOneMenu value="#{acessoAut.area.hrSaida}" editable="true" 
                                                             converter="conversorHora" validator="ValidadorHora"
                                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                                             filter="true" filterMatchMode="contains" style="width: 100%">
                                                <f:selectItems value="#{horas.obterHorario()}" />
                                            </p:selectOneMenu>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <p:panel class="col-md-12" style="background-color:transparent !important; text-align:right; padding:0px 8px 0px 0px !important; margin: 18px 0px 0px 0px !important">
                                <p:commandLink action="#{acessoAut.salvarArea}" 
                                               title="#{localemsgs.Cadastrar}" update="formCadastrar:tabela msgs" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </p:panel>
                        </p:panel>
                    </p:dialog>
                </h:form>


                <!--Pesquisa Rápida de clientes-->
                <h:form id="formPesquisar" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisar"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarCliente}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelPesquisaRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{acessoAut.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection">
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.Obs}" itemValue="OBSERVACAO" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{acessoAut.chavePesquisa eq 'NOME'}" value="#{localemsgs.Nome}: "/>
                                        <p:outputLabel for="opcao" rendered="#{acessoAut.chavePesquisa eq 'OBSERVACAO'}" value="#{localemsgs.Obs}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{acessoAut.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-right: 0px !important">
                                <p:commandLink id="botaoPesquisaRapida"
                                               action="#{acessoAut.carregarListaAutorizacoes}"
                                               update=" :main:tabela :msgs"
                                               oncomplete="PF('dlgPesquisar').hide()"
                                               title="#{localemsgs.Pesquisar}" styleClass="btn btn-primary">
                                    <i class="fa fa-search" style="margin-right:8px !important"></i>#{localemsgs.Pesquisar}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    // <![CDATA[
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                    // ]]>
                </script>

                <!--Ordenação Rápida de clientes-->
                <h:form id="formOrdenacaoRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgOrdenacaoRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgOrdenacaoRapida"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_redondo_ordenar.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Ordenacao}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelOrdenacaoRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoesOrdenacao" value="#{localemsgs.OrdenarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoesOrdenacao"
                                        value="#{acessoAut.chaveOrdem}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.RG}" itemValue="RG" />
                                        <f:selectItem itemLabel="#{localemsgs.Sequencia}" itemValue="SEQUENCIA" />
                                    </p:selectOneRadio>
                                </div>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-right: 0px !important">
                                <p:commandLink id="botaoOrdenacaoRapida"
                                               action="#{acessoAut.carregarListaAutorizacoes}"
                                               process="@this,radioOpcoesOrdenacao"
                                               update=" :main:tabela :msgs"
                                               oncomplete="PF('dlgOrdenacaoRapida').hide()"
                                               title="#{localemsgs.Ordenar}" styleClass="btn btn-primary">
                                    <i class="fa fa-sort" style="margin-right:8px !important"></i>#{localemsgs.Ordenar}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelOrdenacaoRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoOrdenacaoRapida').click();
                        }
                    });
                </script>
            </div>
            <ui:insert name="loading" id="loadJava">
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>       


            <script type="text/javascript">
                // <![CDATA[
                function ConfirmarExclusaoAutorizacao() {
                    $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}',
                            '#{localemsgs.ConfirmaExclusao}',
                            '#{localemsgs.Sim}',
                            '#{localemsgs.Nao}',
                            function () {
                                rcExcluirAutorizacao();
                            },
                            null);
                }
                
                function Autorizar() {
                    $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}',
                            '#{localemsgs.ConfirmaAutorizacao}',
                            '#{localemsgs.Sim}',
                            '#{localemsgs.Nao}',
                            function () {
                                rcAutorizar();
                            },
                            null);
                }

                // ]]>
            </script>


            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; top: 6px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: "  /></label>
                                <p:selectBooleanCheckbox value="#{acessoAut.corporativo}" id="chkCorporativo">
                                    <p:ajax update="msgs main:tabela" listener="#{acessoAut.carregarListaAutorizacoes}" />
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="margin-left: 8px">
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.SomentePendentes}: "  /></label>
                                <p:selectBooleanCheckbox value="#{acessoAut.somentePendentes}" id="chkSomentePendentes" style="margin-left: 5px !important;">
                                    <p:ajax update="msgs main:tabela" listener="#{acessoAut.carregarListaAutorizacoes}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>  
    </f:view>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class VeiculosInspec {

    private String Sequencia;
    private String Numero;
    private String Data;
    private String Hora;
    private String MarcaModelo;
    private String KMIni;
    private String imgKMIni;
    private String KMFim;
    private String imgKMFim;
    private String Combustivel;
    private String imgCombustivel;
    private BigDecimal ProblemaLuzes;
    private String RelatoDanos;
    private String imgRelatoDanos;
    private String RelatoProblemas;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String CodPessoaInspec;

    private String nome;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getMarcaModelo() {
        return MarcaModelo;
    }

    public void setMarcaModelo(String MarcaModelo) {
        this.MarcaModelo = MarcaModelo;
    }

    public String getKMIni() {
        return KMIni;
    }

    public void setKMIni(String KMIni) {
        this.KMIni = KMIni;
    }

    public String getImgKMIni() {
        return imgKMIni;
    }

    public void setImgKMIni(String imgKMIni) {
        this.imgKMIni = imgKMIni;
    }

    public String getKMFim() {
        return KMFim;
    }

    public void setKMFim(String KMFim) {
        this.KMFim = KMFim;
    }

    public String getImgKMFim() {
        return imgKMFim;
    }

    public void setImgKMFim(String imgKMFim) {
        this.imgKMFim = imgKMFim;
    }

    public String getCombustivel() {
        return Combustivel;
    }

    public void setCombustivel(String Combustivel) {
        this.Combustivel = Combustivel;
    }

    public String getImgCombustivel() {
        return imgCombustivel;
    }

    public void setImgCombustivel(String imgCombustivel) {
        this.imgCombustivel = imgCombustivel;
    }

    public BigDecimal getProblemaLuzes() {
        return ProblemaLuzes;
    }

    public void setProblemaLuzes(BigDecimal ProblemaLuzes) {
        this.ProblemaLuzes = ProblemaLuzes;
    }

    public void setProblemaLuzes(String ProblemaLuzes) {
        try {
            this.ProblemaLuzes = new BigDecimal(ProblemaLuzes);
        } catch (Exception e) {
            this.ProblemaLuzes = null;
        }
    }

    public String getRelatoDanos() {
        return RelatoDanos;
    }

    public void setRelatoDanos(String RelatoDanos) {
        this.RelatoDanos = RelatoDanos;
    }

    public String getImgRelatoDanos() {
        return imgRelatoDanos;
    }

    public void setImgRelatoDanos(String imgRelatoDanos) {
        this.imgRelatoDanos = imgRelatoDanos;
    }

    public String getRelatoProblemas() {
        return RelatoProblemas;
    }

    public void setRelatoProblemas(String RelatoProblemas) {
        this.RelatoProblemas = RelatoProblemas;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getCodPessoaInspec() {
        return CodPessoaInspec;
    }

    public void setCodPessoaInspec(String CodPessoaInspec) {
        this.CodPessoaInspec = CodPessoaInspec;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}

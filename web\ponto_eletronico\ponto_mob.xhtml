<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiMob}" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <link href="../assets/css/animate.css" rel="stylesheet" type="text/css"/>
            <link type="text/css" href="../assets/css/webPonto.css" rel="stylesheet" />
            <meta name="theme-color" content="#002172" />
            <meta name="msapplication-navbutton-color" content="#002172" />
            <meta name="apple-mobile-web-app-status-bar-style" content="#002172" />
            <style>
                html, body, form{
                    overflow: hidden !important;
                }
            </style>
        </h:head> 
        <h:body id="bodyWebPonto" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{login.carregarDadosWebPonto}" />
                <f:viewAction action="#{mobEW.PersistenciaWebPonto(login.pp)}" />
            </f:metadata>

            <p:growl id="msgs" />

            <h:form id="frmCabecalho" style="height: 60px; width: 100%; padding: 0px; margin: 0px; background-color: #FFF;">
                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 60px; width: 100%; padding: 0px 0px 0px 65px; margin: 0px; background-color: #002172; text-align: left;">
                    <img src="../assets/logos/SatMobEw.png" height="42" style="border-radius: 50%; left: 10px; top: 7px; position: absolute" />
                    <i class="fa fa-bars" ref="BarsMenu" style="position: absolute; font-size: 16pt; color: #FFF; right: 27px; top: 1px; cursor: pointer; padding: 14px 8px 14px 8px !important; width: 50px; text-align: center"></i>
                    <label class="DescricaoPonto" style="max-width: calc(100% - 100px) !important; font-size: 13pt !important; height: 21px !important; padding: 0px 5px 0px 0px !important; margin-top: 5px !important;">#{login.webPontoNomeGuer}</label>
                    <label class="DescricaoPonto">#{login.webPontoMatr} - #{login.webPontoFuncao}</label>
                    <label class="DescricaoPonto">#{login.webPontoEscala}</label>
                </div>
            </h:form>

            <h:form id="frmPonto" style="height: calc(100% - 60px); width: 100%; padding: 10px; margin: 0px; background-color: #EEE;">
                <h:inputHidden id="txtLatitude" value="#{mobEW.webPontoLatitude}"></h:inputHidden>
                <h:inputHidden id="txtLongitude" value="#{mobEW.webPontoLongitude}"></h:inputHidden>

                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 80px; background-color: #FFF; box-shadow: 2px 2px 3px #CCC; border: thin solid #DDD; border-radius: 4px;text-align: center !important; padding: 10px !important">
                    <table style="width: 100%">
                        <tr>
                            <td style="width: 10px !important; white-space: nowrap; padding-right: 6px;"><label class="DescricaoPonto" style="height: 16px !important; color: #000 !important; margin-right: 10px; font-weight: 500 !Important; font-size: 10pt !important">#{localemsgs.Local}: </label></td>
                            <td><label class="DescricaoLocal" style="height: 16px !important;color: #000 !important; font-size: 10pt !important">#{login.webPontoLocal}</label></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td><label class="DescricaoLocal" style="height: 16px !important;color: #000 !important; font-size: 10pt !important">#{login.webPontoEnde}</label></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td><label class="DescricaoLocal" style="height: 16px !important;color: #000 !important; font-size: 10pt !important">#{login.webPontoDescricao}</label></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100% - 167px); margin-top: 10px; margin-bottom: 12px; background-color: #FFF; box-shadow: 2px 2px 3px #CCC; border: thin solid #DDD; border-radius: 4px; padding: 3px !important;">
                    <!--<div id="mapGoogle" style="min-width:100% !important;width:100% !important;max-width:100% !important;"></div>-->
                    <div id="mapOpen" style="min-width:100% !important;height:100% !important;">

                    </div>
                </div>
                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 60px; border-radius: 4px; padding: 0px;">
                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px 5px 10px 0px !important;">
                        <a id="btRegistrarPonto" class="btn btn-primary BotaoPontoMob" STYLE="padding-top: 18px; background-color: #5472D2 !important;">#{localemsgs.BaterPonto}</a>
                    </div>    
                    <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px 0px 10px 5px !important; display: none">
                        <a href="inspecao_mob.xhtml?faces-redirect=true" id="btInspecoes" class="btn btn-primary BotaoPontoMob" STYLE="padding-top: 18px; background-color: #5472D2 !important;">#{localemsgs.Inspecao}</a>
                    </div>    
                </div>

                <input type="file" accept="image/*" capture="user" id="uplCamera" style="display: none" onchange="encodeImageFileAsURL(this)" />

                <input type="hidden" id="txtBase64" />

                <p:remoteCommand name="rcBaterPonto" partialSubmit="true" 
                                 process="@this,txtLatitude,txtLongitude" 
                                 update="msgs" 
                                 actionListener="#{mobEW.baterPonto}" />       


                <script type="text/javascript">
                    // <![CDATA[
                    var map;
                    var Latitude = ' -15.7801';
                    var Longitude = '-47.9292';
                    var optionsMap;
                    var IntervalRecarrega;
                    var options = {
                        enableHighAccuracy: true,
                        timeout: 5000,
                        maximumAge: 0
                    };

                    $(document).ready(function () {
                        setTimeout(function () {
                            ConsultarLocalizacao();
                        }, 1500);
                    })
                            .on('click', '#btRegistrarPonto', function () {
                                if ($('[id*="txtLatitude"]').val().trim() === '' || $('[id*="txtLongitude"]').val().trim() === '') {
                                    if (navigator.geolocation)
                                        navigator.geolocation.getCurrentPosition(SucessoCapturaPosicao, ErroCapturaPosicao, options);

                                    if ($('[id*="txtLatitude"]').val().trim() !== '' || $('[id*="txtLongitude"]').val().trim() !== '')
                                        $('#uplCamera').click();
                                } else
                                    $('#uplCamera').click();
                            })
                            ;

                    function initMap() {
                        let Altura = $('#mapGoogle').parent('div').height() - 0;
                        $('#mapGoogle').css('min-height', Altura + 'px');
                        let Zoom = $('[id*="txtLatitude"]').val().trim() !== '' && $('[id*="txtLongitude"]').val().trim() !== '' ? 14 : 4;
                        // Configurações Mapa

                        optionsMap = {
                            zoom: Zoom,
                            center: new google.maps.LatLng(Latitude, Longitude),
                            mapTypeId: google.maps.MapTypeId.ROADMAP
                        };

                        // Criar objeto de Mapa
                        map = new google.maps.Map(document.getElementById("mapGoogle"), optionsMap);

                        if ($('[id*="txtLatitude"]').val().trim() !== '' &&
                                $('[id*="txtLongitude"]').val().trim() !== '') {
                            var Marker = new google.maps.Marker({
                                position: new google.maps.LatLng(Latitude, Longitude),
                                map: map,
                                icon: 'https://mobile.sasw.com.br:9091/satmobile/pins/icone_mobile_supervisor.png'
                            });
                        }
                    }

                    function encodeImageFileAsURL(element) {
                        var dataurl = null;
                        var file = element.files[0];
                        var img = document.createElement("img");
                        var reader = new FileReader();

                        reader.onload = function (e)
                        {
                            img.src = e.target.result;

                            img.onload = function () {
                                var canvas = document.createElement("canvas");
                                var ctx = canvas.getContext("2d");
                                ctx.drawImage(img, 0, 0);

                                var MAX_WIDTH = 800;
                                var MAX_HEIGHT = 600;
                                var width = img.width;
                                var height = img.height;

                                if (width > height) {
                                    if (width > MAX_WIDTH) {
                                        height *= MAX_WIDTH / width;
                                        width = MAX_WIDTH;
                                    }
                                } else {
                                    if (height > MAX_HEIGHT) {
                                        width *= MAX_HEIGHT / height;
                                        height = MAX_HEIGHT;
                                    }
                                }
                                canvas.width = width;
                                canvas.height = height;
                                var ctx = canvas.getContext("2d");
                                ctx.drawImage(img, 0, 0, width, height);

                                dataurl = canvas.toDataURL("image/jpeg");
                                //alert(ReplaceAll(dataurl, 'data:image/jpeg;base64,', ''));
                                $('#txtBase64').val(ReplaceAll(dataurl, 'data:image/jpeg;base64,', ''));
                                rcBaterPonto();
                            }
                        }

                        reader.readAsDataURL(file);
                    }

                    function ConsultarLocalizacao() {
                        if (navigator.geolocation) {
                            navigator.geolocation.getCurrentPosition(function (position) {
                                var lat = position.coords.latitude;
                                var lng = position.coords.longitude;

                                SucessoCapturaPosicao(lat, lng);
                            }, function (error) {
                                setTimeout(function () {
                                    ErroCapturaPosicao(error);
                                }, 1000);
                            });
                        } else {
                            setTimeout(function () {
                                ErroCapturaPosicao(error);
                            }, 1000);
                        }

                    }

                    function ErroCapturaPosicao(error) {
                        $('[id*="txtLatitude"]').val('');
                        $('[id*="txtLongitude"]').val('');

                        //initMap();
                        $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.MensagemLigarGPS}');

                        IntervalRecarrega = setInterval(function () {
                            clearInterval(IntervalRecarrega);
                            ConsultarLocalizacao();
                        }, 2000);

                        $('#ifrMapa').remove();
                    }

                    function SucessoCapturaPosicao(lat, lng) {
                        Latitude = lat;
                        Longitude = lng;
                        SucessoCapturaPosicaoEscreve(lat, lng);
                    }

                    function SucessoCapturaPosicaoInterval() {
                        Latitude = position.coords.latitude;
                        Longitude = position.coords.longitude;
                        SucessoCapturaPosicaoEscreve(Latitude, Longitude);
                    }

                    function SucessoCapturaPosicaoEscreve(lat, lng) {
                        $('[id*="txtLatitude"]').val(lat);
                        $('[id*="txtLongitude"]').val(lng);

                        $('#mapOpen').html('<iframe id="ifrMapa" src="https://mobile.sasw.com.br/SatMobWeb/tmpMapaFree.html?lat=' + lat + '&lon=' + lng + '&marcador=icone_mobile_supervisor.png" style="width: 100%; height: 100%; border: none; margin: 0px; paddin: 0px;"></iframe>');
                        //initMap();
                    }

                    function ExecBatidaPonto(inURL, inParams) {
                        var Params = inParams.split('|**|');

                        $.ajax({
                            url: inURL,
                            method: 'POST',
                            data: {
                                "codpessoa": Params[0],
                                "senha": Params[1],
                                "param": Params[2],
                                "matricula": Params[3],
                                "codfil": Params[4],
                                "data": Params[5],
                                "hora": Params[6],
                                "operador": Params[7],
                                "lat": Params[8],
                                "long": Params[9],
                                "secao": Params[10],
                                "dataAtual": Params[11],
                                "horaAtual": Params[12],
                                "idioma": Params[13],
                                "imagem": $('#txtBase64').val()
                            }
                        });

                        $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.PontoRegistrado}', function () {
                            top.location.href = 'ponto_mob_historico.xhtml';
                        });

                        /*.done(function () {
                         $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.DadosSalvosSucesso}', function(){
                         top.location.href='ponto_mob_historico.xhtml';
                         });
                         })
                         .fail(function (err1, err2) {
                         $.MsgBoxVermelhorOk('#{localemsgs.Aviso}', '#{localemsgs.ErroSalvarBD}');
                         });*/
                    }

                    // ]]>
                </script>
            </h:form>
        </h:body>
    </f:view>
</html>

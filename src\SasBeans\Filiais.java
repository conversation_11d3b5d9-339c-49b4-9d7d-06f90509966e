package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Filiais {

    private BigDecimal CodFil;
    private String Descricao;
    private String RazaoSocial;
    private String Endereco;
    private String Bairro;
    private String Cidade;
    private String UF;
    private String CEP;
    private String Fone;
    private String Fone2;
    private String Fax;
    private String Contato;
    private String Email;
    private String EmailCml;
    private String TipoPessoa;
    private String CNPJ;
    private String InscEst;
    private String InscMunic;
    private BigDecimal ISS;
    private int Praca;
    private BigDecimal ICMS;
    private int Praca2;
    private BigDecimal CodFilFp;
    private String CNAE;
    private String CAGED1Decl;
    private String CAGEDAltDados;
    private String PorteEmp;
    private String CodRecolh;
    private String CodGPS;
    private String FPAS;
    private String CodOutrasEnt;
    private BigDecimal RAT;
    private BigDecimal FAP;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public void setCodFil(BigDecimal CodFil) {
        try {
            this.CodFil = CodFil;
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getRazaoSocial() {
        return RazaoSocial;
    }

    public void setRazaoSocial(String RazaoSocial) {
        this.RazaoSocial = RazaoSocial;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getFone() {
        return Fone;
    }

    public void setFone(String Fone) {
        this.Fone = Fone;
    }

    public String getFone2() {
        return Fone2;
    }

    public void setFone2(String Fone2) {
        this.Fone2 = Fone2;
    }

    public String getFax() {
        return Fax;
    }

    public void setFax(String Fax) {
        this.Fax = Fax;
    }

    public String getContato() {
        return Contato;
    }

    public void setContato(String Contato) {
        this.Contato = Contato;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getEmailCml() {
        return EmailCml;
    }

    public void setEmailCml(String EmailCml) {
        this.EmailCml = EmailCml;
    }

    public String getTipoPessoa() {
        return TipoPessoa;
    }

    public void setTipoPessoa(String TipoPessoa) {
        this.TipoPessoa = TipoPessoa;
    }

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getInscEst() {
        return InscEst;
    }

    public void setInscEst(String InscEst) {
        this.InscEst = InscEst;
    }

    public String getInscMunic() {
        return InscMunic;
    }

    public void setInscMunic(String InscMunic) {
        this.InscMunic = InscMunic;
    }

    public BigDecimal getISS() {
        return ISS;
    }

    public void setISS(String ISS) {
        try {
            this.ISS = new BigDecimal(ISS);
        } catch (Exception e) {
            this.ISS = new BigDecimal("0");
        }
    }

    public int getPraca() {
        return Praca;
    }

    public void setPraca(int Praca) {
        this.Praca = Praca;
    }

    public BigDecimal getICMS() {
        return ICMS;
    }

    public void setICMS(String ICMS) {
        try {
            this.ICMS = new BigDecimal(ICMS);
        } catch (Exception e) {
            this.ICMS = new BigDecimal("0");
        }
    }

    public int getPraca2() {
        return Praca2;
    }

    public void setPraca2(int Praca2) {
        this.Praca2 = Praca2;
    }

    public BigDecimal getCodFilFp() {
        return CodFilFp;
    }

    public void setCodFilFp(String CodFilFp) {
        try {
            this.CodFilFp = new BigDecimal(CodFilFp);
        } catch (Exception e) {
            this.CodFilFp = new BigDecimal("0");
        }
    }

    public String getCNAE() {
        return CNAE;
    }

    public void setCNAE(String CNAE) {
        this.CNAE = CNAE;
    }

    public String getCAGED1Decl() {
        return CAGED1Decl;
    }

    public void setCAGED1Decl(String CAGED1Decl) {
        this.CAGED1Decl = CAGED1Decl;
    }

    public String getCAGEDAltDados() {
        return CAGEDAltDados;
    }

    public void setCAGEDAltDados(String CAGEDAltDados) {
        this.CAGEDAltDados = CAGEDAltDados;
    }

    public String getPorteEmp() {
        return PorteEmp;
    }

    public void setPorteEmp(String PorteEmp) {
        this.PorteEmp = PorteEmp;
    }

    public String getCodRecolh() {
        return CodRecolh;
    }

    public void setCodRecolh(String CodRecolh) {
        this.CodRecolh = CodRecolh;
    }

    public String getCodGPS() {
        return CodGPS;
    }

    public void setCodGPS(String CodGPS) {
        this.CodGPS = CodGPS;
    }

    public String getFPAS() {
        return FPAS;
    }

    public void setFPAS(String FPAS) {
        this.FPAS = FPAS;
    }

    public String getCodOutrasEnt() {
        return CodOutrasEnt;
    }

    public void setCodOutrasEnt(String CodOutrasEnt) {
        this.CodOutrasEnt = CodOutrasEnt;
    }

    public BigDecimal getRAT() {
        return RAT;
    }

    public void setRAT(String RAT) {
        try {
            this.RAT = new BigDecimal(RAT);
        } catch (Exception e) {
            this.RAT = new BigDecimal("0");
        }
    }

    public BigDecimal getFAP() {
        return FAP;
    }

    public void setFAP(String FAP) {
        try {
            this.FAP = new BigDecimal(FAP);
        } catch (Exception e) {
            this.FAP = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

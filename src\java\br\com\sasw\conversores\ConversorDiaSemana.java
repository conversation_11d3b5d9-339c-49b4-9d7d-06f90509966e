/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;
import java.time.DayOfWeek;
import java.time.format.TextStyle;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorDiaSemana")
public class ConversorDiaSemana implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            // Converte Enumeração onde Domingo = 1 (conforme banco)
            // na enumeração do Java, que começa em Segunda-Feira = 1
            Integer numero = Math.floorMod((Integer.parseInt(value.toString()) - 2), 7) + 1;
            DayOfWeek dayOfWeek = DayOfWeek.of(numero);
            String name = dayOfWeek.getDisplayName(TextStyle.FULL, FacesContext.getCurrentInstance().getViewRoot().getLocale());
            return name;
        } catch (NumberFormatException e) {
            return value.toString();
        }
    }

}

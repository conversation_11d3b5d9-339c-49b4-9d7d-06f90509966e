/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.operacoes;

import Arquivo.ArquivoLog;
import Controller.Login.LoginSatMobWeb;
import Controller.Pessoas.PessoasSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Escala;
import SasBeans.Pessoa;
import SasBeans.Rotas;
import SasBeans.Rt_Escala;
import SasBeans.Rt_Perc;
import SasBeans.SasPWFill;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.lazydatamodels.RotasLazyList;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "rota")
@ViewScoped
public class RotasSupervisaoMB implements Serializable {

    private Rotas novaRota, rotaSelecionada;
    private List<Rotas> listaRotasSup;
    private List<Rt_Perc> trajetos;
    private List<Pessoa> listaPessoa;
    private Pessoa pessoa;
    private Persistencia persistencia;

    private BigDecimal sequencia, codpessoa;
    private final String operador, banco;
    private String HsInterv, HrChe, filialDesc, dataTela, dataRota, caminho, log, codfil;
    private ArquivoLog logerro;
    private static Date ultimoDia;
    private Clientes cliente;
    private boolean mostrarFiliais, excl, limparFiltros, senha, exclFlag;
    private final LoginSatMobWeb loginSatMobWeb;
    private final PessoasSatMobWeb pessoasatmobweb;
    private SasPWFill filial;
    private final RotasSatWeb rotassatweb;
    private int flag, total;
    private Rt_Escala rt_Escala;
    private Escala escala;
    private Calendar hora1, hora2, hora3, hora4;
    private LazyDataModel<Rotas> rotas = null;
    private Map filters;

    public RotasSupervisaoMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        trajetos = new ArrayList<>();

        dataTela = DataAtual.getDataAtual("SQL");
        ultimoDiadoMes();
        pessoasatmobweb = new PessoasSatMobWeb();
        rotassatweb = new RotasSatWeb();

        rt_Escala = new Rt_Escala();
        escala = new Escala();
        hora1 = Calendar.getInstance();
        hora2 = Calendar.getInstance();
        hora3 = Calendar.getInstance();
        hora4 = Calendar.getInstance();
        excl = true;
        exclFlag = false;
        mostrarFiliais = false;
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        loginSatMobWeb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.filters = new HashMap();
            this.filters.put(" Rotas.Flag_excl <> ? ", "*");
            this.filters.put(" Rotas.CodFil = ? ", this.codfil);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
            this.total = this.rotassatweb.Contagem(this.filters, this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void MostrarFiliais() {
        if (this.mostrarFiliais) {
            this.filters.replace(" Rotas.CodFil = ? ", "");
        } else {
            this.filters.replace(" Rotas.CodFil = ? ", this.codfil);
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllRotas();
        dt.setFirst(0);
    }

    public void SomenteAtivos() {
        if (this.excl) {
            this.filters.replace(" Rotas.Flag_excl <> ? ", "*");
        } else {
            this.filters.replace(" Rotas.Flag_excl <> ? ", "");
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllRotas();
        dt.setFirst(0);
    }

    public void LimparFiltros() {
        this.filters.replace(" Rotas.Flag_excl <> ? ", "*");
        this.filters.replace(" Rotas.CodFil = ? ", this.codfil);
        this.filters.replace(" Rotas.Data = ? ", this.dataTela);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        this.mostrarFiliais = false;
        this.excl = true;
        this.limparFiltros = false;
        dt.setFilters(this.filters);
        getAllRotas();
        dt.setFirst(0);
    }

    public void SelecionarData(SelectEvent data) {
        Date date = (Date) data.getObject();
        this.dataTela = Date2String(date);
        this.filters.put(" Rotas.Data = ? ", this.dataTela);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllRotas();
        dt.setFirst(0);
    }

    /**
     * Tranforma Date em String
     *
     * @param date Data a ser formatada
     * @return String no formato yyyyMMdd
     */
    public String Date2String(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * Procura o último dia do mês atual
     */
    public static void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            RotasSupervisaoMB.ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void Cadastrar() {
        try {
            this.pessoa = this.pessoasatmobweb.ListaPessoa(this.pessoa, this.persistencia).get(0);
            VerificaHoraIntervalo();
            this.novaRota.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novaRota.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novaRota.setDt_Alter(LocalDate.now());
            this.novaRota.setNome(this.novaRota.getNome().toUpperCase());
            this.novaRota.setCodFil(this.filial.getCodfilAc());

            //rt_Escala
            this.rt_Escala.setMatr(this.pessoa.getMatr());

            //Escala
            this.escala.setRota(this.novaRota.getRota());
            this.escala.setCodFil(this.filial.getCodfilAc());
            this.escala.setMatrChe(this.pessoa.getMatr().toPlainString());
            this.escala.setCodPessoaSup(this.pessoa.getCodigo().toPlainString());
            this.escala.setHsInterv(this.HsInterv);

            this.rotassatweb.criarRotaSupervisao(this.novaRota, this.rt_Escala, this.escala, this.persistencia);

            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            this.trajetos = new ArrayList<>();
            SelecionaUltimaRota();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Editar() {
        try {
            this.rt_Escala = new Rt_Escala();
            this.escala = new Escala();
            this.pessoa = this.pessoasatmobweb.ListaPessoa(this.pessoa, this.persistencia).get(0);
            VerificaHoraIntervalo();
            //rt_Escala
            this.rt_Escala.setMatr(this.pessoa.getMatr());

            //Escala
            this.escala.setRota(this.novaRota.getRota());
            this.escala.setCodFil(this.novaRota.getCodFil().toPlainString());
            this.escala.setMatrChe(this.pessoa.getMatr().toPlainString());
            this.escala.setCodPessoaSup(this.pessoa.getCodigo().toPlainString());
            this.escala.setHsInterv(this.HsInterv);

            this.novaRota.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novaRota.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novaRota.setDt_Alter(LocalDate.now());

            this.rotassatweb.atualizarRotaSupervisao(this.novaRota, this.rt_Escala, this.escala, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Excluir() {
        try {
            if (null == this.rotaSelecionada) {
                throw new Exception("SelecioneRota");
            }
            this.novaRota = this.rotaSelecionada;
            this.novaRota.setOperFech(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.novaRota.setDt_Fech(LocalDate.now());
            this.novaRota.setHr_Fech(DataAtual.getDataAtual("HORA"));
            this.rotassatweb.excluirRota(this.novaRota, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void NovoRota() {
        this.rotaSelecionada = new Rotas();
        this.novaRota = new Rotas();
        this.novaRota.setTpVeic("S");
        this.novaRota.setHrIntIni("00:00");
        this.novaRota.setHrIntFim("00:00");
        this.novaRota.setObservacao("");
        this.novaRota.setHrLargada("08:00");
        this.novaRota.setHrChegada("18:00");
        this.novaRota.setHsTotal("10.00");
        this.flag = 1;
        try {
            this.filial = this.loginSatMobWeb.BuscaFilial(this.codfil, this.codpessoa, this.persistencia);
            this.novaRota.setCodFil(this.filial.getCodfilAc());
            this.novaRota.setRota(this.rotassatweb.MaxRota(this.codfil, String2LocalDate2(this.dataTela), this.persistencia));

            if (this.novaRota.getRota().equals("001")) {
                this.novaRota.setRota("701");
            } else {
                this.novaRota.setRota(this.rotassatweb.MaxRota(this.codfil, String2LocalDate2(this.dataTela), this.persistencia));
            }
            this.novaRota.setData(this.dataTela);
            this.pessoa = new Pessoa();
            PrimeFaces.current().resetInputs("cadastroRota");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void SelecionarFilial(SelectEvent event) {
        try {
            this.filial = (SasPWFill) event.getObject();
            this.novaRota.setCodFil(this.filial.getCodfilAc());
            this.novaRota.setRota(this.rotassatweb.MaxRota(this.filial.getCodfilAc(), String2LocalDate2(this.dataTela), this.persistencia));

            if (this.novaRota.getRota().equals("001")) {
                this.novaRota.setRota("701");
            } else {
                this.novaRota.setRota(this.rotassatweb.MaxRota(this.filial.getCodfilAc(), String2LocalDate2(this.dataTela), this.persistencia));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void RotaAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllRotas();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void RotaPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllRotas();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void onRowSelect(SelectEvent event) {
        this.rotaSelecionada = (Rotas) event.getObject();
        buttonAction(null);
    }

    public void buttonAction(ActionEvent actionEvent) {
        if (null == this.rotaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneRota"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                //Rotas, Escala e Rt_Escala
                this.novaRota = this.rotaSelecionada;
                this.escala = this.rotassatweb.selecionaEscala(this.novaRota.getSequencia(), this.persistencia);
                this.rt_Escala = this.rotassatweb.SelecionaRt_Escala(this.novaRota.getSequencia(), this.persistencia);
                this.filial = this.loginSatMobWeb.BuscaFilial(this.novaRota.getCodFil().toString(), this.codpessoa, this.persistencia);
                //Pessoa seleionada para a supervisão
                if (this.novaRota.getFlag_Excl().equals("*")) {
                    this.pessoa = new Pessoa();
                    this.pessoa.setNome(Messages.getMessageS("escalaExcluida"));
                    this.pessoa.setCodigo("0");
                    this.pessoa.setMatr("0");
                    //this.pessoa = this.pessoasatmobweb.ListaPessoa(this.pessoa, this.persistencia).get(0);
                } else {
                    this.pessoa = new Pessoa();
                    this.pessoa.setCodigo(this.escala.getCodPessoaSup());
                    this.pessoa = this.pessoasatmobweb.ListaPessoa(this.pessoa, this.persistencia).get(0);
                }

                this.dataRota = this.novaRota.getData();
                if (this.novaRota.getFlag_Excl().isEmpty()) {
                    this.exclFlag = false;
                } else {
                    this.exclFlag = true;
                }

                this.trajetos = this.rotassatweb.listarTrajetos(this.novaRota.getSequencia(), this.exclFlag, this.persistencia);
                this.flag = 2;

                PrimeFaces.current().resetInputs("cadastroRota");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void ListaTrajetos() {
        try {
            this.trajetos = this.rotassatweb.listarTrajetos(this.novaRota.getSequencia(), this.exclFlag, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void CalculaHorasTrabalhadas() {
        try {
            VerificaHoraIntervalo();
        } catch (ParseException e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("HoraInvalida"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Verifica se a hora do intervalo ultrapassa a hora de trabalho
     *
     * @throws java.lang.Exception
     */
    public void VerificaHoraIntervalo() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        sdf.setLenient(true);

        LocalDate dia = LocalDate.parse(this.novaRota.getData(), DateTimeFormatter.ofPattern("yyyyMMdd"));

        this.hora1.setTime(sdf.parse(this.novaRota.getHrLargada()));
        this.hora1.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora1.get(Calendar.HOUR_OF_DAY), this.hora1.get(Calendar.MINUTE));
        this.hora2.setTime(sdf.parse(this.novaRota.getHrIntIni()));
        this.hora2.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
        this.hora3.setTime(sdf.parse(this.novaRota.getHrIntFim()));
        this.hora3.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
        this.hora4.setTime(sdf.parse(this.novaRota.getHrChegada()));
        this.hora4.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));

        long diferenca, minutos;
        float tot;

        if (!this.novaRota.getHrIntFim().equals(this.novaRota.getHrIntIni())) {
            if (this.hora1.before(this.hora4)) {
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                this.hora4.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            this.novaRota.setHsTotal(Float.toString(tot / 60));
            if (this.hora2.before(this.hora1)) {
                LocalDate dia3 = dia.plusDays(1);
                this.hora2.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
            }
            if (this.hora2.after(this.hora4)) {
                throw new Exception("IntervaloInvalido");
            }
            if (this.hora3.before(this.hora1)) {
                LocalDate dia3 = dia.plusDays(1);
                this.hora3.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
            }
            if (this.hora3.after(this.hora4)) {
                throw new Exception("IntervaloInvalido");
            }
            if (this.hora2.before(this.hora3)) {
                diferenca = this.hora3.getTime().getTime() - this.hora2.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else if (this.hora2.after(this.hora3)) {
                throw new Exception("IntervaloInvalido");
            }
            tot = tot - minutos;
            if (tot > 0) {
                this.novaRota.setHsTotal(Float.toString(tot / 60));
                this.HsInterv = Float.toString(minutos / 60);
            }
            if (tot < 0) {
                throw new Exception("IntervaloErrado");
            }
            hora1.add(Calendar.MINUTE, -15);
            HrChe = Integer.toString(hora1.get(Calendar.HOUR_OF_DAY)) + ":" + Integer.toString(hora1.get(Calendar.MINUTE));
            CalculaHrChe(HrChe);
        } else {
            if (this.hora1.before(this.hora4)) {
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                this.hora4.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
                diferenca = this.hora4.getTime().getTime() - this.hora1.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            this.novaRota.setHsTotal(Float.toString(tot / 60));
            hora1.add(Calendar.MINUTE, -15);
            HrChe = Integer.toString(hora1.get(Calendar.HOUR_OF_DAY)) + ":" + Integer.toString(hora1.get(Calendar.MINUTE));
            CalculaHrChe(HrChe);
        }
    }

    /**
     * Calcula a hora de chegada do supervisor escalado para 15 minutos antes do
     * início da escala; 15 minutos antes como padrão podendo seer modificada
     * pelo usuário
     *
     * @param hrChe
     */
    private void CalculaHrChe(String hrChe) {
        String[] split = hrChe.split(":");
        if (split[0].length() == 1) {
            split[0] = "0" + split[0];
            hrChe = split[0] + ":" + split[1];
        }
        if (split[1].length() == 1) {
            split[1] = "0" + split[1];
            hrChe = split[0] + ":" + split[1];
        }
        this.escala.setHrChe(hrChe);
    }

    /**
     * Busca uma lista de pessoa para ser supervisor
     *
     * @param query String para comparar na lista de pessoa
     * @return Lista de pessoa
     */
    public List<Pessoa> BuscarPessoas(String query) {
        this.listaPessoa = new ArrayList<>();
        try {
            List<Pessoa> retorno = pessoasatmobweb.ListaPessoaQuery(query, persistencia);
            for (Pessoa p : retorno) {
                if (p.getNome().toUpperCase().contains(query.toUpperCase())
                        && p.getSituacao().equals("F")) {
                    listaPessoa.add(p);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return listaPessoa;
    }

    /**
     * Seleciona o supervisor da rota
     *
     * @param event Evento no qual retorna o código de Pessoa
     */
    public void SelecionarSupervisor(SelectEvent event) {
        try {
            Pessoa p = ((Pessoa) event.getObject());
            this.pessoa = pessoasatmobweb.ListaPessoa(p, this.persistencia).get(0);
            this.novaRota.setNome(this.pessoa.getNome());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void PesquisaFilial() {
        List<SasPWFill> list = new ArrayList<>();
        try {
            for (SasPWFill sasPWFill : list) {
                if (sasPWFill.getDescricao().equals(this.filial.getDescricao())) {
                    this.filial.setCodFil(sasPWFill.getCodFil());
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Transforma String para LocalDate no formato yyyyMMdd
     *
     * @param data Data em String
     * @return Data em String
     */
    private LocalDate String2LocalDate2(String data) {
        //String yyyyMMdd

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate date = LocalDate.parse(data, formatter);

        return date;
    }

    /**
     * Transforma Localdate para String no formato yyyy-MM-dd
     *
     * @param date Data em LocalDate
     * @return Data em LocalDate
     */
    public String LocalDate2String(LocalDate date) {
        //LocalDate yyyy-MM-dd
        String data = date.toString();
        return data.substring(0, 4) + data.substring(5, 7) + data.substring(8, 10);
    }

    public void SelecionaUltimaRota() {
        try {
            this.rotaSelecionada = this.novaRota;

            this.flag = 2;

            PrimeFaces.current().ajax().update("cadastroRota");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            PrimeFaces.current().ajax().update("cadastroTrajeto");
            PrimeFaces.current().executeScript("PF('dlgCadastrarTrajetos').show();");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<Rotas> getAllRotas() {
        if (this.rotas == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.put(" Rotas.Flag_excl <> ? ", "*");
            this.filters.put(" Rotas.CodFil = ? ", this.codfil);
            this.filters.put(" Rotas.Data = ? ", this.dataTela);
            dt.setFilters(this.filters);
            this.rotas = new RotasLazyList(this.persistencia, this.codpessoa);
        }
        try {
            this.total = this.rotassatweb.Contagem(this.filters, this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.rotas;
    }

    public Rotas getRotaSelecionada() {
        return rotaSelecionada;
    }

    public void setRotaSelecionada(Rotas rotaSelecionada) {
        this.rotaSelecionada = rotaSelecionada;
    }

    public List<Rotas> getListaRotasSup() {
        return listaRotasSup;
    }

    public void setListaRotasSup(List<Rotas> listaRotasSup) {
        this.listaRotasSup = listaRotasSup;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Clientes getCliente() {
        return cliente;
    }

    public void setCliente(Clientes cliente) {
        this.cliente = cliente;
    }

    public Rotas getNovaRota() {
        return novaRota;
    }

    public void setNovaRota(Rotas novaRota) {
        this.novaRota = novaRota;
    }

    public BigDecimal getSequencia() {
        return sequencia;
    }

    public void setSequencia(BigDecimal sequencia) {
        this.sequencia = sequencia;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public boolean isSenha() {
        return senha;
    }

    public void setSenha(boolean senha) {
        this.senha = senha;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public boolean isExcl() {
        return excl;
    }

    public void setExcl(boolean excl) {
        this.excl = excl;
    }

    public boolean isLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public List<Rt_Perc> getTrajetos() {
        return trajetos;
    }

    public void setTrajetos(List<Rt_Perc> trajetos) {
        this.trajetos = trajetos;
    }

    public String getDataRota() {
        return dataRota;
    }

    public void setDataRota(String dataRota) {
        this.dataRota = dataRota;
    }

    public boolean isExclFlag() {
        return exclFlag;
    }

    public void setExclFlag(boolean exclFlag) {
        this.exclFlag = exclFlag;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public LazyDataModel<Rotas> getRotas() {
        return rotas;
    }

    public void setRotas(LazyDataModel<Rotas> rotas) {
        this.rotas = rotas;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}

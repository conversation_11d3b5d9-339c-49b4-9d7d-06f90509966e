/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.cadastros;

import Arquivo.ArquivoLog;
import Controller.Filiais.FiliaisSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.lazydatamodels.FiliaisLazyList;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "filiaisMB")
@ViewScoped
public class FiliaisMB implements Serializable {

    private List<Filiais> listaFiliais;
    private Filiais filialSelecionada, novaFilial, filialTitulo;
    private Persistencia persistencia;
    private final FiliaisSatMobWeb filiaissatmobweb;
    private BigDecimal codPessoa;
    private String codFil, banco, operador, nomeFilial, caminho, log, filial, dataTela;
    private ArquivoLog logerro;
    private int flag, total;
    private Boolean limparFiltros;
    private LazyDataModel<Filiais> filiais = null;
    private Map filters;
    private boolean eFilial, eDesc, eRazao, eOperador, eHrAlter, eDtAlter;
    private final RotasSatWeb rotassatweb;
    private final String CODFIL = "codfil = ? ",
            DESCRICAO = "descricao LIKE ? ",
            RAZAOSOCIAL = "razaosocial LIKE ? ";
    private String chavePesquisa = "CODFIL", valorPesquisa;

    public FiliaisMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        filiaissatmobweb = new FiliaisSatMobWeb();
        novaFilial = new Filiais();
        limparFiltros = false;
        log = new String();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
        eFilial = true;
        eDesc = true;
        eRazao = true;
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");
    }

    public Filiais getFilialTitulo() {
        return filialTitulo;
    }

    public void setFilialTitulo(Filiais filialTitulo) {
        this.filialTitulo = filialTitulo;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public void Persistencias(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.filters = new HashMap();
            this.filters.put(CODFIL, "");
            this.filters.put(DESCRICAO, "");
            this.filters.put(RAZAOSOCIAL, "");
            this.total = this.filiaissatmobweb.Contagem(this.filters, this.persistencia);
            this.filialTitulo = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void dblSelect(SelectEvent event) {
        this.novaFilial = (Filiais) event.getObject();
        buttonAction(null);
    }

    public void Novo() {
        this.flag = 1;
        this.novaFilial = new Filiais();
        this.filial = null;
    }

    public void Cadastrar() {
        try {
            this.novaFilial.setCodFil(this.filial);
            this.novaFilial.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novaFilial.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novaFilial.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.novaFilial = (Filiais) FuncoesString.removeAcentoObjeto(this.novaFilial);
            this.filiaissatmobweb.InserirFilial(this.novaFilial, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Editar() {
        try {
            this.filialSelecionada.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.filialSelecionada.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.filialSelecionada.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.filialSelecionada = (Filiais) FuncoesString.removeAcentoObjeto(this.filialSelecionada);
            this.filiaissatmobweb.GravarFilial(this.filialSelecionada, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void buttonAction(ActionEvent actionEvent) {
        if (null == this.filialSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneFilial"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.novaFilial = this.filialSelecionada;
                this.filial = FuncoesString.PreencheEsquerda(this.novaFilial.getCodFil().toPlainString(), 6, "0");
                this.flag = 2;
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void onRowSelect(SelectEvent event) {
        this.filialSelecionada = (Filiais) event.getObject();
    }

    private void lazyGetAllFiliais() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            filiais = new FiliaisLazyList(persistencia);
            total = this.filiaissatmobweb.Contagem(filters, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<Filiais> getAllFiliaisMenu() {
        if (this.filiais == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("exportarFiliais:tabela");
            this.filters.replace(CODFIL, "");
            this.filters.replace(DESCRICAO, "");
            this.filters.replace(RAZAOSOCIAL, "");
            dt.setFilters(this.filters);
            this.filiais = new FiliaisLazyList(this.persistencia);
        }
        try {
            this.total = this.filiaissatmobweb.Contagem(this.filters, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.filiais;
    }

    public void PesquisaPaginada() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        if (null != this.novaFilial.getCodFil()) {
            this.filters.replace(CODFIL, this.novaFilial.getCodFil().toPlainString());
        } else {
            this.filters.replace(CODFIL, "");
        }
        if (!this.novaFilial.getRazaoSocial().equals("")) {
            this.filters.replace(RAZAOSOCIAL, "%" + this.novaFilial.getRazaoSocial() + "%");
        } else {
            this.filters.replace(RAZAOSOCIAL, "");
        }
        if (!this.novaFilial.getDescricao().equals("")) {
            this.filters.replace(DESCRICAO, "%" + this.novaFilial.getDescricao() + "%");
        } else {
            this.filters.replace(DESCRICAO, "");
        }
        dt.setFilters(this.filters);
        lazyGetAllFiliais();
        dt.setFirst(0);
    }

    public void pesquisarUnico() {
        limparPesquisa();
        replaceFilter(valorPesquisa);
        lazyGetAllFiliais();
    }

    private boolean isInteger(String valor) {
        try {
            Integer.parseInt(valor);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private void replaceFilter(String valor) {
        try {
            switch (chavePesquisa) {
                case "CODFIL":
                    if (this.isInteger(valor)) {
                        filters.replace(CODFIL, valor);
                        return;
                    } else {
                        throw new Exception("DeveSerNumerico");
                    }
                case "DESCRICAO":
                    filters.replace(DESCRICAO, "%" + valor + "%");
                    return;
                case "RAZAOSOCIAL":
                    filters.replace(RAZAOSOCIAL, "%" + valor + "%");
                    return;
                default:
                    throw new Exception("CampoNaoExiste");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void LimparFiltros() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        this.filters.replace(CODFIL, "");
        this.filters.replace(RAZAOSOCIAL, "");
        this.filters.replace(DESCRICAO, "");
        dt.setFilters(this.filters);
        lazyGetAllFiliais();
        dt.setFirst(0);
        this.limparFiltros = false;
    }

    public void limparPesquisa() {
        filters.replace(CODFIL, "");
        filters.replace(DESCRICAO, "");
        filters.replace(RAZAOSOCIAL, "");
    }

    public void AtualizaTabela() {
        PrimeFaces.current().ajax().update("main:tabela");
    }

    public void AtualizaTabelaMenu() {
        PrimeFaces.current().ajax().update("exportarFiliais:tabela");
    }

    public Filiais getFilialSelecionada() {
        return filialSelecionada;
    }

    public void setFilialSelecionada(Filiais filialSelecionada) {
        this.filialSelecionada = filialSelecionada;
    }

    public Filiais getNovaFilial() {
        return novaFilial;
    }

    public void setNovaFilial(Filiais novaFilial) {
        this.novaFilial = novaFilial;
    }

    public List<Filiais> getListaFiliais() {
        return listaFiliais;
    }

    public void setListaFiliais(List<Filiais> listaFiliais) {
        this.listaFiliais = listaFiliais;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public int getFlag() {
        return flag;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getFilial() {
        return filial;
    }

    public void setFilial(String filial) {
        this.filial = filial;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public boolean iseFilial() {
        return eFilial;
    }

    public void seteFilial(boolean eFilial) {
        this.eFilial = eFilial;
    }

    public boolean iseDesc() {
        return eDesc;
    }

    public void seteDesc(boolean eDesc) {
        this.eDesc = eDesc;
    }

    public boolean iseRazao() {
        return eRazao;
    }

    public void seteRazao(boolean eRazao) {
        this.eRazao = eRazao;
    }

    public boolean iseOperador() {
        return eOperador;
    }

    public void seteOperador(boolean eOperador) {
        this.eOperador = eOperador;
    }

    public boolean iseHrAlter() {
        return eHrAlter;
    }

    public void seteHrAlter(boolean eHrAlter) {
        this.eHrAlter = eHrAlter;
    }

    public boolean iseDtAlter() {
        return eDtAlter;
    }

    public void seteDtAlter(boolean eDtAlter) {
        this.eDtAlter = eDtAlter;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public LazyDataModel<Filiais> getAllFiliais() {
        if (filiais == null) {
            lazyGetAllFiliais();
        }

        return filiais;
    }
}

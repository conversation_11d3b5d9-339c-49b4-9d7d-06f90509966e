package SasBeans;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ComprasRec {

    private BigDecimal sequencia;
    private String tipo;
    private Date data;
    private BigDecimal OCompra;
    private BigDecimal NF;
    private String serie;
    private Integer tipoTit;
    private Integer formaPgto;
    private String dtNF;
    private String compet;
    private String dtVenc;
    private Integer qtdeParc;
    private Integer intervalo;
    private BigDecimal valor;
    private BigDecimal valorBruto;
    private BigDecimal codPagar;
    private String NFEChave;
    private BigDecimal codTransp;
    private BigDecimal valorFrete;
    private BigDecimal aliqFrete;
    private BigDecimal pesoLiq;
    private BigDecimal seguro;
    private Integer formaPagtoFrete;
    private String operador;
    private String dtAlter;
    private String hrAlter;

    public BigDecimal getSequencia() {
        return sequencia;
    }

    public void setSequencia(BigDecimal sequencia) {
        this.sequencia = sequencia;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public BigDecimal getOCompra() {
        return OCompra;
    }

    public void setOCompra(BigDecimal OCompra) {
        this.OCompra = OCompra;
    }

    public BigDecimal getNF() {
        return NF;
    }

    public void setNF(BigDecimal NF) {
        this.NF = NF;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public Integer getTipoTit() {
        return tipoTit;
    }

    public void setTipoTit(Integer tipoTit) {
        this.tipoTit = tipoTit;
    }

    public Integer getFormaPgto() {
        return formaPgto;
    }

    public void setFormaPgto(Integer formaPgto) {
        this.formaPgto = formaPgto;
    }

    public String getDtNF() {
        return dtNF;
    }

    public void setDtNF(String dtNF) {
        this.dtNF = dtNF;
    }

    public String getCompet() {
        return compet;
    }

    public void setCompet(String compet) {
        this.compet = compet;
    }

    public String getDtVenc() {
        return dtVenc;
    }

    public void setDtVenc(String dtVenc) {
        this.dtVenc = dtVenc;
    }

    public Integer getQtdeParc() {
        return qtdeParc;
    }

    public void setQtdeParc(Integer qtdeParc) {
        this.qtdeParc = qtdeParc;
    }

    public Integer getIntervalo() {
        return intervalo;
    }

    public void setIntervalo(Integer intervalo) {
        this.intervalo = intervalo;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public BigDecimal getValorBruto() {
        return valorBruto;
    }

    public void setValorBruto(BigDecimal valorBruto) {
        this.valorBruto = valorBruto;
    }

    public BigDecimal getCodPagar() {
        return codPagar;
    }

    public void setCodPagar(BigDecimal codPagar) {
        this.codPagar = codPagar;
    }

    public String getNFEChave() {
        return NFEChave;
    }

    public void setNFEChave(String NFEChave) {
        this.NFEChave = NFEChave;
    }

    public BigDecimal getCodTransp() {
        return codTransp;
    }

    public void setCodTransp(BigDecimal codTransp) {
        this.codTransp = codTransp;
    }

    public BigDecimal getValorFrete() {
        return valorFrete;
    }

    public void setValorFrete(BigDecimal valorFrete) {
        this.valorFrete = valorFrete;
    }

    public BigDecimal getAliqFrete() {
        return aliqFrete;
    }

    public void setAliqFrete(BigDecimal aliqFrete) {
        this.aliqFrete = aliqFrete;
    }

    public BigDecimal getPesoLiq() {
        return pesoLiq;
    }

    public void setPesoLiq(BigDecimal pesoLiq) {
        this.pesoLiq = pesoLiq;
    }

    public BigDecimal getSeguro() {
        return seguro;
    }

    public void setSeguro(BigDecimal seguro) {
        this.seguro = seguro;
    }

    public Integer getFormaPagtoFrete() {
        return formaPagtoFrete;
    }

    public void setFormaPagtoFrete(Integer formaPagtoFrete) {
        this.formaPagtoFrete = formaPagtoFrete;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDtAlter() {
        return dtAlter;
    }

    public void setDtAlter(String dtAlter) {
        this.dtAlter = dtAlter;
    }

    public String getHrAlter() {
        return hrAlter;
    }

    public void setHrAlter(String hrAlter) {
        this.hrAlter = hrAlter;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.sequencia);
        hash = 59 * hash + Objects.hashCode(this.tipo);
        hash = 59 * hash + Objects.hashCode(this.data);
        hash = 59 * hash + Objects.hashCode(this.OCompra);
        hash = 59 * hash + Objects.hashCode(this.NF);
        hash = 59 * hash + Objects.hashCode(this.serie);
        hash = 59 * hash + Objects.hashCode(this.tipoTit);
        hash = 59 * hash + Objects.hashCode(this.formaPgto);
        hash = 59 * hash + Objects.hashCode(this.dtNF);
        hash = 59 * hash + Objects.hashCode(this.compet);
        hash = 59 * hash + Objects.hashCode(this.dtVenc);
        hash = 59 * hash + Objects.hashCode(this.qtdeParc);
        hash = 59 * hash + Objects.hashCode(this.intervalo);
        hash = 59 * hash + Objects.hashCode(this.valor);
        hash = 59 * hash + Objects.hashCode(this.valorBruto);
        hash = 59 * hash + Objects.hashCode(this.codPagar);
        hash = 59 * hash + Objects.hashCode(this.NFEChave);
        hash = 59 * hash + Objects.hashCode(this.codTransp);
        hash = 59 * hash + Objects.hashCode(this.valorFrete);
        hash = 59 * hash + Objects.hashCode(this.aliqFrete);
        hash = 59 * hash + Objects.hashCode(this.pesoLiq);
        hash = 59 * hash + Objects.hashCode(this.seguro);
        hash = 59 * hash + Objects.hashCode(this.formaPagtoFrete);
        hash = 59 * hash + Objects.hashCode(this.operador);
        hash = 59 * hash + Objects.hashCode(this.dtAlter);
        hash = 59 * hash + Objects.hashCode(this.hrAlter);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ComprasRec other = (ComprasRec) obj;
        if (!Objects.equals(this.sequencia, other.sequencia)) {
            return false;
        }
        return true;
    }

}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RPV;

/**
 * <AUTHOR>
 */
public class RPVDao {

    /**
     * Obtem informações sobre a quantidade de impressões
     *
     * @param seqrota sequencia da rota
     * @param parada numero da parada
     * @param persistencia conexao com o banco de dados
     * @return quantidade de impressão
     * @throws Exception
     */
    public int obterQuantidade(String seqrota, String parada, Persistencia persistencia) throws Exception {
        int quantidade = 0;
        try {
            String sql = "SELECT isnull(Impresso, 0) impresso FROM rpv WHERE parada = ? AND seqrota = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(parada);
            consulta.setString(seqrota);
            consulta.select();

            while (consulta.Proximo()) {
                quantidade = consulta.getInt("impresso");
            }
        } catch (Exception e) {
            throw new Exception("RPVDAO.obterQuantidade - " + e.getMessage() + "\r\n"
                    + "SELECT isnull(Impresso, 0) impresso FROM rpv WHERE parada = " + parada + " AND seqrota = " + seqrota);
        }
        return quantidade;
    }

    /**
     * Marca o registro com o imprimido
     *
     * @param quantidade Quantidade de impressão
     * @param seqrota sequencia da rota
     * @param parada numero da parada
     * @param persistencia conexão com o banco de dados
     * @throws Exception
     */
    public void marcarImprimido(int quantidade, String seqrota, String parada, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE rpv SET Impresso = ? WHERE seqrota = ? AND parada = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(quantidade);
            consulta.setString(seqrota);
            consulta.setString(parada);
            consulta.update();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("RPVDAO.marcarImprimido - " + e.getMessage() + "\r\n"
                    + "UPDATE rpv SET Impresso = " + quantidade + " WHERE seqrota = " + seqrota + " AND parada = " + parada);
        }
    }

    /**
     * Realiza o salvamento
     *
     * @param rpv Objeto do rpv
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void salvarInformacoes(RPV rpv, Persistencia persistencia) throws Exception {
        try {

            if (!rpv.getGuia().replace(".0", "").equals("5") || !rpv.getSerie().replace(".0", "").equals("53")) {
                String sql = "INSERT INTO RPV (rpv, guia, serie, codpessoaaut, seqRota, parada, data, hora, valor, flag_excl, volumes) "
                        + " VALUES (?,?,?,?,?,?,?,?,?,?,?)";

                Consulta consulta = new Consulta(sql, persistencia);
                consulta.setString(rpv.getRpv());
                consulta.setString(rpv.getGuia());
                consulta.setString(rpv.getSerie());
                consulta.setString(rpv.getCodPessoAut());
                consulta.setFloat(rpv.getSeqRota());
                consulta.setInt(rpv.getParada());
                consulta.setString(rpv.getData());
                consulta.setString(rpv.getHora());
                consulta.setFloat(rpv.getValor());
                consulta.setString(rpv.getFlag_excl());
                consulta.setString(rpv.getVolumes());
                consulta.insert();
                consulta.close();
            }

        } catch (Exception e) {
            throw new Exception("RPVDAO.salvarInformacoes - " + e.getMessage() + "\r\n"
                    + "INSERT INTO RPV (rpv, guia, serie, codpessoaaut, seqRota, parada, data, hora, valor, flag_excl, volumes) "
                    + " VALUES (" + rpv.getRpv() + "," + rpv.getGuia() + "," + rpv.getSerie() + "," + rpv.getCodPessoAut() + "," + rpv.getSeqRota() + ","
                    + rpv.getParada() + "," + rpv.getData() + "," + rpv.getHora() + "," + rpv.getValor() + "," + rpv.getFlag_excl() + "," + rpv.getVolumes() + ")");
        }
    }

    /**
     * Verifica a existencia do RPV
     *
     * @param rpv numero da RPV
     * @param persistencia conexao com o banco de dados
     * @return existe RPV
     * @throws Exception
     */
    public boolean existeRPV(String rpv, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM rpv WHERE rpv = ? and flag_excl <> '*' ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rpv);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }

            if (quantidade > 0) {
                existe = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RPVDAO.existeRPV - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM rpv WHERE rpv = " + rpv + " and flag_excl <> '*' ");
        }
        return existe;
    }

    /**
     * Verifica a existencia de não excluído RPV
     *
     * @param rpv numero da RPV
     * @param persistencia conexao com o banco de dados
     * @return existe RPV
     * @throws Exception
     */
    public boolean existeRPVNaoExcluido(String rpv, Persistencia persistencia) throws Exception {
        int quantidade = 0;
        try {
            String sql = "SELECT COUNT(*) qtd FROM rpv WHERE rpv = ? and flag_excl <> '*'";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rpv);
            consulta.select();

            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RPVDAO.existeRPVNaoExcluido - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM rpv WHERE rpv = " + rpv + " and flag_excl <> '*'");
        }
        return quantidade > 0;
    }

    /**
     * Verifica a existencia da guia
     *
     * @param guia
     * @param serie
     * @param sequencia
     * @param parada
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeGuiaRPV(String guia, String serie, String sequencia, String parada, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT count(*) qtd, Flag_Excl FROM rpv WHERE guia = ? AND serie = ? AND seqrota = ? AND parada = ? group by Flag_Excl";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            int quantidade = 0;
            String flag_excl = "";
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
                flag_excl = consulta.getString("flag_excl");
            }

            if (quantidade > 0) {
                existe = true;
                //Caso o RPV possua flag de exclusão, o mesmo é sobrescrito.
                if (flag_excl.equals("*")) {
                    String sqlclean = "Delete FROM rpv WHERE guia = ? AND serie = ? AND seqrota = ? AND parada = ?";
                    consulta = new Consulta(sqlclean, persistencia);
                    consulta.setString(guia);
                    consulta.setString(serie);
                    consulta.setString(sequencia);
                    consulta.setString(parada);
                    consulta.delete();
                    consulta.close();
                    existe = false;
                }
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RPVDAO.existeGuiaRPV - " + e.getMessage());
        }
        return existe;
    }

    /**
     * Retorna informacoes da guia
     *
     * @param rpv numero do rpv
     * @param persistencia conexao com o banco de dados
     * @return numero da guia ja existente
     * @throws Exception
     */
    public String retornaGuia(String rpv, Persistencia persistencia) throws Exception {
        String guia = "";
        try {
            String sql = "SELECT guia FROM rpv WHERE rpv = ? AND flag_excl <> '*'";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rpv);
            consulta.select();

            while (consulta.Proximo()) {
                guia = consulta.getString("guia");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RPVDAO.retornaGuia - " + e.getMessage() + "\r\n"
                    + "SELECT guia FROM rpv WHERE rpv = " + rpv);
        }
        return guia;
    }

    /**
     * Assinado por
     *
     * @param guia numero da guia
     * @param conexao conexao com o banco de dados
     * @return numero de quem assinou
     * @throws Exception
     */
    public String assinadoPor(String guia, Persistencia conexao) throws Exception {
        String codPessoaAuth = "";
        try {
            String sql = "select CodPessoaAut from RPV where Guia = ? AND Flag_Excl = ''";

            Consulta consulta = new Consulta(sql, conexao);
            consulta.setString(guia);
            consulta.select();

            while (consulta.Proximo()) {
                codPessoaAuth = consulta.getString("CodPessoaAut");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RPVDAO.assinadoPor - " + e.getMessage() + "\r\n"
                    + "select CodPessoaAut from RPV where Guia = " + guia + " AND Flag_Excl = ''");
        }
        return codPessoaAuth;
    }

    public String gerarCabecalho(String sequencia, String parada, Persistencia persistencia) throws Exception {
        String RazaoSocial, Endereco, Cidade, UF, CNPJ, Fone, CodPessoaAut, Nome, cabecalho, Rota;
        cabecalho = "";
        try {
            String sql = " Select Filiais.RazaoSocial, Filiais.Endereco ,Filiais.Cidade, Filiais.UF, Filiais.CNPJ, Filiais.Fone,  \n"
                    + "RPV.CodPessoaAut, Pessoa.Nome, Rotas.Rota \n"
                    + "from RPV \n"
                    + "left join Rotas on Rotas.Sequencia = RPV.SeqRota \n"
                    + "left join filiais  on Filiais.Codfil = Rotas.Codfil "
                    + "left Join Pessoa on Pessoa.codigo = RPV.CodPessoaAut "
                    + "where RPV.SeqRota = ?\n"
                    + "  and RPV.Parada    = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            while (consulta.Proximo()) {
                RazaoSocial = consulta.getString("RazaoSocial");
                Endereco = consulta.getString("Endereco");
                Cidade = consulta.getString("Cidade");
                UF = consulta.getString("UF");
                CNPJ = consulta.getString("CNPJ");
                Fone = consulta.getString("Fone");
                CodPessoaAut = consulta.getString("CodPessoaAut");
                Nome = consulta.getString("Nome");
                Rota = consulta.getString("Rota");

                cabecalho = RazaoSocial + "@" + Endereco + "@" + Cidade + "/" + UF + "@" + CNPJ + "@" + Fone + "@" + CodPessoaAut.replace(".0", "") + " - " + Nome + "@" + Rota;
            }
            consulta.close();
            return cabecalho;
        } catch (Exception e) {
            throw new Exception("PreOrderDao.gerarManifesto - " + e.getMessage());
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class S2220 {

    private int sucesso; //
    private String evtMonit_Id;

    private String ideEvento_indRetif;//
    private String ideEvento_nrRecibo;//

    private String ideEvento_tpAmb;//

    private String ideEvento_procEmi;//
    private String ideEvento_verProc;//

    private String ideEmpregador_tpInsc; //
    private String ideEmpregador_nrInsc; //

    private String ideVinculo_cpfTrab;//
    private String ideVinculo_matricula;//
    private String ideVinculo_matr;//
    private String ideVinculo_codCateg;//

    private int exMedOcup_tpExameOcup;
    private String exMedOcup_dtAso;
    private int exMedOcup_resAso;
    private String exMedOcup_dtExm;
    private int exMedOcup_procRealizado;
    private String exMedOcup_obsProc;
    private int exMedOcup_ordExame;
    private int exMedOcup_indResult;
    
    private String exMedOcup_nmMed;
    private String exMedOcup_nrCRM;
    private String exMedOcup_ufCRM;
    
    private String respMonit_cpfResp;
    private String respMonit_nmResp;
    private String respMonit_nrCRM;
    private String respMonit_ufCRM;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getevtMonit_Id() {
        return evtMonit_Id;
    }

    public void setevtMonit_Id(String evtMonit_Id) {
        this.evtMonit_Id = evtMonit_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeVinculo_cpfTrab() {
        return ideVinculo_cpfTrab;
    }

    public void setIdeVinculo_cpfTrab(String ideVinculo_cpfTrab) {
        this.ideVinculo_cpfTrab = ideVinculo_cpfTrab;
    }

    public String getIdeVinculo_matricula() {
        return ideVinculo_matricula;
    }

    public void setIdeVinculo_matricula(String ideVinculo_matricula) {
        this.ideVinculo_matricula = ideVinculo_matricula;
    }

    public String getIdeVinculo_codCateg() {
        return ideVinculo_codCateg;
    }

    public void setIdeVinculo_codCateg(String ideVinculo_codCateg) {
        this.ideVinculo_codCateg = ideVinculo_codCateg;
    }

    public int getexMedOcup_tpExameOcup() {
        return exMedOcup_tpExameOcup;
    }

    public void setexMedOcup_tpExameOcup(int exMedOcup_tpExameOcup) {
        this.exMedOcup_tpExameOcup = exMedOcup_tpExameOcup;
    }

    public String getexMedOcup_dtAso() {
        return exMedOcup_dtAso;
    }

    public void setexMedOcup_dtAso(String exMedOcup_dtAso) {
        this.exMedOcup_dtAso = exMedOcup_dtAso;
    }

    public int getexMedOcup_resAso() {
        return exMedOcup_resAso;
    }

    public void setexMedOcup_resAso(int exMedOcup_resAso) {
        this.exMedOcup_resAso = exMedOcup_resAso;
    }    
    
/**
     * @return the exMedOcup_dtExm
     */
    public String getExMedOcup_dtExm() {
        return exMedOcup_dtExm;
    }

    /**
     * @param exMedOcup_dtExm the exMedOcup_dtExm to set
     */
    public void setExMedOcup_dtExm(String exMedOcup_dtExm) {
        this.exMedOcup_dtExm = exMedOcup_dtExm;
    }

    /**
     * @return the exMedOcup_procRealizado
     */
    public int getExMedOcup_procRealizado() {
        return exMedOcup_procRealizado;
    }

    /**
     * @param exMedOcup_procRealizado the exMedOcup_procRealizado to set
     */
    public void setExMedOcup_procRealizado(int exMedOcup_procRealizado) {
        this.exMedOcup_procRealizado = exMedOcup_procRealizado;
    }

    /**
     * @return the exMedOcup_obsProc
     */
    public String getExMedOcup_obsProc() {
        return exMedOcup_obsProc;
    }

    /**
     * @param exMedOcup_obsProc the exMedOcup_obsProc to set
     */
    public void setExMedOcup_obsProc(String exMedOcup_obsProc) {
        this.exMedOcup_obsProc = exMedOcup_obsProc;
    }

    /**
     * @return the exMedOcup_ordExame
     */
    public int getExMedOcup_ordExame() {
        return exMedOcup_ordExame;
    }

    /**
     * @param exMedOcup_ordExame the exMedOcup_ordExame to set
     */
    public void setExMedOcup_ordExame(int exMedOcup_ordExame) {
        this.exMedOcup_ordExame = exMedOcup_ordExame;
    }

    /**
     * @return the exMedOcup_indResult
     */
    public int getExMedOcup_indResult() {
        return exMedOcup_indResult;
    }

    /**
     * @param exMedOcup_indResult the exMedOcup_indResult to set
     */
    public void setExMedOcup_indResult(int exMedOcup_indResult) {
        this.exMedOcup_indResult = exMedOcup_indResult;
    }

    /**
     * @return the exMedOcup_nmMed
     */
    public String getExMedOcup_nmMed() {
        return exMedOcup_nmMed;
    }

    /**
     * @param exMedOcup_nmMed the exMedOcup_nmMed to set
     */
    public void setExMedOcup_nmMed(String exMedOcup_nmMed) {
        this.exMedOcup_nmMed = exMedOcup_nmMed;
    }

    /**
     * @return the exMedOcup_nrCRM
     */
    public String getExMedOcup_nrCRM() {
        return exMedOcup_nrCRM;
    }

    /**
     * @param exMedOcup_nrCRM the exMedOcup_nrCRM to set
     */
    public void setExMedOcup_nrCRM(String exMedOcup_nrCRM) {
        this.exMedOcup_nrCRM = exMedOcup_nrCRM;
    }

    /**
     * @return the exMedOcup_ufCRM
     */
    public String getExMedOcup_ufCRM() {
        return exMedOcup_ufCRM;
    }

    /**
     * @param exMedOcup_ufCRM the exMedOcup_ufCRM to set
     */
    public void setExMedOcup_ufCRM(String exMedOcup_ufCRM) {
        this.exMedOcup_ufCRM = exMedOcup_ufCRM;
    }

    /**
     * @return the respMonit_cpfResp
     */
    public String getRespMonit_cpfResp() {
        return respMonit_cpfResp;
    }

    /**
     * @param respMonit_cpfResp the respMonit_cpfResp to set
     */
    public void setRespMonit_cpfResp(String respMonit_cpfResp) {
        this.respMonit_cpfResp = respMonit_cpfResp;
    }

    /**
     * @return the respMonit_nmResp
     */
    public String getRespMonit_nmResp() {
        return respMonit_nmResp;
    }

    /**
     * @param respMonit_nmResp the respMonit_nmResp to set
     */
    public void setRespMonit_nmResp(String respMonit_nmResp) {
        this.respMonit_nmResp = respMonit_nmResp;
    }

    /**
     * @return the respMonit_nrCRM
     */
    public String getRespMonit_nrCRM() {
        return respMonit_nrCRM;
    }

    /**
     * @param respMonit_nrCRM the respMonit_nrCRM to set
     */
    public void setRespMonit_nrCRM(String respMonit_nrCRM) {
        this.respMonit_nrCRM = respMonit_nrCRM;
    }

    /**
     * @return the respMonit_ufCRM
     */
    public String getRespMonit_ufCRM() {
        return respMonit_ufCRM;
    }

    /**
     * @param respMonit_ufCRM the respMonit_ufCRM to set
     */
    public void setRespMonit_ufCRM(String respMonit_ufCRM) {
        this.respMonit_ufCRM = respMonit_ufCRM;
    }    
    
    public String getIdeVinculo_matr() {
        return ideVinculo_matr;
    }
    
    

    public void setIdeVinculo_matr(String ideVinculo_matr) {
        this.ideVinculo_matr = ideVinculo_matr;
    }
    
     @Override
    public int hashCode() {
        int hash = 5;
        hash = 29 * hash + Objects.hashCode(this.ideVinculo_cpfTrab);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final S2220 other = (S2220) obj;
        if (!Objects.equals(this.ideVinculo_cpfTrab, other.ideVinculo_cpfTrab)) {
            return false;
        }
        return true;
    }
}

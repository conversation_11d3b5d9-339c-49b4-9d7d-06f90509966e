package SasBeansCompostas;

import SasBeans.Funcion;
import SasBeans.Psthstqst;
import SasBeans.TbVal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class PstHstQstTbValFuncion {

    private Psthstqst psthstqst;
    private TbVal tbval;
    private Funcion funcion;
    private Integer qtdefotos;
    private List<String> endfotos;

    public PstHstQstTbValFuncion() {
        this.psthstqst = new Psthstqst();
        this.tbval = new TbVal();
        this.funcion = new Funcion();
        this.qtdefotos = 0;
        this.endfotos = new ArrayList();
    }

    public Psthstqst getPsthstqst() {
        return psthstqst;
    }

    public void setPsthstqst(Psthstqst psthstqst) {
        this.psthstqst = psthstqst;
    }

    public TbVal getTbval() {
        return tbval;
    }

    public void setTbval(TbVal tbval) {
        this.tbval = tbval;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public Integer getQtdefotos() {
        return qtdefotos;
    }

    public void setQtdefotos(Integer qtdefotos) {
        this.qtdefotos = qtdefotos;
    }

    public List<String> getEndfotos() {
        return endfotos;
    }

    public void setEndfotos(List<String> endfotos) {
        this.endfotos = endfotos;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 13 * hash + Objects.hashCode(this.psthstqst);
        hash = 13 * hash + Objects.hashCode(this.tbval);
        hash = 13 * hash + Objects.hashCode(this.funcion);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PstHstQstTbValFuncion other = (PstHstQstTbValFuncion) obj;
        if (!Objects.equals(this.psthstqst, other.psthstqst)) {
            return false;
        }
        if (!Objects.equals(this.tbval, other.tbval)) {
            return false;
        }
        if (!Objects.equals(this.funcion, other.funcion)) {
            return false;
        }
        return true;
    }

}

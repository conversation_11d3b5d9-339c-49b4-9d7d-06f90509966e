/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class RHPontoProcessa {
    public String Sequencia;
    public String Parametro;
    public String Matr;
    public String CodFil;
    public String DtCompet;
    public String Batida;
    public String EnderecoPDF;
    public String EnderecoHTML;
    public String HtmlEmail;
    public String HtmlPDF;
    public String HtmlPagina;
    public String Gerado;
    public String EnviaEmail;
    public String EnviaWpp;
    public String Log;
    public String Operador;
    public String Dt_Alter;
    public String Hr_Alter;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getParametro() {
        return Parametro;
    }

    public void setParametro(String Parametro) {
        this.Parametro = Parametro;
    }

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDtCompet() {
        return DtCompet;
    }

    public void setDtCompet(String DtCompet) {
        this.DtCompet = DtCompet;
    }

    public String getBatida() {
        return Batida;
    }

    public void setBatida(String Batida) {
        this.Batida = Batida;
    }

    public String getEnderecoPDF() {
        return EnderecoPDF;
    }

    public void setEnderecoPDF(String EnderecoPDF) {
        this.EnderecoPDF = EnderecoPDF;
    }

    public String getEnderecoHTML() {
        return EnderecoHTML;
    }

    public void setEnderecoHTML(String EnderecoHTML) {
        this.EnderecoHTML = EnderecoHTML;
    }

    public String getHtmlEmail() {
        return HtmlEmail;
    }

    public void setHtmlEmail(String HtmlEmail) {
        this.HtmlEmail = HtmlEmail;
    }

    public String getHtmlPDF() {
        return HtmlPDF;
    }

    public void setHtmlPDF(String HtmlPDF) {
        this.HtmlPDF = HtmlPDF;
    }

    public String getGerado() {
        return Gerado;
    }

    public void setGerado(String Gerado) {
        this.Gerado = Gerado;
    }

    public String getEnviaEmail() {
        return EnviaEmail;
    }

    public void setEnviaEmail(String EnviaEmail) {
        this.EnviaEmail = EnviaEmail;
    }

    public String getEnviaWpp() {
        return EnviaWpp;
    }

    public void setEnviaWpp(String EnviaWpp) {
        this.EnviaWpp = EnviaWpp;
    }

    public String getLog() {
        return Log;
    }

    public void setLog(String Log) {
        this.Log = Log;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getHtmlPagina() {
        return HtmlPagina;
    }

    public void setHtmlPagina(String HtmlPagina) {
        this.HtmlPagina = HtmlPagina;
    }
}

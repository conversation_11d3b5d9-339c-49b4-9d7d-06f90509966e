/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FPMensalOP {

    private BigDecimal Sequencia;
    private BigDecimal CodFil;
    private BigDecimal CodMovFP;
    private String TipoFP;
    private String Banco;
    private BigDecimal Matr;
    private LocalDate DtPagto;
    private BigDecimal Liquido;

    /**
     * @return the Sequencia
     */
    public BigDecimal getSequencia() {
        return Sequencia;
    }

    /**
     * @param Sequencia the Sequencia to set
     */
    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the CodMovFP
     */
    public BigDecimal getCodMovFP() {
        return CodMovFP;
    }

    /**
     * @param CodMovFP the CodMovFP to set
     */
    public void setCodMovFP(String CodMovFP) {
        try {
            this.CodMovFP = new BigDecimal(CodMovFP);
        } catch (Exception e) {
            this.CodMovFP = new BigDecimal("0");
        }
    }

    /**
     * @return the TipoFP
     */
    public String getTipoFP() {
        return TipoFP;
    }

    /**
     * @param TipoFP the TipoFP to set
     */
    public void setTipoFP(String TipoFP) {
        this.TipoFP = TipoFP;
    }

    /**
     * @return the Banco
     */
    public String getBanco() {
        return Banco;
    }

    /**
     * @param Banco the Banco to set
     */
    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    /**
     * @return the Matr
     */
    public BigDecimal getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    /**
     * @return the DtPagto
     */
    public LocalDate getDtPagto() {
        return DtPagto;
    }

    /**
     * @param DtPagto the DtPagto to set
     */
    public void setDtPagto(LocalDate DtPagto) {
        this.DtPagto = DtPagto;
    }

    /**
     * @return the Liquido
     */
    public BigDecimal getLiquido() {
        return Liquido;
    }

    /**
     * @param Liquido the Liquido to set
     */
    public void setLiquido(String Liquido) {
        try {
            this.Liquido = new BigDecimal(Liquido);
        } catch (Exception e) {
            this.Liquido = new BigDecimal("0");
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class PedidoLog {
    public String Numero;
    public String CodFil;
    public String NF;
    public String OCarga;
    public String NFSerie;
    public String Volumes;
    public String Peso;
    public String Valor;
    public String NFeChave;
    public String Protocolo;
    public String Operador;
    public String Dt_Alter;
    public String Hr_Alter;
    public String OperExcl;
    public String Dt_Excl;
    public String Hr_Excl;
    public String Flag_Excl;

    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getNF() {
        return NF;
    }

    public void setNF(String NF) {
        this.NF = NF;
    }

    public String getOCarga() {
        return OCarga;
    }

    public void setOCarga(String OCarga) {
        this.OCarga = OCarga;
    }

    public String getNFSerie() {
        return NFSerie;
    }

    public void setNFSerie(String NFSerie) {
        this.NFSerie = NFSerie;
    }

    public String getVolumes() {
        return Volumes;
    }

    public void setVolumes(String Volumes) {
        this.Volumes = Volumes;
    }

    public String getPeso() {
        return Peso;
    }

    public void setPeso(String Peso) {
        this.Peso = Peso;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getNFeChave() {
        return NFeChave;
    }

    public void setNFeChave(String NFeChave) {
        this.NFeChave = NFeChave;
    }

    public String getProtocolo() {
        return Protocolo;
    }

    public void setProtocolo(String Protocolo) {
        this.Protocolo = Protocolo;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOperExcl() {
        return OperExcl;
    }

    public void setOperExcl(String OperExcl) {
        this.OperExcl = OperExcl;
    }

    public String getDt_Excl() {
        return Dt_Excl;
    }

    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    public String getHr_Excl() {
        return Hr_Excl;
    }

    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }
    
    
}

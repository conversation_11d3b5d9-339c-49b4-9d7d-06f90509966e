/*
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Contatos {

    private BigDecimal Codigo;
    private BigDecimal CodFil;
    private String Nome;
    private String Fantasia;
    private String Endereco;
    private String Bairro;
    private String Cidade;
    private BigDecimal CodCidade;
    private String CEP;
    private String UF;
    private String Fone1;
    private String Fone2;
    private String Email;
    private String Contato;
    private String TpCli;
    private String Agencia;
    private String CNPJ;
    private String IE;
    private String IM;
    private String CPF;
    private String RG;
    private String UltCont;
    private String DtNovoCont;
    private String Responsavel;
    private BigDecimal Situacao;
    private String DtSituacao;
    private String Obs;
    private int RamoAtiv;
    private int CodOrigem;
    private String OperIncl;
    private String Dt_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private BigDecimal CodPessoa;
    private String CodCli;
    private String Fax;
    private String latitude;
    private String longitude;

    public Contatos() {
        this.Codigo = BigDecimal.ZERO;
        this.CodFil = BigDecimal.ZERO;
        this.Nome = "";
        this.Fantasia = "";
        this.Endereco = "";
        this.Bairro = "";
        this.Cidade = "";
        this.CodCidade = BigDecimal.ZERO;
        this.CEP = "";
        this.UF = "";
        this.Fone1 = "";
        this.Fone2 = "";
        this.Email = "";
        this.Contato = "";
        this.TpCli = "";
        this.Agencia = "";
        this.CNPJ = "";
        this.IE = "";
        this.IM = "";
        this.CPF = "";
        this.RG = "";
        this.UltCont = "";
        this.DtNovoCont = "";
        this.Responsavel = "";
        this.Situacao = BigDecimal.ZERO;
        this.DtSituacao = "";
        this.Obs = "";
        this.RamoAtiv = 0;
        this.CodOrigem = 0;
        this.OperIncl = "";
        this.Dt_Incl = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
        this.CodPessoa = BigDecimal.ZERO;
        this.CodCli = "";
        this.Fax = "";
        this.latitude = "0.0";
        this.longitude = "0.0";
    }

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(BigDecimal Codigo) {
        this.Codigo = Codigo;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = new BigDecimal(CodFil);
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getFantasia() {
        return Fantasia;
    }

    public void setFantasia(String Fantasia) {
        this.Fantasia = Fantasia;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public BigDecimal getCodCidade() {
        return CodCidade;
    }

    public void setCodCidade(BigDecimal CodCidade) {
        this.CodCidade = CodCidade;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getFone1() {
        return Fone1;
    }

    public void setFone1(String Fone1) {
        this.Fone1 = Fone1;
    }

    public String getFone2() {
        return Fone2;
    }

    public void setFone2(String Fone2) {
        this.Fone2 = Fone2;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getContato() {
        return Contato;
    }

    public void setContato(String Contato) {
        this.Contato = Contato;
    }

    public String getTpCli() {
        return TpCli;
    }

    public void setTpCli(String TpCli) {
        this.TpCli = TpCli;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getIE() {
        return IE;
    }

    public void setIE(String IE) {
        this.IE = IE;
    }

    public String getIM() {
        return IM;
    }

    public void setIM(String IM) {
        this.IM = IM;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getRG() {
        return RG;
    }

    public void setRG(String RG) {
        this.RG = RG;
    }

    public String getUltCont() {
        return UltCont;
    }

    public void setUltCont(String UltCont) {
        this.UltCont = UltCont;
    }

    public String getDtNovoCont() {
        return DtNovoCont;
    }

    public void setDtNovoCont(String DtNovoCont) {
        this.DtNovoCont = DtNovoCont;
    }

    public String getResponsavel() {
        return Responsavel;
    }

    public void setResponsavel(String Responsavel) {
        this.Responsavel = Responsavel;
    }

    public BigDecimal getSituacao() {
        return Situacao;
    }

    public void setSituacao(BigDecimal Situacao) {
        this.Situacao = Situacao;
    }

    public String getDtSituacao() {
        return DtSituacao;
    }

    public void setDtSituacao(String DtSituacao) {
        this.DtSituacao = DtSituacao;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public int getRamoAtiv() {
        return RamoAtiv;
    }

    public void setRamoAtiv(int RamoAtiv) {
        this.RamoAtiv = RamoAtiv;
    }

    public int getCodOrigem() {
        return CodOrigem;
    }

    public void setCodOrigem(int CodOrigem) {
        this.CodOrigem = CodOrigem;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public String getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getFax() {
        return Fax;
    }

    public void setFax(String Fax) {
        this.Fax = Fax;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
}

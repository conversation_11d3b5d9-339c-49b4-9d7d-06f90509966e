package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.XMLNFSE;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class XMLNFSEDao {

    public XMLNFSE buscarNota(String numero, String praca, Persistencia persistencia) throws Exception {
        try {
            XMLNFSE retorno = null;
            String sql = " SELECT XMLNFSE.*, CONVERT(BigInt, NFiscal.NFERetorno) NFERetorno \n"
                    + " FROM XMLNFSE \n"
                    + " LEFT JOIN NFiscal ON NFiscal.Numero = XMLNFSE.Numero \n"
                    + "                 AND NFiscal.Praca = XMLNFSE.Praca \n"
                    + " WHERE XMLNFSE.Numero = ? AND XMLNFSE.Praca = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(numero);
            consulta.setString(praca);
            consulta.select();

            if (consulta.Proximo()) {
                retorno = new XMLNFSE();
                retorno.setCNPJ(consulta.getString("CNPJ"));
                retorno.setPraca(consulta.getInt("Praca"));
                retorno.setSerie(consulta.getString("Serie"));
                retorno.setNumero(consulta.getString("Numero"));
                retorno.setDt_Nota(consulta.getString("Dt_Nota"));
                retorno.setHr_Nota(consulta.getString("Hr_Nota"));
                retorno.setXML_Envio(consulta.getString("XML_Envio"));
                retorno.setXML_Retorno(consulta.getString("XML_Retorno"));
                retorno.setDt_Envio(consulta.getString("Dt_Envio"));
                retorno.setHr_Envio(consulta.getString("Hr_Envio"));
                retorno.setDt_Retorno(consulta.getString("Dt_Retorno"));
                retorno.setHr_Retorno(consulta.getString("Hr_Retorno"));

                retorno.setNFERetorno(consulta.getString("NFERetorno"));
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.buscarNota - " + e.getMessage() + "\r\n"
                    + "  SELECT XMLNFSE.*, CONVERT(BigInt, NFiscal.NFERetorno) NFERetorno \n"
                    + " FROM XMLNFSE \n"
                    + " LEFT JOIN NFiscal ON NFiscal.Numero = XMLNFSE.Numero \n"
                    + "                 AND NFiscal.Praca = XMLNFSE.Praca \n"
                    + " WHERE XMLNFSE.Numero = " + numero + " AND XMLNFSE.Praca = " + praca);
        }
    }

    /**
     * Grava em disco a nota que será enviada ao webservice
     *
     * @param CNPJ - CNPJ do emissor
     * @param Praca - Praça da nota
     * @param Serie - Serie da nota
     * @param Numero - Numero da nota
     * @param Dt_Nota - Data da nota (vai no corpo do XML)
     * @param Hr_Nota - Hora da nota (vai no corpo do XML)
     * @param XML - XML de envio, já assinado
     * @param Dt_Envio - Data do envio
     * @param Hr_Envio - Hora do envio
     * @param persistencia - Conexão com o banco
     * @throws Exception
     */
    public void inserirNota(String CNPJ, int Praca, String Serie, String Numero, String Dt_Nota, String Hr_Nota,
            String XML, String Dt_Envio, String Hr_Envio, Persistencia persistencia) throws Exception {
        String sql = "insert into XMLNFSE (CNPJ, Praca, Serie, Numero, Dt_Nota, Hr_Nota, XML_Envio, Dt_Envio, Hr_Envio) "
                + " values"
                + " (?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CNPJ);
            consulta.setInt(Praca);
            consulta.setString(Serie);
            consulta.setString(Numero);
            consulta.setString(Dt_Nota);
            consulta.setString(Hr_Nota);
            consulta.setString(XML);
            consulta.setString(Dt_Envio);
            consulta.setString(Hr_Envio);
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.inserirNota - " + e.getMessage() + "\r\n"
                    + "insert into XMLNFSE (CNPJ, Praca, Serie, Numero, Dt_Nota, Hr_Nota, XML_Envio, Dt_Envio, Hr_Envio) "
                    + " values (" + CNPJ + "," + Praca + "," + Serie + "," + Numero + "," + Dt_Nota + "," + Hr_Nota + "," + XML + "," + Dt_Envio + "," + Hr_Envio + ")");
        }
    }

    // new method 'insert' using the object 'xmlnfse' from the Class XMLNFSE
    // author: maurizio
    // create
    public boolean gravarXMLNFSE(XMLNFSE xmlnfse, Persistencia persistencia) {
        boolean retorno;
        String sql = "insert into xmlnfse (CNPJ,Praca,Serie,Numero,Dt_Nota,Hr_Nota,XML_Envio,XML_Retorno"
                + "Dt_Envio,Hr_Envio,Dt_Retorno,Hr_Retorno) "
                + "values (?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmlnfse.getCNPJ());
            consulta.setInt(xmlnfse.getPraca());
            consulta.setString(xmlnfse.getSerie());
            consulta.setBigDecimal(xmlnfse.getNumero());
            consulta.setString(xmlnfse.getDt_Nota());
            consulta.setString(xmlnfse.getHr_Nota());
            consulta.setString(xmlnfse.getXML_Envio());
            consulta.setString(xmlnfse.getXML_Retorno());
            consulta.setString(xmlnfse.getDt_Envio());
            consulta.setString(xmlnfse.getHr_Envio());
            consulta.setString(xmlnfse.getDt_Retorno());
            consulta.setString(xmlnfse.getHr_Retorno());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // new method 'read' using the object 'xmlnfse' from the Class XMLNFSE
    // author: maurizio
    // read
    public List<XMLNFSE> buscaXMLNFSE(Persistencia persistencia) throws Exception {
        List<XMLNFSE> listXMLNFSE;
        try {
            XMLNFSE xmlnfse;
            Consulta consult = new Consulta("select CNPJ,Praca,Serie,Numero,Dt_Nota,Hr_Nota,XML_Envio,"
                    + "XML_Retorno,Dt_Envio,Hr_Envio,Dt_Retorno,Hr_Retorno "
                    + "from xmlnfse", persistencia);
            consult.select();
            listXMLNFSE = new ArrayList();
            while (consult.Proximo()) {
                xmlnfse = new XMLNFSE();
                xmlnfse.setCNPJ(consult.getString("CNPJ"));
                xmlnfse.setPraca(consult.getInt("Praca"));
                xmlnfse.setSerie(consult.getString("Serie"));
                xmlnfse.setNumero(consult.getString("Numero"));
                xmlnfse.setDt_Nota(consult.getString("Dt_Nota"));
                xmlnfse.setHr_Nota(consult.getString("Hr_Nota"));
                xmlnfse.setXML_Envio(consult.getString("XML_Envio"));
                xmlnfse.setXML_Retorno(consult.getString("XML_Retorno"));
                xmlnfse.setDt_Envio(consult.getString("Dt_Envio"));
                xmlnfse.setHr_Envio(consult.getString("Hr_Envio"));
                xmlnfse.setDt_Retorno(consult.getString("Dt_Retorno"));
                xmlnfse.setHr_Retorno(consult.getString("Hr_Retorno"));
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.buscaXMLNFSE - " + e.getMessage() + "\r\n"
                    + "select CNPJ,Praca,Serie,Numero,Dt_Nota,Hr_Nota,XML_Envio,"
                    + "XML_Retorno,Dt_Envio,Hr_Envio,Dt_Retorno,Hr_Retorno "
                    + "from xmlnfse");
        }
        return listXMLNFSE;
    }

    // new method 'update' usign the object xmlnfse from the Class XMLNFSE
    // author: maurizio
    // update
    public void atualizarXMLNFSE(XMLNFSE xmlnfse, Persistencia persistencia) throws Exception {
        String sql = "update xmlnfse set CNPJ=?, Praca=?, Serie=?, Numero=?, Dt_Nota=?,"
                + "Hr_Nota=?, XML_Envio=?, XML_Retorno=?, Dt_Envio=?, Hr_Envio=?, "
                + "Dt_Retorno=?, Hr_Retorno=? "
                + "where CNPJ=? and Praca=? and Serie=? and Numero=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmlnfse.getCNPJ());
            consulta.setInt(xmlnfse.getPraca());
            consulta.setString(xmlnfse.getSerie());
            consulta.setBigDecimal(xmlnfse.getNumero());
            consulta.setString(xmlnfse.getDt_Nota());
            consulta.setString(xmlnfse.getHr_Nota());
            consulta.setString(xmlnfse.getXML_Envio());
            consulta.setString(xmlnfse.getXML_Retorno());
            consulta.setString(xmlnfse.getDt_Envio());
            consulta.setString(xmlnfse.getHr_Envio());
            consulta.setString(xmlnfse.getDt_Retorno());
            consulta.setString(xmlnfse.getHr_Retorno());
            consulta.setString(xmlnfse.getCNPJ());
            consulta.setInt(xmlnfse.getPraca());
            consulta.setString(xmlnfse.getSerie());
            consulta.setBigDecimal(xmlnfse.getNumero());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.atualizarXMLNFSE - " + e.getMessage() + "\r\n"
                    + "update xmlnfse set CNPJ=" + xmlnfse.getCNPJ() + ", Praca=" + xmlnfse.getPraca() + ", Serie=" + xmlnfse.getSerie() + ", "
                    + "Numero=" + xmlnfse.getNumero() + ", Dt_Nota=" + xmlnfse.getDt_Nota() + ","
                    + "Hr_Nota=" + xmlnfse.getHr_Nota() + ", XML_Envio=" + xmlnfse.getXML_Envio() + ", XML_Retorno=" + xmlnfse.getXML_Retorno() + ", "
                    + "Dt_Envio=" + xmlnfse.getDt_Envio() + ", Hr_Envio=" + xmlnfse.getHr_Envio() + ", "
                    + "Dt_Retorno=" + xmlnfse.getDt_Retorno() + ", Hr_Retorno=" + xmlnfse.getHr_Retorno()
                    + "where CNPJ=" + xmlnfse.getCNPJ() + " and Praca=" + xmlnfse.getPraca() + " and Serie=" + xmlnfse.getSerie() + " and Numero=" + xmlnfse.getNumero());
        }
    }

    // author: maurizio
    // delete
    public void excluirXMLNFSE(XMLNFSE xmlnfse, Persistencia persistencia) throws Exception {
        String sql = "delete from xmlnfse where CNPJ=? and Praca=? and Serie=? and Numero=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmlnfse.getCNPJ());
            consulta.setInt(xmlnfse.getPraca());
            consulta.setString(xmlnfse.getSerie());
            consulta.setBigDecimal(xmlnfse.getNumero());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.excluirXMLNFSE - " + e.getMessage() + "\r\n"
                    + "delete from xmlnfse where CNPJ=" + xmlnfse.getCNPJ() + " and Praca=" + xmlnfse.getPraca() + " "
                    + " and Serie=" + xmlnfse.getSerie() + " and Numero=" + xmlnfse.getNumero());
        }
    }

    /**
     * Grava retorno dado para uma nota
     *
     * @param CNPJ - CNPJ do emissor
     * @param Praca - Praca de emmissão da nota
     * @param Serie - Série da nota
     * @param Numero - Número da nota
     * @param XML - XML de retorno
     * @param Dt_Retorno - Data de retorno
     * @param Hr_Retorno - Hora de retorno
     * @param persistencia - Conexão com o banco
     * @throws Exception
     */
    public void gravaRetornoNota(String CNPJ, int Praca, String Serie, String Numero, String XML,
            String Dt_Retorno, String Hr_Retorno, Persistencia persistencia) throws Exception {
        String sql = "update XMLNFSE set"
                + " XML_Retorno = ?,"
                + " Dt_Retorno = ?,"
                + " Hr_Retorno = ?"
                + " where CNPJ = ?"
                + " and Praca = ?"
                + " and Serie = ?"
                + " and Numero = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(XML);
            consulta.setString(Dt_Retorno);
            consulta.setString(Hr_Retorno);
            consulta.setString(CNPJ);
            consulta.setInt(Praca);
            consulta.setString(Serie);
            consulta.setString(Numero);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.gravaRetornoNota - " + e.getMessage() + "\r\n"
                    + "update XMLNFSE set"
                    + " XML_Retorno = " + XML + ","
                    + " Dt_Retorno = " + Dt_Retorno + ","
                    + " Hr_Retorno = " + Hr_Retorno
                    + " where CNPJ = " + CNPJ
                    + " and Praca = " + Praca
                    + " and Serie = " + Serie
                    + " and Numero = " + Numero);
        }
    }

    /**
     * Grava retorno dado para uma nota com codcidade
     *
     * @param CNPJ - CNPJ do emissor
     * @param Praca - Praca de emmissão da nota
     * @param Serie - Série da nota
     * @param Numero - Número da nota
     * @param XML - XML de retorno
     * @param Dt_Envio
     * @param Hr_Envio
     * @param Dt_Retorno - Data de retorno
     * @param Hr_Retorno - Hora de retorno
     * @param CodCidade - Código da Cidade no IBGE
     * @param protocolo
     * @param persistencia - Conexão com o banco
     * @throws Exception
     */
    public void gravaRetornoNota(String CNPJ, int Praca, String Serie, String Numero, String XML, String Dt_Envio, String Hr_Envio,
            String Dt_Retorno, String Hr_Retorno, String CodCidade, String protocolo, Persistencia persistencia) throws Exception {
        String sql = "update XMLNFSE set"
                + " XML_Retorno = ?,"
                + " Dt_Retorno = ?,"
                + " Hr_Retorno = ?,"
                + " Dt_Envio = ?,"
                + " Hr_Envio = ?,"
                + " Protocolo = ?"
                + " where CNPJ = ?"
                + " and Praca = ?"
                + " and Serie = ?"
                + " and Numero = ?"
                + " and CodCidade = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(XML);
            consulta.setString(Dt_Retorno);
            consulta.setString(Hr_Retorno);
            consulta.setString(Dt_Envio);
            consulta.setString(Hr_Envio);
            consulta.setString(protocolo);
            consulta.setString(CNPJ);
            consulta.setInt(Praca);
            consulta.setString(Serie);
            consulta.setString(Numero);
            consulta.setString(CodCidade);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.gravaRetornoNota - " + e.getMessage() + "\r\n"
                    + "update XMLNFSE set"
                    + " XML_Retorno = " + XML + ","
                    + " Dt_Retorno = " + Dt_Retorno + ","
                    + " Hr_Retorno = " + Hr_Retorno + ","
                    + " Dt_Envio = " + Dt_Retorno + ","
                    + " Hr_Envio = " + Hr_Retorno + ""
                    + " where CNPJ = " + CNPJ
                    + " and Praca = " + Praca
                    + " and Serie = " + Serie
                    + " and Numero = " + Numero
                    + " and CodCidade = " + CodCidade);
        }
    }

    /**
     * Atualiza nota para envio
     *
     * @param CNPJ - CNPJ do emissor
     * @param Praca - Praça da nota
     * @param Serie - Serie da nota
     * @param Numero - Numero da nota
     * @param Dt_Nota - Data da nota (vai no corpo do XML)
     * @param Hr_Nota - Hora da nota (vai no corpo do XML)
     * @param XML - XML de envio, já assinado
     * @param Dt_Envio - Data do envio
     * @param Hr_Envio - Hora do envio
     * @param persistencia - Conexão com o banco
     * @throws Exception
     */
    public void AtualizaNota(String CNPJ, int Praca, String Serie, String Numero, String Dt_Nota, String Hr_Nota,
            String XML, String Dt_Envio, String Hr_Envio, Persistencia persistencia) throws Exception {
        String sql;
        sql = "update XMLNFSE set"
                + " XML_Envio = ?,"
                + " Dt_Nota = ?,"
                + " Hr_Nota = ?,"
                + " Dt_Envio = ?,"
                + " Hr_Envio = ?,"
                + " Dt_Retorno = ?,"
                + " Hr_Retorno = ?,"
                + " Xml_Retorno = ?"
                + " where CNPJ = ?"
                + " and Praca = ?"
                + " and Serie = ?"
                + " and Numero = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(XML);
            consulta.setString(Dt_Nota);
            consulta.setString(Hr_Nota);
            consulta.setString(Dt_Envio);
            consulta.setString(Hr_Envio);
            consulta.setString(null);
            consulta.setString(null);
            consulta.setString(null);
            consulta.setString(CNPJ);
            consulta.setInt(Praca);
            consulta.setString(Serie);
            consulta.setString(Numero);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.AtualizaNota - " + e.getMessage() + "\r\n"
                    + "update XMLNFSE set"
                    + " XML_Envio = " + XML + ","
                    + " Dt_Nota = " + Dt_Nota + ","
                    + " Hr_Nota = " + Hr_Nota + ","
                    + " Dt_Envio = " + Dt_Envio + ","
                    + " Hr_Envio = " + Hr_Envio + ","
                    + " Dt_Retorno = " + null + ","
                    + " Hr_Retorno = " + null + ","
                    + " Xml_Retorno = " + null
                    + " where CNPJ = " + CNPJ
                    + " and Praca = " + Praca
                    + " and Serie = " + Serie
                    + " and Numero = " + Numero);
        }
    }

    /**
     * Lista os dados de uma nota enviada
     *
     * @param CNPJ - CNPJ do emissor
     * @param Praca - Praça da nota
     * @param Serie - Série da nota
     * @param Numero - Número da nota
     * @param persistencia - Conexão com o banco
     * @return - Retorno a nota
     * @throws Exception
     */
    public XMLNFSE ListaNota(String CNPJ, int Praca, String Serie, String Numero, Persistencia persistencia) throws Exception {
        Consulta consult;
        String sql;
        XMLNFSE nota;
        try {
            sql = "select XML_Envio, Dt_Nota, Hr_Nota, Dt_Envio, Hr_Envio,"
                    + " XML_Retorno, Dt_Retorno, Hr_Retorno from XMLNFSE"
                    + " where CNPJ = ?"
                    + " and Praca = ?"
                    + " and Serie = ?"
                    + " and Numero = ?";
            consult = new Consulta(sql, persistencia);
            consult.setString(CNPJ);
            consult.setInt(Praca);
            consult.setString(Serie);
            consult.setString(Numero);
            consult.select();
            nota = new XMLNFSE();
            while (consult.Proximo()) {
                nota.setCNPJ(CNPJ);
                nota.setPraca(Praca);
                nota.setSerie(Serie);
                nota.setNumero(Numero);
                nota.setXML_Envio(consult.getString("XML_Envio"));
                nota.setDt_Nota(consult.getString("Dt_Nota"));
                nota.setHr_Nota(consult.getString("Hr_Nota"));
                nota.setDt_Envio(consult.getString("Dt_Envio"));
                nota.setHr_Envio(consult.getString("Hr_Envio"));
                nota.setXML_Retorno(consult.getString("XML_Retorno"));
                nota.setDt_Retorno(consult.getString("Dt_Retorno"));
                nota.setHr_Retorno(consult.getString("Hr_Retorno"));
            }
            consult.Close();
            return nota;
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.ListaNota - " + e.getMessage() + "\r\n"
                    + "select XML_Envio, Dt_Nota, Hr_Nota, Dt_Envio, Hr_Envio,"
                    + " XML_Retorno, Dt_Retorno, Hr_Retorno from XMLNFSE"
                    + " where CNPJ = " + CNPJ
                    + " and Praca = " + Praca
                    + " and Serie = " + Serie
                    + " and Numero = " + Numero);
        }
    }

    /**
     * Lista os dados de uma nota enviada
     *
     * @param CNPJ - CNPJ do emissor
     * @param Praca - Praça da nota
     * @param Serie - Série da nota
     * @param Numero - Número da nota
     * @param persistencia - Conexão com o banco
     * @return - Retorno a nota
     * @throws Exception
     */
    public XMLNFSE DatasNota(String CNPJ, int Praca, String Serie, String Numero, Persistencia persistencia) throws Exception {
        Consulta consult;
        String sql;
        try {
            sql = "select Dt_Nota, Dt_Envio, Dt_Retorno from XMLNFSE"
                    + " where CNPJ = ?"
                    + " and Praca = ?"
                    + " and Serie = ?"
                    + " and Numero = ?";
            consult = new Consulta(sql, persistencia);
            consult.setString(CNPJ);
            consult.setInt(Praca);
            consult.setString(Serie);
            consult.setString(Numero);
            consult.select();
            XMLNFSE nota = new XMLNFSE();
            while (consult.Proximo()) {
                nota.setCNPJ(CNPJ);
                nota.setPraca(Praca);
                nota.setSerie(Serie);
                nota.setNumero(Numero);
                nota.setDt_Nota(consult.getString("Dt_Nota"));
                nota.setDt_Envio(consult.getString("Dt_Envio"));
                nota.setDt_Retorno(consult.getString("Dt_Retorno"));
            }
            consult.Close();
            return nota;
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.DatasNota - " + e.getMessage() + "\r\n"
                    + "select Dt_Nota, Dt_Envio, Dt_Retorno from XMLNFSE"
                    + " where CNPJ = " + CNPJ
                    + " and Praca = " + Praca
                    + " and Serie = " + Serie
                    + " and Numero = " + Numero);
        }
    }

    public List<XMLNFSE> BuscaNotasEmitir(Persistencia persistencia) throws Exception {
        List<XMLNFSE> retorno = new ArrayList();
        try {
            String sql = "select cnpj, praca, serie, numero, xml_envio from xmlnfse where dt_retorno is null ";
            XMLNFSE nfse;
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                nfse = new XMLNFSE();
                nfse.setCNPJ(consult.getString("cnpj"));
                nfse.setPraca(consult.getInt("praca"));
                nfse.setSerie(consult.getString("serie"));
                nfse.setNumero(consult.getString("numero"));
                nfse.setXML_Envio(consult.getString("xml_envio"));
                retorno.add(nfse);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.BuscaNotasEmitir - " + e.getMessage() + "\r\n"
                    + "select cnpj, praca, serie, numero, xml_envio from xmlnfse where dt_retorno is null");
        }
    }

    public List<XMLNFSE> BuscaNotasEmitir(Persistencia persistencia, String Cidade) throws Exception {
        List<XMLNFSE> retorno = new ArrayList();
        try {            
            // Consultar NOtas pendentes de envio
            String sql = "select cnpj, praca, serie, numero, REPLACE(xml_envio, char(160), '') xml_envio, dt_envio, dt_retorno, Hr_Retorno, Hr_Envio, Protocolo "
                    + " from xmlnfse where codcidade = ? and dt_envio is null and dt_retorno is null ";
            XMLNFSE nfse;
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Cidade);
            consult.select();
            while (consult.Proximo()) {
                nfse = new XMLNFSE();
                nfse.setCNPJ(consult.getString("cnpj"));
                nfse.setPraca(consult.getInt("praca"));
                nfse.setSerie(consult.getString("serie"));
                nfse.setNumero(consult.getString("numero"));
                nfse.setXML_Envio(consult.getString("xml_envio"));
                nfse.setDt_Envio(consult.getString("dt_envio"));
                nfse.setDt_Retorno(consult.getString("dt_retorno"));
                nfse.setHr_Retorno(consult.getString("Hr_Retorno"));
                nfse.setHr_Envio(consult.getString("Hr_Envio"));
                nfse.setProtocolo(consult.getString("Protocolo"));
                retorno.add(nfse);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.BuscaNotasEmitir - " + e.getMessage() + "\r\n"
                    + "select cnpj, praca, serie, numero, xml_envio, dt_envio, dt_retorno, Hr_Retorno, Hr_Envio, Protocolo "
                    + " from xmlnfse where codcidade = " + Cidade + " and (dt_envio is null or dt_retorno is null) ");
        }
    }

    public List<XMLNFSE> buscaNotasConsultar(Persistencia persistencia, String Cidade) throws Exception {
        List<XMLNFSE> retorno = new ArrayList();
        try {
            String sql = "select cnpj, praca, serie, numero, xml_envio, dt_envio, dt_retorno, Hr_Retorno, Hr_Envio, Protocolo "
                    + " from xmlnfse where codcidade = ? and dt_retorno is null "
                    + " and numero in (select numero from nfiscal where nfiscal.praca = xmlnfse.praca) ";
            XMLNFSE nfse;
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Cidade);
            consult.select();
            while (consult.Proximo()) {
                nfse = new XMLNFSE();
                nfse.setCNPJ(consult.getString("cnpj"));
                nfse.setPraca(consult.getInt("praca"));
                nfse.setSerie(consult.getString("serie"));
                nfse.setNumero(consult.getString("numero"));
                nfse.setXML_Envio(consult.getString("xml_envio"));
                nfse.setDt_Envio(consult.getString("dt_envio"));
                nfse.setDt_Retorno(consult.getString("dt_retorno"));
                nfse.setHr_Retorno(consult.getString("Hr_Retorno"));
                nfse.setHr_Envio(consult.getString("Hr_Envio"));
                nfse.setProtocolo(consult.getString("Protocolo"));
                retorno.add(nfse);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLNFSEDao.BuscaNotasEmitir - " + e.getMessage() + "\r\n"
                    + "select cnpj, praca, serie, numero, xml_envio, dt_envio, dt_retorno, Hr_Retorno, Hr_Envio, Protocolo "
                    + " from xmlnfse where codcidade = " + Cidade + " and (dt_envio is null or dt_retorno is null) ");
        }
    }
}

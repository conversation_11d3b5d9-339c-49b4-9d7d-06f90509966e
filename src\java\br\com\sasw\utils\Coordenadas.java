/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.utils;

import java.util.List;
import org.primefaces.model.map.LatLng;

/**
 *
 * <AUTHOR>
 */
public class Coordenadas {

    public static LatLng getCentroMapa(List<LatLng> latLngs) {
        if (latLngs.size() == 1) {
            return latLngs.get(0);
        }

        double x = 0;
        double y = 0;
        double z = 0;

        for (LatLng latLng : latLngs) {
            double latitude = latLng.getLat() * (Math.PI / 180);
            double longitude = latLng.getLng() * (Math.PI / 180);

            x += (Math.cos(latitude) * Math.cos(longitude));
            y += (Math.cos(latitude) * Math.sin(longitude));
            z += Math.sin(latitude);
        }

        double total = latLngs.size();

        x = x / total;
        y = y / total;
        z = z / total;

        double centralLongitude = Math.atan2(y, x) * (180 / Math.PI);
        double centralSquareRoot = Math.sqrt(x * x + y * y);
        double centralLatitude = Math.atan2(z, centralSquareRoot) * (180 / Math.PI);
        return new LatLng(centralLatitude, centralLongitude);
    }
}

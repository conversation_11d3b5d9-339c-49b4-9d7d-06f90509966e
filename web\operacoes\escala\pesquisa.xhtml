<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <h:form id="formPesquisa">
        <p:hotkey bind="esc" oncomplete="PF('dlgPesquisa').hide()"/>

        <p:dialog
            widgetVar="dlgPesquisa"
            positionType="absolute"
            responsive="true"
            draggable="false"
            modal="true"
            closable="true"
            resizable="false"
            dynamic="true"
            showEffect="drop"
            hideEffect="drop"
            closeOnEscape="false" width="400"
            style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
            <f:facet name="header">
                <img src="../assets/img/icone_tesouraria_entradas.png" height="40" width="40"/>
                <p:spacer width="5px"/>
                <h:outputText
                    value="#{localemsgs.PesquisarNumerario}"
                    style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"
                    />
            </f:facet>

            <p:panel id="pesquisar" style="background: transparent">
                <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                    <div style="flex-grow: 1; min-width: 50%;">
                        <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                        <p:selectOneRadio
                            id="radioOpcoes"
                            value="#{tesEntrada.chavePesquisa}"
                            unselectable="true"
                            layout="pageDirection"
                            >
                            <f:selectItem itemLabel="#{localemsgs.Guia}" itemValue="guia" />
                            <f:selectItem itemLabel="#{localemsgs.CliOri}" itemValue="cliente1" />
                            <f:selectItem itemLabel="#{localemsgs.CliDst}" itemValue="cliente2" />

                            <p:ajax update="direita" />
                        </p:selectOneRadio>
                    </div>

                    <p:outputPanel
                        id="direita"
                        style="padding-left: 16px;">
                        <p:outputPanel>
                            <p:outputLabel for="opcao" rendered="#{tesEntrada.chavePesquisa eq 'guia'}" value="#{localemsgs.Guia}: "/>
                            <p:outputLabel for="opcao" rendered="#{tesEntrada.chavePesquisa eq 'cliente1'}" value="#{localemsgs.CliOri}: "/>
                            <p:outputLabel for="opcao" rendered="#{tesEntrada.chavePesquisa eq 'cliente2'}" value="#{localemsgs.CliDst}: "/>

                            <p:inputText
                                id="opcao"
                                value="#{tesEntrada.valorPesquisa}"
                                style="width: 100%" maxlength="60">
                            </p:inputText>
                        </p:outputPanel>
                    </p:outputPanel>
                </div>

                <div class="form-inline">
                    <p:commandLink action="#{tesEntrada.pesquisar()}"
                                   update="main:tabelaEscala msgs"
                                   oncomplete="PF('dlgPesquisa').hide()"
                                   title="#{localemsgs.Pesquisar}">
                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" width="40" height="40" />
                    </p:commandLink>
                </div>
            </p:panel>
        </p:dialog>
    </h:form>

</ui:composition>
/*
 */
package br.com.sasw.lazydatamodels;

import Controller.CofreInteligente.CofreIntelSatMobWeb;
import Dados.Persistencia;
import SasBeansCompostas.TesCofresResClientes;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class CofresLazyList extends LazyDataModel<TesCofresResClientes> {

    private static final long serialVersionUID = 1L;
    private List<TesCofresResClientes> cofres;
    private final CofreIntelSatMobWeb cofressatmobweb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public CofresLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.cofressatmobweb = new CofreIntelSatMobWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<TesCofresResClientes> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.cofres = this.cofressatmobweb.ListagemPaginada(first, pageSize, filters, this.codPessoa, this.persistencia);

            // set the total of players
            setRowCount(this.cofressatmobweb.Contagem(filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.cofres;
    }

    @Override
    public Object getRowKey(TesCofresResClientes cofre) {
        return cofre.getClientes().getCodCofre();
    }

    @Override
    public TesCofresResClientes getRowData(String codcofre) {
        BigDecimal cc = new BigDecimal(codcofre);
        for (TesCofresResClientes cofre : this.cofres) {
            if (cc.equals(cofre.getClientes().getCodCofre())) {
                return cofre;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

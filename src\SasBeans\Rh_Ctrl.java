/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Rh_Ctrl {

    private BigDecimal Matr;
    private BigDecimal CodFil;
    private String Nome_Guer;
    private String Situacao;
    private String Dt_Ini;
    private String Dt_Fim;
    private BigDecimal SupervDiu;
    private BigDecimal SupervNot;
    private BigDecimal HorasTrab;
    private BigDecimal HorasExtr;
    private BigDecimal HsNeg;
    private BigDecimal HE50;
    private BigDecimal HE100;
    private BigDecimal HE70;
    private BigDecimal HE3;
    private BigDecimal HECC1;
    private BigDecimal HECC2;
    private BigDecimal HECC3;
    private BigDecimal HESup;
    private BigDecimal AdNot;
    private BigDecimal HsNotRed;
    private BigDecimal HsAdNotProrrog;
    private BigDecimal IntraJ;
    private BigDecimal DiasTrab;
    private BigDecimal DiasFolga;
    private BigDecimal Faltas;
    private BigDecimal Suspensao;
    private BigDecimal DiasFerias;
    private String DtRetorno;
    private String DtIniFer;
    private String DtFimFer;
    private BigDecimal HsAbnFalta;
    private BigDecimal HsAbnFeriado;
    private BigDecimal HsProjecao;
    private BigDecimal HsInc;
    private BigDecimal FaltasJust;
    private BigDecimal HsAtMedico;
    private BigDecimal Reciclagem;
    private BigDecimal Sindicato;
    private BigDecimal Transito;
    private BigDecimal DiasInsal;
    private BigDecimal DiasPeric;
    private BigDecimal DiasRonda;
    private BigDecimal DiasCHSup;
    private BigDecimal DiasEscNormal;
    private BigDecimal DiasFerTrab;
    private BigDecimal HEFeriado;
    private BigDecimal HSFeriado;
    private String Secao;
    private String Local;
    private int Regional;
    private BigDecimal DescDSR;
    private String Calculo;
    private int PEL;
    private BigDecimal QtdeFT;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the Matr
     */
    public BigDecimal getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Nome_Guer
     */
    public String getNome_Guer() {
        return Nome_Guer;
    }

    /**
     * @param Nome_Guer the Nome_Guer to set
     */
    public void setNome_Guer(String Nome_Guer) {
        this.Nome_Guer = Nome_Guer;
    }

    /**
     * @return the Situacao
     */
    public String getSituacao() {
        return Situacao;
    }

    /**
     * @param Situacao the Situacao to set
     */
    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    /**
     * @return the Dt_Ini
     */
    public String getDt_Ini() {
        return Dt_Ini;
    }

    /**
     * @param Dt_Ini the Dt_Ini to set
     */
    public void setDt_Ini(String Dt_Ini) {
        this.Dt_Ini = Dt_Ini;
    }

    /**
     * @return the Dt_Fim
     */
    public String getDt_Fim() {
        return Dt_Fim;
    }

    /**
     * @param Dt_Fim the Dt_Fim to set
     */
    public void setDt_Fim(String Dt_Fim) {
        this.Dt_Fim = Dt_Fim;
    }

    /**
     * @return the SupervDiu
     */
    public BigDecimal getSupervDiu() {
        return SupervDiu;
    }

    /**
     * @param SupervDiu the SupervDiu to set
     */
    public void setSupervDiu(String SupervDiu) {
        try {
            this.SupervDiu = new BigDecimal(SupervDiu);
        } catch (Exception e) {
            this.SupervDiu = new BigDecimal("0");
        }
    }

    /**
     * @return the SupervNot
     */
    public BigDecimal getSupervNot() {
        return SupervNot;
    }

    /**
     * @param SupervNot the SupervNot to set
     */
    public void setSupervNot(String SupervNot) {
        try {
            this.SupervNot = new BigDecimal(SupervNot);
        } catch (Exception e) {
            this.SupervNot = new BigDecimal("0");
        }
    }

    /**
     * @return the HorasTrab
     */
    public BigDecimal getHorasTrab() {
        return HorasTrab;
    }

    /**
     * @param HorasTrab the HorasTrab to set
     */
    public void setHorasTrab(String HorasTrab) {
        try {
            this.HorasTrab = new BigDecimal(HorasTrab);
        } catch (Exception e) {
            this.HorasTrab = new BigDecimal("0");
        }
    }

    /**
     * @return the HorasExtr
     */
    public BigDecimal getHorasExtr() {
        return HorasExtr;
    }

    /**
     * @param HorasExtr the HorasExtr to set
     */
    public void setHorasExtr(String HorasExtr) {
        try {
            this.HorasExtr = new BigDecimal(HorasExtr);
        } catch (Exception e) {
            this.HorasExtr = new BigDecimal("0");
        }
    }

    /**
     * @return the HsNeg
     */
    public BigDecimal getHsNeg() {
        return HsNeg;
    }

    /**
     * @param HsNeg the HsNeg to set
     */
    public void setHsNeg(String HsNeg) {
        try {
            this.HsNeg = new BigDecimal(HsNeg);
        } catch (Exception e) {
            this.HsNeg = new BigDecimal("0");
        }
    }

    /**
     * @return the HE50
     */
    public BigDecimal getHE50() {
        return HE50;
    }

    /**
     * @param HE50 the HE50 to set
     */
    public void setHE50(String HE50) {
        try {
            this.HE50 = new BigDecimal(HE50);
        } catch (Exception e) {
            this.HE50 = new BigDecimal("0");
        }
    }

    /**
     * @return the HE100
     */
    public BigDecimal getHE100() {
        return HE100;
    }

    /**
     * @param HE100 the HE100 to set
     */
    public void setHE100(String HE100) {
        try {
            this.HE100 = new BigDecimal(HE100);
        } catch (Exception e) {
            this.HE100 = new BigDecimal("0");
        }
    }

    /**
     * @return the HE70
     */
    public BigDecimal getHE70() {
        return HE70;
    }

    /**
     * @param HE70 the HE70 to set
     */
    public void setHE70(String HE70) {
        try {
            this.HE70 = new BigDecimal(HE70);
        } catch (Exception e) {
            this.HE70 = new BigDecimal("0");
        }
    }

    /**
     * @return the HE3
     */
    public BigDecimal getHE3() {
        return HE3;
    }

    /**
     * @param HE3 the HE3 to set
     */
    public void setHE3(String HE3) {
        try {
            this.HE3 = new BigDecimal(HE3);
        } catch (Exception e) {
            this.HE3 = new BigDecimal("0");
        }
    }

    /**
     * @return the HECC1
     */
    public BigDecimal getHECC1() {
        return HECC1;
    }

    /**
     * @param HECC1 the HECC1 to set
     */
    public void setHECC1(String HECC1) {
        try {
            this.HECC1 = new BigDecimal(HECC1);
        } catch (Exception e) {
            this.HECC1 = new BigDecimal("0");
        }
    }

    /**
     * @return the HECC2
     */
    public BigDecimal getHECC2() {
        return HECC2;
    }

    /**
     * @param HECC2 the HECC2 to set
     */
    public void setHECC2(String HECC2) {
        try {
            this.HECC2 = new BigDecimal(HECC2);
        } catch (Exception e) {
            this.HECC2 = new BigDecimal("0");
        }
    }

    /**
     * @return the HECC3
     */
    public BigDecimal getHECC3() {
        return HECC3;
    }

    /**
     * @param HECC3 the HECC3 to set
     */
    public void setHECC3(String HECC3) {
        try {
            this.HECC3 = new BigDecimal(HECC3);
        } catch (Exception e) {
            this.HECC3 = new BigDecimal("0");
        }
    }

    /**
     * @return the HESup
     */
    public BigDecimal getHESup() {
        return HESup;
    }

    /**
     * @param HESup the HESup to set
     */
    public void setHESup(String HESup) {
        try {
            this.HESup = new BigDecimal(HESup);
        } catch (Exception e) {
            this.HESup = new BigDecimal("0");
        }
    }

    /**
     * @return the AdNot
     */
    public BigDecimal getAdNot() {
        return AdNot;
    }

    /**
     * @param AdNot the AdNot to set
     */
    public void setAdNot(String AdNot) {
        try {
            this.AdNot = new BigDecimal(AdNot);
        } catch (Exception e) {
            this.AdNot = new BigDecimal("0");
        }
    }

    /**
     * @return the HsNotRed
     */
    public BigDecimal getHsNotRed() {
        return HsNotRed;
    }

    /**
     * @param HsNotRed the HsNotRed to set
     */
    public void setHsNotRed(String HsNotRed) {
        try {
            this.HsNotRed = new BigDecimal(HsNotRed);
        } catch (Exception e) {
            this.HsNotRed = new BigDecimal("0");
        }
    }

    /**
     * @return the HsAdNotProrrog
     */
    public BigDecimal getHsAdNotProrrog() {
        return HsAdNotProrrog;
    }

    /**
     * @param HsAdNotProrrog the HsAdNotProrrog to set
     */
    public void setHsAdNotProrrog(String HsAdNotProrrog) {
        try {
            this.HsAdNotProrrog = new BigDecimal(HsAdNotProrrog);
        } catch (Exception e) {
            this.HsAdNotProrrog = new BigDecimal("0");
        }
    }

    /**
     * @return the IntraJ
     */
    public BigDecimal getIntraJ() {
        return IntraJ;
    }

    /**
     * @param IntraJ the IntraJ to set
     */
    public void setIntraJ(String IntraJ) {
        try {
            this.IntraJ = new BigDecimal(IntraJ);
        } catch (Exception e) {
            this.IntraJ = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasTrab
     */
    public BigDecimal getDiasTrab() {
        return DiasTrab;
    }

    /**
     * @param DiasTrab the DiasTrab to set
     */
    public void setDiasTrab(String DiasTrab) {
        try {
            this.DiasTrab = new BigDecimal(DiasTrab);
        } catch (Exception e) {
            this.DiasTrab = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasFolga
     */
    public BigDecimal getDiasFolga() {
        return DiasFolga;
    }

    /**
     * @param DiasFolga the DiasFolga to set
     */
    public void setDiasFolga(String DiasFolga) {
        try {
            this.DiasFolga = new BigDecimal(DiasFolga);
        } catch (Exception e) {
            this.DiasFolga = new BigDecimal("0");
        }
    }

    /**
     * @return the Faltas
     */
    public BigDecimal getFaltas() {
        return Faltas;
    }

    /**
     * @param Faltas the Faltas to set
     */
    public void setFaltas(String Faltas) {
        try {
            this.Faltas = new BigDecimal(Faltas);
        } catch (Exception e) {
            this.Faltas = new BigDecimal("0");
        }
    }

    /**
     * @return the Suspensao
     */
    public BigDecimal getSuspensao() {
        return Suspensao;
    }

    /**
     * @param Suspensao the Suspensao to set
     */
    public void setSuspensao(String Suspensao) {
        try {
            this.Suspensao = new BigDecimal(Suspensao);
        } catch (Exception e) {
            this.Suspensao = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasFerias
     */
    public BigDecimal getDiasFerias() {
        return DiasFerias;
    }

    /**
     * @param DiasFerias the DiasFerias to set
     */
    public void setDiasFerias(String DiasFerias) {
        try {
            this.DiasFerias = new BigDecimal(DiasFerias);
        } catch (Exception e) {
            this.DiasFerias = new BigDecimal("0");
        }
    }

    /**
     * @return the DtRetorno
     */
    public String getDtRetorno() {
        return DtRetorno;
    }

    /**
     * @param DtRetorno the DtRetorno to set
     */
    public void setDtRetorno(String DtRetorno) {
        this.DtRetorno = DtRetorno;
    }

    /**
     * @return the DtIniFer
     */
    public String getDtIniFer() {
        return DtIniFer;
    }

    /**
     * @param DtIniFer the DtIniFer to set
     */
    public void setDtIniFer(String DtIniFer) {
        this.DtIniFer = DtIniFer;
    }

    /**
     * @return the DtFimFer
     */
    public String getDtFimFer() {
        return DtFimFer;
    }

    /**
     * @param DtFimFer the DtFimFer to set
     */
    public void setDtFimFer(String DtFimFer) {
        this.DtFimFer = DtFimFer;
    }

    /**
     * @return the HsAbnFalta
     */
    public BigDecimal getHsAbnFalta() {
        return HsAbnFalta;
    }

    /**
     * @param HsAbnFalta the HsAbnFalta to set
     */
    public void setHsAbnFalta(String HsAbnFalta) {
        try {
            this.HsAbnFalta = new BigDecimal(HsAbnFalta);
        } catch (Exception e) {
            this.HsAbnFalta = new BigDecimal("0");
        }
    }

    /**
     * @return the HsAbnFeriado
     */
    public BigDecimal getHsAbnFeriado() {
        return HsAbnFeriado;
    }

    /**
     * @param HsAbnFeriado the HsAbnFeriado to set
     */
    public void setHsAbnFeriado(String HsAbnFeriado) {
        try {
            this.HsAbnFeriado = new BigDecimal(HsAbnFeriado);
        } catch (Exception e) {
            this.HsAbnFeriado = new BigDecimal("0");
        }
    }

    /**
     * @return the HsProjecao
     */
    public BigDecimal getHsProjecao() {
        return HsProjecao;
    }

    /**
     * @param HsProjecao the HsProjecao to set
     */
    public void setHsProjecao(String HsProjecao) {
        try {
            this.HsProjecao = new BigDecimal(HsProjecao);
        } catch (Exception e) {
            this.HsProjecao = new BigDecimal("0");
        }
    }

    /**
     * @return the HsInc
     */
    public BigDecimal getHsInc() {
        return HsInc;
    }

    /**
     * @param HsInc the HsInc to set
     */
    public void setHsInc(String HsInc) {
        try {
            this.HsInc = new BigDecimal(HsInc);
        } catch (Exception e) {
            this.HsInc = new BigDecimal("0");
        }
    }

    /**
     * @return the FaltasJust
     */
    public BigDecimal getFaltasJust() {
        return FaltasJust;
    }

    /**
     * @param FaltasJust the FaltasJust to set
     */
    public void setFaltasJust(String FaltasJust) {
        try {
            this.FaltasJust = new BigDecimal(FaltasJust);
        } catch (Exception e) {
            this.FaltasJust = new BigDecimal("0");
        }
    }

    /**
     * @return the HsAtMedico
     */
    public BigDecimal getHsAtMedico() {
        return HsAtMedico;
    }

    /**
     * @param HsAtMedico the HsAtMedico to set
     */
    public void setHsAtMedico(String HsAtMedico) {
        try {
            this.HsAtMedico = new BigDecimal(HsAtMedico);
        } catch (Exception e) {
            this.HsAtMedico = new BigDecimal("0");
        }
    }

    /**
     * @return the Reciclagem
     */
    public BigDecimal getReciclagem() {
        return Reciclagem;
    }

    /**
     * @param Reciclagem the Reciclagem to set
     */
    public void setReciclagem(String Reciclagem) {
        try {
            this.Reciclagem = new BigDecimal(Reciclagem);
        } catch (Exception e) {
            this.Reciclagem = new BigDecimal("0");
        }
    }

    /**
     * @return the Sindicato
     */
    public BigDecimal getSindicato() {
        return Sindicato;
    }

    /**
     * @param Sindicato the Sindicato to set
     */
    public void setSindicato(String Sindicato) {
        try {
            this.Sindicato = new BigDecimal(Sindicato);
        } catch (Exception e) {
            this.Sindicato = new BigDecimal("0");
        }
    }

    /**
     * @return the Transito
     */
    public BigDecimal getTransito() {
        return Transito;
    }

    /**
     * @param Transito the Transito to set
     */
    public void setTransito(String Transito) {
        try {
            this.Transito = new BigDecimal(Transito);
        } catch (Exception e) {
            this.Transito = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasInsal
     */
    public BigDecimal getDiasInsal() {
        return DiasInsal;
    }

    /**
     * @param DiasInsal the DiasInsal to set
     */
    public void setDiasInsal(String DiasInsal) {
        try {
            this.DiasInsal = new BigDecimal(DiasInsal);
        } catch (Exception e) {
            this.DiasInsal = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasPeric
     */
    public BigDecimal getDiasPeric() {
        return DiasPeric;
    }

    /**
     * @param DiasPeric the DiasPeric to set
     */
    public void setDiasPeric(String DiasPeric) {
        try {
            this.DiasPeric = new BigDecimal(DiasPeric);
        } catch (Exception e) {
            this.DiasPeric = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasRonda
     */
    public BigDecimal getDiasRonda() {
        return DiasRonda;
    }

    /**
     * @param DiasRonda the DiasRonda to set
     */
    public void setDiasRonda(String DiasRonda) {
        try {
            this.DiasRonda = new BigDecimal(DiasRonda);
        } catch (Exception e) {
            this.DiasRonda = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasCHSup
     */
    public BigDecimal getDiasCHSup() {
        return DiasCHSup;
    }

    /**
     * @param DiasCHSup the DiasCHSup to set
     */
    public void setDiasCHSup(String DiasCHSup) {
        try {
            this.DiasCHSup = new BigDecimal(DiasCHSup);
        } catch (Exception e) {
            this.DiasCHSup = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasEscNormal
     */
    public BigDecimal getDiasEscNormal() {
        return DiasEscNormal;
    }

    /**
     * @param DiasEscNormal the DiasEscNormal to set
     */
    public void setDiasEscNormal(String DiasEscNormal) {
        try {
            this.DiasEscNormal = new BigDecimal(DiasEscNormal);
        } catch (Exception e) {
            this.DiasEscNormal = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasFerTrab
     */
    public BigDecimal getDiasFerTrab() {
        return DiasFerTrab;
    }

    /**
     * @param DiasFerTrab the DiasFerTrab to set
     */
    public void setDiasFerTrab(String DiasFerTrab) {
        try {
            this.DiasFerTrab = new BigDecimal(DiasFerTrab);
        } catch (Exception e) {
            this.DiasFerTrab = new BigDecimal("0");
        }
    }

    /**
     * @return the HEFeriado
     */
    public BigDecimal getHEFeriado() {
        return HEFeriado;
    }

    /**
     * @param HEFeriado the HEFeriado to set
     */
    public void setHEFeriado(String HEFeriado) {
        try {
            this.HEFeriado = new BigDecimal(HEFeriado);
        } catch (Exception e) {
            this.HEFeriado = new BigDecimal("0");
        }
    }

    /**
     * @return the HSFeriado
     */
    public BigDecimal getHSFeriado() {
        return HSFeriado;
    }

    /**
     * @param HSFeriado the HSFeriado to set
     */
    public void setHSFeriado(String HSFeriado) {
        try {
            this.HSFeriado = new BigDecimal(HSFeriado);
        } catch (Exception e) {
            this.HSFeriado = new BigDecimal("0");
        }
    }

    /**
     * @return the Secao
     */
    public String getSecao() {
        return Secao;
    }

    /**
     * @param Secao the Secao to set
     */
    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    /**
     * @return the Local
     */
    public String getLocal() {
        return Local;
    }

    /**
     * @param Local the Local to set
     */
    public void setLocal(String Local) {
        this.Local = Local;
    }

    /**
     * @return the Regional
     */
    public int getRegional() {
        return Regional;
    }

    /**
     * @param Regional the Regional to set
     */
    public void setRegional(int Regional) {
        this.Regional = Regional;
    }

    /**
     * @return the DescDSR
     */
    public BigDecimal getDescDSR() {
        return DescDSR;
    }

    /**
     * @param DescDSR the DescDSR to set
     */
    public void setDescDSR(String DescDSR) {
        try {
            this.DescDSR = new BigDecimal(DescDSR);
        } catch (Exception e) {
            this.DescDSR = new BigDecimal("0");
        }
    }

    /**
     * @return the Calculo
     */
    public String getCalculo() {
        return Calculo;
    }

    /**
     * @param Calculo the Calculo to set
     */
    public void setCalculo(String Calculo) {
        this.Calculo = Calculo;
    }

    /**
     * @return the PEL
     */
    public int getPEL() {
        return PEL;
    }

    /**
     * @param PEL the PEL to set
     */
    public void setPEL(int PEL) {
        this.PEL = PEL;
    }

    /**
     * @return the QtdeFT
     */
    public BigDecimal getQtdeFT() {
        return QtdeFT;
    }

    /**
     * @param QtdeFT the QtdeFT to set
     */
    public void setQtdeFT(String QtdeFT) {
        try {
            this.QtdeFT = new BigDecimal(QtdeFT);
        } catch (Exception e) {
            this.QtdeFT = new BigDecimal("0");
        }
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public String getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

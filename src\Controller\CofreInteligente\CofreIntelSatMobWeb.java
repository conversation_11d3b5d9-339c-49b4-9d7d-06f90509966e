/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.CofreInteligente;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.MobileHW;
import SasBeans.MobileHWSt;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaPortalSrv;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.TesCofresMov;
import SasBeans.TesEntDN;
import SasBeans.TesEntrada;
import SasBeans.TesSaidas;
import SasBeans.TesSaidasDN;
import SasBeansCompostas.MovimentacaoGeralFuncionario;
import SasBeansCompostas.TesCofresResClientes;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasDaos.AcessosDao;
import SasDaos.ClientesDao;
import SasDaos.FiliaisDao;
import SasDaos.MobileHWDao;
import SasDaos.MobileHWStDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.PessoaPortalSrvDao;
import SasDaos.SASGruposDao;
import SasDaos.TesCofresMovDao;
import SasDaos.TesCofresResDao;
import SasDaos.TesEntDNDao;
import SasDaos.TesEntradaDao;
import SasDaos.TesSaidasDNDao;
import SasDaos.TesSaidasDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CofreIntelSatMobWeb {

    public Clientes clienteCofre(String codigo, String codFil, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.getClientesMobile(codFil, codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacaoPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listaPaginada(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public Integer totalMovimentacaoPaginada(Map filtros, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.totalPaginacao(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todas as movimentações do cofre em um período
     *
     * @param codigo Código do cliente
     * @param codfil Código de filial do cliente
     * @param dataInicio Data inicial do período
     * @param dataFinal Data final do período
     * @param persistencia Conexão com o banco de dados
     * @return Retorna lista com as movimentações do cofre para o período
     * @throws Exception
     */
    public List<TesCofresResClientes> MovimentoCofreCliente(String codigo, String codfil, String dataInicio, String dataFinal, String vCodPessoa, Persistencia persistencia) throws Exception {
        try {
            List<TesCofresResClientes> retorno;
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            retorno = tesCofresResDAO.MovimentoCofreClienteSatMob(codigo, codfil, dataInicio, dataFinal, vCodPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list dados da Tesouraria - " + e.getMessage());
        }
    }

    public List<TesCofresResClientes> ListagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<TesCofresResClientes> retorno;
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            retorno = tesCofresResDAO.ListaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresResClientes> ListagemPaginada(int primeiro, int linhas, Map filtros,
            boolean exibirTodos, Persistencia persistencia) throws Exception {
        try {
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            return tesCofresResDAO.ListaPaginada(primeiro, linhas, filtros, exibirTodos, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }
//    
//    public List<MobileHW> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
//        try {
//            MobileHWDao mobileHWDao = new MobileHWDao();
//            return mobileHWDao.ListaPaginada(primeiro, linhas, filtros, persistencia);
//        } catch (Exception e) {
//            throw new Exception("cofres.falhageral<message>" + e.getMessage());
//        }
//    }

    public Integer Contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            retorno = tesCofresResDAO.TotalCofresMobWeb(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public Integer Contagem(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            retorno = tesCofresResDAO.TotalCofresMobWeb(filtros, exibirTodos, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal TotalVlrTotalCred(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            return tesCofresResDAO.TotalVlrTotalCred(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal TotalSaldoFisTotal(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            return tesCofresResDAO.TotalSaldoFisTotal(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal TotalSaldoFisCst(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            return tesCofresResDAO.TotalSaldoFisCst(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal TotalVlrRecolhido(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            TesCofresResDao tesCofresResDAO = new TesCofresResDao();
            return tesCofresResDAO.TotalVlrRecolhido(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca o nome da filial de um cofre
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String filialCofre(String codFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filiaisDao = new FiliaisDao();
            return filiaisDao.getFilial(codFil, persistencia).getRazaoSocial();
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacaoCofre(BigDecimal codCofre, String data, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listarMovimentacaoDiaria(codCofre, data, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacoesAnteriores(BigDecimal codCofre, String data, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listarMovimentacoesAnteriores(codCofre, data, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacaoCofre(String codCofre, String data1, String data2, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listarMovimentacaoPeriodo(codCofre, data1, data2, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<MobileHWSt> listaStatusCofre(String codigo, String IMEI, String dataInicio, String dataFinal, boolean somenteAlertas,
            Persistencia persistencia) throws Exception {
        try {
            MobileHWStDao mobileHWStDao = new MobileHWStDao();
            return mobileHWStDao.listarStatusCore(codigo, IMEI, dataInicio, dataFinal, somenteAlertas, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public MobileHW buscarInfoCofre(String codCofre, Persistencia persistencia) throws Exception {
        try {
            MobileHWDao mobileHWDao = new MobileHWDao();
            return mobileHWDao.obterInfoCofre(codCofre, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de usuarios
     *
     * @param codCli
     * @param persistencia conexão ao banco de dados
     * @param satellite
     * @return
     * @throws Exception
     */
    public List<UsuarioSatMobWeb> listagemUsuarios(String codCli, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.listaUsuarios(codCli, persistencia, satellite);
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            Boolean existeCliente = acessosDAO.existeCliente(cliente, persistencia);
            if (existeCliente) {
                acessosDAO.atualizaCliente(cliente, persistencia);
            } else {
                acessosDAO.inserirCliente(cliente, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirFilial(SasPWFill filial, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            filial = (SasPWFill) FuncoesString.removeAcentoObjeto(filial);
            filial.setDt_Alter(getDataAtual("SQL"));
            filial.setHr_Alter(getDataAtual("HORA"));
            if (!acessosDAO.existeFilialUsuario(filial.getNome(), filial.getCodFil(), filial.getCodfilAc(), persistencia)) {
                acessosDAO.inserirFilial(filial, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirServicoAutomatico(PessoaPortalSrv pessoaPortalSrv, Persistencia persistencia) throws Exception {
        try {
            PessoaPortalSrvDao pessoaPortalSrvDao = new PessoaPortalSrvDao();
            pessoaPortalSrvDao.inserirServicoAutomatico(pessoaPortalSrv, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void criarAcesso(UsuarioSatMobWeb usuario, Persistencia local, Persistencia central) throws Exception {
        try {
            usuario = (UsuarioSatMobWeb) FuncoesString.removeAcentoObjeto(usuario);
            usuario.getSaspw().setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.getSaspw().setHr_Alter(DataAtual.getDataAtual("HORA"));

            usuario.getPessoa().setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.getPessoa().setHr_Alter(DataAtual.getDataAtual("HORA"));

            AcessosDao acessosDAO = new AcessosDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            PessoaDao pessoadao = new PessoaDao();

            if (acessosDAO.existeUsuario(usuario.getSaspw(), local)) {
                acessosDAO.editarUsuario(usuario.getSaspw(), local);
            } else {
                usuario.getSaspw().setPW(usuario.getPessoa().getPWWeb());
                acessosDAO.criarUsuario(usuario.getSaspw(), local);
            }

            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(usuario.getSaspw().getCodPessoaWeb());
            pessoa.setPWWeb(usuario.getPessoa().getPWWeb());
            pessoa.setOperador(usuario.getSaspw().getOperador());
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoadao.atualizaSenhaSatMob(pessoa, central);
            pessoa.setCodigo(usuario.getSaspw().getCodPessoa());
            pessoadao.atualizaSenhaSatMob(pessoa, local);

            if (pessoalogindao.existePessoaLogin(usuario.getPessoalogin(), central)) {
                pessoalogindao.atualizaPessoaLogin(usuario.getPessoalogin(), central);
            } else {
                pessoalogindao.gravaPessoaLogin(usuario.getPessoalogin(), central);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa inserirNovaPessoa(Pessoa pessoa, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            List<Pessoa> pessoasCentral = pessoaDao.buscarPessoaCPFEmail(pessoa.getEmail(), pessoa.getCPF(), central);
            List<Pessoa> pessoasLocal = pessoaDao.buscarPessoaCPFEmail(pessoa.getEmail(), pessoa.getCPF(), local);

            String cpf = pessoa.getCPF();
            String email = pessoa.getEmail();

            // Se não achar nada nas duas bases, insere normalmente.           
            if (pessoasCentral.isEmpty() && pessoasLocal.isEmpty()) {
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoa(pessoa, central));
                pessoa.setCodigo(pessoaDao.inserirPessoa(pessoa, local));
            } else {

                // Se achar email mas não achar CPF, email em uso
                for (Pessoa p : pessoasCentral) {
                    if (email.equals(p.getEmail()) && !cpf.equals(p.getCPF())) {
                        throw new Exception("EmailEmUso");
                    }
                }
                for (Pessoa p : pessoasLocal) {
                    if (email.equals(p.getEmail()) && !cpf.equals(p.getCPF())) {
                        throw new Exception("EmailEmUso");
                    }
                }

                // Se achar o CPF,
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoa(pessoa, central));
                pessoa.setCodigo(pessoaDao.inserirPessoa(pessoa, local));
            }

            return pessoa;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa inserirCodPessoaWeb(Pessoa pessoa, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            if (local.getEmpresa().equals(central.getEmpresa())) {
                pessoa.setCodPessoaWEB(pessoa.getCodigo());
                pessoaDao.updateCodPessoaWeb(pessoa, central);
            } else {
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoaExpressaCentral(pessoa, central));
                pessoaDao.updateCodPessoaWeb(pessoa, local);
            }
            return pessoa;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public UsuarioSatMobWeb buscarUsuario(BigDecimal codPessoaWeb, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            UsuarioSatMobWeb retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.buscarUsuario(codPessoaWeb, persistencia, satellite);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os grupos da empresa
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<SASGrupos> listarGrupos(Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasgruposdao = new SASGruposDao();
            return sasgruposdao.listaSASGrupos(persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pessoa> listaPessoaQuery(String query, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList<>();
        try {
            PessoaDao pessoaDao = new PessoaDao();
            retorno = pessoaDao.listagemPessoaQuery(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
        return retorno;
    }

    public void atualizarTesCofresMov(String data, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            tesCofresMovDao.atualizaCofres(data, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public MovimentacaoGeralFuncionario gerarRelatorioMovimentacaoIndividual(String data, String codCofre, String codPessoa,
            Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.movimentacaoIndividual(data, codCofre, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }
    
    public MovimentacaoGeralFuncionario gerarRelatorioMovimentacaoIndividual(String data, String codCofre, String codPessoa, String nomeUsuario,
            Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.movimentacaoIndividual(data, codCofre, codPessoa, nomeUsuario, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public MovimentacaoGeralFuncionario gerarRelatorioMovimentacoes(String data, String codCofre, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listarMovimentacaoDiariaCofre(codCofre, data, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public MovimentacaoGeralFuncionario gerarRelatorioMovimentacoes(String dataInicio, String dataFim, String horaInicio, String horaFinal, String movimentacao,
            String codCofre, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listarMovimentacaoDiariaCofre(codCofre, dataInicio, dataFim,
                    horaInicio, horaFinal, movimentacao, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<MovimentacaoGeralFuncionario> gerarRelatorioMovimentacaoGeralFuncionarios(String data, String codCofre,
            Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.movimentacaoGeralFuncionarios(data, codCofre, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pessoa> listarOperadores(String data, String codCofre, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            return pessoaDao.listarOperadoresCofre(data, codCofre, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesEntrada> listarDepositos(String codCli, String codFil, String data, Persistencia persistencia) throws Exception {
        try {
            TesEntradaDao tesEntradaDao = new TesEntradaDao();
            return tesEntradaDao.listaTesEntrada(codCli, codFil, "8", data, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesEntDN> listarComposicoesDeposito(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            TesEntDNDao tesEntDNDao = new TesEntDNDao();
            return tesEntDNDao.ListaComposicoes(guia, serie, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesSaidas> listarColetas(String codCli, String codFil, String data, Persistencia persistencia) throws Exception {
        try {
            TesSaidasDao tesSaidasDao = new TesSaidasDao();
            return tesSaidasDao.listaTesSaidas(codCli, codFil, "8", data, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesSaidasDN> listarComposicoesColeta(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            TesSaidasDNDao tesSaidasDNDao = new TesSaidasDNDao();
            return tesSaidasDNDao.listaComposicoes(guia, serie, persistencia);
        } catch (Exception e) {
            throw new Exception("cofres.falhageral<message>" + e.getMessage());
        }
    }
}

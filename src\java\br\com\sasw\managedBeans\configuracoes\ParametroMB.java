/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.configuracoes;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import Dados.Pool.DadosBancos;
import SasBeans.Filiais;
import SasBeans.Paramet;
import SasDaos.ParametDao;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.faces.application.FacesMessage;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.InputStream;
import java.io.Reader;
import java.util.StringTokenizer;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;

/**
 *
 * <AUTHOR>
 */
@Named(value = "parametro")
@ViewScoped
public class ParametroMB implements Serializable {

    private final String banco;
    private final BigDecimal codPessoa;
    private String codFil, operador, nome, nomeFilial, caminho, log, codGrupo, nivel, dataTela, nomeEmpresa, validaParametro, enderecoNavegador;
    private ArquivoLog logerro;
    private Persistencia persistencia, central;
    private Filiais filiaisCabecalho;
    private final RotasSatWeb rotassatweb;
    Paramet paramet;
    ParametDao parametDao;

    public ParametroMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        codGrupo = (String) fc.getExternalContext().getSessionMap().get("codgrupo");
        nivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
        paramet = new Paramet();
        parametDao = new ParametDao();
        validaParametro = "N";
        log = new String();
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog(this.getClass().getSimpleName());
    }

    public void Persistencias(Persistencia pp, Persistencia central) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + this.banco);
            }

            this.central = central;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.filiaisCabecalho = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void novoParametro() {
        this.validaParametro = "N";

        paramet.setTipoBDdescr("MS SQL SERVER");
        paramet.setEmailAdm("");
        paramet.setAbonoPdr("");
        paramet.setAceTolerChe("");
        paramet.setAceTolerMot("");
        paramet.setAceTolerOutros("");
        paramet.setAceTolerVig("");
        paramet.setAutoFechMobile("");
        paramet.setBancoDados("");
        paramet.setCidade_PDR("");
        paramet.setCodCliCxf("");
        paramet.setCodEmpresa("");
        paramet.setCodFornItau("");
        paramet.setConexaoPadraoBD("");
        paramet.setDescIntrajHE("");
        paramet.setDiaSem1("");
        paramet.setDt_Alter("");
        paramet.setEscTolerChe(null);
        paramet.setEscTolerMot(null);
        paramet.setEscTolerOutros("");
        paramet.setEscTolerVig(null);
        paramet.setFilial_PDR("");
        paramet.setFusoHorario("");
        paramet.setFusoHorarioSEFAZ("");
        paramet.setHostName("");
        paramet.setHostNameWEB("");
        paramet.setHr_Alter("");
        paramet.setLimiteCxf("");
        paramet.setLimiteSeg("");
        paramet.setLimiteTes("");
        paramet.setMoedaPdrMobile("");
        paramet.setNetDir("");
        paramet.setNome_empr("");
        paramet.setOperador("");
        paramet.setPath("");
        paramet.setPathDoctos("");
        paramet.setPathEagle("");
        paramet.setPathFotos("");
        paramet.setPathLogoNFE("");
        paramet.setPathSatelite("");
        paramet.setPtoTolerChe("");
        paramet.setPtoTolerMot("");
        paramet.setPtoTolerOutros("");
        paramet.setPtoTolerVig("");
        paramet.setSenha("");
        paramet.setSeqParamPdr("");
        paramet.setSequencia("");
        paramet.setTipo("");
        paramet.setTrocaSenhaMobile("");
        paramet.setUF_PDR("");
        paramet.setUsuario("");
        paramet.setVersao("");
        paramet.setUtilizaGTVe("");
        paramet.setFirmaGtv("");
        paramet.setCapturaValor("");
        paramet.setTranspCacamba("");
    }

    public StringBuilder abrirScript(String caminho) {
        DadosBancos retorno = null;
        StringBuilder sql = new StringBuilder();
        String linha;
        try {

            BufferedReader leitor;
            // instancia do arquivo que vou ler  
            try {
                FileReader reader = new FileReader(caminho);
                leitor = new BufferedReader(reader);
            } catch (Exception e) {
                InputStream in = getClass().getResourceAsStream(caminho);
                Reader reader = new java.io.InputStreamReader(in);
                leitor = new BufferedReader(reader);
            }

            linha = leitor.readLine();

            while (linha != null) {
                try {
                    sql.append(linha.replace("@NomeBanco", this.paramet.getBancoDados())).append("\n");
                    linha = leitor.readLine();
                } catch (Exception e2) {
                    //linha = leitor.readLine();
                }
            }

            leitor.close();
        } catch (Exception e) {

        }

        return sql;
    }

    public void criarParametro() throws Exception {
        try {
            // CRIAR BANCO DE DADOS
            parametDao.criarBancoDadosScript(abrirScript(this.getClass().getResource("script_bd.txt").getPath().replace("%20", " ")), this.paramet, this.persistencia);

            // CRIAR USUÁRIO EM BANCO DE DADOS
            parametDao.criarUsuarioBancoDados(this.paramet, this.persistencia);

            // INSERIR EM PARAMET - BD CENTRAL
            this.paramet.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.paramet.setDt_Alter(getDataAtual("SQL"));
            this.paramet.setHr_Alter(getDataAtual("HORA"));
            parametDao.criarParametroCentral(this.paramet, this.central);

            // INSERIR EM PARAMET - BD LOCAL
            parametDao.criarParametroLocal(this.paramet, this.persistencia);

            // CRIAR FUNCTIONs
            LoginMB novoBD = new LoginMB();
            Persistencia pp = new Persistencia("jdbc:sqlserver://*************:2006;databaseName=" + this.paramet.getBancoDados(), "sasw", "s@$26bd1", this.enderecoNavegador);
            pp.setEmpresa(this.paramet.getNome_empr());
            parametDao.criarFunctionsNovoParametro(this.paramet, pp);
            
            this.nomeEmpresa = "";
            novoParametro();
            PrimeFaces.current().executeScript("$.MsgBoxAzulOk('" + Messages.getMessageS("Aviso") + "', '" + Messages.getMessageS("CadastroSucesso") + "');");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);

            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void validarParametro() throws Exception {
        if (null != this.nomeEmpresa
                && !this.nomeEmpresa.equals("")) {
            this.validaParametro = "S";

            if (parametDao.validarParametro("Sat" + this.nomeEmpresa.split(" ")[0], this.central).equals("0")) {
                paramet.setTipoBDdescr("MS SQL SERVER");
                paramet.setEmailAdm("");
                paramet.setAbonoPdr("");
                paramet.setAceTolerChe("0");
                paramet.setAceTolerMot("0");
                paramet.setAceTolerOutros("0");
                paramet.setAceTolerVig("0");
                paramet.setAutoFechMobile("");
                paramet.setBancoDados("Sat" + this.nomeEmpresa.split(" ")[0]);
                paramet.setCidade_PDR("");
                paramet.setCodCliCxf("");
                paramet.setCodEmpresa("");
                paramet.setCodFornItau("");
                paramet.setConexaoPadraoBD("");
                paramet.setDescIntrajHE("");
                paramet.setDiaSem1("");
                paramet.setDt_Alter("");
                paramet.setEscTolerChe(0);
                paramet.setEscTolerMot(0);
                paramet.setEscTolerOutros("0");
                paramet.setEscTolerVig(0);
                paramet.setFilial_PDR("");
                paramet.setFusoHorario("-1");
                paramet.setFusoHorarioSEFAZ("-1");
                paramet.setHostName("*************,2006");
                paramet.setHostNameWEB("bd.sasw.com.br,2006");
                paramet.setHr_Alter("");
                paramet.setLimiteCxf("0");
                paramet.setLimiteSeg("0");
                paramet.setLimiteTes("0");
                paramet.setMoedaPdrMobile("BRL");
                paramet.setNetDir("");
                paramet.setNome_empr(this.nomeEmpresa);
                paramet.setOperador("");
                paramet.setPath("Sat" + this.nomeEmpresa.split(" ")[0]);
                paramet.setPathDoctos("A:\\Satelite\\Doctos");
                paramet.setPathEagle("");
                paramet.setPathFotos("A:\\Satelite\\Fotos");
                paramet.setPathLogoNFE("");
                paramet.setPathSatelite("");
                paramet.setPtoTolerChe("0");
                paramet.setPtoTolerMot("0");
                paramet.setPtoTolerOutros("0");
                paramet.setPtoTolerVig("0");

                String Senha = this.nomeEmpresa.split(" ")[0].toLowerCase().replace("a", "@") + "*" + this.dataTela;
                Senha = RecortaAteEspaço(Senha, 0, 20);

                paramet.setSenha(Senha);
                paramet.setSeqParamPdr("");
                paramet.setSequencia("");
                paramet.setTipo("2");
                paramet.setTrocaSenhaMobile("");
                paramet.setUF_PDR("");
                paramet.setUsuario(this.nomeEmpresa.split(" ")[0].toLowerCase());
                paramet.setVersao("");
            } else {
                novoParametro();
                PrimeFaces.current().executeScript("$.MsgBoxVermelhoOk('" + Messages.getMessageS("Aviso") + "', '" + Messages.getMessageS("ParamCadastrado") + "');");
            }
        } else {
            novoParametro();
        }
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public String getCaminho() {
        return caminho;
    }

    public void setCaminho(String caminho) {
        this.caminho = caminho;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public String getCodGrupo() {
        return codGrupo;
    }

    public void setCodGrupo(String codGrupo) {
        this.codGrupo = codGrupo;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public ArquivoLog getLogerro() {
        return logerro;
    }

    public void setLogerro(ArquivoLog logerro) {
        this.logerro = logerro;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public Persistencia getCentral() {
        return central;
    }

    public void setCentral(Persistencia central) {
        this.central = central;
    }

    public Filiais getFiliaisCabecalho() {
        return filiaisCabecalho;
    }

    public void setFiliaisCabecalho(Filiais filiaisCabecalho) {
        this.filiaisCabecalho = filiaisCabecalho;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Paramet getParamet() {
        return paramet;
    }

    public void setParamet(Paramet paramet) {
        this.paramet = paramet;
    }

    public String getValidaParametro() {
        return validaParametro;
    }

    public void setValidaParametro(String validaParametro) {
        this.validaParametro = validaParametro;
    }

    public String getEnderecoNavegador() {
        return enderecoNavegador;
    }

    public void setEnderecoNavegador(String enderecoNavegador) {
        this.enderecoNavegador = enderecoNavegador;
    }
    
    
}

package Controller.Supervisao;

import Dados.Persistencia;
import SasBeans.CtrOperV;
import SasBeans.PstServ;
import SasBeansCompostas.PstHstQstTbValFuncion;
import SasBeansCompostas.TmktDetPstPstServClientes;
import SasDaos.CtrOpervDao;
import SasLibrary.Supervisao;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Supervisoes {

    /**
     * Busca uma superviao pela sequencia
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public TmktDetPstPstServClientes obterSupervisao(String sequencia, Persistencia persistencia) throws Exception {
        try {
            Supervisao supervisao = new Supervisao();
            return supervisao.obterSupervisao(sequencia, persistencia);
        } catch (Exception e) {
            throw new Exception("Supervisoes.falha<message>" + e.getMessage());
        }
    }

    /**
     * Listagem de supervisoes Buscará por posto
     *
     * @param posto - posto a ser pesquisado
     * @param dataSupervisao1
     * @param dataSupervisao2
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> ListaSupervisaoPosto(PstServ posto, String dataSupervisao1, String dataSupervisao2,
            Persistencia persistencia)
            throws Exception {
        try {
            List<TmktDetPstPstServClientes> lsup;
            Supervisao supervisao = new Supervisao();
            lsup = supervisao.BuscaSupervisaoPeriodoPosto(posto, dataSupervisao1, dataSupervisao2, persistencia);
            return lsup;
        } catch (Exception e) {
            throw new Exception("listasupervisaodia.falha<message>" + e.getMessage());
        }
    }

    /**
     * Busca de uma supervisão
     *
     * @param supervisao - parametros que devem ser preenchidos: sequencia -
     * numero da supervisão, codfil - codigo da filial, e secao - codigo do
     * posto
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public TmktDetPstPstServClientes BuscarSupervisao(TmktDetPstPstServClientes supervisao,
            Persistencia persistencia)
            throws Exception {
        try {
            TmktDetPstPstServClientes lsup;
            Supervisao supervisaodao = new Supervisao();
            lsup = supervisaodao.BuscaSupervisao(supervisao, persistencia);
            return lsup;
        } catch (Exception e) {
            throw new Exception("listasupervisaodia.falha<message>" + e.getMessage());
        }
    }

    /**
     * Lista a sequencia de supervisões para algum posto a partir de outr
     * supervisão
     *
     * @param supervisao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> ListarSequenciasSupervisao(TmktDetPstPstServClientes supervisao,
            Persistencia persistencia) throws Exception {
        try {
            List<String> seqs;
            Supervisao supervisaodao = new Supervisao();
            seqs = supervisaodao.BuscarSequenciasSupervisao(supervisao, persistencia);
            return seqs;
        } catch (Exception e) {
            throw new Exception("listasupervisaodia.falha<message>" + e.getMessage());
        }
    }

    /**
     * Buscará pela superisão anterior a atual
     *
     * @param codfil - codigo da filial
     * @param persistencia - conexão ao banco de dados
     * @param sequencia - numero da supervisão
     * @param secao - numero do posto
     * @return
     * @throws Exception
     */
    public TmktDetPstPstServClientes SupervisaoPosteriorPosto(BigDecimal codfil,
            String secao, BigDecimal sequencia,
            Persistencia persistencia)
            throws Exception {
        try {
            TmktDetPstPstServClientes lsup;
            Supervisao supervisao = new Supervisao();
            lsup = supervisao.BuscaSupervisaoSeguintePosto(codfil, secao, sequencia, persistencia);
            return lsup;
        } catch (Exception e) {
            throw new Exception("listasupervisaodia.falha<message>" + e.getMessage());
        }
    }

    /**
     * Listagem de supervisoes Buscará por períodos
     *
     * @param codfil - codigo da filial
     * @param dtini - data inicial
     * @param dtfim - data final
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> ListaSupervisaoDia(BigDecimal codfil,
            LocalDate dtini, LocalDate dtfim,
            Persistencia persistencia)
            throws Exception {
        try {
            List<TmktDetPstPstServClientes> lsup;
            Supervisao supervisao = new Supervisao();
            lsup = supervisao.BuscaSupervisaoPeriodo(dtini, dtfim, codfil, persistencia);
            return lsup;
        } catch (Exception e) {
            throw new Exception("listasupervisaodia.falha<message>" + e.getMessage());
        }
    }

    /**
     * Listagem de supervisoes Buscará por períodos
     *
     * @param codfil - codigo da filial
     * @param dtini - data inicial
     * @param dtfim - data final
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> PesquisaSupervisao(LocalDate dtini, LocalDate dtfim,
            TmktDetPstPstServClientes sup, Persistencia persistencia)
            throws Exception {
        try {
            List<TmktDetPstPstServClientes> lsup;
            Supervisao supervisao = new Supervisao();
            lsup = supervisao.PesquisarSupervisoes(dtini, dtfim, sup, persistencia);
            return lsup;
        } catch (Exception e) {
            throw new Exception("listasupervisaodia.falha<message>" + e.getMessage());
        }
    }

    /**
     * Busca questionário pela supervisão
     *
     * @param sequencia - Sequencia da supervisão
     * @param Data - Data da supervisão
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PstHstQstTbValFuncion> ListaQuestionarioSup(BigDecimal sequencia, String Data,
            Persistencia persistencia)
            throws Exception {
        try {
            List<PstHstQstTbValFuncion> retorno = new ArrayList();
            Supervisao supervisao = new Supervisao();
            retorno = supervisao.ListaQuestionarioSupervisao(sequencia, Data, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("listasupervisaoquestoes.falha<message>" + e.getMessage());
        }
    }

    /**
     * Inserir funcionáro
     *
     * @param ctrOperV Objeto contendo registors
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void inserirFuncionarioDAO(CtrOperV ctrOperV, Persistencia persistencia) throws Exception {
        try {
            CtrOpervDao ctrOperVDao = new CtrOpervDao();
            BigDecimal numero = ctrOperVDao.obterNumeroIncremento(ctrOperV.getCodFil(), persistencia);
            ctrOperV.setNumero(numero.toPlainString());
            ctrOperV = (CtrOperV) FuncoesString.removeAcentoObjeto(ctrOperV);
            ctrOperVDao.adicionaCtrOperV(numero, ctrOperV, persistencia);
        } catch (Exception e) {
            throw new Exception("listasupervisaoquestoes.falha<message>" + e.getMessage());
        }
    }

    /**
     * Realiza a validação da matricula do funcionario
     *
     * @param codfil Codigo da filial
     * @param matricula Matricula do funcionario
     * @param persistencia Conexão com a base de dados
     * @return se a matricula é valida ou não
     * @throws Exception
     */
    public boolean validaMatricula(BigDecimal codfil, BigDecimal matricula, Persistencia persistencia) throws Exception {
        boolean validar = false;
        try {
            CtrOpervDao ctrOperVDao = new CtrOpervDao();
            validar = ctrOperVDao.validaMatricula(matricula, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("Ocorre um erro ao validar " + e.getMessage());
        }
        return validar;
    }

    /**
     *
     * @param supervisao
     * @param persistencia
     * @throws Exception
     */
    public void AtualizarSituacao(TmktDetPstPstServClientes supervisao, Persistencia persistencia) throws Exception {
        try {
            Supervisao supervisaoDao = new Supervisao();
            supervisao = (TmktDetPstPstServClientes) FuncoesString.removeAcentoObjeto(supervisao);
            supervisaoDao.AtualizarSupervisao(supervisao, persistencia);
        } catch (Exception e) {
            throw new Exception("Erro ao atualizar supervisão: " + e.getMessage());
        }
    }

    public List<TmktDetPstPstServClientes> ListaSupervisaoCliente(BigDecimal codfil, String codcli, String data, Persistencia persistencia) throws Exception {
        List<TmktDetPstPstServClientes> retorno = new ArrayList<>();
        Supervisao supervisaoDao = new Supervisao();
        try {
            retorno = supervisaoDao.BuscaSupervisaoCliente(codfil, codcli, data, persistencia);
        } catch (Exception e) {
            throw new Exception("Erro ao listar supervisão: " + e.getMessage());
        }
        return retorno;
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de supervisoes
     *
     * @param filtros - filtros para pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            Supervisao supervisaodao = new Supervisao();
            retorno = supervisaodao.TotalSupervisoesMobWeb(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("supervisao.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de supervisoes
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> ListagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<TmktDetPstPstServClientes> retorno;
            Supervisao supervisaodao = new Supervisao();
            retorno = supervisaodao.ListaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("supervisao.falhageral<message>" + e.getMessage());
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/os_vig.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />

            <style>
                [id*="tabItensFaturar"], 
                [id*="tabFrequenciaServicos"],
                [id*="tabGeral"]{
                    padding:4px !important;
                    height:200px !important;
                }

                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#EEE !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="formPesquisar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                .AlturaTab{
                    padding:0px !important;
                }

                .ui-inputtext{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                div.ui-paginator-bottom {
                    display: none !important;
                }

                /*[id*="tabela"] {
                    background: white;
                    margin:0px !important;
                    width: auto !important;
                }

                [id*="tabela"] > .ui-datatable-scrollable-body {
                    max-height: calc(100% - 90px);
                }*/

                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    .ui-paginator-top {
                        white-space: normal !important;
                    }
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    #formCadastrar\:tabs\:tabelaItensFaturar .ui-datatable-scrollable-body,
                    #formCadastrar\:tabs\:tabFrequenciaServicos .ui-datatable-scrollable-body,
                    #formCadastrarItensContrato\:itensContrato .ui-datatable-scrollable-body {
                        max-height: calc(100% - 54px) !important;
                        max-width: 100% !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }
                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }

                #body {
                    height: calc(100% - 40px);
                    max-height: 100% !important;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                }

                #main {
                    flex-grow: 1;
                }

                #formPesquisar .ui-radiobutton {
                    background: transparent !important;
                }

                .ui-datatable-scrollable-body,
                .FundoPagina > .ui-panel-content {
                    height: 100% !important;
                }

                .FundoPagina {
                    border: thin solid #CCC !important;
                    border-top:4px solid #3C8DBC !important;
                }

                .botoesDataTable {
                    width: 40px;
                    margin-top: 8px;
                    position: absolute;
                    right: -14px; top: 50%;
                    transform: translateY(-50%);
                }

                .row-margin {
                    margin-top: 10px;
                    margin-bottom: 10px;
                }

                [id*="gridDefault"] .DataGrid tbody tr td{
                    text-align: center;
                    vertical-align: middle !important;
                }

                @media only screen and (max-width: 700px) and (min-width: 0px) {
                    text-align: auto !important;
                }

                [id*="formCadastrar"] [class*="col-md"]{
                    padding-right:0px !important;
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px){
                    /* [id*="tabela"] tbody tr td,
                     [id*="tabela"] thead tr th{
                         min-width: 150px !important;
                         width: 150px !important;
                         max-width: 150px !important;
                     }
 
                     [id*="tabela"] tbody tr td:nth-child(1),
                     [id*="tabela"] thead tr th:nth-child(1),
                     [id*="tabela"] tbody tr td:nth-child(2),
                     [id*="tabela"] thead tr th:nth-child(2),
                     [id*="tabela"] tbody tr td:nth-child(3),
                     [id*="tabela"] thead tr th:nth-child(3),
                     [id*="tabela"] tbody tr td:nth-child(5),
                     [id*="tabela"] thead tr th:nth-child(5){
                         min-width: 100px !important;
                         width: 100px !important;
                         max-width: 100px !important;
                     }
 
                     [id*="tabela"] tbody tr td:nth-child(4),
                     [id*="tabela"] thead tr th:nth-child(4),
                     [id*="tabela"] tbody tr td:nth-child(6),
                     [id*="tabela"] thead tr th:nth-child(6),
                     [id*="tabela"] tbody tr td:nth-child(9),
                     [id*="tabela"] thead tr th:nth-child(9),
                     [id*="tabela"] tbody tr td:nth-child(10),
                     [id*="tabela"] thead tr th:nth-child(10){
                         min-width: 300px !important;
                         width: 300px !important;
                         max-width: 300px !important;   
                     }
 
                     [id*="tabela"] tbody tr td:nth-child(7),
                     [id*="tabela"] thead tr th:nth-child(7),
                     [id*="tabela"] tbody tr td:nth-child(8),
                     [id*="tabela"] thead tr th:nth-child(8){
                         min-width: 150px !important;
                         width: 150px !important;
                         max-width: 150px !important;
                     }*/
                }

                ui-panel.ui-widget .ui-panel-titlebar.ui-corner-all{
                    background-color: #FFF !important;
                    border-bottom: thin solid #CCC !important
                }

                .ItemCliente, .ItemClienteSemLat{
                    position: relative;
                    height: 230px;
                }

                .ItemCliente .ui-panel-title{
                    margin-top: 0px !important;
                    font-size: 16pt !important;
                    font-weight: bold !important;
                    color: #3c8dbc !important;
                    margin-left: #{localeController.number.toString() eq '1'? '108': localeController.number.toString() eq '2'? '75': '84'}px !important;
                }

                .ItemClienteSemLat .ui-panel-title{
                    margin-top: 0px !important;
                    font-size: 16pt !important;
                    font-weight: bold !important;
                    color: goldenrod !important;
                    margin-left: #{localeController.number.toString() eq '1'? '108': localeController.number.toString() eq '2'? '75': '84'}px !important;
                }

                .lblNred{
                    position: absolute;
                    margin-top: -40px;
                    margin-left: 10px;
                    padding: 1px 10px 2px 10px;
                    background-color: orangered;
                    color: #FFF;
                    font-weight: 600 !important;
                    border-radius: 30px;
                    font-size: 9pt !important;
                }

                .ui-panel.ui-widget .ui-panel-titlebar.ui-corner-all{
                    background-color: #FFF !important;
                    border-bottom: thin solid #CCC !important
                }

                body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                .lblOS{
                    position: absolute;
                    
                    background: linear-gradient(to bottom, orangered, orangered);
                    color: #FFF !Important; 
                    padding: 14px 8px 14px 8px; 
                    font-size: 14pt;
                    border-radius: 0px 0px 30px 0px;
                    width: 112px;
                    text-align: center;
                    top: 1px;
                }

                @media only screen and (max-width: 640px) and (min-width: 10px) {
                    .ItemCliente, .ItemClienteSemLat{
                        height: 490px;
                    }
                }

            </style>
        </h:head>

        <h:body id="h" style="max-height: 100% !important; height: 100% !important;">
            <f:metadata>
                <f:viewAction action="#{os_vig.Persistencias(login.pp)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <h:form>
                <p:hotkey bind="p" actionListener="#{os_vig.prePesquisa}" update="formPesquisar msgs" />
                <p:hotkey bind="s" actionListener="#{os_vig.prePesquisa}" update="formPesquisar msgs" />
                <p:hotkey bind="b" actionListener="#{os_vig.prePesquisa}" update="formPesquisar msgs" />
            </h:form>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_satmob_fopag_G.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.OS}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span>
                                            <h:outputText id="dataDia" value="#{os_vig.dataTela}" converter="conversorDia" />
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{os_vig.filiais.descricao}
                                            <label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco">
                                            #{os_vig.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{os_vig.filiais.bairro}, #{os_vig.filiais.cidade}/#{os_vig.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3"
                                     style="padding:0px 10px 0px 0px !important; text-align: right !important">
                                    <p:commandLink title="#{localemsgs.Voltar}" actionListener="#{os_vig.selecionarOS}" update="main:tabela"
                                                   oncomplete="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <!--Tabela OS-->
                <h:form id="main">
                    <p:panel id="gridDefault" class="ui-grid ui-grid-responsive FundoPagina"
                             style="overflow:hidden !important; overflow-y:auto !important; padding-right:12px !important; border-top-color:#d2d6de !important">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline;">

                                    <p:dataGrid  id="tabela" value="#{os_vig.allOS_Vig}" paginator="true" rows="50" lazy="true"
                                                 rowsPerPageTemplate="5,10,15,20,25,50"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Clientes}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport}
                                                 {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista" columns="1"
                                                 emptyMessage="#{localemsgs.SemRegistros}">
                                        <div class="Cliente">

                                            <p:panel header="#{lista.NRed}" class="ItemCliente">
                                                <!--<label class="lblNred">
                                                    #{localemsgs.NRed}
                                                </label>-->
                                                <label class="lblOS"><b>#{localemsgs.OS}:</b> #{lista.OS}</label>

                                                <div class="col-md-10" style="width: calc(100% - 0px); padding-left: 0px !important;">

                                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                                 layout="grid" styleClass="ui-panelgrid-blank" style="margin-top: 8px !important">                                                    
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.CodFil}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.codFil}">
                                                                    <f:convertNumber pattern="0000"/>
                                                                </h:outputText>
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Validade}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.dtInicio}" converter="conversorData" />  #{localemsgs.a}  <h:outputText value="#{lista.dtFim}" converter="conversorData" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.descricao}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.descricao}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Destino}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.NRedDst} - #{lista.cliDst}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.CliFat}: "/>
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.NRedFat} - #{lista.cliFat}" />
                                                            </div>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Contrato}: "/>
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.descrContrato}" />
                                                            </div>
                                                        </p:column>

                                                        
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Situacao}: "/>
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.situacao eq 'A'? localemsgs.Ativo: localemsgs.Inativo}" />
                                                            </div>
                                                        </p:column>
                                                        
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Agencia}: "/>
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.agencia}" />
                                                            </div>
                                                        </p:column>
                                                        
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.DiaFechaFat}: "/>
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.diaFechaFat}" />
                                                            </div>
                                                        </p:column>
                                                        
                                                        <p:column>
                                                            
                                                            <div class="BotoesGrid" style="width:100px; text-align: right; padding:0px !important;">
                                                                <p:commandLink title="#{localemsgs.Editar}" update="msgs formCadastrar"
                                                                               actionListener="#{os_vig.preEdicaoNovo(lista)}">
                                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                                                </p:commandLink>
                                                            </div>
                                                        </p:column>
                                                        <p:column></p:column>
                                                        <p:column></p:column>


                                                        <p:column style="font-size: 8pt !important">
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Operador}: "  />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.operador}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Dt_Alter}: "/>
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.dt_Alter}" converter="conversorData" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Hr_Alter}: "/>
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.hr_Alter}" converter="conversorHora" />
                                                            </div>
                                                        </p:column>


                                                    </p:panelGrid>


                                                </div>
                                            </p:panel>

                                        </div>
                                    </p:dataGrid>
                                </p:panel>
                            </div>
                        </div>





                    </p:panel>



                    <p:panel rendered="false" id="gridDefaultOLD" class="ui-grid ui-grid-responsive FundoPagina"
                             style="overflow:hidden !important; padding-right:12px !important; max-height: calc(100% - 10px) !important;">

                        <!--Antigo gride de OS-->
                        <p:dataTable id="tabelaOLF" value="#{os_vig.allOS_Vig}" 
                                     selection="#{os_vig.os_vigSelecionado}"
                                     rowKey="#{lista.codFil};#{lista.OS}" 
                                     emptyMessage="#{localemsgs.SemRegistros}" 
                                     var="lista" 
                                     lazy="true" 
                                     paginator="true"
                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.OS}"
                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                     reflow="true" 
                                     rows="25" 
                                     rowsPerPageTemplate="5,10,15, 20, 25" 
                                     scrollWidth="100%" 
                                     selectionMode="single"
                                     styleClass="tabela" 
                                     class="DataGrid"
                                     scrollable="true"
                                     rowStyleClass="#{lista.situacao ne 'A' ? 'inativo' : lista.operador.substring(0,3) eq 'CLN'?'negrito' :''}">
                            <p:ajax event="rowDblselect" listener="#{os_vig.preEdicao}" update="formCadastrar msgs"/>
                            <p:ajax event="rowSelect" update="tabela"/>
                            <p:column headerText="#{localemsgs.OS}" class="texto-centro">
                                <h:outputText value="#{lista.OS}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.CodFil}" class="texto-centro">
                                <h:outputText value="#{lista.codFil}" converter="conversorCodFil"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Situacao}" class="texto-centro">
                                <h:outputText value="#{lista.situacao}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.NRed}">
                                <h:outputText value="#{lista.NRed}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Agencia}" class="texto-centro">
                                <h:outputText value="#{lista.agencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.CliDst}">
                                <h:outputText value="#{lista.NRedDst}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtInicio}">
                                <h:outputText value="#{lista.dtInicio}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtValidade}">
                                <h:outputText value="#{lista.dtFim}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Descricao}">
                                <h:outputText value="#{lista.descricao}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ClienteFaturar}">
                                <h:outputText value="#{lista.NRedFat}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Cliente}">
                                <h:outputText value="#{lista.cliente}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.CliFat}">
                                <h:outputText value="#{lista.cliFat}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DiaFechaFat}">
                                <h:outputText value="#{lista.diaFechaFat}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Contrato}">
                                <h:outputText value="#{lista.contrato}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Agrupador}">
                                <h:outputText value="#{lista.agrupador}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.MsgExtrato}">
                                <h:outputText value="#{lista.msgExtrato}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.TipoOS}">
                                <h:outputText value="#{lista.tipoOS}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.CodSrv}">
                                <h:outputText value="#{lista.codSrv}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.CliDst}">
                                <h:outputText value="#{lista.cliDst}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.KM}">
                                <h:outputText value="#{lista.KM}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.KMTerra}">
                                <h:outputText value="#{lista.KMTerra}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Aditivo}">
                                <h:outputText value="#{lista.aditivo}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.CCusto}">
                                <h:outputText value="#{lista.CCusto}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.OSGrp}">
                                <h:outputText value="#{lista.OSGrp}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.GTVQtde}">
                                <h:outputText value="#{lista.GTVQtde}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.GTVEstMin}">
                                <h:outputText value="#{lista.GTVEstMin}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SitFiscal}">
                                <h:outputText value="#{lista.sitFiscal}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.OperIncl}">
                                <h:outputText value="#{lista.operIncl}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Incl}">
                                <h:outputText value="#{lista.dt_Incl}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Incl}">
                                <h:outputText value="#{lista.hr_Incl}" converter="conversorHora"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Operador}">
                                <h:outputText value="#{lista.operador}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Alter}">
                                <h:outputText value="#{lista.dt_Alter}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Alter}">
                                <h:outputText value="#{lista.hr_Alter}" converter="conversorHora"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 90px !important; background:transparent; height:200px !important;" id="botoes">
                        <p:remoteCommand name="rcClonarOS" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs main" 
                                         actionListener="#{os_vig.clonarOS()}" />                            

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{os_vig.preCadastro}"
                                           update="msgs formCadastrar">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px; display: none">
                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{os_vig.preEdicao}"
                                           update="msgs formCadastrar" >
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{os_vig.prePesquisa}"
                                           update="formPesquisar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Clonar}" actionListener="#{os_vig.preClone}"
                                           update="msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_clonar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{os_vig.limparFiltros}"
                                           update=":main:tabela :msgs :cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <!-- Cadastro Itens de Faturamento -->
                <h:form id="formCadastrarItensFaturamento" class="form-inline">
                    <p:dialog widgetVar="dlgCadastrarItensFaturamento" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dlgCadastrar" focus="slcTipo" style="min-width:800px !important;width:90% !important; max-width:800px !important">
                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.ItensFaturamento}" style="color:#022a48" />
                        </f:facet>

                        <p:panel id="cadastrarItens" style="background-color: transparent; padding:0px !important" styleClass="cadastrar">
                            <p:remoteCommand name="rc" 
                                             process="@this,vigenciaDe,vigenciaAte,slcTipo,txtQtde,txtValor,txtObs,txtExtrato" 
                                             update="vigenciaDe,vigenciaAte,slcTipo,txtQtde,txtValor,txtObs,txtExtrato" 
                                             actionListener="#{os_vig.adicionarItemFatura()}" />

                            <div class="col-md-12" style="padding:0px !important">
                                <div class="col-md-3" style="padding-left:0px !important">
                                    <label class="Rotulo">#{localemsgs.VigenciaInicio}</label>
                                    <p:datePicker id="vigenciaDe" value="#{os_vig.os_vitem.dtInicio}" readonlyInput="true"
                                                  required="true"
                                                  requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.VigenciaInicio}"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                                <div class="col-md-3" style="padding-left:6px !important">
                                    <label class="Rotulo">#{localemsgs.VigenciaFim}</label>
                                    <p:datePicker id="vigenciaAte" value="#{os_vig.os_vitem.dtFim}" readonlyInput="true"
                                                  required="true"
                                                  requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.VigenciaFim}"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                                <div class="col-md-12" style="padding-left:0px !important">
                                    <label class="Rotulo">#{localemsgs.Tipo}</label>
                                    <p:selectOneMenu id="slcTipo" value="#{os_vig.ctrItensListSelecionado}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                     styleClass="form-control" style="width: 100%; padding-top:0px !important;padding-left:3px !important"
                                                     filter="true" filterMatchMode="contains">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="#{os_vig.ctrItensListVazio}" />
                                        <f:selectItems value="#{os_vig.ctrItensList}" var="itens" itemValue="#{itens}"
                                                       itemLabel="#{itens.descricao}"/>
                                        <p:ajax update="cadastrarItens" />
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-#{os_vig.ctrItensListSelecionado.tipoCalc eq 'Q' or os_vig.ctrItensListSelecionado.tipoCalc eq 'M'?'9': '12'}" style="padding-left:0px !important">
                                    <label class="Rotulo">#{localemsgs.TipoCalculo}</label>
                                    <h:inputText class="form-control" value="#{os_vig.ctrItensListSelecionado.tipoCalc eq 'Q'?localemsgs.ItemFatPorQtde:
                                                                               os_vig.ctrItensListSelecionado.tipoCalc eq 'M'?localemsgs.ItemFatPorMes:
                                                                               os_vig.ctrItensListSelecionado.tipoCalc eq 'D'?localemsgs.ItemFatPorDia:
                                                                               os_vig.ctrItensListSelecionado.tipoCalc eq 'H'?localemsgs.ItemFatPorHora:
                                                                               os_vig.ctrItensListSelecionado.tipoCalc eq 'I'?localemsgs.ItemFatPorIndice:''}" 
                                                 onkeydown="return false" onkeypress="return false"
                                                 style="background-color:#EEE"
                                                 required="true"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoCalculo}"
                                                 ></h:inputText>
                                </div>
                                <div class="col-md-3" style="padding-left:6px !important;display:#{os_vig.ctrItensListSelecionado.tipoCalc eq 'Q'?'': 'none'}">
                                    <label class="Rotulo">#{localemsgs.Quantidade}</label>
                                    <p:inputNumber id="txtQtde" decimalPlaces="0" maxlength="3" value="#{os_vig.os_vitem.qtde}" required="#{os_vig.ctrItensListSelecionado.tipoCalc eq 'Q'?'true':'false'}"
                                                   requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Quantidade}"></p:inputNumber>
                                </div>
                                <div class="col-md-3" style="padding-left:0px !important;display:#{os_vig.ctrItensListSelecionado.tipoCalc eq 'M'?'': 'none'}">
                                    <label class="Rotulo">#{localemsgs.Valor}</label>
                                    <p:inputNumber id="txtValor" value="#{os_vig.os_vitem.valor}" required="#{os_vig.ctrItensListSelecionado.tipoCalc eq 'M'?'true':'false'}"
                                                   requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Valor}"></p:inputNumber>
                                </div>
                                <div class="col-md-12" style="padding-left:0px !important">
                                    <label class="Rotulo">#{localemsgs.Obs}</label>
                                    <h:inputText id="txtObs" class="form-control" value="#{os_vig.os_vitem.obs}"></h:inputText>
                                </div>
                                <div class="col-md-12" style="padding-left:0px !important">
                                    <label class="Rotulo">#{localemsgs.MensagemExtrato}</label>
                                    <h:inputText id="txtExtrato" class="form-control" value="#{os_vig.os_vitem.msgExtrato}"></h:inputText>
                                </div>
                                <div class="col-md-12" style="text-align:right; padding-right:0px !important;padding-left:0px !important">
                                    <p:commandLink id="btSalvarItemFatura" onclick="rc();" process="@this"
                                                   update="formCadastrar:cadastrar msgs"
                                                   class="btn btn-primary" style="min-width:110px !important; margin-top:12px !important; padding-bottom:2px !important;" >
                                        <h:outputLabel><i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}</h:outputLabel>
                                    </p:commandLink>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Tipos do Contrato -->
                <h:form id="formCadastrarItensContrato" class="form-inline">
                    <p:dialog widgetVar="dlgCadastrarItensContrato" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dlgCadastrar" >
                        <p:commandButton widgetVar="botaoFecharItens" style="display: none"
                                         oncomplete="PF('dlgCadastrarItensContrato').hide()" id="botaoFecharItens">
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.ItensContrato}" style="color:#022a48" />
                        </f:facet>

                        <p:panel id="itensContrato" style="background-color: transparent;" styleClass="cadastrar">
                            <p:dataTable id="tabelaItensContrato" value="#{os_vig.ctrItensList}"
                                         var="os_vcontrato" selection="#{os_vig.ctrItem}" selectionMode="single"
                                         rowKey="#{os_vcontrato.contrato};#{os_vcontrato.contrato};#{os_vcontrato.tipoPosto}"
                                         styleClass="tabela" class="DataGrid" resizableColumns="true"
                                         scrollable="true" scrollWidth="100%" reflow="true" emptyMessage="#{localemsgs.SemRegistros}">
                                <p:ajax event="rowDblselect" listener="#{os_vig.preEdicaoItensContrato}" update="msgs"/>
                                <p:column headerText="#{localemsgs.TipoPosto}">
                                    <h:outputText value="#{os_vcontrato.tipoPosto}" title="#{os_vcontrato.tipoPosto}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Descricao}">
                                    <h:outputText value="#{os_vcontrato.descricao}" title="#{os_vcontrato.descricao}"/>
                                </p:column>

                                <p:column headerText="#{localemsgs.ValorRot}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores1?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.valorRot}" title="#{os_vcontrato.valorRot}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorEve}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores1?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.valorEve}" title="#{os_vcontrato.valorEve}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorEsp}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores1?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.valorEsp}" title="#{os_vcontrato.valorEsp}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorAst}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores1?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.valorAst}" title="#{os_vcontrato.valorAst}" converter="conversormoeda"/>
                                </p:column>

                                <p:column headerText="#{localemsgs.CHDiaria}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.horas}" title="#{os_vcontrato.horas}"  converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.CHSemana}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.CHSeman}" title="#{os_vcontrato.CHSeman}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorServico}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.valorPosto}" title="#{os_vcontrato.valorPosto}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DescrCalc}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="" title=""/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorRef}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.reforco}" title="#{os_vcontrato.reforco}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HEDiurna}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.HEDiurna}" title="#{os_vcontrato.HEDiurna}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HRNoturna}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.HENoturna}" title="#{os_vcontrato.HENoturna}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HEDiurna2}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.HEDiurna2}" title="#{os_vcontrato.HEDiurna2}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HENoturna2}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.HENoturna2}" title="#{os_vcontrato.HENoturna2}" converter="conversormoeda"/>
                                </p:column>

                                <p:column headerText="#{localemsgs.CHMensal}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="#{os_vcontrato.CHMensal}" title="#{os_vcontrato.CHMensal}" converter="conversormoeda"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorDia}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="" title=""/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorHora}" rendered="#{os_vig.mostrarValores ?os_vig.mostrarValores2?true:false:false}">
                                    <h:outputText value="" title=""/>
                                </p:column>


                                <p:column headerText="#{localemsgs.Operador}">
                                    <h:outputText value="#{os_vcontrato.operador}" title="#{os_vcontrato.operador}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Dt_Alter}">
                                    <h:outputText value="#{os_vcontrato.dt_Alter}" title="#{os_vcontrato.dt_Alter}" converter="conversorData"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hr_Alter}">
                                    <h:outputText value="#{os_vcontrato.hr_Alter}" title="#{os_vcontrato.hr_Alter}" converter="conversorHora"/>
                                </p:column>
                            </p:dataTable>

                            <div style="
                                 width: 40px;
                                 margin-top: 8px;
                                 position: absolute;
                                 right: -14px;
                                 top: 20%;
                                 transform: translateY(-50%);
                                 ">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                    <p:commandLink actionListener="#{os_vig.preEdicaoItensContrato}"
                                                   update="msgs" title="#{localemsgs.Editar}">
                                        <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="col-md-12" style="padding:0px !important; text-align:right; padding-top:8px !important; padding-right: 40px">
                                <p:outputLabel value="#{localemsgs.mostrarValores}" for="chkMostrarPreco"
                                               style="font-weight:bold; margin-left:8px !important; margin-right:8px !important;"/>
                                <p:selectBooleanCheckbox itemLabel="" id="chkMostrarPreco" value="#{os_vig.mostrarValores}" style="padding-right: 15px;">
                                    <p:ajax event="change" listener="#{os_vig.mostrarValoresGrid()}" update="tabelaItensContrato" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formItensContrato">
                    <p:dialog widgetVar="dlgItensContrato" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dlgCadastrar">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgItensContrato').closeIcon.unbind('click');

                                //register your own
                                PF('dlgItensContrato').closeIcon.click(function (e) {
                                    $("#formItensContrato\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>

                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgItensContrato').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarItensContrato}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent;" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="codfil" value="#{localemsgs.CodFil}: "  />
                                <p:selectOneMenu id="codfil" value="#{os_vig.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 styleClass="filial" style="width: 100%" disabled="true"
                                                 filter="true" filterMatchMode="contains" >
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>

                                <p:outputLabel for="contrato" value="#{localemsgs.Contrato}:"/>
                                <p:inputText id="contrato" value="#{os_vig.ctrItem.contrato}" style="width: 100%;" disabled="true"/>
                            </p:panelGrid>

                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-1,ui-grid-col-3,ui-grid-col-2,ui-grid-col-1,ui-grid-col-3"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="tipoPosto" value="#{localemsgs.TipoPosto}: "  />
                                <p:inputText id="tipoPosto" value="#{os_vig.ctrItem.tipoPosto}" style="width: 100%;" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.ctrItem.tipoPosto}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoPosto}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains" >
                                    <p:ajax event="itemSelect" update="tipoPosto"/>
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{os_vig.tipoPosto}" />
                                </p:selectOneMenu>

                                <p:outputLabel for="tipoCalc" value="#{localemsgs.TipoCalc}: "  />
                                <p:inputText id="tipoCalc" value="#{os_vig.ctrItem.tipoCalc}" style="width: 100%;" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.ctrItem.tipoCalc}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoCalc}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="tipoCalc" >
                                    <p:ajax event="itemSelect" update="tipoCalc"/>
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{os_vig.tipoCalc}" />
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "  />
                                <p:inputText id="descricao" value="#{os_vig.ctrItem.descricao}" style="width: 100%;"/>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                         layout="grid" styleClass="ui-panelgrid-blank" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}">
                                <p:column>
                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-8,ui-grid-col-4"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <h:outputText value="#{localemsgs.Precos}" style="line-height: 1.74857143; font-weight: 600;"/>
                                        <h:outputText value="#{localemsgs.Franquia}" style="line-height: 1.74857143; font-weight: 600;"/>
                                    </p:panelGrid>
                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-5,ui-grid-col-4"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel value="#{localemsgs.Rotineiro}: "  />
                                        <p:inputText id="valorRot" value="#{os_vig.ctrItem.valorRot}" class="celula-right" style="width: 100%;"/>
                                        <p:inputText id="franquiaRot" value="#{os_vig.ctrItem.franquiaRot}" class="celula-right" style="width: 100%;"/>

                                        <p:outputLabel value="#{localemsgs.Eventual}: "  />
                                        <p:inputText id="valorEve" value="#{os_vig.ctrItem.valorEve}" class="celula-right" style="width: 100%;"/>
                                        <p:inputText id="franquiaEve" value="#{os_vig.ctrItem.franquiaEve}" class="celula-right" style="width: 100%;"/>

                                        <p:outputLabel value="#{localemsgs.Especial}: "  />
                                        <p:inputText id="valorEsp" value="#{os_vig.ctrItem.valorEsp}" class="celula-right" style="width: 100%;"/>
                                        <p:inputText id="franquiaEsp" value="#{os_vig.ctrItem.franquiaEsp}" class="celula-right" style="width: 100%;"/>

                                        <p:outputLabel value="#{localemsgs.AssistenciaTecnica}: "  />
                                        <p:inputText id="valorAst" value="#{os_vig.ctrItem.valorAst}" class="celula-right" style="width: 100%;"/>
                                        <p:inputText id="franquiaAst" value="#{os_vig.ctrItem.franquiaAst}" class="celula-right" style="width: 100%;"/>
                                    </p:panelGrid>
                                </p:column>
                                <p:column>
                                    <h:outputText value="#{localemsgs.AssistenciaTecnica} - #{localemsgs.TiposEspecificos}"
                                                  style="line-height: 1.74857143; font-weight: 600;"/>
                                </p:column>
                            </p:panelGrid>

                            <p:panel rendered="#{os_vig.os_vigSelecionado.tipoOS eq 4}" style="background: transparent">
                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-6,ui-grid-col-2"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="" value="#{localemsgs.ValorServico}"/>
                                    <p:inputText id="valorPosto" value="#{os_vig.ctrItem.valorPosto}" style="width: 100%;"/>

                                    <p:column/>
                                    <p:selectBooleanCheckbox itemLabel="#{localemsgs.InibirReajuste}"/>
                                </p:panelGrid>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-1,ui-grid-col-9"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="codTipo" value="#{localemsgs.CodigoOperacional}: "  />
                                    <p:inputText id="codTipo" value="#{os_vig.ctrItem.codTipo}" style="width: 100%;" disabled="true"/>
                                    <p:selectOneMenu value="#{os_vig.ctrItem.codTipo}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CodigoOperacional}"
                                                     styleClass="filial" style="width: 100%" disabled="true"
                                                     filter="true" filterMatchMode="contains" >
                                        <p:ajax event="itemSelect" update="codTipo"/>
                                    </p:selectOneMenu>
                                </p:panelGrid>

                                <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,
                                             ui-grid-col-2,ui-grid-col-2,ui-grid-col-2"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="reforco" value="#{localemsgs.ValorReforco}:" />
                                    <p:inputText id="reforco" value="#{os_vig.ctrItem.reforco}" style="width: 100%;"/>

                                    <p:outputLabel for="HEDiurna" value="#{localemsgs.ValorHEDiurna} #{localemsgs.Tipo} 1:"  />
                                    <p:inputText id="HEDiurna" value="#{os_vig.ctrItem.HEDiurna}" style="width: 100%;"/>

                                    <p:outputLabel for="HEDiurna2" value="#{localemsgs.Tipo} 2:"  />
                                    <p:inputText id="HEDiurna2" value="#{os_vig.ctrItem.HEDiurna2}" style="width: 100%;"/>

                                    <p:column/>
                                    <p:column/>

                                    <p:outputLabel for="HENoturna" value="#{localemsgs.ValorHENoturna} #{localemsgs.Tipo} 1:"  />
                                    <p:inputText id="HENoturna" value="#{os_vig.ctrItem.HENoturna}" style="width: 100%;"/>

                                    <p:outputLabel for="HENoturna2" value="#{localemsgs.Tipo} 2:"  />
                                    <p:inputText id="HENoturna2" value="#{os_vig.ctrItem.HENoturna2}" style="width: 100%;"/>
                                </p:panelGrid>

                                <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-2"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="cargo" value="#{localemsgs.Cargo}: "  />
                                    <p:inputText id="cargo" value="#{os_vig.ctrItem.cargo}" style="width: 100%;" disabled="true"/>
                                    <p:selectOneMenu value="#{os_vig.ctrItem.cargo}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cargo}"
                                                     styleClass="filial" style="width: 100%" disabled="true"
                                                     filter="true" filterMatchMode="contains" >
                                        <p:ajax event="itemSelect" update="cargo"/>
                                    </p:selectOneMenu>

                                    <p:outputLabel for="qtdeFunc" value="#{localemsgs.QtdeFunc}:"  />
                                    <p:inputText id="qtdeFunc" value="#{os_vig.ctrItem.qtdeFunc}" style="width: 100%;"/>
                                </p:panelGrid>

                                <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,
                                             ui-grid-col-2,ui-grid-col-2,ui-grid-col-2"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="salario" value="#{localemsgs.Salario}:" />
                                    <p:inputText id="salario" value="#{os_vig.ctrItem.salario}" style="width: 100%;"/>

                                    <p:outputLabel for="CHFuncion" value="#{localemsgs.CHMensal}:"  />
                                    <p:inputText id="CHFuncion" value="#{os_vig.ctrItem.CHFuncion}" style="width: 100%;"/>

                                    <p:outputLabel for="indiceHE" value="#{localemsgs.Tipo}:"  />
                                    <p:inputText id="indiceHE" value="#{os_vig.ctrItem.indiceHE}" style="width: 100%;"/>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <h:outputText value="#{localemsgs.TotalPosto}"/>
                                    <p:column>
                                        <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,
                                                     ui-grid-col-2,ui-grid-col-2,ui-grid-col-2"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="horas" value="#{localemsgs.CHDiaria}:" />
                                            <p:inputText id="horas" value="#{os_vig.ctrItem.horas}" style="width: 100%;"/>

                                            <p:outputLabel for="CHSeman" value="#{localemsgs.CHSeman}:"  />
                                            <p:inputText id="CHSeman" value="#{os_vig.ctrItem.CHSeman}" style="width: 100%;"/>

                                            <p:outputLabel for="CHMensal" value="#{localemsgs.CHMensal}:"  />
                                            <p:inputText id="CHMensal" value="#{os_vig.ctrItem.CHMensal}" style="width: 100%;"/>
                                        </p:panelGrid>
                                    </p:column>
                                </p:panelGrid>
                            </p:panel>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Cadastrar novo -->
                <h:form id="formCadastrar" class="form-inline">
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" focus="descricao"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dlgCadastrar" style="background-color:#FFF !important">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formCadastrar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>

                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarOS}" style="color:#022a48" rendered="#{os_vig.flag eq 1}"/>
                            <h:outputText value="#{localemsgs.OS} #{os_vig.os_vigSelecionado.OS}" style="color:#022a48" rendered="#{os_vig.flag eq 2}"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; padding-right: 10px !important" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                            <div class="col-md-12 row" style="padding:0px !important; margin:8px 0px 0px 0px !important">
                                <div class="col-md-4 col-sm-4 col-xs-12">
                                    <p:outputLabel for="codfil" value="#{localemsgs.Filial}"  />
                                    <p:selectOneMenu id="codfil" value="#{os_vig.filial}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                     styleClass="filial" style="width: 100%" disabled="#{os_vig.flag eq 2}"
                                                     filter="true" filterMatchMode="contains" >
                                        <p:ajax event="itemSelect" update="formCadastrar:cadastrar" listener="#{os_vig.selecionarFilial}"/>
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-6">
                                    <p:outputLabel value="#{localemsgs.Vigencia}"  />
                                    <p:datePicker id="dtInicio" value="#{os_vig.os_vigSelecionado.dtInicio}" readonlyInput="true"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true"
                                                  required="true" monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-6">
                                    <p:outputLabel value="#{localemsgs.Vigencia}"  />
                                    <p:datePicker id="dtFim" value="#{os_vig.os_vigSelecionado.dtFim}" readonlyInput="true"
                                                  required="true" monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                                <div class="col-md-4 col-sm-4 col-xs-12">
                                    <p:outputLabel for="descricao" value="#{localemsgs.Descricao}:"/>
                                    <p:inputText id="descricao" value="#{os_vig.os_vigSelecionado.descricao}"
                                                 required="true" label="#{localemsgs.Descricao}" style="width: 100%;"/>
                                </div>
                            </div>
                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">
                                <div class="col-md-4 col-sm-4 col-xs-12" style="padding:0px !important">
                                    <div class="col-md-12">
                                        <p:outputLabel for="tipoOS" value="#{localemsgs.TipoOS}"/>    
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-3">    
                                        <p:inputText id="tipoOS" value="#{os_vig.os_vigSelecionado.tipoOS}" style="width: 100%; background-color:#EEE !important; text-align:center !important;" disabled="true"/>    
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9">
                                        <p:selectOneMenu value="#{os_vig.os_vigSelecionado.tipoOS}"
                                                         required="true" label="#{localemsgs.TipoOS}" style="width: 100%;"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoOS}">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.TransporteUrbano}" itemValue="1"/>
                                            <f:selectItem itemLabel="#{localemsgs.TransporteInterurbano}" itemValue="2"/>
                                            <f:selectItem itemLabel="#{localemsgs.Tesouraria}" itemValue="3"/>
                                            <f:selectItem itemLabel="#{localemsgs.OutrosServicos}" itemValue="4"/>
                                            <f:selectItem itemLabel="#{localemsgs.TransporteInterestadual}" itemValue="6"/>
                                            <p:ajax event="itemSelect" update="msgs tipoOS formCadastrar:tabs:panelTabGeral"/>
                                        </p:selectOneMenu>
                                    </div>
                                </div>
                                <div class="col-md-4 col-sm-4 col-xs-12" style="padding:0px !important">
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <p:outputLabel for="codSrv" value="#{localemsgs.Servico}"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-3">
                                        <p:inputText id="codSrv" value="#{os_vig.os_vigSelecionado.codSrv}" style="width: 100%; background-color:#EEE !important; text-align:center !important;" disabled="true"/>
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9">
                                        <p:column>
                                            <p:selectOneMenu id="codSrvList" value="#{os_vig.tipoSrvCli}" converter="omnifaces.SelectItemsConverter"
                                                             required="true" label="#{localemsgs.CodigoServico}"
                                                             style="width: calc(100% - 38px); float: left;"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CodigoServico}"
                                                             filter="true" filterMatchMode="contains" >
                                                <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                                <f:selectItems value="#{os_vig.tipoSrvCliList}" var="tipoSrvCli" itemValue="#{tipoSrvCli}"
                                                               itemLabel="#{tipoSrvCli.descricao}" noSelectionValue=""/>
                                                <p:ajax event="itemSelect" listener="#{os_vig.selecionarCodSrv}" update="msgs codSrv"/>
                                            </p:selectOneMenu>
                                            <p:commandLink title="#{localemsgs.Adicionar}"
                                                           actionListener="#{os_vig.preCadastroCodgioServico}"
                                                           update="msgs" process="@this" style="float:right">
                                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30"/>
                                            </p:commandLink>
                                        </p:column>
                                    </div>
                                </div>
                                <div class="col-md-4 col-sm-4 col-xs-12">
                                    <p:outputLabel for="situacao" value="#{localemsgs.Situacao}"/>
                                    <p:selectOneMenu value="#{os_vig.os_vigSelecionado.situacao}" id="situacao"
                                                     required="true" label="#{localemsgs.Situacao}"
                                                     style="width: 100%; color: #{os_vig.os_vigSelecionado.situacao eq 'A' ? '#29de29' : os_vig.os_vigSelecionado.situacao eq 'X' ? 'blue' : 'red'}"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Ativa}" itemValue="A"/>   
                                        <f:selectItem itemLabel="#{localemsgs.Extraordinaria}" itemValue="X"/>
                                        <f:selectItem itemLabel="#{localemsgs.Inativa}" itemValue="I"/>
                                        <p:ajax event="itemSelect" update="situacao"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>
                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">
                                <div class="col-md-4" style="padding:0px !important">
                                    <div class="col-md-12">
                                        <p:outputLabel for="contrato" value="#{localemsgs.Contrato}"/>    
                                    </div>    
                                    <div class="col-md-3 col-sm-3 col-xs-3">    
                                        <p:inputText id="contrato" value="#{os_vig.os_vigSelecionado.contrato}" style="width: 100%; background-color:#EEE !important; text-align:center !important;" disabled="true"/>
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9">
                                        <p:autoComplete
                                            value="#{os_vig.contrato}"
                                            completeMethod="#{os_vig.listarContrato}"
                                            required="true"
                                            label="#{localemsgs.Contrato}"
                                            minQueryLength="3"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Contrato}"
                                            scrollHeight="200"
                                            inputStyle="width: 100%"
                                            placeholder="#{localemsgs.Contrato}"
                                            forceSelection="true"
                                            style="min-width:100% !important"
                                            var="cc"
                                            itemLabel="#{cc.descricao}"
                                            itemValue="#{cc}"
                                            >
                                            <p:column>
                                                <h:outputText value="#{cc.contrato} - #{cc.tipo} - #{cc.descricao}"/>
                                            </p:column>
                                            <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.contratos}" />
                                            <p:ajax event="itemSelect" listener="#{os_vig.selecionarContrato}" update="msgs contrato"/>
                                        </p:autoComplete>
                                    </div>
                                </div>

                                <div class="col-md-4" style="padding:0px !important">
                                    <div class="col-md-12">
                                        <p:outputLabel for="cliFat" value="#{localemsgs.CliFat}"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-3">    
                                        <p:inputText id="cliFat" value="#{os_vig.os_vigSelecionado.cliFat}" style="width: 100%; background-color:#EEE !important; text-align:center !important;" disabled="true"/>    
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9">
                                        <p:autoComplete value="#{os_vig.cliFat}" completeMethod="#{os_vig.listarCliFat}"
                                                        required="true" label="#{localemsgs.CliFat}" minQueryLength="3"
                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CliFat}"  scrollHeight="200"
                                                        inputStyle="width: 100%; font-weight: bold; color:#000 !important;border-color:orangered !important; background-color:lightyellow" placeholder="#{localemsgs.CliFat}" forceSelection="true"
                                                        style="min-width:100% !important;" var="cf" itemLabel="#{cf.NRed}" itemValue="#{cf}">
                                            <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.cliFatList}" />
                                            <p:ajax event="itemSelect" listener="#{os_vig.selecionarCliFat}" update="msgs cliFat panelCliFat"/>
                                        </p:autoComplete>
                                        <p:panel id="panelCliFat" style="background: transparent; padding:0px !important; margin:0px !important">
                                            <div class="col-md-12" style="padding-left: 0px !important;">
                                                <h:outputText id="cliFatEnde" value="#{os_vig.cliFat.ende} - #{os_vig.cliFat.bairro} - #{os_vig.cliFat.cidade} - #{os_vig.cliFat.estado}"
                                                              style="width: 100%; color: #999;white-space:nowrap !important" rendered="#{os_vig.cliFat ne null}"/>
                                            </div>

                                            <div class="col-md-12" style="padding-left: 0px !important; display:#{os_vig.cliFat ne null and os_vig.cliFat.fone1 ne '' and os_vig.cliFat.fone1 ne '0' ? '':'none'}">
                                                <h:outputText id="cliFatFone" value="#{os_vig.cliFat.fone1}" converter="conversorFone"
                                                              style="width: 100%; color: #999" rendered="#{os_vig.cliFat ne null and os_vig.cliFat.fone1 ne ''}"/>

                                                <h:outputText value=" - " style="width: 100%; color: #999" rendered="#{os_vig.cliFat ne null and os_vig.cliFat.fone2 ne ''}"/>

                                                <h:outputText id="cliFatFone2" value="#{os_vig.cliFat.fone2}" converter="conversorFone"
                                                              style="width: 100%; color: #999" rendered="#{os_vig.cliFat ne null and os_vig.cliFat.fone2 ne ''}"/>
                                            </div>

                                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-left: 0px !important; padding-right:10px !important; text-align: right;">
                                                <p:outputLabel for="cliFatEmail" value="#{localemsgs.Cobranca}: " rendered="#{os_vig.cliFat ne null}"/>
                                            </div>

                                            <div class="col-md-9 col-sm-9 col-xs-9" style="padding-left: 0px !important">
                                                <h:outputText id="cliFatEmail" value="#{os_vig.cliFat.email}"
                                                              style="width: 100%; color: #999" rendered="#{os_vig.cliFat ne null and os_vig.cliFat.email ne ''}"/>
                                                <h:outputText value="#{localemsgs.EmailNaoInformado}"
                                                              style="width: 100%; color: red;" rendered="#{os_vig.cliFat ne null and os_vig.cliFat.email eq ''}"/>
                                            </div>
                                        </p:panel>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">
                                <div class="col-md-4" style="padding-top:0px !important">
                                    <p:outputLabel for="aditivo" value="#{localemsgs.Aditivo}"/> 
                                    <p:inputText id="aditivo" value="#{os_vig.os_vigSelecionado.aditivo}" style="width: 100%;"/>
                                </div>
                                <div class="col-md-4" style="padding-top:0px !important">
                                    <p:outputLabel for="CCusto" value="#{localemsgs.CCusto}"/>
                                    <p:inputText id="CCusto" value="#{os_vig.os_vigSelecionado.CCusto}" style="width: 100%;"/>
                                </div>
                                <div class="col-md-4" style="padding-top:0px !important">
                                    <p:outputLabel for="msgExtrato" value="#{localemsgs.MsgExtrato}"/>
                                    <p:inputText id="msgExtrato" value="#{os_vig.os_vigSelecionado.msgExtrato}" style="width: 100%;"/>
                                </div>
                                <div class="col-md-4" style="padding:0px !important">
                                    <div class="col-md-12">
                                        <p:outputLabel for="agrupadorNF" value="#{localemsgs.AgrupadorNF}"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-3">
                                        <p:inputText id="agrupadorNF" value="#{os_vig.os_vigSelecionado.agrupador}" style="width: 100%; background-color:#EEE !important; text-align:center !important;" disabled="true"/>
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9" style="white-space:nowrap !important">
                                        <p:autoComplete value="#{os_vig.agrupador}" completeMethod="#{os_vig.listarAgrupador}"
                                                        required="true" label="#{localemsgs.AgrupadorNF}" minQueryLength="3"
                                                        style="width: calc(100% - 20px); float: left;"
                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.AgrupadorNF}"  scrollHeight="200"
                                                        inputStyle="width: calc(100% - 20px)" placeholder="#{localemsgs.AgrupadorNF}" forceSelection="true"
                                                        var="ag" itemLabel="#{ag.descricao}" itemValue="#{ag}">
                                            <p:column>
                                                <h:outputText value="#{ag.codigo} - #{ag.descricao}"/>
                                            </p:column>
                                            <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.agrupadorList}" />
                                            <p:ajax event="itemSelect" listener="#{os_vig.selecionarAgrupador}"
                                                    update="msgs agrupadorNF panelAgrupadorNF"/>
                                        </p:autoComplete>
                                        <p:commandLink title="#{localemsgs.Adicionar}"
                                                       actionListener="#{os_vig.preCadastroAgrupadorNF}"
                                                       update="msgs" process="@this">
                                            <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30" style="float:right; position:absolute; right:0px !important; margin-top:2px !important"/>
                                        </p:commandLink>
                                        <p:panel id="panelAgrupadorNF" style="background: transparent; padding:0px !important; margin:0px !important">
                                            <div class="col-md-7 col-sm-7 col-xs-12" style="padding-left: 9px !important;">
                                                <h:outputText id="agrupadorMun" value="#{os_vig.agrupador.municipio}/#{os_vig.agrupador.UF} #{localemsgs.ISS} #{os_vig.agrupador.aliqISS}%"
                                                              style="width: 100%;" rendered="#{os_vig.agrupador ne null}"/>
                                            </div>
                                            <div class="col-md-5 col-sm-5 col-xs-12" style="padding-left: 9px !important;">
                                                <h:outputText id="agrupadorISSRet" value="#{os_vig.agrupador.ICMSRet eq 'S' ? localemsgs.ComRetencao : localemsgs.SemRetencao}"
                                                              style="width: 100%;" rendered="#{os_vig.agrupador ne null}"/>
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-left: 9px !important;">
                                                <h:outputText id="agrupadorCFOP" value="#{os_vig.agrupador.CFOP} #{os_vig.agrupador.CFOPDesc}"
                                                              rendered="#{os_vig.agrupador ne null}" style="width: 100%;"/>
                                            </div>
                                        </p:panel>
                                    </div>
                                </div>
                                <div class="col-md-4" style="padding:0px !important">
                                    <div class="col-md-12">
                                        <p:outputLabel for="agrupador2NF" value="#{localemsgs.Agrupador2NF}"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-3">
                                        <p:inputText id="agrupador2NF" value="#{os_vig.os_vigSelecionado.OSGrp}" style="width: 100%; background-color:#EEE !important; text-align:center !important;" disabled="true"/>
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9">
                                        <p:autoComplete value="#{os_vig.agrupador2}" completeMethod="#{os_vig.listaragrupador2}"
                                                        required="true" label="#{localemsgs.Agrupador2NF}" minQueryLength="3"
                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Agrupador2NF}"  scrollHeight="200"
                                                        inputStyle="width: 100%" placeholder="#{localemsgs.Agrupador2NF}" forceSelection="true"
                                                        style="min-width:100% !important" var="ag2" itemLabel="#{ag2.descricao}" itemValue="#{ag2}">
                                            <p:column>
                                                <h:outputText value="#{ag2.codigo} - #{ag2.descricao}"/>
                                            </p:column>
                                            <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.agrupador2List}" />
                                            <p:ajax event="itemSelect" listener="#{os_vig.selecionaragrupador2}"
                                                    update="msgs agrupador2NF panelAgrupador2NF"/>
                                        </p:autoComplete>
                                        <p:panel id="panelAgrupador2NF" style="background: transparent; padding:0px !important; margin:0px !important">
                                            <div class="col-md-7 col-sm-7 col-xs-12" style="padding-left: 9px !important;">
                                                <h:outputText id="agrupador2Mun" value="#{os_vig.agrupador2.municipio}/#{os_vig.agrupador2.UF} #{localemsgs.ISS} #{os_vig.agrupador2.aliqISS}%"
                                                              style="width: 100%;" rendered="#{os_vig.agrupador2 ne null}"/>
                                            </div>
                                            <div class="col-md-5 col-sm-5 col-xs-12" style="padding-left: 9px !important;">
                                                <h:outputText id="agrupador2ISSRet" value="#{os_vig.agrupador2.ICMSRet eq 'S' ? localemsgs.ComRetencao : localemsgs.SemRetencao}"
                                                              style="width: 100%;" rendered="#{os_vig.agrupador2 ne null}"/>
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-left: 9px !important;">
                                                <h:outputText id="agrupador2CFOP" value="#{os_vig.agrupador2.CFOP} #{os_vig.agrupador2.CFOPDesc}"
                                                              style="width: 100%;" rendered="#{os_vig.agrupador2 ne null}"/>
                                            </div>
                                        </p:panel>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px 0px 0px 8px !important">
                                <p:tabView id="tabs" activeIndex="0" onTabShow="PF('dlgCadastrar').initPosition()" dynamic="true" >
                                    <p:tab id="tabGeral" title="#{localemsgs.Geral}" class="AlturaTab">

                                        <p:panel id="panelTabGeral" style="background: #EEE; overflow:auto; min-height:190px !important;height:190px !important;max-height:190px !important; padding:8px 12px 0px 0px !important">
                                            <p:panel class="col-md-#{os_vig.os_vigSelecionado.tipoOS ne 4?'6':'12'}" id="panelCliente" style="background: transparent;padding:0px !important">
                                                <div class="col-md-12">
                                                    <p:outputLabel for="cliente" value="#{localemsgs.Cliente}"/>
                                                </div>
                                                <div class="col-md-3 col-sm-3 col-xs-3">    
                                                    <p:inputText id="cliente" value="#{os_vig.os_vigSelecionado.cliente}" style="width: 100%;" disabled="true"/>
                                                </div>
                                                <div class="col-md-9 col-sm-9 col-xs-9">     
                                                    <p:autoComplete value="#{os_vig.cliente}" completeMethod="#{os_vig.listarCliente}"
                                                                    required="true" label="#{localemsgs.Cliente}" minQueryLength="3"
                                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}"  scrollHeight="200"
                                                                    inputStyle="min-width: 100%; font-weight: bold; color:#000 !important; border-color:orangered !important;background-color:lightyellow" placeholder="#{localemsgs.Cliente}" forceSelection="true"
                                                                    style="width: 100%; font-weight: bold; font-size:  1.1em" var="cl" itemLabel="#{cl.NRed}" itemValue="#{cl}">
                                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.clientes}" />
                                                        <p:ajax event="itemSelect" listener="#{os_vig.selecionarCliente}" update="msgs cliente panelCliente"/>
                                                    </p:autoComplete>
                                                </div>
                                                <div class="col-md-12" style="padding:0px !important;">
                                                    <div class="col-md-3 col-sm-3 col-xs-3"></div>
                                                    <div class="col-md-9 col-sm-9 col-xs-9" style="padding:0px !important;">    
                                                        <div class="col-md-12">
                                                            <h:outputText id="clienteEnde" value="#{os_vig.cliente.ende} - #{os_vig.cliente.bairro} - #{os_vig.cliente.cidade} - #{os_vig.cliente.estado}"
                                                                          style="width: 100%; color: #999" rendered="#{os_vig.cliente ne null}"/>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <h:outputText id="clienteFone" value="#{os_vig.cliente.fone1}" converter="conversorFone"
                                                                          style="width: 100%; color: #999"
                                                                          rendered="#{os_vig.cliente ne null and os_vig.cliente.fone1 ne ''}"/>

                                                            <h:outputText value=" - " style="width: 100%; color: #999"
                                                                          rendered="#{os_vig.cliente ne null and os_vig.cliente.fone2 ne ''}"/>

                                                            <h:outputText id="clienteFone2" value="#{os_vig.cliente.fone2}" converter="conversorFone"
                                                                          style="width: 100%; color: #999"
                                                                          rendered="#{os_vig.cliente ne null and os_vig.cliente.fone2 ne ''}"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </p:panel>

                                            <p:panel class="col-md-6" id="panelCliDst" style="background: transparent" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}">
                                                <div class="col-md-12">
                                                    <p:outputLabel for="cliDst" value="#{localemsgs.CliDst}" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                </div>
                                                <div class="col-md-3 col-sm-3 col-xs-3">    
                                                    <p:inputText id="cliDst" value="#{os_vig.os_vigSelecionado.cliDst}" style="width: 100%;" disabled="true"
                                                                 rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                </div>
                                                <div class="col-md-9 col-sm-9 col-xs-9">     
                                                    <p:autoComplete value="#{os_vig.cliDst}" completeMethod="#{os_vig.listarCliDst}"
                                                                    required="#{os_vig.os_vigSelecionado.tipoOS ne 4}"
                                                                    label="#{localemsgs.CliDst}" minQueryLength="3" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"
                                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CliDst}"  scrollHeight="200"
                                                                    inputStyle="width: 100%; font-weight: bold; color:#000 !important; border-color:orangered !important;background-color:lightyellow" placeholder="#{localemsgs.CliDst}" forceSelection="true"
                                                                    style="width: 100%; font-weight: bold; font-size:  1.1em" var="cl" itemLabel="#{cl.NRed}" itemValue="#{cl}">
                                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.cliDstList}" />
                                                        <p:ajax event="itemSelect" listener="#{os_vig.selecionarCliDst}" update="msgs cliDst panelCliDst"/>
                                                    </p:autoComplete>
                                                </div>
                                                <div class="col-md-12" style="padding:0px !important;">
                                                    <div class="col-md-3 col-sm-3 col-xs-3"></div>
                                                    <div class="col-md-9 col-sm-9 col-xs-9" style="padding:0px !important;">    
                                                        <div class="col-md-12">
                                                            <h:outputText id="cliDstEnde" value="#{os_vig.cliDst.ende} - #{os_vig.cliDst.bairro} - #{os_vig.cliDst.cidade} - #{os_vig.cliDst.estado}"
                                                                          style="width: 100%; color: #999"
                                                                          rendered="#{os_vig.cliDst ne null and os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <h:outputText id="cliDstFone" value="#{os_vig.cliDst.fone1}" converter="conversorFone"
                                                                          style="width: 100%; color: #999"
                                                                          rendered="#{os_vig.cliDst ne null and os_vig.cliDst.fone1 ne '' and os_vig.os_vigSelecionado.tipoOS ne 4}"/>

                                                            <h:outputText value=" - " style="width: 100%; color: #999"
                                                                          rendered="#{os_vig.cliDst ne null and os_vig.cliDst.fone2 ne '' and os_vig.os_vigSelecionado.tipoOS ne 4}"/>

                                                            <h:outputText id="cliDstFone2" value="#{os_vig.cliDst.fone2}" converter="conversorFone"
                                                                          style="width: 100%; color: #999"
                                                                          rendered="#{os_vig.cliDst ne null and os_vig.cliDst.fone2 ne '' and os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </p:panel>

                                            <div class="col-md-2">
                                                <p:outputLabel for="KMAsfalto" value="#{localemsgs.KMAsfalto}" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                <p:inputText type="number" id="KMAsfalto" value="#{os_vig.os_vigSelecionado.KM}" style="width: 100%;" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                            </div>

                                            <div class="col-md-2">
                                                <p:outputLabel for="KMTerra" value="#{localemsgs.KMTerra}" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                <p:inputText type="number" id="KMTerra" value="#{os_vig.os_vigSelecionado.KMTerra}" style="width: 100%;" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                            </div>

                                            <p:panel id='panelViaCxF' class="col-md-8" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}" style='background-color:transparent'>
                                                <div class="col-md-3" style="padding-top:0px !important; text-align: center !important; position:relative;">
                                                    <div style="position:absolute; margin-top:20px; border:thin solid #CCC; width: calc(100% - 14px); padding-top:7px !important;padding-bottom:3px !important; border-radius:3px;background-color:#FFF">
                                                        <p:selectBooleanCheckbox id="viaCxF" value="#{os_vig.viaCxF}"
                                                                                 rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}">
                                                            <p:ajax event="change" update="formCadastrar:tabs:panelViaCxF" />
                                                        </p:selectBooleanCheckbox>
                                                        <p:outputLabel for="viaCxF" value="#{localemsgs.ViaCxForte}" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}" style="margin-left:8px !important;"/>
                                                    </div>
                                                </div>
                                                <div class="col-md-3" style="display:#{os_vig.os_vigSelecionado.tipoOS ne 4 and os_vig.viaCxF?'':'none'};">
                                                    <p:outputLabel for="diasCst" value="#{localemsgs.DiasCst}"
                                                                   rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4 and os_vig.viaCxF}"/>
                                                    <p:inputText type="number" id="diasCst" value="#{os_vig.os_vigSelecionado.diasCst}" style="width: 100%;"
                                                                 rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4 and os_vig.viaCxF}">
                                                        <p:ajax event="change" update="formCadastrar:tabs:panelViaCxF" listener="#{os_vig.atualizarViaCxForte}"/>
                                                    </p:inputText >
                                                </div>
                                                <div class="col-md-3" style="display:#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                                       and os_vig.os_vigSelecionado.diasCst gt 0
                                                                                       and os_vig.viaCxF?'':'none'}">
                                                    <p:outputLabel for="entregaSab" value="#{localemsgs.EntregaSab}"
                                                                   rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                               and os_vig.os_vigSelecionado.diasCst gt 0
                                                                               and os_vig.viaCxF}"/>
                                                    <p:selectBooleanCheckbox id="entregaSab" value="#{os_vig.entregaSab}" style="width: 100%;"
                                                                             rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                                         and os_vig.os_vigSelecionado.diasCst gt 0
                                                                                         and os_vig.viaCxF}"/>
                                                </div>
                                                <div class="col-md-3" style="display:#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                                       and os_vig.os_vigSelecionado.diasCst gt 0
                                                                                       and os_vig.viaCxF?'':'none'}">
                                                    <p:outputLabel for="entregaDom" value="#{localemsgs.EntregaDom}"
                                                                   rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                               and os_vig.os_vigSelecionado.diasCst gt 0
                                                                               and os_vig.viaCxF}"/>
                                                    <p:selectBooleanCheckbox id="entregaDom" value="#{os_vig.entregaDom}" style="width: 100%;"
                                                                             rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                                         and os_vig.os_vigSelecionado.diasCst gt 0
                                                                                         and os_vig.viaCxF}"/>
                                                </div>
                                                <div class="col-md-3" style="display:#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                                       and os_vig.os_vigSelecionado.diasCst gt 0
                                                                                       and os_vig.viaCxF?'':'none'}">
                                                    <p:outputLabel for="entregaFer" value="#{localemsgs.EntregaFer}"
                                                                   rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                               and os_vig.os_vigSelecionado.diasCst gt 0
                                                                               and os_vig.viaCxF}"/>
                                                    <p:selectBooleanCheckbox id="entregaFer" value="#{os_vig.entregaFer}" style="width: 100%;"
                                                                             rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4
                                                                                         and os_vig.os_vigSelecionado.diasCst gt 0
                                                                                         and os_vig.viaCxF}"/>
                                                </div>
                                                <div class="col-md-3">
                                                    <p:outputLabel for="GTVQtde" value="#{localemsgs.GTVQtde}" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                    <p:inputText id="GTVQtde" type="number" value="#{os_vig.os_vigSelecionado.GTVQtde}" style="width: 100%;" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                </div>
                                                <div class="col-md-3">
                                                    <p:outputLabel for="GTVEstMin" value="#{localemsgs.GTVEstMin}" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                    <p:inputText id="GTVEstMin" type="number" value="#{os_vig.os_vigSelecionado.GTVEstMin}" style="width: 100%;" rendered="#{os_vig.os_vigSelecionado.tipoOS ne 4}"/>
                                                </div>
                                            </p:panel>
                                        </p:panel>
                                    </p:tab>

                                    <p:tab id="tabItensFaturar" title="#{localemsgs.ItensFaturar}" class="AlturaTab">
                                        <div class="col-md-11 col-sm-11 col-xs-11" style="width:calc(100% - 62px) !important; padding:0px !important; margin:0px !important; height:195px !important; overflow:auto;">
                                            <p:dataTable id="tabelaItensFaturar" value="#{os_vig.os_vitens}" var="os_vitens" rowKey="#{os_vitens.tipoPosto}"
                                                         styleClass="tabela" class="DataGrid" resizableColumns="true"
                                                         reflow="true" emptyMessage="#{localemsgs.SemRegistros}"
                                                         selection="#{os_vig.os_itemSelecionado}" selectionMode="single" style="padding:0px !important; margin-top:0px !important; margin-left:0px !important; max-height:188px !important">
                                                <p:ajax event="rowDblselect" listener="#{os_vig.dblItensFaturamento}" update="formCadastrarItensFaturamento msgs"/>
                                                <p:column headerText="#{localemsgs.TipoPosto}" class="text-center">
                                                    <h:outputText value="#{os_vitens.tipoPosto}" title="#{os_vitens.tipoPosto}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.TipoPostoDesc}" class="text-center">
                                                    <h:outputText value="#{os_vitens.tipoPostoDesc}" title="#{os_vitens.tipoPostoDesc}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.TipoCalc}" class="text-center">
                                                    <h:outputText value="#{os_vitens.tipoCalc}" title="#{os_vitens.tipoCalc}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Qtde}" class="text-center">
                                                    <h:outputText value="#{os_vitens.qtde}" title="#{os_vitens.qtde}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Valor}" class="text-center">
                                                    <h:outputText value="#{os_vitens.valor}" title="#{os_vitens.valor}" converter="conversormoeda" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.DtInicio}" class="text-center">
                                                    <h:outputText value="#{os_vitens.dtInicio}" title="#{os_vitens.dtInicio}" converter="conversorData" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.DtFim}" class="text-center">
                                                    <h:outputText value="#{os_vitens.dtFim}" title="#{os_vitens.dtFim}" converter="conversorData" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Obs}" class="text-center">
                                                    <h:outputText value="#{os_vitens.obs}" title="#{os_vitens.obs}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                                    <h:outputText value="#{os_vitens.operador}" title="#{os_vitens.operador}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                                    <h:outputText value="#{os_vitens.dt_Alter}" title="#{os_vitens.dt_Alter}" converter="conversorData" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                                    <h:outputText value="#{os_vitens.hr_Alter}" title="#{os_vitens.hr_Alter}" converter="conversorHora" class="text-center"/>
                                                </p:column>
                                            </p:dataTable>
                                        </div>
                                        <div class="col-md-1 col-sm-1 col-xs-1" style="width: 58px; margin-top: 8px; padding:0px !important; margin:0px !important; height:190px; margin-left:4px !important; border:thin solid #DDD;background-color:#EEE;">
                                            <p:panel style="width:100% !important; width:100%; text-align:center; margin-top:10px !important; background: transparent">
                                                <p:commandLink actionListener="#{os_vig.preCadastroItens}"
                                                               update="msgs formCadastrarItensFaturamento:cadastrarItens"
                                                               title="#{localemsgs.Cadastrar}" >
                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40" style="margin-bottom:2px" />
                                                </p:commandLink>

                                                <p:commandLink actionListener="#{os_vig.preCadastroItensEditar}"
                                                               update="msgs formCadastrarItensFaturamento:cadastrarItens"
                                                               title="#{localemsgs.Editar}">
                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="40" height="40" style="margin-bottom:2px" />
                                                </p:commandLink>

                                                <p:commandLink actionListener="#{os_vig.preCadastroItensExcluir}"
                                                               update="msgs tabelaItensFaturar"
                                                               title="#{localemsgs.Excluir}">
                                                    <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="40" height="40" style="margin-bottom:2px" />
                                                </p:commandLink>

                                                <p:commandLink actionListener="#{os_vig.preCadastroItensContrato()}"
                                                               update="msgs"
                                                               title="#{localemsgs.ItensContrato}" process="@this">
                                                    <p:graphicImage url="../assets/img/icone_cadastros.png" width="40" height="40"/>
                                                </p:commandLink>
                                            </p:panel>
                                        </div>
                                    </p:tab>

                                    <p:tab id="tabFrequenciaServicos" title="#{localemsgs.FrequenciaServicos}" class="AlturaTab">
                                        <div class="col-md-12" style="padding:0px !important; margin:0px !important; height:195px !important; overflow:auto;">
                                            <ui:include src="/faturamento/os_vig/tabela_frequencia.xhtml"/>
                                        </div>
                                    </p:tab>

                                </p:tabView>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding:8px 0px 0px 0px !important; text-align:right">
                                <p:commandLink rendered="#{os_vig.flag eq 1}" id="cadastro" action="#{os_vig.cadastrar}"
                                               update=" :main :msgs :cabecalho formCadastrar:cadastrar"
                                               title="#{localemsgs.Cadastrar}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                                <p:commandLink rendered="#{os_vig.flag eq 2}" id="edit" action="#{os_vig.editar}"
                                               update=":msgs :main:tabela :cabecalho formCadastrar:cadastrar"
                                               title="#{localemsgs.Editar}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formCodigoServico">
                    <p:dialog widgetVar="dlgCodigoServico" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              styleClass="dlgCadastrar">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCodigoServico').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCodigoServico').closeIcon.click(function (e) {
                                    $("#formCodigoServico\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>

                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCodigoServico').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarCodigoServico}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent;" styleClass="cadastrar">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-2" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="codigo" value="#{localemsgs.Codigo}"/>
                                <p:inputText id="codigo" value="#{os_vig.novoTipoSrvCli.codigo}" style="width: 100%"
                                             required="true" label="#{localemsgs.Codigo}" disabled="#{os_vig.flagTipoSrvCli eq 2 }"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Codigo}">
                                    <p:ajax event="blur" listener="#{os_vig.buscarCodigoServico}"
                                            update="msgs"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}"/>
                                <p:inputText id="descricao" value="#{os_vig.novoTipoSrvCli.descricao}" style="width: 100%"
                                             required="true" label="#{localemsgs.Descricao}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Descricao}"/>
                            </p:panelGrid>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-8" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="banco" value="#{localemsgs.Banco}"/>
                                <p:inputText id="banco" value="#{os_vig.novoTipoSrvCli.banco}" style="width: 100%" disabled="true"/>
                                <p:autoComplete value="#{os_vig.bb}" completeMethod="#{os_vig.listarBancos}"
                                                required="true" label="#{localemsgs.Banco}" minQueryLength="3"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Banco}" scrollHeight="200"
                                                inputStyle="width: 100%" placeholder="#{localemsgs.Banco}" forceSelection="true"
                                                style="min-width:100% !important" var="bb" itemLabel="#{bb.NRed}" itemValue="#{bb}">
                                    <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.bancos}" />
                                    <p:ajax event="itemSelect" listener="#{os_vig.selecionarBanco}" update="msgs banco"/>
                                </p:autoComplete>

                                <p:outputLabel for="TAtend" value="#{localemsgs.TAtend}"/>
                                <p:inputText id="TAtend" value="#{os_vig.novoTipoSrvCli.TAtend}" style="width: 100%" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.novoTipoSrvCli.TAtend}"
                                                 required="true" label="#{localemsgs.TAtend}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TAtend}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{os_vig.TAtend}" />
                                    <p:ajax event="itemSelect" update="TAtend"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="TCob" value="#{localemsgs.TCob}"/>
                                <p:inputText id="TCob" value="#{os_vig.novoTipoSrvCli.TCob}" style="width: 100%" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.novoTipoSrvCli.TCob}"
                                                 required="true" label="#{localemsgs.TCob}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TCob}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{os_vig.TCob}" />
                                    <p:ajax event="itemSelect" update="TCob"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="TCar" value="#{localemsgs.TCar}"/>
                                <p:inputText id="TCar" value="#{os_vig.novoTipoSrvCli.TCar}" style="width: 100%" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.novoTipoSrvCli.TCar}"
                                                 required="true" label="#{localemsgs.TCar}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TCar}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{os_vig.TCar}" />
                                    <p:ajax event="itemSelect" update="TCar"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="tipoSrv" value="#{localemsgs.TipoSrv}"/>
                                <p:inputText id="tipoSrv" value="#{os_vig.novoTipoSrvCli.tipoSrv}" style="width: 100%" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.novoTipoSrvCli.tipoSrv}"
                                                 required="true" label="#{localemsgs.TipoSrv}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoSrv}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{os_vig.tipoSrv}" />
                                    <p:ajax event="itemSelect" update="tipoSrv"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="ER" value="#{localemsgs.TRota}"/>
                                <p:inputText id="ER" value="#{os_vig.novoTipoSrvCli.ER}" style="width: 100%" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.novoTipoSrvCli.ER}"
                                                 required="true" label="#{localemsgs.TRota}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TRota}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{os_vig.TRota}" />
                                    <p:ajax event="itemSelect" update="ER"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="aditivo" value="#{localemsgs.Aditivo}"/>
                                <p:inputText id="aditivo" value="#{os_vig.novoTipoSrvCli.aditivo}" style="width: 100%"/>

                                <p:outputLabel for="codInterf" value="#{localemsgs.CodInterf}"/>
                                <p:inputText id="codInterf" value="#{os_vig.novoTipoSrvCli.codInterf}" style="width: 100%"/>
                            </p:panelGrid>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-8" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="exportar" value="#{localemsgs.Texportar}"/>
                                <p:inputText id="exportar" value="#{os_vig.novoTipoSrvCli.exportar}" style="width: 100%" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.novoTipoSrvCli.exportar}"
                                                 required="true" label="#{localemsgs.Texportar}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Texportar}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{os_vig.texportar}" />
                                    <p:ajax event="itemSelect" update="exportar"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <div class="form-inline">
                                <p:commandLink rendered="#{os_vig.flagTipoSrvCli eq 1}" id="cadastro" action="#{os_vig.cadastrarCodgioServico}"
                                               update="msgs formCodigoServico:cadastrar formCadastrar:codSrv formCadastrar:codSrvList"
                                               title="#{localemsgs.Cadastrar}" >
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink rendered="#{os_vig.flagTipoSrvCli eq 2}" id="edit" action="#{os_vig.editarCodgioServico}"
                                               update=":msgs formCodigoServico:cadastrar formCadastrar:codSrv formCadastrar:codSrvList"
                                               title="#{localemsgs.Editar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formAgrupador">
                    <p:dialog widgetVar="dlgAgrupador" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              styleClass="dlgCadastrar" focus="descricao">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgAgrupador').closeIcon.unbind('click');

                                //register your own
                                PF('dlgAgrupador').closeIcon.click(function (e) {
                                    $("#formAgrupador\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>

                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgAgrupador').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarAgrupadorNF}" style="color:#022a48" rendered="#{os_vig.flagAgrupadorNF eq 1}"/>
                            <h:outputText value="#{localemsgs.AgrupadorNF} #{os_vig.novoAgrupador.codigo}" style="color:#022a48" rendered="#{os_vig.flagAgrupadorNF eq 2}"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent;" styleClass="cadastrar">

                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-1,ui-grid-col-2,ui-grid-col-2,ui-grid-col-1"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="codfil" value="#{localemsgs.CodFil}: "  />
                                <p:selectOneMenu id="codfil" value="#{os_vig.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 styleClass="filial" style="width: 100%" disabled="true"
                                                 filter="true" filterMatchMode="contains" >
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>

                                <p:outputLabel for="codigo" value="#{localemsgs.Codigo}"/>
                                <p:inputText id="codigo" value="#{os_vig.novoAgrupador.codigo}" style="width: 100%">
                                    <p:ajax event="blur" listener="#{os_vig.buscarAgrupadorNF}"
                                            update="msgs"/>
                                </p:inputText>

                                <p:outputLabel for="sitFiscal" value="#{localemsgs.SitFiscal}"/>
                                <p:inputText id="sitFiscal" value="#{os_vig.novoAgrupador.sitFiscal}" style="width: 100%" disabled="true"/>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}"/>
                                <p:inputText id="descricao" value="#{os_vig.novoAgrupador.descricao}" style="width: 100%"
                                             required="true" label="#{localemsgs.Descricao}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Descricao}"/>
                            </p:panelGrid>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-8"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="fatISSGrp" value="#{localemsgs.PerfilFiscal}:"/>
                                <p:inputText id="fatISSGrp" value="#{os_vig.novoAgrupador.agrupISS}" style="width: 100%;" disabled="true"/>
                                <p:autoComplete value="#{os_vig.fatISSGrp}" completeMethod="#{os_vig.listarFatISSGrp}"
                                                required="true" label="#{localemsgs.PerfilFiscal}" minQueryLength="3"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PerfilFiscal}"  scrollHeight="200"
                                                inputStyle="width: 100%" placeholder="#{localemsgs.PerfilFiscal}" forceSelection="true"
                                                style="min-width:100% !important" var="fig" itemLabel="#{fig.descricao}" itemValue="#{fig}">
                                    <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.fatISSGrpList}" />
                                    <p:ajax event="itemSelect" listener="#{os_vig.selecionarFatISSGrp}"
                                            update="msgs fatISSGrp municipio aliqISS"/>
                                </p:autoComplete>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="municipio" value="#{localemsgs.Municipio}"/>
                                <p:inputText id="municipio" value="#{os_vig.fatISSGrp.municipio}" style="width: 100%" disabled="true"/>

                                <p:outputLabel for="aliqISS" value="#{localemsgs.AliqISS}"/>
                                <p:inputText id="aliqISS" value="#{os_vig.fatISSGrp.aliqISS}" style="width: 100%" disabled="true"/>
                            </p:panelGrid>

                            <p:panel header="#{localemsgs.HistoricoNotaFiscal}">
                                <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-8"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="codHist" value="#{localemsgs.HistFixo}"/>
                                    <p:inputText id="codHist" value="#{os_vig.novoAgrupador.codHist}" style="width: 100%" disabled="true"/>
                                    <p:autoComplete value="#{os_vig.codHist}" completeMethod="#{os_vig.listarCodHist}"
                                                    required="true" label="#{localemsgs.HistFixo}" minQueryLength="3"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.HistFixo}"  scrollHeight="200"
                                                    inputStyle="width: 100%" placeholder="#{localemsgs.HistFixo}" forceSelection="true"
                                                    style="min-width:100% !important" var="ch" itemLabel="#{ch.descricao}" itemValue="#{ch}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.codHistList}" />
                                        <p:ajax event="itemSelect" listener="#{os_vig.selecionarCodHist}"
                                                update="msgs codHist"/>
                                    </p:autoComplete>

                                    <p:outputLabel for="codHistExt" value="#{localemsgs.HistVariavel}"/>
                                    <p:inputText id="codHistExt" value="#{os_vig.novoAgrupador.codHistExt}" style="width: 100%" disabled="true"/>
                                    <p:autoComplete value="#{os_vig.codHistExt}" completeMethod="#{os_vig.listarCodHistExt}"
                                                    required="true" label="#{localemsgs.HistVariavel}" minQueryLength="3"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.HistVariavel}"  scrollHeight="200"
                                                    inputStyle="width: 100%" placeholder="#{localemsgs.HistVariavel}" forceSelection="true"
                                                    style="min-width:100% !important" var="ch" itemLabel="#{ch.descricao}" itemValue="#{ch}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{os_vig.codHistExtList}" />
                                        <p:ajax event="itemSelect" listener="#{os_vig.selecionarCodHistExt}"
                                                update="msgs codHistExt"/>
                                    </p:autoComplete>

                                    <p:outputLabel for="cfop" value="#{localemsgs.CFOP}"/>
                                    <p:inputText id="cfop" value="#{os_vig.novoAgrupador.CFOP}" style="width: 100%" disabled="true"/>
                                    <p:selectOneMenu value="#{os_vig.novoAgrupador.CFOP}"
                                                     required="true" label="#{localemsgs.CFOP}" style="width: 100%"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CFOP}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                        <p:ajax event="itemSelect" update="cfop"/>
                                    </p:selectOneMenu>
                                </p:panelGrid>
                                <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-1"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="aliqISS2" value="#{localemsgs.ISS}"/>
                                    <p:inputText id="aliqISS2" value="#{os_vig.novoAgrupador.aliqISS}" style="width: 100%"/>

                                    <p:outputLabel for="ISSRet" value="#{localemsgs.Retido}"/>
                                    <p:selectBooleanCheckbox id="ISSRet" style="width: 100%;"/>
                                </p:panelGrid>
                            </p:panel>

                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-1,ui-grid-col-3,ui-grid-col-2,ui-grid-col-1,ui-grid-col-3"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="TCobran" value="#{localemsgs.TipoCobranca}"/>
                                <p:inputText id="TCobran" value="#{os_vig.novoAgrupador.TCobran}" style="width: 100%" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.novoAgrupador.TCobran}"
                                                 required="true" label="#{localemsgs.TCobran}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TCobran}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <p:ajax event="itemSelect" update="TCobran"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="agrupInterf" value="#{localemsgs.Interface}"/>
                                <p:inputText id="agrupInterf" value="#{os_vig.novoAgrupador.agrupInterf}" style="width: 100%" disabled="true"/>
                                <p:selectOneMenu value="#{os_vig.novoAgrupador.agrupInterf}"
                                                 required="true" label="#{localemsgs.Interface}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Interface}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <p:ajax event="itemSelect" update="agrupInterf"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="8" layout="grid" styleClass="ui-panelgrid-blank"
                                         columnClasses="ui-grid-col-2,ui-grid-col-1,ui-grid-col-2,ui-grid-col-1,
                                         ui-grid-col-2,ui-grid-col-1,ui-grid-col-2,ui-grid-col-1" >
                                <p:outputLabel for="aliqIRRF" value="#{localemsgs.IRRF}"/>
                                <p:inputText id="aliqIRRF" value="#{os_vig.novoAgrupador.aliqIRRF}" style="width: 100%"/>

                                <p:outputLabel for="aliqINSS" value="#{localemsgs.BaseINSS}"/>
                                <p:inputText id="aliqINSS" value="#{os_vig.novoAgrupador.aliqINSS}" style="width: 100%"/>

                                <p:outputLabel for="inss" value="#{localemsgs.INSS}"/>
                                <p:inputText id="inss" value="#{os_vig.novoAgrupador.aliqINSS}" style="width: 100%"/>

                                <p:outputLabel for="INSSRet" value="#{localemsgs.Retido}" style="width: 100%; text-align: right;"/>
                                <p:selectBooleanCheckbox id="INSSRet" style="width: 100%;"/>

                                <p:outputLabel for="aliqPIS" value="#{localemsgs.PIS}"/>
                                <p:inputText id="aliqPIS" value="#{os_vig.novoAgrupador.aliqPIS}" style="width: 100%"/>

                                <p:outputLabel for="PISRet" value="#{localemsgs.Retido}" style="width: 100%; text-align: right;"/>
                                <p:selectBooleanCheckbox id="PISRet" style="width: 100%;"/>

                                <p:outputLabel for="aliqCOFINS" value="#{localemsgs.COFINS}"/>
                                <p:inputText id="aliqCOFINS" value="#{os_vig.novoAgrupador.aliqCOFINS}" style="width: 100%"/>

                                <p:outputLabel for="COFINSRet" value="#{localemsgs.Retido}" style="width: 100%; text-align: right;"/>
                                <p:selectBooleanCheckbox id="COFINSRet" style="width: 100%;"/>

                                <p:outputLabel for="aliqCSL" value="#{localemsgs.CSL}"/>
                                <p:inputText id="aliqCSL" value="#{os_vig.novoAgrupador.aliqCSL}" style="width: 100%"/>

                                <p:outputLabel for="CSLRet" value="#{localemsgs.Retido}" style="width: 100%; text-align: right;"/>
                                <p:selectBooleanCheckbox id="CSLRet" style="width: 100%;"/>

                                <p:outputLabel for="aliqIRPJ" value="#{localemsgs.IRPJ}"/>
                                <p:inputText id="aliqIRPJ" value="#{os_vig.novoAgrupador.aliqIRPJ}" style="width: 100%"/>

                                <p:outputLabel for="IRPJRet" value="#{localemsgs.Retido}" style="width: 100%; text-align: right;"/>
                                <p:selectBooleanCheckbox id="IRPJRet" style="width: 100%;"/>
                            </p:panelGrid>

                            <p:panelGrid columns="6" layout="grid" styleClass="ui-panelgrid-blank"
                                         columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-1,ui-grid-col-3,ui-grid-col-2,ui-grid-col-2" >
                                <p:outputLabel for="repasse" value="#{localemsgs.Repasse}"/>
                                <p:inputText id="repasse" value="#{os_vig.novoAgrupador.repasse}" style="width: 100%"/>

                                <p:selectBooleanCheckbox id="incluirCustodia" style="width: 100%; text-align: right;"/>
                                <p:outputLabel for="incluirCustodia" value="#{localemsgs.IncluirCustodia}"/>


                                <p:outputLabel for="indRepasseCst" value="#{localemsgs.RepasseLiquido}"/>
                                <p:inputText id="indRepasseCst" value="#{os_vig.novoAgrupador.indRepasseCst}" style="width: 100%" disabled="true"/>
                            </p:panelGrid>

                            <div class="form-inline">
                                <p:commandLink rendered="#{os_vig.flagTipoSrvCli eq 1}" id="cadastro" action="#{os_vig.cadastrarCodgioServico}"
                                               update="msgs formCodigoServico:cadastrar formCadastrar:codSrv formCadastrar:codSrvList"
                                               title="#{localemsgs.Cadastrar}" >
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink rendered="#{os_vig.flagTipoSrvCli eq 2}" id="edit" action="#{os_vig.editarCodgioServico}"
                                               update=":msgs formCodigoServico:cadastrar formCadastrar:codSrv formCadastrar:codSrvList"
                                               title="#{localemsgs.Editar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisar contratos-->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>

                    <p:dialog widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                              focus="opcaoCliente"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarOS}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil" value="#{os_vig.os_vigSelecionado.codFil}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio id="radioOpcoes" value="#{os_vig.opcaoPesquisa}" unselectable="true" layout="pageDirection">
                                        <f:selectItem itemLabel="#{localemsgs.OS}" itemValue="OS" />
                                        <f:selectItem itemLabel="#{localemsgs.Cliente}" itemValue="Cliente" />
                                        <f:selectItem itemLabel="#{localemsgs.Destino}" itemValue="Destino" />
                                        <f:selectItem itemLabel="#{localemsgs.CliFat}" itemValue="CliFat" />
                                        <f:selectItem itemLabel="#{localemsgs.Agrupador}" itemValue="Agrupador" />
                                        <f:selectItem itemLabel="#{localemsgs.Agencia}" itemValue="Agencia" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel rendered="#{os_vig.opcaoPesquisa eq 'OS'}">
                                        <p:outputLabel for="opcaoOS" value="#{localemsgs.OS}: "/>
                                        <p:inputText
                                            id="opcaoOS"
                                            value="#{os_vig.os_vigSelecionado.OS}"
                                            label="#{localemsgs.OS}"
                                            style="width: 100%" maxlength="60">
                                            <p:watermark for="opcaoOS" value="#{localemsgs.OS}"/>
                                        </p:inputText>
                                    </p:outputPanel>

                                    <p:outputPanel rendered="#{os_vig.opcaoPesquisa eq 'Cliente'}">
                                        <p:outputLabel for="opcaoCliente" value="#{localemsgs.Cliente}: "/>
                                        <p:inputText
                                            id="opcaoCliente"
                                            value="#{os_vig.os_vigSelecionado.NRed}"
                                            label="#{localemsgs.Cliente}"
                                            style="width: 100%" maxlength="60">
                                            <p:watermark for="opcaoCliente" value="#{localemsgs.Cliente}"/>
                                        </p:inputText>
                                    </p:outputPanel>

                                    <p:outputPanel rendered="#{os_vig.opcaoPesquisa eq 'Destino'}">
                                        <p:outputLabel for="opcaoDestino" value="#{localemsgs.Destino}: "/>
                                        <p:inputText
                                            id="opcaoDestino"
                                            value="#{os_vig.os_vigSelecionado.NRedDst}"
                                            label="#{localemsgs.Destino}"
                                            style="width: 100%" maxlength="60">
                                            <p:watermark for="opcaoDestino" value="#{localemsgs.Destino}"/>
                                        </p:inputText>
                                    </p:outputPanel>

                                    <p:outputPanel rendered="#{os_vig.opcaoPesquisa eq 'CliFat'}">
                                        <p:outputLabel for="opcaoCliFat" value="#{localemsgs.CliFat}: "/>
                                        <p:inputText
                                            id="opcaoCliFat"
                                            value="#{os_vig.os_vigSelecionado.cliFat}"
                                            label="#{localemsgs.CliFat}"
                                            style="width: 100%" maxlength="60">
                                            <p:watermark for="opcaoCliFat" value="#{localemsgs.CliFat}"/>
                                        </p:inputText>
                                    </p:outputPanel>

                                    <p:outputPanel rendered="#{os_vig.opcaoPesquisa eq 'Agrupador'}">
                                        <p:outputLabel for="opcaoAgrupador" value="#{localemsgs.Agrupador}: "/>
                                        <p:inputText
                                            id="opcaoAgrupador"
                                            value="#{os_vig.os_vigSelecionado.agrupador}"
                                            label="#{localemsgs.Agrupador}"
                                            style="width: 100%" maxlength="60">
                                            <p:watermark for="opcaoAgrupador" value="#{localemsgs.Agrupador}"/>
                                        </p:inputText>
                                    </p:outputPanel>

                                    <p:outputPanel rendered="#{os_vig.opcaoPesquisa eq 'Agencia'}">
                                        <p:outputLabel for="opcaoAgencia" value="#{localemsgs.Agencia}: "/>
                                        <p:inputText
                                            id="opcaoAgencia"
                                            value="#{os_vig.os_vigSelecionado.agencia}"
                                            label="#{localemsgs.Agencia}"
                                            style="width: 100%" maxlength="60">
                                            <p:watermark for="opcaoAgencia" value="#{localemsgs.Agencia}"/>
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="form-inline" style="text-align: right">
                                <p:commandLink id="pesquisaOsOPcoes" action="#{os_vig.pesquisar}" oncomplete="PF('dlgPesquisar').hide()"
                                               update="msgs main:tabela cabecalho corporativo"
                                               title="#{localemsgs.Pesquisar}"  styleClass="btn btn-primary">
                                    <i class="fa fa-search" style="margin-right:8px !important"></i>#{localemsgs.Pesquisar}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <ui:include src="/faturamento/os_vig/frequencia_os.xhtml"/>
            </div>
            <script>
                $(document).on('keydown', 'div[id*="pesquisar"] [id*="opcao"]', function (e) {
                    if (e.keyCode == 13) {
                        $('[id$="pesquisaOsOPcoes"]').click();
                    }
                });
            </script>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <table style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:selectBooleanCheckbox itemLabel="#{localemsgs.Corporativo}" value="#{os_vig.mostraFiliais}">
                                            <p:ajax update="msgs main:tabela" listener="#{os_vig.mostrarFiliais}" />
                                        </p:selectBooleanCheckbox>
                                    </td>
                                    <td>
                                        <p:selectBooleanCheckbox itemLabel="#{localemsgs.SomenteAtivas}" value="#{os_vig.mostraAtivos}">
                                            <p:ajax update="msgs main:tabela" listener="#{os_vig.mostrarAtivos}" />
                                        </p:selectBooleanCheckbox>
                                    </td>
                                </tr>
                            </table>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class FuncionFaltas {
    private String Matr;
    private String Nome;
    private String Data;
    private String Posto;
    private String Local;
    private String MatrCober;
    private String NomeSubs;

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getPosto() {
        return Posto;
    }

    public void setPosto(String Posto) {
        this.Posto = Posto;
    }

    public String getLocal() {
        return Local;
    }

    public void setLocal(String Local) {
        this.Local = Local;
    }

    public String getMatrCober() {
        return MatrCober;
    }

    public void setMatrCober(String MatrCober) {
        this.MatrCober = MatrCober;
    }

    public String getNomeSubs() {
        return NomeSubs;
    }

    public void setNomeSubs(String NomeSubs) {
        this.NomeSubs = NomeSubs;
    }
    
    
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesSaidas;
import br.com.sasw.pacotesuteis.sasbeans.TesSaidasDD;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesSaidasDDDao {

    /**
     * Lista composicoes
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesSaidasDD> listaComposicoes(String guia, String serie, Persistencia persistencia) throws Exception {
        List<TesSaidasDD> composicaoDD = new ArrayList<>();
        try {
            String sql;
            sql = "Select codigo, qtde from TesSaidasDD"
                    + " Where Guia = ?"
                    + "  and Serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            TesSaidasDD tesentdd;
            while (consult.Proximo()) {
                tesentdd = new TesSaidasDD();
                tesentdd.setCodigo(consult.getString("codigo"));
                tesentdd.setQtde(consult.getString("qtde"));
                composicaoDD.add(tesentdd);
            }
            consult.Close();
            return composicaoDD;
        } catch (Exception e) {
            throw new Exception("TesSaidasDDDao.ListaComposicoes - " + e.getMessage() + "\r\n"
                    + "elect codigo, qtde from TesSaidasDD"
                    + " Where Guia = " + guia
                    + "  and Serie = " + serie);
        }
    }

    public List<TesSaidasDD> listaComposicoes(List<TesSaidas> tesSaidasList, String serie, Persistencia persistencia) throws Exception {
        try {
            List<TesSaidasDD> retorno = new ArrayList<>();
            String sql = " SELECT *, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC "
                    + " FROM TesSaidasDD "
                    + " WHERE guia in (";
            for (TesSaidas tesSaidas : tesSaidasList) {
                sql += " ?,";
            }
            sql = sql.substring(0, sql.length() - 1) + " ) AND serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            for (TesSaidas tesSaidas : tesSaidasList) {
                consulta.setString(tesSaidas.getGuia());
            }
            consulta.setString(serie);
            consulta.select();
            TesSaidasDD tesSaidasDD;
            while (consulta.Proximo()) {
                tesSaidasDD = new TesSaidasDD();
                tesSaidasDD.setGuia(consulta.getString("Guia"));
                tesSaidasDD.setSerie(consulta.getString("Serie"));
                tesSaidasDD.setCodigo(consulta.getString("Codigo"));
                tesSaidasDD.setDocto(consulta.getString("Docto"));
                tesSaidasDD.setQtde(consulta.getString("Qtde"));
                tesSaidasDD.setValor(consulta.getString("Valor"));
                tesSaidasDD.setOperador(consulta.getString("Operador"));
                tesSaidasDD.setDt_Alter(consulta.getString("Dt_AlterC"));
                tesSaidasDD.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesSaidasDD);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesSaidasDDDao.listaComposicoes - " + e.getMessage());
        }
    }

    /**
     * Insere uma nova entrada na tabela TesSaidasDD
     *
     * @param tesSaidasDD
     * @param persistencia
     * @throws Exception
     */
    public void inserirTesSaidasDD(TesSaidasDD tesSaidasDD, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO TesSaidasDD values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tesSaidasDD.getGuia());
            consulta.setString(tesSaidasDD.getSerie());
            consulta.setString(tesSaidasDD.getCodigo());
            consulta.setString(tesSaidasDD.getDocto());
            consulta.setString(tesSaidasDD.getQtde());
            consulta.setString(tesSaidasDD.getValor());
            consulta.setString(tesSaidasDD.getTipoCed());
            consulta.setString(tesSaidasDD.getOperador());
            consulta.setString(tesSaidasDD.getDt_Alter());
            consulta.setString(tesSaidasDD.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesSaidasDDDao.inserirTesSaidasDD - " + e.getMessage() + "\r\n"
                    + " INSERT INTO TesSaidasDD values (" + tesSaidasDD.getGuia() + "," + tesSaidasDD.getSerie() + ", \n"
                    + tesSaidasDD.getCodigo() + "," + tesSaidasDD.getDocto() + "," + tesSaidasDD.getQtde() + "," + tesSaidasDD.getValor() + ", \n"
                    + tesSaidasDD.getTipoCed() + "," + tesSaidasDD.getOperador() + "," + tesSaidasDD.getDt_Alter() + ",\n"
                    + tesSaidasDD.getHr_Alter() + ") ");
        }
    }

    public void excluirComposicao(String guia, String serie, String docto, String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "delete from TesSaidasDD "
                    + " where Guia = ?"
                    + " and Serie =  ?"
                    + " and codigo = ?"
                    + " and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codigo);
            consulta.setString(docto);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesSaidasDDDao.excluirComposicao - " + e.getMessage() + "\r\n"
                    + "delete from TesSaidasDD "
                    + " where Guia = " + guia
                    + " and Serie =  " + serie
                    + " and codigo = " + codigo
                    + " and docto = " + docto);
        }
    }
}

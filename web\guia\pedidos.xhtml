<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet"  media="print"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">

            <script type="text/javascript">
                function esc() {
                if (PF('dlgOk').isVisible()) {
                PF('dlgOk').hide();
                } else if (PF('dlgUpload').isVisible()) {
                PF('dlgUpload').hide();
                } else if (PF('dlgImprimir').isVisible()) {
                PF('dlgImprimir').hide();
                } else {
                window.history.back();
                }
                };

                function chartExtender() {
                Chart.defaults.global.tooltips.callbacks.afterLabel = function(tooltipItem, data) {
                return '(' + data['datasets'][0]['data'][tooltipItem['index']] + '%)';
                };          
                Chart.defaults.global.tooltips.callbacks.label = function(tooltipItem, data) {
                return '(' + data['datasets'][0]['data'][tooltipItem['index']] + '%)';
                };                    
                }
            </script>

            <h:form>
                <p:hotkey bind="left" handler="esc();" />
            </h:form>

            <f:metadata>
                <f:viewAction action="#{pedidos.Persistencias(login.pp)}" />
            </f:metadata>
            <p:growl id="msgs" />
            <header>
                <h:form id="cabecalho">
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row cabecalho">
                            <div class="ui-grid-col-5" style="align-self: center;">
                                <img src="../assets/img/icone_pedidos.png" height="40" width="40"/> 
                                #{localemsgs.Pedidos}
                            </div>

                            <div class="ui-grid-col-4" style="align-self: center; text-align: center;">
                                <h:outputText value="#{localemsgs.Periodo}: "/>
                                <h:outputText value="#{pedidos.data}" converter="conversorData" />
                            </div>

                            <div class="ui-grid-col-3" style="align-self: center; text-align: center;">
                                <p:commandLink action="#{pedidos.dataAnterior}"  update="main:tabela cabecalho">
                                    <p:graphicImage url="../assets/img/botao_anterior.png" style="align-self: center;height: 40px"/>  
                                </p:commandLink>

                                <p:commandLink id="calendar" oncomplete="PF('oCalendarios').loadContents();"
                                               styleClass="botao" update="main:tabela cabecalho">
                                    <p:graphicImage url="../assets/img/icone_escaladodia_40.png" style="align-self: center;height: 40px"/>  
                                </p:commandLink>

                                <p:commandLink action="#{pedidos.dataPosterior}" update="main:tabela msgs cabecalho">
                                    <p:graphicImage url="../assets/img/botao_proximo.png" style="align-self: center;height: 40px"/>  
                                </p:commandLink>
                            </div>
                        </div>

                        <div class="ui-grid-row">
                            <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                <div class="ui-grid-col-6" style="#{pedidos.codFil ne 0 ? 'display: block' : 'display: none'}">
                                    <h:outputText value="#{localemsgs.Filial}: " rendered="#{pedidos.codFil ne 0}"/>
                                    <h:outputText value="#{pedidos.codFil}" rendered="#{pedidos.codFil ne 0}">
                                        <f:convertNumber pattern="0000"/>
                                    </h:outputText>
                                    <h:outputText value=" - #{pedidos.nomeFilial}" rendered="#{pedidos.codFil ne 0}"/>
                                </div>
                                <div class="ui-grid-col-6" style="#{pedidos.nomecli ne null and guias.nomecli ne '' 
                                                                    ? 'display: block' : 'display: none'}">
                                    <h:outputText value="#{localemsgs.Cliente}: " rendered="#{pedidos.nomecli ne null and guias.nomecli ne ''}}"/>
                                    <h:outputText value="#{pedidos.nomecli}" style="font-weight: bold" rendered="#{pedidos.nomecli ne null and guias.nomecli ne ''}"/>
                                </div>

                            </p:panel>
                        </div>
                    </div>
                </h:form>
            </header>

            <h:form id="main">
                <div class="ui-grid ui-grid-responsive">
                    <div class="ui-grid-row">
                        <div class="ui-grid-col-12">
                            <p:panel style="display: inline">
                                <p:dataTable id="tabela" value="#{pedidos.allPedidos}" paginator="true" rows="50" lazy="true"
                                             rowsPerPageTemplate="5,10,15, 20, 25, 50, 100, 200, 1000"
                                             currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Pedidos}"
                                             paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                             var="preOrder"
                                             resizableColumns="true" styleClass="tabela" emptyMessage="#{localemsgs.SemRegistros}"
                                             selectionMode="single" selection="#{pedidos.preOrderSelecionado}"
                                             scrollable="true" scrollWidth="100%"
                                             style="font-size: 12px; background: white">    
                                    <p:column headerText="#{localemsgs.PreOrderVolOrdem}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.preOrderVolOrdem}" title="#{preOrder.preOrderVolOrdem}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Lacre}" style="width: 55px">
                                        <h:outputText value="#{preOrder.preOrderVolLacre}" title="#{preOrder.preOrderVolLacre}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Qtde}" style="width: 40px" class="celula-right" rendered="false">
                                        <h:outputText value="#{preOrder.preOrderVolQtde}" title="#{preOrder.preOrderVolQtde}" converter="conversor0"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.PreOrderVolTipo}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.preOrderVolTipo}" title="#{preOrder.preOrderVolTipo}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.PreOrderVolValor}" style="width: 80px" class="celula-right" rendered="false">
                                        <h:outputText value="#{preOrder.preOrderVolValor}" title="#{preOrder.preOrderVolValor}" converter="conversormoeda"/>
                                    </p:column>                                    
                                    <p:column headerText="#{localemsgs.Sequencia}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.sequencia}" title="#{preOrder.sequencia}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.CodFil}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.codFil}" title="#{preOrder.codFil}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Banco}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.banco}" title="#{preOrder.banco}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.DtColeta}" style="width: 75px">
                                        <h:outputText value="#{preOrder.dtColeta}" title="#{preOrder.dtColeta}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.CodCli1}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.codCli1}" title="#{preOrder.codCli1}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.SB}" style="width: 55px">
                                        <h:outputText value="#{preOrder.agenciaOri}/#{preOrder.subAgenciaOri}" 
                                                      title="#{preOrder.agenciaOri}/#{preOrder.subAgenciaOri}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.OrigemColeta}" style="width: 145px">
                                        <h:outputText value="#{preOrder.NRed1}" title="#{preOrder.NRed1}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.HrColeta}" style="width: 80px">
                                        <h:outputText value="#{preOrder.hora1O}" title="#{preOrder.hora1O}" converter="conversorHora"/>
                                        <h:outputText value=" - " rendered="#{preOrder.hora1O ne ''}"/>
                                        <h:outputText value="#{preOrder.hora2O}" title="#{preOrder.hora2O}" converter="conversorHora"/>
                                    </p:column>  
                                    <p:column headerText="#{localemsgs.CodCli2}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.codCli2}" title="#{preOrder.codCli2}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.SBDst}" style="width: 55px">
                                        <h:outputText value="#{preOrder.agencia}/#{preOrder.subAgencia}"
                                                      title="#{preOrder.agencia}/#{preOrder.subAgencia}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.DestinoEntrega}" style="width: 145px">
                                        <h:outputText value="#{preOrder.NRed2}" title="#{preOrder.NRed2}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.DtEntrega}" style="width: 80px">
                                        <h:outputText value="#{preOrder.dtEntrega}" title="#{preOrder.dtEntrega}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.HoraEntrega}" style="width: 80px">
                                        <h:outputText value="#{preOrder.hora1D}" title="#{preOrder.hora1D}" converter="conversorHora"/>
                                        <h:outputText value=" - " rendered="#{preOrder.hora1D ne ''}"/>
                                        <h:outputText value="#{preOrder.hora2D}" title="#{preOrder.hora2D}" converter="conversorHora"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Solicitante}" style="width: 80px">
                                        <h:outputText value="#{preOrder.solicitante}" title="#{preOrder.solicitante}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Remessa}" style="width: 80px">
                                        <h:outputText value="#{preOrder.pedidoCliente}" title="#{preOrder.pedidoCliente}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Pedido}" style="width: 80px">
                                        <h:outputText value="#{preOrder.pedido}" title="#{preOrder.pedido}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Valor}" style="width: 100px" class="celula-right">
                                        <h:outputText value="#{preOrder.valor}" title="#{preOrder.valor}" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Obs}" style="width: 300px">
                                        <h:outputText value="#{preOrder.obs}" title="#{preOrder.obs}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.RPV}" style="width: 90px">
                                        <h:outputText value="#{preOrder.RPV}" title="#{preOrder.RPV}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Guia}" style="width: 70px">
                                        <h:outputText value="#{preOrder.guia}" title="#{preOrder.guia}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Serie}" style="width: 40px">
                                        <h:outputText value="#{preOrder.serie}" title="#{preOrder.serie}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.ClassifSrv}" style="width: 80px">
                                        <h:outputText value="#{preOrder.classifSrv}" title="#{preOrder.classifSrv}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.OperIncl}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.operIncl}" title="#{preOrder.operIncl}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Dt_Incl}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.dt_Incl}" title="#{preOrder.dt_Incl}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Hr_Incl}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.hr_Incl}" title="#{preOrder.hr_Incl}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.OS}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.OS}" title="#{preOrder.OS}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.ChequesQtde}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.chequesQtde}" title="#{preOrder.chequesQtde}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.ChequesValor}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.chequesValor}" title="#{preOrder.chequesValor}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Operador}" style="width: 80px">
                                        <h:outputText value="#{preOrder.operador}" title="#{preOrder.operador}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 80px">
                                        <h:outputText value="#{preOrder.dt_Alter}" title="#{preOrder.dt_Alter}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 80px">
                                        <h:outputText value="#{preOrder.hr_Alter}" title="#{preOrder.hr_Alter}" converter="conversorHora"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.OperExcl}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.operExcl}" title="#{preOrder.operExcl}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Dt_Excl}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.dt_Excl}" title="#{preOrder.dt_Excl}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Hr_Excl}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.hr_Excl}" title="#{preOrder.hr_Excl}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Situacao}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.situacao}" title="#{preOrder.situacao}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Flag_Excl}" style="width: 80px" rendered="false">
                                        <h:outputText value="#{preOrder.flag_Excl}" title="#{preOrder.flag_Excl}"/>
                                    </p:column>
                                </p:dataTable>
                            </p:panel>
                        </div>
                    </div>
                </div>

                <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                    <div style="padding-bottom: 10px">
                        <p:commandLink title="#{localemsgs.Editar}" actionListener="#{pedidos.preparaEdicaoLacre}"
                                       update="msgs">
                            <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                        </p:commandLink>
                    </div>

                    <div style=" top: 0px; right: 5px; position: fixed">
                        <p:commandLink title="#{localemsgs.Voltar}" oncomplete="window.history.back();">
                            <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                        </p:commandLink>                        
                    </div>                                  
                    <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                </p:panel>
            </h:form>

            <h:form id="formEditarLacre" class="form-inline">
                <p:dialog widgetVar="dlgEditarLacre" id="dlgEditarLacre" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="background-image: url('assets/img/menu_fundo.png');
                          background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pedidos.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.EditarLacre}" style="color:black" />
                    </f:facet>
                    <p:panel id="editar" style="background-color: transparent" styleClass="editar">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="origem" value="#{localemsgs.Origem}: " />
                            <p:inputText id="origem" value="#{pedidos.preOrderSelecionado.agenciaOri}/#{pedidos.preOrderSelecionado.subAgenciaOri} #{pedidos.preOrderSelecionado.NRed1}"
                                         disabled="true" style="width: 100%; text-align: right" />
                            <p:outputLabel for="destino" value="#{localemsgs.Destino}: " />
                            <p:inputText id="destino" value="#{pedidos.preOrderSelecionado.agencia}/#{pedidos.preOrderSelecionado.subAgencia} #{pedidos.preOrderSelecionado.NRed2}"
                                         disabled="true" style="width: 100%; text-align: right; font-weight: bold" />
                            
                            <p:spacer/>
                            <p:inputText value="#{pedidos.preOrderSelecionado.obs}"
                                         disabled="true" style="width: 100%; text-align: right;" />
                            
                            <p:outputLabel for="lacre" value="#{localemsgs.Lacre}: " />
                            <p:inputText id="lacre" value="#{pedidos.preOrderSelecionado.preOrderVolLacre}"
                                         disabled="true" style="width: 100%; text-align: right" />

                            <p:outputLabel for="novoLacre" value="#{localemsgs.NovoLacre}: " />
                            <p:inputText id="novoLacre" value="#{pedidos.novoLacre}" style="width: 100%; text-align: right" />
                        </p:panelGrid>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:commandLink actionListener="#{pedidos.editarLacre}" 
                                           title="#{localemsgs.Editar}" update="msgs">     
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form class="form-inline" id="formUpload">    
                <p:dialog widgetVar="dlgUpload" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.UploadPedido}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelUpload" style="background-color: transparent" styleClass="cadastrar">

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9 origem" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="origem" value="#{localemsgs.OrigemPedido}: "/>
                            <p:inputText id="origem" value="#{pedidos.nomecli}" style="width: 100%" readonly="true"/>
                        </p:panelGrid>

                        <p:fileUpload id="espacoUpload" auto="true" skinSimple="true" label="#{localemsgs.SelecioneArquivo}"
                                      update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs formPreOrders" 
                                      onstart="PF('pfBlock').show();" oncomplete="PF('dlgUpload').initPosition();PF('pfBlock').hide();"
                                      class="upload" multiple="true" dragDropSupport="true"
                                      fileUploadListener="#{pedidos.realizarUpload}" mode="advanced" 
                                      allowTypes="/(\.|\/)(txt)$/" previewWidth="10" invalidFileMessage="#{localemsgs.ArquivoInvalido}">
                        </p:fileUpload>

                        <p:dataTable value="#{pedidos.pedidos}" scrollHeight="120" scrollWidth="100%" 
                                     scrollable="true" style="background: transparent;" rendered="#{!guias.pedidos.isEmpty()}"
                                     var="listaDocumentos" id="arquivos" styleClass="tabelaArquivos">
                            <f:facet name="header">
                                <h:outputText value="#{localemsgs.ArquivosRecentes}:"/>
                            </f:facet>
                            <p:column headerText="#{localemsgs.Nome}" style="width: 80%">
                                <h:outputText value="#{listaDocumentos.name}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Tamanho}" style="width: 20%; text-align: center">
                                <h:outputText value="#{listaDocumentos.length()}" converter="conversorKB"/>
                            </p:column>
                        </p:dataTable>

                        <p:dataTable value="#{pedidos.pedidosRecentes}" scrollHeight="120" 
                                     scrollable="true" style="background: transparent;" resizableColumns="true"
                                     var="listaPedidos" id="listaPedidos" styleClass="tabela">
                            <f:facet name="header">
                                <h:outputText value="#{localemsgs.PedidosRecentes}:"/>
                            </f:facet>
                            <p:column headerText="#{localemsgs.Agencia}" style="width: 50px">
                                <h:outputText value="#{listaPedidos.agencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SubAgencia}" style="width: 92px">
                                <h:outputText value="#{listaPedidos.subAgencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Pedido}" style="width: 92px">
                                <h:outputText value="#{listaPedidos.pedidoCliente}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtColeta}" style="width: 82px">
                                <h:outputText value="#{listaPedidos.dtColeta}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtEntrega}" style="width: 82px">
                                <h:outputText value="#{listaPedidos.dtEntrega}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora}" style="width: 140px">
                                <h:outputText value="#{listaPedidos.hora1D}" converter="conversorHora"/>
                                -
                                <h:outputText value="#{listaPedidos.hora2D}" converter="conversorHora"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Valor}" style="width: 135px;" styleClass="celula-right">
                                <h:outputText value="#{listaPedidos.valor}" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Obs}" style="width: 300px">
                                <h:outputText value="#{listaPedidos.obs}"/>
                            </p:column>
                        </p:dataTable>

                        <p:commandLink oncomplete="PF('dlgUpload').hide();"
                                       title="#{localemsgs.Enviar}">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                        </p:commandLink>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="formPreOrders">
                <p:dialog widgetVar="dlgPreOrders" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.UploadPedido}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelPreOrders" style="background-color: transparent" styleClass="cadastrar">

                        <h:outputText value="#{pedidos.mensagemImportacao}"/>

                        <p:dataTable value="#{pedidos.listaAgencias}" scrollHeight="400" 
                                     scrollable="true" style="background: transparent;" resizableColumns="true"
                                     var="listaPedidos" id="listaPedidos" styleClass="tabela">
                            <p:column style="width:16px">
                                <p:rowToggler />
                            </p:column>
                            <p:column headerText="#{localemsgs.Agencia}">
                                <h:outputText value="#{listaPedidos.agencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SubAgencia}">
                                <h:outputText value="#{listaPedidos.subAgencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtColeta}">
                                <h:outputText value="#{listaPedidos.dtColeta}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtEntrega}">
                                <h:outputText value="#{listaPedidos.dtEntrega}" converter="conversorData"/>
                            </p:column>

                            <p:rowExpansion>
                                <p:dataTable value="#{listaPedidos.listaMalotes}"
                                             style="background: transparent; width: 95%" resizableColumns="true"
                                             var="listaLacres" id="listaLacres" styleClass="tabela">
                                    <p:column headerText="#{localemsgs.Pedido}" style="width: 75px">
                                        <h:outputText value="#{listaLacres.pedidoCliente}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Lacre}" style="width: 75px">
                                        <h:outputText value="#{listaLacres.lacre}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Horario}" style="width: 70px">
                                        <h:outputText value="#{listaLacres.horario}" converter="conversorHora"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Valor}" style="width: 130px">
                                        <h:outputText value="#{listaLacres.valor}" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Obs}" style="white-space: normal">
                                        <h:outputText value="#{listaLacres.obs}"/>
                                    </p:column>
                                </p:dataTable>
                            </p:rowExpansion>
                        </p:dataTable>

                        <p:commandLink actionListener="#{pedidos.verificarExistenciaPreOrder}" 
                                       update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs panelConfirmacaoPreOrders"
                                       title="#{localemsgs.Prosseguir}">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                        </p:commandLink>
                        <p:commandLink oncomplete="PF('dlgPreOrders').hide();"
                                       title="#{localemsgs.Cancelar}">
                            <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                        </p:commandLink>
                    </p:panel>
                </p:dialog>

                <p:dialog positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;"
                          header="#{localemsgs.Opcoes}" widgetVar="dlgConfirmacao">
                    <f:facet name="header">
                        <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.UploadPedido}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelConfirmacaoPreOrders" style="background-color: transparent" styleClass="cadastrar">

                        <div class="form-inline">
                            <h:outputText value="#{pedidos.mensagemImportacao}" style="text-align: center"/>
                            <p:spacer height="20px"/>
                        </div>

                        <p:commandLink actionListener="#{pedidos.inserirPreOrders}" 
                                       update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs"
                                       title="#{localemsgs.Prosseguir}">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                        </p:commandLink>
                        <p:commandLink oncomplete="PF('dlgConfirmacao').hide();PF('dlgPreOrders').hide();"
                                       title="#{localemsgs.Cancelar}">
                            <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                        </p:commandLink>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form class="form-inline" id="formManifestosDisponiveis">    
                <p:dialog widgetVar="dlgManifestosDisponiveis" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.ManifestosDisponiveis}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelManifestosDisponiveis" style="background-color: transparent" styleClass="cadastrar">

                        <p:dataTable value="#{pedidos.pedidosRecentes}" scrollHeight="120"  selectionMode="single" 
                                     selection="#{pedidos.preOrderSelecionado}" rowKey="#{listaPedidos.dtColeta}#{listaPedidos.hora2D}"
                                     scrollable="true" style="background: transparent;" resizableColumns="true"
                                     var="listaPedidos" id="listaPedidos" styleClass="tabela">

                            <p:ajax event="rowDblselect" listener="#{pedidos.listarManifestoPreOrder}" update="impressao msgs"/>
                            <p:column headerText="#{localemsgs.CodFil}" style="width: 50px">
                                <h:outputText value="#{listaPedidos.codFil}" converter="conversorCodFil"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.DtColeta}" style="width: 82px">
                                <h:outputText value="#{listaPedidos.dtColeta}" converter="conversorData"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Hora}" style="width: 65px">
                                <h:outputText value="#{listaPedidos.hora2D}" converter="conversorHora"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.QtdGuiasPreOrder}" style="width: 75px">
                                <h:outputText value="#{listaPedidos.RPV}"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Valor}" style="width: 140px">
                                <h:outputText value="#{listaPedidos.valor}" converter="conversormoeda"/>
                            </p:column>
                        </p:dataTable>

                        <p:commandLink title="#{localemsgs.ListarManifestos}" action="#{pedidos.listarManifestoPreOrder}"
                                       update="impressao msgs">
                            <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="40"/>
                        </p:commandLink>
                    </p:panel>
                </p:dialog>
            </h:form>

            <p:overlayPanel id="calendarios" for="cabecalho:calendar" hideEffect="fade" dynamic="true" dismissable="false"
                            style="font-size: 14px;" widgetVar="oCalendarios" my="top" at="bottom" class="overlay">
                <h:form id="panelCals">
                    <div class="ui-grid-row ui-grid-responsive">
                        <div class="ui-grid-col-12">
                            <div class="ui-grid-row">
                                <h:outputText id="cal1" value="#{localemsgs.Data}:" title="#{localemsgs.Data}"/>
                            </div>
                            <div class="ui-grid-row">
                                <p:calendar id="calendario1" styleClass="calendario"
                                            value="#{pedidos.dataSelecionada}" mask="true"
                                            title="#{localemsgs.Data}" label="#{localemsgs.Data}"
                                            pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: right; float: right">
                        <p:commandLink action="#{pedidos.selecionarData}" style="float: right">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" height="40" />
                        </p:commandLink>
                    </div>
                </h:form>
            </p:overlayPanel>

            <p:dialog positionType="absolute" responsive="true"
                      draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                      style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;"
                      header="#{localemsgs.Opcoes}" widgetVar="dlgOk">
                <h:form>
                    <div class="form-inline">
                        <h:outputText value="#{localemsgs.EscolherOpcao}:" style="text-align: center"/>
                    </div>
                    <p:spacer height="30px"/>
                    <div class="form-inline">
                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px"
                                     rendered="#{login.clientes.size() gt 1}">
                            <p:commandLink oncomplete="PF('dlgSelecionarCliente').show()">
                                <p:graphicImage url="../assets/img/icone_satmob_clientes.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink oncomplete="PF('dlgSelecionarCliente').show()">
                                <h:outputText  value="#{localemsgs.TrocarCliente}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px"
                                     rendered="#{login.permissaoControleAcessos}">
                            <p:commandLink action="/configuracoes/acessos.xhtml?faces-redirect=true">
                                <p:graphicImage url="../assets/img/icone_usuarios.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink action="/configuracoes/acessos.xhtml?faces-redirect=true">
                                <h:outputText value="#{localemsgs.Usuarios}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px">
                            <p:commandLink oncomplete="PF('dlgTrocarSenha').show()">
                                <p:graphicImage url="../assets/img/icone_configuracoes.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink oncomplete="PF('dlgTrocarSenha').show()" >
                                <h:outputText   value="#{localemsgs.TrocarSenha}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px">
                            <p:commandLink action="#{login.logOutRH}">
                                <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink action="#{login.logOutRH}">
                                <h:outputText value="#{localemsgs.Sair}"/>
                            </p:commandLink> 
                        </h:panelGrid>
                    </div>

                </h:form>
            </p:dialog>

            <h:form id="cliente">
                <p:dialog widgetVar="dlgSelecionarCliente" positionType="absolute" id="dlgClientes"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                          background-size: 750px 430px; left:200px">  
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panel id="tabelaClientes" style="width: 440px; background: transparent">
                        <div class="form-inline">
                            <p:dataTable id="tabela" value="#{login.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                         var="cli" resizableColumns="true" selection="#{login.selecionado}" rowKey="#{cli.codCli}"
                                         scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabela"
                                         style="font-size: 12px; float: left" styleClass="tabela" filteredValue="#{login.clientesFiltrados}">
                                <f:facet name="footer">
                                    <div class="ui-grid-row ui-grid-responsive">
                                        <div class="ui-grid-col-6" style="color:black; text-align: left; font-weight: normal">
                                            <h:outputText value="#{localemsgs.VerTodos}: "/>
                                            <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                        </div>
                                        <div class="ui-grid-col-6" style="color:black; text-align: right; font-weight: normal">
                                            <h:outputText value="#{localemsgs.Buscar}: " />
                                            <p:inputText id="globalFilter" onkeypress="if (event.keyCode == 13) { PF('tabela').filter(); return false; }"
                                                         style="width:150px;"/>
                                        </div>
                                    </div>
                                </f:facet>
                                <p:ajax event="rowDblselect" listener="#{login.dblSelectGTV}" update="msgs"/>
                                <p:column rendered="#{!login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.Empresa}" style="width: 145px" filterBy="#{cli.operador}"
                                          filterMatchMode="contains">
                                    <h:outputText value="#{cli.operador}" title="#{cli.operador}">
                                    </h:outputText>
                                </p:column>
                                <p:column rendered="#{login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.Pref}" style="width: 60px" filterBy="#{cli.agencia}"
                                          filterMatchMode="contains">
                                    <h:outputText value="#{cli.agencia}" title="#{cli.agencia}">
                                    </h:outputText>
                                </p:column>
                                <p:column rendered="#{login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.NSOP}" style="width: 60px" filterBy="#{cli.subAgencia}"
                                          filterMatchMode="contains">
                                    <h:outputText value="#{cli.subAgencia}" title="#{cli.subAgencia}">
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{login.pp.empresa.contains('CONFEDERAL') ? localemsgs.Agencia: localemsgs.Cliente}" filterBy="#{cli.nomeCli}"
                                          filterMatchMode="contains">
                                    <h:outputText value="#{cli.nomeCli}" title="#{cli.nomeCli}"/>
                                </p:column>
                            </p:dataTable>
                        </div>
                        <div class="form-inline">
                            <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteGTV}" 
                                           title="#{localemsgs.Selecionar}">
                                <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="trocarsenha">
                <p:dialog widgetVar="dlgTrocarSenha" positionType="absolute" id="dlgTrocarSenha"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                          background-size: 750px 430px; left:200px">  
                    <f:facet name="header">
                        <img src="../assets/img/icone_configuracoes.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panel id="panelSenha" style="width: 440px; background: transparent">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:outputLabel for="atual" value="#{localemsgs.SenhaAtual}" />
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:password id="atual" value="#{pedidos.senhaAtual}" label="#{localemsgs.SenhaAtual}" required="true" 
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.SenhaAtual}"/>
                                </div>
                            </div>
                        </div>

                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:outputLabel for="nova1" value="#{localemsgs.NovaSenha}" />
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:password id="nova1" value="#{pedidos.senhaNova}" match="nova2"
                                                label="#{localemsgs.NovaSenha}" required="true" feedback="true"
                                                promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                                goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NovaSenha}">
                                        <f:validateRegex pattern="^[0-9]{5,20}$" for="nova1"/>
                                    </p:password>
                                </div>
                            </div>
                        </div>

                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:outputLabel for="nova2" value="#{localemsgs.confirmarNovaSenha}" />
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:password id="nova2" value="#{pedidos.senhaNova}" label="#{localemsgs.SenhaAtual}" required="true" 
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Confirmacao}"/>
                                </div>
                            </div>
                        </div>

                        <div class="form-inline">
                            <p:commandLink id="btnSelecionar" action="#{pedidos.TrocarSenha}" 
                                           title="#{localemsgs.Concluido}" update="msgs">
                                <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="impressao">
                <p:dialog widgetVar="dlgImprimir" positionType="absolute" id="dlgImprimir" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                          background-size: 750px 430px; left:200px">
                    <f:facet name="header">
                        <div class="ui-grid-col-8">
                            <img src="../assets/img/icone_satmob_guias_40x40.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Imprimir}"/>
                        </div>
                        <div class="ui-grid-col-2" style="text-align: right">
                            <p:commandLink title="#{localemsgs.Imprimir}">
                                <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="40"/>
                                <p:printer target="guiaimpressa"/>
                            </p:commandLink>
                        </div>
                        <div class="ui-grid-col-2" style="text-align: right">
                            <p:commandLink title="#{localemsgs.Download}" update="msgs"
                                           ajax="false"
                                           actionListener="#{pedidos.gerarGuiaDownload}">
                                <p:graphicImage url="../assets/img/icone_pdf.png" height="40"/>
                                <p:fileDownload value="#{pedidos.arquivoDownload}"/>
                            </p:commandLink>
                        </div>
                    </f:facet>

                    <p:panel id="guiaimpressa" class="guiaimpressa" styleClass="guiaimpressa" style="height: 75vh; background: transparent;">

                        <h:outputText value="#{pedidos.html}" escape="false"/>
                        <!--                        <ui:include src="guia.xhtml"/>-->
                    </p:panel>
                </p:dialog>
            </h:form>


            <h:form id="pedido" class="form-inline">
                <p:dialog widgetVar="dlgPedido" id="telaPedido" positionType="absolute" responsive="true"
                          draggable="false" styleClass="dialogo" modal="true" closable="true" resizable="false"
                          dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="true">
                    <script>
                        $(document).ready(function () {
                        //first unbind the original click event
                        PF('dlgPedido').closeIcon.unbind('click');

                        //register your own
                        PF('dlgPedido').closeIcon.click(function (e) {
                        $("#pedido\\:botaoFechar").click();
                        //should be always called
                        e.preventDefault();
                        });
                        })
                    </script>
                    <p:commandButton widgetVar="botaoFechar" style="display: none"
                                     oncomplete="PF('dlgPedido').hide()" id="botaoFechar">
                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert"/> 
                    </p:commandButton>
                    <f:facet name="header">
                        <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Pedido}" style="color:#022a48"/>
                    </f:facet>
                    <p:panel id="cadastrar" style="background-color: transparent" class="cadastrar">
                        <p:confirmDialog global="true">
                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                        </p:confirmDialog>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="solicitante" value="#{localemsgs.Solicitante}:"/>
                            <p:inputText id="solicitante" value="#{pedidos.pedido.solicitante}"   style="width: 100%">
                                <p:watermark for="solicitante" value="#{localemsgs.Solicitante}"/>
                            </p:inputText>

                            <p:outputLabel for="tipoServico" value="#{localemsgs.Servico}:"/>
                            <p:selectOneRadio id="tipoServico" value="#{pedidos.tipoPedido}" style="width: 100%">
                                <f:selectItem itemLabel="#{localemsgs.Recolhimento}"  itemValue="recolhimento"/>
                                <f:selectItem itemLabel="#{localemsgs.Suprimento}"  itemValue="suprimento"/>
                                <p:ajax event="change" listener="#{pedidos.tipoServico}" update="pedido:cadastrar"/>
                            </p:selectOneRadio>

                            <p:outputLabel for="origem" value="#{localemsgs.Origem}:" rendered="#{pedidos.tipoPedido eq 'recolhimento'}"/>
                            <p:outputLabel for="origem" value="#{localemsgs.Destino}:" rendered="#{pedidos.tipoPedido eq 'suprimento'}"/>
                            <p:autoComplete id="origem" value="#{pedidos.os_vig}" completeMethod="#{pedidos.buscarPedidos}" 
                                            styleClass="origem" style="width: 100%" label="#{localemsgs.Origem}" disabled="#{pedidos.tipoPedido eq 'suprimento'}" forceSelection="true"
                                            minQueryLength="4" scrollHeight="200" var="cont" itemValue="#{cont}" itemLabel="#{cont.NRed}">
                                <p:column style="width: 50%">
                                    #{cont.NRed}
                                </p:column>
                                <p:column>
                                    #{cont.NRedDst}
                                </p:column>
                                <p:watermark for="origem" value="#{localemsgs.Origem}"/>
                                <p:ajax event="itemSelect" listener="#{pedidos.selecionarDest}" update="pedido:origem pedido:destino"/>
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{pedidos.listaOsVig}" />
                            </p:autoComplete>

                            <p:outputLabel for="destino" value="#{localemsgs.Destino}:" rendered="#{pedidos.tipoPedido eq 'recolhimento'}"/>
                            <p:outputLabel for="destino" value="#{localemsgs.Origem}:" rendered="#{pedidos.tipoPedido eq 'suprimento'}"/>
                            <p:autoComplete id="destino" value="#{pedidos.os_vig}" completeMethod="#{pedidos.buscarPedidos}" 
                                            minQueryLength="4" styleClass="origem" style="width: 100%" label="#{localemsgs.Origem}" disabled="#{pedidos.tipoPedido eq 'recolhimento'}" forceSelection="true"
                                            scrollHeight="200" var="cont" itemValue="#{cont}" itemLabel="#{cont.NRedDst}">
                                <p:column style="width: 50%">
                                    #{cont.NRedDst}
                                </p:column>
                                <p:column>
                                    #{cont.NRed}
                                </p:column>
                                <p:watermark for="destino" value="#{localemsgs.Destino}"/>
                                <p:ajax event="itemSelect" listener="#{pedidos.selecionarDest}" update="pedido:origem pedido:destino"/>
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{pedidos.listaOsVig}" />
                            </p:autoComplete>
                        </p:panelGrid>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="valor" value="#{localemsgs.Valor}:"/>
                            <p:inputText id="valor" value="#{pedidos.valor}"  style="width: 100%">
                                <p:watermark for="valor" value="#{localemsgs.Valor}"/>
                            </p:inputText>
                            <p:outputLabel for="pedidoCliente" value="#{localemsgs.Pedido} #{localemsgs.Cliente}:"/>
                            <p:inputText id="pedidoCliente" value="#{pedidos.pedido.pedidoCliente}"  style="width: 100%">
                                <p:watermark for="pedidoCliente" value="#{localemsgs.Pedido} #{localemsgs.Cliente}"/>
                            </p:inputText>
                        </p:panelGrid>
                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3, ui-grid-col-3, ui-grid-col-3, ui-grid-col-3"
                                     layout="grid">
                            <p:outputLabel for="data" value="#{localemsgs.Data}:"/>
                            <p:inputMask mask="99/99/9999" disabled="true" value="#{pedidos.data}"  converter="conversorData"   id="data" style="width: 100%">
                                <p:watermark for="data" value="#{localemsgs.Data}"/>
                            </p:inputMask>

                            <p:outputLabel for="dataEntrega" value="#{localemsgs.DataEntrega}:"/>
                            <p:calendar value="#{pedidos.datad}" converter="conversorData" id="dataEntrega" pattern="dd/MM/yyyy" class="calendariopedido">
                                <p:watermark for="dataEntrega" value="#{localemsgs.DataEntrega}"/>
                            </p:calendar>

                            <p:outputLabel for="hora" value="#{localemsgs.Hora}:"/>
                            <p:inputMask mask="99:99" value="#{pedidos.hora1o}" id="hora" maxlength="4" style="width: 100%">
                                <p:watermark for="hora" value="#{localemsgs.Hora}"/>
                            </p:inputMask>

                            <p:outputLabel for="horaEntrega" value="#{localemsgs.HoraEntrega}:"/>
                            <p:inputMask mask="99:99" value="#{pedidos.hora1d}" id="horaEntrega" maxlength="4" style="width: 100%">
                                <p:watermark for="horaEntrega" value="#{localemsgs.HoraEntrega}"/>
                            </p:inputMask>
                        </p:panelGrid>
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3, ui-grid-col-9" layout="grid">
                            <p:outputLabel for="obs" value="#{localemsgs.Obs}:"/>
                            <p:inputTextarea id="obs" value="#{pedidos.pedido.obs}" label="#{localemsgs.Obs}" style="width: 100%" rows="3" maxlength="80"/>
                            <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                        </p:panelGrid>  

                        <div class="form-inline">
                            <p:commandLink id="cadastro" ajax="false" action="#{pedidos.cadastrarPedido}" title="#{localemsgs.Cadastrar}" oncomplete="PF('dlgPedido').hide()">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>

                    </p:panel>
                </p:dialog>
            </h:form>


            <!--Pesquisar guias -->
            <h:form id="formPesquisar">
                <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/> 
                <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                           draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                           showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                           style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">

                    <f:facet name="header">
                        <img src="../assets/img/icone_clientes.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Pesquisar}" style="color:#022a48" /> 

                    </f:facet>
                    <p:panel id="pesquisar" style="background: transparent">
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 25%; float: left">
                                <p:outputLabel for="guia" value="#{localemsgs.Guia}"/>
                            </div>
                            <div style="width: 75%; float: left">
                                <p:inputText id="guia" value="#{pedidos.guiaPesquisa.guia}" label="#{localemsgs.Guia}"
                                             style="width: 100%" maxlength="60">
                                    <p:watermark for="guia" value="#{localemsgs.Guia}"/>
                                </p:inputText>
                            </div>
                        </div>

                        <div class="form-inline">
                            <p:commandLink id="pesquisa" action="#{pedidos.pesquisar}" oncomplete="PF('dlgPesquisar').hide()"
                                           update=" :main:tabela :msgs :cabecalho:status"
                                           title="#{localemsgs.Pesquisar}">
                                <p:hotkey bind="1" oncomplete="PF('dlgPesquisar').hide()"/> 
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>            
                    </p:panel>
                </p:dialog>
            </h:form>


            <p:ajaxStatus onstart="PF('pfBlock').show();" oncomplete="PF('pfBlock').hide();">
                <p:dialog  widgetVar="pfBlock" header="#{localemsgs.ProcessandoAguarde}" modal="true"
                           draggable="false" closable="false" resizable="false">  
                    <p:progressBar widgetVar="progressoIndeterminado" id="progressoIndeterminado"
                                   styleClass="progresso" mode="indeterminate"/>
                </p:dialog>
            </p:ajaxStatus>

            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>
                <div class="footer-body" id="footer-body">
                    <div>
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.LimparFiltros}: " />
                            <p:selectBooleanCheckbox value="#{pedidos.limpar}">
                                <p:ajax update="msgs main:tabela cabecalho:status corporativo" listener="#{pedidos.limparFiltros}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td rowspan="2"><img src="#{login.getLogo(login.empresa.getBancoDados())}" height="47px" width="59px"/></td>
                                </tr>
                                <tr>
                                    <td><h:outputText value="#{localemsgs.Agencia}: #{pedidos.nomecli} - #{pedidos.agencia}/#{pedidos.subagencia}"
                                                      rendered="#{pedidos.nomecli ne ''}"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                e.preventDefault();
                $("footer").toggleClass("toggled");
                $(".footer-toggler").toggleClass("toggled");
                $(".status").toggleClass("toggled");
                $("#body").toggleClass("toggled");
                $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>
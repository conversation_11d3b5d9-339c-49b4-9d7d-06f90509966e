<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/cofres.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{cofre.Persistencia(login.pp, login.satellite)}"/>
            </f:metadata>
            <p:growl id="msgs"/>
            
            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-5" style="align-self: center;">
                                    <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/> 
                                    #{localemsgs.CofreInteligente}
                                </div>

                                <div class="ui-grid-col-4" style="align-self: center; text-align: center;">
                                    <h:outputText value="#{localemsgs.Data}: "/>
                                    <h:outputText id="diaCofre" value="#{cofre.dataCofreStr}" converter="conversorDia"/>
                                </div>

                                <div class="ui-grid-col-3" style="align-self: center; text-align: center;">
                                    <p:commandLink action="#{cofre.DataAnteriorCofre}" update="main:tabela cabecalho totais">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="align-self: center;height: 40px"/>
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true" 
                                                pattern="yyyy-MM-dd" maxdate="#{cofre.dataCofre}" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{cofre.SelecionarData}" update="main:tabela cabecalho totais" />
                                    </p:calendar>

                                    <p:commandLink action="#{cofre.DataPosteriorCofre}" update="main:tabela cabecalho totais movimentos">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="align-self: center;height: 40px"/>
                                    </p:commandLink>
                                </div> 
                            </div>

                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-12">
                                        <div class="ui-grid-col-3">
                                        <h:outputText value="#{localemsgs.Filial}: " rendered="#{cofre.codFil ne 0}"/>
                                        <h:outputText value="#{cofre.codFil}" rendered="#{cofre.codFil ne 0}">
                                            <f:convertNumber pattern="0000"/>
                                        </h:outputText>
                                        <h:outputText value=" - #{cofre.nomeFilial}" rendered="#{cofre.codFil ne 0}"/>
                                    </div>
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>
                <h:form id="main">
                    <p:hotkey bind="e" update="movimentos msgs" actionListener="#{cofre.buttonAction}"/> 
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela" value="#{cofre.allCofres}" paginator="true" rows="15" lazy="true"
                                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords}
                                                 #{login.clientes.size() eq 1 ? localemsgs.Cofres : localemsgs.Cofre}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="cofresclientes"
                                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                                 selection="#{cofre.cofreSelecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true" scrollWidth="100%"
                                                 style="font-size: 12px; background: white">
                                        <p:ajax event="rowDblselect" listener="#{cofre.DblSelect}" update="movimentos"/>
                                        <p:column headerText="#{localemsgs.Cofre}" style="width: 40px">
                                            <h:outputText value="#{cofresclientes.clientes.codCofre}" title="#{cofresclientes.tescofresres.codCofre}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NRed}" style="width: 200px">
                                            <h:outputText value="#{cofresclientes.clientes.NRed}" title="#{cofresclientes.clientes.NRed}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Endereco}" style="width: 200px">
                                            <h:outputText value="#{cofresclientes.clientes.ende}" title="#{cofresclientes.clientes.ende}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Bairro}" style="width: 115px">
                                            <h:outputText value="#{cofresclientes.clientes.bairro}" title="#{cofresclientes.clientes.bairro}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Cidade}" style="width: 180px">
                                            <h:outputText value="#{cofresclientes.clientes.cidade}" title="#{cofresclientes.clientes.cidade}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.UF}" style="width: 30px">
                                            <h:outputText value="#{cofresclientes.clientes.estado}" title="#{cofresclientes.clientes.estado}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CreditoDia}" style="width: 120px" styleClass="celula-right">
                                            <h:outputText value="#{cofresclientes.tescofresres.vlrTotalCred}" style="text-align: center"
                                                          title="#{cofresclientes.tescofresres.vlrTotalCred}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorRecolhido}" style="width: 120px" styleClass="celula-right">
                                            <h:outputText value="#{cofresclientes.tescofresres.vlrTotalRec}" title="#{cofresclientes.tescofresres.vlrTotalRec}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.SaldoCustoDia}" style="width: 120px" styleClass="celula-right">
                                            <h:outputText value="#{cofresclientes.tescofresres.saldoFisCst}" title="#{cofresclientes.tescofresres.saldoFisCst}" converter="conversormoeda" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.SaldoCofre}" style="width: 120px" styleClass="celula-right">
                                            <h:outputText value="#{cofresclientes.tescofresres.saldoFisTotal}" title="#{cofresclientes.tescofresres.saldoFisTotal}" converter="conversormoeda"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panel>

                            </div>

                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" update="movimentos msgs" 
                                           actionListener="#{cofre.buttonAction}">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style=" top: 0px; right: 5px; position: fixed">
                            <p:commandLink title="#{localemsgs.Voltar}" oncomplete="PF('dlgOk').show();">
                                <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                    <p:draggable for="botoes" axis="y" opacity="0.3"/>
                </h:form>

                <h:form id="movimentos">
                    <p:dialog widgetVar="dlgListarMovimentos" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icones_satmob_cofre.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CofreInteligente}" style="color:#022a48" /> 
                        </f:facet>

                        <div>
                            <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                            <h:outputText value="#{localemsgs.Cliente} #{cofre.cofreSelecionado.clientes.NRed}" style="font-weight: bold"/>
                        </div>

                        <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                            <div>
                                <div style="float: left; width: 40%">
                                    <p:outputLabel for="data1" value="#{localemsgs.DataInicial}: "/>
                                    <p:spacer width="2px"/>
                                    <p:inputMask id="data1" value="#{cofre.data1}" mask="99/99/9999" style="width: 90px;"/>
                                </div>
                                <div style="float: left; width: 40%">
                                    <p:outputLabel for="data2" value="#{localemsgs.DataFinal}: "/>
                                    <p:spacer width="2px"/>
                                    <p:inputMask id="data2" value="#{cofre.data2}" mask="99/99/9999" style="width: 90px;"/>

                                    <p:spacer width="5"/>

                                    <p:commandLink title="#{localemsgs.Pesquisar}" update="tabelaMov msgs" action="#{cofre.ListarMovimentacao}">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>    
                                    </p:commandLink>
                                </div>
                                <div style="float: left; width: 20%; text-align: right;">
                                    <p:commandLink title="#{localemsgs.Exportar}" action="#{cofre.setImg(exportarMB.getLogo(cofre.banco))}" onclick="PF('dlgExportar').show();">
                                        <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="30"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <p:dataTable id="tabelaMov" value="#{cofre.listaMovimentacao}" var="movimento"
                                         resizableColumns="true" styleClass="tabela"
                                         selection="#{clientes.guiaSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                         scrollable="true" scrollWidth="100%" scrollHeight="400"
                                         style="font-size: 12px; background: white; float: left">
                                <p:column headerText="#{localemsgs.Cofre}" style="width: 40px" exportable="#{cofre.cofre}">
                                    <h:outputText value="#{movimento.tescofresres.codCofre}"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}">
                                        <f:convertNumber pattern="0000"/>
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{localemsgs.Data}" style="width: 77px;" exportable="#{cofre.data}">
                                    <h:outputText value="#{movimento.tescofresres.dataStr}" converter="conversorData"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DiaSemana}" style="width: 100px;" exportable="#{cofre.diaSeman}">
                                    <h:outputText value="#{movimento.tescofresres.diaSemT}" 
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Feriado}" style="width: 55px;" exportable="#{cofre.feriado}">
                                    <h:outputText value="#{movimento.tescofresres.feriado}" 
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column> 
                                <p:column headerText="#{localemsgs.ValorRecD0}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorCorteD0}">
                                    <h:outputText value="#{movimento.tescofresres.vlrDep}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HoraRecD0}" style="width: 95px;" exportable="#{cofre.horaRecD0}">
                                    <h:outputText value="#{movimento.tescofresres.hrRecDepD0}" converter="conversorHora"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DepDiaAntAposCorte}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.depDiaAntAposCorte}">
                                    <h:outputText value="#{movimento.tescofresres.vlrCredRecD0}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorCorteD0}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorCorteD0}">
                                    <h:outputText value="#{movimento.tescofresres.vlrCredCorteD0}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.TotalCredDia}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.totalCredDia}">
                                    <h:outputText value="#{movimento.tescofresres.vlrTotalCred}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HrRecDia}" style="width: 95px;" exportable="#{cofre.hrRecDia}">
                                    <h:outputText value="#{movimento.tescofresres.hrRecApos}" converter="conversorHora"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorRecDia}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorRecDia}">
                                    <h:outputText value="#{movimento.tescofresres.vlrRecApos}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorRecJaCreditado}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorRecJaCreditado}">
                                    <h:outputText value="#{movimento.tescofresres.vlrD0Apos}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ValorRecACreditar}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.valorRecACreditar}">
                                    <h:outputText value="#{movimento.tescofresres.vlrD1Apos}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.SaldoCofreTotal}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.saldoCofreTotal}">
                                    <h:outputText value="#{movimento.tescofresres.saldoFisTotal}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DepositoJaCreditado}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.depositoJaCreditado}">
                                    <h:outputText value="#{movimento.tescofresres.saldoFisCred}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DepositoProxDU}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.depositoProxDU}">
                                    <h:outputText value="#{movimento.tescofresres.vlrDepProxDU}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.diaSem eq '1' || movimento.tescofresres.diaSem eq '7'
                                                    ? 'color:blue' : 'color:black'}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.SaldoFisCst}" style="width: 95px;" styleClass="celula-right" exportable="#{cofre.saldoFisCst}">
                                    <h:outputText value="#{movimento.tescofresres.saldoFisCst}" converter="conversormoeda"
                                                  style="#{movimento.tescofresres.saldoFisCst.toPlainString().contains('-') ? 'color:red':'color:green'}"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                    </p:dialog>

                    <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                              style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" /> 
                        </f:facet>
                        <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                        <p:separator />
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cofre" value="#{cofre.cofre}">
                                    <p:ajax update="labelCofre"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCofre" value="#{localemsgs.Cofre}" style="#{cofre.cofre eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>    
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="data" value="#{cofre.data}">
                                    <p:ajax update="labelData"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelData" value="#{localemsgs.Data}" style="#{cofre.data eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="diaseman" value="#{cofre.diaSeman}">
                                    <p:ajax update="labelDiaSeman"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDiaSeman" value="#{localemsgs.DiaSemana}" style="#{cofre.diaSeman eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="feriado" value="#{cofre.feriado}">
                                    <p:ajax update="labelFeriado"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFeriado" value="#{localemsgs.Feriado}" style="#{cofre.feriado eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="valorRecD0" value="#{cofre.valorRecD0}">
                                    <p:ajax update="labelValorRecD0"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelValorRecD0" value="#{localemsgs.ValorRecD0}" style="#{cofre.valorRecD0 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>    
                        <div class="ui-grid-row" style="padding-bottom: 3px;">   
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="horaRecD0" value="#{cofre.horaRecD0}">
                                    <p:ajax update="labelHoraRecD0"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelHoraRecD0" value="#{localemsgs.HoraRecD0}" style="#{cofre.horaRecD0 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="depDiaAntAposCorte" value="#{cofre.depDiaAntAposCorte}">
                                    <p:ajax update="labelDepDiaAntAposCorte"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDepDiaAntAposCorte" value="#{localemsgs.DepDiaAntAposCorte}" style="#{cofre.depDiaAntAposCorte eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="valorCorteD0" value="#{cofre.valorCorteD0}">
                                    <p:ajax update="labelValorCorteD0"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelValorCorteD0" value="#{localemsgs.ValorCorteD0}" style="#{cofre.valorCorteD0 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="totalCredDia" value="#{cofre.totalCredDia}">
                                    <p:ajax update="labelTotalCredDia"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelTotalCredDia" value="#{localemsgs.TotalCredDia}" style="#{cofre.totalCredDia eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>    
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="hrRecDia" value="#{cofre.hrRecDia}">
                                    <p:ajax update="labelHrRecDia"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelHrRecDia" value="#{localemsgs.HrRecDia}" style="#{cofre.hrRecDia eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="valorRecDia" value="#{cofre.valorRecDia}">
                                    <p:ajax update="labelValorRecDia"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelValorRecDia" value="#{localemsgs.ValorRecDia}" style="#{cofre.valorRecDia eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="valorRecJaCreditado" value="#{cofre.valorRecJaCreditado}">
                                    <p:ajax update="labelValorRecJaCreditado"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelValorRecJaCreditado" value="#{localemsgs.ValorRecJaCreditado}" style="#{cofre.valorRecJaCreditado eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>   
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="valorRecACreditar" value="#{cofre.valorRecACreditar}">
                                    <p:ajax update="labelValorRecACreditar"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelValorRecACreditar" value="#{localemsgs.ValorRecACreditar}" style="#{cofre.valorRecACreditar eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>    
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="saldoCofreTotal" value="#{cofre.saldoCofreTotal}">
                                    <p:ajax update="labelSaldoCofreTotal"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSaldoCofreTotal" value="#{localemsgs.SaldoCofreTotal}" style="#{cofre.saldoCofreTotal eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>   
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="depositoJaCreditado" value="#{cofre.depositoJaCreditado}">
                                    <p:ajax update="labelDepositoJaCreditado"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDepositoJaCreditado" value="#{localemsgs.DepositoJaCreditado}" style="#{cofre.depositoJaCreditado eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div> 
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="depositoProxDU" value="#{cofre.depositoProxDU}">
                                    <p:ajax update="labelDepositoProxDU"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDepositoProxDU" value="#{localemsgs.DepositoProxDU}" style="#{cofre.depositoProxDU eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>   
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="saldoFisCst" value="#{cofre.saldoFisCst}">
                                    <p:ajax update="labelSaldoFisCst"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSaldoFisCst" value="#{localemsgs.SaldoFisCst}" style="#{cofre.saldoFisCst eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>

                        <p:separator />

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:panel style="text-align: center">
                                <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                                <h:commandLink target="_blank" id="pdf" actionListener="#{cofre.atualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_pdf.png"/>
                                    <p:dataExporter target="movimentos:tabelaMov" type="pdf"
                                                    fileName="RelatorioCofre#{cofre.cofreSelecionado.clientes.codCofre.toBigInteger()}" 
                                                    preProcessor="#{cofre.exportarMovimentacao}" encoding="iso-8859-1"/>
                                </h:commandLink>
                            </p:panel>

                            <p:panel style="text-align: center">
                                <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                                <h:commandLink id="xlsx" actionListener="#{cofre.atualizaTabela}" target="_blank">
                                    <p:graphicImage url="../assets/img/icone_xls.png"/>
                                    <p:dataExporter target="movimentos:tabelaMov" type="xlsx" fileName="RelatorioCofre#{cofre.cofreSelecionado.clientes.codCofre.toBigInteger()}" /> 
                                </h:commandLink>
                            </p:panel>
                        </p:panelGrid>
                    </p:dialog>
                </h:form>

                <p:dialog positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;"
                          header="#{localemsgs.Opcoes}" widgetVar="dlgOk">
                    <h:form style="#{login.clientes.size() gt 1 ? 'width: 400px' : 'width: 250px' }">
                        <div class="form-inline">
                            <h:outputText value="#{localemsgs.EscolherOpcao}:" style="text-align: center"/>
                        </div>
                        <p:spacer height="30px"/>
                        <div class="form-inline">
                             <h:panelGrid columns="1" style="text-align: center; float: left;
                                         #{login.clientes.size() gt 1 ? 'width: 33%' : 'width: 33%' }"
                                         rendered="#{login.clientes.size() gt 1}">
                                <p:commandLink oncomplete="PF('dlgSelecionarCliente').show()">
                                    <p:graphicImage url="../assets/img/icone_satmob_clientes.png" height="40"/>
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgSelecionarCliente').show()">
                                    <h:outputText  value="#{localemsgs.TrocarCliente}"/>
                                </p:commandLink> 
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="text-align: center; float: left;
                                         #{login.clientes.size() gt 1 ? 'width: 33%' : 'width: 50%' }">
                                <p:commandLink oncomplete="PF('dlgTrocarSenha').show()">
                                    <p:graphicImage url="../assets/img/icone_configuracoes.png" height="40"/>
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgTrocarSenha').show()" >
                                    <h:outputText  value="#{localemsgs.TrocarSenha}"/>
                                </p:commandLink> 
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="text-align: center; float: right;
                                         #{login.clientes.size() gt 1 ? 'width: 33%' : 'width: 50%' }">
                                <p:commandLink action="#{login.logOutRH}">
                                    <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                                </p:commandLink>
                                <p:commandLink action="#{login.logOutRH}">
                                    <h:outputText value="#{localemsgs.Sair}"/>
                                </p:commandLink> 
                            </h:panelGrid>
                        </div>
                    </h:form>
                </p:dialog>

                <div>
                    <h:form id="cliente">
                        <p:dialog widgetVar="dlgSelecionarCliente" positionType="absolute" id="dlgClientes"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false"
                                  style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                                      background-size: 750px 430px; left:200px">  
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="tabelaClientes" style="width: 440px; background: transparent">
                                <div class="form-inline">
                                    <p:dataTable id="tabela" value="#{login.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                                 var="cli" resizableColumns="true" selection="#{login.selecionado}" rowKey="#{cli.codCli}"
                                                 scrollable="true" scrollHeight="200" selectionMode="single"
                                                 style="font-size: 12px; float: left" styleClass="tabela" >
                                        <p:ajax event="rowDblselect" listener="#{login.DblSelectCofre}" update="msgs"/>
                                        <p:column headerText="#{localemsgs.Codigo}" style="width: 150px">
                                            <h:outputText value="#{cli.codCli}" title="#{cli.codCli}">
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Cliente}" style="width: 285px">
                                            <h:outputText value="#{cli.nomeCli}" title="#{cli.nomeCli}"/>
                                        </p:column>
                                    </p:dataTable>
                                </div>
                                <div class="form-inline">
                                    <h:outputText value="#{localemsgs.VerTodos}: "/>
                                    <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                    <p:spacer height="25px"/>
                                </div>
                                <div class="form-inline">
                                    <p:commandLink id="btnSelecionar" action="#{login.SelecionarClienteCofre}" 
                                                   title="#{localemsgs.Selecionar}">
                                        <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                    </p:commandLink>
                                </div>
                            </p:panel>
                        </p:dialog>
                    </h:form>
                </div>

                <div>
                    <h:form id="trocarsenha">
                        <p:dialog widgetVar="dlgTrocarSenha" positionType="absolute" id="dlgTrocarSenha"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false"
                                  style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                                      background-size: 750px 430px; left:200px">  
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_configuracoes.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="panelSenha" style="width: 440px; background: transparent">
                                <div class="ui-grid ui-grid-responsive">
                                    <div class="ui-grid-row">
                                        <div class="ui-grid-col-6" style="align-self: flex-start">
                                            <p:outputLabel for="atual" value="#{localemsgs.SenhaAtual}" />
                                        </div>
                                        <div class="ui-grid-col-6" style="align-self: flex-start">
                                            <p:password id="atual" value="#{cofre.senhaAtual}" label="#{localemsgs.SenhaAtual}" required="true" 
                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.SenhaAtual}"/>
                                        </div>
                                    </div>
                                </div>

                                <div class="ui-grid ui-grid-responsive">
                                    <div class="ui-grid-row">
                                        <div class="ui-grid-col-6" style="align-self: flex-start">
                                            <p:outputLabel for="nova1" value="#{localemsgs.NovaSenha}" />
                                        </div>
                                        <div class="ui-grid-col-6" style="align-self: flex-start">
                                            <p:password id="nova1" value="#{cofre.novaSenha}" match="nova2"
                                                        label="#{localemsgs.NovaSenha}" required="true" feedback="true"
                                                        promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                                        goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NovaSenha}">
                                                <f:validateRegex pattern="^[0-9]{5,20}$" for="nova1"/>
                                            </p:password>
                                        </div>
                                    </div>
                                </div>

                                <div class="ui-grid ui-grid-responsive">
                                    <div class="ui-grid-row">
                                        <div class="ui-grid-col-6" style="align-self: flex-start">
                                            <p:outputLabel for="nova2" value="#{localemsgs.confirmarNovaSenha}" />
                                        </div>
                                        <div class="ui-grid-col-6" style="align-self: flex-start">
                                            <p:password id="nova2" value="#{cofre.novaSenha}" label="#{localemsgs.SenhaAtual}" required="true" 
                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Confirmacao}"/>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-inline">
                                    <p:commandLink id="btnSelecionar" action="#{cofre.TrocarSenha}" 
                                                   title="#{localemsgs.Concluido}" update="msgs">
                                        <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                    </p:commandLink>
                                </div>
                            </p:panel>
                        </p:dialog>
                    </h:form>
                </div>
            </div>
            
            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>

                    <p:ajaxStatus class="status" >
                        <f:facet name="start">
                            <p:graphicImage value="../assets/img/ajax-loader.gif" style="height: 20px; width: 20px"/>
                        </f:facet>
                    </p:ajaxStatus>
                </div>
                <div class="footer-body" id="footer-body">
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <p:panel id="totais" class="ui-grid-col-12 cabecalhoFilial">
                                <div class="ui-grid-col-3" style="font-weight: bold">
                                            #{localemsgs.TotalCreditos}: <h:outputText value="#{cofre.totalCreditoDia}" converter="conversormoeda"/>
                                </div>
                                <div class="ui-grid-col-3" style="font-weight: bold">
                                            #{localemsgs.TotalVlrRecolhido}: <h:outputText value="#{cofre.totalValorRecolhido}" converter="conversormoeda"/>
                                </div>
                                <div class="ui-grid-col-3" style="font-weight: bold">
                                            #{localemsgs.TotalSalCustDia}: <h:outputText value="#{cofre.totalSaldoCustoDia}" converter="conversormoeda"/>
                                </div>
                                <div class="ui-grid-col-3" style="font-weight: bold">
                                            #{localemsgs.TotalSaldoCofre}: <h:outputText value="#{cofre.totalSaldoCofre}" converter="conversormoeda"/>
                                </div>
                            </p:panel>
                        </div>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td align="right">
                                    </td>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function(e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

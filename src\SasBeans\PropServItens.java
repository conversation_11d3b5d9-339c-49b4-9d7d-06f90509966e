/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class PropServItens {

    private BigDecimal Numero;
    private BigDecimal CodFil;
    private BigDecimal Ordem;
    private String TipoPosto;
    private String TipoCalc;
    private String Descricao;
    private BigDecimal Valor;
    private BigDecimal ValorRot;
    private BigDecimal FranquiaRot;
    private BigDecimal ValorEve;
    private BigDecimal FranquiaEve;
    private BigDecimal ValorEsp;
    private BigDecimal FranquiaEsp;
    private BigDecimal ValorAst;
    private BigDecimal FranquiaAst;
    private BigDecimal OS;
    private String OperIncl;
    private LocalDate Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getNumero() {
        return Numero;
    }

    public void setNumero(BigDecimal Numero) {
        this.Numero = Numero;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getOrdem() {
        return Ordem;
    }

    public void setOrdem(BigDecimal Ordem) {
        this.Ordem = Ordem;
    }

    public String getTipoPosto() {
        return TipoPosto;
    }

    public void setTipoPosto(String TipoPosto) {
        this.TipoPosto = TipoPosto;
    }

    public String getTipoCalc() {
        return TipoCalc;
    }

    public void setTipoCalc(String TipoCalc) {
        this.TipoCalc = TipoCalc;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(BigDecimal Valor) {
        this.Valor = Valor;
    }

    public BigDecimal getValorRot() {
        return ValorRot;
    }

    public void setValorRot(BigDecimal ValorRot) {
        this.ValorRot = ValorRot;
    }

    public BigDecimal getFranquiaRot() {
        return FranquiaRot;
    }

    public void setFranquiaRot(BigDecimal FranquiaRot) {
        this.FranquiaRot = FranquiaRot;
    }

    public BigDecimal getValorEve() {
        return ValorEve;
    }

    public void setValorEve(BigDecimal ValorEve) {
        this.ValorEve = ValorEve;
    }

    public BigDecimal getFranquiaEve() {
        return FranquiaEve;
    }

    public void setFranquiaEve(BigDecimal FranquiaEve) {
        this.FranquiaEve = FranquiaEve;
    }

    public BigDecimal getValorEsp() {
        return ValorEsp;
    }

    public void setValorEsp(BigDecimal ValorEsp) {
        this.ValorEsp = ValorEsp;
    }

    public BigDecimal getFranquiaEsp() {
        return FranquiaEsp;
    }

    public void setFranquiaEsp(BigDecimal FranquiaEsp) {
        this.FranquiaEsp = FranquiaEsp;
    }

    public BigDecimal getValorAst() {
        return ValorAst;
    }

    public void setValorAst(BigDecimal ValorAst) {
        this.ValorAst = ValorAst;
    }

    public BigDecimal getFranquiaAst() {
        return FranquiaAst;
    }

    public void setFranquiaAst(BigDecimal FranquiaAst) {
        this.FranquiaAst = FranquiaAst;
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(BigDecimal OS) {
        this.OS = OS;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public LocalDate getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(LocalDate Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

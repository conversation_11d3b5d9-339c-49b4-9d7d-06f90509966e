/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.FormasPgto;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FormasPgtoDao {

    public static List<FormasPgto> listarFormasPagamento(Persistencia persistencia) throws Exception {
        try {
            List<FormasPgto> retorno = new ArrayList<>();
            String sql = " SELECT * FROM FormasPgto ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            FormasPgto formasPgto;
            while (consulta.Proximo()) {
                formasPgto = new FormasPgto();
                formasPgto.setCodigo(consulta.getString("Codigo"));
                formasPgto.setDescricao(consulta.getString("Descricao"));
                formasPgto.setOperador(consulta.getString("Operador"));
                formasPgto.setDt_Alter(consulta.getString("Dt_Alter"));
                formasPgto.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(formasPgto);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FormasPgtoDao.listarFormasPagamento - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM FormasPgto ");
        }
    }
}

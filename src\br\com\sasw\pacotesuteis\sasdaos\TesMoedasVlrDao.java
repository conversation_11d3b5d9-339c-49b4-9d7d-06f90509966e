/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.TesMoedasVlr;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesMoedasVlrDao {

    /**
     * Insere a cotação para cada moeda.
     *
     * @param tesMoedasVlr
     * @param persistencia
     * @throws Exception
     */
    public void atualizarCotacao(TesMoedasVlr tesMoedasVlr, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            // Inserir histórico em TesMoedasVlr
            sql = " INSERT INTO \n"
                    + "     TesMoedasVlr (Sequencia, CodFil, CodMoeda, DtCotacao, Valor, Operador, <PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON>_<PERSON>er) \n"
                    + " VALUES \n"
                    + "     ((SELECT \n"
                    + "         ISNULL(MAX(Sequencia),0) + 1 Sequencia \n"
                    + "     FROM \n"
                    + "         TesMoedasVlr),\n"
                    + "     ?, ?, ?, ?, ?, ?, ?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tesMoedasVlr.getCodFil());
            consulta.setString(tesMoedasVlr.getCodMoeda());
            consulta.setString(tesMoedasVlr.getDtCotacao());
            consulta.setString(tesMoedasVlr.getValor());
            consulta.setString(tesMoedasVlr.getOperador());
            consulta.setString(tesMoedasVlr.getDt_Alter());
            consulta.setString(tesMoedasVlr.getHr_Alter());
            consulta.insert();

            // Atualizar último valor em TesMoedas
            sql = "UPDATE TesMoedas SET valor = ?, DtCotacao = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ?"
                    + " WHERE CodMoeda = ?";

            consulta = new Consulta(sql, persistencia);
            
            consulta.setString(tesMoedasVlr.getValor());
            consulta.setString(tesMoedasVlr.getDtCotacao());
            consulta.setString(tesMoedasVlr.getOperador());
            consulta.setString(tesMoedasVlr.getDt_Alter());
            consulta.setString(tesMoedasVlr.getHr_Alter());
            consulta.setString(tesMoedasVlr.getCodMoeda());
            consulta.update();

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("TesMoedasVlrDao.atualizarCotacao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesMoedasVlr> obterCotacaoDiaria(String codFil, Persistencia persistencia) throws Exception {
        try {
            List<TesMoedasVlr> retorno = new ArrayList<>();
            String sql = "SELECT\n"
                    + "    CodMoeda, Valor, DtCotacao\n"
                    + "FROM\n"
                    + "    (SELECT \n"
                    + "        CodMoeda, Valor, DtCotacao, ROW_NUMBER() OVER(PARTITION BY CodMoeda ORDER BY DtCotacao desc) AS RN\n"
                    + "    FROM \n"
                    + "        TesMoedasVlr \n"
                    + "    WHERE \n"
                    + "        CodFil = ? AND Convert(Varchar, DtCotacao, 112) = Convert(Varchar, getDate()-1, 112)) AS A\n"
                    + "WHERE \n"
                    + "    RN = 1;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            TesMoedasVlr tesMoedasVlr;
            while (consulta.Proximo()) {
                tesMoedasVlr = new TesMoedasVlr();
                tesMoedasVlr.setCodMoeda(consulta.getString("CodMoeda"));
                tesMoedasVlr.setValor(consulta.getString("Valor"));
                tesMoedasVlr.setDtCotacao(consulta.getString("DtCotacao"));
                retorno.add(tesMoedasVlr);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesMoedasVlrDao.obterCotacaoDiaria - " + e.getMessage() + "\r\n"
                    + "SELECT\n"
                    + "    CodMoeda, Valor, DtCotacao\n"
                    + "FROM\n"
                    + "    (SELECT \n"
                    + "        CodMoeda, Valor, DtCotacao, ROW_NUMBER() OVER(PARTITION BY CodMoeda ORDER BY DtCotacao desc) AS RN\n"
                    + "    FROM \n"
                    + "        TesMoedasVlr \n"
                    + "    WHERE \n"
                    + "        CodFil = " + codFil + ") AS A\n"
                    + "WHERE \n"
                    + "    RN = 1;");
        }
    }

    public static TesMoedasVlr obterCotacaoDiaria(String codFil, String codMoeda, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT\n"
                    + "    CodMoeda, Valor, DtCotacao\n"
                    + "FROM\n"
                    + "    (SELECT \n"
                    + "        CodMoeda, Valor, DtCotacao, ROW_NUMBER() OVER(PARTITION BY CodMoeda ORDER BY DtCotacao desc) AS RN\n"
                    + "    FROM \n"
                    + "        TesMoedasVlr \n"
                    + "    WHERE \n"
                    + "        CodFil = ? \n"
                    + "        AND codMoeda = ? \n"
                    + "        AND Convert(Varchar, DtCotacao, 112) = Convert(Varchar, getDate()-1, 112)) AS A\n"
                    + "WHERE \n"
                    + "    RN = 1;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(codMoeda);
            consulta.select();
            TesMoedasVlr tesMoedasVlr = null;
            if (consulta.Proximo()) {
                tesMoedasVlr = new TesMoedasVlr();
                tesMoedasVlr.setCodMoeda(consulta.getString("CodMoeda"));
                tesMoedasVlr.setValor(consulta.getString("Valor"));
                tesMoedasVlr.setDtCotacao(consulta.getString("DtCotacao"));
            }
            consulta.close();
            return tesMoedasVlr;
        } catch (Exception e) {
            throw new Exception("TesMoedasVlrDao.obterCotacaoDiaria - " + e.getMessage() + "\r\n"
                    + "SELECT\n"
                    + "    CodMoeda, Valor, DtCotacao\n"
                    + "FROM\n"
                    + "    (SELECT \n"
                    + "        CodMoeda, Valor, DtCotacao, ROW_NUMBER() OVER(PARTITION BY CodMoeda ORDER BY DtCotacao desc) AS RN\n"
                    + "    FROM \n"
                    + "        TesMoedasVlr \n"
                    + "    WHERE \n"
                    + "        CodFil = " + codFil + " \n"
                    + "        AND CodMoeda = " + codMoeda + " \n"
                    + "        AND Convert(Varchar, DtCotacao, 112) = Convert(Varchar, getDate()-1, 112)) AS A\n"
                    + "WHERE \n"
                    + "    RN = 1;");
        }
    }
}

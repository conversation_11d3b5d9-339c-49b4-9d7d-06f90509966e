<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                thead tr th,
                thead tr td{
                    background: linear-gradient(to bottom, #597d98, #4b708d) !important;
                    min-height:46px !important;
                    color: #FFF !important; 
                    border:thin solid #7397b1 !important;
                    font-size: 9pt !important;
                }

                @media only screen and (max-width: 2000px) and (min-width: 641px) {
                    /*thead tr th,
                    tbody tr td{
                        min-width: 200px;
                        width: 200px;
                        max-width: 200px;
                    }*/

                    thead tr th:nth-child(1),
                    tbody tr td:nth-child(1){
                        min-width: 60px;
                        width: 60px;
                        max-width: 60px;
                    }

                    thead tr th:nth-child(2),
                    tbody tr td:nth-child(2),
                    thead tr th:nth-child(3),
                    tbody tr td:nth-child(3){
                        min-width: 200px;
                        width: 200px;
                        max-width: 200px;
                    }

                    thead tr th:nth-child(5),
                    tbody tr td:nth-child(5),
                    thead tr th:nth-child(6),
                    tbody tr td:nth-child(6),
                    thead tr th:nth-child(7),
                    tbody tr td:nth-child(7),
                    thead tr th:nth-child(8),
                    tbody tr td:nth-child(8),
                    thead tr th:nth-child(9),
                    tbody tr td:nth-child(9){
                        max-width: 140px !important;
                    }

                    thead tr th:nth-child(11),
                    tbody tr td:nth-child(11){
                        min-width: 80px;
                        width: 80px;
                        max-width: 80px;
                    }

                    thead tr th:nth-child(10),
                    tbody tr td:nth-child(10){
                        min-width: 120px;
                        width: 120px;
                        max-width: 120px;
                    }
                }

                .ui-inputtext{
                    min-width: 200px !important;
                    width: 200px !important;
                    max-width: 200px !important;
                }

                [id*="range_input"]{
                    min-width: 200px !important;
                    width: 200px !important;
                    max-width: 200px !important;
                }

                [id*="tabela"] tbody tr td:nth-child(11)[atraso="0"]{
                    background-color: honeydew !important;
                    color: forestgreen !important;
                    font-weight: bold !important;
                }
                
                [id*="tabela"] tbody tr td:nth-child(11):not([atraso="0"]){
                    background-color: mistyrose !important;
                    color: red !important;
                }
                
                .ui-datatable-summaryrow > td .ui-column-title{
                    display: none;
                }
            </style>
        </h:head>

        <h:body id="h" style="max-height: 100% !important; height: 100% !important;">
            <f:metadata>
                <f:viewAction action="#{creceber.Persistencias(login.pp)}"/>
                <f:viewAction action="#{creceber.carregarGride}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_contas_receber.png" height="40" style="margin-top:-6px !important; margin-right: 6px !important" />
                                    <label class="TituloPagina">#{localemsgs.ContasReceber}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText value="#{creceber.dataInicio}" converter="conversorDate" />
                                            <h:outputText value=" - "/>
                                            <h:outputText value="#{creceber.dataFim}" converter="conversorDate"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{creceber.filiais.descricao}
                                            <label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco" style="line-height: 9px !important">
                                            #{creceber.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{creceber.filiais.bairro}, #{creceber.filiais.cidade}/#{creceber.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divCalendario" class="ui-grid-col-3" style="align-self: center; text-align: center; margin-top: -7px !important">
                                    <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                                  value="#{creceber.datasSelecionadas}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{creceber.selecionarDatas}" update="msgs main:tabela" />
                                    </p:datePicker>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3"
                                     style="padding:0px 10px 0px 0px !important; text-align: right !important">
                                    <p:commandLink title="#{localemsgs.Voltar}" oncomplete="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:panel id="gridDefault" class="ui-grid ui-grid-responsive FundoPagina"
                             style="overflow:hidden !important; padding-right:12px !important; max-height: calc(100% - 10px) !important; margin-top: 8px !important;">
                        <p:dataTable id="tabela" value="#{creceber.allCReceber}" 
                                     emptyMessage="#{localemsgs.SemRegistros}" 
                                     var="lista" 
                                     lazy="false" 
                                     paginator="false"
                                     reflow="true"  
                                     scrollWidth="100%" 
                                     selectionMode="single"
                                     rowKey="#{lista.NF}#{lista.cliente}"
                                     class="tabela DataGrid"
                                     scrollHeight="100%"
                                     styleClass="tabela"
                                     sortBy="#{lista.dtVenc}"
                                     style="font-size: 12px; background: white"
                                     scrollable="false">
                            <p:column headerText="#{localemsgs.CodFil}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.codFil}" converter="conversor0" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Nome}" class="text-left" exportable="true">
                                <h:outputText value="#{lista.cliente}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.NRed}" class="text-left" exportable="true">
                                <h:outputText value="#{lista.NRed}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.CNPJCPF}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.CMC7}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.NFiscais}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.NF}" converter="conversor0" />
                            </p:column>
                            <p:column headerText="#{localemsgs.DataNF}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.dataNF}" converter="conversorData" />
                            </p:column>
                            <p:column headerText="#{localemsgs.DtVencimento}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.dtVenc}" converter="conversorData" />
                            </p:column>
                            <p:column headerText="#{localemsgs.DtPrevisaoPagto}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.dtPrevPg}" converter="conversorData" />
                            </p:column>
                            <p:column headerText="#{localemsgs.DtPagto}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.dtPagto}" converter="conversorData" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Valor}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.valor}" converter="conversormoeda" />
                            </p:column>
                            <p:column headerText="#{localemsgs.DiasAtraso}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.diasAtraso}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Detalhes}" class="text-center" exportable="true">
                                <h:outputText value="#{lista.obs}" />
                            </p:column>
                            <p:summaryRow>
                                <p:column colspan="9" headerText="" style="text-align: right; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                    <h:outputText value="#{localemsgs.TotalGeral}: " style="color:#FFF !important; padding-right: 10px !important"/>
                                </p:column>
                                <p:column headerText="" style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                    <h:outputText value="#{lista.totalValor}" style="color:#FFF !important" converter="conversormoeda" />
                                </p:column>
                                <p:column colspan="2" headerText="" style="text-align: center; background:linear-gradient(to bottom, #505050, #202020); color:#FFF">
                                    <h:outputText value="" style="color:#FFF !important"/>
                                </p:column>
                            </p:summaryRow>
                        </p:dataTable>
                        
                        <div style="width: 50px; height: 50px; position: absolute; bottom: 10px; right: 10px">
                            <h:commandLink id="xlsx">
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" style="height:40px"/>
                                <p:dataExporter target="main:tabela" type="xlsx" fileName="#{localemsgs.ContasReceber}"/>
                            </h:commandLink>
                        </div>
                    </p:panel>
                    <script>
                        function setAtrasos(){
                            $('[id*="tabela"] tbody tr').each(function(){
                               $(this).find('td:nth-child(11)').attr('atraso', ReplaceAll($(this).find('td:nth-child(11)').text(),$('[id*="tabela"] thead tr:first-child').find('th:nth-child(11)').text(),''));
                            });
                        }
                    </script>
                </h:form>

                <ui:insert name="loading" >
                    <ui:include src="../assets/template/loading.xhtml" />
                </ui:insert>

                <footer>
                    <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                        <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important; display: #{login.pp.empresa ne 'SATMAXIMA'?'none':''}">
                            <h:form id="corporativo">
                                <div style="margin-top: 12px !important">
                                    <label ref="lblCheck"><h:outputText value="#{localemsgs.Excluido}: " /></label>
                                    <p:selectBooleanCheckbox value="#{pessoa.mostraExcluidos}">
                                        <p:ajax update="msgs main:tabela" />
                                    </p:selectBooleanCheckbox>
                                </div>
                            </h:form>
                        </div>
                        <div class="container" style="min-height:10px !important;max-height:40px !important;">
                            <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                                <table class="footer-time" style="min-height:10px !important">
                                    <tr>
                                        <td>
                                            <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                                <table class="footer-user" style="min-height:10px !important">
                                    <tr>
                                        <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                        <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                    <tr>
                                        <td><img src="../assets/img/logo_satweb.png" /></td>
                                        <td>
                                            <h:form>
                                                <h:commandLink actionListener="#{localeController.increment}"
                                                               action="#{localeController.getLocales}" >
                                                    <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                                </h:commandLink>
                                            </h:form>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </h:body>
    </f:view>
</html>
/*
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.SatWebService.Clientes_Rota_Servicos;
import SasBeans.SatWebService.EncuestasDeSalida;
import SasBeans.SatWebService.Visitas_Dia_Regiao;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatoriosGometrixDao {

    public List<EncuestasDeSalida> encuestas(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            String sql = " select supervisor,d_place.punto_servicio, d_place.Cliente, d_place.Codigo_punto_servicio codigo_pds, "
                    + " concat(tmp_encuestas.fecha_checkin,' ',tmp_encuestas.hora_checkin) fecha_hora_checkin, "
                    + " concat(tmp_encuestas.fecha_checkout,' ',hora_checkout) fecha_hora_checkout, num_empleado, "
                    + " presentacion_elemento,consignas,actitud, material,area_trabajo, observaciones "
                    + " from tmp_encuestas "
                    + " inner join d_place on d_place.id_place = tmp_encuestas.id_place "
                    + " where fecha_checkin BETWEEN ? and ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            List<EncuestasDeSalida> encuestas = new ArrayList<>();
            EncuestasDeSalida encuesta;
            while (consulta.Proximo()) {
                encuesta = new EncuestasDeSalida();
                encuesta.setSupervisor(consulta.getString("supervisor"));
                encuesta.setPunto_servico(consulta.getString("punto_servicio"));
                encuesta.setCliente(consulta.getString("cliente"));
                encuesta.setCodigo_pds(consulta.getString("codigo_pds"));
                encuesta.setFecha_hora_checkin(consulta.getString("fecha_hora_checkin"));
                encuesta.setFecha_hora_checkout(consulta.getString("fecha_hora_checkout"));
                encuesta.setNum_empleado(consulta.getString("num_empleado"));
                encuesta.setPresentacion_elemento(consulta.getString("presentacion_elemento"));
                encuesta.setConsignas(consulta.getString("consignas"));
                encuesta.setActitud(consulta.getString("actitud"));
                encuesta.setMaterial(consulta.getString("material"));
                encuesta.setArea_trabajo(consulta.getString("area_trabajo"));
                encuesta.setObservaciones(consulta.getString("observaciones"));
                encuestas.add(encuesta);
            }
            consulta.Close();
            return encuestas;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar encuestas de salida - " + e.getMessage());
        }
    }

    /**
     * calcula o número de clientes
     *
     * @param dataInicio
     * @param dataFim
     * @param persistencia
     * @return
     * @throws java.lang.Exception
     */
    public BigDecimal numeroClientes(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            /* Alterações 10/10/2017 */
 /*String sql = " SELECT count(DISTINCT place.id_client) num_cliente "
                    + " from service "
                    + " left join place on place.id =service.place "
                    + " where due_start_date BETWEEN ? and ? "
                    + "     and service.`status` = 1 "
                    + "     and asignee_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";*/
            String sql = " SELECT COUNT(DISTINCT d_place.id_cliente) num_cliente "
                    + " FROM  gmxdb.tmp_efectividad tmp_efectividad "
                    + " INNER JOIN gmxdb.d_place d_place ON d_place.id_place = tmp_efectividad.id_punto_servicio "
                    + " WHERE fecha_inicio BETWEEN ? AND ? "
                    + "       AND tmp_efectividad.`status` = 1 "
                    + "       AND id_usuario not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            BigDecimal num_cliente = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                num_cliente = consulta.getBigDecimal("num_cliente");
            }
            consulta.Close();
            return num_cliente;
        } catch (Exception e) {
            throw new Exception("Falha ao calcular número de clientes - " + e.getMessage());
        }
    }

    /**
     * calcula o tempo médio
     *
     * @param dataInicio
     * @param dataFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String tempoMedio(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            /* Alterações 10/10/2017 */
 /*String sql = " SELECT SEC_TO_TIME(avg((check_out_date-check_in_date)/1000)) tiempo_promedio "
                    + " from report_report "
                    + " where date(FROM_UNIXTIME(check_in_date/1000)) BETWEEN ? and ? "
                    + "     and report_report.id_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                                       75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                                       4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                                       4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";*/
            String sql = " SELECT DATE_FORMAT(SEC_TO_TIME(AVG(minutos_estancia*60)),'%h:%m:%s') tiempo_promedio "
                    + " FROM gmxdb.tmp_efectividad"
                    + " WHERE fecha_inicio BETWEEN ? AND ? "
                    + "       AND id_usuario NOT IN (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            String tiempo_promedio = "";
            while (consulta.Proximo()) {
                tiempo_promedio = consulta.getString("tiempo_promedio");
            }
            consulta.Close();
            return tiempo_promedio;
        } catch (Exception e) {
            throw new Exception("Falha ao calcular o tempo médio - " + e.getMessage());
        }
    }

    /**
     * calcula o número de pontos de serviço
     *
     * @param dataInicio
     * @param dataFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal numeroPontosServico(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            /* Alterações 10/10/2017 */
 /*String sql = " SELECT count(DISTINCT service.place) num_pds from service "
                    + " where due_start_date BETWEEN ? and ? "
                    + "     and service.`status` = 1 "
                    + "     and asignee_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74,"
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";*/
            String sql = "SELECT COUNT(DISTINCT id_punto_servicio) num_pds "
                    + " FROM gmxdb.tmp_efectividad"
                    + " WHERE fecha_inicio BETWEEN ? AND ? "
                    + "       AND tmp_efectividad.`status` = 1 "
                    + "       AND id_usuario NOT IN (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            BigDecimal num_pds = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                num_pds = consulta.getBigDecimal("num_pds");
            }
            consulta.Close();
            return num_pds;
        } catch (Exception e) {
            throw new Exception("Falha ao calcular número de pontos de serviço - " + e.getMessage());
        }
    }

    /**
     * calcula o número de visitas
     *
     * @param dataInicio
     * @param dataFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal numeroVisitas(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            /* Alterações 10/10/2017 */
 /*String sql = " SELECT count(id) visitas "
                    + " from report_report "
                    + " where date(FROM_UNIXTIME(check_in_date/1000)) BETWEEN ? and ? "
                    + "     and report_report.id_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                                       75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                                       4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                                       4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";*/
            String sql = " SELECT COUNT(id_reporte) visitas "
                    + " FROM gmxdb.tmp_reportes "
                    + " WHERE fecha_checkin BETWEEN ? AND ? "
                    + "       AND id_usuario NOT IN (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            BigDecimal visitas = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                visitas = consulta.getBigDecimal("visitas");
            }
            consulta.Close();
            return visitas;
        } catch (Exception e) {
            throw new Exception("Falha ao calcular número de visitas - " + e.getMessage());
        }
    }

    /**
     * calcula a eficácia das visitas
     *
     * @param dataInicio
     * @param dataFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal eficaciaVisitas(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            /* Alterações 10/10/2017 */
 /*String sql = " SELECT (count(report_report.id)/count(DISTINCT service.id)) efectividad "
                    + " from service "
                    + " left join report_report on service.id =report_report.id_service "
                    + " where due_start_date BETWEEN ? and ? "
                    + "    and service.`status` = 1 "
                    + "    and asignee_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                             75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                             4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                             4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";*/
            String sql = "SELECT (SUM(visita_realizada)/SUM(visita_programada)) efectividad "
                    + " FROM gmxdb.tmp_efectividad "
                    + " WHERE fecha_inicio BETWEEN ? AND ? "
                    + "       AND tmp_efectividad.`status` = 1 "
                    + "       AND id_usuario NOT IN (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            BigDecimal efectividad = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                efectividad = consulta.getBigDecimal("efectividad");
            }
            consulta.Close();
            return efectividad;
        } catch (Exception e) {
            throw new Exception("Falha ao calcular eficácia das visitas - " + e.getMessage());
        }
    }

    /**
     * calcula a eficiência das visitas
     *
     * @param dataInicio
     * @param dataFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal eficienciaVisitas(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            /* Alterações 10/10/2017 */
 /*String sql = " select sum(if(num_pds>2,1,0))/count(num_pds) eficiencia from ( "
                    + "    SELECT id_place,count( id) as num_pds "
                    + "        from report_report "
                    + "        where date(FROM_UNIXTIME(check_in_date/1000)) BETWEEN ? and ? "
                    + "            and report_report.id_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611,"
                    + "                                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) "
                    + " GROUP BY id_place) eficacia ";*/
            String sql = " SELECT SUM(IF(num_pds>2,1,0))/COUNT(num_pds) eficiencia "
                    + " FROM ( SELECT id_punto_servicio, COUNT(id_reporte) AS num_pds "
                    + "        FROM gmxdb.tmp_reportes "
                    + "        WHERE fecha_checkin BETWEEN ? AND ? "
                    + "              AND id_usuario NOT IN (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                                     75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                                     4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                                     4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) "
                    + " GROUP BY id_punto_servicio) eficacia ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            BigDecimal eficiencia = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                eficiencia = consulta.getBigDecimal("eficiencia");
            }
            consulta.Close();
            return eficiencia;
        } catch (Exception e) {
            throw new Exception("Falha ao calcular eficiência das visitas - " + e.getMessage());
        }
    }

    /**
     *
     * @param dataInicio
     * @param dataFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes_Rota_Servicos> rotaServicoPorCliente(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        List<Clientes_Rota_Servicos> retorno = new ArrayList<>();
        try {
            /* Alterações 10/10/2017 */
 /*String sql = "SELECT c_client.`value` cliente, place.place_code Ruta, count(DISTINCT service.id) servicios "
                    + " from service "
                    + " left join place on place.id = service.place "
                    + " left join c_client on c_client.id = place.id_client "
                    + " left join report_report on service.id =report_report.id_service "
                    + " where due_start_date BETWEEN ? and ? "
                    + "     and service.`status` = 1 "
                    + "     and asignee_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74,"
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623,"
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611,"
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) "
                    + "     and report_report.id is null "
                    + " GROUP BY service.place having servicios > 29 "
                    + " ORDER BY servicios DESC";*/
            String sql = " SELECT d_place.Cliente cliente, d_place.Codigo_punto_servicio Ruta, COUNT(DISTINCT id_servicio) servicios "
                    + " FROM gmxdb.tmp_efectividad tmp_efectividad "
                    + " INNER JOIN gmxdb.d_place d_place ON d_place.id_place = tmp_efectividad.id_punto_servicio "
                    + " WHERE fecha_inicio BETWEEN ? and ? "
                    + "       AND tmp_efectividad.`status` = 1 "
                    + "       AND id_usuario NOT IN (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) "
                    + "       AND tmp_efectividad.visita_realizada = 0 "
                    + " GROUP BY tmp_efectividad.id_punto_servicio HAVING servicios > 29 "
                    + " ORDER BY servicios DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            Clientes_Rota_Servicos resposta;
            while (consulta.Proximo()) {
                resposta = new Clientes_Rota_Servicos();
                resposta.setCliente(consulta.getString("cliente"));
                resposta.setRuta(consulta.getString("Ruta"));
                resposta.setServicios(consulta.getBigDecimal("servicios"));
                retorno.add(resposta);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar rota_servico_cliente - " + e.getMessage());
        }
    }

    public List<Visitas_Dia_Regiao> visitasPorRegiaoPorDia(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        List<Visitas_Dia_Regiao> retorno = new ArrayList<>();
        try {
            /* Alterações 10/10/2017 */
 /* String sql = "SELECT c_region.`value` region,day(date(FROM_UNIXTIME(check_in_date/1000))) dia,"
                    + " count(report_report.id) visitas "
                    + " from report_report "
                    + " left join place on place.id = report_report.id_place "
                    + " left join c_region on c_region.id = place.id_region "
                    + " where date(FROM_UNIXTIME(check_in_date/1000)) BETWEEN ? and ? "
                    + "     and report_report.id_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74,"
                    + "                                       75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623,"
                    + "                                       4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611,"
                    + "                                       4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844)  "
                    + " GROUP BY id_region,dia"; */
            String sql = "SELECT d_place.region region, DAY(fecha_checkin) dia, COUNT(id_reporte) visitas "
                    + " FROM gmxdb.tmp_reportes tmp_reportes "
                    + " INNER JOIN gmxdb.d_place d_place ON d_place.id_place = tmp_reportes.id_punto_servicio "
                    + " WHERE fecha_checkin BETWEEN ? and ? "
                    + "       AND id_usuario NOT IN (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74, "
                    + "                              75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623, "
                    + "                              4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611, "
                    + "                              4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844) "
                    + " GROUP BY id_region,dia ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            Visitas_Dia_Regiao resposta;
            while (consulta.Proximo()) {
                resposta = new Visitas_Dia_Regiao();
                resposta.setRegion(consulta.getString("region"));
                resposta.setDia(consulta.getString("dia"));
                resposta.setVisitas(consulta.getBigDecimal("visitas"));
                retorno.add(resposta);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar visitas_dia_regiao - " + e.getMessage());
        }
    }
}

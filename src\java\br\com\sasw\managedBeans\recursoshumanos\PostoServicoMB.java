/*
 */
package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Controller.Login.LoginSatMobWeb;
import Controller.PstServ.PstServSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Controller.SatMobEW.SatMobEWSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.ContrVig;
import SasBeans.CtrItens;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.PstDepen;
import SasBeans.PstInspecao;
import SasBeans.PstServ;
import SasBeans.PstServDoctos;
import SasBeans.RHPonto;
import SasBeans.Rondas;
import SasBeans.SasPWFill;
import SasBeansCompostas.LogsSatMobEW;
import SasBeansCompostas.TmktDetPstPstServClientes;
import br.com.sasw.lazydatamodels.PstServLazyList;
import br.com.sasw.managedBeans.comercial.ClientesMB;
import br.com.sasw.managedBeans.operacoes.SupervisoesMB;
import br.com.sasw.managedBeans.relatorio.InspecoesMB;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.LogsSatMobEWDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.utils.Mapas.MARCADOR;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.primefaces.model.map.DefaultMapModel;
import org.primefaces.model.map.LatLng;
import org.primefaces.model.map.MapModel;
import org.primefaces.model.map.Marker;

/**
 *
 * <AUTHOR>
 */
@Named(value = "postoservico")
@ViewScoped
public class PostoServicoMB implements Serializable {

    private final String banco;
    private Persistencia persistencia;
    private List<PstServ> listaPosto;
    private PstServ selecionado, novo;
    private final PstServSatMobWeb pstservsatmobweb;
    private final BigDecimal codPessoa;
    private String codFil, secao, situacao, local, operador, dataRonda1, dataRonda2, dataPonto, dataRelatorio1, dataRelatorio2,
            dataSupervisao1, dataSupervisao2, enderecoRelatorio, fotoRelatorio, coordenadas, dataTela;
    private Clientes clientes;
    private String endereco, cidadePais, caminho, log;
    private ArquivoLog logerro;
    private int flag, total, posFotoRelatorio;
    private double distPstSup;
    private LazyDataModel<PstServ> postos = null;
    private Map filters;
    private ClientesMB clientesMB;
    private SupervisoesMB supervisaoMB;
    private List<String> sequencias;
    private List<ContrVig> contrvigs;
    private List<CtrItens> ctritens;
    private List<Clientes> listaClientes;
    private Boolean mostrarFiliais, somenteAtivos, limparFiltros;
    private SasPWFill filial;
    private final LoginSatMobWeb loginsatmobweb;
    private boolean eFilial, eCodPosto, ePosto, eTipo, eDescTipo, eInterfExt, eSituacao, eDtSituacao,
            eOperador, eDtAlter, eHrAlter;
    private UploadedFile uploadedFile;
    private InputStream inputStream;
    private List<PstServDoctos> listaDocumentos;
    private PstServDoctos documento;
    private StreamedContent download;
    private List<Rondas> rondas;
    private Rondas ronda;
    private List<PstDepen> dependencias;
    private PstDepen dependencia;
    private List<RHPonto> pontos;
    private RHPonto ponto;
    private List<TmktDetPstPstServClientes> relatorios;
    private TmktDetPstPstServClientes relatorio;
    private Marker marker;
    private MapModel pin;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private final String CODFIL = " pstserv.codfil = ? ",
            SITUACAO = " pstserv.situacao = ? ",
            TIPOPOSTO = " pstserv.tipoposto LIKE ? ",
            SECAO = " pstserv.secao LIKE ? ",
            CODCLI = " pstserv.codcli LIKE ? ",
            LOCAL = " pstserv.local LIKE ? ",
            OS = " pstserv.OS = ? ",
            POSTO = " pstserv.posto LIKE ? ",
            DESCRICAO = " ctritens.descricao LIKE ? ",
            CONTRATO = " ctritens.contrato LIKE ? ";
    private String chavePesquisa = "LOCAL", valorPesquisa;
    // inspeção
    private MapModel mapa;
    private String html, titulo, centroMapa, zoomMapa, listaTrabalhos, markers, centro;
    private final SatMobEWSatWeb satMobEWSatWeb;
    private LogsSatMobEW logsSatMobEWSelecionado;
    private List<LogsSatMobEW> listaLogsSatMobEW;
    private List<InspecoesMB.PstInspecaoDetalhes> pstInspecaoSelecionadaDetalhes;
    private String dataInspecao1, dataInspecao2;
    private List<PstInspecao> pstInspecoesBoletimTrabalho;
    private Pessoa pessoaSelecionada;
    private List<Pessoa> pessoas;

    PostoServicoMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        secao = "";
        local = "";
        situacao = "";
        listaPosto = new ArrayList<>();
        novo = new PstServ();
        novo.setSituacao("A");
        pstservsatmobweb = new PstServSatMobWeb();
        contrvigs = new ArrayList<>();
        ctritens = new ArrayList<>();
        listaClientes = new ArrayList<>();
        sequencias = new ArrayList<>();
        mostrarFiliais = false;
        somenteAtivos = true;
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        filial = new SasPWFill();
        filial.setCodfilAc(codFil);
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
        eFilial = true;
        eCodPosto = true;
        ePosto = true;
        eTipo = true;
        eDescTipo = true;
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");
        satMobEWSatWeb = new SatMobEWSatWeb();
    }

    public PostoServicoMB(Persistencia pp) {
        persistencia = pp;
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        secao = "";
        local = "";
        situacao = "";
        listaPosto = new ArrayList<>();
        novo = new PstServ();
        novo.setSituacao("A");
        pstservsatmobweb = new PstServSatMobWeb();
        contrvigs = new ArrayList<>();
        ctritens = new ArrayList<>();
        listaClientes = new ArrayList<>();
        sequencias = new ArrayList<>();
        mostrarFiliais = false;
        somenteAtivos = true;
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        filial = new SasPWFill();
        filial.setCodfilAc(codFil);
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
        eFilial = true;
        eCodPosto = true;
        ePosto = true;
        eTipo = true;
        eDescTipo = true;
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");
        satMobEWSatWeb = new SatMobEWSatWeb();
    }

    public void Persistencia(Persistencia pp) {
        try {
            persistencia = pp;
            if (null == persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            clientesMB = new ClientesMB(persistencia);
            supervisaoMB = new SupervisoesMB(persistencia);
            filial.setDescricao(supervisaoMB.getNomeFilial());
            filters = new HashMap();
            filters.put(CODFIL, codFil);
            filters.put(SITUACAO, "A");
            filters.put(TIPOPOSTO, "");
            filters.put(SECAO, "");
            filters.put(CODCLI, "");
            filters.put(LOCAL, "");
            filters.put(DESCRICAO, "");
            filters.put(CONTRATO, "");
            filters.put(OS, "");
            filters.put(POSTO, "");
            filiais = rotassatweb.buscaInfoFilial(codFil, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void MostrarFiliais() {
        if (mostrarFiliais) {
            filters.replace(CODFIL, "");
        } else {
            filters.replace(CODFIL, codFil);
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(filters);
        lazyGetAllPostos();
        dt.setFirst(0);
    }

    public void SomenteAtivos() {
        if (this.somenteAtivos) {
            this.filters.replace(SITUACAO, "A");
        } else {
            this.filters.replace(SITUACAO, "");
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        lazyGetAllPostos();
        dt.setFirst(0);
    }

    /**
     * Edita um posto de serviço e fecha a tela de cadastro em caso de sucesso
     */
    public void Editar() {
        try {
            this.clientesMB.setCodigo(this.clientes.getCodigo());
            this.clientesMB.setNome("");
            this.clientesMB.setNred("");
            this.clientesMB.setAgencia("");
            this.clientesMB.BuscarCliente();
            this.clientes = this.clientesMB.getLista().get(0);
            this.novo.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novo.setDt_Alter(LocalDate.now());
            if (!this.situacao.equals(this.novo.getSituacao())) {
                this.novo.setDt_Situacao(LocalDate.now());
            }
            if (!this.local.equalsIgnoreCase(this.novo.getLocal())) {
                //this.novo.setLocal(FuncoesString.RecortaAteEspaço(this.novo.getLocal().toUpperCase() + " " + this.clientes.getNRed().toUpperCase(), 0, 40));
                if (this.novo.getLocal().length() > 40) {
                    this.novo.setLocal(this.novo.getLocal().toUpperCase().substring(40));
                }
            }
            this.novo.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.pstservsatmobweb.GravaPstServ(this.novo, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Monta o endereço de um cliente para exibição na página
     */
    public void EnderecoCliente() {
        try {
            if (!this.clientes.getEnde().equals("") && !this.clientes.getBairro().equals("")) {
                this.endereco = this.clientes.getEnde() + " - " + this.clientes.getBairro();
            } else if ((this.clientes.getEnde().equals("") || this.clientes.getBairro().equals(""))
                    && (!this.clientes.getEnde().equals("") || !this.clientes.getBairro().equals(""))) {
                this.endereco = this.clientes.getEnde() + this.clientes.getBairro();
            } else {
                this.endereco = Messages.getMessageS("SemEnderecoCadastrado");
            }

            if (!this.clientes.getCidade().equals("") && !this.clientes.getEstado().equals("") && !this.clientes.getCEP().equals("")) {
                this.cidadePais = this.clientes.getCidade() + "/" + this.clientes.getEstado()
                        + " - " + Messages.getMessageS("CEP") + ": " + this.clientes.getCEP();
            } else if (!this.clientes.getCidade().equals("") && !this.clientes.getEstado().equals("") && this.clientes.getCEP().equals("")) {
                this.cidadePais = this.clientes.getCidade() + "/" + this.clientes.getEstado();
            } else if (this.clientes.getCidade().equals("") && this.clientes.getEstado().equals("") && !this.clientes.getCEP().equals("")) {
                this.cidadePais = Messages.getMessageS("CEP") + ": " + this.clientes.getCEP();
            } else if ((this.clientes.getCidade().equals("") || this.clientes.getEstado().equals("")) && !this.clientes.getCEP().equals("")) {
                this.cidadePais = this.clientes.getCidade() + this.clientes.getEstado()
                        + " - " + Messages.getMessageS("CEP") + ": " + this.clientes.getCEP();
            } else {
                this.cidadePais = Messages.getMessageS("SemEnderecoCadastrado");
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Inicializa um novo posto de serviço para cadastro ou pesquisa
     */
    public void NovoPosto() {
        try {
            this.novo = new PstServ();
            this.novo.setSituacao("A");
            this.flag = 1;
            this.filial = loginsatmobweb.BuscaFilial(this.codFil, this.codPessoa, this.persistencia);
            this.novo.setCodFil(this.filial.getCodfilAc());
            this.novo.setContrato("");
            this.novo.setTipoPosto("");
            this.novo.setTipoPostoDesc("");
            this.clientes = new Clientes();
            this.endereco = "";
            this.cidadePais = "";
            TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formEditar:tabs");
            tabs.setActiveIndex(0);
            PrimeFaces.current().resetInputs("formEditar:editar");
            PrimeFaces.current().executeScript("PF('dlgEditar').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Lista todas as dependencias de um posto
     */
    public void listarDependencias() {
        try {
            this.dependencias = this.pstservsatmobweb.getPstDepenPosto(this.novo, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Lista as rondas de um posto
     */
    public void listarRondas() {
        try {
            this.rondas = this.pstservsatmobweb.getRondasPosto(Mascaras.removeMascaraData(this.dataRonda1), Mascaras.removeMascaraData(this.dataRonda2), this.novo, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Lista as batidas de ponto de um posto
     */
    public void listarPontos() {
        try {
            this.pontos = this.pstservsatmobweb.getPontosPosto(this.novo.getSecao(), Mascaras.removeMascaraData(this.dataPonto), this.novo.getCodFil().toPlainString(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Seleciona um relatório com um duplo clique na tabela
     *
     * @param event - duplo clique na tabela
     */
    public void dbClickPonto(SelectEvent event) {
        this.ponto = (RHPonto) event.getObject();
        mapaPonto();
        PrimeFaces.current().executeScript("PF('dlgBatida').show();");
    }

    public void mapaPonto() {
        Double pstLat = 0.0, pstLon = 0.0;
        try {
            pstLat = Double.valueOf(this.ponto.getLatitude());
            pstLon = Double.valueOf(this.ponto.getLongitude());
        } catch (Exception e) {
        }
        this.pin = new DefaultMapModel();
        if (pstLat != 0.0 || pstLon != 0.0) {
            this.marker = new Marker(new LatLng(pstLat, pstLon), this.ponto.getFuncionario());
            this.marker.setFlat(true);
            this.marker.setTitle(this.ponto.getFuncionario());
            this.marker.setIcon("https://mobile.sasw.com.br:9091/satmobile/img/PIN_chefe_equipe_googlemaps.png");
            this.pin.addOverlay(this.marker);
            this.coordenadas = pstLat + ", " + pstLon;
        }
    }

    public void listarSupervisoes() {
        try {
            this.supervisaoMB.ListarSupervisaoEdicaoPosto(this.selecionado, this.dataSupervisao1, this.dataSupervisao2);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preExibicaoRelatorios() {
        try {
            Mascaras m = new Mascaras();
            DateTimeFormatter data = DateTimeFormatter.ofPattern(m.getPadraoData());
            this.dataRelatorio1 = LocalDate.now().withDayOfMonth(1).format(data);
            this.dataRelatorio2 = LocalDate.now().format(data);
            this.listaPosto = new ArrayList<>();
            this.relatorios = new ArrayList<>();
            PrimeFaces.current().resetInputs("formRelatoriosPosto");
            PrimeFaces.current().executeScript("PF('dlgRelatoriosPosto').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Lista os postos para exibição de relatórios
     *
     * @param query
     * @return
     */
    public List<PstServ> listarPostosSimples(String query) {
        try {
            this.listaPosto = this.pstservsatmobweb.listarPostos(query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.listaPosto;
    }

    /**
     * Lista os relatórios para o usuário do posto de serviço selecionado
     *
     * @param event
     */
    public void selecionarPostoRelatorio(SelectEvent event) {
        try {
            this.novo = (PstServ) event.getObject();
            this.relatorios = this.pstservsatmobweb.getRelatoriosPosto(this.novo.getCodFil().toPlainString(),
                    this.novo.getSecao(), Mascaras.removeMascaraData(this.dataRelatorio1), Mascaras.removeMascaraData(this.dataRelatorio2),
                    this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Lista as batidas de ponto de um posto
     */
    public void listarRelatorios() {
        try {
            this.relatorios = this.pstservsatmobweb.getRelatoriosPosto(this.novo.getCodFil().toPlainString(),
                    this.novo.getSecao(), Mascaras.removeMascaraData(this.dataRelatorio1), Mascaras.removeMascaraData(this.dataRelatorio2),
                    this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Seleciona um relatório com um duplo clique na tabela
     *
     * @param event - duplo clique na tabela
     */
    public void dbClickRelatorioMenu(SelectEvent event) {
        this.relatorio = (TmktDetPstPstServClientes) event.getObject();
        this.enderecoRelatorio = this.relatorio.getClientes().getCidade() + "/" + this.relatorio.getClientes().getEstado() + " - "
                + Mascaras.CEP(this.relatorio.getClientes().getCEP());
        this.posFotoRelatorio = 0;
        this.fotoRelatorio = this.relatorio.getFotos().isEmpty() ? "" : this.relatorio.getFotos().get(0);
        calculaDistPstSup();
        mapaRelatorio();
        PrimeFaces.current().executeScript("PF('dlgExibicaoRelatorio').show();");
    }

    /**
     * Seleciona um relatório com um duplo clique na tabela
     *
     * @param event - duplo clique na tabela
     */
    public void dbClickRelatorio(SelectEvent event) {
        this.relatorio = (TmktDetPstPstServClientes) event.getObject();
        this.enderecoRelatorio = this.relatorio.getClientes().getCidade() + "/" + this.relatorio.getClientes().getEstado() + " - "
                + Mascaras.CEP(this.relatorio.getClientes().getCEP());
        this.posFotoRelatorio = 0;
        this.fotoRelatorio = this.relatorio.getFotos().isEmpty() ? "" : this.relatorio.getFotos().get(0);
        calculaDistPstSup();
        mapaRelatorio();
        PrimeFaces.current().executeScript("PF('dlgRelatorio').show();");
    }

    public void avancarFotoRelatorio() {
        if (this.posFotoRelatorio + 1 == this.relatorio.getFotos().size()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosFim"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoRelatorio = this.relatorio.getFotos().get(this.posFotoRelatorio + 1);
            this.posFotoRelatorio = this.posFotoRelatorio + 1;
        }
    }

    public void voltarFotoRelatorio() {
        if (this.posFotoRelatorio == 0) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosInicio"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoRelatorio = this.relatorio.getFotos().get(this.posFotoRelatorio - 1);
            this.posFotoRelatorio = this.posFotoRelatorio - 1;
        }
    }

    public void mapaRelatorio() {
        Double cliLat = 0.0, cliLon = 0.0, pstLat = 0.0, pstLon = 0.0;
        try {
            cliLat = Double.parseDouble(this.relatorio.getClientes().getLatitude());
            cliLon = Double.parseDouble(this.relatorio.getClientes().getLongitude());
        } catch (Exception e) {
        }
        try {
            pstLat = Double.valueOf(this.relatorio.getTmktdetpst().getLatitude());
            pstLon = Double.valueOf(this.relatorio.getTmktdetpst().getLongitude());
        } catch (Exception e) {
        }
        this.pin = new DefaultMapModel();
        if (cliLat != 0.0 || cliLon != 0.0) {
            this.marker = new Marker(new LatLng(cliLat, cliLon), this.relatorio.getPstserv().getLocal());
            this.marker.setFlat(true);
            this.marker.setTitle(this.relatorio.getPstserv().getLocal());
            this.marker.setIcon("https://mobile.sasw.com.br:9091/satmobile/img/novo_iconedourado_M.png");
            this.pin.addOverlay(this.marker);
        }
        if (pstLat != 0.0 || pstLon != 0.0) {
            this.marker = new Marker(new LatLng(pstLat, pstLon), this.relatorio.getTmktdetpst().getOperador());
            this.marker.setFlat(true);
            this.marker.setTitle(this.relatorio.getTmktdetpst().getOperador());
            this.marker.setIcon("https://mobile.sasw.com.br:9091/satmobile/img/PIN_chefe_equipe_googlemaps.png");
            this.pin.addOverlay(this.marker);
        }
        try {
            if ((cliLat == 0.0 || cliLon == 0.0) && pstLat != 0.0 && pstLon != 0.0) {
                cliLat = pstLat;
                cliLon = pstLon;
            } else if ((pstLat == 0.0 || pstLon == 0.0) && cliLat != 0.0 && cliLon != 0.0) {
                pstLat = cliLat;
                pstLon = cliLon;
            } else if (cliLat == 0.0 && cliLon == 0.0 && pstLat == 0.0 && pstLon == 0.0) {
                throw new Exception();
            }
            this.coordenadas = "" + (cliLat + pstLat) / 2 + ", " + (cliLon + pstLon) / 2;
        } catch (Exception e) {
        }
    }

    public void calculaDistPstSup() {
        Double cliLat = 0.0, cliLon = 0.0, pstLat = 0.0, pstLon = 0.0;
        try {
            cliLat = Double.parseDouble(this.relatorio.getClientes().getLatitude());
            cliLon = Double.parseDouble(this.relatorio.getClientes().getLongitude());
        } catch (Exception e) {
        }
        try {
            pstLat = Double.valueOf(this.relatorio.getTmktdetpst().getLatitude());
            pstLon = Double.valueOf(this.relatorio.getTmktdetpst().getLongitude());
        } catch (Exception e) {
        }
        /* http://andrew.hedges.name/experiments/haversine/ */
        try {
            if (cliLat == 0.0 || cliLon == 0.0 || pstLat == 0.0 || pstLon == 0.0) {
                throw new Exception();
            }
            /* dlon = lon2 - lon1  */
            double lon = Math.toRadians(Double.parseDouble(this.relatorio.getClientes().getLongitude())
                    - Double.valueOf(this.relatorio.getTmktdetpst().getLongitude()));
            /* dlat = lat2 - lat1 */
            double lat = Math.toRadians(Double.parseDouble(this.relatorio.getClientes().getLatitude())
                    - Double.valueOf(this.relatorio.getTmktdetpst().getLatitude()));
            /* a = (sin(dlat/2))^2 + cos(lat1) * cos(lat2) * (sin(dlon/2))^2 */
            double a = Math.pow(Math.sin(lat / 2), 2)
                    + Math.cos(Math.toRadians(Double.valueOf(this.relatorio.getTmktdetpst().getLatitude())))
                    * Math.cos(Math.toRadians(Double.parseDouble(this.relatorio.getClientes().getLatitude())))
                    * Math.pow(Math.sin(lon / 2), 2);
            /* c = 2 * atan2( sqrt(a), sqrt(1-a) )  */
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            /* d = R * c (where R is the radius of the Earth), R = 6373 km  */
            double d = 6373 * c * 1000;
            this.distPstSup = BigDecimal.valueOf(d).setScale(3, RoundingMode.HALF_UP).doubleValue();
        } catch (Exception e) {
            this.distPstSup = 0.0;
        }
    }

    /**
     * Seleciona um posto de serviço com um duplo clique na tabela
     *
     * @param event - evento de duplo clique na tabela
     */
    public void dblSelect(SelectEvent event) {
        this.selecionado = (PstServ) event.getObject();
        buttonAction(null);
    }

    /**
     * Ação do botão de edição: erro quando nenhum posto de seviço está
     * selecionado. Inicializa as variáveis necessárias para exibir detalhes do
     * posto e abre a tela de edição.
     *
     * @param actionEvent - evento de pressionar um botão
     */
    public void buttonAction(ActionEvent actionEvent) {
        if (null == this.selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePstServ"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                Mascaras m = new Mascaras();
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                DateTimeFormatter data = DateTimeFormatter.ofPattern(m.getPadraoData());

                this.clientesMB.setCodigo(this.selecionado.getCodCli());
                this.clientesMB.setCodfil(this.selecionado.getCodFil().toString());
                this.clientesMB.BuscarCliente();
                try {
                    if (this.clientesMB.getLista().isEmpty()) {
                        throw new Exception(Messages.getMessageS("ClienteNaoEncontrado"));
                    }
                    this.clientes = this.clientesMB.getLista().get(0);
                    EnderecoCliente();
                } catch (Exception e) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                }
                this.dataSupervisao1 = LocalDate.now().withDayOfMonth(1).format(data);
                this.dataSupervisao2 = LocalDate.now().format(data);

                DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyMMdd");
                this.dataInspecao1 = LocalDate.now().withDayOfMonth(1).format(format);
                this.dataInspecao2 = LocalDate.now().format(format);;
                this.supervisaoMB.ListarSupervisaoEdicaoPosto(this.selecionado, this.dataSupervisao1, this.dataSupervisao2);
                listarInspecoesPosto();

                this.situacao = this.selecionado.getSituacao();
                this.local = this.selecionado.getLocal();
                this.selecionado.setDescContrato(this.pstservsatmobweb.ListarContratos(this.selecionado.getCodFil(),
                        this.selecionado.getContrato(), this.persistencia).get(0).getDescricao());
                this.novo = this.selecionado;
                try {
                    this.filial = this.loginsatmobweb.BuscaFilial(this.novo.getCodFil().toString(), this.codPessoa, this.persistencia);
                } catch (Exception e) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                }
                this.flag = 2;

                this.dataPonto = LocalDate.now().format(data);
                this.dataRonda1 = LocalDate.now().withDayOfMonth(1).format(data);
                this.dataRonda2 = LocalDate.now().format(data);
                this.dataRelatorio1 = LocalDate.now().withDayOfMonth(1).format(data);
                this.dataRelatorio2 = LocalDate.now().format(data);

                listarRelatorios();
                listarDependencias();
                listarPontos();
                listarRondas();
                ListarArquivosPosto();
                TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formEditar:tabs");
                tabs.setActiveIndex(0);
                PrimeFaces.current().resetInputs("formEditar:editar");
                PrimeFaces.current().executeScript("PF('dlgEditar').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }

        }
    }

    //
    public void HandleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                this.uploadedFile = fileUploadEvent.getFile();
                this.inputStream = new BufferedInputStream(this.uploadedFile.getInputstream());
                //String arquivo = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"+this.uploadedFile.getFileName();
                PstServDoctos docto = new PstServDoctos();
                docto.setCodFil(this.novo.getCodFil());
                docto.setDescricao(this.uploadedFile.getFileName());
                docto.setSecao(this.novo.getSecao());
                docto.setDt_Alter(LocalDate.now());
                docto.setHr_Alter(DataAtual.getDataAtual("HORA"));
                docto.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                docto = this.pstservsatmobweb.incluirDocumentos(docto, this.persistencia);
                new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\" + this.novo.getCodFil().toBigInteger().toString() + "\\" + this.novo.getSecao()).mkdirs();
                String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\" + this.novo.getCodFil().toBigInteger().toString() + "\\" + this.novo.getSecao() + "\\" + docto.getDescricao();
                File file = new File(arquivo);
                FileOutputStream output = new FileOutputStream(file);
                output.write(this.uploadedFile.getContents());
                output.close();
                ListarArquivosPosto();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void ListarArquivosPosto() {
        try {
            PstServDoctos docto = new PstServDoctos();
            docto.setCodFil(this.novo.getCodFil());
            docto.setSecao(this.novo.getSecao());
            this.listaDocumentos = this.pstservsatmobweb.listarDocumentos(docto, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void HandleFileDownload(PstServDoctos docto) {
        try {
            this.documento = docto;
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\" + this.novo.getCodFil().toBigInteger().toString() + "\\" + this.novo.getSecao() + "\\" + this.documento.getDescricao();
            InputStream stream = new FileInputStream(arquivo);
            this.download = new DefaultStreamedContent(stream, "application/" + this.documento.getDescricao().split("\\.")[this.documento.getDescricao().split("\\.").length - 1], this.documento.getDescricao());
        } catch (FileNotFoundException e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ArquivoNaoEncontrado"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Excluí o documento no servidor O documento excluído e movido para uma
     * pasta de removidos O nome do documento entao e alterado para o formato
     * "Nome_UsuarioQueRealizouAcao_yyyy-MM-dd HH-mm-ss.extensao"
     *
     * @param pstServDoctos
     */
    public void HandleFileDelete(PstServDoctos pstServDoctos) {
        this.documento = pstServDoctos;

        String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\" + this.novo.getCodFil().toBigInteger().toString() + "\\" + this.novo.getSecao() + "\\" + this.documento.getDescricao();
        File file = new File(arquivo);

        //cria path de removidos
        File removidos = new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\" + this.novo.getCodFil().toBigInteger().toString() + "\\" + this.novo.getSecao() + "\\" + "Removidos");
        removidos.mkdirs();

        String nome = pstServDoctos.getDescricao().substring(0, pstServDoctos.getDescricao().lastIndexOf("."));
        String tipo = pstServDoctos.getDescricao().split("\\.")[pstServDoctos.getDescricao().split("\\.").length - 1];

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
        LocalDateTime now = LocalDateTime.now();

        file.renameTo(new File(removidos + "\\" + nome + "_" + FuncoesString.RecortaAteEspaço(this.operador, 0, 10) + "_" + dtf.format(now) + "." + tipo));
        //file.delete();
    }

    /**
     * Atualiza a managed bean SuperviaoMB para atualizar os valores da nova
     * supervisão a ser exibida
     */
    public void AtualizarSupervisao() {
        try {
            this.supervisaoMB.AtualizarFilial();
            Double cliLat = 0.0, cliLon = 0.0, pstLat = 0.0, pstLon = 0.0;
            try {
                cliLat = Double.parseDouble(this.supervisaoMB.getCliente().getLatitude());
                cliLon = Double.parseDouble(this.supervisaoMB.getCliente().getLongitude());
            } catch (Exception e) {
            }
            try {
                pstLat = Double.valueOf(this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getLatitude());
                pstLon = Double.valueOf(this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getLongitude());
            } catch (Exception e) {
            }
            if (cliLat != 0.0 || cliLon != 0.0) {
                this.supervisaoMB.setPin(new DefaultMapModel());
                this.supervisaoMB.setMarker(new Marker(new LatLng(cliLat, cliLon),
                        this.supervisaoMB.getSupervisaoSelecionado().getPstserv().getLocal()));
                this.supervisaoMB.getMarker().setFlat(true);
                this.supervisaoMB.getMarker().setTitle(this.supervisaoMB.getSupervisaoSelecionado().getPstserv().getLocal());
                this.supervisaoMB.getMarker().setIcon("https://mobile.sasw.com.br:9091/satmobile/img/novo_iconedourado_M.png");
                this.supervisaoMB.getPin().addOverlay(this.supervisaoMB.getMarker());
            }
            if (pstLat != 0.0 || pstLon != 0.0) {
                this.supervisaoMB.setMarker(new Marker(new LatLng(pstLat, pstLon),
                        this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getOperador()));
                this.supervisaoMB.getMarker().setFlat(true);
                this.supervisaoMB.getMarker().setTitle(this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getOperador());
                this.supervisaoMB.getMarker().setIcon("https://mobile.sasw.com.br:9091/satmobile/img/PIN_chefe_equipe_googlemaps.png");
                this.supervisaoMB.getPin().addOverlay(this.supervisaoMB.getMarker());
            }
            try {
                if ((cliLat == 0.0 || cliLon == 0.0) && pstLat != 0.0 && pstLon != 0.0) {
                    cliLat = pstLat;
                    cliLon = pstLon;
                } else if ((pstLat == 0.0 || pstLon == 0.0) && cliLat != 0.0 && cliLon != 0.0) {
                    pstLat = cliLat;
                    pstLon = cliLon;
                } else if (cliLat == 0.0 && cliLon == 0.0 && pstLat == 0.0 && pstLon == 0.0) {
                    throw new Exception();
                }
                this.supervisaoMB.setCoordenadas("" + (cliLat + pstLat) / 2 + ", " + (cliLon + pstLon) / 2);
            } catch (Exception e) {
            }

            this.supervisaoMB.CalculaDistPstSup();
            this.supervisaoMB.ListarQuestionarios();
            this.supervisaoMB.setPosFotoFuncion(0);
            this.supervisaoMB.setFotoFuncion(null);
            this.supervisaoMB.setPosFotoPosto(0);
            if (!this.supervisaoMB.getSupervisaoSelecionado().getFotos().isEmpty()) {
                this.supervisaoMB.setFotoPosto(this.supervisaoMB.getSupervisaoSelecionado().getFotos().get(0));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    /**
     * Busca a supervisão seguinte com base em uma lista de sequencias
     */
    public void ProximaSupervisao() {
        try {
            String sequencia = this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getSequencia().toPlainString();
            int index = this.sequencias.indexOf(sequencia);
            String proximaSequencia = this.sequencias.get(index + 1);
            TmktDetPstPstServClientes supervisao = new TmktDetPstPstServClientes();
            supervisao.getTmktdetpst().setSequencia(proximaSequencia);
            supervisao.getTmktdetpst().setCodFil(this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getCodFil().toPlainString());
            supervisao.getTmktdetpst().setSecao(this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getSecao());
            this.supervisaoMB.setSupervisaoSelecionado(this.supervisaoMB.CarregarSupervisao(supervisao));
            AtualizarSupervisao();
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemSupervisaoRecente"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    /**
     * Busca a supervisão anterior com base em uma lista de sequencias
     */
    public void SupervisaoAnterior() {
        try {
            String sequencia = this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getSequencia().toPlainString();
            int index = this.sequencias.indexOf(sequencia);
            String proximaSequencia = this.sequencias.get(index - 1);
            TmktDetPstPstServClientes supervisao = new TmktDetPstPstServClientes();
            supervisao.getTmktdetpst().setSequencia(proximaSequencia);
            supervisao.getTmktdetpst().setCodFil(this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getCodFil().toPlainString());
            supervisao.getTmktdetpst().setSecao(this.supervisaoMB.getSupervisaoSelecionado().getTmktdetpst().getSecao());
            this.supervisaoMB.setSupervisaoSelecionado(this.supervisaoMB.CarregarSupervisao(supervisao));
            AtualizarSupervisao();
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemSupervisaoAntiga"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    /**
     * Seleciona uma supervisão com um duplo clique na tabela
     *
     * @param event - duplo clique na tabela
     */
    public void dlbClickSupervisao(SelectEvent event) {
        this.supervisaoMB.setSupervisaoSelecionado((TmktDetPstPstServClientes) event.getObject());
        AbrirSupervisao();
    }

    /**
     * Erro caso nenhuma supervisão esteja selecionada. Inicializa variáveis
     * necessária para exibir os detalhes das supervisões e abre a tela de
     * detalhes
     */
    public void AbrirSupervisao() {
        if (null == this.supervisaoMB.getSupervisaoSelecionado()) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneSupervisao"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.clientesMB.setNome("");
            this.clientesMB.setNred("");
            this.clientesMB.setAgencia("");
            this.clientesMB.BuscarCliente();
            try {
                this.clientes = this.clientesMB.getLista().get(0);
            } catch (Exception e) {
                this.clientes = new Clientes();
            }
            this.supervisaoMB.setCliente(this.clientes);

            this.supervisaoMB.getSupervisaoSelecionado().setPstserv(this.selecionado);

            AtualizarSupervisao();

            this.sequencias = this.supervisaoMB.Sequencias();

            PrimeFaces.current().executeScript("PF('dlgListarSupervisoes').show();");
        }
    }

    /**
     * Define o número do contrato e a descrição do contrato de um novo posto de
     * serviço
     *
     * @param event - evento de selecão de item
     */
    public void SelecionarFilial(SelectEvent event) {
        this.filial = (SasPWFill) event.getObject();
        this.novo.setCodFil(this.filial.getCodfilAc());
        this.novo.setDescContrato("");
        this.novo.setTipoPosto("");
        this.novo.setTipoPostoDesc("");
        this.clientes = new Clientes();
        this.cidadePais = null;
        this.endereco = null;
    }

    /**
     * Método para autocompletar a busca por contratos
     *
     * @param query - string digitada pelo usuário
     * @return lista de strings contendo o número do contrato, nred do cliente e
     * descricao do contrato que correspondam a busca do usuário
     */
    public List<String> BuscarContratos(String query) {
        try {
            this.contrvigs = pstservsatmobweb.ListarContratos(this.novo.getCodFil(), query, persistencia);
            List<String> retorno = new ArrayList<>();
            for (ContrVig contrato : contrvigs) {
                retorno.add(contrato.getContrato() + ": " + contrato.getNRed() + " - " + contrato.getDescricao());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    /**
     * Define o número do contrato e a descrição do contrato de um novo posto de
     * serviço
     *
     * @param event - evento de selecão de item
     */
    public void SelecionarContrato(SelectEvent event) {
        String[] parts = event.getObject().toString().split(": ");
        this.novo.setContrato(parts[0]);
        parts = parts[1].split(" - ");
        this.novo.setDescContrato(parts[1]);
    }

    /**
     * Método para autocompletar a busca por tipos de posto
     *
     * @param query - string digitada pelo usuário
     * @return lista de strings contendo o tipo do posto e a descrição do tipo
     * que correspondam a busca do usuário
     */
    public List<String> ListarTipos(String query) {
        try {
            List<String> retorno = new ArrayList<>();
            this.ctritens = pstservsatmobweb.ListarTipos(this.novo.getCodFil(), this.novo.getContrato(), query, this.persistencia);
            for (CtrItens tipo : this.ctritens) {
                retorno.add(tipo.getTipoPosto() + ", " + tipo.getDescricao());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    /**
     * Define o tipo do posto e a descrição do tipo de posto de um novo posto de
     * serviço
     *
     * @param event - evento de selecão de item
     */
    public void SelecionarTipo(SelectEvent event) {
        String[] parts = event.getObject().toString().split(", ");
        this.novo.setTipoPosto(parts[0]);
        this.novo.setTipoPostoDesc(parts[1]);
    }

    /**
     * Método para autocompletar a consulta por descrição de tipo de posto
     *
     * @param query - string digitada pelo usuário
     * @return lista de string contendo as descrições de tipo de posto
     */
    public List<String> ListarDescTipos(String query) {
        try {
            this.ctritens = pstservsatmobweb.ListarTodosTipos(this.novo.getCodFil(), query, this.persistencia);
            List<String> retorno = new ArrayList<>();
            for (CtrItens tipo : this.ctritens) {
                retorno.add(tipo.getDescricao());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    /**
     * Seleciona a descrição do tipo de posto;
     *
     * @param event - evento de seleção
     */
    public void SelecionarDescTipo(SelectEvent event) {
        this.novo.setTipoPostoDesc((String) event.getObject());
    }

    /**
     * Método para autocompletar a consulta por clientes
     *
     * @param query - string digitada pelo usuário
     * @return lista de objetos Clientes
     */
    public List<Clientes> ListarClientes(String query) {
        try {
            return this.pstservsatmobweb.ListarClientes(this.novo.getCodFil().toString(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    /**
     * Seleciona um cliente
     *
     * @param event - evento de seleção
     */
    public void SelecionarCliente(SelectEvent event) {
        Clientes cli = ((Clientes) event.getObject());
        this.clientesMB.setCodigo(cli.getCodigo());
        this.clientesMB.setNome("");
        this.clientesMB.setNred("");
        this.clientesMB.setAgencia("");
        this.clientesMB.BuscarCliente();
        this.clientes = this.clientesMB.getLista().get(0);
        EnderecoCliente();
        this.novo.setCodCli(this.clientes.getCodigo());
    }

    /**
     * Cadastra um novo posto de serviço e fecha a tela de cadastro em caso de
     * sucesso.
     */
    public void Cadastrar() {
        try {
            this.clientesMB.setCodigo(this.clientes.getCodigo());
            this.clientesMB.setNome("");
            this.clientesMB.setNred("");
            this.clientesMB.setAgencia("");
            this.clientesMB.BuscarCliente();
            this.clientes = this.clientesMB.getLista().get(0);
            //this.novo.setLocal(FuncoesString.RecortaAteEspaço(this.novo.getLocal().toUpperCase() + " " + this.clientes.getNRed().toUpperCase(), 0, 40));
            if (this.novo.getLocal().length() > 40) {
                this.novo.setLocal(this.novo.getLocal().toUpperCase().substring(40));
            }
            this.novo.setSecao(this.novo.getSecao().toUpperCase());
            this.novo.setContrato(this.novo.getContrato().toUpperCase());
            this.novo.setCodFil(this.codFil);
            this.novo.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.novo.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novo.setDt_Alter(LocalDate.now());
            this.novo.setDt_Situacao(LocalDate.now());
            this.novo.setTipoPosto(this.novo.getTipoPosto().toUpperCase());
            this.novo.setCodFil(this.filial.getCodfilAc());
            if (this.endereco.equals("")) {
                throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("Cliente"));
            }
            this.pstservsatmobweb.Incluir(this.novo, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private void lazyGetAllPostos() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            postos = new PstServLazyList(persistencia, codPessoa);
            total = pstservsatmobweb.Contagem(filters, codPessoa, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<PstServ> getAllPostosMenu() {
        if (postos == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("exportarPstServ:tabela");
            filters.replace(CODFIL, codFil);
            filters.replace(SITUACAO, "A");
            mostrarFiliais = false;
            somenteAtivos = true;
            filters.replace(TIPOPOSTO, "");
            filters.replace(SECAO, "");
            filters.replace(CODCLI, "");
            filters.replace(LOCAL, "");
            filters.replace(DESCRICAO, "");
            filters.replace(CONTRATO, "");
            filters.replace(OS, "");
            filters.replace(POSTO, "");

            dt.setFilters(filters);
            postos = new PstServLazyList(persistencia, codPessoa);
        }
        try {
            total = pstservsatmobweb.Contagem(filters, codPessoa, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return postos;
    }

    public void AtualizaTabela() {
        PrimeFaces.current().ajax().update("main:tabela");
    }

    public void AtualizaTabelaMenu() {
        PrimeFaces.current().ajax().update("exportarPstServ:tabela");
    }

    public void PrePesquisa() {
        try {
            this.novo = new PstServ();
            this.novo.setContrato("");
            this.novo.setTipoPosto("");
            this.novo.setTipoPostoDesc("");
            this.novo.setContrato("");
            this.novo.setSecao("");
            this.novo.setDescContrato("");
            this.novo.setLocal("");
            this.clientes = new Clientes();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void PesquisaPaginada() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        if (null != this.novo.getCodFil() && !this.novo.getCodFil().equals(BigDecimal.ZERO)) {
            this.filters.put(CODFIL, this.novo.getCodFil().toPlainString());
            if (this.novo.getCodFil().equals(this.codFil)) {
                this.mostrarFiliais = false;
            } else {
                this.mostrarFiliais = false;
            }
        } else {
            this.filters.replace(CODFIL, "");
            this.mostrarFiliais = true;
        }
        if (null != this.novo.getSituacao()) {
            this.filters.replace(SITUACAO, this.novo.getSituacao());
            if (this.novo.getSituacao().equals("A")) {
                this.somenteAtivos = true;
            } else {
                this.somenteAtivos = false;
            }
        } else {
            this.filters.replace(SITUACAO, "");
            this.somenteAtivos = false;
        }
        if (!this.novo.getTipoPosto().equals("")) {
            this.filters.replace(TIPOPOSTO, "%" + this.novo.getTipoPosto() + "%");
        } else {
            this.filters.replace(TIPOPOSTO, "");
        }
        if (!this.novo.getSecao().equals("")) {
            this.filters.replace(SECAO, "%" + this.novo.getSecao() + "%");
        } else {
            this.filters.replace(SECAO, "");
        }
        if (!this.novo.getCodCli().equals("")) {
            this.filters.replace(CODCLI, "%" + this.novo.getCodCli() + "%");
        } else {
            this.filters.replace(CODCLI, "");
        }
        if (!this.novo.getLocal().equals("")) {
            this.filters.replace(LOCAL, "%" + this.novo.getLocal() + "%");
        } else {
            this.filters.replace(LOCAL, "");
        }
        if (null != this.novo.getDescContrato()) {
            this.filters.replace(DESCRICAO, "%" + this.novo.getDescContrato() + "%");
        } else {
            this.filters.replace(DESCRICAO, "");
        }
        if (!this.novo.getContrato().equals("")) {
            this.filters.replace(CONTRATO, "%" + this.novo.getContrato() + "%");
        } else {
            this.filters.replace(CONTRATO, "");
        }
        lazyGetAllPostos();
        dt.setFirst(0);
    }

    public void pesquisarUnico() {
        limparPesquisa();
        replaceFilter(valorPesquisa);
        lazyGetAllPostos();
    }

    private boolean isInteger(String valor) {
        try {
            Integer.parseInt(valor);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public List<PstServ> listarPostos(String inCodCli) throws Exception {
        filters = new HashMap();
        filters.put(CODFIL, codFil);
        filters.put(SITUACAO, "A");
        filters.put(CODCLI, inCodCli);

        return this.pstservsatmobweb.ListagemPaginada(0, 99999999, filters, this.codPessoa, this.persistencia);
    }

    private void replaceFilter(String valor) {
        try {
            switch (chavePesquisa) {
                case "CODFIL":
                    filters.replace(CODFIL, valor);
                    return;
                case "SITUACAO":
                    filters.replace(SITUACAO, valor);
                    return;
                case "TIPOPOSTO":
                    filters.replace(TIPOPOSTO, "%" + valor + "%");
                    return;
                case "SECAO":
                    filters.replace(SECAO, "%" + valor + "%");
                    return;
                case "CODCLI":
                    filters.replace(CODCLI, "%" + valor + "%");
                    return;
                case "LOCAL":
                    filters.replace(LOCAL, "%" + valor + "%");
                    return;
                case "DESCRICAO":
                    filters.replace(DESCRICAO, "%" + valor + "%");
                    return;
                case "CONTRATO":
                    filters.replace(CONTRATO, "%" + valor + "%");
                    return;
                case "OS":
                    if (this.isInteger(valor)) {
                        filters.replace(OS, valor);
                        return;
                    } else {
                        throw new Exception("DeveSerNumerico");
                    }
                case "POSTO":
                    filters.replace(POSTO, "%" + valor + "%");
                    return;
                default:
                    throw new Exception("CampoNaoExiste");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void LimparFiltros() {
        filters.replace(CODFIL, codFil);
        mostrarFiliais = false;
        filters.replace(SITUACAO, "A");
        somenteAtivos = true;
        filters.replace(TIPOPOSTO, "");
        filters.replace(SECAO, "");
        filters.replace(CODCLI, "");
        filters.replace(LOCAL, "");
        filters.replace(DESCRICAO, "");
        filters.replace(CONTRATO, "");
        filters.replace(OS, "");
        filters.replace(POSTO, "");
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(filters);
        lazyGetAllPostos();
        dt.setFirst(0);
        limparFiltros = false;
    }

    public void limparPesquisa() {
        filters.replace(CODFIL, codFil);
        filters.replace(SITUACAO, "A");
        filters.replace(TIPOPOSTO, "");
        filters.replace(SECAO, "");
        filters.replace(CODCLI, "");
        filters.replace(LOCAL, "");
        filters.replace(DESCRICAO, "");
        filters.replace(CONTRATO, "");
        filters.replace(OS, "");
        filters.replace(POSTO, "");
    }

    /**
     * Excluí um documento do servidor e posteriormente do banco de dados
     *
     * @param pstServDoctos
     */
    public void excluirDocumento(PstServDoctos pstServDoctos) {

        HandleFileDelete(pstServDoctos);

        try {
            this.pstservsatmobweb.excluirDocumento(pstServDoctos, this.persistencia);
            ListarArquivosPosto();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listarInspecoesPosto() {
        try {
            listaLogsSatMobEW = pstservsatmobweb.listaPorSecaoIntervalo(codFil, selecionado.getSecao(), dataInspecao1, dataInspecao2, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void mostrarInspecoes() {
        try {
            if (logsSatMobEWSelecionado == null) {
                throw new Exception(Messages.getMessageS("SelecioneInspecao"));
            } else {
                if (!this.logsSatMobEWSelecionado.getTitulo().equals("RESUMO DO TRABALHO DE CAMPO")) {
                    gerarRelatorio();
                    PrimeFaces.current().executeScript("PF('dlgRelatorio').show();");
                } else {
                    abrirBoletimTrabalho(false);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void gerarRelatorio() throws Exception {
        String foto, idioma = FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
        pstInspecaoSelecionadaDetalhes = new ArrayList<>();

        // Rondas completas
        mapa = null;
        html = Messages.replaceTags(satMobEWSatWeb.getRelatorioInspecao(logsSatMobEWSelecionado, idioma, persistencia));
        titulo = logsSatMobEWSelecionado.getTitulo();

        if (!logsSatMobEWSelecionado.getLatitude().equals("") || !logsSatMobEWSelecionado.getLongitude().equals("")) {
            mapa = new DefaultMapModel();
            mapa.addOverlay(new Marker(
                    new LatLng(
                            new BigDecimal(logsSatMobEWSelecionado.getLatitude()).doubleValue(),
                            new BigDecimal(logsSatMobEWSelecionado.getLongitude()).doubleValue()),
                    logsSatMobEWSelecionado.getFuncionario(), logsSatMobEWSelecionado,
                    "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
            centroMapa = logsSatMobEWSelecionado.getLatitude() + "," + logsSatMobEWSelecionado.getLongitude();
            zoomMapa = "17";
        } else {
            mapa = null;
        }

        List<PstInspecao> pp = satMobEWSatWeb.getDetalhesPstInspecoes(logsSatMobEWSelecionado, persistencia);

        InspecoesMB.PstInspecaoDetalhes aux;
        for (PstInspecao p : pp) {
            aux = new InspecoesMB.PstInspecaoDetalhes();
            aux.setPstInspecao(p);
            aux.getPstInspecao().setResposta(p.getResposta().replace("\\N", "\n"));
            aux.setFotos(Arrays.asList(p.getCaminhoImagem().split(";")));
            if (!aux.getFotos().isEmpty()) {
                if (aux.getFotos().size() == 1 && aux.getFotos().get(0).equals("")) {
                    aux.setFotos(new ArrayList<>());
                } else {
                    aux.setFoto(aux.getFotos().get(0));
                }
            }

            aux.setVideos(Arrays.asList(p.getCaminhoVideo().split(";")));
            if (!aux.getVideos().isEmpty()) {
                if (aux.getVideos().size() == 1 && aux.getVideos().get(0).equals("")) {
                    aux.setVideos(new ArrayList<>());
                } else {
                    aux.setVideo(aux.getVideos().get(0));
                }
            }

            aux.setPosicaoFoto(0);
            aux.setPosicaoVideo(0);

            pstInspecaoSelecionadaDetalhes.add(aux);
        }
    }

    public void abrirBoletimTrabalho(boolean Reload) {
        try {
            if (!Reload || null == pessoaSelecionada) {
                pessoaSelecionada = new Pessoa();
            }
            LogsSatMobEWDao obj = new LogsSatMobEWDao();

            // Carregar Lista de Trabalho
            pstInspecoesBoletimTrabalho = obj.listaBoletimTrabalho(dataTela, codFil, pessoaSelecionada.getCodigo(), persistencia);
            StringBuilder str = new StringBuilder();

            for (PstInspecao pstInspecoesBoletimTrabalho1 : pstInspecoesBoletimTrabalho) {
                str.append("ArrayBoletimTrabalho.push({");
                str.append("  Nred: '").append(pstInspecoesBoletimTrabalho1.getLocal()).append("',");
                str.append("  Latitude: '").append(pstInspecoesBoletimTrabalho1.getLatitude()).append("',");
                str.append("  Longitude: '").append(pstInspecoesBoletimTrabalho1.getLongitude()).append("',");
                str.append("  Pergunta: '").append(pstInspecoesBoletimTrabalho1.getPergunta()).append("',");
                str.append("  Resposta: '").append(pstInspecoesBoletimTrabalho1.getResposta()).append("'");
                str.append("});");
            }

            listaTrabalhos = str.toString();

            // Carregar Clientes (PINs)
            List<Clientes> lstClientes = obj.listaBoletimTrabalhoClientes(dataTela, codFil, pessoaSelecionada.getCodigo(), persistencia);
            markers = "";
            centro = "";
            int Contador = 0;

            for (Clientes lstClientes1 : lstClientes) {
                if (centro.equals("")) {
                    centro = "{ lat: " + lstClientes1.getLatitude() + ", lng: " + lstClientes1.getLongitude() + " }";
                }

                markers += MARCADOR.replace("@indice", "pin_" + Integer.toString(Contador))
                        .replace("@lat", lstClientes1.getLatitude()).replace("@lng", lstClientes1.getLongitude())
                        .replace("@title", getMessageS("Local") + ": " + lstClientes1.getNRed() + "\\n" + getMessageS("Endereco") + ": " + lstClientes1.getEnde() + "\\n" + getMessageS("Bairro") + ": " + lstClientes1.getBairro() + "\\n" + getMessageS("Cidade") + ": " + lstClientes1.getCidade())
                        .replace("@icon", "https://mobile.sasw.com.br:9091/satmobile/pins/icone_mobile_supervisor.png");

                Contador++;
            }

            // Carregar Pessoas
            pessoas = obj.listaBoletimTrabalhoPessoas(dataTela, codFil, persistencia);

            // Abrir Modal
            if (!Reload) {
                PrimeFaces.current().ajax().update("formBoletimTrabalho");
                PrimeFaces.current().executeScript("PF('dlgBoletimTrabalho').show()");
            }
            PrimeFaces.current().executeScript("setTimeout(function(){ CarregarListaTrabalho('" + Integer.toString(lstClientes.size()) + "'); }, 500)");

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void selecionarPessoa(SelectEvent event) {
        abrirBoletimTrabalho(true);
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public List<PstServ> getListaPosto() {
        return listaPosto;
    }

    public void setListaPosto(List<PstServ> listaPosto) {
        this.listaPosto = listaPosto;
    }

    public PstServ getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(PstServ selecionado) {
        this.selecionado = selecionado;
    }

    public String getSecao() {
        return secao;
    }

    public void setSecao(String secao) {
        this.secao = secao;
    }

    public PstServ getNovo() {
        return novo;
    }

    public void setNovo(PstServ novo) {
        this.novo = novo;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public ClientesMB getClientesMB() {
        return clientesMB;
    }

    public SupervisoesMB getSupervisaoMB() {
        return supervisaoMB;
    }

    public void setClientesMB(ClientesMB clientesMB) {
        this.clientesMB = clientesMB;
    }

    public void setSupervisaoMB(SupervisoesMB supervisaoMB) {
        this.supervisaoMB = supervisaoMB;
    }

    public List<Clientes> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<Clientes> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public String getCidadePais() {
        return cidadePais;
    }

    public void setCidadePais(String cidadePais) {
        this.cidadePais = cidadePais;
    }

    public List<String> getSequencias() {
        return sequencias;
    }

    public void setSequencias(List<String> sequencias) {
        this.sequencias = sequencias;
    }

    public List<ContrVig> getContrvigs() {
        return contrvigs;
    }

    public void setContrvigs(List<ContrVig> contrvigs) {
        this.contrvigs = contrvigs;
    }

    public List<CtrItens> getCtritens() {
        return ctritens;
    }

    public void setCtritens(List<CtrItens> ctritens) {
        this.ctritens = ctritens;
    }

    public Boolean getMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(Boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public Boolean getSomenteAtivos() {
        return somenteAtivos;
    }

    public void setSomenteAtivos(Boolean somenteAtivos) {
        this.somenteAtivos = somenteAtivos;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public boolean iseFilial() {
        return eFilial;
    }

    public void seteFilial(boolean eFilial) {
        this.eFilial = eFilial;
    }

    public boolean iseCodPosto() {
        return eCodPosto;
    }

    public void seteCodPosto(boolean eCodPosto) {
        this.eCodPosto = eCodPosto;
    }

    public boolean isePosto() {
        return ePosto;
    }

    public void setePosto(boolean ePosto) {
        this.ePosto = ePosto;
    }

    public boolean iseTipo() {
        return eTipo;
    }

    public void seteTipo(boolean eTipo) {
        this.eTipo = eTipo;
    }

    public boolean iseDescTipo() {
        return eDescTipo;
    }

    public void seteDescTipo(boolean eDescTipo) {
        this.eDescTipo = eDescTipo;
    }

    public boolean iseInterfExt() {
        return eInterfExt;
    }

    public void seteInterfExt(boolean eInterfExt) {
        this.eInterfExt = eInterfExt;
    }

    public boolean iseSituacao() {
        return eSituacao;
    }

    public void seteSituacao(boolean eSituacao) {
        this.eSituacao = eSituacao;
    }

    public boolean iseDtSituacao() {
        return eDtSituacao;
    }

    public void seteDtSituacao(boolean eDtSituacao) {
        this.eDtSituacao = eDtSituacao;
    }

    public boolean iseOperador() {
        return eOperador;
    }

    public void seteOperador(boolean eOperador) {
        this.eOperador = eOperador;
    }

    public boolean iseDtAlter() {
        return eDtAlter;
    }

    public void seteDtAlter(boolean eDtAlter) {
        this.eDtAlter = eDtAlter;
    }

    public boolean iseHrAlter() {
        return eHrAlter;
    }

    public void seteHrAlter(boolean eHrAlter) {
        this.eHrAlter = eHrAlter;
    }

    public List<PstServDoctos> getListaDocumentos() {
        return listaDocumentos;
    }

    public void setListaDocumentos(List<PstServDoctos> listaDocumentos) {
        this.listaDocumentos = listaDocumentos;
    }

    public PstServDoctos getDocumento() {
        return documento;
    }

    public void setDocumento(PstServDoctos documento) {
        this.documento = documento;
    }

    public StreamedContent getDownload() {
        return download;
    }

    public void setDownload(StreamedContent download) {
        this.download = download;
    }

    public String getDataRonda1() {
        return dataRonda1;
    }

    public void setDataRonda1(String dataRonda1) {
        this.dataRonda1 = dataRonda1;
    }

    public String getDataRonda2() {
        return dataRonda2;
    }

    public void setDataRonda2(String dataRonda2) {
        this.dataRonda2 = dataRonda2;
    }

    public List<Rondas> getRondas() {
        return rondas;
    }

    public void setRondas(List<Rondas> rondas) {
        this.rondas = rondas;
    }

    public Rondas getRonda() {
        return ronda;
    }

    public void setRonda(Rondas ronda) {
        this.ronda = ronda;
    }

    public List<PstDepen> getDependencias() {
        return dependencias;
    }

    public void setDependencias(List<PstDepen> dependencias) {
        this.dependencias = dependencias;
    }

    public PstDepen getDependencia() {
        return dependencia;
    }

    public void setDependencia(PstDepen dependencia) {
        this.dependencia = dependencia;
    }

    public List<RHPonto> getPontos() {
        return pontos;
    }

    public void setPontos(List<RHPonto> pontos) {
        this.pontos = pontos;
    }

    public RHPonto getPonto() {
        return ponto;
    }

    public void setPonto(RHPonto ponto) {
        this.ponto = ponto;
    }

    public String getDataPonto() {
        return dataPonto;
    }

    public void setDataPonto(String dataPonto) {
        this.dataPonto = dataPonto;
    }

    public String getDataRelatorio1() {
        return dataRelatorio1;
    }

    public void setDataRelatorio1(String dataRelatorio1) {
        this.dataRelatorio1 = dataRelatorio1;
    }

    public String getDataRelatorio2() {
        return dataRelatorio2;
    }

    public void setDataRelatorio2(String dataRelatorio2) {
        this.dataRelatorio2 = dataRelatorio2;
    }

    public List<TmktDetPstPstServClientes> getRelatorios() {
        return relatorios;
    }

    public void setRelatorios(List<TmktDetPstPstServClientes> relatorios) {
        this.relatorios = relatorios;
    }

    public TmktDetPstPstServClientes getRelatorio() {
        return relatorio;
    }

    public void setRelatorio(TmktDetPstPstServClientes relatorio) {
        this.relatorio = relatorio;
    }

    public String getDataSupervisao1() {
        return dataSupervisao1;
    }

    public void setDataSupervisao1(String dataSupervisao1) {
        this.dataSupervisao1 = dataSupervisao1;
    }

    public String getDataSupervisao2() {
        return dataSupervisao2;
    }

    public void setDataSupervisao2(String dataSupervisao2) {
        this.dataSupervisao2 = dataSupervisao2;
    }

    public String getEnderecoRelatorio() {
        return enderecoRelatorio;
    }

    public void setEnderecoRelatorio(String enderecoRelatorio) {
        this.enderecoRelatorio = enderecoRelatorio;
    }

    public String getFotoRelatorio() {
        return fotoRelatorio;
    }

    public void setFotoRelatorio(String fotoRelatorio) {
        this.fotoRelatorio = fotoRelatorio;
    }

    public int getPosFotoRelatorio() {
        return posFotoRelatorio;
    }

    public void setPosFotoRelatorio(int posFotoRelatorio) {
        this.posFotoRelatorio = posFotoRelatorio;
    }

    public double getDistPstSup() {
        return distPstSup;
    }

    public void setDistPstSup(double distPstSup) {
        this.distPstSup = distPstSup;
    }

    public String getCoordenadas() {
        return coordenadas;
    }

    public void setCoordenadas(String coordenadas) {
        this.coordenadas = coordenadas;
    }

    public MapModel getPin() {
        return pin;
    }

    public void setPin(MapModel pin) {
        this.pin = pin;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public LazyDataModel<PstServ> getAllPostos() {
        if (postos == null) {
            lazyGetAllPostos();
        }

        return postos;
    }

    public String getHtml() {
        return html;
    }

    public String getTitulo() {
        return titulo;
    }

    public String getCentroMapa() {
        return centroMapa;
    }

    public String getZoomMapa() {
        return zoomMapa;
    }

    public LogsSatMobEW getLogsSatMobEWSelecionado() {
        return logsSatMobEWSelecionado;
    }

    public List<InspecoesMB.PstInspecaoDetalhes> getPstInspecaoSelecionadaDetalhes() {
        return pstInspecaoSelecionadaDetalhes;
    }

    public MapModel getMapa() {
        return mapa;
    }

    public List<LogsSatMobEW> getListaLogsSatMobEW() {
        return listaLogsSatMobEW;
    }

    public String getDataInspecao1() {
        return dataInspecao1;
    }

    public void setDataInspecao1(String dataInspecao1) {
        this.dataInspecao1 = dataInspecao1;
    }

    public String getDataInspecao2() {
        return dataInspecao2;
    }

    public void setDataInspecao2(String dataInspecao2) {
        this.dataInspecao2 = dataInspecao2;
    }

    public void setLogsSatMobEWSelecionado(LogsSatMobEW logsSatMobEWSelecionado) {
        this.logsSatMobEWSelecionado = logsSatMobEWSelecionado;
    }

    public String getListaTrabalhos() {
        return listaTrabalhos;
    }

    public String getMarkers() {
        return markers;
    }

    public String getCentro() {
        return centro;
    }

    public List<Pessoa> getPessoas() {
        return pessoas;
    }

    public Pessoa getPessoaSelecionada() {
        return pessoaSelecionada;
    }

    public void setPessoaSelecionada(Pessoa pessoaSelecionada) {
        this.pessoaSelecionada = pessoaSelecionada;
    }

}

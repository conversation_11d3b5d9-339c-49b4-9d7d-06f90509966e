/*
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.Mascaras;
import static br.com.sasw.utils.Mascaras.removeMascaraData;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.ResolverStyle;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorData")
public class ConversorData implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return removeMascaraData(value);
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        DateTimeFormatter df;
        Mascaras m = new Mascaras();
        DateTimeFormatter data = DateTimeFormatter.ofPattern(m.getPadraoData());
        LocalDate dia = null;
        try {
            df = DateTimeFormatter.ofPattern("yyyyMMdd");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        return value.toString();
    }
}

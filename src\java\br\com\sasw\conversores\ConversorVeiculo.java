/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import SasBeans.Veiculos;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorVeiculo")
public class ConversorVeiculo implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        Veiculos veiculos = new Veiculos();
        veiculos.setPlaca(value);

        return veiculos;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            return ((Veiculos) value).getPlaca();
        } catch (Exception e) {
            return null;
        }
    }

}

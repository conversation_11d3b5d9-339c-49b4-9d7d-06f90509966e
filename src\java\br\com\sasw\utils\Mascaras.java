/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.utils;

import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.getSimboloMoeda;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.ResolverStyle;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.SessionScoped;

/**
 *
 * <AUTHOR>
 */
@ManagedBean
@SessionScoped
public class Mascaras implements Serializable {

    private String mascaraCPF, mascaraCNPJ, mascaraCEP, mascaraFone, mascaraHora;
    private String padraoData, padraoHora;
    private Locale locale;
    private static Locale sLocale;

    public static String CNPJ(String cnpj) {
        sLocale = LocaleController.getsCurrentLocale();
        try {
            switch (sLocale.getLanguage().toUpperCase()) {
                case "PT":
                    if (cnpj.length() == 14) {
                        return FuncoesString.formatarString(cnpj, "##.###.###/####-##");
                    }
            }

        } catch (Exception e) {
        }
        return cnpj;
    }

    public static String CPF(String cpf) {
        if (null == cpf) {
            return "";
        }
        sLocale = LocaleController.getsCurrentLocale();
        try {
            switch (sLocale.getLanguage().toUpperCase()) {
                case "PT":
                    if (cpf.length() == 11) {
                        return FuncoesString.formatarString(cpf, "###.###.###-##");
                    }
                case "EN":
                    if (cpf.length() == 9) {
                        return FuncoesString.formatarString(cpf, "###-##-####");
                    }
            }

        } catch (Exception e) {
        }
        return cpf;
    }

    public static String CEP(String cep) {
        sLocale = LocaleController.getsCurrentLocale();
        try {
            switch (sLocale.getLanguage().toUpperCase()) {
                case "PT":
                    return FuncoesString.formatarString(cep, "##.###-###");
                case "EN":
                    break;
            }

        } catch (Exception e) {
        }
        return cep;
    }

    public static String Fone(String fone) {
        sLocale = LocaleController.getsCurrentLocale();
        try {
            switch (sLocale.getLanguage().toUpperCase()) {
                case "PT":
                    switch (fone.length()) {
                        case 7:
                            return FuncoesString.formatarString(fone, "###-####");
                        case 8:
                            return FuncoesString.formatarString(fone, "####-####");
                        case 9:
                            return FuncoesString.formatarString(fone, "(##) ###-####");
                        case 10:
                            return FuncoesString.formatarString(fone, "(##) ####-####");
                        case 11:
                            return FuncoesString.formatarString(fone, "(##) #####-####");
                        case 12:
                            return FuncoesString.formatarString(fone, "+############");
                        case 13:
                            return FuncoesString.formatarString(fone, "+#############");
                        default:
                            break;
                    }
                    break;

                /*case "EN":
                    return FuncoesString.formatarString(fone, "(###) ###-####");*/
            }
        } catch (Exception e) {
        }
        return fone;
    }

    public static String Data(String dat) {
        DateTimeFormatter df;
        Mascaras m = new Mascaras();
        DateTimeFormatter data = DateTimeFormatter.ofPattern(m.getPadraoData());
        LocalDate dia = null;
        try {
            df = DateTimeFormatter.ofPattern("yyyyMMdd");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(dat, df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(dat, df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(dat, df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(dat, df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(dat, df);
            return dia.format(data);
        } catch (Exception e) {
        }
        return dat;
    }

    public static String Hora(String hora) {

        DateTimeFormatter df;
        Mascaras mascaras = new Mascaras();
        try {
            df = DateTimeFormatter.ofPattern("HH:mm");
            return LocalTime.parse(hora, df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHora()));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("HH:mm:ss");
            return LocalTime.parse(hora, df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHoraSegundos()));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("HHmm");
            return LocalTime.parse(hora, df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHora()));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("H:mm:");
            return LocalTime.parse(hora, df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHora()));
        } catch (Exception e) {
        }

        return hora;
    }

    public static String HoraSegundos(String hora) {

        DateTimeFormatter df;
        Mascaras mascaras = new Mascaras();
        try {
            df = DateTimeFormatter.ofPattern("HH:mm:ss");
            return LocalTime.parse(hora, df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHoraSegundos()));
        } catch (Exception e) {
        }

        return hora;
    }

    public static String Moeda(String entrada, String moeda) {
        return formatarStringMoeda(entrada, sLocale, getSimboloMoeda(moeda));
    }

    public static String Moeda(String val) {
        sLocale = LocaleController.getsCurrentLocale();
        return formatarStringMoeda(val, sLocale, Messages.getMessageS("$"));
        
//        NumberFormat nf = NumberFormat.getNumberInstance(sLocale);
//        String moeda, valor;
//        int casas = 0;
//        BigDecimal mbd = new BigDecimal("0.00");
//        try {
//            mbd = mbd.add(new BigDecimal(val.replaceAll(",", ".")));
//        } catch (Exception x) {
//            mbd = new BigDecimal("0.00");
//        }
//        valor = mbd.setScale(2, RoundingMode.UP).toString();
//        casas = valor.length() - 3;
//        moeda = valor.substring(casas).replace(".", ",");
//        for (int i = 1; i <= casas; i++) {
//            if (i % 3 == 0) {
//                moeda = "." + valor.substring(casas - i, casas - (i - 1)) + moeda;
//            } else {
//                moeda = valor.substring(casas - i, casas - (i - 1)) + moeda;
//            }
//        }
//        if (moeda.indexOf(".") == 0) {
//            moeda = moeda.substring(1);
//        }
//        switch (sLocale.getLanguage()) {
//            case "en":
//                moeda = moeda.replace(",", "x");
//                moeda = moeda.replace(".", ",");
//                moeda = moeda.replace("x", ".");
//                moeda = Messages.getMessageS("$") + moeda;
//                break;
//            default:
//                DecimalFormat pt = (DecimalFormat) nf;
//                pt.applyPattern("###,##0.00");
//                moeda = Messages.getMessageS("$") + moeda;
//        }
//        return moeda;
    }

    public static String removeMascara(String string) {
        if (null == string) {
            return "";
        } else {
            return string.replace("-", "").replace(".", "").replace("_", "").replace("(", "").replace(")", "").replace(" ", "").replace("/", "");
        }
    }

    public static String removeMascaraMoeda(String val) {
        sLocale = LocaleController.getsCurrentLocale();
        NumberFormat nf = NumberFormat.getNumberInstance(sLocale);
        String moeda, valor;
        int casas = 0;
        BigDecimal mbd = new BigDecimal("0.00");
        try {
            mbd = mbd.add(new BigDecimal(val.replaceAll(",", ".").replace(getMessageS("$"), "")));
        } catch (Exception x) {
            mbd = new BigDecimal("0.00");
        }
        valor = mbd.setScale(2, RoundingMode.UP).toString();
        casas = valor.length() - 3;
        moeda = valor.substring(casas).replace(".", ",");
        for (int i = 1; i <= casas; i++) {
            if (i % 3 == 0) {
                moeda = "." + valor.substring(casas - i, casas - (i - 1)) + moeda;
            } else {
                moeda = valor.substring(casas - i, casas - (i - 1)) + moeda;
            }
        }
        if (moeda.indexOf(".") == 0) {
            moeda = moeda.substring(1);
        }
        switch (sLocale.getLanguage()) {
            case "en":
                moeda = moeda.replace(",", "x");
                moeda = moeda.replace(".", ",");
                moeda = moeda.replace("x", ".");
                moeda = Messages.getMessageS("$") + moeda;
                break;
            default:
                DecimalFormat pt = (DecimalFormat) nf;
                pt.applyPattern("###,##0.00");
                moeda = Messages.getMessageS("$") + moeda;
        }
        return moeda;
    }

    public static String removeMascaraData(String data) {
        String d = data;
        try {
            sLocale = LocaleController.getsCurrentLocale();
            DateTimeFormatter f = DateTimeFormatter.ofPattern("yyyyMMdd");
            DateTimeFormatter df;
            LocalDate dia;
            switch (sLocale.getLanguage().toUpperCase()) {
                case "PT":
                    df = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    df.withResolverStyle(ResolverStyle.LENIENT);
                    dia = LocalDate.parse(data, df);
                    d = dia.format(f);
                    break;
                case "EN":
                    df = DateTimeFormatter.ofPattern("MM/dd/yyyy");
                    df.withResolverStyle(ResolverStyle.LENIENT);
                    dia = LocalDate.parse(data, df);
                    d = dia.format(f);
                    break;
                default:
                    df = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    df.withResolverStyle(ResolverStyle.LENIENT);
                    dia = LocalDate.parse(data, df);
                    d = dia.format(f);
                    break;
            }
        } catch (Exception e) {
        }
        return d;
    }

    public static Date removeMascaraData2(String data) throws ParseException {
        String d = data;
        try {
            sLocale = LocaleController.getsCurrentLocale();
            DateTimeFormatter f = DateTimeFormatter.ofPattern("yyyyMMdd");
            DateTimeFormatter df;
            LocalDate dia;
            switch (sLocale.getLanguage().toUpperCase()) {
                case "PT":
                    df = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    dia = LocalDate.parse(data, df);
                    d = dia.format(f);
                    break;
                case "EN":
                    df = DateTimeFormatter.ofPattern("MM/dd/yyyy");
                    dia = LocalDate.parse(data, df);
                    d = dia.format(f);
                    break;
                default:
                    df = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    dia = LocalDate.parse(data, df);
                    d = dia.format(f);
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new SimpleDateFormat("yyyyMMdd").parse(d);
    }

    public static LocalDate removeMascaraLocalDateToLocalDate(String data) {
        try {
            sLocale = LocaleController.getsCurrentLocale();
            DateTimeFormatter df;
            switch (sLocale.getLanguage().toUpperCase()) {
                case "PT":
                    df = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    return LocalDate.parse(data, df);
                case "EN":
                    df = DateTimeFormatter.ofPattern("MM/dd/yyyy");
                    return LocalDate.parse(data, df);
                default:
                    df = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    return LocalDate.parse(data, df);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String removeMascaraHora(String hora) {
        String h = hora;
        try {
            sLocale = LocaleController.getsCurrentLocale();
            SimpleDateFormat df;
            Calendar dia = Calendar.getInstance();
            switch (sLocale.getLanguage().toUpperCase()) {
                case "PT":
                    df = new SimpleDateFormat("HH:mm");
                    break;
                case "EN":
                    df = new SimpleDateFormat("hh:mm a");
                    break;
                default:
                    df = new SimpleDateFormat("HH:mm");
                    break;
            }
            df.setLenient(true);
            dia.setTime(df.parse(hora));
            h = String.format("%02d", dia.get(Calendar.HOUR_OF_DAY)) + ":" + String.format("%02d", dia.get(Calendar.MINUTE));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return h;
    }

    public String getMascaraHora() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                mascaraHora = "99:99";
                break;
            case "EN":
                mascaraHora = "99:99 aa";
                break;
            default:
                mascaraHora = "99:99";
        }
        return mascaraHora;
    }

    public String getMascaraMoeda() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                mascaraHora = "99:99";
                break;
            case "EN":
                mascaraHora = "99:99 aa";
                break;
            default:
                mascaraHora = "99:99";
        }
        return mascaraHora;
    }

    public String getMascaraCPF() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                mascaraCPF = "999.999.999-99";
                break;
            case "EN":
                mascaraCPF = "999-99-9999";
                break;
            default:
                mascaraCPF = "9?*********";
        }
        return mascaraCPF;
    }

    public String getMascaraCNPJ() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                mascaraCNPJ = "99.999.999/9999-99";
                break;
            default:
                mascaraCNPJ = "9?******************99";
        }
        return mascaraCNPJ;
    }

    public String getMascaraCEP() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                mascaraCEP = "99.999-999";
                break;
            case "EN":
                mascaraCEP = "99999";
                break;
            default:
                mascaraCEP = "99999";
        }
        return mascaraCEP;
    }

    /** 
     * Tamanho máximo do campo de CEP/Zip Code
     * Note que o banco atualmente aceita no máximo 8 chars
     * @return 
     */
    public int getMaxLengthCEP() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                return 8;
            case "EN":
                return 5;
            default:
                return 5;
        }
    }

    public String getMascaraFone() {
        locale = LocaleController.getsCurrentLocale();
        mascaraFone = "";
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                mascaraFone = "(99) 99999999?9";
                break;
            /*case "EN":
                mascaraFone = "(999) 999-9999";
                break;*/
            default:
                mascaraFone = "9?*********9";
        }
        return mascaraFone;
    }

    public String getPadraoData() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                padraoData = "dd/MM/yyyy";
                break;
            case "EN":
                padraoData = "MM/dd/yyyy";
                break;
            default:
                padraoData = "dd/MM/yyyy";
        }
        return padraoData;
    }

    public static String getPadraoDataS() {
        String padraoDataS;
        sLocale = LocaleController.getsCurrentLocale();
        switch (sLocale.getLanguage().toUpperCase()) {
            case "PT":
                padraoDataS = "dd/MM/yyyy";
                break;
            case "EN":
                padraoDataS = "MM/dd/yyyy";
                break;
            default:
                padraoDataS = "dd/MM/yyyy";
        }
        return padraoDataS;
    }

    public String getPadraoHora() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                padraoHora = "HH:mm";
                break;
            case "EN":
                padraoHora = "hh:mm a";
                break;
            default:
                padraoHora = "HH:mm";
        }
        return padraoHora;
    }

    public String getPadraoHoraSegundos() {
        locale = LocaleController.getsCurrentLocale();
        switch (locale.getLanguage().toUpperCase()) {
            case "PT":
                padraoHora = "HH:mm:ss";
                break;
            case "EN":
                padraoHora = "hh:mm:ss a";
                break;
            default:
                padraoHora = "HH:mm:ss";
        }
        return padraoHora;
    }

    public static String getPadraoHoraS() {
        String padraoHoraS;
        sLocale = LocaleController.getsCurrentLocale();
        switch (sLocale.getLanguage().toUpperCase()) {
            case "PT":
                padraoHoraS = "HH:mm";
                break;
            case "EN":
                padraoHoraS = "hh:mm a";
                break;
            default:
                padraoHoraS = "HH:mm";
        }
        return padraoHoraS;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDoctos {

    private String Andamento;
    private String Data;
    private String Hora;
    private String TipoCont;
    private BigDecimal CodPessoa;
    private String Secao;
    private String CodFil;
    private String Historico;
    private String Detalhes;
    private Integer QdeFotos;
    private String Latitude;
    private String Longitude;
    private String Precisao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Situacao;
    private String Fotos;
    private String Codigo;

    public RelatorioDoctos() {
        this.Andamento = "0";
        this.Data = "";
        this.Hora = "";
        this.TipoCont = "R";
        this.CodPessoa = new BigDecimal("0");
        this.Secao = "";
        this.CodFil = "";
        this.Historico = "";
        this.Detalhes = "";
        this.QdeFotos = 0;
        this.Latitude = "";
        this.Longitude = "";
        this.Precisao = "0";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
        this.Situacao = "OK";
        this.Fotos = "";
        this.Codigo = "";
    }

    public String getAndamento() {
        return Andamento;
    }

    public void setAndamento(String Andamento) {
        this.Andamento = Andamento;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getTipoCont() {
        return TipoCont;
    }

    public void setTipoCont(String TipoCont) {
        this.TipoCont = TipoCont;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getHistorico() {
        return Historico;
    }

    public void setHistorico(String Historico) {
        this.Historico = Historico;
    }

    public String getDetalhes() {
        return Detalhes;
    }

    public void setDetalhes(String Detalhes) {
        this.Detalhes = Detalhes;
    }

    public Integer getQdeFotos() {
        return QdeFotos;
    }

    public void setQdeFotos(Integer QdeFotos) {
        this.QdeFotos = QdeFotos;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getPrecisao() {
        return Precisao;
    }

    public void setPrecisao(String Precisao) {
        this.Precisao = Precisao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getFotos() {
        return Fotos;
    }

    public void setFotos(String Fotos) {
        this.Fotos = Fotos;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

}

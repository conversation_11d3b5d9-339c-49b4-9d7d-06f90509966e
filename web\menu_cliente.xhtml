<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <link type="text/css" href="assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/animate.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/recursoshumanos/portalrh.css" rel="stylesheet" />
            <script src="assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <script
                src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}">
            </script>
            <link type="text/css" href="assets/css/font-awesome.min.css" rel="stylesheet" />
            <style>
                .jconfirm-content{
                    display: flex;
                }

                .jconfirm-title{
                    color: #3498db!important
                }

                .jconfirm-closeIcon{
                    color: #333 !important;
                }

                [id*="chkCorporativo"]{
                    top: 26px !important;
                    position: absolute;
                    right: 10px;
                    font-weight: bold;
                    color: #3C8DBC !important;
                    border: thin solid #3C8DBC !important;
                    border-radius: 30px;
                    padding: 9px 0px 4px 17px !important;
                    background-color: #FFF !important;
                    text-align: center !important;
                    box-shadow: 2px 2px 3px #ccc;
                }

                .ui-datatable-scrollable-body{
                    height: 200px !important;
                }


                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    [id*="tabelaCxForte"] thead tr th:nth-child(1),
                    [id*="tabelaCxForte"] tbody tr td:nth-child(1){
                        min-width: 130px !important;
                        width:130px !important;
                        max-width:130px !important;
                        text-align: center !important
                    }
                }

                @media only screen and (max-width: 701px) and (min-width: 10px) {
                    .divBt{
                        padding-top: 10px !important
                    }
                    .ui-column-title{
                        width: 50% !important;
                        text-align: right !important;
                    }

                    [id*="tabelaCxForte"] span:last-child{
                        width: 40% !important;
                        text-align: left !important;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    [class*="ui-datatable-scrollable-body"],
                    .ui-datatable-scrollable-body,
                    [id*="formCaixaForteCustodia:tabelaCxForte"] > div:nth-child(2){
                        min-height: 250px !important;
                        height: 250px !important;
                        max-height: 250px !important;
                        overflow-x: none !important;
                        overflow-y: auto !important;
                    }

                    .NegDestaque{
                        font-weight: bold !important;
                    }

                    [id*="pnlFundoCustodia"]{

                    }
                }

                [id*="formCaixaForteCustodia"]:eq(0){
                    height: 255px !important;
                    overflow: auto !Important;
                }

                [id*="tabelaCxForte"]  tr td{
                    white-space: nowrap !important;
                }

                [id*="formGuiasAssina"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                @media(max-width: 550px){
                    #divPortalCliente i{
                        margin-right: 5px !important;
                    }

                    #divPortalCliente{
                        font-size: 10pt !important;
                        padding-top: 40px !important;
                    }

                    #logoSatMob{
                        min-height: 30px !important;
                        height: 30px !important;
                        max-height: 30px !important;
                        bottom: 60px !important;
                    }

                    .btMenu{
                        zoom: 0.75 !important;
                    }
                }
            </style>
        </h:head>
        <h:body id="h" style="background: #FFF; background-color: #FFF">
            <f:metadata>
                <f:viewAction action="#{funcoesadm.PersistenciaSatellite(login.satellite)}"/>
            </f:metadata>
            
            <div class="sate-icon-wrapper" style="height:95% !important; overflow:hidden !important; text-alifgn: center; top: 0px !important">
                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 100px; background: #DDD; border-bottom: thin solid #CCC; box-shadow: 2px 2px 3px #DDD">
                    <div class="col-md-6 col-sm-6 col-xs-6" style="padding-top: 0px">
                        <img src="https://mobile.sasw.com.br:9091/satmobile/logos/logo_#{login.pp.empresa}.png" alt="logocliente" style="max-height: 100px; margin: 0px" />
                    </div>
                    <div id="divPortalCliente" class="col-md-6 col-sm-6 col-xs-6" style="text-align: right; font-size: 20pt; font-weight: bold; color: #000; padding-top: 31px; color: #333; text-shadow: 1px 1px #FFF">
                        <i class="fa fa-briefcase" aria-hidden="true" style="margin-right: 15px"></i>#{localemsgs.PortalCliente}
                    </div>
                </div>
                <p:growl id="msgs"/>
                <p:growl id="msgDeatalhada" showDetail="true" />
                <h:form>

                    <div class="row col-md-6 col-sm-7 col-md-12" style="float:inherit; top:0; right: 0; left: 0; margin: 0 auto; margin-top: 120px; max-width: 600px">
                        <div class="col-md-12 col-xs-12 col-sm-12" style="background-color: #FFF; height: 42px; border: thin solid #CCC; color: #000; padding-top: 8px">
                            <label style="font-size: 14pt !important; margin-left: 4px;"><i class="fa fa-user"></i></label>
                            <label style="font-size: 12pt !important; font-weight: 500; margin-left: 8px;">#{login.usuario.nome}</label>
                        </div>
                    </div>
                    
                    <div class="row col-md-6 col-sm-7 col-md-12" style="float:inherit; top:0; right: 0; left: 0; margin: 0 auto; margin-top: 10px; max-width: 600px">

                        <div class="col-md-12 col-xs-12 col-sm-12" style="background-color: #DDD; height: 78px">
                            <label style="margin-top: 10px; color: #000"><i class="fa fa-filter" aria-hidden="true" style="margin-right: 10px"></i>#{localemsgs.UnidadesCliente}</label>
                            <p:selectOneMenu id="param" value="#{login.selecionado}" converter="omnifaces.SelectItemsConverter"
                                             filter="true" filterMatchMode="contains">

                                <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}"/>
                                <f:selectItems value="#{login.clientes}" var="clientesx"
                                               itemLabel="#{clientesx.nomeCli}"  noSelectionValue="Selecione"/>

                                <p:ajax event="itemSelect"
                                        listener="#{login.selecionarCliente}"
                                        partialSubmit="true"
                                        update="msgs"/>
                            </p:selectOneMenu>
                        </div>

                        <center>
                            <div class="col-md-4 col-sm-4 col-xs-4 btMenu" style="margin-top: 20px;height: 140px; text-align: center !important;">
                                <div class="ui-grid-row">
                                    <p:commandLink action="#{login.trocarOrigem}" update="msgs" >
                                        <p:graphicImage url="assets/img/icone_satmob_fopag_G.png" width="120" style="background: white; border-radius: 5px; border: 1px solid #c7c7c7; padding: 20px" />
                                    </p:commandLink>
                                </div>
                                <div class="ui-grid-row">
                                    <p:commandLink action="#{login.trocarOrigem}" update="msgs">
                                        <p:outputLabel value="#{localemsgs.GuiasEgtv}"
                                                       style="color:#022a48; width: 100%; text-align: center; margin-top: 10px;"/>
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="col-md-4 col-sm-4 col-xs-4 btMenu" style="margin-top: 20px;height: 140px; text-align: center !important;">
                                <div class="ui-grid-row">
                                    <p:commandLink onclick="return ValidarAcesso('10101', 'operacoes/pedidos.xhtml?faces-redirect=true', '#{login.retornaPermissao('59901')?'S':'N'}')">
                                        <p:graphicImage url="assets/img/icone_pedidos.png" width="120" style="background: white; border-radius: 5px; border: 1px solid #c7c7c7; padding: 20px" />
                                    </p:commandLink>
                                </div>
                                <div class="ui-grid-row">
                                    <p:commandLink onclick="return ValidarAcesso('10101', 'operacoes/pedidos.xhtml?faces-redirect=true', '#{login.retornaPermissao('59901')?'S':'N'}')">
                                        <p:outputLabel value="#{localemsgs.Pedidos}"
                                                       style="color:#022a48; width: 100%; text-align: center; margin-top: 10px;"/>
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="col-md-4 col-sm-4 col-xs-4 btMenu" style="margin-top: 20px;height: 140px; text-align: center !important;">
                                <div class="ui-grid-row">
                                    <p:commandLink onclick="return AbrirMapaTesouraria()">
                                        <p:graphicImage url="assets/img/icone_tesouraria.png" width="120" style="background: white; border-radius: 5px; border: 1px solid #c7c7c7; padding: 20px" />
                                    </p:commandLink>
                                </div>
                                <div class="ui-grid-row">
                                    <p:commandLink onclick="return AbrirMapaTesouraria()">
                                        <p:outputLabel value="#{localemsgs.Tesouraria}"
                                                       style="color:#022a48; width: 100%; text-align: center; margin-top: 10px;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </center>
                    </div>

                    <div class="row col-md-6 col-sm-7 col-md-12" style="float:inherit; top:0; right: 0; left: 0; margin: 0 auto; margin-top: 40px; max-width: 600px">
                        <div class="col-md-4 col-sm-4 col-xs-4 btMenu" style="margin-top: 20px;height: 140px; text-align: center !important;">
                            <div class="ui-grid-row">
                                <p:commandLink onclick="return ValidarAcesso('59901', 'cofre/cofre.xhtml?faces-redirect=true', '#{login.retornaPermissao('59901')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_cofres_gerenciados.png" width="120" style="background: white; border-radius: 5px; border: 1px solid #c7c7c7;padding: 20px" />
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink onclick="return ValidarAcesso('59901', 'cofre/cofre.xhtml?faces-redirect=true', '#{login.retornaPermissao('59901')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.CofresGerenciados}"
                                                   style="color:#022a48; width: 100%; text-align: center; margin-top: 10px;"/>
                                </p:commandLink>
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-4 col-xs-4 btMenu" style="margin-top: 20px;height: 140px; text-align: center !important;">
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                               oncomplete="PF('dlgTrocarSenha').show()" update="msgs trocarsenha">
                                    <p:graphicImage url="assets/img/icone_configuracoes.png" width="120" style="background: white; border-radius: 5px; border: 1px solid #c7c7c7;padding: 20px" />
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                               oncomplete="PF('dlgTrocarSenha').show()" update="msgs trocarsenha">
                                    <p:outputLabel value="#{localemsgs.Configuracoes}"
                                                   style="color:#022a48; width: 100%; text-align: center; margin-top: 10px;"/>
                                </p:commandLink>
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-4 col-xs-4 btMenu" style="margin-top: 20px;height: 140px; text-align: center !important;">
                            <div class="ui-grid-row">
                                <p:commandLink action="#{login.logOut}" >
                                    <p:graphicImage url="assets/img/icone_sair.png" width="120" style="background: white; border-radius: 5px; border: 1px solid #c7c7c7; padding: 20px" />
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink action="#{login.logOut}" >
                                    <p:outputLabel value="#{localemsgs.Sair}"
                                                   style="color:#022a48; width: 100%; text-align: center; margin-top: 10px;"/>
                                </p:commandLink>
                            </div>
                        </div>
                    </div>
                </h:form>
            </div>


            <h:form id="trocarsenha"> 
                <p:dialog widgetVar="dlgTrocarSenha" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop" style="max-width: 100%">
                    <f:facet name="header">
                        <img src="assets/img/icone_satmob_trocarsenhaG.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panelGrid columns="2" styleClass="pnl">
                        <p:outputLabel for="atual" value="#{localemsgs.SenhaAtual}:"/>
                        <p:password id="atual" autocomplete="off" value="#{funcoesadm.senhaAtual}"/>

                        <p:outputLabel for="nova" value="#{localemsgs.NovaSenha}:"/>
                        <p:password id="nova" match="confirmar" autocomplete="off" required="true" 
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                    validatorMessage="Senhas não conferem" value="#{funcoesadm.senhaNova}"/>

                        <p:outputLabel for="confirmar" value="#{localemsgs.Confirmacao}:"/>
                        <p:password id="confirmar" autocomplete="off" value="#{funcoesadm.senhaNova}"/>
                    </p:panelGrid>
                    &nbsp;
                    <div class="pnl">
                        <p:commandButton value="#{localemsgs.TrocarSenha}" style="width: 130px !important;" 
                                         action="#{login.setPwweb(funcoesadm.trocarSenhaAdm(login.pwweb))}"
                                         update="msgs" class="custom-button"/>
                    </div>
                </p:dialog>
            </h:form>

            <div id="logoSatMob" style="position: absolute; bottom: 70px; width: 100%; min-height: 80px; max-height: 80px; height: 80px; text-align: center">
                <img src="assets/img/logo_satweb.png" style="filter: invert(20%); height: 100%"/>
            </div>

            <ui:insert name="loading" >
                <ui:include src="assets/template/loading.xhtml" />
            </ui:insert>

            <script type="text/javascript">
                // <![CDATA[
                function openNewTab() {
                    window.open("", "_blank");
                }

                function TirarFoto(permite, tipo) {
                    if (permite == 'S') {
                        $('[id*="txtDescricaoArq"]').val(tipo);
                        rc();
                        $('[id*="uploadFotosRelatorio_input"]').click();
                    }
                }

                $(document).ready(function () {
                    setTimeout(function () {
                        $('.sate-icon-grid').css('display', 'block');
                    }, 300);
                });

                function NaoPermitido(CodigoSecao) {
                    $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.AcessoNegado}<br><br>#{localemsgs.CodigoServico}: ' + CodigoSecao);
                }

                function ValidarAcesso(CodigoSecao, Link, Permite) {
                    location.href = Link;
                }

                function AbrirMapaTesouraria() {
                    let HTML = '';
                    HTML += '<div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important;">';
                    HTML += '  <label id="lblCarregando" style="margin-top: 40px; width: 100%; text-align: center; font-size: 11pt; color: #AAA"><i class="fa fa-refresh fa-spin fa-fw"></i>&nbsp;&nbsp;#{localemsgs.ProcessandoAguarde}</label>';
                    HTML += '  <iframe id="ifrMapa" src="" style="display: none; width: 100%; height: ' + ($(window).height() - 400) + 'px; padding: 0px; margin: 0px; border: none;"></iframe>';
                    HTML += '</div>';


                    $frmUpload = $.alert({
                        icon: 'fa fa-upload',
                        closeIcon: true,
                        type: 'blue',
                        columnClass: 'xlarge',
                        title: '#{localemsgs.MapaTesouraria}',
                        content: HTML,
                        buttons: {
                            cancel: {
                                text: '#{localemsgs.Fechar}'
                            }
                        },
                        onContentReady: function () {
                            $('#ifrMapa').attr('src', 'tesouraria/mapa_tesouraria.xhtml');
                            
                            setTimeout(function(){
                                $('#ifrMapa').css('display','');
                                $('#lblCarregando').remove();
                            }, 1500);
                        }
                    });

                    return false;
                }

                function MensagemDesenvolvimento() {
                    $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', 'Em Desevolvimento!');
                    return false;
                }
                // ]]>
            </script>


            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important; text-align: center; margin-top: -1px !important;">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td rowspan="2">
                                        <img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="negrito">
                                        <h:outputText value="#{login.infoFilial.codFil}">
                                            <f:convertNumber pattern="0000"/>
                                        </h:outputText>
                                        #{login.infoFilial.descricao}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>
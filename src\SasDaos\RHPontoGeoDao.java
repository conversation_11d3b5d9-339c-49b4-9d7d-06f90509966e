package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RHPontoGEO;

/**
 * <AUTHOR>
 */
public class RHPontoGeoDao {

    /**
     * Obtem os dados de localização de uma batida.
     *
     * @param matr
     * @param batida
     * @param dtCompet
     * @param persistencia
     * @return
     * @throws Exception
     */
    public RHPontoGEO obterBatida(String matr, int batida, String dtCompet, Persistencia persistencia) throws Exception {
        try {
            String sql = " select * from RHPontoGeo "
                    + " where matr = ? and batida = ? and dtCompet = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.setInt(batida);
            consulta.setString(dtCompet);
            consulta.select();
            RHPontoGEO retorno = new RHPontoGEO();
            while (consulta.Proximo()) {
                retorno.setBatida(consulta.getInt("batida"));
                retorno.setDtCompet(consulta.getString("dtCompet"));
                retorno.setLatitude(consulta.getString("latitude"));
                retorno.setLongitude(consulta.getString("longitude"));
                retorno.setMatr(consulta.getBigDecimal("matr"));
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("RHPontoGeoDAO.obterBatida - " + e.getMessage() + "\r\n"
                    + " select * from RHPontoGeo "
                    + " where matr = " + matr + " and batida = " + batida + " and dtCompet = " + dtCompet);
        }
    }

    /**
     * Salva a localização do momento de uma batida de ponto
     *
     * @param rhpgeo
     * @param persistencia
     * @throws Exception
     */
    public void salvarBatida(RHPontoGEO rhpgeo, Persistencia persistencia) throws Exception {
        try {
            
            String vLatitude = "";
            String vLongitude = "";
            String vMatr = "";
            String vDtCompet = "";
            if((rhpgeo.getLatitude().contains("Geoloca")) || (rhpgeo.getLatitude() == null)){
               vLatitude = "0.0";
            }else{
               vLatitude = rhpgeo.getLatitude();
            }
            
            if((rhpgeo.getLongitude().contains("Geoloca")) || (rhpgeo.getLongitude()== null)){
               vLongitude = "0.0";
            }else{
               vLongitude = rhpgeo.getLongitude();
            }
            
            String sql = "INSERT INTO RHPontogeo (matr, dtcompet, batida, latitude, longitude) VALUES(?,?,?,?,?)";            
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rhpgeo.getMatr());
            consulta.setString(rhpgeo.getDtCompet());
            consulta.setInt(rhpgeo.getBatida());
            consulta.setString(vLatitude.replaceAll(",", "."));
            consulta.setString(vLongitude.replaceAll(",", "."));
            consulta.insert();
            consulta.close();
        } catch (Exception ex) {
            throw new Exception("RHPontoGeoDAO.salvarBatida - " + ex.getMessage() + "\r\n"
                    + "INSERT INTO RHPontogeo (" + rhpgeo.getMatr() + "," + rhpgeo.getDtCompet() + ","
                    + rhpgeo.getBatida() + "," + rhpgeo.getLatitude() + "," + rhpgeo.getLongitude());
        }
    }
}

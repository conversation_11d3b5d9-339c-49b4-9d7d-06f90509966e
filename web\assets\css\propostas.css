/*
*/
/* 
    Created on : 26-Apr-2017, 16:34:50
    Author     : <PERSON>
*/

hr {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
}

.drag{
    border: 1px #dddddd solid;
    background: #EE595C !important;
    opacity: 0.60;
}

.p50-40{
    width: calc(50% - 40px);
}

.w40{
    width: 40px;
}

.sem-padding, .sem-padding .ui-panelgrid-cell{
    padding: 0px !important;
    padding-right: 0px !important;
    padding-left: 0px;
    padding-top: 0px;
}

.preco input{
    text-align: right;
    width: 100% !important;
}

.cliente .ui-autocomplete-panel {
    width: 100% !important;
}
.cliente .ui-autocomplete-input{
    width: 100% !important;
}

.separador{
    margin-top: 10px;
    margin-bottom: 10px;
}

.margin-auto{
    margin: auto;
}

.produtos .ui-expanded-row-content, .borda .ui-panel-footer{
    border: 1px solid #dddddd !important;
}

.produtosS thead{
    display: none;
}

.produtos .ui-paginator-bottom{
    display: block;
}

.produtos .ui-datatable-header{
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    color: #333333;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
    cursor: pointer;
    background-color: #f5f5f5;
    *background-color: #e6e6e6;
    background-image: -ms-linear-gradient(top, #ffffff, #e6e6e6);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e6e6e6));
    background-image: -webkit-linear-gradient(top, #ffffff, #e6e6e6);
    background-image: -o-linear-gradient(top, #ffffff, #e6e6e6);
    background-image: linear-gradient(top, #ffffff, #e6e6e6);
    background-image: -moz-linear-gradient(top, #ffffff, #e6e6e6);
    background-repeat: repeat-x;
    border: 1px solid #cccccc;
    *border: 0;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    border-color: #e6e6e6 #e6e6e6 #bfbfbf;
    border-bottom-color: #b3b3b3;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffffff', endColorstr='#e6e6e6', GradientType=0);
    filter: progid:dximagetransform.microsoft.gradient(enabled=false);
    *zoom: 1;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.panelProduto .ui-widget-content{
    background: transparent !important;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 12px;
}

.produtos .ui-widget-content{
    background: white;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 12px;
}

.produtos td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.produtos .ui-datatable-selectable, .produtos .ui-datatable-odd, .produtos .ui-datatable-even, .produtos .ui-datatable-empty-message{
    border: 1px solid #dddddd !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.produtos .ui-datatable-scrollable-header-box{
    background: white;
}
.produtos .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.produtos .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}
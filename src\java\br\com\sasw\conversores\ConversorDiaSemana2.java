/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.Mascaras;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.ResolverStyle;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorDiaSemana2")
public class ConversorDiaSemana2 implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        DateTimeFormatter df;
        Mascaras m = new Mascaras();
        DateTimeFormatter data = DateTimeFormatter.ofPattern("EEEE");
        LocalDate dia = null;
        try {
            df = DateTimeFormatter.ofPattern("yyyyMMdd");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(value.toString(), df);
            return dia.format(data);
        } catch (Exception e) {
        }
        return value.toString();
    }

}

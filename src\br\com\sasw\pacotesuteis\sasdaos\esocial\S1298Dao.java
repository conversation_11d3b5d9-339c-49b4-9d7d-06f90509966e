/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1298;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1298Dao {

    public List<S1298> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select "
                    + " '1' ideEvento_indApuracao, "
                    + " '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) ideEvento_perApur, "
                    + " Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,   "
                    + " (select max(sucesso) from  (  "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2)  "
                    + "             and z.evento = 'S-1298'  "
                    + "                and z.CodFil = ?\n"
                    + "                and z.Compet = ?\n"
                    + "                and z.Ambiente = ?\n"
                    + "             and (z.Xml_Retorno like '%aguardando%'  "
                    + "                     or z.Xml_Retorno = '' "
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))  "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2)  "
                    + "             and z.evento = 'S-1298'  "
                    + "                and z.CodFil = ?\n"
                    + "                and z.Compet = ?\n"
                    + "                and z.Ambiente = ?\n"
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%'  "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2)  "
                    + "             and z.evento = 'S-1298'  "
                    + "                and z.CodFil = ?\n"
                    + "                and z.Compet = ?\n"
                    + "                and z.Ambiente = ?\n"
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso                 "
                    + " From FPPeriodos  "
                    + " Left join FPMensal  on FPPeriodos.CodMovFP = FPMEnsal.CodMovFP "
                    + " Left join Filiais  on Filiais.CodFil = FPMensal.CodFil "
                    + " Where Convert(Varchar,FPPeriodos.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                    + "   and FPMensal.CodFil = ? "
                    + " Group by FPPeriodos.CodMovFP, Filiais.TipoPessoa,Filiais.CNPJ, Filiais.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            List<S1298> retorno = new ArrayList<>();
            S1298 s1298;
            while (consulta.Proximo()) {
                s1298 = new S1298();
                s1298.setIdeEvento_indApuracao(consulta.getString("ideEvento_indApuracao"));
                s1298.setIdeEvento_perApur(consulta.getString("ideEvento_perApur"));
                s1298.setIdeEvento_tpAmb(ambiente);
                s1298.setIdeEvento_procEmi("1");
                s1298.setIdeEvento_verProc("Satellite eSocial");

                s1298.setSucesso(consulta.getString("sucesso"));

                s1298.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1298.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));

                retorno.add(s1298);
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("S1298Dao.get - " + e.getMessage() + "\r\n");
        }
    }
}

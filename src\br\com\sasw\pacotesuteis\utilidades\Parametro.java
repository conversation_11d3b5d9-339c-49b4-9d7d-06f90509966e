/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.pacotesuteis.utilidades;

/**
 *
 * <AUTHOR>
 */
public class Parametro {
    
    public static String getParam(String cnpj) {
        String param;
        switch (cnpj) {
            case "11179264000170":
            case "08787673000145":
            case "08787673000226":
            case "11179264000251":
            case "08165946000110":
            case "11179264000502":
            case "08139850000198":
            case "40864571000199":
            case "11179264000685":
            case "11179264000928":
            case "11179264000766":
            case "11179264001061":
            case "11179264000847":
            case "11179264001223":
            case "11179264001304":
            case "01873815000148":
            case "11179264001495":
            case "11179264001576":
//                logo = "assets/logosCTE/LOGO_PRESERVE.png";
                param = "SATPRESERVE";
                break;
            case "02445414000150":
            case "02445414000583":
            case "02445414000400":
            case "02445414000230":
            case "02445414000664":
            case "02445414000745":
            case "04829165000121":
//                logo = "assets/logosCTE/LogoTVip.jpg";
                param = "SATTRANSVIP";
                break;
            case "23245012000262":
            case "23245012000181":
            case "23245012000343":
            case "23245012000424":
            case "23245012000505":
            case "23245012000777":
            case "23245012000858":
            case "23245012000939":
            case "23245012001072":
            case "23245012001234":
            case "23245012001315":
            case "23245012001404":
            case "23245012001587":
            case "23245012001668":
//                logo = "assets/logosCTE/logo_rodoban.jpg";
                param = "SATRODOBAN";
                break;
            case "02103266000195":
            case "06291321000179":
            case "09494448000183":
//                logo = "assets/logosCTE/Logomarca Transexcel.jpg";
                param = "SATTRANSEXCEL";
                break;
            case "02361081000180":
            case "02361081000261":
//                logo = "assets/logosCTE/logotseg.jpg";
                param = "SATTSEG";
                break;
            case "07957111000210":
            case "07957111000130":
            case "07957111000482":
            case "07957111000300":
            case "04781359000102":
            case "04617596000124":
            case "07957111000644":
            case "07957111000725":
            case "07957111000806":
            case "20840697000116":
//                logo = "assets/logosCTE/LogoCPV.jpg";
                param = "SATCORPVS";
                break;
            case "26324424000103":
            case "31546484000364":
            case "31546484000526":
            case "26324424000286":
            case "26324424000367":
//                logo = "assets/logosCTE/logoTransfederalCTE.png";          
                param = "SATCONFEDERALBSB";
                break;
            case "06145774000197":
//                logo = "assets/logosCTE/logo_invioseg.png";
                param = "SATCONFEDERALBSB";
                break;
            case "02060306000169":
//                logo = "assets/logosCTE/logo_fenix.jpg";             
                param = "SATFENIXX";
                break;
            case "26729300000108":
            case "26729300000361":
            case "28474085000169":
            case "26729300000523":
            case "26729300000795":
//                logo = "assets/logosCTE/logo_ibl.jpg";                
                param = "SATIBL";
                break;
            case "06263849000134":
            case "06263849000304":
            case "06263849000649":
            case "06263849000568":
//                logo = "assets/logosCTE/Logo_Brasifort.png";               
                param = "SATBRASIFORT";
                break;
            case "00000233485609":
            case "**************":
                param = "SPM";
                break;
            case "32244406000106":
                param = "SATECOVISAO";
                break;
            case "07782730000130":
                param = "SATGLOBAL";
                break;                
            case "17551921000145":
                param = "SATASO";
                break;
            case "51427102000129":
                param = "SATTECBAN";
                break;    
            case "08819936000150":
            case "08819936000312":
            case "08819936000401":                
            case "08819936000584":                
            case "08819936000827":                
            case "08819936000746":                
                param = "SATFIDELYS";
                break;                    
            default:
                param = null;
        }
        return param;
    }
}

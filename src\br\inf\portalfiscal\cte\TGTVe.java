//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.5-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2020.08.24 at 06:12:43 PM BRT 
//


package br.inf.portalfiscal.cte;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlID;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.CollapsedStringAdapter;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import org.w3._2000._09.xmldsig.SignatureType;


/**
 * Tipo Guia de Transporte de Valores Eletr�nica (Modelo 64)
 * 
 * <p>Java class for TGTVe complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * <complexType name="TGTVe">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="infCte">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="ide">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="cUF" type="{http://www.portalfiscal.inf.br/cte}TCodUfIBGE"/>
 *                             <element name="cCT">
 *                               <simpleType>
 *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   <whiteSpace value="preserve"/>
 *                                   <pattern value="[0-9]{8}"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="CFOP" type="{http://www.portalfiscal.inf.br/cte}TCfop"/>
 *                             <element name="natOp">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <minLength value="1"/>
 *                                   <maxLength value="60"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="mod" type="{http://www.portalfiscal.inf.br/cte}TModGTVe"/>
 *                             <element name="serie">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TSerie">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="nCT" type="{http://www.portalfiscal.inf.br/cte}TNF"/>
 *                             <element name="dhEmi">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="tpImp">
 *                               <simpleType>
 *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   <whiteSpace value="preserve"/>
 *                                   <enumeration value="1"/>
 *                                   <enumeration value="2"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="tpEmis">
 *                               <simpleType>
 *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   <whiteSpace value="preserve"/>
 *                                   <enumeration value="1"/>
 *                                   <enumeration value="2"/>
 *                                   <enumeration value="7"/>
 *                                   <enumeration value="8"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="cDV">
 *                               <simpleType>
 *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   <whiteSpace value="preserve"/>
 *                                   <pattern value="[0-9]{1}"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="tpAmb" type="{http://www.portalfiscal.inf.br/cte}TAmb"/>
 *                             <element name="tpCTe">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TFinGTVe">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="verProc">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <minLength value="1"/>
 *                                   <maxLength value="20"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="cMunEnv" type="{http://www.portalfiscal.inf.br/cte}TCodMunIBGE"/>
 *                             <element name="xMunEnv">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <minLength value="2"/>
 *                                   <maxLength value="60"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="UFEnv" type="{http://www.portalfiscal.inf.br/cte}TUf"/>
 *                             <element name="modal">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TModTranspGTVe">
 *                                   <enumeration value="01"/>
 *                                   <enumeration value="06"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="tpServ">
 *                               <simpleType>
 *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   <whiteSpace value="preserve"/>
 *                                   <enumeration value="9"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="indIEToma">
 *                               <simpleType>
 *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   <whiteSpace value="preserve"/>
 *                                   <enumeration value="1"/>
 *                                   <enumeration value="2"/>
 *                                   <enumeration value="9"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="dhSaidaOrig">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="dhChegadaDest">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <choice>
 *                               <element name="toma">
 *                                 <complexType>
 *                                   <complexContent>
 *                                     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                       <sequence>
 *                                         <element name="toma">
 *                                           <simpleType>
 *                                             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                               <whiteSpace value="preserve"/>
 *                                               <enumeration value="0"/>
 *                                               <enumeration value="1"/>
 *                                             </restriction>
 *                                           </simpleType>
 *                                         </element>
 *                                       </sequence>
 *                                     </restriction>
 *                                   </complexContent>
 *                                 </complexType>
 *                               </element>
 *                               <element name="tomaTerceiro">
 *                                 <complexType>
 *                                   <complexContent>
 *                                     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                       <sequence>
 *                                         <element name="toma">
 *                                           <simpleType>
 *                                             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                               <whiteSpace value="preserve"/>
 *                                               <enumeration value="2"/>
 *                                             </restriction>
 *                                           </simpleType>
 *                                         </element>
 *                                         <choice>
 *                                           <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
 *                                           <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
 *                                         </choice>
 *                                         <element name="IE" minOccurs="0">
 *                                           <simpleType>
 *                                             <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
 *                                             </restriction>
 *                                           </simpleType>
 *                                         </element>
 *                                         <element name="xNome">
 *                                           <simpleType>
 *                                             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                               <maxLength value="60"/>
 *                                               <minLength value="2"/>
 *                                             </restriction>
 *                                           </simpleType>
 *                                         </element>
 *                                         <element name="xFant" minOccurs="0">
 *                                           <simpleType>
 *                                             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                               <maxLength value="60"/>
 *                                               <minLength value="2"/>
 *                                             </restriction>
 *                                           </simpleType>
 *                                         </element>
 *                                         <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
 *                                         <element name="enderToma" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
 *                                         <element name="email" type="{http://www.portalfiscal.inf.br/cte}TEmail" minOccurs="0"/>
 *                                       </sequence>
 *                                     </restriction>
 *                                   </complexContent>
 *                                 </complexType>
 *                               </element>
 *                             </choice>
 *                             <sequence minOccurs="0">
 *                               <element name="dhCont" type="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC"/>
 *                               <element name="xJust">
 *                                 <simpleType>
 *                                   <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                     <minLength value="15"/>
 *                                     <maxLength value="256"/>
 *                                   </restriction>
 *                                 </simpleType>
 *                               </element>
 *                             </sequence>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="compl" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="xCaracAd" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <minLength value="1"/>
 *                                   <maxLength value="15"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="xCaracSer" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <minLength value="1"/>
 *                                   <maxLength value="30"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="xEmi" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <minLength value="1"/>
 *                                   <maxLength value="20"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="xObs" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <minLength value="1"/>
 *                                   <maxLength value="2000"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="ObsCont" maxOccurs="10" minOccurs="0">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <element name="xTexto">
 *                                         <simpleType>
 *                                           <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                             <minLength value="1"/>
 *                                             <maxLength value="160"/>
 *                                           </restriction>
 *                                         </simpleType>
 *                                       </element>
 *                                     </sequence>
 *                                     <attribute name="xCampo" use="required">
 *                                       <simpleType>
 *                                         <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                           <minLength value="1"/>
 *                                           <maxLength value="20"/>
 *                                         </restriction>
 *                                       </simpleType>
 *                                     </attribute>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                             <element name="ObsFisco" maxOccurs="10" minOccurs="0">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <element name="xTexto">
 *                                         <simpleType>
 *                                           <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                             <minLength value="1"/>
 *                                             <maxLength value="60"/>
 *                                           </restriction>
 *                                         </simpleType>
 *                                       </element>
 *                                     </sequence>
 *                                     <attribute name="xCampo" use="required">
 *                                       <simpleType>
 *                                         <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                           <minLength value="1"/>
 *                                           <maxLength value="20"/>
 *                                         </restriction>
 *                                       </simpleType>
 *                                     </attribute>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="emit">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpj"/>
 *                             <element name="IE">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TIe">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="IEST" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TIe">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="xNome">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <maxLength value="60"/>
 *                                   <minLength value="2"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="xFant" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <maxLength value="60"/>
 *                                   <minLength value="2"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="enderEmit" type="{http://www.portalfiscal.inf.br/cte}TEndeEmi"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="rem">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <choice>
 *                               <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
 *                               <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
 *                             </choice>
 *                             <element name="IE" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="xNome">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <maxLength value="60"/>
 *                                   <minLength value="2"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="xFant" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <maxLength value="60"/>
 *                                   <minLength value="2"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
 *                             <element name="enderReme" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
 *                             <element name="email" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TEmail">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="dest">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <choice>
 *                               <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
 *                               <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
 *                             </choice>
 *                             <element name="IE" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="xNome">
 *                               <simpleType>
 *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                   <maxLength value="60"/>
 *                                   <minLength value="2"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
 *                             <element name="ISUF" minOccurs="0">
 *                               <simpleType>
 *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   <whiteSpace value="preserve"/>
 *                                   <pattern value="[0-9]{8,9}"/>
 *                                 </restriction>
 *                               </simpleType>
 *                             </element>
 *                             <element name="enderDest" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
 *                             <element name="email" type="{http://www.portalfiscal.inf.br/cte}TEmail" minOccurs="0"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="origem" type="{http://www.portalfiscal.inf.br/cte}TEndeEmi" minOccurs="0"/>
 *                   <element name="destino" type="{http://www.portalfiscal.inf.br/cte}TEndeEmi" minOccurs="0"/>
 *                   <element name="detGTV">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="infEspecie" maxOccurs="unbounded">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <element name="tpEspecie">
 *                                         <simpleType>
 *                                           <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             <whiteSpace value="preserve"/>
 *                                             <enumeration value="1"/>
 *                                             <enumeration value="2"/>
 *                                             <enumeration value="3"/>
 *                                             <enumeration value="4"/>
 *                                           </restriction>
 *                                         </simpleType>
 *                                       </element>
 *                                       <element name="vEspecie" type="{http://www.portalfiscal.inf.br/cte}TDec_1302"/>
 *                                       <element name="tpNumerario">
 *                                         <simpleType>
 *                                           <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             <whiteSpace value="preserve"/>
 *                                             <enumeration value="1"/>
 *                                             <enumeration value="2"/>
 *                                             <enumeration value="3"/>
 *                                             <enumeration value="4"/>
 *                                           </restriction>
 *                                         </simpleType>
 *                                       </element>
 *                                       <element name="xMoedaEstr" minOccurs="0">
 *                                         <simpleType>
 *                                           <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
 *                                             <maxLength value="60"/>
 *                                             <minLength value="2"/>
 *                                           </restriction>
 *                                         </simpleType>
 *                                       </element>
 *                                     </sequence>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                             <element name="qCarga" type="{http://www.portalfiscal.inf.br/cte}TDec_1104"/>
 *                             <element name="infVeiculo" maxOccurs="unbounded">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <element name="placa" type="{http://www.portalfiscal.inf.br/cte}TPlaca"/>
 *                                       <element name="UF" type="{http://www.portalfiscal.inf.br/cte}TUf"/>
 *                                       <element name="RNTRC" minOccurs="0">
 *                                         <simpleType>
 *                                           <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             <whiteSpace value="preserve"/>
 *                                             <pattern value="[0-9]{8}|ISENTO"/>
 *                                           </restriction>
 *                                         </simpleType>
 *                                       </element>
 *                                     </sequence>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="autXML" maxOccurs="10" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <choice>
 *                               <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpj"/>
 *                               <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
 *                             </choice>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="infRespTec" type="{http://www.portalfiscal.inf.br/cte}TRespTec" minOccurs="0"/>
 *                 </sequence>
 *                 <attribute name="versao" use="required">
 *                   <simpleType>
 *                     <restriction base="{http://www.portalfiscal.inf.br/cte}TVerCTe">
 *                     </restriction>
 *                   </simpleType>
 *                 </attribute>
 *                 <attribute name="Id" use="required">
 *                   <simpleType>
 *                     <restriction base="{http://www.w3.org/2001/XMLSchema}ID">
 *                       <pattern value="CTe[0-9]{44}"/>
 *                     </restriction>
 *                   </simpleType>
 *                 </attribute>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="infCTeSupl" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="qrCodCTe">
 *                     <simpleType>
 *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                         <whiteSpace value="preserve"/>
 *                         <minLength value="50"/>
 *                         <maxLength value="1000"/>
 *                         <pattern value="((HTTPS?|https?)://.*\?chCTe=[0-9]{44}&amp;tpAmb=[1-2](&amp;sign=[!-�]{1}[ -�]{0,}[!-�]{1}|[!-�]{1})?)"/>
 *                       </restriction>
 *                     </simpleType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element ref="{http://www.w3.org/2000/09/xmldsig#}Signature"/>
 *       </sequence>
 *       <attribute name="versao" use="required">
 *         <simpleType>
 *           <restriction base="{http://www.portalfiscal.inf.br/cte}TVerCTe">
 *           </restriction>
 *         </simpleType>
 *       </attribute>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * </pre>
 * 
 * 
 */
@XmlRootElement(name="GTVe")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TGTVe", propOrder = {
    "infCte",
    "infCTeSupl",
    "signature"
})
public class TGTVe {

    @XmlElement(required = true)
    protected TGTVe.InfCte infCte;
    protected TGTVe.InfCTeSupl infCTeSupl;
//    @XmlElement(name = "Signature", namespace = "http://www.w3.org/2000/09/xmldsig#", required = true)
    protected SignatureType signature;
    @XmlAttribute(name = "versao", required = true)
    protected String versao;

    /**
     * Gets the value of the infCte property.
     * 
     * @return
     *     possible object is
     *     {@link TGTVe.InfCte }
     *     
     */
    public TGTVe.InfCte getInfCte() {
        return infCte;
    }

    /**
     * Sets the value of the infCte property.
     * 
     * @param value
     *     allowed object is
     *     {@link TGTVe.InfCte }
     *     
     */
    public void setInfCte(TGTVe.InfCte value) {
        this.infCte = value;
    }

    /**
     * Gets the value of the infCTeSupl property.
     * 
     * @return
     *     possible object is
     *     {@link TGTVe.InfCTeSupl }
     *     
     */
    public TGTVe.InfCTeSupl getInfCTeSupl() {
        return infCTeSupl;
    }

    /**
     * Sets the value of the infCTeSupl property.
     * 
     * @param value
     *     allowed object is
     *     {@link TGTVe.InfCTeSupl }
     *     
     */
    public void setInfCTeSupl(TGTVe.InfCTeSupl value) {
        this.infCTeSupl = value;
    }

    /**
     * Gets the value of the signature property.
     * 
     * @return
     *     possible object is
     *     {@link SignatureType }
     *     
     */
    public SignatureType getSignature() {
        return signature;
    }

    /**
     * Sets the value of the signature property.
     * 
     * @param value
     *     allowed object is
     *     {@link SignatureType }
     *     
     */
    public void setSignature(SignatureType value) {
        this.signature = value;
    }

    /**
     * Gets the value of the versao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVersao() {
        return versao;
    }

    /**
     * Sets the value of the versao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVersao(String value) {
        this.versao = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="qrCodCTe">
     *           <simpleType>
     *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *               <whiteSpace value="preserve"/>
     *               <minLength value="50"/>
     *               <maxLength value="1000"/>
     *               <pattern value="((HTTPS?|https?)://.*\?chCTe=[0-9]{44}&amp;tpAmb=[1-2](&amp;sign=[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1})?)"/>
     *             </restriction>
     *           </simpleType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "qrCodCTe"
    })
    public static class InfCTeSupl {

        @XmlElement(required = true)
        protected String qrCodCTe;

        /**
         * Gets the value of the qrCodCTe property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getQrCodCTe() {
            return qrCodCTe;
        }

        /**
         * Sets the value of the qrCodCTe property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setQrCodCTe(String value) {
            this.qrCodCTe = value;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ide">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="cUF" type="{http://www.portalfiscal.inf.br/cte}TCodUfIBGE"/>
     *                   <element name="cCT">
     *                     <simpleType>
     *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         <whiteSpace value="preserve"/>
     *                         <pattern value="[0-9]{8}"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="CFOP" type="{http://www.portalfiscal.inf.br/cte}TCfop"/>
     *                   <element name="natOp">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <minLength value="1"/>
     *                         <maxLength value="60"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="mod" type="{http://www.portalfiscal.inf.br/cte}TModGTVe"/>
     *                   <element name="serie">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TSerie">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="nCT" type="{http://www.portalfiscal.inf.br/cte}TNF"/>
     *                   <element name="dhEmi">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="tpImp">
     *                     <simpleType>
     *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         <whiteSpace value="preserve"/>
     *                         <enumeration value="1"/>
     *                         <enumeration value="2"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="tpEmis">
     *                     <simpleType>
     *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         <whiteSpace value="preserve"/>
     *                         <enumeration value="1"/>
     *                         <enumeration value="2"/>
     *                         <enumeration value="7"/>
     *                         <enumeration value="8"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="cDV">
     *                     <simpleType>
     *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         <whiteSpace value="preserve"/>
     *                         <pattern value="[0-9]{1}"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="tpAmb" type="{http://www.portalfiscal.inf.br/cte}TAmb"/>
     *                   <element name="tpCTe">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TFinGTVe">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="verProc">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <minLength value="1"/>
     *                         <maxLength value="20"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="cMunEnv" type="{http://www.portalfiscal.inf.br/cte}TCodMunIBGE"/>
     *                   <element name="xMunEnv">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <minLength value="2"/>
     *                         <maxLength value="60"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="UFEnv" type="{http://www.portalfiscal.inf.br/cte}TUf"/>
     *                   <element name="modal">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TModTranspGTVe">
     *                         <enumeration value="01"/>
     *                         <enumeration value="06"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="tpServ">
     *                     <simpleType>
     *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         <whiteSpace value="preserve"/>
     *                         <enumeration value="9"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="indIEToma">
     *                     <simpleType>
     *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         <whiteSpace value="preserve"/>
     *                         <enumeration value="1"/>
     *                         <enumeration value="2"/>
     *                         <enumeration value="9"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="dhSaidaOrig">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="dhChegadaDest">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <choice>
     *                     <element name="toma">
     *                       <complexType>
     *                         <complexContent>
     *                           <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                             <sequence>
     *                               <element name="toma">
     *                                 <simpleType>
     *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                     <whiteSpace value="preserve"/>
     *                                     <enumeration value="0"/>
     *                                     <enumeration value="1"/>
     *                                   </restriction>
     *                                 </simpleType>
     *                               </element>
     *                             </sequence>
     *                           </restriction>
     *                         </complexContent>
     *                       </complexType>
     *                     </element>
     *                     <element name="tomaTerceiro">
     *                       <complexType>
     *                         <complexContent>
     *                           <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                             <sequence>
     *                               <element name="toma">
     *                                 <simpleType>
     *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                     <whiteSpace value="preserve"/>
     *                                     <enumeration value="2"/>
     *                                   </restriction>
     *                                 </simpleType>
     *                               </element>
     *                               <choice>
     *                                 <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
     *                                 <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
     *                               </choice>
     *                               <element name="IE" minOccurs="0">
     *                                 <simpleType>
     *                                   <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
     *                                   </restriction>
     *                                 </simpleType>
     *                               </element>
     *                               <element name="xNome">
     *                                 <simpleType>
     *                                   <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                                     <maxLength value="60"/>
     *                                     <minLength value="2"/>
     *                                   </restriction>
     *                                 </simpleType>
     *                               </element>
     *                               <element name="xFant" minOccurs="0">
     *                                 <simpleType>
     *                                   <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                                     <maxLength value="60"/>
     *                                     <minLength value="2"/>
     *                                   </restriction>
     *                                 </simpleType>
     *                               </element>
     *                               <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
     *                               <element name="enderToma" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
     *                               <element name="email" type="{http://www.portalfiscal.inf.br/cte}TEmail" minOccurs="0"/>
     *                             </sequence>
     *                           </restriction>
     *                         </complexContent>
     *                       </complexType>
     *                     </element>
     *                   </choice>
     *                   <sequence minOccurs="0">
     *                     <element name="dhCont" type="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC"/>
     *                     <element name="xJust">
     *                       <simpleType>
     *                         <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                           <minLength value="15"/>
     *                           <maxLength value="256"/>
     *                         </restriction>
     *                       </simpleType>
     *                     </element>
     *                   </sequence>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="compl" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="xCaracAd" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <minLength value="1"/>
     *                         <maxLength value="15"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="xCaracSer" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <minLength value="1"/>
     *                         <maxLength value="30"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="xEmi" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <minLength value="1"/>
     *                         <maxLength value="20"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="xObs" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <minLength value="1"/>
     *                         <maxLength value="2000"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="ObsCont" maxOccurs="10" minOccurs="0">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <element name="xTexto">
     *                               <simpleType>
     *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                                   <minLength value="1"/>
     *                                   <maxLength value="160"/>
     *                                 </restriction>
     *                               </simpleType>
     *                             </element>
     *                           </sequence>
     *                           <attribute name="xCampo" use="required">
     *                             <simpleType>
     *                               <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                                 <minLength value="1"/>
     *                                 <maxLength value="20"/>
     *                               </restriction>
     *                             </simpleType>
     *                           </attribute>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                   <element name="ObsFisco" maxOccurs="10" minOccurs="0">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <element name="xTexto">
     *                               <simpleType>
     *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                                   <minLength value="1"/>
     *                                   <maxLength value="60"/>
     *                                 </restriction>
     *                               </simpleType>
     *                             </element>
     *                           </sequence>
     *                           <attribute name="xCampo" use="required">
     *                             <simpleType>
     *                               <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                                 <minLength value="1"/>
     *                                 <maxLength value="20"/>
     *                               </restriction>
     *                             </simpleType>
     *                           </attribute>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="emit">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpj"/>
     *                   <element name="IE">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TIe">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="IEST" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TIe">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="xNome">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <maxLength value="60"/>
     *                         <minLength value="2"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="xFant" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <maxLength value="60"/>
     *                         <minLength value="2"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="enderEmit" type="{http://www.portalfiscal.inf.br/cte}TEndeEmi"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="rem">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <choice>
     *                     <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
     *                     <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
     *                   </choice>
     *                   <element name="IE" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="xNome">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <maxLength value="60"/>
     *                         <minLength value="2"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="xFant" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <maxLength value="60"/>
     *                         <minLength value="2"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
     *                   <element name="enderReme" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
     *                   <element name="email" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TEmail">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="dest">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <choice>
     *                     <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
     *                     <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
     *                   </choice>
     *                   <element name="IE" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="xNome">
     *                     <simpleType>
     *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                         <maxLength value="60"/>
     *                         <minLength value="2"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
     *                   <element name="ISUF" minOccurs="0">
     *                     <simpleType>
     *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         <whiteSpace value="preserve"/>
     *                         <pattern value="[0-9]{8,9}"/>
     *                       </restriction>
     *                     </simpleType>
     *                   </element>
     *                   <element name="enderDest" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
     *                   <element name="email" type="{http://www.portalfiscal.inf.br/cte}TEmail" minOccurs="0"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="origem" type="{http://www.portalfiscal.inf.br/cte}TEndeEmi" minOccurs="0"/>
     *         <element name="destino" type="{http://www.portalfiscal.inf.br/cte}TEndeEmi" minOccurs="0"/>
     *         <element name="detGTV">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="infEspecie" maxOccurs="unbounded">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <element name="tpEspecie">
     *                               <simpleType>
     *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   <whiteSpace value="preserve"/>
     *                                   <enumeration value="1"/>
     *                                   <enumeration value="2"/>
     *                                   <enumeration value="3"/>
     *                                   <enumeration value="4"/>
     *                                 </restriction>
     *                               </simpleType>
     *                             </element>
     *                             <element name="vEspecie" type="{http://www.portalfiscal.inf.br/cte}TDec_1302"/>
     *                             <element name="tpNumerario">
     *                               <simpleType>
     *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   <whiteSpace value="preserve"/>
     *                                   <enumeration value="1"/>
     *                                   <enumeration value="2"/>
     *                                   <enumeration value="3"/>
     *                                   <enumeration value="4"/>
     *                                 </restriction>
     *                               </simpleType>
     *                             </element>
     *                             <element name="xMoedaEstr" minOccurs="0">
     *                               <simpleType>
     *                                 <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
     *                                   <maxLength value="60"/>
     *                                   <minLength value="2"/>
     *                                 </restriction>
     *                               </simpleType>
     *                             </element>
     *                           </sequence>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                   <element name="qCarga" type="{http://www.portalfiscal.inf.br/cte}TDec_1104"/>
     *                   <element name="infVeiculo" maxOccurs="unbounded">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <element name="placa" type="{http://www.portalfiscal.inf.br/cte}TPlaca"/>
     *                             <element name="UF" type="{http://www.portalfiscal.inf.br/cte}TUf"/>
     *                             <element name="RNTRC" minOccurs="0">
     *                               <simpleType>
     *                                 <restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   <whiteSpace value="preserve"/>
     *                                   <pattern value="[0-9]{8}|ISENTO"/>
     *                                 </restriction>
     *                               </simpleType>
     *                             </element>
     *                           </sequence>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="autXML" maxOccurs="10" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <choice>
     *                     <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpj"/>
     *                     <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
     *                   </choice>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="infRespTec" type="{http://www.portalfiscal.inf.br/cte}TRespTec" minOccurs="0"/>
     *       </sequence>
     *       <attribute name="versao" use="required">
     *         <simpleType>
     *           <restriction base="{http://www.portalfiscal.inf.br/cte}TVerCTe">
     *           </restriction>
     *         </simpleType>
     *       </attribute>
     *       <attribute name="Id" use="required">
     *         <simpleType>
     *           <restriction base="{http://www.w3.org/2001/XMLSchema}ID">
     *             <pattern value="CTe[0-9]{44}"/>
     *           </restriction>
     *         </simpleType>
     *       </attribute>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "versao",
        "ide",
        "compl",
        "emit",
        "rem",
        "dest",
        "origem",
        "destino",
        "detGTV",
        "autXML",
        "infRespTec"
    })
    public static class InfCte {

        @XmlElement(required = true)
        protected TGTVe.InfCte.Ide ide;
        protected TGTVe.InfCte.Compl compl;
        @XmlElement(required = true)
        protected TGTVe.InfCte.Emit emit;
        @XmlElement(required = true)
        protected TGTVe.InfCte.Rem rem;
        @XmlElement(required = true)
        protected TGTVe.InfCte.Dest dest;
        protected TEndeEmi origem;
        protected TEndeEmi destino;
        @XmlElement(required = true)
        protected TGTVe.InfCte.DetGTV detGTV;
        protected List<TGTVe.InfCte.AutXML> autXML;
        protected TRespTec infRespTec;
        @XmlAttribute(name = "Id", required = true)
        @XmlJavaTypeAdapter(CollapsedStringAdapter.class)
        @XmlID
        protected String id;
        @XmlAttribute(name = "versao", required = true)
        protected String versao;

        /**
         * Gets the value of the ide property.
         * 
         * @return
         *     possible object is
         *     {@link TGTVe.InfCte.Ide }
         *     
         */
        public TGTVe.InfCte.Ide getIde() {
            return ide;
        }

        /**
         * Sets the value of the ide property.
         * 
         * @param value
         *     allowed object is
         *     {@link TGTVe.InfCte.Ide }
         *     
         */
        public void setIde(TGTVe.InfCte.Ide value) {
            this.ide = value;
        }

        /**
         * Gets the value of the compl property.
         * 
         * @return
         *     possible object is
         *     {@link TGTVe.InfCte.Compl }
         *     
         */
        public TGTVe.InfCte.Compl getCompl() {
            return compl;
        }

        /**
         * Sets the value of the compl property.
         * 
         * @param value
         *     allowed object is
         *     {@link TGTVe.InfCte.Compl }
         *     
         */
        public void setCompl(TGTVe.InfCte.Compl value) {
            this.compl = value;
        }

        /**
         * Gets the value of the emit property.
         * 
         * @return
         *     possible object is
         *     {@link TGTVe.InfCte.Emit }
         *     
         */
        public TGTVe.InfCte.Emit getEmit() {
            return emit;
        }

        /**
         * Sets the value of the emit property.
         * 
         * @param value
         *     allowed object is
         *     {@link TGTVe.InfCte.Emit }
         *     
         */
        public void setEmit(TGTVe.InfCte.Emit value) {
            this.emit = value;
        }

        /**
         * Gets the value of the rem property.
         * 
         * @return
         *     possible object is
         *     {@link TGTVe.InfCte.Rem }
         *     
         */
        public TGTVe.InfCte.Rem getRem() {
            return rem;
        }

        /**
         * Sets the value of the rem property.
         * 
         * @param value
         *     allowed object is
         *     {@link TGTVe.InfCte.Rem }
         *     
         */
        public void setRem(TGTVe.InfCte.Rem value) {
            this.rem = value;
        }

        /**
         * Gets the value of the dest property.
         * 
         * @return
         *     possible object is
         *     {@link TGTVe.InfCte.Dest }
         *     
         */
        public TGTVe.InfCte.Dest getDest() {
            return dest;
        }

        /**
         * Sets the value of the dest property.
         * 
         * @param value
         *     allowed object is
         *     {@link TGTVe.InfCte.Dest }
         *     
         */
        public void setDest(TGTVe.InfCte.Dest value) {
            this.dest = value;
        }

        /**
         * Gets the value of the origem property.
         * 
         * @return
         *     possible object is
         *     {@link TEndeEmi }
         *     
         */
        public TEndeEmi getOrigem() {
            return origem;
        }

        /**
         * Sets the value of the origem property.
         * 
         * @param value
         *     allowed object is
         *     {@link TEndeEmi }
         *     
         */
        public void setOrigem(TEndeEmi value) {
            this.origem = value;
        }

        /**
         * Gets the value of the destino property.
         * 
         * @return
         *     possible object is
         *     {@link TEndeEmi }
         *     
         */
        public TEndeEmi getDestino() {
            return destino;
        }

        /**
         * Sets the value of the destino property.
         * 
         * @param value
         *     allowed object is
         *     {@link TEndeEmi }
         *     
         */
        public void setDestino(TEndeEmi value) {
            this.destino = value;
        }

        /**
         * Gets the value of the detGTV property.
         * 
         * @return
         *     possible object is
         *     {@link TGTVe.InfCte.DetGTV }
         *     
         */
        public TGTVe.InfCte.DetGTV getDetGTV() {
            return detGTV;
        }

        /**
         * Sets the value of the detGTV property.
         * 
         * @param value
         *     allowed object is
         *     {@link TGTVe.InfCte.DetGTV }
         *     
         */
        public void setDetGTV(TGTVe.InfCte.DetGTV value) {
            this.detGTV = value;
        }

        /**
         * Gets the value of the autXML property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the autXML property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getAutXML().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link TGTVe.InfCte.AutXML }
         * 
         * 
         */
        public List<TGTVe.InfCte.AutXML> getAutXML() {
            if (autXML == null) {
                autXML = new ArrayList<TGTVe.InfCte.AutXML>();
            }
            return this.autXML;
        }

        /**
         * Gets the value of the infRespTec property.
         * 
         * @return
         *     possible object is
         *     {@link TRespTec }
         *     
         */
        public TRespTec getInfRespTec() {
            return infRespTec;
        }

        /**
         * Sets the value of the infRespTec property.
         * 
         * @param value
         *     allowed object is
         *     {@link TRespTec }
         *     
         */
        public void setInfRespTec(TRespTec value) {
            this.infRespTec = value;
        }

        /**
         * Gets the value of the id property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getId() {
            return id;
        }

        /**
         * Sets the value of the id property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setId(String value) {
            this.id = value;
        }

        /**
         * Gets the value of the versao property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getVersao() {
            return versao;
        }

        /**
         * Sets the value of the versao property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setVersao(String value) {
            this.versao = value;
        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <choice>
         *           <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpj"/>
         *           <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
         *         </choice>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "cnpj",
            "cpf"
        })
        public static class AutXML {

            @XmlElement(name = "CNPJ")
            protected String cnpj;
            @XmlElement(name = "CPF")
            protected String cpf;

            /**
             * Gets the value of the cnpj property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCNPJ() {
                return cnpj;
            }

            /**
             * Sets the value of the cnpj property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCNPJ(String value) {
                this.cnpj = value;
            }

            /**
             * Gets the value of the cpf property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCPF() {
                return cpf;
            }

            /**
             * Sets the value of the cpf property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCPF(String value) {
                this.cpf = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="xCaracAd" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <minLength value="1"/>
         *               <maxLength value="15"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="xCaracSer" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <minLength value="1"/>
         *               <maxLength value="30"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="xEmi" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <minLength value="1"/>
         *               <maxLength value="20"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="xObs" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <minLength value="1"/>
         *               <maxLength value="2000"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="ObsCont" maxOccurs="10" minOccurs="0">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <element name="xTexto">
         *                     <simpleType>
         *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *                         <minLength value="1"/>
         *                         <maxLength value="160"/>
         *                       </restriction>
         *                     </simpleType>
         *                   </element>
         *                 </sequence>
         *                 <attribute name="xCampo" use="required">
         *                   <simpleType>
         *                     <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *                       <minLength value="1"/>
         *                       <maxLength value="20"/>
         *                     </restriction>
         *                   </simpleType>
         *                 </attribute>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *         <element name="ObsFisco" maxOccurs="10" minOccurs="0">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <element name="xTexto">
         *                     <simpleType>
         *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *                         <minLength value="1"/>
         *                         <maxLength value="60"/>
         *                       </restriction>
         *                     </simpleType>
         *                   </element>
         *                 </sequence>
         *                 <attribute name="xCampo" use="required">
         *                   <simpleType>
         *                     <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *                       <minLength value="1"/>
         *                       <maxLength value="20"/>
         *                     </restriction>
         *                   </simpleType>
         *                 </attribute>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "xCaracAd",
            "xCaracSer",
            "xEmi",
            "xObs",
            "obsCont",
            "obsFisco"
        })
        public static class Compl {

            protected String xCaracAd;
            protected String xCaracSer;
            protected String xEmi;
            protected String xObs;
            @XmlElement(name = "ObsCont")
            protected List<TGTVe.InfCte.Compl.ObsCont> obsCont;
            @XmlElement(name = "ObsFisco")
            protected List<TGTVe.InfCte.Compl.ObsFisco> obsFisco;

            /**
             * Gets the value of the xCaracAd property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXCaracAd() {
                return xCaracAd;
            }

            /**
             * Sets the value of the xCaracAd property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXCaracAd(String value) {
                this.xCaracAd = value;
            }

            /**
             * Gets the value of the xCaracSer property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXCaracSer() {
                return xCaracSer;
            }

            /**
             * Sets the value of the xCaracSer property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXCaracSer(String value) {
                this.xCaracSer = value;
            }

            /**
             * Gets the value of the xEmi property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXEmi() {
                return xEmi;
            }

            /**
             * Sets the value of the xEmi property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXEmi(String value) {
                this.xEmi = value;
            }

            /**
             * Gets the value of the xObs property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXObs() {
                return xObs;
            }

            /**
             * Sets the value of the xObs property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXObs(String value) {
                this.xObs = value;
            }

            /**
             * Gets the value of the obsCont property.
             * 
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the obsCont property.
             * 
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getObsCont().add(newItem);
             * </pre>
             * 
             * 
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link TGTVe.InfCte.Compl.ObsCont }
             * 
             * 
             */
            public List<TGTVe.InfCte.Compl.ObsCont> getObsCont() {
                if (obsCont == null) {
                    obsCont = new ArrayList<TGTVe.InfCte.Compl.ObsCont>();
                }
                return this.obsCont;
            }

            /**
             * Gets the value of the obsFisco property.
             * 
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the obsFisco property.
             * 
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getObsFisco().add(newItem);
             * </pre>
             * 
             * 
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link TGTVe.InfCte.Compl.ObsFisco }
             * 
             * 
             */
            public List<TGTVe.InfCte.Compl.ObsFisco> getObsFisco() {
                if (obsFisco == null) {
                    obsFisco = new ArrayList<TGTVe.InfCte.Compl.ObsFisco>();
                }
                return this.obsFisco;
            }


            /**
             * <p>Java class for anonymous complex type.
             * 
             * <p>The following schema fragment specifies the expected content contained within this class.
             * 
             * <pre>
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <element name="xTexto">
             *           <simpleType>
             *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
             *               <minLength value="1"/>
             *               <maxLength value="160"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *       </sequence>
             *       <attribute name="xCampo" use="required">
             *         <simpleType>
             *           <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
             *             <minLength value="1"/>
             *             <maxLength value="20"/>
             *           </restriction>
             *         </simpleType>
             *       </attribute>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "xTexto"
            })
            public static class ObsCont {

                @XmlElement(required = true)
                protected String xTexto;
                @XmlAttribute(name = "xCampo", required = true)
                protected String xCampo;

                /**
                 * Gets the value of the xTexto property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getXTexto() {
                    return xTexto;
                }

                /**
                 * Sets the value of the xTexto property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setXTexto(String value) {
                    this.xTexto = value;
                }

                /**
                 * Gets the value of the xCampo property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getXCampo() {
                    return xCampo;
                }

                /**
                 * Sets the value of the xCampo property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setXCampo(String value) {
                    this.xCampo = value;
                }

            }


            /**
             * <p>Java class for anonymous complex type.
             * 
             * <p>The following schema fragment specifies the expected content contained within this class.
             * 
             * <pre>
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <element name="xTexto">
             *           <simpleType>
             *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
             *               <minLength value="1"/>
             *               <maxLength value="60"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *       </sequence>
             *       <attribute name="xCampo" use="required">
             *         <simpleType>
             *           <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
             *             <minLength value="1"/>
             *             <maxLength value="20"/>
             *           </restriction>
             *         </simpleType>
             *       </attribute>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "xTexto"
            })
            public static class ObsFisco {

                @XmlElement(required = true)
                protected String xTexto;
                @XmlAttribute(name = "xCampo", required = true)
                protected String xCampo;

                /**
                 * Gets the value of the xTexto property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getXTexto() {
                    return xTexto;
                }

                /**
                 * Sets the value of the xTexto property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setXTexto(String value) {
                    this.xTexto = value;
                }

                /**
                 * Gets the value of the xCampo property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getXCampo() {
                    return xCampo;
                }

                /**
                 * Sets the value of the xCampo property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setXCampo(String value) {
                    this.xCampo = value;
                }

            }

        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <choice>
         *           <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
         *           <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
         *         </choice>
         *         <element name="IE" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="xNome">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <maxLength value="60"/>
         *               <minLength value="2"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
         *         <element name="ISUF" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               <whiteSpace value="preserve"/>
         *               <pattern value="[0-9]{8,9}"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="enderDest" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
         *         <element name="email" type="{http://www.portalfiscal.inf.br/cte}TEmail" minOccurs="0"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "cnpj",
            "cpf",
            "ie",
            "xNome",
            "fone",
            "isuf",
            "enderDest",
            "email"
        })
        public static class Dest {

            @XmlElement(name = "CNPJ")
            protected String cnpj;
            @XmlElement(name = "CPF")
            protected String cpf;
            @XmlElement(name = "IE")
            protected String ie;
            @XmlElement(required = true)
            protected String xNome;
            protected String fone;
            @XmlElement(name = "ISUF")
            protected String isuf;
            @XmlElement(required = true)
            protected TEndereco enderDest;
            protected String email;

            /**
             * Gets the value of the cnpj property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCNPJ() {
                return cnpj;
            }

            /**
             * Sets the value of the cnpj property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCNPJ(String value) {
                this.cnpj = value;
            }

            /**
             * Gets the value of the cpf property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCPF() {
                return cpf;
            }

            /**
             * Sets the value of the cpf property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCPF(String value) {
                this.cpf = value;
            }

            /**
             * Gets the value of the ie property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getIE() {
                return ie;
            }

            /**
             * Sets the value of the ie property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setIE(String value) {
                this.ie = value;
            }

            /**
             * Gets the value of the xNome property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXNome() {
                return xNome;
            }

            /**
             * Sets the value of the xNome property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXNome(String value) {
                this.xNome = value;
            }

            /**
             * Gets the value of the fone property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getFone() {
                return fone;
            }

            /**
             * Sets the value of the fone property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setFone(String value) {
                this.fone = value;
            }

            /**
             * Gets the value of the isuf property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getISUF() {
                return isuf;
            }

            /**
             * Sets the value of the isuf property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setISUF(String value) {
                this.isuf = value;
            }

            /**
             * Gets the value of the enderDest property.
             * 
             * @return
             *     possible object is
             *     {@link TEndereco }
             *     
             */
            public TEndereco getEnderDest() {
                return enderDest;
            }

            /**
             * Sets the value of the enderDest property.
             * 
             * @param value
             *     allowed object is
             *     {@link TEndereco }
             *     
             */
            public void setEnderDest(TEndereco value) {
                this.enderDest = value;
            }

            /**
             * Gets the value of the email property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getEmail() {
                return email;
            }

            /**
             * Sets the value of the email property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setEmail(String value) {
                this.email = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="infEspecie" maxOccurs="unbounded">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <element name="tpEspecie">
         *                     <simpleType>
         *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         <whiteSpace value="preserve"/>
         *                         <enumeration value="1"/>
         *                         <enumeration value="2"/>
         *                         <enumeration value="3"/>
         *                         <enumeration value="4"/>
         *                       </restriction>
         *                     </simpleType>
         *                   </element>
         *                   <element name="vEspecie" type="{http://www.portalfiscal.inf.br/cte}TDec_1302"/>
         *                   <element name="tpNumerario">
         *                     <simpleType>
         *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         <whiteSpace value="preserve"/>
         *                         <enumeration value="1"/>
         *                         <enumeration value="2"/>
         *                         <enumeration value="3"/>
         *                         <enumeration value="4"/>
         *                       </restriction>
         *                     </simpleType>
         *                   </element>
         *                   <element name="xMoedaEstr" minOccurs="0">
         *                     <simpleType>
         *                       <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *                         <maxLength value="60"/>
         *                         <minLength value="2"/>
         *                       </restriction>
         *                     </simpleType>
         *                   </element>
         *                 </sequence>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *         <element name="qCarga" type="{http://www.portalfiscal.inf.br/cte}TDec_1104"/>
         *         <element name="infVeiculo" maxOccurs="unbounded">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <element name="placa" type="{http://www.portalfiscal.inf.br/cte}TPlaca"/>
         *                   <element name="UF" type="{http://www.portalfiscal.inf.br/cte}TUf"/>
         *                   <element name="RNTRC" minOccurs="0">
         *                     <simpleType>
         *                       <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         <whiteSpace value="preserve"/>
         *                         <pattern value="[0-9]{8}|ISENTO"/>
         *                       </restriction>
         *                     </simpleType>
         *                   </element>
         *                 </sequence>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "infEspecie",
            "qCarga",
            "infVeiculo"
        })
        public static class DetGTV {

            @XmlElement(required = true)
            protected List<TGTVe.InfCte.DetGTV.InfEspecie> infEspecie;
            @XmlElement(required = true)
            protected String qCarga;
            @XmlElement(required = true)
            protected List<TGTVe.InfCte.DetGTV.InfVeiculo> infVeiculo;

            /**
             * Gets the value of the infEspecie property.
             * 
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the infEspecie property.
             * 
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getInfEspecie().add(newItem);
             * </pre>
             * 
             * 
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link TGTVe.InfCte.DetGTV.InfEspecie }
             * 
             * 
             */
            public List<TGTVe.InfCte.DetGTV.InfEspecie> getInfEspecie() {
                if (infEspecie == null) {
                    infEspecie = new ArrayList<TGTVe.InfCte.DetGTV.InfEspecie>();
                }
                return this.infEspecie;
            }

            /**
             * Gets the value of the qCarga property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getQCarga() {
                return qCarga;
            }

            /**
             * Sets the value of the qCarga property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setQCarga(String value) {
                this.qCarga = value;
            }

            /**
             * Gets the value of the infVeiculo property.
             * 
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the infVeiculo property.
             * 
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getInfVeiculo().add(newItem);
             * </pre>
             * 
             * 
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link TGTVe.InfCte.DetGTV.InfVeiculo }
             * 
             * 
             */
            public List<TGTVe.InfCte.DetGTV.InfVeiculo> getInfVeiculo() {
                if (infVeiculo == null) {
                    infVeiculo = new ArrayList<TGTVe.InfCte.DetGTV.InfVeiculo>();
                }
                return this.infVeiculo;
            }


            /**
             * <p>Java class for anonymous complex type.
             * 
             * <p>The following schema fragment specifies the expected content contained within this class.
             * 
             * <pre>
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <element name="tpEspecie">
             *           <simpleType>
             *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               <whiteSpace value="preserve"/>
             *               <enumeration value="1"/>
             *               <enumeration value="2"/>
             *               <enumeration value="3"/>
             *               <enumeration value="4"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *         <element name="vEspecie" type="{http://www.portalfiscal.inf.br/cte}TDec_1302"/>
             *         <element name="tpNumerario">
             *           <simpleType>
             *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               <whiteSpace value="preserve"/>
             *               <enumeration value="1"/>
             *               <enumeration value="2"/>
             *               <enumeration value="3"/>
             *               <enumeration value="4"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *         <element name="xMoedaEstr" minOccurs="0">
             *           <simpleType>
             *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
             *               <maxLength value="60"/>
             *               <minLength value="2"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "tpEspecie",
                "vEspecie",
                "tpNumerario",
                "xMoedaEstr"
            })
            public static class InfEspecie {

                @XmlElement(required = true)
                protected String tpEspecie;
                @XmlElement(required = true)
                protected String vEspecie;
                @XmlElement(required = true)
                protected String tpNumerario;
                protected String xMoedaEstr;

                /**
                 * Gets the value of the tpEspecie property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getTpEspecie() {
                    return tpEspecie;
                }

                /**
                 * Sets the value of the tpEspecie property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setTpEspecie(String value) {
                    this.tpEspecie = value;
                }

                /**
                 * Gets the value of the vEspecie property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getVEspecie() {
                    return vEspecie;
                }

                /**
                 * Sets the value of the vEspecie property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setVEspecie(String value) {
                    this.vEspecie = value;
                }

                /**
                 * Gets the value of the tpNumerario property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getTpNumerario() {
                    return tpNumerario;
                }

                /**
                 * Sets the value of the tpNumerario property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setTpNumerario(String value) {
                    this.tpNumerario = value;
                }

                /**
                 * Gets the value of the xMoedaEstr property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getXMoedaEstr() {
                    return xMoedaEstr;
                }

                /**
                 * Sets the value of the xMoedaEstr property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setXMoedaEstr(String value) {
                    this.xMoedaEstr = value;
                }

            }


            /**
             * <p>Java class for anonymous complex type.
             * 
             * <p>The following schema fragment specifies the expected content contained within this class.
             * 
             * <pre>
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <element name="placa" type="{http://www.portalfiscal.inf.br/cte}TPlaca"/>
             *         <element name="UF" type="{http://www.portalfiscal.inf.br/cte}TUf"/>
             *         <element name="RNTRC" minOccurs="0">
             *           <simpleType>
             *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               <whiteSpace value="preserve"/>
             *               <pattern value="[0-9]{8}|ISENTO"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "placa",
                "uf",
                "rntrc"
            })
            public static class InfVeiculo {

                @XmlElement(required = true)
                protected String placa;
                @XmlElement(name = "UF", required = true)
                protected TUf uf;
                @XmlElement(name = "RNTRC")
                protected String rntrc;

                /**
                 * Gets the value of the placa property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getPlaca() {
                    return placa;
                }

                /**
                 * Sets the value of the placa property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setPlaca(String value) {
                    this.placa = value;
                }

                /**
                 * Gets the value of the uf property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link TUf }
                 *     
                 */
                public TUf getUF() {
                    return uf;
                }

                /**
                 * Sets the value of the uf property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link TUf }
                 *     
                 */
                public void setUF(TUf value) {
                    this.uf = value;
                }

                /**
                 * Gets the value of the rntrc property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getRNTRC() {
                    return rntrc;
                }

                /**
                 * Sets the value of the rntrc property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setRNTRC(String value) {
                    this.rntrc = value;
                }

            }

        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpj"/>
         *         <element name="IE">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TIe">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="IEST" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TIe">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="xNome">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <maxLength value="60"/>
         *               <minLength value="2"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="xFant" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <maxLength value="60"/>
         *               <minLength value="2"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="enderEmit" type="{http://www.portalfiscal.inf.br/cte}TEndeEmi"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "cnpj",
            "ie",
            "iest",
            "xNome",
            "xFant",
            "enderEmit"
        })
        public static class Emit {

            @XmlElement(name = "CNPJ", required = true)
            protected String cnpj;
            @XmlElement(name = "IE", required = true)
            protected String ie;
            @XmlElement(name = "IEST")
            protected String iest;
            @XmlElement(required = true)
            protected String xNome;
            protected String xFant;
            @XmlElement(required = true)
            protected TEndeEmi enderEmit;

            /**
             * Gets the value of the cnpj property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCNPJ() {
                return cnpj;
            }

            /**
             * Sets the value of the cnpj property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCNPJ(String value) {
                this.cnpj = value;
            }

            /**
             * Gets the value of the ie property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getIE() {
                return ie;
            }

            /**
             * Sets the value of the ie property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setIE(String value) {
                this.ie = value;
            }

            /**
             * Gets the value of the iest property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getIEST() {
                return iest;
            }

            /**
             * Sets the value of the iest property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setIEST(String value) {
                this.iest = value;
            }

            /**
             * Gets the value of the xNome property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXNome() {
                return xNome;
            }

            /**
             * Sets the value of the xNome property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXNome(String value) {
                this.xNome = value;
            }

            /**
             * Gets the value of the xFant property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXFant() {
                return xFant;
            }

            /**
             * Sets the value of the xFant property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXFant(String value) {
                this.xFant = value;
            }

            /**
             * Gets the value of the enderEmit property.
             * 
             * @return
             *     possible object is
             *     {@link TEndeEmi }
             *     
             */
            public TEndeEmi getEnderEmit() {
                return enderEmit;
            }

            /**
             * Sets the value of the enderEmit property.
             * 
             * @param value
             *     allowed object is
             *     {@link TEndeEmi }
             *     
             */
            public void setEnderEmit(TEndeEmi value) {
                this.enderEmit = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="cUF" type="{http://www.portalfiscal.inf.br/cte}TCodUfIBGE"/>
         *         <element name="cCT">
         *           <simpleType>
         *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               <whiteSpace value="preserve"/>
         *               <pattern value="[0-9]{8}"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="CFOP" type="{http://www.portalfiscal.inf.br/cte}TCfop"/>
         *         <element name="natOp">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <minLength value="1"/>
         *               <maxLength value="60"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="mod" type="{http://www.portalfiscal.inf.br/cte}TModGTVe"/>
         *         <element name="serie">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TSerie">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="nCT" type="{http://www.portalfiscal.inf.br/cte}TNF"/>
         *         <element name="dhEmi">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="tpImp">
         *           <simpleType>
         *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               <whiteSpace value="preserve"/>
         *               <enumeration value="1"/>
         *               <enumeration value="2"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="tpEmis">
         *           <simpleType>
         *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               <whiteSpace value="preserve"/>
         *               <enumeration value="1"/>
         *               <enumeration value="2"/>
         *               <enumeration value="7"/>
         *               <enumeration value="8"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="cDV">
         *           <simpleType>
         *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               <whiteSpace value="preserve"/>
         *               <pattern value="[0-9]{1}"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="tpAmb" type="{http://www.portalfiscal.inf.br/cte}TAmb"/>
         *         <element name="tpCTe">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TFinGTVe">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="verProc">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <minLength value="1"/>
         *               <maxLength value="20"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="cMunEnv" type="{http://www.portalfiscal.inf.br/cte}TCodMunIBGE"/>
         *         <element name="xMunEnv">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <minLength value="2"/>
         *               <maxLength value="60"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="UFEnv" type="{http://www.portalfiscal.inf.br/cte}TUf"/>
         *         <element name="modal">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TModTranspGTVe">
         *               <enumeration value="01"/>
         *               <enumeration value="06"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="tpServ">
         *           <simpleType>
         *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               <whiteSpace value="preserve"/>
         *               <enumeration value="9"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="indIEToma">
         *           <simpleType>
         *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               <whiteSpace value="preserve"/>
         *               <enumeration value="1"/>
         *               <enumeration value="2"/>
         *               <enumeration value="9"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="dhSaidaOrig">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="dhChegadaDest">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <choice>
         *           <element name="toma">
         *             <complexType>
         *               <complexContent>
         *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                   <sequence>
         *                     <element name="toma">
         *                       <simpleType>
         *                         <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                           <whiteSpace value="preserve"/>
         *                           <enumeration value="0"/>
         *                           <enumeration value="1"/>
         *                         </restriction>
         *                       </simpleType>
         *                     </element>
         *                   </sequence>
         *                 </restriction>
         *               </complexContent>
         *             </complexType>
         *           </element>
         *           <element name="tomaTerceiro">
         *             <complexType>
         *               <complexContent>
         *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                   <sequence>
         *                     <element name="toma">
         *                       <simpleType>
         *                         <restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                           <whiteSpace value="preserve"/>
         *                           <enumeration value="2"/>
         *                         </restriction>
         *                       </simpleType>
         *                     </element>
         *                     <choice>
         *                       <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
         *                       <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
         *                     </choice>
         *                     <element name="IE" minOccurs="0">
         *                       <simpleType>
         *                         <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
         *                         </restriction>
         *                       </simpleType>
         *                     </element>
         *                     <element name="xNome">
         *                       <simpleType>
         *                         <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *                           <maxLength value="60"/>
         *                           <minLength value="2"/>
         *                         </restriction>
         *                       </simpleType>
         *                     </element>
         *                     <element name="xFant" minOccurs="0">
         *                       <simpleType>
         *                         <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *                           <maxLength value="60"/>
         *                           <minLength value="2"/>
         *                         </restriction>
         *                       </simpleType>
         *                     </element>
         *                     <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
         *                     <element name="enderToma" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
         *                     <element name="email" type="{http://www.portalfiscal.inf.br/cte}TEmail" minOccurs="0"/>
         *                   </sequence>
         *                 </restriction>
         *               </complexContent>
         *             </complexType>
         *           </element>
         *         </choice>
         *         <sequence minOccurs="0">
         *           <element name="dhCont" type="{http://www.portalfiscal.inf.br/cte}TDateTimeUTC"/>
         *           <element name="xJust">
         *             <simpleType>
         *               <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *                 <minLength value="15"/>
         *                 <maxLength value="256"/>
         *               </restriction>
         *             </simpleType>
         *           </element>
         *         </sequence>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "cuf",
            "cct",
            "cfop",
            "natOp",
            "mod",
            "serie",
            "nct",
            "dhEmi",
            "tpImp",
            "tpEmis",
            "cdv",
            "tpAmb",
            "tpCTe",
            "verProc",
            "cMunEnv",
            "xMunEnv",
            "ufEnv",
            "modal",
            "tpServ",
            "indIEToma",
            "dhSaidaOrig",
            "dhChegadaDest",
            "toma",
            "tomaTerceiro",
            "dhCont",
            "xJust"
        })
        public static class Ide {

            @XmlElement(name = "cUF", required = true)
            protected String cuf;
            @XmlElement(name = "cCT", required = true)
            protected String cct;
            @XmlElement(name = "CFOP", required = true)
            protected String cfop;
            @XmlElement(required = true)
            protected String natOp;
            @XmlElement(required = true)
            protected String mod;
            @XmlElement(required = true)
            protected String serie;
            @XmlElement(name = "nCT", required = true)
            protected String nct;
            @XmlElement(required = true)
            protected String dhEmi;
            @XmlElement(required = true)
            protected String tpImp;
            @XmlElement(required = true)
            protected String tpEmis;
            @XmlElement(name = "cDV", required = true)
            protected String cdv;
            @XmlElement(required = true)
            protected String tpAmb;
            @XmlElement(required = true)
            protected String tpCTe;
            @XmlElement(required = true)
            protected String verProc;
            @XmlElement(required = true)
            protected String cMunEnv;
            @XmlElement(required = true)
            protected String xMunEnv;
            @XmlElement(name = "UFEnv", required = true)
            protected TUf ufEnv;
            @XmlElement(required = true)
            protected String modal;
            @XmlElement(required = true)
            protected String tpServ;
            @XmlElement(required = true)
            protected String indIEToma;
            @XmlElement(required = true)
            protected String dhSaidaOrig;
            @XmlElement(required = true)
            protected String dhChegadaDest;
            protected TGTVe.InfCte.Ide.Toma toma;
            protected TGTVe.InfCte.Ide.TomaTerceiro tomaTerceiro;
            protected String dhCont;
            protected String xJust;

            /**
             * Gets the value of the cuf property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCUF() {
                return cuf;
            }

            /**
             * Sets the value of the cuf property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCUF(String value) {
                this.cuf = value;
            }

            /**
             * Gets the value of the cct property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCCT() {
                return cct;
            }

            /**
             * Sets the value of the cct property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCCT(String value) {
                this.cct = value;
            }

            /**
             * Gets the value of the cfop property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCFOP() {
                return cfop;
            }

            /**
             * Sets the value of the cfop property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCFOP(String value) {
                this.cfop = value;
            }

            /**
             * Gets the value of the natOp property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getNatOp() {
                return natOp;
            }

            /**
             * Sets the value of the natOp property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setNatOp(String value) {
                this.natOp = value;
            }

            /**
             * Gets the value of the mod property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getMod() {
                return mod;
            }

            /**
             * Sets the value of the mod property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setMod(String value) {
                this.mod = value;
            }

            /**
             * Gets the value of the serie property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getSerie() {
                return serie;
            }

            /**
             * Sets the value of the serie property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setSerie(String value) {
                this.serie = value;
            }

            /**
             * Gets the value of the nct property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getNCT() {
                return nct;
            }

            /**
             * Sets the value of the nct property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setNCT(String value) {
                this.nct = value;
            }

            /**
             * Gets the value of the dhEmi property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDhEmi() {
                return dhEmi;
            }

            /**
             * Sets the value of the dhEmi property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDhEmi(String value) {
                this.dhEmi = value;
            }

            /**
             * Gets the value of the tpImp property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTpImp() {
                return tpImp;
            }

            /**
             * Sets the value of the tpImp property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTpImp(String value) {
                this.tpImp = value;
            }

            /**
             * Gets the value of the tpEmis property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTpEmis() {
                return tpEmis;
            }

            /**
             * Sets the value of the tpEmis property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTpEmis(String value) {
                this.tpEmis = value;
            }

            /**
             * Gets the value of the cdv property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCDV() {
                return cdv;
            }

            /**
             * Sets the value of the cdv property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCDV(String value) {
                this.cdv = value;
            }

            /**
             * Gets the value of the tpAmb property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTpAmb() {
                return tpAmb;
            }

            /**
             * Sets the value of the tpAmb property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTpAmb(String value) {
                this.tpAmb = value;
            }

            /**
             * Gets the value of the tpCTe property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTpCTe() {
                return tpCTe;
            }

            /**
             * Sets the value of the tpCTe property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTpCTe(String value) {
                this.tpCTe = value;
            }

            /**
             * Gets the value of the verProc property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getVerProc() {
                return verProc;
            }

            /**
             * Sets the value of the verProc property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setVerProc(String value) {
                this.verProc = value;
            }

            /**
             * Gets the value of the cMunEnv property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCMunEnv() {
                return cMunEnv;
            }

            /**
             * Sets the value of the cMunEnv property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCMunEnv(String value) {
                this.cMunEnv = value;
            }

            /**
             * Gets the value of the xMunEnv property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXMunEnv() {
                return xMunEnv;
            }

            /**
             * Sets the value of the xMunEnv property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXMunEnv(String value) {
                this.xMunEnv = value;
            }

            /**
             * Gets the value of the ufEnv property.
             * 
             * @return
             *     possible object is
             *     {@link TUf }
             *     
             */
            public TUf getUFEnv() {
                return ufEnv;
            }

            /**
             * Sets the value of the ufEnv property.
             * 
             * @param value
             *     allowed object is
             *     {@link TUf }
             *     
             */
            public void setUFEnv(TUf value) {
                this.ufEnv = value;
            }

            /**
             * Gets the value of the modal property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getModal() {
                return modal;
            }

            /**
             * Sets the value of the modal property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setModal(String value) {
                this.modal = value;
            }

            /**
             * Gets the value of the tpServ property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTpServ() {
                return tpServ;
            }

            /**
             * Sets the value of the tpServ property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTpServ(String value) {
                this.tpServ = value;
            }

            /**
             * Gets the value of the indIEToma property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getIndIEToma() {
                return indIEToma;
            }

            /**
             * Sets the value of the indIEToma property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setIndIEToma(String value) {
                this.indIEToma = value;
            }

            /**
             * Gets the value of the dhSaidaOrig property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDhSaidaOrig() {
                return dhSaidaOrig;
            }

            /**
             * Sets the value of the dhSaidaOrig property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDhSaidaOrig(String value) {
                this.dhSaidaOrig = value;
            }

            /**
             * Gets the value of the dhChegadaDest property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDhChegadaDest() {
                return dhChegadaDest;
            }

            /**
             * Sets the value of the dhChegadaDest property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDhChegadaDest(String value) {
                this.dhChegadaDest = value;
            }

            /**
             * Gets the value of the toma property.
             * 
             * @return
             *     possible object is
             *     {@link TGTVe.InfCte.Ide.Toma }
             *     
             */
            public TGTVe.InfCte.Ide.Toma getToma() {
                return toma;
            }

            /**
             * Sets the value of the toma property.
             * 
             * @param value
             *     allowed object is
             *     {@link TGTVe.InfCte.Ide.Toma }
             *     
             */
            public void setToma(TGTVe.InfCte.Ide.Toma value) {
                this.toma = value;
            }

            /**
             * Gets the value of the tomaTerceiro property.
             * 
             * @return
             *     possible object is
             *     {@link TGTVe.InfCte.Ide.TomaTerceiro }
             *     
             */
            public TGTVe.InfCte.Ide.TomaTerceiro getTomaTerceiro() {
                return tomaTerceiro;
            }

            /**
             * Sets the value of the tomaTerceiro property.
             * 
             * @param value
             *     allowed object is
             *     {@link TGTVe.InfCte.Ide.TomaTerceiro }
             *     
             */
            public void setTomaTerceiro(TGTVe.InfCte.Ide.TomaTerceiro value) {
                this.tomaTerceiro = value;
            }

            /**
             * Gets the value of the dhCont property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDhCont() {
                return dhCont;
            }

            /**
             * Sets the value of the dhCont property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDhCont(String value) {
                this.dhCont = value;
            }

            /**
             * Gets the value of the xJust property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXJust() {
                return xJust;
            }

            /**
             * Sets the value of the xJust property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXJust(String value) {
                this.xJust = value;
            }


            /**
             * <p>Java class for anonymous complex type.
             * 
             * <p>The following schema fragment specifies the expected content contained within this class.
             * 
             * <pre>
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <element name="toma">
             *           <simpleType>
             *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               <whiteSpace value="preserve"/>
             *               <enumeration value="0"/>
             *               <enumeration value="1"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "toma"
            })
            public static class Toma {

                @XmlElement(required = true)
                protected String toma;

                /**
                 * Gets the value of the toma property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getToma() {
                    return toma;
                }

                /**
                 * Sets the value of the toma property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setToma(String value) {
                    this.toma = value;
                }

            }


            /**
             * <p>Java class for anonymous complex type.
             * 
             * <p>The following schema fragment specifies the expected content contained within this class.
             * 
             * <pre>
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <element name="toma">
             *           <simpleType>
             *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               <whiteSpace value="preserve"/>
             *               <enumeration value="2"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *         <choice>
             *           <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
             *           <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
             *         </choice>
             *         <element name="IE" minOccurs="0">
             *           <simpleType>
             *             <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
             *             </restriction>
             *           </simpleType>
             *         </element>
             *         <element name="xNome">
             *           <simpleType>
             *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
             *               <maxLength value="60"/>
             *               <minLength value="2"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *         <element name="xFant" minOccurs="0">
             *           <simpleType>
             *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
             *               <maxLength value="60"/>
             *               <minLength value="2"/>
             *             </restriction>
             *           </simpleType>
             *         </element>
             *         <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
             *         <element name="enderToma" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
             *         <element name="email" type="{http://www.portalfiscal.inf.br/cte}TEmail" minOccurs="0"/>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "toma",
                "cnpj",
                "cpf",
                "ie",
                "xNome",
                "xFant",
                "fone",
                "enderToma",
                "email"
            })
            public static class TomaTerceiro {

                @XmlElement(required = true)
                protected String toma;
                @XmlElement(name = "CNPJ")
                protected String cnpj;
                @XmlElement(name = "CPF")
                protected String cpf;
                @XmlElement(name = "IE")
                protected String ie;
                @XmlElement(required = true)
                protected String xNome;
                protected String xFant;
                protected String fone;
                @XmlElement(required = true)
                protected TEndereco enderToma;
                protected String email;

                /**
                 * Gets the value of the toma property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getToma() {
                    return toma;
                }

                /**
                 * Sets the value of the toma property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setToma(String value) {
                    this.toma = value;
                }

                /**
                 * Gets the value of the cnpj property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getCNPJ() {
                    return cnpj;
                }

                /**
                 * Sets the value of the cnpj property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setCNPJ(String value) {
                    this.cnpj = value;
                }

                /**
                 * Gets the value of the cpf property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getCPF() {
                    return cpf;
                }

                /**
                 * Sets the value of the cpf property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setCPF(String value) {
                    this.cpf = value;
                }

                /**
                 * Gets the value of the ie property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getIE() {
                    return ie;
                }

                /**
                 * Sets the value of the ie property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setIE(String value) {
                    this.ie = value;
                }

                /**
                 * Gets the value of the xNome property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getXNome() {
                    return xNome;
                }

                /**
                 * Sets the value of the xNome property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setXNome(String value) {
                    this.xNome = value;
                }

                /**
                 * Gets the value of the xFant property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getXFant() {
                    return xFant;
                }

                /**
                 * Sets the value of the xFant property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setXFant(String value) {
                    this.xFant = value;
                }

                /**
                 * Gets the value of the fone property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getFone() {
                    return fone;
                }

                /**
                 * Sets the value of the fone property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setFone(String value) {
                    this.fone = value;
                }

                /**
                 * Gets the value of the enderToma property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link TEndereco }
                 *     
                 */
                public TEndereco getEnderToma() {
                    return enderToma;
                }

                /**
                 * Sets the value of the enderToma property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link TEndereco }
                 *     
                 */
                public void setEnderToma(TEndereco value) {
                    this.enderToma = value;
                }

                /**
                 * Gets the value of the email property.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getEmail() {
                    return email;
                }

                /**
                 * Sets the value of the email property.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setEmail(String value) {
                    this.email = value;
                }

            }

        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <choice>
         *           <element name="CNPJ" type="{http://www.portalfiscal.inf.br/cte}TCnpjOpc"/>
         *           <element name="CPF" type="{http://www.portalfiscal.inf.br/cte}TCpf"/>
         *         </choice>
         *         <element name="IE" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TIeDest">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="xNome">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <maxLength value="60"/>
         *               <minLength value="2"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="xFant" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TString">
         *               <maxLength value="60"/>
         *               <minLength value="2"/>
         *             </restriction>
         *           </simpleType>
         *         </element>
         *         <element name="fone" type="{http://www.portalfiscal.inf.br/cte}TFone" minOccurs="0"/>
         *         <element name="enderReme" type="{http://www.portalfiscal.inf.br/cte}TEndereco"/>
         *         <element name="email" minOccurs="0">
         *           <simpleType>
         *             <restriction base="{http://www.portalfiscal.inf.br/cte}TEmail">
         *             </restriction>
         *           </simpleType>
         *         </element>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "cnpj",
            "cpf",
            "ie",
            "xNome",
            "xFant",
            "fone",
            "enderReme",
            "email"
        })
        public static class Rem {

            @XmlElement(name = "CNPJ")
            protected String cnpj;
            @XmlElement(name = "CPF")
            protected String cpf;
            @XmlElement(name = "IE")
            protected String ie;
            @XmlElement(required = true)
            protected String xNome;
            protected String xFant;
            protected String fone;
            @XmlElement(required = true)
            protected TEndereco enderReme;
            protected String email;

            /**
             * Gets the value of the cnpj property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCNPJ() {
                return cnpj;
            }

            /**
             * Sets the value of the cnpj property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCNPJ(String value) {
                this.cnpj = value;
            }

            /**
             * Gets the value of the cpf property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCPF() {
                return cpf;
            }

            /**
             * Sets the value of the cpf property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCPF(String value) {
                this.cpf = value;
            }

            /**
             * Gets the value of the ie property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getIE() {
                return ie;
            }

            /**
             * Sets the value of the ie property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setIE(String value) {
                this.ie = value;
            }

            /**
             * Gets the value of the xNome property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXNome() {
                return xNome;
            }

            /**
             * Sets the value of the xNome property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXNome(String value) {
                this.xNome = value;
            }

            /**
             * Gets the value of the xFant property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getXFant() {
                return xFant;
            }

            /**
             * Sets the value of the xFant property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setXFant(String value) {
                this.xFant = value;
            }

            /**
             * Gets the value of the fone property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getFone() {
                return fone;
            }

            /**
             * Sets the value of the fone property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setFone(String value) {
                this.fone = value;
            }

            /**
             * Gets the value of the enderReme property.
             * 
             * @return
             *     possible object is
             *     {@link TEndereco }
             *     
             */
            public TEndereco getEnderReme() {
                return enderReme;
            }

            /**
             * Sets the value of the enderReme property.
             * 
             * @param value
             *     allowed object is
             *     {@link TEndereco }
             *     
             */
            public void setEnderReme(TEndereco value) {
                this.enderReme = value;
            }

            /**
             * Gets the value of the email property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getEmail() {
                return email;
            }

            /**
             * Sets the value of the email property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setEmail(String value) {
                this.email = value;
            }

        }

    }

}

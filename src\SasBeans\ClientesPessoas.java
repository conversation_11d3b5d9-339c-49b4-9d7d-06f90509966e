package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class ClientesPessoas {

    private String CodCli;
    private BigDecimal CodFil;
    private BigDecimal CodPessoa;
    private BigDecimal CodPessoaWEB;

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("1");
        }
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("0");

        }
    }

    public BigDecimal getCodPessoaWEB() {
        return CodPessoaWEB;
    }

    public void setCodPessoaWEB(String CodPessoaWEB) {
        try {
            this.CodPessoaWEB = new BigDecimal(CodPessoaWEB);
        } catch (Exception e) {
            this.CodPessoaWEB = new BigDecimal("0");
        }
    }

}

package Dados;

import java.sql.*;

/**
 *
 * <AUTHOR>
 */
public class ConectaSyBase {

    private Connection conexao; //Cria a conexão propriamente dita  

    /**
     * Abre uma concexao com o banco Sybase
     *
     * @param URL - IP do banco
     * @param Porta - Porta que o banco escuta
     * @param Banco - Nome do banco
     * @param usuario - Usuario - importante lembrar que deve ser criado um
     * usuario para conexao externa
     * @param senha - Senha para conexao do banco
     * @throws Exception - Gera excesao em caso de problema com o JDBC que é web
     * ou em caso de banco inacessível
     */
    public void conectar(String URL, String Porta, String Banco, String usuario, String senha) throws Exception {
        String url = "jdbc:jtds:sybase://" + URL + ":" + Porta + "/" + Banco;
        //String url = "jdbc:sybase:Tds:"+URL+":"+Porta+"?ServiceName="+Ban<PERSON>;
        //String driver = "com.sybase.jdbc3.jdbc.SybDriver";  
        String driver = "net.sourceforge.jtds.jdbc.Driver";
        try {
            Class.forName(driver); //Nesse momento eu carrego o driver definido anteriormente  
            conexao = DriverManager.getConnection(url, usuario, senha); //Aqui eu crio a conexão com o banco através do objeto "conexao" criado anteriormente do tipo "Connection"  
        } catch (ClassNotFoundException ex) {
            throw new Exception("Erro de classe de conexao - " + ex.getMessage());
        } catch (SQLException e) {
            throw new Exception("Erro de fonte de dados - " + e.getMessage());
        }

    }

    /**
     * Retorna um Statement da conexao Sempre tratar com try catch(Exception)
     */
    public Statement getStmt() throws Exception {
        try {
            return conexao.createStatement();
        } catch (SQLException ex) {
            throw new Exception("Erro criando Statement - " + ex.getMessage());
        }
    }

    /**
     * Retorna um PreparedStatement do sql passado da conexao atual
     *
     * @param sql que sera usado Sempre tratar com try catch(Exception)
     */
    public PreparedStatement getState(String sql) throws Exception {
        try {
            return conexao.prepareStatement(sql);
        } catch (SQLException ex) {
            throw new Exception("Erro criando PrepareStatement- " + ex.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class TOKENS {
    
    private String Codigo;
    private String BancoDados;
    private String Modulo;
    private String Chave;
    private String Data;
    private String Hora;
    private String DtValid;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getBancoDados() {
        return BancoDados;
    }

    public void setBancoDados(String BancoDados) {
        this.BancoDados = BancoDados;
    }

    public String getModulo() {
        return Modulo;
    }

    public void setModulo(String Modulo) {
        this.Modulo = Modulo;
    }

    public String getChave() {
        return Chave;
    }

    public void setChave(String Chave) {
        this.Chave = Chave;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getDtValid() {
        return DtValid;
    }

    public void setDtValid(String DtValid) {
        this.DtValid = DtValid;
    }
}

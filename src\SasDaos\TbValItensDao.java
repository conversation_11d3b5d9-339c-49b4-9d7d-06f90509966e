/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TbValItens;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TbValItensDao {

    public boolean gravaTbItensDao(TbValItens tbvalitens, Persistencia persistencia) {
        boolean retorno;
        String sql = "insert into tbvalitens (Tabela, Codigo, Ordem, Descricao, Operador,"
                + " Dt_Alter, Hr_alter) "
                + "values (?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbvalitens.getTabela());
            consulta.setInt(tbvalitens.getCodigo());
            consulta.setInt(tbvalitens.getOrdem());
            consulta.setString(tbvalitens.getDescricao());
            consulta.setString(tbvalitens.getOperador());
            consulta.setString(tbvalitens.getDt_Alter());
            consulta.setString(tbvalitens.getHr_Alter());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    public List<TbValItens> buscaTbValItens(int iCod, Persistencia persistencia) throws Exception {
        List<TbValItens> listTbValItens;
        try {
            TbValItens tbvalitens;
            Consulta consult = new Consulta("select Tabela, Codigo, Ordem, Descricao "
                    + "from tbvalitens where codigo=?", persistencia);
            consult.setInt(iCod);
            consult.select();
            listTbValItens = new ArrayList();
            while (consult.Proximo()) {
                tbvalitens = new TbValItens();
                tbvalitens.setTabela(consult.getInt("Tabela"));
                tbvalitens.setCodigo(consult.getInt("Codigo"));
                tbvalitens.setOrdem(consult.getInt("Ordem"));
                tbvalitens.setAba(consult.getString("Descricao"));
//              consulta.setAba(consult.getString("Descricao").substring( consult.getString("Descricao").indexOf(".")));
//              tbvalitens.setPergunta(consult.getString("Descricao").
//              substring(consult.getString("Descricao").indexOf(".")+1,consult.getString("Descricao").length()));

                listTbValItens.add(tbvalitens);
            }
            consult.Close();
        } catch (Exception e) {
            listTbValItens = null;
            throw new Exception("Falha ao buscar..." + e.getMessage());
        }
        return listTbValItens;
    }

    public void atualizarTbValItens(TbValItens tbvalitens, Persistencia persistencia) throws Exception {
        String sql = "update tbvalitens set Tabela=?, Codigo=?, Ordem=?, Desccricao=?, Operador=?, "
                + "Dt_Alter=?, Hr_Alter=? "
                + "where Tabela=?, Codigo=?, Orderm=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbvalitens.getTabela());
            consulta.setInt(tbvalitens.getCodigo());
            consulta.setInt(tbvalitens.getOrdem());
            consulta.setString(tbvalitens.getDescricao());
            consulta.setString(tbvalitens.getOperador());
            consulta.setString(tbvalitens.getHr_Alter());
            consulta.setString(tbvalitens.getHr_Alter());
            consulta.setInt(tbvalitens.getTabela());
            consulta.setInt(tbvalitens.getCodigo());
            consulta.setInt(tbvalitens.getOrdem());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao atualizar..." + e.getMessage());
        }
    }

    public void excluirTbValItens(TbValItens tbvalitens, Persistencia persistencia) throws Exception {
        String sql = "delete from tbvalitens "
                + "where Tabela=?, Codigo=?, Orderm=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbvalitens.getTabela());
            consulta.setInt(tbvalitens.getCodigo());
            consulta.setInt(tbvalitens.getOrdem());
            consulta.delete();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Erro ao excluir..." + e.getMessage());
        }
    }

}

/*
 */
package br.com.sasw.lazydatamodels;

import Controller.PstServ.PstServSatMobWeb;
import Dados.Persistencia;
import SasBeans.PstServ;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PstServLazyList extends LazyDataModel<PstServ> {

    private static final long serialVersionUID = 1L;
    private List<PstServ> postos;
    private final PstServSatMobWeb pstservsatmobweb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public PstServLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.pstservsatmobweb = new PstServSatMobWeb();
        this.codPessoa = codPessoa;
        this.persistencia = pst;
    }

    @Override
    public List<PstServ> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.postos = this.pstservsatmobweb.ListagemPaginada(first, pageSize, filters, this.codPessoa, this.persistencia);

            // set the total of players
            setRowCount(this.pstservsatmobweb.Contagem(filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.postos;
    }

    @Override
    public Object getRowKey(PstServ posto) {
        return posto.getSecao();
    }

    @Override
    public PstServ getRowData(String secao) {
        for (PstServ posto : this.postos) {
            if (secao.equals(posto.getSecao())) {
                return posto;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

}

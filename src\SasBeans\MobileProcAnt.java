package SasBeans;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class MobileProcAnt {

    private BigDecimal sequencia;

    private String param;
    private String comando;
    private String dtProcAnt;
    private String dtRecebido;
    private String hrRecebido;

    public MobileProcAnt() {
        sequencia = new BigDecimal("0");

        param = "";
        comando = "";
        dtProcAnt = "";
        dtRecebido = "";
        hrRecebido = "";
    }

    public BigDecimal getSequencia() {
        return sequencia;
    }

    public void setSequencia(BigDecimal sequencia) {
        this.sequencia = sequencia;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getComando() {
        return comando;
    }

    public void setComando(String comando) {
        this.comando = comando;
    }

    public String getDtProcAnt() {
        return dtProcAnt;
    }

    public void setDtProcAnt(String dtProcAnt) {
        this.dtProcAnt = dtProcAnt;
    }

    public String getDtRecebido() {
        return dtRecebido;
    }

    public void setDtRecebido(String dtRecebido) {
        this.dtRecebido = dtRecebido;
    }

    public String getHrRecebido() {
        return hrRecebido;
    }

    public void setHrRecebido(String hrRecebido) {
        this.hrRecebido = hrRecebido;
    }

}

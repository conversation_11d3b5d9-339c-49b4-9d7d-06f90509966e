package Dados.Pool;

import Dados.OLD.Persistencia_OLD;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PoolClientesConexoes {

    private List<Persistencia_OLD> pool = new ArrayList();
    private String Empresa;
    private int Fornecer = 0;
    private int tamanhoPool = 1;
    private boolean jtds = false;
    private boolean mysql = false;

    /**
     * Define o uso do driver jtds
     *
     * @param jtds
     */
    public void setJtds(boolean jtds) {
        this.jtds = jtds;
    }

    /**
     * Define o uso do driver mysql
     *
     * @param mysql
     */
    public void setMysql(boolean mysql) {
        this.mysql = mysql;
    }

    /**
     * Retorna o Pool de conexões do cliente
     *
     * @return
     */
    public List<Persistencia_OLD> getPool() {
        return pool;
    }

    /**
     * Devolve uma conexão para a aplicação
     *
     * @return
     */
    public Persistencia_OLD getConexao() {
        Persistencia_OLD retornar = pool.get(Fornecer);
        Fornecer++;
        if (Fornecer >= tamanhoPool) {
            Fornecer = 0;
        }
        return retornar;
    }

    /**
     * Retorna a Empresa desse pool de conexões
     *
     * @return
     */
    public String getEmpresa() {
        return Empresa;
    }

    /**
     * Determina a Empresa desse pool de conexões
     *
     * @param Empresa
     */
    public void setEmpresa(String Empresa) {
        this.Empresa = Empresa;
    }

    /**
     * Gera o pool de conexões para os dados passados
     *
     * @param IP - IP do Banco
     * @param Login - Login do Banco
     * @param Senha - Senha do Banco
     * @param TamanhoPool - Quantidade de conexões por cliente mínimo 1
     * @throws Exception - Pode gerar exception em erros de conexão
     */
    public void MontaPool(String IP, String Login, String Senha, int TamanhoPool) throws Exception {
        try {
            tamanhoPool = TamanhoPool;
            for (int i = 1; i <= tamanhoPool; i++) {
                Persistencia_OLD conexao = new Persistencia_OLD();
                conexao.setJtds(jtds);
                conexao.setMysql(mysql);
                conexao.ConexaoDireta(IP, Login, Senha);
                conexao.setEmpresa(Empresa);
                pool.add(conexao);
            }
        } catch (Exception e) {
            throw new Exception("Falha ao montar Pool de conexao - " + e.getMessage());
        }
    }
}

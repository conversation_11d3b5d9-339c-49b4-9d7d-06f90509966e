package br.com.sasw.lazydatamodels;

import Controller.Graficos.GraficoSatMobWeb;
import Dados.Persistencia;
import SasBeans.GraficoBean;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class GraficoLazyList extends LazyDataModel<GraficoBean> {

    private static final long serialVersionUID = 1L;
    private List<GraficoBean> graficos;
    private final GraficoSatMobWeb graficoSatMobWeb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public GraficoLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.graficoSatMobWeb = new GraficoSatMobWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<GraficoBean> load(int primeiro, int linhas, String sortField, SortOrder sortOrder, Map filtros) {
        try {
            this.graficos = this.graficoSatMobWeb.listagemPaginada(primeiro, linhas, filtros, this.codPessoa, this.persistencia);

//            setRowCount(this.graficoSatMobWeb.contagem(filtros, this.codPessoa, this.persistencia));
            setPageSize(linhas);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            System.out.println("br.com.sasw.lazydatamodels.MovimentacoesLazyList.load()" + e.getMessage());
        }
        return this.graficos;
    }

}

<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">
      <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{tbval.Listar}" />
            </f:metadata>
            <p:growl id="msgs" />
            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-12">
                                    <img src="../assets/img/icone_satmob_supervisoesrecentes.png" height="40" width="40"/> 
                                    #{localemsgs.TbVal}
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{tbval.nomeFilial}
                                    </div>
                                    <div class="ui-grid-col-8">
                                        #{localemsgs.QtdTbVal}: #{tbval.lista.size()}
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>
                <div id="mainContainer">
                    <h:form id="main">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-12">
                                    <p:panel style="display: inline;">
                                        <p:dataTable id="tabela" value="#{tbval.lista}" styleClass="tabela"
                                                     var="lista" rowKey="#{lista.tabela}" resizableColumns="true"
                                                     scrollable="true"
                                                     selectionMode="single" selection="#{tbval.selecionado}" style="font-size: 12px; background: white">
                                            <p:column headerText="#{localemsgs.Codigo}" style="width: 55px">
                                                <h:outputText value="#{lista.codigo}" title="#{lista.codigo}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Descricao}">
                                                <h:outputText value="#{lista.descricao}" title="#{lista.descricao}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador}" style="width: 110px">
                                                <h:outputText value="#{lista.operador}" title="#{lista.operador}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 90px">
                                                <h:outputText value="#{lista.dt_Alter}" title="#{lista.dt_Alter}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 60px">
                                                <h:outputText value="#{lista.hr_Alter}" title="#{lista.hr_Alter}"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </div>
                            </div>
                        </div>
                        <p:panel style="position: fixed; z-index: 1"> 
                            <div style=" top: 100px; right: 5px; position: fixed">
                                <p:commandLink title="#{localemsgs.Cadastrar}" update="cadastrar"
                                                  oncomplete="PF('dlgCadastrar').show();">
                                   <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style=" top: 0px; right: 5px; position: fixed">
                                <p:commandLink title="#{localemsgs.Voltar}"
                                               action="#{login.voltar}">
                                    <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </h:form>

                    <!-- Incluir Questão  -->
                    <p:dialog header="#{localemsgs.Cadastrar}" widgetVar="dlgCadastrar"
                            modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                            style="background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px">
                        <p:panel id="cadastrar" style="background-color: transparent">
                            <h:form>
                                <div class="form-inline">
                                    <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "/>
                                    <p:inputText id="descricao" value="#{tbval.novo.descricao}"
                                             required="true" label="#{localemsgs.Descricao}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Descricao}" size="50"/>
                                    <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                </div>

                                <div class="form-inline" style="top:270px;position: absolute" >
                                    <p:commandLink id="btnLogar" action="#{tbval.Cadastrar}" update=":msgs cabecalho :main:tabela :cadastrar"
                                                  title="#{localemsgs.Cadastrar}" oncomplete="PF('dlgCadastrar').hide();">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                                <div class="col-sm-12 text-center" style="height: 32px">
                                    <p:ajaxStatus>
                                        <f:facet name="start">
                                            <p:graphicImage value="../assets/img/ajax-loader.gif" />
                                        </f:facet>
                                    </p:ajaxStatus>
                                    <a href="../../../../../../AppData/Local/Temp/ogy12t0u.bmp"></a>
                                </div>
                            </h:form>
                        </p:panel>
                    </p:dialog>

                </div>
            </div>
            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>

                    <p:ajaxStatus class="status" >
                        <f:facet name="start">
                            <p:graphicImage value="../assets/img/ajax-loader.gif" style="height: 20px; width: 20px"/>
                        </f:facet>
                    </p:ajaxStatus>
                </div>
                <div class="footer-body" id="footer-body">
                    <div class="container">
                    <div class="col-sm-3">
                        <table class="footer-time">
                            <tr>
                                <td>
                                    <p:clock pattern="HH:mm:ss" />
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h:outputText value="#{localeController.mostraData}" />
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-sm-6">
                        <table class="footer-user">
                            <tr>
                                <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-sm-3">
                        <table class="footer-logos">
                            <tr>
                                <td><img src="../assets/img/logo_satweb.png" /></td>
                                <td>
                                    <h:form>
                                        <h:commandLink actionListener="#{localeController.increment}" 
                                                       action="#{localeController.getLocales}" >
                                            <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                        </h:commandLink>
                                    </h:form>   
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function(e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>


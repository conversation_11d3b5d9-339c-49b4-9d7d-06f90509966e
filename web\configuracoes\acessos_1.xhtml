<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/usuarios.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{acessos.persistencias(login.pp, login.satellite)}" />
            </f:metadata>
            <p:growl id="msgs" />
            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-12">
                                    <img src="../assets/img/icone_satmob_usuarios.png" height="40" width="40"/> 
                                    #{localemsgs.Usuarios}
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{acessos.nomeFilial}
                                    </div>
                                    <div class="ui-grid-col-8">
                                        #{localemsgs.QtdUsuarios}: #{acessos.total}
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>
                <h:form id="main">
                    <p:hotkey bind="p" update="pesquisar" actionListener="#{acessos.PrePesquisar}" oncomplete="PF('dlgPesquisar').show();"/> 
                    <p:hotkey bind="e" actionListener="#{acessos.buttonAction}" update="formEditar:editar msgs"/> 
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela" value="#{acessos.allAcessos}" paginator="true" rows="15" lazy="true"
                                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Usuarios}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista" selection="#{acessos.selecionado}"
                                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true" scrollWidth="100%"
                                                 style="font-size: 12px; background: white">
                                        <p:ajax event="rowDblselect" listener="#{acessos.dblSelect}" update="formEditar msgs"/>
                                        <p:column headerText="#{localemsgs.Filial}" style="width: 40px">
                                            <h:outputText value="#{lista.saspw.codFil}" title="#{lista.saspw.codFil}" >
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nome}" style="width: 280px">
                                            <h:outputText value="#{lista.pessoa.nome}"
                                                          title="#{lista.pessoa.nome}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Email}" style="width: 215px">
                                            <h:outputText value="#{lista.pessoa.email}" title="#{lista.pessoa.email}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nivel}" style="width: 90px">
                                            <h:outputText value="#{lista.saspw.nivelOP}" title="#{lista.saspw.nivelOP}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Grupo}" style="width: 150px">
                                            <h:outputText value="#{lista.grupo.descricao}" title="#{lista.grupo.descricao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Descricao}" style="width: 260px">
                                            <h:outputText value="#{lista.saspw.descricao}" title="#{lista.saspw.descricao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Situacao}" style="width: 59px">
                                            <h:outputText value="#{lista.saspw.situacao}" title="#{lista.saspw.situacao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" style="width: 110px">
                                            <h:outputText value="#{lista.saspw.operador}" title="#{lista.saspw.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}"  style="width: 90px">
                                            <h:outputText value="#{lista.saspw.dt_Alter}" title="#{lista.saspw.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 60px">
                                            <h:outputText value="#{lista.saspw.hr_Alter}" title="#{lista.saspw.hr_Alter}"/>
                                        </p:column>
                                    </p:dataTable>
                                 </p:panel>
                            </div>
                        </div>
                    </div>
                    
                    <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Cadastrar}" update="formEditar:editar msgs formEditar"
                                           action="#{acessos.NovoUsuario}">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{acessos.buttonAction}"
                                           update="formEditar:editar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" update="pesquisar" actionListener="#{acessos.PrePesquisar}"
                                               oncomplete="PF('dlgPesquisar').show();">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style=" top: 0px; right: 5px; position: fixed">
                            <p:commandLink title="#{localemsgs.Voltar}"
                                                action="#{login.voltar}">
                                <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                    </p:panel>
                </h:form>
                <h:form class="form-inline" id="formEditar">
                    <p:dialog widgetVar="dlgEditar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" focus="formEditar:codpessoa"
                              style="background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px; background-repeat: no-repeat; ">
                        <script>
                            $(document).ready(function() {
                                //first unbind the original click event
                                PF('dlgEditar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgEditar').closeIcon.click(function(e) {
                                    $("#formEditar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                                
                                
                            })
                        </script>
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_usuarios.png" height="40" width="40"/> 
                            #{localemsgs.CadastrarUsuario}
                        </f:facet>
                        <p:panel id="editar" style="background-color: transparent;">
                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                                 oncomplete="PF('dlgEditar').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                            <p:focus context="formEditar:codpessoa"/>
                            
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="subFil" value="#{localemsgs.Filial}:"/>
                                <p:selectOneMenu id="subFil" value="#{acessos.filial}" converter="omnifaces.SelectItemsConverter"
                                                 filter="true" filterMatchMode="contains"  disabled="#{acessos.flag eq 2}"
                                                 style="width: 100%">
                                    <f:selectItems value="#{acessos.todasFiliais}" var="filial" itemValue="#{filial}" 
                                                   itemLabel="#{filial.descricao}"/>
                                    <p:ajax event="itemSelect" listener="#{acessos.SelecionarFilialUsuario}" update="formEditar:editar"/>
                                </p:selectOneMenu>
                                
                                <p:outputLabel for="codpessoa" value="#{localemsgs.Pessoa}:"/>
                                <p:autoComplete id="codpessoa" value="#{acessos.pessoa}" completeMethod="#{acessos.pessoas.ListarQueryValida}"
                                                required="true" label="#{localemsgs.Pessoa}" disabled="#{acessos.flag eq 2}" forceSelection="true"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Pessoa}" scrollHeight="200"
                                                style="width: 100%" styleClass="pessoa"
                                                var="ppl" itemLabel="#{ppl.nome}" itemValue="#{ppl}" converter="conversorPessoa">
                                    <p:ajax event="itemSelect" listener="#{acessos.selecionarPessoa}" update="formEditar:email msgs
                                            formEditar:tabs:panelFiliais
                                            formEditar:tabs:filiais
                                            formEditar:codpessoa
                                            formEditar:grupo
                                            formEditar:senha
                                            formEditar:nivel
                                            formEditar:confirmacao
                                            formEditar:motivo
                                            formEditar:descricao
                                            formEditar:usuario"/>
                                </p:autoComplete>
                                
                                <p:outputLabel for="email" value="#{localemsgs.Email}:"/>
                                <p:inputText value="#{acessos.pessoa.email}" readonly="true" id="email" style="width: 100%">
                                    <p:watermark for="email" value="#{localemsgs.Email}"/>
                                </p:inputText>
                            </p:panelGrid>
                            
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">    
                                <p:outputLabel for="nivel" value="#{localemsgs.Nivel}:" />
                                <p:selectOneMenu value="#{acessos.novo.saspw.nivelx}" id="nivel"
                                                 required="true" label="#{localemsgs.Nivel}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nivel}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Operacao}" itemValue="1"/>
                                    <f:selectItem itemLabel="#{localemsgs.Manutencao}" itemValue="2"/>
                                    <f:selectItem itemLabel="#{localemsgs.Gerencia}" itemValue="3"/>
                                    <f:selectItem itemLabel="#{localemsgs.PortalRH}" itemValue="4"/>
                                    <f:selectItem itemLabel="#{localemsgs.GTV}" itemValue="5"/>
                                    <f:selectItem itemLabel="#{localemsgs.AssinarGTV}" itemValue="7"/>
                                    <f:selectItem itemLabel="#{localemsgs.CofreInteligente}" itemValue="6"/>
                                    <f:selectItem itemLabel="#{localemsgs.SatMobEW}" itemValue="8" rendered="#{acessos.spm}"/>
                                    <f:selectItem itemLabel="#{localemsgs.SPMEW}" itemValue="8" rendered="#{!acessos.spm}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Administrador}" itemValue="9"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="grupo" value="#{localemsgs.Grupo}:" />
                                <p:selectOneMenu value="#{acessos.novo.grupo.codigo}" id="grupo" style="width: 100%"
                                                 required="true" label="#{localemsgs.Grupo}" filter="true" filterMatchMode="contains"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Grupo}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItems value="#{acessos.grupos}" var="grupos" itemValue="#{grupos.codigo}"
                                                   itemLabel="#{grupos.descricao}" noSelectionValue="Selecione"/>
                                </p:selectOneMenu>
                            </p:panelGrid>
                            
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="usuario" value="#{localemsgs.Usuario}:"/>
                                <p:inputText value="#{acessos.novo.saspw.nome}" id="usuario" style="width: 100%" disabled="#{acessos.flag eq 2}">
                                    <p:watermark for="usuario" value="#{localemsgs.Usuario}"/>
                                </p:inputText>
                            </p:panelGrid>
                            
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:" />
                                <p:selectOneMenu value="#{acessos.novo.saspw.situacao}" id="situacao"
                                                 required="true" label="#{localemsgs.Situacao}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                    <f:selectItem itemLabel="#{localemsgs.Bloqueado}" itemValue="B"/>
                                </p:selectOneMenu>
                                
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}:"/>
                                <p:inputText id="descricao" value="#{acessos.novo.saspw.descricao}"
                                             label="#{localemsgs.Descricao} " style="width: 100%">
                                    <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                </p:inputText>
                                
                                <p:outputLabel for="senha" value="#{localemsgs.Senha}:"/>
                                <p:password id="senha" value="#{acessos.novo.pessoa.PWWeb}" required="true" transient="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                            label="#{localemsgs.Senha}" feedback="true" redisplay="true" match="confirmacao"
                                            promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                            goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                            style="width: 100%">
                                    <f:validateRegex pattern="^[0-9]{5,20}$" for="senha"/>
                                    <p:watermark for="senha" value="#{localemsgs.Senha}"/>
                                </p:password>

                                <p:outputLabel for="confirmacao" value="#{localemsgs.Confirmacao}:" />
                                <p:password id="confirmacao" value="#{acessos.novo.pessoa.PWWeb}" redisplay="true"
                                            label="#{localemsgs.Senha}" style="width: 100%">
                                    <p:watermark for="confirmacao" value="#{localemsgs.Senha}"/>
                                </p:password>
                            </p:panelGrid>
                            
                            <p:blockUI block="formEditar:editar" trigger="formEditar:btnCadastrar"/>
                            <p:blockUI block="formEditar:editar" trigger="formEditar:btnLogar"/>
                                
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="motivo" value="#{localemsgs.Motivo}:"/>
                                <p:inputText id="motivo" value="#{acessos.novo.saspw.motivo}"
                                             label="#{localemsgs.Motivo}" style="width: 100%">
                                    <p:watermark for="motivo" value="#{localemsgs.Motivo}"/>
                                </p:inputText>
                                 

                                <p:commandLink id="btnCadastrar" update=":msgs :main:tabela cabecalho formEditar:editar"
                                               action="#{acessos.PermissoesGrupo}"
                                               title="#{localemsgs.Cadastrar}" rendered="#{acessos.flag eq 1}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink id="btnLogar" update=":msgs :main:tabela cabecalho formEditar:editar"
                                               action="#{acessos.Editar}"
                                               title="#{localemsgs.Editar}" rendered="#{acessos.flag eq 2}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:confirmDialog message="#{acessos.grupoSelecionado}" header="#{localemsgs.Confirmacao}"
                                                 showEffect="drop" appendTo="@(body)" width="300"
                                                 hideEffect="drop" widgetVar="permissaoGrupo"> 
                                    <p:commandButton value="#{localemsgs.Sim}" process="@this"
                                                     styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                     onclick="PF('dlgOk').show()"
                                                     update="msgs formEditar:tabs:permissoes"/>
                                    <p:commandButton value="#{localemsgs.Nao}" action="#{acessos.Cadastrar}" process="formEditar" partialSubmit="true"
                                                     styleClass="ui-confirmdialog-no" icon="ui-icon-close" update="msgs"
                                                     oncomplete="PF('permissaoGrupo').hide()"/>
                                </p:confirmDialog>

                                <p:dialog header="#{localemsgs.Permissoes}" widgetVar="dlgOk" resizable="false" appendTo="@(body)"
                                                draggable="false" closable="true" width="300">
                                    <p:panel id="permissoesDeGrupo" style="height: 150px">
                                        <div class="form-inline">
                                            <h:outputText value="#{localemsgs.Permissoes}:" style="position: absolute; float: left"/>
                                        </div>

                                        <p:spacer height="30px"/>

                                        <div class="form-inline">
                                            <p:outputLabel for="inclusao" value="#{localemsgs.Inclusao}:" style="position: absolute; float: left"/>
                                            <p:selectBooleanCheckbox id="inclusao" value="#{acessos.inclusao}"
                                                                     style="position: absolute; float: left; left: 100px"/>
                                        </div>

                                        <p:spacer height="30px"/>

                                        <div class="form-inline">
                                            <p:outputLabel for="alteracao" value="#{localemsgs.Alteracao}:" style="position: absolute; float: left"/>
                                            <p:selectBooleanCheckbox id="alteracao" value="#{acessos.alteracao}"
                                                                     style="position: absolute; float: left; left: 100px"/>
                                        </div>

                                        <p:spacer height="30px"/>

                                        <div class="form-inline">
                                            <p:outputLabel for="exclusao" value="#{localemsgs.Exclusao}:" style="position: absolute; float: left"/>
                                            <p:selectBooleanCheckbox id="exclusao" value="#{acessos.exclusao}" 
                                                                     style="position: absolute; float: left; left: 100px"/>
                                        </div>

                                        <p:spacer height="30px"/>

                                        <div class="form-inline">
                                            <p:commandLink id="btnPermGrupos" update=":msgs :main:tabela cabecalho formEditar:editar"
                                                       action="#{acessos.AdicionarPermissoes}" partialSubmit="true"
                                                       process="formEditar:permissoesDeGrupo"
                                                       title="#{localemsgs.Cadastrar}" style="float:left; left:15px; position: absolute">
                                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                            </p:commandLink>
                                            <p:blockUI block="formEditar:permissoesDeGrupo" trigger="formEditar:btnPermGrupos"/>
                                        </div>
                                    </p:panel>
                                </p:dialog>
                            </p:panelGrid>
                            
                            <p:tabView id="tabs" dynamic="true" cache="true" style="height: 210px;" class="tabs">
                                <p:tab class="tabs">
                                    <f:facet name="title">
                                        <h:outputText value="#{localemsgs.Filiais}" style="color:black"/>
                                    </f:facet>
                                    <p:panelGrid columns="2">
                                        <p:panel id="tabelaFiliais" class="panelTabela">
                                            <p:dataTable id="filiais" value="#{acessos.filiais}" scrollable="true" emptyMessage="#{localemsgs.SemRegistros}"
                                                         var="listaFiliais" rowKey="#{listaFiliais.codfilAc}" scrollHeight="100" sortBy="#{listaFiliais.codfilAc}"
                                                         scrollWidth="100%"
                                                         resizableColumns="true" selectionMode="single" selection="#{acessos.filialSelecionada}" styleClass="tabela"
                                                         style="font-size: 12px">
                                                <p:ajax event="rowSelect" listener="#{acessos.SelecionarFilial}"/>
                                                <p:column headerText="#{localemsgs.Filiais}" style="width: 35px">
                                                    <p:outputLabel value="#{listaFiliais.codfilAc}">
                                                        <f:convertNumber pattern="0000"/>
                                                    </p:outputLabel>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Descricao}" style="width: 239px">
                                                    <p:outputLabel value="#{listaFiliais.descricao}" title="#{listaFiliais.descricao}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Operador}" style="width: 122px">
                                                    <h:outputText value="#{listaFiliais.operador}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hora}" style="width: 49px">
                                                    <h:outputText value="#{listaFiliais.hr_Alter}"/>  
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Data}" style="width: 84px">
                                                    <h:outputText value="#{listaFiliais.dt_Alter}" title="#{listaFiliais.dt_Alter}" converter="conversorData"/>
                                                </p:column>
                                            </p:dataTable>
                                        </p:panel>
                                        <p:panel style="width: 30px; padding-right: 0px">
                                            <p:commandLink title="#{localemsgs.Adicionar}" id="adicionarFilial" process="@this"
                                                           oncomplete="PF('dlgFiliais').show()"
                                                           update="filiais msgs formEditar:tabs:panelFiliais">
                                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="30" height="30" />
                                            </p:commandLink>
                                            <p:spacer height="30px"/>
                                            <p:commandLink title="#{localemsgs.Remover}" process="@this" action="#{acessos.ApagarFilial}"
                                                           update="filiais msgs" >
                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="30" height="30" />
                                            </p:commandLink>

                                            <p:dialog header="#{localemsgs.AdicionarFilial}"
                                                      widgetVar="dlgFiliais" closable="true" resizable="false" width="400" height="50"
                                                      hideEffect="fade">
                                                <p:outputPanel id="panelFiliais">
                                                    <p:outputLabel for="addFil" value="#{localemsgs.Filial}:"
                                                                   style="position: absolute; float: left"/>
                                                    <p:selectOneMenu id="addFil" value="#{acessos.novaFilial}" 
                                                                     converter="omnifaces.SelectItemsConverter"
                                                                     style="position: absolute; float: left; left: 60px; width: 280px;"
                                                                     filterMatchMode="contains" filter="true">
                                                        <f:selectItems value="#{acessos.todasFiliais}" var="fil" itemValue="#{fil}" 
                                                                       itemLabel="#{fil.codfilAc} - #{fil.descricao}"/>
                                                        <p:ajax event="itemSelect" listener="#{acessos.SelecionarNovaFilial}"/>
                                                        <p:watermark for="addFil" value="#{localemsgs.Filial}"/>
                                                    </p:selectOneMenu>

                                                    <p:commandLink oncomplete="PF('dlgFiliais').hide()" action="#{acessos.AdicionarFilial}"
                                                                   title="#{localemsgs.Selecionar}" update="filiais msgs"
                                                                   partialSubmit="true" process="panelFiliais"
                                                                   style="position: absolute; float: left; left: 350px;">
                                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30" />
                                                    </p:commandLink>
                                                </p:outputPanel>
                                            </p:dialog>
                                        </p:panel>
                                    </p:panelGrid>
                                </p:tab>
                                <p:tab id="tabPemissoes">
                                    <f:facet name="title">
                                        <h:outputText value="#{localemsgs.Permissoes}" style="color:black"/>
                                    </f:facet>
                                    <p:panelGrid columns="2">
                                        <p:panel id="tabelaPermissoes" class="panelTabela">
                                            <p:dataTable id="permissoes" value="#{acessos.permissoes}" emptyMessage="#{localemsgs.SemRegistros}"
                                                         var="listaPermissoes" rowKey="#{listaPermissoes.saspwac.sistema}" scrollWidth="100%"
                                                         resizableColumns="true" scrollable="true" scrollHeight="100" styleClass="tabela"
                                                         style="font-size: 12px" selectionMode="single" selection="#{acessos.permissaoSelecionada}">
                                                <p:ajax event="rowSelect" listener="#{acessos.SelecionarPermissao}"/>
                                                <p:column headerText="#{localemsgs.Sistema}" style="width: 80px">
                                                    <p:outputLabel value="#{listaPermissoes.saspwac.sistema}">
                                                        <f:convertNumber pattern="0"/>
                                                    </p:outputLabel>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Descricao}" style="width: 120px">
                                                    <h:outputText value="#{listaPermissoes.sysdef.subSistema}"
                                                                  title="#{listaPermissoes.sysdef.subSistema}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Inclusao}" style="width: 60px">
                                                    <h:outputText value="#{listaPermissoes.saspwac.inclusao}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Alteracao}" style="width: 60px">
                                                    <p:outputLabel value="#{listaPermissoes.saspwac.alteracao}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Exclusao}" style="width: 60px">
                                                    <h:outputText value="#{listaPermissoes.saspwac.exclusao}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Operador}" style="width: 70px">
                                                    <h:outputText value="#{listaPermissoes.saspwac.operador}"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hora}" style="width: 49px">
                                                    <h:outputText value="#{listaPermissoes.saspwac.hr_Alter}"/>  
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                                    <h:outputText value="#{listaPermissoes.saspwac.dt_Alter}" 
                                                                  title="#{listaFiliais.saspwac.dt_Alter}"
                                                                  converter="conversorData"/>
                                                </p:column>
                                            </p:dataTable>
                                            <h:outputText id="qtdPermissoes" value="#{localemsgs.QtdPermissoes}: #{acessos.permissoes.size()}"
                                                          style="font-size: 12px"/>
                                        </p:panel>
                                        <p:panel style="width: 30px">
                                            <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{acessos.PrepararPermissao}"
                                                           oncomplete="PF('dlgAdicionarPermissoes').show()"
                                                           update="permissoes adicionarPermissoes formEditar:tabs:qtdPermissoes"> 
                                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="30" height="30" />
                                            </p:commandLink>
                                           <p:spacer height="30px"/>
                                            <p:commandLink title="#{localemsgs.Editar}" process="tabPemissoes"
                                                           action="#{acessos.AbrirDialogoPermissao}"
                                                           update="permissoes editarPermissoes msgs">
                                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="30" height="30" />
                                            </p:commandLink>
                                            <p:spacer height="30px"/>
                                            <p:commandLink title="#{localemsgs.Remover}" process="@this" action="#{acessos.ApagarPermissao}"
                                                           update="permissoes formEditar:tabs:qtdPermissoes">
                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="30" height="30" />
                                            </p:commandLink>

                                            <p:dialog widgetVar="dlgAdicionarPermissoes" header="#{localemsgs.Permissoes}"
                                                        resizable="false" dynamic="true" closable="true" 
                                                        width="400" showEffect="drop" hideEffect="drop">
                                                <p:panel id="adicionarPermissoes" style="background-color: transparent;">
                                                    <div class="form-inline">
                                                        <p:outputLabel for="addPermissao" value="#{localemsgs.Sistema}:"
                                                                   style="position: absolute; float: left"/>
                                                        <p:selectOneMenu id="addPermissao" value="#{acessos.novaPermissao.sysdef}" 
                                                                         converter="omnifaces.SelectItemsConverter"
                                                                         style="position: absolute; float: left; left: 100px; width: 250px;"
                                                                         filterMatchMode="contains" filter="true">
                                                            <f:selectItems value="#{acessos.todasPermissoes}" var="permissoes" itemValue="#{permissoes}" 
                                                                           itemLabel="#{permissoes.codigo} - #{permissoes.subSistema}"/>
                                                        </p:selectOneMenu>
                                                        <p:watermark for="addPermissao" value="#{localemsgs.Permissao}"/>

                                                    </div>

                                                    <p:spacer height="40px"/>

                                                    <div class="form-inline">
                                                        <p:outputLabel for="inclusao" value="#{localemsgs.Inclusao}:" style="position: absolute; float: left"/>
                                                        <p:selectBooleanCheckbox id="inclusao" value="#{acessos.inclusao}"
                                                                                 style="position: absolute; float: left; left: 100px"/>
                                                    </div>

                                                    <p:spacer height="30px"/>

                                                    <div class="form-inline">
                                                        <p:outputLabel for="alteracao" value="#{localemsgs.Alteracao}:" style="position: absolute; float: left"/>
                                                        <p:selectBooleanCheckbox id="alteracao" value="#{acessos.alteracao}"
                                                                                 style="position: absolute; float: left; left: 100px"/>
                                                    </div>

                                                    <p:spacer height="30px"/>

                                                    <div class="form-inline">
                                                        <p:outputLabel for="exclusao" value="#{localemsgs.Exclusao}:" style="position: absolute; float: left"/>
                                                        <p:selectBooleanCheckbox id="exclusao" value="#{acessos.exclusao}" 
                                                                                 style="position: absolute; float: left; left: 100px"/>
                                                    </div>

                                                    <p:spacer height="30px"/>

                                                    <div class="col-sm-12 text-center" style="height: 32px;">
                                                        <p:ajaxStatus>
                                                            <f:facet name="start">
                                                                <p:graphicImage value="../assets/img/ajax-loader.gif" />
                                                            </f:facet>
                                                        </p:ajaxStatus>
                                                    </div>
                                                    <div class="form-inline">
                                                        <p:commandLink oncomplete="PF('dlgAdicionarPermissoes').hide()" 
                                                                       action="#{acessos.AdicionarPermissao}" process="adicionarPermissoes"
                                                                       title="#{localemsgs.Selecionar}"
                                                                       update="permissoes adicionarPermissoes formEditar:tabs:qtdPermissoes">
                                                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                                        </p:commandLink>
                                                    </div>
                                                </p:panel>
                                            </p:dialog>

                                            <p:dialog widgetVar="dlgEditarPermissoes" 
                                                        resizable="false" dynamic="true" closable="true" 
                                                        width="400" showEffect="drop" hideEffect="drop">
                                                <f:facet name="header">
                                                    <h:outputText value="#{localemsgs.Permissoes}"/>
                                                </f:facet>
                                                <p:panel id="editarPermissoes" style="background-color: transparent;">
                                                    <div class="form-inline">
                                                        <p:outputLabel for="sistema" value="#{localemsgs.Sistema}:" style="position: absolute; float: left"/>
                                                        <p:inputText id="sistema" value="#{acessos.permissaoSelecionada.saspwac.sistema}" disabled="true"
                                                                 required="true" label="#{localemsgs.Sistema}" 
                                                                 style="position: absolute; float: left; left: 100px; width: 250px;"
                                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Sistema}">
                                                            <f:convertNumber pattern="0"/>
                                                        </p:inputText>
                                                    </div>

                                                    <p:spacer height="40px"/>

                                                    <div class="form-inline">
                                                        <p:outputLabel for="inclusaoEditar" value="#{localemsgs.Inclusao}:" style="position: absolute; float: left"/>
                                                        <p:selectBooleanCheckbox id="inclusaoEditar" value="#{acessos.inclusao}" 
                                                                                 style="position: absolute; float: left; left: 100px"/>
                                                    </div>

                                                    <p:spacer height="30px"/>

                                                    <div class="form-inline">
                                                        <p:outputLabel for="alteracaoEditar" value="#{localemsgs.Alteracao}:" style="position: absolute; float: left"/>
                                                        <p:selectBooleanCheckbox id="alteracaoEditar" value="#{acessos.alteracao}" 
                                                                                 style="position: absolute; float: left; left: 100px"/>
                                                    </div>

                                                    <p:spacer height="30px"/>

                                                    <div class="form-inline">
                                                        <p:outputLabel for="exclusaoEditar" value="#{localemsgs.Exclusao}:" style="position: absolute; float: left"/>
                                                        <p:selectBooleanCheckbox id="exclusaoEditar" value="#{acessos.exclusao}" 
                                                                                 style="position: absolute; float: left; left: 100px"/>
                                                    </div>

                                                    <p:spacer height="30px"/>

                                                    <div class="col-sm-12 text-center" style="height: 32px;">
                                                        <p:ajaxStatus>
                                                            <f:facet name="start">
                                                                <p:graphicImage value="../assets/img/ajax-loader.gif" />
                                                            </f:facet>
                                                        </p:ajaxStatus>
                                                    </div>
                                                    <div class="form-inline">
                                                        <p:commandLink oncomplete="PF('dlgEditarPermissoes').hide()" 
                                                                       action="#{acessos.EditarPermissao}" process="editarPermissoes"
                                                                       title="#{localemsgs.Selecionar}" update="permissoes">
                                                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                                        </p:commandLink>
                                                    </div>
                                                </p:panel>
                                            </p:dialog>
                                        </p:panel>
                                    </p:panelGrid>
                                </p:tab>
                                <p:tab id="tabClientes">
                                    <f:facet name="title">
                                        <h:outputText value="#{localemsgs.Clientes}" style="color:black"/>
                                    </f:facet>
                                    <p:panelGrid columns="2">
                                        <p:panel id="tabelaClientes" class="panelTabela">
                                            <p:dataTable id="clientes" value="#{acessos.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                                         var="listaClientes" rowKey="#{listaClientes.codCli}" scrollWidth="100%"
                                                         resizableColumns="true" scrollable="true" scrollHeight="95" styleClass="tabela"
                                                         style="font-size: 12px" selectionMode="single" 
                                                         selection="#{acessos.clienteSelecionado}">
                                                <p:ajax event="rowSelect" listener="#{acessos.SelecionarPessoaCliAut}"/>
                                                <p:column headerText="#{localemsgs.CodCli}" style="width: 58px">
                                                    <h:outputText value="#{listaClientes.codCli}" 
                                                                  style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.NRed}" style="width: 152px">
                                                    <h:outputText value="#{listaClientes.nomeCli}" 
                                                                  style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.CodFil}" style="width: 50px">
                                                    <h:outputText value="#{listaClientes.codFil}" converter="conversor0" 
                                                                  style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Codigo}" style="width: 52px">
                                                    <h:outputText value="#{listaClientes.codigo}" converter="conversor0" 
                                                                  style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Operador}" style="width: 70px">
                                                    <h:outputText value="#{listaClientes.operador}" 
                                                                  style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hora}" style="width: 49px">
                                                    <h:outputText value="#{listaClientes.hr_Alter}" 
                                                                  style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                                    <h:outputText value="#{listaClientes.dt_Alter}" title="#{listaClientes.dt_Alter}" 
                                                                  converter="conversorData" 
                                                                  style="#{listaClientes.flag_Excl eq '*' ? 'color: cyan' : 'color: black' }"/>
                                                </p:column>
                                            </p:dataTable>
                                            <div class="ui-grid ui-grid-responsive">
                                                <div class="ui-grid-row">
                                                    <div class="ui-grid-col-6">
                                                        <h:outputText id="qtdClientes" value="#{localemsgs.QtdClientes}: #{acessos.clientes.size()}"
                                                          style="font-size: 12px"/>
                                                    </div>
                                                    <div class="ui-grid-col-6">
                                                        <p:outputLabel for="checkboxCliente" value="#{localemsgs.ExibirExcluidos}: "
                                                              style="font-size: 12px"/>
                                                        <p:selectBooleanCheckbox 
                                                            id="checkboxCliente"
                                                            value="#{acessos.flag_exclPessoaCliAut}" 
                                                            style="font-size: 12px">
                                                            <p:ajax update="clientes adicionarClientes formEditar:tabs:qtdClientes msgs"
                                                                    listener="#{acessos.ListarPessoaCliAut}" />
                                                        </p:selectBooleanCheckbox>
                                                    </div>
                                                </div>
                                            </div>
                                        </p:panel>
                                        <p:panel style="width: 30px">
                                            <p:commandLink title="#{localemsgs.Adicionar}" process="@this" actionListener="#{acessos.NovoCliente}"
                                                           update="clientes adicionarClientes msgs formEditar:tabs:qtdClientes"> 
                                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="30" height="30" />
                                            </p:commandLink>
                                            <p:spacer height="30px"/>
                                            <p:commandLink title="#{localemsgs.Remover}" process="@this" action="#{acessos.ApagarCliente}"
                                                           update="clientes formEditar:tabs:qtdClientes msgs">
                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="30" height="30" />
                                            </p:commandLink>

                                            <p:dialog widgetVar="dlgAdicionarClientes" header="#{localemsgs.AdicionarCliente}"
                                                        resizable="false" dynamic="true" closable="true" 
                                                        width="400" showEffect="drop" hideEffect="drop">
                                                <p:panel id="adicionarClientes" style="background-color: transparent;">
                                                    <div class="form-inline">
                                                        <p:outputLabel for="addCliente" value="#{localemsgs.Cliente}:" 
                                                                       style="float: left;position:absolute;"/>
                                                        <p:autoComplete id="addCliente" value="#{acessos.todosClientesSelecao}" styleClass="cliente2"
                                                                        style="float: left;left: 100px;position:absolute; width: 275px"
                                                                        completeMethod="#{acessos.ListarClientes}"
                                                                        var="cli" itemLabel="#{cli.nome}" itemValue="#{cli}"
                                                                        converter="conversorCliente" scrollHeight="250">
                                                            <p:ajax event="itemSelect" listener="#{acessos.SelecionarCliente}" update="adicionarClientes msgs"/>
                                                        </p:autoComplete>
                                                        <p:watermark for="addCliente" value="#{localemsgs.Cliente}" />
                                                    </div>

                                                    <p:spacer height="40px"/>

                                                    <div class="form-inline">
                                                        <p:outputLabel for="fil" value="#{localemsgs.Filial}:" 
                                                                       style="float: left;position:absolute;" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                        <h:outputText id="fil" value="#{acessos.todosClientesSelecao.codFil}"
                                                                      rendered="#{acessos.todosClientesSelecao ne null}" converter="conversor0"
                                                                       style="float: left;left: 100px;position:absolute; font-weight: bold"/>
                                                        <p:spacer height="30px" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                    </div>
                                                    <div class="form-inline">
                                                        <p:outputLabel for="nred" value="#{localemsgs.NRed}:" 
                                                                       style="float: left;position:absolute;" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                        <h:outputText id="nred" value="#{acessos.todosClientesSelecao.NRed}"
                                                                      rendered="#{acessos.todosClientesSelecao ne null}"
                                                                       style="float: left;left: 100px;position:absolute; font-weight: bold"/>
                                                        <p:spacer height="30px" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                    </div>
                                                    <div class="form-inline">
                                                        <p:outputLabel for="ende" value="#{localemsgs.Ende}:" 
                                                                       style="float: left;position:absolute;" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                        <h:outputText id="ende" value="#{acessos.todosClientesSelecao.ende}"
                                                                      style="float: left;left: 100px;position:absolute; font-weight: bold"
                                                                      rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                        <p:spacer height="30px" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                    </div>
                                                    <div class="form-inline">
                                                        <p:outputLabel for="email" value="#{localemsgs.Email}:" 
                                                                       style="float: left;position:absolute;" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                        <h:outputText id="email" value="#{acessos.todosClientesSelecao.email}"
                                                                      style="float: left;left: 100px;position:absolute; font-weight: bold"
                                                                      rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                        <p:spacer height="30px" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                    </div>
                                                    <div class="form-inline">
                                                        <p:outputLabel for="fone1" value="#{localemsgs.Fone1}:" 
                                                                       style="float: left;position:absolute;" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                        <h:outputText id="fone1" value="#{acessos.todosClientesSelecao.fone1}" converter="conversorFone"
                                                                      style="float: left;left: 100px;position:absolute; font-weight: bold"
                                                                      rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                        <p:spacer height="30px" rendered="#{acessos.todosClientesSelecao ne null}"/>
                                                    </div>

                                                    <div class="form-inline">
                                                        <p:commandLink oncomplete="PF('dlgAdicionarClientes').hide()" 
                                                                       action="#{acessos.AdicionarCliente}" process="adicionarClientes"
                                                                       title="#{localemsgs.Selecionar}"
                                                                       update="clientes msgs adicionarClientes formEditar:tabs:qtdClientes">
                                                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                                        </p:commandLink>
                                                    </div>
                                                </p:panel>
                                            </p:dialog>
                                        </p:panel>
                                    </p:panelGrid>
                                </p:tab>
                            </p:tabView>
                        </p:panel>
                    </p:dialog>
                </h:form>
                <!-- Pesquisar Funcionarios  -->
                <p:dialog header="#{localemsgs.Pesquisar}" widgetVar="dlgPesquisar" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_usuarios.png" height="40" width="40"/> 
                        #{localemsgs.PesquisarUsuario}
                    </f:facet>
                    <p:panel id="pesquisar">
                        <h:form id="formPesquisar" class="form-inline">   
                            <div class="form-inline">
                                <p:outputLabel for="subFil" value="#{localemsgs.Filial}:" style="float: left;position:absolute;"/>
                                <p:selectOneMenu id="subFil" value="#{acessos.filial}" converter="omnifaces.SelectItemsConverter"
                                                 filter="true" filterMatchMode="contains"
                                                 style="float: left;left:120px;position:absolute; width: 280px">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItems value="#{acessos.todasFiliais}" var="filial" itemValue="#{filial}" 
                                                   itemLabel="#{filial.descricao}"/>
                                    <p:ajax event="itemSelect" listener="#{acessos.SelecionarFilialUsuario}"/>
                                </p:selectOneMenu>
                            </div>
                            
                            <p:spacer height="40px"/>
                            
                            <div class="form-inline">
                                <p:outputLabel for="nome" value="#{localemsgs.Nome}:" style="float: left;position:absolute;"/>
                                <p:inputText id="nome" value="#{acessos.selecionado.pessoa.nome}"
                                             style="float: left;left:120px;position:absolute; width: 280px"/>
                            </div>

                            <p:spacer height="40px"/>
                            
                            <div class="form-inline">
                                <p:outputLabel for="nivel" value="#{localemsgs.Nivel}:" style="float: left;position:absolute;"/>
                                <p:selectOneMenu value="#{acessos.selecionado.saspw.nivelx}" id="nivel"
                                                 label="#{localemsgs.Nivel}"
                                                 style="color: black ;float: left;left:120px;position:absolute; width:280px;">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Operacao}" itemValue="1"/>
                                    <f:selectItem itemLabel="#{localemsgs.Manutencao}" itemValue="2"/>
                                    <f:selectItem itemLabel="#{localemsgs.Gerencia}" itemValue="3"/>
                                    <f:selectItem itemLabel="#{localemsgs.PortalRH}" itemValue="4"/>
                                    <f:selectItem itemLabel="#{localemsgs.GTV}" itemValue="5"/>
                                    <f:selectItem itemLabel="#{localemsgs.AssinarGTV}" itemValue="7"/>
                                    <f:selectItem itemLabel="#{localemsgs.CofreInteligente}" itemValue="6"/>
                                    <f:selectItem itemLabel="#{localemsgs.Administrador}" itemValue="9"/>
                                </p:selectOneMenu>
                            </div>
                            
                            <p:spacer height="40px"/>
                            
                            <div class="form-inline">
                                <p:outputLabel for="grupo" value="#{localemsgs.Grupo}:" style="float: left;position:absolute;"/>
                                <p:selectOneMenu value="#{acessos.selecionado.saspw.codGrupo}" id="grupo"
                                                 style="color: black ;float: left;left:120px;position:absolute; width:280px;"
                                                 label="#{localemsgs.Grupo}" filter="true" filterMatchMode="contains">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItems value="#{acessos.grupos}" var="grupos" itemValue="#{grupos.codigo}"
                                                   itemLabel="#{grupos.descricao}" noSelectionValue="Selecione"/>
                                </p:selectOneMenu>
                            </div>
                            
                            <p:spacer height="40px"/>
                            
                            <div class="form-inline">
                                <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:" style="float: left;position:absolute;"/>
                                <p:selectOneMenu value="#{acessos.selecionado.saspw.situacao}" id="situacao" label="#{localemsgs.Situacao}"
                                                 style="color: black ;float: left;left:120px;position:absolute; width:280px;">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                    <f:selectItem itemLabel="#{localemsgs.Bloqueado}" itemValue="B"/>
                                </p:selectOneMenu>
                            </div>
                            
                            <p:spacer height="40px"/>
                            
                            <div class="form-inline">
                                <p:outputLabel for="addCliente" value="#{localemsgs.Cliente}:" 
                                               style="float: left;position:absolute;"/>
                                <p:autoComplete id="addCliente" value="#{acessos.todosClientesSelecao.NRed}" styleClass="cliente3"
                                                style="float: left;left:120px;position:absolute; width: 280px"
                                                completeMethod="#{acessos.ListarClientesPesquisa}" 
                                                var="cli" itemLabel="#{cli}" itemValue="#{cli}" scrollHeight="250">
                                </p:autoComplete>
                                <p:watermark for="addCliente" value="#{localemsgs.Cliente}" />
                            </div>
                            
                            <p:spacer height="40px"/>
                            
                            <div class="form-inline">
                                <p:commandLink oncomplete="PF('dlgPesquisar').hide()" 
                                               action="#{acessos.PesquisaPaginada}"
                                               title="#{localemsgs.Pesquisar}" update="main:tabela msgs cabecalho corporativo">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                            <div class="col-sm-12 text-center" style="height: 32px;">
                                <p:ajaxStatus>
                                    <f:facet name="start">
                                        <p:graphicImage value="../assets/img/ajax-loader.gif" />
                                    </f:facet>
                                </p:ajaxStatus>
                            </div>
                        </h:form>
                    </p:panel>
                </p:dialog>
            </div>
            

            <footer>
                <div class="footer-toggler">
                    <a  href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>

                    <p:ajaxStatus class="status" >
                        <f:facet name="start">
                            <p:graphicImage value="../assets/img/ajax-loader.gif" style="height: 20px; width: 20px"/>
                        </f:facet>
                    </p:ajaxStatus>
                </div>
                <div class="footer-body" id="footer-body">
                    <div>
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.Corporativo}: " />
                            <p:selectBooleanCheckbox value="#{acessos.mostrarFiliais}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{acessos.MostrarFiliais}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.SomenteAtivos}: " />
                            <p:selectBooleanCheckbox value="#{acessos.somenteAtivos}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{acessos.SomenteAtivos}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.LimparFiltros}: " />
                            <p:selectBooleanCheckbox value="#{acessos.limparFiltros}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{acessos.LimparFiltros}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function(e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>
  
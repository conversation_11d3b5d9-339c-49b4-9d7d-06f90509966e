/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PstServ;
import SasBeans.Rondas;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RondasDao {

    /**
     * Lista as rondas dado um período
     *
     * @param dataInicio
     * @param dataFim
     * @param codFil
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> listaRondasLog(String tipo, String dataInicio, String dataFim, String codFil,
            String matricula, String secao, Persistencia persistencia) throws Exception {
        try {
            List<Rondas> retorno = new ArrayList<>();
            String sql = "select rondas.hora, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome, "
                    + " min(rondas.hr_alter) inicioRonda, max(rondas.hr_alter) finalRonda, count(rondas.hora) rondasHORA, "
                    + "(select count(*) from pstdepenronda "
                    + "     where pstdepenronda.hora = rondas.hora and pstdepenronda.secao = rondas.secao "
                    + "     and pstdepenronda.codfil = rondas.codfil and pstdepenronda.tipo = ?) rondasTOTAL  "
                    + " from rondas  "
                    + " left join funcion on funcion.matr = rondas.matr "
                    + "                and funcion.codfil = rondas.codfil "
                    + " left join pstserv on pstserv.secao = rondas.secao "
                    + "                 and pstserv.codfil = rondas.codfil "
                    + " where rondas.Data between ? and ? ";
            if (!codFil.equals("")) {
                sql += " and pstserv.codfil = ? ";
            }
            if (!matricula.equals("")) {
                sql += " and rondas.matr = ? ";
            }
            if (!secao.equals("")) {
                sql += " and rondas.secao = ? ";
            }
            sql += " group by rondas.hora, rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tipo);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }
            consulta.select();
            Rondas ronda;
            while (consulta.Proximo()) {
                ronda = new Rondas();
                ronda.setCodFil(consulta.getString("codfil"));
                /**
                 * Salvando o nome do funcionário em operador
                 */
                ronda.setOperador(consulta.getString("nome"));
                ronda.setLocal(consulta.getString("local"));
                ronda.setSecao(consulta.getString("secao"));
                ronda.setData(consulta.getString("data"));
                ronda.setDescricao(consulta.getString("inicioRonda") + ";" + consulta.getString("finalRonda") + ";"
                        + consulta.getString("rondasHORA") + ";" + consulta.getString("rondasTOTAL"));
                ronda.setHr_alter(consulta.getString("finalRonda"));
                ronda.setHora(consulta.getInt("hora"));
                retorno.add(ronda);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RondasDao.getRondasPendentes - " + e.getMessage() + "\r\n"
                    + "select rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome, "
                    + " min(rondas.hr_alter) inicioRonda, max(rondas.hr_alter) finalRonda, "
                    + " (select count(*) from rhPonto "
                    + "            where dtCompet = rondas.data "
                    + "          and rhPonto.matr = rondas.matr) batidas "
                    + " from rondas  "
                    + " left join funcion on funcion.matr = rondas.matr "
                    + "                and funcion.codfil = rondas.codfil "
                    + " left join pstserv on pstserv.secao = rondas.secao "
                    + "                 and pstserv.codfil = rondas.codfil "
                    + " where rondas.Data between " + dataInicio + " and " + dataFim + " and rondas.codfil = " + codFil
                    + (!matricula.equals("") ? " and rondas.matr = " + matricula : "")
                    + " group by rondas.matr, rondas.secao, rondas.codfil, rondas.data, pstserv.local, funcion.nome");
        }
    }

    /**
     * Lista as rondas dado um período
     *
     * @param dataInicio
     * @param dataFim
     * @param codFil
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> listaTodasRondas(String dataInicio, String dataFim, String codFil, String matricula, Persistencia persistencia) throws Exception {
        try {
            List<Rondas> retorno = new ArrayList<>();
            String sql = " select  rondas.*, pstdepen.descricao, pstserv.local, funcion.nome  "
                    + " from rondas "
                    + " left join funcion on funcion.matr = rondas.matr "
                    + " left join pstdepen on pstdepen.secao = rondas.secao "
                    + "                    and pstdepen.codfil = rondas.codfil "
                    + "                    and pstdepen.codigo = rondas.CodDepen "
                    + " left join pstserv on pstserv.secao = rondas.secao "
                    + "                    and pstserv.codfil = rondas.codfil "
                    + " where rondas.Data between ? and ? and rondas.codfil = ? ";
            if (!matricula.equals("")) {
                sql += " and rondas.matr = ? ";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(codFil);
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            consulta.select();
            Rondas ronda;
            while (consulta.Proximo()) {
                ronda = new Rondas();
                ronda.setCodDepen(consulta.getString("coddepen"));
                ronda.setCodFil(consulta.getString("codfil"));
                ronda.setDescricao(consulta.getString("descricao"));
                ronda.setLatitude(consulta.getString("latitude"));
                ronda.setLongitude(consulta.getString("longitude"));
                /**
                 * Salvando o nome do funcionário em operador
                 */
                ronda.setOperador(consulta.getString("nome"));
                ronda.setLocal(consulta.getString("local"));
                ronda.setSecao(consulta.getString("secao"));
                ronda.setSequencia(consulta.getString("sequencia"));
                ronda.setData(consulta.getString("data"));
                ronda.setHr_alter(consulta.getString("hr_alter"));
                ronda.setDt_alter(consulta.getString("dt_alter"));
                retorno.add(ronda);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RondasDao.getRondasPendentes - " + e.getMessage() + "\r\n"
                    + " select rondas.*, pstdepen.descricao, pstserv.local "
                    + " from rondas "
                    + " left join pstdepen on pstdepen.secao = rondas.secao "
                    + "                    and pstdepen.codfil = rondas.codfil "
                    + "                    and pstdepen.codigo = rondas.CodDepen "
                    + " where rondas.Data between " + dataInicio + " and " + dataFim + " and rondas.codfil = " + codFil);
        }
    }

    /**
     * Lista as rondas dado um período
     *
     * @param dataInicio
     * @param dataFim
     * @param codFil
     * @param secao
     * @param matricula
     * @param hora
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> listaTodasRondasSecao(String dataInicio, String dataFim, String codFil,
            String secao, String matricula, String hora, Persistencia persistencia) throws Exception {
        try {
            List<Rondas> retorno = new ArrayList<>();
            String sql = " select rondas.*, pstdepen.descricao, pstserv.local, funcion.nome  "
                    + " from rondas "
                    + " left join funcion on funcion.matr = rondas.matr "
                    + " left join pstdepen on pstdepen.secao = rondas.secao "
                    + "                    and pstdepen.codfil = rondas.codfil "
                    + "                    and pstdepen.codigo = rondas.CodDepen "
                    + " left join pstserv on pstserv.secao = rondas.secao "
                    + "                    and pstserv.codfil = rondas.codfil "
                    + " where rondas.Data between ? and ? and rondas.codfil = ? and rondas.secao = ? and rondas.hora = ? ";
            if (!matricula.equals("")) {
                sql += " and rondas.matr = ? ";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(codFil);
            consulta.setString(secao);
            consulta.setString(hora);
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            consulta.select();
            Rondas ronda;
            while (consulta.Proximo()) {
                ronda = new Rondas();
                ronda.setCodDepen(consulta.getString("coddepen"));
                ronda.setCodFil(consulta.getString("codfil"));
                ronda.setDescricao(consulta.getString("descricao"));
                ronda.setLatitude(consulta.getString("latitude"));
                ronda.setLongitude(consulta.getString("longitude"));
                /**
                 * Salvando o nome do funcionário em operador
                 */
                ronda.setOperador(consulta.getString("nome"));
                ronda.setLocal(consulta.getString("local"));
                ronda.setSecao(consulta.getString("secao"));
                ronda.setSequencia(consulta.getString("sequencia"));
                ronda.setData(consulta.getString("data"));
                ronda.setHr_alter(consulta.getString("hr_alter"));
                ronda.setDt_alter(consulta.getString("dt_alter"));
                retorno.add(ronda);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RondasDao.getRondasPendentes - " + e.getMessage() + "\r\n"
                    + " select rondas.*, pstdepen.descricao, pstserv.local "
                    + " from rondas "
                    + " left join pstdepen on pstdepen.secao = rondas.secao "
                    + "                    and pstdepen.codfil = rondas.codfil "
                    + "                    and pstdepen.codigo = rondas.CodDepen "
                    + " where rondas.Data between " + dataInicio + " and " + dataFim + " and rondas.codfil = " + codFil
                    + " and rondas.secao = " + secao);
        }
    }

    /**
     * Tenta inserir uma nova ronda
     *
     * @param ronda
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String insereRonda(Rondas ronda, Persistencia persistencia) throws Exception {
        int cont = 1;
        String erro = "";
        while (true) {
            try {
                String sqlSequencia = " Select isnull(max(sequencia),0) + 1 sequencia from Rondas ";
                Consulta consulta = new Consulta(sqlSequencia, persistencia);
                consulta.select();
                String sequencia = "";
                while (consulta.Proximo()) {
                    sequencia = consulta.getString("sequencia");
                }
                consulta.Close();
                ronda.setSequencia(sequencia);
                String sql = " INSERT INTO Rondas (Sequencia, Secao, CodFil, CodDepen, Data, Matr, Hora, "
                        + " Latitude, Longitude, Operador, Dt_alter, Hr_alter) "
                        + " values (?,?,?,?,?,?,?,?,?,?,?,?) ";
                consulta = new Consulta(sql, persistencia);
                consulta.setString(ronda.getSequencia());
                consulta.setString(ronda.getSecao());
                consulta.setString(ronda.getCodFil());
                consulta.setString(ronda.getCodDepen());
                consulta.setString(ronda.getData());
                consulta.setString(ronda.getMatr());
                consulta.setInt(ronda.getHora());
                consulta.setString(ronda.getLatitude());
                consulta.setString(ronda.getLongitude());
                consulta.setString(ronda.getOperador());
                consulta.setString(ronda.getDt_alter());
                consulta.setString(ronda.getHr_alter());
                consulta.insert();
                consulta.close();
                return sequencia;
            } catch (Exception e) {
                erro = e.getMessage();
                cont++;
            }
            if (cont == 20) {
                throw new Exception("RondasDao.insereRonda - " + erro);
            }
        }
    }

    /**
     * Lista rondas de um posto para determinada hora.
     *
     * @param data
     * @param secao
     * @param tipo
     * @param codfil
     * @param hora
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> getRondasPendentes(String data, String secao, String tipo, String codfil, String hora, Persistencia persistencia) throws Exception {
        try {
            List<Rondas> retorno = new ArrayList<>();
            String sql = "select rondas.sequencia, pstdepen.descricao, pstdepenronda.secao, pstdepenronda.codfil, "
                    + " pstdepenronda.codigo, pstdepenronda.hora "
                    + " from pstdepenronda "
                    + " left join rondas on rondas.secao = pstdepenronda.secao "
                    + "                    and rondas.codfil = pstdepenronda.codfil "
                    + "                    and rondas.coddepen = pstdepenronda.codigo "
                    + "                    and rondas.hora = ? "
                    + "                    and rondas.data = ? "
                    + " left join pstdepen on pstdepen.secao = pstdepenronda.secao "
                    + "                    and pstdepen.codfil = pstdepenronda.codfil "
                    + "                    and pstdepen.codigo = pstdepenronda.codigo "
                    + " where pstdepenronda.secao = ? and pstdepenronda.tipo = ? and pstdepenronda.codfil = ? and pstdepenronda.hora = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(hora);
            consulta.setString(data);
            consulta.setString(secao);
            consulta.setString(tipo);
            consulta.setString(codfil);
            consulta.setString(hora);
            consulta.select();
            Rondas rondas;
            while (consulta.Proximo()) {
                rondas = new Rondas();
                rondas.setSequencia(consulta.getString("sequencia"));
                rondas.setSecao(consulta.getString("secao"));
                rondas.setCodFil(consulta.getString("codfil"));
                rondas.setCodDepen(consulta.getString("codigo"));
                rondas.setDescricao(consulta.getString("descricao"));
                rondas.setHoraPrevista(consulta.getInt("hora"));
                retorno.add(rondas);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RondasDao.getRondasPendentes - " + e.getMessage() + "\r\n"
                    + "select sequencia, pstdepen.descricao, pstdepenronda.secao, pstdepenronda.codfil, pstdepenronda.codigo, pstdepenronda.hora "
                    + " from pstdepenronda "
                    + " left join rondas on rondas.secao = pstdepenronda.secao "
                    + "                    and rondas.codfil = pstdepenronda.codfil "
                    + "                    and rondas.data = " + data
                    + " left join pstdepen on pstdepen.secao = pstdepenronda.secao "
                    + "                    and pstdepen.codfil = pstdepenronda.codfil "
                    + "                    and pstdepen.codigo = pstdepenronda.codigo "
                    + " where pstdepenronda.secao = " + secao + " and pstdepenronda.tipo = " + tipo
                    + " and pstdepenronda.codfil = " + codfil + " and pstdepenronda.hora = " + hora);
        }
    }

    /**
     * Lista as rondas de um posto no intervalo definido
     *
     * @param dataInicio
     * @param dataFim
     * @param pstServ
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> getRondasPosto(String dataInicio, String dataFim, PstServ pstServ, Persistencia persistencia) throws Exception {
        try {
            List<Rondas> retorno = new ArrayList<>();
            String sql = "select pstdepen.descricao, rondas.* from rondas "
                    + " left join pstdepen on pstdepen.codigo = rondas.coddepen "
                    + "                     and rondas.secao = pstdepen.secao "
                    + "                     and rondas.codfil = pstdepen.codfil "
                    + " where rondas.secao = ? and rondas.codfil = ? "
                    + "                     and rondas.data between ? and ? "
                    + " order by sequencia desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstServ.getSecao());
            consulta.setString(pstServ.getCodFil().toPlainString());
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            Rondas rondas;
            while (consulta.Proximo()) {
                rondas = new Rondas();
                rondas.setDescricao(consulta.getString("Descricao"));
                rondas.setSequencia(consulta.getString("Sequencia"));
                rondas.setSecao(consulta.getString("Secao"));
                rondas.setCodFil(consulta.getString("CodFil"));
                rondas.setCodDepen(consulta.getString("CodDepen"));
                rondas.setData(consulta.getString("Data"));
                rondas.setMatr(consulta.getString("Matr"));
                rondas.setLatitude(consulta.getString("Latitude"));
                rondas.setLongitude(consulta.getString("Longitude"));
                rondas.setOperador(consulta.getString("Operador"));
                rondas.setDt_alter(consulta.getString("Dt_alter"));
                rondas.setHr_alter(consulta.getString("Hr_alter"));
                retorno.add(rondas);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RondasDao.getRondasPendentes - " + e.getMessage() + "\r\n"
                    + "select pstdepen.descricao, rondas.* from rondas "
                    + " left join pstdepen on pstdepen.codigo = rondas.coddepen "
                    + "                     and rondas.secao = pstdepen.secao "
                    + "                     and rondas.codfil = pstdepen.codfil "
                    + " where rondas.secao = " + pstServ.getSecao() + " and rondas.codfil = " + pstServ.getCodFil().toPlainString()
                    + "                     and rondas.data between " + dataInicio + " and " + dataFim
                    + " order by sequencia desc ");
        }
    }

    /**
     *
     * @param data
     * @param secao
     * @param hora
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> getRondasHora(String data, String secao, String hora, String codfil, Persistencia persistencia) throws Exception {
        try {
            List<Rondas> retorno = new ArrayList<>();
            String sql = "select pstdepen.descricao, rondas.* from rondas "
                    + " left join pstdepen on pstdepen.codigo = rondas.coddepen "
                    + "                     and rondas.secao = pstdepen.secao "
                    + "                     and rondas.codfil = pstdepen.codfil "
                    + " where rondas.data = ? and rondas.secao = ? and rondas.hora = ? and rondas.codfil = ? "
                    + " order by sequencia desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(secao);
            consulta.setString(hora);
            consulta.setString(codfil);
            consulta.select();
            Rondas rondas;
            while (consulta.Proximo()) {
                rondas = new Rondas();
                rondas.setDescricao(consulta.getString("Descricao"));
                rondas.setSequencia(consulta.getString("Sequencia"));
                rondas.setSecao(consulta.getString("Secao"));
                rondas.setCodFil(consulta.getString("CodFil"));
                rondas.setCodDepen(consulta.getString("CodDepen"));
                rondas.setData(consulta.getString("Data"));
                rondas.setMatr(consulta.getString("Matr"));
                rondas.setLatitude(consulta.getString("Latitude"));
                rondas.setLongitude(consulta.getString("Longitude"));
                rondas.setOperador(consulta.getString("Operador"));
                rondas.setDt_alter(consulta.getString("Dt_alter"));
                rondas.setHr_alter(consulta.getString("Hr_alter"));
                retorno.add(rondas);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RondasDao.getRondasPendentes - " + e.getMessage() + "\r\n"
                    + "select pstdepen.descricao, rondas.* from rondas "
                    + " left join pstdepen on pstdepen.codigo = rondas.coddepen "
                    + "                     and rondas.secao = pstdepen.secao "
                    + "                     and rondas.codfil = pstdepen.codfil "
                    + " where rondas.data = " + data + " and rondas.secao = " + secao + " and rondas.hora = " + hora + " and rondas.codfil = " + codfil
                    + " order by sequencia desc ");
        }
    }

    /**
     *
     * @param data
     * @param secao
     * @param tipo
     * @param codfil
     * @param hora
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> getProximasRondas(String data, String secao, String tipo, String codfil, String hora, Persistencia persistencia) throws Exception {
        try {
            List<Rondas> retorno = new ArrayList<>();
            String sql = "select sequencia, pstdepen.descricao, pstdepenronda.secao, pstdepenronda.codfil, pstdepenronda.codigo, pstdepenronda.hora "
                    + " from pstdepenronda "
                    + " left join rondas on rondas.secao = pstdepenronda.secao "
                    + "                    and rondas.codfil = pstdepenronda.codfil "
                    + "                    and rondas.data = ? "
                    + " left join pstdepen on pstdepen.secao = pstdepenronda.secao "
                    + "                    and pstdepen.codfil = pstdepenronda.codfil "
                    + "                    and pstdepen.codigo = pstdepenronda.codigo "
                    + " where pstdepenronda.secao = ? and pstdepenronda.tipo = ? and pstdepenronda.codfil = ? and pstdepenronda.hora > ? "
                    + " order by pstdepenronda.hora asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(secao);
            consulta.setString(tipo);
            consulta.setString(codfil);
            consulta.setString(hora);
            consulta.select();
            Rondas rondas;
            while (consulta.Proximo()) {
                rondas = new Rondas();
                rondas.setSequencia(consulta.getString("sequencia"));
                rondas.setSecao(consulta.getString("secao"));
                rondas.setCodFil(consulta.getString("codfil"));
                rondas.setCodDepen(consulta.getString("codigo"));
                rondas.setDescricao(consulta.getString("descricao"));
                rondas.setHoraPrevista(consulta.getInt("hora"));
                retorno.add(rondas);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RondasDao.getRondasPendentes - " + e.getMessage() + "\r\n"
                    + "select sequencia, pstdepen.descricao, pstdepenronda.secao, pstdepenronda.codfil, pstdepenronda.codigo, pstdepenronda.hora "
                    + " from pstdepenronda "
                    + " left join rondas on rondas.secao = pstdepenronda.secao "
                    + "                    and rondas.codfil = pstdepenronda.codfil "
                    + "                    and rondas.data = " + data
                    + " left join pstdepen on pstdepen.secao = pstdepenronda.secao "
                    + "                    and pstdepen.codfil = pstdepenronda.codfil "
                    + "                    and pstdepen.codigo = pstdepenronda.codigo "
                    + " where pstdepenronda.secao = " + secao + " and pstdepenronda.tipo = " + tipo
                    + " and pstdepenronda.codfil = " + codfil + " and pstdepenronda.hora = " + hora
                    + " order by pstdepenronda.hora asc ");
        }
    }

    /**
     * Busca informações de uma ronda
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Rondas buscarRonda(String sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = " select rondas.*, pstdepen.descricao, pstserv.local, funcion.nome  "
                    + " from rondas "
                    + " left join funcion on funcion.matr = rondas.matr "
                    + " left join pstdepen on pstdepen.secao = rondas.secao "
                    + "                    and pstdepen.codfil = rondas.codfil "
                    + "                    and pstdepen.codigo = rondas.CodDepen "
                    + " left join pstserv on pstserv.secao = rondas.secao "
                    + "                    and pstserv.codfil = rondas.codfil "
                    + " where rondas.sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.select();
            Rondas ronda = new Rondas();
            while (consulta.Proximo()) {
                ronda.setCodDepen(consulta.getString("coddepen"));
                ronda.setCodFil(consulta.getString("codfil"));
                ronda.setDescricao(consulta.getString("descricao"));
                /**
                 * Salvando o nome do funcionário em operador
                 */
                ronda.setOperador(consulta.getString("nome"));
                /**
                 * Salvando o local em seção
                 */
                ronda.setSecao(consulta.getString("local"));
                ronda.setSequencia(consulta.getString("sequencia"));
                ronda.setData(consulta.getString("data"));
                ronda.setHr_alter(consulta.getString("hr_alter"));
                ronda.setLatitude(consulta.getString("latitude"));
                ronda.setLongitude(consulta.getString("longitude"));
            }
            consulta.Close();
            return ronda;
        } catch (Exception e) {
            throw new Exception("RondasDao.getRondasPendentes - " + e.getMessage() + "\r\n"
                    + "select sequencia, pstdepen.descricao, pstdepenronda.secao, pstdepenronda.codfil, pstdepenronda.codigo, pstdepenronda.hora "
                    + " from pstdepenronda "
                    + " left join rondas on rondas.secao = pstdepenronda.secao "
                    + "                    and rondas.codfil = pstdepenronda.codfil "
                    + " left join pstdepen on pstdepen.secao = pstdepenronda.secao "
                    + "                    and pstdepen.codfil = pstdepenronda.codfil "
                    + "                    and pstdepen.codigo = pstdepenronda.codigo "
                    + " where rondas.sequencia = " + sequencia);
        }
    }

    /**
     * Busca informações de uma ronda
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rondas> buscaInicioFimRonda(String dataInicio, String dataFim, String codFil, String matricula, Persistencia persistencia) throws Exception {
        {
            List<Rondas> retorno = new ArrayList<>();
            String sql = " select  Min(Rondas.Hr_Alter) HoraIni, Max(Rondas.Hr_Alter) HoraFim, Min(rondas.sequencia) Sequencia, rondas.codfil, "
                    + " rodas.data,  Min(pstdepen.descricao) Descricao, pstserv.local, funcion.nome  "
                    + " from rondas "
                    + " left join funcion on funcion.matr = rondas.matr "
                    + " left join pstdepen on pstdepen.secao = rondas.secao "
                    + "                    and pstdepen.codfil = rondas.codfil "
                    + "                    and pstdepen.codigo = rondas.CodDepen "
                    + " left join pstserv on pstserv.secao = rondas.secao "
                    + "                    and pstserv.codfil = rondas.codfil "
                    + " where rondas.Data between ? and ? and rondas.codfil = ? ";
            if (!matricula.equals("")) {
                sql += " and rondas.matr = ? ";
            }
            sql += " Group by rondas.codfil, rondas.data,  "
                    + " pstserv.local, funcion.nome ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(codFil);
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            consulta.select();
            Rondas ronda;
            while (consulta.Proximo()) {
                ronda = new Rondas();
                ronda.setCodDepen(consulta.getString("coddepen"));
                ronda.setCodFil(consulta.getString("codfil"));
                ronda.setDescricao(consulta.getString("descricao"));
                ronda.setLatitude("");
                ronda.setLongitude("");
                /**
                 * Salvando o nome do funcionário em operador
                 */
                ronda.setOperador(consulta.getString("nome"));
                ronda.setLocal(consulta.getString("local"));
                ronda.setSecao(consulta.getString("secao"));
                ronda.setSequencia(consulta.getString("sequencia"));
                ronda.setData(consulta.getString("data"));
                ronda.setHoraIni(consulta.getString("HoraIni"));
                ronda.setHoraFim(consulta.getString("HoraFim"));
                retorno.add(ronda);
            }
            consulta.Close();
            return retorno;
        }
    }
}

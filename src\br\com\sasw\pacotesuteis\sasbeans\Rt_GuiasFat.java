/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class Rt_GuiasFat {

    private String Sequencia;
    private String Parada;
    private String Guia;
    private String Serie;
    private String CodFil;
    private String OS;
    private String Embarques;
    private String ValorEmb;
    private String ValorAst;
    private String ValorAdv;
    private String ValorTot;
    private String FormaPgto;
    private String Obs;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String FormaPgtoDescricao;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getParada() {
        return Parada;
    }

    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getEmbarques() {
        return Embarques;
    }

    public void setEmbarques(String Embarques) {
        this.Embarques = Embarques;
    }

    public String getValorEmb() {
        return ValorEmb;
    }

    public void setValorEmb(String ValorEmb) {
        this.ValorEmb = ValorEmb;
    }

    public String getValorAst() {
        return ValorAst;
    }

    public void setValorAst(String ValorAst) {
        this.ValorAst = ValorAst;
    }

    public String getValorAdv() {
        return ValorAdv;
    }

    public void setValorAdv(String ValorAdv) {
        this.ValorAdv = ValorAdv;
    }

    public String getValorTot() {
        return ValorTot;
    }

    public void setValorTot(String ValorTot) {
        this.ValorTot = ValorTot;
    }

    public String getFormaPgto() {
        return FormaPgto;
    }

    public void setFormaPgto(String FormaPgto) {
        this.FormaPgto = FormaPgto;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getFormaPgtoDescricao() {
        return FormaPgtoDescricao;
    }

    public void setFormaPgtoDescricao(String FormaPgtoDescricao) {
        this.FormaPgtoDescricao = FormaPgtoDescricao;
    }
}

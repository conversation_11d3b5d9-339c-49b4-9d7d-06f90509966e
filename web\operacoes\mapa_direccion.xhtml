<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                .ui-button-icon-left{
                    position:absolute;
                    z-index: 999 !important;
                    width:40px !important;
                    top:15px !important;
                }

                .ui-button-icon-left::before {
                    font-size: 14pt !important;
                    margin-left:3px !important;
                }

                [id*="btMapaTotal"] .ui-button-icon-left{
                    top:15px !important;
                    font-size: 16pt !important;
                    height:40px !important;
                }

                .AbrirFiltros{
                    position: absolute;
                    z-index:1 !important;
                    background-color:#000;
                    color:#FFF;
                    writing-mode: vertical-rl;
                    text-orientation: upright !important;
                    padding:10px 6px 10px 6px !important;
                    top:75px;
                    border-radius: 0px 8px 8px 0px;
                    box-shadow: 1px 1px 2px #666;
                    cursor: pointer;
                    font-family: 'Open Sans', sans-serif;
                    font-size:10pt !important;
                    text-align:center !important;
                }

                #divQuadroResumo{
                    position:absolute !important;
                    width:340px !important;
                    top:70px !important;
                    z-index:1 !important;
                    height: 150px;
                }

                #divQuadroResumo .FecharFiltros{
                    position:absolute;
                    right:-10px;
                    top:-15px;
                    color:#FFF;
                    background-color:#000;
                    width:31px;
                    height: 31px;
                    border-radius:50%;
                    text-align: center;
                    cursor: pointer;
                    padding-top: 4px;
                    padding-left: 1px;
                    font-size:16pt !important;
                    box-shadow:1px 1px 2px #666;
                }

                #divQuadroResumo .ItemResumo{
                    background-color: rgba(211, 221, 228, 0.8) !important;
                    width:100% !important;
                    border-radius:12px;
                    padding:1px 1px 0px 1px !important;
                    box-shadow:1px  1px 3px #666;
                    border-radius:4px;
                }

                #divQuadroResumo input[type="checkbox"]{
                    padding:0 !important;
                    background-color:black;
                    position:absolute;
                    margin-top:0px !important;
                    border:thin solid #AAA !important;
                    border-radius:2px !important;
                    height:22px !important;
                }

                #divQuadroResumo input[type="checkbox"] + label{
                    font-family:'Open Sans', sans-serif !important;
                    font-size:10pt;
                    font-weight:600;
                    color:#404040;
                    margin:0px !important;
                    padding:0px 0px 0px 16px !important;
                    cursor:pointer;
                    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
                }

                #divQuadroResumo .Item,
                #divQuadroResumo .ItemZebrado{
                    position:relative;
                    width:calc(100% - 12px) !important;
                    margin-left: 6px !important;
                    padding:2px 8px 0px 8px !important;
                    border-radius:2px;
                }

                #divQuadroResumo .ItemZebrado{
                    background-color:rgba(187, 187, 187, 0.5) !important;
                }

                #divQuadroResumo #lblTituloFiltro{
                    font-family:'Open Sans', sans-serif !important;
                    font-size:11pt !important;
                    font-weight:600 !important;
                    color:#000 !important;
                    padding:5px 0px 3px 10px !important;
                    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
                    border:none !important;
                }

                #divQuadroResumo .QdeStatus{
                    position: absolute;
                    width:40px;
                    height:10px !important;
                    border-radius:6px;
                    right:6px;
                    top: 9px;
                    font-size:8pt !important;
                    text-align:center;
                    color:#FFF;
                    font-weight:bold;
                    padding:0x !important;
                }

                #divQuadroResumo .QdeStatus[cor="R"]{
                    background-color:#F00;
                }

                #divQuadroResumo .QdeStatus[cor="G"]{
                    background-color: #006633;
                }

                #divQuadroResumo .QdeStatus[cor="B"]{
                    background-color:#2c58b1;
                }

                #divQuadroResumo .QdeStatus[cor="C"]{
                    background-color:#505050;
                }

                div[id*="lightboxListaRotas_panel"],
                div[id*="lightboxListaPosicoes_panel"]{
                    width:85% !important;
                    height:85% !important;
                    top: 0 !important;
                    bottom: 0 !important;
                    margin: auto !important;
                }

                .ui-lightbox-content-wrapper,
                .ui-lightbox-content,
                .ui-lightbox-content iframe{
                    width:100% !important;
                    height:100% !important;
                    background-color: #ECF0F5 !important;
                }

                .BotaoResumoQuadro{
                    text-align:center !important;
                    /*width:190px !important;*/
                    width:100% !important;
                    margin-top:12px;
                    box-shadow:1px 1px 3px #666!important;
                    font-size:12pt !important;
                    padding:  0px !important;
                }

                div[id*="lightboxListaRotas"] .fa-list{
                    top: 18px !important;
                }

                div[id*="lightboxListaRotas"] span[class*="ui-button-text"]{
                    margin-top: -2px !important;
                }

                #btFechar, #btLightBoxClose{
                    position: absolute;
                    right: 0px;
                    top:-20px;
                    width: 145px !important;
                    background-color: red !important;
                    color:#FFF !important;
                    font-size:8pt !important;
                    font-weight: bold !important;
                    text-align: center !important;
                    border-radius: 3px !important;
                    border:thin solid darkred !important;
                    cursor:pointer;
                    padding-bottom:2px !important;
                }

                .FundoMapa{
                    height: 100% !important;
                    background-color: #FFF;
                    border: thin solid #CCC;
                    padding:10px 0px 10px 15px !important;
                    border-radius: 4px !important;
                    border-top:4px solid #3C8DBC !important;
                }

                #main{
                    background-color:#ECF0F5 !important;
                    padding:16px 14px 14px 14px !important;
                    overflow:hidden !important;
                    height: calc(100% - 0px) !important
                }

                .Rotas{
                    border:thin solid #DDD !important;
                    height:100% !important;
                    max-height:100% !important;
                    overflow:auto !important;
                    padding:0px 3px 3px 3px !important;
                    width:100% !important;
                    border-radius:4px !important;
                    margin:0px !important;
                    background-color: #FFF !important;
                    /*box-shadow:2px 2px 3px #EEE;*/
                }

                .botao {
                    display: inline-block !important;
                    color: #fff !important;
                    width: 125px !important;
                    height: 40px !important;
                    padding: 0 20px !important;
                    background: #3479A3 !important;
                    border-radius: 5px !important;
                    outline: none !important;
                    border: none !important;
                    cursor: pointer !important;
                    text-align: center !important;
                    transition: all 0.2s linear !important;
                    letter-spacing: 0.05em !important;
                }

                a:hover,
                a:focus{
                    color:#3479A3;
                }

                #firstHeading{
                    color: #3479A3;
                    margin: 0px!important;
                    line-height:25px !important;
                }

                .tableVeiculo {
                    margin:-20px 0px 0px 0px !important;
                    border-collapse: collapse;
                }

                .tableVeiculo td, .tableVeiculo th {
                    border: 1px solid #ddd;
                    padding: 5px;
                }

                .tableVeiculo tr:nth-child(even){background-color: #f2f2f2;}

                .tableVeiculo tr:hover {background-color: #88BBD9;}

                .tableVeiculo th {
                    padding-bottom: 4px;
                }

                .gm-style-iw{
                    min-width: 460px !important;
                }
                .gm-style-iw p{
                    line-height: 12px !important;
                }

                .gm-style-iw{
                    min-height: 330px !important;
                    min-width: 410px !important;
                    overflow:hidden !important;
                }

                .gm-style-iw div:first-child{
                    padding:0px 3px 0px 0px !important;
                    /*min-height: 320px !important;*/
                    overflow:hidden !important;
                }

                .BotaoResumoQuadro:hover,
                #btMapaTotal:hover{
                    color:#FFF !important;
                }

                #legend div{
                    width:100% !important;
                    padding-top:6px !important;
                    padding-left:8px !important;
                    padding-right:8px !important;
                    margin-bottom:10px !important;
                }

                .InfoColetaEntrega{
                    font-size:8pt !important;
                    text-align:center;
                    width:120px !important;
                    color:#FFF !important;
                    background-color:#000 !important;
                    font-weight:bold !important;
                    padding:1px 4px 0px 4px !important;
                    height:17px !important;
                    border-radius:4px !important;
                    box-shadow:2px 2px 4px #CCC !important;
                }

                .InfoColetaEntrega[tipo^="E"]{
                    background-color:forestgreen !important;
                    border:thin solid darkgreen;
                }

                .InfoColetaEntrega[tipo^="C"],
                .InfoColetaEntrega[tipo^="R"]{
                    background-color:red !important;
                    border:thin solid darkred;
                }

                .DadosInfo{
                    padding:0px !important;
                    width:100%;
                    border: thin solid #CCC;
                    margin: 0px !important;
                }

                .DadosInfo table{
                    border-spacing:2px !important;
                    border-collapse: separate;
                    width:100%;
                }

                .DadosInfo table thead tr th{
                    background-color:#303030 !important;
                    color:#FFF !important;
                    text-align:center !important;
                    padding:3px 1px 3px 1px !important;
                    font-weight:500 !important;
                    height:20px !important;
                }

                .DadosInfo table tr td{
                    border: thin solid #CCC;
                    padding:3px !important;
                    text-align:center !important;
                }
                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    #firstHeading{
                        font-size:12pt !important;
                        line-height:15px !important;
                    }

                    #divFooterTimer{
                        display:none !important;
                    }

                    .gm-style-iw{
                        min-height: 200px !important;
                        max-height: 200px !important;
                        min-width: 300px !important;
                        overflow-x:hidden !important;
                        overflow-y:auto !important;
                        font-size:8pt !important;
                        padding-bottom:0px !important;
                        margin:0px !important;
                    }

                    .gm-style-iw div:first-child{
                        padding:0px 0px 0px 0px !important;
                        min-height: 305px !important;
                        max-height: 305px !important;
                        min-width: 280px !important;
                        max-width: 280px !important;
                        overflow:hidden !important;
                        overflow-x:auto !important;
                        font-size:8pt !important;
                        padding-bottom:8px !important;
                        margin:0px !important;
                    }

                    .gm-style-iw p{
                        max-width:400px;
                        white-space: nowrap;
                        text-align:left !important;
                        line-height: 10px !important;
                        padding:0px !important;
                        margin:4px 0px 0px 0px !important;
                    }

                    [id*="lblOrigem"]{
                        display:none !important;
                    }

                    #divQuadroResumo input[type="checkbox"] + label{
                        font-size:8pt;
                    }

                    #divQuadroResumo{
                        width:290px !important;
                    }

                    .BotaoResumoQuadro{
                        margin-top:5px !important;
                    }

                    .painelCadastro{
                        padding-bottom:50px !important;
                    }

                    .InfoColetaEntrega{
                        padding-top:2px !important;
                        margin-top:-50px !important;
                    }
                }

                #legenda1{
                    margin-top:0px !important;
                }

                [id*="btConsultarHorarios"] > .ui-icon.fa, .ui-widget .ui-icon.fa, .ui-icon.fab, .ui-widget .ui-icon.fab, .ui-icon.fas, .ui-widget .ui-icon.fas, .ui-icon.far, .ui-widget .ui-icon.far, .ui-icon.fal, .ui-widget .ui-icon.fal {
                    margin-top: -4px !important;
                }

                a[ref="viewMap"]{
                    color: blue !important;
                    display: block;
                    margin-top: 10px !important;
                    width: 150px !important;
                    border: thin solid blue !important;
                    padding: 6px !important;
                    border-radius: 30px !important;
                    top: 10px !important;
                    right:0;
                    left:0;
                    margin: auto;
                }
            </style>
        </h:head>
        <h:body id="h" style="overflow:hidden !important;">

            <f:metadata>
                <f:viewAction action="#{valores.PersistenciaMapa(login.pp, login.satellite)}"/>
                <f:viewParam name="seqRota" value="#{valores.sequencia}" />
                <f:viewParam name="codfil" value="#{valores.codfil}" />
                <f:viewAction action="#{valores.carregarMapa()}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <ui:include src="/botao_panico.xhtml"/>

                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icones_satmob_carroforte.png" height="40" style="margin-top:-6px !important;" />
                                    <label class="TituloPagina">#{localemsgs.RotasValores}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{valores.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{valores.filialDesc}<label id="btTrocarFilial">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{valores.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{valores.filiais.bairro}, #{valores.filiais.cidade}/#{valores.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6">
                                    <p:commandLink action="#{valores.MapaDiaAnterior}" update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{valores.dataTela}" maxdate="#{valores.ultimoDia}" locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                        <p:ajax event="dateSelect" listener="#{valores.SelecionarDataMapa}" update="main cabecalho" />
                                    </p:calendar>

                                    <p:commandLink action="#{valores.MapaDiaPosterior}" update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="display:none !important;">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{valores.filialDesc}
                                    </div>
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.QtdRotas}: #{valores.total}
                                    </div>
                                    <div class="ui-grid-col-4">

                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>

                <div id="divFundoLightBox" style="position: absolute; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 999; display: none;"></div>
                <div id="divItemLightBox" style="position: absolute; width: 80%; height: 80%; background-color: rgb(255,255,255); z-index: 9991; top:0;right:0;bottom:0;left:0;margin:auto; display: none;">
                    <label id="btLightBoxClose" onmousedown="fecharLight();"><i class="fa fa-times"></i>&nbsp;&nbsp;#{localemsgs.Fechar}</label>
                </div>
                
                <h:form id="main">
                    <div class="FundoMapa">
                        <p:panel styleClass="painelCadastro" id="painelCadastro" style="overflow:auto !important;">
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-6,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel value="#{localemsgs.Rota} #{valores.rota}" style="font-size:12pt !important;" />
                                <p:outputLabel for="tipoMapa" value="#{localemsgs.OrigemMapa}" id="lblOrigem" style="font-size:12pt !important; float:right"/>
                                <p:selectOneMenu id="tipoMapa" value="#{valores.tipoMapa}" style="width: 100%" >
                                    <f:selectItem itemLabel="#{localemsgs.Clientes}" itemValue="1" />
                                    <f:selectItem itemLabel="#{localemsgs.Rastreador}" itemValue="2" />
                                    <p:ajax event="valueChange" update="msgs painelCadastro"
                                            listener="#{valores.carregarMapa}" />
                                </p:selectOneMenu>
                                <p:commandButton id="btAtualizarMapa" value="#{localemsgs.AtualizarMapa}" onclick="location.reload();" icon="fa fa-refresh"
                                                 styleClass="botao" style="width: 100% !important; height: 100% !important; padding: 0px !important; text-align:center !important;"
                                                 type="submit"/>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-10,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:panel style="position:relative;">
                                    <label class="AbrirFiltros" style="left:0px; display: none;"><i class="fa fa-filter"></i>&nbsp;<h:outputText value="#{localemsgs.Filtrar.toUpperCase()}" /></label>
                                    <div id="divQuadroResumo" style="left: -260px; display:none">
                                        <h:outputText class="fa fa-times FecharFiltros" title="#{localemsgs.Fechar}" style="display:none" />

                                        <div class="ItemResumo" style="padding-bottom: 1px !important;">
                                            <label id="lblTituloFiltro">
                                                <i class="fa fa-filter"/>
                                                &nbsp;
                                                <h:outputText value="#{localemsgs.Trajetos}" />
                                            </label>
                                            <div class="ItemZebrado">
                                                <label class="QdeStatus" cor="R"></label>
                                                <label>
                                                    <input type='checkbox' id='chkTrajetoAndamentoExecutado' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkTrajetoAndamentoExecutado'>
                                                        <h:outputText value=" #{localemsgs.TrajetoAndamentoExecutado}" />
                                                    </label>
                                                </label>
                                            </div>
                                            <div class="Item">
                                                <label class="QdeStatus" cor="G"></label>
                                                <label>
                                                    <input type='checkbox' id='chkTrajetoAndamentoRestante' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkTrajetoAndamentoRestante'>
                                                        <h:outputText value="#{localemsgs.TrajetoAndamentoRestante}"/>
                                                    </label>
                                                </label>
                                            </div>
                                            <div class="ItemZebrado">
                                                <label class="QdeStatus" cor="B" style="padding:1px !important"></label>
                                                <label>
                                                    <input type='checkbox' id='chkTrajetoExecutado' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkTrajetoExecutado'>
                                                        <h:outputText value="#{localemsgs.TrajetoExecutado}" />
                                                    </label>

                                                </label>
                                            </div>
                                            <div class="Item">
                                                <label class="QdeStatus" cor="C" style="padding:1px !important"></label>
                                                <label>
                                                    <input type='checkbox' id='chkTrajetoProgramado' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkTrajetoProgramado'>
                                                        <h:outputText value="#{localemsgs.TrajetoProgramado}" />
                                                    </label>
                                                </label>
                                            </div>
                                        </div>
                                        <p:lightBox iframe="true" id="lightboxListaRotas">
                                            <h:link outcome="direccion.xhtml?faces-redirect=true" includeViewParams="true">
                                                <f:viewParam name="ref_upd" value="#{Math.random()}" />
                                                <f:viewParam name="seqRota" value="#{valores.sequencia}" />
                                                <f:viewParam name="codfil" value="#{valores.codfil}" />
                                                <f:viewParam name="dataTela" value="#{valores.dataTela}" />

                                                <p:commandButton value="#{localemsgs.TodasRotas}" onclick="OcultarObjetos();" id="btAbrirModal" icon="fa fa-list"
                                                                 styleClass="botao BotaoResumoQuadro" style="width: 100% !important; text-align:center !important;"
                                                                 type="submit"/>
                                            </h:link>
                                        </p:lightBox>
                                        <p:linkButton id="btMapaTotal" outcome="mapa_total.xhtml" styleClass="botao BotaoResumoQuadro" value="#{localemsgs.ProgramacaoGeral}" icon="fa fa-clock-o" style="color:#FFF !important; min-width: 100% !important; padding-top:5px !important;">
                                            <f:param name="faces-redirect" value="true" />
                                            <f:param name="codfil" value="#{valores.codfil}" />
                                            <f:param name="dataTela" value="#{valores.dataTela}" />
                                        </p:linkButton>
                                        <p:lightBox iframe="true" id="lightboxListaPosicoes">
                                            <h:link outcome="lista_posicoes.xhtml?faces-redirect=true" includeViewParams="true">
                                                <f:viewParam name="ref_upd" value="#{Math.random()}" />
                                                <f:viewParam name="seqRota" value="#{valores.sequencia}" />
                                                <f:viewParam name="codfil" value="#{valores.codfil}" />
                                                <f:viewParam name="dataTela" value="#{valores.dataTela}" /> 

                                                <p:commandButton value="#{localemsgs.PosicoesRota}" onclick="OcultarObjetos2();" id="btAbrirModalPosicoes" icon="fa fa-map-marker"
                                                                 styleClass="botao BotaoResumoQuadro" style="width: 100% !important; text-align:center !important;"
                                                                 type="submit" />
                                            </h:link>
                                        </p:lightBox>
                                        <input id="txtPesquisarCliente" type="text" class="form-control" style="width:340px !important; margin-top:15px; padding:15px !important; height:50px !important" placeholder="#{localemsgs.PesquisarCliente}" />
                                    </div>

                                    <div id="mapGoogle" style="height: calc(100vh - 240px)"></div>
                                </p:panel>
                                <p:panel class="Rotas">
                                    <div id="legend"><h3 style="color:#FFF !important; background-color: #3479A3 !important; border-radius:3px 3px 0px 0px; margin-top: 3px !important; margin-bottom:3px !important; padding-bottom:5px !important; padding-top:3px !important;padding-left:7px; font-size:14pt !important; font-weight:500 !important;"><i class="fa fa-map" style="font-size:12pt !important;"></i>&nbsp;#{localemsgs.Rotas}<label style="background-color:#FFF; color:steelblue; font-weight: bold; font-size:10pt; text-align:center; width: 60px; border-radius: 40px; padding-top:3px !important; float:right !important; padding-bottom:2px !important; margin-right: 6px; margin-top:1px">#{valores.qtdeRotasMapa}</label></h3></div>
                                </p:panel>
                            </p:panelGrid>

                            <p:panelGrid columns="12" columnClasses="ui-grid-col-12,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="margin-left: 0px;padding:2px; border:thin solid #DDD; background-color:whitesmoke; max-height: 300px !important; overflow: hidden; border-radius: 3px; margin-top:6px; margin-bottom:8px;width: calc(100% - 15px)">
                                <div id="directionsText" style="max-height: 300px !important; overflow: auto; width:100%;">
                                    <center>
                                        <i class="fa fa-refresh fa-spin fa-fw"></i>
                                    </center>
                                </div>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <h:outputLabel for="clientes" value="#{localemsgs.Clientes}: " rendered="#{valores.tipoMapa eq '1'}"/>
                                <p:selectCheckboxMenu id="clientes" value="#{valores.posicoesClientesMapa}" label="#{localemsgs.Clientes}"
                                                      multiple="true" filter="true" filterMatchMode="startsWith" panelStyle="width:250px"
                                                      showHeader="false" converter="omnifaces.SelectItemsIndexConverter" updateLabel="rue"
                                                      rendered="#{valores.tipoMapa eq '1'}">
                                    <f:selectItems value="#{valores.posicoesClientes}"/>
                                    <p:ajax event="change" listener="#{valores.atualizarRoteiroClientes}"
                                            update="msgs painelCadastro"/>
                                </p:selectCheckboxMenu>

                                <h:outputLabel for="horarios" value="#{localemsgs.HorarioPosicoes}: " rendered="#{valores.tipoMapa eq '2'}"/>
                                <p:selectCheckboxMenu id="horarios" value="#{valores.posicoesRastreadorMapa}" label="#{localemsgs.HorarioPosicoes}"
                                                      multiple="true" filter="true" filterMatchMode="startsWith" panelStyle="width:250px"
                                                      showHeader="false" converter="omnifaces.SelectItemsIndexConverter" updateLabel="rue"
                                                      rendered="#{valores.tipoMapa eq '2'}">
                                    <f:selectItems value="#{valores.posicoesRastreador}"/>
                                    <p:ajax event="change" listener="#{valores.atualizarRoteiroRastreador}"
                                            update="msgs painelCadastro"/>
                                </p:selectCheckboxMenu>

                                <h:outputText value="#{localemsgs.Horario}:" />
                                <p:selectOneButton value="#{valores.horarioMapa}">
                                    <f:selectItem itemLabel="#{localemsgs.Manha}" itemValue="manha" />
                                    <f:selectItem itemLabel="#{localemsgs.Tarde}" itemValue="tarde" />
                                    <p:ajax event="change" update="msgs painelCadastro"
                                            listener="#{valores.carregarMapa}" />
                                </p:selectOneButton>
                            </p:panelGrid>
                            <script type="text/javascript">
                                // <![CDATA[
                                $('.painelCadastro').css('max-height', ($('html').height() - 147) + 'px');
                                $('#legend').css('height', ($('.Rotas').height() - 0) + 'px');
                                var map;
                                var ListaMarcadores = [];
                                var directionsDisplayNext;
                                var directionsDisplayBack;
                                var directionsDisplayProgramming;
                                var directionsDisplay;
                                var tmrOcultarObjetos;
                                $('#divQuadroResumo').css('display', 'none');
                                function fechar(){
                                $('[id*="lightboxListaPosicoes_modal"]').click();
                                return false;
                                }

                                function OcultarObjetos2(){
                                $('[id*="btFechar"]').remove();
                                $('.ui-lightbox-content').prepend('<label id="btFechar" onmousedown="fechar();" ref="fecha"><i class="fa fa-times"></i>&nbsp;&nbsp;#{localemsgs.Fechar}</label>');
                                }


                                function OcultarObjetos(){
                                $('[id*="btFechar"]').remove();
                                setTimeout(function(){
                                $('.ui-lightbox-content').prepend('<label id="btFechar"><i class="fa fa-times"></i>&nbsp;&nbsp;#{localemsgs.FecharAtualizar}</label>');
                                }, 1000);
                                clearInterval(tmrOcultarObjetos);
                                tmrOcultarObjetos = setInterval(function(){
                                if (!$('.ui-lightbox-content iframe').attr('src'))
                                        clearInterval(tmrOcultarObjetos);
                                var iBody = $('.ui-lightbox-content iframe').contents().find('body');
                                if (iBody){
                                if (iBody.html()){
                                try{
                                iBody.find('header').remove();
                                iBody.find('footer').remove();
                                iBody.find('#sidebar').remove();
                                iBody.find('.content-wrapper').attr('class', 'a');
                                }
                                catch (e){}
                                }
                                }
                                }, 10);
                                }

                                function initMap() {
                                map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                zoom: 6,
                                        center: #{valores.centro},
                                        gestureHandling: 'cooperative'
                                });
                                var directionsServiceNext = new google.maps.DirectionsService;
                                directionsDisplayNext = new google.maps.DirectionsRenderer({ polylineOptions: { strokeColor: "#006633", strokeOpacity: 0.9, strokeWeight: 5 },
                                        suppressMarkers: true });
                                var directionsServiceBack = new google.maps.DirectionsService;
                                directionsDisplayBack = new google.maps.DirectionsRenderer({ polylineOptions: { strokeColor: "#F00", strokeOpacity: 0.8, strokeWeight: 7 },
                                        suppressMarkers: true });
                                var directionsService = new google.maps.DirectionsService;
                                directionsDisplay = new google.maps.DirectionsRenderer({ polylineOptions: { strokeColor: "#2c58b1", strokeOpacity: 0.7, strokeWeight: 6 },
                                        suppressMarkers: true });
                                var directionsServiceProgramming = new google.maps.DirectionsService;
                                directionsDisplayProgramming = new google.maps.DirectionsRenderer({ polylineOptions: { strokeColor: "#505050", strokeOpacity: 0.7, strokeWeight: 6 },
                                        suppressMarkers: true });
                                var legend = document.getElementById('legend');
                                #{valores.markers}
                                #{valores.contentStrings}
                                #{valores.infoWindows}
                                #{valores.legend}

                                directionsDisplay.setMap(map);
                                directionsDisplay.setPanel(document.getElementById('directionsText'));
                                directionsDisplayBack.setMap(map);
                                directionsDisplayNext.setMap(map);
                                directionsDisplayProgramming.setMap(map);
                                var onChangeHandler = function () {
                                $('#chkTrajetoAndamentoExecutado, #chkTrajetoAndamentoRestante, #chkTrajetoExecutado').prop('checked', true);
                                if ($('select[id="main:tipoMapa_input"]').val() == '1'){
                                calculateAndDisplayRouteBack(directionsServiceBack, directionsDisplayBack);
                                calculateAndDisplayRouteNext(directionsServiceNext, directionsDisplayNext);
                                calculateAndDisplayRouteProgramming(directionsServiceProgramming, directionsDisplayProgramming);
                                setTimeout(function(){

                                if ($(document).width() >= 600){
                                //Carregar filtro aberto
                                $('.AbrirFiltros').click();
                                setTimeout(function(){
                                $('.AbrirFiltros').css('display', 'none');
                                }, 800);
                                }
                                else{
                                //Deixar filtro fechado
                                $('.AbrirFiltros').fadeIn();
                                }
                                }, 1500);
                                }

                                setTimeout(function(){
                                calculateAndDisplayRoute(directionsService, directionsDisplay);
                                setTimeout(function(){
                                $('#directionsText i').remove();
                                }, 500);
                                }, 500);
                                };
                                onChangeHandler();
                                }

                                function calculateAndDisplayRoute(directionsService, directionsDisplay) {
                                directionsService.route({
                                #{valores.directions}
                                travelMode: 'DRIVING',
                                        region:'es'
                                }, function (response, status) {
                                if (status === 'OK') {
                                directionsDisplay.setDirections(response);
                                } else {

                                }
                                console.log(response);
                                });
                                }

                                function calculateAndDisplayRouteBack(directionsService, directionsDisplay) {
                                directionsService.route({
                                #{valores.directionsTrajetoAtual}
                                travelMode: 'DRIVING'
                                }, function (response, status) {
                                if (status === 'OK') {
                                directionsDisplay.setDirections(response);
                                } else {

                                }
                                console.log(response);
                                });
                                }

                                function calculateAndDisplayRouteNext(directionsService, directionsDisplay) {
                                directionsService.route({
                                #{valores.directionsTrajetoProximo}
                                travelMode: 'DRIVING'
                                }, function (response, status) {
                                if (status === 'OK') {
                                directionsDisplay.setDirections(response);
                                } else {

                                }
                                console.log(response);
                                });
                                }

                                function calculateAndDisplayRouteProgramming(directionsService, directionsDisplay) {
                                directionsService.route({
                                #{valores.directionsTrajetoFuturo}
                                travelMode: 'DRIVING'
                                }, function (response, status) {
                                if (status === 'OK') {
                                directionsDisplay.setDirections(response);
                                } else {

                                }
                                console.log(response);
                                });
                                }

                                $(document)
                                        .on('change', '#divQuadroResumo input[type="checkbox"]', function(){
                                        switch ($(this).attr('id')){
                                        case 'chkTrajetoAndamentoExecutado':
                                                if ($(this).prop('checked'))
                                                directionsDisplayBack.setMap(map);
                                        else
                                                directionsDisplayBack.setMap(null);
                                        break;
                                        case 'chkTrajetoAndamentoRestante':
                                                if ($(this).prop('checked'))
                                                directionsDisplayNext.setMap(map);
                                        else
                                                directionsDisplayNext.setMap(null);
                                        break;
                                        case 'chkTrajetoExecutado':
                                                if ($(this).prop('checked'))
                                                directionsDisplay.setMap(map);
                                        else
                                                directionsDisplay.setMap(null);
                                        break;
                                        case 'chkTrajetoProgramado':
                                                if ($(this).prop('checked'))
                                                directionsDisplayProgramming.setMap(map);
                                        else
                                                directionsDisplayProgramming.setMap(null);
                                        break;
                                        }
                                        })
                                        .on('click', '.btAbrirModal', function(){
                                        $('[id*="btAbrirModal"]').click();
                                        })
                                        .on('click', '#btFechar:not([ref="fecha"])', function(){
                                        $(this).find('.fa-times').attr('class', 'fa fa-refresh fa-spin fa-fw');
                                        location.reload();
                                        })
                                        .on('click', '.AbrirFiltros', function(){
                                        $(this).stop().animate({
                                        'left': '-50px'
                                        }, 300);
                                        setTimeout(function(){
                                        $('#divQuadroResumo').css('display', '').stop().animate({
                                        'left': '10px'
                                        }, 500);
                                        setTimeout(function(){
                                        $('.FecharFiltros').fadeIn(600);
                                        }, 800);
                                        $('.AbrirFiltros').css('display', 'none');
                                        }, 300);
                                        })
                                        .on('click', '.FecharFiltros', function(){
                                        $('#divQuadroResumo').stop().animate({
                                        'left': '-380px'
                                        }, 500);
                                        setTimeout(function(){
                                        $('.AbrirFiltros').css('display', '').stop().animate({
                                        'left': '0px'
                                        }, 300);
                                        $('.FecharFiltros').fadeOut();
                                        $('#divQuadroResumo').css('display', 'none');
                                        }, 500);
                                        })
                                        .on('click', '[id*="btAtualizarMapa"]', function(){
                                        //$(this).find('.fa-refresh').addClass('fa-spin fa-fw');
                                        })
                                        .on('click', '[id*="btMapaTotal"]', function(){
                                        //$(this).find('.fa-clock-o').addClass('fa-spin').addClass('fa-fw');
                                        })
                                        .on('click', '#btTrocarFilial', function(){
                                        top.location.href = '../param.xhtml';
                                        })
                                        .on('keyup', '#txtPesquisarCliente', function(){
                                        if ($(this).val().trim() != ''){
                                        for (I = 0; I < ListaMarcadores.length; I++){
                                        if (ListaMarcadores[I].title.indexOf($(this).val().toUpperCase()) > - 1)
                                                ListaMarcadores[I].setMap(map);
                                        else
                                                ListaMarcadores[I].setMap(null);
                                        }
                                        }
                                        else
                                                for (I = 0; I < ListaMarcadores.length; I++)
                                                ListaMarcadores[I].setMap(map);
                                        })
                                        .on('click', 'a[ref="viewMap"]', function(){
                                        let inURl = 'lista_posicoes.xhtml?';
                                        inURl += 'seqRota=' + $(this).attr('seqrota');
                                        inURl += '&codfil=' + $(this).attr('codfil');
                                        inURl += '&dataTela=' + $(this).attr('datatela');
                                        inURl += '&lat=' + $(this).attr('lat');
                                        inURl += '&lon=' + $(this).attr('lon');
                                        inURl += '&hora=' + $(this).attr('hora');
                                        inURl += '&icon=' + $(this).attr('icon');
                                        
                                        $('#divFundoLightBox').css('display','');
                                        $('#divItemLightBox').find('iframe').remove();
                                        $('#divItemLightBox').css('display','').append('<iframe src="'+inURl+'" style="padding:0px; margin:0px; width:100%;height:100%;border:none"></iframe>');
                                        })
                                            .on('click','#divFundoLightBox', function(){
                                                fecharLight();
                                            })
                                        ;
                                $(window).resize(function () {
                                $('.painelCadastro').css('max-height', ($('html').height() - 147) + 'px');
                                $('#legend').css('height', ($('.Rotas').height() - 0) + 'px');
                                })
                                        ;
                                        
                               function fecharLight(){
                                   $('#divFundoLightBox').css('display','none');
                                   $('#divItemLightBox').css('display','none');
                                   $('#divItemLightBox').find('iframe').remove();
                               }
                                // ]]>
                            </script>
                            <script src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}&amp;callback=initMap"></script>
                        </p:panel>

                        <p:dialog header="#{localemsgs.Guias}" styleClass="box-primary"
                                  widgetVar="dlgGuiasSelecao" minHeight="500" width="500" modal="true"  appendTo="@(body)"
                                  responsive="true" dynamic="true">

                            <p:dataGrid id="tabelaGtv" value="#{valores.guias}"
                                        var="gtv" columns="1">
                                <p:accordionPanel styleClass="painelCadastro" activeIndex="1"
                                                  onTabShow="PF('dlgGuiasSelecao').initPosition()"
                                                  onTabClose="PF('dlgGuiasSelecao').initPosition()">
                                    <p:tab>
                                        <f:facet name="title">
                                            <h:outputText value="#{localemsgs.Guia} #{gtv.guia} - #{gtv.serie}"/>

                                            <p:commandLink title="#{localemsgs.Guias}" ajax="true" update="main:tabelaGtv" style="float: right"
                                                           actionListener="#{valores.abrirGuia(gtv)}">
                                                <h:outputText styleClass="fas fa-print" style="margin:0 auto;"/>
                                            </p:commandLink>
                                        </f:facet>
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                                     layout="grid" styleClass="ui-panelgrid-blank">

                                            <h:outputText value="#{localemsgs.Valor}:"/>
                                            <h:outputText value="#{gtv.valor}"
                                                          title="#{gtv.valor}" converter="conversormoeda"/>

                                            <h:outputText value="#{localemsgs.OrigemColeta}:"/>
                                            <p:column>
                                                <h:outputText value="#{gtv.agSB}" title="#{gtv.agSB}"/>
                                                <h:outputText value="#{gtv.origem}" title="#{gtv.origem}"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <h:outputText value="#{localemsgs.DtColeta}: "/>
                                            <h:outputText value="#{gtv.dtColeta}" title="#{gtv.dtColeta}" converter="conversorData"/>

                                            <h:outputText value="#{localemsgs.HrColeta}: "/>
                                            <p:column>
                                                <h:outputText value="#{gtv.hrCheg}" title="#{gtv.hrCheg}" converter="conversorHora"/>
                                                <h:outputText value=" - "/>
                                                <h:outputText value="#{gtv.hrSaida}" title="#{gtv.hrSaida}" converter="conversorHora"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <h:outputText value="#{localemsgs.DestinoEntrega}:"/>
                                            <p:column>
                                                <h:outputText value="#{gtv.agSBD}" title="#{gtv.agSBD}"/>
                                                <h:outputText value="#{gtv.destino}" title="#{gtv.destino}"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <h:outputText value="#{localemsgs.DtEntrega}: "/>
                                            <h:outputText value="#{gtv.dtEntrega}" title="#{gtv.dtEntrega}" converter="conversorData"/>

                                            <h:outputText value="#{localemsgs.HrCheg}: "/>
                                            <p:column>
                                                <h:outputText value="#{gtv.hrCheg_E}" title="#{gtv.hrCheg_E}" converter="conversorHora"/>
                                                <h:outputText value=" - " rendered="#{gtv.hrCheg_E ne '' and gtv.hrCheg_S ne '' and gtv.hrCheg_E ne null and gtv.hrCheg_S ne null}"/>
                                                <h:outputText value="#{gtv.hrCheg_S}" title="#{gtv.hrCheg_S}" converter="conversorHora"/>
                                            </p:column>
                                        </p:panelGrid>
                                    </p:tab>
                                </p:accordionPanel>
                            </p:dataGrid>
                        </p:dialog>

                    </div>
                </h:form>

                <h:form id="impressao">
                    <p:dialog widgetVar="dlgImprimir" positionType="absolute" id="dlgImprimir" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" class="box-primary">
                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.Guia}" style="float: left"/>

                            <p:commandLink title="#{localemsgs.Imprimir}" style="position: absolute; width:10px !important; right: 70px;">
                                <i class="fa fa-print"></i>
                                <p:printer target="guiaimpressa"/>
                            </p:commandLink>

                            <p:commandLink title="#{localemsgs.Download}" update="msgs" ajax="false"
                                           actionListener="#{valores.gerarGuiaDownload}" style="position: absolute; width:10px !important; right: 100px;">
                                <i class="fa fa-download"></i>
                                <p:fileDownload value="#{valores.arquivoDownload}"/>
                            </p:commandLink>

                        </f:facet>

                        <p:panel class="guiaimpressa" styleClass="guiaimpressa"  style="padding:12px !important; border:thin solid #DDD !important; overflow:auto !important;max-height: 500px !important">
                            <h:outputText id="guiaimpressa" value="#{valores.html}" escape="false"/>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>
            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <!--<div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>-->
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>
        </h:body>
    </f:view>
</html>

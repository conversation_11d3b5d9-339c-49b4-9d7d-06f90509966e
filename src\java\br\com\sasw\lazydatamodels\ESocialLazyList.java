/*
 */
package br.com.sasw.lazydatamodels;

import Controller.ESocial.ESocialSatWeb;
import Dados.Persistencia;
import SasBeans.XMLeSocial;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ESocialLazyList extends LazyDataModel<XMLeSocial> {

    private static final long serialVersionUID = 1L;
    private List<XMLeSocial> xmleSocial;
    private final ESocialSatWeb eSocialSatWeb;
    private Persistencia persistencia;

    public ESocialLazyList(Persistencia pst) {
        eSocialSatWeb = new ESocialSatWeb();
        persistencia = pst;
    }

    @Override
    public List<XMLeSocial> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.xmleSocial = this.eSocialSatWeb.listagemPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.eSocialSatWeb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.xmleSocial;
    }

    @Override
    public Object getRowKey(XMLeSocial xml) {
        return xml.getRow();
    }

    @Override
    public XMLeSocial getRowData(String row) {
        for (XMLeSocial xml : this.xmleSocial) {
            if (row.equals(xml.getRow())) {
                return xml;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ATMHist {

    private BigDecimal OS;
    private BigDecimal CodFil;
    private BigDecimal Sequencia;
    private String Historico;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public ATMHist() {
        this.OS = new BigDecimal("0");
        this.CodFil = new BigDecimal("0");
        this.Sequencia = new BigDecimal("0");
        this.Historico = "";
        this.Operador = "";
        this.Dt_Alter = LocalDate.now();
        this.Hr_Alter = "";
    }

    /**
     * @return the OS
     */
    public BigDecimal getOS() {
        return this.OS;
    }

    /**
     * @param OS the OS to set
     */
    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return this.CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Sequencia
     */
    public BigDecimal getSequencia() {
        return this.Sequencia;
    }

    /**
     * @param Sequencia the Sequencia to set
     */
    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    /**
     * @return the Historico
     */
    public String getHistorico() {
        return this.Historico;
    }

    /**
     * @param Historico the Historico to set
     */
    public void setHistorico(String Historico) {
        this.Historico = Historico;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return this.Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return this.Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return this.Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

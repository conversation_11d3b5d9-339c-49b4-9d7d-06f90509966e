package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.TesFecha;
import SasBeansCompostas.TesFechaGeracaoGTV;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesFechaDao {

    private final Persistencia persistencia;

    public TesFechaDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public List<TesFechaGeracaoGTV> getTesFechasGeracaoGTV(String codFil, String data) throws Exception {
        String sql = "SELECT TesFecha.CodCli, Clientes.NRed, Count(DISTINCT Pedido.Numero) Qtde, Sum(Pedido.Valor) Valor\n"
                + "FROM TesFecha\n"
                + "         LEFT JOIN Clientes ON Clientes.Codigo = TesFecha.CodCli\n"
                + "    AND Clientes.CodFil = TesFecha.CodFil\n"
                + "         INNER JOIN Pedido ON Pedido.Data = ? \n"
                + "    AND Pedido.CodFil = TesFecha.CodFil\n"
                + "    AND Pedido.CodCli1 IN (\n"
                + "        SELECT CodCli\n"
                + "        FROM CxForte\n"
                + "        WHERE Cxforte.CodFil = TesFecha.CodFil)\n"
                + "         INNER JOIN OS_Vig\n"
                + "                    ON OS_Vig.OS = Pedido.OS\n"
                + "                        AND OS_Vig.CodFil = Pedido.CodFil\n"
                + "                        AND OS_Vig.CliDst = TesFecha.CodCli\n"
                + "WHERE TesFecha.codfil = ? \n"
                + "  AND Pedido.Numero NOT IN (SELECT Pedido\n"
                + "                            FROM TesSaidas\n"
                + "                            WHERE TesSaidas.CodFil = Pedido.CodFil\n"
                + "                              AND TesSaidas.Pedido IS NOT NULL)\n"
                + "GROUP BY TesFecha.CodCli, Clientes.NRed";

        List<TesFechaGeracaoGTV> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);

            consulta.select();
            while (consulta.Proximo()) {
                TesFechaGeracaoGTV row = new TesFechaGeracaoGTV();
                TesFecha tesFecha = new TesFecha();
                Clientes cliente = new Clientes();

                tesFecha.setCodCli(consulta.getString("CodCli"));
                cliente.setNRed(consulta.getString("NRed"));
                row.setQtde(consulta.getBigDecimal("Qtde"));
                row.setValor(consulta.getBigDecimal("Valor"));

                row.setTesFecha(tesFecha);
                row.setClientes(cliente);
                lista.add(row);
            }

            return lista;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class TesMoedasVlr {

    private String Sequencia;
    private String CodFil;
    private String CodMoeda;
    private String DtCotacao;
    private String Valor;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodMoeda() {
        return CodMoeda;
    }

    public void setCodMoeda(String CodMoeda) {
        this.CodMoeda = CodMoeda;
    }

    public String getDtCotacao() {
        return DtCotacao;
    }

    public void setDtCotacao(String DtCotacao) {
        this.DtCotacao = DtCotacao;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }
}

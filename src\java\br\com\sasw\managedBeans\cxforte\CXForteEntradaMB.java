package br.com.sasw.managedBeans.cxforte;

import Controller.CxForte.CxForteSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Controller.Tesouraria.TesourariaController;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.OS_Vig;
import SasBeans.Rotas;
import SasBeans.Rt_Perc;
import SasBeansCompostas.CxFEntradasDTO;
import SasBeansCompostas.CxFGuiasGTVDTO;
import br.com.sasw.managedBeans.BaseBeanMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isBrasifort;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isConfederal;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isGloval;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isPreserve;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isProtege;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isSasw;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isTecban;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isTecno;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isTransexpert;
import static br.com.sasw.pacotesuteis.utilidades.Empresas.isTransvip;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.inject.Named;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

/**
 *
 * <AUTHOR>
 */
@Named(value = "cxForteEntrada")
@ViewScoped
public class CXForteEntradaMB extends BaseBeanMB {

    private final CxForteSatMobWeb service;
    private final RotasSatWeb rotasSatWeb;
    private final TesourariaController tesourariaController;
    private CxForte caixaForteSelecionado;
    private List<CxForte> caixasFortes;
    private Rotas rotaSelecionada;
    private List<Rotas> rotas;
    private CxFEntradasDTO entradaSelecionada;
    private List<CxFEntradasDTO> entradas;
    private CxFGuias guiaSelecionada, guiaEdicao;
    private List<CxFGuias> guias;
    private CxFGuiasGTVDTO guiasCxForteAtual;
    private List<CxFGuiasGTVDTO> listaGuiasCxForte;
    private int guiasCxForteIndex;
    private Rt_Perc percursoAtual, listaHorasRtPercAtual;
    private List<Rt_Perc> listaPercurso, listaHorasRtPerc;
    private OS_Vig OSAtual;
    private int listaHorasRtPercIndex;
    private final Map filters;
    private boolean somenteDestinoCxForte, capturaAuto;
    private String guia, serie, tipo;
    private String valor;
    private BigDecimal valorSomaVol, valorQtdeVol;
    private Integer qtdeVol;
    private static final String ROTA_TESOURARIA = "090";
    private final String operadorRed;
    private boolean alterarOS, gerarPedido;
    private boolean mostrarResultado, rtGuiasExiste;
    // labels
    private Integer parada;
    private String codCliOriTmp, NRedOriTmp;
    private String codigoClioenteOri, NRedClienteOri, vHrO, vHrD;
    private String hora1, codigoClienteDst, NRedClienteDst;
    private String codigoOS, regiaoCliente;

    public CXForteEntradaMB() throws Exception {
        super();
        service = new CxForteSatMobWeb(persistencia);
        tesourariaController = new TesourariaController(persistencia);
        rotasSatWeb = new RotasSatWeb();
        somenteDestinoCxForte = true;
        capturaAuto = false;
        filters = getFilters();
        gerarPedidoPorEmpresa();
        operadorRed = RecortaAteEspaço(RecortaString(operador, 0, 10), 0, 10);
    }

    private Map getFilters() {
        Map newfilters = new HashMap<>();
        newfilters.put("sequencia", "");
        newfilters.put("rota", "");
        newfilters.put("codFil", codFil);
        newfilters.put("data", dataTela);
        newfilters.put("checked", somenteDestinoCxForte);

        return newfilters;
    }

    private void gerarPedidoPorEmpresa() {
        gerarPedido = false;
        if (isProtege(persistencia)
                || isTransexpert(persistencia)
                || isTransvip(persistencia)
                || isSasw(persistencia)
                || isGloval(persistencia)
                || isBrasifort(persistencia)) {
            gerarPedido = true;
        }
    }

    @PostConstruct
    public void init() {
        try {
            carregarCaixasFortes();
            carregarRotas();
        } catch (Exception e) {
            displayFatal(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void carregarCaixasFortes() {
        try {
            caixasFortes = service.getCxForteListFromCodFil(codFil);
            caixaForteSelecionado = caixasFortes.isEmpty() ? null : caixasFortes.get(0);
            if (caixaForteSelecionado != null && caixaForteSelecionado.getCodCli() != null) {
                filters.replace("sequencia", caixaForteSelecionado.getCodCli());
            } else {
                throw new Exception("SelecioneCaixaForte");
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            displayFatal("ErroObterCaixaForte");
        }
    }

    private void reaproveitaRotaAnterior() {
        if (rotaSelecionada == null) {
            return;
        }

        Optional<Rotas> rota = rotas.stream().
                filter((e) -> e.getRota().equals(rotaSelecionada.getRota()))
                .findFirst();

        rotaSelecionada = rota.isPresent() ? rota.get() : null;
    }

    private void carregarRotas() {
        try {
            rotas = rotasSatWeb.listarRotasData(codFil, dataTela, persistencia);
            if (rotas.isEmpty()) {
                displayWarn("ErroSemRotasNaData");
            } else if (rotas.size() == 1) {
                rotaSelecionada = rotas.get(0);
                onRotaUpdate();
            } else {
                reaproveitaRotaAnterior();
                onRotaUpdate();
            }
            percursoAtual = null;
        } catch (Exception e) {
            displayFatal(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void carregarEntradas() {
        try {
            entradas = service.listaTodasEntradasCxForte(filters);
//            PrimeFaces.current().ajax().update("main:tabela");
            limparCadastroTotal();
        } catch (Exception e) {
            displayFatal(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void limparEntradas() {
        entradas = new ArrayList<>();
//        PrimeFaces.current().ajax().update("main:tabela");
    }

    private void carregarGuias() {
        try {
            guias = service.getCxfGuiasEntrega2(entradaSelecionada.getSequencia(), entradaSelecionada.getHora1());
            PrimeFaces.current().ajax().update("guiaForm");
        } catch (Exception e) {
            displayFatal("ErroLerBD");
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void onRotaUpdate() {
        if (rotaSelecionada != null) {
            filters.replace("rota", rotaSelecionada.getRota());
            carregarEntradas();
        } else {
            limparEntradas();
        }
    }

    public void mudarDia(int incremento) {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, incremento);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            onChangeDate();
        } catch (ParseException e) {
            displayError("DiaInvalido");
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarData(SelectEvent data) {
        dataTela = (String) data.getObject();
        onChangeDate();
    }

    private void onChangeDate() {
        filters.replace("rota", "");
        filters.replace("data", dataTela);
        carregarRotas();
        carregarEntradas();
    }

    public void onChangeCodFil() {
        mudarFilialTela();
        filters.replace("sequencia", "");
        filters.replace("rota", "");
        filters.replace("codFil", codFil);
        filters.replace("data", dataTela);
        carregarCaixasFortes();
        carregarRotas();
        carregarEntradas();
    }

    public void onCaixaForteUpdate() {
        filters.replace("sequencia", "");
        filters.replace("rota", "");
        carregarRotas();
        carregarEntradas();
    }

    public void selecionarEntrada() {
        if (entradaSelecionada != null) {
            carregarGuias();
        } else {
            displayError("SelecioneEntrada");
        }
    }

    public void onSomenteDestinoCxForteUpdate() {
        filters.replace("checked", somenteDestinoCxForte);
        carregarEntradas();
    }

    public void onCapturaAutomatica() {
        displayWarn("ImplementandoFeature");
    }

    public void analisarGuiaESerie() {
        limparCadastro();
        if (guia != null && serie != null) {
            processarGuiaSerie();
        }
    }

    private boolean isRotaTesouraria() {
        return rotaSelecionada != null && rotaSelecionada.getRota().equals(ROTA_TESOURARIA);
    }

    public int sortNumericString(Object parada1, Object parada2) {
        try {
            int num1 = Integer.parseInt((String) parada1);
            int num2 = Integer.parseInt((String) parada2);
            if (num1 == num2) {
                return 0;
            }
            return num1 < num2 ? 1 : -1;
        } catch (NumberFormatException e) {
            return ((String) parada1).compareTo((String) parada2);
        }
    }

    private void locate(String parada) {
        for (Rt_Perc item : listaPercurso) {
            if (String.valueOf(item.getParada()).equals(parada)) {
                percursoAtual = item;
                return;
            }
        }
    }

    private void processarGuiaSerie() {
        try {
            verificarSerieOK();
            if (rotaSelecionada == null) {
                throw new Exception("SelecioneRota");
            }
            if (entradas == null || entradas.isEmpty()) {
                throw new Exception("RotaSemParadaDisponivel");
            }
            transExpertTesouraria();
            buscarParada();
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    private void verificarSerieOK() throws Exception {
        boolean existe = tesourariaController.validaSerieGuia(serie);
        if (!existe) {
            throw new Exception("SerieNaoCadastrada");
        }
    }

    private void transExpertTesouraria() throws Exception {
        if (isTransexpert(persistencia) && isRotaTesouraria()) {
            if (service.isGuiaPendenteTesouraria(codFil, guia, serie)) {
                throw new Exception("GuiaPendenteTesouraria");
            }
        }
    }

    private void buscarParada() throws Exception {
        alterarOS = false;
        mostrarResultado = true;
        rtGuiasExiste = true;
        percursoAtual = null;
        String sequencia = rotaSelecionada.getSequencia().toString();

        listaPercurso = service.getFromSequenciaCodCli2(sequencia, caixaForteSelecionado.getCodCli());
        locate(entradas.get(0).getParada());

        listaGuiasCxForte = service.getListaCxForteEntBuscaL(codFil, rotaSelecionada.getRota(), guia, serie, sequencia);
        if (!listaGuiasCxForte.isEmpty()) {
            guiasCxForteIndex = 0;
            guiasCxForteAtual = listaGuiasCxForte.get(0);
            if (listaGuiasCxForte.size() > 1) {
                PrimeFaces.current().executeScript("PF('dlgConfirmarParada').show();");
                return;
            }

            buscarParadaParte2();
        } else {
            preencherComResultados();
        }
    }

    public void utilizarParada() {
        try {
            PrimeFaces.current().executeScript("PF('dlgConfirmarParada').hide();");
            buscarParadaParte2();
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public void naoUtilizarParada() {
        guiasCxForteIndex++;
        if (guiasCxForteIndex >= listaGuiasCxForte.size()) {
            utilizarParada();
        } else {
            guiasCxForteAtual = listaGuiasCxForte.get(guiasCxForteIndex);
        }
    }

    public void utilizarHoraParada() {
        try {
            PrimeFaces.current().executeScript("PF('dlgParadaHorario').hide();");
            buscarParadaParte2();
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public void naoUtilizarHoraParada() {
        listaHorasRtPercIndex++;
        if (listaHorasRtPercIndex >= listaHorasRtPerc.size()) {
            displayError("TODO: Esgotou");
            PrimeFaces.current().executeScript("PF('dlgConfirmarParada').hide();");
        } else {
            listaHorasRtPercAtual = listaHorasRtPerc.get(listaHorasRtPercIndex);
        }
    }

    private void buscarParadaParte2() throws Exception {
        if (isTransvip(persistencia) && !rotaSelecionada.getRota().equals(ROTA_TESOURARIA)) {
            if (service.semGuiaSerieRtGuias(guia, serie)) {
                rtGuiasExiste = false;
            }
        }

        if (guiasCxForteAtual == null && rotaSelecionada.getRota().equals(ROTA_TESOURARIA)) {
            semParadaRotaNormal();
        } else if (guiasCxForteAtual != null && guiasCxForteAtual.getParada() > 0 && rtGuiasExiste) {
            locate(String.valueOf(guiasCxForteAtual.getParada()));
            parada = guiasCxForteAtual.getParada();
        } else {
            if (isTransvip(persistencia)) {
                Rt_Perc rtPerc = service.getRtPercFromDataNRed(dataTela, guiasCxForteAtual.getNRed());
                if (rtPerc != null) {
                    locate(String.valueOf(rtPerc.getParada()));
                    parada = rtPerc.getParada();
                } else {
                    mostrarResultado = false;
                    throw new Exception("ParadaNaoLocalizada");
                }
            } else {
                PrimeFaces.current().executeScript("PF('dlgCancelarOperacao').show();");
                return;
            }
        }

        mostrarParada();
        preencherComResultados();
    }

    private void semParadaRotaNormal() throws Exception {
        String sequencia = rotaSelecionada.getSequencia().toString();

        listaHorasRtPerc = service.getListaHora1DeSequencia(sequencia);

        if (!listaHorasRtPerc.isEmpty()) {
            vHrO = listaHorasRtPerc.get(0).getHora1(); // +1
            if (vHrO == null || vHrO.equals("")) {
                vHrO = "2010";
            }
            vHrD = service.getParadaCaixaForte(sequencia, caixaForteSelecionado.getCodCli()); // +2
            if (vHrD == null || vHrO.equals("")) {
                vHrD = "2010";
            }
            // RtInsere TODO
        }

        listaHorasRtPerc = service.getParadasOriCaixaForte(sequencia, guiasCxForteAtual.getCliOri(), caixaForteSelecionado.getCodCli());
        if (listaHorasRtPerc.size() > 1) {
            listaHorasRtPercIndex = 0;
            listaHorasRtPercAtual = listaHorasRtPerc.get(0);
            PrimeFaces.current().executeScript("PF('dlgParadaHorario').show();");
        }
    }

    private void mostrarParada() {
        String paradaStr = String.valueOf(parada);
        for (CxFEntradasDTO entrada : entradas) {
            if (entrada.getParada().equals(paradaStr)) {
                entradaSelecionada = entrada;
                break;
            }
        }
        if (entradaSelecionada != null) {
            carregarGuias();
        }
    }

    public void cancelarOperacao() {
        try {
            PrimeFaces.current().executeScript("PF('dlgCancelarOperacao').hide();");
            mostrarResultado = false;
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public void prosseguirOperacao() {
        try {
            PrimeFaces.current().executeScript("PF('dlgCancelarOperacao').hide();");
            if (percursoAtual.getNRed().toUpperCase().contains("SERET")) {
                service.updateOSDeGTV(codFil, guia, serie, "0");
            }
            mostrarParada();
            preencherComResultados();
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    private void preencherComResultados() {
        try {
            if (percursoAtual == null) {
                throw new Exception("PercursoNaoEncontrado");
            }

            if (mostrarResultado) {
                codCliOriTmp = percursoAtual.getCodCli1();
                NRedOriTmp = percursoAtual.getNRed();
                codigoOS = percursoAtual.getOS().toString();
                buscarValor();
                buscarValorTotalRtGuias();
                buscarValorTotalRtGuiasVol();

                if (gerarPedido || alterarOS) {
                    alterarOS();
                }
            }
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    private void buscarValor() {
        if (!listaGuiasCxForte.isEmpty()) {
            codigoClioenteOri = codCliOriTmp;
            NRedClienteOri = NRedOriTmp;
            vHrO = ""; // TODO
            hora1 = vHrO.equals("") ? guiasCxForteAtual.getHora1() : vHrO;
            codigoClienteDst = guiasCxForteAtual.getCliDst();
            NRedClienteDst = guiasCxForteAtual.getNRedDst();
            if (rotaSelecionada.getRota().equals(ROTA_TESOURARIA)) {
                valor = guiasCxForteAtual.getTotalGeral().toString();
            } else {
                if (guiasCxForteAtual.getParadaRtGuias() != null && !guiasCxForteAtual.getParadaRtGuias().equals("0")) {
                    BigDecimal valorGTV = guiasCxForteAtual.getValorGTV();
                    valor = valorGTV != null ? valorGTV.toString() : "";
                } else {
                    BigDecimal totalGeral = guiasCxForteAtual.getTotalGeral();
                    valor = totalGeral != null ? totalGeral.toString() : "";
                }
            }
        } else {
            codigoClienteDst = "";
            NRedClienteDst = "";
            codigoClioenteOri = "";
            NRedClienteOri = "";
            hora1 = "";
        }
    }

    private void buscarValorTotalRtGuias() throws Exception {
        if ((valor == null || valor.equals("")) && (isTransvip(persistencia)
                || isPreserve(persistencia) || isConfederal(persistencia)
                || isBrasifort(persistencia) || isTecno(persistencia))
                && !rotaSelecionada.getRota().equals(ROTA_TESOURARIA)) {
            BigDecimal valorTotal = service.getTotalGeralRtGuias(guia, serie);
            if (valorTotal != null) {
                valor = valorTotal.toString();
            }
        }
    }

    private void buscarValorTotalRtGuiasVol() throws Exception {
        if (valor == null || valor.equals("")) {
            BigDecimal valorTotal = service.getTotalGeralCxfGuiasVol(guia, serie);
            valor = valorTotal != null ? valorTotal.toString() : "";
        }
    }

    private void alterarOS() throws Exception {
        // Fase 1 - Busca OS na GTV pré-impressa
        OSAtual = service.buscaOsNaGtvPreImpressa(codFil, guia, serie);

        if (OSAtual == null || OSAtual.getOS() == null || OSAtual.getOS().equals("")) {
            // Fase 2 - Busca OS pelos parametros da parada
            List<OS_Vig> listaOS = service.buscaOsParametroParada(codFil, percursoAtual.getCodCli1(), codigoClienteDst);

            if (listaOS.isEmpty()) {
                throw new Exception("ImplementandoFeature");
                // TODO:
                // PrimeFaces.current().executeScript("PF('TFrmOSVigilancia').show();"); sem filtro
                // return;
            } else {
                OSAtual = listaOS.get(0);
                codigoOS = OSAtual.getOS();
                if (listaOS.size() > 1) {
                    throw new Exception("ImplementandoFeature");
                    // TODO:
                    // PrimeFaces.current().executeScript("PF('TFrmOSVigilancia').show();"); filtrado
                    // return;
                } else {
                    codigoClienteDst = OSAtual.getCliDst();
                    NRedClienteDst = OSAtual.getNRedDst();
                }
            }
        } else {
            codigoOS = OSAtual.getOS();
            codigoClienteDst = OSAtual.getCliDst();
            NRedClienteDst = OSAtual.getNRedDst();
        }

        alterarOSParte2();
    }

    private void alterarOSParte2() throws Exception {
        service.updateOSDeGTV(codFil, guia, serie, codigoOS);

        // Cliente possui destino final diferente de tesouraria
        // Analisar geração de pedido de entrega
        String CliDst = OSAtual.getCliDst();
        if (CliDst == null || !CliDst.substring(0, 3).equals("9997")) {
            throw new Exception("ImplementandoFeature");
            // TODO
            // PrimeFaces.current().executeScript("PF('TFrmCxForteEntDst').show();");
        }

        finalizarPreenchimento();
    }

    private void finalizarPreenchimento() throws Exception {
        if (mostrarResultado) {
            if (codigoClioenteOri == null || codigoClioenteOri.equals("")) {
                // carregarGuias();
            }
            if (hora1 == null || hora1.equals("")) {
                // LbHora1.Caption   := QyHora1.AsString; TODO
            }
            mostrarParada();
        } else {
            guia = null;
            serie = null;
            // TODO  DBGrid1.SetFocus;
        }

        transvip();
        obterSomaEQquantidade();
        tecban();
    }

    private void transvip() throws Exception {
        if (isTransvip(persistencia)) {
            CxFGuiasVol guiaVol = service.obtemPrimeiroTipoGuiasVol(codFil, guia, serie);
            if (guiaVol == null) {
                // InsereVolumes(ValR(EditGuia.Text),EditSerie.Text);
            } else {
                tipo = guiaVol.getTipo();
            }

            if (rotaSelecionada.getRota().equals(ROTA_TESOURARIA)) {
                // SbtnGravarClick(Self);
            }
        }
    }

    private void obterSomaEQquantidade() throws Exception {
        CxFGuiasVol guiaVol = service.obtemPrimeiroQuantidadesEValores(codFil, guia, serie);
        valorSomaVol = BigDecimal.ZERO;
        valorQtdeVol = BigDecimal.ZERO;
        if (guiaVol.getValor() != null && guiaVol.getValor().compareTo(BigDecimal.ZERO) == 1) {
            valorSomaVol = guiaVol.getValor();
            valorQtdeVol = new BigDecimal(guiaVol.getQtde());
            valor = valorSomaVol.toString();
        }
    }

    private void tecban() {
        if (isTecban(persistencia)) {
            tipo = "1";
        }
    }

    public void metodoGTV() {
        displayWarn("ImplementandoFeature");
    }

    public void gravarGuia() {
        String codCXF = caixaForteSelecionado.getCodCli();
        String clienteOrigem = entradaSelecionada.getCodCli1();
        String clienteDestinoCod = codigoClienteDst;
        String rota = rotaSelecionada.getRota();
        String data = DataAtual.getDataAtual("SQL");
        String horaAtual = DataAtual.getDataAtual("HORA");
        String sequenciaRota = rotaSelecionada.getSequencia().toString();
        String hora1Parada = entradaSelecionada.getHora1();

        try {
            String OS = guiasCxForteAtual != null ? guiasCxForteAtual.getOS() : service.obterOS(codFil, guia, serie).getOS(); // FIXME: isso está errado
            service.inserirGuiaSerie(codFil, codCXF, guia, serie, clienteOrigem, clienteDestinoCod, valor, tipo, rota, operadorRed, data, horaAtual, sequenciaRota, OS, hora1Parada);

            carregarEntradas();
            if (entradaSelecionada != null) {
                carregarGuias();
            }
            limparCadastro();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            displayFatal(e.getMessage());
        }
    }

    private void limparCadastro() {
        parada = null;
        codCliOriTmp = null;
        NRedOriTmp = null;
        codigoClioenteOri = null;
        NRedClienteOri = null;
        vHrO = null;
        hora1 = null;
        codigoClienteDst = null;
        NRedClienteDst = null;
        regiaoCliente = null;
    }

    private void limparCadastroTotal() {
        parada = null;
        codCliOriTmp = null;
        NRedOriTmp = null;
        codigoClioenteOri = null;
        NRedClienteOri = null;
        vHrO = null;
        hora1 = null;
        codigoClienteDst = null;
        NRedClienteDst = null;
        regiaoCliente = null;
        vHrD = null;
        codigoOS = null;
        serie = null;
        guia = null;
        valor = null;
        tipo = null;

        PrimeFaces.current().ajax().update("guiaCadastro:cadastrar");
    }

    public void deletarGuia() {
        try {
            validarDelecao();

            String guiaDel = guiaSelecionada.getGuia();
            String serieDel = guiaSelecionada.getSerie();
            CxFGuias cxfGuia = service.getSeqRotaSaiByGuiaSerie(guiaDel, serieDel);
            if (cxfGuia != null && !cxfGuia.getSeqRota().equals("0")) {
                alertarExclusaoNaoPermitida(cxfGuia);
                return;
            }

            service.deletarGuiaSerie(codFil, caixaForteSelecionado.getCodCli(), guiaDel, serieDel, operadorRed);
            carregarEntradas();
            carregarGuias();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            displayFatal(e.getMessage());
        }
    }

    private void validarDelecao() throws Exception {
        if (caixaForteSelecionado == null) {
            throw new Exception("SelecioneCaixaForte");
        }
        if (guiaSelecionada == null
                || guiaSelecionada.getGuia() == null
                || guiaSelecionada.getSerie() == null) {
            throw new Exception("SelecioneGuia");
        }
    }

    private void alertarExclusaoNaoPermitida(CxFGuias cxfGuia) {
        String s1 = Messages.getMessageS("GuiaSaiuEmCxForte") + "\n";
        String s2 = Messages.getMessageS("Rota") + ": " + cxfGuia.getRotaSai() + "\n";
        String s3 = Messages.getMessageS("Data") + ": " + cxfGuia.getDtSai() + "\n";
        String s4 = Messages.getMessageS("ExclusaoNaoPermitida");

        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, s1 + s2 + s3 + s4, null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    public void dialogVolumesGTV() {
        guiaEdicao = new CxFGuias(guiaSelecionada);
        PrimeFaces.current().executeScript("PF('dlgVolumesGTV').show();");
    }

    public void gravarVolumesGTV() {
        displayWarn("Implementando...");
    }

    public void imprimirEntradas() {
        displayWarn("Implementar...");
    }

    public void verGuiasMobile() {
        displayWarn("Implementar...");
    }

    // getters & setters:
    public List<CxFEntradasDTO> getEntradas() {
        return entradas;
    }

    public Rotas getRotaSelecionada() {
        return rotaSelecionada;
    }

    public void setRotaSelecionada(Rotas rotaSelecionada) {
        this.rotaSelecionada = rotaSelecionada;
    }

    public CxFEntradasDTO getEntradaSelecionada() {
        return entradaSelecionada;
    }

    public void setEntradaSelecionada(CxFEntradasDTO entradaSelecionada) {
        this.entradaSelecionada = entradaSelecionada;
    }

    public boolean isSomenteDestinoCxForte() {
        return somenteDestinoCxForte;
    }

    public void setSomenteDestinoCxForte(boolean somenteDestinoCxForte) {
        this.somenteDestinoCxForte = somenteDestinoCxForte;
    }

    public List<Rotas> getRotas() {
        return rotas;
    }

    public boolean isCapturaAuto() {
        return capturaAuto;
    }

    public void setCapturaAuto(boolean capturaAuto) {
        this.capturaAuto = capturaAuto;
    }

    public List<CxFGuias> getGuias() {
        return guias;
    }

    public String getGuia() {
        return guia;
    }

    public void setGuia(String guia) {
        this.guia = guia;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getValor() {
        return valor;
    }

    public List<CxForte> getCaixasFortes() {
        return caixasFortes;
    }

    public CxForte getCaixaForteSelecionado() {
        return caixaForteSelecionado;
    }

    public void setCaixaForteSelecionado(CxForte caixaForteSelecionado) {
        this.caixaForteSelecionado = caixaForteSelecionado;
    }

    public BigDecimal getValorSomaVol() {
        return valorSomaVol;
    }

    public Integer getQtdeVol() {
        return qtdeVol;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getROTA_TESOURARIA() {
        return ROTA_TESOURARIA;
    }

    public CxFGuias getGuiaSelecionada() {
        return guiaSelecionada;
    }

    public void setGuiaSelecionada(CxFGuias guiaSelecionada) {
        this.guiaSelecionada = guiaSelecionada;
    }

    public CxFGuias getGuiaEdicao() {
        return guiaEdicao;
    }

    public void setGuiaEdicao(CxFGuias guiaEdicao) {
        this.guiaEdicao = guiaEdicao;
    }

    public CxFGuiasGTVDTO getGuiasCxForteAtual() {
        return guiasCxForteAtual;
    }

    public List<CxFGuiasGTVDTO> getListaGuiasCxForte() {
        return listaGuiasCxForte;
    }

    public int getGuiasCxForteIndex() {
        return guiasCxForteIndex;
    }

    public Rt_Perc getListaHorasRtPercAtual() {
        return listaHorasRtPercAtual;
    }

    public int getListaHorasRtPercIndex() {
        return listaHorasRtPercIndex;
    }

    public List<Rt_Perc> getListaHorasRtPerc() {
        return listaHorasRtPerc;
    }

    public Rt_Perc getPercursoAtual() {
        return percursoAtual;
    }

    public String getCodigoClioenteOri() {
        return codigoClioenteOri;
    }

    public String getNRedClienteOri() {
        return NRedClienteOri;
    }

    public String getHora1() {
        return hora1;
    }

    public String getCodigoClienteDst() {
        return codigoClienteDst;
    }

    public String getNRedClienteDst() {
        return NRedClienteDst;
    }

    public Integer getParada() {
        return parada;
    }

    public BigDecimal getValorQtdeVol() {
        return valorQtdeVol;
    }

    public String getCodigoOS() {
        return codigoOS;
    }

    public String getRegiaoCliente() {
        return regiaoCliente;
    }
}

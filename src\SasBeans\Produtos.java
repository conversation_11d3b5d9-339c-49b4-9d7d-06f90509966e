/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Produtos {

    private BigDecimal Codigo;
    private String Descricao;
    private String RefFabrica;
    private String Aplicacao;
    private String CodBarras;
    private String Un;
    private BigDecimal QtdeItensUn;
    private BigDecimal Qtde;
    private BigDecimal QtdeMaxPed;
    private BigDecimal EstoqueMin;
    private BigDecimal EstoqueIdeal;
    private BigDecimal PrecoCusto;
    private BigDecimal PrecoVenda;
    private String Produtos;
    private int Grupo;
    private int Categoria;
    private int Classe;
    private String TipoCombust;
    private String Obs;
    private BigDecimal Margem;
    private int PrazoEntrega;
    private String CCusto;
    private String CodInterf;
    private String ICMSID;
    private String CFOP;
    private String CFOPEnt;
    private String Mensagem;
    private String Mensagem2;
    private String Situacao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String IndServico;

    public Produtos() {
        this.Qtde = BigDecimal.ONE;
    }

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    public void setCodigo(BigDecimal Codigo) {
        try {
            this.Codigo = Codigo;
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getRefFabrica() {
        return RefFabrica;
    }

    public void setRefFabrica(String RefFabrica) {
        this.RefFabrica = RefFabrica;
    }

    public String getAplicacao() {
        return Aplicacao;
    }

    public void setAplicacao(String Aplicacao) {
        this.Aplicacao = Aplicacao;
    }

    public String getCodBarras() {
        return CodBarras;
    }

    public void setCodBarras(String CodBarras) {
        this.CodBarras = CodBarras;
    }

    public String getUn() {
        return Un;
    }

    public void setUn(String Un) {
        this.Un = Un;
    }

    public BigDecimal getQtdeItensUn() {
        return QtdeItensUn;
    }

    public void setQtdeItensUn(String QtdeItensUn) {
        try {
            this.QtdeItensUn = new BigDecimal(QtdeItensUn);
        } catch (Exception e) {
            this.QtdeItensUn = new BigDecimal("0");
        }
    }

    public BigDecimal getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        try {
            this.Qtde = new BigDecimal(Qtde);
        } catch (Exception e) {
            this.Qtde = new BigDecimal("0");
        }
    }

    public void setQtde(BigDecimal Qtde) {
        try {
            this.Qtde = Qtde;
        } catch (Exception e) {
            this.Qtde = new BigDecimal("0");
        }
    }

    public BigDecimal getQtdeMaxPed() {
        return QtdeMaxPed;
    }

    public void setQtdeMaxPed(String QtdeMaxPed) {
        try {
            this.QtdeMaxPed = new BigDecimal(QtdeMaxPed);
        } catch (Exception e) {
            this.QtdeMaxPed = new BigDecimal("0");
        }
    }

    public BigDecimal getEstoqueMin() {
        return EstoqueMin;
    }

    public void setEstoqueMin(String EstoqueMin) {
        try {
            this.EstoqueMin = new BigDecimal(EstoqueMin);
        } catch (Exception e) {
            this.EstoqueMin = new BigDecimal("0");
        }
    }

    public BigDecimal getEstoqueIdeal() {
        return EstoqueIdeal;
    }

    public void setEstoqueIdeal(String EstoqueIdeal) {
        try {
            this.EstoqueIdeal = new BigDecimal(EstoqueIdeal);
        } catch (Exception e) {
            this.EstoqueIdeal = new BigDecimal("0");
        }
    }

    public BigDecimal getPrecoCusto() {
        return PrecoCusto;
    }

    public void setPrecoCusto(String PrecoCusto) {
        try {
            this.PrecoCusto = new BigDecimal(PrecoCusto);
        } catch (Exception e) {
            this.PrecoCusto = new BigDecimal("0");
        }
    }

    public void setPrecoCusto(BigDecimal PrecoCusto) {
        try {
            this.PrecoCusto = PrecoCusto;
        } catch (Exception e) {
            this.PrecoCusto = new BigDecimal("0");
        }
    }

    public BigDecimal getPrecoVenda() {
        return PrecoVenda;
    }

    public void setPrecoVenda(String PrecoVenda) {
        try {
            this.PrecoVenda = new BigDecimal(PrecoVenda);
        } catch (Exception e) {
            this.PrecoVenda = new BigDecimal("0");
        }
    }

    public void setPrecoVenda(BigDecimal PrecoVenda) {
        try {
            this.PrecoVenda = PrecoVenda;
        } catch (Exception e) {
            this.PrecoVenda = new BigDecimal("0");
        }
    }

    public String getProdutos() {
        return Produtos;
    }

    public void setProdutos(String Produtos) {
        this.Produtos = Produtos;
    }

    public int getGrupo() {
        return Grupo;
    }

    public void setGrupo(int Grupo) {
        this.Grupo = Grupo;
    }

    public int getCategoria() {
        return Categoria;
    }

    public void setCategoria(int Categoria) {
        this.Categoria = Categoria;
    }

    public int getClasse() {
        return Classe;
    }

    public void setClasse(int Classe) {
        this.Classe = Classe;
    }

    public String getTipoCombust() {
        return TipoCombust;
    }

    public void setTipoCombust(String TipoCombust) {
        this.TipoCombust = TipoCombust;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public BigDecimal getMargem() {
        return Margem;
    }

    public void setMargem(String Margem) {
        try {
            this.Margem = new BigDecimal(Margem);
        } catch (Exception e) {
            this.Margem = new BigDecimal("0");
        }
    }

    public void setMargem(BigDecimal Margem) {
        try {
            this.Margem = Margem;
        } catch (Exception e) {
            this.Margem = new BigDecimal("0");
        }
    }

    public int getPrazoEntrega() {
        return PrazoEntrega;
    }

    public void setPrazoEntrega(int PrazoEntrega) {
        this.PrazoEntrega = PrazoEntrega;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public String getCodInterf() {
        return CodInterf;
    }

    public void setCodInterf(String CodInterf) {
        this.CodInterf = CodInterf;
    }

    public String getICMSID() {
        return ICMSID;
    }

    public void setICMSID(String ICMSID) {
        this.ICMSID = ICMSID;
    }

    public String getCFOP() {
        return CFOP;
    }

    public void setCFOP(String CFOP) {
        this.CFOP = CFOP;
    }

    public String getCFOPEnt() {
        return CFOPEnt;
    }

    public void setCFOPEnt(String CFOPEnt) {
        this.CFOPEnt = CFOPEnt;
    }

    public String getMensagem() {
        return Mensagem;
    }

    public void setMensagem(String Mensagem) {
        this.Mensagem = Mensagem;
    }

    public String getMensagem2() {
        return Mensagem2;
    }

    public void setMensagem2(String Mensagem2) {
        this.Mensagem2 = Mensagem2;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getIndServico() {
        return IndServico;
    }

    public void setIndServico(String IndServico) {
        this.IndServico = IndServico;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 73 * hash + Objects.hashCode(this.Codigo);
        hash = 73 * hash + Objects.hashCode(this.Descricao);
        hash = 73 * hash + Objects.hashCode(this.RefFabrica);
        hash = 73 * hash + Objects.hashCode(this.Aplicacao);
        hash = 73 * hash + Objects.hashCode(this.CodBarras);
        hash = 73 * hash + Objects.hashCode(this.Un);
        hash = 73 * hash + Objects.hashCode(this.QtdeItensUn);
        hash = 73 * hash + Objects.hashCode(this.Qtde);
        hash = 73 * hash + Objects.hashCode(this.QtdeMaxPed);
        hash = 73 * hash + Objects.hashCode(this.EstoqueMin);
        hash = 73 * hash + Objects.hashCode(this.EstoqueIdeal);
        hash = 73 * hash + Objects.hashCode(this.PrecoCusto);
        hash = 73 * hash + Objects.hashCode(this.PrecoVenda);
        hash = 73 * hash + Objects.hashCode(this.Produtos);
        hash = 73 * hash + this.Grupo;
        hash = 73 * hash + this.Categoria;
        hash = 73 * hash + this.Classe;
        hash = 73 * hash + Objects.hashCode(this.TipoCombust);
        hash = 73 * hash + Objects.hashCode(this.Obs);
        hash = 73 * hash + Objects.hashCode(this.Margem);
        hash = 73 * hash + this.PrazoEntrega;
        hash = 73 * hash + Objects.hashCode(this.CCusto);
        hash = 73 * hash + Objects.hashCode(this.CodInterf);
        hash = 73 * hash + Objects.hashCode(this.ICMSID);
        hash = 73 * hash + Objects.hashCode(this.CFOP);
        hash = 73 * hash + Objects.hashCode(this.CFOPEnt);
        hash = 73 * hash + Objects.hashCode(this.Mensagem);
        hash = 73 * hash + Objects.hashCode(this.Mensagem2);
        hash = 73 * hash + Objects.hashCode(this.Situacao);
        hash = 73 * hash + Objects.hashCode(this.Operador);
        hash = 73 * hash + Objects.hashCode(this.Dt_Alter);
        hash = 73 * hash + Objects.hashCode(this.Hr_Alter);
        hash = 73 * hash + Objects.hashCode(this.IndServico);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Produtos other = (Produtos) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }

}

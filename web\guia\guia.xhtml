<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:f="http://xmlns.jcp.org/jsf/core">
    <h:head>
        <link type="text/css" href="../assets/css/guias.css" rel="stylesheet" />
    </h:head>
    <ui:composition>
        <div >
            <table id="cabecalho"  class="cabecalhoNota">
                <tbody>
                    <tr style="text-align: center">
                        <td rowspan="4">
                            <img src="#{login.getLogo(login.pp.getEmpresa())}" height="47px" width="59px"/>
                        </td>
                        <td colspan="3">
                            <h:outputText value="#{guias.guia.razaoSocial}" class="negrito"/>
                        </td>
                    </tr>
                    <tr style="text-align: center">
                        <td colspan="3">
                            <h:outputText value="#{guias.guia.enderecoFilial}"/>,<h:outputText value="#{guias.guia.bairroFilial}"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" style="text-align: center">
                            <h:outputText value="#{guias.guia.cidadeFilial}"/>/<h:outputText value="#{guias.guia.ufFilial}"/>
                            &nbsp;-&nbsp;
                            <h:outputText value="#{guias.guia.cepFilial}" converter="conversorCEP"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" style="text-align: center">
                            <h:outputText value="#{localemsgs.CGC}: " class="campoCabecalho"/>
                            &nbsp;
                            <h:outputText value="#{guias.guia.cnpjFilial}" converter="conversorCNPJ"/>
                            <h:outputText value="#{localemsgs.Telefone}: " class="campoCabecalho"/>
                            &nbsp;
                            <h:outputText value="#{guias.guia.foneFilial}" converter="conversorFone"/>
                        </td>
                    </tr>
                </tbody>   
            </table>
            <table id="filialOS" class="cabecalhoNota">
                <tbody>
                    <tr style="text-align: center">
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.GETV}" class="negrito"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h:outputText value="#{localemsgs.Filial}: " class="campoCabecalho"/>
                            &nbsp;
                            <h:outputText value="#{guias.guia.codFil}" converter="conversorCodFil"/>
                        </td>
                        <td rowspan="2" style="text-align: right">
                            <p:graphicImage value="#{barCodeMB.image}">
                                <f:param name="barcode" value="#{guias.guia.codBarras}" />
                            </p:graphicImage>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h:outputText value="#{localemsgs.OS}: " class="campoCabecalho"/>
                            &nbsp;
                            <h:outputText value="#{guias.guia.OS}" converter="conversor0"/>
                        </td>
                    </tr>
                </tbody>   
            </table>
            <table id="cliente" class="datagrid">
                <tbody>
                    <tr class="caixa">
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.ClienteP}" class="negrito"/> 
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <h:outputText value="#{guias.guia.nRedFat}" class="cliente"/>
                        </td>
                    </tr>            
                    <tr>                
                        <td  colspan="2">
                            <h:outputText value="#{localemsgs.Endereco}: " style="font-style: italic"/>
                            &nbsp;
                            <h:outputText value="#{guias.guia.endFat}" />
                        </td>            
                    </tr>  
                    <tr>                
                        <td  colspan="2">
                            <h:outputText value="#{guias.guia.bairroFat}, #{guias.guia.cidadeFat}/#{guias.guia.estadoFat}"/>
                        </td>            
                    </tr> 
                    <tr>
                        <td width="50%">
                            <h:outputText value="#{localemsgs.CGC}: " class="campo"/>
                            &nbsp;
                            <h:outputText value="#{guias.guia.cgcFat}" converter="conversorCNPJ"/>
                        </td>  
                        <td width="50%">
                            <h:outputText value="#{localemsgs.InscEstadual}: " class="campo"/>
                            &nbsp;  
                            <h:outputText value="#{guias.guia.ieFat}" />
                        </td>
                    </tr>   
                </tbody>   
            </table>
            <table id="origem" class="datagrid">
                <tbody>
                    <tr class="caixa">
                        <td>
                            <h:outputText value="#{localemsgs.DadosOrigem}" class="negrito"/>  
                            <h:outputText value="#{guias.guia.agencia}" />
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center">
                            <h:outputText value="#{localemsgs.Remetente}: " class="campo"/> 
                            <h:outputText value="#{guias.guia.nRedOri}" class="cliente"/>
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center">
                            <h:outputText value="#{localemsgs.Endereco}: " class="campo"/> 
                            <h:outputText value="#{guias.guia.endOri}"/>
                        </td>
                    </tr>
                </tbody>   
            </table>
            <table id="enderecoOrigem" class="datagrid">
                <tbody>
                    <tr>
                        <td style="text-align: center">
                            <h:outputText value="#{guias.guia.bairroOri}, #{guias.guia.cidadeOri}/#{guias.guia.estadoOri}"/>
                        </td> 
                    </tr>
                </tbody>   
            </table>
            <table id="dadosOrigem" class="datagrid">
                <tbody>
                    <tr class="caixa">
                        <td>
                            <h:outputText value="#{localemsgs.Data}: " class="campo"/>
                            &nbsp;
                            <h:outputText value="#{guias.guia.coletaOri}" converter="conversorData"/>
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Veiculo}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{guias.guia.veiculoOri}" converter="conversor0"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Rota}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{guias.guia.rotaOri}" class="negrito" converter="conversor0"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Chegada}: " class="campo"/>
                            <h:outputText value="#{guias.guia.horaChegada}" converter="conversorHora"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Said}: " class="campo"/> 
                            <h:outputText value="#{guias.guia.horaSaida}" converter="conversorHora"/> 
                        </td>
                    </tr>
                </tbody>   
            </table>
            <table id="destino" class="datagrid">
                <tbody>
                    <tr class="caixaAcima">  
                        <td>
                            <h:outputText value="#{localemsgs.DadosDestino}" class="negrito"/> 
                        </td>       
                    </tr>   
                    <tr>    
                        <td style="text-align: center">
                            <h:outputText value="#{localemsgs.Destinatario}: " class="campo"/> 
                            <h:outputText value="#{guias.guia.nRedDst}" class="cliente"/> 
                        </td>   
                    </tr>   
                    <tr>    
                        <td style="text-align: center">
                            <h:outputText value="#{localemsgs.Endereco}: " class="campo"/> 
                            <h:outputText value="#{guias.guia.endDst}"/>
                        </td>    
                    </tr>
                </tbody>   
            </table>
            <table id="enderecoDestino" class="datagrid">
                <tbody>
                    <tr>      
                        <td style="text-align: center">
                            <h:outputText value="#{guias.guia.bairroDst}, #{guias.guia.cidadeDst}/#{guias.guia.estadoDst}"/>
                        </td> 
                    </tr>
                </tbody>   
            </table>
            <table id="dadosDestino" class="datagrid">
                <tbody>
                    <tr class="caixa"> 
                        <td>
                            <h:outputText value="#{localemsgs.Data}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{guias.guia.coletaDst}" converter="conversorData"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Veiculo}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{guias.guia.veiculoDst}" converter="conversor0"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Chegada}: " class="campo"/> 
                            <h:outputText value="#{guias.guia.hora1}" converter="conversorHora"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Said}: " class="campo"/> 
                            <h:outputText value="#{guias.guia.hora2}" converter="conversorHora"/> 
                        </td>        
                    </tr>
                </tbody>   
            </table>
            <table id="detalhes" class="datagrid">
                <tbody>
                    <tr class="caixaAcima">   
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.DiscriminacaoValorIdentificacao}" class="negrito"/> 
                        </td>       
                    </tr>        
                    <tr>         
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.ValorDeclarado}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{guias.guia.valor}" class="negrito" converter="conversormoeda"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="preencheLinha" colspan="2">
                            <h:outputText value="#{guias.guia.valorExtenso}"/>
                        </td>  
                    </tr>
                    <tr class="caixa">
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.IdentificacaoMalote}" class="negrito"/> 
                        </td>  
                    </tr>      
                    <tr>       
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.Lacres}:" class="campo"/> 
                        </td>     
                    </tr>    
                    <c:forEach var="lacre" items="#{guias.guia.lacres}">
                        <tr>       
                            <td colspan="2">
                                <h:outputText value="#{lacre.lacre}" class="negrito"/>
                            </td>
                        </tr>    
                    </c:forEach>

                    <tr class="caixa">
                        <td style="text-align: justify; padding-right: 4px !important;" colspan="2">
                            <h:outputText value="&emsp; #{localemsgs.TermoAssGuia}" class="campo"/>
                        </td>     
                    </tr>     
                    <tr>     
                        <td style="border-right: 1px solid black ;vertical-align: baseline;" width="50%">
                            <h:outputText value="#{localemsgs.AssRemetente}: " class="campo"/> 
                        </td>
                        <td style="vertical-align: baseline;" width="50%">
                            <h:outputText value="#{localemsgs.AssDestinatario}:" class="campo"/> 
                        </td>       
                    </tr>
                    <tr class="caixaAcima">     
                        <td style="border-right: 1px solid black;vertical-align: baseline;" width="50%">
                            <h:outputText value="#{guias.guia.assRemetente}"/>
                        </td>      
                        <td style="border-right: 1px solid black;vertical-align: baseline;" width="50%">
                            <h:outputText value="#{guias.guia.assDestinatario}"/>
                        </td>      
                    </tr>
                    <tr>     
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.Autenticacao}" class="campo"/>
                        </td>              
                    </tr>
                    <tr style="text-align: center">
                        <td colspan="2">
                            <h:outputText value="#{guias.guia.autenticacao}" class="negrito" escape="false"/>
                            &nbsp;
                        </td>              
                    </tr>
                    <tr class="caixa">     
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.Obs}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{guias.guia.observ}"/> 
                        </td>                
                    </tr>                   
                </tbody>
            </table>
        </div>
    </ui:composition>
</html>
/*
 */
package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Arquivo.ArquivoLogs;
import Controller.Funcion.FuncionSatMobWeb;
import Controller.Login.LoginSatMobWeb;
import Controller.Pessoas.PessoasSatMobWeb;
import Controller.PstServ.PstServSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Cargos;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.Municipios;
import SasBeans.Pe_Doctos;
import SasBeans.Pessoa;
import SasBeans.PstServ;
import SasBeans.RHEscala;
import SasBeans.RHHorario;
import SasBeans.SasPWFill;
import SasBeans.TbVal;
import SasBeansCompostas.FuncionPstServ;
import SasDaos.FuncionDao;
import SasDaos.PessoaDao;
import SasDaos.RHEscalaDao;
import SasDaos.TbValDao;
import br.com.sasw.lazydatamodels.FuncionLazyList;
import br.com.sasw.pacotesuteis.utilidades.BuscarEndereco;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import br.com.sasw.pacotesuteis.utilidades.ValidadorCPF_CNPJ;
import br.com.sasw.pacotesuteis.utilidades.winzap.Winzap;
import br.com.sasw.utils.LocaleController;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.AjaxBehaviorEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.inputtext.InputText;
import org.primefaces.component.selectonemenu.SelectOneMenu;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.json.JSONObject;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Named(value = "funcionario")
@ViewScoped
public class FuncionMB implements Serializable {

    private String banco, operador, dataTela, nomeFilial, posto, filial, caminho, log, cargo, horario, nome, nome_guer, codFil;
    private BigDecimal codPessoa, matr, codPessoaAproveitamento;
    private Persistencia persistenciaLocal, persistenciaCentral;
    private List<FuncionPstServ> lista;
    private List<Funcion> listafun, configuracaoEWFuncionList;
    private List<Cargos> listaCargos;
    private List<PstServ> listaPostos;
    private FuncionPstServ selecionado;
    private Funcion novo, configuracaoEWFuncion;
    private final FuncionSatMobWeb funcionsatmobweb;
    private final LoginSatMobWeb loginsatmobweb;
    private PessoasMB pessoaMB;
    private Pessoa pessoa;
    private List<RHHorario> rhHorarios;
    private List<RHEscala> rhEscalas;
    private RHHorario rhHorario;
    private RHEscala rhEscala;
    private int flag, total;
    private ArquivoLog logerro;
    private final PessoasSatMobWeb pessoasatmobweb;
    private Boolean atualizado, mostrarFiliais, somenteAtivos, cpf;
    private SasPWFill filialFuncion;
    private LazyDataModel<FuncionPstServ> funcions = null;
    private List<Municipios> cidades;
    private Map filters;
    private boolean eFilial, eMatr, eNome, eEmail, eInterfExt, eCodPessoaWeb, eNomeGuer,
            eRG, eOrgEmis, eCPF, eDtNasc, eSituacao, eDtSituacao, eDtAdmis, eCodPosto, ePosto,
            eOperador, eDtAlter, eHrAlter, matriculaAutomatica,
            configuracaoEWLiberarCliente, configuracaoEWLiberarPosto, configuracaoEWLiberarFuncion;
    private Filiais filiais, configuracaoEWFilial;
    private final RotasSatWeb rotassatweb;
    private final RHEscalaDao rhEscalaDao;
    private final String CODFIL = "funcion.codfil = ?",
            SITUACAO = "funcion.situacao LIKE ?",
            NOME_GUER = "funcion.nome_guer LIKE ?",
            NOME = "funcion.nome LIKE ?",
            LOCAL = "pstserv.local LIKE ?",
            MATR = "funcion.matr = ?",
            PIS = "funcion.PIS = ?";
    private String chavePesquisa = "MATR", valorPesquisa, tipoArq;
    private Clientes configuracaoEWCliente;
    private PstServ configuracaoEWPstServ;
    private List<Clientes> configuracaoEWClienteList;
    private List<PstServ> configuracaoEWPstServList;
    private UploadedFile uploadedFile;
    private TbValDao tbValDao;
    private TbVal tbVal;
    private List<TbVal> listTbVal;

    public FuncionMB() throws Exception {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        lista = new ArrayList<>();
        funcionsatmobweb = new FuncionSatMobWeb();
        novo = new Funcion();
        novo.setSituacao("A");
        selecionado = new FuncionPstServ();
        selecionado.setFuncion(new Funcion());
        selecionado.getFuncion().setNome("");
        selecionado.getFuncion().setNome_Guer("");
        selecionado.getFuncion().setCodPessoaWeb(codPessoa);
        selecionado.setPstserv(new PstServ());
        selecionado.getPstserv().setLocal(null);
        pessoasatmobweb = new PessoasSatMobWeb();
        pessoa = new Pessoa();
        posto = new String();
        cargo = new String();
        horario = new String();
        somenteAtivos = true;
        mostrarFiliais = false;
        filial = new String();
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
        eFilial = true;
        eMatr = true;
        eNome = true;
        eEmail = true;
        eCPF = true;
        eDtNasc = true;
        ePosto = true;
        matriculaAutomatica = true;
        dataTela = DataAtual.getDataAtual("SQL");
        rotassatweb = new RotasSatWeb();
        rhEscalaDao = new RHEscalaDao();
        rhEscala = new RHEscala();
        tbValDao = new TbValDao();
        tbVal = new TbVal();
        listTbVal = new ArrayList<>();
    }

    public FuncionMB(Persistencia persistencia) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    public String padLeftZeros(String inputString, int length) {
        if (inputString.length() >= length) {
            return inputString;
        }
        StringBuilder sb = new StringBuilder();
        while (sb.length() < length - inputString.length()) {
            sb.append('0');
        }
        sb.append(inputString);

        return sb.toString();
    }

    public void HandleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                if (this.tipoArq.equals("")) {
                    this.tipoArq = "FICHA_FUNCIONAL";
                }

                this.uploadedFile = fileUploadEvent.getFile();
                FuncionDao funcionDao = new FuncionDao();

                int Tamanho = (this.uploadedFile.getFileName().toString().length() - 4);

                String CodPessoaTratado = this.codPessoa.toPlainString().replace(".0", ""),
                        NomeArquivo = this.uploadedFile.getFileName().toString(),
                        ExtensaoArquivo = NomeArquivo.replace(NomeArquivo.substring(0, Tamanho), ""),
                        CodigoArquivo = funcionDao.retornaUltimoCodigoPeDoctos(CodPessoaTratado, this.persistenciaLocal).replace(".0", ""),
                        NomeArquivoUpload = "PES" + padLeftZeros(CodPessoaTratado, 8) + "-" + padLeftZeros(CodigoArquivo, 3),
                        NomeArquivoInsert = this.tipoArq + "-" + ExtensaoArquivo;

                new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistenciaLocal.getEmpresa() + "\\Pe_Doctos\\" + CodPessoaTratado).mkdirs();

                if (ExtensaoArquivo.indexOf(".") == -1) {
                    ExtensaoArquivo = "." + ExtensaoArquivo;
                }

                String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistenciaLocal.getEmpresa() + "\\Pe_Doctos\\" + CodPessoaTratado + "\\" + NomeArquivoUpload + ExtensaoArquivo;

                byte[] conteudo = fileUploadEvent.getFile().getContents();
                FileOutputStream fos = new FileOutputStream(arquivo);
                fos.write(conteudo);
                fos.close();

                // Insere em BD
                Pe_Doctos peDoctos = new Pe_Doctos();
                peDoctos.setCodigo(this.codPessoa.toPlainString().replace(".0", ""));
                peDoctos.setDescricao(FuncoesString.RecortaString(NomeArquivoInsert, 0, 50));
                peDoctos.setDt_alter(DataAtual.getDataAtual("SQL"));
                peDoctos.setHr_Alter(DataAtual.getDataAtual("HORA"));
                peDoctos.setOperador(FuncoesString.RecortaString(this.selecionado.getFuncion().getNome_Guer(), 0, 10));
                peDoctos.setOrdem(CodigoArquivo);

                funcionDao.inserePeDoctos(peDoctos, this.persistenciaLocal);

                // Atualiza em Página
                carregarArrayPeDoctos();
                PrimeFaces.current().executeScript("CarregarArquivos(true);");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarArrayPeDoctos() throws Exception {
        this.pessoaMB.getNovaPessoa().setCodigo(this.codPessoa);
        String array = "";
        FuncionDao funcionDao = new FuncionDao();
        if (this.matr == null){
            this.matr = this.novo.getMatr();
        }

        for (Pe_Doctos peDocto : funcionDao.listarDocumentos(this.matr.toPlainString().replace(".0", ""), this.persistenciaLocal)) {
            if (!array.equals("")) {
                array += "|**|";
            }

            array += padLeftZeros(peDocto.getCodigo().replace(".0", ""), 8) + "|__|" + padLeftZeros(peDocto.getOrdem().replace(".0", ""), 3) + "|__|" + peDocto.getDescricao() + "|__|" + peDocto.getMinutos();
        }

        PrimeFaces.current().executeScript("RecarregarArray('" + array + "');");
    }

    public void carregarDadosEdicao(BigDecimal matricula) throws Exception {
        String refCodFil = this.codFil;
        String refMatricula = matricula.toPlainString().replace(".0", "");

        FuncionDao funcionDao = new FuncionDao();

        this.selecionado = funcionDao.FuncionarioEdicao(refCodFil, refMatricula, this.persistenciaLocal);
        this.pessoaMB.PreCadastro2();
        this.novo = this.selecionado.getFuncion();
        try {
            this.filialFuncion = this.loginsatmobweb.BuscaFilial(this.novo.getCodFil().toString(), this.codPessoa, this.persistenciaLocal);
            carregarPostosFilial();
            this.posto = BuscarPstServ(this.novo.getSecao()).get(0);
            this.cargo = this.novo.getCargo();
            this.rhHorarios = this.funcionsatmobweb.listaHorarios(this.novo.getCodFil().toPlainString(), this.persistenciaLocal);
            RHHorario rh = new RHHorario();
            rh.setDescricao("");
            rh.setCodigo(BigDecimal.ZERO);
            this.rhHorarios.add(0, rh);
            this.rhHorario = this.funcionsatmobweb.buscarHorario(this.novo.getCodFil(), this.novo.getHorario(), this.persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }

        this.pessoaMB.getNovaPessoa().setCPF(Mascaras.removeMascara(this.novo.getCPF()));
        this.pessoaMB.BuscarPessoaLocal();
        this.pessoaMB.getNovaPessoa().setCEP(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getCEP()));
        this.novo.setCPF(Mascaras.CPF(this.novo.getCPF()));
        this.pessoaMB.getNovaPessoa().setCEP(Mascaras.CEP(this.pessoaMB.getNovaPessoa().getCEP()));
        this.pessoaMB.getNovaPessoa().setMatr(this.novo.getMatr());
        this.pessoaMB.getNovaPessoa().setRGOrgEmis(this.novo.getOrgEmis());
        this.pessoaMB.getNovaPessoa().setFone1(this.novo.getFone1());
        this.pessoaMB.getNovaPessoa().setFone2(this.novo.getFone2());
        this.pessoaMB.getNovaPessoa().setObs(this.novo.getObs());
        if (null != this.novo.getAltura()) {
            this.pessoaMB.getNovaPessoa().setAltura(this.novo.getAltura().toPlainString().replace(".0", ""));
        }
        if (null != this.novo.getPeso()) {
            this.pessoaMB.getNovaPessoa().setPeso(this.novo.getPeso().toPlainString().replace(".0", ""));
        }

        this.flag = 2;
        this.cpf = false;
        PrimeFaces.current().executeScript("MascarasJS();");
    }

    public void Persistencias(Persistencia pstLocal, Persistencia pstCentral) {
        try {
            persistenciaCentral = pstCentral;
            if (null == persistenciaCentral) {
                throw new Exception("ImpossivelConectarSatellite");
            }
            persistenciaLocal = pstLocal;
            if (null == persistenciaLocal) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
            }
            pessoaMB = new PessoasMB(persistenciaLocal, persistenciaCentral);
            filters = new HashMap();

            filters.put(CODFIL, codFil);
            filters.put(SITUACAO, "A");
            filters.put(NOME_GUER, "");
            filters.put(NOME, "");
            filters.put(LOCAL, "");
            filters.put(MATR, "");
            filters.put(PIS, "");

            total = funcionsatmobweb.Contagem(filters, codPessoa, persistenciaLocal);
            filiais = rotassatweb.buscaInfoFilial(codFil, persistenciaLocal);
            listaCargos = this.funcionsatmobweb.buscaCargos("", this.persistenciaLocal);
            rhEscalas = rhEscalaDao.listarEscalas(this.codFil, this.persistenciaLocal);
            rhHorarios = this.funcionsatmobweb.listaHorarios(this.codFil, this.persistenciaLocal);
            listTbVal = tbValDao.getTbVal052(this.persistenciaLocal);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void novoFuncionarioSemMatr() {
        this.matriculaAutomatica = false;
        NovoFuncionario();
    }

    public void novoFuncionarioMatr() {
        this.matriculaAutomatica = true;
        NovoFuncionario();
    }

    public void excluirDocumento() {
        try {
            // Capturar códigos para exclusão
            String codPessoaRef = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("codPessoa"),
                    ordemRef = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("ordem");

            // Excluir arquivo
            FuncionDao funcionDao = new FuncionDao();
            funcionDao.excluiPeDoctos(codPessoaRef, ordemRef, this.persistenciaLocal);

            // Recarregar Lista de Arquivos
            this.codPessoa = BigDecimal.valueOf(Double.parseDouble(codPessoaRef));
            carregarArrayPeDoctos();
            PrimeFaces.current().executeScript("$('.fundoPasta.select').click(); $.MsgBoxAzulOk('" + Messages.getMessageS("Aviso") + "','" + Messages.getMessageS("ExcluidoSucesso") + "')");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void NovoFuncionario() {
        this.cpf = true;
        this.posto = new String();
        this.cargo = new String();
        this.pessoaMB.PreCadastro();
        this.atualizado = true;
        this.novo = new Funcion();
        this.novo.setSituacao("A");
        this.novo.setHorario(0);
        this.rhHorario = new RHHorario();
        this.flag = 1;
        this.rhEscala = new RHEscala();
        try {
            RHHorario rh = new RHHorario();
            rh.setDescricao("");
            rh.setCodigo(BigDecimal.ZERO);
            this.rhHorarios.add(0, rh);
            this.filialFuncion = this.loginsatmobweb.BuscaFilial(this.codFil, this.codPessoa, this.persistenciaLocal);
            this.novo.setCodFil(this.filialFuncion.getCodfilAc());
            this.novo.setFuncao("O");
            carregarPostosFilial();
            buscarMatriculaAutomatica();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public boolean getIDSSN() {
        Locale locale = LocaleController.getsCurrentLocale();
        if (locale.getLanguage().toUpperCase().equals("PT")) {
            return true;
        } else {
            return !this.matriculaAutomatica;
        }
    }

    /**
     * Valida o nome de guerra em funcion e saspw
     */
    public void validarNomeGuer() {
        try {
            if (!this.novo.getNome_Guer().equals("")) {
                InputText input1 = (InputText) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:nome_guer");
                if (this.funcionsatmobweb.existeSASPWNome(this.novo.getNome_Guer(), this.persistenciaLocal)) {
                    input1.setValid(false);
                    throw new Exception(Messages.getMessageS("NomeExisteSaspw"));
                } else if (this.funcionsatmobweb.existeFuncionNomeGuer(this.novo.getNome_Guer(), this.persistenciaLocal)) {
                    input1.setValid(false);
                    throw new Exception(Messages.getMessageS("NomeExisteFuncion"));
                } else {
                    input1.setValid(true);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void BuscarPessoa() {
        try {
            this.cpf = true;
            String cpfFuncion = Mascaras.removeMascara(this.novo.getCPF());
            if (!cpfFuncion.equals("")) {
                Locale locale = LocaleController.getsCurrentLocale();
                if (locale.getLanguage().toUpperCase().equals("PT")) {
                    if (!ValidadorCPF_CNPJ.ValidarCPF(cpfFuncion)) {
                        throw new Exception(Messages.getMessageS("CPFInvalido"));
                    }
                }
                this.pessoaMB.getNovaPessoa().setCPF(cpfFuncion);

                if (!this.funcionsatmobweb.BuscaFuncionCPF(cpfFuncion, this.persistenciaLocal).isEmpty()) {
                    throw new Exception(Messages.getMessageS("FuncionarioCadastrado"));
                }

                this.pessoaMB.BuscarPessoaLocal();
                this.pessoaMB.getNovaPessoa().setSituacao("F");

                /*if (null == this.pessoaMB.getNovaPessoa().getMatr()) {
                    if (this.matriculaAutomatica) {
                        this.pessoaMB.getNovaPessoa().setMatr(this.funcionsatmobweb.sugereMatricula(this.persistenciaLocal));
                    } else {
                        this.pessoaMB.getNovaPessoa().setMatr(BigDecimal.ZERO);
                    }
                }*/
                if (this.atualizado) {
                    this.novo.setCPF(Mascaras.CPF(this.novo.getCPF()));
                    UpdateFormulario();
                }
                this.atualizado = false;
                this.cpf = false;
                buscarMatriculaAutomatica();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void buscarMatriculaAutomatica() throws Exception {
        if (this.matriculaAutomatica) {
            this.pessoaMB.getNovaPessoa().setMatr(this.funcionsatmobweb.sugereMatricula(this.persistenciaLocal));
        } else {
            this.pessoaMB.getNovaPessoa().setMatr(BigDecimal.ZERO);
        }
    }

    public void limparDataSituacao() {
        if (!this.novo.getSituacao().equals("D")) {
            this.novo.setDt_Situac("");
        }
    }

    public void UpdateFormulario() {
        PrimeFaces.current().ajax().update("formCadastrar:cpf");
        PrimeFaces.current().ajax().update("formCadastrar:nome_guer");
        PrimeFaces.current().ajax().update("formCadastrar:subFil");
        PrimeFaces.current().ajax().update("formCadastrar:situacao2");
        PrimeFaces.current().ajax().update("formCadastrar:dt_nasc");
        PrimeFaces.current().ajax().update("formCadastrar:dt_admis");
        PrimeFaces.current().ajax().update("formCadastrar:interfext");
        PrimeFaces.current().ajax().update("formCadastrar:pstserv");
        PrimeFaces.current().ajax().update("formCadastrar:cargo");
        PrimeFaces.current().ajax().update("formCadastrar:horario");
    }

    public void carregarPostosFilial() throws Exception {
        PstServSatMobWeb pstservsatmobweb = new PstServSatMobWeb();
        PstServ temp = new PstServ();
        temp.setCodFil(this.novo.getCodFil());
        temp.setSecao("");
        temp.setLocal("");
        temp.setDescContrato("");
        this.listaPostos = pstservsatmobweb.BuscaPosto(temp, this.persistenciaLocal);
    }

    public void preConfiguracaoFuncionEW() {
        this.configuracaoEWFuncion = new Funcion();
        this.configuracaoEWFuncionList = new ArrayList<>();
        this.configuracaoEWFuncionList.add(this.configuracaoEWFuncion);
        this.configuracaoEWFilial = new Filiais();
        this.configuracaoEWCliente = new Clientes();
        this.configuracaoEWClienteList = new ArrayList<>();
        this.configuracaoEWClienteList.add(this.configuracaoEWCliente);
        this.configuracaoEWPstServ = new PstServ();
        this.configuracaoEWPstServ.setDescricao("");
        this.configuracaoEWPstServList = new ArrayList<>();
        this.configuracaoEWPstServList.add(this.configuracaoEWPstServ);
        this.configuracaoEWLiberarFuncion = false;
        this.configuracaoEWLiberarCliente = true;
        this.configuracaoEWLiberarPosto = true;
    }

    public List<Funcion> buscarFuncion(String query) {
        try {
            this.configuracaoEWFuncionList = this.funcionsatmobweb.buscarFuncion(query, this.persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.configuracaoEWFuncionList;
    }

    public void selectionarFuncion(SelectEvent event) {
        try {
            this.configuracaoEWFuncion = (Funcion) event.getObject();
            this.configuracaoEWFilial = this.rotassatweb.buscaInfoFilial(this.configuracaoEWFuncion.getCodFil().toBigInteger().toString(), this.persistenciaLocal);
            this.configuracaoEWLiberarFuncion = true;
            this.configuracaoEWLiberarCliente = false;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Clientes> buscarCliente(String query) {
        try {
            this.configuracaoEWClienteList = this.funcionsatmobweb.buscarCliente(query, this.configuracaoEWFuncion.getCodFil().toString(), this.persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.configuracaoEWClienteList;
    }

    public void selectionarCliente(SelectEvent event) {
        try {
            this.configuracaoEWCliente = (Clientes) event.getObject();
            this.configuracaoEWPstServList = new ArrayList<>();
            this.configuracaoEWPstServList.add(this.configuracaoEWPstServ);
            this.configuracaoEWPstServList.addAll(this.funcionsatmobweb.buscarPstServ(
                    this.configuracaoEWCliente.getCodigo(),
                    this.configuracaoEWCliente.getCodFil().toString(),
                    this.persistenciaLocal));
            if (this.configuracaoEWPstServList.size() == 1 || this.configuracaoEWPstServList.isEmpty()) {
                throw new Exception("ClienteSemPosto");
            } else {
                this.configuracaoEWLiberarCliente = true;
                this.configuracaoEWLiberarPosto = false;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selectionarPstServ(AjaxBehaviorEvent event) {
        try {
//            this.configuracaoEWPstServ = (PstServ) event.getObject();
            this.configuracaoEWLiberarPosto = true;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void alertaWhatsApp() {
        try {
            PrimeFaces.current().executeScript("AlertaEnvioWhatsapp('Confirmar envio da mensagem para " + this.configuracaoEWFuncion.getNome() + " no número "
                    + Mascaras.Fone(this.configuracaoEWFuncion.getFone1()) + ", filial " + this.configuracaoEWFilial.getRazaoSocial() + ", "
                    + "cliente " + this.configuracaoEWCliente.getNRed() + " e posto de serviço " + this.configuracaoEWPstServ.getLocal() + "?')");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Endereco() {
        try {
            String enderecoCompleto = new BuscarEndereco().BuscarPeloCEP(this.novo.getCEP());
            JSONObject obj = new JSONObject(enderecoCompleto);
            if (Integer.parseInt(obj.get("resultado").toString()) == 1) {
                this.novo.setBairro(FuncoesString.RecortaString(obj.get("bairro").toString(), 0, 20));
                this.novo.setEndereco(obj.get("tipo_logradouro").toString() + " " + obj.get("logradouro").toString());
                if (obj.get("uf").toString().equals("DF") || obj.get("uf").toString().equals("df")) {
                    this.novo.setCidade("BRASILIA");
                    this.novo.setUF("DF");
                } else {
                    this.cidades = pessoasatmobweb.ListaMunicipios(obj.get("uf").toString(), obj.get("cidade").toString(), this.persistenciaLocal);
                    this.novo.setCidade(this.cidades.get(0).getNome());
                    this.novo.setUF(this.cidades.get(0).getUF().substring(0, 2));
                }
                this.novo.setCEP(Mascaras.CEP(this.novo.getCEP()));
                //PrimeFaces.current().executeScript("PF('dlgOk').show()");
                PrimeFaces.current().executeScript("$.MsgBoxAzulOk('" + Messages.getMessageS("Aviso") + "','" + Messages.getMessageS("CompletarEndereco") + "')");
            } else {
                this.novo.setCEP(Mascaras.CEP(this.novo.getCEP()));
                throw new Exception(Messages.getMessageS("EnderecoNaoEncontrado"));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            try {
                this.logerro.Grava(log, caminho);
            } catch (Exception ex) {
            }
        }
    }

    public void enviarConfiguracaoFuncionEW() {
        try {
            JSONObject configJO = new JSONObject();
            configJO.put("param", this.persistenciaLocal.getEmpresa());
            configJO.put("codfil", this.configuracaoEWFilial.getCodFil().toBigInteger().toString());
            configJO.put("filial", this.configuracaoEWFilial.getRazaoSocial());
            configJO.put("fone", this.configuracaoEWFilial.getFone());
            configJO.put("codcli", this.configuracaoEWCliente.getCodigo());
            configJO.put("nred", this.configuracaoEWCliente.getNRed());
            configJO.put("endereco", this.configuracaoEWCliente.getEnde());
            configJO.put("latitude", this.configuracaoEWCliente.getLatitude());
            configJO.put("longitude", this.configuracaoEWCliente.getLongitude());
            configJO.put("secao", this.configuracaoEWPstServ.getSecao());
            configJO.put("local", this.configuracaoEWPstServ.getLocal());
            configJO.put("descricao", this.configuracaoEWPstServ.getDescContrato());

            Winzap.enviarLink(
                    "http://mobile.sasw.com.br/" + Base64.encode(configJO.toString().getBytes()),
                    getLogo(this.persistenciaLocal.getEmpresa(), this.configuracaoEWFilial.getCodFil().toBigInteger().toString()),
                    "SatMobEW",
                    "Configuração EW",
                    this.configuracaoEWFuncion.getFone1(),
                    this.configuracaoEWFilial.getCodFil().toBigInteger().toString(),
                    this.operador, this.persistenciaLocal.getEmpresa(), this.persistenciaCentral, new ArquivoLogs("caminho"));

            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("MensagemSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<String> BuscarPstServ(String query) {
        try {
            List<String> retorno = new ArrayList<>();
            PstServSatMobWeb pstservsatmobweb = new PstServSatMobWeb();
            PstServ temp = new PstServ();
            temp.setCodFil(this.novo.getCodFil());
            temp.setSecao(query.toUpperCase());
            temp.setLocal(query.toUpperCase());
            temp.setDescContrato(query.toUpperCase());
            for (PstServ p : pstservsatmobweb.BuscaPosto(temp, this.persistenciaLocal)) {
                retorno.add(p.getSecao() + ", " + p.getLocal() + ": " + p.getDescContrato());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    /**
     * Lista cargos para autocomplete
     *
     * @param query
     * @return
     */
    public List<String> buscaCargos(String query) {
        try {
            List<String> retorno = new ArrayList<>();
            for (Cargos c : this.funcionsatmobweb.buscaCargos(query, this.persistenciaLocal)) {
                retorno.add(FuncoesString.preencheCom(c.getCodigo().toBigInteger().toString(), "0", 5, 1) + " - " + c.getDescricao());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    /**
     * Atualiza a lista de horários ao trocar a filial
     *
     * @param event
     */
    public void atualizaHorarios(SelectEvent event) {
        try {
            SasPWFill f = (SasPWFill) event.getObject();
            this.novo.setCodFil(f.getCodfilAc());
            this.novo.setHorario(0);
            this.rhHorarios = this.funcionsatmobweb.listaHorarios(this.novo.getCodFil().toPlainString(), this.persistenciaLocal);
            RHHorario rh = new RHHorario();
            rh.setDescricao("");
            rh.setCodigo(BigDecimal.ZERO);
            this.rhHorarios.add(0, rh);
            carregarPostosFilial();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Seleção do cargo do funcionário
     *
     * @param event
     */
    public void selecionarCargo(SelectEvent event) {
        String[] parts = event.getObject().toString().split(" - ");
        this.novo.setCargo(parts[0]);
        this.novo.setCodCargo(parts[0]);
        this.cargo = (String) event.getObject();
    }

    public void selecionarCidade(SelectEvent event) {
        String[] parts = event.getObject().toString().split(",  ");
        this.novo.setCidade(parts[0]);
        this.novo.setUF(parts[1]);
        this.pessoaMB.getNovaPessoa().setCidade(this.novo.getCidade());
        this.pessoaMB.getNovaPessoa().setUF(this.novo.getUF());
    }

    public void SelecionarPstServ(SelectEvent event) {
        String[] parts = event.getObject().toString().split(", ");
        this.novo.setSecao(parts[0]);
        this.posto = (String) event.getObject();
    }

    public void buttonActionDocumentacaoSel(FuncionPstServ item) throws Exception {
        this.selecionado = item;
        buttonActionDocumentacao(null);
    }

    public void buttonActionDocumentacao(ActionEvent actionEvent) throws Exception {
        try {
            if (null == this.selecionado) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneFuncion"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.pessoaMB.PreCadastro2();
                this.novo = this.selecionado.getFuncion();
                this.pessoaMB.getNovaPessoa().setCPF(Mascaras.removeMascara(this.novo.getCPF()));
                this.pessoaMB.BuscarPessoaLocal();

                this.codPessoa = this.pessoaMB.getNovaPessoa().getCodigo();
                
                this.matr = this.novo.getMatr();
                
                carregarArrayPeDoctos();
                PrimeFaces.current().executeScript("AbrirDocumentacao();");
            }
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, ex.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void buttonActionDocumentacao2(ActionEvent actionEvent) throws Exception {
        try {
            if (null == this.selecionado) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneFuncion"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.codPessoa = this.pessoaMB.getNovaPessoa().getCodigo();

                if (null == this.codPessoa) {
                    PessoaDao pessoaDao = new PessoaDao();
                    Pessoa pessoaBusca = pessoaDao.buscaMatr(this.selecionado.getFuncion().getMatr().toPlainString().replace(".0", ""), this.persistenciaLocal);

                    this.codPessoa = pessoaBusca.getCodigo();
                }

                carregarArrayPeDoctos();
                PrimeFaces.current().executeScript("AbrirDocumentacao();");
            }
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, ex.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void buttonActionEditar(FuncionPstServ item) throws Exception {
        this.selecionado = item;
        buttonAction(null);
    }

    public void buttonAction(ActionEvent actionEvent) throws Exception {
        if (null == this.selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneFuncion"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.pessoaMB.PreCadastro();
            this.novo = this.selecionado.getFuncion();

            PessoaDao pessoaDao = new PessoaDao();
            Pessoa pessoaBusca = pessoaDao.buscaMatr(this.selecionado.getFuncion().getMatr().toPlainString().replace(".0", ""), this.persistenciaLocal);

            this.codPessoa = pessoaBusca.getCodigo();

            try {
                this.filialFuncion = this.loginsatmobweb.BuscaFilial(this.novo.getCodFil().toString(), this.codPessoa, this.persistenciaLocal);
                carregarPostosFilial();
                //Cargos c = this.funcionsatmobweb.getCargoFuncion(this.novo.getCargo(), this.persistenciaLocal);
                //this.cargo = FuncoesString.preencheCom(c.getCodigo().toBigInteger().toString(), "0", 5, 1) + " - " + c.getDescricao();
                this.posto = BuscarPstServ(this.novo.getSecao()).get(0);
                this.cargo = this.novo.getCargo();
                this.rhHorarios = this.funcionsatmobweb.listaHorarios(this.novo.getCodFil().toPlainString(), this.persistenciaLocal);
                RHHorario rh = new RHHorario();
                rh.setDescricao("");
                rh.setCodigo(BigDecimal.ZERO);
                this.rhHorarios.add(0, rh);
                this.rhHorario = this.funcionsatmobweb.buscarHorario(this.novo.getCodFil(), this.novo.getHorario(), this.persistenciaLocal);

                this.rhEscala = new RHEscala();
                this.rhEscala.setDescricao("");
                this.rhEscala.setCodigo(this.novo.getEscala());
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }

            this.pessoaMB.getNovaPessoa().setCPF(Mascaras.removeMascara(this.novo.getCPF()));
            this.pessoaMB.BuscarPessoaLocal();
            this.pessoaMB.getNovaPessoa().setCEP(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getCEP()));
            this.novo.setCPF(Mascaras.CPF(this.novo.getCPF()));
            this.pessoaMB.getNovaPessoa().setCEP(Mascaras.CEP(this.pessoaMB.getNovaPessoa().getCEP()));
            this.pessoaMB.getNovaPessoa().setMatr(this.novo.getMatr());
            this.pessoaMB.getNovaPessoa().setRGOrgEmis(this.novo.getOrgEmis());
            this.pessoaMB.getNovaPessoa().setFone1(this.novo.getFone1());
            this.pessoaMB.getNovaPessoa().setFone2(this.novo.getFone2());
            this.pessoaMB.getNovaPessoa().setObs(this.novo.getObs());

            this.flag = 2;
            this.cpf = false;
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        }
    }

    public void GerarRG() throws Exception {
        if (null != pessoaMB.getNovaPessoa().getRGOrgEmis()
                && pessoaMB.getNovaPessoa().getRGOrgEmis().toUpperCase().equals("MSL")) {
            FuncionDao funcionDao = new FuncionDao();
            this.novo.setRG(funcionDao.gerarRG(this.persistenciaLocal));
        }
    }

    public void Cadastrar() {
        try {
            Locale locale = LocaleController.getsCurrentLocale();

            if ((null == this.pessoaMB.getNovaPessoa().getCPF() || this.pessoaMB.getNovaPessoa().getCPF().equals(""))
                    && null != this.novo.getCPF() && !this.novo.getCPF().equals("")) {
                this.pessoaMB.getNovaPessoa().setCPF(this.novo.getCPF());
            }

            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!ValidadorCPF_CNPJ.ValidarCPF(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getCPF()))) {
                    throw new Exception(Messages.getMessageS("CPFInvalido"));
                }
            }

            if (this.pessoaMB.getNovaPessoa().getMatr().toPlainString().equals("0.0")
                    || this.pessoaMB.getNovaPessoa().getMatr().toPlainString().equals("0")) {
                throw new Exception(Messages.getMessageS("MatriculaInvalida"));
            }

            if (!this.novo.getNome_Guer().equals("") && this.flag == 1) {
                InputText input1 = (InputText) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:nome_guer");
                if (this.funcionsatmobweb.existeSASPWNome(this.novo.getNome_Guer(), this.persistenciaLocal)) {
                    input1.setValid(false);
                    throw new Exception(Messages.getMessageS("NomeExisteSaspw"));
                } else if (this.funcionsatmobweb.existeFuncionNomeGuer(this.novo.getNome_Guer(), this.persistenciaLocal)) {
                    input1.setValid(false);
                    throw new Exception(Messages.getMessageS("NomeExisteFuncion"));
                } else {
                    input1.setValid(true);
                }
            }

            this.pessoaMB.getNovaPessoa().setFuncao(this.novo.getFuncao());
            this.pessoaMB.getNovaPessoa().setCNH(this.novo.getCNH());
            this.pessoaMB.getNovaPessoa().setCNHDtVenc(this.novo.getDt_VenCNH());
            this.pessoaMB.getNovaPessoa().setEmail(this.novo.getEmail().toLowerCase());
            InputText inputEmail = (InputText) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:email");

            if (null != this.novo.getEmail()
                    && !this.novo.getEmail().equals("")
                    && !this.novo.getEmail().contains("@")) {
                inputEmail.setValid(false);
                throw new Exception(Messages.getMessageS("EmailInvalido"));
            } else {
                inputEmail.setValid(true);
            }

            //this.novo.setRG(this.pessoaMB.getNovaPessoa().getRG());
            this.novo.setOrgEmis(this.pessoaMB.getNovaPessoa().getRGOrgEmis());
            if (getIDSSN()) { //obrigatorio
                InputText inputRG = (InputText) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:rg");
                if (null == this.novo.getRG() || this.novo.getRG().equals("")) {
                    inputRG.setValid(false);
                    throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("RG"));
                } else {
                    inputRG.setValid(true);
                }
                InputText inputRgOrg = (InputText) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:rgorg");
                if (null == this.novo.getOrgEmis() || this.novo.getOrgEmis().equals("")) {
                    inputRgOrg.setValid(false);
                    throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("RGOrg"));
                } else {
                    inputRgOrg.setValid(true);
                }
            } else {
                if (null == this.novo.getRG() || this.novo.getRG().equals("")) {
                    //this.pessoaMB.getNovaPessoa().setRG(this.pessoaMB.getNovaPessoa().getMatr().toBigInteger().toString());
                }
                if (null == this.novo.getOrgEmis() || this.novo.getOrgEmis().equals("")) {
                    this.pessoaMB.getNovaPessoa().setRGOrgEmis("Sat-Auto");
                }
            }

            this.novo.setHorario(this.rhHorario.getCodigo().toBigInteger().intValue());
            SelectOneMenu inputHorario = (SelectOneMenu) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:horario");
            if (this.novo.getHorario() == 0) {
                inputHorario.setValid(false);
                throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("Horario"));
            } else {
                inputHorario.setValid(true);
            }

            if (this.rhEscala != null) {
                this.novo.setEscala(this.rhEscala.getCodigo());
            } else {
                this.novo.setEscala("");
            }

            this.novo.setCodFil(this.filialFuncion.getCodfilAc());
            this.novo.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.pessoaMB.getNovaPessoa().setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.novo.setMatr(this.pessoaMB.getNovaPessoa().getMatr().toPlainString());
            this.novo.setCPF(Mascaras.removeMascara(this.novo.getCPF()));

            this.pessoaMB.getNovaPessoa().setCPF(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getCPF()));

            //this.novo.setRG(this.pessoaMB.getNovaPessoa().getRG());
            this.pessoaMB.getNovaPessoa().setRG(this.novo.getRG());
            this.novo.setOrgEmis(this.pessoaMB.getNovaPessoa().getRGOrgEmis());
            this.novo.setNome((this.novo.getNome()).toUpperCase());

            this.novo.setDt_Admis(Mascaras.removeMascaraData(this.novo.getDt_Admis()));
            this.novo.setDt_Nasc(Mascaras.removeMascaraData(this.novo.getDt_Nasc()));

            if (null != this.pessoaMB.getNovaPessoa().getAltura()) {
                this.novo.setAltura(this.pessoaMB.getNovaPessoa().getAltura().toPlainString());
            }
            if (null != this.pessoaMB.getNovaPessoa().getPeso()) {
                this.novo.setPeso(this.pessoaMB.getNovaPessoa().getPeso().toPlainString());
            }
            this.pessoaMB.getNovaPessoa().setNome(this.novo.getNome().toUpperCase());
            this.novo.setNome(this.novo.getNome());

            this.pessoaMB.getNovaPessoa().setEndereco(this.novo.getEndereco().toUpperCase());
            this.novo.setEndereco(FuncoesString.RecortaString(this.novo.getEndereco(), 0, 40));
            this.pessoaMB.getNovaPessoa().setBairro(this.novo.getBairro().toUpperCase());
            this.pessoaMB.getNovaPessoa().setBairro(FuncoesString.RecortaString(this.novo.getBairro(), 0, 20));
            this.pessoaMB.getNovaPessoa().setCidade(this.novo.getCidade());
            this.pessoaMB.getNovaPessoa().setUF(this.novo.getUF());
            this.pessoaMB.getNovaPessoa().setObs(this.pessoaMB.getNovaPessoa().getObs().toUpperCase());
            this.novo.setObs(this.pessoaMB.getNovaPessoa().getObs());
            this.pessoaMB.getNovaPessoa().setSexo(this.novo.getSexo());

            this.pessoaMB.getNovaPessoa().setCEP(Mascaras.removeMascara(this.novo.getCEP()));
            this.novo.setCEP(this.novo.getCEP());

            this.pessoaMB.getNovaPessoa().setFone1(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getFone1()));
            this.novo.setFone1(this.pessoaMB.getNovaPessoa().getFone1());

            this.pessoaMB.getNovaPessoa().setFone2(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getFone2()));
            this.novo.setFone2(this.pessoaMB.getNovaPessoa().getFone2());
            this.pessoaMB.getNovaPessoa().setMatr(this.novo.getMatr());

            this.novo.setCargo(this.cargo);
            this.pessoaMB.getNovaPessoa().setCargoPretend(this.cargo);

            if (this.flag == 1) {
                this.funcionsatmobweb.Inserir(this.novo, this.pessoaMB.getNovaPessoa(), this.persistenciaLocal, this.persistenciaCentral);
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else if (this.flag == 2) {
                this.funcionsatmobweb.GravaFuncion(this.novo, this.pessoaMB.getNovaPessoa(), this.persistenciaLocal, this.persistenciaCentral);
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void EdicaoFuncionario() {
        try {
            this.pessoaMB.getNovaPessoa().setCPF(this.novo.getCPF());

            Locale locale = LocaleController.getsCurrentLocale();
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!ValidadorCPF_CNPJ.ValidarCPF(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getCPF()))) {
                    this.novo.setCPF("");
                    this.pessoaMB.getNovaPessoa().setCPF("");
                    throw new Exception(Messages.getMessageS("CPFInvalido"));
                }
            }
            this.pessoaMB.getNovaPessoa().setFuncao(this.novo.getFuncao());
            this.pessoaMB.getNovaPessoa().setCNH(this.novo.getCNH());
            this.pessoaMB.getNovaPessoa().setCNHDtVenc(this.novo.getDt_VenCNH());
            this.pessoaMB.getNovaPessoa().setEmail(this.novo.getEmail().toLowerCase());
            InputText inputEmail = (InputText) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:email");

            if (null != this.novo.getEmail()
                    && !this.novo.getEmail().equals("")
                    && !this.novo.getEmail().contains("@")) {
                inputEmail.setValid(false);
                throw new Exception(Messages.getMessageS("EmailInvalido"));
            } else {
                inputEmail.setValid(true);
            }

            //this.novo.setRG(this.pessoaMB.getNovaPessoa().getRG());
            this.novo.setOrgEmis(this.pessoaMB.getNovaPessoa().getRGOrgEmis());
            if (getIDSSN()) { //obrigatorio
                InputText inputRG = (InputText) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:rg");
                if (null == this.novo.getRG() || this.novo.getRG().equals("")) {
                    inputRG.setValid(false);
                    throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("RG"));
                } else {
                    inputRG.setValid(true);
                }
                InputText inputRgOrg = (InputText) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:rgorg");
                if (null == this.novo.getOrgEmis() || this.novo.getOrgEmis().equals("")) {
                    inputRgOrg.setValid(false);
                    throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("RGOrg"));
                } else {
                    inputRgOrg.setValid(true);
                }
            } else {
                if (null == this.novo.getRG() || this.novo.getRG().equals("")) {
                    //this.pessoaMB.getNovaPessoa().setRG(this.pessoaMB.getNovaPessoa().getMatr().toBigInteger().toString());
                }
                if (null == this.novo.getOrgEmis() || this.novo.getOrgEmis().equals("")) {
                    this.pessoaMB.getNovaPessoa().setRGOrgEmis("Sat-Auto");
                }
            }

            this.novo.setCodFil(this.filialFuncion.getCodfilAc());
            this.novo.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.pessoaMB.getNovaPessoa().setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.novo.setMatr(this.pessoaMB.getNovaPessoa().getMatr().toPlainString());
            this.novo.setCPF(Mascaras.removeMascara(this.novo.getCPF()));

            this.pessoaMB.getNovaPessoa().setCPF(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getCPF()));

            this.pessoaMB.getNovaPessoa().setRG(this.novo.getRG());
            this.novo.setOrgEmis(this.pessoaMB.getNovaPessoa().getRGOrgEmis());
            this.novo.setNome((this.novo.getNome()).toUpperCase());

            this.novo.setDt_Admis(Mascaras.removeMascaraData(this.novo.getDt_Admis()));
            this.novo.setDt_Nasc(Mascaras.removeMascaraData(this.novo.getDt_Nasc()));

            if (null != this.pessoaMB.getNovaPessoa().getAltura()) {
                this.novo.setAltura(this.pessoaMB.getNovaPessoa().getAltura().toPlainString());
            }
            if (null != this.pessoaMB.getNovaPessoa().getPeso()) {
                this.novo.setPeso(this.pessoaMB.getNovaPessoa().getPeso().toPlainString());
            }
            this.pessoaMB.getNovaPessoa().setNome(this.novo.getNome().toUpperCase());
            this.novo.setNome(this.novo.getNome());

            this.pessoaMB.getNovaPessoa().setEndereco(this.novo.getEndereco().toUpperCase());
            this.novo.setEndereco(FuncoesString.RecortaString(this.novo.getEndereco(), 0, 40));
            this.pessoaMB.getNovaPessoa().setBairro(this.novo.getBairro().toUpperCase());
            this.pessoaMB.getNovaPessoa().setBairro(FuncoesString.RecortaString(this.novo.getBairro(), 0, 20));
            this.pessoaMB.getNovaPessoa().setCidade(this.novo.getCidade());
            this.pessoaMB.getNovaPessoa().setUF(this.novo.getUF());
            this.pessoaMB.getNovaPessoa().setObs(this.pessoaMB.getNovaPessoa().getObs().toUpperCase());
            this.novo.setObs(this.pessoaMB.getNovaPessoa().getObs());
            this.pessoaMB.getNovaPessoa().setSexo(this.novo.getSexo());

            this.pessoaMB.getNovaPessoa().setCEP(Mascaras.removeMascara(this.novo.getCEP()));
            this.novo.setCEP(this.pessoaMB.getNovaPessoa().getCEP());

            this.pessoaMB.getNovaPessoa().setFone1(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getFone1()));
            this.novo.setFone1(this.pessoaMB.getNovaPessoa().getFone1());

            this.pessoaMB.getNovaPessoa().setFone2(Mascaras.removeMascara(this.pessoaMB.getNovaPessoa().getFone2()));
            this.novo.setFone2(this.pessoaMB.getNovaPessoa().getFone2());
            this.pessoaMB.getNovaPessoa().setMatr(this.novo.getMatr());

            this.novo.setCargo(this.cargo);
            this.pessoaMB.getNovaPessoa().setCargoPretend(this.cargo);

            this.novo.setCodFil(BigDecimal.valueOf(Double.parseDouble(this.codFil)));

            this.funcionsatmobweb.GravaFuncion(this.novo, this.pessoaMB.getNovaPessoa(), this.persistenciaLocal, this.persistenciaCentral);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            PrimeFaces.current().executeScript("MascarasJS();");
        }
    }

    public void dblSelect(SelectEvent event) throws Exception {
        this.selecionado = (FuncionPstServ) event.getObject();
        this.rhEscala = new RHEscala();

        if (null != this.selecionado
                && null != this.selecionado.getFuncion()
                && null != this.selecionado.getFuncion().getEscala()) {
            this.rhEscala.setCodigo(this.selecionado.getFuncion().getEscala());
        }

        buttonAction(null);
    }

    public void PrePesquisar() {
        this.selecionado = new FuncionPstServ();
        this.selecionado.getPstserv().setLocal("");
        this.selecionado.getFuncion().setCodPessoaWeb(this.codPessoa);
        this.filial = "";
        this.filialFuncion = new SasPWFill();
    }

    public void SomenteAtivos() {
        filters.replace(SITUACAO, somenteAtivos ? "A" : "");
        //DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        //dt.setFilters(filters);
        getAllFuncion();
        //dt.setFirst(0);
    }

    public void MostrarFiliais() {
        filters.replace(CODFIL, mostrarFiliais ? "" : codFil);
        //DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        //dt.setFilters(filters);
        getAllFuncion();
        //dt.setFirst(0);
    }

    public void limparFiltros() {
        mostrarFiliais = false;
        somenteAtivos = true;
        filters.replace(CODFIL, codFil);
        filters.replace(SITUACAO, "A");
        filters.replace(NOME, "");
        filters.replace(LOCAL, "");
        filters.replace(NOME_GUER, "");
        filters.replace(MATR, "");
        filters.replace(PIS, "");

        //DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        //dt.setFilters(filters);
        getAllFuncion();
        //dt.setFirst(0);
    }

    public void limparPesquisa() {
        filters.replace(NOME, "");
        filters.replace(LOCAL, "");
        filters.replace(NOME_GUER, "");
        filters.replace(MATR, "");
        filters.replace(PIS, "");
    }

    public LazyDataModel<FuncionPstServ> getAllFuncion() {
        if (funcions == null) {
            //DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            filters.replace(CODFIL, codFil);
            filters.replace(SITUACAO, "A");
            //((FuncionLazyList) funcions).setFilters(filters);
            this.funcions = new FuncionLazyList(persistenciaLocal, codPessoa, filters);
        } else {
            //((FuncionLazyList) funcions).setFilters(filters);
        }
        try {
            this.total = this.funcionsatmobweb.Contagem(this.filters, this.codPessoa, this.persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.funcions;
    }

    public void PesquisaPaginada() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        if (null != this.selecionado.getFuncion().getCodFil() && !this.selecionado.getFuncion().getCodFil().equals(BigDecimal.ZERO)) {
            filters.replace(CODFIL, this.selecionado.getFuncion().getCodFil().toPlainString());
            if (this.selecionado.getFuncion().getCodFil().equals(this.codFil)) {
                this.mostrarFiliais = false;
            } else {
                this.mostrarFiliais = false;
            }
        } else {
            filters.replace(CODFIL, "");
            this.mostrarFiliais = true;
        }
        if (!this.selecionado.getFuncion().getNome().equals("")) {
            filters.replace(NOME, "%" + this.selecionado.getFuncion().getNome() + "%");
        } else {
            filters.replace(NOME, "");
        }
        if (!this.selecionado.getFuncion().getNome_Guer().equals("")) {
            filters.replace(NOME_GUER, "%" + this.selecionado.getFuncion().getNome_Guer() + "%");
        } else {
            filters.replace(NOME_GUER, "");
        }
        if (null != this.selecionado.getFuncion().getSituacao()) {
            filters.replace(SITUACAO, this.selecionado.getFuncion().getSituacao());
            if (this.selecionado.getFuncion().getSituacao().equals("A")) {
                this.somenteAtivos = true;
            } else {
                this.somenteAtivos = false;
            }
        } else {
            filters.replace(SITUACAO, "");
            this.somenteAtivos = false;
        }
        if (null != this.selecionado.getPstserv().getLocal()) {
            filters.replace(LOCAL, "%" + this.selecionado.getPstserv().getLocal().split(", ")[1].split(": ")[0] + "%");
        } else {
            filters.replace(LOCAL, "");
        }
        if (null != this.selecionado.getFuncion().getMatr()) {
            filters.replace(MATR, this.selecionado.getFuncion().getMatr().toPlainString());
        } else {
            filters.replace(MATR, "");
        }
        dt.setFilters(this.filters);
        getAllFuncion();
        dt.setFirst(0);
    }

    public void carregarDadosPessoaAproveitamento() throws Exception {
        PessoaDao pessoaDao = new PessoaDao();
        Pessoa pessoaReaproveitamento = pessoaDao.buscarPessoaCodigo(this.codPessoaAproveitamento, this.persistenciaLocal);

        this.novo.setCodPessoaReaproveitamento(this.codPessoaAproveitamento);
        this.pessoaMB.getNovaPessoa().setCodigo(this.codPessoaAproveitamento);
        this.novo.setCPF(pessoaReaproveitamento.getCPF());
        this.novo.setNome(pessoaReaproveitamento.getNome());
        this.pessoaMB.getNovaPessoa().setNome(pessoaReaproveitamento.getNome());
        this.novo.setRG(pessoaReaproveitamento.getRG());
        this.novo.setRgDtEmis(pessoaReaproveitamento.getRgDtEmis());
        this.pessoaMB.getNovaPessoa().setRGOrgEmis(pessoaReaproveitamento.getRGOrgEmis());
        this.pessoaMB.getNovaPessoa().setFone1(pessoaReaproveitamento.getFone1());
        this.pessoaMB.getNovaPessoa().setFone2(pessoaReaproveitamento.getFone2());
        this.novo.setEmail(pessoaReaproveitamento.getEmail());
        this.novo.setCEP(pessoaReaproveitamento.getCEP());
        this.novo.setBairro(pessoaReaproveitamento.getBairro());
        this.novo.setEndereco(pessoaReaproveitamento.getEndereco());
        this.novo.setCidade(pessoaReaproveitamento.getCidade());
        this.novo.setUF(pessoaReaproveitamento.getUF());
        this.pessoaMB.getNovaPessoa().setObs(pessoaReaproveitamento.getObs());
        this.novo.setSituacao(pessoaReaproveitamento.getSituacao());
        this.pessoaMB.getNovaPessoa().setAltura(pessoaReaproveitamento.getAltura());
        this.pessoaMB.getNovaPessoa().setPeso(pessoaReaproveitamento.getPeso());
        this.novo.setDt_Nasc(pessoaReaproveitamento.getDt_nasc());
        this.novo.setSexo(pessoaReaproveitamento.getSexo());
        this.setPosto(pessoaReaproveitamento.getSecaoPretend());
        this.novo.setCargo(pessoaReaproveitamento.getCargoPretend());
        this.novo.setHorario(pessoaReaproveitamento.getHorarioPretend());
    }

    public void pesquisarUnico() {
        if (chavePesquisa.equals("MATR")) {
            try {
                int TesteValid = Integer.parseInt(valorPesquisa);
                pesquisarUnicoAction();
            } catch (Exception ex) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("MatriculaInvalida"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                return;
            }
        } else {
            pesquisarUnicoAction();
        }
    }

    private void pesquisarUnicoAction() {
        //DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        this.limparPesquisa();
        this.replaceFilter(valorPesquisa);
        funcions = null;
        //dt.setFilters(filters);
        getAllFuncion();
        //dt.setFirst(0);

        PrimeFaces.current().ajax().update("main:tabela");
        PrimeFaces.current().ajax().update("cabecalho");
        PrimeFaces.current().executeScript("PF('dlgPesquisaRapida').hide()");
    }

    private void replaceFilter(String valor) {
        try {
            switch (chavePesquisa) {
                case "CODFIL":
                    filters.replace(CODFIL, valor);
                    return;
                case "SITUACAO":
                    filters.replace(SITUACAO, "%" + valor + "%");
                    return;
                case "NOME_GUER":
                    filters.replace(NOME_GUER, "%" + valor + "%");
                    return;
                case "NOME":
                    filters.replace(NOME, "%" + valor + "%");
                    return;
                case "LOCAL":
                    filters.replace(LOCAL, "%" + valor + "%");
                    return;
                case "MATR":
                    filters.replace(MATR, valor);
                    return;
                case "PIS":
                    filters.replace(PIS, valor);
                    return;
                default:
                    throw new Exception("Field does not exist");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void buscarFunc() {
        try {
            this.listafun = this.funcionsatmobweb.listaFuncion(this.codFil, this.matr, this.nome, this.nome_guer, this.persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void AtualizaTabela() {
        PrimeFaces.current().ajax().update("main:tabela");
    }

    public void AtualizaTabelaMenu() {
        PrimeFaces.current().ajax().update("exportarFuncion:tabela");
    }

    public LazyDataModel<FuncionPstServ> getAllFuncionMenu() {
        if (this.funcions == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("exportarFuncion:tabela");
            filters.replace(CODFIL, codFil);
            filters.replace(SITUACAO, "A");
            dt.setFilters(this.filters);
            funcions = new FuncionLazyList(persistenciaLocal, codPessoa);
        }
        try {
            total = funcionsatmobweb.Contagem(filters, codPessoa, persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return funcions;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public List<FuncionPstServ> getLista() {
        return lista;
    }

    public void setLista(List<FuncionPstServ> lista) {
        this.lista = lista;
    }

    public FuncionPstServ getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(FuncionPstServ selecionado) {
        this.selecionado = selecionado;
    }

    public Funcion getNovo() {
        return novo;
    }

    public void setNovo(Funcion novo) {
        this.novo = novo;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public PessoasMB getPessoaMB() {
        return pessoaMB;
    }

    public void setPessoaMB(PessoasMB pessoaMB) {
        this.pessoaMB = pessoaMB;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getPosto() {
        return posto;
    }

    public void setPosto(String posto) {
        this.posto = posto;
    }

    public boolean isCpf() {
        return cpf;
    }

    public void setCpf(boolean cpf) {
        this.cpf = cpf;
    }

    public Boolean getAtualizado() {
        return atualizado;
    }

    public void setAtualizado(Boolean atualizado) {
        this.atualizado = atualizado;
    }

    public Boolean getMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(Boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public Boolean getSomenteAtivos() {
        return somenteAtivos;
    }

    public void setSomenteAtivos(Boolean somenteAtivos) {
        this.somenteAtivos = somenteAtivos;
    }

    public String getFilial() {
        return filial;
    }

    public void setFilial(String filial) {
        this.filial = filial;
    }

    public SasPWFill getFilialFuncion() {
        return filialFuncion;
    }

    public void setFilialFuncion(SasPWFill filialFuncion) {
        this.filialFuncion = filialFuncion;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public boolean iseFilial() {
        return eFilial;
    }

    public void seteFilial(boolean eFilial) {
        this.eFilial = eFilial;
    }

    public boolean iseMatr() {
        return eMatr;
    }

    public void seteMatr(boolean eMatr) {
        this.eMatr = eMatr;
    }

    public boolean iseNome() {
        return eNome;
    }

    public void seteNome(boolean eNome) {
        this.eNome = eNome;
    }

    public boolean iseEmail() {
        return eEmail;
    }

    public void seteEmail(boolean eEmail) {
        this.eEmail = eEmail;
    }

    public boolean iseInterfExt() {
        return eInterfExt;
    }

    public void seteInterfExt(boolean eInterfExt) {
        this.eInterfExt = eInterfExt;
    }

    public boolean iseCodPessoaWeb() {
        return eCodPessoaWeb;
    }

    public void seteCodPessoaWeb(boolean eCodPessoaWeb) {
        this.eCodPessoaWeb = eCodPessoaWeb;
    }

    public boolean iseNomeGuer() {
        return eNomeGuer;
    }

    public void seteNomeGuer(boolean eNomeGuer) {
        this.eNomeGuer = eNomeGuer;
    }

    public boolean iseRG() {
        return eRG;
    }

    public void seteRG(boolean eRG) {
        this.eRG = eRG;
    }

    public boolean iseOrgEmis() {
        return eOrgEmis;
    }

    public void seteOrgEmis(boolean eOrgEmis) {
        this.eOrgEmis = eOrgEmis;
    }

    public boolean iseCPF() {
        return eCPF;
    }

    public void seteCPF(boolean eCPF) {
        this.eCPF = eCPF;
    }

    public boolean iseDtNasc() {
        return eDtNasc;
    }

    public void seteDtNasc(boolean eDtNasc) {
        this.eDtNasc = eDtNasc;
    }

    public boolean iseSituacao() {
        return eSituacao;
    }

    public void seteSituacao(boolean eSituacao) {
        this.eSituacao = eSituacao;
    }

    public boolean iseDtSituacao() {
        return eDtSituacao;
    }

    public void seteDtSituacao(boolean eDtSituacao) {
        this.eDtSituacao = eDtSituacao;
    }

    public boolean iseDtAdmis() {
        return eDtAdmis;
    }

    public void seteDtAdmis(boolean eDtAdmis) {
        this.eDtAdmis = eDtAdmis;
    }

    public boolean iseCodPosto() {
        return eCodPosto;
    }

    public void seteCodPosto(boolean eCodPosto) {
        this.eCodPosto = eCodPosto;
    }

    public boolean isePosto() {
        return ePosto;
    }

    public void setePosto(boolean ePosto) {
        this.ePosto = ePosto;
    }

    public boolean iseOperador() {
        return eOperador;
    }

    public void seteOperador(boolean eOperador) {
        this.eOperador = eOperador;
    }

    public boolean iseDtAlter() {
        return eDtAlter;
    }

    public void seteDtAlter(boolean eDtAlter) {
        this.eDtAlter = eDtAlter;
    }

    public boolean iseHrAlter() {
        return eHrAlter;
    }

    public void seteHrAlter(boolean eHrAlter) {
        this.eHrAlter = eHrAlter;
    }

    public boolean isMatriculaAutomatica() {
        return matriculaAutomatica;
    }

    public void setMatriculaAutomatica(boolean matriculaAutomatica) {
        this.matriculaAutomatica = matriculaAutomatica;
    }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    public List<RHHorario> getRhHorarios() {
        return rhHorarios;
    }

    public void setRhHorarios(List<RHHorario> rhHorarios) {
        this.rhHorarios = rhHorarios;
    }

    public RHHorario getRhHorario() {
        return rhHorario;
    }

    public void setRhHorario(RHHorario rhHorario) {
        this.rhHorario = rhHorario;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNome_guer() {
        return nome_guer;
    }

    public void setNome_guer(String nome_guer) {
        this.nome_guer = nome_guer;
    }

    public BigDecimal getMatr() {
        return matr;
    }

    public void setMatr(BigDecimal matr) {
        this.matr = matr;
    }

    public List<Funcion> getListafun() {
        return listafun;
    }

    public void setListafun(List<Funcion> listafun) {
        this.listafun = listafun;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public BigDecimal getCodPessoaAproveitamento() {
        return codPessoaAproveitamento;
    }

    public void setCodPessoaAproveitamento(BigDecimal codPessoaAproveitamento) {
        this.codPessoaAproveitamento = codPessoaAproveitamento;
    }

    public List<Cargos> getListaCargos() {
        return listaCargos;
    }

    public void setListaCargos(List<Cargos> listaCargos) {
        this.listaCargos = listaCargos;
    }

    public List<PstServ> getListaPostos() {
        return listaPostos;
    }

    public void setListaPostos(List<PstServ> listaPostos) {
        this.listaPostos = listaPostos;
    }

    public List<Funcion> getConfiguracaoEWFuncionList() {
        return configuracaoEWFuncionList;
    }

    public void setConfiguracaoEWFuncionList(List<Funcion> configuracaoEWFuncionList) {
        this.configuracaoEWFuncionList = configuracaoEWFuncionList;
    }

    public Funcion getConfiguracaoEWFuncion() {
        return configuracaoEWFuncion;
    }

    public void setConfiguracaoEWFuncion(Funcion configuracaoEWFuncion) {
        this.configuracaoEWFuncion = configuracaoEWFuncion;
    }

    public Filiais getConfiguracaoEWFilial() {
        return configuracaoEWFilial;
    }

    public void setConfiguracaoEWFilial(Filiais configuracaoEWFilial) {
        this.configuracaoEWFilial = configuracaoEWFilial;
    }

    public boolean isConfiguracaoEWLiberarCliente() {
        return configuracaoEWLiberarCliente;
    }

    public void setConfiguracaoEWLiberarCliente(boolean configuracaoEWLiberarCliente) {
        this.configuracaoEWLiberarCliente = configuracaoEWLiberarCliente;
    }

    public boolean isConfiguracaoEWLiberarPosto() {
        return configuracaoEWLiberarPosto;
    }

    public void setConfiguracaoEWLiberarPosto(boolean configuracaoEWLiberarPosto) {
        this.configuracaoEWLiberarPosto = configuracaoEWLiberarPosto;
    }

    public Clientes getConfiguracaoEWCliente() {
        return configuracaoEWCliente;
    }

    public void setConfiguracaoEWCliente(Clientes configuracaoEWCliente) {
        this.configuracaoEWCliente = configuracaoEWCliente;
    }

    public PstServ getConfiguracaoEWPstServ() {
        return configuracaoEWPstServ;
    }

    public void setConfiguracaoEWPstServ(PstServ configuracaoEWPstServ) {
        this.configuracaoEWPstServ = configuracaoEWPstServ;
    }

    public boolean isConfiguracaoEWLiberarFuncion() {
        return configuracaoEWLiberarFuncion;
    }

    public void setConfiguracaoEWLiberarFuncion(boolean configuracaoEWLiberarFuncion) {
        this.configuracaoEWLiberarFuncion = configuracaoEWLiberarFuncion;
    }

    public List<Clientes> getConfiguracaoEWClienteList() {
        return configuracaoEWClienteList;
    }

    public void setConfiguracaoEWClienteList(List<Clientes> configuracaoEWClienteList) {
        this.configuracaoEWClienteList = configuracaoEWClienteList;
    }

    public List<PstServ> getConfiguracaoEWPstServList() {
        return configuracaoEWPstServList;
    }

    public void setConfiguracaoEWPstServList(List<PstServ> configuracaoEWPstServList) {
        this.configuracaoEWPstServList = configuracaoEWPstServList;
    }

    public List<Municipios> getCidades() {
        return cidades;
    }

    public void setCidades(List<Municipios> cidades) {
        this.cidades = cidades;
    }

    public List<RHEscala> getRhEscalas() {
        return rhEscalas;
    }

    public void setRhEscalas(List<RHEscala> rhEscalas) {
        this.rhEscalas = rhEscalas;
    }

    public RHEscala getRhEscala() {
        return rhEscala;
    }

    public void setRhEscala(RHEscala rhEscala) {
        this.rhEscala = rhEscala;
    }

    public String getTipoArq() {
        return tipoArq;
    }

    public void setTipoArq(String tipoArq) {
        this.tipoArq = tipoArq;
    }

    public TbVal getTbVal() {
        return tbVal;
    }

    public void setTbVal(TbVal tbVal) {
        this.tbVal = tbVal;
    }

    public List<TbVal> getListTbVal() {
        return listTbVal;
    }

    public void setListTbVal(List<TbVal> listTbVal) {
        this.listTbVal = listTbVal;
    }

}

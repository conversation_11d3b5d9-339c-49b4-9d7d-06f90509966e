/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RamosAtiv;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RamosAtivDao {

    public boolean inserirRamosAtiv(RamosAtiv ramosAtiv, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO RamosAtiv (Codigo, Descricao, Operador, Dt_alter, Hr_Alter) \n"
                    + " VALUES (?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ramosAtiv.getCodigo());
            consulta.setString(ramosAtiv.getDescricao());
            consulta.setString(ramosAtiv.getOperador());
            consulta.setString(ramosAtiv.getDt_alter());
            consulta.setString(ramosAtiv.getHr_Alter());
            int retorno = consulta.insert();
            consulta.close();
            return retorno > 0;
        } catch (Exception e) {
            throw new Exception("RamosAtivDao.inserirRamosAtiv - " + e.getMessage() + "\r\n"
                    + " INSERT INTO RamosAtiv (Codigo, Descricao, Operador, Dt_alter, Hr_Alter) \n"
                    + " VALUES (" + ramosAtiv.getCodigo() + ", " + ramosAtiv.getDescricao() + ", " + ramosAtiv.getOperador() + ", "
                    + ramosAtiv.getDt_alter() + ", " + ramosAtiv.getHr_Alter() + ") ");
        }
    }

    public boolean existeRamoAtiv(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM RamosAtiv WHERE Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RamosAtivDao.existeRamoAtiv - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM RamosAtiv WHERE Codigo = " + codigo);
        }
    }

    public List<RamosAtiv> listarRamosAtiv(Persistencia persistencia) throws Exception {
        try {
            List<RamosAtiv> retorno = new ArrayList<>();
            String sql = " SELECT * FROM RamosAtiv ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            RamosAtiv ramosAtiv;
            while (consulta.Proximo()) {
                ramosAtiv = new RamosAtiv();
                ramosAtiv.setCodigo(consulta.getString("Codigo"));
                ramosAtiv.setDescricao(consulta.getString("Descricao"));
                ramosAtiv.setOperador(consulta.getString("Operador"));
                ramosAtiv.setDt_alter(consulta.getString("Dt_alter"));
                ramosAtiv.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(ramosAtiv);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RamosAtivDao.listarRamosAtiv - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM RamosAtiv ");
        }
    }
}

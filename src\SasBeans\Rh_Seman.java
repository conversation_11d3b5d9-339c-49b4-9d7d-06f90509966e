/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Rh_Seman {

    private BigDecimal Matr;
    private BigDecimal CodFil;
    private BigDecimal Semana;
    private BigDecimal Hr50;
    private BigDecimal Hr100;
    private BigDecimal Hr3;
    private BigDecimal Hr50i;
    private BigDecimal Hr100i;
    private BigDecimal Hr3i;
    private BigDecimal AdNot;
    private BigDecimal HorasTrab;
    private BigDecimal HsAbnFalta;
    private BigDecimal HsAbnFeriado;
    private BigDecimal HsProjecao;
    private BigDecimal Faltas;
    private BigDecimal FaltasJust;
    private BigDecimal Reciclagem;
    private BigDecimal Sindicato;
    private BigDecimal Transito;
    private BigDecimal HEInc;
    private BigDecimal HorasExtr;
    private BigDecimal HsAComp;
    private BigDecimal DiasFolga;
    private BigDecimal DiasFerTrab;
    private BigDecimal HEFeriado;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the Matr
     */
    public BigDecimal getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Semana
     */
    public BigDecimal getSemana() {
        return Semana;
    }

    /**
     * @param Semana the Semana to set
     */
    public void setSemana(String Semana) {
        try {
            this.Semana = new BigDecimal(Semana);
        } catch (Exception e) {
            this.Semana = new BigDecimal("0");
        }
    }

    /**
     * @return the Hr50
     */
    public BigDecimal getHr50() {
        return Hr50;
    }

    /**
     * @param Hr50 the Hr50 to set
     */
    public void setHr50(String Hr50) {
        try {
            this.Hr50 = new BigDecimal(Hr50);
        } catch (Exception e) {
            this.Hr50 = new BigDecimal("0");
        }
    }

    /**
     * @return the Hr100
     */
    public BigDecimal getHr100() {
        return Hr100;
    }

    /**
     * @param Hr100 the Hr100 to set
     */
    public void setHr100(String Hr100) {
        try {
            this.Hr100 = new BigDecimal(Hr100);
        } catch (Exception e) {
            this.Hr100 = new BigDecimal("0");
        }
    }

    /**
     * @return the Hr3
     */
    public BigDecimal getHr3() {
        return Hr3;
    }

    /**
     * @param Hr3 the Hr3 to set
     */
    public void setHr3(String Hr3) {
        try {
            this.Hr3 = new BigDecimal(Hr3);
        } catch (Exception e) {
            this.Hr3 = new BigDecimal("0");
        }
    }

    /**
     * @return the Hr50i
     */
    public BigDecimal getHr50i() {
        return Hr50i;
    }

    /**
     * @param Hr50i the Hr50i to set
     */
    public void setHr50i(String Hr50i) {
        try {
            this.Hr50i = new BigDecimal(Hr50i);
        } catch (Exception e) {
            this.Hr50i = new BigDecimal("0");
        }
    }

    /**
     * @return the Hr100i
     */
    public BigDecimal getHr100i() {
        return Hr100i;
    }

    /**
     * @param Hr100i the Hr100i to set
     */
    public void setHr100i(String Hr100i) {
        try {
            this.Hr100i = new BigDecimal(Hr100i);
        } catch (Exception e) {
            this.Hr100i = new BigDecimal("0");
        }
    }

    /**
     * @return the Hr3i
     */
    public BigDecimal getHr3i() {
        return Hr3i;
    }

    /**
     * @param Hr3i the Hr3i to set
     */
    public void setHr3i(String Hr3i) {
        try {
            this.Hr3i = new BigDecimal(Hr3i);
        } catch (Exception e) {
            this.Hr3i = new BigDecimal("0");
        }
    }

    /**
     * @return the AdNot
     */
    public BigDecimal getAdNot() {
        return AdNot;
    }

    /**
     * @param AdNot the AdNot to set
     */
    public void setAdNot(String AdNot) {
        try {
            this.AdNot = new BigDecimal(AdNot);
        } catch (Exception e) {
            this.AdNot = new BigDecimal("0");
        }
    }

    /**
     * @return the HorasTrab
     */
    public BigDecimal getHorasTrab() {
        return HorasTrab;
    }

    /**
     * @param HorasTrab the HorasTrab to set
     */
    public void setHorasTrab(String HorasTrab) {
        try {
            this.HorasTrab = new BigDecimal(HorasTrab);
        } catch (Exception e) {
            this.HorasTrab = new BigDecimal("0");
        }
    }

    /**
     * @return the HsAbnFalta
     */
    public BigDecimal getHsAbnFalta() {
        return HsAbnFalta;
    }

    /**
     * @param HsAbnFalta the HsAbnFalta to set
     */
    public void setHsAbnFalta(String HsAbnFalta) {
        try {
            this.HsAbnFalta = new BigDecimal(HsAbnFalta);
        } catch (Exception e) {
            this.HsAbnFalta = new BigDecimal("0");
        }
    }

    /**
     * @return the HsAbnFeriado
     */
    public BigDecimal getHsAbnFeriado() {
        return HsAbnFeriado;
    }

    /**
     * @param HsAbnFeriado the HsAbnFeriado to set
     */
    public void setHsAbnFeriado(String HsAbnFeriado) {
        try {
            this.HsAbnFeriado = new BigDecimal(HsAbnFeriado);
        } catch (Exception e) {
            this.HsAbnFeriado = new BigDecimal("0");
        }
    }

    /**
     * @return the HsProjecao
     */
    public BigDecimal getHsProjecao() {
        return HsProjecao;
    }

    /**
     * @param HsProjecao the HsProjecao to set
     */
    public void setHsProjecao(String HsProjecao) {
        try {
            this.HsProjecao = new BigDecimal(HsProjecao);
        } catch (Exception e) {
            this.HsProjecao = new BigDecimal("0");
        }
    }

    /**
     * @return the Faltas
     */
    public BigDecimal getFaltas() {
        return Faltas;
    }

    /**
     * @param Faltas the Faltas to set
     */
    public void setFaltas(String Faltas) {
        try {
            this.Faltas = new BigDecimal(Faltas);
        } catch (Exception e) {
            this.Faltas = new BigDecimal("0");
        }
    }

    /**
     * @return the FaltasJust
     */
    public BigDecimal getFaltasJust() {
        return FaltasJust;
    }

    /**
     * @param FaltasJust the FaltasJust to set
     */
    public void setFaltasJust(String FaltasJust) {
        try {
            this.FaltasJust = new BigDecimal(FaltasJust);
        } catch (Exception e) {
            this.FaltasJust = new BigDecimal("0");
        }
    }

    /**
     * @return the Reciclagem
     */
    public BigDecimal getReciclagem() {
        return Reciclagem;
    }

    /**
     * @param Reciclagem the Reciclagem to set
     */
    public void setReciclagem(String Reciclagem) {
        try {
            this.Reciclagem = new BigDecimal(Reciclagem);
        } catch (Exception e) {
            this.Reciclagem = new BigDecimal("0");
        }
    }

    /**
     * @return the Sindicato
     */
    public BigDecimal getSindicato() {
        return Sindicato;
    }

    /**
     * @param Sindicato the Sindicato to set
     */
    public void setSindicato(String Sindicato) {
        try {
            this.Sindicato = new BigDecimal(Sindicato);
        } catch (Exception e) {
            this.Sindicato = new BigDecimal("0");
        }
    }

    /**
     * @return the Transito
     */
    public BigDecimal getTransito() {
        return Transito;
    }

    /**
     * @param Transito the Transito to set
     */
    public void setTransito(String Transito) {
        try {
            this.Transito = new BigDecimal(Transito);
        } catch (Exception e) {
            this.Transito = new BigDecimal("0");
        }
    }

    /**
     * @return the HEInc
     */
    public BigDecimal getHEInc() {
        return HEInc;
    }

    /**
     * @param HEInc the HEInc to set
     */
    public void setHEInc(String HEInc) {
        try {
            this.HEInc = new BigDecimal(HEInc);
        } catch (Exception e) {
            this.HEInc = new BigDecimal("0");
        }
    }

    /**
     * @return the HorasExtr
     */
    public BigDecimal getHorasExtr() {
        return HorasExtr;
    }

    /**
     * @param HorasExtr the HorasExtr to set
     */
    public void setHorasExtr(String HorasExtr) {
        try {
            this.HorasExtr = new BigDecimal(HorasExtr);
        } catch (Exception e) {
            this.HorasExtr = new BigDecimal("0");
        }
    }

    /**
     * @return the HsAComp
     */
    public BigDecimal getHsAComp() {
        return HsAComp;
    }

    /**
     * @param HsAComp the HsAComp to set
     */
    public void setHsAComp(String HsAComp) {
        try {
            this.HsAComp = new BigDecimal(HsAComp);
        } catch (Exception e) {
            this.HsAComp = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasFolga
     */
    public BigDecimal getDiasFolga() {
        return DiasFolga;
    }

    /**
     * @param DiasFolga the DiasFolga to set
     */
    public void setDiasFolga(String DiasFolga) {
        try {
            this.DiasFolga = new BigDecimal(DiasFolga);
        } catch (Exception e) {
            this.DiasFolga = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasFerTrab
     */
    public BigDecimal getDiasFerTrab() {
        return DiasFerTrab;
    }

    /**
     * @param DiasFerTrab the DiasFerTrab to set
     */
    public void setDiasFerTrab(String DiasFerTrab) {
        try {
            this.DiasFerTrab = new BigDecimal(DiasFerTrab);
        } catch (Exception e) {
            this.DiasFerTrab = new BigDecimal("0");
        }
    }

    /**
     * @return the HEFeriado
     */
    public BigDecimal getHEFeriado() {
        return HEFeriado;
    }

    /**
     * @param HEFeriado the HEFeriado to set
     */
    public void setHEFeriado(String HEFeriado) {
        try {
            this.HEFeriado = new BigDecimal(HEFeriado);
        } catch (Exception e) {
            this.HEFeriado = new BigDecimal("0");
        }
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public String getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

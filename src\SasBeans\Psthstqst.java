/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Psthstqst {

    private String Sequencia;
    private BigDecimal Matr;
    private Integer QtdeFotos;
    private String CodQuestao;
    private String Resposta;
    private String Detalhes;
    private BigDecimal CodPessoa;
    private LocalDate Dt_Alter;
    private String Hr_alter;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public Integer getQtdeFotos() {
        return QtdeFotos;
    }

    public void setQtdeFotos(Integer QtdeFotos) {
        this.QtdeFotos = QtdeFotos;
    }

    public String getDetalhes() {
        return Detalhes;
    }

    public void setDetalhes(String Detalhes) {
        this.Detalhes = Detalhes;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("0");
        }
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getCodQuestao() {
        return CodQuestao;
    }

    public void setCodQuestao(String CodQuestao) {
        this.CodQuestao = CodQuestao;
    }

    public String getResposta() {
        return Resposta;
    }

    public void setResposta(String Resposta) {
        this.Resposta = Resposta;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.Sequencia);
        hash = 97 * hash + Objects.hashCode(this.Matr);
        hash = 97 * hash + Objects.hashCode(this.CodQuestao);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Psthstqst other = (Psthstqst) obj;
        if (!Objects.equals(this.Sequencia, other.Sequencia)) {
            return false;
        }
        if (!Objects.equals(this.CodQuestao, other.CodQuestao)) {
            return false;
        }
        if (!Objects.equals(this.Matr, other.Matr)) {
            return false;
        }
        return true;
    }

}

﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtRmnRPPS/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtRmnRPPS/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtRmnRPPS">
                    <xs:annotation>
                        <xs:documentation>Remuneracao de trabalhador nao vinculado ao RGPS</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveFopag">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmprPJ">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideTrabalhador">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do Trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="11"/>
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nisTrab" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>NIS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:maxLength value="11"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="qtdDepFP" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Qtde de dependentes para fins do Regime PrOprio</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d{1,2}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="procJudTrab" minOccurs="0" maxOccurs="99">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes sobre a existencia de processos judiciais do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpTrib">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Abrangencia pela decisao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrProcJud">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Nr Processo Judicial</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:length value="20"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codSusp" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:integer">
                                                                <xs:pattern value="\d{1,14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="dmDev" maxOccurs="99">
                                <xs:annotation>
                                    <xs:documentation>Demonstrativos de valores devidos ao trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ideDmDev">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Identificador do demonstrativo de pagamento</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="30"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="infoPerApur" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes relativas ao periodo de apuracao</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="ideEstab" maxOccurs="24">
                                                        <xs:annotation>
                                                            <xs:documentation>Identificacao da unidade do Orgao publico</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpInsc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="nrInsc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Numero de Inscricao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="\d{8,14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="remunPerApur" maxOccurs="10">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Remuneracao do Trabalhador no Periodo de Apuracao</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="matricula" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Matricula</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="30"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="codCateg">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo da Categoria do Trabalhador</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:integer">
                                                                                        <xs:pattern value="\d{3}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="itensRemun" type="TItemRemunRPPS" maxOccurs="200">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Itens da Remuneracao do Trabalhador</xs:documentation>
                                                                                </xs:annotation>
                                                                            </xs:element>
                                                                            <xs:element name="infoSaudeColet" type="TSaudeCol" minOccurs="0">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Informacoes de plano privado coletivo empresarial de assistencia à saude</xs:documentation>
                                                                                </xs:annotation>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoPerAnt" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Remuneracao relativa a Periodos Anteriores</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="ideADC" maxOccurs="8">
                                                        <xs:annotation>
                                                            <xs:documentation>Identificacao da lei que determinou reajuste retroativo</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="dtLei">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data da Lei</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="nrLei">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Numero da Lei</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="12"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtEf" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data dos efeitos</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="idePeriodo" maxOccurs="200">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Identificacao do periodo de referencia da remuneracao</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="perRef">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Periodo de referencia</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="ideEstab" maxOccurs="24">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Registro que identifica a unidade do Orgao publico</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="tpInsc">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:byte">
                                                                                                    <xs:pattern value="\d"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="nrInsc">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Numero de Inscricao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:pattern value="\d{8,14}"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="remunPerAnt" maxOccurs="10">
                                                                                            <xs:annotation>
                                                                                                <xs:documentation>Remuneracao do Trabalhador</xs:documentation>
                                                                                            </xs:annotation>
                                                                                            <xs:complexType>
                                                                                                <xs:sequence>
                                                                                                    <xs:element name="matricula" minOccurs="0">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Matricula</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:string">
                                                                                                                <xs:minLength value="1"/>
                                                                                                                <xs:maxLength value="30"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="codCateg">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>COdigo da Categoria do Trabalhador</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:integer">
                                                                                                                <xs:pattern value="\d{3}"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="itensRemun" type="TItemRemunRPPS" maxOccurs="200">
                                                                                                        <xs:annotation>
                                                                                                            <xs:documentation>Itens da Remuneracao do Trabalhador</xs:documentation>
                                                                                                        </xs:annotation>
                                                                                                    </xs:element>
                                                                                                </xs:sequence>
                                                                                            </xs:complexType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveFopag">
        <xs:annotation>
            <xs:documentation>Identificacao do evento periOdico</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indApuracao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="perApur">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmprPJ">
        <xs:annotation>
            <xs:documentation>Informacoes do Empregador PJ</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TItemRemunRPPS">
        <xs:annotation>
            <xs:documentation>Informacoes dos itens da remuneracao</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="codRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="ideTabRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Identificador da tabela de rubricas</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="8"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="qtdRubr" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="6"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="9999.99"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fatorRubr" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="5"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999.99"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vrUnit" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor Unitario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vrRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor total da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TSaudeCol">
        <xs:annotation>
            <xs:documentation>Planos de saude coletivo</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="detOper" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Detalhamento dos valores pagos a Operadoras de Planos de Saude</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="cnpjOper">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>CNPJ de Operadora do Plano de Saude</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="14"/>
                                    <xs:pattern value="\d{14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="regANS">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Registro da operadora na Agencia Nacional de Saude</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="6"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="vrPgTit">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Valor pago pelo Titular</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="14"/>
                                    <xs:fractionDigits value="2"/>
                                    <xs:maxInclusive value="999999999999"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="detPlano" minOccurs="0" maxOccurs="99">
                            <xs:annotation>
                                <xs:documentation>Informacoes do dependente do plano privado de saude</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="tpDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Tipo de dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:length value="2"/>
                                                <xs:pattern value="\d{2}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="cpfDep" minOccurs="0">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Numero de Inscricao no CPF</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:pattern value="\d{11}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nmDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Nome do Dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:minLength value="2"/>
                                                <xs:maxLength value="70"/>
                                                <xs:whiteSpace value="preserve"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="dtNascto">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Data de Nascimento</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:date">
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="vlrPgDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Valor pago relativo ao dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="14"/>
                                                <xs:fractionDigits value="2"/>
                                                <xs:maxInclusive value="999999999999"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

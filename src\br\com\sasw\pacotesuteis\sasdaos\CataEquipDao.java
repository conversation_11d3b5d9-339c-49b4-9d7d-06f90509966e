/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.CataEquip;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CataEquipDao {

    /**
     * Atualiza o status do cofre
     *
     * @param cataEquip
     * @param persistencia
     * @throws Exception
     */
    public void atualizarStatusEquipamento(CataEquip cataEquip, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE CataEquip SET Status = ?, Data = ?, Hora = ?, Envio = ?, Dt_Envio = ?, Hr_Envio = ? \n"
                    + " WHERE Serial = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cataEquip.getStatus());
            consulta.setString(cataEquip.getData());
            consulta.setString(cataEquip.getHora());
            consulta.setString(cataEquip.getEnvio());
            consulta.setString(cataEquip.getDt_Envio());
            consulta.setString(cataEquip.getHr_Envio());
            consulta.setString(cataEquip.getSerial());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CataEquipDao.atualizarStatusEquipamento - " + e.getMessage() + "\r\n"
                    + " UPDATE CataEquip SET Status = " + cataEquip.getStatus() + ", Data = " + cataEquip.getData() + ","
                    + " Hora = " + cataEquip.getHora() + ", Envio = " + cataEquip.getEnvio() + " \n"
                    + " WHERE Serial = " + cataEquip.getSerial() + " ");
        }
    }

    /**
     * Verifica existência do cofre antes de tentar inserir
     *
     * @param serial
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeEquipamento(String serial, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT TOP 1 * FROM CataEquip (nolock) WHERE Serial = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(serial);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataEquipDao.existeEquipamento - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 * FROM CataEquip WHERE Serial = " + serial);
        }
    }

    /**
     * Lista todos os equipamentos já cadastrados
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CataEquip> listaEquipamentos(Persistencia persistencia) throws Exception {
        try {
            List<CataEquip> retorno = new ArrayList<>();
            String sql = " SELECT email, codFil, nome, codigo, \n"
                    + "convert(varchar, CataEquip.Data, 112) DataC,\n"
                    + "convert(varchar, CataEquip.Dt_Envio, 112) Dt_EnvioC,\n"
                    + "CataEquip.* FROM CataEquip \n"
                    + "LEFT JOIN Clientes ON Clientes.CodPtoCli+CAST(Clientes.CodCofre AS Char) = CataEquip.Serial";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            CataEquip cataEquip;
            while (consulta.Proximo()) {
                cataEquip = new CataEquip();
                cataEquip.setSerial(consulta.getString("Serial"));
                cataEquip.setIDEquip(consulta.getString("IDEquip"));
                cataEquip.setGeo(consulta.getString("Geo"));
                cataEquip.setAddress(consulta.getString("Address"));
                cataEquip.setStatus(consulta.getString("Status"));
                cataEquip.setData(consulta.getString("DataC"));
                cataEquip.setHora(consulta.getString("Hora"));
                cataEquip.setEnvio(consulta.getString("Envio"));
                cataEquip.setEmail(consulta.getString("Email"));
                cataEquip.setCodFil(consulta.getString("codFil"));
                cataEquip.setNome(consulta.getString("nome"));
                cataEquip.setCodCli(consulta.getString("codigo"));
                cataEquip.setDt_Envio(consulta.getString("Dt_EnvioC"));
                cataEquip.setHr_Envio(consulta.getString("Hr_Envio"));
                retorno.add(cataEquip);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataEquipDao.listaEquipamentos - " + e.getMessage() + "\r\n"
                    + " SELECT email, codFil, nome, codigo, CataEquip.* FROM CataEquip \n"
                    + "LEFT JOIN Clientes ON Clientes.CodPtoCli+CAST(Clientes.CodCofre AS Char) = CataEquip.Serial");
        }
    }

    /**
     * Inserir equipamento no banco
     *
     * @param cataEquip
     * @param persistencia
     * @throws Exception
     */
    public void inserirEquipamento(CataEquip cataEquip, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO CataEquip (Serial, IDEquip, Geo, Address) VALUES(?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cataEquip.getSerial());
            consulta.setString(cataEquip.getIDEquip());
            consulta.setString(cataEquip.getGeo());
            consulta.setString(cataEquip.getAddress());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CataEquipDao.inserirEquipamento - " + e.getMessage() + "\r\n"
                    + " INSERT INTO CataEquip (Serial, IDEquip, Geo, Address) "
                    + " VALUES(" + cataEquip.getSerial() + "," + cataEquip.getIDEquip() + "," + cataEquip.getGeo() + "," + cataEquip.getAddress() + ")");
        }
    }

    /**
     * Inserir equipamento no banco
     *
     * @param cataEquip
     * @param persistencia
     * @throws Exception
     */
    public void inserirAtualizarEquipamento(CataEquip cataEquip, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE CataEquip\n"
                    + "SET Serial = ?,\n"
                    + "     IDEquip= ?,\n"
                    + "     Geo = ?,\n"
                    + "     Address = ?\n"
                    + "WHERE Serial = ?\n"
                    + "\n"
                    + "INSERT INTO CataEquip (Serial, IDEquip, Geo, Address)\n"
                    + "SELECT TOP 1 \n"
                    + "?, ?, ?, ? \n"
                    + "FROM (SELECT\n"
                    + "      COUNT(*) AS qtde_cadastrado \n"
                    + "      FROM CataEquip\n"
                    + "      WHERE Serial = ?) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0;";
            Consulta consulta = new Consulta(sql, persistencia);
            // UPDATE
            consulta.setString(cataEquip.getSerial());
            consulta.setString(cataEquip.getIDEquip());
            consulta.setString(cataEquip.getGeo());
            consulta.setString(cataEquip.getAddress());
            // WHERE UPDATE
            consulta.setString(cataEquip.getSerial());
            // INSERT 
            consulta.setString(cataEquip.getSerial());
            consulta.setString(cataEquip.getIDEquip());
            consulta.setString(cataEquip.getGeo());
            consulta.setString(cataEquip.getAddress());
            // WHERE INSERT
            consulta.setString(cataEquip.getSerial());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CataEquipDao.inserirAtualizarEquipamento - " + e.getMessage() + "\r\n"
                    + " UPDATE CataEquip\n"
                    + "SET Serial = " + cataEquip.getSerial() + ",\n"
                    + "     IDEquip= " + cataEquip.getIDEquip() + ",\n"
                    + "     Geo = " + cataEquip.getGeo() + ",\n"
                    + "     Address = " + cataEquip.getAddress() + "\n"
                    + "WHERE Serial = " + cataEquip.getSerial() + "\n"
                    + "\n"
                    + "INSERT INTO CataEquip (Serial, IDEquip, Geo, Address)\n"
                    + "SELECT TOP 1 \n"
                    + cataEquip.getSerial() + "," + cataEquip.getIDEquip() + "," + cataEquip.getGeo() + "," + cataEquip.getAddress() + "\n"
                    + "FROM (SELECT\n"
                    + "      COUNT(*) AS qtde_cadastrado \n"
                    + "      FROM CataEquip\n"
                    + "      WHERE Serial = " + cataEquip.getSerial() + ") AS A\n"
                    + "WHERE A.qtde_cadastrado = 0;");
        }
    }

    public void registroComunicacao(String idEquip, String data, String hora, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CataEquip set Data = ?, Hora = ?, Status = 1 where idEquip = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(hora);
            consulta.setString(idEquip);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CataEquipDao.registroComunicacao - " + e.getMessage());
        }
    }

    public void registroComunicacaoSerial(String Serial, String data, String hora, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CataEquip set Data = ?, Hora = ?, Status = 1 where Serial = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(hora);
            consulta.setString(Serial);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CataEquipDao.registroComunicacaoSerial - " + e.getMessage() + "\r\n"
                    + "Update CataEquip set Data = " + data + ", Hora = " + hora + ", Status = 1 where Serial = " + Serial);
        }
    }

    public void verificaStatus(String status, String limite, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CataEquip set Status = ? where DateDiff(Mi, Convert(Datetime,Convert(Varchar,Data,110)+' '+Hora),Getdate()) > ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(status);
            consulta.setString(limite);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CataEquipDao.verificaStatus - " + e.getMessage());
        }
    }

    public void atulizaDataConferenciaTransacoes(String idEquip, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CataEquip set Dt_Envio = Convert(Varchar,getdate(),112), Hr_Envio = Substring(Convert(Varchar,getdate(),114),1,5) where idEquip = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(idEquip);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CataEquipDao.atulizaDataConferenciaTransacoes - " + e.getMessage());
        }
    }
}

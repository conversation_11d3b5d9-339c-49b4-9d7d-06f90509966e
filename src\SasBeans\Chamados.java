/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

public class Chamados {

    private BigDecimal Sequencia;
    private String Descricao;
    private String Detalhes;
    private BigDecimal CodPessoa;
    private BigDecimal CodResponsavel;
    private BigDecimal Situacao;
    private BigDecimal Tipo;
    private String Empresa;
    private String Dt_Abertura;
    private String Hr_Abertura;
    private String Dt_Fecha;
    private String Hr_Fecha;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getDetalhes() {
        return Detalhes;
    }

    public void setDetalhes(String Detalhes) {
        this.Detalhes = Detalhes;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public BigDecimal getCodResponsavel() {
        return CodResponsavel;
    }

    public void setCodResponsavel(BigDecimal CodResponsavel) {
        this.CodResponsavel = CodResponsavel;
    }

    public BigDecimal getSituacao() {
        return Situacao;
    }

    public void setSituacao(BigDecimal Situacao) {
        this.Situacao = Situacao;
    }

    public BigDecimal getTipo() {
        return Tipo;
    }

    public void setTipo(BigDecimal Tipo) {
        this.Tipo = Tipo;
    }

    public String getEmpresa() {
        return Empresa;
    }

    public void setEmpresa(String Empresa) {
        this.Empresa = Empresa;
    }

    public String getDt_Abertura() {
        return Dt_Abertura;
    }

    public void setDt_Abertura(String Dt_Abertura) {
        this.Dt_Abertura = Dt_Abertura;
    }

    public String getHr_Abertura() {
        return Hr_Abertura;
    }

    public void setHr_Abertura(String Hr_Abertura) {
        this.Hr_Abertura = Hr_Abertura;
    }

    public String getDt_Fecha() {
        return Dt_Fecha;
    }

    public void setDt_Fecha(String Dt_Fecha) {
        this.Dt_Fecha = Dt_Fecha;
    }

    public String getHr_Fecha() {
        return Hr_Fecha;
    }

    public void setHr_Fecha(String Hr_Fecha) {
        this.Hr_Fecha = Hr_Fecha;
    }

}

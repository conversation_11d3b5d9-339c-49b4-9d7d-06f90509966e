/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class GTV {

    private BigDecimal CodFil;
    private BigDecimal Guia;
    private String Serie;
    private BigDecimal Pedido;
    private BigDecimal SeqRota;
    private BigDecimal Parada;
    private String DtGeracao;
    private BigDecimal OS;
    private String Situacao;
    private String Status;
    private String DtSituacao;
    private String Obs;
    private String Operador;
    private String Dt_alter;
    private String Hr_Alter;
    private String OperBaixa;
    private String Dt_Baixa;
    private String Hr_Baixa;

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Guia
     */
    public BigDecimal getGuia() {
        return Guia;
    }

    /**
     * @param Guia the Guia to set
     */
    public void setGuia(String Guia) {
        try {
            this.Guia = new BigDecimal(Guia);
        } catch (Exception e) {
            this.Guia = new BigDecimal("0");
        }
    }

    /**
     * @return the Serie
     */
    public String getSerie() {
        return Serie;
    }

    /**
     * @param Serie the Serie to set
     */
    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    /**
     * @return the Pedido
     */
    public BigDecimal getPedido() {
        return Pedido;
    }

    /**
     * @param Pedido the Pedido to set
     */
    public void setPedido(String Pedido) {
        try {
            this.Pedido = new BigDecimal(Pedido);
        } catch (Exception e) {
            this.Pedido = new BigDecimal("0");
        }
    }

    /**
     * @return the SeqRota
     */
    public BigDecimal getSeqRota() {
        return SeqRota;
    }

    /**
     * @param SeqRota the SeqRota to set
     */
    public void setSeqRota(String SeqRota) {
        try {
            this.SeqRota = new BigDecimal(SeqRota);
        } catch (Exception e) {
            this.SeqRota = new BigDecimal("0");
        }
    }

    /**
     * @return the Parada
     */
    public BigDecimal getParada() {
        return Parada;
    }

    /**
     * @param Parada the Parada to set
     */
    public void setParada(String Parada) {
        try {
            this.Parada = new BigDecimal(Parada);
        } catch (Exception e) {
            this.Parada = new BigDecimal("0");
        }
    }

    /**
     * @return the DtGeracao
     */
    public String getDtGeracao() {
        return DtGeracao;
    }

    /**
     * @param DtGeracao the DtGeracao to set
     */
    public void setDtGeracao(String DtGeracao) {
        this.DtGeracao = DtGeracao;
    }

    /**
     * @return the OS
     */
    public BigDecimal getOS() {
        return OS;
    }

    /**
     * @param OS the OS to set
     */
    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    /**
     * @return the Situacao
     */
    public String getSituacao() {
        return Situacao;
    }

    /**
     * @param Situacao the Situacao to set
     */
    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    /**
     * @return the Status
     */
    public String getStatus() {
        return Status;
    }

    /**
     * @param Status the Status to set
     */
    public void setStatus(String Status) {
        this.Status = Status;
    }

    /**
     * @return the DtSituacao
     */
    public String getDtSituacao() {
        return DtSituacao;
    }

    /**
     * @param DtSituacao the DtSituacao to set
     */
    public void setDtSituacao(String DtSituacao) {
        this.DtSituacao = DtSituacao;
    }

    /**
     * @return the Obs
     */
    public String getObs() {
        return Obs;
    }

    /**
     * @param Obs the Obs to set
     */
    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_alter
     */
    public String getDt_alter() {
        return Dt_alter;
    }

    /**
     * @param Dt_alter the Dt_alter to set
     */
    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    /**
     * @return the OperBaixa
     */
    public String getOperBaixa() {
        return OperBaixa;
    }

    /**
     * @param OperBaixa the OperBaixa to set
     */
    public void setOperBaixa(String OperBaixa) {
        this.OperBaixa = OperBaixa;
    }

    /**
     * @return the Dt_Baixa
     */
    public String getDt_Baixa() {
        return Dt_Baixa;
    }

    /**
     * @param Dt_Baixa the Dt_Baixa to set
     */
    public void setDt_Baixa(String Dt_Baixa) {
        this.Dt_Baixa = Dt_Baixa;
    }

    /**
     * @return the Hr_Baixa
     */
    public String getHr_Baixa() {
        return Hr_Baixa;
    }

    /**
     * @param Hr_Baixa the Hr_Baixa to set
     */
    public void setHr_Baixa(String Hr_Baixa) {
        this.Hr_Baixa = Hr_Baixa;
    }

    @Override
    public String toString() {
        return "GTV{" + "CodFil=" + CodFil + ", Guia=" + Guia + ", Serie=" + Serie + '}';
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.CodFil);
        hash = 59 * hash + Objects.hashCode(this.Guia);
        hash = 59 * hash + Objects.hashCode(this.Serie);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final GTV other = (GTV) obj;
        if (!Objects.equals(this.Serie, other.Serie)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        if (!Objects.equals(this.Guia, other.Guia)) {
            return false;
        }
        return true;
    }
    
    
}

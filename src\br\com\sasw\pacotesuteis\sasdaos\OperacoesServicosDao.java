/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.OperacoesServicos;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class OperacoesServicosDao {

    public List<OperacoesServicos> grideMovimentacaoOperacional(String dataInicio, String dataFim, String codFil, Boolean todasFiliais, Boolean excluidos, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<OperacoesServicos> Retorno = new ArrayList<>();
            OperacoesServicos operacoesServicos;

            sql = "Select CtrOperV.Numero, CtrOperv.CodFil, CtrOperV.Data, Filiais.Descricao DescrFilial, \n"
                    + " CtrOperV.FuncSubs, CtrOperV.Motivo_Subs, CtrOperV.Hora_Extra, \n"
                    + " CtrOperV.VR, CtrOperV.VT, CtrOperV.Hospedagem, CtrOperV.Hora1, CtrOperV.Hora4, CtrOperV.HEDiurna, CtrOperV.HENoturna, CtrOperV.Nro_HE, \n"
                    + " CtrOperV.Motivo_Aus, CtrOperV.FuncAus, CtrOperV.Mesario, CtrOperV.Operador, CtrOperV.Dt_Alter, \n"
                    + " CtrOperV.Hr_Alter, CtrOperV.Periodo, CtrOperV.Posto, CtrOperV.RefTipo2, CtrOperV.NumCalculo,  CtrOperv.Intraj,\n"
                    + " F1.Nome NomeAus, F2.Nome NomeSubs, CtrOperV.CodSrv, \n"
                    + " PstServ.Local LocalPosto, Substring(CtrOperv.OBS,1,100) OBS, CtrOperv.Pedido, PstServ.Regional, Regional.Descricao DescRegiao, CtrOperv.Flag_Excl \n"
                    + "  from CtrOperV\n"
                    + "  Left Join Filiais    on  (CtrOperV.CodFil   = Filiais.CodFil   ) \n"
                    + "  Left Join Funcion F1 on  (CtrOperV.FuncAus  = F1.Matr          ) \n"
                    + "  Left Join Funcion F2 on  (CtrOperV.FuncSubs = F2.Matr          ) \n"
                    + "  Left Join PstServ    on  ((CtrOperV.Posto   = PstServ.Secao    ) \n"
                    + "                       and  (CtrOperV.CodFil  = PstServ.CodFil   ))\n"
                    + "  Left Join Regional   on  (Regional.Codigo   = PstServ.Regional ) \n"
                    + "  where CtrOperv.Numero > 0 \n";
            if (!excluidos) {
                sql += "    and CtrOperV.Flag_Excl <> '*'  \n";
            }
            if (!todasFiliais) {
                sql += "    and CtrOperV.CodFil = ?   \n";
            }
            sql += "    and CtrOperV.Data <= ?\n"
                    + "    and CtrOperV.Data >= ?"
                    + " ORDER BY CtrOperV.Data DESC, CtrOperV.Numero";

            Consulta consulta = new Consulta(sql, persistencia);

            if (!todasFiliais) {
                consulta.setString(codFil);
            }
            consulta.setString(dataFim);
            consulta.setString(dataInicio);

            consulta.select();

            while (consulta.Proximo()) {
                operacoesServicos = new OperacoesServicos();

                operacoesServicos.setCodFil(consulta.getString("CodFil"));
                operacoesServicos.setCodSrv(consulta.getString("CodSrv"));
                operacoesServicos.setData(consulta.getString("Data"));
                operacoesServicos.setDescRegiao(consulta.getString("DescRegiao"));
                operacoesServicos.setDescrFilial(consulta.getString("DescrFilial"));
                operacoesServicos.setDt_Alter(consulta.getString("Dt_Alter"));
                operacoesServicos.setFlag_Excl(consulta.getString("Flag_Excl"));
                operacoesServicos.setFuncAus(consulta.getString("FuncAus"));
                operacoesServicos.setFuncSubs(consulta.getString("FuncSubs"));
                operacoesServicos.setHEDiurna(consulta.getString("HEDiurna"));
                operacoesServicos.setHENoturna(consulta.getString("HENoturna"));
                operacoesServicos.setHora1(consulta.getString("Hora1"));
                operacoesServicos.setHora4(consulta.getString("Hora4"));
                operacoesServicos.setHora_Extra(consulta.getString("Hora_Extra"));
                operacoesServicos.setHospedagem(consulta.getString("Hospedagem"));
                operacoesServicos.setHr_Alter(consulta.getString("Hr_Alter"));
                operacoesServicos.setIntraj(consulta.getString("Intraj"));
                operacoesServicos.setLocalPosto(consulta.getString("LocalPosto"));
                operacoesServicos.setMesario(consulta.getString("Mesario"));
                operacoesServicos.setMotivo_Aus(consulta.getString("Motivo_Aus"));
                operacoesServicos.setMotivo_Subs(consulta.getString("Motivo_Subs"));
                operacoesServicos.setOcorrencia(consulta.getString("Motivo_Subs"));
                operacoesServicos.setNro_HE(consulta.getString("Nro_HE"));
                operacoesServicos.setNumCalculo(consulta.getString("NumCalculo"));
                operacoesServicos.setNomeSubs(consulta.getString("NomeSubs"));
                operacoesServicos.setNomeAus(consulta.getString("NomeAus"));
                operacoesServicos.setNumero(consulta.getString("Numero"));
                operacoesServicos.setOBS(consulta.getString("OBS"));
                operacoesServicos.setOperador(consulta.getString("Operador"));
                operacoesServicos.setPedido(consulta.getString("Pedido"));
                operacoesServicos.setPeriodo(consulta.getString("Periodo"));
                operacoesServicos.setPosto(consulta.getString("Posto"));
                operacoesServicos.setRefTipo2(consulta.getString("RefTipo2"));
                operacoesServicos.setRegional(consulta.getString("Regional"));
                operacoesServicos.setVR(consulta.getString("VR"));
                operacoesServicos.setVT(consulta.getString("VT"));

                Retorno.add(operacoesServicos);
            }

            return Retorno;
        } catch (Exception e) {
            throw new Exception("OperacoesServicosDao.grideMovimentacaoOperacional - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public boolean validarDataMovimentacao(String data, String dataAtual, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            boolean retorno = false;

            sql = "Select DtFechaPto, * from FPPeriodos\n"
                    + " WHERE DtInicioP  <= ?\n"
                    + " AND   DtFinalP   >= ?\n"
                    + " AND   DtFechaPto > ?";
            
            Consulta consulta = new Consulta(sql, persistencia);
            
            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(dataAtual);

            consulta.select();
            
            while(consulta.Proximo()){
                retorno = true;
            }
            
            consulta.close();
            
            return retorno;
        } catch (Exception e) {
            throw new Exception("OperacoesServicosDao.grideMovimentacaoOperacional - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void gravarMovimentacao(OperacoesServicos operacoesServicos, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            if (operacoesServicos.getNumero().equals("0")) {
                // INSERT
                sql = "INSERT INTO CtrOperV (Numero,\n"
                        + "                      CodFil,\n"
                        + "                      Data,\n"
                        + "                      Periodo,\n"
                        + "                      Posto,\n"
                        + "                      Mesario,\n"
                        + "                      FuncAus,\n"
                        + "                      Motivo_Aus,\n"
                        + "                      FuncSubs,\n"
                        + "                      Motivo_Subs,\n"
                        + "                      Hora_Extra,\n"
                        + "                      VR,\n"
                        + "                      VT,\n"
                        + "                      Hospedagem,\n"
                        + "                      Hora1,\n"
                        + "                      Hora4,\n"
                        + "                      HEDiurna,\n"
                        + "                      HENoturna,\n"
                        + "                      Nro_HE,\n"
                        + "                      RefTipo2,\n"
                        + "                      CodSrv,\n"
                        + "                      Notas,\n"
                        + "                      Obs,\n"
                        + "                      Pedido,\n"
                        + "                      Intraj,\n"
                        + "                      Operador,\n"
                        + "                      Dt_Alter,\n"
                        + "                      Hr_Alter,\n"
                        + "                      Flag_Excl) VALUES (\n"
                        + "           (SELECT ISNULL(MAX(Numero) + 1,1) FROM CtrOperV WHERE CodFil = ?),\n"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?,"
                        + "           ?"
                        + ")";

                Consulta consulta = new Consulta(sql, persistencia);

                consulta.setString(operacoesServicos.getCodFil());
                consulta.setString(operacoesServicos.getCodFil());
                consulta.setString(operacoesServicos.getData());
                consulta.setString(operacoesServicos.getPeriodo());
                consulta.setString(operacoesServicos.getPosto());
                consulta.setString(operacoesServicos.getMesario());
                consulta.setString(operacoesServicos.getFuncAus());
                consulta.setString(operacoesServicos.getMotivo_Aus());
                consulta.setString(operacoesServicos.getFuncSubs());
                consulta.setString(operacoesServicos.getOcorrencia());
                consulta.setString(operacoesServicos.getHora_Extra());
                consulta.setString(operacoesServicos.getVR());
                consulta.setString(operacoesServicos.getVT());
                consulta.setString(operacoesServicos.getHospedagem());
                consulta.setString(operacoesServicos.getHora1());
                consulta.setString(operacoesServicos.getHora4());
                consulta.setString(operacoesServicos.getHEDiurna());
                consulta.setString(operacoesServicos.getHENoturna());
                consulta.setString(operacoesServicos.getNro_HE());
                if (operacoesServicos.isPagarTipo2()) {
                    consulta.setString("S");
                } else {
                    consulta.setString("N");
                }
                consulta.setString(operacoesServicos.getCodSrv());
                consulta.setString("");
                consulta.setString(operacoesServicos.getOBS());
                consulta.setString(operacoesServicos.getPedido());
                consulta.setString(operacoesServicos.getIntraj());
                consulta.setString(operacoesServicos.getOperador());
                consulta.setString(operacoesServicos.getDt_Alter());
                consulta.setString(operacoesServicos.getHr_Alter());
                consulta.setString("");

                consulta.insert();
                consulta.close();
            } else {
                // UPDATE
                sql = "UPDATE CtrOperV SET Data = ?,\n"
                        + "                 Periodo = ?,\n"
                        + "                 Posto = ?,\n"
                        + "                 Mesario = ?,\n"
                        + "                 FuncAus = ?,\n"
                        + "                 Motivo_Aus = ?,\n"
                        + "                 FuncSubs = ?,\n"
                        + "                 Motivo_Subs = ?,\n"
                        + "                 Hora_Extra = ?,\n"
                        + "                 VR = ?,\n"
                        + "                 VT = ?,\n"
                        + "                 Hospedagem = ?,\n"
                        + "                 Hora1 = ?,\n"
                        + "                 Hora4 = ?,\n"
                        + "                 HEDiurna = ?,\n"
                        + "                 HENoturna = ?,\n"
                        + "                 Nro_HE = ?,\n"
                        + "                 RefTipo2 = ?,\n"
                        + "                 CodSrv = ?,\n"
                        + "                 Notas = ?,\n"
                        + "                 Obs = ?,\n"
                        + "                 Pedido = ?,\n"
                        + "                 Intraj = ?,\n"
                        + "                 Operador = ?,\n"
                        + "                 Dt_Alter = ?,\n"
                        + "                 Hr_Alter = ?\n"
                        + " WHERE Numero = ? AND CodFil = ?";

                Consulta consulta = new Consulta(sql, persistencia);

                consulta.setString(operacoesServicos.getData());
                consulta.setString(operacoesServicos.getPeriodo());
                consulta.setString(operacoesServicos.getPosto());
                consulta.setString(operacoesServicos.getMesario());
                consulta.setString(operacoesServicos.getFuncAus());
                consulta.setString(operacoesServicos.getMotivo_Aus());
                consulta.setString(operacoesServicos.getFuncSubs());
                consulta.setString(operacoesServicos.getMotivo_Subs());
                consulta.setString(operacoesServicos.getHora_Extra());
                consulta.setString(operacoesServicos.getVR());
                consulta.setString(operacoesServicos.getVT());
                consulta.setString(operacoesServicos.getHospedagem());
                consulta.setString(operacoesServicos.getHora1());
                consulta.setString(operacoesServicos.getHora4());
                consulta.setString(operacoesServicos.getHEDiurna());
                consulta.setString(operacoesServicos.getHENoturna());
                consulta.setString(operacoesServicos.getNro_HE());
                if (operacoesServicos.isPagarTipo2()) {
                    consulta.setString("S");
                } else {
                    consulta.setString("N");
                }
                consulta.setString(operacoesServicos.getCodSrv());
                consulta.setString("");
                consulta.setString(operacoesServicos.getOBS());
                consulta.setString(operacoesServicos.getPedido());
                consulta.setString(operacoesServicos.getIntraj());
                consulta.setString(operacoesServicos.getOperador());
                consulta.setString(operacoesServicos.getDt_Alter());
                consulta.setString(operacoesServicos.getHr_Alter());
                consulta.setString(operacoesServicos.getNumero());
                consulta.setString(operacoesServicos.getCodFil());

                consulta.update();
                consulta.close();
            }
        } catch (Exception e) {
            throw new Exception("OperacoesServicosDao.gravarMovimentacao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void excluirMovimentacao(OperacoesServicos operacoesServicos, Persistencia persistencia) throws Exception {
        String sql = "";
        try {

            // UPDATE
            sql = "Update CtrOperV Set Oper_Excl = ?,\n"
                    + "       Dt_Excl   = ?,\n"
                    + "       Hr_Excl   = ?,\n"
                    + "       Flag_Excl = '*'\n"
                    + "       where Numero = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(operacoesServicos.getOperador());
            consulta.setString(operacoesServicos.getDt_Alter());
            consulta.setString(operacoesServicos.getHr_Alter());
            consulta.setString(operacoesServicos.getNumero());

            consulta.update();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("OperacoesServicosDao.excluirMovimentacao - " + e.getMessage() + "\r\n" + sql);
        }
    }
}

package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class ChamadosMov {

    private BigDecimal Sequencia;
    private BigDecimal Ordem;
    private BigDecimal Situacao;
    private BigDecimal Tipo;
    private BigDecimal CodResponsavel;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Dt_Inicio;
    private String Hr_Inicio;
    private String Dt_Fim;
    private String Hr_Fim;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public BigDecimal getOrdem() {
        return Ordem;
    }

    public void setOrdem(BigDecimal Ordem) {
        this.Ordem = Ordem;
    }

    public BigDecimal getSituacao() {
        return Situacao;
    }

    public void setSituacao(BigDecimal Situacao) {
        this.Situacao = Situacao;
    }

    public BigDecimal getTipo() {
        return Tipo;
    }

    public void setTipo(BigDecimal Tipo) {
        this.Tipo = Tipo;
    }

    public BigDecimal getCodResponsavel() {
        return CodResponsavel;
    }

    public void setCodResponsavel(BigDecimal CodResponsavel) {
        this.CodResponsavel = CodResponsavel;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getDt_Inicio() {
        return Dt_Inicio;
    }

    public void setDt_Inicio(String Dt_Inicio) {
        this.Dt_Inicio = Dt_Inicio;
    }

    public String getHr_Inicio() {
        return Hr_Inicio;
    }

    public void setHr_Inicio(String Hr_Inicio) {
        this.Hr_Inicio = Hr_Inicio;
    }

    public String getDt_Fim() {
        return Dt_Fim;
    }

    public void setDt_Fim(String Dt_Fim) {
        this.Dt_Fim = Dt_Fim;
    }

    public String getHr_Fim() {
        return Hr_Fim;
    }

    public void setHr_Fim(String Hr_Fim) {
        this.Hr_Fim = Hr_Fim;
    }

}

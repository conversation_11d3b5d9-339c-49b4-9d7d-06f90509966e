/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Tesouraria;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.TesSaidas;
import SasBeans.TesSaidasDN;
import br.com.sasw.pacotesuteis.sasbeans.TesSaidasMD;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.Funcionarios;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class TesourariaSaidaController {

    private final Persistencia persistencia;

    public TesourariaSaidaController(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public void processarGuias(String CodFil, String DataInicio, String DataFim, String Operador, String Dt_Alter, String Hr_Alter) throws Exception {
        String sql = "";

        try {
            sql = "Declare @codfil                float;\n"
                    + " Declare @data             Date;\n"
                    + " Declare @data1             Date;\n"
                    + " Declare @data2             Date;\n"
                    + " Declare @pedido           int;\n"
                    + " Declare @codcli1          Varchar(7);\n"
                    + " Declare @codcli2          Varchar(7);\n"
                    + " Declare @codtes           Varchar(7);\n"
                    + " Declare @nredtes          Varchar(7);\n"
                    + " Declare @clifat           Varchar(7);\n"
                    + " Declare @codconta         Float;\n"
                    + " Declare @codlote          Varchar(3);\n"
                    + " Declare @os               int;\n"
                    + " Declare @pedidovalor      Float;\n"
                    + " Declare @serieGtv         Varchar(3);\n"
                    + " Declare @gtv              float;\n"
                    + " Declare @tiposrv          Varchar(3);\n"
                    + " Declare @obs              Varchar(80);\n"
                    + " Declare @operador         Varchar(10);\n"
                    + " Declare @dtalter          date;\n"
                    + " Declare @hralter          Varchar(5);\n"
                    + " Declare @lacre            Varchar(8);\n"
                    + " Declare @tesexiste        int;\n"
                    + " Declare @seqrota          float;\n"
                    + " Declare @rota             float;\n"
                    + " Declare @rotaant          float;\n"
                    + " Declare @hora1            varchar(5);\n"
                    + " Declare @seqparada        int;\n"
                    + " Declare @cxforte          varchar(07);\n"
                    + " Declare @cxforteNred      Varchar(20);\n"
                    + " Declare @cxforteRegiao    Varchar(03);\n"
                    + " Declare @horaCxfManha     Varchar(05);\n"
                    + " Declare @horaCxfNoite     Varchar(05);\n"
                    + " Declare @seqParadaOri     int;\n"
                    + " Declare @seqParadaDst     int;\n"
                    + " Declare @acrescimoMinutos int;\n"
                    + " Declare @seqParadaCxf     int;\n"
                    + " Declare @cxfExiste        int;\n"
                    + " Declare @observ           Varchar(20);\n"
                    + " Declare @valordn          float;\n"
                    + " Declare @valormd          float;\n"
                    + " Declare @valortotal       float;\n"
                    + " Declare @codsrv           Varchar(03);\n"
                    + " Declare @qtde             Float;\n"
                    + " Declare @volumes          Float;\n"
                    + " Declare @valor            Float;\n"
                    + " Declare @codremessa       VarChar(10);\n"
                    + " Declare @remessa          Float;\n"
                    /*-------------------------------------------------
                      --Parametros-------------------------------------*/
                    + " Set @codfil           = ? ;\n"
                    + " Set @data1             = ? ;\n"
                    + " Set @data2             = ? ;\n"
                    + " Set @operador         = ? ;\n"
                    + " Set @dtalter          = ? ;\n"
                    + " Set @hralter          = ? ;\n"
                    + " set @horaCxfManha     = '0800';\n"
                    + " set @horaCxfNoite     = '1900';\n"
                    + " set @acrescimoMinutos = 10;\n"
                    + " set @observ           = '';\n"
                    /*-------------------------------------------------"
                      --Buscando dados CxForte*/
                    + " Select top 1 \n"
                    + " @cxforte = Clientes.Codigo,\n"
                    + " @cxforteNred = Clientes.Nred,\n"
                    + " @cxforteRegiao = Clientes.Regiao\n"
                    + " from CxForte \n"
                    + " Left join Clientes  on Clientes.Codigo = CxForte.CodCli\n"
                    + "					and Clientes.CodFil = CxForte.CodFil\n"
                    + " where CxForte.CodFil = @codfil \n"
                    + " Order By DtFecha Desc;\n"
                    /*-------------------------------------------------
                     --Buscando pedidos e gerando Cxforte entrada-----*/
                    + " Select \n"
                    + " Pedido.Numero, Pedido.CodFil, Pedido.Data, Pedido.CodCli1, Pedido.CodCli2, Pedido.OS, Pedido.Valor, Pedido.ClassifSRV, Pedido.OBS, TesSaidas.Guia, TesSaidas.Serie \n"
                    + " into #pedidos\n"
                    + " From Pedido \n"
                    + " Inner join TesSaidas  on TesSaidas.Pedido = Pedido.Numero\n"
                    + "                     and TesSaidas.CodFil = Pedido.CodFil\n"
                    + " where Pedido.data Between @data1 and @data2\n"
                    + "   and Pedido.CodFil = @codfil\n"
                    + "   and Pedido.CodCli1 = @cxforte\n"
                    + "   and TesSaidas.Guia in (Select Guia from TesSaidasLacres (Nolock) where TesSaidasLacres.Serie = TesSaidas.Serie)\n"
                    + "   and TesSaidas.Guia not in (Select Guia from CxfGuias (Nolock)  where CxfGuias.Serie = TesSaidas.Serie)\n"
                    + "   and Flag_Excl <> '*'\n"
                    + "   and Tipo = 'T'\n"
                    + "   and OS > 0;\n"
                    + " WHILE (SELECT count(*) FROM #pedidos) > 0\n"
                    + " BEGIN \n"
                    + "	Select top 01 \n"
                    + "	@pedido = Numero,\n"
                    + "	@codcli1 = CodCli1,\n"
                    + "	@codcli2 = CodCli2,\n"
                    + "	@os = OS,\n"
                    + "	@tiposrv = ClassifSRV,\n"
                    + "	@pedidovalor = Valor,\n"
                    + "	@obs = OBS, \n"
                    + " @data = Data, \n"
                    + " @gtv =  Guia, \n"
                    + " @serieGtv = Serie\n"
                    + "	from #pedidos;\n"
                    + "	Select\n"
                    + "	@codtes = OS_Vig.CliDst,\n"
                    + "	@nredtes = OS_Vig.NRedDst,\n"
                    + "	@codconta = OS_VTes.ContaPdr,\n"
                    + "    @codlote = LotePdr,\n"
                    + "	@clifat = OS_Vig.CliFat\n"
                    + "	from OS_Vig\n"
                    + "	Left join OS_VTes  on OS_Vig.OS = OS_VTes.OS\n"
                    + "	                  and OS_Vig.CodFil = OS_VTes.CodFil\n"
                    + "	where OS_Vig.OS = @os;\n"
                    /*+ "	--Verificando se clientes destino da OS é tesouraria\n"*/
                    + "	if(Substring(@codtes,4,1) = 7) begin\n"
                    /*+ "		--Gerando Entrada CXF		\n"
                    + "		----Verificando se existe rota 090\n"*/
                    + "		Select @seqrota = Sequencia from Rotas where Data = @data and CodFil = @codfil and Rota = '090' and Flag_Excl <> '*';\n"
                    + "		if((@seqrota <= 0) or (@seqrota is null)) begin			\n"
                    + "			Insert into Rotas (Sequencia, Rota, Data, CodFil, TpVeic, Viagem, ATM, Bacen, Aeroporto, HrLargada, HrChegada, HrIntIni, HrIntFim, \n"
                    + "			HsTotal, DtFim, Operador, Dt_Alter, Hr_Alter, Flag_Excl)\n"
                    + "			Select Max(Isnull(Sequencia,0))+1, '090', @data, @codfil, 'N', 'N', 'N', 'N', 'N', '08:00', '18:00', '12:00', '13:00', 8, @data,\n"
                    + "			@operador, @dtalter, @hralter, '' from Rotas;\n"
                    + "			\n"
                    + "			Select @seqrota = Sequencia from Rotas where Data = @data and CodFil = @codfil and Rota = '090' and Flag_Excl <> '*';			\n"
                    + "		end\n"
                    + "		\n"
                    /*+ "		--Verificando existencia Caixa-Forte Manha\n"*/
                    + "		if((Select Isnull(Count(*),0) from Rt_Perc where Sequencia = @seqrota and CodCli1 = @cxforte and Hora1 >= '0800' and Hora1 <= '1100' and Flag_Excl <> '*') <= 0) begin\n"
                    /*+ "		   --Buscando Hora1 Disponivel\n"*/
                    + "			WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @horaCxfManha and Flag_Excl <> '*') > 0 BEGIN      \n"
                    /*+ "				--Caso já exista serviço para outro cliente no horário adiciona @acrescimoMinutos\n"*/
                    + "				set @horaCxfManha = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@horaCxfManha,1,2)+':'+Substring(@horaCxfManha,3,2))),113),1,5),':','');	  \n"
                    + "				IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @horaCxfManha and Flag_Excl <> '*') = 0\n"
                    + "					BREAK  \n"
                    + "			ELSE  \n"
                    + "				CONTINUE  \n"
                    + "			END\n"
                    + "			Select @seqParadaCxf = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota;					\n"
                    + "			Insert into Rt_Perc (Sequencia, Hora1, Parada, CodCli1, Nred, Regiao, ER, TipoSrv, CodFil, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ) \n"
                    + "			Values (@seqrota, @horaCxfManha, @seqParadaCxf, @cxforte, @cxforteNred, @cxforteRegiao, 'E', 'R', @codfil, @operador,@dtalter,@hralter, '', '', '',@observ);	\n"
                    + "		end\n"
                    /*+ "		--Verificando existencia Caixa-Forte Noite\n"*/
                    + "		Select @cxfExiste = Isnull(Count(*),0) from Rt_Perc where Sequencia = @seqrota and CodCli1 = @cxforte and Hora1 >= '1700' and Hora1 <= '2000' and Flag_Excl <> '*';\n"
                    + "		if(@cxfExiste <= 0) begin		\n"
                    /*+ "			--Buscando Hora1 Disponivel\n"*/
                    + "			WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @horaCxfNoite and Flag_Excl <> '*') > 0 begin\n"
                    + "      \n"
                    /*+ "				--Caso já exista serviço para outro cliente no horário adiciona @acrescimoMinutos\n"*/
                    + "				set @horaCxfNoite = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@horaCxfNoite,1,2)+':'+Substring(@horaCxfNoite,3,2))),113),1,5),':','');\n"
                    + "	  \n"
                    + "				IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @horaCxfNoite and Flag_Excl <> '*') = 0\n"
                    + "					BREAK  \n"
                    + "			ELSE  \n"
                    + "				CONTINUE  \n"
                    + "			END\n"
                    + "			\n"
                    /*+ "			--Gerando número parada destino\n"*/
                    + "			Select @seqParadaDst = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqrota;					\n"
                    + "			Insert into Rt_Perc (Sequencia, Hora1, CodCli1, Nred, Regiao, Parada, ER, TipoSrv, CodFil, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ) \n"
                    + "			Values (@seqRota, @horaCxfNoite, @cxforte, @cxforteNred, @cxforteRegiao, @seqParadaDst, 'E', 'R', @codfil, @operador,@dtalter,@hralter, '', '', '',@observ);	\n"
                    /*+ "			--Atualizando Hora1D Origem\n"*/
                    + "			Update Rt_Perc set Hora1D = @horaCxfNoite, dPar = @seqParadaDst where Sequencia = @seqrota and Parada = @seqParadaCxf;\n"
                    + "		end\n"
                    /*+ "		----Verificando se existe parada para tesouraria na rota 090\n"
                    + "		--Select @seqrota = Sequencia from Rotas where Data = @data and CodFil = @codfil and Rota = '090' and Flag_Excl <> '*';\n"*/
                    + "		Select @tesexiste = Isnull(Count(*),0) from Rt_Perc where Sequencia = @seqrota and CodCli1 = @codtes and ER = 'R' and Flag_Excl <> '*';\n"
                    + "		set @hora1 = '0810';		\n"
                    + "		if(@tesexiste <= 0) begin\n"
                    /*+ "			--Buscando Hora1 Disponivel\n"*/
                    + "			WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @hora1 and Flag_Excl <> '*') > 0 BEGIN\n"
                    + "      \n"
                    /*+ "				--Caso já exista serviço para outro cliente no horário adiciona @acrescimoMinutos\n"*/
                    + "				set @hora1 = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@hora1,1,2)+':'+Substring(@hora1,3,2))),113),1,5),':','');\n"
                    + "	  \n"
                    + "				IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @hora1 and Flag_Excl <> '*') = 0\n"
                    + "				BREAK  \n"
                    + "			ELSE  \n"
                    + "				CONTINUE  \n"
                    + "			END\n"
                    + "			Select @seqparada = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqrota;					\n"
                    + "			Insert into Rt_Perc (Sequencia, Hora1, Parada, CodCli1, Nred, Regiao, ER, TipoSrv, CodFil, OperIncl, \n"
                    + "			Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ,CodCli2) \n"
                    + "			Values (@seqrota, @hora1, @seqparada, @codtes, @nredtes, @cxforteRegiao, 'R', 'R', @codfil, @operador,@dtalter,@hralter, '', '', '',@observ, @cxforte);	\n"
                    + "			Select @horaCxfNoite = Hora1,@seqParadaDst = Parada from Rt_Perc where Sequencia = @seqrota and codcli1 = @cxforte and Hora1 > @hora1 and Flag_Excl <> '*';\n"
                    + "			Update Rt_Perc set Hora1D = @horaCxfNoite, dPar = @seqParadaDst where Sequencia = @seqrota and Parada = @seqparada;\n"
                    + "		end\n"
                    /*+ "		----Registrando Entrada CXF\n"*/
                    + "		Select \n"
                    + "		@seqparada = Sequencia, \n"
                    + "		@hora1 = Hora1\n"
                    + "		from Rt_Perc where Sequencia = @seqrota and CodCli1 = @codtes and ER = 'R' and Flag_Excl <> '*';\n"
                    + "		Insert into CxFGuias (CodFil, CodCli, Guia, Serie, CliOri, CliDst, Valor, Tipo, RotaEnt, OperEnt, DtEnt, HrEnt, SeqRota, OS, Hora1, Volumes, \n"
                    + "                              VolDh, ValorDh, VolCh, ValorCh, VolMd, ValorMd, VolMet, ValorMet, VolMEstr, ValorMEstr, VolOutr, ValorOutr ) \n"
                    + "		Values (@codfil, @cxforte,@gtv, @seriegtv,@codtes, @codtes, @valortotal, @tiposrv, '090', @operador, @data, @hralter, @seqrota, @os, @hora1,0,0,0,0,0,0,0,0,0,0,0,0,0);\n"
                    + "		Insert into CXFGuiasVol (CodFil, CodCli, Guia, Serie, Ordem, Qtde, Lacre, Tipo, Obs, Valor) \n"
                    + "		Values (@codfil, @codtes, @gtv, @seriegtv,1, 1, @lacre, 1, '', @valortotal);\n"
                    + "	end  \n"
                    + "	Delete from #pedidos where Numero = @pedido;\n"
                    + "   IF (SELECT count(*) FROM #pedidos) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + " END  \n"
                    + " Drop table #pedidos; \n"
                    /*+ "-------------------------------------------------\n"
                    + "--Buscando pedidos roteirizados e gerando Cxforte\n"
                    + "--saida------------------------------------------\n"*/
                    + " Select \n"
                    + " Pedido.Numero, Pedido.CodFil, Pedido.Data, Rt_Perc.CodCli1, Pedido.OS, Pedido.Valor, \n"
                    + " Pedido.SeqRota, Pedido.Parada, Rotas.Rota, Pedido.ClassifSRV, OS_Vig.CodSrv, TesSaidas.Guia, TesSaidas.Serie,\n"
                    + " Rt_Perc.Hora1\n"
                    + " into #pedidos2\n"
                    + " From Pedido \n"
                    + " Inner join Rt_Perc  on Rt_Perc.Sequencia = Pedido.SeqRota\n"
                    + "                    and Rt_Perc.Parada = Pedido.Parada\n"
                    + " Inner join Rotas  on Rotas.Sequencia = Pedido.SeqRota\n"
                    + " Left join OS_Vig  on OS_Vig.OS = Pedido.OS\n"
                    + "                  and OS_Vig.CodFil = Pedido.CodFil\n"
                    + " Inner join TesSaidas  on TesSaidas.Pedido = Pedido.Numero\n"
                    + "                      and TesSaidas.CodFil = Pedido.CodFil\n"
                    + " Left join CxfGuias  on CxfGuias.SeqRotaSai = Rt_Perc.Sequencia\n"
                    + "                    and Cxfguias.Hora1D = Rt_Perc.Hora1\n"
                    + " where Pedido.data = @data\n"
                    + "   and Pedido.CodFil = @codfil\n"
                    + "   and Pedido.CodCli1 = @cxforte\n"
                    + "   and Pedido.Flag_Excl <> '*'\n"
                    + "   and Rt_Perc.Flag_Excl <> '*'\n"
                    + "   and Pedido.Tipo = 'T'\n"
                    + "   and CxfGuias.SeqRotaSai is null\n"
                    + "   and Pedido.OS > 0;\n"
                    + " WHILE (SELECT count(*) FROM #pedidos2) > 0\n"
                    + " BEGIN \n"
                    + "	Select top 01 \n"
                    + "	@pedido = Numero,\n"
                    + "	@codcli1 = CodCli1,\n"
                    + "	@hora1 = Hora1,\n"
                    + "	@os = OS,\n"
                    + "	@tiposrv = ClassifSRV,\n"
                    + "	@pedidovalor = Valor,\n"
                    + "	@seqrota = SeqRota,\n"
                    + "	@seqparada = Parada,\n"
                    + "	@rota = Rota,\n"
                    + "	@codsrv = CodSRV,\n"
                    + "	@gtv = Guia, \n"
                    + "	@seriegtv = Serie\n"
                    + "	from #pedidos2\n"
                    + "	Order by Rota;\n"
                    + "	Update CxFGuias set \n"
                    + "           RotaSai    = @rota, \n"
                    + "           SeqRotaSai = @seqrota, \n"
                    + "           Remessa    = 9999,\n"
                    + "           Hora1D     = @hora1, \n"
                    + "           CliDst     =  @codcli1, \n"
                    + "           OperSai    = @operador,\n"
                    + "           Dtsai      = @data,\n"
                    + "           HrSai      = @hralter,\n"
                    + "           OS         = @os,\n"
                    + "           Lote       = @codsrv\n"
                    + "           where Guia = @gtv\n"
                    + "             and Serie = @seriegtv\n"
                    + "             and CodFil = @codfil;\n"
                    + "			 		\n"
                    + "	Delete from #pedidos2 where Numero = @pedido;\n"
                    + "   IF (SELECT count(*) FROM #pedidos2) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + " END  \n"
                    + " Drop table #pedidos2; \n"
                    /*+ "-------------------------------------------------\n"
                    + "--Buscando pedidos roteirizados e gerando remessa\n"
                    + "--saida CXF--------------------------------------\n"*/
                    + " Select SeqRotaSai, Count(*) Qtde, Sum(Volumes) Volumes, Sum(Valor) Valor\n"
                    + " into #remessaspendentes \n"
                    + " from CxfGuias where DtSai = @data \n"
                    + " and Remessa = 9999 \n"
                    + " and CodFil = @codfil\n"
                    + " Group by SeqRotaSai;\n"
                    + " WHILE (SELECT count(*) FROM #remessaspendentes) > 0\n"
                    + " BEGIN \n"
                    + "	Select top 01\n"
                    + "	@seqrota = SeqRotaSai,\n"
                    + "	@qtde = Qtde, \n"
                    + "	@volumes = Volumes, \n"
                    + "	@valor = Valor\n"
                    + "	From #remessaspendentes;\n"
                    + "	Select @remessa = isnull(Remessa,0)+1 from CxFSaidas where SeqRota = @seqrota;\n"
                    + "	if (@remessa is null)\n"
                    + "		set @remessa = 1;\n"
                    + "	set @codremessa = Replicate('0', 6-len(Convert(Varchar,@seqrota))) + Convert(Varchar,@seqrota)+Replicate('0', 3-len(Convert(Varchar,@remessa)))+Convert(Varchar,@remessa);\n"
                    + "	Insert into CxfSaidas (CodFil, SeqRota, Remessa, CodRemessa, Qtde, QtdeVol, Valor, Oper_Prep, Dt_Prep, Hr_Prep)\n"
                    + "	Values(@codfil, @seqrota, @remessa, @codremessa, @qtde, @volumes, @valor, @operador, @dtalter, @hralter);\n"
                    + "	Update CxfGuias set Remessa = @remessa where SeqRotaSai = @seqrota and Remessa = 9999;\n"
                    + "	Delete from #remessaspendentes where SeqRotaSai = @seqrota;\n"
                    + "   IF (SELECT count(*) FROM #remessaspendentes) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + " END  \n"
                    + " Drop table #remessaspendentes;";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(CodFil);
            consulta.setString(DataInicio); //Inicio
            consulta.setString(DataFim); //Fim
            consulta.setString(Operador);
            consulta.setString(Dt_Alter);
            consulta.setString(Hr_Alter);
            consulta.insert();

            consulta.close();

        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.processarGuias - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesSaidas> quantidadeGuiasCxForte(String CodFil, String CodCli1, String DataInicio, String DataFim) throws Exception {
        String sql = "";
        try {
            List<TesSaidas> Retorno = new ArrayList<>();

            sql = "Select \n"
                    + " Clientes.Nred Tesouraria, TesSaidas.Data, Convert(varchar,Convert(BigInt,TesSaidas.Guia)) Guia, TesSaidas.Serie \n"
                    + " From Pedido \n"
                    + " Inner join TesSaidas  on TesSaidas.Pedido = Pedido.Numero\n"
                    + "                     and TesSaidas.CodFil = Pedido.CodFil \n"
                    + " Inner join Clientes  on Clientes.Codigo = TesSaidas.CodCli1\n" 
                    + "                     and Clientes.CodFil = TesSaidas.CodFil \n"
                    + " where Pedido.data Between ? and ? \n"
                    + "   and Pedido.CodFil = ? \n"
                    + "   and Pedido.CodCli1 in (Select CodCli from Cxforte where codfil = ?) \n"
                    + "   and TesSaidas.Guia in (Select Guia from TesSaidasLacres (Nolock) where TesSaidasLacres.Serie = TesSaidas.Serie)\n"
                    + "   and TesSaidas.Guia not in (Select Guia from CxfGuias (Nolock)  where CxfGuias.Serie = TesSaidas.Serie)\n"
                    + "   and Flag_Excl <> '*'\n"
                    + "   and Tipo = 'T'\n"
                    + "   and OS > 0 \n"
                    + " Order by Tesouraria, Data, Guia";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(DataInicio);
            consulta.setString(DataFim);
            consulta.setString(CodFil);
            consulta.setString(CodFil);            
            consulta.select();

            TesSaidas tesSaida;
            
            while (consulta.Proximo()) {
                tesSaida = new TesSaidas();
                
                tesSaida.setCodCli1(consulta.getString("Tesouraria"));
                tesSaida.setData(consulta.getString("Data"));
                tesSaida.setGuia(consulta.getString("Guia").replace(".0", ""));
                tesSaida.setSerie(consulta.getString("Serie"));
                
                Retorno.add(tesSaida);
            }

            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.quantidadeGuiasCxForte - " + e.getMessage());
        }
    }

    public int contagemAllTesSaidas(Map filters) throws Exception {
        try {
            int Retorno = 0;
            List<TesSaidas> Listagem = allTesSaidasPaginada(0, 10000000, filters);

            Retorno = Listagem.size();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.contagemAllTesSaidas - " + e.getMessage());
        }
    }

    public List<TesSaidas> allTesSaidasPaginada(int first, int pageSize, Map filters) throws Exception {
        String sql = "";
        List<TesSaidas> Retorno = new ArrayList<>();
        try {
            String CodFil = (String) filters.get("CodFil");
            String CodCli1 = (String) filters.get("CodCli1");
            String DataInicio = (String) filters.get("DataInicio");
            String DataFim = (String) filters.get("DataFim");
            String TipoMov = (String) filters.get("TipoMov");

            sql = "SELECT * FROM(\n"
                    + " Select ROW_NUMBER() OVER ( ORDER BY TesSaidas.Guia, TesSaidas.Serie) AS RowNum, OS_Vig.CodSrv CodSrv2, TesSaidas.*, Pedido.Data DtRota, \n"
                    + " STUFF((Select ', '+a.Lacre\n"
                    + "		From TesSaidasLacres a (Nolock)\n"
                    + "		Where a.Guia = TesSaidas.Guia\n"
                    + "		  and a.Serie = TesSaidas.Serie\n"
                    + "		For XML PATH('')),1,2,''\n"
                    + "		) as Lacre,"
                    + " CliOri.NRed NRedOri, CliDst.Agencia, CliDst.NRed NRedDst, \n"
                    + " CliDst.MarcaATM, Rt_perc.Hora1, Rotas.Rota, Rt_Perc.TipoSrv TipoSrvRota, \n"
                    + " TesSaidasCxF.Remessa, TpCliInterf.TpCliBrad from TesSaidas \n"
                    /*+ " left Join TesSaidasLacres on  TesSaidasLacres.Guia  = TesSaidas.Guia   \n"
                    + "                           and TesSaidasLacres.Serie = TesSaidas.Serie  \n"
                    + "                           and TesSaidasLacres.Malote= 001   \n"*/
                    + " left Join Clientes CliOri on  CliOri.Codigo         = TesSaidas.CodCli1\n"
                    + "                           and CliOri.CodFil         = TesSaidas.CodFil \n"
                    + " left Join Clientes CliDst on  CliDst.Codigo         = TesSaidas.CodCli2\n"
                    + "                           and CliDst.CodFil         = TesSaidas.CodFil \n"
                    + " left join TesSaidasCxF    on  TesSaidasCxF.Guia     = TesSaidas.Guia   \n"
                    + "                           and TesSaidasCxF.Serie    = TesSaidas.Serie  \n"
                    + " Left Join GTV             on  GTV.Guia              = TesSaidas.Guia   \n"
                    + "                           and GTV.Serie             = TEsSaidas.Serie  \n"
                    + " left join TpCliInterf     on  TpCliInterf.TpCli     = CliDst.TpCli  \n"
                    + " left join Pedido on Pedido.Numero       = TesSaidas.Pedido\n"
                    + "                        and pedido.CodFil      = TesSaidas.CodFil\n"
                    + "       left Join Rt_Perc on  Rt_Perc.Pedido    = TesSaidas.Pedido\n"
                    + "                         and Rt_Perc.ER        = 'E'\n"
                    + "                         and Rt_Perc.Flag_Excl <> '*'\n"
                    + "                         and Rt_Perc.CodCli1 = TesSaidas.CodCli2   \n"
                    + "                         and Rt_perc.CodFil  = TesSaidas.CodFil\n"
                    + "       left Join Rotas   on  Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "       Left Join OS_Vig          on  Os_Vig.Os         = Pedido.OS   \n"
                    + "                                 and OS_vig.CodFil     = Pedido.CodFil\n"
                    + " where TesSaidas.Codfil  = ?\n"
                    + "   and TesSaidas.CodCli1 = ?\n"
                    + "   and TesSaidas.Serie   <> 'ZGE'\n"
                    + "   and TesSaidas.Data   >= ?\n"
                    + "   and TesSaidas.Data   <= ?\n"
                    + "   and TesSaidas.TipoMov = ?\n";
            sql += ") AS RowConstrainedResult\n"
                    + " WHERE   RowNum >= ?\n"
                    + "     AND RowNum <= ?\n"
                    + " ORDER BY RowNum;\n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(CodFil);
            consulta.setString(CodCli1);
            consulta.setString(DataInicio);
            consulta.setString(DataFim);
            consulta.setString(TipoMov);

            consulta.setInt(first);
            consulta.setInt(pageSize);

            consulta.select();

            TesSaidas tesSaida;

            while (consulta.Proximo()) {
                tesSaida = new TesSaidas();

                tesSaida.setData(consulta.getString("Data"));
                tesSaida.setTipoMov(consulta.getString("TipoMov"));
                tesSaida.setGuia(consulta.getBigDecimal("Guia").toPlainString());
                tesSaida.setSerie(consulta.getString("Serie"));
                tesSaida.setAgencia(consulta.getString("Agencia"));
                tesSaida.setDestino(consulta.getString("NRedDst"));
                tesSaida.setTotalGeral(consulta.getString("TotalGeral"));
                tesSaida.setLacre(consulta.getString("Lacre"));
                tesSaida.setCodCli2(consulta.getString("CodCli2"));
                tesSaida.setCodCli3(consulta.getString("CodCli3"));
                tesSaida.setTotalDN(consulta.getString("TotalDN"));
                tesSaida.setTotalDD(consulta.getString("TotalDD"));
                tesSaida.setMarcaAtm(consulta.getString("MArcaATM"));
                tesSaida.setTotalMoeda(consulta.getString("TotalMoeda"));
                tesSaida.setChequesQtde(consulta.getString("ChequesQtde"));
                tesSaida.setChequesValor(consulta.getString("ChequesValor"));
                tesSaida.setTicketsQtde(consulta.getString("TicketsQtde"));
                tesSaida.setTicketsValor(consulta.getString("TicketsValor"));
                tesSaida.setMatrConf(consulta.getString("MatrConf"));
                tesSaida.setHrInicio(consulta.getString("HrInicio"));
                tesSaida.setCamera(consulta.getInt("Camera"));
                tesSaida.setDiferenca(consulta.getString("Diferenca"));
                tesSaida.setContaTes(consulta.getString("ContaTes"));
                tesSaida.setTipoSrv(consulta.getString("TipoSrv"));
                tesSaida.setCodSrv(consulta.getString("CodSrv2"));
                tesSaida.setObs(consulta.getString("Obs"));
                tesSaida.setSituacao(consulta.getString("Situacao"));
                tesSaida.setPedido(consulta.getString("Pedido"));
                //?(Duplicado)? tesSaida.setData(consulta.getString(""));
                tesSaida.setHora1(consulta.getString("Hora1"));
                tesSaida.setRota(consulta.getString("Rota"));
                tesSaida.setTipoSrvRota(consulta.getString("TipoSrvRota"));
                tesSaida.setRemessa(consulta.getString("Remessa"));
                tesSaida.setDt_Pedido(consulta.getString("Dt_Pedido"));
                tesSaida.setTpCliBrad(consulta.getString("TpCliBrad"));
                tesSaida.setOperador(consulta.getString("Operador"));
                tesSaida.setDt_Alter(consulta.getString("Dt_Alter"));
                tesSaida.setHr_Alter(consulta.getString("Hr_Alter"));

                Retorno.add(tesSaida);
            }

            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.allTesSaidasPaginada - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Clientes> listaTesouraria(String CodFil) throws Exception {
        String sql = "";
        List<Clientes> Retorno = new ArrayList<>();

        try {
            sql = "Select TesFecha.*, Clientes.NRed\n"
                    + " from TesFecha \n"
                    + " Left join Clientes  on Clientes.Codigo = TesFecha.CodCli\n"
                    + "                    and Clientes.CodFil = TesFecha.CodFil\n"
                    + " where TesFecha.codfil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.select();

            Clientes cliente;

            while (consulta.Proximo()) {
                cliente = new Clientes();

                cliente.setCodigo(consulta.getString("CodCli"));
                cliente.setNRed(consulta.getString("Nred"));

                Retorno.add(cliente);
            }

            consulta.close();

            return Retorno;

        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.listaTesouraria - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<Funcionarios> listaFuncionarios(String CodFil) throws Exception {
        String sql = "";
        List<Funcionarios> Retorno = new ArrayList<>();

        try {
            sql = "SELECT Matr, Nome\n"
                    + " FROM Funcion \n"
                    + " WHERE CodFil   = ?\n"
                    + " AND   Situacao = 'A'\n"
                    + " ORDER BY Nome";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.select();

            Funcionarios funcionario;

            while (consulta.Proximo()) {
                funcionario = new Funcionarios();

                funcionario.setMatr(consulta.getString("Matr"));
                funcionario.setNome(consulta.getString("Nome"));

                Retorno.add(funcionario);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.listaFuncionarios - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesSaidas> listaLacres(String Guia, String Serie) throws Exception {
        String sql = "";
        List<TesSaidas> Retorno = new ArrayList<>();

        try {
            sql = "SELECT Lacre\n"
                    + " FROM TesSaidasLacres\n"
                    + " WHERE Guia  = ?\n"
                    + " AND   Serie = ?\n"
                    + " ORDER BY Lacre";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Guia);
            consulta.setString(Serie);
            consulta.select();

            TesSaidas tesSaida;

            while (consulta.Proximo()) {
                tesSaida = new TesSaidas();

                tesSaida.setLacre(consulta.getString("Lacre"));

                Retorno.add(tesSaida);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.listaLacres - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesSaidasDN> listaComposicaoDN(String Guia, String Serie) throws Exception {
        String sql = "";
        List<TesSaidasDN> Retorno = new ArrayList<>();

        try {
            sql = "SELECT \n"
                    + " Qtde,\n"
                    + " Valor,\n"
                    + " 'DN' Tipo\n"
                    + " FROM TesSaidasDN\n"
                    + " WHERE Guia  = ?\n"
                    + " AND   Serie = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Guia);
            consulta.setString(Serie);
            consulta.select();

            TesSaidasDN tesSaida;

            while (consulta.Proximo()) {
                tesSaida = new TesSaidasDN();

                tesSaida.setValor(consulta.getString("Valor"));
                tesSaida.setQtde(consulta.getString("Qtde"));

                Retorno.add(tesSaida);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.listaComposicaoDN - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<TesSaidasMD> listaComposicaoMD(String Guia, String Serie) throws Exception {
        String sql = "";
        List<TesSaidasMD> Retorno = new ArrayList<>();

        try {
            sql = "SELECT \n"
                    + " Qtde,\n"
                    + " Valor,\n"
                    + " 'MD' Tipo\n"
                    + " FROM TesSaidasMD\n"
                    + " WHERE Guia  = ?\n"
                    + " AND   Serie = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Guia);
            consulta.setString(Serie);
            consulta.select();

            TesSaidasMD tesSaida;

            while (consulta.Proximo()) {
                tesSaida = new TesSaidasMD();

                tesSaida.setValor(consulta.getString("Valor"));
                tesSaida.setQtde(consulta.getString("Qtde"));

                Retorno.add(tesSaida);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.listaComposicaoMD - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void gravarLacres(List<TesSaidas> Lacres, String Guia, String Serie, String MatrConf) throws Exception {
        String sql = "";
        int Malote = 1;
        try {
            sql = "UPDATE TesSaidas\n"
                    + " SET MatrConf = ?\n"
                    + " WHERE Guia   = ?\n"
                    + " AND   Serie  = ?;";

            sql += "DELETE FROM TesSaidasLacres\n"
                    + " WHERE Guia  = ?\n"
                    + " AND   Serie = ?;\n";

            for (TesSaidas Lacre : Lacres) {
                sql += " INSERT INTO TesSaidasLacres(Guia, Serie, Malote, Lacre, Operador, Dt_Alter, Hr_Alter) VALUES(\n"
                        + " ?,\n"
                        + " ?,\n"
                        + " ?,\n"
                        + " ?,\n"
                        + " ?,\n"
                        + " ?,\n"
                        + " ?\n"
                        + ");";
            }

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(MatrConf);
            consulta.setString(Guia);
            consulta.setString(Serie);

            consulta.setString(Guia);
            consulta.setString(Serie);

            for (TesSaidas Lacre : Lacres) {
                consulta.setString(Guia);
                consulta.setString(Serie);
                consulta.setInt(Malote);
                consulta.setString(Lacre.getLacre());
                consulta.setString(Lacre.getOperador());
                consulta.setString(Lacre.getDt_Alter());
                consulta.setString(Lacre.getHr_Alter());
                Malote++;
            }

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesourariaSaidaController.gravarLacres - " + e.getMessage() + "\r\n" + sql);
        }
    }

}

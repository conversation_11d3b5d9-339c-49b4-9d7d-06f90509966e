/*
 */
package ClassesJson.BI;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Grafico {

    private String tipo;
    private String titulo;
    private String desc_geral;
    private List<Dado> dados = new ArrayList();

    public String getDesc_geral() {
        return desc_geral;
    }

    public void setDesc_geral(String desc_geral) {
        this.desc_geral = desc_geral;
    }

    public List<Dado> getDados() {
        return dados;
    }

    public void setDados(List<Dado> dados) {
        this.dados = dados;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

}

/*
 */
package br.com.sasw.arquivos;

import SasBeans.Contatos;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.PropCml;
import SasBeans.PropCmlModItens;
import SasBeans.PropCondPgto;
import SasBeansCompostas.ProdutosPropProdServ;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import com.itextpdf.text.BadElementException;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.List;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
public class PdfProposta {

    File file;
    private Document document = new Document();
    private String Arquivo, caminho;
    PdfWriter writer;
    CabecalhoProposta cabecalho;

    public PdfProposta(String cam, String arquivo, Filiais filial, PropCml proposta, String banco) throws BadElementException, IOException, Exception {
        caminho = cam;
        Arquivo = caminho + "\\" + arquivo;
        cabecalho = new CabecalhoProposta(filial, proposta, FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator) + this.getLogo(banco));
        document = new Document(PageSize.A4, 36, 36, cabecalho.getCabecalhoHeight(), 16 + cabecalho.getRodapeHeight());
        try {
            File theFile = new File(caminho);
            theFile.mkdirs();// will create a sub folder for each user (currently does not work, below hopefully is a solution) (DOES WORK NOW)

            writer = PdfWriter.getInstance(document, new FileOutputStream(Arquivo));
        } catch (DocumentException | IOException de) {
            System.err.println(de.getMessage());
        }
        document.open();
    }

    public void contato(Filiais filial, Contatos contato, PropCml proposta, String detalhe) throws BadElementException, IOException, DocumentException, Exception {
        writer.setPageEvent(cabecalho);

        //INTRODUÇÃO
        Font f1 = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL, BaseColor.BLACK);
        Font f2 = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLD, BaseColor.BLACK);
        Paragraph p = new Paragraph(contato.getNome(), f2);
        p.setAlignment(Element.PARAGRAPH);
        p.setIndentationLeft(10);
        document.add(p);
        p = new Paragraph(contato.getEndereco(), f1);
        p.setAlignment(Element.PARAGRAPH);
        p.setIndentationLeft(10);
        document.add(p);
        p = new Paragraph(contato.getBairro() + " - " + contato.getCidade() + " - " + contato.getUF(), f1);
        p.setAlignment(Element.PARAGRAPH);
        p.setIndentationLeft(10);
        document.add(p);
        p = new Paragraph(((null != contato.getCEP() && !contato.getCEP().equals(""))
                ? "CEP: " + FuncoesString.formatarString(contato.getCEP(), "##.###-###") : "")
                + ((null != contato.getFone1() && !contato.getFone1().equals(""))
                ? (" - Tel: " + FuncoesString.formatarString(contato.getFone1(), "(##) ########?#")) : ""), f1);
        p.setAlignment(Element.PARAGRAPH);
        p.setIndentationLeft(10);
        p.setSpacingAfter(10);
        document.add(p);

        float[] columnWidths = {1, 10};
        PdfPTable table = new PdfPTable(columnWidths);
        table.setTotalWidth(523);
        table.setLockedWidth(true);
        p = new Paragraph("A/C Sr.", f1);
        PdfPCell cell = new PdfPCell(p);
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setIndent(10);
        table.addCell(cell);
        p = new Paragraph(proposta.getContato(), f2);
        cell = new PdfPCell(p);
        cell.setBorder(Rectangle.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell();
        cell.setBorder(Rectangle.NO_BORDER);
        table.addCell(cell);
        table.addCell(cell);
        p = new Paragraph("Ref:", f1);
        cell = new PdfPCell(p);
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setIndent(10);
        table.addCell(cell);
        p = new Paragraph(proposta.getRefProp(), f2);
        cell = new PdfPCell(p);
        cell.setBorder(Rectangle.NO_BORDER);
        table.addCell(cell);
        table.setSpacingAfter(10);
        document.add(table);

        p = new Paragraph((null != proposta.getSexo() && proposta.getSexo().equals("F")) ? "Prezada Senhora" : "Prezado Senhor", f1);
        p.setAlignment(Element.PARAGRAPH);
        p.setIndentationLeft(10);
        p.setSpacingAfter(10);
        document.add(p);

        p = new Paragraph(detalhe, f1);
        p.setAlignment(Element.ALIGN_JUSTIFIED);
        p.setIndentationLeft(30);
        p.setSpacingAfter(10);
        document.add(p);
    }

    public void detalhes(List<PropCmlModItens> itens, List<ProdutosPropProdServ> listaProdutos, PropCml proposta, List<PropCondPgto> condicoes) throws DocumentException {
        float[] columnWidths = {2, 30};
        PdfPTable table = new PdfPTable(columnWidths);
        PdfPCell cell;
        Paragraph p;

        table.setTotalWidth(523);
        table.setLockedWidth(true);
        Font f4 = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLDITALIC, BaseColor.BLACK);
        Font f3 = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLDITALIC, BaseColor.BLACK);
        Font f2 = new Font(Font.FontFamily.HELVETICA, 8, Font.ITALIC, BaseColor.BLACK);
        Font f1 = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL, BaseColor.BLACK);
        Font f0 = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLD, BaseColor.BLACK);
        for (PropCmlModItens item : itens) {
            p = new Paragraph(item.getItem().toBigInteger().toString(), f3);
            p.setAlignment(Element.ALIGN_RIGHT);
            cell = new PdfPCell(p);
            cell.setBorder(Rectangle.NO_BORDER);
            cell.setIndent(10);
            cell.setPaddingBottom(10);
            table.addCell(cell);
            p = new Paragraph(item.getDescricao(), f3);
            p.setSpacingAfter(10);
            cell = new PdfPCell(p);
            cell.setPaddingBottom(10);
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell();
            cell.setBorder(Rectangle.NO_BORDER);
            cell.setPaddingBottom(10);
            table.addCell(cell);
            p = new Paragraph(item.getDetalhe(), f1);
            p.setSpacingAfter(10);
            cell = new PdfPCell(p);

            if (item.getItem().intValue() == 1) {
                boolean even = true;

                cell.setBorder(Rectangle.NO_BORDER);
                table.addCell(cell);

                float[] largura = {10, 1, 2, 2};
                PdfPTable produtos = new PdfPTable(largura);
                produtos.setWidthPercentage(100);
                PdfPCell produto = new PdfPCell();

                produto.addElement(new Paragraph("Produtos e Serviços", f4));
                produto.setColspan(4);
                produto.setBorder(Rectangle.NO_BORDER);
                produtos.addCell(produto);

                produto = new PdfPCell();
                produto.addElement(new Paragraph("Descrição", f2));
                produto.setBorder(Rectangle.NO_BORDER);
                produtos.addCell(produto);
                produto = new PdfPCell();
                p = new Paragraph("Qtde", f2);
                p.setAlignment(Element.ALIGN_RIGHT);
                produto.addElement(p);
                produto.setBorder(Rectangle.NO_BORDER);
                produtos.addCell(produto);
                produto = new PdfPCell();
                p = new Paragraph("Valor Unitário", f2);
                p.setAlignment(Element.ALIGN_RIGHT);
                produto.addElement(p);
                produto.setBorder(Rectangle.NO_BORDER);
                produtos.addCell(produto);
                produto = new PdfPCell();
                p = new Paragraph("Valor Total", f2);
                p.setAlignment(Element.ALIGN_RIGHT);
                produto.addElement(p);
                produto.setBorder(Rectangle.NO_BORDER);
                produtos.addCell(produto);

                for (ProdutosPropProdServ prod : listaProdutos) {
                    if (prod.getProduto().getIndServico().equals("N")) {
                        produto = new PdfPCell();
                        p = new Paragraph(prod.getProduto().getDescricao(), f1);
                        produto.setFixedHeight(20);
                        produto.addElement(p);
                        produto.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        produto.setBorder(Rectangle.NO_BORDER);
                        produto.setBackgroundColor(even ? BaseColor.LIGHT_GRAY : BaseColor.WHITE);
                        produtos.addCell(produto);
                        produto = new PdfPCell();
                        p = new Paragraph(prod.getProduto().getQtde().toBigInteger().toString(), f1);
                        p.setAlignment(Element.ALIGN_RIGHT);
                        produto.addElement(p);
                        produto.setFixedHeight(20);
                        produto.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        produto.setBorder(Rectangle.NO_BORDER);
                        produto.setHorizontalAlignment(Element.ALIGN_LEFT);
                        produto.setBackgroundColor(even ? BaseColor.LIGHT_GRAY : BaseColor.WHITE);
                        produtos.addCell(produto);
                        produto = new PdfPCell();
                        p = new Paragraph(prod.getProduto().getPrecoVenda().setScale(2, BigDecimal.ROUND_CEILING).toString(), f1);
                        p.setAlignment(Element.ALIGN_RIGHT);
                        produto.setFixedHeight(20);
                        produto.addElement(p);
                        produto.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        produto.setBorder(Rectangle.NO_BORDER);
                        produto.setHorizontalAlignment(Element.ALIGN_LEFT);
                        produto.setBackgroundColor(even ? BaseColor.LIGHT_GRAY : BaseColor.WHITE);
                        produtos.addCell(produto);
                        produto = new PdfPCell();
                        p = new Paragraph(prod.getProduto().getQtde().multiply(prod.getProduto().getPrecoVenda()).setScale(2, BigDecimal.ROUND_CEILING).toPlainString(), f1);
                        p.setAlignment(Element.ALIGN_RIGHT);
                        produto.setFixedHeight(20);
                        produto.addElement(p);
                        produto.setBorder(Rectangle.NO_BORDER);
                        produto.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        produto.setHorizontalAlignment(Element.ALIGN_LEFT);
                        produto.setBackgroundColor(even ? BaseColor.LIGHT_GRAY : BaseColor.WHITE);
                        produtos.addCell(produto);
                    }

                    even = !even;
                }

                produto = new PdfPCell();
                produto.setColspan(3);
                produto.setBorder(Rectangle.NO_BORDER);
                produto.setHorizontalAlignment(Element.ALIGN_RIGHT);
                p = new Paragraph("Total Geral:", f1);
                p.setAlignment(Element.ALIGN_RIGHT);
                produto.addElement(p);
                produtos.addCell(produto);
                produto = new PdfPCell();
                produto.setBorder(Rectangle.NO_BORDER);
                produto.setHorizontalAlignment(Element.ALIGN_RIGHT);
                p = new Paragraph(proposta.getValorProd().setScale(2, BigDecimal.ROUND_CEILING).toString(), f1);
                p.setAlignment(Element.ALIGN_RIGHT);
                produto.addElement(p);
                produtos.addCell(produto);

                cell = new PdfPCell(produtos);
                cell.setBorder(Rectangle.NO_BORDER);
                cell.setColspan(2);
                cell.setPaddingBottom(10);
                table.addCell(cell);

                largura = new float[]{3, 9};
                PdfPTable infos = new PdfPTable(largura);
                infos.setWidthPercentage(100);
                PdfPCell info;

                if (!condicoes.isEmpty()) {
                    info = new PdfPCell();
                    p = new Paragraph("Condições de Pagamento", f4);
                    info.addElement(p);
                    info.setBorder(Rectangle.NO_BORDER);
                    info.setColspan(2);
                    infos.addCell(info);

                    largura = new float[]{2, 20};
                    PdfPTable conds = new PdfPTable(largura);
                    conds.setWidthPercentage(100);
                    PdfPCell cond;
                    for (PropCondPgto condicao : condicoes) {
                        cond = new PdfPCell();
                        p = new Paragraph("( ) - " + condicao.getOpcao(), f0);
                        p.setAlignment(Element.ALIGN_RIGHT);
                        cond.setBorder(Rectangle.NO_BORDER);
                        cond.addElement(p);
                        cond.setHorizontalAlignment(Element.ALIGN_RIGHT);

                        conds.addCell(cond);

                        cond = new PdfPCell();
                        p = new Paragraph(condicao.getDescricao(), f1);
                        cond.setBorder(Rectangle.NO_BORDER);
                        cond.addElement(p);
                        conds.addCell(cond);
                    }
                    info = new PdfPCell();
                    info.setColspan(2);
                    info.setBorder(Rectangle.NO_BORDER);
                    info.addElement(conds);
                    infos.addCell(info);
                }

                if (!proposta.getGarantia().equals("")) {
                    info = new PdfPCell();
                    p = new Paragraph("Garantia:", f4);
                    info.addElement(p);
                    info.setBorder(Rectangle.NO_BORDER);
                    infos.addCell(info);

                    info = new PdfPCell();
                    p = new Paragraph(proposta.getGarantia(), f1);
                    info.addElement(p);
                    info.setBorder(Rectangle.NO_BORDER);
                    infos.addCell(info);
                }

                if (!proposta.getPrazoEntrega().equals("")) {
                    info = new PdfPCell();
                    p = new Paragraph("Prazo de Entrega:", f4);
                    info.setBorder(Rectangle.NO_BORDER);
                    info.addElement(p);
                    infos.addCell(info);

                    info = new PdfPCell();
                    p = new Paragraph(proposta.getPrazoEntrega(), f1);
                    info.setBorder(Rectangle.NO_BORDER);
                    info.addElement(p);
                    infos.addCell(info);
                }

                cell = new PdfPCell(infos);
                cell.setBorder(Rectangle.NO_BORDER);
                cell.setColspan(2);
                cell.setPaddingBottom(10);
                table.addCell(cell);
            } else {
                cell.setPaddingBottom(10);
                cell.setBorder(Rectangle.NO_BORDER);
                table.addCell(cell);
            }
        }
        document.add(table);
    }

    public void fechaDocumento(List<Pessoa> assinatura) throws DocumentException {
        Font f = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL, BaseColor.BLACK);
        Paragraph p = new Paragraph("Colocamo-nos a disposição para os esclarecimentos necessários.", f);
        p.setAlignment(Element.PARAGRAPH);
        p.setIndentationLeft(10);
        p.setSpacingAfter(10);
        p.setSpacingBefore(10);
        document.add(p);
        p = new Paragraph("Atenciosamente,", f);
        p.setAlignment(Element.PARAGRAPH);
        p.setIndentationLeft(10);
        p.setSpacingAfter(30);
        document.add(p);

        PdfPTable table = new PdfPTable(2);
        table.setTotalWidth(523);
        table.setLockedWidth(true);
        PdfPCell cell;
        f = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLDITALIC, BaseColor.BLACK);
        for (Pessoa ass : assinatura) {
            p = new Paragraph(ass.getNome(), f);
            p.setAlignment(Paragraph.ALIGN_CENTER);
            cell = new PdfPCell();
            cell.addElement(p);
            p = new Paragraph(ass.getFuncao(), f);
            p.setAlignment(Paragraph.ALIGN_CENTER);
            cell.addElement(p);
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
        }
        document.add(table);
        document.close();
    }

    private static byte[] fileToByte(File imagem) throws Exception {
        FileInputStream fis = new FileInputStream(imagem);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int tamanho = fis.available();
        byte[] buffer = new byte[tamanho];
        int bytesRead;
        while ((bytesRead = fis.read(buffer, 0, tamanho)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        fis.close();
        return baos.toByteArray();
    }

    public void service() throws Exception {
        try {
            byte[] arquivo = null;
            file = new File(Arquivo);
            try {
                arquivo = fileToByte(file);
            } catch (Exception e) {
            }
            FacesContext context = FacesContext.getCurrentInstance();
            context.getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) context.getExternalContext().getResponse();
            response.setContentLength(arquivo.length);
            response.setHeader("Content-disposition", "inline; filename=\"propostacomercial.pdf\"");
            OutputStream ouputStream = response.getOutputStream();
            ouputStream.write(arquivo, 0, arquivo.length);
            ouputStream.flush();
            ouputStream.close();
            context.responseComplete();
        } catch (Exception e) {
            throw new Exception("Falha ao finalizar arquivo - " + e.getMessage());
        }
    }

    public String getLogo(String banco) throws Exception {
        return LoginMB.getLogoS(banco);
        /*
        String url;
        switch (banco) {
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                try {
                    FacesContext fc = FacesContext.getCurrentInstance();
                    String CodigoFilial = (String) fc.getExternalContext().getSessionMap().get("filial");
                    if (!CodigoFilial.equals("3001")) {
                        url = "../assets/img/logo_confederal.jpg";
                    } else {
                        url = "../assets/img/logo_confere.jpg";
                    }
                } catch (Exception ex) {
                    url = "../assets/img/logo_confederal.jpg";
                }
            break;

            case "SATCORPVS":
            case "SATCORPVSPE":
                url = "../assets/img/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "../assets/img/TRANSVIP.gif";
                break;
            case "SATPRESERVE":
                url = "../assets/img/logo_preserve.jpg";
                break;
            case "VSG":
                url = "../assets/img/VSG.gif";
                break;
            case "SATTSEG":
                url = "../assets/img/TECNOSEG.gif";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
                url = "../assets/img/AGIL.gif";
                break;
            case "SATLOYAL":
                url = "../assets/img/LOYAL.gif";
                break;
            case "SATTRANSVIG":
                url = "../assets/img/TRANSVIG.gif";
                break;
            case "SATINVLMT":
                url = "../assets/img/INVLMT.gif";
                break;
            case "SATINVLRS":
                url = "../assets/img/INVLRS.gif";
                break;
            case "SATGSI":
                url = "../assets/img/GSI.gif";
                break;
            case "SATTRANSEXCEL":
                url = "../assets/img/logoExcel.jpg";
                break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "../assets/img/LogoRDB.jpg";
                break;
            case "SATTAMEME":
                url = "../assets/img/logo_tameme.jpg";
                break;
            case "SATCOMETRA":
                url = "../assets/img/logo_GSI.jpg";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "../assets/img/imgASEX.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "../assets/img/eagle.jpg";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "../assets/img/logo.png";
                break;
            case "SATQUALIFOCO":
                url = "../assets/img/QUALIFOCO.png";
                break;
            case "SATCIT":
                url = "../assets/img/logo_citcompany.png";
                break;
            case "SATSHALOM":
                url = "../assets/img/logo_shalom.png";
                break;
            default:
                url = "";
        }
        return url;*/
    }
}

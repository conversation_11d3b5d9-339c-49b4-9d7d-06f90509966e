package br.com.sasw.pacotesuteis.utilidades;

import Dados.Persistencia;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;

/**
 *
 * <AUTHOR>
 */
public class Empresas {

    private String schema = "";
    private String CodCliCxf;
    private String empresa;
    private int codfil;
    private String paramBanco = "";

    /**
     * Insere Empresa no objeto sem processar
     *
     * @param Param - Nome da empresa em letras maiusculas, ex. SASW
     */
    public Empresas() {

    }

    public Empresas(String Param) {
        this.setEmpresa(Param);
    }

    public void setEmpresa(String Param) {
        empresa = Param;
    }

    /**
     * Insere Codigo de Filial no objeto sem processar
     *
     * @param Codfil - codigo da filial em inteiro
     */
    public void setCodfil(String Codfil) {
        this.codfil = Integer.parseInt(Codfil);
    }

    /**
     * Construtor que prepara os parametros a serem usados no sistema
     *
     * @param Param - Nome da empresa em letra maiuscula exemplo SASW
     * @param codfil - Codigo da filial desejada
     */
    public void processaEmpresas() {
        String Param = empresa;
        if ("CORPVS".equals(Param)) {
            CodCliCxf = "6200001";
            schema = "";
        } else if (("SEBIVAL".equals(Param)) || ("BASE TESTE".equals(Param)) || ("BR_INK".equals(Param))) {
            CodCliCxf = "9990002";
            schema = "";
        } else if (("RODOBAN".equals(Param))) {
            schema = "";
            switch (codfil) {
                case (1):
                    CodCliCxf = "9990432";
                    break;
                case (2):
                    CodCliCxf = "9990441";
                    break;
                case (3):
                    CodCliCxf = "9987001";
                    break;
                case (4):
                    CodCliCxf = "9999158";
                    break;
                case (5):
                    CodCliCxf = "9997001";
                    break;
                case (8):
                    CodCliCxf = "9990432";
                    break;
                case (9):
                    CodCliCxf = "9990432";
                    break;
                case (10):
                    CodCliCxf = "9998064";
                    break;
                case (12):
                    CodCliCxf = "9997009";
                    break;
                case (13):
                    CodCliCxf = "9996001";
                    break;
            }
        } else if (("TRANSEGURO".equals(Param))) {
            schema = "";
            if (codfil == 1) {
                CodCliCxf = "9990002";
            } else if (codfil == 2) {
                CodCliCxf = "9990001";
            } else {
                CodCliCxf = "9996001";
            }
        } else if (("RRJ".equals(Param))) {
            schema = "";
            if (codfil == 1) {
                CodCliCxf = "9990001";
            } else {
                CodCliCxf = "9996001";
            }
        } else if (("CONFEDERAL".equals(Param))) {
            CodCliCxf = "6410002";
            schema = "";
        } else if (("TRANSEXPERT").equals(Param)) {
            CodCliCxf = "9990001";
            schema = "";
        } else if (("PROTEGE".equals(Param))) {
            CodCliCxf = "9990001";
            schema = "";
        } else if (("SASW".equals(Param))) {
            CodCliCxf = "9990002";
            schema = "";
        } else if ("TRANSVIP".equals(Param)) {
            CodCliCxf = "9990001";
            schema = "saswtransvip.";
        } else if ("VSG".equals(Param)) {
            CodCliCxf = "9990001";
            if (codfil == 1) {
                schema = "satellite.dbo.";
            } else if (codfil == 3) {
                schema = "satvsgtec.dbo.";
            } else {
                schema = "";
            }
        } else if ("AGIL".equals(Param)) {
            if (codfil == 1) {
                paramBanco = "AGILSERV";
            } else if (codfil == 2) {
                paramBanco = "AGILVIG";
            } else if (codfil == 3) {
                paramBanco = "AGILCOND";
            }
            schema = "";
            CodCliCxf = "9990001";
        } else {
            CodCliCxf = "9990001";
            schema = "";
        }
    }

    /**
     * Retorna o Schema do banco
     *
     * @return - string que completa o sql com schema ou nome do banco no
     * servidor
     */
    public String getSchema() {
        return schema;
    }

    /**
     * Retorna o Codigo do Caixa Forte da Empresa
     *
     * @return - string com o CodCliCxf
     */
    public String getCodCliCxf() {
        return CodCliCxf;
    }

    /**
     * Retorna o nome da empresa para parametro
     *
     * @return - string em letras maiusculas
     */
    public String getEmpresa() {
        return empresa;
    }

    /**
     * Retorna o codfil que estamos trabalhando
     *
     * @return - float com o codfil trabalhado
     */
    public float getCodfil() {
        return codfil;
    }

    /**
     * Retorna o parametro do banco
     *
     * @return - string em letras maiusculas com o parametro
     */
    public String getparamBanco() {
        if (empresa.equals("AGIL")) {
            return paramBanco;
        } else {
            return empresa;
        }

    }

    public static final boolean isTransvip(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 8);
        return nomeRecortado.toUpperCase().equals("TRANSVIP");
    }

    public static final boolean isTecban(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 6);
        String nomeRecortadoRed = RecortaString(persistencia.getEmpresa(), 0, 2);
        return nomeRecortado.toUpperCase().equals("TECBAN")
                || nomeRecortadoRed.toUpperCase().equals("TB");
    }

    public static final boolean isTransexpert(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 11);
        return nomeRecortado.toUpperCase().equals("TRANSEXPERT");
    }

    public static final boolean isPreserve(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 8);
        return nomeRecortado.toUpperCase().equals("PRESERVE");
    }

    public static final boolean isConfederal(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 10);
        return nomeRecortado.toUpperCase().equals("CONFEDERAL");
    }

    public static final boolean isBrasifort(Persistencia persistencia) {
        return persistencia.getEmpresa().toUpperCase().equals("BRASIFORT");
    }

    public static final boolean isTecno(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 5);
        return nomeRecortado.toUpperCase().equals("TECNO");
    }

    public static final boolean isProtege(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 7);
        return nomeRecortado.toUpperCase().equals("PROTEGE");
    }

    public static final boolean isSasw(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 4);
        return nomeRecortado.toUpperCase().equals("SASW");
    }

    public static final boolean isGloval(Persistencia persistencia) {
        String nomeRecortado = RecortaString(persistencia.getEmpresa(), 0, 5);
        return nomeRecortado.toUpperCase().equals("GLOVAL");
    }

}

/*
 */
package Controller.Guias;

import Dados.Persistencia;
import SasBeans.CxFGuiasVol;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.GuiasCliente;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.PreOrder;
import SasBeans.PreOrderVol;
import SasBeans.Rt_Guias;
import SasBeans.Rt_Perc;
import SasBeans.SASLog;
import SasBeans.SasPWFill;
import SasBeansCompostas.BBPedidoAgencia;
import SasBeansCompostas.BBPedidoMalote;
import SasBeansCompostas.EGtv;
import SasBeansCompostas.PreOrderManifesto;
import SasDaos.CxFGuiasVolDao;
import SasDaos.EGtvDao;
import SasDaos.FiliaisDao;
import SasDaos.FuncionDao;
import SasDaos.GuiasClienteDao;
import SasDaos.OS_VigDao;
import SasDaos.PedidoDao;
import SasDaos.PreOrderDao;
import SasDaos.PreOrderVolDao;
import SasDaos.RPVDao;
import SasDaos.Rt_GuiasDao;
import SasDaos.Rt_PercDao;
import SasDaos.SASLogDao;
import SasDaos.SasPwFilDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PedidosPreOrderSatWeb {

    public void atualizarLacre(PreOrder preOrder, String lacre, Persistencia persistencia) throws Exception {
        try {
            PreOrderVolDao preOrderVolDao = new PreOrderVolDao();
            preOrderVolDao.editarLacre(preOrder.getSequencia(), preOrder.getCodFil(), preOrder.getPreOrderVolOrdem(), lacre, preOrder.getPreOrderVolLacre(), persistencia);

            SASLogDao sasLogDao = new SASLogDao();
            SASLog sasLog = new SASLog();

            sasLog.setTabela("PreOrderVol");
            sasLog.setCodigo(preOrder.getSequencia());
            sasLog.setCodFil(preOrder.getCodFil());
            sasLog.setComando("ALTERACAO LACRE DE " + preOrder.getPreOrderVolLacre() + " PARA " + lacre);
            sasLog.setHistorico("ALTERACAO LACRE PREORDER DE " + preOrder.getPreOrderVolLacre() + " PARA " + lacre);
            sasLog.setData(getDataAtual("SQL"));
            sasLog.setHora(getDataAtual("HORA"));
            sasLog.setOperador(preOrder.getOperador());

            for (int i = 0; i < 20; i++) {

                sasLog.setSequencia(sasLogDao.maxSasLog(persistencia));

                if (sasLogDao.inserirSasLog(sasLog, persistencia)) {
                    break;
                }
            }
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Perc> graficoReforcos(String dtInicio, String dtFim, String codPessoa, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            return rt_PercDao.reforcos(dtInicio, dtFim, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Perc> graficoAlivios(String dtInicio, String dtFim, String codPessoa, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            return rt_PercDao.alivios(dtInicio, dtFim, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<PreOrder> listarManifestosDisponiveis(String CodFil, String dtInicio, String dtFim, Persistencia persistencia) throws Exception {
        try {
            PreOrderDao preOrderDao = new PreOrderDao();
            return preOrderDao.buscarPreOrder(CodFil, dtInicio, dtFim, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<PreOrderManifesto> listarManifestosPreOrder(String codfil, String dtColeta, String hora1D, Persistencia persistencia) throws Exception {
        try {
            PreOrderDao preOrderDao = new PreOrderDao();
            return preOrderDao.gerarManifesto(codfil, dtColeta, hora1D, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<GuiasCliente> Listar(BigDecimal codPessoa, String codFil, String codCli, String data1, String data2, Persistencia persistencia) throws Exception {
        try {
            GuiasClienteDao guiasClienteDAO = new GuiasClienteDao();
            List<GuiasCliente> retorno = guiasClienteDAO.listarGuias(codPessoa, codFil, codCli, data1, data2, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxFGuiasVol> ListarLacres(GuiasCliente guia, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfguiasvoldao = new CxFGuiasVolDao();
            return cxfguiasvoldao.getLacres(guia.getGuia(), guia.getSerie(), persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public String gerarCabecalho(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            RPVDao rpvDao = new RPVDao();
            return rpvDao.gerarCabecalho(sequencia, parada, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public SasPWFill buscaFilial(String CodFil, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFillLogin(CodFil, CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxFGuiasVol> listarLacres(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfguiasvoldao = new CxFGuiasVolDao();
            return cxfguiasvoldao.getLacres(guia, serie, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Guias> buscarGuia(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            Boolean pedido = rt_PercDao.existePedido(sequencia, parada, persistencia);
            Rt_GuiasDao rt_GuiasDao = new Rt_GuiasDao();

            List<Rt_Guias> guias = rt_GuiasDao.listaGuias(sequencia, parada, pedido, persistencia);
            if (guias.isEmpty()) {
                guias = rt_GuiasDao.listaGuiasEntrega(sequencia, parada, pedido, persistencia);
            }
            return guias;
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais filialNota(String codFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filiaisdao = new FiliaisDao();
            Filiais retorno = filiaisdao.getFilial(codFil, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Contagem do cadastro de guias
     *
     * @param primeiro
     * @param linhas
     * @param filtros - filtros para pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PreOrder> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            PreOrderDao preOrderDao = new PreOrderDao();
            return preOrderDao.buscarPreOrderPaginado(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            PreOrderDao preOrderDao = new PreOrderDao();
            return preOrderDao.totalPreOrder(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public int contagemVolumes(Map filtros, Persistencia persistencia) throws Exception {

        try {
            int retorno;
            GuiasClienteDao guiasClienteDAO = new GuiasClienteDao();
            retorno = guiasClienteDAO.volumesGuias(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal qtdVolumes(Map filtros, Persistencia persistencia) throws Exception {
        try {
            EGtvDao guiasClienteDAO = new EGtvDao();
            return guiasClienteDAO.qtdTotalVolumes(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal contagemValor(Map filtros, Persistencia persistencia) throws Exception {

        try {
            BigDecimal retorno;
            GuiasClienteDao guiasClienteDAO = new GuiasClienteDao();
            retorno = guiasClienteDAO.valorGuias(filtros, persistencia);
            if (null == retorno) {
                return BigDecimal.ZERO;
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal valorGuias(Map filtros, Persistencia persistencia) throws Exception {
        try {
            EGtvDao guiasClienteDAO = new EGtvDao();
            return guiasClienteDAO.valorTotalGuias(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<OS_Vig> buscarPedidos(String query, boolean tipo, String codigo, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_vigDao = new OS_VigDao();
            return os_vigDao.listarPedido(query, tipo, codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            pedidoDao.salvarPedido(pedido, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());

        }
    }

    public Rt_Guias obterInfoGuia(EGtv eGTV, Persistencia persistencia) throws Exception {
        try {
            Rt_GuiasDao rt_guiasDao = new Rt_GuiasDao();
            if (eGTV.getOperacao().equals("E") || eGTV.getOperacao().contains("Entrega")) {
                return rt_guiasDao.infoGuiaEntrega(eGTV.getGuia(), eGTV.getSerie(), persistencia);
            } else {
                return rt_guiasDao.infoGuia(eGTV.getGuia(), eGTV.getSerie(), null != eGTV.getPedido() && !eGTV.getPedido().equals(""), persistencia);
            }
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Verifica se já existe algum pedido importado para a data em questão.
     *
     * @param dtColeta
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean verificarExistenciaPreOrder(String dtColeta, String codCli, Persistencia persistencia) throws Exception {
        try {
            PreOrderDao preOrderDao = new PreOrderDao();
            return preOrderDao.verificarExistenciaPreOrder(dtColeta, codCli, persistencia);
        } catch (Exception e) {
            throw new Exception("guiass.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirPedidosImportados(List<BBPedidoAgencia> pedidosAgencia, String cliOri,
            String operador, Persistencia persistencia) throws Exception {
        StringBuilder erros = new StringBuilder();
        try {
            PreOrder preOrder;
            PreOrderVol preOrderVol;
            PreOrderDao preOrderDao = new PreOrderDao();
            PreOrderVolDao preOrderVolDao = new PreOrderVolDao();

            // Excluir pedidos anteriores para a mesma data antes de inserir novamente
//            preOrderVolDao.excluirPreOrderVol(pedidosAgencia.get(0).getDtColeta(), cliOri, persistencia);
//            preOrderDao.excluirPreOrder(pedidosAgencia.get(0).getDtColeta(), cliOri, persistencia);
            for (BBPedidoAgencia pedidoAgencia : pedidosAgencia) {
                for (BBPedidoMalote pedidoMalote : pedidoAgencia.getListaMalotes()) {
                    try {
                        preOrder = new PreOrder();
                        //                    preOrder.setSequencia(cliOri); max + 1
                        preOrder.setCodFil("2001");
                        preOrder.setBanco("001");
                        preOrder.setAgencia(pedidoAgencia.getAgencia());
                        preOrder.setSubAgencia(pedidoAgencia.getSubAgencia());
                        preOrder.setDtColeta(pedidoAgencia.getDtColeta());
                        preOrder.setDtEntrega(pedidoAgencia.getDtEntrega());
                        preOrder.setCodCli1(cliOri);
//                        preOrder.setHora1O(LocalTime.parse(pedidoMalote.getHorario()).plusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm")));
//                        preOrder.setHora2O(LocalTime.parse(pedidoMalote.getHorario()).minusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm")));
                        //                    preOrder.setCodCli2(cliOri);
                        preOrder.setHora1D(LocalTime.parse(pedidoMalote.getHorario()).minusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm")));
                        preOrder.setHora2D(LocalTime.parse(pedidoMalote.getHorario()).plusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm")));
//                        preOrder.setSolicitante(cliOri);
                        preOrder.setPedidoCliente(pedidoMalote.getPedidoCliente());
                        preOrder.setValor(pedidoMalote.getValor());
                        preOrder.setObs(pedidoMalote.getObs());
//                        preOrder.setClassifSrv(cliOri);
                        preOrder.setDt_Incl(getDataAtual("SQL"));
                        preOrder.setHr_Incl(getDataAtual("HORA"));
//                        preOrder.setOS(cliOri);
//                        preOrder.setChequesQtde(cliOri);
//                        preOrder.setChequesValor(cliOri);
                        preOrder.setOperador(operador);
                        preOrder.setDt_Alter(getDataAtual("SQL"));
                        preOrder.setHr_Alter(getDataAtual("HORA"));
//                        preOrder.setOperExcl(cliOri);
//                        preOrder.setDt_Excl(cliOri);
//                        preOrder.setHr_Excl(cliOri);
//                        preOrder.setSituacao(cliOri);
                        preOrder.setFlag_Excl("");

                        String sequencia = preOrderDao.inserirNovoPreOrder(preOrder, persistencia);

                        preOrderVol = new PreOrderVol();
                        preOrderVol.setSequencia(sequencia);
                        preOrderVol.setCodFil("2001");
//                        preOrderVol.setOrdem("1");
                        preOrderVol.setQtde(new BigDecimal(pedidoMalote.getValor())
                                //                                .divide(new BigDecimal("100"))
                                .divide(new BigDecimal("100"))
                                .toString());
                        preOrderVol.setLacre(pedidoMalote.getLacre());
                        preOrderVol.setTipo("T");
                        preOrderVol.setValor(pedidoMalote.getValor());

                        preOrderVolDao.inserirPreOrderVol(preOrderVol, persistencia);
                    } catch (Exception erro) {
                        erros.append(erro.getMessage());
                    }
                }
            }
            preOrderDao.atualizarCodCli2OS(persistencia);
        } catch (Exception e) {
            throw new Exception(erros.toString());
        }
    }

    public List<PreOrder> pedidosRecentes(String CodCli1, String CodFil, Persistencia persistencia) throws Exception {
        try {
            PreOrderDao preOrderDao = new PreOrderDao();
            return preOrderDao.listaPedidosRecentesCliente(CodCli1, CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Funcion> buscaNome(String nome, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funcion = new FuncionDao();
            return funcion.buscarFuncionNome(nome, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
        }
    }

//    public List<Pessoa> buscaPessoa (String nome, Persistencia persistencia) throws Exception{
//        try{
//            PessoaDao pessoa = new PessoaDao();
//            return pessoa.listaPessoaNomeCodigo(nome, persistencia);
//            
//        }catch (Exception e){
//            throw new Exception("PedidosPreOrderSatWeb.falhageral<message>" + e.getMessage());
//        }
//    }
}

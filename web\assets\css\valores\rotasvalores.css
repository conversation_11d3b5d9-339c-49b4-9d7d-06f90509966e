/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 19-Feb-2020, 11:47:16
    Author     : SASWRichard
*/

.ui-inputfield[id*="tolerancia"]{
    min-width:50px !important;
    width:50px !important;
    max-width:50px !important;
}
#divQuadroResumo .ui-selectonemenu-label,
#divQuadroResumo > .ui-selectonemenu-label{
    background-color:#CED2DA !important;
    padding:5px 3px 6px 10px !important;
    box-shadow:none !important;
    border:none !important;
}

#divQuadroResumo{
    position:absolute !important;
    width:240px !important;
    top:74px !important;
    left:26px !important;
    z-index:2 !important;
    height: 100%;
    max-height:500px !important;
}

#divQuadroResumo .ItemResumo{
    background-color: rgba(211, 221, 228, 0.85) !important;
    width:100% !important;
    border-radius:12px;
    padding:1px 1px 0px 1px !important;
    box-shadow:1px  1px 3px #666;
    border-radius:4px;
}

#divQuadroResumo .FiltrosMapa{
    width:100%;
    padding:8px 4px 8px 4px !important;
    outline:none;
    border:thin solid #CCC;
    background-color:#EEE;
    color:#505050;
    cursor:pointer
}

#divQuadroResumo input[type="checkbox"]{
    padding:0 !important;
    background-color:black;
    position:absolute;
    margin-top:-3px;
    border:thin solid #AAA !important;
    border-radius:2px !important;
}

#divQuadroResumo input[type="checkbox"] + label{
    font-size:10pt;
    font-weight:600;
    color:#505050;
    margin:0px !important;
    padding:0px 0px 0px 16px !important;
    cursor:pointer;
    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
}

#divQuadroResumo .Item,
#divQuadroResumo .ItemZebrado{
    position:relative;
    width:calc(100% - 12px) !important;
    margin-left: 6px !important;
    padding:8px;
    border-radius:2px;
}

#divQuadroResumo .ItemZebrado{
    background-color:rgba(187, 187, 187, 0.4) !important;
}

#divQuadroResumo #lblTituloFiltro{
    font-size:11pt !important;
    font-weight:600 !important;
    color:#000 !important;
    padding:5px 0px 3px 10px !important;
    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
}

#divQuadroResumo .QdeStatus{
    position: absolute;
    width:45px;
    height:18px;
    border-radius:4px;
    right:6px;
    top: 11px;
    font-size:8pt !important;
    text-align:center;
    color:#FFF;
    font-weight:bold;
    padding-top:0x !important;
}

#divQuadroResumo .QdeStatus[cor="R"]{
    background-color:#ff0000;
    border:thin solid #cc0000;
}

#divQuadroResumo .QdeStatus[cor="G"]{
    background-color: #00cc00;
    border:thin solid #009900;
}

#divQuadroResumo .QdeStatus[cor="Y"]{
    background-color: #ffba00;
    border:thin solid #cca300;
}

#divQuadroResumo .QdeStatus[cor="W"]{
    background-color:#FFF;
    color:#505050;
}

#divQuadroResumo .fa-fw{
    float:left;
    width:100%;
    text-align:center;
    margin-top:2px;
}

div[id*="infoWindowCamcambas"] .panelgrid .ui-widget{
    width:100% !important;
    z-index:2 !important;
}

div[id*="infoWindowCamcambas"] .ui-panelgrid-cell{
    text-align:left !important;
    font-size:9pt !important;
    white-space: nowrap !important;
    width:100% !important;
}

div[id*="infoWindowCamcambas"] .Titulo{
    font-weight:bold;
    width:100% !important;
    text-align:left;
    font-size:12pt !important;
    color:#5065a1;
}

div[id*="infoWindowCamcambas"] .Conteudo{
    font-weight:bold;
    width:100% !important;
    text-align:left !important;
    font-size:10pt !important;
    color:#000;
    display:block;
}

.tblDadosWindowInfo{
    width:100% !important;
    padding: 0px !important;
    box-shadow: 2px 2px 3px #CCC;
    border:thin solid #DDD;

    border-spacing: 2px !important;
    border-collapse:separate;
}

.tblDadosWindowInfo thead tr th{
    background-color:#5065a1;
    border: thin solid #445a9a;
    color:#FFF!important;
    text-align:center;
    font-weight:bold;
    padding:6px !important;
}

.tblDadosWindowInfo tbody tr td{
    background-color:#FFF;
    color:#666 !important;
    text-align:center;
    border:thin solid #DDD;
    font-weight:500;
    padding:4px 6px 4px 6px !important;
}

@media only screen and (max-width: 700px) and (min-width: 10px) {

    #divDadosFilial,
    #divDadosFilial div,
    .FilialNome,
    .FilialEndereco,
    .FilialBairroCidade{
        min-width:100% !important;
        width:100% !important;
        max-width:100% !important;
        text-align: center !important;
    }

    .ui-paginator-top {
        white-space: normal !important;
    }

    .tabela .ui-datatable-scrollable-body {
        flex-grow: 1;
        height: 100% !important;
    }
}

@media only screen and (max-width: 3000px) and (min-width: 701px) {
    .DataGrid{
        width:100% !important;
        border:none !important;
    }

    .DataGrid:not([id*="rotasDeTrajeto"]) [role="columnheader"] > span {
        top: -4px !important;
        position: relative !important;
    }

    .DataGrid[id*="rotasDeTrajeto"] [role="columnheader"] > span {
        top: 0px !important;
        position: static !important;
    }

    .DataGrid thead tr th,
    .DataGrid tbody tr td {
        min-width: 120px !important;
        max-width: 120px !important;
    }

    [id$="rotasDeTrajeto"] thead > tr > th {
        background: linear-gradient(to bottom, #202020, #555) !important;
        border-color:#999 !important;
    }

    /* médio | padrão */
    [id$="rotasDeTrajeto"] thead > tr > th,
    [id$="rotasDeTrajeto"] tbody > tr > td{
        min-width: 70px !important;
        max-width: 70px !important;
    }

    /* pequeno */
    [id$="rotasDeTrajeto"] thead > tr > th:nth-child(1),
    [id$="rotasDeTrajeto"] tbody > tr > td:nth-child(1),
    [id$="rotasDeTrajeto"] thead > tr > th:nth-child(7),
    [id$="rotasDeTrajeto"] tbody > tr > td:nth-child(7){
        min-width:30px !important;
        max-width:30px !important;
    }

    /* grande */
    [id$="rotasDeTrajeto"] thead > tr > th:nth-child(8),
    [id$="rotasDeTrajeto"] tbody > tr > td:nth-child(8),
    [id$="rotasDeTrajeto"] thead > tr > th:nth-child(10),
    [id$="rotasDeTrajeto"] tbody > tr > td:nth-child(10),
    [id$="rotasDeTrajeto"] thead > tr > th:nth-child(14),
    [id$="rotasDeTrajeto"] tbody > tr > td:nth-child(14),
    [id$="rotasDeTrajeto"] thead > tr > th:nth-child(16),
    [id$="rotasDeTrajeto"] tbody > tr > td:nth-child(16){
        min-width:150px !important;
        max-width:150px !important;
    }

    [id$="tabela"] thead > tr > th,
    [id$="tabela"] tbody > tr > td{
        min-width: 90px !important;
        max-width: 90px !important;
    }

    [id$="tabela"] thead > tr > th:nth-child(1),
    [id$="tabela"] tbody > tr > td:nth-child(1){
        min-width:30px !important;
        max-width:30px !important;
    }

    [id$="tabela"] thead > tr > th:nth-child(7),
    [id$="tabela"] tbody > tr > td:nth-child(7),
    [id$="tabela"] thead > tr > th:nth-child(17),
    [id$="tabela"] tbody > tr > td:nth-child(17){
        min-width: 120px !important;
        max-width: 120px !important;
    }

    [id$="tabela"] thead > tr > th:nth-child(4),
    [id$="tabela"] tbody > tr > td:nth-child(4),
    [id$="tabela"] thead > tr > th:nth-child(8),
    [id$="tabela"] tbody > tr > td:nth-child(8){
        min-width: 180px !important;
        max-width: 180px !important;
    }

    [id$="cadastrar"] thead > tr > th:nth-child(1),
    [id$="cadastrar"] tbody > tr > td:nth-child(1) {
        min-width: 50px !important;
        max-width: 50px !important;
        text-align: center;
    }

    [id$="cadastrar"] thead > tr > th:nth-child(2),
    [id$="cadastrar"] tbody > tr > td:nth-child(2) {
        min-width: 80px !important;
        max-width: 80px !important;
        text-align: center;
    }

    [id$="cadastrar"] thead > tr > th:nth-child(3),
    [id$="cadastrar"] tbody > tr > td:nth-child(3) {
        min-width: 150px !important;
        max-width: 150px !important;
        text-align: right;
    }

    [id$="trajetos"] thead > tr > th:nth-child(1),
    [id$="trajetos"] tbody > tr > td:nth-child(1) {
        min-width: 70px !important;
        max-width: 70px !important;
    }

    [id$="trajetos"] thead > tr > th:nth-child(3),
    [id$="trajetos"] tbody > tr > td:nth-child(3) {
        min-width: 35px !important;
        max-width: 35px !important;
    }
}

html, body{
    max-height:100% !important;
    overflow:hidden !important;
}
#divCorporativo{
    bottom:23px !important;
}

#corporativo {
    max-width: 18vw;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

#corporativo label[ref="lblCheck"]{
    font-size:11px !important;
    min-width:75px !important;
    font-weight:500 !important;
}

footer .ui-chkbox-box {
    max-width: 12px !important;
    max-height: 12px !important;
}

.ui-dialog .ui-panel-content {
    height: auto !important;
}

#formCadastrar .ui-selectonemenu.ui-state-default {
    background: #fff !important;
}

#formCadastrar .ui-selectonemenu.ui-state-disabled {
    color: #555 !important;
    background: #f7f7f7 !important;
    opacity: 0.7 !important;
}


#body {
    height: calc(100% - 40px);
    max-height: 100% !important;
    position: relative;
    display: flex;
    flex-direction: column;
}

#main {
    flex-grow: 1;
}

#formPesquisar .ui-radiobutton {
    background: transparent !important;
}

.ui-datatable-scrollable-body,
.FundoPagina > .ui-panel-content {
    height: 100% !important;
}

[id$="gridModelos"] > .ui-datatable-scrollable-body {
    height: calc(100% - 54px) !important;
}

[id*="cabecalho2:dlgDetalhesValores"] > .ui-dialog-content > .ui-widget-content {
    max-height: 65vh !important;
}

[id*="cadastroTrajeto:dlgCadastroTrajeto"] > .ui-dialog-content {
    max-height: 450px !important;
}

.FundoPagina {
    border: thin solid #CCC !important;
    border-top:4px solid #3C8DBC !important;
}

.verticalSpace {
    margin-bottom: 10px;
}

@media only screen and (max-width: 3000px) and (min-width: 701px) {
    .DataGrid [role="columnheader"] > span {
        top: -4px !important;
        position: relative !important;
    }
}

/*
 */
package br.com.sasw.lazydatamodels;

import Controller.NFiscal.NFiscalSatMobWeb;
import Dados.Persistencia;
import SasBeans.NFiscal;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class NFiscalLazyList extends LazyDataModel<NFiscal> {

    private static final long serialVersionUID = 1L;
    private List<NFiscal> nFiscais;
    private final NFiscalSatMobWeb nFiscalSatMobWeb;
    private final Persistencia persistencia;
    private final BigDecimal codPessoa;

    public NFiscalLazyList(Persistencia persistencia, BigDecimal codPessoa) {
        this.nFiscalSatMobWeb = new NFiscalSatMobWeb();
        this.persistencia = persistencia;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<NFiscal> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.nFiscais = this.nFiscalSatMobWeb.listaPaginada(first, pageSize, filters, this.codPessoa, this.persistencia);

            // set the total of players
            setRowCount(this.nFiscalSatMobWeb.total(filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.nFiscais;
    }

    @Override
    public Object getRowKey(NFiscal nFiscal) {
        return nFiscal.getPraca() + ";" + nFiscal.getSerie().replace(".0", "") + ";" + nFiscal.getNumero().replace(".0", "");
    }

    @Override
    public NFiscal getRowData(String codigo) {
        try {
            if (codigo.split(";").length != 3) {
                return null;
            }
            String praca = codigo.split(";")[0].replace(".0", "");
            String serie = codigo.split(";")[1].replace(".0", "");
            String numero = codigo.split(";")[2].replace(".0", "");
            for (NFiscal nFiscal : this.nFiscais) {
                if (praca.equals(String.valueOf(nFiscal.getPraca()).replace(".0", ""))
                        && serie.equals(nFiscal.getSerie().replace(".0", ""))
                        && numero.equals(nFiscal.getNumero().replace(".0", ""))) {
                    return nFiscal;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}

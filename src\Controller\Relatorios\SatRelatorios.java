/*
 */
package Controller.Relatorios;

import Dados.Persistencia;
import SasBeans.SatWebService.HorasEscala_HorasCobrir;
import SasBeans.SatWebService.Visitas_Dia_Regiao;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.HorasEscala_HorasCobrirDao;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.Visitas_Dia_RegiaoDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SatRelatorios {

    public List<HorasEscala_HorasCobrir> horasEscala_horasCobrir_geral(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            HorasEscala_HorasCobrirDao horasEscalaHorasCobrirDao = new HorasEscala_HorasCobrirDao();
            return horasEscalaHorasCobrirDao.horasEscalaHorasCobrirGeralPorCliente(dataInicio, dataFim, persistencia);
        } catch (Exception e) {
            throw new Exception("Falha ao gerar relatório geral - " + e.getMessage());
        }
    }

    public List<Visitas_Dia_Regiao> visitas_Regiao_Dia(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            Visitas_Dia_RegiaoDao visitas_Dia_RegiaoDao = new Visitas_Dia_RegiaoDao();
            return visitas_Dia_RegiaoDao.visitasPorRegiaoPorDia(dataInicio, dataFim, persistencia);
        } catch (Exception e) {
            throw new Exception("Falha ao gerar relatório geral - " + e.getMessage());
        }
    }
}

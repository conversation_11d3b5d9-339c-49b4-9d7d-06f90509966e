package br.com.sasw.pacotesuteis.utilidades;

/**
 * <PERSON><PERSON>
 */
public class Horaminuto {

    private String Hora1, Hora2, Retorno, Horacompleta;
    private int resultado, teste1;

    public Horaminuto() {
    }

    public Horaminuto(String Hora1, String Hora2) {
        setHora1(Hora1);
        setHora2(Hora2);
    }

    /**
     * Atribuir 2 valores de horas ao mesmo tempo
     *
     * @param H1 - hora 1 formatos (hh:mm ou h:mm ou hhmm ou hmm)
     * @param H2 - hora 2 formatos (hh:mm ou h:mm ou hhmm ou hmm)
     */
    public void setHoras(String H1, String H2) {
        this.setHora1(H1);
        this.setHora2(H2);
    }

    /**
     * Atribuir o primeiro horario
     *
     * @param H1 hora 1 formatos (hh:mm ou h:mm ou hhmm ou hmm)
     */
    public void setHora1(String H1) {
        String Aux1, Aux2;
        if (H1.contains(":")) {
            Aux1 = H1.substring(0, H1.indexOf(":"));
            Aux2 = H1.substring(H1.indexOf(":") + 1);
            Hora1 = Aux1 + Aux2;
        } else {
            Hora1 = H1;
        }
        if (Hora1.length() < 4) {
            try {
                Hora1 = FuncoesString.PreencheEsquerda(Hora1, 4, "0");
            } catch (Exception e) {
            }
        }
    }

    /**
     * Atribuir o segundo horario
     *
     * @param H2 hora 2 formatos (hh:mm ou h:mm ou hhmm ou hmm)
     */
    public void setHora2(String H2) {
        String Aux1, Aux2;
        if (H2.contains(":")) {
            Aux1 = H2.substring(0, H2.indexOf(":"));
            Aux2 = H2.substring(H2.indexOf(":") + 1);
            Hora2 = Aux1 + Aux2;
        } else {
            Hora2 = H2;
        }
        if (Hora2.length() < 4) {
            try {
                Hora2 = FuncoesString.PreencheEsquerda(Hora2, 4, "0");
            } catch (Exception e) {
            }
        }
    }

    /**
     * Setar uma string de horario Completo
     *
     * @param H3 pode ser nos formatos (hh:mm:ss ou hhmmss)
     */
    public void setHoraCompleta(String H3) {
        String Aux1, Aux2, Aux3, Aux4;
        Aux1 = H3;
        if (Aux1.length() == 8) {
            Aux2 = Aux1.substring(0, 2);
            Aux3 = Aux1.substring(3, 5);
            Aux4 = Aux1.substring(6);
        } else if (Aux1.length() == 6) {
            Aux2 = Aux1.substring(0, 2);
            Aux3 = Aux1.substring(2, 4);
            Aux4 = Aux1.substring(4);
        } else {
            Aux2 = "";
            Aux3 = "";
            Aux4 = "";
        }
        Horacompleta = Aux2 + Aux3 + Aux4;
    }

    /**
     * Devolve a hora1 em string formatada HH:mm
     *
     * @return
     */
    public String getHora1formatada() {
        String ret;
        ret = this.min2hora(this.getHora1Integer());
        return ret;
    }

    /**
     * Devolve a hora2 em string formatada HH:mm
     *
     * @return
     */
    public String getHora2formatada() {
        String ret;
        ret = this.min2hora(this.getHora2Integer());
        return ret;
    }

    /**
     * Devolve a Hora 1 em minutos
     *
     * @return minutos em inteiros
     */
    public int getHora1Integer() {
        int aux1, aux2, aux3;
        aux1 = Integer.parseInt(Hora1);
        aux2 = aux1 % 100;
        aux3 = aux1 / 100;
        resultado = aux2 + (aux3 * 60);
        return resultado;
    }

    /**
     * Devolve a Hora 2 em minutos
     *
     * @return minutos em inteiros
     */
    public int getHora2Integer() {
        int aux1, aux2, aux3;
        aux1 = Integer.parseInt(Hora2);
        aux2 = aux1 % 100;
        aux3 = aux1 / 100;
        resultado = aux2 + (aux3 * 60);
        return resultado;
    }

    /**
     * Diferenca entre Hora1 e Hora 2
     *
     * @return minutos em inteiros
     */
    public int iDifHora1Hora2min() {
        resultado = this.getHora1Integer() - this.getHora2Integer();
        return resultado;
    }

    /**
     * Devolve diferenca entre Hora1 e Hora2
     *
     * @return String formatada (HH:mm)
     */
    public String DifHora1Hora2() {
        int aux1, aux2, neg = 0;
        String Ret;
        resultado = this.iDifHora1Hora2min();
        if (resultado < 0) {
            neg = -1;
            resultado *= neg;
        }
        Ret = this.min2hora(resultado);
        if (neg == -1) {
            Retorno = "-" + Ret;
        } else {
            Retorno = Ret;
        }
        return Retorno;
    }

    /**
     * Soma entre Hora1 e Hora 2
     *
     * @return minutos em inteiros
     */
    public int iSomaHora1Hora2min() {
        resultado = this.getHora1Integer() + this.getHora2Integer();
        return resultado;
    }

    /**
     * Devolve Soma entre Hora1 e Hora2
     *
     * @return String formatada (HH:mm)
     */
    public String SomaHora1Hora2() {
        int aux1, aux2, neg = 0;
        String Ret;
        resultado = this.iSomaHora1Hora2min();
        if (resultado < 0) {
            resultado = 1440 + resultado;
        }
        if (resultado > 1439) { //verifica se maior que 23:59
            resultado -= 1440; //subtrai para chegar ao horario após as 23:59
        }
        Ret = this.min2hora(resultado);
        return Ret;
    }

    /**
     * Converte minutos em horas formatda
     *
     * @param minutos - minutos em inteiros
     * @return Hora formatada em (HH:mm)
     */
    public String min2hora(int minutos) {
        String Ret;
        int aux1, aux2;
        aux1 = minutos % 60;
        aux2 = minutos / 60;
        if (aux1 / 10 == 0) {
            if (aux2 / 10 == 0) {
                Ret = "0" + String.valueOf(aux2) + ":0" + String.valueOf(aux1);
            } else {
                Ret = String.valueOf(aux2) + ":0" + String.valueOf(aux1);
            }
        } else {
            if (aux2 / 10 == 0) {
                Ret = "0" + String.valueOf(aux2) + ":" + String.valueOf(aux1);
            } else {
                Ret = String.valueOf(aux2) + ":" + String.valueOf(aux1);
            }
        }
        return Ret;
    }

    /**
     * Reduz a hora1 em 180 minutos
     *
     * @return - String formatada (HH:mm)
     */
    public String reduzHora1formatada() {
        int horacerta;
        horacerta = this.getHora1Integer() - 180;
        return this.min2hora(horacerta);
    }

    /**
     * Reduz a hora2 em 180 minutos
     *
     * @return - String formatada (HH:mm)
     */
    public String reduzHora2formatada() {
        int horacerta;
        horacerta = this.getHora2Integer() - 180;
        return this.min2hora(horacerta);
    }

    /**
     * Transforma Hora Completa em segundos
     *
     * @return segundos em integer
     */
    public int getHoracompletaInteger() {
        int aux1, aux2, aux3, aux4;
        aux1 = Integer.parseInt(Horacompleta);
        aux2 = aux1 % 100;
        aux3 = (aux1 % 10000) / 100;
        aux4 = aux1 / 10000;
        resultado = aux2 + (aux3 * 60) + (aux4 * 3600);
        return resultado;
    }

    /**
     * Devolve Hora Completa formatda
     *
     * @return String (HH:mm:SS)
     */
    public String getHoracompletaformatada() {
        String ret;
        ret = this.seg2hora(this.getHoracompletaInteger());
        return ret;
    }

    /**
     * Reduz a hora completa em 10800 segundos
     *
     * @return - Hora completa formatada (HH:mm:SS)
     */
    public String reduzHoraCompleta() {
        return this.seg2hora(this.getHoracompletaInteger() - (10800));
    }

    /**
     * Transforma segundos em Hora Completa
     *
     * @param segundos - segundos em inteiros
     * @return - string formatada (HH:mm:SS)
     */
    public String seg2hora(int segundos) {
        String Ret;
        int aux1, aux2, aux3, aux4;
        aux1 = segundos % 60;
        aux4 = (segundos - aux1) / 60;
        aux2 = aux4 % 60;
        aux3 = (aux4 - aux2) / 60;
        if (aux1 / 10 == 0) {
            if (aux2 / 10 == 0) {
                if (aux3 / 10 == 0) {
                    Ret = "0" + String.valueOf(aux3) + ":0" + String.valueOf(aux2) + ":0" + String.valueOf(aux1);
                } else {
                    Ret = String.valueOf(aux3) + ":0" + String.valueOf(aux2) + ":0" + String.valueOf(aux1);
                }

            } else {
                if (aux3 / 10 == 0) {
                    Ret = "0" + String.valueOf(aux3) + ":" + String.valueOf(aux2) + ":0" + String.valueOf(aux1);
                } else {
                    Ret = String.valueOf(aux3) + ":" + String.valueOf(aux2) + ":0" + String.valueOf(aux1);
                }
            }
        } else {
            if (aux2 / 10 == 0) {
                if (aux3 / 10 == 0) {
                    Ret = "0" + String.valueOf(aux3) + ":0" + String.valueOf(aux2) + ":" + String.valueOf(aux1);
                } else {
                    Ret = String.valueOf(aux3) + ":0" + String.valueOf(aux2) + ":" + String.valueOf(aux1);
                }

            } else {
                if (aux3 / 10 == 0) {
                    Ret = "0" + String.valueOf(aux3) + ":" + String.valueOf(aux2) + ":" + String.valueOf(aux1);
                } else {
                    Ret = String.valueOf(aux3) + ":" + String.valueOf(aux2) + ":" + String.valueOf(aux1);
                }
            }
        }
        return Ret;
    }
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Pessoa;
import SasBeans.QueueFech;
import SasBeansCompostas.QueueFechDTO;
import SasBeansCompostas.QueueFechPessoaDTO;
import SasBeansCompostas.QueueFechSolicitacaoSenhaDTO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueueFechDao {

    private Persistencia persistencia;

    public QueueFechDao() {
    }

    public QueueFechDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    /**
     * Verififca atualização senha
     *
     * @param codPessoa
     * @param persistencia - Conexão ao banco
     * @return - Lista com dados da parada
     * @throws java.lang.Exception
     */
    public QueueFech verificaSolicitacaoPanico(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT TOP 1 QueueFech.* FROM QueueFech \n"
                    + "WHERE CodPessoa = ? AND Comando_Ref = 'PANIC'\n"
                    + "ORDER BY Sequencia DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.select();
            QueueFech retorno = null;
            if (consulta.Proximo()) {
                retorno = new QueueFech();
                retorno.setSequencia(consulta.getString("Sequencia"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setTipoOperacao(consulta.getString("TipoOperacao"));
                retorno.setCodFech(consulta.getString("CodFech"));
                retorno.setCodPessoa(consulta.getString("CodPessoa"));
                retorno.setFechIdentif(consulta.getString("FechIdentif"));
                retorno.setBottom(consulta.getString("Bottom"));
                retorno.setPathSrv(consulta.getString("PathSrv"));
                retorno.setChaveSrv(consulta.getString("ChaveSrv"));
                retorno.setContraSenha(consulta.getString("ContraSenha"));
                retorno.setSenha(consulta.getString("Senha"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setData(consulta.getString("Data"));
                retorno.setHora(consulta.getString("Hora"));
                retorno.setPk_Fechadura(consulta.getString("Pk_Fechadura"));
                retorno.setDataHoraUltAtual(consulta.getString("DataHoraUltAtual"));
                retorno.setDataHoraComando(consulta.getString("DataHoraComando"));
                retorno.setSeqRota(consulta.getString("SeqRota"));
                retorno.setParada(consulta.getString("Parada"));
                retorno.setHoraComando(consulta.getString("HoraComando"));
                retorno.setComando_Org(consulta.getString("Comando_Org"));
                retorno.setComando_Ref(consulta.getString("Comando_Ref"));
                retorno.setComando_Crt(consulta.getString("Comando_Crt"));
                retorno.setSenha_Fecha(consulta.getString("Senha_Fecha"));
                retorno.setComando_Aceito(consulta.getString("Comando_Aceito"));
                retorno.setSrvOK(consulta.getString("SrvOK"));
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setPrecisao(consulta.getString("Precisao"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("QueueFechDao.verificaSolicitacaoPanico - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 * FROM QueueFech \n"
                    + "WHERE CodPessoa = " + codPessoa + " AND Comando_Ref = 'PANIC'\n"
                    + "ORDER BY Sequencia DESC ");
        }
    }

    public void inserirPanico(QueueFech queueFech, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO QueueFech \n"
                    + " (Sequencia, Codfil, CodPessoa, Operador, Data, Hora, DataHoraComando, SeqRota, HoraComando, \n"
                    + " Latitude, Longitude, Precisao, TipoOperacao, Comando_Ref, Comando_Crt, FechIdentif, ChaveSrv) \n"
                    + " VALUES ((SELECT ISNULL(MAX(Sequencia), 0) + 1 FROM QueueFech),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(queueFech.getCodFil());
            consulta.setString(queueFech.getCodPessoa());
            consulta.setString(queueFech.getOperador());
            consulta.setString(queueFech.getData());
            consulta.setString(queueFech.getHora());
            consulta.setString(queueFech.getDataHoraComando());
            consulta.setString(queueFech.getSeqRota());
            consulta.setString(queueFech.getHoraComando());
            consulta.setString(queueFech.getLatitude());
            consulta.setString(queueFech.getLongitude());
            consulta.setString(queueFech.getPrecisao());
            consulta.setString(queueFech.getTipoOperacao());
            consulta.setString(queueFech.getComando_Ref());
            consulta.setString(queueFech.getComando_Crt());
            consulta.setString(queueFech.getFechIdentif());
            consulta.setString(queueFech.getChaveSrv());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("QueueFechDao.inserirPanico - " + e.getMessage());
        }
    }

    /**
     * Atualiza comando
     *
     * @param queueseq - Sequencia da tabela QueueFech
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void atualizaComandoRef(String queueseq, Persistencia persistencia) throws Exception {
        String sql = "update queuefech "
                + " set Comando_Ref = 'SOLICITA' "
                + " where sequencia = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(queueseq);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("QueueFechDao.atualizaComandoRef - " + e.getMessage() + "\r\n"
                    + "update queuefech "
                    + " set Comando_Ref = 'SOLICITA' "
                    + " where sequencia = " + queueseq);
        }
    }

    /**
     * Seleciona dados da tabela QueueFech
     *
     * @param queueseq - Seqüência de QueueFech
     * @param persistencia - Conexão ao banco
     * @return - Lista com dados QueueFech
     * @throws java.lang.Exception - pode gerar exception
     */
    public QueueFech pegaCampos(String queueseq, Persistencia persistencia) throws Exception {
        QueueFech queue = null;
        try {
            Consulta rs = new Consulta("Select "
                    + " QueueFech.CodPessoa, "
                    + " QueueFech.Comando_Org, "
                    + " QueueFech.Senha, "
                    + " Pessoa.Matr, "
                    + " QueueFech.ChaveSRV,"
                    + " QueueFech.CodFil,"
                    + " QueueFech.FechIdentif, "
                    + " fechaduras.ModTecban "
                    + " from QueueFech "
                    + " left join pessoa on pessoa.codigo = QueueFech.codpessoa"
                    + " left Join Fechaduras on fechaduras.Codigo = Queuefech.Codfech "
                    + "                 and fechaduras.CodFil = QueueFech.CodFil "
                    + " where queuefech.sequencia = ?", persistencia);
            rs.setString(queueseq);
            rs.select();
            while (rs.Proximo()) {
                queue = new QueueFech();
                queue.setCodPessoa(rs.getString("CodPessoa"));
                queue.setComando_Org(rs.getString("Comando_Org"));
                queue.setSenha(rs.getString("Senha"));
                queue.setMatr(rs.getString("Matr").replace(".0", ""));
                queue.setChaveSrv(rs.getString("ChaveSRV"));
                queue.setCodFil(rs.getString("CodFil"));
                queue.setFechIdentif(rs.getString("FechIdentif"));
                queue.setModTecban(rs.getString("ModTecban"));
            }
            rs.Close();
            return queue;
        } catch (Exception e) {
            throw new Exception("QueueFechDao.pegaCampos - " + e.getMessage() + "\r\n"
                    + "Select "
                    + " QueueFech.CodPessoa, "
                    + " QueueFech.Comando_Org, "
                    + " QueueFech.Senha, "
                    + " Pessoa.Matr, "
                    + " QueueFech.ChaveSRV,"
                    + " QueueFech.CodFil,"
                    + " QueueFech.FechIdentif, "
                    + " fechaduras.ModTecban "
                    + " from QueueFech "
                    + " left join pessoa on pessoa.codigo = QueueFech.codpessoa"
                    + " left Join Fechaduras on fechaduras.Codigo = Queuefech.Codfech "
                    + "                 and fechaduras.CodFil = QueueFech.CodFil "                    
                    + " where queuefech.sequencia = " + queueseq);
        }
    }

    /**
     * Atualiza comando e referencia na tabela queuefech
     *
     * @param aceita - Confirma aceitação comando
     * @param comando_Ref - Referência comando
     * @param sequencia - Seqüência de queuefech
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void atualizaComando(String aceita, String comando_Ref, String sequencia, Persistencia persistencia) throws Exception {
        String sql = "update queuefech "
                + " set Comando_Aceito = ?, Comando_Ref = ? "
                + " where sequencia = ?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(aceita);
            consulta.setString(comando_Ref);
            consulta.setString(sequencia);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("QueueFechDao.atualizaComando - " + e.getMessage() + "\r\n"
                    + "update queuefech "
                    + " set Comando_Aceito = " + aceita + ", Comando_Ref = " + comando_Ref
                    + " where sequencia = " + sequencia);
        }
    }

    /**
     * Atualiza hora de chegada e atraso(min) da Parada da Rota Selecionada
     *
     * @param queueseq - Seqüência de QueueFech
     * @param persistencia - Conexão ao banco
     * @return - Lista com dados
     * @throws java.lang.Exception - pode gerar exception
     */
    public QueueFech chaveSrv(String queueseq, Persistencia persistencia) throws Exception {
        QueueFech queue = null;
        try {
            Consulta rs = new Consulta("Select Comando_Ref, ChaveSrv, CodFech, "
                    + " QueueFech.CodPessoa, "
                    + " QueueFech.Comando_Org, "
                    + " QueueFech.Senha, "
                    + " QueueFech.ChaveSRV,"
                    + " QueueFech.FechIdentif,"
                    + " QueueFech.SeqRota,"
                    + " QueueFech.Parada"
                    + " from queuefech "
                    + " where sequencia = ?", persistencia);
            rs.setString(queueseq);
            rs.select();
            while (rs.Proximo()) {
                queue = new QueueFech();
                queue.setComando_Ref(rs.getString("Comando_Ref"));
                queue.setChaveSrv(rs.getString("ChaveSrv"));
                queue.setCodFech(rs.getString("CodFech"));
                queue.setSeqRota("SeqRota");
                queue.setParada("Parada");
            }
            rs.Close();
            return queue;
        } catch (Exception e) {
            throw new Exception("QueueFechDao.chaveSrv - " + e.getMessage() + "\r\n"
                    + "Select Comando_Ref, ChaveSrv, CodFech, "
                    + " QueueFech.CodPessoa, "
                    + " QueueFech.Comando_Org, "
                    + " QueueFech.Senha, "
                    + " QueueFech.ChaveSRV,"
                    + " QueueFech.FechIdentif,"
                    + " QueueFech.SeqRota,"
                    + " QueueFech.Parada"
                    + " from queuefech "
                    + " where sequencia = " + queueseq);
        }
    }

    /**
     * Atualiza contra senha
     *
     * @param queueseq - Seqüência de QueueFech
     * @param persistencia - Conexão ao banco
     * @throws Exception - pode gerar exception
     */
    public void atualizaContraSenha(String queueseq, Persistencia persistencia) throws Exception {
        String sql = "update queuefech "
                   + " set SRVOK = 'R' "
                   + " where sequencia = ?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(queueseq);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("QueueFechDao.atualizaContraSenha - " + e.getMessage() + "\r\n"
                    + "update queuefech "
                    + " set SRVOK = 'R' "
                    + " where sequencia = " + queueseq);
        }
    }

    /**
     * Seleciona contra senha
     *
     * @param queueseq - Seqüência de QueueFech
     * @param persistencia - Conexão ao banco
     * @return - String contra senha
     * @throws java.lang.Exception - pode gerar exception
     */
    public String contraSenha(String queueseq, Persistencia persistencia) throws Exception {
        String contrasenha = null;
        try {
            Consulta rs = new Consulta("select ContraSenha "
                    + " from QueueFech where Sequencia = ?"
                    + " and ContraSenha is not null", persistencia);
            rs.setString(queueseq);
            rs.select();
            //busca a senha
            while (rs.Proximo()) {
                contrasenha = rs.getString("ContraSenha");
            }
            rs.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar senha - " + e.getMessage());
        }
        return contrasenha;
    }

    /**
     * Seleciona sequencia
     *
     * @param persistencia - Conexão ao banco
     * @return - O valor da maior sequencia em BigDecimal
     * @throws java.lang.Exception - pode gerar exception
     */

    public BigDecimal sequencia(Persistencia persistencia) throws Exception {
        Consulta consult;
        BigDecimal sequencia = null;
        //encontra o proximo número de sequencia na tabela
        try {
            consult = new Consulta("Select MAX(Sequencia) Sequencia From QueueFech", persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    sequencia = new BigDecimal(consult.getString("Sequencia"));
                } catch (Exception e) {
                    sequencia = new BigDecimal("1");
                }
            }
            consult.Close();
            return sequencia;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar posicao QueueFech - " + e.getMessage());
        }
    }


    public BigDecimal sequencia(Persistencia persistencia, BigDecimal seqRota, 
            String parada) throws Exception {
        Consulta consult;
        BigDecimal sequencia = null;
        //encontra o proximo número de sequencia na tabela
        try {
            consult = new Consulta("Select MAX(Sequencia) Sequencia From QueueFech "
                    + "Where Seqrota = " + seqRota.toString() 
                            + " and Parada = " + parada + 
                    " order by Sequencia desc",  persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    sequencia = new BigDecimal(consult.getString("Sequencia"));
                } catch (Exception e) {
                    sequencia = new BigDecimal("1");
                }
            }
            consult.Close();
            return sequencia;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar posicao QueueFech - " + e.getMessage());
        }
    }

    /**
     * Seleciona pw
     *
     * @param persistencia - Conexão ao banco
     * @return - A senha gravada em PW
     * @throws java.lang.Exception - pode gerar exception
     */
    public String pw(String codPessoa, Persistencia persistencia) throws Exception {
        Consulta consult;
        String retorno = null;
        
        try {
            //consult = new Consulta("SELECT COALESCE(pw,pwweb) pw FROM pessoa where codigo = ?", persistencia); //Carlos 15/12/2021
            consult = new Consulta("SELECT  pw FROM pessoa where codigo = ?", persistencia);
            consult.setString(codPessoa);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getString("pw");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar pw QueueFech - " + e.getMessage());
        }
    }
    
    /**
     * Grava dados na tabela QueueFech
     *
     * @param sequencia - Seqüência de QueueFech
     * @param codfil - Número codFil
     * @param codpessoa - Número código pessoa
     * @param codcli - Número código cliente
     * @param ATMCAR -
     * @param pw -
     * @param h1 -
     * @param data - Data
     * @param hora - Hora
     * @param sequencia1 - Sequencia rota
     * @param parada - Número paradas
     * @param latitude - Número latitude
     * @param longitude - Numero longitude
     * @param precisao -
     * @param codfech
     * @param persistencia - Conexão ao banco
     */
    public void insereQueueFech(BigDecimal sequencia, String codfil, String codpessoa, String codcli, String ATMCAR,
            String pw, String h1, String data, String hora, BigDecimal sequencia1, String parada, String latitude,
            String longitude, String precisao, String codfech, String senha_fecha, String porta, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into QueueFech "
                    + " (Sequencia, Codfil, TipoOperacao, CodPessoa, FechIdentif, ChaveSrv, "
                    + " Senha, Hora, DataHoraComando, HoraComando, Comando_Ref, SeqRota, Parada,"
                    + " Latitude, Longitude, Precisao, CodFech, Senha_Fecha, Porta)"
                    + " Select Max(Sequencia) + 1,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? from QueueFech";
            Consulta consulta = new Consulta(sql, persistencia);
//            consulta.setBigDecimal(sequencia);;
            consulta.setString(codfil);
            consulta.setString("1");
            consulta.setString(codpessoa); //Codpessoa pessoa
            consulta.setString(codcli);//codcli1 rt_perc
            consulta.setString(ATMCAR); //ATM ou VEICULO
            //consulta.setString(pw); //pw pessoa criptografada
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.SatCripto.Criptografar(senha_fecha, "Active Solutions SAS Systems")); //pw pessoa criptografada //Alterado para pegar senha Cripto Carlos 14/12            
            consulta.setString(h1); //hora1 da rt_perc
            consulta.setString(data);
            consulta.setString(hora);
            consulta.setString("SOLICITA");
            consulta.setBigDecimal(sequencia1);
            consulta.setString(parada);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(precisao);
            consulta.setString(codfech);
            consulta.setString(senha_fecha);
            consulta.setString(porta);
            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("QueueFechDao.insereQueueFech - " + e.getMessage() + "\r\n"
                    + " insert into QueueFech "
                    + " (Sequencia, Codfil, TipoOperacao, CodPessoa, FechIdentif, ChaveSrv, "
                    + " Senha, Hora, DataHoraComando, HoraComando, Comando_Ref, SeqRota, Parada,"
                    + " Latitude, Longitude, Precisao, CodFech, Senha_Fecha)"
                    + " values (sequencia, codfil, \"1\", codpessoa, codcli, ATMCAR, pw, h1, data, hora, \"SOLICITA\", "
                    + " sequencia1, parada, latitude, longitude, precisao, codfech, senha_fecha)");
        }
    }

    /**
     * Grava dados na tabela QueueFech
     *
     * @param sequencia - Seqüência de QueueFech
     * @param codfil - Número codFil
     * @param codpessoa - Número código pessoa
     * @param codcli - Número código cliente
     * @param ATMCAR -
     * @param pw -
     * @param h1 -
     * @param data - Data
     * @param hora - Hora
     * @param sequencia1 - Sequencia rota
     * @param parada - Número paradas
     * @param latitude - Número latitude
     * @param longitude - Numero longitude
     * @param precisao -
     * @param codfech
     * @param tokenTB
     * @param persistencia - Conexão ao banco
     */
    public void insereQueueFechTB(BigDecimal sequencia, String codfil, String codpessoa, String codcli, String ATMCAR,
            String pw, String h1, String data, String hora, BigDecimal sequencia1, String parada, String latitude,
            String longitude, String precisao, String codfech, String senha_fecha, String tokenTB, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into QueueFech "
                    + " (Sequencia, Codfil, TipoOperacao, CodPessoa, FechIdentif, ChaveSrv, "
                    + " Senha, Hora, DataHoraComando, HoraComando, Comando_Ref, SeqRota, Parada,"
                    + " Latitude, Longitude, Precisao, CodFech, Senha_Fecha, Bottom)"
                    + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.setString(codfil);
            consulta.setString("1");
            consulta.setString(codpessoa); //Codpessoa pessoa
            consulta.setString(codcli);//codcli1 rt_perc
            consulta.setString(ATMCAR); //ATM ou VEICULO
            //consulta.setString(pw); //pw pessoa criptografada
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.SatCripto.Criptografar(senha_fecha, "Active Solutions SAS Systems")); //pw pessoa criptografada //Alterado para pegar senha Cripto Carlos 14/12            
            consulta.setString(h1); //hora1 da rt_perc
            consulta.setString(data);
            consulta.setString(hora);
            consulta.setString("SOLICITA");
            consulta.setBigDecimal(sequencia1);
            consulta.setString(parada);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(precisao);
            consulta.setString(codfech);
            consulta.setString(senha_fecha);
            consulta.setString(tokenTB);
            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("QueueFechDao.insereQueueFech - " + e.getMessage() + "\r\n"
                    + " insert into QueueFech "
                    + " (Sequencia, Codfil, TipoOperacao, CodPessoa, FechIdentif, ChaveSrv, "
                    + " Senha, Hora, DataHoraComando, HoraComando, Comando_Ref, SeqRota, Parada,"
                    + " Latitude, Longitude, Precisao, CodFech, Senha_Fecha)"
                    + " values (sequencia, codfil, \"1\", codpessoa, codcli, ATMCAR, pw, h1, data, hora, \"SOLICITA\", "
                    + " sequencia1, parada, latitude, longitude, precisao, codfech, senha_fecha)");
        }
    }
    
    /**
     * Verififca atualização senha Token
     *
     * @param seqrota - Sequencia de rota
     * @param parada -
     * @param persistencia - Conexão ao banco
     * @return - Lista com dados da parada
     */
    public QueueFech verificaSolicitacaoSenha(String seqrota, String parada, Persistencia persistencia)
            throws Exception {
        QueueFech qf = null;
        try {
            Consulta consult = new Consulta("select top 1 SrvOK,Sequencia,ContraSenha "
                    + " from QueueFech "
                    + " where seqrota=?"
                    + " and parada= ?"
                    + " and Comando_Ref<>'RECUSADO' "
                    + " and Comando_Ref<>'ACEITO'"
                    + " and ((ContraSenha<>'RECUSADO' and  ContraSenha<>'CANCELADO') or ContraSenha is null)"
                    + " order by sequencia desc", persistencia);
            consult.setString(seqrota);
            consult.setString(parada);
            consult.select();
            while (consult.Proximo()) {
                qf = new QueueFech();
                qf.setSequencia(consult.getString("Sequencia"));
                qf.setSrvOK(consult.getString("SrvOK"));
                qf.setContraSenha(consult.getString("ContraSenha"));
            }
            consult.Close();
            return qf;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar solicitacao anterior - " + e.getMessage());
        }
    }

    public QueueFech verificaSolicitacaoSenha(String seqrota, String parada, String CodFech, Persistencia persistencia)
            throws Exception {
        QueueFech qf = null;
        try {
            Consulta consult = new Consulta("select top 1 SrvOK,Sequencia,ContraSenha \n"
                    + " from QueueFech \n"
                    + " where seqrota=?\n"
                    + " and parada= ?\n"
                    + " and CodFech = ?\n"
                    + " and Comando_Ref<>'RECUSADO' \n"
                    + " and Comando_Ref<>'ACEITO'\n"
                    + " and ((ContraSenha<>'RECUSADO' and  ContraSenha<>'CANCELADO') or ContraSenha is null)\n"
                    + " order by sequencia desc\n", persistencia);
            consult.setString(seqrota);
            consult.setString(parada);
            consult.setString(CodFech);
            consult.select();
            if (consult.Proximo()) {
                qf = new QueueFech();
                qf.setSequencia(consult.getString("Sequencia"));
                qf.setSrvOK(consult.getString("SrvOK"));
                qf.setContraSenha(consult.getString("ContraSenha"));
            }
            consult.Close();
            return qf;
        } catch (Exception e) {
            throw new Exception("QueueFechDao.verificaSolicitacaoSenha - " + e.getMessage() + "\r\n"
                    + "select top 1 SrvOK,Sequencia,ContraSenha \n"
                    + " from QueueFech \n"
                    + " where seqrota=" + seqrota + "\n"
                    + " and parada= " + parada + "\n"
                    + " and CodFech = " + CodFech + "\n"
                    + " and Comando_Ref<>'RECUSADO' \n"
                    + " and Comando_Ref<>'ACEITO'\n"
                    + " and ((ContraSenha<>'RECUSADO' and  ContraSenha<>'CANCELADO') or ContraSenha is null)\n"
                    + " order by sequencia desc\n");
        }
    }

    /**
     * Obtém primeiro queueFech com pânico, pelo id
     *
     * @param codFil
     * @return encontrado ou null
     * @throws Exception
     */
    public QueueFechDTO getQueueFechComPanico(String codFil) throws Exception {
        String sql = "SELECT TOP 1\n"
                + "QueueFech.Comando_Ref,\n"
                + "QueueFech.ChaveSrv,\n"
                + "QueueFech.Sequencia,\n"
                + "QueueFech.DataHoraComando,\n "
                + "QueueFech.HoraComando,\n"
                + "QueueFech.Latitude,\n "
                + "QueueFech.Longitude,\n"
                + "QueueFech.FechIdentif,\n"
                + "Funcion.Nome_Guer,\n"
                + "Escala.Rota,\n Escala.Veiculo\n"
                + "FROM QueueFech\n"
                + "LEFT JOIN Pessoa ON Pessoa.Codigo = QueueFech.CodPessoa\n"
                + "LEFT JOIN Funcion ON Funcion.Matr  = Pessoa.Matr\n"
                + "LEFT JOIN Escala ON Escala.MatrChe = Pessoa.Matr \n"
                + "    AND Escala.CodFil = QueueFech.CodFil \n"
                + "    AND Escala.Data = QueueFech.DataHoraComando\n"
                + "LEFT JOIN Veiculos ON Veiculos.Numero = Escala.Veiculo \n"
                + "    AND Veiculos.CodFil = Escala.CodFil \n"
                + "WHERE\n"
                + "    QueueFech.TipoOperacao = 1 \n"
                + "    AND (QueueFech.Comando_Ref = 'PANIC' OR QueueFech.Comando_Ref = 'MENSAGEM') \n"
                + "    AND QueueFech.Comando_Crt = 'ENVIADO'\n"
                + "    AND Funcion.Codfil = ? "
                + "    AND QueueFech.Latitude  <> ''\n"
                + "    AND QueueFech.Longitude <> ''";

        QueueFechDTO dto = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.select();

            if (consulta.Proximo()) {
                dto = new QueueFechDTO();

                dto.setSequencia(consulta.getBigDecimal("Sequencia"));
                dto.setComando_Ref(consulta.getString("Comando_Ref"));
                dto.setDataHoraComando(consulta.getLocalDate("DataHoraComando"));
                dto.setHoraComando(consulta.getString("HoraComando"));
                dto.setLatitude(consulta.getString("Latitude"));
                dto.setLongitude(consulta.getString("Longitude"));
                dto.setNome_Guer(consulta.getString("Nome_Guer"));
                dto.setRota(consulta.getString("Rota"));
                dto.setVeiculo(consulta.getString("Veiculo"));
                dto.setLocal(consulta.getString("FechIdentif"));
                dto.setMensagem(consulta.getString("ChaveSrv"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return dto;
    }

    /**
     *
     * @param queueFech
     * @return
     * @throws Exception
     */
    public boolean gravarQueueFech(QueueFech queueFech) throws Exception {
        String sql = "UPDATE QueueFech SET\n"
                + "    TipoOperacao = ?,\n"
                + "    Comando_Crt = ?,\n"
                + "    Operador = ?,\n"
                + "    Data = ?,\n"
                + "    Hora = ?\n"
                + "WHERE\n"
                + "    Sequencia = ? \n"
                + "    AND TipoOperacao = 1 \n"
                + "    AND (Comando_Ref  = 'PANIC' OR Comando_Ref = 'MENSAGEM')\n"
                + "    AND Comando_Crt  = 'ENVIADO' ;";

        Consulta consulta = null;

        try {
            consulta = new Consulta(sql, persistencia);
            // set
            consulta.setString(queueFech.getTipoOperacao());
            consulta.setString(queueFech.getComando_Crt());
            consulta.setString(queueFech.getOperador());
            consulta.setString(queueFech.getData());
            consulta.setString(queueFech.getHora());
            // where
            consulta.setBigDecimal(queueFech.getSequencia());

            consulta.update();
            consulta.Close();
            return true;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public List<QueueFechSolicitacaoSenhaDTO> selecionaSolicitacoesSenha(String codFil, String data, boolean marcado) throws Exception {
        String sql = "SELECT DISTINCT TOP 25 \n"
                + "    Escala.Rota, \n"
                + "    Rt_Perc.Parada, \n"
                + "    QueueFech.Sequencia, \n"
                + "    QueueFech.CodFil, \n"
                + "    QueueFech.TipoOperacao, \n"
                + "    Rt_perc.NRed LocalSolic, \n"
                + "    QueueFech.HoraComando HrSolic, \n"
                + "    Funcion.Nome_Guer ChEquipe, \n"
                + "    QueueFech.ChaveSrv, \n"
                + "    QueueFech.CodPessoa, \n"
                + "    QueueFech.FechIdentif, \n"
                + "    QueueFech.Comando_Ref, \n"
                + "    QueueFech.Senha, \n"
                + "    QueueFech.DataHoraComando Data, \n"
                + "    SrvOK, \n"
                + "    Pessoa.PW, \n"
                + "    Escala.MatrChe, \n"
                + "    P2.PW PWVig1, \n"
                + "    QueueFech.Comando_Crt, \n"
                + "    Escala.MatrVig1, \n"
                + "    Escala.SeqRota, \n"
                + "    Escala.Veiculo, \n"
                + "    QueueFech.Latitude, \n"
                + "    QueueFech.Longitude, \n"
                + "    Rt_Perc.CodCli1, \n"
                + "    Rt_Perc.Hora1, \n"
                + "    Veiculos.Placa, \n"
                + "    QueueFech.Comando_Aceito, \n"
                + "    Rotas.TpVeic, \n"
                + "    FechVei.Tipo TipoFechVei, \n"
                + "    FechCli.Tipo TipoFechCli, \n"
                + "    FechCli.Status StatusFechCli, \n"
                + "    FechVei.Status StatusFechVei, \n"
                + "    FechCli.Codigo CodigoFechCli, \n"
                + "    FechVei.Codigo CodigoFechVei, \n"
                + "    FechCli.SenhaUsuario1 SenhaUsr1FechCli, \n"
                + "    FechVei.SenhaUsuario1 SenhaUsr1FechVei, \n"
                + "    FechCli.PK_Fechadura MACFechCli, \n"
                + "    FechVei.PK_Fechadura MACFechVei, \n"
                + "    Clientes.Cheque, \n"
                + "    Clientes.Latitude 'Clientes.Latitude',\n"
                + "    Clientes.Longitude 'Clientes.Longitude',\n"
                + "    dbo.fun_CalcDistancia(QueueFech.Latitude, QueueFech.Longitude, Clientes.Latitude, Clientes.Longitude) AS distancia,\n"
                + "    Clientes.CercaElet 'Clientes.CercaElet'\n"
                + "FROM \n"
                + "    QueueFech \n"
                + "    LEFT JOIN Pessoa ON Pessoa.Codigo = QueueFech.CodPessoa \n"
                + "    LEFT JOIN Funcion ON Funcion.Matr = Pessoa.Matr \n"
                + "    LEFT JOIN Escala ON ( Escala.MatrChe = Pessoa.Matr OR Escala.MatrVig1  = Pessoa.Matr ) \n"
                + "    AND Escala.CodFil = QueueFech.CodFil \n"
                + "   AND Escala.Data = QueueFech.DataHoraComando \n"
                + "    LEFT JOIN Pessoa P2 ON P2.Matr = Escala.MatrVig1 \n"
                + "    AND P2.Matr <> 0 \n"
                + "    LEFT JOIN Veiculos ON Veiculos.Numero = Escala.Veiculo \n"
                + "    AND Veiculos.CodFil = Escala.CodFil \n"
                + "    LEFT JOIN Rotas ON Rotas.Sequencia   = Escala.SeqRota \n"
                + "    AND Rotas.CodFil = QueueFech.CodFil \n"
                + "    LEFT JOIN Rt_Perc ON Rt_Perc.Sequencia = Escala.SeqRota \n"
                + "    AND Rt_Perc.CodCli1 = QueueFech.FechIdentif \n"
                + "    AND Rt_perc.Flag_Excl <> '*' \n"
                + "    AND Rt_Perc.Hora1 = Queuefech.Hora \n"
                + "    LEFT JOIN Clientes ON Clientes.Codigo    = Rt_Perc.CodCli1 \n"
                + "    AND Clientes.CodFil = Rotas.CodFil \n"
                + "    LEFT JOIN Fechaduras FechCli ON FechCli.CodCli  = QueueFech.FechIdentif \n"
                + "    AND FechCli.CodFil = QueueFech.CodFil \n"
                + "    AND FechCli.Status = 'A' \n"
                + "    AND FechCli.Codigo = QueueFech.Codfech \n"
                + "    LEFT JOIN Fechaduras FechVei ON FechVei.Veiculo = Escala.Veiculo \n"
                + "    AND FechVei.CodFil  = QueueFech.CodFil \n"
                + "    AND FechVei.Status  = 'A' \n"
                + "    AND FechVei.Codigo  = QueueFech.Codfech \n"
                + "WHERE \n"
                + "    QueueFech.TipoOperacao = '1' \n"
                + "    AND QueueFech.Comando_Ref = 'SOLICITA' \n"
                + "    AND ( QueueFech.Comando_Aceito = 'N' OR QueueFech.Comando_Aceito IS NULL OR QueueFech.Comando_Aceito = '' ) \n"
                + "    AND Rt_Perc.Nred IS NOT NULL \n"
                + "    AND Rotas.Codfil = ? \n"
                + "    AND QueueFech.DataHoraComando = ? \n"
                + "    AND ((QueueFech.Comando_Crt <> 'ENVIADO') or (QueueFech.Comando_Crt is null)) \n";

        if (marcado) {
            sql += "   AND ( QueueFech.SrvOK <> 'E' OR QueueFech.SrvOK IS NULL ) \n"
                    + "   AND ( QueueFech.SrvOK <> 'R' OR QueueFech.SrvOK IS NULL ) \n";
        }
        sql += "ORDER BY Data DESC, Hora1 DESC, SrvOK, HrSolic;";

        List<QueueFechSolicitacaoSenhaDTO> lista = new ArrayList<>();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.setString(data);
            consulta.select();

            while (consulta.Proximo()) {
                QueueFechSolicitacaoSenhaDTO solicitacao = new QueueFechSolicitacaoSenhaDTO();

                solicitacao.setRota(consulta.getString("Rota"));
                solicitacao.setParada(consulta.getString("Parada"));
                solicitacao.setSequencia(consulta.getString("Sequencia"));
                solicitacao.setCodFil(consulta.getString("CodFil"));
                solicitacao.setTipoOperacao(consulta.getString("TipoOperacao"));
                solicitacao.setLocalSolic(consulta.getString("LocalSolic"));
                solicitacao.setHrSolic(consulta.getString("HrSolic"));
                solicitacao.setChEquipe(consulta.getString("ChEquipe"));
                solicitacao.setChaveSrv(consulta.getString("ChaveSrv"));
                solicitacao.setCodPessoa(consulta.getString("CodPessoa"));
                solicitacao.setFechIdentif(consulta.getString("FechIdentif"));
                solicitacao.setComando_Ref(consulta.getString("Comando_Ref"));
                solicitacao.setSenha(consulta.getString("Senha"));
                solicitacao.setData(consulta.getString("Data"));
                solicitacao.setSrvOK(consulta.getString("SrvOK"));
                solicitacao.setPW(consulta.getString("PW"));
                solicitacao.setMatrChe(consulta.getString("MatrChe"));
                solicitacao.setPWVig1(consulta.getString("PWVig1"));
                solicitacao.setComando_Crt(consulta.getString("Comando_Crt"));
                solicitacao.setMatrVig1(consulta.getString("MatrVig1"));
                solicitacao.setSeqRota(consulta.getString("SeqRota"));
                solicitacao.setVeiculo(consulta.getString("Veiculo"));
                solicitacao.setLatitude(consulta.getString("Latitude"));
                solicitacao.setLongitude(consulta.getString("Longitude"));
                solicitacao.setCodCli1(consulta.getString("CodCli1"));
                solicitacao.setHora1(consulta.getString("Hora1"));
                solicitacao.setPlaca(consulta.getString("Placa"));
                solicitacao.setComando_Aceito(consulta.getString("Comando_Aceito"));
                solicitacao.setTpVeic(consulta.getString("TpVeic"));
                solicitacao.setTipoFechVei(consulta.getString("TipoFechVei"));
                solicitacao.setTipoFechCli(consulta.getString("TipoFechCli"));
                solicitacao.setStatusFechCli(consulta.getString("StatusFechCli"));
                solicitacao.setStatusFechVei(consulta.getString("StatusFechVei"));
                solicitacao.setCodigoFechCli(consulta.getString("CodigoFechCli"));
                solicitacao.setCodigoFechVei(consulta.getString("CodigoFechVei"));
                solicitacao.setSenhaUsr1FechCli(consulta.getString("SenhaUsr1FechCli"));
                solicitacao.setSenhaUsr1FechVei(consulta.getString("SenhaUsr1FechVei"));
                solicitacao.setMACFechCli(consulta.getString("MACFechCli"));
                solicitacao.setMACFechVei(consulta.getString("MACFechVei"));
                solicitacao.setCheque(consulta.getString("Cheque"));
                solicitacao.setClienteLatitude(consulta.getString("Clientes.Latitude"));
                solicitacao.setClienteLongitude(consulta.getString("Clientes.Longitude"));
                solicitacao.setClienteCercaElet(consulta.getString("Clientes.CercaElet"));
                solicitacao.setDistanciaAteCliente(consulta.getString("distancia"));

                lista.add(solicitacao);
            }

        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public boolean rejeitarSolicitacaoSenha(String sequencia) throws Exception {
        String sql = "UPDATE queueFech\n"
                + "SET Comando_Ref = 'RECUSADO',\n"
                + "    ContraSenha = 'RECUSADO',\n"
                + "    SrvOK = 'E'\n"
                + "WHERE Sequencia = ? ;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.update();
            return true;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public final boolean aprovarSolicitacaoSenha(String senha, String fechadura, String sequencia) throws Exception {
        String sql = "UPDATE queueFech\n"
                + "SET Comando_Crt = 'ENVIADO',\n"
                + "    ContraSenha = ? ,\n"
                + "    Comando_Org = ? ,\n"
                + "    SrvOK = 'E'\n"
                + "WHERE Sequencia = ? ;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(senha);
            consulta.setBigDecimal(fechadura);
            consulta.setBigDecimal(sequencia);
            consulta.update();
            return true;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public boolean aprovarSolicitacaoSenhaComCancelado(String sequencia) throws Exception {
        String sql = "UPDATE queueFech\n"
                + "SET Comando_Ref = 'CANCELADO',\n"
                + "    ContraSenha = 'CANCELADO',\n"
                + "    SrvOK = 'E'\n"
                + "WHERE Sequencia = ? ;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.update();
            return true;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public List<QueueFechPessoaDTO> listarCentralAlertaPorDatas(String codFil, LocalDate dataInicio, LocalDate dataFim) throws Exception {
        String sql = "SELECT Sequencia,\n"
                + "       Data,\n"
                + "       Hora,\n"
                + "       Pessoa.Nome Solicitante,\n"
                + "       Comando_Ref TipoMensagem,\n"
                + "       Comando_Crt StatusMensagem,\n"
                + "       PathSrv\n"
                + "FROM queuefech\n"
                + "         LEFT JOIN Pessoa ON Pessoa.Codigo = QueueFech.CodPessoa\n"
                + "WHERE QueueFech.CodFil = ? \n"
                + "  AND QueueFech.Data BETWEEN ? AND ? \n"
                + "ORDER BY Data DESC, Hora DESC";

        List<QueueFechPessoaDTO> lista = new ArrayList<>();
        Consulta consulta = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.setString(dataInicio.format(formatter));
            consulta.setString(dataFim.format(formatter));
            consulta.select();

            while (consulta.Proximo()) {
                QueueFech queueFech = new QueueFech();
                Pessoa pessoa = new Pessoa();
                QueueFechPessoaDTO row = new QueueFechPessoaDTO(queueFech, pessoa);

                queueFech.setSequencia(consulta.getString("Sequencia"));
                queueFech.setData(consulta.getString("Data"));
                queueFech.setHora(consulta.getString("Hora"));
                pessoa.setNome(consulta.getString("Solicitante"));
                queueFech.setComando_Ref(consulta.getString("TipoMensagem"));
                queueFech.setComando_Crt(consulta.getString("StatusMensagem"));
                queueFech.setPathSrv(consulta.getString("PathSrv"));

                lista.add(row);
            }

        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public void updatePathSrv(String sequencia, String pathSrv) throws Exception {
        String sql = "UPDATE queuefech\n"
                + "SET PathSrv = ? \n"
                + "WHERE Sequencia = ? ;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(pathSrv);
            consulta.setBigDecimal(sequencia);
            consulta.update();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    
}

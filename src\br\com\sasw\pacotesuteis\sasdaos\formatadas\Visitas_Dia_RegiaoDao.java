/*
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.SatWebService.Visitas_Dia_Regiao;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Visitas_Dia_RegiaoDao {

    public List<Visitas_Dia_Regiao> visitasPorRegiaoPorDia(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        List<Visitas_Dia_Regiao> retorno = new ArrayList<>();
        try {
            String sql = "SELECT c_region.`value` region,date(FROM_UNIXTIME(check_in_date/1000)) dia,"
                    + " count(report_report.id) visitas "
                    + " from report_report "
                    + " inner join place on place.id = report_report.id_place "
                    + " inner join c_region on c_region.id = place.id_region "
                    + " where date(FROM_UNIXTIME(check_in_date/1000)) BETWEEN ? and ? "
                    + "     and report_report.id_user not in (1,2,34,41,42,47,48,49,50,51,52,53,54,69,70,71,71,72,73,74,"
                    + "                                       75,77,78,79,81,260,262,257,256,258,259,316,4854,4622,4623,"
                    + "                                       4624,4625,4626,4548,4627,4628,4619,4629,4621,4616,4612,4611,"
                    + "                                       4613,4615,4550,4585,4718,411,4548,4549,4550,4835,4842,4844)  "
                    + " GROUP BY id_region,dia";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            Visitas_Dia_Regiao resposta;
            while (consulta.Proximo()) {
                resposta = new Visitas_Dia_Regiao();
                resposta.setRegion(consulta.getString("region"));
                resposta.setDia(consulta.getString("dia"));
                resposta.setVisitas(consulta.getBigDecimal("visitas"));
                retorno.add(resposta);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao gerar relatório - " + e.getMessage());
        }
    }
}

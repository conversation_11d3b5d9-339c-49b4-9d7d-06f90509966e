<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:cc="http://xmlns.jcp.org/jsf/composite/components">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/valores/rotasvalores.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/rotas.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet"  media="print"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                [id*="guias"] tbody tr td,
                [id*="guias"] thead tr td,
                [id*="guias"] thead tr th{
                    min-width: 150px !important;
                    text-align: center !important;
                }

                [id*="guias"] tbody tr td:nth-child(4),
                [id*="guias"] thead tr td:nth-child(4),
                [id*="guias"] thead tr th:nth-child(4){
                    min-width: 60px !important;
                    text-align: center !important;
                }

                [id*="guias"] tbody tr td:nth-child(5),
                [id*="guias"] thead tr td:nth-child(5),
                [id*="guias"] thead tr th:nth-child(5){
                    min-width: 100px !important;
                    text-align: center !important;
                }

                [id*="guias"] tbody tr td:nth-last-child(2),
                [id*="guias"] thead tr td:nth-last-child(2),
                [id*="guias"] thead tr th:nth-last-child(2),
                [id*="guias"] tbody tr td:last-child,
                [id*="guias"] thead tr td:last-child,
                [id*="guias"] thead tr th:last-child{
                    min-width: 300px !important;
                    text-align: center !important;
                }

                .jconfirm-closeIcon{
                    color: #333 !important;
                }

                .ui-inputtext{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: 0px !important;
                        position: relative !important;
                    }

                    .DataGrid[id$="trajetos"] [role="columnheader"] > span {
                        top: -1px !important;
                        position: relative !important;
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td{
                        text-align: center !important;
                    }
                }

                [id*="cadastroRota"] div[class*="col-md"],
                [id*="cadastroTrajeto"] div[class*="col-md"],
                [id*="cadastroEscala"] div[class*="col-md"]{
                    padding: 5px 5px 0px 5px !important;
                }

                [id*="cadastroRota"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="cadastroTrajeto"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formRotasModelos"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formRelatoriosDisponiveis"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="cadastroEscala"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="cadastroTrajeto:dlgCadastroTrajeto"] > .ui-dialog-content {
                    max-height: 560px !important;
                }
            </style>
            <ui:fragment rendered="#{solicitacaoSenhaMB.temPermissao}">
                <script
                    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBLXQgn09RZmoQ6NfHxGQaR7Y0M5eFTMts">
                </script>
            </ui:fragment>

        </h:head>

        <h:body id="h" style="max-height: 100% !important; height: 100% !important;">
            <f:metadata>
                <f:viewAction action="#{rotasescala.Persistencia(login.pp, login.satellite)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <ui:include rendered="false" src="/botao_panico.xhtml"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_satmob_contratosG.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Rotas}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{rotasescala.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{rotasescala.filialDesc}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{rotasescala.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{rotasescala.filiais.bairro}, #{rotasescala.filiais.cidade}/#{rotasescala.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:commandLink action="#{rotasescala.voltarDia()}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{rotasescala.dataTela}"
                                                locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                        <p:ajax event="dateSelect" listener="#{rotasescala.SelecionarData}" update="main cabecalho msgs" />
                                    </p:calendar>

                                    <p:commandLink action="#{rotasescala.avancarDia()}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="e" update="msgs cadastroRota cadastroRota:cadastrar" actionListener="#{rotasescala.buttonAction}"/>

                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:dataTable id="tabela" 
                                     value="#{rotasescala.allRotas}" 
                                     selection="#{rotasescala.rotaSelecionada}" 
                                     emptyMessage="#{localemsgs.SemRegistros}"
                                     rowKey="#{rotasescala.sequencia}"
                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.RotasValores}"
                                     var="listaRotas" 
                                     lazy="true" 
                                     paginator="true" 
                                     reflow="true" 
                                     rows="15" 
                                     rowsPerPageTemplate="5, 10, 15, 20, 25"
                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                     scrollWidth="100%" 
                                     scrollable="true" 
                                     selectionMode="single" 
                                     styleClass="tabela DataGrid">
                            <p:ajax event="rowDblselect" listener="#{rotasescala.onRowSelect}"
                                    update="cadastroRota msgs"/>
                            <p:column>
                                <p:rowToggler />
                            </p:column>

                            <p:column headerText="#{localemsgs.CodFil}"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.codFil}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>

                            <p:column headerText="#{localemsgs.Rota}"
                                      class="celula-right"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.hrLargada.compareTo('18:00') ge 0
                                                       ? '☾   '.concat(listaRotas.rota)
                                                       : listaRotas.rota}"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.ChefeEquipe}"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.nome}"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Data}" style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.data}" converter="conversorData"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.TpVeic}" style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.tpVeic}"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.ValorTotal}" class="celula-right"
                                      rendered="#{!login.transpCacamba}"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.valor}" converter="conversormoeda"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Obs}" style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.observacao}"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Paradas}" style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.qtdTrajetos}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>

                            <p:column headerText="#{!login.transpCacamba ? localemsgs.Volumes : localemsgs.Containers}"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.qtdVolumes}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>

                            <p:column headerText="#{localemsgs.Hr_Largada}" style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.hrLargada}" converter="conversorHora"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Hr_Chegada}" style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.hrChegada}" converter="conversorHora"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Hr_IntIni}" style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.hrIntIni}" converter="conversorHora"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Hr_IntFim}" style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.hrIntFim}" converter="conversorHora"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Hr_Total}" class="celula-right"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.hsTotal}">
                                    <f:convertNumber pattern="0.00"/>
                                </h:outputText>
                            </p:column>

                            <p:column headerText="#{localemsgs.Sequencia}" class="celula-right"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.sequencia}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>

                            <p:column headerText="#{localemsgs.Operador}"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.operador}"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Dt_Alter}"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.dt_Alter}" converter="conversorData"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Hr_Alter}"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.hr_Alter}"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Flag_Excl}"
                                      style="#{listaRotas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                <h:outputText value="#{listaRotas.flag_Excl}"/>
                            </p:column>

                            <p:ajax
                                event="rowToggle"
                                id="rowToggle"
                                listener="#{rotasescala.onRowToggle}"
                                update="msgs"/>

                            <p:rowExpansion id="tabelaTrajetosRota">
                                <p:dataTable
                                    id="rotasDeTrajeto"
                                    emptyMessage="#{localemsgs.SemRegistros}"
                                    rowKey="#{listaTrajetosRota.parada}"
                                    selection="#{rotasescala.trajetoSelecionado}"
                                    sortBy="#{listaTrajetosRota.hora1}"
                                    value="#{listaRotas.trajetos}"
                                    scrollHeight="150"
                                    scrollable="true"
                                    selectionMode="single"
                                    var="listaTrajetosRota"
                                    styleClass="tabela DataGrid"
                                    style="font-size: 12px; background: transparent; padding:0px !important; margin:0px !important;max-width:100% !important; width:100% !important;">
                                    <p:ajax event="rowDblselect" update="msgs cadastroTrajeto" partialSubmit="true" process="@this"
                                            listener="#{rotasescala.selecionarTrajeto}"/>
                                    <p:column>
                                        <p:commandLink title="#{localemsgs.Editar}"
                                                       update="msgs cadastroTrajeto"
                                                       action="#{rotasescala.abrirTrajeto}">
                                            <f:setPropertyActionListener value="#{listaTrajetosRota}" target="#{rotasescala.trajetoSelecionado}" />
                                            <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="20"/>
                                        </p:commandLink>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Rota}" style="font-size: 12px;
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.rota}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Parada}" style="font-size: 12px;
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.parada}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Hora1}"
                                              style="#{flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.hora1}" converter="conversorHora"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.HrCheg}"
                                              style="#{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.hrCheg}" converter="conversorHora"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.HrSaida}"
                                              style="#{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.hrSaida}" converter="conversorHora"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.ER}"
                                              style="#{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.ER}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.NRed}" style="
                                              #{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.NRed}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.DPar}"
                                              style="#{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.DPar}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Destino}" style="
                                              #{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.NRedDst}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Hora1D}"
                                              style="#{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.hora1D}" converter="conversorHora"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Valor}" style="
                                              #{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.valor}" converter="conversormoeda"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.tipoServ}"
                                              style="#{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.tipoSrv}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Obs}" style="
                                              #{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.observ}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Pedido}" style="
                                              #{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.pedido}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Operador}" style="
                                              #{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.operador}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Dt_Alter}" style="
                                              #{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.dt_Alter}" converter="conversorData"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>

                                    <p:column headerText="#{localemsgs.Hr_Alter}"
                                              style="#{listaTrajetosRota.flag_Excl eq '*' ? 'color:orange' : 'color:black'};
                                              #{listaTrajetosRota.latitude eq '' ? 'background-color: yellow' : 'background-color: none'}">
                                        <h:outputText value="#{listaTrajetosRota.hr_Alter}"
                                                      style="#{listaTrajetosRota.hrCheg ne '' ? 'color:green' :
                                                               (listaTrajetosRota.hora1.compareTo(rotasescala.horaAtual) ge 0 ? 'color:black' : 'color:red')};"/>
                                    </p:column>
                                </p:dataTable>
                            </p:rowExpansion>
                        </p:dataTable>
                    </p:panel>

                    <p:panel id="botoes" style="position: fixed; z-index: 1; right: 1px; bottom: 190px !important; background: transparent; height:200px !important;">
                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Adicionar}" update="msgs" actionListener="#{rotasescala.prepararCadastro}">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Editar}"
                                           update="msgs cadastroRota cadastroRota:cadastrar"
                                           actionListener="#{rotasescala.buttonAction}">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Excluir}"
                                           update="msgs tabela"
                                           actionListener="#{rotasescala.buttonActionExclusao}">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                                <p:confirm header="#{localemsgs.Confirmacao}"
                                           message="#{localemsgs.ExcluirRota}"
                                           icon="ui-icon-alert" />
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.RotasXModelos}"
                                           action="#{rotasescala.criarRotaPorModelo()}"
                                           process="@this"
                                           update="msgs corporativo">
                                <p:graphicImage url="../assets/img/icone_satmob_contracheque_funcaoadm_P.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Relatorios}"
                                           actionListener="#{rotasescala.relatoriosDisponiveis()}"
                                           update="msgs">
                                <p:graphicImage url="../assets/img/icone_relatorios.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Escala}"
                                           update="msgs cadastroEscala cadastroEscala:dlgEscala"
                                           actionListener="#{rotasescala.abrirEscala}">
                                <p:graphicImage url="../assets/img/icon_calendario_novo.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.SaidasAutomaticas}"
                                           actionListener="#{rotasescala.gerarSaidasAutomaticas()}"
                                           update="msgs">
                                <p:graphicImage url="../assets/img/icone_satmob_contracheque_funcaoadm_P.png" height="40"/>
                            </p:commandLink>
                        </div>                        

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}"
                                           action="#{rotasescala.LimparFiltros()}"
                                           update="msgs corporativo">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div ref="IconCacamba" numero="2" style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Containers}"
                                           update="msgs"
                                           rendered="#{login.transpCacamba}"
                                           actionListener="#{rotasescala.listarContainers}">
                                <p:graphicImage url="../assets/img/icone_cacamba.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px; display: none">
                            <p:commandLink title="#{localemsgs.Excluir}"
                                           action="#{rotasescala.Excluir}"
                                           update="tabela msgs cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                                <p:confirm header="#{localemsgs.Confirmacao}"
                                           message="#{localemsgs.ExcluirEscala}"
                                           icon="ui-icon-alert" />
                            </p:commandLink>

                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}"
                                                 type="button"
                                                 styleClass="ui-confirmdialog-yes"
                                                 icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}"
                                                 type="button"
                                                 styleClass="ui-confirmdialog-no"
                                                 icon="ui-icon-close"/>
                            </p:confirmDialog>
                        </div>
                    </p:panel>
                </h:form>

                <h:form id="cabecalho2">
                    <p:dialog
                        id="dlgDetalhesValores"
                        widgetVar="dlgDetalhesValores"
                        modal="true"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        style=" background-image: url('assets/img/menu_fundo.png');
                        max-height: 85vh;
                        border-top:4px solid #3C8DBC !important;"
                        >
                        <p:commandButton widgetVar="botaoFecharValores" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFecharValores">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_faturamento.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.ValorTotalRotas}" style="color:black" />
                        </f:facet>

                        <p:panel
                            id="cadastrar"
                            styleClass="cadastrar"
                            style="width: 400px;"
                            >
                            <div
                                style="background-color: transparent; display:flex;
                                flex-direction: column;
                                flex-grow: 1;
                                overflow: auto;"
                                >
                                <p:dataTable
                                    id="tabela2"
                                    value="#{rotasescala.rotasDet}"
                                    emptyMessage="#{localemsgs.SemRegistros}"
                                    var="lista"
                                    scrollable="true"
                                    styleClass="tabela DataGrid"
                                    style="font-size: 12px; height: 350px; flex-grow: 1;">
                                    <p:column headerText="#{localemsgs.Rota}"
                                              style="#{lista.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                        <h:outputText
                                            value="#{lista.hrLargada.compareTo('18:00') ge 0
                                                     ? '☾   '.concat(lista.rota)
                                                     : lista.rota}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.TpVeic}"
                                              style="#{lista.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                        <h:outputText value="#{lista.tpVeic}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.ValorTotal}"
                                              style="#{lista.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                        <h:outputText value="#{lista.valor}" converter="conversormoeda"/>
                                    </p:column>
                                </p:dataTable>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formRelatoriosDisponiveis">
                    <p:hotkey bind="esc" oncomplete="PF('dlgRelatorios').hide()"/>
                    <p:dialog widgetVar="dlgRelatorios" positionType="absolute" responsive="true" draggable="false" modal="true" closable="true"
                              resizable="false" dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="false" class="dialogoPequeno"
                              style="padding:0px !important">
                        <f:facet name="header">
                            <img src="../assets/img/icone_relatorios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Relatorios}"/>
                        </f:facet>

                        <p:outputPanel id="relatorios" style="padding: 0px !important">
                            <div class="col-md-2 col-sm-2 col-xs-12">
                                <p:outputLabel for="tipo" value="#{localemsgs.Tipo}: " />
                            </div>
                            <div class="verticalSpace col-md-10 col-sm-10 col-xs-12" style="width: 250px; padding-right: 0px !important">
                                <p:selectOneMenu id="tipo" value="#{rotasescala.relatorioSelecionado}" required="true"
                                                 style="min-width: 100px !important; font-size:12pt; font-weight:600 !important;
                                                 font-family: 'Trebuchet MS'">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{rotasescala.tiposRelatorios}"/>
                                    <p:ajax update="relatorios" listener="#{rotasescala.selecionarRelatorio}"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-2 col-sm-2 col-xs-12">
                                <p:outputLabel for="data" value="#{localemsgs.Data}: " />
                            </div>
                            <div class="verticalSpace col-md-5 col-sm-5 col-xs-12" style="width: 125px; padding-right: 0px !important">
                                <p:calendar id="data" value="#{rotasescala.dataModelo}" pattern="#{mascaras.padraoData}" 
                                            locale="#{localeController.getCurrentLocale()}" 
                                            size="10" style="width: 125px;" converter="conversorData">
                                    <p:ajax event="dateSelect" listener="#{rotasescala.selecionarDataModelo}" update="@next" />
                                </p:calendar>
                            </div>
                            <div class="verticalSpace col-md-5 col-sm-5 col-xs-12" style="width: 125px; padding-right: 0px !important">
                                <p:inputText disabled="true" value="#{rotasescala.dataModelo}" converter="conversorDiaSemNumero" style="width: 100%;"/>
                            </div>


                            <div class="col-md-2 col-sm-2 col-xs-12">
                                <p:outputLabel for="data" value="#{localemsgs.DataFinal}: "
                                               rendered="#{rotasescala.relatorioDuasDatas}"/>
                            </div>
                            <div class="verticalSpace col-md-5 col-sm-5 col-xs-12" style="width: 125px; padding-right: 0px !important">
                                <p:calendar id="dataFinal" value="#{rotasescala.dataModeloFinal}" pattern="#{mascaras.padraoData}" 
                                            locale="#{localeController.getCurrentLocale()}"
                                            rendered="#{rotasescala.relatorioDuasDatas}"
                                            size="10" style="width: 125px;" converter="conversorData">
                                    <p:ajax event="dateSelect" listener="#{rotasescala.selecionarDataModeloFinal}" update="@next" />
                                </p:calendar>
                            </div>
                            <div class="verticalSpace col-md-5 col-sm-5 col-xs-12" style="width: 125px; padding-right: 0px !important">
                                <p:inputText disabled="true" value="#{rotasescala.dataModeloFinal}" converter="conversorDiaSemNumero" style="width: 100%;"
                                             rendered="#{rotasescala.relatorioDuasDatas}"/>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right">
                                <p:commandButton title="#{localemsgs.DownloadPDF}" update="msgs" value="#{localemsgs.DownloadPDF}"
                                                 ajax="false" actionListener="#{rotasescala.gerarRelatorio}" class="btn btn-danger" style="color: #FFF; width:100%;">
                                    <p:fileDownload value="#{rotasescala.arquivoDownload}" />
                                </p:commandButton>
                            </div>
                        </p:outputPanel>
                    </p:dialog>
                </h:form>

                <h:form id="cadastroEscala">
                    <p:hotkey bind="esc" oncomplete="PF('dlgEscala').hide()"/>
                    <p:dialog id="dlgEscala" widgetVar="dlgEscala" positionType="absolute" responsive="true" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="false" class="dialogoGrande" style="padding:0px !important; overflow: hidden !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgEscala').closeIcon.unbind('click');
                                //register your own
                                PF('dlgEscala').closeIcon.click(function (e) {
                                    $("#cadastroEscala\\:botaoFecharEscala").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            }; );
                        </script>
                        <div class="col-md-12" style="padding:0px !important; overflow-y: auto; overflow-x: hidden !important; max-height: 70vh;">
                            <p:commandButton widgetVar="botaoFecharEscala" style="display: none"
                                             oncomplete="PF('dlgEscala').hide()" id="botaoFecharEscala">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>

                            <f:facet name="header">
                                <img src="../assets/img/icones_satmob_carroforte.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.CadastrarEscala}" style="color:black" />
                            </f:facet>

                            <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                </p:confirmDialog>

                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top:0px !important">
                                    <p:outputLabel for="filial" value="#{localemsgs.Filial}"  />
                                    <p:selectOneMenu id="filial" value="#{rotasescala.filial}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                     filter="true" filterMatchMode="contains" style="width: 100%" label="">
                                        <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                                       itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" listener="#{rotasescala.selecionarFilialEscala}"
                                                />
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="data" value="#{localemsgs.Data}" />
                                    <p:inputMask id="data" value="#{rotasescala.escala.data}" mask="99/99/9999"
                                                 required="true" label="#{localemsgs.Data}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                 style="width: 100%"
                                                 maxlength="8" placeholder="00/00/0000"
                                                 converter="conversorData" disabled="true"/> 
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="rota" value="#{localemsgs.Rota}"  />
                                    <p:selectOneMenu id="rota" value="#{rotasescala.rotaSelecionada}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Rota}"
                                                     style="width: 100%" label="">
                                        <f:selectItems value="#{rotasescala.rotasSelecao}" var="rotaSelecao" itemValue="#{rotaSelecao}"
                                                       itemLabel="#{rotaSelecao.rota}"
                                                       />
                                        <p:ajax event="itemSelect" update="cadastroEscala:cadastrar msgs" listener="#{rotasescala.selecionarRotaEscala}"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="rotaSeq" value="#{localemsgs.Sequencia}"  />
                                    <p:inputText id="rotaSeq" value="#{rotasescala.rotaSelecionada.sequencia}"
                                                 label="#{localemsgs.Rota}" style="width: 100%"
                                                 disabled="true" converter="conversor0">
                                        <p:watermark for="rotaSeq" value="#{localemsgs.Rota}"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="totalhr" value="#{localemsgs.Hr_Total}" escape="false"/>
                                    <p:inputText id="totalhr" value="#{rotasescala.escala.hsTot}"
                                                 maxlength="4" style="width: 100%"
                                                 disabled="true">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </p:inputText>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrLargada" value="#{localemsgs.Horario}"/>
                                    <p:inputMask id="hrLargada" value="#{rotasescala.escala.hora1}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true" placeholder="00:00"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Largada}"
                                                 maxlength="4" style="width: 100%" >
                                        <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrChegada" value="#{localemsgs.a}" style="width: 100%"/>
                                    <p:inputMask id="hrChegada" value="#{rotasescala.escala.hora4}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true"  placeholder="00:00"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Chegada}"
                                                 maxlength="4" style="width: 100%">
                                        <p:ajax process="@this" update="msgs totalhr" partialSubmit="true"
                                                listener="#{rotasescala.calculaHorasTrabalhadasEscala}" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrIntIni" value="#{localemsgs.Intervalo}"/>
                                    <p:inputMask id="hrIntIni" value="#{rotasescala.escala.hora2}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true" placeholder="00:00" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Intini}"
                                                 maxlength="4" >
                                        <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrIntFim" value="#{localemsgs.a}" style="width: 100%"/>
                                    <p:inputMask id="hrIntFim" value="#{rotasescala.escala.hora3}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true"  placeholder="00:00"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_IntFim}"
                                                 maxlength="4" style="width: 100%">
                                        <p:ajax process="@this" update="msgs totalhr" partialSubmit="true"
                                                listener="#{rotasescala.calculaHorasTrabalhadasEscala}" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="motorista" value="#{localemsgs.Motorista}"/>
                                    <p:inputText id="matrMotorista" value="#{rotasescala.motorista.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel value="#{localemsgs.Motorista}" style="opacity:0;"/>
                                    <p:autoComplete id="motorista" value="#{rotasescala.motorista}" styleClass="cliente2"
                                                    style="width: 100%" completeMethod="#{rotasescala.buscarMotorista}"
                                                    var="motoristaSelecao" itemLabel="#{motoristaSelecao.nome}" itemValue="#{motoristaSelecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{rotasescala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{rotasescala.selecionarMotorista}"
                                                update="matrMotorista hrMotorista motorista msgs"/>
                                        <p:watermark for="motorista" value="#{localemsgs.Motorista}" />
                                    </p:autoComplete>
                                    <div style="position:absolute; right:12px; top:30px;">
                                        <p:commandLink title="#{localemsgs.Adicionar}"
                                                       actionListener="#{rotasescala.buscarFuncionario}"
                                                       update="msgs" process="@this">
                                            <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="24"/>
                                        </p:commandLink>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrMotorista" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrMotorista" value="#{rotasescala.escala.hrMot}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="chEquipe" value="#{localemsgs.ChEquipe}"/>
                                    <p:inputText id="matrChEquipe" value="#{rotasescala.chEquipe.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="chEquipe" value="#{localemsgs.ChEquipe}" style="opacity:0;"/>
                                    <p:autoComplete id="chEquipe" value="#{rotasescala.chEquipe}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{rotasescala.buscarChEquip}"
                                                    var="chEquipeSelecao" itemLabel="#{chEquipeSelecao.nome}" itemValue="#{chEquipeSelecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{rotasescala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{rotasescala.selecionarChEquipe}"
                                                update="matrChEquipe hrChEquipe panelInfoChEquipe chEquipe msgs"/>
                                        <p:watermark for="chEquipe" value="#{localemsgs.ChEquipe}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrChEquipe" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrChEquipe" value="#{rotasescala.escala.hrChe}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>

                                <p:panel id="panelInfoChEquipe" class="col-md-12 col-sm col-xs-12" style="background: lightyellow; border: thin solid orangered; padding:4px 10px 10px 10px !important; margin-top: 6px; margin-bottom: 6px; margin-left: 5px; width:calc(100%  - 10px) !important; display:#{rotasescala.infoChEquipe?'':'none'}">
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="codigoChEquipe" value="#{localemsgs.CodPessoa}" rendered="#{rotasescala.infoChEquipe}"/>
                                        <p:inputText id="codigoChEquipe" value="#{rotasescala.chEquipe.codigo}" converter="conversor0"
                                                     disabled="true" style="width: 100%; background-color:#FFF !important;" rendered="#{rotasescala.infoChEquipe}" />
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-12">
                                        <p:outputLabel for="senhaChEquipe" value="#{localemsgs.SenhaMobile}" rendered="#{rotasescala.infoChEquipe}"/>
                                        <p:inputText id="senhaChEquipe" value="#{rotasescala.chEquipe.PWWeb}" rendered="#{rotasescala.infoChEquipe}"
                                                     disabled="true" style="width: 100%; background-color:#FFF !important;"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="permissaoChEquipe" value="#{localemsgs.PermissaoRotas}" rendered="#{rotasescala.infoChEquipe}"/>
                                        <p:inputText id="permissaoChEquipe" value="#{rotasescala.permissaoRota ? localemsgs.OK :  localemsgs.Nao}" rendered="#{rotasescala.infoChEquipe}"
                                                     disabled="true" style="width: 100%; background-color:#FFF !important; #{rotasescala.permissaoRota ? 'color:green' : 'color:red'}"/>
                                    </div>
                                </p:panel>

                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante1" value="#{localemsgs.Vigilante1}"/>
                                    <p:inputText id="matrVigilante1" value="#{rotasescala.vigilante1.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="vigilante1" value="#{localemsgs.Vigilante1}" style="opacity:0;"/>
                                    <p:autoComplete id="vigilante1" value="#{rotasescala.vigilante1}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{rotasescala.buscarVigilante}"
                                                    var="vigilante1Selecao" itemLabel="#{vigilante1Selecao.nome}" itemValue="#{vigilante1Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{rotasescala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{rotasescala.selecionarVigilante1}"
                                                update="matrVigilante1 hrVigilante1 msgs"/>
                                        <p:watermark for="vigilante1" value="#{localemsgs.Vigilante1}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante1" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrVigilante1" value="#{rotasescala.escala.hrVig1}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante2" value="#{localemsgs.Vigilante2}"/>
                                    <p:inputText id="matrVigilante2" value="#{rotasescala.vigilante2.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante2" value="#{localemsgs.Vigilante2}" style="opacity:0;"/>
                                    <p:autoComplete id="vigilante2" value="#{rotasescala.vigilante2}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{rotasescala.buscarVigilante}"
                                                    var="vigilante2Selecao" itemLabel="#{vigilante2Selecao.nome}" itemValue="#{vigilante2Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{rotasescala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{rotasescala.selecionarVigilante2}"
                                                update="matrVigilante2 hrVigilante2 msgs"/>
                                        <p:watermark for="vigilante2" value="#{localemsgs.Vigilante2}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante2" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrVigilante2" value="#{rotasescala.escala.hrVig2}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante3" value="#{localemsgs.Vigilante3}"/>
                                    <p:inputText id="matrVigilante3" value="#{rotasescala.vigilante3.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="vigilante3" value="#{localemsgs.Vigilante3}" style="opacity:0;"/>
                                    <p:autoComplete id="vigilante3" value="#{rotasescala.vigilante3}" styleClass="cliente2"
                                                    style="width: 100%"
                                                    completeMethod="#{rotasescala.buscarVigilante}"
                                                    var="vigilante3Selecao" itemLabel="#{vigilante3Selecao.nome}" itemValue="#{vigilante3Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{rotasescala.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{rotasescala.selecionarVigilante3}"
                                                update="matrVigilante3 hrVigilante3 msgs"/>
                                        <p:watermark for="vigilante3" value="#{localemsgs.Vigilante3}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante3" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrVigilante3" value="#{rotasescala.escala.hrVig3}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="veiculo" value="#{localemsgs.Veiculo}"/>
                                    <p:inputText id="numeroVeiculo" value="#{rotasescala.veiculo.numero}" disabled="true"
                                                 style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="veiculo" value="#{localemsgs.Veiculo}" style="opacity:0;"/>
                                    <p:selectOneMenu id="veiculo" value="#{rotasescala.veiculo}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains" style="width: 100%" label="">
                                        <f:selectItems value="#{rotasescala.veiculos}" var="veiculoSelecao" itemValue="#{veiculoSelecao}"
                                                       itemLabel="#{veiculoSelecao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" update="numeroVeiculo msgVeiculo msgs" listener="#{rotasescala.selecionarVeiculo}"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="padding-top: 22px !important">
                                    <h:outputText id="msgVeiculo" value="#{rotasescala.msgVeiculo}"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="text-align: left; padding-top: 8px !important;">
                                    <p:selectBooleanCheckbox value="#{rotasescala.infoChEquipe}" id="senhaMobile"
                                                             style="width: 30px; text-align: left">
                                        <p:ajax update="panelInfoChEquipe" event="change" oncomplete="PF('dlgEscala').initPosition()"/>
                                    </p:selectBooleanCheckbox>
                                    <p:outputLabel for="senhaMobile" value="#{localemsgs.senhaMobile}" style="color: orangered" />
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="text-align: right">
                                    <p:commandLink
                                        rendered="#{rotasescala.flagEscala eq 1}"
                                        id="cadastroEscalaAction"
                                        update="main:tabela msgs"
                                        actionListener="#{rotasescala.cadastrarEscala(false)}"
                                        title="#{localemsgs.Cadastrar}" styleClass="btn btn-primary" style="color:#FFF !important">
                                        <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                    </p:commandLink>

                                    <p:commandLink
                                        rendered="#{rotasescala.flagEscala eq 2}"
                                        id="editEscalaAction"
                                        actionListener="#{rotasescala.editarEscala(false)}"
                                        update=":msgs main:tabela cadastrar"
                                        styleClass="btn btn-primary" style="color:#FFF !important">
                                        <f:viewAction action="#{rotasescala.ListarData}" />
                                        <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                    </p:commandLink>

                                </div>
                            </p:panel>
                        </div>
                    </p:dialog>

                    <p:dialog widgetVar="dlgFuncionarioFolga" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.FuncionariosFolga}" style="color:black" />
                        </f:facet>
                        <p:panel id="panelFuncionarioFolga" style="background-color: transparent" styleClass="cadastrar2">
                            <h:outputText value="#{rotasescala.folgas.size() > 1 ? localemsgs.AceitarFuncionariosFolga :  localemsgs.AceitarFuncionarioFolga}"/>
                            <p:dataTable id="tabelaFuncionarioFolga" value="#{rotasescala.folgas}"
                                         style="font-size: 12px" var="folgas"
                                         styleClass="tabela" scrollWidth="100%"
                                         resizableColumns="true" scrollable="true" scrollHeight="85" >
                                <p:column headerText="#{localemsgs.Matr}" style="font-size: 12px;">
                                    <h:outputText value="#{folgas.matr}" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Funcion}">
                                    <h:outputText value="#{folgas.nome_Guer}"/>
                                </p:column>
                            </p:dataTable>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:commandLink title="#{localemsgs.Avancar}" rendered="#{rotasescala.flagEscala eq 1}"
                                               update="main:tabela :msgs" actionListener="#{rotasescala.cadastrarEscala(true)}">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>

                                <p:commandLink title="#{localemsgs.Avancar}" rendered="#{rotasescala.flagEscala eq 2}"
                                               update="main:tabela :msgs" actionListener="#{rotasescala.editarEscala(true)}">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>

                                <p:commandLink title="#{localemsgs.Cancelar}" oncomplete="PF('dlgFuncionarioFolga').hide()">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>
                            </p:panelGrid>

                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- MODAL DE ROTAS-->
                <h:form id="cadastroRota" style="padding-bottom: 0px !important">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>

                    <p:dialog id="dlgCadastrar" widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop"
                              hideEffect="drop" closeOnEscape="false" class="dialogoGrande" focus="#{rotasescala.flag eq 1? 'subFil': 'obs'}" style="padding-bottom: 0px !important">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');
                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#cadastroRota\\:botaoFecharRota").click()
                                    //should be always called
                                    e.preventDefault()
                                })
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFecharRota" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFecharRota">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icones_satmob_carroforte.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarRota}" style="color:black" />
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; padding-top: 0px !important; padding-bottom: 0px !important" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top:0px !important">
                                <p:outputLabel for="subFil" value="#{localemsgs.Filial}"  />
                                <p:selectOneMenu id="subFil" value="#{rotasescala.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%" label=""
                                                 disabled="#{rotasescala.flag eq 2 or rotasescala.novaRota.flag_Excl eq '*'}">
                                    <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                                   itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                    <p:ajax event="itemSelect" listener="#{rotasescala.SelecionarFilial}"
                                            update="cadastroRota:valores"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-6">
                                <p:outputLabel for="valores" value="#{localemsgs.Rota}"/>
                                <p:inputText id="valores" value="#{rotasescala.novaRota.rota}"
                                             required="true" label="#{localemsgs.Rota}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Rota}"
                                             style="width: 100%"
                                             maxlength="3" disabled="#{rotasescala.flag eq 2 or rotasescala.novaRota.flag_Excl eq '*'}">
                                    <p:watermark for="valores" value="#{localemsgs.Rota}"/>
                                </p:inputText>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-6">
                                <p:outputLabel for="data" value="#{localemsgs.Data}" />
                                <p:inputMask id="data" value="#{rotasescala.novaRota.data}" mask="99/99/9999"
                                             required="true" label="#{localemsgs.Data}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                             style="width: 100%"
                                             maxlength="8" placeholder="00/00/0000"
                                             converter="conversorData" disabled="#{rotasescala.flag eq 2 or rotasescala.novaRota.flag_Excl eq '*'}">
                                </p:inputMask>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12">
                                <p:outputLabel for="tpvei" value="#{localemsgs.TpVeic}"/>
                                <p:selectOneMenu value="#{rotasescala.novaRota.tpVeic}"
                                                 style="width: 100%"  required="true"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                 id="tpvei" disabled="#{rotasescala.flag eq 2 or rotasescala.novaRota.flag_Excl eq '*'}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Forte}" itemValue="F"/>
                                    <f:selectItem itemLabel="#{localemsgs.Leve}" itemValue="L"/>
                                    <f:selectItem itemLabel="#{localemsgs.Moto}" itemValue="M"/>
                                    <f:selectItem itemLabel="#{localemsgs.Pesado}" itemValue="P"/>
                                    <f:selectItem itemLabel="#{localemsgs.Aeronave}" itemValue="A"/>
                                    <f:selectItem itemLabel="#{localemsgs.Nenhum}" itemValue="N"/>

                                    <p:ajax update="msgs infoForte" oncomplete="PF('dlgCadastrar').initPosition()"/>
                                </p:selectOneMenu>
                            </div>

                            <p:panel id="infoForte" class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; background-color: transparent;" rendered="#{rotasescala.novaRota.tpVeic == 'F' }">
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="viagem" value="#{localemsgs.Viagem}" />
                                    <p:inputText id="viagem" value="#{rotasescala.novaRota.viagem}"
                                                 style="width: 100%" validatorMessage="#{localemsgs.Viagem}"
                                                 maxlength="1" disabled="#{rotasescala.flag eq 2 or rotasescala.novaRota.flag_Excl eq '*'}">
                                        <f:validateRegex pattern="[SYNsyn]"  />
                                        <p:ajax update="msgs" event="keyup" />
                                    </p:inputText>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="atm" value="#{localemsgs.ATM}" />
                                    <p:inputText id="atm" value="#{rotasescala.novaRota.ATM}"
                                                 style="width: 100%"
                                                 maxlength="1" disabled="#{rotasescala.flag eq 2 or rotasescala.novaRota.flag_Excl eq '*'}">
                                        <f:validateRegex pattern="[SYNsyn]"  />
                                        <p:ajax update="msgs" event="keyup" />
                                    </p:inputText>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="bacen" value="#{localemsgs.Bacen}" />
                                    <p:inputText id="bacen" value="#{rotasescala.novaRota.BACEN}"
                                                 style="width: 100%"
                                                 maxlength="1" disabled="#{rotasescala.flag eq 2 or rotasescala.novaRota.flag_Excl eq '*'}">
                                        <f:validateRegex pattern="[SYNsyn]"  />
                                        <p:ajax update="msgs" event="keyup" />
                                    </p:inputText>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="aeroporto" value="#{localemsgs.Aeroporto}" />
                                    <p:inputText id="aeroporto" value="#{rotasescala.novaRota.aeroporto}"
                                                 style="width: 100%"
                                                 maxlength="1" disabled="#{rotasescala.flag eq 2 or rotasescala.novaRota.flag_Excl eq '*'}">
                                        <f:validateRegex pattern="[SYNsyn]"  />
                                        <p:ajax update="msgs" event="keyup" />
                                    </p:inputText>
                                </div>
                            </p:panel>


                            <div class="col-md-3 col-sm-3 col-xs-6">
                                <p:outputLabel for="hrLargada" value="#{localemsgs.Horario}"/>
                                <p:inputMask id="hrLargada" value="#{rotasescala.novaRota.hrLargada}"
                                             mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                             required="true" placeholder="00:00"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Largada}"
                                             maxlength="4" style="width: 100%" disabled="#{rotasescala.novaRota.flag_Excl eq '*'}">
                                    <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                </p:inputMask>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-6">
                                <p:outputLabel for="hrChegada" value="#{localemsgs.a}" />
                                <p:inputMask id="hrChegada" value="#{rotasescala.novaRota.hrChegada}"
                                             mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                             required="true"  placeholder="00:00"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Chegada}"
                                             maxlength="4" style="width: 100%" disabled="#{rotasescala.novaRota.flag_Excl eq '*'}">
                                    <p:ajax process="@this" update="msgs totalhr" partialSubmit="true"
                                            listener="#{rotasescala.calculaHorasTrabalhadas}" event="blur"/>
                                </p:inputMask>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-6">
                                <p:outputLabel for="hrIntIni" value="#{localemsgs.Intervalo} "/>
                                <p:inputMask id="hrIntIni" value="#{rotasescala.novaRota.hrIntIni}"
                                             mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                             required="true" placeholder="00:00" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Intini}"
                                             maxlength="4" disabled="#{rotasescala.novaRota.flag_Excl eq '*'}">
                                    <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                </p:inputMask>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">
                                <p:outputLabel for="hrIntFim" value="#{localemsgs.a}" />
                                <p:inputMask id="hrIntFim" value="#{rotasescala.novaRota.hrIntFim}"
                                             mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                             required="true"  placeholder="00:00"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_IntFim}"
                                             maxlength="4" style="width: 100%" disabled="#{rotasescala.novaRota.flag_Excl eq '*'}">
                                    <p:ajax process="@this" update="msgs totalhr" partialSubmit="true"
                                            listener="#{rotasescala.calculaHorasTrabalhadas}" event="blur"/>
                                </p:inputMask>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">
                                <p:outputLabel for="totalhr" value="#{localemsgs.Hr_Total}" escape="false"/>
                                <p:inputText id="totalhr" value="#{rotasescala.novaRota.hsTotal}"
                                             maxlength="4" style="width: 100%"
                                             disabled="true">
                                    <f:convertNumber maxFractionDigits="2"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-#{rotasescala.novaRota.tpVeic == 'F' ?'9':'12'} col-sm-#{rotasescala.novaRota.tpVeic == 'F' ?'9':'12'} col-xs-12">
                                <p:outputLabel for="obs" value="#{localemsgs.Obs}" />
                                <p:inputText id="obs" value="#{rotasescala.novaRota.observacao}" style="width: 100%"
                                             label="#{localemsgs.Obs}" disabled="#{rotasescala.novaRota.flag_Excl eq '*'}" >
                                    <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                                </p:inputText>
                            </div>

                            <p:panel id="panelTabelaSupervisao" class="col-md-12 col-sm-12 col-xs-12" style="background-color: transparent;" rendered="#{rotasescala.flag eq 2}">
                                <label style="white-space: nowrap">
                                    <img src="../assets/img/icone_satmob_trajetos_40x40.png"
                                         height="30"
                                         width="30" rendered="#{rotasescala.flag eq 2}"/>

                                    <h:outputText value="#{localemsgs.Trajetos}"
                                                  style="font-size: 14px; font-weight: bold"/>
                                </label>

                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top:0px !important; padding-left: 0px !important; padding-right: 0px !important;">
                                    <div class="col-md-11 col-sm-11 col-xs-11" style="width:calc(100% - 62px) !important; padding:0px !important; margin:0px !important; height:195px !important; overflow:hidden;">
                                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; margin:0px !important; height:160px !important; overflow:auto;">
                                            <p:dataTable
                                                id="trajetos"
                                                value="#{rotasescala.trajetos}"
                                                sortBy="#{listaTrajetos.hora1}"
                                                rowKey="#{listaTrajetos.parada}"
                                                emptyMessage="#{localemsgs.SemRegistros}"
                                                rendered="#{rotasescala.flag eq 2}"
                                                selection="#{rotasescala.trajetoSelecionado}"
                                                var="listaTrajetos"
                                                reflow="true"
                                                scrollWidth="100%" 
                                                selectionMode="single"
                                                styleClass="tabela DataGrid"
                                                style="padding:0px !important; margin-top:0px !important; margin-left:0px !important;"
                                                >
                                                <p:ajax event="rowDblselect" update="msgs cadastroTrajeto"
                                                        listener="#{rotasescala.selecionarTrajeto}"/>
                                                <p:column headerText="#{localemsgs.Parada}" class="text-center" style="width: 80px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.parada}" class="text-center" />
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hora1}" class="text-center"
                                                          style="width: 100px; #{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.hora1}" converter="conversorHora" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.ER}" class="text-center"
                                                          style="width: 100px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.ER}" class="text-center" />
                                                </p:column>
                                                <p:column headerText="#{localemsgs.CodCli1}" class="text-center"
                                                          style="width: 140px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.codCli1}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.NRed}" class="text-center" style="width: 200px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.NRed}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.CodCli2}" class="text-center"
                                                          style="width: 140px; #{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.codCli2}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.DPar}" class="text-center"
                                                          style="width: 100px; #{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.DPar}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hora1D}" class="text-center"
                                                          style="width: 100px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.hora1D}" converter="conversorHora" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Valor}" class="text-center" style="width: 100px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.valor}" converter="conversormoeda" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.tipoServ}" class="text-center"
                                                          style="width: 100px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.tipoSrv}" class="text-center"/>
                                                </p:column> 
                                                <p:column headerText="#{localemsgs.Obs}" class="text-center"
                                                          style="width: 200px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.observ}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Pedido}" class="text-center"
                                                          style="width: 100px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.pedido}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Operador}" class="text-center"
                                                          style="width: 140px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.operador}" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center"
                                                          style="width: 100px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.dt_Alter}" converter="conversorData" class="text-center"/>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center"
                                                          style="width: 100px;#{listaTrajetos.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                                    <h:outputText value="#{listaTrajetos.hr_Alter}" class="text-center"/>
                                                </p:column>
                                            </p:dataTable>
                                        </div>
                                        <div class="col-md-6 col-sm-6 col-xs-6">
                                            <h:outputText
                                                id="qtdParadas"
                                                value="#{localemsgs.QtdParadas}: #{rotasescala.trajetos.size()}"
                                                style="font-size: 12px; color:#000 !important"/>
                                        </div>
                                        <div class="col-md-6 col-sm-6 col-xs-6" style="text-align:right; padding-right: 0px !important;">
                                            <p:outputLabel
                                                for="checkboxCliente"
                                                value="#{localemsgs.ExibirExcluidos}: "
                                                style="font-size: 12px; color: red !important; margin-right: 6px !important"/>
                                            <p:selectBooleanCheckbox
                                                id="checkboxCliente"
                                                value="#{rotasescala.exclFlag}"
                                                style="font-size: 12px"
                                                disabled="#{rotasescala.novaRota.flag_Excl eq '*'}">
                                                <p:ajax
                                                    update="msgs trajetos qtdParadas"
                                                    listener="#{rotasescala.listaTrajetosSemCoordenada()}"/>
                                            </p:selectBooleanCheckbox>
                                        </div>
                                    </div>
                                    <div class="col-md-1 col-sm-1 col-xs-1" style="width: 58px; margin-top: 8px; padding:6px 0px 0px 0px !important; margin:0px !important; height:160px; margin-left:4px !important; border:thin solid #DDD;background-color:#FFF;">
                                        <p:panel style="width:100% !important; width:100%; text-align:center; margin-top:10px !important; background: transparent">
                                            <p:commandLink
                                                title="#{localemsgs.Adicionar}"
                                                action="#{rotasescala.novoTrajeto}"
                                                update="cadastroTrajeto"
                                                disabled="#{rotasescala.novaRota.flag_Excl eq '*'}">
                                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40" style="margin-bottom:2px" />
                                            </p:commandLink>

                                            <p:commandLink title="#{localemsgs.Editar}" update="cadastroTrajeto msgs"
                                                           disabled="#{rotasescala.novaRota.flag_Excl eq '*'}"
                                                           actionListener="#{rotasescala.abrirTrajeto}">
                                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40" style="margin-bottom:2px" />
                                            </p:commandLink>

                                            <p:commandLink
                                                title="#{localemsgs.Excluir}"
                                                actionListener="#{rotasescala.excluirTrajeto}"
                                                action="#{rotasescala.listaTrajetosSemCoordenada()}"
                                                update="cadastroTrajeto cadastroRota:trajetos msgs"
                                                disabled="#{rotasescala.novaRota.flag_Excl eq '*'}">
                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40" style="margin-bottom:2px" />
                                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirTrajeto}" icon="ui-icon-alert"/>
                                            </p:commandLink>
                                        </p:panel>
                                    </div>
                                </div>
                            </p:panel>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; padding-bottom: 0px !important;">
                                <p:commandLink rendered="#{rotasescala.flag eq 1}" id="cadastroRotaAction"
                                               update="main:tabela :msgs" actionListener="#{rotasescala.cadastrar}"
                                               title="#{localemsgs.Cadastrar}" disabled="#{rotasescala.novaRota.flag_Excl eq '*'}" styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                                <p:commandLink rendered="#{rotasescala.flag eq 2}" id="edit" action="#{rotasescala.editar}"
                                               update=":msgs main:tabela cadastrar"
                                               title="#{localemsgs.Editar}" disabled="#{rotasescala.novaRota.flag_Excl eq '*'}" styleClass="btn btn-primary" style="color:#FFF !important">
                                    <f:viewAction action="#{rotasescala.ListarData}"/>
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--INÍCIO MODAL DE TRAJETOS-->
                <h:form id="cadastroTrajeto" class="form-inline" style="padding-bottom:0px !important">
                    <p:dialog
                        widgetVar="dlgCadastrarTrajetos"
                        id="dlgCadastroTrajeto"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        focus="horaRota"
                        style="height:95% !important;min-height:600px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 0px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_trajetos_40x40.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarTrajeto}" style="color:black" />
                        </f:facet>

                        <p:panel id="cadastrarTrajeto" style="background-color: transparent; padding-top:0px !important; padding-bottom:0px !important;" styleClass="cadastrar">
                            <div class="row" style="padding: 0px; margin: 0px;">
                            <div class="col-md-9 col-sm-9 col-xs-12" style="padding-top:0px !important">
                                <p:outputLabel for="filialrota" value="#{localemsgs.Filial}"/>
                                <p:selectOneMenu id="filialrota" value="#{rotasescala.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%" label=""
                                                 disabled="true">
                                    <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                                   itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6" style="padding-top:0px !important">
                                <p:outputLabel for="rotaTrajeto" value="#{localemsgs.Rota}"/>
                                <p:inputText value="#{rotasescala.novaRota.rota}" disabled="true" id="rotaTrajeto"
                                             style="width: 100%;">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </div>
                                </div>
                            <div class="row" style="padding: 0px; margin: 0px;">
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="parada" value="#{localemsgs.Parada}"/>
                                    <p:inputText value="#{rotasescala.trajetoSelecionado.parada}" disabled="true" id="parada"
                                                 style="width: 100%;">
                                        <f:convertNumber pattern="000"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="horaRota" value="#{localemsgs.Hora}" />
                                    <p:inputMask id="horaRota" mask="#{mascaras.mascaraHora}" value="#{rotasescala.trajetoSelecionado.hora1}"
                                                 disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}"
                                                 required="true" placeholder="00:00" converter="conversorHora"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                                 style="width: 100%" maxlength="4">
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="ER" value="#{localemsgs.Tipo}" indicateRequired="false"/>
                                    <p:selectOneMenu value="#{rotasescala.trajetoSelecionado.ER}"
                                                     style="width: 100%"  required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                     id="ER" disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Entrega}" itemValue="E"/>
                                        <f:selectItem itemLabel="#{localemsgs.Recolhimento}" itemValue="R"/>
                                        <f:selectItem itemLabel="#{localemsgs.EntregaRecolhimento}" itemValue="ER"/>
                                        <f:selectItem itemLabel="#{localemsgs.Transbordo}" itemValue="T"/>
                                        <p:ajax update="destinoRecolhimento" oncomplete="PF('dlgCadastrarTrajetos').initPosition()"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="tipoSrv" value="#{localemsgs.tipoServ}" indicateRequired="false"/>
                                    <p:selectOneMenu value="#{rotasescala.trajetoSelecionado.tipoSrv}"
                                                     style="width: 100%"  required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                     id="tipoSrv" disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Rotineiro}" itemValue="R"/>
                                        <f:selectItem itemLabel="#{localemsgs.Eventual}" itemValue="V"/>
                                        <f:selectItem itemLabel="#{localemsgs.Especial}" itemValue="E"/>
                                        <f:selectItem itemLabel="#{localemsgs.AssistTecnica}" itemValue="A"/>
                                        <f:selectItem itemLabel="#{localemsgs.Intermediaria}" itemValue="I"/>
                                        <f:selectItem itemLabel="#{localemsgs.Preliminar}" itemValue="P"/>
                                        <f:selectItem itemLabel="#{localemsgs.ProjetoEspecial}" itemValue="J"/>
                                        <p:ajax update="destinoRecolhimento"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>
                            <!-- ITENS PARA REAPROVEITAMENTO DA TELA DE CLIENTES -->
                            <!-- Início -->
                            <h:inputHidden value="#{rotasescala.codCliReaproveitaOrigem}" id="txtReaproveitaOri" />
                            <h:inputHidden value="#{rotasescala.codCliReaproveitaDestino}" id="txtReaproveitaDst" />
                            <p:commandLink id="btReaproveitaOri" partialSubmit="true" process="@this" onclick="rc();" />
                            <p:commandLink id="btReaproveitaDst" partialSubmit="true" process="@this" onclick="rc1();" />
                            <p:remoteCommand name="rc" partialSubmit="true" 
                                             process="@this,txtReaproveitaOri,txtReaproveitaDst" 
                                             update="origem origemCodFil origemEnde cliente1 msgs" 
                                             actionListener="#{rotasescala.reaproveitaClienteOrigem()}" />                            
                            <p:remoteCommand name="rc1" partialSubmit="true" 
                                             process="@this,txtReaproveitaOri,txtReaproveitaDst" 
                                             update="destinoRecolhimento msgs" 
                                             actionListener="#{rotasescala.reaproveitaClienteDestino()}" />
                            <!-- FIM -->

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-bottom: 0px !important;">
                                <p:outputLabel for="origem" value="#{localemsgs.Origem}" />
                                <label id="btPesquisarClienteOrigem" style="position:absolute !important; font-size: 8pt; padding:1px 8px 1px 8px !important; text-align:center; border-radius:30px; background-color:steelblue; color:#FFF; cursor:pointer; margin-left: 12px; width: 80px; height:18px"><i class="fa fa-search"></i>&nbsp;&nbsp;#{localemsgs.Pesquisar}</label>
                            </div>                                
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:inputText value="#{rotasescala.trajetoSelecionado.codCli1}" id="origem" style="width: 100%;" disabled="true"/>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12">
                                <p:autoComplete id="cliente1" value="#{rotasescala.cliOri}" styleClass="cliente2"
                                                style="width: 100%"
                                                inputStyle="width: 100%; font-weight: bold; color:#000 !important;border-color:orangered !important; background-color:lightyellow"
                                                completeMethod="#{rotasescala.listarClientes}"
                                                var="cli" itemLabel="#{cli.NRed}" itemValue="#{cli}" scrollHeight="250"
                                                disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}">
                                    <o:converter converterId="omnifaces.ListConverter" list="#{rotasescala.listaClientes}"/>
                                    <p:ajax event="itemSelect" listener="#{rotasescala.selecionarCliOri}"
                                            update="origem origemCodFil origemEnde cliente1"/>
                                    <p:watermark for="cliente1" value="#{localemsgs.Cliente}" />
                                </p:autoComplete>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:inputText value="#{rotasescala.cliOri.codFil}" id="origemCodFil" style="width: 100%;" disabled="true">
                                    <f:convertNumber pattern="0000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <p:inputText value="#{rotasescala.cliOri.ende}, #{rotasescala.cliOri.cidade}/#{rotasescala.cliOri.estado} - #{rotasescala.cliOri.CEP}" id="origemEnde" style="width: 100%;" disabled="true"/>
                            </div>

                            <p:panel id="destinoRecolhimento" style="background: inherit;">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; display:#{rotasescala.trajetoSelecionado.ER == 'R' or rotasescala.trajetoSelecionado.ER == 'ER'?'':'none'} ">
                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-bottom: 0px !important;">
                                        <p:outputLabel for="destino" value="#{localemsgs.Destino}" />
                                        <label id="btPesquisarClienteDestino" style="position:absolute !important; font-size: 8pt; padding:1px 8px 1px 8px !important; text-align:center; border-radius:30px; background-color:steelblue; color:#FFF; cursor:pointer; margin-left: 12px; width: 80px; height:18px"><i class="fa fa-search"></i>&nbsp;&nbsp;#{localemsgs.Pesquisar}</label>
                                    </div>    
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:inputText
                                            value="#{rotasescala.trajetoSelecionado.codCli2}" id="destino" style="width: 100%;" disabled="true"/>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-12">
                                        <p:autoComplete
                                            id="cliente2"
                                            value="#{rotasescala.cliDst}"
                                            styleClass="cliente2"
                                            style="width: 100%"
                                            inputStyle="width: 100%; font-weight: bold; color:#000 !important;border-color:orangered !important; background-color:lightyellow"
                                            completeMethod="#{rotasescala.listarClientes}"
                                            var="cli2"
                                            itemLabel="#{cli2.NRed}"
                                            itemValue="#{cli2}"
                                            scrollHeight="250"
                                            disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}">
                                            <o:converter
                                                converterId="omnifaces.ListConverter"
                                                list="#{rotasescala.listaClientes}"/>

                                            <p:ajax
                                                event="itemSelect"
                                                listener="#{rotasescala.selecionarCliDst}"
                                                process="@this,horaRota"
                                                update="msgs destinoRecolhimento"/>

                                            <p:watermark for="cliente2" value="#{localemsgs.Cliente}" />
                                        </p:autoComplete>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:inputText
                                            value="#{rotasescala.cliDst.codFil}"
                                            id="destinoCodFil"
                                            style="width: 100%;"
                                            disabled="true">
                                            <f:convertNumber pattern="0000"/>
                                        </p:inputText>
                                    </div>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <p:inputText
                                            id="destinoEnde"
                                            value="#{rotasescala.cliDst.ende}, #{rotasescala.cliDst.cidade}/#{rotasescala.cliDst.estado} - #{rotasescala.cliDst.CEP}"
                                            style="width: 100%;"
                                            disabled="true"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6">
                                        <p:outputLabel
                                            for="dpar"
                                            value="#{localemsgs.Parada}"/>

                                        <p:inputText
                                            value="#{rotasescala.trajetoSelecionado.DPar}"
                                            id="dpar"
                                            style="width: 100%;"
                                            disabled="true"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-6">
                                        <p:outputLabel for="hora1D" value="#{localemsgs.Hora}" indicateRequired="false"/>
                                        <p:inputMask id="hora1D"
                                                     mask="#{mascaras.mascaraHora}"
                                                     value="#{rotasescala.trajetoSelecionado.hora1D}"
                                                     disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}"
                                                     required="#{rotasescala.trajetoSelecionado.ER ne 'E'}"
                                                     placeholder="00:00"
                                                     converter="conversorHora"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                                     style="width: 100%"
                                                     maxlength="4">
                                            <p:ajax process="@this,horaRota" update="msgs" partialSubmit="true"
                                                    listener="#{rotasescala.validaHoraRecolhimento}" event="blur"/>
                                        </p:inputMask>
                                    </div>
                                </div>
                            </p:panel>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel value="#{localemsgs.Valor}" />
                                <p:inputNumber id="input2" value="#{rotasescala.valor}" symbol="#{localemsgs.$} "
                                               decimalSeparator="#{localemsgs.decimalSeparator}"
                                               thousandSeparator="#{localemsgs.thousandSeparator}" style="min-width: 100%;" />
                            </div>
                            <div class="col-md-9 col-sm-9 col-xs-12">
                                <p:outputLabel for="obsTrajeto1" value="#{localemsgs.Obs}"/>
                                <p:inputText id="obsTrajeto1" value="#{rotasescala.trajetoSelecionado.observ}"
                                             style="width:100%"
                                             label="#{localemsgs.Obs}" placeholder="#{localemsgs.Obs}" maxlength="20" size="40"
                                             disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}"/>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-top: 10px !important">
                                <p:commandLink id="adicionarParada" title="#{localemsgs.Cadastrar}"
                                               actionListener="#{rotasescala.cadastrarTrajeto}"
                                               update="cadastroRota:trajetos msgs" disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}"
                                               rendered="#{rotasescala.flagTrajeto eq 1}" styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>

                                <p:commandLink id="editarParada" action="#{rotasescala.editarTrajeto}"
                                               disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}"
                                               title="#{localemsgs.Editar}" update=":cadastroRota:trajetos msgs"
                                               rendered="#{rotasescala.flagTrajeto eq 2}" styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>

                                <p:commandLink id="email" actionListener="#{rotasescala.prepararManifest}"
                                               disabled="#{rotasescala.trajetoSelecionado.flag_Excl eq '*'}"
                                               title="#{localemsgs.EnviarEmail}" update=":cadastroRota:trajetos :msgs"
                                               rendered="#{rotasescala.flagTrajeto eq 2}" styleClass="btn btn-success" style="margin-left: 8px; color:#FFF !important">
                                    <i class="fa fa-envelope" style="margin-right:8px !important"></i>#{localemsgs.EnviarEmail}
                                </p:commandLink>
                            </div>

                            <p:panelGrid
                                columns="1"
                                columnClasses="ui-grid-col-12"
                                layout="grid"
                                styleClass="ui-panelgrid-blank tabs"
                                style="background-color: transparent;"
                                rendered="#{rotasescala.flagTrajeto eq 2}">
                                <p:tabView id="tabs" activeIndex="0">
                                    <p:tab id="tabNotas" title="#{localemsgs.Notas}">
                                        <p:panelGrid
                                            columns="2"
                                            columnClasses="ui-grid-col-11 ui-grid-col-1"
                                            >
                                            <p:panel class="panelTabTabela">
                                                <p:dataTable id="notas" value="#{rotasescala.historico}" sortBy="#{listaHistorico.ordem}"
                                                             style="font-size: 12px" var="listaHistorico" rowKey="#{listaHistorico.ordem}"
                                                             styleClass="tabela" scrollWidth="100%"
                                                             resizableColumns="true" scrollable="true" selectionMode="single" scrollHeight="85"
                                                             emptyMessage="#{localemsgs.SemRegistros}"
                                                             selection="#{rotasescala.historicoSelecionado}">
                                                    <p:column headerText="#{localemsgs.Notas}">
                                                        <h:outputText value="#{listaHistorico.historico}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Ordem}">
                                                        <h:outputText value="#{listaHistorico.ordem}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Operador}">
                                                        <h:outputText value="#{listaHistorico.operador}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Dt_Alter}">
                                                        <h:outputText value="#{listaHistorico.dt_Alter}" converter="conversorData"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hr_Alter}">
                                                        <h:outputText value="#{listaHistorico.hr_Alter}"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </p:panel>

                                            <p:panel
                                                rendered="false"
                                                style="width: 30px; background: none !important; float: right !important">
                                                <p:commandLink
                                                    title="#{localemsgs.Adicionar}"
                                                    style="height: 40px"
                                                    update="cadastroTrajeto">
                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30" style="margin-bottom: 10px"/>
                                                </p:commandLink>

                                                <p:commandLink
                                                    title="#{localemsgs.Editar}"
                                                    update="cadastroTrajeto msgs">
                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30" style="margin-bottom: 10px"/>
                                                </p:commandLink>

                                                <p:commandLink
                                                    title="#{localemsgs.Excluir}"
                                                    update="cadastroTrajeto cadastroRota:trajetos msgs">
                                                    <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="30"/>
                                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirTrajeto}" icon="ui-icon-alert" />
                                                </p:commandLink>
                                            </p:panel>
                                        </p:panelGrid>
                                    </p:tab>

                                    <p:tab id="tabGuias" title="#{localemsgs.Guias} (#{rotasescala.guias.size()})">
                                        <p:panelGrid columns="2">
                                            <p:panel class="panelTabTabela">
                                                <p:dataTable id="guias" value="#{rotasescala.guias}"
                                                             var="guiacliente" rowKey="#{guiacliente.guia}"
                                                             resizableColumns="true" 
                                                             styleClass="tabela" 
                                                             emptyMessage="#{localemsgs.SemRegistros}"
                                                             selectionMode="single" 
                                                             selection="#{rotasescala.guiaSelecionada}"
                                                             scrollable="true" 
                                                             scrollHeight="150"
                                                             style="font-size: 12px; background: transparent; padding:0px !important; margin:0px !important; white-space: nowrap !important">
                                                    <p:ajax event="rowDblselect" listener="#{rotasescala.imprimir}" update="impressao msgs"/>
                                                    <p:column headerText="#{localemsgs.Data}">
                                                        <h:outputText value="#{guiacliente.data}" title="#{guiacliente.data}" converter="conversorData"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Pedido}">
                                                        <h:outputText value="#{guiacliente.pedido}" title="#{guiacliente.pedido}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Guia}">
                                                        <h:outputText value="#{guiacliente.guia}" title="#{guiacliente.guia}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Serie}">
                                                        <h:outputText value="#{guiacliente.serie}" title="#{guiacliente.serie}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Volume}" styleClass="celula-right">
                                                        <h:outputText value="#{guiacliente.volumes}"
                                                                      title="#{guiacliente.volumes}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Lacre}" styleClass="celula-right">
                                                        <h:outputText value="#{guiacliente.volume1}"
                                                                      title="#{guiacliente.volume1}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Operacao}">
                                                        <h:outputText value="#{guiacliente.operacao}" title="#{guiacliente.operacao}" converter="tradutor"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.NRedFat}">
                                                        <h:outputText value="#{guiacliente.NRedFat}" title="#{guiacliente.NRedFat}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.CodCli1}">
                                                        <h:outputText value="#{guiacliente.codCli1}" title="#{guiacliente.codCli1}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.LocalParada}">
                                                        <h:outputText value="#{guiacliente.localParada}" title="#{guiacliente.localParada}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Agencia}">
                                                        <h:outputText value="#{guiacliente.agenciaParada}" title="#{guiacliente.agenciaParada}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.HrCheg}">
                                                        <h:outputText value="#{guiacliente.hrCheg}" title="#{guiacliente.hrCheg}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.HrSaida}">
                                                        <h:outputText value="#{guiacliente.hrSaida}" title="#{guiacliente.hrSaida}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Destino}">
                                                        <h:outputText value="#{guiacliente.destino}" title="#{guiacliente.destino}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Entrega}">
                                                        <h:outputText value="#{guiacliente.entrega}" title="#{guiacliente.entrega}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Valor}" styleClass="celula-right">
                                                        <h:outputText value="#{guiacliente.valor}"
                                                                      title="#{guiacliente.valor}" converter="conversormoeda"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Assinatura}">
                                                        <h:outputText value="#{guiacliente.assinatura}" title="#{guiacliente.assinatura}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.AssinaturaDestino}">
                                                        <h:outputText value="#{guiacliente.assinaturaDestino}" title="#{guiacliente.assinaturaDestino}"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </p:panel>
                                            <p:panel style="width: 30px;">
                                                <p:commandLink title="#{localemsgs.Editar}" style="height: 40px"
                                                               actionListener="#{rotasescala.imprimir}" update="impressao msgs">
                                                    <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="30" style="margin-bottom: 10px"/>
                                                </p:commandLink>
                                            </p:panel>
                                        </p:panelGrid>
                                    </p:tab>
                                </p:tabView>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>

                    <p:dialog widgetVar="dlgTrajetosEntrega" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Clientes}" style="color:black" />
                        </f:facet>
                        <p:panel id="panelTrajetosEntrega" style="background-color: transparent" styleClass="cadastrar">
                            <p:dataTable id="trajetosSugeridos" value="#{rotasescala.trajetosSugeridos}" sortBy="#{entregas.hora1}"
                                         style="font-size: 12px" var="entregas" rowKey="#{entregas.hora1}"
                                         styleClass="tabela" scrollWidth="100%"
                                         resizableColumns="true" scrollable="true" selectionMode="single" scrollHeight="85"
                                         emptyMessage="#{localemsgs.SemRegistros}"
                                         selection="#{rotasescala.trajetoSugeridoSelecionado}">
                                <p:ajax event="rowDblselect" listener="#{rotasescala.selecionarParadaEntrega}"
                                        update="cadastroTrajeto:hora1D cadastroTrajeto:dpar msgs"/>
                                <p:column headerText="#{localemsgs.Parada}" class="text-center" style="width: 47px; font-size: 12px;
                                          #{entregas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                    <h:outputText value="#{entregas.parada}" class="text-center" />
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora1}" class="text-center"
                                          style="width: 50px; #{entregas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                    <h:outputText value="#{entregas.hora1}" converter="conversorHora" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.NRed}" class="text-center" style="width: 150px;
                                          #{entregas.flag_Excl eq '*' ? 'color:orange' : 'color:black'}">
                                    <h:outputText value="#{entregas.NRed}" class="text-center"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                    </p:dialog>
                </h:form>


                <h:form id="paradasPreOrder">
                    <p:dialog widgetVar="dlgParadasPreOrder" positionType="absolute" id="dlgImprimir" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Manifesto}" style="color:black" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                            <p:outputLabel for="paradas" value="#{localemsgs.SelecioneParada}:" />

                            <p:selectOneRadio id="paradas" value="#{rotasescala.trajetoSugeridoSelecionado}" layout="grid" columns="1">
                                <o:converter converterId="omnifaces.ListConverter" list="#{rotasescala.trajetosSugeridos}"/>
                                <f:selectItems value="#{rotasescala.trajetosSugeridos}" var="parada"
                                               itemValue="#{parada}" itemLabel="#{parada.parada}"/>
                            </p:selectOneRadio>

                            <p:commandLink title="#{localemsgs.Avancar}"
                                           update="impressao msgs" action="#{rotasescala.listarManifestoPreOrder}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" height="40"/>
                            </p:commandLink>
                        </p:panel>

                    </p:dialog>
                </h:form>

                <h:form id="impressao">
                    <p:dialog widgetVar="dlgImprimir" positionType="absolute" id="dlgImprimir" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">
                        <f:facet name="header">
                            <div class="ui-grid-col-8">
                                <img src="../assets/img/icone_satmob_guias_40x40.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Imprimir}"/>
                            </div>
                            <div class="ui-grid-col-2" style="text-align: right">
                                <p:commandLink title="#{localemsgs.Imprimir}">
                                    <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="40"/>
                                    <p:printer target="guiaimpressa"/>
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-col-2" style="text-align: right">
                                <p:commandLink title="#{localemsgs.Download}" update="msgs"
                                               ajax="false"
                                               actionListener="#{rotasescala.gerarGuiaDownload}">
                                    <p:graphicImage url="../assets/img/icone_pdf.png" height="40"/>
                                    <p:fileDownload value="#{rotasescala.arquivoDownload}"/>
                                </p:commandLink>
                            </div>
                        </f:facet>

                        <p:panel id="guiaimpressa" class="guiaimpressa" styleClass="guiaimpressa" style="height: 75vh;">

                            <h:outputText value="#{rotasescala.html}" escape="false"/>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formClientesManifest">
                    <p:dialog widgetVar="dlgClientesManifest" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Clientes}" style="color:black" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                            <p:outputLabel for="clientes" value="#{localemsgs.ManifestoPara}:" />
                            <p:selectOneRadio id="clientes" value="#{rotasescala.clienteManifest.codigo}" layout="grid" columns="1">
                                <f:selectItems value="#{rotasescala.clientesManifest}" var="cliente"
                                               itemValue="#{cliente.codigo}" itemLabel="#{cliente.codigo} - #{cliente.NRed}"/>
                            </p:selectOneRadio>

                            <p:commandLink title="#{localemsgs.EnviarEmail}"
                                           update="formClientesManifest msgs" action="#{rotasescala.gerarManifest}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" height="40"/>
                            </p:commandLink>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formEmail">
                    <p:dialog widgetVar="dlgEmail" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Email}" style="color:black" />

                            <p:commandLink title="#{localemsgs.EnviarEmail}" action="#{rotasescala.enviarManifest}"
                                           update="msgs" style="text-align: right; float: right">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.EnviarEmail}?" icon="ui-icon-alert" />
                                <p:graphicImage url="../assets/img/icone_redondo_email.png" height="30"/>
                            </p:commandLink>

                            <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                            </p:confirmDialog>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-6"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="de" value="#{localemsgs.DeRemetente}: "  />
                                <p:inputText id="de" value="#{rotasescala.email.remet_nome}"
                                             style="width: 100%"/>
                                <p:inputText id="deEmail" value="#{rotasescala.email.remet_email}"
                                             style="width: 100%" disabled="true"/>

                                <p:outputLabel for="para" value="#{localemsgs.Para}: "  />
                                <p:inputText id="para" value="#{rotasescala.email.dest_nome}"
                                             style="width: 100%"/>
                                <p:inputText id="paraEmai" value="#{rotasescala.email.dest_email}"
                                             style="width: 100%"/>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">

                                <p:outputLabel for="assunto" value="#{localemsgs.Assunto}: "  />
                                <p:inputText id="assunto" value="#{rotasescala.email.assunto}" style="width: 100%"/>

                                <h:outputText id="mensagemLbl" value="#{localemsgs.Mensagem}: "/>
                                <h:outputText id="mensagem" value="#{rotasescala.email.mensagem}" escape="false"/>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formContainers">
                    <p:dialog widgetVar="dlgContainers" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_cacamba.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Containers}" style="color:black" />
                        </f:facet>
                        <p:panel id="panelContainers" style="background-color: transparent" styleClass="cadastrar2">

                            <div class="form-inline">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="dataContainer1" value="#{localemsgs.DtInicio}:" indicateRequired="false"/>
                                    <p:inputMask id="dataContainer1" value="#{rotasescala.dataContainer1}" mask="99/99/9999"
                                                 required="true" label="#{localemsgs.Data}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtInicio}"
                                                 style="width: 100%"
                                                 maxlength="8" placeholder="#{mascaras.padraoData}"
                                                 converter="conversorData"/>

                                    <p:outputLabel for="dataContainer2" value="#{localemsgs.DtFim}:" indicateRequired="false"/>
                                    <p:inputMask id="dataContainer2" value="#{rotasescala.dataContainer2}" mask="99/99/9999"
                                                 required="true" label="#{localemsgs.Data}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtFim}"
                                                 style="width: 100%"
                                                 maxlength="8" placeholder="#{mascaras.padraoData}"
                                                 converter="conversorData"/>
                                </p:panelGrid>
                            </div>
                            <div class="form-inline">
                                <p:dataTable id="tabelaContainers" value="#{rotasescala.containers}" filteredValue="#{rotasescala.containersFiltrado}"
                                             style="font-size: 12px" var="containers" rowKey="#{containers.lacre}" selection="#{rotasescala.containerSelecionado}"
                                             styleClass="tabela" scrollWidth="100%" widgetVar="tabelaContainers" selectionMode="single"
                                             resizableColumns="true" scrollable="true" scrollHeight="85" >
                                    <p:ajax event="rowDblselect" listener="#{rotasescala.dblSelectContainer}" update="msgs"/>
                                    <p:column headerText="#{localemsgs.Container}" style="width: 47px; font-size: 12px;"
                                              filterBy="#{containers.lacre}" filterMatchMode="contains">
                                        <h:outputText value="#{containers.lacre}"/>
                                    </p:column>
                                    <f:facet name="footer">
                                        <div class="ui-grid-row ui-grid-responsive">
                                            <div class="ui-grid-col-6" style="color:black; text-align: left; font-weight: normal">
                                                <h:outputText value="#{localemsgs.Buscar}: " />
                                            </div>
                                            <div class="ui-grid-col-6" style="color:black; text-align: right; font-weight: normal">
                                                <p:inputText id="globalFilter" onkeyup="PF('tabelaContainers').filter()" style="width:100%;"/>
                                            </div>
                                        </div>
                                    </f:facet>
                                </p:dataTable>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{rotasescala.listarMovimentacaoContainers}"
                                               title="#{localemsgs.Selecionar}" update="msgs">
                                    <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>

                    <p:dialog widgetVar="dlgMovimentacaoContainer" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_cacamba.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.MovimentacaoContainer}" style="color:black" />
                        </f:facet>
                        <p:panel id="panelHistoricoContainers" style="background-color: transparent" styleClass="cadastrar">
                            <div class="form-inline">
                                <p:dataTable id="tabelaMovimentacaoContainer" value="#{rotasescala.historicoMovimentacao}"
                                             style="font-size: 12px" var="historicoMovimentacao"
                                             styleClass="tabela" scrollWidth="100%"
                                             resizableColumns="true" scrollable="true" scrollHeight="400" >
                                    <f:facet name="header">
                                        <h:outputText value="#{localemsgs.Container}: #{rotasescala.containerSelecionado.lacre}. "/>
                                        <h:outputText value="#{rotasescala.dataContainer1}" converter="conversorData"/>
                                        <h:outputText value=" - "/>
                                        <h:outputText value="#{rotasescala.dataContainer2}" converter="conversorData"/>
                                    </f:facet>
                                    <p:column headerText="#{localemsgs.ER}" style="width: 25px">
                                        <h:outputText value="#{historicoMovimentacao.tpServico}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.CodFil}" style="width: 50px">
                                        <h:outputText value="#{historicoMovimentacao.codFil}" converter="conversorCodFil"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                        <h:outputText value="#{historicoMovimentacao.data}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Rota}" style="width: 50px">
                                        <h:outputText value="#{historicoMovimentacao.rota}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.HrServico}" style="width: 65px">
                                        <h:outputText value="#{historicoMovimentacao.hrServico}" converter="conversorHora"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Nome}" style="width: 200px">
                                        <h:outputText value="#{historicoMovimentacao.nome}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.NRed}" style="width: 150px">
                                        <h:outputText value="#{historicoMovimentacao.nred}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Ende}" style="width: 280px">
                                        <h:outputText value="#{historicoMovimentacao.ende}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Bairro}" style="width: 150px">
                                        <h:outputText value="#{historicoMovimentacao.bairro}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cidade}" style="width: 125px">
                                        <h:outputText value="#{historicoMovimentacao.cidade}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Estado}" style="width: 55px">
                                        <h:outputText value="#{historicoMovimentacao.estado}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Motorista}" style="width: 200px">
                                        <h:outputText value="#{historicoMovimentacao.motorista}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Placa}" style="width: 90px">
                                        <h:outputText value="#{historicoMovimentacao.placa}"/>
                                    </p:column>
                                </p:dataTable>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formRotasModelos">
                    <p:dialog
                        widgetVar="dialogCriarRotaModelo"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        style="height: auto; max-height:90% !important; max-width: 350px !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 0px 0px !important; background-color:#EEE !important;"
                        >
                        <f:facet name="header">
                            <img src="../assets/img/icone_adicionar.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.GeracaoRotasXModelos}" style="color:black" />
                        </f:facet>

                        <p:outputPanel id="outputModelos" style="padding:0px !important">
                            <div class="col-md-2 col-sm-2 col-xs-12">
                                <p:outputLabel for="@next" value="#{localemsgs.Tipo}: " />
                            </div>
                            <div class="verticalSpace col-md-10 col-sm-10 col-xs-12"
                                 style="width: 250px;"
                                 >
                                <p:selectOneMenu
                                    id="tipo"
                                    value="#{rotasescala.tipoModelo}"
                                    required="true"
                                    style="min-width: 100px !important; font-size:12pt; font-weight:600 !important; font-family: 'Trebuchet MS'">
                                    <f:selectItem itemLabel="1 - #{localemsgs.GerarRotasUsandoModelo}" itemValue="1" />
                                    <f:selectItem itemLabel="2 - #{localemsgs.GerarModeloUsandoRotas}" itemValue="2" />
                                    <f:selectItem itemLabel="3 - #{localemsgs.GerarPedidosUsandoFrequencia}" itemValue="3" />
                                    <f:selectItem itemLabel="4 - #{localemsgs.RoteirizarPedidos}" itemValue="4" />
                                    <f:selectItem itemLabel="5 - #{localemsgs.FrequenciaRota}" itemValue="5" />
                                    <p:ajax update="outputModelos cadastroComModelo"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-2 col-sm-2 col-xs-12">
                                <p:outputLabel for="data" value="#{localemsgs.Data}: " />
                            </div>
                            <div class="verticalSpace col-md-5 col-sm-5 col-xs-12"
                                 style="width: 125px;"
                                 >
                                <p:calendar
                                    id="data"
                                    value="#{rotasescala.dataModelo}"
                                    pattern="#{mascaras.padraoData}"
                                    locale="#{localeController.getCurrentLocale()}"
                                    size="10"
                                    style="width: 125px;"
                                    converter="conversorData">
                                    <p:ajax event="dateSelect"
                                            listener="#{rotasescala.selecionarDataModelo}"
                                            update="@next" />
                                </p:calendar>
                            </div>
                            <div class="verticalSpace col-md-5 col-sm-5 col-xs-12"
                                 style="width: 125px;"
                                 >
                                <p:inputText
                                    disabled="true"
                                    value="#{rotasescala.dataModelo}"
                                    converter="conversorDiaSemNumero"
                                    style="width: 100%;"
                                    />
                            </div>

                            <div class='col-md-12' style="display:#{rotasescala.tipoModelo eq '2'?'':'none'}; background-color:lightyellow; border:thin solid orange; padding:8px !important; margin-bottom:10px !important">
                                <p:outputLabel for="txtNomeModelo" value="#{localemsgs.ModeloNome}: " />
                                <p:inputText id="txtNomeModelo" value="#{rotasescala.nomeNovoModelo}" style="width: 100%;" />
                            </div>

                            <ui:fragment rendered="#{rotasescala.tipoModelo eq '1'}">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="background-color:#EEE !important">
                                    <p:outputLabel for="gridModelos" value="#{localemsgs.ModeloUtilizar}: " />
                                </div>
                                <div class="verticalSpace col-md-12 col-sm-12 col-xs-12" style="background-color:#EEE !important">
                                    <p:dataTable
                                        id="gridModelos"
                                        value="#{rotasescala.listaRotaModelos}"
                                        selection="#{rotasescala.rotaModeloSelecionada}"
                                        rowKey="#{listaModelo.modelo};#{listaModelo.qtdeRotas};#{listaModelo.flag_Excl}"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        var="listaModelo"
                                        scrollable="true"
                                        selectionMode="single"
                                        styleClass="DataGrid"
                                        style="font-size: 12px; max-height: 200px !important; height: 200px !important; overflow-y: hidden !important;background-color:#EEE !important">
                                        <p:ajax event="rowSelect" listener="#{rotasescala.onModeloRowSelect}"
                                                update="msgs"/>
                                        <p:column headerText="Modelo">
                                            <h:outputText
                                                style="#{listaModelo.flag_Excl eq '*' ? 'color:red' : null}"
                                                value="#{listaModelo.modelo}"/>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.QtdRotas}">
                                            <h:outputText
                                                style="#{listaModelo.flag_Excl eq '*' ? 'color:red' : null}"
                                                value="#{listaModelo.qtdeRotas}"/>
                                        </p:column>
                                    </p:dataTable>
                                </div>
                            </ui:fragment>

                            <div class="col-md-7 col-sm-7 col-xs-7">
                                <p:outputLabel for="chkSomenteAtivos" value="#{localemsgs.MostrarSomenteAtivos}: " rendered="#{rotasescala.tipoModelo eq '1'}"/>
                            </div>
                            <div class="verticalSpace col-md-5 col-sm-5 col-xs-5">
                                <p:selectBooleanCheckbox id="chkSomenteAtivos" value="#{rotasescala.apenasModelosAtivos}" rendered="#{rotasescala.tipoModelo eq '1'}">
                                    <p:ajax update="msgs outputModelos"
                                            listener="#{rotasescala.verModelosAtivos()}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div class="col-md-7 col-sm-7 col-xs-7">
                                <p:outputLabel for="tolerancia" value="#{localemsgs.Tolerancia}: " />
                            </div>
                            <div class="verticalSpace col-md-5 col-sm-5 col-xs-5"
                                 style="width: 50px;"
                                 >
                                <p:inputNumber
                                    id="tolerancia"
                                    value="#{rotasescala.tolerancia}"
                                    required="true"
                                    label="#{localemsgs.Rota}"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tolerancia}"
                                    maxlength="3"
                                    decimalPlaces="0"
                                    style="max-width: 50px !important;"
                                    >
                                    <p:watermark for="tolerancia" value="#{localemsgs.Rota}"/>
                                </p:inputNumber>
                            </div>

                            <!--TODO-->
                            <ui:fragment id="roteirizaAuto" rendered="false">
                                <div class="col-md-7 col-sm-7 col-xs-7">
                                    <p:outputLabel for="tolerancia" value="#{localemsgs.RoteirizaAuto}: " />
                                </div>
                                <div class="verticalSpace col-md-5 col-sm-5 col-xs-5">
                                    <p:selectBooleanCheckbox value="#{rotasescala.roteirizaAuto}" />
                                </div>
                            </ui:fragment>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; ">
                                <p:commandLink
                                    id="cadastroComModelo"
                                    update="main formRotasModelos msgs"
                                    actionListener="#{rotasescala.cadastrarRotaModelo}"
                                    rendered="#{rotasescala.tipoModelo eq '1' or rotasescala.tipoModelo eq '2' or rotasescala.tipoModelo eq '3'}"
                                    title="#{localemsgs.Cadastrar}" styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:outputPanel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <script type="text/javascript">
                // <![CDATA[
                function CarregarClienteDestino(inCodigoCliente) {
                    $JanelaFormClientes.close();

                    $('[id*="txtReaproveitaDst"]').val(inCodigoCliente);
                    $('[id*="btReaproveitaDst"]').click();
                }

                function CarregarClienteOrigem(inCodigoCliente) {
                    $JanelaFormClientes.close();

                    $('[id*="txtReaproveitaOri"]').val(inCodigoCliente);
                    $('[id*="btReaproveitaOri"]').click();
                }

                $(document)
                        .on('mousedown', 'label[id*="btPesquisarCliente"]', function () {
                            let Tipo = '';
                            let Altura = $('body').height() - 280;

                            if ($(this).attr('id') === 'btPesquisarClienteDestino')
                                Tipo = 'D';
                            else if ($(this).attr('id') === 'btPesquisarClienteOrigem')
                                Tipo = 'O';

                            let HTML = '<iframe id="ifrClientes" src="../comercial/clientes.xhtml?selecao=S&tipo=' + Tipo + '" style="border:thin solid #CCC !important; margin:0px !important; padding:0px !important; width: 100% !important; height: ' + Altura.toString() + 'px !important;"></iframe>';

                            $JanelaFormClientes = $.alert({
                                icon: 'fa fa-search',
                                type: 'blue',
                                title: '<font color="#000">#{localemsgs.SelecioneCliente}</font>',
                                content: HTML,
                                boxWidth: '90%',
                                closeIcon: true,
                                useBootstrap: false,
                                buttons: {
                                    cancel: {
                                        btnClass: 'btn-red',
                                        text: '<i class="fa fa-ban"></i>&nbsp;#{localemsgs.Cancelar}'
                                    }
                                }
                            });
                        })
                        ;
                // ]]>
            </script>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText styleClass="corporativo-label" value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{rotasescala.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{rotasescala.MostrarFiliais()}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText styleClass="corporativo-label" value="#{localemsgs.SomenteAtivos}: " /></label>
                                <p:selectBooleanCheckbox value="#{rotasescala.somenteAtivos}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{rotasescala.verSomenteAtivos()}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>

                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" action="#{localeController.getLocales}">
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25"/>
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>
        </h:body>
    </f:view>
</html>

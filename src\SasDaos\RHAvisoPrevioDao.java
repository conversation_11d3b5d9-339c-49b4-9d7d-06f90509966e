/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RHAvisoPrevio;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RHAvisoPrevioDao {

    public List<RHAvisoPrevio> lista(String matricula, Persistencia persistencia) throws Exception {
        try {
            List<RHAvisoPrevio> retorno = new ArrayList<>();
            String sql = "Select RHAvisoPrevio.* from RHAvisoPrevio \n"
                    + " Where Matr = ?\n"
                    + " order by Matr ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();

            RHAvisoPrevio rhAvisoPrevio;

            while (consulta.Proximo()) {
                rhAvisoPrevio = new RHAvisoPrevio();

                rhAvisoPrevio.setNumero(consulta.getString("Numero"));
                rhAvisoPrevio.setData(consulta.getLocalDate("Data"));
                rhAvisoPrevio.setMatr(consulta.getString("Matr"));
                rhAvisoPrevio.setIniciativa(consulta.getString("Iniciativa"));
                rhAvisoPrevio.setTipo(consulta.getString("Tipo"));
                rhAvisoPrevio.setDtIncio(consulta.getLocalDate("DtInicio"));
                rhAvisoPrevio.setDtFim(consulta.getLocalDate("DtFim"));
                rhAvisoPrevio.setMensagem(consulta.getString("Mensagem"));
                rhAvisoPrevio.setObs(consulta.getString("Obs"));
                rhAvisoPrevio.setCodModelo(consulta.getString("CodModelo"));
                rhAvisoPrevio.setOperador(consulta.getString("Operador"));
                rhAvisoPrevio.setDt_alter(consulta.getString("Dt_Alter"));
                rhAvisoPrevio.setHr_alter(consulta.getString("Hr_Alter"));

                retorno.add(rhAvisoPrevio);
            }

            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("RHAvisoPrevioDao.Lista - " + e.getMessage()
                    + "Select RHAvisoPrevio.* from RHAvisoPrevio \n"
                    + " Where Matr = " + matricula + "\n"
                    + " order by Matr ");
        }
    }

}

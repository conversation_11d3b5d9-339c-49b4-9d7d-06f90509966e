/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.Escala.EscalaSatMobWeb;
import Dados.Persistencia;
import SasBeans.Escala;
import SasBeansCompostas.EscalaPessoaDTO;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class EscalaPessoaLazyList extends LazyDataModel<EscalaPessoaDTO> {

    private List<EscalaPessoaDTO> lista;
    private final EscalaSatMobWeb service;

    public EscalaPessoaLazyList(Persistencia persistencia) {
        service = new EscalaSatMobWeb(persistencia);
    }

    @Override
    public List<EscalaPessoaDTO> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            String codFil = (String) filters.get("escala.codfil = ? ");
            String data = (String) filters.get("escala.data = ? ");
            boolean excluded = filters.get("rotas.flag_excl <> ? ").equals("*");
            String rota = (String) filters.get("rota");
            String nome = (String) filters.get("nome");
            String nred = (String) filters.get("nred");

            lista = service.listaPaginadaEscala(first, pageSize, codFil, data, excluded, rota, nome, nred);
            setRowCount(service.contagemEscalaPessoa(codFil, data, excluded, rota, nome, nred));
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return lista;
    }

    @Override
    public Object getRowKey(EscalaPessoaDTO row) {
        Escala escala = row.getEscala();
        String Rota = escala.getRota();
        String Data = escala.getData();
        String CodFil = escala.getCodFil().toString();

        return Rota + ";" + Data + ";" + CodFil;
    }

    @Override
    public EscalaPessoaDTO getRowData(String key) {
        for (EscalaPessoaDTO row : lista) {
            if (key.equals(getRowKey(row))) {
                return row;
            }
        }
        return null;
    }
}

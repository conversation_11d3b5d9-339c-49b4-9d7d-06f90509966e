/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.Tesouraria.TesourariaSaidaController;
import Dados.Persistencia;
import SasBeans.TesSaidas;
import br.com.sasw.utils.Messages;
import java.sql.Date;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class TesSaidaLazyList extends LazyDataModel<TesSaidas> {

    private static final long serialVersionUID = 1L;
    private final TesourariaSaidaController tesourariaController;
    private List<TesSaidas> lista;
    private Map filtersPage = new HashMap();

    public TesSaidaLazyList(Persistencia persistencia, Map filters) {
        tesourariaController = new TesourariaSaidaController(persistencia);
        filtersPage = filters;
    }

    @Override
    public List<TesSaidas> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        lista = new ArrayList<>();
        try {
            lista = tesourariaController.allTesSaidasPaginada(first, pageSize, filtersPage);
            setRowCount(tesourariaController.contagemAllTesSaidas(filtersPage));
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return lista;
    }

    @Override
    public Object getRowKey(TesSaidas saida) {
        return saida.getGuia()+ "_" + saida.getSerie();
    }

    @Override
    public TesSaidas getRowData(String chavePrimaria) {
        for (TesSaidas saida : this.lista) {
            if (chavePrimaria.equals(saida.getGuia() + "_" + saida.getSerie())) {
                return saida;
            }
        }
        return null;
    }
}

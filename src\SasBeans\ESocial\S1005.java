/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1005 {

    private int sucesso;
    private String evtTabEstab_Id;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideEstab_tpInsc;
    private String ideEstab_nrInsc;
    private String ideEstab_iniValid;
    private String dadosEstab_cnaePrep;
    private String aliqGilrat_aliqRat;
    private String aliqGilrat_fap;
    private String aliqRatAjust_aliqRatAjust;
    private String regPt_regPt;
    private String contApr_contApr;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtTabEstab_Id() {
        return null == evtTabEstab_Id ? "" : evtTabEstab_Id;
    }

    public void setEvtTabEstab_Id(String evtTabEstab_Id) {
        this.evtTabEstab_Id = evtTabEstab_Id;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeEstab_tpInsc() {
        return null == ideEstab_tpInsc ? "" : ideEstab_tpInsc;
    }

    public void setIdeEstab_tpInsc(String ideEstab_tpInsc) {
        this.ideEstab_tpInsc = ideEstab_tpInsc;
    }

    public String getIdeEstab_nrInsc() {
        return null == ideEstab_nrInsc ? "" : ideEstab_nrInsc;
    }

    public void setIdeEstab_nrInsc(String ideEstab_nrInsc) {
        this.ideEstab_nrInsc = ideEstab_nrInsc;
    }

    public String getIdeEstab_iniValid() {
        return null == ideEstab_iniValid ? "" : ideEstab_iniValid;
    }

    public void setIdeEstab_iniValid(String ideEstab_iniValid) {
        this.ideEstab_iniValid = ideEstab_iniValid;
    }

    public String getDadosEstab_cnaePrep() {
        return null == dadosEstab_cnaePrep ? "" : dadosEstab_cnaePrep;
    }

    public void setDadosEstab_cnaePrep(String dadosEstab_cnaePrep) {
        this.dadosEstab_cnaePrep = dadosEstab_cnaePrep;
    }

    public String getAliqGilrat_aliqRat() {
        return null == aliqGilrat_aliqRat ? "" : aliqGilrat_aliqRat;
    }

    public void setAliqGilrat_aliqRat(String aliqGilrat_aliqRat) {
        this.aliqGilrat_aliqRat = aliqGilrat_aliqRat;
    }

    public String getAliqGilrat_fap() {
        return null == aliqGilrat_fap ? "" : aliqGilrat_fap;
    }

    public void setAliqGilrat_fap(String aliqGilrat_fap) {
        this.aliqGilrat_fap = aliqGilrat_fap;
    }

    public String getAliqRatAjust_aliqRatAjust() {
        return null == aliqRatAjust_aliqRatAjust ? "" : aliqRatAjust_aliqRatAjust;
    }

    public void setAliqRatAjust_aliqRatAjust(String aliqRatAjust_aliqRatAjust) {
        this.aliqRatAjust_aliqRatAjust = aliqRatAjust_aliqRatAjust;
    }

    public String getRegPt_regPt() {
        return null == regPt_regPt ? "" : regPt_regPt;
    }

    public void setRegPt_regPt(String regPt_regPt) {
        this.regPt_regPt = regPt_regPt;
    }

    public String getContApr_contApr() {
        return null == contApr_contApr ? "" : contApr_contApr;
    }

    public void setContApr_contApr(String contApr_contApr) {
        this.contApr_contApr = contApr_contApr;
    }
}

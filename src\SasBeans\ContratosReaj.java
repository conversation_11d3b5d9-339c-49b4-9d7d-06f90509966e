/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class ContratosReaj {

    private String Contrato,
            FormulaDesc,
            CodFil,
            DtBase,
            Ordem,
            IndiceDF,
            Formula,
            IndMOSolic,
            IndMOAprov,
            IndCombSolic,
            IndCombAprov,
            IndDFSolic,
            IndDFAprov,
            IndiceSolic,
            IndiceFinal,
            Embarques,
            TE,
            Envelopes,
            Milheiros,
            KM,
            FixoMensal,
            Malotes,
            CargaDescarga,
            AST,
            KitTroco,
            UnidadesProdServ,
            AdValorem,
            CodCarta,
            DtCarta,
            Obs,
            Operador,
            Dt_Alter,
            Hr_Alter,
            OperReaj,
            DtReaj,
            HrReaj;

    public ContratosReaj() {
        this.Contrato = "";
        this.FormulaDesc = "";
        this.CodFil = "0";
        this.DtBase = "";
        this.Ordem = "";
        this.IndiceDF = "";
        this.Formula = "";
        this.IndMOSolic = "";
        this.IndMOAprov = "";
        this.IndCombSolic = "";
        this.IndCombAprov = "";
        this.IndDFSolic = "";
        this.IndDFAprov = "";
        this.IndiceSolic = "";
        this.IndiceFinal = "";
        this.Embarques = "";
        this.TE = "";
        this.Envelopes = "";
        this.Milheiros = "";
        this.KM = "";
        this.FixoMensal = "";
        this.Malotes = "";
        this.CargaDescarga = "";
        this.AST = "";
        this.KitTroco = "";
        this.UnidadesProdServ = "";
        this.AdValorem = "";
        this.CodCarta = "";
        this.DtCarta = "";
        this.Obs = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
        this.OperReaj = "";
        this.DtReaj = "";
        this.HrReaj = "";
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDtBase() {
        return DtBase;
    }

    public void setDtBase(String DtBase) {
        this.DtBase = DtBase;
    }

    public String getOrdem() {
        return Ordem;
    }

    public void setOrdem(String Ordem) {
        this.Ordem = Ordem;
    }

    public String getIndiceDF() {
        return IndiceDF;
    }

    public void setIndiceDF(String IndiceDF) {
        this.IndiceDF = IndiceDF;
    }

    public String getFormula() {
        return Formula;
    }

    public void setFormula(String Formula) {
        this.Formula = Formula;
    }

    public String getIndMOSolic() {
        return IndMOSolic;
    }

    public void setIndMOSolic(String IndMOSolic) {
        this.IndMOSolic = IndMOSolic;
    }

    public String getIndMOAprov() {
        return IndMOAprov;
    }

    public void setIndMOAprov(String IndMOAprov) {
        this.IndMOAprov = IndMOAprov;
    }

    public String getIndCombSolic() {
        return IndCombSolic;
    }

    public void setIndCombSolic(String IndCombSolic) {
        this.IndCombSolic = IndCombSolic;
    }

    public String getIndCombAprov() {
        return IndCombAprov;
    }

    public void setIndCombAprov(String IndCombAprov) {
        this.IndCombAprov = IndCombAprov;
    }

    public String getIndDFSolic() {
        return IndDFSolic;
    }

    public void setIndDFSolic(String IndDFSolic) {
        this.IndDFSolic = IndDFSolic;
    }

    public String getIndDFAprov() {
        return IndDFAprov;
    }

    public void setIndDFAprov(String IndDFAprov) {
        this.IndDFAprov = IndDFAprov;
    }

    public String getIndiceSolic() {
        return IndiceSolic;
    }

    public void setIndiceSolic(String IndiceSolic) {
        this.IndiceSolic = IndiceSolic;
    }

    public String getIndiceFinal() {
        return IndiceFinal;
    }

    public void setIndiceFinal(String IndiceFinal) {
        this.IndiceFinal = IndiceFinal;
    }

    public String getEmbarques() {
        return Embarques;
    }

    public void setEmbarques(String Embarques) {
        this.Embarques = Embarques;
    }

    public String getTE() {
        return TE;
    }

    public void setTE(String TE) {
        this.TE = TE;
    }

    public String getEnvelopes() {
        return Envelopes;
    }

    public void setEnvelopes(String Envelopes) {
        this.Envelopes = Envelopes;
    }

    public String getMilheiros() {
        return Milheiros;
    }

    public void setMilheiros(String Milheiros) {
        this.Milheiros = Milheiros;
    }

    public String getKM() {
        return KM;
    }

    public void setKM(String KM) {
        this.KM = KM;
    }

    public String getFixoMensal() {
        return FixoMensal;
    }

    public void setFixoMensal(String FixoMensal) {
        this.FixoMensal = FixoMensal;
    }

    public String getMalotes() {
        return Malotes;
    }

    public void setMalotes(String Malotes) {
        this.Malotes = Malotes;
    }

    public String getCargaDescarga() {
        return CargaDescarga;
    }

    public void setCargaDescarga(String CargaDescarga) {
        this.CargaDescarga = CargaDescarga;
    }

    public String getAST() {
        return AST;
    }

    public void setAST(String AST) {
        this.AST = AST;
    }

    public String getKitTroco() {
        return KitTroco;
    }

    public void setKitTroco(String KitTroco) {
        this.KitTroco = KitTroco;
    }

    public String getUnidadesProdServ() {
        return UnidadesProdServ;
    }

    public void setUnidadesProdServ(String UnidadesProdServ) {
        this.UnidadesProdServ = UnidadesProdServ;
    }

    public String getAdValorem() {
        return AdValorem;
    }

    public void setAdValorem(String AdValorem) {
        this.AdValorem = AdValorem;
    }

    public String getCodCarta() {
        return CodCarta;
    }

    public void setCodCarta(String CodCarta) {
        this.CodCarta = CodCarta;
    }

    public String getDtCarta() {
        return DtCarta;
    }

    public void setDtCarta(String DtCarta) {
        this.DtCarta = DtCarta;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOperReaj() {
        return OperReaj;
    }

    public void setOperReaj(String OperReaj) {
        this.OperReaj = OperReaj;
    }

    public String getDtReaj() {
        return DtReaj;
    }

    public void setDtReaj(String DtReaj) {
        this.DtReaj = DtReaj;
    }

    public String getHrReaj() {
        return HrReaj;
    }

    public void setHrReaj(String HrReaj) {
        this.HrReaj = HrReaj;
    }

    public String getFormulaDesc() {
        return FormulaDesc;
    }

    public void setFormulaDesc(String FormulaDesc) {
        this.FormulaDesc = FormulaDesc;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TbVal;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TbValDao {

    /**
     * Verifica a existencia de uma descricao na tabela tbval
     *
     * @param tabela
     * @param descricao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeDescricao(int tabela, String descricao, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = false;
            String sql = " select * from tbval where tabela = ? and descricao like ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tabela);
            consulta.setString(descricao);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = true;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TbValDao.existeDescricao - " + e.getMessage() + "\r\n"
                    + " select * from tbval where tabela = " + tabela + " and descricao like " + descricao);
        }
    }

    // CREATE
    public boolean gravaTbVal(TbVal tbval, Persistencia persistencia) {
        boolean retorno;
        String sql;
        sql = "INSERT INTO TbVal\n"
                + "(Tabela,Codigo,Descricao,Operador,Dt_Alter,Hr_Alter)\n"
                + "VALUES (?,?,?,?,?,?);\n";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbval.getTabela());
            consulta.setInt(tbval.getCodigo());
            consulta.setString(tbval.getDescricao());
            consulta.setString(FuncoesString.RecortaString(tbval.getOperador(), 0, 10));
            consulta.setString(tbval.getDt_Alter());
            consulta.setString(tbval.getHr_Alter());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // READ
    public List<TbVal> buscaTbVal(Persistencia persistencia) throws Exception {
        List<TbVal> listTbVal;
        try {
            TbVal tbval;
            Consulta consult = new Consulta("select Tabela,Codigo,Descricao,Operador,Dt_Alter,Hr_Alter"
                    + "from tbval", persistencia);
            consult.select();
            listTbVal = new ArrayList();
            while (consult.Proximo()) {
                tbval = new TbVal();
                tbval.setTabela(consult.getInt("Tabela"));
                tbval.setCodigo(consult.getInt("Codigo"));
                tbval.setDescricao(consult.getString("Descricao"));
                tbval.setOperador(consult.getString("Operador"));
                tbval.setDt_Alter(consult.getString("Dt_Alter"));
                tbval.setHr_Alter(consult.getString("Hr_Alter"));
                listTbVal.add(tbval);
            }
            consult.Close();
        } catch (Exception e) {
            listTbVal = null;
            throw new Exception("Falha ao buscar..." + e.getMessage());
        }
        return listTbVal;
    }

    /**
     * Busca o próximo código disponível em tbval
     *
     * @param tabela
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Integer getCodTBVal(int tabela, Persistencia persistencia) throws Exception {
        int seq = 1;
        try {
            String sql = " select max(Codigo) + 1 as codigo from tbval"
                    + " where Tabela = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tabela);
            consulta.select();
            while (consulta.Proximo()) {
                try {
                    seq = consulta.getInt("codigo");
                } catch (Exception e) {
                    seq = 1;
                }
            }
            consulta.Close();
        } catch (Exception e) {
            throw new SQLException("TbValDao.getCodTBVal() - " + e.getMessage() + "\r\n"
                    + "");
        }
        return seq;
    }

    public List<TbVal> getTbVal312(Persistencia persistencia) throws Exception {
        List<TbVal> listTbVal;
        try {
            TbVal tbval;
            Consulta consult = new Consulta("select Tabela, Codigo, Descricao, Operador, Dt_Alter, Hr_Alter "
                    + " from tbval "
                    + " where Tabela='312' ", persistencia);
            consult.select();
            listTbVal = new ArrayList();
            while (consult.Proximo()) {
                tbval = new TbVal();
                tbval.setTabela(consult.getInt("Tabela"));
                tbval.setCodigo(consult.getInt("Codigo"));
                tbval.setDescricao(consult.getString("Descricao"));
                tbval.setOperador(consult.getString("Operador"));
                tbval.setDt_Alter(consult.getString("Dt_Alter"));
                tbval.setHr_Alter(consult.getString("Hr_Alter"));
                listTbVal.add(tbval);
            }
            consult.Close();
        } catch (Exception e) {
            listTbVal = null;
            throw new Exception("Falha ao buscar..." + e.getMessage());
        }
        return listTbVal;
    }

    public List<TbVal> getTbVal052(Persistencia persistencia) throws Exception {
        List<TbVal> listTbVal;
        try {
            TbVal tbval;
            Consulta consult = new Consulta("select codigo, descricao from tbval where tabela = 52", persistencia);
            consult.select();
            listTbVal = new ArrayList();
            while (consult.Proximo()) {
                tbval = new TbVal();
                tbval.setCodigo(consult.getInt("Codigo"));
                tbval.setDescricao(consult.getString("Descricao"));
                listTbVal.add(tbval);
            }
            consult.Close();
        } catch (Exception e) {
            listTbVal = null;
            throw new Exception("Falha ao buscar Escolaridades..." + e.getMessage());
        }
        return listTbVal;
    }

    public List<TbVal> getTbVal311(Persistencia persistencia) throws Exception {
        List<TbVal> listTbVal;
        try {
            TbVal tbval;
            Consulta consult = new Consulta("select Tabela, Codigo, Descricao, Operador, Dt_Alter, Hr_Alter "
                    + " from tbval "
                    + " where Tabela='311' ", persistencia);
            consult.select();
            listTbVal = new ArrayList();
            while (consult.Proximo()) {
                tbval = new TbVal();
                tbval.setTabela(consult.getInt("Tabela"));
                tbval.setCodigo(consult.getInt("Codigo"));
                tbval.setDescricao(consult.getString("Descricao"));
                tbval.setOperador(consult.getString("Operador"));
                tbval.setDt_Alter(consult.getString("Dt_Alter"));
                tbval.setHr_Alter(consult.getString("Hr_Alter"));
                listTbVal.add(tbval);
            }
            consult.Close();
        } catch (Exception e) {
            listTbVal = null;
            throw new Exception("Falha ao buscar motivos..." + e.getMessage());
        }
        return listTbVal;
    }

    /* create for tabela 201 */
    public boolean gravaTbVal201(TbVal tbval, Persistencia persistencia) {
        boolean retorno;
        String sql;
        sql = "insert into TbVal (Tabela,Codigo,Descricao,Operador,Dt_Alter,Hr_Alter) "
                + "Values (?,?,?,?,?,?) "
                + "where Tabela='201'";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbval.getTabela());
            consulta.setInt(tbval.getCodigo());
            consulta.setString(tbval.getDescricao());
            consulta.setString(tbval.getOperador());
            consulta.setString(tbval.getDt_Alter());
            consulta.setString(tbval.getHr_Alter());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    /* read for tabela 201 */
    public List<TbVal> buscaTbVal201(Persistencia persistencia) throws Exception {
        List<TbVal> listTbVal;
        try {
            TbVal tbval;
            Consulta consult = new Consulta("select Tabela, Codigo, Descricao, Operador, Dt_Alter, Hr_Alter "
                    + " from tbval "
                    + " where Tabela='201' ", persistencia);
            consult.select();
            listTbVal = new ArrayList();
            while (consult.Proximo()) {
                tbval = new TbVal();
                tbval.setTabela(consult.getInt("Tabela"));
                tbval.setCodigo(consult.getInt("Codigo"));
                tbval.setDescricao(consult.getString("Descricao"));
                tbval.setOperador(consult.getString("Operador"));
                tbval.setDt_Alter(consult.getString("Dt_Alter"));
                tbval.setHr_Alter(consult.getString("Hr_Alter"));
                listTbVal.add(tbval);
            }
            consult.Close();
        } catch (Exception e) {
            listTbVal = null;
            throw new Exception("Falha ao buscar motivos..." + e.getMessage());
        }
        return listTbVal;
    }

    /* update for tabela 201 */
    public void atualizaTbVal201(TbVal tbval, Persistencia persistencia) throws Exception {
        String sql = "update TbVal set Tabela=?, Codigo=?, Descricao=?, Operador=?, Dt_Alter=?, Hr_Alter=? "
                + "where Tabela='201' and Codigo=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbval.getTabela());
            consulta.setInt(tbval.getCodigo());
            consulta.setString(tbval.getDescricao());
            consulta.setString(tbval.getOperador());
            consulta.setString(tbval.getDt_Alter());
            consulta.setString(tbval.getHr_Alter());
            consulta.setInt(tbval.getCodigo());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao atualizar... " + e.getMessage());
        }
    }

    /* delete for tabela 201 */
    public void excluirTbVal201(TbVal tbval, Persistencia persistencia) throws Exception {
        String sql;
        sql = "delete from tbval where Tabela='201' and Codigo=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbval.getCodigo());
            consulta.delete();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Erro ao excluir..." + e.getMessage());
        }
    }

    // UPDATE
    public void atualizaTbVal(TbVal tbval, Persistencia persistencia) throws Exception {
        String sql = "update TbVal set Tabela=?, Codigo=?, Descricao=?, Operador=?, Dt_Alter=?, Hr_Alter=? "
                + "where Tabela=? and Codigo=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbval.getTabela());
            consulta.setInt(tbval.getCodigo());
            consulta.setString(tbval.getDescricao());
            consulta.setString(tbval.getOperador());
            consulta.setString(tbval.getDt_Alter());
            consulta.setString(tbval.getHr_Alter());
            consulta.setInt(tbval.getTabela());
            consulta.setInt(tbval.getCodigo());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao atualizar... " + e.getMessage());
        }
    }

    // DELETE
    public void excluirTbVal(TbVal tbval, Persistencia persistencia) throws Exception {
        String sql;
        sql = "delete from tbval where Tabela=? and Codigo=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbval.getTabela());
            consulta.setInt(tbval.getCodigo());
            consulta.delete();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Erro ao excluir..." + e.getMessage());
        }
    }

    /**
     * Busca próximo código da tabela passada
     *
     * @param tabela - código da tabela
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer getCodTBVal(String tabela, Persistencia persistencia) throws Exception {
        int seq = 1;
        try {
            Consulta stm = new Consulta("select max(Codigo)+1 as codigo from tbval"
                    + " where Tabela = ? ", persistencia);
            stm.setString(tabela);
            stm.select();

            while (stm.Proximo()) {
                try {
                    seq = stm.getInt("codigo");
                } catch (Exception e) {
                    seq = 1;
                }
            }
            stm.Close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao carregar código - " + e.getMessage());
        }
        return seq;
    }

    public List<TbVal> buscaTbVal96(Persistencia persistencia) throws Exception {
        List<TbVal> listTbVal;
        try {
            TbVal tbval;
            Consulta consult = new Consulta("select Codigo, Descricao"
                    + " from tbval "
                    + " where Tabela='96' ", persistencia);
            consult.select();
            listTbVal = new ArrayList();
            while (consult.Proximo()) {
                tbval = new TbVal();
                tbval.setCodigo(consult.getInt("Codigo"));
                tbval.setDescricao(consult.getString("Descricao"));
                listTbVal.add(tbval);
            }
            consult.Close();
        } catch (Exception e) {
            listTbVal = null;
            throw new Exception("Falha ao buscar tbval 96..." + e.getMessage());
        }
        return listTbVal;
    }

    /**
     * Lista as entradas de uma tabela
     *
     * @param tabela
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TbVal> retornaLista(int tabela, Persistencia persistencia) throws Exception {
        List<TbVal> retorno = new ArrayList<>();
        try {
            Consulta stm = new Consulta("select * from tbval"
                    + " where Tabela = ? ", persistencia);
            stm.setInt(tabela);
            stm.select();
            TbVal entrada;
            while (stm.Proximo()) {
                entrada = new TbVal();
                entrada.setCodigo(stm.getInt("codigo"));
                entrada.setDescricao(stm.getString("descricao"));
                entrada.setDt_Alter(stm.getString("dt_alter"));
                entrada.setHr_Alter(stm.getString("hr_alter"));
                entrada.setOperador(stm.getString("operador"));
                entrada.setTabela(stm.getInt("tabela"));
                retorno.add(entrada);
            }
            stm.Close();
        } catch (Exception e) {
            throw new SQLException("TbValDao.retornaLista() - " + e.getMessage() + "\r\n"
                    + "select * from tbval where Tabela = " + tabela);
        }
        return retorno;
    }

    /**
     * Lista as entradas de uma tabela
     *
     * @param tabela
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> retornaListaString(int tabela, Persistencia persistencia) throws Exception {
        List<String> retorno = new ArrayList<>();
        try {
            Consulta stm = new Consulta("select * from tbval"
                    + " where Tabela = ? ", persistencia);
            stm.setInt(tabela);
            stm.select();
            String entrada;
            while (stm.Proximo()) {
//                entrada = new TbVal();
//                entrada.setCodigo(stm.getInt("codigo"));
                entrada = stm.getString("descricao");
//                entrada.setDt_Alter(stm.getString("dt_alter"));
//                entrada.setHr_Alter(stm.getString("hr_alter"));
//                entrada.setOperador(stm.getString("operador"));
//                entrada.setTabela(stm.getInt("tabela"));
                retorno.add(entrada);
            }
            stm.Close();
        } catch (Exception e) {
            throw new SQLException("TbValDao.retornaLista() - " + e.getMessage() + "\r\n"
                    + "select * from tbval where Tabela = " + tabela);
        }
        return retorno;
    }

    /**
     * Retorna as descrições de uma tabela
     *
     * @param tabela
     * @param query
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> buscaDescLista(int tabela, String query, Persistencia persistencia) throws Exception {
        List<String> retorno = new ArrayList<>();
        try {
            Consulta stm = new Consulta("select * from tbval"
                    + " where Tabela = ? and descricao like ?", persistencia);
            stm.setInt(tabela);
            stm.setString("%" + query + "%");
            stm.select();
            String entrada;
            while (stm.Proximo()) {
                entrada = (stm.getString("descricao"));
                retorno.add(entrada);
            }
            stm.Close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao carregar tabela - " + e.getMessage());
        }
        return retorno;
    }
}

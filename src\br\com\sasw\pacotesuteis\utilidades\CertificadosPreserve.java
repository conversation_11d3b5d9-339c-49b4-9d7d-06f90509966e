/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

/**
 *
 * <AUTHOR>
 */
public enum CertificadosPreserve {
    PRESERVE_PE("SATPRESERVE", "1", "PRESERVE", "1234");    

    private String parametro;
    private String codfil;
    private String nome;
    private String senha;
    
    CertificadosPreserve(final String parametro, final String codfil, final String nome, final String senha){
        this.parametro = parametro;
        this.codfil = codfil;
        this.nome = nome;
        this.senha = senha;
    }

    public String getParametro() {
        return parametro;
    }

    public String getCodfil() {
        return codfil;
    }

    public String getNome() {
        return nome;
    }

    public String getSenha() {
        return senha;
    }
}



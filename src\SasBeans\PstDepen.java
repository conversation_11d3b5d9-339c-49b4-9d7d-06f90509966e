/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class PstDepen {

    private String Codigo;
    private String Secao;
    private String CodFil;
    private String Descricao;
    private String HrIni;
    private String HrFim;
    private BigDecimal NroVig;
    private String EscAuto;
    private String QrCode;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private BigDecimal latitude;
    private BigDecimal longitude;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getHrIni() {
        return HrIni;
    }

    public void setHrIni(String HrIni) {
        this.HrIni = HrIni;
    }

    public String getHrFim() {
        return HrFim;
    }

    public void setHrFim(String HrFim) {
        this.HrFim = HrFim;
    }

    public BigDecimal getNroVig() {
        return NroVig;
    }

    public void setNroVig(BigDecimal NroVig) {
        this.NroVig = NroVig;
    }

    public String getEscAuto() {
        return EscAuto;
    }

    public void setEscAuto(String EscAuto) {
        this.EscAuto = EscAuto;
    }

    public String getQrCode() {
        return QrCode;
    }

    public void setQrCode(String QrCode) {
        this.QrCode = QrCode;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
}

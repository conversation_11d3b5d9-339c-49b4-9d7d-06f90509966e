/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.R9000;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class R9000Dao {

    public List<R9000> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select  Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc, XMLeSocial.Evento InfoExclusao_tpEvento, Convert(BigInt, XMLeSocial.Identificador) Identificador, "
                    + " Substring(XML_Retorno,CHARINDEX('<nrRecibo>',XML_Retorno)+10,(CHARINDEX('</nrRecibo>',XML_Retorno)-(CHARINDEX('<nrRecibo>',XML_Retorno)+10))) InfoExclusao_nrRecEvt,"
                    + " Funcion.CPF ideTrabalhador_cpfTrab, Funcion.PIS ideTrabalhador_nisTrab, "
                    + " (select max(sucesso) from  ( "
                    + "   (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "       From XmleSocial z  "
                    + "       where z.evento = 'S-3000' "
                    + "           and z.Compet = ? "
                    + "           and z.Ambiente = ? "
                    + "           and z.CodFil = ? "
                    + " 	        and z.Identificador = XMLeSocial.Identificador"
                    + "           and (z.Xml_Retorno like '%aguardando%' "
                    + "                   or z.Xml_Retorno = ''"
                    + "                   or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union "
                    + "   (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "       From XmleSocial z  "
                    + "       where z.evento = 'S-3000' "
                    + "           and z.Compet = ? "
                    + "           and z.Ambiente = ? "
                    + "           and z.CodFil = ? "
                    + " 	     and z.Identificador = XMLeSocial.Identificador"
                    + " 	     and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "             or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "   (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "       From XmleSocial z  "
                    + "       where z.evento = 'S-3000' "
                    + "           and z.Compet = ? "
                    + "           and z.Ambiente = ? "
                    + "           and z.CodFil = ? "
                    + " 	     and z.Identificador = XMLeSocial.Identificador"
                    + " 	     and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso                "
                    + "  From Filiais"
                    + "  Left join XMLeSocial on XMLeSocial.CodFil = Filiais.CodFil"
                    + "  Left join Funcion  on Convert(BigInt,Funcion.CPF) = XMLeSocial.Identificador                   "
                    + " Where Filiais.CodFil = ? "
                    + "   and XMLeSocial.Compet = ? "
                    + "   and XMLeSocial.Ambiente = ? "
                    + "   and XMLeSocial.Protocolo_Envio in (Select Protocolo_Envio from XMLeSocial where Compet = ? and Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')"
                    + "   and (XMLeSocial.Evento = 'S-2190' "
                    + "     or XMLeSocial.Evento between 'S-1200' and 'S-1280'  "
                    + "     or XMLeSocial.Evento = 'S-1300') "
                    + "   and XmlEsocial.Xml_Retorno like '%<nrRecibo>%'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.select();
            List<R9000> retorno = new ArrayList<>();
            R9000 r2099;
            while (consulta.Proximo()) {
                r2099 = new R9000();

                //r2099.setEvtFechaEvPer_Id(consulta.getString("evtFechaEvPer_Id"));
                //r2099.setIdeEvento_tpAmb(consulta.getString("ideEvento_tpAmb"));                
                retorno.add(r2099);
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("R9000Dao.get - " + e.getMessage() + "\r\n");
        }
    }
}

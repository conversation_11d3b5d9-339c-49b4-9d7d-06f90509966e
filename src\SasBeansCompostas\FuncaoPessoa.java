/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Funcion;
import SasBeans.Pessoa;

/**
 *
 * <AUTHOR>
 */
public class FuncaoPessoa {

    private Pessoa pessoa;
    private Funcion funcion;

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.cxforte;

import Arquivo.ArquivoLog;
import Controller.CxForte.CustodiaSatMobWeb;
import Dados.Persistencia;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.Filiais;
import SasBeans.OS_Vig;
import br.com.sasw.lazydatamodels.cxforte.CustodiaLazyList;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "custodia")
@javax.enterprise.context.SessionScoped
public class CustodiaMB implements Serializable {

    private Date dataSelecionada1, dataSelecionada2;
    private Filiais filiais;
    private final Calendar calendar;
    private Map filters;
    private Persistencia persistencia;
    private ArquivoLog logerro;
    private final String codfil, banco, operador, caminho, filialDesc;
    private final BigDecimal codpessoa;
    private final CustodiaSatMobWeb custodiaSatMobWeb;
    private String log, d1, d2, codcli;
    private LazyDataModel<CxFGuias> guias = null;
    private CxFGuias cxFGuiasSelecionado;
    private List<CxFGuiasVol> cxfGuiasVolLista;
    private OS_Vig osSelecionada;
    private CxForte cxForte;
    private List<CxForte> cxForteLista;
    private boolean historicoGeral, remessas, custodiaCorporativo;

    public CustodiaMB() throws Exception {
        FacesContext facesContext = FacesContext.getCurrentInstance();
        banco = (String) facesContext.getExternalContext().getSessionMap().get("banco");
        codfil = (String) facesContext.getExternalContext().getSessionMap().get("filial");
        operador = (String) facesContext.getExternalContext().getSessionMap().get("nome");
        codpessoa = (BigDecimal) facesContext.getExternalContext().getSessionMap().get("codpessoa");
        filialDesc = (String) facesContext.getExternalContext().getSessionMap().get("nomeFilial");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        calendar = Calendar.getInstance();
        calendar.setTime(Date.from(Instant.now()));
        dataSelecionada2 = calendar.getTime();
        d2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        calendar.add(Calendar.DATE, -45 + 1); // de 45 dias atrás até hoje
        dataSelecionada1 = calendar.getTime();
        d1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        custodiaSatMobWeb = new CustodiaSatMobWeb();
        logerro = new ArquivoLog();
        historicoGeral = false;
        remessas = false;
        custodiaCorporativo = false;
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            carregarCaixasForte();
            this.cxFGuiasSelecionado = null;
            custodiaCorporativo = false;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }
    

    public void iniciarPagina() {
        try {
            this.codcli = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("cliente");
            this.filiais = this.custodiaSatMobWeb.buscaInfoFilial(this.codfil, persistencia);
            this.cxForteLista = this.custodiaSatMobWeb.listarCaixasForte(this.codfil, this.persistencia);

            CxForte c = new CxForte();
            c.setCodCli(this.codcli);
            c.setCodFil(this.codfil);
            int indice = this.cxForteLista.indexOf(c);
            if (indice >= 0) {
                this.cxForte = this.cxForteLista.get(indice);
            } else {
                this.cxForte = c;
            }

            this.filters = new HashMap();

            this.filters.put("CodCli", this.codcli);
            this.filters.put("CodFil", this.codfil);
            this.filters.put("DataInicio", this.d1);
            this.filters.put("DataFim", this.d2);
            this.filters.put("geral", historicoGeral);
            this.filters.put("remessa", this.remessas);

            if (this.cxFGuiasSelecionado != null) {
                String os = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("os");
                if (os != null) {
                    this.cxFGuiasSelecionado.setOS(os);
                }
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().replace("os", null);
                buttonAction(null);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<CxFGuias> getGuias() {
        try {
            if (this.guias == null) {
                this.filters.put("CodCli", this.codcli);
                this.filters.put("CodFil", this.codfil);
                this.filters.put("DataInicio", this.d1);
                this.filters.put("DataFim", this.d2);
                this.filters.put("geral", historicoGeral);
                this.filters.put("remessa", this.remessas);
                this.guias = new CustodiaLazyList(this.persistencia, this.filters);
            } else {
                ((CustodiaLazyList) this.guias).setFilters(this.filters);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.guias;
    }

    public void exibirGuia(SelectEvent event) {
        this.cxFGuiasSelecionado = (CxFGuias) event.getObject();
        buttonAction(null);
    }

    public void buttonAction(ActionEvent actionEvent) {
        if (null == this.cxFGuiasSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneGuia"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.cxfGuiasVolLista = this.custodiaSatMobWeb.listarVolumesGuia(this.cxFGuiasSelecionado.getGuia().toString(),
                        this.cxFGuiasSelecionado.getSerie(),
                        this.cxFGuiasSelecionado.getCodFil().toString(), this.persistencia);
                this.osSelecionada = this.custodiaSatMobWeb.buscarOS_Vig(this.cxFGuiasSelecionado.getCodFil().toString(),
                        this.cxFGuiasSelecionado.getOS(), this.persistencia);

                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                logerro.Grava(log, caminho);
            }
        }
    }

    public void dataAnterior() {
        try {
            this.calendar.setTime(this.dataSelecionada1);
            this.calendar.set(Calendar.DAY_OF_MONTH, 1);
            this.calendar.add(Calendar.MONTH, -1);
            this.dataSelecionada1 = this.calendar.getTime();
            this.d1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.calendar.setTime(this.dataSelecionada2);
            this.calendar.set(Calendar.DAY_OF_MONTH, 1);
            this.calendar.add(Calendar.MONTH, -1);
            this.dataSelecionada2 = this.calendar.getTime();
            this.d2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.filters.replace("DataInicio", this.d1);
            this.filters.replace("DataFim", this.d2);
            getGuias();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void dataPosterior() {
        try {
            this.calendar.setTime(this.dataSelecionada1);
            this.calendar.set(Calendar.DAY_OF_MONTH, 1);
            this.calendar.add(Calendar.MONTH, 1);
            this.dataSelecionada1 = this.calendar.getTime();
            this.d1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.calendar.setTime(this.dataSelecionada2);
            this.calendar.set(Calendar.DAY_OF_MONTH, 1);
            this.calendar.add(Calendar.MONTH, 1);
            this.dataSelecionada2 = this.calendar.getTime();
            this.d2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.filters.replace("DataInicio", this.d1);
            this.filters.replace("DataFim", this.d2);
            getGuias();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void escreverData1() {
        try {
            this.dataSelecionada1 = new SimpleDateFormat("yyyyMMdd").parse(this.d1);

            if (this.dataSelecionada1.after(this.dataSelecionada2)) {
                this.d2 = this.d1;
                this.dataSelecionada2 = this.dataSelecionada1;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void escreverData2() {
        try {
            this.dataSelecionada2 = new SimpleDateFormat("yyyyMMdd").parse(this.d2);

            if (this.dataSelecionada2.before(this.dataSelecionada1)) {
                this.d1 = this.d2;
                this.dataSelecionada1 = this.dataSelecionada2;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void selecionarData1(SelectEvent data) {
        this.dataSelecionada1 = (Date) data.getObject();
        this.d1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        try {
            if (this.dataSelecionada1.after(this.dataSelecionada2)) {
                this.d2 = this.d1;
                this.dataSelecionada2 = this.dataSelecionada1;
            }
        } catch (Exception e) {

        }
    }

    public void selecionarData2(SelectEvent data) {
        this.dataSelecionada2 = (Date) data.getObject();
        this.d2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        try {
            if (this.dataSelecionada2.before(this.dataSelecionada1)) {
                this.d1 = this.d2;
                this.dataSelecionada1 = this.dataSelecionada2;
            }
        } catch (Exception e) {

        }
    }

    public void selecionarData() {
        try {
            this.filters.replace("DataInicio", this.d1);
            this.filters.replace("DataFim", this.d2);
            getGuias();
            PrimeFaces.current().executeScript("PF('oCalendarios').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void dblSelect(SelectEvent event) throws IOException {
        this.cxForte = (CxForte) event.getObject();
        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.cxForte.getCodCli());
        FacesContext.getCurrentInstance().getExternalContext().redirect("cxforte/custodia.xhtml");
    }

    public void dblSelectPre(SelectEvent event) throws IOException {
        this.cxForte = (CxForte) event.getObject();
        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.cxForte.getCodCli());
        FacesContext.getCurrentInstance().getExternalContext().redirect("custodia.xhtml");
    }

    public void carregarCaixasForte() throws Exception {
        this.cxForteLista = this.custodiaSatMobWeb.listarCaixasForte(this.codfil, this.persistencia);
    }

    public void pesquisarOS() {
        try {
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("os", this.cxFGuiasSelecionado.getOS());
            FacesContext.getCurrentInstance().getExternalContext().redirect("../faturamento/os_vig.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void mostrarCustodiaCorporativo() throws Exception {
        if (custodiaCorporativo) {
            this.cxForteLista = this.custodiaSatMobWeb.listarCaixasForte(null, this.persistencia);
        } else {
            this.cxForteLista = this.custodiaSatMobWeb.listarCaixasForte(this.codfil, this.persistencia);
        }
    }

    public void mostrarHistoricoGeral() {
        this.filters.replace("geral", historicoGeral);
        getGuias();
    }

    public void mostrarRemessas() {
        this.filters.replace("remessa", this.remessas);
        getGuias();
    }

    public CxFGuias getCxFGuiasSelecionado() {
        return cxFGuiasSelecionado;
    }

    public void setCxFGuiasSelecionado(CxFGuias cxFGuiasSelecionado) {
        this.cxFGuiasSelecionado = cxFGuiasSelecionado;
    }

    public String getD1() {
        return d1;
    }

    public void setD1(String d1) {
        this.d1 = d1;
    }

    public String getD2() {
        return d2;
    }

    public void setD2(String d2) {
        this.d2 = d2;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataSelecionada2() {
        return dataSelecionada2;
    }

    public void setDataSelecionada2(Date dataSelecionada2) {
        this.dataSelecionada2 = dataSelecionada2;
    }

    public CxForte getCxForte() {
        return cxForte;
    }

    public void setCxForte(CxForte cxForte) {
        this.cxForte = cxForte;
    }

    public List<CxForte> getCxForteLista() {
        return cxForteLista;
    }

    public void setCxForteLista(List<CxForte> cxForteLista) {
        this.cxForteLista = cxForteLista;
    }

    public boolean isHistoricoGeral() {
        return historicoGeral;
    }

    public void setHistoricoGeral(boolean historicoGeral) {
        this.historicoGeral = historicoGeral;
    }

    public List<CxFGuiasVol> getCxfGuiasVolLista() {
        return cxfGuiasVolLista;
    }

    public void setCxfGuiasVolLista(List<CxFGuiasVol> cxfGuiasVolLista) {
        this.cxfGuiasVolLista = cxfGuiasVolLista;
    }

    public OS_Vig getOsSelecionada() {
        return osSelecionada;
    }

    public void setOsSelecionada(OS_Vig osSelecionada) {
        this.osSelecionada = osSelecionada;
    }

    public boolean isRemessas() {
        return remessas;
    }

    public void setRemessas(boolean remessas) {
        this.remessas = remessas;
    }

    public boolean isCustodiaCorporativo() {
        return custodiaCorporativo;
    }

    public void setCustodiaCorporativo(boolean custodiaCorporativo) {
        this.custodiaCorporativo = custodiaCorporativo;
    }

}

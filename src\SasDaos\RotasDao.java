package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.ATMInter;
import SasBeans.Paramet;
import SasBeans.Pedido;
import SasBeans.Rastrear;
import SasBeans.Rotas;
import SasBeans.Rt_Perc;
import SasBeansCompostas.RotasATMInterPedido;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Sqls;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class RotasDao {

    private Boolean isTranspCacamba(String empresa) throws Exception {
        SasPoolPersistencia pool = new SasPoolPersistencia();
        pool.setCaminho("/Dados/mapconect_deploy.txt");
        Persistencia inSatellite;
        inSatellite = pool.getConexao("SATELLITE", "");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    public List<Rastrear> consultarListaPosicoes(String Data, BigDecimal SeqRota, String CodFil, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            List<Rastrear> Retorno = new ArrayList<>();
            Rastrear Item;

            sql.append("SELECT\n");
            sql.append(" RastrearStat.DataPOS,\n");
            sql.append(" RastrearStat.HoraPOS,\n");
            sql.append(" Rotas.Rota,\n");
            sql.append(" rastrear.Latitude,\n");
            sql.append(" Rastrear.Longitude\n");
            sql.append(" FROM RastrearStat\n");
            sql.append(" JOIN Rotas\n");
            sql.append("   ON RastrearStat.SeqRota = Rotas.Sequencia\n");
            sql.append(" JOIN Rastrear\n");
            sql.append("   ON RastrearStat.Sequencia = Rastrear.Codigo\n");
            sql.append(" WHERE RastrearStat.SeqRota = ?\n");
            sql.append(" AND   RastrearStat.DataPOS = ?\n");
            sql.append(" AND   Rotas.CodFil         = ?\n");
            sql.append(" ORDER BY RastrearStat.HoraPOS");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setBigDecimal(SeqRota);
            consulta.setString(Data);
            consulta.setString(CodFil);

            consulta.select();

            while (consulta.Proximo()) {
                Item = new Rastrear();

                Item.setLatitude(consulta.getString("Latitude"));
                Item.setLongitude(consulta.getString("Longitude"));
                Item.setData(consulta.getLocalDate("DataPOS"));
                Item.setHora(consulta.getString("HoraPOS"));
                Item.setRota(consulta.getString("Rota"));

                Retorno.add(Item);
            }

            consulta.Close();

            return Retorno;

        } catch (Exception e) {
            throw new Exception("RotasDao.consultarListaPosicoes - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    public String gerarResumoRotaMobile(String matricula, String codfil, String dataAtual,
            Persistencia persistencia) {
        String retorno = "<resumo>";
        try {
            String sql = "SELECT\n"
                    + "    CASE WHEN \n"
                    + "        ISNULL((SELECT CodFil FROM SASLog WHERE Codigo = Escala.SeqRota AND Tabela = 'VeiculosAbast' AND Comando = '1'),'0') = '0' \n"
                    + "    THEN \n"
                    + "        '0' \n"
                    + "    ELSE \n"
                    + "        (SELECT CodFil FROM SASLog WHERE Codigo = Escala.SeqRota AND Tabela = 'VeiculosAbast' AND Comando = '1')\n"
                    + "    END kmDifIni, \n"
                    + "    CASE WHEN \n"
                    + "        CONVERT(VARCHAR, ISNULL((SELECT Historico FROM SASLog WHERE Codigo = Escala.SeqRota AND Tabela = 'VeiculosAbast' AND Comando = '1'),'0')) = '0' \n"
                    + "    THEN \n"
                    + "        '0' \n"
                    + "    ELSE \n"
                    + "        (SELECT Historico FROM SASLog WHERE Codigo = Escala.SeqRota AND Tabela = 'VeiculosAbast' AND Comando = '1')\n"
                    + "    END kmDifMotivoIni, \n"
                    + "    CASE WHEN \n"
                    + "        ISNULL((SELECT CodFil FROM SASLog WHERE Codigo = Escala.SeqRota AND Tabela = 'VeiculosAbast' AND Comando <> '1'),'0') = '0' \n"
                    + "    THEN \n"
                    + "        '0' \n"
                    + "    ELSE \n"
                    + "        (SELECT CodFil FROM SASLog WHERE Codigo = Escala.SeqRota AND Tabela = 'VeiculosAbast' AND Comando <> '1')\n"
                    + "    END kmDifFim, \n"
                    + "    CASE WHEN \n"
                    + "        CONVERT(VARCHAR, ISNULL((SELECT Historico FROM SASLog WHERE Codigo = Escala.SeqRota AND Tabela = 'VeiculosAbast' AND Comando <> '1'),'0')) = '0' \n"
                    + "    THEN \n"
                    + "        '0' \n"
                    + "    ELSE \n"
                    + "        (SELECT Historico FROM SASLog WHERE Codigo = Escala.SeqRota AND Tabela = 'VeiculosAbast' AND Comando <> '1')\n"
                    + "    END kmDifMotivoFim, \n"
                    + "    (SELECT \n"
                    + "        COUNT(*) \n"
                    + "    FROM \n"
                    + "        Rt_PercSLA \n"
                    + "    WHERE \n"
                    + "        Rt_PercSLA.Sequencia = Escala.SeqRota \n"
                    + "        AND LEN(ISNULL(Rt_PercSLA.HrChegVei,'')) > 0 \n"
                    + "        AND LEN(ISNULL(Rt_PercSla.HrSaidaVei,'')) = 0 ) Atendimento,\n"
                    + "    Escala.rota,  Escala.codfil, \n"
                    + "    CASE WHEN Funcion.nome IS NULL THEN FuncionMot.Nome ELSE Funcion.nome END nome, \n"
                    + "    Escala.veiculo, Escala.SeqRota, veiculos.placa, VeiculosMod.Descricao modeloVeic, \n"
                    + "    Funcion.nome nomeChe, FuncionMot.nome nomeMot, Escala.hora1, Escala.hora2, Escala.hora3, Escala.hora4,  \n"
                    + "    y.entOk, y.entPd, y.entGuias, y.entLacres, y.entValor, y.entPdGuias, y.entPdValor,\n"
                    + "    y.recOk, y.recPd, y.recGuias, y.recLacres, y.recValor, y.entPdLacres, y.ValorRecepCXF, y.ValorEntDir, y.ServAtrasados, \n"
                    + "    CASE WHEN y.kmFin = y.kmIni THEN 0 ELSE CONVERT(BigInt,y.kmFin) END kmFin, CONVERT(BigInt,y.kmIni) kmIni, \n"
                    + "    (SELECT \n"
                    + "        CONVERT(BigInt,Max(ISNULL(KM,0))) KM\n"
                    + "    FROM \n"
                    + "        Rt_Perc\n"
                    + "    LEFT JOIN \n"
                    + "        Escala ee\n"
                    + "        ON ee.SeqRota = Rt_Perc.Sequencia\n"
                    + "        AND ee.CodFil = Rt_Perc.CodFil\n"
                    + "    LEFT JOIN \n"
                    + "        Rt_PercDet (NoLock)  \n"
                    + "        ON Rt_PercDet.Sequencia = Rt_Perc.Sequencia\n"
                    + "        AND Rt_PercDet.Parada = Rt_Perc.Parada\n"
                    + "        AND Rt_PercDet.CodFil = Rt_Perc.CodFil\n"
                    + "    WHERE \n"
                    + "        ee.Veiculo = Escala.Veiculo\n"
                    + "    GROUP BY \n"
                    + "        ee.Veiculo) kmAnt, \n"
                    + "    y.SrvEfetivos, y.SrvAdiantados, y.SrvAtrasados, y.SrvPendentes \n"
                    + "FROM \n"
                    + "Escala \n"
                    + "LEFT JOIN Funcion \n"
                    + "    ON Funcion.matr = Escala.matrche\n"
                    + "    AND Funcion.codfil = Escala.codfil  \n"
                    + "LEFT JOIN Funcion FuncionMot  \n"
                    + "    ON FuncionMot.matr = Escala.matrmot \n"
                    + "    AND FuncionMot.codfil = Escala.codfil  \n"
                    + "LEFT JOIN veiculos \n"
                    + "    ON veiculos.numero = Escala.veiculo\n"
                    + "LEFT JOIN VeiculosMod\n"
                    + "    ON VeiculosMod.Codigo = Veiculos.Modelo\n"
                    + "LEFT JOIN \n"
                    + "    (SELECT \n"
                    + "        Sequencia, SUM(entOk) entOk, SUM(entPD) entPd, SUM(entGuias) entGuias, SUM(entLacres) entLacres, SUM(entValor) entValor,\n"
                    + "        SUM(entPdGuias) entPdGuias, SUM(entPdValor) entPdValor, SUM(recOk) recOk, SUM(recPd) recPd, SUM(recguias) recGuias, SUM(recLacres) recLacres,\n"
                    + "        SUM(recValor) recValor, SUM(entPdLacres) entPdLacres, Max(ISNULL(ValorRecepCXF,0)) ValorRecepCXF, SUM(ISNULL(ValorEntDir,0)) ValorEntDir, \n"
                    + "        SUM(ISNULL(ServAtrasados,0)) ServAtrasados, ISNULL(MAX(z.KM),0) kmFin, ISNULL(MIN(z.KM),0) kmIni, SUM(ISNULL(SrvEfetivos,0))SrvEfetivos ,\n"
                    + "        SUM(ISNULL(SrvAdiantados,0)) SrvAdiantados, SUM(ISNULL(SrvAtrasados,0)) SrvAtrasados, SUM(ISNULL(SrvPendentes,0)) SrvPendentes \n"
                    + "    FROM (\n"
                    + "        SELECT\n"
                    + "            Rt_Perc.Sequencia, Rt_Perc.Parada,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'E' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) > 0 THEN COUNT(Distinct Rt_Perc.Parada) ELSE 0 END entOk,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'E' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) = 0 THEN COUNT(Distinct Rt_Perc.Parada) ELSE 0 END entPd,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'E' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) > 0 THEN ISNULL(COUNT(Distinct Rt_Guias.Guia),0) ELSE 0 END entGuias,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'E' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) > 0 THEN ISNULL(COUNT(Distinct CxfGuiasVol.Lacre),0) ELSE 0 END entLacres,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'E' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) > 0 THEN ((ISNULL(SUM(Distinct Rt_Guias.Valor),0))/\n"
                    + "            ISNULL((SELECT \n"
                    + "                TOP 01 ISNULL(Round(Valor,5),1) \n"
                    + "            FROM \n"
                    + "                TesMoedasVlr \n"
                    + "            WHERE \n"
                    + "                TesMoedasVlr.CodFil = Rotas.CodFil \n"
                    + "                AND CONVERT(Date, DtCotacao) <= CONVERT(Date,Getdate()-1) \n"
                    + "                AND CodMoeda = (CASE WHEN Rt_GuiasMoeda.Moeda IS NOT NULL THEN Rt_GuiasMoeda.Moeda \n"
                    + "                    WHEN Pedido.TipoMoeda IS NOT NULL THEN Pedido.TipoMoeda\n"
                    + "                    ELSE MoedaPdrMobile END) ORDER BY DtCotacao Desc),1)) ELSE 0 END entValor,                    \n"
                    + "            CASE WHEN Rt_Perc.ER = 'E' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) = 0 THEN\n"
                    + "            (SELECT \n"
                    + "                ISNULL(COUNT(Distinct CxfGuias.Guia),0)\n"
                    + "            FROM \n"
                    + "                CxfGuias \n"
                    + "            WHERE \n"
                    + "                CxfGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                AND CxfGuias.Hora1D = Rt_Perc.Hora1) ELSE 0 END entPdGuias,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'E' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) = 0 THEN\n"
                    + "            (SELECT \n"
                    + "                SUM(xy.Valor) \n"
                    + "            FROM \n"
                    + "                (SELECT \n"
                    + "                    ((ISNULL((CxfGuias.Valor),0)) / \n"
                    + "                    ISNULL((SELECT \n"
                    + "                        TOP 01 ISNULL(Round(Valor,5),1) \n"
                    + "                    FROM \n"
                    + "                        TesMoedasVlr \n"
                    + "                    WHERE \n"
                    + "                        TesMoedasVlr.CodFil = Rotas.CodFil \n"
                    + "                        AND CONVERT(Date, DtCotacao) <= CONVERT(Date,Getdate()-1) \n"
                    + "                        AND CodMoeda = (CASE WHEN xy.Moeda IS NOT NULL THEN xy.Moeda \n"
                    + "                            WHEN Pedido.TipoMoeda IS NOT NULL THEN Pedido.TipoMoeda\n"
                    + "                            ELSE MoedaPdrMobile END)\n"
                    + "                    ORDER BY \n"
                    + "                        DtCotacao Desc),1)) Valor\n"
                    + "                FROM \n"
                    + "                    CxfGuias (Nolock)                    \n"
                    + "                LEFT JOIN\n"
                    + "                    Rt_guias (NoLock)\n"
                    + "                    ON Rt_Guias.Guia = CxfGuias.Guia\n"
                    + "                    AND	Rt_Guias.Serie = CxfGuias.Serie\n"
                    + "                LEFT JOIN \n"
                    + "                    Pedido (NoLock)\n"
                    + "                    ON Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                    AND Pedido.CodFil = Rotas.CodFil\n"
                    + "                LEFT JOIN \n"
                    + "                    Paramet (NoLock) \n"
                    + "                    ON Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "                LEFT JOIN\n"
                    + "                    Rt_GuiasMoeda xy (Nolock)\n"
                    + "                    ON xy.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    AND xy.Parada = Rt_Guias.Parada\n"
                    + "                    AND xy.Guia = Rt_Guias.Guia\n"
                    + "                    AND xy.Serie = Rt_Guias.Serie\n"
                    + "                WHERE \n"
                    + "                    CxfGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                    AND CxfGuias.Hora1D = Rt_Perc.Hora1) xy) ELSE 0 END entPdValor,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'E' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) = 0 THEN ISNULL(COUNT(Distinct CxfGuiasVol.Lacre),0) ELSE 0 END entPdLacres,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'R' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) > 0 THEN COUNT(Distinct Rt_Perc.Parada) ELSE 0 END recOk,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'R' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) = 0 THEN COUNT(Distinct Rt_Perc.Parada) ELSE 0 END recPd,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'R' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) > 0 THEN COUNT(Distinct Rt_Guias.Guia) ELSE 0 END recGuias,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'R' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) > 0 THEN ISNULL(COUNT(Distinct CxfGuiasVol.Lacre),0) ELSE 0 END recLacres,\n"
                    + "            CASE WHEN Rt_Perc.ER = 'R' AND LEN(ISNULL(Rt_Perc.HrCheg,'')) > 0 THEN ((ISNULL(SUM(Distinct Rt_Guias.Valor),0)) /\n"
                    + "            ISNULL((SELECT \n"
                    + "                TOP 01 ISNULL(Round(Valor,5),1) \n"
                    + "            FROM\n"
                    + "                TesMoedasVlr \n"
                    + "            WHERE \n"
                    + "                TesMoedasVlr.CodFil = Rotas.CodFil \n"
                    + "                AND CONVERT(Date, DtCotacao) <= CONVERT(Date,Getdate()-1) \n"
                    + "                AND CodMoeda = (CASE WHEN Rt_GuiasMoeda.Moeda IS NOT NULL THEN Rt_GuiasMoeda.Moeda \n"
                    + "                    WHEN Pedido.TipoMoeda IS NOT NULL THEN Pedido.TipoMoeda\n"
                    + "                    ELSE MoedaPdrMobile END) ORDER BY DtCotacao Desc),1)) ELSE 0 END recValor,\n"
                    + "            (SELECT \n"
                    + "                SUM(za.Valor) \n"
                    + "            FROM (\n"
                    + "                SELECT (\n"
                    + "                    (ISNULL(CxfGuias.Valor,0)) / \n"
                    + "                    ISNULL((SELECT \n"
                    + "                        TOP 01 ISNULL(Round(Valor,5),1) \n"
                    + "                    FROM \n"
                    + "                        TesMoedasVlr \n"
                    + "                    WHERE \n"
                    + "                        TesMoedasVlr.CodFil = Rotas.CodFil\n"
                    + "                        AND CONVERT(Date, DtCotacao) <= CONVERT(Date,Getdate()-1) \n"
                    + "                        AND CodMoeda = (CASE WHEN xy.Moeda IS NOT NULL THEN xy.Moeda \n"
                    + "                            WHEN Pedido.TipoMoeda IS NOT NULL THEN Pedido.TipoMoeda\n"
                    + "                            ELSE MoedaPdrMobile END) ORDER BY DtCotacao Desc),1)) Valor,  CxfGuias.Guia, CxfGuias.Serie \n"
                    + "                FROM \n"
                    + "                    Rotas y\n"
                    + "                LEFT JOIN \n"
                    + "                    Rt_guias (NoLock) \n"
                    + "                    ON Rt_Guias.Sequencia = y.Sequencia\n"
                    + "                    AND y.CodFil = Rotas.CodFil\n"
                    + "                LEFT JOIN \n"
                    + "                    Rt_Perc x (NoLock) \n"
                    + "                    ON x.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    AND x.Parada = Rt_Guias.Parada \n"
                    + "                LEFT JOIN \n"
                    + "                    Pedido (NoLock) \n"
                    + "                    ON Pedido.Numero = x.Pedido\n"
                    + "                    AND Pedido.CodFil = Rotas.CodFil\n"
                    + "                LEFT JOIN \n"
                    + "                    Paramet (NoLock)\n"
                    + "                    ON Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "                LEFT JOIN\n"
                    + "                    CxfGuias (NoLock)\n"
                    + "                    ON CxfGuias.Guia = Rt_Guias.Guia  \n"
                    + "                    AND CxfGuias.Serie    = Rt_Guias.SerieAnt\n"
                    + "                    AND CxfGuias.RotaEnt  = y.Rota \n"
                    + "                    AND CXFGuias.Hora1    = x.Hora1 \n"
                    + "                    AND CxfGuias.DtEnt = y.Data\n"
                    + "                LEFT JOIN \n"
                    + "                    Rt_GuiasMoeda xy (Nolock)\n"
                    + "                    ON xy.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    AND xy.Parada = Rt_Guias.Parada\n"
                    + "                    AND xy.Guia = Rt_Guias.Guia\n"
                    + "                    AND xy.Serie = Rt_Guias.Serie\n"
                    + "                WHERE \n"
                    + "                    y.Sequencia = Rotas.Sequencia\n"
                    + "                    AND CxfGuias.DtEnt IS NOT NULL ) za) ValorRecepCXF, \n"
                    + "            (SELECT \n"
                    + "                SUM(zy.Valor)\n"
                    + "            FROM \n"
                    + "                (SELECT ((ISNULL(b.Valor,0)) / \n"
                    + "                ISNULL((SELECT \n"
                    + "                    TOP 01 ISNULL(Round(Valor,5),1) \n"
                    + "                FROM\n"
                    + "                    TesMoedasVlr \n"
                    + "                WHERE \n"
                    + "                    TesMoedasVlr.CodFil = Rotas.CodFil \n"
                    + "                    AND CONVERT(Date, DtCotacao) <= CONVERT(Date,Getdate()-1) \n"
                    + "                    AND CodMoeda = (CASE WHEN xy.Moeda IS NOT NULL THEN xy.Moeda \n"
                    + "                    WHEN Pedido.TipoMoeda IS NOT NULL THEN Pedido.TipoMoeda\n"
                    + "                    ELSE MoedaPdrMobile END) ORDER BY DtCotacao Desc),1)) Valor\n"
                    + "        FROM Rt_Guias b (noLock) \n"
                    + "  LEFT JOIN Rt_Perc c (noLock) ON b.Sequencia = c.Sequencia \n"
                    + "                      AND b.Parada = c.Parada\n"
                    + "  LEFT JOIN Rotas (Nolock)  ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + " LEFT JOIN Pedido (NoLock)  ON Pedido.Numero = c.Pedido\n"
                    + "                                                    AND Pedido.CodFil = Rotas.Codfil\n"
                    + " LEFT JOIN Paramet (NoLock)  ON Paramet.Filial_PDR = Rotas.CodFil \n"
                    + "   LEFT JOIN Rt_GuiasMoeda xy (Nolock) ON xy.Sequencia = b.Sequencia\n"
                    + "                                 AND xy.Parada = b.Parada\n"
                    + "                                                             AND xy.Guia = b.Guia\n"
                    + "                                                             AND xy.Serie = b.Serie\n"
                    + "  WHERE b.Sequencia = Rt_Perc.Sequencia \n"
                    + "    AND c.Hora1 = Rt_Perc.Hora1D \n"
                    + "    AND LEN(c.HrCheg) >= 5) zy\n"
                    + ") ValorEntDir,  \n"
                    + " (SELECT COUNT(*) FROM Rt_Perc xa (nolock) \n"
                    + "   WHERE xa.Sequencia = Rt_Perc.Sequencia \n"
                    + "     AND xa.Flag_Excl <> '*' \n"
                    + "     AND (LEN(xa.HrCheg) < 4 or xa.HrCheg IS NULL) \n"
                    + "     AND DateDiff(mi, CONVERT(Datetime, (CONVERT(VarChar,Rotas.Data,112) + ' ' + SUBSTRING(xa.Hora1,1,2)+':'+SUBSTRING(xa.Hora1,3,2)+':00' )), getdate()) > 15 \n"
                    + "     AND SUBSTRING(xa.Hora1,1,2) BETWEEN 00 AND 23 \n"
                    + "     AND SUBSTRING(xa.Hora1,3,2) BETWEEN 00 AND 59 \n"
                    + ")  ServAtrasados, Rt_PercDet.Km KM, \n"
                    + "(SELECT COUNT(*) Qtde \n"
                    + "FROM Rt_Perc a (noLock)\n"
                    + "LEFT JOIN Rotas b (noLock) ON b.Sequencia = a.Sequencia\n"
                    + "WHERE a.Sequencia = Rotas.Sequencia\n"
                    + "  AND a.Flag_Excl <> '*'\n"
                    + "  AND a.Parada = Rt_Perc.Parada\n"
                    + "  AND a.HrCheg IS NOT NULL \n"
                    + "  AND a.HrCheg <> ''\n"
                    + "  AND abs(DateDiff(mi, CASE WHEN LEN(a.Hora1) = 4 THEN SUBSTRING(a.Hora1,1,2)+':'+SUBSTRING(a.Hora1,3,2) ELSE a.Hora1 END, a.HrCheg)) BETWEEN 0 AND 15) SrvEfetivos, \n"
                    + "(SELECT COUNT(*) Qtde  \n"
                    + "FROM Rt_Perc a (noLock)\n"
                    + "LEFT JOIN Rotas b (noLock) ON b.Sequencia = a.Sequencia\n"
                    + "WHERE a.Sequencia = Rotas.Sequencia \n"
                    + "    AND a.Flag_Excl <> '*'\n"
                    + "  AND a.Parada = Rt_Perc.Parada\n"
                    + "    AND a.HrCheg IS NOT NULL \n"
                    + "    AND a.HrCheg <> ''\n"
                    + "    AND DateDiff(mi, CASE WHEN LEN(a.Hora1) = 4 THEN SUBSTRING(a.Hora1,1,2)+':'+SUBSTRING(a.Hora1,3,2) ELSE a.Hora1 END, a.HrCheg) < 0) SrvAdiantados, \n"
                    + "(SELECT COUNT(*) Qtde \n"
                    + "FROM Rt_Perc a (noLock)\n"
                    + "LEFT JOIN Rotas b (noLock) ON b.Sequencia = a.Sequencia\n"
                    + "WHERE a.Sequencia = Rotas.Sequencia\n"
                    + "    AND a.Flag_Excl <> '*'\n"
                    + "  AND a.Parada = Rt_Perc.Parada                    \n"
                    + "    AND a.HrCheg IS NOT NULL \n"
                    + "    AND a.HrCheg <> ''\n"
                    + "    AND DateDiff(mi, CASE WHEN LEN(a.Hora1) = 4 THEN SUBSTRING(a.Hora1,1,2)+':'+SUBSTRING(a.Hora1,3,2) ELSE a.Hora1 END, a.HrCheg) > 15) SrvAtrasados, \n"
                    + "(SELECT COUNT(*) Qtde  \n"
                    + "FROM Rt_Perc a (noLock)\n"
                    + "LEFT JOIN Rotas b (noLock) ON b.Sequencia = a.Sequencia\n"
                    + "WHERE a.Sequencia = Rotas.Sequencia\n"
                    + "  AND a.Flag_Excl <> '*'\n"
                    + "  AND a.Parada = Rt_Perc.Parada                    \n"
                    + "  AND (a.HrCheg IS NULL or a.HrCheg = '')) SrvPendentes \n"
                    + " FROM Rt_Perc \n"
                    + "LEFT JOIN Rt_PercDet (NoLock)  ON Rt_PercDet.Sequencia = Rt_Perc.Sequencia\n"
                    + "                     AND Rt_PercDet.Parada = Rt_Perc.Parada\n"
                    + "                     AND Rt_PercDet.CodFil = Rt_Perc.CodFil\n"
                    + "                    AND Rt_PercDet.Km > 0\n"
                    + "LEFT JOIN Rotas (NoLock) ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "LEFT JOIN Rt_Guias (NoLock) ON Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   AND Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "LEFT JOIN CxfGuiasVol (NoLock) ON CxfGuiasVol.Guia = Rt_Guias.Guia\n"
                    + "                      AND CxfGuiasVol.Serie = Rt_Guias.Serie\n"
                    + "LEFT JOIN Pedido (NoLock)  ON Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                                                                          AND Pedido.CodFil = Rotas.Codfil\n"
                    + "LEFT JOIN Paramet (NoLock)  ON Paramet.Filial_PDR = Rotas.CodFil	\n"
                    + "LEFT JOIN Rt_GuiasMoeda  (Nolock) ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                 AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "                                                             AND Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                                                             AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "WHERE Rotas.Data = ? \n"
                    + " AND Rt_Perc.Flag_Excl <> '*'\n"
                    + "AND Rt_Perc.CodCli1 NOT in (SELECT CodCli FROM CxForte WHERE CxForte.CodFil = Rotas.CodFil) \n"
                    + "GROUP BY Rt_Perc.ER, Rt_Perc.HrCheg, Rt_Perc.Parada, Rt_Perc.Sequencia, Rt_Perc.Parada, Rt_Perc.Hora1, \n"
                    + "Rotas.Data, Rotas.CodFil, Rt_Perc.Hora1D, Rt_PercDet.KM, Pedido.TipoMoeda, Paramet.MoedaPdrMobile, Rotas.Sequencia, Rt_GuiasMoeda.Moeda, Rt_Perc.Pedido) z\n"
                    + "GROUP BY z.Sequencia) y  ON y.Sequencia = Escala.SeqRota   \n"
                    + "WHERE Escala.Data = ? AND Escala.codFil = ? \n"
                    + "AND (MatrChe = ?  OR MatrMot = ? );\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.setString(codfil);
            consulta.setString(matricula);
            consulta.setString(matricula);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = retorno + Xmls.tag("atendimento", consulta.getString("atendimento"));
                retorno = retorno + Xmls.tag("rota", consulta.getString("rota"));
                retorno = retorno + Xmls.tag("codfil", consulta.getString("codfil").replace(".0", ""));
                retorno = retorno + Xmls.tag("nome", consulta.getString("nome"));
                retorno = retorno + Xmls.tag("veiculo", consulta.getString("veiculo").replace(".0", ""));
                retorno = retorno + Xmls.tag("seqrota", consulta.getString("seqrota").replace(".0", ""));
                retorno = retorno + Xmls.tag("placa", consulta.getString("placa"));
                retorno = retorno + Xmls.tag("modeloveic", consulta.getString("modeloveic"));
                retorno = retorno + Xmls.tag("nomeche", consulta.getString("nomeche"));
                retorno = retorno + Xmls.tag("nomemot", consulta.getString("nomemot"));
                retorno = retorno + Xmls.tag("hora1", consulta.getString("hora1"));
                retorno = retorno + Xmls.tag("hora2", consulta.getString("hora2"));
                retorno = retorno + Xmls.tag("hora3", consulta.getString("hora3"));
                retorno = retorno + Xmls.tag("hora4", consulta.getString("hora4"));
                retorno = retorno + Xmls.tag("entok", consulta.getString("entok"));
                retorno = retorno + Xmls.tag("entpd", consulta.getString("entpd"));
                retorno = retorno + Xmls.tag("entguias", consulta.getString("entguias"));
                retorno = retorno + Xmls.tag("entlacres", consulta.getString("entlacres"));
                retorno = retorno + Xmls.tag("entvalor", consulta.getString("entvalor"));
                retorno = retorno + Xmls.tag("entpdguias", consulta.getString("entpdguias"));
                retorno = retorno + Xmls.tag("entpdvalor", consulta.getString("entpdvalor"));
                retorno = retorno + Xmls.tag("recok", consulta.getString("recok"));
                retorno = retorno + Xmls.tag("recpd", consulta.getString("recpd"));
                retorno = retorno + Xmls.tag("recguias", consulta.getString("recguias"));
                retorno = retorno + Xmls.tag("reclacres", consulta.getString("reclacres"));
                retorno = retorno + Xmls.tag("recvalor", consulta.getString("recvalor"));
                retorno = retorno + Xmls.tag("entpdlacres", consulta.getString("entpdlacres"));
                retorno = retorno + Xmls.tag("valorrecepcxf", consulta.getFloat("valorrecepcxf"));
                retorno = retorno + Xmls.tag("valorentdir", consulta.getString("valorentdir"));
                retorno = retorno + Xmls.tag("servatrasados", consulta.getString("servatrasados"));
                retorno = retorno + Xmls.tag("kmAnt", consulta.getString("kmAnt").replace(".0", ""));
                retorno = retorno + Xmls.tag("kmIni", consulta.getString("kmIni").replace(".0", ""));
                retorno = retorno + Xmls.tag("kmFin", consulta.getString("kmFin").replace(".0", ""));
                retorno = retorno + Xmls.tag("kmDifIni", consulta.getString("kmDifIni").replace(".0", ""));
                retorno = retorno + Xmls.tag("kmDifFim", consulta.getString("kmDifFim").replace(".0", ""));
                retorno = retorno + Xmls.tag("kmDifMotivoIni", consulta.getString("kmDifMotivoIni").replace(".0", ""));
                retorno = retorno + Xmls.tag("kmDifMotivoFim", consulta.getString("kmDifMotivoFim").replace(".0", ""));

                retorno = retorno + Xmls.tag("SrvEfetivos", consulta.getString("SrvEfetivos"));
                retorno = retorno + Xmls.tag("SrvAdiantados", consulta.getString("SrvAdiantados"));
                retorno = retorno + Xmls.tag("SrvAtrasados", consulta.getString("SrvAtrasados"));
                retorno = retorno + Xmls.tag("SrvPendentes", consulta.getString("SrvPendentes"));
            }
            retorno = retorno + "</resumo>";
            consulta.Close();
        } catch (Exception e) {
            retorno = Xmls.tag("resumo", "");
        } finally {
            return retorno;
        }
    }

    /**
     * Busca último número de sequencia de rota
     *
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public BigDecimal MaxSequencia(Persistencia persistencia) throws Exception {
        String sql = "select max(isnull(sequencia,0)) Sequencia from rotas";
        BigDecimal retorno = new BigDecimal("0");
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = new BigDecimal(consult.getString("Sequencia"));
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.MaxSequencia - " + e.getMessage() + "\r\n"
                    + "select max(sequencia) 'Sequencia' from rotas");
        }
    }

    /**
     * Verifica a existencia da rota
     *
     * @param sequencia sequencia da rota
     * @param data verifica a data do sistema
     * @param persistencia conexão com o banco de dados
     * @return informando se existe rota ou não
     * @throws Exception
     */
    public String existeRota(BigDecimal sequencia, String data, Persistencia persistencia) throws Exception {
        String dataRota = "";
        try {
            String sql = "SELECT data FROM Rotas WHERE Sequencia = ? AND data != ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.setString(data);
            consulta.select();

            while (consulta.Proximo()) {
                dataRota = consulta.getString("data");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.existeRota - " + e.getMessage() + "\r\n"
                    + "SELECT data FROM Rotas WHERE Sequencia = " + sequencia + " AND data != " + data);
        }
        return dataRota;
    }

    /**
     * Busca o próximo número de rota
     *
     * @param CodFil - Filial da rota
     * @param Data - Data da rota
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String MaxRota(BigDecimal CodFil, String Data, Persistencia persistencia) throws Exception {
        try {
            Integer rota;
            String retorno = "000";
            String sql = "select max(rota) 'Rota' "
                    + " from rotas "
                    + " where codfil = ? "
                    + " and data = ? "
                    + " and rota > 800";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.setString(Data);
            consult.select();
            while (consult.Proximo()) {
                rota = consult.getInt("Rota");
                rota++;
                retorno = FuncoesString.PreencheEsquerda(String.valueOf(rota), 3, "0");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.MaxRota - " + e.getMessage() + "\r\n"
                    + "select max(rota) 'Rota' "
                    + " from rotas "
                    + " where codfil = " + CodFil
                    + " and data = " + Data
                    + " and rota > 800");
        }
    }

    /**
     * Busca pedido relacionado a uma parada da rota
     *
     * @param sequencia - sequencia da rota
     * @param parada - parada da rota
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<RotasATMInterPedido> selecionaPedido(String sequencia, String parada, Persistencia persistencia) throws Exception {
        String sql = "Select Pedido.CodFil,ATMInter.OS, Pedido.OS as osfat "
                + " From Rotas "
                + " Left Join Rt_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia "
                + " Left Join Pedido   on Pedido.Numero = Rt_Perc.Pedido "
                + " Left Join ATMInter on ATMInter.Pedido = Rt_Perc.Pedido "
                + " Where Rt_Perc.Sequencia = ?"
                + " and Rt_Perc.Parada = ?";
        List<RotasATMInterPedido> rotas = new ArrayList();
        try {
            Consulta rs = new Consulta(sql, persistencia);
            rs.setString(sequencia);
            rs.setString(parada);
            rs.select();

            while (rs.Proximo()) {
                Rotas rota = new Rotas();
                ATMInter atminter = new ATMInter();
                Pedido pedido = new Pedido();
                RotasATMInterPedido rotaatminterpedido = new RotasATMInterPedido();
                pedido.setCodFil(rs.getString("CodFil"));
                atminter.setOS(rs.getString("OS"));
                pedido.setOS(rs.getString("osfat"));
                rotaatminterpedido.setRotas(rota);
                rotaatminterpedido.setAtminter(atminter);
                rotaatminterpedido.setPedido(pedido);
                rotas.add(rotaatminterpedido);
            }
            rs.Close();
            return rotas;
        } catch (SQLException e) {
            throw new Exception("RotasDao.selecionaPedido - " + e.getMessage() + "\r\n"
                    + "Select Pedido.CodFil,ATMInter.OS, Pedido.OS as osfat "
                    + " From Rotas "
                    + " Left Join Rt_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia "
                    + " Left Join Pedido   on Pedido.Numero = Rt_Perc.Pedido "
                    + " Left Join ATMInter on ATMInter.Pedido = Rt_Perc.Pedido "
                    + " Where Rt_Perc.Sequencia = " + sequencia
                    + " and Rt_Perc.Parada = " + parada);
        }
    }

    /**
     * Inserção de nova rota
     *
     * @param rotas - Objeto rota a ser inserido
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void Inserir(Rotas rotas, Persistencia persistencia) throws Exception {
        try {
            Rotas rota = new Rotas();
            String sql = Sqls.montaInsert(rota);
            Consulta consulta = new Consulta(sql, persistencia);
            rota = rotas;
            consulta.setBigDecimal(rota.getSequencia());
            consulta.setString(rota.getRota());
            consulta.setString(rota.getData());
            consulta.setBigDecimal(rota.getCodFil());
            consulta.setString(rota.getTpVeic());
            consulta.setString(rota.getViagem());
            consulta.setString(rota.getATM());
            consulta.setString(rota.getBACEN());
            consulta.setString(rota.getAeroporto());
            consulta.setString(rota.getHrLargada());
            consulta.setString(rota.getHrChegada());
            consulta.setString(rota.getHrIntIni());
            consulta.setString(rota.getHrIntFim());
            consulta.setBigDecimal(rota.getHsTotal());
            consulta.setString(rota.getObservacao());
            consulta.setBigDecimal(rota.getKmSaida());
            consulta.setBigDecimal(rota.getKmChegada());
            consulta.setBigDecimal(rota.getKmTotal());
            consulta.setBigDecimal(rota.getHrUrb());
            consulta.setBigDecimal(rota.getHrInter());
            consulta.setBigDecimal(rota.getPrdUrb());
            consulta.setBigDecimal(rota.getPrdInter());
            consulta.setBigDecimal(rota.getGuiasUrb());
            consulta.setBigDecimal(rota.getGuiasInter());
            consulta.setBigDecimal(rota.getKmTotConj());
            consulta.setBigDecimal(rota.getKmTotConj2());
            consulta.setString(rota.getOperador());
            consulta.setDate(DataAtual.LC2Date(rota.getDt_Alter()));
            consulta.setString(rota.getHr_Alter());
            consulta.setString(rota.getOperFech());
            consulta.setDate(DataAtual.LC2Date(rota.getDt_Fech()));
            consulta.setString(rota.getHr_Fech());
            consulta.setString(rota.getFlag_Excl());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RotasDao.Inserir - " + e.getMessage());
        }
    }

    /**
     * Inserir rota
     *
     * @param rotas objetos contendo informações de rota - Campos necessários:
     * sequencia, rota, data, codfil, TpVeic, Viagem, bacen, aeroporto,
     * hrlargada, hrchegada, hrintini, hrintfim, observacao, operador, dt_alter,
     * hr_alter
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void inserirRota(Rotas rotas, Persistencia persistencia) throws Exception {
        try {
            String query = "INSERT INTO rotas (sequencia, rota, data, codfil, TpVeic,"
                    + "Viagem, bacen, aeroporto, hrlargada, hrchegada,"
                    + "hrintini, hrintfim, observacao, operador, dt_alter, hr_alter, "
                    + "atm, flag_excl, hstotal, DtFim) "
                    + "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?); \n"
                    + "Insert into RotasPnt (Sequencia, SeqRota, Data, Operador, Dt_Alter, Hr_Alter)\n"
                    + "Values(?,?,?,?,?,?) \n";

            Consulta consulta = new Consulta(query, persistencia);

            consulta.setBigDecimal(rotas.getSequencia());
            consulta.setString(rotas.getRota());
            consulta.setString(rotas.getData());
            consulta.setBigDecimal(rotas.getCodFil());
            consulta.setString(rotas.getTpVeic());
            consulta.setString(rotas.getViagem());
            consulta.setString(rotas.getBACEN());
            consulta.setString(rotas.getAeroporto());
            consulta.setString(rotas.getHrLargada());
            consulta.setString(rotas.getHrChegada());
            consulta.setString(rotas.getHrIntIni());
            consulta.setString(rotas.getHrIntFim());
            consulta.setString(rotas.getObservacao());
            consulta.setString(rotas.getOperador());
            consulta.setString(rotas.getDt_Alter().toString());
            consulta.setString(rotas.getHr_Alter());
            consulta.setString(rotas.getATM());
            consulta.setString(rotas.getFlag_Excl());
            consulta.setBigDecimal(rotas.getHsTotal());
            consulta.setString(rotas.getData());

            consulta.setBigDecimal(rotas.getSequencia());
            consulta.setBigDecimal(rotas.getSequencia());
            consulta.setString(rotas.getData());
            consulta.setString(rotas.getOperador());
            consulta.setString(rotas.getDt_Alter().toString());
            consulta.setString(rotas.getHr_Alter());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RotasDao.inserirRotasSupervisao - " + e.getMessage() + "\r\n"
                    + "INSERT INTO rotas (sequencia, rota, data, codfil, TpVeic,"
                    + "Viagem, bacen, aeroporto, hrlargada, hrchegada,"
                    + "hrintini, hrintfim, observacao, operador, dt_alter, hr_alter, "
                    + "atm, flag_excl, hstotal) "
                    + "VALUES(" + rotas.getSequencia() + "," + rotas.getRota() + "," + rotas.getData() + "," + rotas.getCodFil() + ","
                    + rotas.getTpVeic() + "," + rotas.getViagem() + "," + rotas.getBACEN() + "," + rotas.getAeroporto() + "," + rotas.getHrLargada() + ","
                    + rotas.getHrChegada() + "," + rotas.getHrIntIni() + "," + rotas.getHrIntFim() + "," + rotas.getObservacao() + "," + rotas.getOperador() + ","
                    + rotas.getDt_Alter().toString() + "," + rotas.getHr_Alter() + "," + rotas.getATM() + "," + rotas.getFlag_Excl() + "," + rotas.getHsTotal() + ")");
        }
    }

    /**
     * Inserir rota
     *
     * @param rotas objetos contendo informações de rota - Campos necessários:
     * sequencia, rota, data, codfil, TpVeic, Viagem, bacen, aeroporto,
     * hrlargada, hrchegada, hrintini, hrintfim, observacao, operador, dt_alter,
     * hr_alter
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public String inserirRotaSequencia(Rotas rota, Persistencia persistencia) throws Exception {
        try {
            String query = "DECLARE @SeqRota Float\n"
                    + "SET @SeqRota = (SELECT Max(ISNULL(Sequencia, 0) + 1) FROM Rotas)\n"
                    + "SET NOCOUNT ON\n"
                    + "INSERT INTO rotas (sequencia, rota, data, codfil, TpVeic, HrLargada, HrChegada, HrIntIni, HrIntFim, \n"
                    + "Viagem, bacen, aeroporto, operador, dt_alter, hr_alter, atm, flag_excl, DtFim) \n"
                    + "VALUES(@SeqRota, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) \n"
                    + "Insert into RotasPnt (Sequencia, SeqRota, Data, Operador, Dt_Alter, Hr_Alter)\n"
                    + "Values(@SeqRota,@SeqRota,?,?,?,?) \n"
                    + "SELECT @SeqRota SeqRota\n";

            Consulta consulta = new Consulta(query, persistencia);

            consulta.setString(rota.getRota());
            consulta.setString(rota.getData());
            consulta.setBigDecimal(rota.getCodFil());
            consulta.setString(rota.getTpVeic());
            consulta.setString(rota.getHrLargada());
            consulta.setString(rota.getHrChegada());
            consulta.setString(rota.getHrIntIni());
            consulta.setString(rota.getHrIntFim());
            consulta.setString(rota.getViagem());
            consulta.setString(rota.getBACEN());
            consulta.setString(rota.getAeroporto());
            consulta.setString(rota.getOperador());
            consulta.setString(rota.getDt_Alter().toString());
            consulta.setString(rota.getHr_Alter());
            consulta.setString(rota.getATM());
            consulta.setString(rota.getFlag_Excl());
            consulta.setString(rota.getData());

            consulta.setString(rota.getData());
            consulta.setString(rota.getOperador());
            consulta.setString(rota.getDt_Alter().toString());
            consulta.setString(rota.getHr_Alter());

            consulta.select();

            String seqRota = null;
            if (consulta.Proximo()) {
                seqRota = consulta.getString("SeqRota");
            }

            consulta.close();
            return seqRota;
        } catch (Exception e) {
            throw new Exception("RotasDao.inserirRotaSequencia - " + e.getMessage() + "\r\n"
                    + "DECLARE @SeqRota Float\n"
                    + "SET @SeqRota = (SELECT Max(ISNULL(Sequencia, 0) + 1) FROM Rotas)\n"
                    + "SET NOCOUNT ON\n"
                    + "INSERT INTO rotas (sequencia, rota, data, codfil, TpVeic,\n"
                    + "Viagem, bacen, aeroporto, operador, dt_alter, hr_alter, atm, flag_excl, DtFim) \n"
                    + "VALUES(@SeqRota, " + rota.getRota() + ", " + rota.getData() + ", " + rota.getCodFil() + ", " + rota.getTpVeic() + ",\n"
                    + "" + rota.getViagem() + ", " + rota.getBACEN() + ", " + rota.getAeroporto() + ", " + rota.getOperador() + ", " + rota.getDt_Alter() + ", " + rota.getHr_Alter() + ", " + rota.getATM() + ", " + rota.getFlag_Excl() + ", " + rota.getDtFim() + ") \n"
                    + "Insert into RotasPnt (Sequencia, SeqRota, Data, Operador, Dt_Alter, Hr_Alter)\n"
                    + "Values(@SeqRota,@SeqRota, " + rota.getData() + ", " + rota.getOperador() + ", " + rota.getDt_Alter() + ", " + rota.getHr_Alter() + ") \n"
                    + "SELECT @SeqRota\n");
        }
    }

    /**
     * Atualiza rota
     *
     * @param rotas objetos contendo informações de rota - Campos necessários:
     * sequencia, rota, data, codfil, TpVeic, Viagem, bacen, aeroporto,
     * hrlargada, hrchegada, hrintini, hrintfim, observacao, operador, dt_alter,
     * hr_alter
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void atualizarRota(Rotas rotas, Persistencia persistencia) throws Exception {
        try {
            String query = "UPDATE rotas SET data=?, codfil=?, TpVeic=?,"
                    + "Viagem=?, bacen=?, aeroporto=?, hrlargada=?, hrchegada=?,"
                    + "hrintini=?, hrintfim=?, observacao=?, operador=?, dt_alter=?, hr_alter=?,"
                    + " atm=?, flag_excl=?, hstotal=? WHERE rota = ? AND sequencia = ?";
            Consulta consulta = new Consulta(query, persistencia);

            consulta.setString(rotas.getData());
            consulta.setBigDecimal(rotas.getCodFil());
            consulta.setString(rotas.getTpVeic());
            consulta.setString(rotas.getViagem());
            consulta.setString(rotas.getBACEN());
            consulta.setString(rotas.getAeroporto());
            consulta.setString(rotas.getHrLargada());
            consulta.setString(rotas.getHrChegada());
            consulta.setString(rotas.getHrIntIni());
            consulta.setString(rotas.getHrIntFim());
            consulta.setString(rotas.getObservacao());
            consulta.setString(rotas.getOperador());
            consulta.setString(rotas.getDt_Alter().toString());
            consulta.setString(rotas.getHr_Alter());
            consulta.setString(rotas.getATM());
            consulta.setString(rotas.getFlag_Excl());
            consulta.setBigDecimal(rotas.getHsTotal());
            consulta.setString(rotas.getRota());
            consulta.setBigDecimal(rotas.getSequencia());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RotasDao.atualizarRotasSupervisao - " + e.getMessage() + "\r\n"
                    + "UPDATE rotas SET data=" + rotas.getData().toString() + ", codfil=" + rotas.getCodFil() + ", TpVeic=" + rotas.getTpVeic() + ","
                    + "Viagem=" + rotas.getViagem() + ", bacen=" + rotas.getBACEN() + ", aeroporto=" + rotas.getAeroporto() + ", hrlargada=" + rotas.getHrLargada() + ","
                    + " hrchegada=" + rotas.getHrChegada() + ","
                    + " hrintini=" + rotas.getHrIntIni() + ", hrintfim=" + rotas.getHrIntFim() + ", observacao=" + rotas.getObservacao() + ","
                    + " operador=" + rotas.getOperador() + ", dt_alter=" + rotas.getDt_Alter().toString() + ", hr_alter=" + rotas.getHr_Alter() + ","
                    + " atm=" + rotas.getATM() + ", flag_excl=" + rotas.getFlag_Excl() + " WHERE rota = " + rotas.getRota() + " AND sequencia = " + rotas.getSequencia() + "");
        }
    }

    public void atualizarRotasSupervisaoSatMob(Rotas rotas, Persistencia persistencia) throws Exception {
        try {
            String query = "UPDATE rotas SET hrlargada=?, hrchegada=?, hrintini=?, hrintfim=?,"
                    + " hstotal=?, observacao=?, operador=?, dt_alter=?, hr_alter=? "
                    + " WHERE sequencia = ?";

            Consulta consulta = new Consulta(query, persistencia);

            consulta.setString(rotas.getHrLargada());
            consulta.setString(rotas.getHrChegada());
            consulta.setString(rotas.getHrIntIni());
            consulta.setString(rotas.getHrIntFim());
            consulta.setBigDecimal(rotas.getHsTotal());
            consulta.setString(rotas.getObservacao());
            consulta.setString(rotas.getOperador());
            consulta.setString(rotas.getDt_Alter().toString());
            consulta.setString(rotas.getHr_Alter());
            consulta.setBigDecimal(rotas.getSequencia());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RotasDao.atualizarRotasSupervisaoSatMob - " + e.getMessage() + "\r\n"
                    + "UPDATE rotas SET hrlargada=" + rotas.getHrLargada() + ", hrchegada=" + rotas.getHrChegada() + ", hrintini=" + rotas.getHrIntIni() + ", "
                    + " hrintfim=" + rotas.getHrIntFim() + ", hstotal=" + rotas.getHsTotal() + ", observacao=" + rotas.getObservacao() + ", "
                    + "operador=" + rotas.getOperador() + ", dt_alter=" + rotas.getDt_Alter() + ", hr_alter=" + rotas.getHr_Alter()
                    + " WHERE sequencia = " + rotas.getSequencia());
        }
    }

    /**
     * Inserir uma flag de exclusão em rotas
     *
     * @param rotas Rota a ser excluída
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void excluir(Rotas rotas, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE rotas SET flag_excl = ? WHERE sequencia = ? AND rota = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString("*");
            consulta.setBigDecimal(rotas.getSequencia());
            consulta.setString(rotas.getRota());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RotasDao.excluir - " + e.getMessage() + "\r\n"
                    + "UPDATE rotas SET flag_excl = " + "*" + " WHERE sequencia = " + rotas.getSequencia() + " AND rota = " + rotas.getRota());
        }
    }

    public void excluirCompleto(Rotas rotas, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "Update Rotas \n"
                    + " set Flag_Excl = '*',\n"
                    + "     Operador  = ?,\n"
                    + "     Dt_Alter  = ?,\n"
                    + "     Hr_Alter  = ?\n"
                    + " where Sequencia = ?;\n"
                    + "\n"
                    + " Update Rt_Perc \n"
                    + " SET Flag_Excl = '*',\n"
                    + "     OperExcl  = ?,\n"
                    + "     Dt_Excl   = ?,\n"
                    + "     Hr_Excl   = ?\n"
                    + " where Sequencia = ?;\n"
                    + " UPDATE pedido\n" +
                    " SET situacao = 'PD',\n" +
                    "     seqrota  = null,\n" +
                    "     parada   = null\n" +
                    " where seqrota = ?;";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(rotas.getOperador());
            consulta.setDate(Date.valueOf(rotas.getDt_Alter()));
            consulta.setString(rotas.getHr_Alter());
            consulta.setBigDecimal(rotas.getSequencia());

            consulta.setString(rotas.getOperador());
            consulta.setDate(Date.valueOf(rotas.getDt_Alter()));
            consulta.setString(rotas.getHr_Alter());
            consulta.setBigDecimal(rotas.getSequencia());
            
            consulta.setBigDecimal(rotas.getSequencia());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RotasDao.excluirCompleto - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * realiza a listagem das rotas de supervisão
     *
     * @param codFil Código da filial
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os registros
     * @throws Exception
     */
    public List<Rotas> listarRotas(String codFil, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList();
        try {
            String sql = "SELECT pessoa.nome, rotas.* FROM rotas "
                    + "JOIN escala "
                    + "ON rotas.sequencia = escala.seqrota "
                    + "JOIN pessoa "
                    + "ON pessoa.codigo = escala.codpessoasup "
                    + "WHERE rotas.codfil = ? ORDER BY data DESC";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();

            Rotas rota = null;
            while (consulta.Proximo()) {
                rota = new Rotas();

                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("Data"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));

                rotas.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listarRotas - " + e.getMessage() + "\r\n"
                    + "SELECT pessoa.nome, rotas.* FROM rotas "
                    + "JOIN escala "
                    + "ON rotas.sequencia = escala.seqrota "
                    + "JOIN pessoa "
                    + "ON pessoa.codigo = escala.codpessoasup "
                    + "WHERE rotas.codfil = " + codFil + " ORDER BY data DESC");
        }
        return rotas;
    }

    /**
     * realiza a listagem das rotas de supervisão por data
     *
     * @param codFil Código da filial
     * @param data Data da(s) rota(s)
     * @param excl
     * @param codpessoa Código da pessoa para permissão de filiais
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os registros
     * @throws Exception
     */
    public List<Rotas> listarRotasData(String codFil, String data, Boolean excl, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList();
        try {
            String sql = "Select Pessoa.Nome, \n"
                    + "Rotas.*,  \n"
                    + "Escala.CodPessoaSup  from  Rotas \n"
                    + "left join Escala on  Escala.SeqRota = Rotas.Sequencia\n"
                    + "and Escala.CodFil  = Rotas.CodFil     \n"
                    + "left join Pessoa on  Pessoa.Codigo  = Escala.CodPessoaSup    \n"
                    + "where Rotas.Data = ? and Rotas.TpVeic = 'S'\n";

            Rotas rota = null;

            if (excl) {
                sql = sql + "and Rotas.Flag_excl <> '*' \n";
            }
            if (!codFil.equals(" ")) {
                sql = sql + "and Rotas.CodFil = ? \n";
            } else {
                sql = sql + "and Rotas.CodFil in \n"
                        + "(select filiais.codfil \n"
                        + " from saspw"
                        + " inner join saspwfil on saspwfil.nome = saspw.nome"
                        + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                        + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                        + " where saspw.codpessoa = ? and paramet.path = ?)";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            if (!codFil.equals(" ")) {
                consulta.setString(codFil);
            } else {
                consulta.setBigDecimal(codpessoa);
//                if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                    consulta.setString("SatCVB");
//                } else {
                consulta.setString(persistencia.getEmpresa());
//                }
            }
            consulta.select();
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("Data"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));

                rotas.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listarRotasData - " + e.getMessage());
        }
        return rotas;
    }

    /**
     * Lista todas as rotas que não sejam de supervisão do dia.
     *
     * @param codFil
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rotas> resumoRotas(String codFil, String dataInicial, String dataFinal, Persistencia persistencia) throws Exception {
        try {
            List<Rotas> retorno = new ArrayList<>();
            String sql = "Select CONVERT(VARCHAR,Escala.Data,112) Data, \n"
                    + "(Select count(*) from Rt_PercSLA where Rt_PercSLA.Sequencia = escala.SeqRota and len(isnull(Rt_PercSLA.HrChegVei,'')) > 0 \n"
                    + "    and len(isnull(Rt_PercSla.HrSaidaVei,'')) = 0 ) Atendimento,\n"
                    + " escala.rota,  escala.codfil, \n"
                    + " case when funcion.nome is null then funcionMot.Nome else funcion.nome end nome, \n"
                    + " escala.veiculo, escala.SeqRota, veiculos.placa,\n"
                    + " VeiculosMod.Descricao modeloVeic, funcion.nome nomeChe, funcionMot.nome nomeMot,\n"
                    + "  escala.hora1, escala.hora2, escala.hora3, escala.hora4,  \n"
                    + " isnull(y.entOk,0) entOk, isnull(y.entPd,0) entPd, isnull(y.entGuias,0) entGuias, isnull(y.entLacres,0) entLacres, isnull(y.entValor,0) entValor,\n"
                    + " isnull(y.entPdGuias,0) entPdGuias, isnull(y.entPdValor,0) entPdValor, isnull(y.recOk,0) recOk, isnull(y.recPd,0) recPd, isnull(y.recGuias,0) recGuias, \n"
                    + " isnull(y.recLacres,0) recLacres, isnull(y.recValor,0) recValor, isnull(y.entPdLacres,0) entPdLacres, isnull(y.ValorRecepCXF,0) ValorRecepCXF,\n"
                    + " isnull(y.ValorEntDir,0) ValorEntDir, isnull(y.ServAtrasados,0) ServAtrasados\n"
                    + " from escala \n"
                    + " left join funcion on funcion.matr = escala.matrche\n"
                    + "                   and funcion.codfil = escala.codfil  \n"
                    + " left join funcion funcionMot  on funcionMot.matr = escala.matrmot \n"
                    + "                              and funcionMot.codfil = escala.codfil  \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo\n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo\n"
                    + " left join (Select Sequencia, sum(entOk) entOk, sum(entPD) entPd, sum(entGuias) entGuias, sum(entLacres) entLacres, sum(entValor) entValor,\n"
                    + "sum(entPdGuias) entPdGuias, sum(entPdValor) entPdValor, sum(recOk) recOk, sum(recPd) recPd, sum(recguias) recGuias, sum(recLacres) recLacres,\n"
                    + " sum(recValor) recValor, Sum(entPdLacres) entPdLacres, sum(isnull(ValorRecepCXF,0)) ValorRecepCXF, sum(isnull(ValorEntDir,0)) ValorEntDir, \n"
                    + " sum(isnull(ServAtrasados,0)) ServAtrasados \n"
                    + "From (\n"
                    + "Select\n"
                    + "Rt_Perc.Sequencia, Rt_Perc.Parada,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Perc.Parada) else 0 end entOk,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then count(Distinct Rt_Perc.Parada) else 0 end entPd,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct Rt_Guias.Guia),0) else 0 end entGuias,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end entLacres,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(sum(Distinct Rt_Guias.Valor),0) else 0 end entValor,\n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then\n"
                    + "(Select isnull(Count(Distinct CxfGuias.Guia),0) from CxfGuias \n"
                    + "    where CxfGuias.SeqRotaSai = Rt_Perc.Sequencia and CxfGuias.Hora1D = Rt_Perc.Hora1) else 0 end entPdGuias,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then\n"
                    + "(Select isnull(Sum(Distinct CxfGuias.Valor),0) from CxfGuias\n"
                    + "    where CxfGuias.SeqRotaSai = Rt_Perc.Sequencia and CxfGuias.Hora1D = Rt_Perc.Hora1) else 0 end entPdValor,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end entPdLacres,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Perc.Parada) else 0 end recOk,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then count(Distinct Rt_Perc.Parada) else 0 end recPd,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Guias.Guia) else 0 end recGuias,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end recLacres,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(sum(Distinct Rt_Guias.Valor),0) else 0 end recValor,\n"
                    + "(Select Sum(Isnull(CxfGuias.Valor,0)) From Rotas y\n"
                    + " Left Join Rt_guias  on  Rt_Guias.Sequencia   = y.Sequencia\n"
                    + "                    and y.CodFil = rotas.codfil\n"
                    + " Left Join CxfGuias  on  CxfGuias.Guia    = Rt_Guias.Guia  \n"
                    + "                    and CxfGuias.Serie    = Rt_Guias.SerieAnt\n"
                    + "                    and CxfGuias.RotaEnt  = y.Rota \n"
                    + "                    and CXFGuias.Hora1    = Rt_Perc.Hora1 \n"
                    + "                    and CxfGuias.DtEnt = y.Data\n"
                    + "where y.Sequencia = Rt_Perc.Sequencia \n"
                    + "  and CxfGuias.DtEnt is not Null \n"
                    + ") ValorRecepCXF, \n"
                    + " (Select Sum(isnull(b.Valor,0)) From Rt_Guias b (noLock) \n"
                    + "  Left join Rt_Perc c (noLock) on b.Sequencia = c.Sequencia \n"
                    + "                      and b.Parada = c.Parada \n"
                    + "  Where b.Sequencia = Rt_Perc.Sequencia \n"
                    + "    and c.Hora1 = Rt_Perc.Hora1D \n"
                    + "    and Len(c.HrCheg) >= 5 \n"
                    + ") ValorEntDir,  \n"
                    + " (Select count(*) From Rt_Perc xa (nolock) \n"
                    + "   Where xa.Sequencia = Rt_Perc.Sequencia \n"
                    + "     and xa.Flag_Excl <> '*' \n"
                    + "	 and (len(xa.HrCheg) < 4 or xa.HrCheg is null) \n"
                    + "     and DateDiff(mi, Convert(Datetime, (Convert(varchar,Rotas.Data,112) + ' ' + Substring(xa.Hora1,1,2)+':'+Substring(xa.Hora1,3,2)+':00' )), getdate()) > 15 \n"
                    + "	 and Substring(xa.Hora1,1,2) between 00 and 23 \n"
                    + "	 and Substring(xa.Hora1,3,2) between 00 and 59 \n"
                    + ")  ServAtrasados From Rt_Perc \n"
                    + "Left join Rotas  on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join CxfGuiasVol  on CxfGuiasVol.Guia = Rt_Guias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = Rt_Guias.Serie\n"
                    + "Where Rotas.Data between ? and ? \n"
                    + " and Rt_Perc.Flag_Excl <> '*' \n";
            if (isTranspCacamba(persistencia.getEmpresa())) {
                sql += "AND Rt_Perc.CodCli1 NOT IN (SELECT Codigo FROM Clientes WHERE NRed LIKE '%aterro%' OR NRed LIKE '%base%' OR NRed LIKE '%manuten%')";
            }
            sql += "Group by Rt_Perc.ER, Rt_Perc.HrCheg, Rt_Perc.Parada, Rt_Perc.Sequencia, Rt_Perc.Parada, Rt_Perc.Hora1, Rotas.Data, Rotas.CodFil, Rt_Perc.Hora1D) z\n"
                    + "Group by z.Sequencia) y  on y.Sequencia = Escala.SeqRota  \n"
                    + "where escala.Data between ? and ? and escala.codFil = ?\n\n"
                    + "ORDER BY Escala.Data ASC \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicial);
            consulta.setString(dataFinal);
            consulta.setString(dataInicial);
            consulta.setString(dataFinal);
            consulta.setString(codFil);
            consulta.select();
            Rotas rota;
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setData(consulta.getString("Data"));
                rota.setRota(consulta.getString("rota"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setNome(consulta.getString("nome"));
                rota.setVeiculo(consulta.getString("veiculo"));
                rota.setSequencia(consulta.getString("seqrota"));
                rota.setPlaca(consulta.getString("placa"));
                rota.setModeloVeiculo(consulta.getString("modeloveic"));
                rota.setNome(consulta.getString("nomeche"));
                rota.setNomeMotorista(consulta.getString("nomemot"));
                rota.setEntOk(consulta.getString("entok"));
                rota.setEntPd(consulta.getString("entpd"));
                rota.setEntGuias(consulta.getString("entguias"));
                rota.setEntLacres(consulta.getString("entlacres"));
                rota.setEntValor(consulta.getString("entvalor"));
                rota.setEntPdGuias(consulta.getString("entpdguias"));
                rota.setEntPdValor(consulta.getString("entpdvalor"));
                rota.setRecOk(consulta.getString("recok"));
                rota.setRecPd(consulta.getString("recpd"));
                rota.setRecGuias(consulta.getString("recguias"));
                rota.setRecLacres(consulta.getString("reclacres"));
                rota.setRecValor(consulta.getString("recvalor"));
                rota.setEntPdLacres(consulta.getString("entpdlacres"));
//                rota.setValorecepcxf(consulta.getString("valorrecepcxf"));
//                rota.setValorentdir(consulta.getString("valorentdir"));
//                rota.setServatrasados(consulta.getString("servatrasados"));
                retorno.add(rota);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.resumoRotas - " + e.getMessage());
        }
    }

    /**
     * Lista todas as rotas que não sejam de supervisão do dia.
     *
     * @param codFil
     * @param dat
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rotas> listarRotasData(String codFil, String data, Persistencia persistencia) throws Exception {
        try {
            List<Rotas> retorno = new ArrayList<>();
            String sql = "Select Rotas.*"
                    + " FROM Rotas \n"
                    + " where Rotas.Data = ? and Rotas.TpVeic <> 'S' and Rotas.Flag_excl <> '*' \n"
                    + " and Rotas.CodFil = ? AND Rotas.TpVeic != 'N'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.select();
            Rotas rota;
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("Data"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));

                retorno.add(rota);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.listarRotasData - " + e.getMessage() + "\n\r"
                    + "Select Rotas.*"
                    + " FROM Rotas \n"
                    + " where Rotas.Data = " + data + " and Rotas.TpVeic <> 'S' and Rotas.Flag_excl <> '*' \n"
                    + " and Rotas.CodFil = " + codFil);
        }
    }

    /**
     * Busca o próximo número de rota para o SatMobWeb
     *
     * @param CodFil - Filial da rota
     * @param Data - Data da rota
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String proximaRotaSupervisao(String CodFil, LocalDate Data, Persistencia persistencia) throws Exception {
        try {
            Integer rota;
            String retorno = "000";
            String sql = "select max(rota) 'Rota' "
                    + " from rotas "
                    + " where codfil = ? "
                    + " and data = ? "
                    + " and flag_excl <> '*'"
                    + " and rota > 700";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.setDate(Date.valueOf(Data));
            consult.select();
            while (consult.Proximo()) {
                rota = consult.getInt("Rota");
                rota++;
                retorno = FuncoesString.PreencheEsquerda(String.valueOf(rota), 3, "0");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.proximaRotaSupervisao - " + e.getMessage() + "\r\n"
                    + "select max(rota) 'Rota' "
                    + " from rotas "
                    + " where codfil = " + CodFil
                    + " and data = " + Data
                    + " and flag_excl <> '*'"
                    + " and rota > 700");
        }
    }

    public String proximaRotaTransporte(String codFil, String data, Persistencia persistencia) throws Exception {
        try {
            Integer rota;
            String retorno = "000";
            String sql = "select max(rota) 'Rota' "
                    + " from rotas "
                    + " where codfil = ? "
                    + " and data = ? "
                    + " and flag_excl <> '*'"
                    + " and rota < 700";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codFil);
            consult.setString(data);
            consult.select();
            while (consult.Proximo()) {
                rota = consult.getInt("Rota");
                rota++;
                retorno = FuncoesString.PreencheEsquerda(String.valueOf(rota), 3, "0");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.proximaRotaSupervisao - " + e.getMessage() + "\r\n"
                    + "select max(rota) 'Rota' "
                    + " from rotas "
                    + " where codfil = " + codFil
                    + " and data = " + data
                    + " and flag_excl <> '*'"
                    + " and rota < 700");
        }
    }

    /**
     * Busca o próximo número de rota para o SatMobWeb
     *
     * @param Codfil Filial da rota
     * @param Data - Data da rota
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public BigDecimal SelecionaUltimaSequenciaSatMobWeb(BigDecimal Codfil, LocalDate Data, Persistencia persistencia) throws Exception {
        try {
            BigDecimal sequencia = null;
            String sql = "select max (sequencia) 'Sequencia' "
                    + " from rotas "
                    + " where codfil = ? "
                    + " and data = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(Codfil);
            consult.setDate(Date.valueOf(Data));
            consult.select();
            while (consult.Proximo()) {
                sequencia = consult.getBigDecimal("Sequencia");

            }
            consult.Close();
            return sequencia;
        } catch (Exception e) {
            throw new Exception("RotasDao.SelecionaUltimaSequenciaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "select max (sequencia) 'Sequencia' "
                    + " from rotas "
                    + " where codfil = " + Codfil
                    + " and data = " + Data);

        }
    }

    /**
     * Busca o próximo número de rota para o SatMobWeb
     *
     * @param sequencia Sequência da rota
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Rotas SelecionaUltimaRotaSatMobWeb(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            Rotas rota = new Rotas();
            String sql = "SELECT Rotas.*"
                    + " FROM rotas "
                    + " WHERE sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();
            while (consulta.Proximo()) {

                //rota.setNome(consulta.getString("supervisor"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("Data"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
            }
            consulta.Close();
            return rota;
        } catch (Exception e) {
            throw new Exception("RotasDao.SelecionaUltimaRotaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "SELECT Rotas.*"
                    + " FROM rotas "
                    + " WHERE sequencia = " + sequencia);
        }
    }

    /**
     * Busca rota a partir do número da rota ,Codfil e data
     *
     * @param codfil Código da filial
     * @param rotas Número da rota
     * @param data Data da rota
     * @param persistencia Conexão com o banco
     * @return Retorna rota
     * @throws Exception
     */
    public Rotas buscaRota(String codfil, String rotas, String data, Persistencia persistencia) throws Exception {

        try {
            Rotas rota = new Rotas();

            String sql = "SELECT Rotas.*"
                    + " FROM rotas "
                    + " WHERE codfil = ?"
                    + " AND rota = ? "
                    + " AND data = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(rotas);
            consulta.setString(data);
            consulta.select();

            while (consulta.Proximo()) {

                //rota.setNome(consulta.getString("supervisor"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("Data"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
            }
            consulta.Close();
            return rota;

        } catch (Exception e) {
            throw new Exception("RotasDao.BuscaRota - " + e.getMessage() + "\r\n"
                    + "SELECT Rotas.*"
                    + " FROM rotas "
                    + " WHERE codfil = " + codfil
                    + " AND rota = " + rotas
                    + " AND data = " + data);
        }
    }

    /**
     * Busca rota a partir do número da rota ,Codfil e data
     *
     * @param codfil Código da filial
     * @param rotas Número da rota
     * @param data Data da rota
     * @param persistencia Conexão com o banco
     * @return Retorna rota
     * @throws Exception
     */
    public String buscarSeqRota(String codfil, String rotas, String data, Persistencia persistencia) throws Exception {

        try {
            String sequencia = null;
            String sql = "SELECT Rotas.*"
                    + " FROM rotas "
                    + " WHERE codfil = ?"
                    + " AND rota = ? "
                    + " AND data = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(rotas);
            consulta.setString(data);
            consulta.select();

            if (consulta.Proximo()) {
                sequencia = consulta.getString("sequencia");
            }
            consulta.Close();
            return sequencia;
        } catch (Exception e) {
            throw new Exception("RotasDao.BuscaRota - " + e.getMessage() + "\r\n"
                    + "SELECT Rotas.*"
                    + " FROM rotas "
                    + " WHERE codfil = " + codfil
                    + " AND rota = " + rotas
                    + " AND data = " + data);
        }
    }

    /**
     * ************** LISTA PAGINADA *******************
     */
    /**
     * realiza a listagem das rotas de supervisão por data
     *
     * @param primeiro
     * @param linhas
     * @param codpessoa Código da pessoa para permissão de filiais
     * @param filtros
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os registros
     * @throws Exception
     */
    public List<Rotas> listaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList();
        try {
            String sql = "SELECT  *  FROM"
                    + "    ( SELECT    ROW_NUMBER() OVER ( ORDER BY hrlargada asc, rotas.rota desc, rotas.sequencia desc ) AS RowNum,"
                    + " Pessoa.Nome, Rotas.*, Escala.CodPessoaSup "
                    + " from  Rotas "
                    + " left join Escala on  Escala.SeqRota = Rotas.Sequencia"
                    + " and Escala.CodFil  = Rotas.CodFil    "
                    + " left join Pessoa on  Pessoa.Codigo  = Escala.CodPessoaSup    "
                    + " where Rotas.TpVeic = 'S' "
                    + " and Rotas.CodFil in "
                    + " (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and paramet.path = ?)";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }

            sql = sql + ") AS RowConstrainedResult"
                    + " WHERE RowNum >= ?"
                    + " AND RowNum < ?"
                    + " ORDER BY RowNum";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Rotas rota;
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("Data"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));
                rotas.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listaPaginada - " + e.getMessage());
        }
        return rotas;
    }

    /**
     * realiza a contagem das rotas de supervisão por data
     *
     * @param codpessoa Código da pessoa para permissão de filiais
     * @param filtros
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os registros
     * @throws Exception
     */
    public Integer TotalRotasMobWeb(Map filtros, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT count(*) total"
                    + " from  Rotas "
                    + " left join Escala on  Escala.SeqRota = Rotas.Sequencia"
                    + " and Escala.CodFil  = Rotas.CodFil    "
                    + " left join Pessoa on  Pessoa.Codigo  = Escala.CodPessoaSup    "
                    + " where Rotas.TpVeic = 'S' "
                    + " and Rotas.CodFil in "
                    + " (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and paramet.path = ?)";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.TotalRotasMobWeb - " + e.getMessage());
        }
    }

    public List<Rotas> listaPaginadaValores(int primeiro, int linhas, Map filtros, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList();
        try {
            String sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER ( ORDER BY rotas.rota asc ) AS RowNum, "
                    + " funcion.Nome, sum(CxfGuiasVol.Valor) valor , "
                    + " (Select Count(*) Qtde FROM Rt_Perc where Rt_Perc.Sequencia = Rotas.Sequencia) Qtde, Sum(CxfGuiasVol.Qtde) Volumes,  Rotas.*,"
                    + " CONVERT(VARCHAR, Rotas.Data, 112) DataRota \n"
                    + " from Rotas \n"
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                  and Escala.CodFil = Rotas.CodFil \n"
                    + " left join funcion on  funcion.matr = Escala.matrche \n"
                    + "                 and funcion.codfil = escala.codfil \n"
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia \n"
                    + " Left JOin Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia \n"
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada \n"
                    + " Left jOIn CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia \n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    //                    + " Left Join Rt_Perc Rt_PercDSt on Rt_PercDSt.Sequencia = Rt_perc.Sequencia \n"
                    //                    + "                                and Rt_PercDSt.Parada = Rt_perc.DPar \n"
                    //                    + "                             and Rt_PercDst.Flag_Excl <> '*'\n"
                    + " where Rotas.TpVeic != 'S'\n"
                    //                    + "      and (Rt_PercDst.HrBaixa is null or Rt_PercDst.HrBaixa = '') \n"
                    //                    + "      and SubString(Rt_PercDst.ER,1,1) = 'E' \n"
                    //                    + "      and Rt_perc.Valor is not Null \n"
                    + " and Rotas.CodFil in \n"
                    + " (select filiais.codfil \n"
                    + " from saspw\n"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome\n"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac\n"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil\n"
                    + " where saspw.codpessoa = ? and paramet.path = ?)\n";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND \n" + entrada.getKey();
                }
            }

            sql = sql + " GROUP BY rotas.rota, funcion.nome, rotas.sequencia, rotas.data, rotas.codfil, \n"
                    + " rotas.tpveic, rotas.viagem, rotas.atm, rotas.bacen, rotas.aeroporto, rotas.hrlargada, \n"
                    + " rotas.hrchegada, rotas.hrintini, rotas.hrintfim, rotas.hstotal, rotas.observacao, rotas.kmsaida, \n"
                    + " rotas.kmchegada, rotas.kmtotal, rotas.hrurb, rotas.hrinter, rotas.prdurb, rotas.prdinter, \n"
                    + " rotas.guiasurb, rotas.guiasinter, rotas.kmtotconj,rotas.kmtotconj2, rotas.operador,rotas.Dt_Alter, \n"
                    + " rotas.Hr_Alter,rotas.OperFech,rotas.Dt_Fech,rotas.Hr_Fech,rotas.Flag_Excl, rotas.DtFim ) \n"
                    + " AS RowConstrainedResult WHERE RowNum >= ? AND RowNum < ? \n"
                    + " ORDER BY RowNum \n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Rotas rota;
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("DataRota"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));
                rota.setValor(consulta.getBigDecimal("valor"));
                rota.setQtdTrajetos(consulta.getBigDecimal("Qtde"));
                rota.setQtdVolumes(consulta.getBigDecimal("Volumes"));
                rotas.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listaPaginadaValores - " + e.getMessage());
        }
        return rotas;
    }

    public List<Rotas> listaPaginadaValoresDtFim(int primeiro, int linhas, Map filtros, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList();
        try {
            String sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER ( ORDER BY rotas.rota asc ) AS RowNum, "
                    + " funcion.Nome, sum(CxfGuiasVol.Valor) valor , "
                    + " (Select Count(*) Qtde FROM Rt_Perc where Rt_Perc.Sequencia = Rotas.Sequencia) Qtde, Sum(CxfGuiasVol.Qtde) Volumes,  Rotas.*,"
                    + " CONVERT(VARCHAR, Rotas.Data, 112) DataRota \n"
                    + " from Rotas \n"
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                  and Escala.CodFil = Rotas.CodFil \n"
                    + " left join funcion on  funcion.matr = Escala.matrche \n"
                    + "                 and funcion.codfil = escala.codfil \n"
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia \n"
                    + " Left JOin Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia \n"
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada \n"
                    + " Left jOIn CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia \n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    //                    + " Left Join Rt_Perc Rt_PercDSt on Rt_PercDSt.Sequencia = Rt_perc.Sequencia \n"
                    //                    + "                                and Rt_PercDSt.Parada = Rt_perc.DPar \n"
                    //                    + "                             and Rt_PercDst.Flag_Excl <> '*'\n"
                    + " where Rotas.TpVeic != 'S'\n"
                    //                    + "      and (Rt_PercDst.HrBaixa is null or Rt_PercDst.HrBaixa = '') \n"
                    //                    + "      and SubString(Rt_PercDst.ER,1,1) = 'E' \n"
                    //                    + "      and Rt_perc.Valor is not Null \n"
                    + " and Rotas.CodFil in \n"
                    + " (select filiais.codfil \n"
                    + " from saspw\n"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome\n"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac\n"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil\n"
                    + " where saspw.codpessoa = ? and paramet.path = ?)\n";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND \n" + entrada.getKey();
                }
            }

            sql = sql + " GROUP BY rotas.dtFim, rotas.rota, funcion.nome, rotas.sequencia, rotas.data, rotas.codfil, \n"
                    + " rotas.tpveic, rotas.viagem, rotas.atm, rotas.bacen, rotas.aeroporto, rotas.hrlargada, \n"
                    + " rotas.hrchegada, rotas.hrintini, rotas.hrintfim, rotas.hstotal, rotas.observacao, rotas.kmsaida, \n"
                    + " rotas.kmchegada, rotas.kmtotal, rotas.hrurb, rotas.hrinter, rotas.prdurb, rotas.prdinter, \n"
                    + " rotas.guiasurb, rotas.guiasinter, rotas.kmtotconj,rotas.kmtotconj2, rotas.operador,rotas.Dt_Alter, \n"
                    + " rotas.Hr_Alter,rotas.OperFech,rotas.Dt_Fech,rotas.Hr_Fech,rotas.Flag_Excl) \n"
                    + " AS RowConstrainedResult WHERE RowNum >= ? AND RowNum < ? \n"
                    + " ORDER BY RowNum \n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Rotas rota;
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("DataRota"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));
                rota.setValor(consulta.getBigDecimal("valor"));
                rota.setQtdTrajetos(consulta.getBigDecimal("Qtde"));
                rota.setQtdVolumes(consulta.getBigDecimal("Volumes"));
                rotas.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listaPaginadaValores - " + e.getMessage());
        }
        return rotas;
    }

    public List<Rotas> listaPaginadaValores(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList();
        try {
            String sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER ( ORDER BY rotas.rota asc ) AS RowNum, "
                    + " funcion.Nome, ISNULL(sum(CxfGuiasVol.Valor),0) valor , "
                    + " (Select Count(*) Qtde FROM Rt_Perc where Rt_Perc.Sequencia = Rotas.Sequencia) Qtde, ISNULL(Sum(CxfGuiasVol.Qtde),0) Volumes,  Rotas.*,"
                    + " CONVERT(VARCHAR, Rotas.Data, 112) DataRota, escala.veiculo, veiculos.placa, VeiculosMod.Descricao ModeloVeic, mot.nome motNome \n"
                    + " from Rotas \n"
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                  and Escala.CodFil = Rotas.CodFil \n"
                    + " left join funcion on  funcion.matr = Escala.matrche \n"
                    + "                 and funcion.codfil = escala.codfil \n"
                    + " left join funcion mot on mot.matr   = escala.matrmot \n"
                    + "                      and mot.codfil = escala.codfil \n"
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia \n"
                    + " left join Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia \n"
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada \n"
                    + " left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia \n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " where Rotas.TpVeic != 'S'\n";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND \n" + entrada.getKey();
                }
            }

            sql = sql + " GROUP BY rotas.rota, funcion.nome, rotas.sequencia, rotas.data, rotas.codfil, \n"
                    + " rotas.tpveic, rotas.viagem, rotas.atm, rotas.bacen, rotas.aeroporto, rotas.hrlargada, \n"
                    + " rotas.hrchegada, rotas.hrintini, rotas.hrintfim, rotas.hstotal, rotas.observacao, rotas.kmsaida, \n"
                    + " rotas.kmchegada, rotas.kmtotal, rotas.hrurb, rotas.hrinter, rotas.prdurb, rotas.prdinter, \n"
                    + " rotas.guiasurb, rotas.guiasinter, rotas.kmtotconj,rotas.kmtotconj2, rotas.operador,rotas.Dt_Alter, \n"
                    + " rotas.Hr_Alter,rotas.OperFech,rotas.Dt_Fech,rotas.Hr_Fech,rotas.Flag_Excl, escala.veiculo, veiculos.placa, VeiculosMod.Descricao, mot.nome) \n"
                    + " AS RowConstrainedResult WHERE RowNum >= ? AND RowNum < ? \n"
                    + " ORDER BY RowNum \n";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Rotas rota;
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("DataRota"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));
                rota.setValor(consulta.getBigDecimal("valor"));
                rota.setQtdTrajetos(consulta.getBigDecimal("Qtde"));
                rota.setQtdVolumes(consulta.getBigDecimal("Volumes"));
                rota.setVeiculo(consulta.getString("veiculo").replace(".0", ""));
                rota.setModeloVeiculo(consulta.getString("ModeloVeic"));
                rota.setPlaca(consulta.getString("placa"));
                rota.setNomeMotorista(consulta.getString("motNome"));
                rotas.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listaPaginadaValores - " + e.getMessage());
        }
        return rotas;
    }

    public List<Rotas> listaPaginadaValoresDtFim(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList();
        try {
            String sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER ( ORDER BY rotas.rota asc ) AS RowNum, "
                    + " funcion.Nome, ISNULL(sum(CxfGuiasVol.Valor),0) valor , "
                    + " (Select Count(*) Qtde FROM Rt_Perc where Rt_Perc.Sequencia = Rotas.Sequencia) Qtde, \n"
                    + " ISNULL(Sum(CxfGuiasVol.Qtde),0) Volumes,  Rotas.*, \n"
                    + " CONVERT(VARCHAR, Rotas.Data, 112) DataRota, escala.veiculo, veiculos.placa, VeiculosMod.Descricao ModeloVeic, \n"
                    + " mot.nome motNome \n"
                    + " from Rotas \n"
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                  and Escala.CodFil = Rotas.CodFil \n"
                    + " left join funcion on  funcion.matr = Escala.matrche \n"
                    + "                 and funcion.codfil = escala.codfil \n"
                    + " left join funcion mot on mot.matr   = escala.matrmot \n"
                    + "                      and mot.codfil = escala.codfil \n"
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia \n"
                    + " left join Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia \n"
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada \n"
                    + " left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia \n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " where Rotas.TpVeic != 'S'\n";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND \n" + entrada.getKey();
                }
            }

            sql = sql + " GROUP BY rotas.rota, funcion.nome, rotas.sequencia, rotas.data, rotas.codfil, \n"
                    + " rotas.DtFim, rotas.tpveic, rotas.viagem, rotas.atm, rotas.bacen, rotas.aeroporto, rotas.hrlargada, \n"
                    + " rotas.hrchegada, rotas.hrintini, rotas.hrintfim, rotas.hstotal, rotas.observacao, rotas.kmsaida, \n"
                    + " rotas.kmchegada, rotas.kmtotal, rotas.hrurb, rotas.hrinter, rotas.prdurb, rotas.prdinter, \n"
                    + " rotas.guiasurb, rotas.guiasinter, rotas.kmtotconj,rotas.kmtotconj2, rotas.operador,rotas.Dt_Alter, \n"
                    + " rotas.Hr_Alter,rotas.OperFech,rotas.Dt_Fech,rotas.Hr_Fech,rotas.Flag_Excl, escala.veiculo, veiculos.placa, VeiculosMod.Descricao, mot.nome) \n"
                    + " AS RowConstrainedResult WHERE RowNum >= ? AND RowNum < ? \n"
                    + " ORDER BY RowNum \n";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Rotas rota;
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("DataRota"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));
                rota.setValor(consulta.getBigDecimal("valor"));
                rota.setQtdTrajetos(consulta.getBigDecimal("Qtde"));
                rota.setQtdVolumes(consulta.getBigDecimal("Volumes"));
                rota.setVeiculo(consulta.getString("veiculo").replace(".0", ""));
                rota.setModeloVeiculo(consulta.getString("ModeloVeic"));
                rota.setPlaca(consulta.getString("placa"));
                rota.setNomeMotorista(consulta.getString("motNome"));
                rotas.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listaPaginadaValores - " + e.getMessage());
        }
        return rotas;
    }

    public Integer totalRotasValores(Map filtros, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT (Select Count(*) from Rotas TRotas where TRotas.Data = Rotas.Data and TRotas.codFil = Rotas.CoDFil) as total "
                    + " from  Rotas "
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia "
                    + " left join Escala on  Escala.SeqRota = Rotas.Sequencia"
                    + " and Escala.CodFil  = Rotas.CodFil    "
                    + " left join Pessoa on  Pessoa.Codigo  = Escala.CodPessoaSup    "
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " where Rotas.TpVeic != 'S' "
                    + " and Rotas.CodFil in "
                    + " (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and paramet.path = ?)";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql += " Group by Rotas.Data, Rotas.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.totalRotasValores - " + e.getMessage());
        }
    }

    public List<Rotas> listaPaginadaSPMValoresDtFim(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList();
        try {
            String sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER ( ORDER BY rotas.rota asc ) AS RowNum, "
                    + " funcion.Nome, ISNULL(sum(CxfGuiasVol.Valor),0) valor , "
                    + " (Select Count(*) Qtde FROM Rt_Perc where Rt_Perc.Sequencia = Rotas.Sequencia) Qtde, \n"
                    + " ISNULL(Sum(CxfGuiasVol.Qtde),0) Volumes,  Rotas.*, \n"
                    + " CONVERT(VARCHAR, Rotas.Data, 112) DataRota, escala.veiculo, veiculos.placa, VeiculosMod.Descricao ModeloVeic, \n"
                    + " mot.nome motNome \n"
                    + " from Rotas \n"
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                  and Escala.CodFil = Rotas.CodFil \n"
                    + " left join funcion on  funcion.matr = Escala.matrche \n"
                    + "                 and funcion.codfil = escala.codfil \n"
                    + " left join funcion mot on mot.matr   = escala.matrmot \n"
                    + "                      and mot.codfil = escala.codfil \n"
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia \n"
                    + " left join Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia \n"
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada \n"
                    + " left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia \n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " where Rotas.TpVeic != 'S'\n";

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            sql = sql + " GROUP BY rotas.rota, funcion.nome, rotas.sequencia, rotas.data, rotas.codfil, \n"
                    + " rotas.DtFim, rotas.tpveic, rotas.viagem, rotas.atm, rotas.bacen, rotas.aeroporto, rotas.hrlargada, \n"
                    + " rotas.hrchegada, rotas.hrintini, rotas.hrintfim, rotas.hstotal, rotas.observacao, rotas.kmsaida, \n"
                    + " rotas.kmchegada, rotas.kmtotal, rotas.hrurb, rotas.hrinter, rotas.prdurb, rotas.prdinter, \n"
                    + " rotas.guiasurb, rotas.guiasinter, rotas.kmtotconj,rotas.kmtotconj2, rotas.operador,rotas.Dt_Alter, \n"
                    + " rotas.Hr_Alter,rotas.OperFech,rotas.Dt_Fech,rotas.Hr_Fech,rotas.Flag_Excl, escala.veiculo, veiculos.placa, VeiculosMod.Descricao, mot.nome) \n"
                    + " AS RowConstrainedResult WHERE RowNum >= ? AND RowNum < ? \n"
                    + " ORDER BY RowNum \n";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consulta.setString(entry);
                    }
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Rotas rota;
            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("DataRota"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));
                rota.setValor(consulta.getBigDecimal("valor"));
                rota.setQtdTrajetos(consulta.getBigDecimal("Qtde"));
                rota.setQtdVolumes(consulta.getBigDecimal("Volumes"));
                rota.setVeiculo(consulta.getString("veiculo").replace(".0", ""));
                rota.setModeloVeiculo(consulta.getString("ModeloVeic"));
                rota.setPlaca(consulta.getString("placa"));
                rota.setNomeMotorista(consulta.getString("motNome"));
                rotas.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listaPaginadaValores - " + e.getMessage());
        }
        return rotas;
    }

    public Rotas listaPaginadaSPMValoresDtFimModal(BigDecimal SeqRota, Persistencia persistencia) throws Exception {
        Rotas rota = null;
        try {
            String sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER ( ORDER BY rotas.rota asc ) AS RowNum, "
                    + " funcion.Nome, ISNULL(sum(CxfGuiasVol.Valor),0) valor , "
                    + " (Select Count(*) Qtde FROM Rt_Perc where Rt_Perc.Sequencia = Rotas.Sequencia) Qtde, \n"
                    + " ISNULL(Sum(CxfGuiasVol.Qtde),0) Volumes,  Rotas.*, \n"
                    + " CONVERT(VARCHAR, Rotas.Data, 112) DataRota, escala.veiculo, veiculos.placa, VeiculosMod.Descricao ModeloVeic, \n"
                    + " mot.nome motNome \n"
                    + " from Rotas \n"
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                  and Escala.CodFil = Rotas.CodFil \n"
                    + " left join funcion on  funcion.matr = Escala.matrche \n"
                    + "                 and funcion.codfil = escala.codfil \n"
                    + " left join funcion mot on mot.matr   = escala.matrmot \n"
                    + "                      and mot.codfil = escala.codfil \n"
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia \n"
                    + " left join Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia \n"
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada \n"
                    + " left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia \n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " where Rotas.TpVeic != 'S'\n"
                    + " and Rotas.Sequencia = ?";

            sql = sql + " GROUP BY rotas.rota, funcion.nome, rotas.sequencia, rotas.data, rotas.codfil, \n"
                    + " rotas.DtFim, rotas.tpveic, rotas.viagem, rotas.atm, rotas.bacen, rotas.aeroporto, rotas.hrlargada, \n"
                    + " rotas.hrchegada, rotas.hrintini, rotas.hrintfim, rotas.hstotal, rotas.observacao, rotas.kmsaida, \n"
                    + " rotas.kmchegada, rotas.kmtotal, rotas.hrurb, rotas.hrinter, rotas.prdurb, rotas.prdinter, \n"
                    + " rotas.guiasurb, rotas.guiasinter, rotas.kmtotconj,rotas.kmtotconj2, rotas.operador,rotas.Dt_Alter, \n"
                    + " rotas.Hr_Alter,rotas.OperFech,rotas.Dt_Fech,rotas.Hr_Fech,rotas.Flag_Excl, escala.veiculo, veiculos.placa, VeiculosMod.Descricao, mot.nome) \n"
                    + " AS RowConstrainedResult \n"
                    + " ORDER BY RowNum \n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(SeqRota);
            consulta.select();

            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("DataRota"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));
                rota.setValor(consulta.getBigDecimal("valor"));
                rota.setQtdTrajetos(consulta.getBigDecimal("Qtde"));
                rota.setQtdVolumes(consulta.getBigDecimal("Volumes"));
                rota.setVeiculo(consulta.getString("veiculo").replace(".0", ""));
                rota.setModeloVeiculo(consulta.getString("ModeloVeic"));
                rota.setPlaca(consulta.getString("placa"));
                rota.setNomeMotorista(consulta.getString("motNome"));
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listaPaginadaValores - " + e.getMessage());
        }
        return rota;
    }

    public List<Rotas> listaTrajetosData(String Data, String CodFil, String Rota, Persistencia persistencia) throws Exception {
        List<Rotas> rotasLista = new ArrayList();
        Rotas rota = null;

        try {
            String sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER ( ORDER BY rotas.rota asc ) AS RowNum, "
                    + " funcion.Nome, ISNULL(sum(CxfGuiasVol.Valor),0) valor , "
                    + " (Select Count(*) Qtde FROM Rt_Perc where Rt_Perc.Sequencia = Rotas.Sequencia) Qtde, \n"
                    + " ISNULL(Sum(CxfGuiasVol.Qtde),0) Volumes,  Rotas.*, \n"
                    + " CONVERT(VARCHAR, Rotas.Data, 112) DataRota, escala.veiculo, veiculos.placa, VeiculosMod.Descricao ModeloVeic, \n"
                    + " mot.nome motNome \n"
                    + " from Rotas \n"
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                  and Escala.CodFil = Rotas.CodFil \n"
                    + " left join funcion on  funcion.matr = Escala.matrche \n"
                    + "                 and funcion.codfil = escala.codfil \n"
                    + " left join funcion mot on mot.matr   = escala.matrmot \n"
                    + "                      and mot.codfil = escala.codfil \n"
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia \n"
                    + " left join Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia \n"
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada \n"
                    + " left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia \n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " where Rotas.TpVeic NOT IN('S','N')\n";
            if (!Rota.equals("")) {
                sql += " and   Rotas.Rota   = ?\n";
            }
            sql += " and   Rotas.Data   = ?\n"
                    + " and   Rotas.CodFil = ?\n";

            sql = sql + " GROUP BY rotas.rota, funcion.nome, rotas.sequencia, rotas.data, rotas.codfil, \n"
                    + " rotas.DtFim, rotas.tpveic, rotas.viagem, rotas.atm, rotas.bacen, rotas.aeroporto, rotas.hrlargada, \n"
                    + " rotas.hrchegada, rotas.hrintini, rotas.hrintfim, rotas.hstotal, rotas.observacao, rotas.kmsaida, \n"
                    + " rotas.kmchegada, rotas.kmtotal, rotas.hrurb, rotas.hrinter, rotas.prdurb, rotas.prdinter, \n"
                    + " rotas.guiasurb, rotas.guiasinter, rotas.kmtotconj,rotas.kmtotconj2, rotas.operador,rotas.Dt_Alter, \n"
                    + " rotas.Hr_Alter,rotas.OperFech,rotas.Dt_Fech,rotas.Hr_Fech,rotas.Flag_Excl, escala.veiculo, veiculos.placa, VeiculosMod.Descricao, mot.nome) \n"
                    + " AS RowConstrainedResult \n"
                    + " ORDER BY RowNum \n";

            Consulta consulta = new Consulta(sql, persistencia);

            if (!Rota.equals("")) {
                consulta.setString(Rota);
            }
            consulta.setString(Data);
            consulta.setString(CodFil);

            consulta.select();

            while (consulta.Proximo()) {
                rota = new Rotas();
                rota.setNome(consulta.getString("nome"));
                rota.setSequencia(consulta.getString("sequencia"));
                rota.setRota(consulta.getString("rota"));
                rota.setData(consulta.getString("DataRota"));
                rota.setCodFil(consulta.getString("codfil"));
                rota.setTpVeic(consulta.getString("tpveic"));
                rota.setViagem(consulta.getString("viagem"));
                rota.setATM(consulta.getString("ATM"));
                rota.setBACEN(consulta.getString("BACEN"));
                rota.setAeroporto(consulta.getString("aeroporto"));
                rota.setHrLargada(consulta.getString("hrlargada"));
                rota.setHrChegada(consulta.getString("hrchegada"));
                rota.setHrIntFim(consulta.getString("hrintfim"));
                rota.setHrIntIni(consulta.getString("hrintini"));
                rota.setHsTotal(consulta.getString("hstotal"));
                rota.setObservacao(consulta.getString("observacao"));
                rota.setKmSaida(consulta.getString("kmsaida"));
                rota.setKmChegada(consulta.getString("kmchegada"));
                rota.setKmTotal(consulta.getString("kmtotal"));
                rota.setHrUrb(consulta.getString("hrurb"));
                rota.setHrInter(consulta.getString("hrinter"));
                rota.setPrdUrb(consulta.getString("prdurb"));
                rota.setPrdInter(consulta.getString("prdinter"));
                rota.setGuiasUrb(consulta.getString("guiasurb"));
                rota.setGuiasInter(consulta.getString("guiasinter"));
                rota.setKmTotConj(consulta.getString("kmtotconj"));
                rota.setKmTotConj2(consulta.getString("kmtotconj2"));
                rota.setOperador(consulta.getString("operador"));
                rota.setDt_Alter(consulta.getLocalDate("dt_alter"));
                rota.setHr_Alter(consulta.getString("hr_alter"));
                rota.setFlag_Excl(consulta.getString("flag_excl"));
                rota.setValor(consulta.getBigDecimal("valor"));
                rota.setQtdTrajetos(consulta.getBigDecimal("Qtde"));
                rota.setQtdVolumes(consulta.getBigDecimal("Volumes"));
                rota.setVeiculo(consulta.getString("veiculo").replace(".0", ""));
                rota.setModeloVeiculo(consulta.getString("ModeloVeic"));
                rota.setPlaca(consulta.getString("placa"));
                rota.setNomeMotorista(consulta.getString("motNome"));

                rotasLista.add(rota);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("RotasDao.listaTrajetosData - " + e.getMessage());
        }
        return rotasLista;
    }

    public Integer totalSPMRotasValores(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT count(*) total FROM (SELECT ROW_NUMBER() OVER ( ORDER BY rotas.rota asc ) AS RowNum, "
                    + " funcion.Nome, ISNULL(sum(CxfGuiasVol.Valor),0) valor , "
                    + " (Select Count(*) Qtde FROM Rt_Perc where Rt_Perc.Sequencia = Rotas.Sequencia) Qtde, \n"
                    + " ISNULL(Sum(CxfGuiasVol.Qtde),0) Volumes,  Rotas.*, \n"
                    + " CONVERT(VARCHAR, Rotas.Data, 112) DataRota, escala.veiculo, veiculos.placa, VeiculosMod.Descricao ModeloVeic, \n"
                    + " mot.nome motNome \n"
                    + " from Rotas \n"
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                  and Escala.CodFil = Rotas.CodFil \n"
                    + " left join funcion on  funcion.matr = Escala.matrche \n"
                    + "                 and funcion.codfil = escala.codfil \n"
                    + " left join funcion mot on mot.matr   = escala.matrmot \n"
                    + "                      and mot.codfil = escala.codfil \n"
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia \n"
                    + " left join Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia \n"
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada \n"
                    + " left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia \n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " where Rotas.TpVeic != 'S'\n";

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            sql = sql + " GROUP BY rotas.rota, funcion.nome, rotas.sequencia, rotas.data, rotas.codfil, \n"
                    + " rotas.DtFim, rotas.tpveic, rotas.viagem, rotas.atm, rotas.bacen, rotas.aeroporto, rotas.hrlargada, \n"
                    + " rotas.hrchegada, rotas.hrintini, rotas.hrintfim, rotas.hstotal, rotas.observacao, rotas.kmsaida, \n"
                    + " rotas.kmchegada, rotas.kmtotal, rotas.hrurb, rotas.hrinter, rotas.prdurb, rotas.prdinter, \n"
                    + " rotas.guiasurb, rotas.guiasinter, rotas.kmtotconj,rotas.kmtotconj2, rotas.operador,rotas.Dt_Alter, \n"
                    + " rotas.Hr_Alter,rotas.OperFech,rotas.Dt_Fech,rotas.Hr_Fech,rotas.Flag_Excl, escala.veiculo, veiculos.placa, VeiculosMod.Descricao, mot.nome) \n"
                    + " AS RowConstrainedResult \n";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consulta.setString(entry);
                    }
                }
            }
            consulta.select();

            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.totalRotasValores - " + e.getMessage());
        }
    }

    public Integer totalRotasValores(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT (Select Count(*) from Rotas TRotas where TRotas.Data = Rotas.Data and TRotas.codFil = Rotas.CoDFil) as total "
                    + " from  Rotas "
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia "
                    + " left join Escala on  Escala.SeqRota = Rotas.Sequencia"
                    + " and Escala.CodFil  = Rotas.CodFil    "
                    + " left join Pessoa on  Pessoa.Codigo  = Escala.CodPessoaSup    "
                    + " where Rotas.TpVeic != 'S' ";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql += " Group by Rotas.Data, Rotas.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.totalRotasValores - " + e.getMessage());
        }
    }

    public BigDecimal totalValores(Map filtros, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT sum(CxfGuiasVol.valor) valor "
                    + " from  Rotas "
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia "
                    + " Left JOin Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia "
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada "
                    + " Left jOIn CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia "
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie "
                    + " Left Join Rt_Perc Rt_PercDSt on Rt_PercDSt.Sequencia = Rt_perc.Sequencia "
                    + "                                and Rt_PercDSt.Parada = Rt_perc.DPar "
                    + "                             and Rt_PercDst.Flag_Excl <> '*' "
                    + " where Rotas.TpVeic != 'S' "
                    + "      and Rt_perc.Valor is not Null "
                    + "      and (Rt_PercDst.HrBaixa is null or Rt_PercDst.HrBaixa = '') "
                    + "      and SubString(Rt_PercDst.ER,1,1) = 'E' "
                    + "      and Rotas.CodFil in "
                    + " (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and paramet.path = ?)";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }

            sql += " Group by Rotas.Data, Rotas.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            BigDecimal retorno = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                retorno = consulta.getBigDecimal("valor");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.totalValores - " + e.getMessage());
        }
    }

    public BigDecimal totalValoresSemCodPessoa(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT sum(CxfGuiasVol.valor) valor "
                    + " from  Rotas "
                    + " left join rt_perc on rt_perc.sequencia = rotas.sequencia "
                    + " Left JOin Rt_Guias on Rt_Guias.Sequencia = Rotas.Sequencia "
                    + "                      and Rt_Guias.Parada = Rt_perc.PArada "
                    + " Left jOIn CxfGuiasVol on CxfGuiasVol.Guia = Rt_Guias.Guia "
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie "
                    + " Left Join Rt_Perc Rt_PercDSt on Rt_PercDSt.Sequencia = Rt_perc.Sequencia "
                    + "                                and Rt_PercDSt.Parada = Rt_perc.DPar "
                    + "                             and Rt_PercDst.Flag_Excl <> '*' "
                    + " where Rotas.TpVeic != 'S' "
                    + "      and Rt_perc.Valor is not Null "
                    + "      and (Rt_PercDst.HrBaixa is null or Rt_PercDst.HrBaixa = '') "
                    + "      and SubString(Rt_PercDst.ER,1,1) = 'E' "
                    + "      and Rotas.CodFil in "
                    + " (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where paramet.path = ?)";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }

            sql += " Group by Rotas.Data, Rotas.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            BigDecimal retorno = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                retorno = consulta.getBigDecimal("valor");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.totalValores - " + e.getMessage());
        }
    }

    public List<Rt_Perc> relatorioProdutividade(String data, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> rt_perc = new ArrayList<>();
            String sql = "Select Rotas.Data, Rotas.Rota, Rt_Perc.Nred Origem, Rt_perc.ER, Rt_percDst.Nred Destino, Substring(Rt_perc.Hora1,1,2)+':'+SubsTring(Rt_perc.Hora1,3,2) HrProg, Rt_Perc.HrCheg,  Rt_perc.HrSaida, \n"
                    + "Isnull((Select RtpA.Atraso from Rt_Perc RtpA where RtpA.Sequencia = Rt_perc.Sequencia and RtpA.Parada = Rt_perc.Parada and RtpA.Atraso >= 0),0) Atraso, Isnull((Select RtpA.Atraso*(-1) from Rt_Perc RtpA where RtpA.Sequencia = Rt_perc.Sequencia and RtpA.Parada = Rt_perc.Parada and RtpA.Atraso <= 0),0) Antecipado, Rt_perc.TempoEspera, Rt_PercSLA.HrChegVei, Rt_percSLA.HrSaidaVei, isnull((Select SUm(valor) from Rt_Guias where Sequencia = Rotas.Sequencia and Parada = Rt_perc.Parada),0) Valor, \n"
                    + "isnull(DATEDIFF(minute, Convert(time,Rt_Perc.HrCheg) , Convert(time,Rt_PercSLA.HrChegVei)),0) + \n"
                    + "isnull(DateDiff(minute,Convert(time,Rt_percSLA.HrSaidaVei), convert(time,Rt_Perc.HrSaida)),0) HsImprod \n"
                    + "from Rotas\n"
                    + "Left Join Rt_Perc on  Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "                  and Rt_perc.Flag_Excl <> '*'\n"
                    + "Left Join Rt_Perc Rt_PercDst on  Rt_PercDst.Sequencia = Rt_perc.Sequencia\n"
                    + "                             and Rt_percDSt.parada = Rt_Perc.DPar\n"
                    + "                             and Rt_percDst.Flag_Excl <> '*'\n"
                    + "Left Join Rt_PercSLA on  RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "where Rotas.Data = ? \n"
                    + "  and Rotas.CodFil = ? \n"
                    + "  and Rt_percSLA.HrCheg <> '' \n"
                    + "  and Rotas.Flag_Excl <> '*' \n"
                    + "order by Rotas.Data, RT_perc.Hora1";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.select();
            Rt_Perc trajeto;
            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();
                trajeto.setData(consulta.getString("Data"));
                trajeto.setRota(consulta.getString("Rota"));
                trajeto.setNRed(consulta.getString("Origem"));
                trajeto.setER(consulta.getString("ER"));
                trajeto.setNRedDst(consulta.getString("Destino"));
                trajeto.setHora1(consulta.getString("HrProg"));
                trajeto.setHrCheg(consulta.getString("Hrcheg"));
                trajeto.setHrSaida(consulta.getString("HrSaida"));
                trajeto.setAtraso(consulta.getString("Atraso"));
                trajeto.setAntecipado(consulta.getString("Antecipado"));
                trajeto.setTempoEspera(consulta.getString("TempoEspera"));
                trajeto.setHsImprod(consulta.getString("HsImprod"));
                trajeto.setValor(consulta.getString("Valor"));
                trajeto.setHsImprod(consulta.getString("HsImprod"));
                rt_perc.add(trajeto);
            }
            consulta.Close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("RotasDao.RelatorioProdutividade - " + e.getMessage() + "\r\n"
                    + "Select Rotas.Data, Rotas.Rota, Rt_Perc.Nred Origem, Rt_perc.ER, Rt_percDst.Nred Destino, Substring(Rt_perc.Hora1,1,2)+':'+SubsTring(Rt_perc.Hora1,3,2) HrProg, Rt_Perc.HrCheg,  Rt_perc.HrSaida, \n"
                    + "Isnull((Select RtpA.Atraso from Rt_Perc RtpA where RtpA.Sequencia = Rt_perc.Sequencia and RtpA.Parada = Rt_perc.Parada and RtpA.Atraso >= 0),0) Atraso, Isnull((Select RtpA.Atraso*(-1) from Rt_Perc RtpA where RtpA.Sequencia = Rt_perc.Sequencia and RtpA.Parada = Rt_perc.Parada and RtpA.Atraso <= 0),0) Antecipado, Rt_perc.TempoEspera, Rt_PercSLA.HrChegVei, Rt_percSLA.HrSaidaVei, isnull((Select SUm(valor) from Rt_Guias where Sequencia = Rotas.Sequencia and Parada = Rt_perc.Parada),0) Valor, \n"
                    + "isnull(DATEDIFF(minute, Convert(time,Rt_Perc.HrCheg) , Convert(time,Rt_PercSLA.HrChegVei)),0) + \n"
                    + "isnull(DateDiff(minute,Convert(time,Rt_percSLA.HrSaidaVei), convert(time,Rt_Perc.HrSaida)),0) HsImprod \n"
                    + "from Rotas\n"
                    + "Left Join Rt_Perc on  Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "                  and Rt_perc.Flag_Excl <> '*'\n"
                    + "Left Join Rt_Perc Rt_PercDst on  Rt_PercDst.Sequencia = Rt_perc.Sequencia\n"
                    + "                             and Rt_percDSt.parada = Rt_Perc.DPar\n"
                    + "                             and Rt_percDst.Flag_Excl <> '*'\n"
                    + "Left Join Rt_PercSLA on  RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "where Rotas.Data = " + data + " \n"
                    + "  and Rotas.CodFil = " + codFil + " \n"
                    + "  and Rt_percSLA.HrCheg <> '' \n"
                    + "  and Rotas.Flag_Excl <> '*' \n"
                    + "order by Rotas.Data, RT_perc.Hora1");
        }
    }

    public List<Rt_Perc> detalhesRotasData(String data, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> rt_perc = new ArrayList<>();
            String sql = "Select Rotas.Sequencia, Rotas.Data, Rotas.Rota,(Select Count(Rt_perc.ER) from Rt_perc where Sequencia = Rotas.Sequencia and ER = 'E' Group By Rt_perc.ER) Entregas,(Select Count(Rt_perc.ER) from Rt_perc where Sequencia = Rotas.Sequencia and ER = 'R' Group By Rt_perc.ER) Recolhimentos, MIn(Substring(Rt_perc.Hora1,1,2)+':'+SubsTring(Rt_perc.Hora1,3,2)) HrInicio, Max(Substring(Rt_perc.Hora1,1,2)+':'+SubsTring(Rt_perc.Hora1,3,2)) HrFinal, \n"
                    + " Isnull((Select sum(RtpA.Atraso) from Rt_Perc RtpA where RtpA.Sequencia = Rotas.Sequencia and isnull(RtpA.Atraso,0) >= 0),0) Atraso, \n"
                    + "	Isnull((Select sum(RtpA.Atraso*(-1)) from Rt_Perc RtpA where RtpA.Sequencia = Rotas.Sequencia and isnull(RtpA.Atraso,0) <= 0),0) Antecipado, \n"
                    + "Sum(Rt_perc.TempoEspera) TempoCliente, isnull((Select SUm(valor) from Rt_Guias where Sequencia = Rotas.Sequencia),0) Valor from Rotas\n"
                    + "Left Join Rt_Perc on  Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "                  and Rt_perc.Flag_Excl <> '*'\n"
                    + "Left Join Rt_Perc Rt_PercDst on  Rt_PercDst.Sequencia = Rt_perc.Sequencia\n"
                    + "                             and Rt_percDSt.parada = Rt_Perc.DPar\n"
                    + "                             and Rt_percDst.Flag_Excl <> '*'\n"
                    + "Left Join Rt_PercSLA on  RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "where Rotas.Data = ? \n"
                    + "  and Rotas.CodFil = ? \n"
                    + "  and Rt_percSLA.HrCheg <> ''\n"
                    + "  and Rotas.Flag_Excl <> '*'\n"
                    + "Group by  Rotas.Sequencia, Rotas.Data, Rotas.Rota\n"
                    + "order by Rotas.Sequencia, Rotas.Data";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.select();
            Rt_Perc trajeto;
            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();
                trajeto.setData(consulta.getString("Data"));
                trajeto.setRota(consulta.getString("Rota"));
                trajeto.setEntregas(consulta.getString("Entregas"));
                trajeto.setRecolhimentos(consulta.getString("Recolhimentos"));
                trajeto.setHrCheg(consulta.getString("HrInicio"));
                trajeto.setHrSaida(consulta.getString("HrFinal"));
                trajeto.setAtraso(consulta.getString("Atraso"));
                trajeto.setAntecipado(consulta.getString("Antecipado"));
                trajeto.setTempoEspera(consulta.getString("TempoCliente"));
                trajeto.setValor(consulta.getString("Valor"));
                rt_perc.add(trajeto);
            }
            consulta.Close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("RotasDao.detalhesRotasData - " + e.getMessage() + "\r\n"
                    + "Select Rotas.Sequencia, Rotas.Data, Rotas.Rota,(Select Count(Rt_perc.ER) from Rt_perc where Sequencia = Rotas.Sequencia and ER = 'E' Group By Rt_perc.ER) Entregas,(Select Count(Rt_perc.ER) from Rt_perc where Sequencia = Rotas.Sequencia and ER = 'R' Group By Rt_perc.ER) Recolhimentos, MIn(Substring(Rt_perc.Hora1,1,2)+':'+SubsTring(Rt_perc.Hora1,3,2)) HrInicio, Max(Substring(Rt_perc.Hora1,1,2)+':'+SubsTring(Rt_perc.Hora1,3,2)) HrFinal, Sum(Rt_perc.Atraso) Atrasos, Sum(Rt_perc.TempoEspera) TempoCliente, isnull((Select SUm(valor) from Rt_Guias where Sequencia = Rotas.Sequencia),0) Valor from Rotas\n"
                    + "Left Join Rt_Perc on  Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "                  and Rt_perc.Flag_Excl <> '*'\n"
                    + "Left Join Rt_Perc Rt_PercDst on  Rt_PercDst.Sequencia = Rt_perc.Sequencia\n"
                    + "                             and Rt_percDSt.parada = Rt_Perc.DPar\n"
                    + "                             and Rt_percDst.Flag_Excl <> '*'\n"
                    + "Left Join Rt_PercSLA on  RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "where Rotas.Data = " + data + " \n"
                    + "  and Rotas.CodFil = " + codFil + " \n"
                    + "  and Rt_percSLA.HrCheg <> ''\n"
                    + "  and Rotas.Flag_Excl <> '*'\n"
                    + "Group by  Rotas.Sequencia, Rotas.Data, Rotas.Rota\n"
                    + "order by Rotas.Sequencia, Rotas.Data");
        }
    }

    public List<Rt_Perc> resultadoRoteiros(String data, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> rt_perc = new ArrayList<>();
            String sql = "Select \n"
                    + "Rotas.Rota, Convert(BigInt,Rotas.Sequencia) Sequencia, Rotas.CodFil, Rotas.Data, Convert(BigInt,Rt_Perc.Parada) Parada, Rt_Perc.Nred Cliente,  \n"
                    + "Rt_PercSLA.HrCheg, Rt_PercSLA.HrSaida, Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei,\n"
                    + "Rt_Guias.Guia, Rt_Guias.Serie, Rt_Perc.Valor, Rt_Perc.ER \n"
                    + "From Rotas \n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "inner Join Rt_PercSLA  on RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "where Rotas.data = ? \n"
                    + "  and Rotas.CodFil = ? \n"
                    + "Order by Rotas.CodFil, Rotas.Rota";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.select();
            Rt_Perc trajeto;
            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();
                trajeto.setData(consulta.getString("Data"));
                trajeto.setRota(consulta.getString("Rota"));
                trajeto.setSequencia(consulta.getString("Sequencia"));
                trajeto.setCodFil(consulta.getString("CodFil"));
                trajeto.setParada(consulta.getInt("Parada"));
                trajeto.setNRed(consulta.getString("Cliente"));
                trajeto.setER(consulta.getString("ER"));
                trajeto.setHrCheg(consulta.getString("HrCheg"));
                trajeto.setHrSaida(consulta.getString("HrSaida"));
                trajeto.setHrChegVei(consulta.getString("HrChegVei"));
                trajeto.setHrSaidaVei(consulta.getString("HrSaidaVei"));
                trajeto.setGuia(consulta.getString("Guia"));
                trajeto.setSerie(consulta.getString("Serie"));
                trajeto.setValor(consulta.getString("Valor"));
                rt_perc.add(trajeto);
            }
            consulta.Close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("RotasDao.resultadoRoteiros - " + e.getMessage() + "\r\n"
                    + "Select \n"
                    + "Rotas.Rota, Rotas.Sequencia, Rotas.CodFil, Rotas.Data, Rt_Perc.Parada, Rt_Perc.Nred Cliente,  \n"
                    + "Rt_PercSLA.HrCheg, Rt_PercSLA.HrSaida, Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei,\n"
                    + "Rt_Guias.Guia, Rt_Guias.Serie, Rt_Perc.Valor, Rt_Perc.ER\n"
                    + "From Rotas \n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "inner Join Rt_PercSLA  on RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "where Rotas.data = " + data + " \n"
                    + "  and Rotas.CodFil = " + codFil + " \n"
                    + "Order by Rotas.CodFil, Rotas.Rota");
        }
    }

    public List<Rt_Perc> preFaturaRoteiros(String data, String codFil, BigDecimal codPessoa, String portal, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> rt_perc = new ArrayList<>();
            String sql = "Select \n"
                    + "Rotas.Rota, Convert(BigInt,Rotas.Sequencia) Sequencia, Rotas.CodFil, Rotas.Data, Convert(BigInt,Rt_Perc.Parada) Parada, Rt_Perc.Nred Cliente,  \n"
                    + "Rt_PercSLA.HrCheg, Rt_PercSLA.HrSaida, Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei,\n"
                    + "Case when Sum(Rt_Guias.Valor) is null then Sum(isnull(Rt_Perc.Valor,0)) else Sum(isnull(Rt_Guias.Valor,0)) end Valor, Rt_Perc.ER, \n"
                    + "STUFF((Select ', '+Convert(varchar,Convert(BigInt,a.Guia))+'-'+a.Serie\n"
                    + "		From Rt_Guias a (Nolock)\n"
                    + "		Where a.Sequencia = Rotas.Sequencia\n"
                    + "		  and a.Parada = Rt_Perc.Parada\n"
                    + "		For XML PATH('')),1,2,''\n"
                    + "		) as Guia, \n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' then max(Ctr010.ValorRot)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' then max(Ctr010.ValorEve)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' then max(Ctr010.ValorEsp)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' then max(Ctr010.ValorAst)\n"
                    + "else 0 end ValorEmb, \n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' then isnull(Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2),0)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' then isnull(Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2),0)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' then isnull(Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2),0)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' then isnull(Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2),0)\n"
                    + "else 0 end ValorADV,\n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then isnull(Round(max(Ctr030.ValorRot)*Max(Rt_Perc.TempoEspera),2),0)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then isnull(Round(max(Ctr030.ValorEve)*Max(Rt_Perc.TempoEspera),2),0)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then isnull(Round(max(Ctr030.ValorEsp)*Max(Rt_Perc.TempoEspera),2),0)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then isnull(Round(max(Ctr030.ValorAst)*Max(Rt_Perc.TempoEspera),2),0)\n"
                    + "else 0 end ValorTE,\n"
                    + "isnull(Round(max(Ctr050.ValorRot)*(Sum(ProcessamentoDN.Qtde)/1000),2),0) ValorProcessamentoDN,\n"
                    + "isnull(Round(max(Ctr053.ValorRot)*(Sum(ProcessamentoMD.Qtde)/1000),2),0) ValorProcessamentoMD, \n"
                    + "case when isnull(Sum(ProcessamentoDN.Qtde),0)+isnull(Sum(ProcessamentoMD.Qtde),0) > 0 then\n"
                    + "   isnull(Round(max(Ctr060.ValorRot)*(Sum(Custodia.ValorRec)/1000),2),0) \n"
                    + "else 0 end Custodia \n"
                    + "From Rotas \n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "inner join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join OS_Vig  on OS_Vig.OS = Rt_Guias.OS\n"
                    + "                 and OS_Vig.CodFil = Rotas.CodFil\n"
                    + "Inner Join Rt_PercSLA  on RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "Left join (Select Guia, Serie, Sum(Isnull(Qtde,0)) Qtde From (\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntDN (NoLock)\n"
                    + "			Union\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntDD (NoLock)) a\n"
                    + "			Group by Guia, Serie) ProcessamentoDN  on ProcessamentoDN.Guia = Rt_Guias.Guia\n"
                    + "			                                    and ProcessamentoDN.Serie = Rt_Guias.Serie\n"
                    + "Left join (Select Guia, Serie, Sum(Isnull(Qtde,0)) Qtde From (\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntMD (NoLock)) a\n"
                    + "			Group by Guia, Serie) ProcessamentoMD  on ProcessamentoMD.Guia = Rt_Guias.Guia\n"
                    + "			                                    and ProcessamentoMD.Serie = Rt_Guias.Serie			                                    \n"
                    + "Left Join CtrItens Ctr010   on Ctr010.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr010.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr010.TipoPosto = '010' \n"
                    + "Left Join CtrItens Ctr020   on Ctr020.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr020.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr020.TipoPosto = '020' \n"
                    + "Left Join CtrItens Ctr030   on Ctr030.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr030.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr030.TipoPosto = '030' \n"
                    + "Left Join CtrItens Ctr050   on Ctr050.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr050.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr050.TipoPosto = '050' \n"
                    + "Left Join CtrItens Ctr053   on Ctr053.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr053.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr053.TipoPosto = '053' \n"
                    + "Left Join CtrItens Ctr060   on Ctr060.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr060.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr060.TipoPosto = '060' 						   \n"
                    + "Left Join (Select TesCustAg.ValorRec, TesCustAg.CodCli, TesCustAg.Codcli1, TesCustAg.CodFil, TesCustAg.Data from TesCustAg (nolock)) Custodia\n"
                    + "                   ON Custodia.Codcli = OS_Vig.Clidst \n"
                    + "                  and Custodia.Codcli1 = OS_Vig.Cliente \n"
                    + "                  and Custodia.CodFil = OS_Vig.CodFil\n"
                    + "                  and Custodia.Data = Rotas.Data\n";
            if (portal.equals("S")) {
                sql += " LEFT JOIN PessoaCliAut PessoaCliAutOri\n";
                sql += "   ON PessoaCliAutOri.Codigo = " + new Float(codPessoa.toPlainString());
                sql += "  AND Rt_Perc.CodCli1        = PessoaCliAutOri.CodCli\n";
                sql += "  AND Rotas.CodFil         = PessoaCliAutOri.CodFil\n";

                sql += " LEFT JOIN PessoaCliAut PessoaCliAutDst\n";
                sql += "   ON PessoaCliAutDst.Codigo = " + new Float(codPessoa.toPlainString());
                sql += "  AND Rt_Perc.CodCli2        = PessoaCliAutDst.CodCli\n";
                sql += "  AND Rotas.CodFil         = PessoaCliAutDst.CodFil\n";
            }
            sql += " where Rotas.data = ? \n";
            if (!codFil.equals("0")) {
                sql += "  and Rotas.CodFil = ? \n";
            }
            sql += "  and Rotas.Rota <> 90 \n"
                    + "  and Rt_Perc.CodCli1 <> '9990001' \n";
            if (portal.equals("S")) {
                sql += " AND (PessoaCliAutOri.Codigo IS NOT NULL OR PessoaCliAutDst.Codigo IS NOT NULL)";
            }
            sql += "Group by Rotas.Rota, Rotas.Sequencia, Rotas.CodFil, Rotas.Data, Rt_Perc.Parada, Rt_Perc.Nred,  \n"
                    + "Rt_PercSLA.HrCheg, Rt_PercSLA.HrSaida, Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei, Rt_perc.ER, \n"
                    + "Rt_Guias.Parada, Rt_Perc.TipoSrv\n"
                    + "Order by Rotas.CodFil, Rotas.Rota, Rt_Perc.ER";
            Consulta consulta = new Consulta(sql, persistencia);
            Consulta consultaTotais = new Consulta(sql, persistencia);
            consulta.setString(data);
            if (!codFil.equals("0")) {
                consulta.setString(codFil);
            }
            consultaTotais.setString(data);
            if (!codFil.equals("0")) {
                consultaTotais.setString(codFil);
            }
            consulta.select();
            consultaTotais.select();

            Rt_Perc trajeto = new Rt_Perc(), trajetoCalculo = new Rt_Perc();

            /*-----------------------------------------------
            |             CALCULAR TOTAIS: Inicio           |
            ------------------------------------------------*/
            List<Rt_Perc> trajetoLista = new ArrayList<>();
            double TotalMontante = 0.0;
            double TotalEmb = 0.0;
            double TotalAdv = 0.0;
            double TotalTE = 0.0;
            double TotalProcDN = 0.0;
            double TotalProcMD = 0.0;
            double TotalCustodia = 0.0;

            String Rota = "";

            while (consultaTotais.Proximo()) {
                if (!consultaTotais.getString("Rota").equals(Rota)) {
                    if (!Rota.equals((""))) {

                        trajetoCalculo = new Rt_Perc();

                        trajetoCalculo.setRota(Rota);
                        trajetoCalculo.setValor(String.valueOf(TotalMontante));
                        trajetoCalculo.setValorEmb(String.valueOf(TotalEmb));
                        trajetoCalculo.setValorAdv(String.valueOf(TotalAdv));
                        trajetoCalculo.setValorTE(String.valueOf(TotalTE));
                        trajetoCalculo.setValorProcDN(String.valueOf(TotalProcDN));
                        trajetoCalculo.setValorProcMD(String.valueOf(TotalProcMD));
                        trajetoCalculo.setValorCustodia(String.valueOf(TotalCustodia));

                        trajetoLista.add(trajetoCalculo);

                        TotalMontante = 0.0;
                        TotalEmb = 0.0;
                        TotalAdv = 0.0;
                        TotalTE = 0.0;
                    }
                }

                Rota = consultaTotais.getString("Rota");
                if (null != consultaTotais.getString("Valor")
                        && !consultaTotais.getString("Valor").equals("")) {
                    TotalMontante += new Double(consultaTotais.getString("Valor"));
                }

                if (null != consultaTotais.getString("ValorEmb")
                        && !consultaTotais.getString("ValorEmb").equals("")) {
                    TotalEmb += new Double(consultaTotais.getString("ValorEmb"));
                }

                if (null != consultaTotais.getString("ValorADV")
                        && !consultaTotais.getString("ValorADV").equals("")) {
                    TotalAdv += new Double(consultaTotais.getString("ValorADV"));
                }

                if (null != consultaTotais.getString("ValorTE")
                        && !consultaTotais.getString("ValorTE").equals("")) {
                    TotalTE += new Double(consultaTotais.getString("ValorTE"));
                }
                if (null != consultaTotais.getString("ValorProcessamentoDN")
                        && !consultaTotais.getString("ValorProcessamentoDN").equals("")) {
                    TotalProcDN += new Double(consultaTotais.getString("ValorProcessamentoDN"));
                }
                if (null != consultaTotais.getString("ValorProcessamentoMD")
                        && !consultaTotais.getString("ValorProcessamentoMD").equals("")) {
                    TotalProcMD += new Double(consultaTotais.getString("ValorProcessamentoMD"));
                }
                if (null != consultaTotais.getString("Custodia")
                        && !consultaTotais.getString("Custodia").equals("")) {
                    TotalCustodia += new Double(consultaTotais.getString("Custodia"));
                }
            }

            if (!Rota.equals((""))) {
                trajetoCalculo = new Rt_Perc();

                trajetoCalculo.setRota(Rota);
                trajetoCalculo.setValor(String.valueOf(TotalMontante));
                trajetoCalculo.setValorEmb(String.valueOf(TotalEmb));
                trajetoCalculo.setValorAdv(String.valueOf(TotalAdv));
                trajetoCalculo.setValorTE(String.valueOf(TotalTE));
                trajetoCalculo.setValorProcDN(String.valueOf(TotalProcDN));
                trajetoCalculo.setValorProcMD(String.valueOf(TotalProcMD));
                trajetoCalculo.setValorCustodia(String.valueOf(TotalCustodia));

                trajetoLista.add(trajetoCalculo);
            }

            consultaTotais.close();
            /*-----------------------------------------------
            |             CALCULAR TOTAIS: Fim              |
            ------------------------------------------------*/

            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();
                trajeto.setData(consulta.getString("Data"));
                trajeto.setRota(consulta.getString("Rota"));
                trajeto.setSequencia(consulta.getString("Sequencia"));
                trajeto.setCodFil(consulta.getString("CodFil"));
                trajeto.setParada(consulta.getInt("Parada"));
                trajeto.setNRed(consulta.getString("Cliente"));
                trajeto.setER(consulta.getString("ER"));
                trajeto.setHrCheg(consulta.getString("HrCheg"));
                trajeto.setHrSaida(consulta.getString("HrSaida"));
                trajeto.setHrChegVei(consulta.getString("HrChegVei"));
                trajeto.setHrSaidaVei(consulta.getString("HrSaidaVei"));
                trajeto.setGuia(consulta.getString("Guia"));
                //trajeto.setSerie(consulta.getString("Serie"));

                if (null != consulta.getString("Valor")
                        && !consulta.getString("Valor").equals("")) {
                    trajeto.setMontante(consulta.getString("Valor"));
                } else {
                    trajeto.setMontante("0.0");
                }

                if (null != consulta.getString("ValorEmb")
                        && !consulta.getString("ValorEmb").equals("")) {
                    trajeto.setValorEmb(consulta.getString("ValorEmb"));
                } else {
                    trajeto.setValorEmb("0.0");
                }

                if (null != consulta.getString("ValorADV")
                        && !consulta.getString("ValorADV").equals("")) {
                    trajeto.setValorAdv(consulta.getString("ValorADV"));
                } else {
                    trajeto.setValorAdv("0.0");
                }

                if (null != consulta.getString("ValorTE")
                        && !consulta.getString("ValorTE").equals("")) {
                    trajeto.setValorTE(consulta.getString("ValorTE"));
                } else {
                    trajeto.setValorTE("0.0");
                }

                if (null != consulta.getString("ValorProcessamentoDN")
                        && !consulta.getString("ValorProcessamentoDN").equals("")) {
                    trajeto.setValorProcDN(consulta.getString("ValorProcessamentoDN"));
                } else {
                    trajeto.setValorProcDN("0.0");
                }

                if (null != consulta.getString("ValorProcessamentoMD")
                        && !consulta.getString("ValorProcessamentoMD").equals("")) {
                    trajeto.setValorProcMD(consulta.getString("ValorProcessamentoMD"));
                } else {
                    trajeto.setValorProcMD("0.0");
                }

                if (null != consulta.getString("Custodia")
                        && !consulta.getString("Custodia").equals("")) {
                    trajeto.setValorCustodia(consulta.getString("Custodia"));
                } else {
                    trajeto.setValorCustodia("0.0");
                }

                for (Rt_Perc trajetoLista1 : trajetoLista) {
                    if (trajetoLista1.getRota().equals(consulta.getString("Rota"))) {
                        trajeto.setTotalMontante(trajetoLista1.getValor());
                        trajeto.setTotalValorAdv(trajetoLista1.getValorAdv());
                        trajeto.setTotalValorEmb(trajetoLista1.getValorEmb());
                        trajeto.setTotalValorTE(trajetoLista1.getValorTE());
                        trajeto.setTotalProcDN(trajetoLista1.getValorProcDN());
                        trajeto.setTotalProcMD(trajetoLista1.getValorProcMD());
                        trajeto.setTotalCustodia(trajetoLista1.getValorCustodia());
                        break;
                    }
                }

                rt_perc.add(trajeto);
            }
            consulta.Close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("RotasDao.resultadoRoteiros - " + e.getMessage() + "\r\n"
                    + "Select \n"
                    + "Rotas.Rota, Convert(BigInt,Rotas.Sequencia) Sequencia, Rotas.CodFil, Rotas.Data, Convert(BigInt,Rt_Perc.Parada) Parada, Rt_Perc.Nred Cliente,  \n"
                    + "Rt_PercSLA.HrCheg, Rt_PercSLA.HrSaida, Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei,\n"
                    + "Case when Sum(Rt_Guias.Valor) is null then Sum(isnull(Rt_Perc.Valor,0)) else Sum(isnull(Rt_Guias.Valor,0)) end Valor, Rt_Perc.ER, \n"
                    + "STUFF((Select ' / '+Convert(varchar,Convert(BigInt,a.Guia))+' - '+a.Serie\n"
                    + "		From Rt_Guias a (Nolock)\n"
                    + "		Where a.Sequencia = Rotas.Sequencia\n"
                    + "		  and a.Parada = Rt_Perc.Parada\n"
                    + "		For XML PATH('')),1,2,''\n"
                    + "		) as Guia,\n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' then max(Ctr010.ValorRot)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' then max(Ctr010.ValorEve)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' then max(Ctr010.ValorEsp)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' then max(Ctr010.ValorAst)\n"
                    + "else 0 end ValorEmb, \n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' then Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' then Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' then Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' then Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2)\n"
                    + "else 0 end ValorADV,\n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then Round(max(Ctr030.ValorRot)*Max(Rt_Perc.TempoEspera),2)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then Round(max(Ctr030.ValorEve)*Max(Rt_Perc.TempoEspera),2)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then Round(max(Ctr030.ValorEsp)*Max(Rt_Perc.TempoEspera),2)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then Round(max(Ctr030.ValorAst)*Max(Rt_Perc.TempoEspera),2)\n"
                    + "else 0 end ValorTE, \n"
                    + "Round(max(Ctr050.ValorRot)*(Sum(Processamento.Qtde)/1000),2) ValorProcessamento \n"
                    + "From Rotas \n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join OS_Vig  on OS_Vig.OS = Rt_Guias.OS\n"
                    + "                 and OS_Vig.CodFil = Rotas.CodFil\n"
                    + "Inner Join Rt_PercSLA  on RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "Left join (Select Guia, Serie, Sum(Isnull(Qtde,0)) Qtde From (\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntDN (NoLock)\n"
                    + "			Union\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntDD (NoLock)\n"
                    + "			Union\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntMD (NoLock)) a\n"
                    + "			Group by Guia, Serie) Processamento  on Processamento.Guia = Rt_Guias.Guia\n"
                    + "			                                    and Processamento.Serie = Rt_Guias.Serie \n"
                    + "Left Join CtrItens Ctr010   on Ctr010.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr010.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr010.TipoPosto = '010' --Embarques\n"
                    + "Left Join CtrItens Ctr020   on Ctr020.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr020.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr020.TipoPosto = '020' --AdValorem\n"
                    + "Left Join CtrItens Ctr030   on Ctr030.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr030.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr030.TipoPosto = '030' --Tempo Espera\n"
                    + "Left Join CtrItens Ctr050   on Ctr050.Contrato = OS_Vig.Contrato \n"
                    + "                           and Ctr050.CodFil = OS_Vig.CodFil \n"
                    + "						   and Ctr050.TipoPosto = '050' \n"
                    + "where Rotas.data = " + data + " \n"
                    + "  and Rotas.CodFil = " + codFil + " \n"
                    + "  and Rotas.Rota <> 90 \n"
                    + "Group by Rotas.Rota, Rotas.Sequencia, Rotas.CodFil, Rotas.Data, Rt_Perc.Parada, Rt_Perc.Nred,  \n"
                    + "Rt_PercSLA.HrCheg, Rt_PercSLA.HrSaida, Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei, Rt_perc.ER, \n"
                    + "Rt_Guias.Parada, Rt_Perc.TipoSrv\n"
                    + "Order by Rotas.CodFil, Rotas.Rota, Rt_Perc.ER");
        }
    }

    public List<Rt_Perc> preFaturaCliFatData(String dtIni, String dtFim, String codFil, BigDecimal codPessoa, String portal, Persistencia persistencia) throws Exception {
        try {
            List<Rt_Perc> rt_perc = new ArrayList<>();
            String sql = "Select Data, Guias, Cliente, Rota, Parada, ER, TipoSRV, Sum(QtdeParadas)QtdeParadas, Sum(Valor) Valor, Sum(ValorEmb) ValorEmb, Sum(ValorADV) ValorADV, Sum(ValorTE) ValorTE, "
                    + "Sum(ValorProcessamentoDN) ValorProcessamentoDN, Sum(ValorProcessamentoMD) ValorProcessamentoMD, Sum(Custodia) Custodia \n"
                    + "from (\n"
                    + "Select \n"
                    + "Rotas.Rota, Rt_Perc.ER, Rt_Perc.TipoSRV, Convert(BigInt,Rotas.Sequencia) Sequencia, Rotas.CodFil, Rotas.Data, Convert(BigInt,Rt_Perc.Parada) Parada, Rt_Perc.Nred Cliente,  \n"
                    + "Rt_PercSLA.HrCheg, Rt_PercSLA.HrSaida, Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei,\n"
                    + "Case when Sum(Rt_Guias.Valor) is null then Sum(isnull(Rt_Perc.Valor,0)) else Sum(isnull(Rt_Guias.Valor,0)) end Valor, \n"
                    + "Count(distinct(Rt_Perc.Sequencia)) QtdeParadas, \n"
                    + "STUFF((Select ', '+Convert(varchar,Convert(BigInt,a.Guia))+'-'+a.Serie\n"
                    + "		From Rt_Guias a (Nolock)\n"
                    + "		Where a.Sequencia = Rotas.Sequencia\n"
                    + "		  and a.Parada = Rt_Perc.Parada\n"
                    + "		For XML PATH('')),1,2,''\n"
                    + "		) as Guias, \n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' then max(Ctr010.ValorRot)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' then max(Ctr010.ValorEve)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' then max(Ctr010.ValorEsp)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' then max(Ctr010.ValorAst)\n"
                    + "else 0 end ValorEmb, \n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' then isnull(Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2),0)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' then isnull(Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2),0)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' then isnull(Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2),0)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' then isnull(Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2),0)\n"
                    + "else 0 end ValorADV,\n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then isnull(Round(max(Ctr030.ValorRot)*Max(Rt_Perc.TempoEspera),2),0)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then isnull(Round(max(Ctr030.ValorEve)*Max(Rt_Perc.TempoEspera),2),0)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then isnull(Round(max(Ctr030.ValorEsp)*Max(Rt_Perc.TempoEspera),2),0)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then isnull(Round(max(Ctr030.ValorAst)*Max(Rt_Perc.TempoEspera),2),0)\n"
                    + "else 0 end ValorTE,\n"
                    + "isnull(Round(max(Ctr050.ValorRot)*(Sum(ProcessamentoDN.Qtde)/1000),2),0) ValorProcessamentoDN,\n"
                    + "isnull(Round(max(Ctr053.ValorRot)*(Sum(ProcessamentoMD.Qtde)/1000),2),0) ValorProcessamentoMD, \n"
                    + "case when isnull(Sum(ProcessamentoDN.Qtde),0)+isnull(Sum(ProcessamentoMD.Qtde),0) > 0 then\n"
                    + "   isnull(Round(max(Ctr060.ValorRot)*(Sum(Custodia.ValorRec)/1000),2),0) \n"
                    + "else 0 end Custodia \n"
                    + "From Rotas \n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "inner join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join OS_Vig  on OS_Vig.OS = Rt_Guias.OS\n"
                    + "                 and OS_Vig.CodFil = Rotas.CodFil\n"
                    + "Inner Join Rt_PercSLA  on RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "Left join (Select Guia, Serie, Sum(Isnull(Qtde,0)) Qtde From (\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntDN (NoLock)\n"
                    + "			Union\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntDD (NoLock)) a\n"
                    + "			Group by Guia, Serie) ProcessamentoDN  on ProcessamentoDN.Guia = Rt_Guias.Guia\n"
                    + "			                                    and ProcessamentoDN.Serie = Rt_Guias.Serie\n"
                    + "Left join (Select Guia, Serie, Sum(Isnull(Qtde,0)) Qtde From (\n"
                    + "			Select Guia, Serie, Codigo, Qtde, Valor From TesEntMD (NoLock)) a\n"
                    + "			Group by Guia, Serie) ProcessamentoMD  on ProcessamentoMD.Guia = Rt_Guias.Guia\n"
                    + "			                                    and ProcessamentoMD.Serie = Rt_Guias.Serie			                                    \n"
                    + "Left Join CtrItens Ctr010   on Ctr010.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr010.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr010.TipoPosto = '010' \n"
                    + "Left Join CtrItens Ctr020   on Ctr020.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr020.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr020.TipoPosto = '020' \n"
                    + "Left Join CtrItens Ctr030   on Ctr030.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr030.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr030.TipoPosto = '030' \n"
                    + "Left Join CtrItens Ctr050   on Ctr050.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr050.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr050.TipoPosto = '050' \n"
                    + "Left Join CtrItens Ctr053   on Ctr053.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr053.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr053.TipoPosto = '053' \n"
                    + "Left Join CtrItens Ctr060   on Ctr060.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr060.CodFil = OS_Vig.CodFil\n"
                    + "						   and Ctr060.TipoPosto = '060' 						   \n"
                    + "Left Join (Select TesCustAg.ValorRec, TesCustAg.CodCli, TesCustAg.Codcli1, TesCustAg.CodFil, TesCustAg.Data from TesCustAg (nolock)) Custodia\n"
                    + "                   ON Custodia.Codcli = OS_Vig.Clidst \n"
                    + "                  and Custodia.Codcli1 = OS_Vig.Cliente \n"
                    + "                  and Custodia.CodFil = OS_Vig.CodFil\n"
                    + "                  and Custodia.Data = Rotas.Data\n";
            if (portal.equals("S")) {
                sql += " LEFT JOIN PessoaCliAut PessoaCliAutOri\n";
                sql += "   ON PessoaCliAutOri.Codigo = " + new Float(codPessoa.toPlainString());
                sql += "  AND Rt_Perc.CodCli1        = PessoaCliAutOri.CodCli\n";
                sql += "  AND Rotas.CodFil         = PessoaCliAutOri.CodFil\n";

                sql += " LEFT JOIN PessoaCliAut PessoaCliAutDst\n";
                sql += "   ON PessoaCliAutDst.Codigo = " + new Float(codPessoa.toPlainString());
                sql += "  AND Rt_Perc.CodCli2        = PessoaCliAutDst.CodCli\n";
                sql += "  AND Rotas.CodFil         = PessoaCliAutDst.CodFil\n";
            }
            sql += " where Rotas.data between ? and ? \n";
            if (!codFil.equals("0")) {
                sql += "  and Rotas.CodFil = ? \n";
            }
            sql += "  and Rotas.Rota <> 90 \n"
                    + "  and Rt_Perc.CodCli1 <> '9990001' \n";
            if (portal.equals("S")) {
                sql += " AND (PessoaCliAutOri.Codigo IS NOT NULL OR PessoaCliAutDst.Codigo IS NOT NULL)";
            }
            sql += "Group by Rotas.Rota, Rotas.Sequencia, Rotas.CodFil, Rotas.Data, Rt_Perc.Parada, Rt_Perc.Nred,  \n"
                    + "Rt_PercSLA.HrCheg, Rt_PercSLA.HrSaida, Rt_PercSLA.HrChegVei, Rt_PercSLA.HrSaidaVei, \n"
                    + "Rt_Guias.Parada, Rt_Perc.TipoSrv, Rotas.Sequencia, Rt_Perc.ER, Rt_Perc.TipoSRV) z\n"
                    + "Group by Cliente, Data, Guias, Rota, Parada, ER, TipoSRV \n"
                    + "Order by Cliente, Data, Rota, Parada";
//            Consulta consulta = new Consulta(sql, persistencia);
//            consulta.setString(dtIni);
//            consulta.setString(dtFim);
//            consulta.setString(codFil);
//            consulta.select();
//            Rt_Perc trajeto;

            Consulta consulta = new Consulta(sql, persistencia);
            Consulta consultaTotais = new Consulta(sql, persistencia);
            consulta.setString(dtIni);
            consulta.setString(dtFim);
            if (!codFil.equals("0")) {
                consulta.setString(codFil);
            }
            consultaTotais.setString(dtIni);
            consultaTotais.setString(dtFim);
            if (!codFil.equals("0")) {
                consultaTotais.setString(codFil);
            }
            consulta.select();
            consultaTotais.select();

            Rt_Perc trajeto = new Rt_Perc(), trajetoCalculo = new Rt_Perc();

            /*-----------------------------------------------
            |             CALCULAR TOTAIS: Inicio           |
            ------------------------------------------------*/
            List<Rt_Perc> trajetoLista = new ArrayList<>();
            double TotalMontante = 0.0;
            double TotalEmb = 0.0;
            double TotalAdv = 0.0;
            double TotalTE = 0.0;
            double TotalProcDN = 0.0;
            double TotalProcMD = 0.0;
            double TotalCustodia = 0.0;
            double TotalParadas = 0;

            String Cliente = "";

            while (consultaTotais.Proximo()) {
                if (!consultaTotais.getString("Cliente").equals(Cliente)) {
                    if (!Cliente.equals((""))) {

                        trajetoCalculo = new Rt_Perc();

                        trajetoCalculo.setNRed(Cliente);
                        trajetoCalculo.setValor(String.valueOf(TotalMontante));
                        trajetoCalculo.setValorEmb(String.valueOf(TotalEmb));
                        trajetoCalculo.setValorAdv(String.valueOf(TotalAdv));
                        trajetoCalculo.setValorTE(String.valueOf(TotalTE));
                        trajetoCalculo.setValorProcDN(String.valueOf(TotalProcDN));
                        trajetoCalculo.setValorProcMD(String.valueOf(TotalProcMD));
                        trajetoCalculo.setValorCustodia(String.valueOf(TotalCustodia));

                        trajetoLista.add(trajetoCalculo);

                        TotalMontante = 0.0;
                        TotalEmb = 0.0;
                        TotalAdv = 0.0;
                        TotalTE = 0.0;
                        TotalProcDN = 0.0;
                        TotalProcMD = 0.0;
                        TotalCustodia = 0.0;
                        TotalParadas = 0;

                    }
                }

                Cliente = consultaTotais.getString("Cliente");
                TotalParadas += consultaTotais.getInt("QtdeParadas");
                if (null != consultaTotais.getString("Valor") && !consultaTotais.getString("Valor").equals("")) {
                    TotalMontante += new Double(consultaTotais.getString("Valor"));
                }

                if (null != consultaTotais.getString("ValorEmb") && !consultaTotais.getString("ValorEmb").equals("")) {
                    TotalEmb += new Double(consultaTotais.getString("ValorEmb"));
                }

                if (null != consultaTotais.getString("ValorADV") && !consultaTotais.getString("ValorADV").equals("")) {
                    TotalAdv += new Double(consultaTotais.getString("ValorADV"));
                }

                if (null != consultaTotais.getString("ValorTE") && !consultaTotais.getString("ValorTE").equals("")) {
                    TotalTE += new Double(consultaTotais.getString("ValorTE"));
                }

                if (null != consultaTotais.getString("ValorProcessamentoDN") && !consultaTotais.getString("ValorProcessamentoDN").equals("")) {
                    TotalProcDN += new Double(consultaTotais.getString("ValorProcessamentoDN"));
                }

                if (null != consultaTotais.getString("ValorProcessamentoMD") && !consultaTotais.getString("ValorProcessamentoMD").equals("")) {
                    TotalProcMD += new Double(consultaTotais.getString("ValorProcessamentoMD"));
                }

                if (null != consultaTotais.getString("Custodia") && !consultaTotais.getString("Custodia").equals("")) {
                    TotalCustodia += new Double(consultaTotais.getString("Custodia"));
                }

            }

            if (!Cliente.equals((""))) {
                trajetoCalculo = new Rt_Perc();

                trajetoCalculo.setNRed(Cliente);
                trajetoCalculo.setValor(String.valueOf(TotalMontante));
                trajetoCalculo.setValorEmb(String.valueOf(TotalEmb));
                trajetoCalculo.setValorAdv(String.valueOf(TotalAdv));
                trajetoCalculo.setValorTE(String.valueOf(TotalTE));
                trajetoCalculo.setValorProcDN(String.valueOf(TotalProcDN));
                trajetoCalculo.setValorProcMD(String.valueOf(TotalProcMD));
                trajetoCalculo.setValorCustodia(String.valueOf(TotalCustodia));

                trajetoLista.add(trajetoCalculo);
            }

            consultaTotais.close();
            /*-----------------------------------------------
            |             CALCULAR TOTAIS: Fim              |
            ------------------------------------------------*/

            while (consulta.Proximo()) {
                trajeto = new Rt_Perc();

                trajeto.setTotalParadas(Double.toString(TotalParadas).replace(".0", ""));
                trajeto.setTotalMontante(Double.toString(TotalMontante));
                trajeto.setTotalValorAdv(Double.toString(TotalAdv));
                trajeto.setTotalValorEmb(Double.toString(TotalEmb));
                trajeto.setTotalValorTE(Double.toString(TotalTE));
                trajeto.setTotalProcDN(Double.toString(TotalProcDN));
                trajeto.setTotalProcMD(Double.toString(TotalProcMD));
                trajeto.setTotalCustodia(Double.toString(TotalCustodia));

                trajeto.setData(consulta.getString("Data"));
                trajeto.setNRed(consulta.getString("Cliente"));
                trajeto.setParada(consulta.getInt("Parada"));
                trajeto.setGuia(consulta.getString("Guias"));
                trajeto.setRota(consulta.getString("Rota"));
                trajeto.setER(consulta.getString("ER"));
                trajeto.setTipoSrv(consulta.getString("TipoSRV"));

                if (null != consulta.getString("Valor")
                        && !consulta.getString("Valor").equals("")) {
                    trajeto.setMontante(consulta.getString("Valor"));
                } else {
                    trajeto.setMontante("0.0");
                }

                if (null != consulta.getString("ValorEmb")
                        && !consulta.getString("ValorEmb").equals("")) {
                    trajeto.setValorEmb(consulta.getString("ValorEmb"));
                } else {
                    trajeto.setValorEmb("0.0");
                }

                if (null != consulta.getString("ValorADV")
                        && !consulta.getString("ValorADV").equals("")) {
                    trajeto.setValorAdv(consulta.getString("ValorADV"));
                } else {
                    trajeto.setValorAdv("0.0");
                }

                if (null != consulta.getString("ValorTE")
                        && !consulta.getString("ValorTE").equals("")) {
                    trajeto.setValorTE(consulta.getString("ValorTE"));
                } else {
                    trajeto.setValorTE("0.0");
                }

                if (null != consulta.getString("ValorProcessamentoDN")
                        && !consulta.getString("ValorProcessamentoDN").equals("")) {
                    trajeto.setValorProcDN(consulta.getString("ValorProcessamentoDN"));
                } else {
                    trajeto.setValorProcDN("0.0");
                }

                if (null != consulta.getString("ValorProcessamentoMD")
                        && !consulta.getString("ValorProcessamentoMD").equals("")) {
                    trajeto.setValorProcMD(consulta.getString("ValorProcessamentoMD"));
                } else {
                    trajeto.setValorProcMD("0.0");
                }

                if (null != consulta.getString("Custodia")
                        && !consulta.getString("Custodia").equals("")) {
                    trajeto.setValorCustodia(consulta.getString("Custodia"));
                } else {
                    trajeto.setValorCustodia("0.0");
                }

                for (Rt_Perc trajetoLista1 : trajetoLista) {
                    if (trajetoLista1.getNRed().equals(consulta.getString("Cliente"))) {
                        trajeto.setTotalMontante(trajetoLista1.getValor());
                        trajeto.setTotalValorAdv(trajetoLista1.getValorAdv());
                        trajeto.setTotalValorEmb(trajetoLista1.getValorEmb());
                        trajeto.setTotalValorTE(trajetoLista1.getValorTE());
                        trajeto.setTotalProcDN(trajetoLista1.getValorProcDN());
                        trajeto.setTotalProcMD(trajetoLista1.getValorProcMD());
                        trajeto.setTotalCustodia(trajetoLista1.getValorCustodia());
                        break;
                    }
                }

                rt_perc.add(trajeto);
            }
            consulta.Close();
            return rt_perc;
        } catch (Exception e) {
            throw new Exception("RotasDao.resultadoRoteiros - " + e.getMessage() + "\r\n"
                    + "Select Cliente, Sum(QtdeParadas)QtdeParadas, Sum(Valor) Valor, Sum(ValorEmb) ValorEmb, Sum(ValorADV) ValorADV, Sum(ValorTE) ValorTE\n"
                    + "from (\n"
                    + "Select \n"
                    + "CliFat.Nred Cliente, Count(distinct(Rt_Perc.Sequencia)) QtdeParadas, \n"
                    + "Case when Sum(Rt_Guias.Valor) is null then Sum(isnull(Rt_Perc.Valor,0)) else Sum(isnull(Rt_Guias.Valor,0)) end Valor,\n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' then max(Ctr010.ValorRot)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' then max(Ctr010.ValorEve)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' then max(Ctr010.ValorEsp)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' then max(Ctr010.ValorAst)\n"
                    + "else 0 end ValorEmb, \n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' then Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' then Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' then Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' then Round(Sum(Rt_Guias.Valor)*(max(Ctr020.ValorRot)/100),2)\n"
                    + "else 0 end ValorADV,\n"
                    + "Case when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'R' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then Round(max(Ctr030.ValorRot)*Max(Rt_Perc.TempoEspera),2)\n"
                    + "     when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'V' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then Round(max(Ctr030.ValorEve)*Max(Rt_Perc.TempoEspera),2)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'E' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then Round(max(Ctr030.ValorEsp)*Max(Rt_Perc.TempoEspera),2)\n"
                    + "	 when Rt_Guias.Parada > 0 and Rt_Perc.TipoSRV = 'A' and (Max(Rt_Perc.TempoEspera)-Max(Ctr030.FranquiaRot)) > 0 then Round(max(Ctr030.ValorAst)*Max(Rt_Perc.TempoEspera),2)\n"
                    + "else 0 end ValorTE	\n"
                    + "\n"
                    + "From Rotas \n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "Left join Rt_Guias  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Inner join OS_Vig  on OS_Vig.OS = Rt_Guias.OS\n"
                    + "                 and OS_Vig.CodFil = Rotas.CodFil\n"
                    + "Left join Clientes CliFat  on CliFat.Codigo = OS_Vig.CliFat\n"
                    + "                          and CliFat.CodFil = OS_Vig.CodFil\n"
                    + "Inner Join Rt_PercSLA  on RT_PercSLA.Sequencia = Rt_perc.Sequencia\n"
                    + "                     and Rt_percSLA.Parada = Rt_Perc.Parada\n"
                    + "                     and Rt_percSLA.CodFil = Rotas.CodFil\n"
                    + "Left Join CtrItens Ctr010   on Ctr010.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr010.CodFil = OS_Vig.CodFil\n"
                    + "				  and Ctr010.TipoPosto = '010' \n"
                    + "Left Join CtrItens Ctr020   on Ctr020.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr020.CodFil = OS_Vig.CodFil\n"
                    + "				  and Ctr020.TipoPosto = '020' \n"
                    + "Left Join CtrItens Ctr030   on Ctr030.Contrato = OS_Vig.Contrato\n"
                    + "                           and Ctr030.CodFil = OS_Vig.CodFil\n"
                    + "				  and Ctr030.TipoPosto = '030' \n"
                    + "where Rotas.data between " + dtIni + " and " + dtFim + " \n"
                    + "  and Rotas.CodFil = " + codFil + " \n"
                    + "Group by CliFat.Nred, Rt_Guias.Parada, Rt_Perc.TipoSrv ) z\n"
                    + "Group by Cliente\n"
                    + "Order by Cliente");
        }
    }

    public boolean validarModeloRotaExistente(String data, String codFil, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        try {
            sql.append("Select Isnull(Count(*),0) Qtde from Rotas \n");
            sql.append(" where Data = ?\n");
            sql.append("  and CodFil = ?\n");
            sql.append("  and Flag_Excl <> '*'\n");
            sql.append("  and TpVeic <> 'S';");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.select();

            String Permitido = "N";

            while (consulta.Proximo()) {
                if (!consulta.getString("Qtde").equals("0")) {
                    Permitido = "S";
                }
            }

            return Permitido.equals("S");
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.validarModeloRotaExistente - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    public boolean validarModeloExistente(String modelo, String codFil, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        try {
            sql.append("Select Isnull(Count(*),0) Qtde from GRotas\n");
            sql.append(" where Modelo = ?\n");
            sql.append("   and CodFil = ?");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setString(modelo);
            consulta.setString(codFil);
            consulta.select();

            String Permitido = "S";

            while (consulta.Proximo()) {
                if (!consulta.getString("Qtde").equals("0")) {
                    Permitido = "N";
                }
            }

            return Permitido.equals("S");
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.validarModeloExistente - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    /**
     * Verifica se, nesta data, existe alguma rota modelo já feita
     *
     * @param codFil - Filial da rota
     * @param data - Data da rota
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public boolean verificaRotaModelo(String codFil, String data, Persistencia persistencia) throws Exception {
        Consulta consulta = null;
        try {
            String sql = "SELECT COUNT(*) Qtde\n"
                    + "FROM Rotas\n"
                    + "WHERE\n"
                    + "    Data = ?\n"
                    + "    AND CodFil = ?\n"
                    + "    AND Flag_Excl <> '*'\n"
                    + "    AND Rotas.TpVeic <> 'S'\n";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setBigDecimal(codFil);
            consulta.select();
            while (consulta.Proximo()) {
                return consulta.getInt("Qtde") > 0;
            }
            return false;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    /**
     * TODO
     *
     * @param data
     * @param modelo - TODO
     * @param operador
     * @param dt_alter
     * @param hr_alter
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public int inserirRotaModelo(
            String data,
            String modelo,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {
        Consulta consulta = null;
        try {
            String sqlOriginal = "Declare @Sequencia int;\n"
                    + "Declare @SeqRota int;\n"
                    + "Declare @data Varchar(10);\n"
                    + "Declare @modelo Varchar(20);\n"
                    + "Declare @operador Varchar(10);\n"
                    + "Declare @dt_alter Varchar(10);\n"
                    + "Declare @hr_alter Varchar(5);\n"
                    //Defina parametros
                    + "set @data = ?;\n"
                    + "set @modelo = ?;\n"
                    + "set @operador = ?;\n"
                    + "set @dt_alter = ?;\n"
                    + "set @hr_alter = ?;\n"
                    //Tratamento usuário incluindo "GER-"
                    + "set @operador = Substring('GER-'+@operador,1,10); \n"
                    + "Select * into #gRotas from gRotas where modelo = @modelo and Flag_Excl <> '*';\n"
                    + "Select * into #gRt_Perc from gRt_Perc where Sequencia in (Select Sequencia from #gRotas) and Flag_Excl <> '*';\n"
                    + "Update #gRt_Perc set Valor = null, HrBaixa = null, operFech = null, Dt_Fech = null, Hr_Fech = null, CodLan = null, Pedido = null, Operador = null, Dt_Alter = null, Hr_Alter = null, Observ = null; \n"
                    + "WHILE (SELECT count(*) FROM #gRotas) > 0\n"
                    + "BEGIN\n"
                    + "	Select top 01 @Sequencia = Sequencia from #gRotas;\n"
                    + "	Select @SeqRota = Max(Isnull(Sequencia,0)+1) from Rotas;\n"
                    + "	Update #gRotas set Sequencia = @SeqRota, Data = @data, Operador = @operador, Dt_Alter = @dt_alter, Hr_Alter = @hr_alter, DtFim = @data\n"
                    + "	where Sequencia = @Sequencia;	\n"
                    + "	Insert into Rotas (Sequencia, Data, Rota, CodFil, TpVeic, Viagem, ATM, BACEN, Aeroporto, HrLargada, HrChegada,\n"
                    + "	HrIntIni, HrIntFim, HsTotal, Observacao, Operador, DtFim, Dt_alter, Hr_alter, OperFech, Flag_Excl)\n"
                    + "	Select Sequencia, Data, Rota, CodFil, TpVeic, Viagem, ATM, BACEN, Aeroporto, HrLargada, HrChegada,\n"
                    + "	HrIntIni, HrIntFim, HsTotal, Observacao, Operador, DtFim, Dt_alter, Hr_alter, OperFech, Flag_Excl \n"
                    + "	from #gRotas where Sequencia = @SeqRota;\n"
                    + "	Update #gRt_Perc set Sequencia = @SeqRota, Operador = @operador, Dt_Alter = @dt_alter, Hr_Alter = @hr_alter,\n"
                    + "	OperIncl = @operador, Dt_Incl = @dt_alter, Hr_Incl = @hr_alter\n"
                    + "	where Sequencia = @Sequencia;\n"
                    + "	Insert into RotasPnt (Sequencia, SeqRota, Data, Operador, Dt_Alter, Hr_Alter)\n"
                    + "	Select Sequencia, Sequencia, Data, Operador, Dt_Alter, Hr_Alter from #gRotas where Sequencia = @SeqRota;\n"
                    + "	Insert into Rt_Perc (Sequencia, CodFil, Parada, Hora1, ER, CodCli1, NRed, CodCli2, Hora1D, TipoSrv,\n"
                    + "                    Chave, DPar, Regiao, Observ, Valor, HrBaixa, OS, OperIncl, Dt_Incl, Hr_Incl, OperFech, Flag_excl)\n"
                    + "	Select Sequencia, CodFil, Parada, Hora1, ER, CodCli1, NRed, CodCli2, Hora1D, TipoSrv,\n"
                    + "                    Chave, DPar, Regiao, Observ, Valor, HrBaixa, OS, OperIncl, Dt_Incl, Hr_Incl, OperFech, Flag_excl\n"
                    + "	from #gRt_Perc\n"
                    + "	where Sequencia = @SeqRota;\n"
                    + "	Delete from #gRotas where Sequencia = @SeqRota;\n"
                    + "	Delete from #gRt_Perc where Sequencia = @SeqRota;\n"
                    + "   IF (SELECT count(*) FROM #gRotas) = 0\n"
                    + "      BREAK\n"
                    + "   ELSE\n"
                    + "      CONTINUE\n"
                    + "END\n"
                    + "drop table #gRotas;\n"
                    + "drop table #gRt_Perc;\n";

            consulta = new Consulta(sqlOriginal, persistencia);
            consulta.setString(data);
            consulta.setString(modelo);
            consulta.setString(operador);
            consulta.setString(dt_alter);
            consulta.setString(hr_alter);
            return consulta.insert();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    /**
     * TODO
     *
     * @param data
     * @param codFil
     * @param modelo
     * @param operador
     * @param dt_alter
     * @param hr_alter
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public int inserirModeloRota(
            String data,
            String codFil,
            String modelo,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {
        Consulta consulta = null;
        try {
            String sqlOriginal = "Declare @Sequencia int;\n"
                    + " Declare @SeqRota  int;\n"
                    + " Declare @data     Varchar(10);\n"
                    + " Declare @codfil   int;\n"
                    + " Declare @modelo   Varchar(10);\n"
                    + " Declare @operador Varchar(10);\n"
                    + " Declare @dt_alter Varchar(10);\n"
                    + " Declare @hr_alter Varchar(5);\n"
                    // Defina parametros
                    + " set @data     = ?;\n"
                    + " set @codfil   = ?;\n"
                    + " set @modelo   = ?;\n"
                    + " set @operador = ?;\n"
                    + " set @dt_alter = ?;\n"
                    + " set @hr_alter = ?;\n"
                    + " Select * into #Rotas\n"
                    + " from Rotas \n"
                    + "  where Data   = @data\n"
                    + "   and CodFil = @codfil\n"
                    + "   and Flag_Excl <> '*'\n"
                    + "   and TpVeic    <> 'S';\n"
                    + " Select * into #Rt_Perc\n"
                    + " from Rt_Perc \n"
                    + "  where Sequencia in (Select Sequencia from #Rotas)\n"
                    + "   and Flag_Excl <> '*';\n"
                    + " WHILE (SELECT count(*) FROM #Rotas) > 0\n"
                    + " BEGIN  \n"
                    + " Select top 01 @Sequencia = Sequencia from #Rotas;\n"
                    + " Select @SeqRota = Max(Isnull(Sequencia,0)+1) from gRotas;\n"
                    + " Update #Rotas set Sequencia = @SeqRota, Data = @data, Operador = @operador, Dt_Alter = @dt_alter, Hr_Alter = @hr_alter\n"
                    + " where Sequencia = @Sequencia;	\n"
                    + " Update #Rt_Perc set Sequencia = @SeqRota, Operador = @operador, Dt_Alter = @dt_alter, Hr_Alter = @hr_alter, \n"
                    + " OperIncl = @operador, Dt_Incl = @dt_alter, Hr_Incl = @hr_alter\n"
                    + " where Sequencia = @Sequencia;\n"
                    + "	Insert into GRotas (Sequencia, Modelo, Data, Rota, CodFil, TpVeic, Viagem, ATM, BACEN, Aeroporto, HrLargada, HrChegada, \n"
                    + "	HrIntIni, HrIntFim, HsTotal, Observacao, Operador, DtFim, Dt_alter, Hr_alter, OperFech, Flag_Excl)\n"
                    + "	Select Sequencia, @modelo, Data, Rota, CodFil, TpVeic, Viagem, ATM, BACEN, Aeroporto, HrLargada, HrChegada, \n"
                    + "	HrIntIni, HrIntFim, HsTotal, Observacao, Operador, DtFim, Dt_alter, Hr_alter, OperFech, Flag_Excl \n"
                    + "	from #Rotas where Sequencia = @SeqRota;\n"
                    + "	Insert into gRt_Perc (Sequencia, CodFil, Parada, Hora1, ER, CodCli1, NRed, CodCli2, Hora1D, TipoSrv, \n"
                    + "                    Chave, DPar, Regiao, Observ, Valor, HrBaixa, OS, OperIncl, Dt_Incl, Hr_Incl, OperFech, Flag_excl) \n"
                    + "	Select Sequencia, CodFil, Parada, Hora1, ER, CodCli1, NRed, CodCli2, Hora1D, TipoSrv, \n"
                    + "                    Chave, DPar, Regiao, Observ, Valor, HrBaixa, OS, OperIncl, Dt_Incl, Hr_Incl, OperFech, Flag_excl \n"
                    + "	from #Rt_Perc\n"
                    + "	where Sequencia = @SeqRota;	\n"
                    + "	Delete from #Rotas where Sequencia = @SeqRota;\n"
                    + "	Delete from #Rt_Perc where Sequencia = @SeqRota;\n"
                    + "   IF (SELECT count(*) FROM #Rotas) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + "END\n"
                    + "drop table #Rotas;\n"
                    + "drop table #Rt_Perc;";

            consulta = new Consulta(sqlOriginal, persistencia);
            consulta.setString(data.replace("-", ""));
            consulta.setString(codFil);
            consulta.setString(modelo);
            consulta.setString(operador);
            consulta.setString(dt_alter.replace("-", ""));
            consulta.setString(hr_alter);
            return consulta.insert();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    /**
     * TODO
     *
     * @param data
     * @param codFil
     * @param operador
     * @param dt_alter
     * @param hr_alter
     * @param tolerancia
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public int gerarPedidoUsandoFrequencia(
            String data,
            String codFil,
            String operador,
            String dt_alter,
            String hr_alter,
            String tolerancia,
            Persistencia persistencia
    ) throws Exception {
        Consulta consulta = null;
        try {
            String sqlOriginal = "Declare @diaSem int;\n"
                    + " Declare @data date;\n"
                    + " Declare @codfil int;\n"
                    + " Declare @os float;\n"
                    + " Declare @operador Varchar(10);\n"
                    + " Declare @dt_alter Varchar(10);\n"
                    + " Declare @hr_alter Varchar(05);\n"
                    + " Declare @tolerancia int;\n"
                    + " Declare @codcli Varchar(07);\n"
                    + " Declare @codigo Varchar(07);\n"
                    + " Declare @gerarPedido int; --1-True / 2-False\n"
                    + " Declare @feriado int; --1-True / 2-False\n"
                    + " Declare @hora1 Varchar(05);\n"
                    + " Declare @hora2 Varchar(05);\n"
                    + " Declare @nred Varchar(20);\n"
                    + " Declare @regiao Varchar(03);\n"
                    + " Declare @codclidst Varchar(07);\n"
                    + " Declare @nreddst Varchar(20);\n"
                    + " Declare @regiaodst Varchar(03);\n"
                    + " Declare @rotaC Varchar(20);\n"
                    + " Declare @diautil date;\n"
                    + " Declare @cidadeFilial Varchar(30);\n"
                    + " Declare @ufFilial Varchar(02);\n"
                    + " Declare @dias Varchar(31);\n"
                    + " Declare @du Varchar(31);\n"
                    + " Declare @dia int;\n"
                    + " Declare @cxforte Varchar(07);\n"
                    + " Declare @cxforteNred Varchar(20);\n"
                    + " Declare @cxforteRegiao Varchar(03);\n"
                    + " Declare @pedidoExistente int;\n"
                    + "\n"
                    + " Declare @hrRec1 Varchar(05);\n"
                    + " Declare @hrRec2 Varchar(05);\n"
                    + " Declare @hrEnt1 Varchar(05);\n"
                    + " Declare @hrEnt2 Varchar(05);\n"
                    + " Declare @hr1 Varchar(05);\n"
                    + " Declare @hr2 Varchar(05);\n"
                    + " Declare @qtdTrajetos int;\n"
                    + " Declare @sequencia int;\n"
                    + " Declare @parada int;\n"
                    + " Declare @solicitante Varchar(30);\n"
                    // Defina parametros
                    + " set @data       = ?;\n"
                    + " set @codfil     = ?;\n"
                    + " set @tolerancia = ?;\n"
                    + " set @solicitante = 'Generated Frequency Orders';\n"
                    + "\n"
                    + " set @operador = ?;\n"
                    + " set @dt_alter = ?;\n"
                    + " set @hr_alter = ?;\n"
                    + " Select @diaSem = DatePart(WeekDay,@data);\n"
                    + "\n"
                    + " Select \n"
                    + " @cidadeFilial = Cidade, \n"
                    + " @ufFilial = UF\n"
                    + " from Filiais (NoLock) where CodFil = @codfil;\n"
                    + "\n"
                    + " Select top 01 \n"
                    + " @cxforte = Clientes.Codigo,\n"
                    + " @cxforteNred = Clientes.Nred,\n"
                    + " @cxforteRegiao = Regiao\n"
                    + " from CxForte \n"
                    + " Left join Clientes  on Clientes.Codigo = CxForte.CodCli\n"
                    + "                    and Clientes.CodFil = CxForte.CodFil\n"
                    + " where CxForte.CodFil = @codfil Order By DtFecha Desc;\n"
                    + "\n"
                    // Feriados nacionais
                    + " Select @feriado = Case when Count(*) > 0 then 1 else 2 end from Calendar where data = @data and Tipo = 'N';\n"
                    // Feriados estaduais
                    + " if(@feriado = 2)\n"
                    + "	Select @feriado = Case when Count(*) > 0 then 1 else 2 end from Calendar where data = @data and Tipo = 'E' and UF = @ufFilial;\n"
                    // Feriados municipais
                    + " if(@feriado = 2)\n"
                    + "	Select @feriado = Case when Count(*) > 0 then 1 else 2 end from Calendar where data = @data and Tipo = 'M' and UF = @ufFilial and Municipio = @cidadeFilial;\n"
                    + "\n"
                    + " set @diautil = @data;\n"
                    + " if((@diaSem in (1,7)) or (@feriado = 1))\n"
                    + " set @diautil = DateAdd(day,1,@data);\n"
                    + "\n"
                    + " Select \n"
                    + " OS_Vig.Cliente, OS_Vig.NRed, OS_Vig.CliDst, OS_Vig.NRedDst, OS_Vig.ViaCxf, OS_VFreq.*, \n"
                    + " Cli1.Regiao RegiaoOri, Cli2.Regiao RegiaoDst \n"
                    + " into #OsFreq\n"
                    + " from OS_VFreq (NoLock) \n"
                    + " left join OS_Vig (NoLock)       on  OS_Vig.OS     = OS_VFreq.OS      \n"
                    + "                         and OS_Vig.CodFil = OS_VFreq.CodFil  \n"
                    + " left join Clientes Cli1 (NoLock) on  Cli1.Codigo   = OS_Vig.Cliente   \n"
                    + "                         and Cli1.CodFil   = OS_VFreq.CodFil  \n"
                    + " left join Clientes Cli2 (NoLock) on  Cli2.Codigo   = OS_Vig.CliDst    \n"
                    + "                         and Cli2.CodFil   = OS_VFreq.CodFil  \n"
                    + " where OS_VFreq.CodFil    = @codfil\n"
                    + " and (OS_VFreq.DiaSem   = @diaSem or OS_VFreq.DiaSem = 9) \n"
                    + " and (OS_VFreq.Tipo     = 'R') \n"
                    + " and (OS_Vig.Situacao   = 'A') \n"
                    + " and (OS_Vig.DtInicio <= @data) \n"
                    + " and (OS_Vig.DtFim    >= @data);\n"
                    + " \n"
                    + " WHILE (SELECT count(*) FROM #OsFreq) > 0\n"
                    + " BEGIN  \n"
                    + " set @gerarPedido = 2;\n"
                    + "\n"
                    + " Select top 01 \n"
                    + "		@codcli = Cliente, \n"
                    + "		@codigo = Cliente,\n"
                    + "		@hora1 = Hora1,\n"
                    + "		@hora2 = Hora2,\n"
                    + "		@nred = Nred,\n"
                    + "		@regiao = RegiaoOri,\n"
                    + "		@codclidst = CliDst,\n"
                    + "		@nreddst = NRedDst,\n"
                    + "		@regiaodst = RegiaoDst,\n"
                    + "		@dias = Dias,\n"
                    + "		@du = DU,\n"
                    + "		@rotaC = RotaC,\n"
                    + "		@os = OS\n"
                    + "	from #OsFreq where Codfil = @codfil;\n"
                    + "	\n"
                    + " if(substring(@codclidst,4,1) = '7')begin\n"
                    + "		set @codclidst = @cxforte;\n"
                    + "		set @nreddst = @cxforteNred;\n"
                    + "		set @regiaodst = @cxforteRegiao;\n"
                    + "	 end\n"
                    + "	\n"
                    + "	set @dia = DatePart(day,@diautil);\n"
                    + "\n"
                    + "	if(Substring(@dias,DatePart(day,@diautil),1) = '1') begin\n"
                    //+ "		if(Substring(@du,DatePart(day,@diautil),1) = '1')\n"
                    + "			Set @gerarPedido = 1;\n"
                    + "	end\n"
                    + "\n"
                    + "	if(@gerarPedido = 1) begin		\n"
                    + "\n"
                    + "		Select @pedidoExistente = Count(*)\n"
                    + "		from Pedido \n"
                    + "        where CodFil   = @codfil\n"
                    + "          and Data     = @data\n"
                    + "          and CodCli1  = @codcli\n"
                    + "          and CodCli2  = @codclidst\n"
                    + "          and Hora1O  >= @hora1\n"
                    + "          and Hora2O  <= @hora2\n"
                    + "          and Pedido.Flag_Excl <> '*';\n"
                    + "\n"
                    + "		if(@pedidoExistente = 0) begin\n"
                    + "			if (Substring(@codcli,4,1) = '9') or\n"
                    + "               (Substring(@codcli,4,1) = '5') begin\n"
                    + "				set @hrEnt1 = Substring(Convert(Varchar,DateAdd(Minute, (@tolerancia*-1),Convert(time,@hora2)),113),1,5);\n"
                    + "				set @hrEnt2 = Substring(Convert(Varchar,DateAdd(Minute, (@tolerancia),Convert(time,@hora2)),113),1,5);\n"
                    + "				set @hr1 = @hrEnt1;\n"
                    + "				set @hr2 = @hrEnt2;\n"
                    + "\n"
                    + "				set @codclidst = @codcli;\n"
                    + "				set @nreddst  = @nred;\n"
                    + "				set @regiaodst = @regiao;\n"
                    + "				set @codcli = @cxforte;\n"
                    + "				set @nred = @cxfortenred;\n"
                    + "				set @regiao = @cxforteRegiao;\n"
                    + "\n"
                    + "			end else begin\n"
                    + "				set @hrRec1 = Substring(Convert(Varchar,DateAdd(Minute, (@tolerancia*-1),Convert(time,@hora1)),113),1,5);\n"
                    + "				set @hrRec2 = Substring(Convert(Varchar,DateAdd(Minute, (@tolerancia),Convert(time,@hora1)),113),1,5);\n"
                    + "				set @hr1 = @hrRec1;\n"
                    + "				set @hr2 = @hrRec2;\n"
                    + "			end\n"
                    + "\n"
                    + "			Select \n"
                    + "			@qtdTrajetos = Count(*), \n"
                    + "			@sequencia = Min(Rt_perc.Sequencia), \n"
                    + "			@parada = Min(parada) \n"
                    + "			from Rt_Perc \n"
                    + "			left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia \n"
                    + "            where Rotas.CodFil     = @codfil\n"
                    + "              and Rotas.Data       = @data\n"
                    + "              and Rt_Perc.CodCli1  = @codcli\n"
                    + "              and Rt_Perc.CodCli2  = @codclidst\n"
                    + "              and Hora1  >= @hr1\n"
                    + "              and Hora1  <= @hr2\n"
                    + "              and Rt_Perc.Flag_Excl <> '*';\n"
                    + "			\n"
                    + "			if(@qtdTrajetos = 0) begin\n"
                    + "				Select \n"
                    + "				@qtdTrajetos = Count(*), \n"
                    + "				@sequencia = Min(Rt_perc.Sequencia), \n"
                    + "				@parada = Min(parada)\n"
                    + "				from Rt_Perc \n"
                    + "                left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia \n"
                    + "                where Rotas.CodFil     = @codfil\n"
                    + "                  and Rotas.Data       = @data\n"
                    + "                  and Rt_Perc.CodCli1  = @codclidst\n"
                    + "                  and Rt_Perc.CodCli2  = @codcli\n"
                    + "                  and Hora1  >= @hr1\n"
                    + "                  and Hora1  <= @hr2\n"
                    + "                  and Rt_Perc.Flag_Excl <> '*';\n"
                    + "\n"
                    + "				insert into Pedido (Data,Tipo,CodCli1,NRed1,Regiao1,Hora1O,Hora2O,CodCli2,NRed2,Regiao2,\n"
                    + "                             Hora1D,Hora2D,Solicitante,Valor,Obs,ClassifSrv,OS,Operador,Dt_Alter,Hr_Alter,\n"
                    + "                             HrOper,OperIncl,Dt_Incl,Hr_Incl,Situacao,Flag_Excl,Numero,Codfil)\n"
                    + "				values (@data, 'T', @codcli, @nred, @regiao, @hora1, @hora2, @codclidst, @nreddst, @regiaodst,\n"
                    + "				@hr1, @hr2, @solicitante, 0, @rotaC, 'R', @os, @operador, @dt_alter, @hr_alter,\n"
                    + "				'00:00', @operador, @dt_alter, @hr_alter, 'PD', '', (Select Isnull(Max(Numero),0)+1 from pedido where codfil = @codfil), @codfil);\n"
                    + "			end			\n"
                    + "		end\n"
                    + "	end\n"
                    + "\n"
                    + "   Delete from #OsFreq where OS = @os and codfil = @codfil;\n"
                    + "   IF (SELECT count(*) FROM #OsFreq) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + "END\n"
                    + "Drop table #OsFreq;";

            consulta = new Consulta(sqlOriginal, persistencia);
            consulta.setString(data.replace("-", ""));
            consulta.setString(codFil);
            consulta.setString(tolerancia);
            consulta.setString(operador);
            consulta.setString(dt_alter.replace("-", ""));
            consulta.setString(hr_alter);
            return consulta.insert();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    /**
     * gerarSaidasAutomaticas
     *
     * @param data
     * @param codFil
     * @param operador
     * @param dt_alter
     * @param hr_alter
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public int gerarSaidasAutomaticas(
            String data,
            String codFil,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {
        Consulta consulta = null;
        try {
            String sqlOriginal = "Declare @codfil float;\n"
                    + "Declare @data Date;\n"
                    + "Declare @pedido int;\n"
                    + "Declare @codcli1 Varchar(7);\n"
                    + "Declare @codcli2 Varchar(7);\n"
                    + "Declare @codtes Varchar(7);\n"
                    + "Declare @nredtes Varchar(7);\n"
                    + "Declare @clifat Varchar(7);\n"
                    + "Declare @codconta Float;\n"
                    + "Declare @codlote Varchar(3);\n"
                    + "Declare @os int;\n"
                    + "Declare @pedidovalor Float;\n"
                    + "Declare @serieGtv Varchar(3);\n"
                    + "Declare @gtv float;\n"
                    + "Declare @tiposrv Varchar(3);\n"
                    + "Declare @obs Varchar(80);\n"
                    + "Declare @operador Varchar(10);\n"
                    + "Declare @dtalter date;\n"
                    + "Declare @hralter Varchar(5);\n"
                    + "Declare @lacre Varchar(8);\n"
                    + "Declare @tesexiste int;\n"
                    + "Declare @seqrota float;\n"
                    + "Declare @rota float;\n"
                    + "Declare @rotaant float;\n"
                    + "Declare @hora1 varchar(5);\n"
                    + "Declare @seqparada int;\n"
                    + "Declare @cxforte varchar(07);\n"
                    + "Declare @cxforteNred Varchar(20);\n"
                    + "Declare @cxforteRegiao Varchar(03);\n"
                    + "Declare @horaCxfManha Varchar(05);\n"
                    + "Declare @horaCxfNoite Varchar(05);\n"
                    + "Declare @seqParadaOri int;\n"
                    + "Declare @seqParadaDst int;\n"
                    + "Declare @acrescimoMinutos int;\n"
                    + "Declare @seqParadaCxf int;\n"
                    + "Declare @cxfExiste int;\n"
                    + "Declare @observ Varchar(20);\n"
                    + "Declare @valordn float;\n"
                    + "Declare @valormd float;\n"
                    + "Declare @valortotal float;\n"
                    + "Declare @codsrv Varchar(03);\n"
                    + "Declare @qtde Float;\n"
                    + "Declare @volumes Float;\n"
                    + "Declare @valor Float;\n"
                    + "Declare @codremessa VarChar(10);\n"
                    + "Declare @remessa Float;\n"
                    + "\n"
                    + "-------------------------------------------------\n"
                    + "--Parametros-------------------------------------\n"
                    + "Set @codfil = ? ;\n"
                    + "Set @data = ? ;\n"
                    + "Set @serieGtv = ? ;\n"
                    + "Set @operador = ?;\n"
                    + "Set @dtalter = ?;\n"
                    + "Set @hralter = ?;\n"
                    + "set @horaCxfManha = '0800';\n"
                    + "set @horaCxfNoite = '1900';\n"
                    + "set @acrescimoMinutos = 10;\n"
                    + "set @observ = '';\n"
                    + "-------------------------------------------------\n"
                    + "--Buscando dados CxForte\n"
                    + "Select top 1 \n"
                    + "@cxforte = Clientes.Codigo,\n"
                    + "@cxforteNred = Clientes.Nred,\n"
                    + "@cxforteRegiao = Clientes.Regiao\n"
                    + "from CxForte \n"
                    + "Left join Clientes  on Clientes.Codigo = CxForte.CodCli\n"
                    + "					and Clientes.CodFil = CxForte.CodFil\n"
                    + "where CxForte.CodFil = @codfil \n"
                    + "Order By DtFecha Desc;\n"
                    + "-------------------------------------------------\n"
                    + "--Buscando pedidos e gerando tesSaidas e Cxforte-\n"
                    + "--entrada----------------------------------------\n"
                    + "Select \n"
                    + "Numero, CodFil, Data, CodCli1, CodCli2, OS, Valor, ClassifSRV, OBS\n"
                    + "into #pedidos\n"
                    + "From Pedido \n"
                    + "where data = @data\n"
                    + "  and CodFil = @codfil\n"
                    + "  and CodCli1 = @cxforte\n"
                    + "  and Numero not in (Select Pedido from TesSaidas where TesSaidas.CodFil = Pedido.CodFil and TesSaidas.Pedido is not null)\n"
                    + "  and Flag_Excl <> '*'\n"
                    + "  and Tipo = 'T'\n"
                    + "  and OS > 0;\n"
                    + "\n"
                    + "WHILE (SELECT count(*) FROM #pedidos) > 0\n"
                    + "BEGIN \n"
                    + "\n"
                    + "	set @valordn = 0;\n"
                    + "	set @valormd = 0;\n"
                    + "    set @valortotal = 0;\n"
                    + "\n"
                    + "	Select top 01 \n"
                    + "	@pedido = Numero,\n"
                    + "	@codcli1 = CodCli1,\n"
                    + "	@codcli2 = CodCli2,\n"
                    + "	@os = OS,\n"
                    + "	@tiposrv = ClassifSRV,\n"
                    + "	@pedidovalor = Valor,\n"
                    + "	@obs = OBS\n"
                    + "	from #pedidos;\n"
                    + "\n"
                    + "	Select\n"
                    + "	@codtes = OS_Vig.CliDst,\n"
                    + "	@nredtes = OS_Vig.NRedDst,\n"
                    + "	@codconta = OS_VTes.ContaPdr,\n"
                    + "    @codlote = LotePdr,\n"
                    + "	@clifat = OS_Vig.CliFat\n"
                    + "	from OS_Vig\n"
                    + "	Left join OS_VTes  on OS_Vig.OS = OS_VTes.OS\n"
                    + "	                  and OS_Vig.CodFil = OS_VTes.CodFil\n"
                    + "	where OS_Vig.OS = @os;\n"
                    + "	--Verificando se clientes destino da OS é tesouraria\n"
                    + "	if(Substring(@codtes,4,1) = 7) begin\n"
                    + "		--Gerando GTV\n"
                    + "		Insert into GTV (CodFil, Guia, Serie, Pedido, DtGeracao, OS, Situacao, Status, Operador, Dt_alter, Hr_alter)\n"
                    + "		Select @codfil, Max(Isnull(Guia,0))+1, @serieGtv, @pedido, @data, @os, 2, 'PD', @operador, @dtalter, @hralter\n"
                    + "		From GTV where Serie = @serieGtv;\n"
                    + "\n"
                    + "		Select @gtv = Guia from GTV where Pedido = @pedido and CodFil = @codfil and Serie = @serieGtv;\n"
                    + "\n"
                    + "		--Inserindo TesSaidas\n"
                    + "		Insert into TesSaidas (Guia, Serie, CodFil, Data, CodGTV, TipoMov, CodCli1, CodCli2, CodCli3, ContaTes,  \n"
                    + "		TotalDN, TotalMoeda, TotalGeral, Pedido, TipoSrv, Situacao, Obs, Baixa, ChequesQtde, ChequesValor, TicketsQtde, \n"
                    + "		TicketsValor, Dt_Pedido, Operador, Dt_alter, Hr_alter) \n"
                    + "		Values(@gtv, @serieGtv, @codfil, @data, (Select min(Codigo) from GTVSeq where Serie = @serieGtv), 0,@codtes,@codcli2,\n"
                    + "		@clifat, @codconta, @pedidovalor, 0, @pedidovalor, @pedido, @tiposrv, 'OK', @obs, 'S', 0, 0, 0, 0, @data, @operador, @dtalter, @hralter);\n"
                    + "\n"
                    + "		--Inserindo TesSaidasDN\n"
                    + "		Insert into TesSaidasDN (Guia, Serie, Codigo, Docto, Qtde, Valor, Operador, Dt_alter, Hr_alter)\n"
                    + "		Select @gtv, @serieGtv, Codigo, Docto, Qtde,\n"
                    + "		((Case when codigo = 11 then 10 \n"
                    + "		     when codigo = 21 then 20 \n"
                    + "			 when codigo = 51 then 50 \n"
                    + "			 when codigo = 101 then 100\n"
                    + "			 when codigo = 102 then 2\n"
                    + "			 when codigo = 105 then 5\n"
                    + "			 else codigo end)*Qtde),\n"
                    + "		@operador, @dtalter, @hralter  from PedidoDN \n"
                    + "		where Numero = @pedido\n"
                    + "		  and CodFil = @codfil;\n"
                    + "\n"
                    + "		--Inserindo TesSaidasMD\n"
                    + "		Insert into TesSaidasMD (Guia, Serie, Codigo, Docto, Qtde, Valor, Operador, Dt_alter, Hr_alter)\n"
                    + "		Select @gtv, @serieGtv, Codigo, Docto, Qtde, Round(((Codigo*qtde)/100),2), @operador, @dtalter, @hralter from PedidoMD \n"
                    + "		where Numero = @pedido\n"
                    + "		  and CodFil = @codfil;\n"
                    + "\n"
                    + "	    --Ajustando valor TesSaidas\n"
                    + "	    Select @valordn = sum(isnull(Valor,0)) from TesSaidasDN where Guia = @gtv and Serie = @serieGtv;\n"
                    + "		if(@valordn is null)\n"
                    + "		set @valordn = 0;\n"
                    + "\n"
                    + "		Select @valormd = sum(isnull(Valor,0)) from TesSaidasMD where Guia = @gtv and Serie = @serieGtv;	\n"
                    + "		if(@valormd is null)\n"
                    + "			set @valormd = 0;		\n"
                    + "\n"
                    + "		set @valortotal = @valordn + @valormd;\n"
                    + "		\n"
                    + "		\n"
                    + "		Update TesSaidas Set \n"
                    + "	    TotalDN = @valordn,\n"
                    + "	    TotalMoeda = @valormd,\n"
                    + "	    TotalGeral = @valortotal\n"
                    + "	    from TesSaidas \n"
                    + "	    where Guia = @gtv\n"
                    + "	      and Serie = @serieGtv;\n"
                    + "\n"
                    + "		--Gerando lacre \n"
                    + "		Select @lacre = Isnull(Convert(BigInt,Lacre),70000000)+1 \n"
                    + "		From MalotesVirtuais\n"
                    + "		where Convert(BigInt,Lacre) >= 70000000\n"
                    + "          and Convert(BigInt,Lacre) <= 79999999		\n"
                    + "\n"
                    + "		if(@lacre is null) begin\n"
                    + "			set @lacre = '70000000';\n"
                    + "		end		\n"
                    + "\n"
                    + "		Insert into MalotesVirtuais(Lacre, Operador, DtGeracao, Hr_Geracao, OS, CodCli, DtCaptura, Hr_Captura, Impressa, OBS)\n"
                    + "		Values(@lacre, @operador, @data, @hralter, @os, @codcli2, @data, @hralter, 'S', '');\n"
                    + "\n"
                    + "		--Gerando TesSaidasCxf\n"
                    + "		Insert into TesSaidasCxf (CodFil, Data, Remessa, QtdeVol, Guia, Serie, Lacre, Operador, Dt_Alter, Hr_Alter)\n"
                    + "		Values(@codfil, @data, 1, 1, @gtv, @seriegtv, @lacre, @operador, @dtalter, @hralter);\n"
                    + "\n"
                    + "		--Gerando TesSaidasLacres\n"
                    + "		Insert into TesSaidasLacres (Guia, Serie, Malote, Lacre, Operador, Dt_Alter, Hr_Alter)\n"
                    + "		Values(@gtv, @serieGtv, 1, @lacre, @operador, @dtalter, @hralter);\n"
                    + "\n"
                    + "		--Gerando Entrada CXF		\n"
                    + "		----Verificando se existe rota 090\n"
                    + "		Select @seqrota = Sequencia from Rotas where Data = @data and CodFil = @codfil and Rota = '090' and Flag_Excl <> '*';\n"
                    + "		if((@seqrota <= 0) or (@seqrota is null)) begin			\n"
                    + "			Insert into Rotas (Sequencia, Rota, Data, CodFil, TpVeic, Viagem, ATM, Bacen, Aeroporto, HrLargada, HrChegada, HrIntIni, HrIntFim, \n"
                    + "			HsTotal, DtFim, Operador, Dt_Alter, Hr_Alter, Flag_Excl)\n"
                    + "			Select Max(Isnull(Sequencia,0))+1, '090', @data, @codfil, 'N', 'N', 'N', 'N', 'N', '08:00', '18:00', '12:00', '13:00', 8, @data,\n"
                    + "			@operador, @dtalter, @hralter, '' from Rotas;\n"
                    + "			\n"
                    + "			Select @seqrota = Sequencia from Rotas where Data = @data and CodFil = @codfil and Rota = '090' and Flag_Excl <> '*';			\n"
                    + "		end\n"
                    + "		\n"
                    + "		--Verificando existencia Caixa-Forte Manha\n"
                    + "		if((Select Isnull(Count(*),0) from Rt_Perc where Sequencia = @seqrota and CodCli1 = @cxforte and Hora1 >= '0800' and Hora1 <= '1100' and Flag_Excl <> '*') <= 0) begin\n"
                    + "		   --Buscando Hora1 Disponivel\n"
                    + "			WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @horaCxfManha and Flag_Excl <> '*') > 0 BEGIN      \n"
                    + "				--Caso já exista serviço para outro cliente no horário adiciona @acrescimoMinutos\n"
                    + "				set @horaCxfManha = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@horaCxfManha,1,2)+':'+Substring(@horaCxfManha,3,2))),113),1,5),':','');	  \n"
                    + "				IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @horaCxfManha and Flag_Excl <> '*') = 0\n"
                    + "					BREAK  \n"
                    + "			ELSE  \n"
                    + "				CONTINUE  \n"
                    + "			END\n"
                    + "\n"
                    + "			Select @seqParadaCxf = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota;					\n"
                    + "			Insert into Rt_Perc (Sequencia, Hora1, Parada, CodCli1, Nred, Regiao, ER, TipoSrv, CodFil, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ) \n"
                    + "			Values (@seqrota, @horaCxfManha, @seqParadaCxf, @cxforte, @cxforteNred, @cxforteRegiao, 'E', 'R', @codfil, @operador,@dtalter,@hralter, '', '', '',@observ);	\n"
                    + "		end\n"
                    + "\n"
                    + "		--Verificando existencia Caixa-Forte Noite\n"
                    + "		Select @cxfExiste = Isnull(Count(*),0) from Rt_Perc where Sequencia = @seqrota and CodCli1 = @cxforte and Hora1 >= '1700' and Hora1 <= '2000' and Flag_Excl <> '*';\n"
                    + "		if(@cxfExiste <= 0) begin		\n"
                    + "			--Buscando Hora1 Disponivel\n"
                    + "			WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @horaCxfNoite and Flag_Excl <> '*') > 0 begin\n"
                    + "      \n"
                    + "				--Caso já exista serviço para outro cliente no horário adiciona @acrescimoMinutos\n"
                    + "				set @horaCxfNoite = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@horaCxfNoite,1,2)+':'+Substring(@horaCxfNoite,3,2))),113),1,5),':','');\n"
                    + "	  \n"
                    + "				IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @horaCxfNoite and Flag_Excl <> '*') = 0\n"
                    + "					BREAK  \n"
                    + "			ELSE  \n"
                    + "				CONTINUE  \n"
                    + "			END\n"
                    + "			\n"
                    + "			--Gerando número parada destino\n"
                    + "			Select @seqParadaDst = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqrota;					\n"
                    + "			Insert into Rt_Perc (Sequencia, Hora1, CodCli1, Nred, Regiao, Parada, ER, TipoSrv, CodFil, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ) \n"
                    + "			Values (@seqRota, @horaCxfNoite, @cxforte, @cxforteNred, @cxforteRegiao, @seqParadaDst, 'E', 'R', @codfil, @operador,@dtalter,@hralter, '', '', '',@observ);	\n"
                    + "\n"
                    + "			--Atualizando Hora1D Origem\n"
                    + "			Update Rt_Perc set Hora1D = @horaCxfNoite, dPar = @seqParadaDst where Sequencia = @seqrota and Parada = @seqParadaCxf;\n"
                    + "\n"
                    + "		end\n"
                    + "\n"
                    + "		----Verificando se existe parada para tesouraria na rota 090\n"
                    + "		--Select @seqrota = Sequencia from Rotas where Data = @data and CodFil = @codfil and Rota = '090' and Flag_Excl <> '*';\n"
                    + "		Select @tesexiste = Isnull(Count(*),0) from Rt_Perc where Sequencia = @seqrota and CodCli1 = @codtes and ER = 'R' and Flag_Excl <> '*';\n"
                    + "		set @hora1 = '0810';		\n"
                    + "		if(@tesexiste <= 0) begin\n"
                    + "\n"
                    + "			--Buscando Hora1 Disponivel\n"
                    + "			WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @hora1 and Flag_Excl <> '*') > 0 BEGIN\n"
                    + "      \n"
                    + "				--Caso já exista serviço para outro cliente no horário adiciona @acrescimoMinutos\n"
                    + "				set @hora1 = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@hora1,1,2)+':'+Substring(@hora1,3,2))),113),1,5),':','');\n"
                    + "	  \n"
                    + "				IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqrota and Hora1 = @hora1 and Flag_Excl <> '*') = 0\n"
                    + "				BREAK  \n"
                    + "			ELSE  \n"
                    + "				CONTINUE  \n"
                    + "			END\n"
                    + "\n"
                    + "			Select @seqparada = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqrota;					\n"
                    + "			Insert into Rt_Perc (Sequencia, Hora1, Parada, CodCli1, Nred, Regiao, ER, TipoSrv, CodFil, OperIncl, \n"
                    + "			Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ,CodCli2) \n"
                    + "			Values (@seqrota, @hora1, @seqparada, @codtes, @nredtes, @cxforteRegiao, 'R', 'R', @codfil, @operador,@dtalter,@hralter, '', '', '',@observ, @cxforte);	\n"
                    + "\n"
                    + "			Select @horaCxfNoite = Hora1,@seqParadaDst = Parada from Rt_Perc where Sequencia = @seqrota and codcli1 = @cxforte and Hora1 > @hora1 and Flag_Excl <> '*';\n"
                    + "			Update Rt_Perc set Hora1D = @horaCxfNoite, dPar = @seqParadaDst where Sequencia = @seqrota and Parada = @seqparada;\n"
                    + "\n"
                    + "		end\n"
                    + "\n"
                    + "		----Registrando Entrada CXF\n"
                    + "		Select \n"
                    + "		@seqparada = Sequencia, \n"
                    + "		@hora1 = Hora1\n"
                    + "		from Rt_Perc where Sequencia = @seqrota and CodCli1 = @codtes and ER = 'R' and Flag_Excl <> '*';\n"
                    + "\n"
                    + "		Insert into CxFGuias (CodFil, CodCli, Guia, Serie, CliOri, CliDst, Valor, Tipo, RotaEnt, OperEnt, DtEnt, HrEnt, SeqRota, OS, Hora1, Volumes, \n"
                    + "                              VolDh, ValorDh, VolCh, ValorCh, VolMd, ValorMd, VolMet, ValorMet, VolMEstr, ValorMEstr, VolOutr, ValorOutr ) \n"
                    + "		Values (@codfil, @cxforte,@gtv, @seriegtv,@codtes, @codtes, @valortotal, @tiposrv, '090', @operador, @data, @hralter, @seqrota, @os, @hora1,0,0,0,0,0,0,0,0,0,0,0,0,0);\n"
                    + "\n"
                    + "		Insert into CXFGuiasVol (CodFil, CodCli, Guia, Serie, Ordem, Qtde, Lacre, Tipo, Obs, Valor) \n"
                    + "		Values (@codfil, @codtes, @gtv, @seriegtv,1, 1, @lacre, 1, '', @valortotal);\n"
                    + "\n"
                    + "	end  \n"
                    + "\n"
                    + "	Delete from #pedidos where Numero = @pedido;\n"
                    + "   IF (SELECT count(*) FROM #pedidos) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + "END  \n"
                    + "Drop table #pedidos; \n"
                    + "-------------------------------------------------\n"
                    + "--Buscando pedidos roteirizados e gerando Cxforte\n"
                    + "--saida------------------------------------------\n"
                    + "Select \n"
                    + "Pedido.Numero, Pedido.CodFil, Pedido.Data, Rt_Perc.CodCli1, Pedido.OS, Pedido.Valor, \n"
                    + "Pedido.SeqRota, Pedido.Parada, Rotas.Rota, Pedido.ClassifSRV, OS_Vig.CodSrv, TesSaidas.Guia, TesSaidas.Serie,\n"
                    + "Rt_Perc.Hora1\n"
                    + "into #pedidos2\n"
                    + "From Pedido \n"
                    + "Inner join Rt_Perc  on Rt_Perc.Sequencia = Pedido.SeqRota\n"
                    + "                   and Rt_Perc.Parada = Pedido.Parada\n"
                    + "Inner join Rotas  on Rotas.Sequencia = Pedido.SeqRota\n"
                    + "Left join OS_Vig  on OS_Vig.OS = Pedido.OS\n"
                    + "                 and OS_Vig.CodFil = Pedido.CodFil\n"
                    + "Inner join TesSaidas  on TesSaidas.Pedido = Pedido.Numero\n"
                    + "                     and TesSaidas.CodFil = Pedido.CodFil\n"
                    + "Left join CxfGuias  on CxfGuias.SeqRotaSai = Rt_Perc.Sequencia\n"
                    + "                   and Cxfguias.Hora1D = Rt_Perc.Hora1\n"
                    + "where Pedido.data = @data\n"
                    + "  and Pedido.CodFil = @codfil\n"
                    + "  and Pedido.CodCli1 = @cxforte\n"
                    + "  and Pedido.Flag_Excl <> '*'\n"
                    + "  and Rt_Perc.Flag_Excl <> '*'\n"
                    + "  and Pedido.Tipo = 'T'\n"
                    + "  and CxfGuias.SeqRotaSai is null\n"
                    + "  and Pedido.OS > 0;\n"
                    + "\n"
                    + "WHILE (SELECT count(*) FROM #pedidos2) > 0\n"
                    + "BEGIN \n"
                    + "\n"
                    + "	Select top 01 \n"
                    + "	@pedido = Numero,\n"
                    + "	@codcli1 = CodCli1,\n"
                    + "	@hora1 = Hora1,\n"
                    + "	@os = OS,\n"
                    + "	@tiposrv = ClassifSRV,\n"
                    + "	@pedidovalor = Valor,\n"
                    + "	@seqrota = SeqRota,\n"
                    + "	@seqparada = Parada,\n"
                    + "	@rota = Rota,\n"
                    + "	@codsrv = CodSRV,\n"
                    + "	@gtv = Guia, \n"
                    + "	@seriegtv = Serie\n"
                    + "	from #pedidos2\n"
                    + "	Order by Rota;\n"
                    + "\n"
                    + "	Update CxFGuias set \n"
                    + "           RotaSai    = @rota, \n"
                    + "           SeqRotaSai = @seqrota, \n"
                    + "           Remessa    = 9999,\n"
                    + "           Hora1D     = @hora1, \n"
                    + "           CliDst     =  @codcli1, \n"
                    + "           OperSai    = @operador,\n"
                    + "           Dtsai      = @data,\n"
                    + "           HrSai      = @hralter,\n"
                    + "           OS         = @os,\n"
                    + "           Lote       = @codsrv\n"
                    + "           where Guia = @gtv\n"
                    + "             and Serie = @seriegtv\n"
                    + "             and CodFil = @codfil;\n"
                    + "			 		\n"
                    + "	Delete from #pedidos2 where Numero = @pedido;\n"
                    + "   IF (SELECT count(*) FROM #pedidos2) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + "END  \n"
                    + "Drop table #pedidos2; \n"
                    + "-------------------------------------------------\n"
                    + "--Buscando pedidos roteirizados e gerando remessa\n"
                    + "--saida CXF e Baixando estas remessas------------\n"
                    + "Select SeqRotaSai, Count(*) Qtde, Sum(Volumes) Volumes, Sum(Valor) Valor\n"
                    + "into #remessaspendentes \n"
                    + "from CxfGuias where DtSai = @data \n"
                    + "and Remessa = 9999 \n"
                    + "and CodFil = @codfil\n"
                    + "Group by SeqRotaSai;\n"
                    + "\n"
                    + "WHILE (SELECT count(*) FROM #remessaspendentes) > 0\n"
                    + "BEGIN \n"
                    + "\n"
                    + "	Select top 01\n"
                    + "	@seqrota = SeqRotaSai,\n"
                    + "	@qtde = Qtde, \n"
                    + "	@volumes = Volumes, \n"
                    + "	@valor = Valor\n"
                    + "	From #remessaspendentes;\n"
                    + "\n"
                    + "	Select @remessa = isnull(Remessa,0)+1 from CxFSaidas where SeqRota = @seqrota;\n"
                    + "	if (@remessa is null)\n"
                    + "		set @remessa = 1;\n"
                    + "\n"
                    + "	set @codremessa = Replicate('0', 6-len(Convert(Varchar,@seqrota))) + Convert(Varchar,@seqrota)+Replicate('0', 3-len(Convert(Varchar,@remessa)))+Convert(Varchar,@remessa);\n"
                    + "\n"
                    + "	Insert into CxfSaidas (CodFil, SeqRota, Remessa, CodRemessa, Qtde, QtdeVol, Valor, Oper_Prep, Dt_Prep, Hr_Prep, Oper_Saida, Dt_Saida, Hr_Saida)\n"
                    + "	Values(@codfil, @seqrota, @remessa, @codremessa, @qtde, @volumes, @valor, @operador, @dtalter, @hralter, @operador, @dtalter, @hralter);\n"
                    + "\n"
                    + "	Update CxfGuias set Remessa = @remessa where SeqRotaSai = @seqrota and Remessa = 9999;\n"
                    + "\n"
                    + "	Delete from #remessaspendentes where SeqRotaSai = @seqrota;\n"
                    + "   IF (SELECT count(*) FROM #remessaspendentes) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + "END  \n"
                    + "Drop table #remessaspendentes;\n";
            consulta = new Consulta(sqlOriginal, persistencia);
            consulta.setString(codFil);
            consulta.setString(data.replace("-", ""));
            consulta.setString("53");
            consulta.setString(operador);
            consulta.setString(dt_alter.replace("-", ""));
            consulta.setString(hr_alter);
            return consulta.insert();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    /**
     * gerarSaidasTesouraria
     *
     * @param data
     * @param codFil
     * @param serie
     * @param tesouraria
     * @param operador
     * @param dt_alter
     * @param hr_alter
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public int gerarSaidasTesouraria(
            String data,
            String codFil,
            String serie,
            String tesouraria,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {
        Consulta consulta = null;
        try {
            String sql = "Declare @codfil float;\n"
                    + "Declare @data Date;\n"
                    + "Declare @pedido int;\n"
                    + "Declare @codcli1 Varchar(7);\n"
                    + "Declare @codcli2 Varchar(7);\n"
                    + "Declare @codtes Varchar(7);\n"
                    + "Declare @nredtes Varchar(7);\n"
                    + "Declare @clifat Varchar(7);\n"
                    + "Declare @codconta Float;\n"
                    + "Declare @codlote Varchar(3);\n"
                    + "Declare @os int;\n"
                    + "Declare @pedidovalor Float;\n"
                    + "Declare @serieGtv Varchar(3);\n"
                    + "Declare @gtv float;\n"
                    + "Declare @tiposrv Varchar(3);\n"
                    + "Declare @obs Varchar(80);\n"
                    + "Declare @operador Varchar(10);\n"
                    + "Declare @dtalter date;\n"
                    + "Declare @hralter Varchar(5);\n"
                    + "Declare @lacre Varchar(8);\n"
                    + "Declare @tesexiste int;\n"
                    + "Declare @seqrota float;\n"
                    + "Declare @rota float;\n"
                    + "Declare @rotaant float;\n"
                    + "Declare @hora1 varchar(5);\n"
                    + "Declare @seqparada int;\n"
                    + "Declare @cxforte varchar(07);\n"
                    + "Declare @cxforteNred Varchar(20);\n"
                    + "Declare @cxforteRegiao Varchar(03);\n"
                    + "Declare @horaCxfManha Varchar(05);\n"
                    + "Declare @horaCxfNoite Varchar(05);\n"
                    + "Declare @seqParadaOri int;\n"
                    + "Declare @seqParadaDst int;\n"
                    + "Declare @acrescimoMinutos int;\n"
                    + "Declare @seqParadaCxf int;\n"
                    + "Declare @cxfExiste int;\n"
                    + "Declare @observ Varchar(20);\n"
                    + "Declare @valordn float;\n"
                    + "Declare @valormd float;\n"
                    + "Declare @valortotal float;\n"
                    + "Declare @codsrv Varchar(03);\n"
                    + "Declare @qtde Float;\n"
                    + "Declare @volumes Float;\n"
                    + "Declare @valor Float;\n"
                    + "Declare @codremessa VarChar(10);\n"
                    + "Declare @remessa Float;\n"
                    + "Declare @tesouraria Varchar(07);\n"
                    + "\n"
                    + "-------------------------------------------------\n"
                    + "--Parametros-------------------------------------\n"
                    + "Set @codfil = ? ;\n"
                    + "Set @data = ? ;\n"
                    + "Set @serieGtv = ? ;\n"
                    + "Set @tesouraria = ? ;\n"
                    + "Set @operador = ? ;\n"
                    + "Set @dtalter = ? ;\n"
                    + "Set @hralter = ? ;\n"
                    + "set @observ = '';\n"
                    + "-------------------------------------------------\n"
                    + "--Buscando dados CxForte\n"
                    + "Select top 1 \n"
                    + "@cxforte = Clientes.Codigo,\n"
                    + "@cxforteNred = Clientes.Nred,\n"
                    + "@cxforteRegiao = Clientes.Regiao\n"
                    + "from CxForte \n"
                    + "Left join Clientes  on Clientes.Codigo = CxForte.CodCli\n"
                    + "					and Clientes.CodFil = CxForte.CodFil\n"
                    + "where CxForte.CodFil = @codfil \n"
                    + "Order By DtFecha Desc;\n"
                    + "-------------------------------------------------\n"
                    + "--Buscando pedidos e gerando tesSaidas e Cxforte-\n"
                    + "--entrada----------------------------------------\n"
                    + "Select \n"
                    + "Pedido.Numero, Pedido.CodFil, Pedido.Data, Pedido.CodCli1, Pedido.CodCli2, Pedido.OS, Pedido.Valor, Pedido.ClassifSRV, Pedido.OBS\n"
                    + "into #pedidos\n"
                    + "From Pedido \n"
                    + "Left join OS_Vig  on OS_Vig.OS = Pedido.OS\n"
                    + "                 and OS_Vig.CodFil = Pedido.CodFil\n"
                    + "where Pedido.data = @data\n"
                    + "  and Pedido.CodFil = @codfil\n"
                    + "  and Pedido.CodCli1 = @cxforte\n"
                    + "  and Pedido.Numero not in (Select Pedido from TesSaidas where TesSaidas.CodFil = Pedido.CodFil and TesSaidas.Pedido is not null)\n"
                    + "  and OS_Vig.CliDst = @tesouraria\n"
                    + "  and Pedido.Flag_Excl <> '*'\n"
                    + "  and Pedido.Tipo = 'T';\n"
                    + "\n"
                    + "WHILE (SELECT count(*) FROM #pedidos) > 0\n"
                    + "BEGIN \n"
                    + "\n"
                    + "	set @valordn = 0;\n"
                    + "	set @valormd = 0;\n"
                    + "    set @valortotal = 0;\n"
                    + "\n"
                    + "	Select top 01 \n"
                    + "	@pedido = Numero,\n"
                    + "	@codcli1 = CodCli1,\n"
                    + "	@codcli2 = CodCli2,\n"
                    + "	@os = OS,\n"
                    + "	@tiposrv = ClassifSRV,\n"
                    + "	@pedidovalor = Valor,\n"
                    + "	@obs = OBS\n"
                    + "	from #pedidos;\n"
                    + "\n"
                    + "	Select\n"
                    + "	@codtes = OS_Vig.CliDst,\n"
                    + "	@nredtes = OS_Vig.NRedDst,\n"
                    + "	@codconta = OS_VTes.ContaPdr,\n"
                    + "    @codlote = LotePdr,\n"
                    + "	@clifat = OS_Vig.CliFat\n"
                    + "	from OS_Vig\n"
                    + "	Left join OS_VTes  on OS_Vig.OS = OS_VTes.OS\n"
                    + "	                  and OS_Vig.CodFil = OS_VTes.CodFil\n"
                    + "	where OS_Vig.OS = @os;\n"
                    + "	--Verificando se clientes destino da OS é tesouraria\n"
                    + "	if(Substring(@codtes,4,1) = 7) begin\n"
                    + "		--Gerando GTV\n"
                    + "		Insert into GTV (CodFil, Guia, Serie, Pedido, DtGeracao, OS, Situacao, Status, Operador, Dt_alter, Hr_alter)\n"
                    + "		Select @codfil, ISNULL((SELECT (Max(Isnull(Guia,0))+1) From GTV where Serie = @serieGtv),1), @serieGtv, @pedido, @data, @os, 2, 'PD', @operador, @dtalter, @hralter\n"
                    //+ "		From GTV where Serie = @serieGtv\n"
                    + " ;\n"
                    + "\n"
                    + "		Select @gtv = Guia from GTV where Pedido = @pedido and CodFil = @codfil and Serie = @serieGtv;\n"
                    + "\n"
                    + "		--Inserindo TesSaidas\n"
                    + "		Insert into TesSaidas (Guia, Serie, CodFil, Data, CodGTV, TipoMov, CodCli1, CodCli2, CodCli3, ContaTes,  \n"
                    + "		TotalDN, TotalMoeda, TotalGeral, Pedido, TipoSrv, Situacao, Obs, Baixa, ChequesQtde, ChequesValor, TicketsQtde, \n"
                    + "		TicketsValor, Dt_Pedido, Operador, Dt_alter, Hr_alter) \n"
                    + "		Values(@gtv, @serieGtv, @codfil, @data, (Select min(Codigo) from GTVSeq where Serie = @serieGtv), 0,@codtes,@codcli2,\n"
                    + "		@clifat, @codconta, @pedidovalor, 0, @pedidovalor, @pedido, @tiposrv, 'OK', @obs, 'S', 0, 0, 0, 0, @data, @operador, @dtalter, @hralter);\n"
                    + "\n"
                    + "		--Inserindo TesSaidasDN\n"
                    + "		Insert into TesSaidasDN (Guia, Serie, Codigo, Docto, Qtde, Valor, Operador, Dt_alter, Hr_alter)\n"
                    + "		Select @gtv, @serieGtv, Codigo, Docto, Qtde,\n"
                    + "		((Case when codigo = 11 then 10 \n"
                    + "		     when codigo = 21 then 20 \n"
                    + "			 when codigo = 51 then 50 \n"
                    + "			 when codigo = 101 then 100\n"
                    + "			 when codigo = 102 then 2\n"
                    + "			 when codigo = 105 then 5\n"
                    + "			 else codigo end)*Qtde),\n"
                    + "		@operador, @dtalter, @hralter  from PedidoDN \n"
                    + "		where Numero = @pedido\n"
                    + "		  and CodFil = @codfil;\n"
                    + "\n"
                    + "		--Inserindo TesSaidasMD\n"
                    + "		Insert into TesSaidasMD (Guia, Serie, Codigo, Docto, Qtde, Valor, Operador, Dt_alter, Hr_alter)\n"
                    + "		Select @gtv, @serieGtv, Codigo, Docto, Qtde, Round(((Codigo*qtde)/100),2), @operador, @dtalter, @hralter from PedidoMD \n"
                    + "		where Numero = @pedido\n"
                    + "		  and CodFil = @codfil;\n"
                    + "\n"
                    + "	    --Ajustando valor TesSaidas\n"
                    + "	    Select @valordn = sum(isnull(Valor,0)) from TesSaidasDN where Guia = @gtv and Serie = @serieGtv;\n"
                    + "		if(@valordn is null)\n"
                    + "		set @valordn = 0;\n"
                    + "\n"
                    + "		Select @valormd = sum(isnull(Valor,0)) from TesSaidasMD where Guia = @gtv and Serie = @serieGtv;	\n"
                    + "		if(@valormd is null)\n"
                    + "			set @valormd = 0;		\n"
                    + "\n"
                    + "		set @valortotal = @valordn + @valormd;\n"
                    + "		\n"
                    + "		\n"
                    + "		Update TesSaidas Set \n"
                    + "	    TotalDN = @valordn,\n"
                    + "	    TotalMoeda = @valormd,\n"
                    + "	    TotalGeral = @valortotal\n"
                    + "	    from TesSaidas \n"
                    + "	    where Guia = @gtv\n"
                    + "	      and Serie = @serieGtv;\n"
                    + "\n"
                    + "	end	\n"
                    + "	Delete from #pedidos where Numero = @pedido;\n"
                    + "   IF (SELECT count(*) FROM #pedidos) = 0\n"
                    + "      BREAK  \n"
                    + "   ELSE  \n"
                    + "      CONTINUE  \n"
                    + "END  \n"
                    + "Drop table #pedidos;\n"
                    + "UPDATE GtvSeq\n"
                    + "SET Sequencia = (SELECT Max(Guia) FROM GTV WHERE Serie = @serieGtv AND codfil = @codfil)\n"
                    + "WHERE serie = @serieGtv\n"
                    + "  AND CodFil = @codfil;";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(data);
            consulta.setString(serie);
            consulta.setString(tesouraria);
            consulta.setString(operador);
            consulta.setString(dt_alter);
            consulta.setString(hr_alter);

            return consulta.insert();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public Rotas buscarDadosSeqRota(String seqRota, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            Rotas retorno = new Rotas();

            sql = "SELECT\n"
                    + " Sequencia,\n"
                    + " Rota,\n"
                    + " CodFil\n"
                    + " FROM Rotas \n"
                    + " WHERE Sequencia = ?";
            
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            
            consulta.select();
            
            while(consulta.Proximo()){
                retorno.setRota(consulta.getString("Rota"));
                retorno.setSequencia(consulta.getString("Sequencia"));
                retorno.setCodFil(consulta.getString("CodFil"));
            }
            
            consulta.close();
            
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasDao.buscarDadosSeqRota - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }
}

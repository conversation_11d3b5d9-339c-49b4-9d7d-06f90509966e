/*
 */
package br.com.sasw.managedBeans.cadastros;

import Arquivo.ArquivoLog;
import Controller.TbVal.ControlerTbVal;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.TbVal;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

/**
 *
 * <AUTHOR>
 */
@Named(value = "tbval")
@ViewScoped
public class TbValMB implements Serializable {

    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private final String banco;
    private String nomeFilial, caminho, log;
    private ArquivoLog logerro;
    private final String operador;
    private List<TbVal> lista;
    private ControlerTbVal controlertbval;
    private TbVal selecionado;
    private TbVal novo;

    TbValMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(20);
        pool.setCaminho(FacesContext.getCurrentInstance().getExternalContext().getRealPath("/mapconect.txt"));
        lista = new ArrayList<>();
        controlertbval = new ControlerTbVal();
        selecionado = new TbVal();
        novo = new TbVal();
        log = new String();
        caminho = FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator) + "msgerros_" + banco + ".txt";
        logerro = new ArquivoLog();
        try {
            persistencia = pool.getConexao(banco);
            if (null == persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()) + " " + banco, null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void Listar() {
        try {
            this.lista = controlertbval.Listagem312(this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void Cadastrar() {
        try {
            this.novo.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            controlertbval.Inserir312(this.novo, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        Listar();
    }

    public List<TbVal> getLista() {
        return lista;
    }

    public void setLista(List<TbVal> lista) {
        this.lista = lista;
    }

    public TbVal getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(TbVal selecionado) {
        this.selecionado = selecionado;
    }

    public TbVal getNovo() {
        return novo;
    }

    public void setNovo(TbVal novo) {
        this.novo = novo;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

}

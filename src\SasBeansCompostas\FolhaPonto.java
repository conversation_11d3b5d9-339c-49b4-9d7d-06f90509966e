/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.FPLancamentos;
import SasBeans.Verbas;

/**
 *
 * <AUTHOR>
 */
public class FolhaPonto {

    private FPLancamentos fPLancamentos;
    private Verbas verbas;

    /**
     * @return the fPLancamentos
     */
    public FPLancamentos getfPLancamentos() {
        return fPLancamentos;
    }

    /**
     * @param fPLancamentos the fPLancamentos to set
     */
    public void setfPLancamentos(FPLancamentos fPLancamentos) {
        this.fPLancamentos = fPLancamentos;
    }

    /**
     * @return the verbas
     */
    public Verbas getVerbas() {
        return verbas;
    }

    /**
     * @param verbas the verbas to set
     */
    public void setVerbas(Verbas verbas) {
        this.verbas = verbas;
    }
}

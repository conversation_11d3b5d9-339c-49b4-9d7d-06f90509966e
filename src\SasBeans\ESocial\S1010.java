/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1010 {

    private int sucesso;
    private String evtTabRubrica_Id;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    /**
     *
     */
    private String ideRubrica_codRubr;
    /**
     *
     */
    private String ideRubrica_ideTabRubr;
    private String ideRubrica_iniValid;
    private String dadosRubrica_dscRubr;
    /**
     * Tabela 03
     */
    private String dadosRubrica_natRubr;
    /**
     * Tipo de rubrica: 1 - Vencimento, provento ou pensão; 2 - Desconto; 3 -
     * Informativa; 4 - Informativa dedutora. Valores Válidos: 1, 2, 3, 4
     */
    private String dadosRubrica_tpRubr;
    /**
     * Código de incidência tributária da rubrica para a Previdência Social: 00
     * - Não é base de cálculo; 01 - Não é base de cálculo em função de acordos
     * internacionais de previdência social;
     *
     * Base de cálculo das contribuições sociais - Salário de Contribuição: 11 -
     * Mensal; 12 - 13o Salário; 13 - Exclusiva do Empregador - mensal; 14 -
     * Exclusiva do Empregador - 13° salário; 15 - Exclusiva do segurado -
     * mensal; 16 - Exclusiva do segurado - 13° salário; 21 - Salário
     * maternidade mensal pago pelo Empregador; 22 - Salário maternidade - 13o
     * Salário, pago pelo Empregador; 23 - Auxilio doença mensal - Regime
     * Próprio de Previdência Social; 24 - Auxilio doença 13o salário doença -
     * Regime próprio de previdência social; 25 - Salário maternidade mensal
     * pago pelo INSS; 26 - Salário maternidade - 13° salário, pago pelo INSS;
     *
     * Contribuição descontada do Segurado sobre salário de contribuição: 31 -
     * Mensal; 32 - 13o Salário; 34 - SEST; 35 - SENAT;
     *
     * Outros: 51 - Salário-família; 61 - Complemento de salário-mínimo - Regime
     * próprio de previdência social;
     *
     * Suspensão de incidência sobre Salário de Contribuição em decorrência de
     * decisão judicial: 91 - Mensal; 92 - 13o Salário; 93 - Salário
     * maternidade; 94 - Salário maternidade 13o salário; 95 - Exclusiva do
     * Empregador - mensal; 96 - Exclusiva do Empregador - 13º salário; 97 -
     * Exclusiva do Empregador - Salário maternidade; 98 - Exclusiva do
     * Empregador - Salário maternidade 13º salário.
     *
     * Validação: Para utilização dos códigos [91,92,93,94,95,96,97,98], é
     * necessária a existência de registro complementar com informações de
     * processo. Valores Válidos: 00, 01, 11, 12, 13, 14, 15, 16, 21, 22, 23,
     * 24, 25, 26, 31, 32, 34, 35, 51, 61, 91, 92, 93, 94, 95, 96, 97, 98.
     */
    private String dadosRubrica_codIncCP;
    /**
     * Código de incidência tributária da rubrica para o IRRF: 00 - Rendimento
     * não tributável; 01 - Rendimento não tributável em função de acordos
     * internacionais de bitributação; 09 - Outras verbas não consideradas como
     * base de cálculo ou rendimento;
     *
     * Rendimentos tributáveis - base de cálculo do IRRF: 11 - Remuneração
     * mensal; 12 - 13o Salário; 13 - Férias; 14 - PLR; 15 - Rendimentos
     * Recebidos Acumuladamente - RRA;
     *
     * Retenções do IRRF efetuadas sobre: 31 - Remuneração mensal; 32 - 13o
     * Salário; 33 - Férias; 34 - PLR; 35 - RRA;
     *
     * Deduções da base de cálculo do IRRF: 41 - Previdência Social Oficial -
     * PSO - Remuner. mensal; 42 - PSO - 13° salário; 43 - PSO - Férias; 44 -
     * PSO - RRA; 46 - Previdência Privada - salário mensal; 47 - Previdência
     * Privada - 13° salário; 51 - Pensão Alimentícia - Remuneração mensal; 52 -
     * Pensão Alimentícia - 13° salário; 53 - Pensão Alimentícia - Férias; 54 -
     * Pensão Alimentícia - PLR; 55 - Pensão Alimentícia - RRA; 61 - Fundo de
     * Aposentadoria Programada Individual - FAPI - Remuneração mensal; 62 -
     * Fundo de Aposentadoria Programada Individual - FAPI - 13° salário; 63 -
     * Fundação de Previdência Complementar do Servidor Público - Funpresp -
     * Remuneração mensal; 64 - Fundação de Previdência Complementar do Servidor
     * Público - Funpresp - 13° salário;
     *
     * Isenções do IRRF: 70 - Parcela Isenta 65 anos - Remuneração mensal; 71 -
     * Parcela Isenta 65 anos - 13° salário; 72 - Diárias; 73 - Ajuda de custo;
     * 74 - Indenização e rescisão de contrato, inclusive a título de PDV e
     * acidentes de trabalho; 75 - Abono pecuniário; 76 - Pensão, aposentadoria
     * ou reforma por moléstia grave ou acidente em serviço - Remuneração
     * Mensal; 77 - Pensão, aposentadoria ou reforma por moléstia grave ou
     * acidente em serviço - 13° salário; 78 - Valores pagos a titular ou sócio
     * de microempresa ou empresa de pequeno porte, exceto pró-labore e
     * alugueis; 79 - Outras isenções (o nome da rubrica deve ser claro para
     * identificação da natureza dos valores);
     *
     * Demandas Judiciais: 81 - Depósito judicial; 82 - Compensação judicial do
     * ano calendário; 83 - Compensação judicial de anos anteriores;
     *
     * Incidência Suspensa decorrente de decisão judicial, relativas a base de
     * cálculo do IRRF sobre: 91 - Remuneração mensal; 92 - 13o Salário; 93 -
     * Férias; 94 - PLR; 95 - RRA.
     *
     * Validação: Deve ser um dos códigos disponibilizados nesse campo. No caso
     * de preenchimento com os códigos [91,92,93,94,95], é necessária a
     * existência de registro complementar com as informações do processo.
     * Valores Válidos:
     * 00,01,09,11,12,13,14,15,31,32,33,34,35,41,42,43,44,46,47,51,52,53,54,55,61,62,63,64,70,71,72,73,74,75,76,77,78,79,81,82,83,91,92,93,94,95
     */
    private String dadosRubrica_codIncIRRF;
    /**
     * Código de incidência da rubrica para o FGTS: 00 - Não é Base de Cálculo
     * do FGTS; 11 - Base de Cálculo do FGTS; 12 - Base de Cálculo do FGTS 13°
     * salário; 21 - Base de Cálculo do FGTS Rescisório (aviso prévio); 91 -
     * Incidência suspensa em decorrência de decisão judicial. Validação: No
     * caso de preenchimento com o código 91, é necessária a existência de
     * registro complementar com informações relativas ao processo Valores
     * Válidos: 00, 11, 12, 21, 91
     */
    private String dadosRubrica_codIncFGTS;
    /**
     * Código de incidência tributária da rubrica para a Contribuição Sindical
     * Laboral: 00 - Não é base de cálculo; 11 - Base de cálculo; 31 - Valor da
     * contribuição sindical laboral descontada; 91 - Incidência suspensa em
     * decorrência de decisão judicial Validação: No caso de preenchimento com o
     * código 91, é necessária a existência de registro complementar com
     * informações do processo. Valores Válidos: 00, 11, 31, 91
     */
    private String dadosRubrica_codIncSIND;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtTabRubrica_Id() {
        return evtTabRubrica_Id;
    }

    public void setEvtTabRubrica_Id(String evtTabRubrica_Id) {
        this.evtTabRubrica_Id = evtTabRubrica_Id;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeRubrica_codRubr() {
        return ideRubrica_codRubr;
    }

    public void setIdeRubrica_codRubr(String ideRubrica_codRubr) {
        this.ideRubrica_codRubr = ideRubrica_codRubr;
    }

    public String getIdeRubrica_ideTabRubr() {
        return ideRubrica_ideTabRubr;
    }

    public void setIdeRubrica_ideTabRubr(String ideRubrica_ideTabRubr) {
        this.ideRubrica_ideTabRubr = ideRubrica_ideTabRubr;
    }

    public String getIdeRubrica_iniValid() {
        return ideRubrica_iniValid;
    }

    public void setIdeRubrica_iniValid(String ideRubrica_iniValid) {
        this.ideRubrica_iniValid = ideRubrica_iniValid;
    }

    public String getDadosRubrica_dscRubr() {
        return dadosRubrica_dscRubr;
    }

    public void setDadosRubrica_dscRubr(String dadosRubrica_dscRubr) {
        this.dadosRubrica_dscRubr = dadosRubrica_dscRubr;
    }

    public String getDadosRubrica_natRubr() {
        return dadosRubrica_natRubr;
    }

    public void setDadosRubrica_natRubr(String dadosRubrica_natRubr) {
        this.dadosRubrica_natRubr = dadosRubrica_natRubr;
    }

    public String getDadosRubrica_tpRubr() {
        return dadosRubrica_tpRubr;
    }

    public void setDadosRubrica_tpRubr(String dadosRubrica_tpRubr) {
        this.dadosRubrica_tpRubr = dadosRubrica_tpRubr;
    }

    public String getDadosRubrica_codIncCP() {
        return dadosRubrica_codIncCP;
    }

    public void setDadosRubrica_codIncCP(String dadosRubrica_codIncCP) {
        this.dadosRubrica_codIncCP = dadosRubrica_codIncCP;
    }

    public String getDadosRubrica_codIncIRRF() {
        return dadosRubrica_codIncIRRF;
    }

    public void setDadosRubrica_codIncIRRF(String dadosRubrica_codIncIRRF) {
        this.dadosRubrica_codIncIRRF = dadosRubrica_codIncIRRF;
    }

    public String getDadosRubrica_codIncFGTS() {
        return dadosRubrica_codIncFGTS;
    }

    public void setDadosRubrica_codIncFGTS(String dadosRubrica_codIncFGTS) {
        this.dadosRubrica_codIncFGTS = dadosRubrica_codIncFGTS;
    }

    public String getDadosRubrica_codIncSIND() {
        return dadosRubrica_codIncSIND;
    }

    public void setDadosRubrica_codIncSIND(String dadosRubrica_codIncSIND) {
        this.dadosRubrica_codIncSIND = dadosRubrica_codIncSIND;
    }

}

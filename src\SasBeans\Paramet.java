package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class Paramet {

    private String Sequencia;
    private String CodEmpresa;
    private String Filial_PDR;
    private String Nome_empr;
    private String Cidade_PDR;
    private String UF_PDR;
    private String Path;
    private String Tipo;
    private String Usuario;
    private String Senha;
    private String HostName;
    private String HostNameWEB;
    private String BancoDados;
    private String PathSatelite;
    private String PathEagle;
    private String PathFotos;
    private String PathDoctos;
    private String PathLogoNFE;
    private String NetDir;
    private String Versao;
    private String CodCliCxf;
    private String DiaSem1;
    private String AbonoPdr;
    private String DescIntrajHE;
    private String CodFornItau;
    private Integer EscTolerMot;
    private Integer EscTolerChe;
    private Integer EscTolerVig;
    private String EscTolerOutros;
    private String PtoTolerMot;
    private String PtoTolerChe;
    private String PtoTolerVig;
    private String PtoTolerOutros;
    private String AceTolerMot;
    private String AceTolerChe;
    private String AceTolerVig;
    private String AceTolerOutros;
    private String FusoHorario;
    private String FusoHorarioSEFAZ;
    private String LimiteSeg;
    private String LimiteCxf;
    private String LimiteTes;
    private String AutoFechMobile;
    private String ConexaoPadraoBD;
    private String TrocaSenhaMobile;
    private String SeqParamPdr;
    private String MoedaPdrMobile;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String EmailAdm;
    private String TipoBDdescr;
    
    private String Certificado;
    private String SenhaCertificado;
    
    private String GoogleApiMob;
    private String GoogleApiOper;
    
    private String UtilizaGTVe;
    private String TranspCacamba;
    private String FirmaGtv;
    private String CapturaValor;

    public Paramet(){
    }
    
    public Paramet(String NovoBancoDados, String NovoCertificado, String NovaSenhaCertificado) {
        this.BancoDados = NovoBancoDados;
        this.Certificado = NovoCertificado;
        this.SenhaCertificado = NovaSenhaCertificado;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodEmpresa() {
        return CodEmpresa;
    }

    public void setCodEmpresa(String CodEmpresa) {
        this.CodEmpresa = CodEmpresa;
    }

    public String getFilial_PDR() {
        return Filial_PDR;
    }

    public void setFilial_PDR(String Filial_PDR) {
        this.Filial_PDR = Filial_PDR;
    }

    public String getNome_empr() {
        return Nome_empr;
    }

    public void setNome_empr(String Nome_empr) {
        this.Nome_empr = Nome_empr;
    }

    public String getCidade_PDR() {
        return Cidade_PDR;
    }

    public void setCidade_PDR(String Cidade_PDR) {
        this.Cidade_PDR = Cidade_PDR;
    }

    public String getUF_PDR() {
        return UF_PDR;
    }

    public void setUF_PDR(String UF_PDR) {
        this.UF_PDR = UF_PDR;
    }

    public String getPath() {
        return Path;
    }

    public void setPath(String Path) {
        this.Path = Path;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getUsuario() {
        return Usuario;
    }

    public void setUsuario(String Usuario) {
        this.Usuario = Usuario;
    }

    public String getSenha() {
        return Senha;
    }

    public void setSenha(String Senha) {
        this.Senha = Senha;
    }

    public String getHostName() {
        return HostName;
    }

    public void setHostName(String HostName) {
        this.HostName = HostName;
    }

    public String getHostNameWEB() {
        return HostNameWEB;
    }

    public void setHostNameWEB(String HostNameWEB) {
        this.HostNameWEB = HostNameWEB;
    }

    public String getBancoDados() {
        return BancoDados;
    }

    public void setBancoDados(String BancoDados) {
        this.BancoDados = BancoDados;
    }

    public String getPathSatelite() {
        return PathSatelite;
    }

    public void setPathSatelite(String PathSatelite) {
        this.PathSatelite = PathSatelite;
    }

    public String getPathEagle() {
        return PathEagle;
    }

    public void setPathEagle(String PathEagle) {
        this.PathEagle = PathEagle;
    }

    public String getPathFotos() {
        return PathFotos;
    }

    public void setPathFotos(String PathFotos) {
        this.PathFotos = PathFotos;
    }

    public String getPathDoctos() {
        return PathDoctos;
    }

    public void setPathDoctos(String PathDoctos) {
        this.PathDoctos = PathDoctos;
    }

    public String getPathLogoNFE() {
        return PathLogoNFE;
    }

    public void setPathLogoNFE(String PathLogoNFE) {
        this.PathLogoNFE = PathLogoNFE;
    }

    public String getNetDir() {
        return NetDir;
    }

    public void setNetDir(String NetDir) {
        this.NetDir = NetDir;
    }

    public String getVersao() {
        return Versao;
    }

    public void setVersao(String Versao) {
        this.Versao = Versao;
    }

    public String getCodCliCxf() {
        return CodCliCxf;
    }

    public void setCodCliCxf(String CodCliCxf) {
        this.CodCliCxf = CodCliCxf;
    }

    public String getDiaSem1() {
        return DiaSem1;
    }

    public void setDiaSem1(String DiaSem1) {
        this.DiaSem1 = DiaSem1;
    }

    public String getAbonoPdr() {
        return AbonoPdr;
    }

    public void setAbonoPdr(String AbonoPdr) {
        this.AbonoPdr = AbonoPdr;
    }

    public String getDescIntrajHE() {
        return DescIntrajHE;
    }

    public void setDescIntrajHE(String DescIntrajHE) {
        this.DescIntrajHE = DescIntrajHE;
    }

    public String getCodFornItau() {
        return CodFornItau;
    }

    public void setCodFornItau(String CodFornItau) {
        this.CodFornItau = CodFornItau;
    }

    public Integer getEscTolerMot() {
        return EscTolerMot;
    }

    public void setEscTolerMot(Integer EscTolerMot) {
        this.EscTolerMot = EscTolerMot;
    }

    public Integer getEscTolerChe() {
        return EscTolerChe;
    }

    public void setEscTolerChe(Integer EscTolerChe) {
        this.EscTolerChe = EscTolerChe;
    }

    public Integer getEscTolerVig() {
        return EscTolerVig;
    }

    public void setEscTolerVig(Integer EscTolerVig) {
        this.EscTolerVig = EscTolerVig;
    }

    public String getEscTolerOutros() {
        return EscTolerOutros;
    }

    public void setEscTolerOutros(String EscTolerOutros) {
        this.EscTolerOutros = EscTolerOutros;
    }

    public void setEscTolerOutros(int EscTolerOutros) {
        this.EscTolerOutros = Integer.toString(EscTolerOutros);
    }

    public String getPtoTolerMot() {
        return PtoTolerMot;
    }

    public void setPtoTolerMot(String PtoTolerMot) {
        this.PtoTolerMot = PtoTolerMot;
    }

    public void setPtoTolerMot(int PtoTolerMot) {
        this.PtoTolerMot = Integer.toString(PtoTolerMot);
    }

    public String getPtoTolerChe() {
        return PtoTolerChe;
    }

    public void setPtoTolerChe(String PtoTolerChe) {
        this.PtoTolerChe = PtoTolerChe;
    }

    public void setPtoTolerChe(int PtoTolerChe) {
        this.PtoTolerChe = Integer.toString(PtoTolerChe);
    }

    public String getPtoTolerVig() {
        return PtoTolerVig;
    }

    public void setPtoTolerVig(String PtoTolerVig) {
        this.PtoTolerVig = PtoTolerVig;
    }

    public void setPtoTolerVig(int PtoTolerVig) {
        this.PtoTolerVig = Integer.toString(PtoTolerVig);
    }

    public String getPtoTolerOutros() {
        return PtoTolerOutros;
    }

    public void setPtoTolerOutros(String PtoTolerOutros) {
        this.PtoTolerOutros = PtoTolerOutros;
    }

    public void setPtoTolerOutros(int PtoTolerOutros) {
        this.PtoTolerOutros = Integer.toString(PtoTolerOutros);
    }

    public String getAceTolerMot() {
        return AceTolerMot;
    }

    public void setAceTolerMot(String AceTolerMot) {
        this.AceTolerMot = AceTolerMot;
    }

    public void setAceTolerMot(int AceTolerMot) {
        this.AceTolerMot = Integer.toString(AceTolerMot);
    }

    public String getAceTolerChe() {
        return AceTolerChe;
    }

    public void setAceTolerChe(String AceTolerChe) {
        this.AceTolerChe = AceTolerChe;
    }

    public void setAceTolerChe(int AceTolerChe) {
        this.AceTolerChe = Integer.toString(AceTolerChe);
    }

    public String getAceTolerVig() {
        return AceTolerVig;
    }

    public void setAceTolerVig(String AceTolerVig) {
        this.AceTolerVig = AceTolerVig;
    }

    public void setAceTolerVig(int AceTolerVig) {
        this.AceTolerVig = Integer.toString(AceTolerVig);
    }

    public String getAceTolerOutros() {
        return AceTolerOutros;
    }

    public void setAceTolerOutros(String AceTolerOutros) {
        this.AceTolerOutros = AceTolerOutros;
    }

    public void setAceTolerOutros(int AceTolerOutros) {
        this.AceTolerOutros = Integer.toString(AceTolerOutros);
    }

    public String getFusoHorario() {
        return FusoHorario;
    }

    public void setFusoHorario(String FusoHorario) {
        this.FusoHorario = FusoHorario;
    }

    public String getFusoHorarioSEFAZ() {
        return FusoHorarioSEFAZ;
    }

    public void setFusoHorarioSEFAZ(String FusoHorarioSEFAZ) {
        this.FusoHorarioSEFAZ = FusoHorarioSEFAZ;
    }

    public String getLimiteSeg() {
        return LimiteSeg;
    }

    public void setLimiteSeg(String LimiteSeg) {
        this.LimiteSeg = LimiteSeg;
    }

    public String getLimiteCxf() {
        return LimiteCxf;
    }

    public void setLimiteCxf(String LimiteCxf) {
        this.LimiteCxf = LimiteCxf;
    }

    public String getLimiteTes() {
        return LimiteTes;
    }

    public void setLimiteTes(String LimiteTes) {
        this.LimiteTes = LimiteTes;
    }

    public String getAutoFechMobile() {
        return AutoFechMobile;
    }

    public void setAutoFechMobile(String AutoFechMobile) {
        this.AutoFechMobile = AutoFechMobile;
    }

    public String getConexaoPadraoBD() {
        return ConexaoPadraoBD;
    }

    public void setConexaoPadraoBD(String ConexaoPadraoBD) {
        this.ConexaoPadraoBD = ConexaoPadraoBD;
    }

    public String getTrocaSenhaMobile() {
        return TrocaSenhaMobile;
    }

    public void setTrocaSenhaMobile(String TrocaSenhaMobile) {
        this.TrocaSenhaMobile = TrocaSenhaMobile;
    }

    public String getSeqParamPdr() {
        return SeqParamPdr;
    }

    public void setSeqParamPdr(String SeqParamPdr) {
        this.SeqParamPdr = SeqParamPdr;
    }

    public String getMoedaPdrMobile() {
        return MoedaPdrMobile;
    }

    public void setMoedaPdrMobile(String MoedaPdrMobile) {
        this.MoedaPdrMobile = MoedaPdrMobile;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getEmailAdm() {
        return EmailAdm;
    }

    public void setEmailAdm(String EmailAdm) {
        this.EmailAdm = EmailAdm;
    }

    public String getTipoBDdescr() {
        return TipoBDdescr;
    }

    public void setTipoBDdescr(String TipoBDdescr) {
        this.TipoBDdescr = TipoBDdescr;
    }

    public String getCertificado() {
        return Certificado;
    }

    public void setCertificado(String Certificado) {
        this.Certificado = Certificado;
    }

    public String getSenhaCertificado() {
        return SenhaCertificado;
    }

    public void setSenhaCertificado(String SenhaCertificado) {
        this.SenhaCertificado = SenhaCertificado;
    }

    public String getGoogleApiMob() {
        return GoogleApiMob;
    }

    public String getGoogleApiOper() {
        return GoogleApiOper;
    }

    public void setGoogleApiMob(String GoogleApiMob) {
        this.GoogleApiMob = GoogleApiMob;
    }

    public void setGoogleApiOper(String GoogleApiOper) {
        this.GoogleApiOper = GoogleApiOper;
    }

    public String getUtilizaGTVe() {
        return UtilizaGTVe;
    }

    public void setUtilizaGTVe(String UtilizaGTVe) {
        this.UtilizaGTVe = UtilizaGTVe;
    }

    public String getTranspCacamba() {
        return TranspCacamba;
    }

    public void setTranspCacamba(String TranspCacamba) {
        this.TranspCacamba = TranspCacamba;
    }

    public String getFirmaGtv() {
        return FirmaGtv;
    }

    public void setFirmaGtv(String FirmaGtv) {
        this.FirmaGtv = FirmaGtv;
    }

    public String getCapturaValor() {
        return CapturaValor;
    }

    public void setCapturaValor(String CapturaValor) {
        this.CapturaValor = CapturaValor;
    }
}

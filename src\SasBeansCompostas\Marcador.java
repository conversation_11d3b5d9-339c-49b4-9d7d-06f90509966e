/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

/**
 *
 * <AUTHOR>
 */
public class Marcador {

    public final String PADRAO_MARCADOR = "&markers=icon:@Icon|label:@Localidade|@Latitude,@Longitude";

    private String icon;
    private String localidade;
    private String latitude;
    private String longitude;

    public Marcador() {
        this.icon = "https://mobile.sasw.com.br:9091/satmobile/img/pin_localizacao_64.png";
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getLocalidade() {
        return localidade;
    }

    public void setLocalidade(String localidade) {
        this.localidade = localidade;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
}

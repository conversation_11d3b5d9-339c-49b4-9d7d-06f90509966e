/*
 */
package org.primefaces.component.export;

import javax.faces.FacesException;

/**
 *
 * <AUTHOR>
 */
public class ExporterFactory {

    public static Exporter getExporterForType(String type, ExporterOptions exporterOptions) {
        Exporter exporter = null;

        try {
            ExporterType exporterType = ExporterType.valueOf(type.toUpperCase());

            switch (exporterType) {
                case XLS:
                    exporter = new ExcelExporter();
                    break;

                case PDF:
                    exporter = new CustomPDFExporter();
                    break;

                case CSV:
                    // Não testado nem validado 12/02/2019 Richard
                    exporter = new CSVExporter(new CSVOptions('\'', ';', "\n"));
                    break;

                case XML:
                    exporter = new XMLExporter();
                    break;

                case XLSX:
                    exporter = new ExcelXExporter();
                    break;
            }
        } catch (IllegalArgumentException e) {
            throw new FacesException(e);
        }

        return exporter;
    }
}

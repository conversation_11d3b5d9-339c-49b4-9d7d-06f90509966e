/*
 * To change this license header, choose License Headers in Project Properties_
 * To change this template file, choose Tools | Templates
 * and open the template in the editor_
 */
package SasBeans.ESocial;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class S2200 {

    private int sucesso;
    private String evtAdmissao_Id;
    /**
     * 1 - Original / 2 - Retificação
     */
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    /**
     * 1 - Produção / 2 - Homologação
     */
    private String ideEvento_tpAmb;
    /**
     * 1 - Aplicativo empregador
     */
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    /**
     * Converter: J -> 1 / F -> 2
     */
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String trabalhador_cpfTrab;
    private String trabalhador_nisTrab;
    private String trabalhador_nmTrab;
    private String trabalhador_sexo;
    /**
     * Converter: 2 -> 1 / 4 -> 2 / 8 -> 3 / 6 -> 4 / 0 -> 5
     */
    private String trabalhador_racaCor;
    /**
     * Converter: S -> 1 / C - > 2 / Q -> 4 / D -> 3 / V -> 5 / M -> 1
     */
    private String trabalhador_estCiv;
    /**
     * Converter: 10 -> 01 / 20 -> 02 / 25 -> 03 / 30 -> 04 / 35 -> 05 / 40 ->
     * 06 / 45 -> 07 / 50 -> 08 / 55 -> 09 / 65 -> 11 / 75 -> 12
     */
    private String trabalhador_grauInstr;
    /**
     * Converter: 10 -> S / <> 10 -> N
     */
    private String trabalhador_indPriEmpr;
    /**
     * AAAA-MM-DD
     */
    private String nascimento_dtNascto;
    private String nascimento_codMunic;
    private String nascimento_uf;
    private String nascimento_paisNascto;
    private String nascimento_paisNac;
    private String nascimento_nmMae;
    private String nascimento_nmPai;
    private String CTPS_nrCtps;
    private String CTPS_serieCtps;
    private String CTPS_ufCtps;
    private String RG_nrRg;
    private String RG_orgaoEmissor;
    /**
     * AAAA-MM-DD
     */
    private String RG_dtExped;
    private String CNH_nrRegCnh;
    private String CNH_ufCnh;
    private String CNH_dtValid;
    private String CNH_categoriaCnh;
    private String brasil_tpLograd;
    private String brasil_dscLograd;
    private String brasil_nrLograd;
    private String brasil_complemento;
    private String brasil_bairro;
    private String brasil_cep;
    private String brasil_codMunic;
    private String brasil_uf;
   /**
    * Deficiente - Carlos 08/07/2022
    */
    private String infoDeficiencia_defFisica;
    private String infoDeficiencia_defVisual;
    private String infoDeficiencia_defAuditiva;
    private String infoDeficiencia_defMental;
    private String infoDeficiencia_defIntelectual;
    private String infoDeficiencia_reabReadap;
    private String infoDeficiencia_infoCota;
    private String infoDeficiencia_observacao;
    
    
    /**
     * Converter: C -> 01 / F -> 03 / M -> 09 / P -> 09 / E -> 3 / O -> 99
     */
    private List<Dependentes> dependentes;

    private String contato_fonePrinc;
    private String contato_foneAlternat;
    private String contato_emailPrinc;
    private String vinculo_matricula;
    /**
     * 1 - CLT / 2 - Estatuario
     */
    private String vinculo_tpRegTrab;
    private String vinculo_tpRegPrev;
    private String vinculo_nrRecInfPrelim;
    /**
     * Quando 2200, exibir na tela opção para o operador setar se cadastro
     * inicial ou não (S e N)_
     */
    private String vinculo_cadIni;
    /**
     * AAAA-MM-DD
     */
    private String infoCeletista_dtAdm;
    private String infoCeletista_tpAdmissao;
    private String infoCeletista_indAdmissao;
    private String infoCeletista_tpRegJor;
    /**
     * 1 - Trabalho Urbano / 2 - Trabalho Rural
     */
    private String infoCeletista_natAtividade;
    private String infoCeletista_dtBase;
    private String infoCeletista_cnpjSindCategProf;
    private String FGTS_opcFGTS;
    private String CBO;    
    private String FGTS_dtOpcFGTS;
    private String infoContrato_nmCargo;
    private String infoContrato_codCateg;
    private String remuneracao_vrSalFx;
    /**
     * Converter: H -> 1 / D -> 2 / M -> 5
     */
    private String remuneracao_undSalFixo;
    private String duracao_tpContr;
    private String duracao_dtTerm;
    private String localTrabGeral_tpInsc;
    private String localTrabGeral_nrInsc;
    private String horContratual_qtdHrsSem;
    /**
     * Fazer regra junto Richard
     */
    private String horContratual_tpJornada;
    private String horContratual_tmpParc;
    private String horContratual_dscJorn;

    public static class Horario {

        private String horario_dia;
        private String horario_codHorContrat;

        public String getHorario_codHorContrat() {
            return null == horario_codHorContrat ? "" : horario_codHorContrat;
        }

        public void setHorario_codHorContrat(String horario_codHorContrat) {
            this.horario_codHorContrat = horario_codHorContrat;
        }

        public String getHorario_dia() {
            return null == horario_dia ? "" : horario_dia;
        }

        public void setHorario_dia(String horario_dia) {
            this.horario_dia = horario_dia;
        }
    }

    private List<Horario> horarios;

    private String filiacaoSindical_cnpjSindTrab;

    private String sucessaoVinc_cnpjEmpregAnt;
    private String sucessaoVinc_matricAnt;
    private String sucessaoVinc_dtTransf;

    private String trabEstrangeiro_dtChegada;
    private String trabEstrangeiro_classTrabEstrang;
    private String trabEstrangeiro_casadoBr;
    private String trabEstrangeiro_filhosBr;

    /**
     * Demonstrar apenas se situacao = D
     */
    private String desligamento_dtDesligamento;

    public static class Dependentes {

        private String dependente_tpDep;
        private String dependente_nmDep;
        private String dependente_dtNascto;
        private String dependente_depIRRF;
        private String dependente_depSF;
        private String dependente_incTrab;
        private String dependente_cpfDep;
        private String dependente_sexoDep;

        public String getDependente_tpDep() {
            return null == dependente_tpDep ? "" : dependente_tpDep;
        }

        public void setDependente_tpDep(String dependente_tpDep) {
            this.dependente_tpDep = dependente_tpDep;
        }

        public String getDependente_nmDep() {
            return null == dependente_nmDep ? "" : dependente_nmDep;
        }

        public void setDependente_nmDep(String dependente_nmDep) {
            this.dependente_nmDep = dependente_nmDep;
        }

        public String getDependente_dtNascto() {
            return null == dependente_dtNascto ? "" : dependente_dtNascto;
        }

        public void setDependente_dtNascto(String dependente_dtNascto) {
            this.dependente_dtNascto = dependente_dtNascto;
        }

        public String getDependente_depIRRF() {
            return null == dependente_depIRRF ? "" : dependente_depIRRF;
        }

        public void setDependente_depIRRF(String dependente_depIRRF) {
            this.dependente_depIRRF = dependente_depIRRF;
        }

        public String getDependente_depSF() {
            return null == dependente_depSF ? "" : dependente_depSF;
        }

        public void setDependente_depSF(String dependente_depSF) {
            this.dependente_depSF = dependente_depSF;
        }

        public String getDependente_incTrab() {
            return null == dependente_incTrab ? "" : dependente_incTrab;
        }

        public void setDependente_incTrab(String dependente_incTrab) {
            this.dependente_incTrab = dependente_incTrab;
        }

        public String getDependente_cpfDep() {
            return dependente_cpfDep;
        }

        public void setDependente_cpfDep(String dependente_cpfDep) {
            this.dependente_cpfDep = dependente_cpfDep;
        }
        
        public String getDependente_sexoDep() {
            return dependente_sexoDep;
        }

        public void setDependente_sexoDep(String dependente_sexoDep) {
            this.dependente_sexoDep = dependente_sexoDep;
        }
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public List<Horario> getHorarios() {
        return null == horarios ? new ArrayList<>() : horarios;
    }

    public void setHorarios(List<Horario> horarios) {
        this.horarios = horarios;
    }

    public List<Dependentes> getDependentes() {
        return null == dependentes ? new ArrayList<>() : dependentes;
    }

    public void setDependentes(List<Dependentes> dependentes) {
        this.dependentes = dependentes;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtAdmissao_Id() {
        return null == evtAdmissao_Id ? "" : evtAdmissao_Id;
    }

    public void setEvtAdmissao_Id(String evtAdmissao_Id) {
        this.evtAdmissao_Id = evtAdmissao_Id;
    }

    public String getIdeEvento_indRetif() {
        return null == ideEvento_indRetif ? "" : ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getTrabalhador_cpfTrab() {
        return null == trabalhador_cpfTrab ? "" : trabalhador_cpfTrab;
    }

    public void setTrabalhador_cpfTrab(String trabalhador_cpfTrab) {
        this.trabalhador_cpfTrab = trabalhador_cpfTrab;
    }

    public String getTrabalhador_nisTrab() {
        return null == trabalhador_nisTrab ? "" : trabalhador_nisTrab;
    }

    public void setTrabalhador_nisTrab(String trabalhador_nisTrab) {
        this.trabalhador_nisTrab = trabalhador_nisTrab;
    }

    public String getTrabalhador_nmTrab() {
        return null == trabalhador_nmTrab ? "" : trabalhador_nmTrab;
    }

    public void setTrabalhador_nmTrab(String trabalhador_nmTrab) {
        this.trabalhador_nmTrab = trabalhador_nmTrab;
    }

    public String getTrabalhador_sexo() {
        return null == trabalhador_sexo ? "" : trabalhador_sexo;
    }

    public void setTrabalhador_sexo(String trabalhador_sexo) {
        this.trabalhador_sexo = trabalhador_sexo;
    }

    public String getTrabalhador_racaCor() {
        return null == trabalhador_racaCor ? "" : trabalhador_racaCor;
    }

    public void setTrabalhador_racaCor(String trabalhador_racaCor) {
        this.trabalhador_racaCor = trabalhador_racaCor;
    }

    public String getTrabalhador_estCiv() {
        return null == trabalhador_estCiv ? "" : trabalhador_estCiv;
    }

    public void setTrabalhador_estCiv(String trabalhador_estCiv) {
        this.trabalhador_estCiv = trabalhador_estCiv;
    }

    public String getTrabalhador_grauInstr() {
        return null == trabalhador_grauInstr ? "" : trabalhador_grauInstr;
    }

    public void setTrabalhador_grauInstr(String trabalhador_grauInstr) {
        this.trabalhador_grauInstr = trabalhador_grauInstr;
    }

    public String getTrabalhador_indPriEmpr() {
        return null == trabalhador_indPriEmpr ? "" : trabalhador_indPriEmpr;
    }

    public void setTrabalhador_indPriEmpr(String trabalhador_indPriEmpr) {
        this.trabalhador_indPriEmpr = trabalhador_indPriEmpr;
    }

    public String getNascimento_dtNascto() {
        return null == nascimento_dtNascto ? "" : nascimento_dtNascto;
    }

    public void setNascimento_dtNascto(String nascimento_dtNascto) {
        this.nascimento_dtNascto = nascimento_dtNascto;
    }

    public String getNascimento_codMunic() {
        return null == nascimento_codMunic ? "" : nascimento_codMunic;
    }

    public void setNascimento_codMunic(String nascimento_codMunic) {
        this.nascimento_codMunic = nascimento_codMunic;
    }

    public String getNascimento_uf() {
        return null == nascimento_uf ? "" : nascimento_uf;
    }

    public void setNascimento_uf(String nascimento_uf) {
        this.nascimento_uf = nascimento_uf;
    }

    public String getNascimento_paisNascto() {
        return null == nascimento_paisNascto ? "" : nascimento_paisNascto;
    }

    public void setNascimento_paisNascto(String nascimento_paisNascto) {
        this.nascimento_paisNascto = nascimento_paisNascto;
    }

    public String getNascimento_paisNac() {
        return null == nascimento_paisNac ? "" : nascimento_paisNac;
    }

    public void setNascimento_paisNac(String nascimento_paisNac) {
        this.nascimento_paisNac = nascimento_paisNac;
    }

    public String getNascimento_nmMae() {
        return null == nascimento_nmMae ? "" : nascimento_nmMae;
    }

    public void setNascimento_nmMae(String nascimento_nmMae) {
        this.nascimento_nmMae = nascimento_nmMae;
    }

    public String getNascimento_nmPai() {
        return null == nascimento_nmPai ? "" : nascimento_nmPai;
    }

    public void setNascimento_nmPai(String nascimento_nmPai) {
        this.nascimento_nmPai = nascimento_nmPai;
    }

    public String getCTPS_nrCtps() {
        return null == CTPS_nrCtps ? "" : CTPS_nrCtps;
    }

    public void setCTPS_nrCtps(String CTPS_nrCtps) {
        this.CTPS_nrCtps = CTPS_nrCtps;
    }

    public String getCTPS_serieCtps() {
        return null == CTPS_serieCtps ? "" : CTPS_serieCtps;
    }

    public void setCTPS_serieCtps(String CTPS_serieCtps) {
        this.CTPS_serieCtps = CTPS_serieCtps;
    }

    public String getCTPS_ufCtps() {
        return null == CTPS_ufCtps ? "" : CTPS_ufCtps;
    }

    public void setCTPS_ufCtps(String CTPS_ufCtps) {
        this.CTPS_ufCtps = CTPS_ufCtps;
    }

    public String getRG_nrRg() {
        return null == RG_nrRg ? "" : RG_nrRg;
    }

    public void setRG_nrRg(String RG_nrRg) {
        this.RG_nrRg = RG_nrRg;
    }

    public String getRG_orgaoEmissor() {
        return null == RG_orgaoEmissor ? "" : RG_orgaoEmissor;
    }

    public void setRG_orgaoEmissor(String RG_orgaoEmissor) {
        this.RG_orgaoEmissor = RG_orgaoEmissor;
    }

    public String getRG_dtExped() {
        return null == RG_dtExped ? "" : RG_dtExped;
    }

    public void setRG_dtExped(String RG_dtExped) {
        this.RG_dtExped = RG_dtExped;
    }

    public String getCNH_nrRegCnh() {
        return null == CNH_nrRegCnh ? "" : CNH_nrRegCnh;
    }

    public void setCNH_nrRegCnh(String CNH_nrRegCnh) {
        this.CNH_nrRegCnh = CNH_nrRegCnh;
    }

    public String getCNH_ufCnh() {
        return null == CNH_ufCnh ? "" : CNH_ufCnh;
    }

    public void setCNH_ufCnh(String CNH_ufCnh) {
        this.CNH_ufCnh = CNH_ufCnh;
    }

    public String getCNH_dtValid() {
        return null == CNH_dtValid ? "" : CNH_dtValid;
    }

    public void setCNH_dtValid(String CNH_dtValid) {
        this.CNH_dtValid = CNH_dtValid;
    }

    public String getCNH_categoriaCnh() {
        return null == CNH_categoriaCnh ? "" : CNH_categoriaCnh;
    }

    public void setCNH_categoriaCnh(String CNH_categoriaCnh) {
        this.CNH_categoriaCnh = CNH_categoriaCnh;
    }

    public String getBrasil_tpLograd() {
        return null == brasil_tpLograd ? "" : brasil_tpLograd;
    }

    public void setBrasil_tpLograd(String brasil_tpLograd) {
        this.brasil_tpLograd = brasil_tpLograd;
    }

    public String getBrasil_dscLograd() {
        return null == brasil_dscLograd ? "" : brasil_dscLograd;
    }

    public void setBrasil_dscLograd(String brasil_dscLograd) {
        this.brasil_dscLograd = brasil_dscLograd;
    }

    public String getBrasil_nrLograd() {
        return null == brasil_nrLograd ? "" : brasil_nrLograd;
    }

    public void setBrasil_nrLograd(String brasil_nrLograd) {
        this.brasil_nrLograd = brasil_nrLograd;
    }

    public String getBrasil_complemento() {
        return null == brasil_complemento ? "" : brasil_complemento;
    }

    public void setBrasil_complemento(String brasil_complemento) {
        this.brasil_complemento = brasil_complemento;
    }

    public String getBrasil_bairro() {
        return null == brasil_bairro ? "" : brasil_bairro;
    }

    public void setBrasil_bairro(String brasil_bairro) {
        this.brasil_bairro = brasil_bairro;
    }

    public String getBrasil_cep() {
        return null == brasil_cep ? "" : brasil_cep;
    }

    public void setBrasil_cep(String brasil_cep) {
        this.brasil_cep = brasil_cep;
    }

    public String getBrasil_codMunic() {
        return null == brasil_codMunic ? "" : brasil_codMunic;
    }

    public void setBrasil_codMunic(String brasil_codMunic) {
        this.brasil_codMunic = brasil_codMunic;
    }

    public String getBrasil_uf() {
        return null == brasil_uf ? "" : brasil_uf;
    }

    public void setBrasil_uf(String brasil_uf) {
        this.brasil_uf = brasil_uf;
    }

    public String getInfoDeficiencia_defFisica() {
        return infoDeficiencia_defFisica;
    }

    public void setInfoDeficiencia_defFisica(String infoDeficiencia_defFisica) {
        this.infoDeficiencia_defFisica = infoDeficiencia_defFisica;
    }

    public String getInfoDeficiencia_defVisual() {
        return infoDeficiencia_defVisual;
    }

    public void setInfoDeficiencia_defVisual(String infoDeficiencia_defVisual) {
        this.infoDeficiencia_defVisual = infoDeficiencia_defVisual;
    }

    public String getInfoDeficiencia_defAuditiva() {
        return infoDeficiencia_defAuditiva;
    }

    public void setInfoDeficiencia_defAuditiva(String infoDeficiencia_defAuditiva) {
        this.infoDeficiencia_defAuditiva = infoDeficiencia_defAuditiva;
    }

    public String getInfoDeficiencia_defMental() {
        return infoDeficiencia_defMental;
    }

    public void setInfoDeficiencia_defMental(String infoDeficiencia_defMental) {
        this.infoDeficiencia_defMental = infoDeficiencia_defMental;
    }

    public String getInfoDeficiencia_defIntelectual() {
        return infoDeficiencia_defIntelectual;
    }

    public void setInfoDeficiencia_defIntelectual(String infoDeficiencia_defIntelectual) {
        this.infoDeficiencia_defIntelectual = infoDeficiencia_defIntelectual;
    }

    public String getInfoDeficiencia_reabReadap() {
        return infoDeficiencia_reabReadap;
    }

    public void setInfoDeficiencia_reabReadap(String infoDeficiencia_reabReadap) {
        this.infoDeficiencia_reabReadap = infoDeficiencia_reabReadap;
    }

    public String getInfoDeficiencia_infoCota() {
        return infoDeficiencia_infoCota;
    }

    public void setInfoDeficiencia_infoCota(String infoDeficiencia_infoCota) {
        this.infoDeficiencia_infoCota = infoDeficiencia_infoCota;
    }

    public String getInfoDeficiencia_observacao() {
        return infoDeficiencia_observacao;
    }

    public void setInfoDeficiencia_observacao(String infoDeficiencia_observacao) {
        this.infoDeficiencia_observacao = infoDeficiencia_observacao;
    }
    
    
    public String getContato_fonePrinc() {
        return null == contato_fonePrinc ? "" : contato_fonePrinc;
    }

    public void setContato_fonePrinc(String contato_fonePrinc) {
        this.contato_fonePrinc = contato_fonePrinc;
    }

    public String getContato_foneAlternat() {
        return null == contato_foneAlternat ? "" : contato_foneAlternat;
    }

    public void setContato_foneAlternat(String contato_foneAlternat) {
        this.contato_foneAlternat = contato_foneAlternat;
    }

    public String getContato_emailPrinc() {
        return null == contato_emailPrinc ? "" : contato_emailPrinc;
    }

    public void setContato_emailPrinc(String contato_emailPrinc) {
        this.contato_emailPrinc = contato_emailPrinc;
    }

    public String getVinculo_matricula() {
        return null == vinculo_matricula ? "" : vinculo_matricula;
    }

    public void setVinculo_matricula(String vinculo_matricula) {
        this.vinculo_matricula = vinculo_matricula;
    }

    public String getVinculo_tpRegTrab() {
        return null == vinculo_tpRegTrab ? "" : vinculo_tpRegTrab;
    }

    public void setVinculo_tpRegTrab(String vinculo_tpRegTrab) {
        this.vinculo_tpRegTrab = vinculo_tpRegTrab;
    }

    public String getVinculo_tpRegPrev() {
        return null == vinculo_tpRegPrev ? "" : vinculo_tpRegPrev;
    }

    public void setVinculo_tpRegPrev(String vinculo_tpRegPrev) {
        this.vinculo_tpRegPrev = vinculo_tpRegPrev;
    }

    public String getVinculo_cadIni() {
        return null == vinculo_cadIni ? "" : vinculo_cadIni;
    }

    public void setVinculo_cadIni(String vinculo_cadIni) {
        this.vinculo_cadIni = vinculo_cadIni;
    }

    public String getInfoCeletista_dtAdm() {
        return null == infoCeletista_dtAdm ? "" : infoCeletista_dtAdm;
    }

    public void setInfoCeletista_dtAdm(String infoCeletista_dtAdm) {
        this.infoCeletista_dtAdm = infoCeletista_dtAdm;
    }

    public String getInfoCeletista_tpAdmissao() {
        return null == infoCeletista_tpAdmissao ? "" : infoCeletista_tpAdmissao;
    }

    public void setInfoCeletista_tpAdmissao(String infoCeletista_tpAdmissao) {
        this.infoCeletista_tpAdmissao = infoCeletista_tpAdmissao;
    }

    public String getInfoCeletista_indAdmissao() {
        return null == infoCeletista_indAdmissao ? "" : infoCeletista_indAdmissao;
    }

    public void setInfoCeletista_indAdmissao(String infoCeletista_indAdmissao) {
        this.infoCeletista_indAdmissao = infoCeletista_indAdmissao;
    }

    public String getInfoCeletista_tpRegJor() {
        return null == infoCeletista_tpRegJor ? "" : infoCeletista_tpRegJor;
    }

    public void setInfoCeletista_tpRegJor(String infoCeletista_tpRegJor) {
        this.infoCeletista_tpRegJor = infoCeletista_tpRegJor;
    }

    public String getInfoCeletista_natAtividade() {
        return null == infoCeletista_natAtividade ? "" : infoCeletista_natAtividade;
    }

    public void setInfoCeletista_natAtividade(String infoCeletista_natAtividade) {
        this.infoCeletista_natAtividade = infoCeletista_natAtividade;
    }

    public String getInfoCeletista_cnpjSindCategProf() {
        return null == infoCeletista_cnpjSindCategProf ? "" : infoCeletista_cnpjSindCategProf;
    }

    public void setInfoCeletista_cnpjSindCategProf(String infoCeletista_cnpjSindCategProf) {
        this.infoCeletista_cnpjSindCategProf = infoCeletista_cnpjSindCategProf;
    }

    public String getFGTS_opcFGTS() {
        return null == FGTS_opcFGTS ? "" : FGTS_opcFGTS;
    }

    public String getCBO() {
        return null == CBO ? "" : CBO;
    }    
    
    public void setFGTS_opcFGTS(String FGTS_opcFGTS) {
        this.FGTS_opcFGTS = FGTS_opcFGTS;
    }

    public void setCBO(String CBO) {
        this.CBO = CBO;
    }
    
    
    public String getFGTS_dtOpcFGTS() {
        return null == FGTS_dtOpcFGTS ? "" : FGTS_dtOpcFGTS;
    }

    public void setFGTS_dtOpcFGTS(String FGTS_dtOpcFGTS) {
        this.FGTS_dtOpcFGTS = FGTS_dtOpcFGTS;
    }

    public String getInfoContrato_nmCargo() {
        return null == infoContrato_nmCargo ? "" : infoContrato_nmCargo;
    }

    public void setInfoContrato_nmCargo(String infoContrato_nmCargo) {
        this.infoContrato_nmCargo = infoContrato_nmCargo;
    }

    public String getInfoContrato_codCateg() {
        return null == infoContrato_codCateg ? "" : infoContrato_codCateg;
    }

    public void setInfoContrato_codCateg(String infoContrato_codCateg) {
        this.infoContrato_codCateg = infoContrato_codCateg;
    }

    public String getRemuneracao_vrSalFx() {
        return null == remuneracao_vrSalFx ? "" : remuneracao_vrSalFx;
    }

    public void setRemuneracao_vrSalFx(String remuneracao_vrSalFx) {
        this.remuneracao_vrSalFx = remuneracao_vrSalFx;
    }

    public String getRemuneracao_undSalFixo() {
        return null == remuneracao_undSalFixo ? "" : remuneracao_undSalFixo;
    }

    public void setRemuneracao_undSalFixo(String remuneracao_undSalFixo) {
        this.remuneracao_undSalFixo = remuneracao_undSalFixo;
    }

    public String getDuracao_tpContr() {
        return null == duracao_tpContr ? "" : duracao_tpContr;
    }

    public void setDuracao_tpContr(String duracao_tpContr) {
        this.duracao_tpContr = duracao_tpContr;
    }

    public String getLocalTrabGeral_tpInsc() {
        return null == localTrabGeral_tpInsc ? "" : localTrabGeral_tpInsc;
    }

    public void setLocalTrabGeral_tpInsc(String localTrabGeral_tpInsc) {
        this.localTrabGeral_tpInsc = localTrabGeral_tpInsc;
    }

    public String getLocalTrabGeral_nrInsc() {
        return null == localTrabGeral_nrInsc ? "" : localTrabGeral_nrInsc;
    }

    public void setLocalTrabGeral_nrInsc(String localTrabGeral_nrInsc) {
        this.localTrabGeral_nrInsc = localTrabGeral_nrInsc;
    }

    public String getHorContratual_qtdHrsSem() {
        return null == horContratual_qtdHrsSem ? "" : horContratual_qtdHrsSem;
    }

    public void setHorContratual_qtdHrsSem(String horContratual_qtdHrsSem) {
        this.horContratual_qtdHrsSem = horContratual_qtdHrsSem;
    }

    public String getHorContratual_tpJornada() {
        return null == horContratual_tpJornada ? "" : horContratual_tpJornada;
    }

    public void setHorContratual_tpJornada(String horContratual_tpJornada) {
        this.horContratual_tpJornada = horContratual_tpJornada;
    }

    public String getHorContratual_tmpParc() {
        return null == horContratual_tmpParc ? "" : horContratual_tmpParc;
    }

    public void setHorContratual_tmpParc(String horContratual_tmpParc) {
        this.horContratual_tmpParc = horContratual_tmpParc;
    }

    public String getFiliacaoSindical_cnpjSindTrab() {
        return null == filiacaoSindical_cnpjSindTrab ? "" : filiacaoSindical_cnpjSindTrab;
    }

    public void setFiliacaoSindical_cnpjSindTrab(String filiacaoSindical_cnpjSindTrab) {
        this.filiacaoSindical_cnpjSindTrab = filiacaoSindical_cnpjSindTrab;
    }

    public String getDesligamento_dtDesligamento() {
        return null == desligamento_dtDesligamento ? "" : desligamento_dtDesligamento;
    }

    public void setDesligamento_dtDesligamento(String desligamento_dtDesligamento) {
        this.desligamento_dtDesligamento = desligamento_dtDesligamento;
    }

    public String getVinculo_nrRecInfPrelim() {
        return vinculo_nrRecInfPrelim == null ? "" : vinculo_nrRecInfPrelim;
    }

    public void setVinculo_nrRecInfPrelim(String vinculo_nrRecInfPrelim) {
        this.vinculo_nrRecInfPrelim = vinculo_nrRecInfPrelim;
    }

    public String getSucessaoVinc_cnpjEmpregAnt() {
        return sucessaoVinc_cnpjEmpregAnt == null ? "" : sucessaoVinc_cnpjEmpregAnt;
    }

    public void setSucessaoVinc_cnpjEmpregAnt(String sucessaoVinc_cnpjEmpregAnt) {
        this.sucessaoVinc_cnpjEmpregAnt = sucessaoVinc_cnpjEmpregAnt;
    }

    public String getSucessaoVinc_matricAnt() {
        return sucessaoVinc_matricAnt == null ? "" : sucessaoVinc_matricAnt;
    }

    public void setSucessaoVinc_matricAnt(String sucessaoVinc_matricAnt) {
        this.sucessaoVinc_matricAnt = sucessaoVinc_matricAnt;
    }

    public String getSucessaoVinc_dtTransf() {
        return sucessaoVinc_dtTransf == null ? "" : sucessaoVinc_dtTransf;
    }

    public void setSucessaoVinc_dtTransf(String sucessaoVinc_dtTransf) {
        this.sucessaoVinc_dtTransf = sucessaoVinc_dtTransf;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 61 * hash + Objects.hashCode(this.trabalhador_cpfTrab);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final S2200 other = (S2200) obj;
        if (!Objects.equals(this.trabalhador_cpfTrab, other.trabalhador_cpfTrab)) {
            return false;
        }
        return true;
    }

    public String getTrabEstrangeiro_dtChegada() {
        return trabEstrangeiro_dtChegada;
    }

    public void setTrabEstrangeiro_dtChegada(String trabEstrangeiro_dtChegada) {
        this.trabEstrangeiro_dtChegada = trabEstrangeiro_dtChegada;
    }

    public String getTrabEstrangeiro_classTrabEstrang() {
        return trabEstrangeiro_classTrabEstrang;
    }

    public void setTrabEstrangeiro_classTrabEstrang(String trabEstrangeiro_classTrabEstrang) {
        this.trabEstrangeiro_classTrabEstrang = trabEstrangeiro_classTrabEstrang;
    }

    public String getTrabEstrangeiro_casadoBr() {
        return trabEstrangeiro_casadoBr;
    }

    public void setTrabEstrangeiro_casadoBr(String trabEstrangeiro_casadoBr) {
        this.trabEstrangeiro_casadoBr = trabEstrangeiro_casadoBr;
    }

    public String getTrabEstrangeiro_filhosBr() {
        return trabEstrangeiro_filhosBr;
    }

    public void setTrabEstrangeiro_filhosBr(String trabEstrangeiro_filhosBr) {
        this.trabEstrangeiro_filhosBr = trabEstrangeiro_filhosBr;
    }

    /**
     * @return the duracao_dtTerm
     */
    public String getDuracao_dtTerm() {
        return duracao_dtTerm;
    }

    /**
     * @param duracao_dtTerm the duracao_dtTerm to set
     */
    public void setDuracao_dtTerm(String duracao_dtTerm) {
        this.duracao_dtTerm = duracao_dtTerm;
    }


    public String getInfoCeletista_dtBase() {
        return infoCeletista_dtBase;
    }

    public void setInfoCeletista_dtBase(String infoCeletista_dtBase) {
        this.infoCeletista_dtBase = infoCeletista_dtBase;
    }

    public String getHorContratual_dscJorn() {
        return horContratual_dscJorn;
    }

    public void setHorContratual_dscJorn(String horContratual_dscJorn) {
        this.horContratual_dscJorn = horContratual_dscJorn;
    }
    
}

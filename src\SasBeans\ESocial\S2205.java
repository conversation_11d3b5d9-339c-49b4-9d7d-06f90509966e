/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2205 {

    private int sucesso;

    private String matr;

    private String evtAltCadastral_Id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideTrabalhador_cpfTrab;
    private String alteracao_dtAlteracao;
    private String dadosTrabalhador_nisTrab;
    private String dadosTrabalhador_nmTrab;
    private String dadosTrabalhador_sexo;
    private String dadosTrabalhador_racaCor;
    private String dadosTrabalhador_estCiv;
    private String dadosTrabalhador_grauInstr;
    private String nascimento_dtNascto;
    private String nascimento_codMunic;
    private String nascimento_uf;
    private String nascimento_paisNascto;
    private String nascimento_paisNac;
    private String nascimento_nmMae;
    private String nascimento_nmPai;
    private String CTPS_nrCtps;
    private String nrCtps_serieCtps;
    private String nrCtps_ufCtps;
    private String RIC_nrRic;
    private String RIC_orgaoEmissor;
    private String RIC_dtExped;
    private String RG_nrRg;
    private String RG_orgaoEmissor;
    private String RG_dtExped;
    private String RNE_nrRne;
    private String RNE_orgaoEmissor;
    private String RNE_dtExped;
    private String OC_nrOC;
    private String OC_orgaoEmissor;
    private String OC_dtExped;
    private String OC_dtValid;
    private String CNH_nrRegCnh;
    private String CNH_dtExped;
    private String CNH_ufCnh;
    private String CNH_dtValid;
    private String CNH_dtPriHab;
    private String CNH_categCnh;
    private String brasil_tpLograd;
    private String brasil_dscLograd;
    private String brasil_nrLograd;
    private String brasil_complemento;
    private String brasil_bairro;
    private String brasil_cep;
    private String brasil_codMunic;
    private String brasil_uf;
    private String infoDeficiencia_defFisica;
    private String infoDeficiencia_defVisual;
    private String infoDeficiencia_defAuditiva;
    private String infoDeficiencia_defMental;
    private String infoDeficiencia_defIntelectual;
    private String infoDeficiencia_reabReadap;
    private String infoDeficiencia_infoCota;
    private String infoDeficiencia_observacao;

    private List<Dependentes> dependentes;

    private String aposentadoria_trabAposent;
    private String contato_fonePrinc;
    private String contato_foneAlternat;
    private String contato_emailPrinc;
    private String contato_emailAlternat;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtAltCadastral_Id() {
        return evtAltCadastral_Id;
    }

    public void setEvtAltCadastral_Id(String evtAltCadastral_Id) {
        this.evtAltCadastral_Id = evtAltCadastral_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeTrabalhador_cpfTrab() {
        return ideTrabalhador_cpfTrab;
    }

    public void setIdeTrabalhador_cpfTrab(String ideTrabalhador_cpfTrab) {
        this.ideTrabalhador_cpfTrab = ideTrabalhador_cpfTrab;
    }

    public String getAlteracao_dtAlteracao() {
        return alteracao_dtAlteracao;
    }

    public void setAlteracao_dtAlteracao(String alteracao_dtAlteracao) {
        this.alteracao_dtAlteracao = alteracao_dtAlteracao;
    }

    public String getDadosTrabalhador_nisTrab() {
        return dadosTrabalhador_nisTrab;
    }

    public void setDadosTrabalhador_nisTrab(String dadosTrabalhador_nisTrab) {
        this.dadosTrabalhador_nisTrab = dadosTrabalhador_nisTrab;
    }

    public String getDadosTrabalhador_nmTrab() {
        return dadosTrabalhador_nmTrab;
    }

    public void setDadosTrabalhador_nmTrab(String dadosTrabalhador_nmTrab) {
        this.dadosTrabalhador_nmTrab = dadosTrabalhador_nmTrab;
    }

    public String getDadosTrabalhador_sexo() {
        return dadosTrabalhador_sexo;
    }

    public void setDadosTrabalhador_sexo(String dadosTrabalhador_sexo) {
        this.dadosTrabalhador_sexo = dadosTrabalhador_sexo;
    }

    public String getDadosTrabalhador_racaCor() {
        return dadosTrabalhador_racaCor;
    }

    public void setDadosTrabalhador_racaCor(String dadosTrabalhador_racaCor) {
        this.dadosTrabalhador_racaCor = dadosTrabalhador_racaCor;
    }

    public String getDadosTrabalhador_estCiv() {
        return dadosTrabalhador_estCiv;
    }

    public void setDadosTrabalhador_estCiv(String dadosTrabalhador_estCiv) {
        this.dadosTrabalhador_estCiv = dadosTrabalhador_estCiv;
    }

    public String getDadosTrabalhador_grauInstr() {
        return dadosTrabalhador_grauInstr;
    }

    public void setDadosTrabalhador_grauInstr(String dadosTrabalhador_grauInstr) {
        this.dadosTrabalhador_grauInstr = dadosTrabalhador_grauInstr;
    }

    public String getNascimento_dtNascto() {
        return nascimento_dtNascto;
    }

    public void setNascimento_dtNascto(String nascimento_dtNascto) {
        this.nascimento_dtNascto = nascimento_dtNascto;
    }

    public String getNascimento_codMunic() {
        return nascimento_codMunic;
    }

    public void setNascimento_codMunic(String nascimento_codMunic) {
        this.nascimento_codMunic = nascimento_codMunic;
    }

    public String getNascimento_uf() {
        return nascimento_uf;
    }

    public void setNascimento_uf(String nascimento_uf) {
        this.nascimento_uf = nascimento_uf;
    }

    public String getNascimento_paisNascto() {
        return nascimento_paisNascto;
    }

    public void setNascimento_paisNascto(String nascimento_paisNascto) {
        this.nascimento_paisNascto = nascimento_paisNascto;
    }

    public String getNascimento_paisNac() {
        return nascimento_paisNac;
    }

    public void setNascimento_paisNac(String nascimento_paisNac) {
        this.nascimento_paisNac = nascimento_paisNac;
    }

    public String getNascimento_nmMae() {
        return nascimento_nmMae;
    }

    public void setNascimento_nmMae(String nascimento_nmMae) {
        this.nascimento_nmMae = nascimento_nmMae;
    }

    public String getNascimento_nmPai() {
        return nascimento_nmPai;
    }

    public void setNascimento_nmPai(String nascimento_nmPai) {
        this.nascimento_nmPai = nascimento_nmPai;
    }

    public String getCTPS_nrCtps() {
        return CTPS_nrCtps;
    }

    public void setCTPS_nrCtps(String CTPS_nrCtps) {
        this.CTPS_nrCtps = CTPS_nrCtps;
    }

    public String getNrCtps_serieCtps() {
        return nrCtps_serieCtps;
    }

    public void setNrCtps_serieCtps(String nrCtps_serieCtps) {
        this.nrCtps_serieCtps = nrCtps_serieCtps;
    }

    public String getNrCtps_ufCtps() {
        return nrCtps_ufCtps;
    }

    public void setNrCtps_ufCtps(String nrCtps_ufCtps) {
        this.nrCtps_ufCtps = nrCtps_ufCtps;
    }

    public String getRIC_nrRic() {
        return RIC_nrRic;
    }

    public void setRIC_nrRic(String RIC_nrRic) {
        this.RIC_nrRic = RIC_nrRic;
    }

    public String getRIC_orgaoEmissor() {
        return RIC_orgaoEmissor;
    }

    public void setRIC_orgaoEmissor(String RIC_orgaoEmissor) {
        this.RIC_orgaoEmissor = RIC_orgaoEmissor;
    }

    public String getRIC_dtExped() {
        return RIC_dtExped;
    }

    public void setRIC_dtExped(String RIC_dtExped) {
        this.RIC_dtExped = RIC_dtExped;
    }

    public String getRG_nrRg() {
        return RG_nrRg;
    }

    public void setRG_nrRg(String RG_nrRg) {
        this.RG_nrRg = RG_nrRg;
    }

    public String getRG_orgaoEmissor() {
        return RG_orgaoEmissor;
    }

    public void setRG_orgaoEmissor(String RG_orgaoEmissor) {
        this.RG_orgaoEmissor = RG_orgaoEmissor;
    }

    public String getRG_dtExped() {
        return RG_dtExped;
    }

    public void setRG_dtExped(String RG_dtExped) {
        this.RG_dtExped = RG_dtExped;
    }

    public String getRNE_nrRne() {
        return RNE_nrRne;
    }

    public void setRNE_nrRne(String RNE_nrRne) {
        this.RNE_nrRne = RNE_nrRne;
    }

    public String getRNE_orgaoEmissor() {
        return RNE_orgaoEmissor;
    }

    public void setRNE_orgaoEmissor(String RNE_orgaoEmissor) {
        this.RNE_orgaoEmissor = RNE_orgaoEmissor;
    }

    public String getRNE_dtExped() {
        return RNE_dtExped;
    }

    public void setRNE_dtExped(String RNE_dtExped) {
        this.RNE_dtExped = RNE_dtExped;
    }

    public String getOC_nrOC() {
        return OC_nrOC;
    }

    public void setOC_nrOC(String OC_nrOC) {
        this.OC_nrOC = OC_nrOC;
    }

    public String getOC_orgaoEmissor() {
        return OC_orgaoEmissor;
    }

    public void setOC_orgaoEmissor(String OC_orgaoEmissor) {
        this.OC_orgaoEmissor = OC_orgaoEmissor;
    }

    public String getOC_dtExped() {
        return OC_dtExped;
    }

    public void setOC_dtExped(String OC_dtExped) {
        this.OC_dtExped = OC_dtExped;
    }

    public String getOC_dtValid() {
        return OC_dtValid;
    }

    public void setOC_dtValid(String OC_dtValid) {
        this.OC_dtValid = OC_dtValid;
    }

    public String getCNH_nrRegCnh() {
        return CNH_nrRegCnh;
    }

    public void setCNH_nrRegCnh(String CNH_nrRegCnh) {
        this.CNH_nrRegCnh = CNH_nrRegCnh;
    }

    public String getCNH_dtExped() {
        return CNH_dtExped;
    }

    public void setCNH_dtExped(String CNH_dtExped) {
        this.CNH_dtExped = CNH_dtExped;
    }

    public String getCNH_ufCnh() {
        return CNH_ufCnh;
    }

    public void setCNH_ufCnh(String CNH_ufCnh) {
        this.CNH_ufCnh = CNH_ufCnh;
    }

    public String getCNH_dtValid() {
        return CNH_dtValid;
    }

    public void setCNH_dtValid(String CNH_dtValid) {
        this.CNH_dtValid = CNH_dtValid;
    }

    public String getCNH_dtPriHab() {
        return CNH_dtPriHab;
    }

    public void setCNH_dtPriHab(String CNH_dtPriHab) {
        this.CNH_dtPriHab = CNH_dtPriHab;
    }

    public String getCNH_categCnh() {
        return CNH_categCnh;
    }

    public void setCNH_categCnh(String CNH_categCnh) {
        this.CNH_categCnh = CNH_categCnh;
    }

    public String getBrasil_tpLograd() {
        return brasil_tpLograd;
    }

    public void setBrasil_tpLograd(String brasil_tpLograd) {
        this.brasil_tpLograd = brasil_tpLograd;
    }

    public String getBrasil_dscLograd() {
        return brasil_dscLograd;
    }

    public void setBrasil_dscLograd(String brasil_dscLograd) {
        this.brasil_dscLograd = brasil_dscLograd;
    }

    public String getBrasil_nrLograd() {
        return brasil_nrLograd;
    }

    public void setBrasil_nrLograd(String brasil_nrLograd) {
        this.brasil_nrLograd = brasil_nrLograd;
    }

    public String getBrasil_complemento() {
        return brasil_complemento;
    }

    public void setBrasil_complemento(String brasil_complemento) {
        this.brasil_complemento = brasil_complemento;
    }

    public String getBrasil_bairro() {
        return brasil_bairro;
    }

    public void setBrasil_bairro(String brasil_bairro) {
        this.brasil_bairro = brasil_bairro;
    }

    public String getBrasil_cep() {
        return brasil_cep;
    }

    public void setBrasil_cep(String brasil_cep) {
        this.brasil_cep = brasil_cep;
    }

    public String getBrasil_codMunic() {
        return brasil_codMunic;
    }

    public void setBrasil_codMunic(String brasil_codMunic) {
        this.brasil_codMunic = brasil_codMunic;
    }

    public String getBrasil_uf() {
        return brasil_uf;
    }

    public void setBrasil_uf(String brasil_uf) {
        this.brasil_uf = brasil_uf;
    }

    public String getInfoDeficiencia_defFisica() {
        return infoDeficiencia_defFisica;
    }

    public void setInfoDeficiencia_defFisica(String infoDeficiencia_defFisica) {
        this.infoDeficiencia_defFisica = infoDeficiencia_defFisica;
    }

    public String getInfoDeficiencia_defVisual() {
        return infoDeficiencia_defVisual;
    }

    public void setInfoDeficiencia_defVisual(String infoDeficiencia_defVisual) {
        this.infoDeficiencia_defVisual = infoDeficiencia_defVisual;
    }

    public String getInfoDeficiencia_defAuditiva() {
        return infoDeficiencia_defAuditiva;
    }

    public void setInfoDeficiencia_defAuditiva(String infoDeficiencia_defAuditiva) {
        this.infoDeficiencia_defAuditiva = infoDeficiencia_defAuditiva;
    }

    public String getInfoDeficiencia_defMental() {
        return infoDeficiencia_defMental;
    }

    public void setInfoDeficiencia_defMental(String infoDeficiencia_defMental) {
        this.infoDeficiencia_defMental = infoDeficiencia_defMental;
    }

    public String getInfoDeficiencia_defIntelectual() {
        return infoDeficiencia_defIntelectual;
    }

    public void setInfoDeficiencia_defIntelectual(String infoDeficiencia_defIntelectual) {
        this.infoDeficiencia_defIntelectual = infoDeficiencia_defIntelectual;
    }

    public String getInfoDeficiencia_reabReadap() {
        return infoDeficiencia_reabReadap;
    }

    public void setInfoDeficiencia_reabReadap(String infoDeficiencia_reabReadap) {
        this.infoDeficiencia_reabReadap = infoDeficiencia_reabReadap;
    }

    public String getInfoDeficiencia_infoCota() {
        return infoDeficiencia_infoCota;
    }

    public void setInfoDeficiencia_infoCota(String infoDeficiencia_infoCota) {
        this.infoDeficiencia_infoCota = infoDeficiencia_infoCota;
    }

    public String getInfoDeficiencia_observacao() {
        return infoDeficiencia_observacao;
    }

    public void setInfoDeficiencia_observacao(String infoDeficiencia_observacao) {
        this.infoDeficiencia_observacao = infoDeficiencia_observacao;
    }

    public List<Dependentes> getDependentes() {
        return null == dependentes ? new ArrayList<>() : dependentes;
    }

    public void setDependentes(List<Dependentes> dependentes) {
        this.dependentes = dependentes;
    }

    public String getAposentadoria_trabAposent() {
        return aposentadoria_trabAposent;
    }

    public void setAposentadoria_trabAposent(String aposentadoria_trabAposent) {
        this.aposentadoria_trabAposent = aposentadoria_trabAposent;
    }

    public String getContato_fonePrinc() {
        return contato_fonePrinc;
    }

    public void setContato_fonePrinc(String contato_fonePrinc) {
        this.contato_fonePrinc = contato_fonePrinc;
    }

    public String getContato_foneAlternat() {
        return contato_foneAlternat;
    }

    public void setContato_foneAlternat(String contato_foneAlternat) {
        this.contato_foneAlternat = contato_foneAlternat;
    }

    public String getContato_emailPrinc() {
        return contato_emailPrinc;
    }

    public void setContato_emailPrinc(String contato_emailPrinc) {
        this.contato_emailPrinc = contato_emailPrinc;
    }

    public String getContato_emailAlternat() {
        return contato_emailAlternat;
    }

    public void setContato_emailAlternat(String contato_emailAlternat) {
        this.contato_emailAlternat = contato_emailAlternat;
    }

    public static class Dependentes {

        private String dependente_tpDep;
        private String dependente_nmDep;
        private String dependente_dtNascto;
        private String dependente_depIRRF;
        private String dependente_depSF;
        private String dependente_incTrab;
        private String dependente_cpfDep;
        private String dependente_sexoDep;

        public String getDependente_tpDep() {
            return null == dependente_tpDep ? "" : dependente_tpDep;
        }

        public void setDependente_tpDep(String dependente_tpDep) {
            this.dependente_tpDep = dependente_tpDep;
        }

        public String getDependente_nmDep() {
            return null == dependente_nmDep ? "" : dependente_nmDep;
        }

        public void setDependente_nmDep(String dependente_nmDep) {
            this.dependente_nmDep = dependente_nmDep;
        }

        public String getDependente_dtNascto() {
            return null == dependente_dtNascto ? "" : dependente_dtNascto;
        }

        public void setDependente_dtNascto(String dependente_dtNascto) {
            this.dependente_dtNascto = dependente_dtNascto;
        }

        public String getDependente_depIRRF() {
            return null == dependente_depIRRF ? "" : dependente_depIRRF;
        }

        public void setDependente_depIRRF(String dependente_depIRRF) {
            this.dependente_depIRRF = dependente_depIRRF;
        }

        public String getDependente_depSF() {
            return null == dependente_depSF ? "" : dependente_depSF;
        }

        public void setDependente_depSF(String dependente_depSF) {
            this.dependente_depSF = dependente_depSF;
        }

        public String getDependente_incTrab() {
            return null == dependente_incTrab ? "" : dependente_incTrab;
        }

        public void setDependente_incTrab(String dependente_incTrab) {
            this.dependente_incTrab = dependente_incTrab;
        }

        public String getDependente_cpfDep() {
            return dependente_cpfDep;
        }

        public void setDependente_cpfDep(String dependente_cpfDep) {
            this.dependente_cpfDep = dependente_cpfDep;
        }
        
        public String getDependente_sexoDep() {
            return dependente_sexoDep;
        }

        public void setDependente_sexoDep(String dependente_sexoDep) {
            this.dependente_sexoDep = dependente_sexoDep;
        }
    }

    public String getMatr() {
        return matr;
    }

    public void setMatr(String matr) {
        this.matr = matr;
    }

}

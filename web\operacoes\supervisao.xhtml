<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/supervisao.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css"  href="../assets/css/style.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>

            <ui:fragment rendered="#{solicitacaoSenhaMB.temPermissao}">
                <script
                    src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}">
                </script>
            </ui:fragment>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{supervisao.Persistencia(login.pp)}" />
            </f:metadata>
            <p:growl id="msgs" />
            <div id="body">
                <ui:include src="/botao_panico.xhtml"/>

                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-5" style="align-self: center;">
                                    <img src="../assets/img/icone_supervisao.png" height="40" width="40"/>
                                    #{localemsgs.Supervisoes}
                                </div>

                                <div class="ui-grid-col-4" style="align-self: center; text-align: center;">
                                    <h:outputText value="#{localemsgs.Periodo}: "/>
                                    <h:outputText value="#{supervisao.data1}" converter="conversorData" />
                                    <h:outputText value=" - "/>
                                    <h:outputText value="#{supervisao.data2}" converter="conversorData"/>
                                </div>

                                <div class="ui-grid-col-3" style="align-self: center; text-align: center;">
                                    <p:hotkey bind="left" action="#{supervisao.DataAnterior}"  update="main cabecalho"/>
                                    <p:hotkey bind="right" action="#{supervisao.DataPosterior}" update="main cabecalho msgs"/>
                                    <p:hotkey bind="c" oncomplete="PF('dlgCalendarios').show();" update="cabecalho"/>
                                    <p:commandLink action="#{supervisao.DataAnterior}"  update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 40px"/>
                                    </p:commandLink>

                                    <p:commandLink oncomplete="PF('dlgCalendarios').show();"
                                                   styleClass="botao" update="cabecalho">
                                        <p:graphicImage url="../assets/img/icone_escaladodia.png" style="align-self: center;height: 40px"/>
                                    </p:commandLink>

                                    <p:commandLink action="#{supervisao.DataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 40px"/>
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-6">
                                        #{localemsgs.Filial}: #{supervisao.nomeFilial}
                                    </div>
                                    <div class="ui-grid-col-6">
                                        #{localemsgs.QtdSupervisoes}: #{supervisao.total}
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="p" actionListener="#{supervisao.PrePesquisar}" oncomplete="PF('dlgPesquisar').show()" update="formPesquisar"/>
                    <p:hotkey bind="e" update="formDetalhes msgs" actionListener="#{supervisao.buttonAction}"/>
                    <p:hotkey bind="q" actionListener="#{supervisao.ListarQuestoes}" oncomplete="PF('dlgQuestoes').show()" update="formQuestoes"/>
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline">
                                    <p:dataTable id="tabela" value="#{supervisao.allSupervisoes}" paginator="true" rows="15" lazy="true"
                                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Supervisoes}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista" rowKey="#{lista.tmktdetpst.sequencia}"
                                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                                 selection="#{supervisao.supervisaoSelecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true" scrollWidth="100%"
                                                 style="font-size: 12px; background: white">
                                        <p:ajax event="rowDblselect" listener="#{supervisao.dblSelect}" update="formDetalhes msgs"/>
                                        <p:column headerText="#{localemsgs.CodFil}" style="width: 48px;">
                                            <h:outputText value="#{lista.tmktdetpst.codFil}" title="#{lista.tmktdetpst.codFil}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }">
                                                <f:convertNumber pattern="0000" />
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Local}" style="width: 360px;">
                                            <h:outputText value="#{lista.pstserv.local}" title="#{lista.pstserv.local}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.TipoPosto}" style="width: 45px">
                                            <h:outputText value="#{lista.pstserv.tipoPosto}" title="#{lista.pstserv.tipoPosto}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.TipoPostoDesc}" style="width: 170px">
                                            <h:outputText value="#{lista.pstserv.tipoPostoDesc}" title="#{lista.pstserv.tipoPostoDesc}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Situacao}" style="width: 60px">
                                            <h:outputText value="#{lista.tmktdetpst.situacao}" title="#{lista.tmktdetpst.situacao}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Historico}" style="width: 215px">
                                            <h:outputText value="#{lista.tmktdetpst.historico}" title="#{lista.tmktdetpst.historico}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Detalhes}" style="width: 265px">
                                            <h:outputText value="#{lista.tmktdetpst.detalhes}" title="#{lista.tmktdetpst.detalhes}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.QtdEntrevistas}" style="width: 177px">
                                            <h:outputText value="#{lista.qtdEntrevistas}" title="#{lista.qtdEntrevistas}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Data}" style="width: 77px">
                                            <h:outputText value="#{lista.tmktdetpst.data}" title="#{lista.tmktdetpst.data}" converter="conversorData"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora}" style="width: 58px">
                                            <h:outputText value="#{lista.tmktdetpst.hora}" title="#{lista.tmktdetpst.hora}"
                                                          style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                   lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" update="formDetalhes msgs" actionListener="#{supervisao.buttonAction}">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{supervisao.PrePesquisar}"
                                           oncomplete="PF('dlgPesquisar').show()" update="formPesquisar">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40" />
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Questoes}" actionListener="#{supervisao.ListarQuestoes}"
                                           oncomplete="PF('dlgQuestoes').show()" update="formQuestoes">
                                <p:graphicImage url="../assets/img/icone_redondo_entrevistas.png" height="40" />
                            </p:commandLink>
                        </div>
                        <div style=" top: 0px; right: 5px; position: fixed">
                            <p:commandLink title="#{localemsgs.Voltar}" action="#{login.voltar}">
                                <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                            </p:commandLink>
                        </div>
                        <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                    </p:panel>

                    <p:dialog widgetVar="dlgCalendarios" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-size: 750px 430px;">
                        <p:hotkey bind="esc" oncomplete="PF('dlgCalendarios').hide()"/>
                        <div class="ui-grid-row ui-grid-responsive">
                            <div class="ui-grid-col-6">
                                <div class="ui-grid-row">
                                    <div style="width: 50%; float: left">
                                        <p:outputLabel for="cal1" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}"/>
                                    </div>
                                    <div style="width: 50%; float: left">
                                        <p:inputText id="empty" style="width: 0px; z-index: -1; position: fixed"/>
                                        <p:inputMask id="cal1" value="#{supervisao.d1}" mask="99/99/9999"
                                                     style="width: 85px" converter="conversorData">
                                            <p:ajax event="blur" listener="#{supervisao.EscreverData1}" update="calendario1"/>
                                        </p:inputMask>
                                    </div>
                                </div>
                                <p:calendar id="calendario1" styleClass="calendario" mode="inline"
                                            value="#{supervisao.dataSelecionada1}"
                                            title="#{localemsgs.DataInicial}" label="#{localemsgs.DataInicial}"
                                            pattern="yyyyMMdd" locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{supervisao.SelecionarData1}" update="cal1"/>
                                </p:calendar>
                            </div>
                            <div class="ui-grid-col-6">
                                <div class="ui-grid-row">
                                    <div style="width: 50%; float: left">
                                        <p:outputLabel for="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}"/>
                                    </div>
                                    <div style="width: 50%; float: left">
                                        <p:inputMask id="cal2" value="#{supervisao.d2}" mask="99/99/9999"
                                                     style="width: 85px" converter="conversorData">
                                            <p:ajax event="blur" listener="#{supervisao.EscreverData2}" update="calendario2"/>
                                        </p:inputMask>
                                    </div>
                                </div>
                                <p:calendar id="calendario2" styleClass="calendario" mode="inline"
                                            value="#{supervisao.dataSelecionada2}"
                                            title="#{localemsgs.DataFinal}" label="#{localemsgs.DataFinal}"
                                            pattern="yyyyMMdd" locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{supervisao.SelecionarData2}" update="cal2"/>
                                </p:calendar>
                            </div>
                        </div>
                        <div style="text-align: right; float: right">
                            <p:commandLink action="#{supervisao.SelecionarData}" style="float: right">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" height="40" />
                            </p:commandLink>
                        </div>
                    </p:dialog>
                </h:form>

                <h:form id="formDetalhes" class="ui-fluid">
                    <p:hotkey bind="esc" oncomplete="PF('dlgListarSupervisoes').hide()"/>
                    <p:dialog widgetVar="dlgListarSupervisoes" positionType="absolute" responsive="true"
                              draggable="false" resizable="false" dynamic="true" closable="true" modal="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dialogosupervisao"
                              style="background-image: url('../assets/img/bk.jpg')">
                        <f:facet name="header">
                            <img src="../assets/img/icone_supervisao.png" height="40" width="40"/>
                            <h:outputText value="#{localemsgs.Supervisoes}" style="color:#022a48" />
                        </f:facet>

                        <p:panel id="supervisao" styleClass="panelTelaSupervisao">
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row cabecalhoSupervisao" style="background-color: white">
                                    <div class="ui-grid-col-5" style="align-self: center;">
                                        #{localemsgs.Filial}: #{supervisao.filial.descricao}
                                    </div>
                                    <div class="ui-grid-col-4" style="align-self: center;">
                                        #{localemsgs.Operador}: #{supervisao.supervisaoSelecionado.tmktdetpst.operador}
                                    </div>
                                    <div class="ui-grid-col-3" style="align-self: center;">
                                        #{localemsgs.Data}:
                                        <h:outputText value="#{supervisao.supervisaoSelecionado.tmktdetpst.data}" converter="conversorData" />
                                        #{supervisao.supervisaoSelecionado.tmktdetpst.hora}
                                    </div>
                                </div>
                                <div class="ui-grid-row detalhesSupervisao">
                                    <div class="ui-grid-col-6" >
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float: left;">
                                                <h:outputText value="#{localemsgs.Posto}: " style="color: black; font-size: 12px;"/>
                                            </div>
                                            <div style="width: 80% !important; float: left;">
                                                <h:outputText value="#{supervisao.supervisaoSelecionado.pstserv.local}"
                                                              style="color: black;  font-size: 16px;"/>
                                                <div style="color: #black; font-size: 14px;">
                                                    #{supervisao.supervisaoSelecionado.clientes.NRed} - #{supervisao.supervisaoSelecionado.clientes.nome}
                                                </div>
                                                <div style="color: black; font-size: 12px;">
                                                    #{supervisao.supervisaoSelecionado.clientes.ende} - #{supervisao.supervisaoSelecionado.clientes.bairro}
                                                </div>
                                                <div style="color: black; font-size: 12px;">
                                                    #{supervisao.supervisaoSelecionado.clientes.cidade}/#{supervisao.supervisaoSelecionado.clientes.estado} - #{localemsgs.CEP}: <h:outputText value="#{supervisao.supervisaoSelecionado.clientes.CEP}" converter="conversorCEP"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div class="ui-grid-col-6">
                                                <h:outputText value="#{localemsgs.FotosLocal}: " style="color: black; font-size: 12px;"/>
                                            </div>
                                            <div class="ui-grid-col-6">
                                                <h:outputText value="#{localemsgs.QtdFotos}: " style="color: black; font-size: 12px;"/>
                                                <h:outputText value="#{supervisao.supervisaoSelecionado.fotos.size()}" style="color: black; font-size: 12px;"/>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div style="width: 10% !important; float: left; text-align: center">
                                                <p:commandLink action="#{supervisao.VoltarFotoPosto}" rendered="#{supervisao.supervisaoSelecionado.fotos.size() gt 0}"
                                                               update="formDetalhes:fotoPosto msgs">
                                                    <p:graphicImage url="../assets/img/botao_anterior.png" height="20" title="#{localemsgs.FotoAnterior}"/>
                                                </p:commandLink>
                                            </div>
                                            <div style="width: 80% !important; float: left; text-align: center">
                                                <p:lightBox style=" text-align: center;" id="fotoPosto">
                                                    <h:outputLink value="#{supervisao.fotoPosto}"
                                                                  title="#{supervisao.supervisaoSelecionado.pstserv.local}">
                                                        <h:graphicImage value="#{supervisao.fotoPosto}" id="panelFotoQuestionario" style="height: 200px;"/>
                                                    </h:outputLink>
                                                </p:lightBox>
                                            </div>
                                            <div style="width: 10% !important; float: left; text-align: center">
                                                <p:commandLink action="#{supervisao.AvancarFotoPosto}" rendered="#{supervisao.supervisaoSelecionado.fotos.size() gt 0}"
                                                               update="formDetalhes:fotoPosto msgs">
                                                    <p:graphicImage url="../assets/img/botao_proximo.png" height="20" title="#{localemsgs.ProximaFoto}"/>
                                                </p:commandLink>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ui-grid-col-6" >
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float:left">
                                                <h:outputText value="#{localemsgs.Tipo}:" style="color:black;font-size: 12px;"/>
                                            </div>
                                            <div style="width: 80% !important; float:left">
                                                <h:outputText value="#{supervisao.supervisaoSelecionado.pstserv.tipoPosto} " style="color: black; font-size: 16px"/>
                                                <h:outputText value="- #{supervisao.supervisaoSelecionado.pstserv.tipoPostoDesc}" style="color: black; font-size: 16px"/>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float:left">
                                                <h:outputText value="#{localemsgs.Situacao}:" style="color: black;font-size: 12px; "/>
                                            </div>
                                            <div style="width: 40% !important; float:left">
                                                <h:outputText id="situacao1" value="#{supervisao.supervisaoSelecionado.tmktdetpst.situacao} " style="color: black;"/>
                                                <p:commandLink oncomplete="PF('dlgSituacao').show()" >
                                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="20"/>
                                                </p:commandLink>
                                                <p:inputText id="situacao2" value="#{supervisao.situacoes.get(supervisao.supervisaoSelecionado.tmktdetpst.situacao)}"
                                                             style="font-size: 12px; width: 100px" disabled="true"/>
                                                <p:dialog widgetVar="dlgSituacao"
                                                          resizable="false" dynamic="true" closable="false"
                                                          width="400" showEffect="drop" hideEffect="drop"
                                                          style="background-image: url('../assets/img/bk.jpg');">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{localemsgs.Situacao}" style="color: black"/>
                                                    </f:facet>
                                                    <p:panel id="listarSituacoes" style="background-color: transparent;  padding-left: 13px ">
                                                        <p:dataTable id="niveis" value="#{supervisao.listaSituacoes}"  emptyMessage="#{localemsgs.SemRegistros}"
                                                                     var="lista" rowKey="#{lista}" resizableColumns="true"
                                                                     style="font-size: 12px" selectionMode="single" styleClass="tabela"
                                                                     selection="#{supervisao.situacao}">
                                                            <p:ajax event="rowSelect" listener="#{supervisao.SelecionarSituacao}" />
                                                            <p:column headerText="#{localemsgs.Situacao}">
                                                                <h:outputText value="#{lista}" title="#{lista}"/>
                                                            </p:column>
                                                            <p:column headerText="#{localemsgs.Descricao}" >
                                                                <h:outputText value="#{supervisao.situacoes.get(lista)}" title="#{supervisao.situacoes.get(lista)}"/>
                                                            </p:column>
                                                        </p:dataTable>
                                                        <div class="form-inline">
                                                            <p:commandLink oncomplete="PF('dlgSituacao').hide()" action="#{supervisao.AtualizarSituacao}"
                                                                           title="#{localemsgs.Selecionar}" update="formDetalhes:situacao1 formDetalhes:situacao2 main:tabela" process="@this">
                                                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                                            </p:commandLink>
                                                            <p:commandLink oncomplete="PF('dlgSituacao').hide()"
                                                                           title="#{localemsgs.Voltar}">
                                                                <p:graphicImage url="../assets/img/icone_voltar.png" width="40" height="40" />
                                                            </p:commandLink>
                                                        </div>
                                                    </p:panel>
                                                </p:dialog>
                                            </div>
                                            <div style="width: 40% !important; float:left">
                                                <h:outputText value="#{supervisao.supervisaoSelecionado.pstserv.secao} " style="color: black; font-size: 11px;"/>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float:left">
                                                <h:outputText value="#{localemsgs.Historico}:" style="color: black;font-size: 12px;"/>
                                            </div>
                                            <div style="width: 80% !important; float:left">
                                                <h:outputText value="#{supervisao.supervisaoSelecionado.tmktdetpst.historico} " style="color:black;"/>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <div style="width: 20% !important; float:left">
                                                <h:outputText value="#{localemsgs.Mapa}:" style="color:black;font-size: 12px;"/>
                                            </div>
                                            <div style="width: 80% !important; float:left">
                                                <h:outputText value="#{localemsgs.Distancia} (m): " style="color:black; text-align: right" />
                                                <h:outputText value="#{supervisao.distPstSup}" style="color:black; text-align: right">
                                                    <f:convertNumber maxFractionDigits="2" />
                                                </h:outputText>
                                            </div>
                                        </div>
                                        <div class="ui-grid-row">
                                            <p:gmap center="#{supervisao.coordenadas}" zoom="15" model="#{supervisao.pin}"
                                                    type="ROADMAP" style="width:100%;height:210px;"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="ui-grid-row detalhesSupervisao">
                                    <div class="ui-grid-col-1">
                                        <p:outputLabel for="detalhes" value="#{localemsgs.Detalhes}: " style="color: black;"/>
                                    </div>
                                    <div class="ui-grid-col-11">
                                        <p:inputText style="width: 100%" disabled="true" id="detalhes"
                                                     value="#{supervisao.supervisaoSelecionado.tmktdetpst.detalhes}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row detalhesSupervisao">
                                    <div class="ui-grid-col-3" style="text-align: center; padding-right: 5px">
                                        <h:outputText rendered="#{not empty supervisao.funcionarios}"
                                                      style="font-size:14px; color:balck; font-weight: normal"
                                                      value="#{localemsgs.Entrevistas}" />
                                        <p:dataTable id="tabelaFuncion" value="#{supervisao.funcionarios}"  emptyMessage="#{localemsgs.SemRegistros}"
                                                     styleClass="tabela" var="lista" rowKey="#{lista.funcion.matr}" resizableColumns="true"
                                                     scrollable="true" scrollHeight="130" rendered="#{not empty supervisao.funcionarios}"
                                                     selectionMode="single" selection="#{supervisao.questionarioSelecionado}"
                                                     style="font-size: 12px; width: 100%">
                                            <p:ajax event="rowSelect" listener="#{supervisao.SelecionarQuestionario}"
                                                    update="formDetalhes:panelQuestoes formDetalhes:panelFotoFuncion" />
                                            <p:column headerText="#{localemsgs.Matr}" style="width: 65px">
                                                <h:outputText value="#{lista.funcion.matr}" title="#{lista.funcion.matr}">
                                                    <f:convertNumber pattern="0" />
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Nome}">
                                                <h:outputText value="#{lista.funcion.nome}" title="#{lista.funcion.nome}"/>
                                            </p:column>
                                        </p:dataTable>
                                        <p:graphicImage url="../assets/img/funcionarios.png" style="width:100%;"
                                                        rendered="#{empty supervisao.funcionarios}"/>
                                    </div>
                                    <div class="ui-grid-col-5" style="text-align: center">
                                        <p:panel id="panelQuestoes">
                                            <h:outputText style="font-size:14px; font-weight: normal; color: black;"
                                                          value="#{localemsgs.Checklist}"
                                                          rendered="#{supervisao.questoes.size() ge 1}"/>
                                            <p:panel rendered="#{not empty supervisao.questoes}">
                                                <p:inputTextarea style="font-size:12px; font-weight: normal; text-align: left; width: 100%; height: 40px"
                                                                 scrollHeight="20" autoResize="false" readonly="true" disabled="true"
                                                                 value="#{supervisao.detalhesQuestoes}"
                                                                 title="#{supervisao.detalhesQuestoes}"/>
                                                <p:dataTable id="tabelaDetalhesQuestionario" value="#{supervisao.questoes}" emptyMessage="#{localemsgs.SemRegistros}"
                                                             styleClass="tabela" var="lista" rowKey="#{lista.psthstqst.codQuestao}"
                                                             resizableColumns="true" rendered="#{supervisao.questoes.size() gt 1}"
                                                             scrollable="true" scrollHeight="100"
                                                             style="font-size: 11px">
                                                    <p:column headerText="#{localemsgs.Qst}" style="width: 210px">
                                                        <h:outputText value="#{lista.tbval.descricao}" title="#{lista.tbval.descricao}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Resp}" style="width: 70px">
                                                        <h:outputText value="#{supervisao.respostas.get(lista.psthstqst.resposta)}"
                                                                      title="#{supervisao.respostas.get(lista.psthstqst.resposta)}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Detalhes}">
                                                        <h:outputText value="#{lista.psthstqst.detalhes}" title="#{lista.psthstqst.detalhes}"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </p:panel>
                                            <p:graphicImage url="../assets/img/questionario.png" style="max-height:200px"
                                                            rendered="#{empty supervisao.questoes}" />
                                        </p:panel>
                                    </div>
                                    <div class="ui-grid-col-4" style="text-align: center; padding-left: 5px">
                                        <p:panel id="panelFotoFuncion">
                                            <div class="ui-grid-row">
                                                <div class="ui-grid-col-12" style="text-align: center">
                                                    <h:outputText style="font-size:14px; font-weight: normal; color:black;
                                                                  text-align: center" value="#{localemsgs.Galeria}"
                                                                  rendered="#{not empty supervisao.questionarioSelecionado.endfotos}"/>
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div class="ui-grid-col-12" style="text-align: left">
                                                    <h:outputText value="#{localemsgs.Foto} #{supervisao.posFotoFuncion + 1} #{localemsgs.De}
                                                                  #{supervisao.questionarioSelecionado.qtdefotos}:"
                                                                  style="font-size:12px; font-weight: normal; color: black; "
                                                                  rendered="#{not empty supervisao.questionarioSelecionado.endfotos}" />
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div style="width: 20% !important; float: left; text-align: center">
                                                    <p:commandLink action="#{supervisao.VoltarFotoFuncion}"
                                                                   rendered="#{not empty supervisao.questionarioSelecionado.endfotos}"
                                                                   update="formDetalhes:panelFotoFuncion msgs">
                                                        <p:graphicImage url="../assets/img/botao_anterior.png" height="20" title="#{localemsgs.FotoAnterior}"/>
                                                    </p:commandLink>
                                                </div>
                                                <div style="width: 60% !important; float: left; text-align: center">
                                                    <p:lightBox styleClass="fotoGrande" rendered="#{not empty supervisao.questionarioSelecionado.endfotos}">
                                                        <h:outputLink value="#{supervisao.fotoFuncion}" title="#{supervisao.questionarioSelecionado.funcion.nome}">
                                                            <h:graphicImage value="#{supervisao.fotoFuncion}" id="fotoFuncionario" style="height: 150px;"/>
                                                        </h:outputLink>
                                                    </p:lightBox>
                                                </div>
                                                <div style="width: 20% !important; float: left; text-align: center">
                                                    <p:commandLink action="#{supervisao.AvancarFotoFuncion}"
                                                                   rendered="#{not empty supervisao.questionarioSelecionado.endfotos}"
                                                                   update="formDetalhes:panelFotoFuncion msgs">
                                                        <p:graphicImage url="../assets/img/botao_proximo.png" height="20" title="#{localemsgs.ProximaFoto}"/>
                                                    </p:commandLink>
                                                </div>
                                            </div>
                                            <p:graphicImage url="../assets/img/galeria.png" style="height:200px" rendered="#{empty supervisao.questionarioSelecionado.endfotos}"/>
                                        </p:panel>
                                    </div>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog widgetVar="dlgPesquisar" draggable="false" fitViewport="true" positionType="absolute" responsive="true"
                              modal="true" closable="true" resizable="false" dynamic="true" width="440"
                              showEffect="drop" hideEffect="drop">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_postosdeservico.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarSupervisao}" style="color:#022a48" />
                        </f:facet>
                        <div class="form-inline">
                            <p:outputLabel for="subFil" value="#{localemsgs.Filial}: " style="color: black; float:left; position:absolute;"/>
                            <p:selectOneMenu id="subFil" converter="omnifaces.SelectItemsConverter" value="#{supervisao.filial}"
                                             filter="true" filterMatchMode="contains" style="float: left;left:120px;position:absolute; width: 280px">
                                <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                               itemLabel="#{filial.descricao}"/>
                            </p:selectOneMenu>
                        </div>

                        <p:spacer height="35px"/>

                        <div>
                            <p:outputLabel for="dataInicio" value="#{localemsgs.Periodo}:" style="color: black; float:left; position:absolute;"/>
                            <p:calendar id="dataInicio" style="float: left;left:120px;position:absolute;" value="#{supervisao.dataSelecionada1}"
                                        pattern="dd/MM/yyyy" styleClass="calendarios" mask="true"
                                        navigator="true">
                                <p:ajax event="dateSelect" listener="#{supervisao.SelecionarDataInicio}" update="formPesquisar:dataFim"/>
                            </p:calendar>
                            <p:calendar id="dataFim" style="float: left;left:220px;position:absolute" value="#{supervisao.dataSelecionada2}"
                                        disabled="#{supervisao.dataSelecionada1 eq null}" mask="true" navigator="true"
                                        pattern="dd/MM/yyyy" styleClass="calendarios" mindate="#{supervisao.dataSelecionada1}">
                                <p:ajax event="dateSelect" listener="#{supervisao.SelecionarDataFim}"/>
                            </p:calendar>
                        </div>

                        <p:spacer height="35px"/>

                        <div class="form-inline">
                            <p:commandLink id="btnLogar" action="#{supervisao.PesquisaPaginada}"
                                           update=":msgs :main:tabela main cabecalho"
                                           title="#{localemsgs.Pesquisar}">
                                <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:dialog>
                </h:form>

                <h:form id="formQuestoes">
                    <p:hotkey bind="esc" oncomplete="PF('dlgQuestoes').hide()"/>
                    <p:dialog widgetVar="dlgQuestoes" draggable="false" fitViewport="true" positionType="absolute" responsive="true"
                              modal="true" closable="true" resizable="false" dynamic="true" styleClass="dlgquestoes" id="dlgquestoes"
                              showEffect="drop" hideEffect="drop">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_supervisoesrecentes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarQuestoes}" style="color:#022a48;" />
                        </f:facet>
                        <p:panelGrid columns="2" styleClass="botao">
                            <p:panel style="width: 735px;">
                                <p:dataTable id="tabela" value="#{supervisao.lista}" styleClass="tabela" scrollHeight="500px"
                                             var="lista" rowKey="#{lista.codigo}" resizableColumns="true" scrollable="true"
                                             selectionMode="single" selection="#{supervisao.questao}" style="font-size: 12px">
                                    <p:column headerText="#{localemsgs.Codigo}" style="width: 55px">
                                        <h:outputText value="#{lista.codigo}" title="#{lista.codigo}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Descricao}" style="width: 330px">
                                        <h:outputText value="#{lista.descricao}" title="#{lista.descricao}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Operador}" style="width: 110px">
                                        <h:outputText value="#{lista.operador}" title="#{lista.operador}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 90px">
                                        <h:outputText value="#{lista.dt_Alter}" title="#{lista.dt_Alter}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 60px">
                                        <h:outputText value="#{lista.hr_Alter}" title="#{lista.hr_Alter}"/>
                                    </p:column>
                                </p:dataTable>
                            </p:panel>
                            <p:panel style="width: 30px;">
                                <div style="position: absolute; top: 5px;">
                                    <p:commandLink title="#{localemsgs.Adicionar}" update="cadastrarQuestoes"
                                                   oncomplete="PF('dlgCadastrarQuestao').show();" actionListener="#{supervisao.NovaQuestao}">
                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30"/>
                                    </p:commandLink>

                                    <p:spacer height="30px"/>

                                    <p:commandLink title="#{localemsgs.Editar}" update="cadastrarQuestoes"
                                                   actionListener="#{supervisao.PreEdicaoQuestao}">
                                        <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                    </p:commandLink>

                                    <p:spacer height="30px"/>

                                    <p:commandLink title="#{localemsgs.Editar}" update="formQuestoes:tabela msgs"
                                                   action="#{supervisao.ExcluirQuestao}">
                                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirQuestao}" icon="ui-icon-alert" />
                                        <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="30"/>
                                    </p:commandLink>
                                    <p:confirmDialog global="true">
                                        <div>
                                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                        </div>
                                    </p:confirmDialog>
                                </div>
                            </p:panel>
                        </p:panelGrid>
                    </p:dialog>
                </h:form>

                <h:form id="cadastrarQuestoes">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrarQuestao').hide()"/>
                    <p:dialog widgetVar="dlgCadastrarQuestao" draggable="false" fitViewport="true" positionType="absolute" responsive="true"
                              modal="true" closable="true" resizable="false" dynamic="true" styleClass="dlgquestoes" width="440"
                              showEffect="drop" hideEffect="drop">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_supervisoesrecentes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarQuestao}" style="color:#022a48;" />
                        </f:facet>
                        <div class="form-inline">
                            <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: " style="color: black; float:left; position:absolute;"/>
                            <p:inputText id="descricao" value="#{supervisao.questao.descricao}"
                                         required="true" label="#{localemsgs.Descricao}"
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Descricao}"
                                         style="float: left;left:120px;position:absolute; width: 280px"/>
                            <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                        </div>

                        <p:spacer height="35px"/>

                        <div class="form-inline">
                            <p:commandLink id="btnLogar" action="#{supervisao.CadastrarQuestao}" update="msgs formQuestoes:tabela"
                                           title="#{localemsgs.Cadastrar}" rendered="#{supervisao.flagQuestao eq 1}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                            <p:commandLink id="btnLogar2" action="#{supervisao.EditarQuestao}" update="msgs formQuestoes:tabela"
                                           title="#{localemsgs.Editar}" rendered="#{supervisao.flagQuestao eq 2}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>
                <div class="footer-body" id="footer-body">
                    <div>
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.Corporativo}: " />
                            <p:selectBooleanCheckbox value="#{supervisao.mostrarFiliais}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{supervisao.MostrarFiliais}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.LimparFiltros}: " />
                            <p:selectBooleanCheckbox value="#{supervisao.limparFiltros}">
                                <p:ajax update="msgs main cabecalho corporativo" listener="#{supervisao.LimparFiltros}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>

            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>


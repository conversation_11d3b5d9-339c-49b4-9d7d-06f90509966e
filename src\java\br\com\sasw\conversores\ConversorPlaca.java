/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorPlaca")
public class ConversorPlaca implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        String placaFormatada = " ";

        if (value.toString().length() == 7) {
            placaFormatada = value.toString().substring(0, 3) + "-" + value.toString().substring(3, 7);
        } else {
            placaFormatada = value.toString();
        }

        return placaFormatada;
    }

}

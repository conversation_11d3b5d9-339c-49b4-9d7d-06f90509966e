/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2298;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2298Dao {

    public List<S2298> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<S2298> retorno = new ArrayList<>();
            String sql = "Select \n"
                    + "'1' ideEvento_indRetif, \n"
                    + "'' ideEvento_nrRecibo, \n"
                    + "'1' ideEvento_procEmi, \n"
                    + "'Satellite eSocial' ideEvento_verProc, \n"
                    + "Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, \n"
                    + "Substring(Filiais.CNPJ,1,8) ideEmpregador_nrInsc,\n"
                    + "Funcion.CPF ideVinculo_cpfTrab,\n"
                    + "Funcion.PIS ideVinculo_nisTrab,\n"
                    + "Funcion.Matr ideVinculo_matricula,\n"
                    + "FuncionAdic.TpReint infoReintegr_tpReint,\n"
                    + "substring(replace(convert(varchar,FuncionAdic.DtEfetRetorno,111),'/','-'),0,11) infoReintegr_dtEfetRetorno,\n"
                    + "substring(replace(convert(varchar,FuncionAdic.DtEfetRetorno,111),'/','-'),0,11) infoReintegr_dtEfeito,\n"
                    + "FuncionAdic.IndPagtoJuizo infoReintegr_indPagtoJuizo,"
                    + " (select max(sucesso) from ( \n"
                    + " (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  \n"
                    + " From XmleSocial z where z.Identificador = Funcion.Matr  \n"
                    + "                and z.evento = 'S-2298'  \n"
                    + "                and z.CodFil = ?  \n"
                    + "                and z.Compet = ?  \n"
                    + "                and z.Ambiente = ?  \n"
                    + "                and (z.Xml_Retorno like '%aguardando%'  \n"
                    + "                        or z.Xml_Retorno = ''  \n"
                    + "                        or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))  \n"
                    + " union  \n"
                    + " (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  \n"
                    + " From XmleSocial z where z.Identificador = Funcion.Matr  \n"
                    + "                and z.evento = 'S-2298'  \n"
                    + "                and z.CodFil = ?  \n"
                    + "                and z.Compet = ?  \n"
                    + "                and z.Ambiente = ?  \n"
                    + "                and (z.Xml_Retorno like '%<ocorrencia>%'  \n"
                    + "                        or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  \n"
                    + " union  \n"
                    + " (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  \n"
                    + " From XmleSocial z where z.Identificador = Funcion.Matr  \n"
                    + "                and z.evento = 'S-2298'  \n"
                    + "                and z.CodFil = ?  \n"
                    + "                and z.Compet = ?  \n"
                    + "                and z.Ambiente = ?  \n"
                    + "                and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso  \n"
                    + "From FuncionAdic \n"
                    + "Left join Funcion  on Funcion.Matr = FuncionAdic.Matr\n"
                    + "Left join Filiais  on Filiais.CodFil = Funcion.CodFil \n"
                    + "where substring(convert(varchar,FuncionAdic.DtEfetRetorno ,121),1,7) = ? "
                    + "  and Funcion.CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.select();
            S2298 s2298;
            while (consulta.Proximo()) {
                s2298 = new S2298();
                s2298.setSucesso(consulta.getInt("sucesso"));
                s2298.setIdeEvento_indRetif(consulta.getString("ideEvento_indRetif"));
                s2298.setIdeEvento_nrRecibo(consulta.getString("ideEvento_nrRecibo"));
                s2298.setIdeEvento_tpAmb(ambiente);
                s2298.setIdeEvento_procEmi(consulta.getString("ideEvento_procEmi"));
                s2298.setIdeEvento_verProc(consulta.getString("ideEvento_verProc"));
                s2298.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2298.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2298.setIdeVinculo_cpfTrab(consulta.getString("ideVinculo_cpfTrab"));
                s2298.setIdeVinculo_nisTrab(consulta.getString("ideVinculo_nisTrab"));
                s2298.setIdeVinculo_matricula(consulta.getString("ideVinculo_matricula"));

                s2298.setInfoReintegr_tpReint(consulta.getString("infoReintegr_tpReint"));
                s2298.setInfoReintegr_dtEfeito(consulta.getString("infoReintegr_dtEfeito"));
                s2298.setInfoReintegr_dtEfetRetorno(consulta.getString("infoReintegr_dtEfetRetorno"));
                s2298.setInfoReintegr_indPagtoJuizo(consulta.getString("infoReintegr_indPagtoJuizo"));
                retorno.add(s2298);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S2298Dao.get - " + e.getMessage());
        }
    }

}

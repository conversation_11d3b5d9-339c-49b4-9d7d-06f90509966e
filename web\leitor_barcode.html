<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title> Ler Barcode </title>
    <script src="assets/scripts/jquery.min.js"></script>
    <meta charset=utf-8 />
    <style>
        html, body {
            padding: 0px !important;
            margin: 0px !important;
            overflow: hidden !important;
        }

        #LinhaCodigo {
            width: 100%;
            position: absolute;
            height: 2px;
            background-color: #ff00005d;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            margin: auto;
            z-index: 2;
        }

        #camera {
            position: relative;
            width: 100%;
            padding: 0px !important;
            margin: 0px !important;
        }

        video {
            height: 100vh;
            padding: 0px !important;
            margin: 0px !important;
        }
    </style>
</head>

<body>
    <div id="resultado"></div>
    <div id="LinhaCodigo"></div>
    <div id="camera"></div>

    <script src="assets/scripts/quagga.min.js"></script>
    <script>

        $(document).ready(function () {
            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.querySelector('#camera')
                },
                decoder: {
                    readers: ["code_128_reader"]
                }
            }, function (err) {
                if (err) {
                    alert('Erro ao inicializar câmera: ' + err);
                    console.log(err);
                    return
                }
                console.log("Initialization finished. Ready to start");
                Quagga.start();
            });

            Quagga.onDetected(function (data) {
                // Código capturado + beep
                $('body').append('<embed src="assets/beeps/beep.mp3" width="1" height="1">');
                console.log(data.codeResult.code);
                window.parent.RetornoBarCode(data.codeResult.code);
            });

            $('video').css('width', $('#camera').width() + 'px');
        });
    </script>

</body>

</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1000 {

    private String id;
    private int sucesso;

    /**
     * 1 - Produção / 2 - Homologação
     */
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    /**
     * J = 1 - CNPJ / F = 2 - CPF
     */
    private String ideEmpregador_tpInsc;
    /**
     * Copy(CNPJ,1,08)+</nrInsc> //Raiz CNPJ
     */
    private String ideEmpregador_nrInsc;
    private String idePeriodo_iniValid;
    private String infoCadastro_nmRazao;
    private String infoCadastro_classTrib;
    private String infoCadastro_natJurid;
    private String infoCadastro_indCoop;
    private String infoCadastro_indConstr;
    private String infoCadastro_indDesFolha;
    private String infoCadastro_indOptRegEletron;
    private String infoCadastro_indEntEd;
    private String infoCadastro_indEtt;
    private String contato_nmCtt;
    private String contato_cpfCtt;
    private String contato_foneFixo;
    private String contato_email;
    private String softwareHouse_cnpjSoftHouse;
    private String softwareHouse_nmRazao;
    private String softwareHouse_nmCont;
    private String softwareHouse_telefone;
    private String softwareHouse_email;
    private String situacaoPJ_indSitPJ;

    public String getId() {
        return null == id ? "" : id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdePeriodo_iniValid() {
        return null == idePeriodo_iniValid ? "" : idePeriodo_iniValid;
    }

    public void setIdePeriodo_iniValid(String idePeriodo_iniValid) {
        this.idePeriodo_iniValid = idePeriodo_iniValid;
    }

    public String getInfoCadastro_nmRazao() {
        return null == infoCadastro_nmRazao ? "" : infoCadastro_nmRazao;
    }

    public void setInfoCadastro_nmRazao(String infoCadastro_nmRazao) {
        this.infoCadastro_nmRazao = infoCadastro_nmRazao;
    }

    public String getInfoCadastro_classTrib() {
        return null == infoCadastro_classTrib ? "" : infoCadastro_classTrib;
    }

    public void setInfoCadastro_classTrib(String infoCadastro_classTrib) {
        this.infoCadastro_classTrib = infoCadastro_classTrib;
    }

    public String getInfoCadastro_natJurid() {
        return null == infoCadastro_natJurid ? "" : infoCadastro_natJurid;
    }

    public void setInfoCadastro_natJurid(String infoCadastro_natJurid) {
        this.infoCadastro_natJurid = infoCadastro_natJurid;
    }

    public String getInfoCadastro_indCoop() {
        return null == infoCadastro_indCoop ? "" : infoCadastro_indCoop;
    }

    public void setInfoCadastro_indCoop(String infoCadastro_indCoop) {
        this.infoCadastro_indCoop = infoCadastro_indCoop;
    }

    public String getInfoCadastro_indConstr() {
        return null == infoCadastro_indConstr ? "" : infoCadastro_indConstr;
    }

    public void setInfoCadastro_indConstr(String infoCadastro_indConstr) {
        this.infoCadastro_indConstr = infoCadastro_indConstr;
    }

    public String getInfoCadastro_indDesFolha() {
        return null == infoCadastro_indDesFolha ? "" : infoCadastro_indDesFolha;
    }

    public void setInfoCadastro_indDesFolha(String infoCadastro_indDesFolha) {
        this.infoCadastro_indDesFolha = infoCadastro_indDesFolha;
    }

    public String getInfoCadastro_indOptRegEletron() {
        return null == infoCadastro_indOptRegEletron ? "" : infoCadastro_indOptRegEletron;
    }

    public void setInfoCadastro_indOptRegEletron(String infoCadastro_indOptRegEletron) {
        this.infoCadastro_indOptRegEletron = infoCadastro_indOptRegEletron;
    }

    public String getInfoCadastro_indEntEd() {
        return null == infoCadastro_indEntEd ? "" : infoCadastro_indEntEd;
    }

    public void setInfoCadastro_indEntEd(String infoCadastro_indEntEd) {
        this.infoCadastro_indEntEd = infoCadastro_indEntEd;
    }

    public String getInfoCadastro_indEtt() {
        return null == infoCadastro_indEtt ? "" : infoCadastro_indEtt;
    }

    public void setInfoCadastro_indEtt(String infoCadastro_indEtt) {
        this.infoCadastro_indEtt = infoCadastro_indEtt;
    }

    public String getContato_nmCtt() {
        return null == contato_nmCtt ? "" : contato_nmCtt;
    }

    public void setContato_nmCtt(String contato_nmCtt) {
        this.contato_nmCtt = contato_nmCtt;
    }

    public String getContato_cpfCtt() {
        return null == contato_cpfCtt ? "" : contato_cpfCtt;
    }

    public void setContato_cpfCtt(String contato_cpfCtt) {
        this.contato_cpfCtt = contato_cpfCtt;
    }

    public String getContato_foneFixo() {
        return null == contato_foneFixo ? "" : contato_foneFixo;
    }

    public void setContato_foneFixo(String contato_foneFixo) {
        this.contato_foneFixo = contato_foneFixo;
    }

    public String getContato_email() {
        return null == contato_email ? "" : contato_email;
    }

    public void setContato_email(String contato_email) {
        this.contato_email = contato_email;
    }

    public String getSoftwareHouse_cnpjSoftHouse() {
        return null == softwareHouse_cnpjSoftHouse ? "" : softwareHouse_cnpjSoftHouse;
    }

    public void setSoftwareHouse_cnpjSoftHouse(String softwareHouse_cnpjSoftHouse) {
        this.softwareHouse_cnpjSoftHouse = softwareHouse_cnpjSoftHouse;
    }

    public String getSoftwareHouse_nmRazao() {
        return null == softwareHouse_nmRazao ? "" : softwareHouse_nmRazao;
    }

    public void setSoftwareHouse_nmRazao(String softwareHouse_nmRazao) {
        this.softwareHouse_nmRazao = softwareHouse_nmRazao;
    }

    public String getSoftwareHouse_nmCont() {
        return null == softwareHouse_nmCont ? "" : softwareHouse_nmCont;
    }

    public void setSoftwareHouse_nmCont(String softwareHouse_nmCont) {
        this.softwareHouse_nmCont = softwareHouse_nmCont;
    }

    public String getSoftwareHouse_telefone() {
        return null == softwareHouse_telefone ? "" : softwareHouse_telefone;
    }

    public void setSoftwareHouse_telefone(String softwareHouse_telefone) {
        this.softwareHouse_telefone = softwareHouse_telefone;
    }

    public String getSoftwareHouse_email() {
        return null == softwareHouse_email ? "" : softwareHouse_email;
    }

    public void setSoftwareHouse_email(String softwareHouse_email) {
        this.softwareHouse_email = softwareHouse_email;
    }

    public String getSituacaoPJ_indSitPJ() {
        return null == situacaoPJ_indSitPJ ? "" : situacaoPJ_indSitPJ;
    }

    public void setSituacaoPJ_indSitPJ(String situacaoPJ_indSitPJ) {
        this.situacaoPJ_indSitPJ = situacaoPJ_indSitPJ;
    }

    public String getIdeContri_nrInsc() {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

}

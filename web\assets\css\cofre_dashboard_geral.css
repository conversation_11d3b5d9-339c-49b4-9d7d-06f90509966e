/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 05/11/2019, 09:05:47
    Author     : <PERSON>
*/

body {
    height: 100%;
    /* fallback */
    background-color: #1a82f7;
    background: linear-gradient(to bottom, #000, #131313);

    /* Safari 4-5, Chrome 1-9 */
    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#000), to(#131313));

    /* Safari 5.1, Chrome 10+ */
    background: -webkit-linear-gradient(top, #000, #131313);

    /* Firefox 3.6+ */
    background: -moz-linear-gradient(top, #000, #131313);

    /* IE 10 */
    background: -ms-linear-gradient(top, #000, #131313);

    /* Opera 11.10+ */
    background: -o-linear-gradient(top, #000, #131313);
}

#main{
    padding:0px 10px 0px 15px; 
    width:100% !important;
    height: calc(100vh - 60px) !important;
    overflow-y: auto !important;
}

[grid-style="dark"]{
    color:#FFF;
    border-spacing:0px !important;
    text-align: center;
}

[grid-style="dark"] thead tr th{
    height:40px !important;
}

[grid-style="dark"] thead tr th{
    background-color: #222;
    padding:0px 4px 0px 4px;
    text-align: center;
    font-size: 8pt;
}

[grid-style="dark"] thead{
    display: block;
    width: 100%;
}

[grid-style="dark"] tbody {
    width: 100%;
    display:block;
    height: 190px;
    overflow-y: auto;
    overflow-x: hidden;
}

[grid-style="dark"] thead tr th input{
    background-color: #151515;
    width: 100%;
    border: thin solid #000;
    border-radius: 4px;
    padding: 4px;
}

[grid-style="dark"] tbody tr td{
    background-color: #000;
    padding:5px 4px 5px 4px;
    vertical-align: middle;
    border-bottom: thin solid #222; 
    font-size:10pt !important;
}

#tblSaldosCofres tbody tr td:nth-child(2), 
#tblStatusCofres tbody tr td:nth-child(3),
#tblUltimasMovimentacoesCofre tbody tr td:nth-child(4),
#tblUltimasMovimentacoes tbody tr td:nth-child(3){
    width:100%;
    overflow:hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
}

#tblStatusCofres tbody tr td:nth-child(7),
#tblStatusCofres tbody tr td:nth-child(8){
    width:auto;
    white-space: nowrap !important;
}

#tblStatusCofres tbody tr td:nth-child(8){
    padding-right: 7px !important;
}

#tblSaldosCofres tbody tr td:nth-child(4),
#tblUltimasMovimentacoesCofre tbody tr td:nth-child(5),
#tblUltimasMovimentacoes tbody tr td:nth-child(5){
    min-width: 100px !important;
    width: 100px !important;
    max-width: 100px !important;
    text-align:center !important;
}

[grid-style="dark"] tbody tr:nth-child(even) td{
    background-color: #151515;
    color:#DDD;
}

header{
    background-color:#000;
}

#top{
    background-color: #000;
    height:100%;
}

#LinhaDivisao{
    height: 3px;
    background-color:#202020;
    margin-top:2px;
}

.panelPrincipal{
    padding:0px !important;
    min-height: 100% !important;
    background-color:transparent;
    border:none;
}

.graficoPequeno{
    float: left;
    height: 150px;
    width:100%;
    margin-top:15px;
    margin-bottom:15px;
}

@font-face {
    font-family: 'icomoon';
    src:  url('../fonts/icomoon.eot?jdrbxw');
    src:  url('../fonts/icomoon.eot?jdrbxw#iefix') format('embedded-opentype'),
        url('../fonts/icomoon.ttf?jdrbxw') format('truetype'),
        url('../fonts/icomoon.woff?jdrbxw') format('woff'),
        url('../fonts/icomoon.svg?jdrbxw#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-wifi_low:before {
    content: "\e900";
}
.icon-wifi_off:before {
    content: "\e901";
}
.icon-wifi:before {
    content: "\e902";
}

::-webkit-scrollbar {
    width: 8px !important;;
    height: 8px !important;;
    font-weight: 500 !important;
}

::-webkit-scrollbar-track-piece {
    background: #ccc;
    border-radius: 20px;
}

::-webkit-scrollbar-thumb:horizontal {
    width: 10%;
    background: #999;
    border-radius: 20px;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:vertical {
    height: 7px !important;
    background: #999;
    border-radius: 25px;
}
e
.DadosTab .ui-widget-header {
    background-color: #222 !important;
    color: #999;
    font-size: 10pt;
    font-weight: 500 !important;
    width: 100%;
    text-align: center;
    text-transform: uppercase;
}

.be .ui-dalastable th[role="columnheader"].ui-state-default, body .ui-treetable th[role="columnheader"].ui-state-default {
    border: 1px solid #070707 !important;
}

.DadosTab .ui-widget-content {
    background-color: #222 !important;
    color: #FFF;
    font-weight: 500 !important;
    width: 100%;
    text-align: center;
    margin: 0px !important;
    border: 1px solid #070707 !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-list-item, .ui-selectonemenu .ui-selectonemenu-label{
    text-align:center !important;
}

.seletor {
    padding-left: 10px !important;
    border:none !important;
    bottom:6px !important; 
    font-size:14pt; 
    text-align:center !important;
    color:#FFF !important;
    font-weight:600 !important;
    font-family: 'Trebuchet MS'
}

.ui-datatable-scrollable-theadclone {
    visibility: collapse;
}

body{
    min-height:100% !important;
    height:100% !important;
}

#TopoDash{
    height:60px;
    padding:0px !important;
    font-family: 'Trebuchet MS', sans-serif;
}

#TopoDash .Empresa{
    font-size:11pt;
    font-weight: bold;
    color:#FFF;
    text-transform: uppercase;
    display:block;
    line-height: 15px;
    margin-top: 6px;
    margin-left:8px !important;
}

#TopoDash .Filial{
    font-size:7pt;
    font-weight: bold;
    color:#FFF;
    text-transform: uppercase;
    display:block;
    line-height: 8px;
    margin-left:8px !important;
    color:#AAA !important;
}

#TopoDash .Voltar{
    background-color:orangered;
    color:#FFF;
    border-radius:12px;
    padding:0px 8px 0px 8px;
    border: 2px solid #ae2f00;
    font-size:9pt;
    cursor:pointer;
    position:absolute;
    left:6px;
    bottom:-23px;
    font-size:8pt !important;
    width:80px;
    text-align:center;
}

#TopoDash .ItemDash{
    padding:0px 1px 0px 1px !important;
    height:60px;
}

#TopoDash .ItemDash [ref="Titulo"]{
    font-size:8pt !important;
    font-weight: 500;
    color:#BBB;
    line-height:10px !important;
    text-transform: uppercase;
    width:100%;
    text-align:center;
    margin-top:12px;
}

#TopoDash .ItemDash [ref="Valor"]{
    font-size:14pt !important;
    font-weight: 600;
    color:#FFF;
    width:100%;
    text-align:center;
    margin-top:1px;
}

#TopoDash .ItemDashDados{
    background-image: linear-gradient(#000, #222);
    height:60px;
}

.FundoChart{
    padding: 5px 5px 20px 5px;
    margin-bottom: 10px;
    background-image: linear-gradient(rgba(0,0,0,0.1), #202020);
    height: 180px !important;
    overflow: hidden; 
    border:none !important;
}

.FundoTabelas{
    padding: 5px 5px 10px 5px;
    margin-bottom:20px !important;
    border-bottom: 1px dotted #BBB !important;
    background-image: linear-gradient(rgba(0,0,0,0.1), #202020);
    height: 340px !important; 
    overflow: hidden; 
}

.FundoTabelas table{
    width:100%;
}

.Tabelas{
    height: 290px !important;
    overflow-y: auto !important;
    border:none !important;
    padding: 5px 5px 0px 5px !important;
}

.DadosTab div {
    font-size: 8pt;
}

.DadosTab div::-webkit-scrollbar-thumb:vertical {
    background: #111 !important;                    
}

.DadosTab div:hover::-webkit-scrollbar-thumb:vertical {
    background: #ff6a00 !important;
    border: 2px solid #c35201;
}

::-webkit-scrollbar-track-piece {
    background: #222 !important;
}

.Titulo {
    font-size: 12pt !important;
    color: #EEE !important;
    font-weight: bold !important;
    line-height:18px !important;
}

.Titulo span{
    font-size: 9pt !important;
    color: #666 !important;
    font-weight: bold !important;
}

::-webkit-scrollbar {
    width: 7px;
    height: 8px;
    font-weight: 500 !important;
}

::-webkit-scrollbar-track-piece {
    background: #ccc;
    border-radius: 20px;
}

::-webkit-scrollbar-thumb:horizontal {
    width: 10%;
    background: #999;
    border-radius: 20px;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:vertical {
    height: 5px;
    background: #999;
    border-radius: 25px;
}

.BoxTabelasEsquerda{
    padding-left: 5px !important;
}

.BoxTabelasDireita{
    padding-right: 5px !important;
}

.BoxMedia{
    padding:0px 8px 0px 0px !important;
}

.BoxMedia div{
    background-color:#222 !important;
    border-radius:6px;
}

.BoxMedia [ref="Titulo"]{
    color:#999;
    font-size:10pt;
    font-weight:500 !important;
    width:100%;
    text-align:center;
    margin:8px 0px 0px 0px !important;
    text-transform: uppercase;
}

.BoxMedia [ref="Valor"]{
    color:#FFF;
    font-size:18pt;
    font-weight:500 !important;
    width:100%;
    text-align:center;
    margin:0px !important;
}

@media only screen and (max-width: 700px) and (min-width: 10px) {                    
    [id*="main"]{
        height:2120px !important;
    }

    .BoxMedia div{
        margin-bottom:8px !important;
    }

    .BoxMedia [ref="Titulo"]{
        margin:15px 0px 0px 0px !important;
        font-size:8pt !important;
    }

    .BoxMedia [ref="Valor"]{
        font-size:12pt !important;
    }

    #LinhaDivisao{
        position:absolute;
        top:36px;
        width:100% !important;
    }

    #TopoDash .Voltar{
        left:auto;
        right:2px !important;
        top:2px !important;
        width:70px;
    }
}
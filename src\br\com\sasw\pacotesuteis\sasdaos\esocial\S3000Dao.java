/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S3000;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S3000Dao {

    public List<S3000> get(String codFil, String compet, String ambiente, String evento, Persistencia persistencia) throws Exception {
        try {
            List<S3000> retorno = new ArrayList<>();
            String sql, e = evento.split(" - ")[0];

            if (e.equals("S-1200")
                    || e.equals("S-1210")
                    || e.equals("S-1300")
                    || e.equals("S-2190")) {
                sql = "Select  Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc, XMLeSocial.Evento InfoExclusao_tpEvento, Convert(BigInt, XMLeSocial.Identificador) Identificador, "
                        + " Substring(XML_Retorno,CHARINDEX('<nrRecibo>',XML_Retorno)+10,(CHARINDEX('</nrRecibo>',XML_Retorno)-(CHARINDEX('<nrRecibo>',XML_Retorno)+10))) InfoExclusao_nrRecEvt, "
                        + " Case when evento in ('S-1200','S-1210') then\n"
                        + "	Substring(XML_Envio,CHARINDEX('<perApur>',XML_Envio)+09,(CHARINDEX('</perApur>',XML_Envio)-(CHARINDEX('<perApur>',XML_Envio)+09))) \n"
                        + "	else '' end ideFolhaPagto_perApur,"
                        + " Funcion.CPF ideTrabalhador_cpfTrab, Funcion.PIS ideTrabalhador_nisTrab, "
                        + " (select max(sucesso) from  ( "
                        + "   (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                        + "       From XmleSocial z  (nolock) "
                        + "       where z.evento = 'S-3000' "
                        + "           and z.Compet = ? "
                        + "           and z.Ambiente = ? "
                        + "           and z.CodFil = ? "
                        + " 	        and z.Identificador = XMLeSocial.Identificador"
                        + "           and (z.Xml_Retorno like '%aguardando%' "
                        + "                   or z.Xml_Retorno = ''"
                        + "                   or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                        + " union "
                        + "   (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                        + "       From XmleSocial z (nolock) "
                        + "       where z.evento = 'S-3000' "
                        + "           and z.Compet = ? "
                        + "           and z.Ambiente = ? "
                        + "           and z.CodFil = ? "
                        + " 	     and z.Identificador = XMLeSocial.Identificador"
                        + " 	     and (z.Xml_Retorno like '%<ocorrencia>%' "
                        + "             or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                        + " union "
                        + "   (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                        + "       From XmleSocial z (nolock) "
                        + "       where z.evento = 'S-3000' "
                        + "           and z.Compet = ? "
                        + "           and z.Ambiente = ? "
                        + "           and z.CodFil = ? "
                        + " 	      and z.Identificador = XMLeSocial.Identificador"
                        + " 	      and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or    z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%')  )) a) sucesso   "
                        + "  From Filiais (nolock) "
                        + "  Left join XMLeSocial (nolock) on XMLeSocial.CodFil = Filiais.CodFil"
                        + "  Left join Funcion (nolock) on Convert(BigInt,Funcion.CPF) = XMLeSocial.Identificador                   "
                        + " Where XMLeSocial.Compet = ? "
                        + "   and Filiais.CodFil = ? "
                        + "   and XMLeSocial.Ambiente = ? "
                        + "   and XMLeSocial.Protocolo_Envio in (Select Protocolo_Envio from XMLeSocial (nolock) where Compet = ? and codfil = ? and Evento = ? "
                        + "   and (Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )"
                        + "   and XMLeSocial.Evento = ? "
                        + "   and XmlEsocial.Xml_Retorno like '%<nrRecibo>%'";
                        //+ " ORDER BY InfoExclusao_tpEvento, Identificador ";
            } else {
                sql = "Select  Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc, XMLeSocial.Evento InfoExclusao_tpEvento, Convert(BigInt, XMLeSocial.Identificador) Identificador, "
                        + " Substring(XML_Retorno,CHARINDEX('<nrRecibo>',XML_Retorno)+10,(CHARINDEX('</nrRecibo>',XML_Retorno)-(CHARINDEX('<nrRecibo>',XML_Retorno)+10))) InfoExclusao_nrRecEvt,"
                        + " Case when evento in ('S-1200','S-1210') then\n"
                        + "	Substring(XML_Envio,CHARINDEX('<perApur>',XML_Envio)+09,(CHARINDEX('</perApur>',XML_Envio)-(CHARINDEX('<perApur>',XML_Envio)+09))) \n"
                        + "	else '' end ideFolhaPagto_perApur,"
                        + " Funcion.CPF ideTrabalhador_cpfTrab, Funcion.PIS ideTrabalhador_nisTrab, "
                        + " (select max(sucesso) from  ( "
                        + "   (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                        + "       From XmleSocial z  (nolock) "
                        + "       where z.evento = 'S-3000' "
                        + "           and z.Compet = ? "
                        + "           and z.Ambiente = ? "
                        + "           and z.CodFil = ? "
                        + " 	     and z.Identificador = XMLeSocial.Identificador"
                        + "           and (z.Xml_Retorno like '%aguardando%' "
                        + "                   or z.Xml_Retorno = ''"
                        + "                   or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                        + " union "
                        + "   (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                        + "       From XmleSocial z (nolock) "
                        + "       where z.evento = 'S-3000' "
                        + "           and z.Compet = ? "
                        + "           and z.Ambiente = ? "
                        + "           and z.CodFil = ? "
                        + " 	     and z.Identificador = XMLeSocial.Identificador"
                        + " 	     and (z.Xml_Retorno like '%<ocorrencia>%' "
                        + "             or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                        + " union "
                        + "   (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                        + "       From XmleSocial z (nolock) "
                        + "       where z.evento = 'S-3000' "
                        + "           and z.Compet = ? "
                        + "           and z.Ambiente = ? "
                        + "           and z.CodFil = ? "
                        + " 	      and z.Identificador = XMLeSocial.Identificador"
                        + " 	      and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%'))) a) sucesso                "
                        + "  From Filiais (nolock) "
                        + "  Left join XMLeSocial (nolock) on XMLeSocial.CodFil = Filiais.CodFil";
                if (e.equals("S-2399")) {
                    sql = sql + "  Left join Funcion (nolock) on Convert(BigInt,Funcion.CPF) = XMLeSocial.Identificador     ";
                } else {
                    sql = sql + "  Left join Funcion (nolock) on Funcion.Matr = XMLeSocial.Identificador     ";
                }
                sql = sql + " Where XMLeSocial.Compet = ? "
                        + "   and Filiais.CodFil = ? "
                        + "   and XMLeSocial.Ambiente = ? "
                        + "   and XMLeSocial.Protocolo_Envio in (Select Protocolo_Envio from XMLeSocial (nolock) where Compet = ? and CodFil = ? and Evento = ? "
                        + "   and (Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'   or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') ) "
                        + "   and XMLeSocial.Evento = ? "
                        + "   and XmlEsocial.Xml_Retorno like '%<nrRecibo>%' ";
                        // + " ORDER BY Identificador ";
                        //+ " ORDER BY InfoExclusao_tpEvento, Identificador ";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.setString(e);
            consulta.setString(e);

            consulta.select();
            S3000 s3000;
            while (consulta.Proximo()) {
                s3000 = new S3000();
                s3000.setSucesso(consulta.getInt("sucesso"));
                s3000.setIdeEvento_procEmi("1");
                s3000.setIdeEvento_verProc("Satellite eSocial");
                s3000.setIdeEvento_identificador(consulta.getString("Identificador"));
                s3000.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s3000.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));

                s3000.setInfoExclusao_tpEvento(consulta.getString("InfoExclusao_tpEvento"));
                s3000.setInfoExclusao_nrRecEvt(consulta.getString("InfoExclusao_nrRecEvt"));

                s3000.setIdeTrabalhador_cpfTrab(consulta.getString("ideTrabalhador_cpfTrab"));
                s3000.setIdeTrabalhador_nisTrab(consulta.getString("ideTrabalhador_nisTrab"));

                s3000.setIdeFolhaPagto_perApur(consulta.getString("ideFolhaPagto_perApur"));
                if (compet.contains("-13") && consulta.getString("InfoExclusao_tpEvento").equals("S-1200")) {
                    s3000.setIdeFolhaPagto_indApuracao("2");
                } else {
                    s3000.setIdeFolhaPagto_indApuracao("1");
                }

                s3000.setIdeEvento_tpAmb(ambiente);
                s3000.setId(s3000.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001");
                s3000.setIdeEmpregador_tpInsc(s3000.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s3000.setIdeEmpregador_nrInsc(s3000.getIdeEmpregador_nrInsc().substring(0, 8));

                retorno.add(s3000);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S3000Dao.get - " + e.getMessage() + "\r\n"
                    + "Select Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc, XMLeSocial.Evento InfoExclusao_tpEvento, Convert(BigInt, XMLeSocial.Identificador) Identificador, "
                    + " Substring(XML_Retorno,CHARINDEX('<nrRecibo>',XML_Retorno)+10,(CHARINDEX('</nrRecibo>',XML_Retorno)-(CHARINDEX('<nrRecibo>',XML_Retorno)+10))) InfoExclusao_nrRecEvt,"
                    + " Funcion.CPF ideTrabalhador_cpfTrab, Funcion.PIS ideTrabalhador_nisTrab, "
                    + " (select max(sucesso) from  ( "
                    + "   (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "       From XmleSocial z  "
                    + "       where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "           and z.evento = 'S-3000' "
                    + "           and z.Compet = ? "
                    + "           and z.Ambiente = ?"
                    + "           and (z.Xml_Retorno like '%aguardando%' "
                    + "                   or z.Xml_Retorno = ''"
                    + "                   or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union "
                    + "   (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "       From XmleSocial z  "
                    + "       where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "           and z.evento = 'S-3000' "
                    + "           and z.Compet = ? "
                    + "           and z.Ambiente = ? "
                    + "           and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                   or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "   (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "       From XmleSocial z  "
                    + "       where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "           and z.evento = 'S-3000' "
                    + "           and z.Compet = ? "
                    + "           and z.Ambiente = ? "
                    + "           and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'  or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%'))) a) sucesso                "
                    + "  From Filiais"
                    + "  Left join XMLeSocial on XMLeSocial.CodFil = Filiais.CodFil"
                    + "  Left join Funcion  on Funcion.Matr = XMLeSocial.Identificador "
                    + " Where Filiais.CodFil = ? "
                    + "   and XMLeSocial.Compet = ? "
                    + "   and XMLeSocial.Protocolo_Envio in (Select Protocolo_Envio from XMLeSocial where Compet = ? "
                    + "   and (Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%')) "
                    + " ORDER BY sucesso asc, substring(Filiais.CNPJ,1,8) asc ");
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class Rondas {

    private String Sequencia;
    private String Secao;
    private String CodFil;
    private String CodDepen;
    private String Data;
    private String Matr;
    private int Hora;
    private String Latitude;
    private String Longitude;
    private String Operador;
    private String Dt_alter;
    private String Hr_alter;

    private String Descricao;
    private String local;
    private int horaPrevista;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodDepen() {
        return CodDepen;
    }

    public void setCodDepen(String CodDepen) {
        this.CodDepen = CodDepen;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public int getHora() {
        return Hora;
    }

    public void setHora(int Hora) {
        this.Hora = Hora;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public int getHoraPrevista() {
        return horaPrevista;
    }

    public void setHoraPrevista(int horaPrevista) {
        this.horaPrevista = horaPrevista;
    }

    public String getHoraIni() {
        return Hr_alter;
    }

    public void setHoraIni(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getHoraFim() {
        return Hr_alter;
    }

    public void setHoraFim(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }
}

package SasBeansCompostas;

import SasBeans.ATMInter;
import SasBeans.Pedido;
import SasBeans.Rotas;

/**
 *
 * <AUTHOR>
 */
public class RotasATMInterPedido {

    private Pedido pedido;
    private ATMInter atminter;
    private Rotas rotas;

    public RotasATMInterPedido() {
        this.pedido = new Pedido();
        this.atminter = new ATMInter();
        this.rotas = new Rotas();
    }

    public Pedido getPedido() {
        return pedido;
    }

    public void setPedido(Pedido pedido) {
        this.pedido = pedido;
    }

    public ATMInter getAtminter() {
        return atminter;
    }

    public void setAtminter(ATMInter atminter) {
        this.atminter = atminter;
    }

    public Rotas getRotas() {
        return rotas;
    }

    public void setRotas(Rotas rotas) {
        this.rotas = rotas;
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/cadastros/veiculos.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <style>

                [id*="formVeiculo"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                label{
                    white-space: nowrap;
                }

                [id*="cadastrar"] div[class*="col-md"]{
                    padding: 5px !important;
                }

                #formVeiculo .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formVeiculo .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }

                [id*="formVeiculo"] label{
                    margin-bottom: 0px !important;
                }
                
                .ui-panel.ui-widget .ui-panel-titlebar.ui-corner-all{
                    background-color: #FFF !important;
                    border-bottom: thin solid #CCC !important
                }
                
                
                .veiculoCardInner{
                    position: relative;
                }
                
                .ui-panel.veiculoCardInner{
                    padding-bottom: 6px !important;
                }
                
                .veiculoCardInner .ui-panel-title{
                    padding-left: 130px !important;
                }
                
                .lblNred{
                    position: absolute;
                    margin-top: -48px;
                    margin-left: 10px;
                    padding: 1px 10px 2px 10px;
                    background-color: orangered;
                    color: #FFF;
                    font-weight: 600 !important;
                    border-radius: 30px;
                    font-size: 9pt !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{veiculos.Persistencia(login.pp)}" />
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_satmob_veiculos.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Veiculos}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="20200330" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12">
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{veiculos.filiais.descricao}
                                            <label id="btTrocarFilial"
                                                   onclick="top.location.href = '../param.xhtml'"
                                                   >
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco">
                                            #{veiculos.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{veiculos.filiais.bairro}, #{veiculos.filiais.cidade}/#{veiculos.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3">
                                    <p:commandLink title="#{localemsgs.Voltar}" onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; overflow-y:auto !important;
                         padding-right:12px !important; border-top-color:#d2d6de !important">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel id="cadastrar" style="display: inline;">
                                    <p:dataGrid id="tabela"
                                                value="#{veiculos.allVeiculos}" paginator="true" rows="50" lazy="true"
                                                rowsPerPageTemplate="5,10,15,20,25,50"
                                                currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Veiculos}"
                                                paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport}
                                                {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                var="veiculo" columns="1"
                                                emptyMessage="#{localemsgs.SemRegistros}">
                                        <div class="veiculoCard">

                                            <p:panel header="#{veiculo.numeroFormatado}" class="veiculoCardInner">
                                                <label class="lblNred">
                                                    #{localemsgs.NumeroVeiculo}
                                                </label>
                                                
                                                <p:panelGrid columns="4" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                             layout="grid" styleClass="ui-panelgrid-blank" style="margin-top: 8px !important">
                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.CodFil}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.codFil}">
                                                                <f:convertNumber pattern="0000"/>
                                                            </h:outputText>
                                                        </div>
                                                    </p:column>

                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.Placa}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.placa}"></h:outputText>
                                                        </div>
                                                    </p:column>
                                                    

                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.Modelo}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.descricaoModelo}"></h:outputText>
                                                        </div>
                                                    </p:column>
                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.Ano}/#{localemsgs.AnoModelo}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.ano}/#{veiculo.anoModelo}"></h:outputText>
                                                        </div>
                                                    </p:column>
                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.Tipo}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.tipo}" converter="conversorTipoVeiculo"/>
                                                        </div>
                                                    </p:column>
                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.dt_Ipva}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.dt_Ipva}" converter="conversorData"/>
                                                        </div>
                                                    </p:column>
                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.RENAVAN}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.RENAVAN}"/>
                                                        </div>
                                                    </p:column>
                                                    <p:column>
                                                        <div class="gridTitulo">
                                                            <h:outputText value="#{localemsgs.Chassis}: " />
                                                        </div>
                                                        <div class="gridValor">
                                                            <h:outputText value="#{veiculo.chassis}"/>
                                                        </div>
                                                    </p:column>
                                                </p:panelGrid>
                                                <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                             layout="grid" styleClass="ui-panelgrid-blank">
                                                    <p:column style="padding:0px !important; margin:0px !important;">
                                                        <div class="BotoesGrid" style="width:100px; text-align: right; padding:0px !important;">
                                                            <p:commandLink title="#{localemsgs.Editar}" update="msgs formVeiculo:cadastrar cabecalho"
                                                                           actionListener="#{veiculos.editarVeiculo(veiculo)}">
                                                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                                            </p:commandLink>
                                                        </div>
                                                    </p:column>
                                                </p:panelGrid>

                                            </p:panel>

                                            <script>
                                                // <![CDATA[
                                                $(document).ready(function () {
                                                    if ($(document).width() <= 700)
                                                        $('.FundoPagina').css('max-height', ($('body').height() - 200) + 'px');
                                                    else
                                                        $('.FundoPagina').css('max-height', ($('body').height() - 121) + 'px');
                                                });

                                                $(window).resize(function () {
                                                    if ($(document).width() <= 700)
                                                        $('.FundoPagina').css('max-height', ($('body').height() - 200) + 'px');
                                                    else
                                                        $('.FundoPagina').css('max-height', ($('body').height() - 121) + 'px');
                                                });
                                                // ]]>
                                            </script>
                                        </div>
                                    </p:dataGrid>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 0px; bottom: 180px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}"
                                           update="formVeiculo:dlgCadastrar cabecalho" actionListener="#{veiculos.novoVeiculo()}">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <h:form id="formVeiculo">
                    <p:dialog widgetVar="dlgCadastrar" id="dlgCadastrar"
                              positionType="absolute"
                              draggable="false" modal="true" closable="true"
                              resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              styleClass="dlgCadastrar">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formVeiculo\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                        </p:confirmDialog>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_veiculos.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText
                                value="#{veiculos.flag eq 1 ? localemsgs.CadastrarVeiculo : localemsgs.EditarVeiculo}"
                                style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="cadastrar" styleClass="cadastrar" style="background-color:#EEE; padding:0px !important; overflow-x: hidden;">

                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">
                                <div class="col-md-4 col-sm-4 col-xs-12">
                                    <label>
                                        <p:outputLabel for="filial" value="#{localemsgs.Filial}:"/>
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>
                                    <p:selectOneMenu id="filial" value="#{veiculos.filial}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%" disabled="#{veiculos.flag eq 2}">
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" update="motorista matr_Mot" listener="#{veiculos.selecionarFilial}"/>
                                    </p:selectOneMenu>   
                                </div>

                                <div class="col-md-2 col-sm-2 col-xs-6">
                                    <label>
                                        <p:outputLabel for="numero" value="#{localemsgs.Numero}:" />
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>
                                    <p:inputNumber id="numero" value="#{veiculos.veiculo.numero}" decimalPlaces="0"
                                                   label="#{localemsgs.Numero}" inputStyle="width: 100%; font-weight: bold; color: blue"
                                                   required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Numero}"
                                                   style="width: 100%" disabled="#{veiculos.flag eq 2}">
                                    </p:inputNumber>   
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-6">
                                    <label>
                                        <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:" />
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>
                                    <p:selectOneMenu id="situacao" value="#{veiculos.veiculo.situacao}" style="width: 100%" required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                        <f:selectItem itemLabel="#{localemsgs.Manutencao}" itemValue="M"/>
                                        <f:selectItem itemLabel="#{localemsgs.Desativado}" itemValue="D"/>
                                    </p:selectOneMenu>
                                </div>

                                <div class="col-md-1 col-sm-1 col-xs-3">
                                    <label>
                                        <p:outputLabel for="CCusto" value="#{localemsgs.CCusto}:" />
                                    </label>
                                    <p:inputText id="CCusto" value="#{veiculos.ccusto.CCusto}" style="width: 100%" disabled="true">
                                    </p:inputText>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-6">
                                    <label>
                                        &nbsp;
                                        &nbsp;
                                    </label>
                                    <p:selectOneMenu id="CCustoDescricao" value="#{veiculos.ccusto}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains" label="#{localemsgs.CCusto}"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="" noSelectionOption="true" />
                                        <f:selectItems value="#{veiculos.ccustos}" var="ccusto" itemValue="#{ccusto}"
                                                       itemLabel="#{ccusto.descricao.toUpperCase()}"/>
                                        <p:ajax event="itemSelect" update="msgs formVeiculo:CCusto formVeiculo:CCustoNRed"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-1 col-sm-1 col-xs-3">
                                    <label>
                                        &nbsp;
                                        &nbsp;
                                    </label>
                                    <p:inputText id="CCustoNRed" value="#{veiculos.ccusto.NRed}" style="width: 100%" disabled="true">
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="col-md-12 row" style="padding:0px !important; margin:8px 0px 0px 0px !important">
                                <div class="col-md-2 col-sm-6 col-xs-12">
                                    <label>
                                        <p:outputLabel for="placa" value="#{localemsgs.Placa}:" /> 
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>
                                    <p:inputText value="#{veiculos.veiculo.placa}" maxlength="7"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Placa}"
                                                 id="placa" style="width: 100%" required="true">
                                        <p:watermark for="placa" value="#{localemsgs.Placa}"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-1 col-sm-3 col-xs-6">
                                    <label>
                                        <p:outputLabel for="ano" value="#{localemsgs.Ano}" /> 
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>    
                                    <p:inputText value="#{veiculos.veiculo.ano}" maxlength="4"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Ano}"
                                                 id="ano" style="width: 100%" required="true">
                                        <p:watermark for="ano" value="#{localemsgs.AnoModelo}"/>
                                    </p:inputText>

                                </div>
                                <div class="col-md-1 col-sm-3 col-xs-6">
                                    <label><p:outputLabel for="anoModelo" value="#{localemsgs.AnoModelo}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                    <p:inputText value="#{veiculos.veiculo.anoModelo}" maxlength="4"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.AnoModelo}"
                                                 id="anoModelo" style="width: 100%" required="true">
                                        <p:watermark for="anoModelo" value="#{localemsgs.AnoModelo}"/>
                                    </p:inputText>
                                </div>

                                <div class="col-md-3 col-sm-4 col-xs-9">
                                    <label>
                                        <p:outputLabel for="mun_Placa" value="#{localemsgs.Municipio}"/> 
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>
                                    <p:selectOneMenu id="mun_Placa" value="#{veiculos.municipio}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains" 
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Municipio}"
                                                     required="true" label="#{localemsgs.Modelo}"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="" />
                                        <f:selectItems value="#{veiculos.municipios}" var="municipio" itemValue="#{municipio}"
                                                       itemLabel="#{municipio.nome.toUpperCase()}"/>
                                        <p:ajax event="itemSelect" listener="#{veiculos.selecionarMunicipio}" 
                                                update="msgs formVeiculo:mun_Placa formVeiculo:UF_Placa"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-1 col-sm-2 col-xs-3">
                                    <label>
                                        <p:outputLabel for="UF_Placa" value="#{localemsgs.UF}"/> 
                                    </label>
                                    <p:inputText id="UF_Placa" style="width: 100%" disabled="true" label="#{localemsgs.Municipio}"
                                                 maxlength="2" value="#{veiculos.veiculo.UF_Placa}">
                                    </p:inputText>
                                </div>

                                <div class="col-md-1 col-sm-2 col-xs-3">
                                    <label>
                                        <p:outputLabel for="modelo" value="#{localemsgs.Modelo}"/> 
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>
                                    <p:inputText id="modelo" value="#{veiculos.veiculo.modelo}"
                                                 label="#{localemsgs.Modelo}" disabled="true"
                                                 style="width: 100%">
                                    </p:inputText>   
                                </div>
                                <div class="col-md-3 col-sm-4 col-xs-9">
                                    <label>
                                        &nbsp;
                                        &nbsp;
                                    </label>
                                    <p:selectOneMenu value="#{veiculos.veiculo.modelo}" style="width: 100%" required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Modelo}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItems value="#{veiculos.modelos}" var="modelo" itemValue="#{modelo.codigo}"
                                                       itemLabel="#{modelo.descricao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" update="modelo"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">
                                <div class="col-md-3 col-sm-4 col-xs-12">
                                    <p:outputLabel for="carroceria" value="#{localemsgs.Carroceria}:"/>
                                    <p:inputText id="carroceria" value="#{veiculos.veiculo.carroceria}"
                                                 label="#{localemsgs.Modelo}" maxlength="30"
                                                 style="width: 100%">
                                    </p:inputText>   
                                </div>

                                <div class="col-md-1 col-sm-2 col-xs-3">
                                    <label>
                                        <p:outputLabel for="tipo" value="#{localemsgs.Tipo}:"/>
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>
                                    <p:inputText id="tipo" value="#{veiculos.veiculo.tipo}"
                                                 label="#{localemsgs.Tipo}" disabled="true"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                 style="width: 100%">
                                    </p:inputText>   
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-9">
                                    <label>
                                        &nbsp;
                                    </label>
                                    <p:selectOneMenu value="#{veiculos.veiculo.tipo}" style="width: 100%" required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.CarroForte}" itemValue="F"/>
                                        <f:selectItem itemLabel="#{localemsgs.CarroLeve}" itemValue="L"/>
                                        <f:selectItem itemLabel="#{localemsgs.Moto}" itemValue="M"/>
                                        <f:selectItem itemLabel="#{localemsgs.Pesado}" itemValue="P"/>
                                        <f:selectItem itemLabel="#{localemsgs.Blindado}" itemValue="B"/>
                                        <f:selectItem itemLabel="#{localemsgs.Particular}" itemValue="R"/>
                                        <p:ajax event="itemSelect" update="tipo panelForte" oncomplete="PF('dlgCadastrar').initPosition()"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-12">
                                    <label>
                                        &nbsp;
                                    </label>
                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding:7px 5px 3px 5px !important; 
                                         background-color:#FFF; border: thin solid #DDD; border-radius: 3px; text-align: center !important">
                                        <p:selectBooleanCheckbox id="matrAut" style="float: right; margin-right: 6px !important;" 
                                                                 value="#{veiculos.veicTerceiros}"/>
                                        <p:outputLabel for="matrAut" value="#{localemsgs.VeicTerceiros}" style="font-size:8pt !important" />
                                    </div>
                                </div>

                                <div class="col-md-1 col-sm-4 col-xs-3">
                                    <label>
                                        <p:outputLabel for="combust" value="#{localemsgs.Combustivel}:"/>
                                        <font style="font-weight: bold; color: red">(*)</font>
                                    </label>
                                    <p:inputText  id="combust" value="#{veiculos.veiculo.combust}"
                                                  label="#{localemsgs.Combustivel}" disabled="true"
                                                  style="width: 100%">
                                    </p:inputText>   
                                </div>
                                <div class="col-md-3 col-sm-4 col-xs-9">
                                    <label>
                                        &nbsp;
                                        &nbsp;
                                    </label>
                                    <p:selectOneMenu value="#{veiculos.veiculo.combust}" style="width: 100%" required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Combustivel}">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Gasolina}" itemValue="G"/>
                                        <f:selectItem itemLabel="#{localemsgs.Alcool}" itemValue="A"/>
                                        <f:selectItem itemLabel="#{localemsgs.Flex}" itemValue="F"/>
                                        <f:selectItem itemLabel="#{localemsgs.Triflex}" itemValue="T"/>
                                        <f:selectItem itemLabel="#{localemsgs.GasNatural}" itemValue="N"/>
                                        <f:selectItem itemLabel="#{localemsgs.Diesel}" itemValue="D"/>
                                        <f:selectItem itemLabel="#{localemsgs.Querosene}" itemValue="Q"/>
                                        <f:selectItem itemLabel="#{localemsgs.OutrosDesc}" itemValue="O"/>
                                        <p:ajax event="itemSelect" update="combust"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">
                                <div class="col-md-4 col-sm-4 col-xs-6">
                                    <label>
                                        <p:outputLabel for="chassis" value="#{localemsgs.Chassis}:"/>
                                    </label>
                                    <p:inputText  id="chassis" value="#{veiculos.veiculo.chassis}"
                                                  label="#{localemsgs.Chassis}" maxlength="20"
                                                  style="width: 100%">
                                    </p:inputText>   
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-6">
                                    <label>
                                        <p:outputLabel for="dt_Compra" value="#{localemsgs.dt_Compra}:"/>
                                    </label>
                                    <p:datePicker id="dt_Compra" value="#{veiculos.veiculo.dt_Compra}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>

                                <div class="col-md-4 col-sm-4 col-xs-6">
                                    <label>
                                        <p:outputLabel for="RENAVAN" value="#{localemsgs.RENAVAN}:"/>
                                    </label>
                                    <p:inputText id="RENAVAN" value="#{veiculos.veiculo.RENAVAN}"
                                                 label="#{localemsgs.RENAVAN}" maxlength="20"
                                                 style="width: 100%">
                                    </p:inputText>   
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-6">
                                    <label>
                                        <p:outputLabel for="dt_Ipva" value="#{localemsgs.dt_Ipva}:"/>
                                    </label>
                                    <p:datePicker id="dt_Ipva" value="#{veiculos.veiculo.dt_Ipva}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                            </div>

                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <label>
                                        <p:outputLabel for="obs" value="#{localemsgs.Obs}:"/>
                                    </label>
                                    <p:inputText id="obs" value="#{veiculos.veiculo.obs}"
                                                 label="#{localemsgs.Obs}"
                                                 style="width: 100%">
                                    </p:inputText>   
                                </div>
                            </div>

                            <div class="col-md-12 row" style="border: 1px solid #cccccc; padding:0px !important; margin: 10px 0px 0px 0px !important">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important;">
                                    <h:outputText value="#{localemsgs.Seguro}"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <label>
                                        <p:outputLabel for="seguradora" value="#{localemsgs.Seguradora}:"/>
                                    </label>
                                    <p:inputText id="seguradora" value="#{veiculos.veiculo.seguradora}"
                                                 label="#{localemsgs.Seguradora}" maxlength="40"
                                                 style="width: 100%"/>
                                </div>

                                <div class="col-md-2 col-sm-2 col-xs-4">
                                    <label>
                                        <p:outputLabel for="apolice" value="#{localemsgs.Apolice}:"/>
                                    </label>
                                    <p:inputText id="apolice" value="#{veiculos.veiculo.apolice}"
                                                 label="#{localemsgs.Apolice}" maxlength="20"
                                                 style="width: 100%"/>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-4">
                                    <label>
                                        <p:outputLabel for="dt_SegIni" value="#{localemsgs.Vigencia}:"/>
                                    </label>
                                    <p:datePicker id="dt_SegIni" value="#{veiculos.veiculo.dt_SegIni}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                                <div class="col-md-2 col-sm-2 col-xs-4">
                                    <label>
                                        &nbsp;
                                        &nbsp;
                                    </label>
                                    <p:datePicker id="dt_VencSeg" value="#{veiculos.veiculo.dt_VencSeg}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                            </div>

                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">

                                <div class="col-md-1 col-sm-2 col-xs-3">
                                    <label>
                                        <p:outputLabel for="matr_Mot" value="#{localemsgs.Motorista}"/>
                                    </label>
                                    <p:inputText id="matr_Mot" value="#{veiculos.veiculo.matr_Mot}"
                                                 label="#{localemsgs.Motorista}" disabled="true"
                                                 style="width: 100%" converter="conversor0"/>
                                </div>

                                <div class="col-md-3 col-sm-4 col-xs-9">
                                    <label>
                                        &nbsp;
                                        &nbsp;
                                    </label>
                                    <p:autoComplete id="motorista" value="#{veiculos.motorista}" styleClass="cliente2" minQueryLength="3"
                                                    style="width: 100%" completeMethod="#{veiculos.buscarPessoas}" inputStyle="width: 100%"
                                                    var="motoristaSelecao" itemLabel="#{motoristaSelecao.nome}" itemValue="#{motoristaSelecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{veiculos.listaPessoa}"/>
                                        <p:ajax event="itemSelect" update="matr_Mot motorista msgs" listener="#{veiculos.selecionarMotorista}"/>
                                        <p:watermark for="motorista" value="#{localemsgs.Motorista}" />
                                    </p:autoComplete>
                                </div>

                                <div class="col-md-2 col-sm-3 col-xs-3">
                                    <label>
                                        <p:outputLabel for="PRXRadio" value="#{localemsgs.PRXRadio}"/>
                                    </label>
                                    <p:inputText id="PRXRadio"
                                                 label="#{localemsgs.PRXRadio}"
                                                 style="width: 100%"/>
                                </div>

                                <div class="col-md-2 col-sm-3 col-xs-3">
                                    <label>
                                        <p:outputLabel for="ESN" value="#{localemsgs.ESN}"/>
                                    </label>
                                    <p:inputText id="ESN"
                                                 label="#{localemsgs.ESN}"
                                                 style="width: 100%"/>
                                </div>
                            </div>

                            <p:panel  id="panelForte">
                                <ui:fragment rendered="#{veiculos.veiculo.tipo eq 'F' or veiculos.veiculo.tipo eq 'B'}">
                                    <div class="col-md-6 row" style="border: 1px solid #cccccc; padding:0px !important; margin: 10px 0px 0px 0px !important">
                                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important;">
                                            <h:outputText value="#{localemsgs.Blindagens}"/>
                                        </div>

                                        <div class="col-md-2 col-sm-3 col-xs-4">
                                            <label>
                                                <p:outputLabel for="blindCab" value="#{localemsgs.Cabine}:"/>
                                            </label>
                                            <p:inputText id="blindCab" value="#{veiculos.veiculo.blindCab}"
                                                         label="#{localemsgs.Cabine}" maxlength="1"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-3 col-xs-4">
                                            <label>
                                                <p:outputLabel for="blindTeto" value="#{localemsgs.Teto}:"/>
                                            </label>
                                            <p:inputText id="blindTeto" value="#{veiculos.veiculo.blindTeto}"
                                                         label="#{localemsgs.Teto}" maxlength="1"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-3 col-xs-4">
                                            <label>
                                                <p:outputLabel for="blindAssoa" value="#{localemsgs.Assoalho}:"/>
                                            </label>
                                            <p:inputText id="blindAssoa" value="#{veiculos.veiculo.blindAssoa}"
                                                         label="#{localemsgs.Assoalho}" maxlength="1"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-3 col-xs-4">
                                            <label>
                                                <p:outputLabel for="blindCofre" value="#{localemsgs.Cofre}:"/>
                                            </label>
                                            <p:inputText id="blindCofre" value="#{veiculos.veiculo.blindCofre}"
                                                         label="#{localemsgs.Cofre}" maxlength="1"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-3 col-xs-4">
                                            <label>
                                                <p:outputLabel for="blindVidro" value="#{localemsgs.Vidro}:"/>
                                            </label>
                                            <p:inputText id="blindVidro" value="#{veiculos.veiculo.blindVidro}"
                                                         label="#{localemsgs.Vidro}" maxlength="1"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-3 col-xs-4">
                                            <label>
                                                &nbsp;
                                                &nbsp;
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 row" style="border: 1px solid #cccccc; padding:0px !important; margin: 10px 0px 0px 0px !important">
                                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important;">
                                            <h:outputText value="#{localemsgs.Habilitacoes}"/>
                                        </div>

                                        <div class="col-md-4 col-sm-4 col-xs-4">
                                            <label>
                                                &nbsp;
                                            </label>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding:7px 5px 3px 5px !important; 
                                                 background-color:#FFF; border: thin solid #DDD; border-radius: 3px; text-align: center !important">
                                                <p:selectBooleanCheckbox id="viagem" style="float: right; margin-right: 6px !important;" 
                                                                         value="#{veiculos.viagem}"/>
                                                <p:outputLabel for="viagem" value="#{localemsgs.Viagem}" style="font-size:8pt !important" />
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-4">
                                            <label>
                                                &nbsp;
                                            </label>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding:7px 5px 3px 5px !important; 
                                                 background-color:#FFF; border: thin solid #DDD; border-radius: 3px; text-align: center !important">
                                                <p:selectBooleanCheckbox id="bacen" style="float: right; margin-right: 6px !important;" 
                                                                         value="#{veiculos.bacen}"/>
                                                <p:outputLabel for="bacen" value="#{localemsgs.Bacen}" style="font-size:8pt !important" />
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-4">
                                            <label>
                                                &nbsp;
                                            </label>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding:7px 5px 3px 5px !important; 
                                                 background-color:#FFF; border: thin solid #DDD; border-radius: 3px; text-align: center !important">
                                                <p:selectBooleanCheckbox id="aeroporto" style="float: right; margin-right: 6px !important;" 
                                                                         value="#{veiculos.aeroporto}"/>
                                                <p:outputLabel for="aeroporto" value="#{localemsgs.Aeroporto}" style="font-size:8pt !important" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-12 row" style="padding:0px !important; margin: 0px !important">
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="vistoriaPF" value="#{localemsgs.VistoriaPF}:"/>
                                            </label>
                                            <p:inputText id="vistoriaPF" value="#{veiculos.veiculo.vistoriaPF}"
                                                         label="#{localemsgs.VistoriaPF}" maxlength="8"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VisPF" value="#{localemsgs.Data}:"/>
                                            </label>
                                            <p:datePicker id="dt_VisPF" value="#{veiculos.veiculo.dt_VisPF}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VenPF" value="#{localemsgs.Vencimento}:"/>
                                            </label>
                                            <p:datePicker id="dt_VenPF" value="#{veiculos.veiculo.dt_VenPF}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>  
                                        </div>

                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="vistoriaFabric" value="#{localemsgs.VistoriaFabricante}:"/>
                                            </label>
                                            <p:inputText id="vistoriaFabric" value="#{veiculos.veiculo.vistoriaFabric}"
                                                         label="#{localemsgs.VistoriaFabricante}" maxlength="15"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VisFabric" value="#{localemsgs.Data}:"/>
                                            </label>
                                            <p:datePicker id="dt_VisFabric" value="#{veiculos.veiculo.dt_VisFabric}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VenFabric" value="#{localemsgs.Vencimento}:"/>
                                            </label>
                                            <p:datePicker id="dt_VenFabric" value="#{veiculos.veiculo.dt_VenFabric}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>  
                                        </div>
                                    </div>

                                    <div class="col-md-12 row" style="padding:0px !important; margin: 0px !important">
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="vistoriaQualid" value="#{localemsgs.VistoriaQualidade}:"/>
                                            </label>
                                            <p:inputText id="vistoriaQualid" value="#{veiculos.veiculo.vistoriaQualid}"
                                                         label="#{localemsgs.VistoriaQualidade}" maxlength="15"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VisQualid" value="#{localemsgs.Data}:"/>
                                            </label>
                                            <p:datePicker id="dt_VisQualid" value="#{veiculos.veiculo.dt_VisQualid}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VenQualid" value="#{localemsgs.Vencimento}:"/>
                                            </label>
                                            <p:datePicker id="dt_VenQualid" value="#{veiculos.veiculo.dt_VenQualid}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>  
                                        </div>

                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="vistoriaQualidII" value="#{localemsgs.VistoriaQualidadeII}:"/>
                                            </label>
                                            <p:inputText id="vistoriaQualidII" value="#{veiculos.veiculo.vistoriaQualidII}"
                                                         label="#{localemsgs.VistoriaQualidade}" maxlength="15"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VisQualidII" value="#{localemsgs.Data}:"/>
                                            </label>
                                            <p:datePicker id="dt_VisQualidII" value="#{veiculos.veiculo.dt_VisQualidII}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VenQualidII" value="#{localemsgs.Vencimento}:"/>
                                            </label>
                                            <p:datePicker id="dt_VenQualidII" value="#{veiculos.veiculo.dt_VenQualidII}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>  
                                        </div>
                                    </div>

                                    <div class="col-md-12 row" style="padding:0px !important; margin: 0px !important">
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="vistoriaConfor" value="#{localemsgs.VistoriaConformidade}:"/>
                                            </label>
                                            <p:inputText id="vistoriaConfor" value="#{veiculos.veiculo.vistoriaConfor}"
                                                         label="#{localemsgs.VistoriaConformidade}" maxlength="15"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VisConfor" value="#{localemsgs.Data}:"/>
                                            </label>
                                            <p:datePicker id="dt_VisConfor" value="#{veiculos.veiculo.dt_VisConfor}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VenConfor" value="#{localemsgs.Vencimento}:"/>
                                            </label>
                                            <p:datePicker id="dt_VenConfor" value="#{veiculos.veiculo.dt_VenConfor}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>  
                                        </div>

                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="vistoriaConforII" value="#{localemsgs.VistoriaConformidadeII}:"/>
                                            </label>
                                            <p:inputText id="vistoriaConforII" value="#{veiculos.veiculo.vistoriaConforII}"
                                                         label="#{localemsgs.VistoriaConformidade}" maxlength="15"
                                                         style="width: 100%">
                                            </p:inputText>   
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VisConforII" value="#{localemsgs.Data}:"/>
                                            </label>
                                            <p:datePicker id="dt_VisConforII" value="#{veiculos.veiculo.dt_VisConforII}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>
                                        </div>
                                        <div class="col-md-2 col-sm-4 col-xs-4">
                                            <label>
                                                <p:outputLabel for="dt_VenConforII" value="#{localemsgs.Vencimento}:"/>
                                            </label>
                                            <p:datePicker id="dt_VenConforII" value="#{veiculos.veiculo.dt_VenConforII}"
                                                          monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                          pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                          converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                            </p:datePicker>  
                                        </div>
                                    </div>
                                </ui:fragment>
                            </p:panel>

                            <div class="col-md-12 row" style="padding:0px !important; margin: 0px !important">
                                <div class="col-md-2 col-sm-4 col-xs-4">
                                    <label>
                                        <p:outputLabel for="tacografo" value="#{localemsgs.Tacografo}:"/>
                                    </label>
                                    <p:inputText id="tacografo" value="#{veiculos.veiculo.tacografo}"
                                                 label="#{localemsgs.Tacografo}" maxlength="30"
                                                 style="width: 100%">
                                    </p:inputText>   
                                </div>
                                <div class="col-md-2 col-sm-4 col-xs-4">
                                    <label>
                                        <p:outputLabel for="dt_Tacografo" value="#{localemsgs.Data}:"/>
                                    </label>
                                    <p:datePicker id="dt_Tacografo" value="#{veiculos.veiculo.dt_Tacografo}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                                <div class="col-md-2 col-sm-4 col-xs-4">
                                    <label>
                                        <p:outputLabel for="dt_VencTacografo" value="#{localemsgs.Vencimento}:"/>
                                    </label>
                                    <p:datePicker id="dt_VencTacografo" value="#{veiculos.veiculo.dt_VencTacografo}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>  
                                </div>

                                <div class="col-md-2 col-sm-4 col-xs-4">
                                    <label>
                                        <p:outputLabel for="alvara" value="#{localemsgs.Alvara}:"/>
                                    </label>
                                    <p:inputText id="alvara" value="#{veiculos.veiculo.alvara}"
                                                 label="#{localemsgs.Alvara}" maxlength="15"
                                                 style="width: 100%">
                                    </p:inputText>   
                                </div>
                                <div class="col-md-2 col-sm-4 col-xs-4">
                                    <label>
                                        <p:outputLabel for="dt_Alvara" value="#{localemsgs.Data}:"/>
                                    </label>
                                    <p:datePicker id="dt_Alvara" value="#{veiculos.veiculo.dt_Alvara}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>
                                </div>
                                <div class="col-md-2 col-sm-4 col-xs-4">
                                    <label>
                                        <p:outputLabel for="dt_VencAlvara" value="#{localemsgs.Vencimento}:"/>
                                    </label>
                                    <p:datePicker id="dt_VencAlvara" value="#{veiculos.veiculo.dt_VencAlvara}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="1999:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true" inputStyle="width: 100%"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                    </p:datePicker>  
                                </div>
                            </div>

                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important; text-align: right">
                                <p:commandLink rendered="#{veiculos.flag eq 1}" id="cadastro" action="#{veiculos.cadastrar}"
                                               process="@form"
                                               update=":msgs :main:tabela cabecalho formVeiculo:cadastrar"
                                               title="#{localemsgs.Cadastrar}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                                <p:commandLink rendered="#{veiculos.flag eq 2}" id="editar" action="#{veiculos.editar}"
                                               process="@form"
                                               update=":msgs :main:tabela cabecalho formVeiculo:cadastrar"
                                               title="#{localemsgs.Editar}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <!-- TODO -->
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"   
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets">
    <h:body>
        <ui:composition> 
            <div class="ui-grid-row">
                <h:outputText value="#{login.infoFilial.descricao}" class="negrito"/>
            </div>
            <div class="ui-grid-row">
                #{login.infoFilial.endereco}
            </div>
            <div class="ui-grid-row">
                #{login.infoFilial.bairro}<h:outputText value=", " rendered="#{login.infoFilial.bairro ne '' and login.infoFilial.bairro ne null}"/>#{login.infoFilial.cidade}/#{login.infoFilial.UF}
            </div>
        </ui:composition>	
   </h:body>
</html>

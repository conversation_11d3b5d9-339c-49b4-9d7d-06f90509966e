/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.XMLeSocial;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class XMLeSocialDao {

    /* CONSULTAS PAGINADAS */
    /**
     * Conta o número de xmls enviados no banco
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer totalXML(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY evento, codfil asc, compet desc) AS RowNum, "
                    + " evento, codfil, compet, ambiente, max(dt_envio) dt_envio, tipo "
                    + " FROM XMLeSocial "
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + " codFil IS NOT null "
                    + " GROUP BY evento, codfil, compet, ambiente, tipo) AS RowConstrainedResult "
                    + " ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            int retorno = 0;
            while (consulta.Proximo()) {
                retorno++;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.totalXML - " + e.getMessage());
        }
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<XMLeSocial> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<XMLeSocial> retorno = new ArrayList<>();
        try {
            String sql = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY evento, codfil asc, compet desc) AS RowNum, "
                    + " evento, codfil, compet, ambiente, max(dt_envio) dt_envio, tipo "
                    + " FROM XMLeSocial "
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + " codFil IS NOT null "
                    + " GROUP BY evento, codfil, compet, ambiente, tipo) AS RowConstrainedResult "
                    + " WHERE   RowNum >= ? "
                    + " AND RowNum < ? "
                    + " ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            XMLeSocial xmleSocial;
            while (consulta.Proximo()) {
                xmleSocial = new XMLeSocial();
                xmleSocial.setRow(consulta.getString("RowNum"));
                xmleSocial.setCodFil(consulta.getString("CodFil"));
                xmleSocial.setCompet(consulta.getString("Compet"));
                xmleSocial.setAmbiente(consulta.getString("Ambiente").replace(".0", ""));
                xmleSocial.setEvento(consulta.getString("Evento"));
                xmleSocial.setDt_Envio(consulta.getString("Dt_Envio"));
                xmleSocial.setTipo(consulta.getString("tipo"));
                retorno.add(xmleSocial);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.listaPaginada - " + e.getMessage());
        }
    }

    public boolean existeEvento(XMLeSocial xmleSocial, Persistencia persistencia) throws Exception {
        try {
            String sql = "select * from XMLeSocial "
                    + " where codfil = ? and evento = ? and compet = ? and ambiente = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmleSocial.getCodFil());
            consulta.setString(xmleSocial.getEvento().split(" - ")[0]);
            consulta.setString(xmleSocial.getCompet());
            consulta.setString(xmleSocial.getAmbiente());
            consulta.select();
            boolean existe = false;
            while (consulta.Proximo()) {
                existe = true;
            }
            consulta.Close();
            return existe;
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.existeEvento - " + e.getMessage() + "\r\n"
                    + " select * from XMLeSocial "
                    + " where codfil = " + xmleSocial.getCodFil() + " and "
                    + " evento = " + xmleSocial.getEvento() + " and compet = " + xmleSocial.getCompet());
        }
    }

    public void inserirNovoEvento(XMLeSocial xmleSocial, Persistencia persistencia) throws Exception {
        try {
            String sql = " insert into XMLeSocial (Sequencia, Identificador, Tipo, Ambiente, CodFil, Evento, Compet,"
                    + " Protocolo_Envio, XML_Envio, XML_Retorno, Assinar, Dt_Envio, Hr_Envio, Dt_Retorno, Hr_Retorno) "
                    + " values ((select isnull(max(Sequencia),0) + 1 from XMLeSocial), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmleSocial.getIdentificador());
            consulta.setString(xmleSocial.getTipo());
            consulta.setString(xmleSocial.getAmbiente());
            consulta.setString(xmleSocial.getCodFil());
            consulta.setString(xmleSocial.getEvento().split(" - ")[0]);
            consulta.setString(xmleSocial.getCompet());
            consulta.setString(xmleSocial.getProtocolo_Envio());
            consulta.setString(xmleSocial.getXML_Envio());
            consulta.setString(xmleSocial.getXML_Retorno());
            consulta.setString(xmleSocial.getAssinar());
            consulta.setString(xmleSocial.getDt_Envio());
            consulta.setString(xmleSocial.getHr_Envio());
            consulta.setString(xmleSocial.getDt_Retorno());
            consulta.setString(xmleSocial.getHr_Retorno());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.inserirNovoEvento - " + e.getMessage() + "\r\n"
                    + " insert into XMLeSocial (Sequencia, Identificador, Tipo, Ambiente, CodFil, Evento, Compet,"
                    + " Protocolo_Envio, XML_Envio, XML_Retorno, Assinar, Dt_Envio, Hr_Envio, Dt_Retorno, Hr_Retorno) "
                    + " values ((select isnull(max(Sequencia),0) + 1 from XMLeSocial), " + xmleSocial.getIdentificador() + ", " + xmleSocial.getTipo() + ", "
                    + xmleSocial.getAmbiente() + ", " + xmleSocial.getCodFil() + ", " + xmleSocial.getEvento().split(" - ")[0] + ", " + xmleSocial.getCompet() + ", "
                    + xmleSocial.getProtocolo_Envio() + ", " + xmleSocial.getXML_Envio() + ", " + xmleSocial.getXML_Retorno() + ", " + xmleSocial.getAssinar() + ", "
                    + xmleSocial.getDt_Envio() + ", " + xmleSocial.getHr_Envio() + ", " + xmleSocial.getDt_Retorno() + ", " + xmleSocial.getHr_Retorno() + ") ");
        }
    }

    public void atualizarEvento(XMLeSocial xmleSocial, Persistencia persistencia) throws Exception {
        try {
            String sql = " update XMLeSocial set XML_Retorno = ?, Dt_Retorno = ?, Hr_Retorno = ? "
                    + " where sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmleSocial.getXML_Retorno());
            consulta.setString(xmleSocial.getDt_Retorno());
            consulta.setString(xmleSocial.getHr_Retorno());
            consulta.setBigDecimal(xmleSocial.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.atualizarEvento - " + e.getMessage() + "\r\n"
                    + " update XMLeSocial set XML_Retorno = " + xmleSocial.getXML_Retorno() + ", "
                    + " Dt_Retorno = " + xmleSocial.getDt_Retorno() + ", Hr_Retorno = " + xmleSocial.getHr_Retorno()
                    + " where sequencia = " + xmleSocial.getSequencia());
        }
    }

    public List<XMLeSocial> eventosPendentes(String filial, String evento, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<XMLeSocial> retorno = new ArrayList<>();
            String sql = "select protocolo_envio, sequencia, xml_retorno from xmlesocial "
                    + " where (xml_retorno = '' "
                    + "     or xml_retorno like '%<descRetorno>EM PROCESSAMENTO</descRetorno>%'"
                    + "     or xml_retorno like '%aguardando%'"
                    + "     or xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%') "
                    + " and codfil = ? and evento = ? and compet = ? and ambiente = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(filial);
            consulta.setString(evento);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.select();
            XMLeSocial xmleSocial;
            while (consulta.Proximo()) {
                xmleSocial = new XMLeSocial();
                xmleSocial.setProtocolo_Envio(consulta.getString("protocolo_envio"));
                xmleSocial.setSequencia(consulta.getBigDecimal("sequencia"));
                xmleSocial.setXML_Retorno(consulta.getString("xml_retorno"));
                retorno.add(xmleSocial);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.eventosPendentes - " + e.getMessage() + "\r\n"
                    + " select protocolo_envio, sequencia from xmlesocial "
                    + " where (xml_retorno = '' or xml_retorno like '%aguardando%') and codfil = " + filial + " and "
                    + " evento = " + evento + " and compet = " + compet);
        }
    }

    public List<XMLeSocial> eventosVerificar(XMLeSocial parametros, Persistencia persistencia) throws Exception {
        try {
            List<XMLeSocial> retorno = new ArrayList<>();
            String sql = "select sequencia, xml_retorno, identificador, evento from xmlesocial "
                    + " where identificador = ? and codfil = ? and evento = ? and compet = ? and ambiente = ? "
                    + " order by identificador asc, sequencia desc ";
                Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(parametros.getIdentificador());
            consulta.setString(parametros.getCodFil());
            consulta.setString(parametros.getEvento().split(" - ")[0]);
            consulta.setString(parametros.getCompet());
            consulta.setString(parametros.getAmbiente());
            consulta.select();
            XMLeSocial xmleSocial;
            while (consulta.Proximo()) {
                xmleSocial = new XMLeSocial();
                xmleSocial.setXML_Retorno(consulta.getString("xml_retorno"));
                xmleSocial.setIdentificador(consulta.getString("identificador").replace(".0", ""));
                xmleSocial.setSequencia(consulta.getBigDecimal("sequencia"));
                xmleSocial.setEvento(consulta.getString("evento"));
                retorno.add(xmleSocial);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.eventosVerificar - " + e.getMessage() + "\r\n"
                    + " select sequencia, xml_retorno, identificador from xmlesocial "
                    + " where identificador = " + parametros.getIdentificador() + " and codfil = " + parametros.getCodFil() + " and "
                    + " evento = " + parametros.getEvento().split(" - ")[0] + " and compet = " + parametros.getCompet()
                    + " order by  identificador asc, sequencia desc ");
        }
    }

    public List<XMLeSocial> eventosAssinar(Persistencia persistencia) throws Exception {
        try {
            List<XMLeSocial> retorno = new ArrayList<>();
            String sql = " SELECT top 300 *, CONVERT(BIGINT, Identificador) Identificador2 FROM xmlesocial "
                    + " WHERE Assinar IS NOT NULL AND Assinar <> '0' "
                    + " Order by Sequencia ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            XMLeSocial xmleSocial;
            while (consulta.Proximo()) {
                xmleSocial = new XMLeSocial();
                xmleSocial.setSequencia(consulta.getBigDecimal("Sequencia"));
                xmleSocial.setIdentificador(consulta.getString("Identificador2"));
                xmleSocial.setTipo(consulta.getString("Tipo"));
                xmleSocial.setAmbiente(consulta.getString("Ambiente"));
                xmleSocial.setCodFil(consulta.getString("CodFil"));
                xmleSocial.setEvento(consulta.getString("Evento"));
                xmleSocial.setCompet(consulta.getString("Compet"));
                xmleSocial.setProtocolo_Envio(consulta.getString("Protocolo_Envio"));
                xmleSocial.setXML_Envio(consulta.getString("XML_Envio"));
                xmleSocial.setXML_Retorno(consulta.getString("XML_Retorno"));
                xmleSocial.setAssinar(consulta.getString("Assinar"));
                xmleSocial.setDt_Envio(consulta.getString("Dt_Envio"));
                xmleSocial.setHr_Envio(consulta.getString("Hr_Envio"));
                xmleSocial.setDt_Retorno(consulta.getString("Dt_Retorno"));
                xmleSocial.setHr_Retorno(consulta.getString("Hr_Retorno"));
                retorno.add(xmleSocial);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.eventosAssinar - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM xmlesocial "
                    + " WHERE Assinar IS NOT NULL OR Assinar <> '0' ");
        }
    }

    public List<XMLeSocial> eventos(Persistencia persistencia) throws Exception {
        try {
            List<XMLeSocial> retorno = new ArrayList<>();
            String sql = " SELECT Convert(BigInt,Identificador) Ident,Convert(BigInt,Sequencia) Seq, * FROM xmlesocial "
                    + "Order by Sequencia ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            XMLeSocial xmleSocial;
            while (consulta.Proximo()) {
                xmleSocial = new XMLeSocial();
                xmleSocial.setSequencia(consulta.getBigDecimal("Seq"));
                xmleSocial.setIdentificador(consulta.getString("Ident"));
                xmleSocial.setTipo(consulta.getString("Tipo"));
                xmleSocial.setAmbiente(consulta.getString("Ambiente"));
                xmleSocial.setCodFil(consulta.getString("CodFil"));
                xmleSocial.setEvento(consulta.getString("Evento"));
                xmleSocial.setCompet(consulta.getString("Compet"));
                xmleSocial.setProtocolo_Envio(consulta.getString("Protocolo_Envio"));
                xmleSocial.setXML_Envio(consulta.getString("XML_Envio"));
                xmleSocial.setXML_Retorno(consulta.getString("XML_Retorno"));
                xmleSocial.setAssinar(consulta.getString("Assinar"));
                xmleSocial.setDt_Envio(consulta.getString("Dt_Envio"));
                xmleSocial.setHr_Envio(consulta.getString("Hr_Envio"));
                xmleSocial.setDt_Retorno(consulta.getString("Dt_Retorno"));
                xmleSocial.setHr_Retorno(consulta.getString("Hr_Retorno"));
                retorno.add(xmleSocial);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.eventosAssinar - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM xmlesocial ");
        }
    }

    public void marcarAssinar(XMLeSocial xmleSocial, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE XMLeSocial SET Assinar = ? WHERE sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmleSocial.getAssinar());
            consulta.setBigDecimal(xmleSocial.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.marcarAssinar - " + e.getMessage() + "\r\n"
                    + "  UPDATE XMLeSocial SET Assinar = " + xmleSocial.getAssinar() + " WHERE sequencia = " + xmleSocial.getSequencia());
        }
    }

    public void atualizarEnvio(XMLeSocial xmleSocial, Persistencia persistencia) throws Exception {
        try {
            String sql = " update XMLeSocial set Protocolo_Envio = ?, Assinar = ?, Dt_Envio = ?, Hr_Envio = ? "
                    + " where sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmleSocial.getProtocolo_Envio());
            consulta.setString(xmleSocial.getAssinar());
            consulta.setString(xmleSocial.getDt_Envio());
            consulta.setString(xmleSocial.getHr_Envio());
            consulta.setBigDecimal(xmleSocial.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLeSocialDao.atualizarEvento - " + e.getMessage() + "\r\n"
                    + " update XMLeSocial set Protocolo = " + xmleSocial.getProtocolo_Envio() + ", Assinar = " + xmleSocial.getAssinar() + ", "
                    + " Dt_Envio = " + xmleSocial.getDt_Envio() + ", Hr_Envio = " + xmleSocial.getHr_Envio()
                    + " where sequencia = " + xmleSocial.getSequencia());
        }
    }
}

package br.com.sasw.pacotesuteis.utilidades;

/**
 * <AUTHOR>
 */
public class CriarJSON {

    private String json = "";

    public CriarJSON() {
        json = "";
    }

    /**
     * Chave do json principal
     *
     * @param nomeChave
     */
    public void chavePrincipal(String nomeChave) {
        json = "{\"" + nomeChave + "\":[";
    }

    /**
     * Abre a insereção de registro na tag "{"
     */
    public void abrir() {
        json += "{";
    }

    /**
     * inseri itens para o json
     *
     * @param chave chave do json
     * @param dado dado do json
     * @param continuar se continua registro seri o "," no final de cada
     * registros
     */
    public void inserirItemArray(String chave, Object dado, boolean continuar) {
        if (eNumero(dado)) {
            json += "\"" + chave + "\": " + dado;
        } else {
            json += "\"" + chave + "\": \"" + dado + "\"";
        }

        if (continuar) {
            json += ",";
        }
    }

    /**
     * Realiza o fechamento do json na tag "},"
     */
    public void fechar() {
        json += "},";
    }

    /**
     * Visualiza registros do json
     *
     * @return retor do dado
     */
    public String ver() {
        json = json + "]}";
        if (json.contains("},]}")) {
            json = json.replace("},]}", "}]}");
        }
        return json;
    }

    //Realiza a verificação do número
    private boolean eNumero(Object dado) {
        boolean eNumero = false;
        try {
            Integer.parseInt(dado.toString());
            eNumero = true;
        } catch (Exception e) {
            eNumero = false;
        }
        return eNumero;
    }
}

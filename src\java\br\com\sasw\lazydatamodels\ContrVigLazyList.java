/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.OS_Vig.OS_VigSatMobWeb;
import Dados.Persistencia;
import SasBeans.ContrVig;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ContrVigLazyList extends LazyDataModel<ContrVig> {

    private static final long serialVersionUID = 1L;
    private List<ContrVig> subContratos = null;
    private final Persistencia persistencia;
    private final OS_VigSatMobWeb osVigController;
    private final String idContrato;

    public ContrVigLazyList(String idContrato, Persistencia pst) {
        this.idContrato = idContrato;
        this.persistencia = pst;
        osVigController = new OS_VigSatMobWeb();
    }

    @Override
    public List<ContrVig> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            subContratos = osVigController.listarSubContratosPaginada(first, pageSize, idContrato, filters, persistencia);

            // set the total of players
            setRowCount(osVigController.contagemSubContratos(idContrato, filters, persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return subContratos;
    }

    @Override
    public Object getRowKey(ContrVig contrVig) {
        if (null == contrVig.getCodFil() || null == contrVig.getContrato()) {
            return null;
        }
        return contrVig.getCodFil().toString().replace(".0", "") + ";" + contrVig.getContrato();
    }

    @Override
    public ContrVig getRowData(String codFilContrato) {
        String[] codFilContratoArray = codFilContrato.split(";");
        if (codFilContratoArray.length != 2) {
            return null;
        }
        for (ContrVig subContrato : subContratos) {
            if (codFilContratoArray[0].equals(subContrato.getCodFil().toString().replace(".0", ""))
                    && codFilContratoArray[1].equals(subContrato.getContrato())) {
                return subContrato;
            }
        }
        return null;
    }
}

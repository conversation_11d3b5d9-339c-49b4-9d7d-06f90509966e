package br.com.sasw.pacotesuteis.utilidades;

import Arquivo.ArquivoLog;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR>
 */
public class Trace {

    private static final int TAMANHO_MAXIMO_PARAMETRO = 300;

    /**
     * Grava o trace no arquivo
     *
     * @param context
     * @param servletNome - this.getServletName()
     * @param texto
     * @param sCodPessoa
     * @param param
     * @param logerro
     */
    public static void gerarTrace(ServletContext context, String servletNome, String texto, String sCodPessoa, String param, ArquivoLog logerro) {
        if (null == param || param.equals("")) {
            param = "erro";
        }
        if (null == sCodPessoa || sCodPessoa.equals("")) {
            sCodPessoa = "erro";
        }
        try {
            //montagem do trace
            String trace;
            String nome_trace;
            //monta nome do trace
            nome_trace = context.getRealPath("") + "Tracers\\" + param + "\\" + DataAtual.getDataAtual("SQL") + "\\"
                    + (sCodPessoa.contains(".0") ? sCodPessoa.replace(".0", "") : sCodPessoa) + ".txt";

            trace = Trace.Tracer(texto);
            logerro.Grava(servletNome + "\r\n"
                    + trace, nome_trace);
        } catch (Exception e) {
            logerro.Grava("Falha ao gravar Trace \r\n" + e.getMessage(),
                    context.getRealPath("") + "MsgErros\\" + param + ".txt");
        }
    }

    /**
     * Tracer sem codPessoa
     *
     * @param context
     * @param servletNome
     * @param texto
     * @param param
     * @param logerro
     */
    public static void gerarTrace(ServletContext context, String servletNome, String texto, String param, ArquivoLog logerro) {
        if (null == param || param.equals("")) {
            param = "erro";
        }
        try {
            //montagem do trace
            String trace;
            String nome_trace;            
            //monta nome do trace
            nome_trace = context.getRealPath("") + "Tracers\\" + param + "\\" + DataAtual.getDataAtual("SQL") + ".txt";

            trace = Trace.Tracer(texto);
            logerro.Grava(servletNome + "\r\n"
                    + trace, nome_trace);
        } catch (Exception e) {
            logerro.Grava("Falha ao gravar Trace \r\n" + e.getMessage(),
                    context.getRealPath("") + "MsgErros\\" + param + ".txt");
        }
    }

    /**
     * Grava o trace no arquivo
     *
     * @param context
     * @param servletNome - this.getServletName()
     * @param parametros
     * @param sCodPessoa
     * @param param
     * @param logerro
     */
    public static void gerarTrace(ServletContext context, String servletNome, 
            Map<String, String[]> parametros, String sCodPessoa, String param, 
            ArquivoLog logerro) {
        if (null == param || param.equals("")) {
            param = "erro";
        }
        if (null == sCodPessoa || sCodPessoa.equals("")) {
            sCodPessoa = "erro";
        }
        try {
            //montagem do trace
            String trace, url;
            String nome_trace;
            //monta nome do trace
            nome_trace = context.getRealPath("") + "Tracers\\" + param + "\\" + 
                    DataAtual.getDataAtual("SQL") + "\\" + 
                    (sCodPessoa.contains(".0") ? sCodPessoa.replace(".0", "") : sCodPessoa) 
                    + ".txt";

            trace = Trace.Tracer(parametros);
            url = Trace.TracerURL(parametros);

            logerro.Grava(servletNome + "\r\n" + trace + 
                    "\r\nhttp://localhost:8080/" +  servletNome + url + "\r\n", 
                    nome_trace);
        } catch (Exception e) {
            logerro.Grava("Falha ao gravar Trace " + sCodPessoa + " \r\n" + e.getMessage(),
                    context.getRealPath("") + "MsgErros\\" + param + ".txt");
        }
    }
    
    public static void gerarTrace(ServletContext context, String servletNome, HttpServletRequest request, String sCodPessoa, String param, ArquivoLog logerro) {
        if (null == param || param.equals("")) {
            param = "erro";
        }
        if (null == sCodPessoa || sCodPessoa.equals("")) {
            sCodPessoa = "erro";
        }
        try {
            //montagem do trace
            String trace, url;
            String nome_trace;
            //monta nome do trace
            nome_trace = context.getRealPath("") + "Tracers\\" + param + "\\" + DataAtual.getDataAtual("SQL") + "\\"
                    + (sCodPessoa.contains(".0") ? sCodPessoa.replace(".0", "") : sCodPessoa) + ".txt";

            trace = Trace.Tracer(request.getParameterMap());
            url = Trace.TracerURL(request);

            logerro.Grava(servletNome + "\r\n" + trace + "\r\n" + url + "\r\n", nome_trace);
        } catch (Exception e) {
            logerro.Grava("Falha ao gravar Trace " + sCodPessoa + " \r\n" + e.getMessage(),
                    context.getRealPath("") + "MsgErros\\" + param + ".txt");
        }
    }

    /**
     * Monta um trace conforme dados pasados
     *
     * @param parametros - map dos parametros Map<String,String[]>
     * - para web usar request.getParameterMap()
     * @return String com os dados prontos para serem gravados em forma de trace
     */
    public static String Tracer(Map<String, String[]> parametros) {
        String trace = "";
        Set<Map.Entry<String, String[]>> set = parametros.entrySet();
        Iterator it = set.iterator();
        while (it.hasNext()) {
            Map.Entry<String, String[]> entry = (Map.Entry) it.next();
            String valor = ArrayToString(entry.getValue());
            if (valor.length() > TAMANHO_MAXIMO_PARAMETRO) {
                valor = valor.substring(0, TAMANHO_MAXIMO_PARAMETRO);
            }               
            trace += entry.getKey() + " - " + valor + "\r\n";
        }
        return trace;
    }

    /**
     * Monta um trace conforme dados pasados
     *
     * @param parametros - map dos parametros Map<String,String[]>
     * - para web usar request.getParameterMap()
     * @return String com os dados prontos para serem gravados em forma de trace
     */
    public static String TracerURL(Map<String, String[]> parametros) {
        String trace = "?";
        Set<Map.Entry<String, String[]>> set = parametros.entrySet();
        Iterator it = set.iterator();
        while (it.hasNext()) {
            Map.Entry<String, String[]> entry = (Map.Entry) it.next();
            String valor = ArrayToString(entry.getValue());
            if (valor.length() > TAMANHO_MAXIMO_PARAMETRO) {
                valor = valor.substring(0, TAMANHO_MAXIMO_PARAMETRO);
            }
            trace += entry.getKey() + "=" + valor + "&";
        }
        return trace.substring(0, trace.length() - 1);
    }
    
    public static String TracerURL(HttpServletRequest request) {
        StringBuilder requestURL = new StringBuilder(request.getRequestURL().toString());
        String queryString = request.getQueryString();

        if (queryString == null) {
            return requestURL.toString();
        } else {
            return requestURL.append('?').append(queryString).toString();
        }
    }

    public static void Erros(ServletContext context, HttpServletRequest request, ArquivoLog logerro) {
        try {
            int qtdErros = Integer.valueOf(request.getParameter("erros"));
            if (qtdErros > 0) {
                String[] classes = request.getParameter("classes").split(";");
                String[] metodos = request.getParameter("metodos").split(";");
                String[] errs = request.getParameter("errs").split(";");
                String[] datas = request.getParameter("datas").split(";");
                String[] horas = request.getParameter("horas").split(";");
                StringBuilder erros;
                String nomeArquivo = context.getRealPath("") + "Erros\\" + request.getParameter("param")
                        + "\\" + request.getParameter("aplicacao")
                        + "\\" + DataAtual.getDataAtual("SQL") + ".txt";
                for (int i = 0; i < qtdErros; i++) {
                    erros = new StringBuilder();
                    erros.append(classes[i]).append("\r\n");
                    erros.append(metodos[i]).append("\r\n");
                    erros.append(errs[i]).append("\r\n");
                    erros.append(datas[i]).append(" ").append(horas[i]);
                    logerro.Grava(erros.toString(), nomeArquivo);
                }
            }
        } catch (Exception e) {
        }
    }

    // @Override método padrão Arrays.toString()
    public static String ArrayToString(Object[] a) {
        if (a == null) {
            return "null";
        }

        int iMax = a.length - 1;
        if (iMax == -1) {
            return "";
        }

        StringBuilder b = new StringBuilder();
        for (int i = 0;; i++) {
            b.append(String.valueOf(a[i]));
            if (i == iMax) {
                return b.toString();
            }
            b.append(", ");
        }
    }

    /**
     * Monta um trace conforme dados passados
     *
     * @param parametro
     * @return String com os dados prontos para serem gravados em forma de trace
     */
    public static String Tracer(String parametro) {
        return "Resposta - " + parametro + "\r\n";
    }
}

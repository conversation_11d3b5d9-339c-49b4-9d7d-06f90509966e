/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;

/**
 *
 * <AUTHOR>
 */
public class TOKENSDao {

    /**
     * Busca as informações a partir do código de um token
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public TOKENS obterToken(String codigo, Persistencia persistencia) throws Exception {
        try {
            TOKENS retorno = null;
            String sql = " SELECT *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, DtValid, 20) DtValidC FROM TOKENS WHERE Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new TOKENS();
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setBancoDados(consulta.getString("BancoDados"));
                retorno.setModulo(consulta.getString("Modulo"));
                retorno.setChave(consulta.getString("Chave"));
                retorno.setData(consulta.getString("DataC"));
                retorno.setHora(consulta.getString("Hora"));
                retorno.setDtValid(consulta.getString("DtValidC"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TOKENSDao.obterToken - " + e.getMessage() + "\r\n"
                    + " SELECT *, CONVERT(VarChar, Data, 112) DataC, CONVERT(VarChar, DtValid, 112) DtValidC FROM TOKENS WHERE Codigo = " + codigo);
        }
    }

    /**
     * Insere no banco um novo token
     *
     * @param token
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String gerarToken(TOKENS token, Persistencia persistencia) throws Exception {
        try {
            String sql = "DECLARE @Codigo Varchar(32)\n"
                    + "DECLARE @CodigoAnterior Varchar(32)\n"
                    + "DECLARE @Bloco1Int INT\n"
                    + "DECLARE @Bloco2Int INT\n"
                    + "DECLARE @Bloco3Int INT\n"
                    + "DECLARE @Bloco1 Varchar(6)\n"
                    + "DECLARE @Bloco2 Varchar(6)\n"
                    + "DECLARE @Bloco3 Varchar(6)\n"
                    + "\n"
                    + "SET @Bloco1Int = FLOOR(RAND()*(16777216)) -- FFFFFF\n"
                    + "SET @Bloco2Int = FLOOR(RAND()*(16777216))\n"
                    + "SET @Bloco3Int = FLOOR(RAND()*(16777216))\n"
                    + "\n"
                    + "SET @Bloco1 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco1Int,'X')))) + RTRIM(FORMAT(@Bloco1Int,'X'))\n"
                    + "SET @Bloco2 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco2Int,'X')))) + RTRIM(FORMAT(@Bloco2Int,'X'))\n"
                    + "SET @Bloco3 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco3Int,'X')))) + RTRIM(FORMAT(@Bloco3Int,'X'))\n"
                    + "\n" //SET NOCOUNT ON
                    + "\n"
                    + "SET NOCOUNT ON\n"
                    + "\n"
                    + "SET @CodigoAnterior = (SELECT TOP 1 FORMAT(CONVERT(INT, CONVERT(VARBINARY, '0' + SUBSTRING(ISNULL(Codigo, 'B4FFFF2'), 1 ,7), 2)) + 14,'X') FROM TOKENS ORDER BY Codigo DESC)\n"
                    + "SET @Codigo = CONCAT(@CodigoAnterior,\n"
                    + "    @Bloco1, @Bloco2, @Bloco3,\n"
                    + "    REVERSE(@CodigoAnterior))\n"
                    + "\n"
                    + "INSERT INTO \n"
                    + "    TOKENS (Codigo, BancoDados, Modulo, Chave, Data, Hora, DtValid)\n"
                    + "VALUES \n"
                    + "    (@Codigo, ?, ?, ?, ?, ?, ";

            if (null != token.getDtValid()) {
                sql += "?";
            } else {
                sql += "DATEADD(HOUR, 1, '" + token.getData() + " " + token.getHora() + "')";
            }

            sql += ")\n"
                    + "SELECT @Codigo Token \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(token.getBancoDados());
            consulta.setString(token.getModulo());
            consulta.setString(token.getChave());
            consulta.setString(token.getData());
            consulta.setString(token.getHora());
            if (null != token.getDtValid()) {
                consulta.setString(token.getDtValid());
            }
            consulta.select();
            String codigo = null;
            if (consulta.Proximo()) {
                codigo = consulta.getString("Token");
            }
            consulta.close();
            return codigo;
        } catch (Exception e) {
            throw new Exception("TOKENSDao.gerarToken - " + e.getMessage() + "\r\n"
                    + "DECLARE @Codigo Varchar(32)\n"
                    + "DECLARE @Bloco1Int INT\n"
                    + "DECLARE @Bloco2Int INT\n"
                    + "DECLARE @Bloco3Int INT\n"
                    + "DECLARE @Bloco1 Varchar(6)\n"
                    + "DECLARE @Bloco2 Varchar(6)\n"
                    + "DECLARE @Bloco3 Varchar(6)\n"
                    + "\n"
                    + "SET @Bloco1Int = FLOOR(RAND()*(16777216)) -- FFFFFF\n"
                    + "SET @Bloco2Int = FLOOR(RAND()*(16777216))\n"
                    + "SET @Bloco3Int = FLOOR(RAND()*(16777216))\n"
                    + "\n"
                    + "SET @Bloco1 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco1Int,'X')))) + RTRIM(FORMAT(@Bloco1Int,'X'))\n"
                    + "SET @Bloco2 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco2Int,'X')))) + RTRIM(FORMAT(@Bloco2Int,'X'))\n"
                    + "SET @Bloco3 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco3Int,'X')))) + RTRIM(FORMAT(@Bloco3Int,'X'))\n"
                    + "\n"
                    + "SET @Codigo = CONCAT(FORMAT(CONVERT(INT, CONVERT(VARBINARY, '0' + SUBSTRING(ISNULL(@Codigo, 'B4FFFF2'), 1 ,7), 2)) + 14,'X'),\n"
                    + "    @Bloco1, @Bloco2, @Bloco3,\n"
                    + "    REVERSE(FORMAT(CONVERT(INT, CONVERT(VARBINARY, '0' + SUBSTRING(ISNULL(@Codigo, 'B4FFFF2'), 1 ,7), 2)) + 14,'X')))\n"
                    + "\n"
                    + "INSERT INTO \n"
                    + "    TOKENS (Codigo, " + token.getBancoDados() + ", " + token.getModulo() + ", " + token.getChave() + ", "
                    + token.getData() + ", " + token.getHora() + ", " + token.getDtValid() + ")\n"
                    + "VALUES \n"
                    + "    (@Codigo, ?, ?, ?, ?, ?, ?)\n"
                    + "\n"
                    + "SELECT @Codigo Token \n");
        }
    }
}

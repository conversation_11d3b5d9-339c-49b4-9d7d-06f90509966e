<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite="http://java.sun.com/jsf/composite">
    <composite:interface>
        <composite:attribute name="idCorporativo" />
        <composite:attribute name="idForm" />
    </composite:interface>

    <composite:implementation>
        <footer
            class="footer-body"
            style="
            position: fixed;
            height: 50px !important;
            background-color: darkblue;
            bottom: 0px;
            left: 0px;
            right: 0px;
            margin-bottom: 0px;
            display: flex;
            justify-content: space-around;
            padding: 0px !important;

            vertical-align: middle;
            z-index: 999;
            width: 100%;
            min-height: 63px;
            height: auto;
            overflow: auto;
            color: #fff;
            font-size: 11px;
            line-height: normal;

            min-height: unset !important;
            max-height: unset !important;
            line-height:10px !important;
            bottom:0px !important;
            margin-bottom:0px !important;
            padding:0px !important;
            "
            >
            <c:if test="#{not empty cc.attrs.idCorporativo}">
                <div
                    id="#{cc.attrs.idCorporativo}"
                    >
                    <h:form id="#{cc.attrs.idForm}">
                        <composite:insertChildren />
                    </h:form>
                </div>
            </c:if>

            <div
                id="divFooterTimer"
                style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                <table class="footer-time" style="min-height:10px !important">
                    <tr>
                        <td>
                            <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                        </td>
                    </tr>
                </table>
            </div>

            <div
                class="col-md-5 col-sm-6 col-xs-7"
                style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                <table
                    class="footer-user"
                    style="min-height:10px !important">
                    <tr>
                        <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                        <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                    </tr>
                </table>
            </div>

            <div class="col-md-2 col-sm-3 col-xs-5"
                 style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                    <tr>
                        <td><img src="../assets/img/logo_satweb.png" /></td>

                        <td>
                            <h:form>
                                <h:commandLink
                                    actionListener="#{localeController.increment}" 
                                    action="#{localeController.getLocales}">
                                    <p:graphicImage 
                                        url="../assets/img/#{localeController.number}.png"
                                        height="25"/>
                                </h:commandLink>
                            </h:form>   
                        </td>
                    </tr>
                </table>
            </div>
        </footer>
    </composite:implementation>
</html>

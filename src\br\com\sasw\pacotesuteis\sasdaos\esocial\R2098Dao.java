/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.R2098;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class R2098Dao {

    public List<R2098> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select "
                    + " '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) ideEvento_perApur, "
                    + " Case when Filiais.TipoPessoa = 'J' then 1 else 2 end ideContri_tpInsc, "
                    + " Substring(Filiais.CNPJ,1,8) ideContri_nrInsc,   "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + "             and z.evento = 'R-2098' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + "             and z.evento = 'R-2098' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>1%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + "             and z.evento = 'R-2098' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%')) a) sucesso "
                    + " From FPPeriodos  "
                    + " Left join Filiais  on Filiais.CodFil = ?"
                    + " Left join Dirf  on Dirf.CodFil = Filiais.CodFil "
                    + " Left join Pessoa on Pessoa.Codigo = DIRF.CodPessoaRIn "
                    + " Where Convert(Varchar,FPPeriodos.CodMovFP) = REPLACE(RIGHT(?,5),'-','') "
                    + " Group by FPPeriodos.CodMovFP, Filiais.TipoPessoa,Filiais.CNPJ,Pessoa.Nome,Pessoa.CPF,Pessoa.Fone1,DIRF.emailRInf, Filiais.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.select();
            List<R2098> retorno = new ArrayList<>();
            R2098 r2098;
            while (consulta.Proximo()) {
                r2098 = new R2098();

                r2098.setSucesso(consulta.getInt("sucesso"));
                r2098.setIdeEvento_procEmi("1");
                r2098.setIdeEvento_verProc("Satellite Reinf");

                r2098.setIdeEvento_perApur(consulta.getString("ideEvento_perApur"));

                r2098.setIdeContri_tpInsc(consulta.getString("ideContri_tpInsc"));
                r2098.setIdeContri_nrInsc(consulta.getString("ideContri_nrInsc"));

                retorno.add(r2098);
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("R2098Dao.get - " + e.getMessage() + "\r\n");
        }
    }
}

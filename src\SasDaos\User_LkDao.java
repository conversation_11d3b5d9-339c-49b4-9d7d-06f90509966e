/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;

/**
 *
 * <AUTHOR>
 */
public class User_LkDao {

    private final Persistencia persistencia;

    public User_LkDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public boolean eliminaBloqueios() throws Exception {
        // Elimina os bloqueios de usuário na Lagard
        String sql = "UPDATE USER_LK SET STATUS = 0  WHERE STATUS = 1";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.update();
            return true;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }
}

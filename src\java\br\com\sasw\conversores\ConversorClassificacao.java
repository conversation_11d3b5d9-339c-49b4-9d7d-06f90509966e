/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.Messages;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorClassificacao")
public class ConversorClassificacao implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return value;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        String retorno;

        switch (value.toString()) {
            case "R":
                retorno = Messages.getMessageS("Rotineiro");
                break;
            case "V":
                retorno = Messages.getMessageS("Eventual");
                break;
            case "E":
                retorno = Messages.getMessageS("Especial");
                break;
            case "A":
                retorno = Messages.getMessageS("AssistTecnica");
                break;
            default:
                retorno = value.toString().toUpperCase();
        }
        return retorno;
    }
}

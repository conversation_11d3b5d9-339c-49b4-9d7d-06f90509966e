/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1298 {

    private String sucesso;

    private String evtReabreEvPer_Id;

    private String ideEvento_indApuracao;
    private String ideEvento_perApur;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;

    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;

    public String getEvtReabreEvPer_Id() {
        return evtReabreEvPer_Id;
    }

    public void setEvtReabreEvPer_Id(String evtReabreEvPer_Id) {
        this.evtReabreEvPer_Id = evtReabreEvPer_Id;
    }

    public String getIdeEvento_indApuracao() {
        return ideEvento_indApuracao;
    }

    public void setIdeEvento_indApuracao(String ideEvento_indApuracao) {
        this.ideEvento_indApuracao = ideEvento_indApuracao;
    }

    public String getIdeEvento_perApur() {
        return ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }

}

package br.com.sasw.pacotesuteis.utilidades;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *
 * <AUTHOR>
 */
public class GPS {

    /**
     * Calcula em quilômetros a dsitância entre duas coordenadas considerando o
     * raio de curvatura da Terra.
     *
     * @param sLat1
     * @param sLon1
     * @param sLat2
     * @param sLon2
     * @return
     */
    public static BigDecimal distanciaCoordenadas(String sLat1, String sLon1, String sLat2, String sLon2) {
        Double lat1, lat2, lon1, lon2;
        BigDecimal dist;
        try {
            lat1 = Double.parseDouble(sLat1);
        } catch (Exception e) {
            lat1 = 0.0;
        }
        try {
            lat2 = Double.parseDouble(sLat2);
        } catch (Exception e) {
            lat2 = 0.0;
        }
        try {
            lon1 = Double.parseDouble(sLon1);
        } catch (Exception e) {
            lon1 = 0.0;
        }
        try {
            lon2 = Double.parseDouble(sLon2);
        } catch (Exception e) {
            lon2 = 0.0;
        }

        /* http://andrew.hedges.name/experiments/haversine/ */
        try {
            if (lat1 == 0.0 || lat2 == 0.0 || lon1 == 0.0 || lon2 == 0.0) {
                throw new Exception();
            }
            /* dlon = lon2 - lon1  */
            double lon = Math.toRadians(lon1 - lon2);
            /* dlat = lat2 - lat1 */
            double lat = Math.toRadians(lat1 - lat2);
            /* a = (sin(dlat/2))^2 + cos(lat1) * cos(lat2) * (sin(dlon/2))^2 */
            double a = Math.pow(Math.sin(lat / 2), 2)
                    + Math.cos(Math.toRadians(lat2))
                    * Math.cos(Math.toRadians(lat1))
                    * Math.pow(Math.sin(lon / 2), 2);
            /* c = 2 * atan2( sqrt(a), sqrt(1-a) )  */
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            /* d = R * c (where R is the radius of the Earth), R = 6373 km  */
            double d = 6373 * c;// * 1000;
            dist = BigDecimal.valueOf(d).setScale(3, RoundingMode.HALF_UP);
        } catch (Exception e) {
            dist = new BigDecimal("999999999");
        }
        return dist;
    }

    /**
     * transforma a coordenada em segundos
     *
     * @param grau
     * @param minuto
     * @param segundo
     * @return
     */
    public double getSegundos(double grau, double minuto, double segundo) {
        double retorno = 0;
        retorno = (grau * 3600);
        retorno += (minuto * 60);
        retorno += segundo;
        return retorno;
    }

    /**
     * Devolve a diferença entre 2 coordenadas em segundos
     *
     * @param valor1
     * @param valor2
     * @return - retorna sempre em valor negativo
     */
    public double diferencaSegundos(double valor1, double valor2) {
        double retorno = 0;
        retorno = valor1 - valor2;
        if (retorno < 0) {
            retorno = retorno * -1;
        }
        return retorno;
    }

    /**
     * Distancia em NM
     *
     * @param dla - diferença latitude
     * @param dlo - diferença longitude
     * @return - devolve a diferença e NM
     */
    public double distanciaNM(double dla, double dlo) {
        double retorno = 0;
        retorno = (dla * dla) + (dlo * dlo);
        retorno = Math.sqrt(retorno);
        return retorno;
    }

    /**
     * Distancia em Metros
     *
     * @param NM - distancia em NM
     * @return
     */
    public double distanciaM(double NM) {
        double retorno = 0;
        retorno = NM * 1852;
        return retorno;
    }

    /**
     * Distancia entre 2 coordenadas em metros
     *
     * @param graulat1 - grau da latitude 1
     * @param minutolat1 - minuto da latitude 1
     * @param segundolat1 - segundo da latitude 1
     * @param graulat2 - grau da latitude 2
     * @param minutolat2 - minuto da latitude 2
     * @param segundolat2 - segundo da latitude 2
     * @param graulon1 - grau longitude 1
     * @param minutolon1 - minuto longitude 1
     * @param segundolon1 - segundo longitude 1
     * @param graulon2 - grau longitude 2
     * @param minutolon2 - minuto longitude 2
     * @param segundolon2 - segundo longitude 2
     * @return - distancia em metros
     */
    public double distancia2pontos(double graulat1, double minutolat1, double segundolat1,
            double graulat2, double minutolat2, double segundolat2,
            double graulon1, double minutolon1, double segundolon1,
            double graulon2, double minutolon2, double segundolon2) {
        double retorno = 0;
        double ponto1 = this.getSegundos(graulat1, minutolat1, segundolat1);
        double ponto2 = this.getSegundos(graulat2, minutolat2, segundolat2);
        double ponto3 = this.getSegundos(graulon1, minutolon1, segundolon1);
        double ponto4 = this.getSegundos(graulon2, minutolon2, segundolon2);
        retorno = this.distanciaM(
                this.distanciaNM(
                        this.diferencaSegundos(ponto1, ponto2),
                        this.diferencaSegundos(ponto3, ponto4)
                )
        );
        return retorno;
    }
}

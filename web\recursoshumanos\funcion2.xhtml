<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/funcion.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
            <style>
                .ui-inputtext{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                .calendario .ui-inputfield{
                    margin-top: 3px !important
                }

                [id*="formPesquisaRapida"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }


                [id*="cadastrar"] div[class*="col-md"]{
                    padding: 5px !important;
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -3px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid tbody tr td{
                        white-space:nowrap;
                        overflow:hidden;
                        text-overflow:ellipsis;
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid tbody tr td:nth-child(1),
                    .DataGrid thead tr th:nth-child(12),
                    .DataGrid tbody tr td:nth-child(12) {
                        min-width:50px !important;
                    }

                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2){
                        min-width:150px !important;
                    }

                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid tbody tr td:nth-child(5),
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7),
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid tbody tr td:nth-child(8),
                    .DataGrid thead tr th:nth-child(9),
                    .DataGrid tbody tr td:nth-child(9),
                    .DataGrid thead tr th:nth-child(13),
                    .DataGrid tbody tr td:nth-child(13),
                    .DataGrid thead tr th:nth-child(14),
                    .DataGrid tbody tr td:nth-child(14),
                    .DataGrid thead tr th:nth-child(15),
                    .DataGrid tbody tr td:nth-child(15),
                    .DataGrid thead tr th:nth-child(16),
                    .DataGrid tbody tr td:nth-child(16),
                    .DataGrid thead tr th:nth-child(18),
                    .DataGrid tbody tr td:nth-child(18),
                    .DataGrid thead tr th:nth-child(19),
                    .DataGrid tbody tr td:nth-child(19){
                        min-width:130px !important;
                    }

                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid tbody tr td:nth-child(4),
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid tbody tr td:nth-child(6),
                    .DataGrid thead tr th:nth-child(11),
                    .DataGrid tbody tr td:nth-child(11) {
                        min-width:300px !important;
                    }

                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid tbody tr td:nth-child(3),
                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid tbody tr td:nth-child(10),
                    .DataGrid thead tr th:nth-child(17),
                    .DataGrid tbody tr td:nth-child(17){
                        min-width:200px !important;
                    }
                }

                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }

                #formPesquisar .ui-radiobutton {
                    background: transparent !important;
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                #formPesquisaRapida .ui-radiobutton {
                    background: transparent !important;
                }

                [id*="formCadastrar"] label{
                    margin-bottom: 0px !important;
                }
            </style>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{funcionario.Persistencias(login.pp, login.satellite)}" />
            </f:metadata>

            <p:growl id="msgs" />

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_funcionarios.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Funcion}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{funcionario.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">#{funcionario.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                        <label class="FilialEndereco">#{funcionario.filiais.endereco}</label>
                                        <label class="FilialBairroCidade">#{funcionario.filiais.bairro}, #{funcionario.filiais.cidade}/#{funcionario.filiais.UF}</label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3" style="padding:0px 10px 0px 0px !important; text-align: right !important">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="a" update=" msgs formCadastrar" actionListener="#{funcionario.NovoFuncionario}" oncomplete="PF('dlgCadastrar').show();"/>
                    <p:hotkey bind="p" actionListener="#{funcionario.PrePesquisar}" oncomplete="PF('dlgPesquisaRapida').show();" update="formPesquisaRapida"/>
                    <p:hotkey bind="e" update="formCadastrar msgs" actionListener="#{funcionario.buttonAction}"/>
                    <p:hotkey bind="shift+x" actionListener="#{exportarMB.setTitulo(localemsgs.Funcion)}" oncomplete="PF('dlgExportar').show();"/>
                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 140px !important; background: transparent; height:200px !important;" id="botoes">

                        <p:remoteCommand name="rcGerarMatrAuto" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs formCadastrar" 
                                         oncomplete="PF('dlgCadastrar').show();"
                                         actionListener="#{funcionario.novoFuncionarioMatr}" />  

                        <p:remoteCommand name="rcNaoGerarMatrAuto" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs formCadastrar" 
                                         oncomplete="PF('dlgCadastrar').show();"
                                         actionListener="#{funcionario.novoFuncionarioSemMatr}" />                            

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Cadastrar}" id="btAddFuncion" onclick="GerarMatriculaAtutomatica()">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" update="formCadastrar msgs"
                                           actionListener="#{funcionario.buttonAction}">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Configurar}" actionListener="#{funcionario.preConfiguracaoFuncionEW}"
                                           oncomplete="PF('dlgConfiguracaoEW').show();" update="formConfiguracaoEW">
                                <p:graphicImage url="../assets/img/icone_ew.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{funcionario.PrePesquisar}"
                                           oncomplete="PF('dlgPesquisaRapida').show();" update="formPesquisaRapida">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{funcionario.limparFiltros()}"
                                           update="msgs main:tabela corporativo">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Exportar}" actionListener="#{exportarMB.setTitulo(localemsgs.Funcion)}"
                                           oncomplete="PF('dlgExportar').show();" >
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline">
                                    <p:dataTable id="tabela"
                                                 value="#{funcionario.allFuncion}"
                                                 paginator="true"
                                                 rows="25"
                                                 lazy="true"
                                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Funcionarios}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista"
                                                 selectionMode="single"
                                                 selection="#{funcionario.selecionado}"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true"
                                                 class="tabela DataGrid"
                                                 scrollHeight="100%"
                                                 reflow="true"
                                                 style="font-size: 12px; background: white"
                                                 rowKey="#{lista.funcion.matr}"
                                                 >
                                        <p:ajax event="rowDblselect" listener="#{funcionario.dblSelect}" update="formCadastrar" class="tabela DataGrid"/>
                                        <p:column headerText="#{localemsgs.CodFil}" class="text-center" exportable="#{funcionario.eFilial}">
                                            <h:outputText value="#{lista.funcion.codFil}" title="#{lista.funcion.codFil}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Matr}" exportable="#{funcionario.eMatr}" class="text-center">
                                            <h:outputText value="#{lista.funcion.matr}">
                                                <f:convertNumber pattern="00000000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nome_Guer}" exportable="#{funcionario.eNomeGuer}">
                                            <h:outputText value="#{lista.funcion.nome_Guer}" title="#{lista.funcion.nome_Guer}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nome}" exportable="#{funcionario.eNome}">
                                            <h:outputText value="#{lista.funcion.nome}" title="#{lista.funcion.nome}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CPF}" exportable="#{funcionario.eCPF}" class="text-center">
                                            <h:outputText value="#{lista.funcion.CPF}" title="#{lista.funcion.CPF}" converter="conversorCPF"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Email}" exportable="#{funcionario.eEmail}" class="text-center">
                                            <h:outputText value="#{lista.funcion.email}" title="#{lista.funcion.email}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.RG}" exportable="#{funcionario.eRG}" class="text-center">
                                            <h:outputText value="#{lista.funcion.RG}" title="#{lista.funcion.RG}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.RGOrg}" exportable="#{funcionario.eOrgEmis}" class="text-center">
                                            <h:outputText value="#{lista.funcion.orgEmis}" title="#{lista.funcion.orgEmis}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Nasc}" exportable="#{funcionario.eDtNasc}" class="text-center">
                                            <h:outputText value="#{lista.funcion.dt_Nasc}" title="#{lista.funcion.dt_Nasc}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Secao}" exportable="#{funcionario.eCodPosto}" class="text-center">
                                            <h:outputText value="#{lista.funcion.secao}" title="#{lista.funcion.secao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Posto}" exportable="#{funcionario.ePosto}">
                                            <h:outputText value="#{lista.pstserv.local}" title="#{lista.pstserv.local}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Situacao}" exportable="#{funcionario.eSituacao}" class="text-center">
                                            <h:outputText value="#{lista.funcion.situacao}" title="#{lista.funcion.situacao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Situac}" exportable="#{funcionario.eDtSituacao}" class="text-center">
                                            <h:outputText value="#{lista.funcion.dt_Situac}" title="#{lista.funcion.dt_Situac}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Admis}" exportable="#{funcionario.eDtAdmis}" class="text-center">
                                            <h:outputText value="#{lista.funcion.dt_Admis}" title="#{lista.funcion.dt_Admis}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.InterfExt}" exportable="#{funcionario.eInterfExt}" class="text-center">
                                            <h:outputText value="#{lista.pstserv.interfExt}" title="#{lista.pstserv.interfExt}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CodPessoaWeb}" class="text-center" exportable="#{funcionario.eCodPessoaWeb}">
                                            <h:outputText value="#{lista.funcion.codPessoaWeb}" title="#{lista.funcion.codPessoaWeb}"
                                                          converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" exportable="#{funcionario.eOperador}" class="text-center">
                                            <h:outputText value="#{lista.funcion.operador}" title="#{lista.funcion.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}" exportable="#{funcionario.eDtAlter}" class="text-center">
                                            <h:outputText value="#{lista.funcion.dt_Alter}" title="#{lista.funcion.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}" exportable="#{funcionario.eHrAlter}" class="text-center">
                                            <h:outputText value="#{lista.funcion.hr_Alter}" converter="conversorHora" title="#{lista.funcion.hr_Alter}"/>
                                        </p:column>
                                    </p:dataTable>
                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else {
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                                $('thead tr th').each(function () {
                                                    $(this).parents('table').find('td:nth-child(' + ($(this).index() + 1) + ')').css('max-width', $(this).width() + 'px');
                                                });
                                            }
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else {
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                                $('thead tr th').each(function () {
                                                    $(this).parents('table').find('td:nth-child(' + ($(this).index() + 1) + ')').css('max-width', $(this).width() + 'px');
                                                });
                                            }
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>
                </h:form>

                <h:form class="form-inline" id="formCadastrar">
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" focus="matr" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                              style="min-height:95% !important;height:95% !important;max-height:95% !important;max-width:95% !important;min-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <script>
                            /* $(document).ready(function () {
                             //first unbind the original click event
                             PF('dlgCadastrar').closeIcon.unbind('click');
                             
                             //register your own
                             PF('dlgCadastrar').closeIcon.click(function (e) {
                             $("#formCadastrar\\:botaoFechar").click();
                             //should be always called
                             e.preventDefault();
                             });
                             })*/
                        </script>
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarFuncion}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="cadastrar" class="cadastrar" style="background-color:#EEE; overflow:hidden !important; padding:0px !important;">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top: 0px !important; margin: 0px !important;display:#{funcionario.flag eq 2?'none':''}">
                                <a href="javascript:;" id="btnAtalhoPessoa" class="btn btn-success" style="border-radius: 20px"><i class="fa fa-search"></i>&nbsp;#{localemsgs.PesquisarPessoa}</a>
                            </div>

                            <!-- ITENS PARA REAPROVEITAMENTO DA TELA DE PESSOAS -->
                            <!-- Início -->
                            <h:inputHidden value="#{funcionario.codPessoaAproveitamento}" id="txtReaproveitaPessoal" />
                            <p:remoteCommand name="rcPessoa" partialSubmit="true" 
                                             process="@this,txtReaproveitaPessoal" 
                                             update="msgs pnlDados" 
                                             actionListener="#{funcionario.carregarDadosPessoaAproveitamento()}" />                            
                            <!-- FIM -->
                            <div class="col-md-2 col-sm-2 col-xs-12" style="padding-left: 0px !Important">
                                <label><p:outputLabel for="matr" value="#{localemsgs.Matr}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                <p:inputText id="matr" value="#{funcionario.pessoaMB.novaPessoa.matr}"
                                             required="true" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Matr}"
                                             disabled="#{funcionario.flag eq 2 or funcionario.novo.matr.equals('0') }"
                                             label="#{localemsgs.Matr}" maxlength="15">
                                    <f:convertNumber pattern="000000"/>
                                </p:inputText>    
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6">
                                <label><p:outputLabel for="situacao2" value="#{localemsgs.Situacao}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                <p:selectOneMenu id="situacao2" required="true"
                                                 value="#{funcionario.novo.situacao}"
                                                 disabled="#{funcionario.flag eq 1}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}"
                                                 style="width: 100%;">
                                    <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                    <f:selectItem itemLabel="#{localemsgs.Demitido}" itemValue="D" />
                                    <p:ajax update="dataSituacao" process="@this" ></p:ajax>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6" style="position: relative">
                                <p:outputLabel for="dataSituacao" value="#{localemsgs.DataSituacao}" />
                                <p:calendar id="dataSituacao" value="#{funcionario.novo.dt_Situac}" 
                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario" disabled="#{funcionario.novo.situacao ne 'D'}"
                                            style="width: 100%;" required="#{funcionario.novo.situacao ne 'D'?'false':'true'}" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.dt_Situac}" />
                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-12" style="display:#{funcionario.flag eq 1?'':'none'}">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:7px 5px 3px 5px !important; background-color:#FFF; border: thin solid #DDD; border-radius: 3px; margin-top:21px; text-align: center !important">
                                    <p:selectBooleanCheckbox id="matrAut" style="float: right; margin-right: 6px !important;" value="#{funcionario.matriculaAutomatica}">
                                        <p:ajax update="matr" process="@this" listener="#{funcionario.buscarMatriculaAutomatica()}"/>
                                    </p:selectBooleanCheckbox>
                                    <p:outputLabel for="matrAut" value="#{localemsgs.MatrAut}" style="font-size:8pt !important" />
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-12" style="position: relative">
                                <label><p:outputLabel for="dt_admis" value="#{localemsgs.Dt_Admis}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                <p:calendar id="dt_admis" value="#{funcionario.novo.dt_Admis}" 
                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Dt_Admis}" style="width: 100%; text-align:left !important;">
                                </p:calendar>
                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding:0px !important; margin:0px !important">
                                <div class="col-md-6 col-sm-6 col-xs-12" style="padding-left: 0px !Important">
                                    <label><p:outputLabel for="nome" value="#{localemsgs.Nome}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                    <p:inputText id="nome" value="#{funcionario.novo.nome}" style="width: 100%"
                                                 required="true" label="#{localemsgs.Nome}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                                 maxlength="50">
                                    </p:inputText>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <label><p:outputLabel for="nome_guer" value="#{localemsgs.Nome_Guer}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                    <p:inputText id="nome_guer" value="#{funcionario.novo.nome_Guer}"
                                                 label="#{localemsgs.Nome_Guer}" disabled="#{funcionario.flag eq 2}"
                                                 maxlength="10"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome_Guer}"
                                                 style="width: 100%">
                                        <p:ajax event="blur" listener="#{funcionario.validarNomeGuer}"
                                                update="msgs formCadastrar:nome_guer"/>
                                    </p:inputText>   
                                </div>
                            </div>
                            <ul class="nav nav-tabs">
                                <li class="active"><a data-toggle="tab" href="#DadosOperacionais">#{localemsgs.DadosOperacionais}</a></li>
                                <li><a data-toggle="tab" href="#DadosPessoais">#{localemsgs.DadosPessoais}</a></li>
                                <li><a data-toggle="tab" href="#DadosFormacao">#{localemsgs.DadosFormacao}</a></li>
                            </ul>
                            <div class="tab-content">
                                <div id="DadosOperacionais" class="tab-pane fade in active" style="background-color: #FFF !important;background: #FFF !important;height: calc(100% - 10px) !important; padding: 0px !important">
                                    <p:panel class="col-md-12 col-sm-12 col-xs-12" id="pnlDados" style="padding: 8px !important; height: calc(100vh - 452px) !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;border: thin solid #DDD !important; border-top:none !important">
                                        <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding: 0px !important; margin: 0px !important">
                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                <label><p:outputLabel for="dataAdmissao" value="#{localemsgs.DataAdmissao}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                                <p:calendar id="dataAdmissao" value="#{funcionario.novo.dt_Admis}" 
                                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                            style="width: 100%;" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataAdmissao}" />
                                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                <label><p:outputLabel for="registroDRI" value="#{localemsgs.RegistroDRI}"/></label>
                                                <p:inputText id="registroDRI" value=""
                                                             style="width: 100%"
                                                             maxlength="15">

                                                </p:inputText>    
                                            </div>
                                            <div class="col-md-4 col-sm-4 col-xs-12">
                                                <label><p:outputLabel for="apresentadoPor" value="#{localemsgs.ApresentadoPor}"/></label>
                                                <p:inputText id="apresentadoPor" value="#{funcionario.novo.apresen}"
                                                             style="width: 100%"
                                                             maxlength="15">

                                                </p:inputText>     
                                            </div>
                                            <div class="col-md-4 col-sm-4 col-xs-12">
                                                <label><p:outputLabel for="subFil" value="#{localemsgs.Filial}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                                <p:selectOneMenu id="subFil" value="#{funcionario.filialFuncion}" converter="omnifaces.SelectItemsConverter"
                                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                                 filter="true" filterMatchMode="contains"
                                                                 style="width: 100%">
                                                    <p:ajax event="itemSelect" listener="#{funcionario.atualizaHorarios}" update="formCadastrar:horario formCadastrar:pstserv"/>
                                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                                   itemLabel="#{filial.descricao}"/>
                                                </p:selectOneMenu>
                                            </div>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="regional" value="#{localemsgs.Regional}"/>
                                            <p:selectOneMenu id="regional" required="true"
                                                             value="#{funcionario.novo.regional}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Regional}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>

                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <label><p:outputLabel for="pstserv" value="#{localemsgs.PstServ}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:selectOneMenu id="pstserv" value="#{funcionario.novo.secao}" converter="omnifaces.SelectItemsConverter"
                                                             required="true" 
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PstServ}"
                                                             filter="true" filterMatchMode="contains"
                                                             style="width: 100%">
                                                <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Selecione}" />
                                                <f:selectItems value="#{funcionario.listaPostos}" var="pstserv" itemValue="#{pstserv.secao}"
                                                               itemLabel="#{pstserv.secao}, #{pstserv.local}: #{pstserv.descContrato}" noSelectionValue=""/>
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="posto" value="#{localemsgs.CCusto}"/>
                                            <p:selectOneMenu id="posto" required="true"
                                                             value="#{funcionario.novo.CCusto}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CCusto}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <p:outputLabel for="turnoEscala" value="#{localemsgs.Escala}"/>
                                            <p:selectOneMenu id="turnoEscala" required="true"
                                                             value="#{funcionario.novo.escala}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Escala}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>

                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <label><p:outputLabel for="horario" value="#{localemsgs.Horario}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:selectOneMenu id="horario" value="#{funcionario.rhHorario}" converter="omnifaces.SelectItemsConverter"
                                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Horario}"
                                                             filter="true" filterMatchMode="contains"
                                                             style="width: 100%">
                                                <f:selectItems value="#{funcionario.rhHorarios}" var="rhHorario" itemValue="#{rhHorario}"
                                                               itemLabel="#{rhHorario.descricao}"/>
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <label><p:outputLabel for="cargo" value="#{localemsgs.Cargo}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:selectOneMenu id="cargo" value="#{funcionario.cargo}" converter="omnifaces.SelectItemsConverter"
                                                             required="true" 
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cargo}"
                                                             filter="true" filterMatchMode="contains"
                                                             style="width: 100%">
                                                <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Selecione}" />
                                                <f:selectItems value="#{funcionario.listaCargos}" var="cargos" itemValue="#{cargos.cargo}"
                                                               itemLabel="#{cargos.descricao}" noSelectionValue=""/>
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <p:outputLabel for="sindicato" value="#{localemsgs.Sindicato}"/>
                                            <p:selectOneMenu id="sindicato" required="true"
                                                             value="#{funcionario.novo.sindicato}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Sindicato}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="grupo" value="#{localemsgs.Grupo}"/>
                                            <p:selectOneMenu id="grupo" required="true"
                                                             value="#{funcionario.novo.grupoSang}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Grupo}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>

                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <label><p:outputLabel for="funcao" value="#{localemsgs.Funcao}"  />  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:selectOneMenu
                                                value="#{funcionario.novo.funcao}"
                                                id="funcao"
                                                required="true"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Funcao}"
                                                style="width: 100%;">
                                                <f:selectItem itemLabel="#{localemsgs.Motorista}" itemValue="M"/>
                                                <f:selectItem itemLabel="#{localemsgs.ChefeEquipe}" itemValue="C"/>
                                                <f:selectItem itemLabel="#{localemsgs.Vigilante}" itemValue="V"/>
                                                <f:selectItem itemLabel="#{localemsgs.Patrimonial}" itemValue="P"/>
                                                <f:selectItem itemLabel="#{localemsgs.Todos}" itemValue="T"/>
                                                <f:selectItem itemLabel="#{localemsgs.Outros}" itemValue="O"/>
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="cep" value="#{localemsgs.CEP}"/>
                                            <p:inputText id="cep" value="#{funcionario.novo.CEP}" 
                                                         style="width: 100%"
                                                         label="#{localemsgs.CEP}" maxlength="8">
                                            </p:inputText>

                                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                                           partialSubmit="true" process="@this formCadastrar:cep" id="cep_pesquisa" 
                                                           update="formCadastrar:cep formCadastrar:ende formCadastrar:bairro formCadastrar:cidade formCadastrar:uf msgs"
                                                           actionListener="#{funcionario.pessoaMB.Endereco}" rendered="#{localeController.number eq 1}">
                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="24" style="position: absolute; right: 12px; margin-top: 4px;"/>
                                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.AtualizarCEP}" icon="ui-icon-alert" />
                                                <p:confirmDialog global="true" >
                                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                                </p:confirmDialog>
                                                <p:dialog header="#{localemsgs.Aviso}" widgetVar="dlgOk" resizable="false"
                                                          draggable="false" closable="true" width="300">
                                                    <div class="form-inline">
                                                        <h:outputText value="#{localemsgs.CompletarEndereco}" style="text-align: center"/>
                                                        <p:spacer height="20px"/> 
                                                    </div>
                                                    <p:commandButton value="#{localemsgs.OK}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                                     onclick="PF('dlgOk').hide();" />
                                                </p:dialog>
                                            </p:commandLink>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <label><p:outputLabel for="ende" value="#{localemsgs.Endereco}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:inputText id="ende" value="#{funcionario.novo.endereco}" 
                                                         required="true" label="#{localemsgs.Endereco}"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Endereco}"
                                                         style="width: 100%"
                                                         maxlength="50">
                                            </p:inputText>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <label><p:outputLabel for="bairro" value="#{localemsgs.Bairro}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:inputText id="bairro" value="#{funcionario.novo.bairro}" 
                                                         style="width: 100%"
                                                         required="true" label="#{localemsgs.Bairro}"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Bairro}"
                                                         maxlength="20" />
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <label><p:outputLabel for="cidade" value="#{localemsgs.Cidade}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:autoComplete id="cidade" value="#{funcionario.novo.cidade}" styleClass="cidade" 
                                                            required="true" placeholder="#{localemsgs.Cidade}" completeMethod="#{funcionario.pessoaMB.BuscarCidade}"
                                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}" scrollHeight="200"
                                                            style="width: 100%" maxlength="25" forceSelection="true" >
                                                <p:ajax event="itemSelect" listener="#{funcionario.selecionarCidade}"
                                                        update="formCadastrar:cidade formCadastrar:uf"/>
                                            </p:autoComplete>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <label><p:outputLabel for="uf" value="#{localemsgs.UF}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                            <p:inputText id="uf" value="#{funcionario.novo.UF}" disabled="true"
                                                         required="true" style="width: 100%"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.UF}"
                                                         label="#{localemsgs.UF}" maxlength="2">
                                            </p:inputText>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="fone1" value="#{localemsgs.Fone1}" />
                                            <p:inputMask id="fone1" mask="#{mascaras.mascaraFone}" value="#{funcionario.pessoaMB.novaPessoa.fone1}"
                                                         style="width: 100%" maxlength="11">
                                            </p:inputMask>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="fone2" value="#{localemsgs.Fone2}"/>
                                            <p:inputMask id="fone2" mask="#{mascaras.mascaraFone}" value="#{funcionario.pessoaMB.novaPessoa.fone2}" 
                                                         maxlength="11" style="width: 100%">
                                            </p:inputMask>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                            <label style="margin:0px !important"><p:outputLabel for="dt_exame" value="#{localemsgs.dtVencimentoExameMedico}" />  <font style="font-weight: bold; color: red">(*)</font></label>

                                            <p:calendar id="dt_exame" value="#{funcionario.novo.dt_ExameMe}" yearRange="1900:2020"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.dtVencimentoExameMedico}" />
                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                            <label style="margin:0px !important"><p:outputLabel for="dt_venc_psicotecnico" value="#{localemsgs.dtVencimentoPsicotecnico}" />  <font style="font-weight: bold; color: red">(*)</font></label>

                                            <p:calendar id="dt_venc_psicotecnico" value="#{funcionario.novo.dt_Psico}" yearRange="1900:2020"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.dtVencimentoPsicotecnico}" />
                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="rgorg" value="#{localemsgs.RGOrg}"/>
                                            <p:inputText id="rgorg" value="#{funcionario.pessoaMB.novaPessoa.RGOrgEmis}" 
                                                         label="#{localemsgs.RGOrg}" style="width: 100%"
                                                         maxlength="15">
                                                <p:ajax event="change" process="@this" listener="#{funcionario.GerarRG}" update="rg"></p:ajax>
                                            </p:inputText>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="rg" value="#{localemsgs.RG}"/>
                                            <p:inputText id="rg" value="#{funcionario.novo.RG}"
                                                         label="#{localemsgs.RG}" style="width: 100%"
                                                         maxlength="20">
                                            </p:inputText>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                            <label style="margin:0px !important"><p:outputLabel for="dt_emissao_rg" value="#{localemsgs.dtRgEmissao}" />  <font style="font-weight: bold; color: red">(*)</font></label>

                                            <p:calendar id="dt_emissao_rg" value="#{funcionario.novo.rgDtEmis}" yearRange="1900:2020"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.dtRgEmissao}" />
                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12" style="display:#{localeController.number eq 1?'':'none'}">
                                            <p:outputLabel for="cpf" value="#{localemsgs.CPF}"/>
                                            <p:inputMask id="cpf" value="#{funcionario.novo.CPF}" disabled="#{funcionario.flag eq 2}"
                                                         maxlength="11" style="width: 100%" mask="#{mascaras.mascaraCPF}">
                                                <p:ajax event="blur" listener="#{funcionario.BuscarPessoa}"
                                                        update="formCadastrar:cep_pesquisa msgs"/>
                                            </p:inputMask>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="nacionalidade" value="#{localemsgs.Nacionalidade}"/>
                                            <p:selectOneMenu id="nacionalidade" required="true"
                                                             value="#{funcionario.novo.nacionalid}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nacionalidade}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="experiencia" value="#{localemsgs.Experiencia}"/>
                                            <p:selectOneMenu id="experiencia" required="true"
                                                             value="#{funcionario.novo.expGESP}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Experiencia}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding:0px !important; margin:0px !important;">
                                            <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                                <label style="margin:0px !important"><p:outputLabel for="dt_experiencia_primeiro" value="#{localemsgs.PrimeiroPeriodo}" />  <font style="font-weight: bold; color: red">(*)</font></label>

                                                <p:calendar id="dt_experiencia_primeiro" value="" yearRange="1900:2020"
                                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                            style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PrimeiroPeriodo}" />
                                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                                <label style="margin:0px !important"><p:outputLabel for="dt_experiencia_segundo" value="#{localemsgs.SegundoPeriodo}" />  <font style="font-weight: bold; color: red">(*)</font></label>

                                                <p:calendar id="dt_experiencia_segundo" value="" yearRange="1900:2020"
                                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                            style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.SegundoPeriodo}" />
                                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                            </div>
                                            <div class="col-md-8 col-sm-8 col-xs-12">
                                                <p:outputLabel for="obs" value="#{localemsgs.Obs}" />
                                                <p:inputText id="obs" value="#{funcionario.pessoaMB.novaPessoa.obs}" 
                                                             label="#{localemsgs.Obs}" style="width: 100%"
                                                             maxlength="60">
                                                </p:inputText>
                                            </div>
                                        </div>
                                    </p:panel> 
                                </div>
                                <div id="DadosPessoais" class="tab-pane fade" style="background-color: #FFF !important;background: #FFF !important;height: calc(100% - 10px) !important; padding: 0px !important">
                                    <p:panel class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px !important; height: calc(100vh - 452px) !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;border: thin solid #DDD !important; border-top:none !important">
                                        <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                            <label style="margin:0px !important"><p:outputLabel for="dt_nasc" value="#{localemsgs.Dt_Nasc}" />  <font style="font-weight: bold; color: red">(*)</font></label>

                                            <p:calendar id="dt_nasc" value="#{funcionario.novo.dt_Nasc}" yearRange="1900:2020"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Dt_Nasc}" />
                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                        </div>

                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="naturalidade" value="#{localemsgs.Naturalidade}"/>
                                            <p:inputText id="naturalidade" value="#{funcionario.novo.naturalid}"
                                                         label="#{localemsgs.RG}" style="width: 100%"
                                                         maxlength="20">
                                            </p:inputText>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="sexo" value="#{localemsgs.Sexo}"/>
                                            <p:selectOneMenu value="#{funcionario.novo.sexo}"
                                                             id="sexo"

                                                             style="width: 100%;">
                                                <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                                <f:selectItem itemLabel="#{localemsgs.Masculino}" itemValue="M"/>
                                                <f:selectItem itemLabel="#{localemsgs.Feminino}" itemValue="F"/>
                                                <f:selectItem itemLabel="#{localemsgs.Outros}" itemValue="O"/>
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="raca" value="#{localemsgs.Raca}"/>
                                            <p:selectOneMenu id="raca" required="true"
                                                             value="#{funcionario.novo.raca}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Raca}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="instrucao" value="#{localemsgs.Instrucao}"/>
                                            <p:selectOneMenu id="instrucao" required="true"
                                                             value="#{funcionario.novo.raca}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Instrucao}"
                                                             style="width: 100%;">

                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="estado_civil" value="#{localemsgs.EstadoCivil}"/>
                                            <p:selectOneMenu id="estado_civil" required="true"
                                                             value="#{funcionario.novo.estCivil}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.EstadoCivil}"
                                                             style="width: 100%;">
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding: 0px !important; margin: 0px !important;">
                                            <div class="col-md-4 col-sm-4 col-xs-12">
                                                <p:outputLabel for="conjuge" value="#{localemsgs.Conjuge}"/>
                                                <p:inputText id="conjuge" value="#{funcionario.novo.conjuge}"
                                                             label="#{localemsgs.RG}" style="width: 100%"
                                                             maxlength="20">
                                                </p:inputText>
                                            </div>
                                            <div class="col-md-4 col-sm-4 col-xs-12">
                                                <p:outputLabel for="pai" value="#{localemsgs.Pai}"/>
                                                <p:inputText id="pai" value="#{funcionario.novo.pai}"
                                                             label="#{localemsgs.RG}" style="width: 100%"
                                                             maxlength="20">
                                                </p:inputText>
                                            </div>
                                            <div class="col-md-4 col-sm-4 col-xs-12">
                                                <p:outputLabel for="mae" value="#{localemsgs.Mae}"/>
                                                <p:inputText id="mae" value="#{funcionario.novo.mae}"
                                                             label="#{localemsgs.RG}" style="width: 100%"
                                                             maxlength="20">
                                                </p:inputText>
                                            </div>
                                        </div>
                                        <div class="col-md-8 col-sm-8 col-xs-12">
                                            <p:outputLabel for="email" value="#{localemsgs.Email}" />
                                            <p:inputText id="email" value="#{funcionario.novo.email}"
                                                         label="#{localemsgs.Email}" style="width: 100%"
                                                         maxlength="50" >
                                            </p:inputText>
                                        </div>


                                        <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding: 0px !important; margin: 10px 0px 0px 6px !important; width: calc(100% - 10px) !important">
                                            <ul class="nav nav-tabs">
                                                <li class="active"><a data-toggle="tab" href="#Documentacao">#{localemsgs.Documentacao}</a></li>
                                                <li><a data-toggle="tab" href="#DadosFolha">#{localemsgs.DadosFolha}</a></li>
                                                <li><a data-toggle="tab" href="#InformacoesAdicionais">#{localemsgs.InformacoesAdicionais}</a></li>
                                            </ul>
                                            <div class="tab-content">
                                                <div id="Documentacao" class="tab-pane fade in active" style="background-color: #FFF !important;background: #FFF !important;height: calc(100% - 10px) !important; padding: 0px !important">
                                                    <p:panel class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px !important; height: 280px !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;border: thin solid #DDD !important; border-top:none !important">
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="pis" value="#{localemsgs.PIS}" />
                                                            <p:inputText id="pis" value="#{funcionario.novo.PIS}" 
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.PIS}" maxlength="4">
                                                                <f:convertNumber pattern="000"/>
                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="ctps_num" value="#{localemsgs.CTPS_Nro}" />
                                                            <p:inputText id="ctps_num" value="#{funcionario.novo.CTPS_Nro}" 
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.CTPS_Nro}">

                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="ctps_serie" value="#{localemsgs.CTPS_Serie}" />
                                                            <p:inputText id="ctps_serie" value="#{funcionario.novo.CTPS_Serie}" 
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.CTPS_Serie}">

                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="ctps_uf" value="#{localemsgs.UF}" />
                                                            <p:inputText id="ctps_uf" value="#{funcionario.novo.CTPS_UF}" 
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.UF}">

                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="ctps_emissao" value="#{localemsgs.Emissao}" />
                                                            <p:calendar id="ctps_emissao" value="#{funcionario.novo.CTPS_Emis}" 
                                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Emissao}" style="width: 100%; text-align:left !important;">
                                                            </p:calendar>
                                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="cnh" value="#{localemsgs.CNH}" />
                                                            <p:inputText id="cnh" value="#{funcionario.novo.CNH}" 
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.CNH}">

                                                            </p:inputText>
                                                        </div>

                                                        <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding: 0px !important; margin: 0px !important">
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="cnh_validade" value="#{localemsgs.DtValidade}" />
                                                                <p:inputText id="cnh_validade" value="#{funcionario.novo.dt_VenCNH}" 
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.DtValidade}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="cnh_categoria" value="#{localemsgs.Categoria}" />
                                                                <p:inputText id="cnh_categoria" value="#{funcionario.novo.categoria}" 
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.Categoria}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="cnh_uf" value="#{localemsgs.UF}" />
                                                                <p:inputText id="cnh_uf" value="#{funcionario.novo.UF}" 
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.UF}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="reservista" value="#{localemsgs.Reservista}" />
                                                                <p:inputText id="reservista" value="#{funcionario.novo.reservista}" 
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.Reservista}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="reservista_cat" value="#{localemsgs.Categoria}" />
                                                                <p:inputText id="reservista_cat" value="#{funcionario.novo.reservCat}" 
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.Categoria}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="exame_cnh_num" value="#{localemsgs.ExameCnhNum}" />
                                                                <p:inputText id="exame_cnh_num"  
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.ExameCnhNum}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="dt_exame_cnh" value="#{localemsgs.DtExameCnhNum}" />
                                                                <p:calendar id="dt_exame_cnh" 
                                                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                                            requiredMessage="#{localemsgs.Obrigatorio}: #{DtExameCnhNum.Dt_Admis}" style="width: 100%; text-align:left !important;">
                                                                </p:calendar>
                                                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="cnpj_lab" value="#{localemsgs.CNPJLab}" />
                                                                <p:inputText id="cnpj_lab" 
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.CNPJLab}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="lab_uf" value="#{localemsgs.UF}" />
                                                                <p:inputText id="lab_uf"
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.UF}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="lab_crm" value="#{localemsgs.CRM}" />
                                                                <p:inputText id="lab_crm"
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.CRM}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="titulo_eleitor" value="#{localemsgs.TituloEleitor}" />
                                                                <p:inputText id="titulo_eleitor" value="#{funcionario.novo.titEleit}" 
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.TituloEleitor}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="titulo_eleitor_zona" value="#{localemsgs.Zona}" />
                                                                <p:inputText id="titulo_eleitor_zona" value="#{funcionario.novo.titEZona}" 
                                                                             style="width: 100%"
                                                                             label="#{localemsgs.Zona}">

                                                                </p:inputText>
                                                            </div>
                                                            <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding: 0px !important; margin: 0px !important">
                                                                <div class="col-md-2 col-sm-2 col-xs-12">
                                                                    <p:outputLabel for="titulo_eleitor_secao" value="#{localemsgs.secao}" />
                                                                    <p:inputText id="titulo_eleitor_secao" value="#{funcionario.novo.titSecao}" 
                                                                                 style="width: 100%"
                                                                                 label="#{localemsgs.secao}">

                                                                    </p:inputText>
                                                                </div>
                                                                <div class="col-md-2 col-sm-2 col-xs-12">
                                                                    <p:outputLabel for="grupo_sanguineo" value="#{localemsgs.GrupoSanguineo}" />
                                                                    <p:selectOneMenu id="grupo_sanguineo" 
                                                                                     value="#{funcionario.novo.grupoSang}"
                                                                                     style="width: 100%;">

                                                                    </p:selectOneMenu>
                                                                </div>
                                                                <div class="col-md-2 col-sm-2 col-xs-12">
                                                                    <p:outputLabel for="altura" value="#{localemsgs.Altura}" />
                                                                    <p:inputText id="altura" value="#{funcionario.pessoaMB.novaPessoa.altura}" 
                                                                                 style="width: 100%"
                                                                                 label="#{localemsgs.Altura}" maxlength="4">
                                                                        <f:convertNumber pattern="000"/>
                                                                    </p:inputText>
                                                                </div>
                                                                <div class="col-md-2 col-sm-2 col-xs-12">
                                                                    <p:outputLabel for="peso" value="#{localemsgs.Peso}"/>
                                                                    <p:inputText id="peso" value="#{funcionario.pessoaMB.novaPessoa.peso}" 
                                                                                 style="width: 100%"
                                                                                 label="#{localemsgs.Peso}" maxlength="3">
                                                                        <f:convertNumber pattern="000"/>
                                                                    </p:inputText>
                                                                </div>
                                                            </div>
                                                            <!--<div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="interfext" value="#{localemsgs.InterfExt}"/>
                                                                <p:inputText id="interfext" value="#{funcionario.novo.interfExt}"
                                                                             label="#{localemsgs.InterfExt}"  style="width: 100%">
                                                                </p:inputText>
                                                            </div>-->

                                                        </div>
                                                    </p:panel>
                                                </div>

                                                <div id="DadosFolha" class="tab-pane fade in" style="background-color: #FFF !important;background: #FFF !important;height: calc(100% - 10px) !important; padding: 0px !important">
                                                    <p:panel class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px !important; height: 280px !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;border: thin solid #DDD !important; border-top:none !important">
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="banco" value="#{localemsgs.Banco}" />
                                                            <p:inputText id="banco"  
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.Banco}">
                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-1 col-sm-1 col-xs-12">
                                                            <p:outputLabel for="agencia" value="#{localemsgs.Agencia}" />
                                                            <p:inputText id="agencia"  
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.Agencia}">
                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-1 col-sm-1 col-xs-12">
                                                            <p:outputLabel for="conta" value="#{localemsgs.Conta}" />
                                                            <p:inputText id="conta"  
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.Conta}">
                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="codigo_operacao" value="#{localemsgs.CodigoOperacao}" />
                                                            <p:inputText id="codigo_operacao"  
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.CodigoOperacao}">
                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="salario" value="#{localemsgs.Salario}" />
                                                            <p:inputText id="salario" value="#{funcionario.novo.salario}"
                                                                         style="width: 100%"
                                                                         label="#{localemsgs.Salario}">
                                                            </p:inputText>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="vinculo" value="#{localemsgs.Vinculo}"/>
                                                            <p:selectOneMenu id="vinculo"
                                                                             value="#{funcionario.novo.vinculo}"
                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Vinculo}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="jornada" value="#{localemsgs.Jornada}"/>
                                                            <p:inputNumber id="jornada" value="#{funcionario.novo.jornada}"
                                                                           style="width: 100%" decimalPlaces="0"
                                                                           label="#{localemsgs.Jornada}">
                                                            </p:inputNumber>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="tipoAdm" value="#{localemsgs.TipoAdm}"/>
                                                            <p:selectOneMenu id="tipoAdm"
                                                                             value="#{funcionario.novo.tipoAdm}"
                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoAdm}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="defFis" value="#{localemsgs.DefFis}"/>
                                                            <p:selectOneMenu id="defFis"
                                                                             value="#{funcionario.novo.defFis}"
                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DefFis}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="formaPagamento" value="#{localemsgs.FormasPagamento}"/>
                                                            <p:selectOneMenu id="formaPagamento"
                                                                             value="#{funcionario.novo.formaPgto}"
                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.FormasPagamento}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="contSind" value="#{localemsgs.ContSind}"/>
                                                            <p:selectOneMenu id="contSind"

                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.ContSind}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="inss" value="#{localemsgs.INSS}"/>
                                                            <p:selectOneMenu id="inss"

                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.INSS}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="imposto_renda" value="#{localemsgs.IRRF}"/>
                                                            <p:selectOneMenu id="imposto_renda"

                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.IRRF}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                    </p:panel>
                                                </div>

                                                <div id="InformacoesAdicionais" class="tab-pane fade in" style="background-color: #FFF !important;background: #FFF !important;height: calc(100% - 10px) !important; padding: 0px !important">
                                                    <p:panel class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px !important; height: 280px !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;border: thin solid #DDD !important; border-top:none !important">
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="natureza" value="#{localemsgs.NaturezaEstagio}" />
                                                            <p:selectOneMenu id="natureza"

                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NaturezaEstagio}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="nivel_estagio" value="#{localemsgs.NivelEstagio}" />
                                                            <p:selectOneMenu id="nivel_estagio"

                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NivelEstagio}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="previsao_termino" value="#{localemsgs.PrevisaoTermino}" />

                                                            <p:calendar id="previsao_termino" yearRange="1900:2020"
                                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PrevisaoTermino}" />
                                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>

                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="instituicao_ensino" value="#{localemsgs.InstituicaoEnsino}" />
                                                            <p:selectOneMenu id="instituicao_ensino"

                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.InstituicaoEnsino}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="agente_integracao" value="#{localemsgs.AgenteIntegracao}" />
                                                            <p:selectOneMenu id="agente_integracao"

                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.AgenteIntegracao}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                                            <p:outputLabel for="supervisor" value="#{localemsgs.Supervisor}" />
                                                            <p:selectOneMenu id="supervisor"

                                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.AgenteIntegracao}"
                                                                             style="width: 100%;">
                                                            </p:selectOneMenu>
                                                        </div>
                                                        <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding: 0px !important; margin: 0px !important">
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="tipo_reintegracao" value="#{localemsgs.TipoReintegracao}" />
                                                                <p:selectOneMenu id="tipo_reintegracao"

                                                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoReintegracao}"
                                                                                 style="width: 100%;">
                                                                </p:selectOneMenu>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="info_pagto_juizo" value="#{localemsgs.InfoPagtoJuizo}" />
                                                                <p:selectOneMenu id="info_pagto_juizo"

                                                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.InfoPagtoJuizo}"
                                                                                 style="width: 100%;">
                                                                </p:selectOneMenu>
                                                            </div>
                                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                                <p:outputLabel for="data_reintegracao" value="#{localemsgs.DataReintegracao}" />
                                                                <p:calendar id="data_reintegracao" yearRange="1900:2020"
                                                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                                            style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataReintegracao}" />
                                                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                                            </div>
                                                        </div>
                                                    </p:panel>
                                                </div>
                                            </div>
                                        </div>
                                    </p:panel>

                                </div>
                                <div id="DadosFormacao" class="tab-pane fade">
                                    <p:panel class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px !important; height: calc(100vh - 452px) !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;border: thin solid #DDD !important; border-top:none !important">
                                        <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                            <p:outputLabel for="dt_form_ini" value="#{localemsgs.DataFormacaoIni}" />
                                            <p:calendar id="dt_form_ini" yearRange="1900:2020"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataFormacaoIni}" />
                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                        </div>

                                        <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                            <p:outputLabel for="dt_form_fin" value="#{localemsgs.DataFormacaoFim}" />
                                            <p:calendar id="dt_form_fin" yearRange="1900:2020"
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataFormacaoFim}" />
                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                        </div>

                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <p:outputLabel for="local_formacao" value="#{localemsgs.LocalForm}" />
                                            <p:inputText id="local_formacao" value="#{funcionario.novo.localForm}"
                                                         style="width: 100%"
                                                         label="#{localemsgs.LocalForm}">
                                            </p:inputText>
                                        </div>

                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="reg_min_trabalho" value="#{localemsgs.RegMinTrabalho}" />
                                            <p:inputText id="reg_min_trabalho" value="#{funcionario.novo.reg_MT}"
                                                         style="width: 100%"
                                                         label="#{localemsgs.RegMinTrabalho}">
                                            </p:inputText>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="num_certificado" value="#{localemsgs.NCertificado}" />
                                            <p:inputText id="num_certificado" value="#{funcionario.novo.certific}"
                                                         style="width: 100%"
                                                         label="#{localemsgs.NCertificado}">
                                            </p:inputText>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding: 0px !important; margin: 0px !important">
                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                <p:outputLabel for="reg_policia_federal" value="#{localemsgs.RegPoliciaFederal}" />
                                                <p:inputText id="reg_policia_federal" value="#{funcionario.novo.reg_PF}"
                                                             style="width: 100%"
                                                             label="#{localemsgs.RegPoliciaFederal}">
                                                </p:inputText>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                <p:outputLabel for="reg_policia_federal_uf" value="#{localemsgs.UF}" />
                                                <p:selectOneMenu id="reg_policia_federal_uf"
                                                                 value="#{funcionario.novo.reg_PFUF}"
                                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.UF}"
                                                                 style="width: 100%;">
                                                </p:selectOneMenu>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                                <p:outputLabel for="reg_policia_federal_data" value="#{localemsgs.Data}" />
                                                <p:calendar id="reg_policia_federal_data" yearRange="1900:2020" value="#{funcionario.novo.reg_PFDt}"
                                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                            style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}" />
                                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                <p:outputLabel for="cnv" value="#{localemsgs.CNV}" />
                                                <p:inputText id="cnv" 
                                                             style="width: 100%"
                                                             label="#{localemsgs.CNV}">
                                                </p:inputText>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                                <p:outputLabel for="dt_validade_cnv" value="#{localemsgs.Data}" />
                                                <p:calendar id="dt_validade_cnv" yearRange="1900:2020" value="#{funcionario.novo.dtValCNV}"
                                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                            style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgsData}" />
                                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                                <p:outputLabel for="dt_emissao_cnv" value="#{localemsgs.DataEmissao}" />
                                                <p:calendar id="dt_emissao_cnv" yearRange="1900:2020" 
                                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                            style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataEmissao}" />
                                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                            </div>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                            <p:outputLabel for="dt_reciclagem" value="#{localemsgs.DataReciclagem}" />
                                            <p:calendar id="dt_reciclagem" yearRange="1900:2020" 
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataReciclagem}" />
                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12" style="position:relative;">
                                            <p:outputLabel for="dt_vencimento_curso" value="#{localemsgs.DataVencimentoCurso}" />
                                            <p:calendar id="dt_vencimento_curso" yearRange="1900:2020" 
                                                        converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                        locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                        style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataVencimentoCurso}" />
                                            <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="exportacao_gesp" value="#{localemsgs.ExportacaoGesp}" />
                                            <p:selectOneMenu id="exportacao_gesp"
                                                             value="#{funcionario.novo.expGESP}"
                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.ExportacaoGesp}"
                                                             style="width: 100%;">
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="cadastro_afis" value="#{localemsgs.CadastroAfis}" />
                                            <p:selectOneMenu id="cadastro_afis"

                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CadastroAfis}"
                                                             style="width: 100%;">
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="transporte_valores" value="#{localemsgs.TransporteValores}" />
                                            <p:selectOneMenu id="transporte_valores"

                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TransporteValores}"
                                                             style="width: 100%;">
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-2 col-sm-2 col-xs-12">
                                            <p:outputLabel for="seguranca_pessoal" value="#{localemsgs.SegurancaPessoal}" />
                                            <p:selectOneMenu id="seguranca_pessoal"

                                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.SegurancaPessoal}"
                                                             style="width: 100%;">
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding: 0px !Important; margin: 0px !important">
                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                <p:outputLabel for="escolta_armada" value="#{localemsgs.EscoltaArmada}" />
                                                <p:selectOneMenu id="escolta_armada"

                                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.EscoltaArmada}"
                                                                 style="width: 100%;">
                                                </p:selectOneMenu>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                <p:outputLabel for="grandes_eventos" value="#{localemsgs.GrandesEventos}" />
                                                <p:selectOneMenu id="grandes_eventos"
                                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.GrandesEventos}"
                                                                 style="width: 100%;">
                                                </p:selectOneMenu>
                                            </div>
                                            <div class="col-md-2 col-sm-2 col-xs-12">
                                                <p:outputLabel for="armas_nao_letais" value="#{localemsgs.ArmasNaoLetais}" />
                                                <p:selectOneMenu id="armas_nao_letais"
                                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.ArmasNaoLetais}"
                                                                 style="width: 100%;">
                                                </p:selectOneMenu>
                                            </div>
                                        </div>
                                    </p:panel>
                                </div>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; margin-top: 8px !important">
                                <p:commandLink rendered="#{funcionario.flag eq 1}" id="cadastro" action="#{funcionario.Cadastrar}"
                                               update=":msgs :main:tabela cabecalho formCadastrar:cadastrar"
                                               title="#{localemsgs.Cadastrar}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                                <p:commandLink rendered="#{funcionario.flag eq 2}" id="editar" action="#{funcionario.Cadastrar}"
                                               update=":msgs :main:tabela cabecalho formCadastrar:cadastrar"
                                               title="#{localemsgs.Editar}" styleClass="btn btn-primary">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
                
                <!--Geração do link de configuração do EW-->
                <h:form id="formConfiguracaoEW">
                    <p:hotkey bind="esc" oncomplete="PF('dlgConfiguracaoEW').hide()"/>
                    
                    <p:remoteCommand name="preConfiguracaoFuncionEW" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs formConfiguracaoEW" 
                                         oncomplete="PF('dlgConfiguracaoEW').show();"
                                         actionListener="#{funcionario.preConfiguracaoFuncionEW}" />  

                    <p:remoteCommand name="enviarConfiguracaoFuncionEW" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs formConfiguracaoEW" 
                                         actionListener="#{funcionario.enviarConfiguracaoFuncionEW}" />    
                    <p:dialog
                        widgetVar="dlgConfiguracaoEW" positionType="absolute" responsive="true" focus="opcao" draggable="false" modal="true"
                        closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; 
                        box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;
                        border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; 
                        padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarFuncion}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="panelConfiguracoesEW" style="background: transparent">                            
                            <p:outputLabel for="funcion" value="#{localemsgs.Funcion}"/>
                            <p:autoComplete id="funcion" value="#{funcionario.configuracaoEWFuncion}" styleClass="pstserv"
                                            label="#{localemsgs.Funcion}" placeholder="#{localemsgs.Funcion}" completeMethod="#{funcionario.buscarFuncion}" 
                                            style="width: 100%;" minQueryLength="3" scrollHeight="200" forceSelection="true"
                                            var="func" itemValue="#{func}" itemLabel="#{func.nome}" disabled="#{funcionario.configuracaoEWLiberarFuncion}">
                                <p:ajax event="itemSelect" listener="#{funcionario.selectionarFuncion}" update="funcion filial enviar cliente msgs" />
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{funcionario.configuracaoEWFuncionList}" />
                                <p:watermark for="funcion" value="#{localemsgs.Funcion}"/>
                            </p:autoComplete>
                            
                            <p:outputLabel for="filial" value="#{localemsgs.Filial}"/>
                            <p:inputText id="filial" value="#{funcionario.configuracaoEWFilial.descricao}" styleClass="pstserv" disabled="true"
                                            label="#{localemsgs.Filial}"
                                            style="width: 100%;">
                                <p:watermark for="filial" value="#{localemsgs.Filial}"/>
                            </p:inputText>
                            
                            <p:outputLabel for="cliente" value="#{localemsgs.Cliente}"/>
                            <p:autoComplete id="cliente" value="#{funcionario.configuracaoEWCliente}" styleClass="pstserv"
                                            label="#{localemsgs.Cliente}" placeholder="#{localemsgs.Cliente}" disabled="#{funcionario.configuracaoEWLiberarCliente}"
                                            completeMethod="#{funcionario.buscarCliente}" scrollHeight="200"
                                            style="width: 100%;" forceSelection="true" minQueryLength="3" 
                                            var="cli" itemValue="#{cli}" itemLabel="#{cli.codExt}">
                                <p:ajax event="itemSelect" listener="#{funcionario.selectionarCliente}" update="cliente pstserv enviar msgs" />
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{funcionario.configuracaoEWClienteList}" />
                            </p:autoComplete>
                            
                            <p:outputLabel for="pstserv" value="#{localemsgs.PstServ}"/>
                            <p:selectOneMenu id="pstserv" value="#{funcionario.configuracaoEWPstServ}" converter="omnifaces.SelectItemsConverter" 
                                             styleClass="pstserv" required="true"  disabled="#{funcionario.configuracaoEWLiberarPosto}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PstServ}" label="#{localemsgs.PstServ}"
                                             filter="true" filterMatchMode="contains" placeholder="#{localemsgs.PstServ}">
                                <p:ajax listener="#{funcionario.selectionarPstServ}" update="cliente pstserv enviar cancelar panelConfiguracoesEW msgs" />
                                <f:selectItems value="#{funcionario.configuracaoEWPstServList}" var="pst" itemValue="#{pst}"
                                               itemLabel="#{pst.descricao}"  noSelectionValue="Selecione"/>
                                <p:watermark for="pstserv" value="#{localemsgs.PstServ}"/>
                            </p:selectOneMenu>
                            

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-top: 10px !important; padding-right:0px !important">

                                <p:commandLink id="cancelar" actionListener="#{funcionario.preConfiguracaoFuncionEW}"
                                               oncomplete="PF('dlgConfiguracaoEW').show();"
                                               rendered="#{funcionario.configuracaoEWLiberarPosto 
                                                           and funcionario.configuracaoEWLiberarCliente 
                                                           and funcionario.configuracaoEWLiberarFuncion}"
                                               update=":msgs formConfiguracaoEW" style="margin: 10px;"
                                               title="#{localemsgs.Cadastrar}" styleClass="btn btn-danger">
                                    <i class="fa fa-times" style="margin-right:8px !important"></i>#{localemsgs.Cancelar}
                                </p:commandLink>

                                <p:commandLink id="enviar" action="#{funcionario.alertaWhatsApp}" 
                                               rendered="#{funcionario.configuracaoEWLiberarPosto 
                                                           and funcionario.configuracaoEWLiberarCliente 
                                                           and funcionario.configuracaoEWLiberarFuncion}"
                                               update=":msgs formConfiguracaoEW" style="margin: 10px;"
                                               title="#{localemsgs.Enviar}" styleClass="btn btn-primary">
                                    <i class="fa fa-check" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                            
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisa rápida funcionário-->
                <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarFuncion}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelPesquisaRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{funcionario.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Matr}" itemValue="MATR" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome_Guer}" itemValue="NOME_GUER" />
                                        <f:selectItem itemLabel="#{localemsgs.PstServ}" itemValue="LOCAL" />
                                        <f:selectItem itemLabel="#{localemsgs.PIS}" itemValue="PIS" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'MATR'}" value="#{localemsgs.Matr}: "/>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'NOME'}" value="#{localemsgs.Nome}: "/>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'NOME_GUER'}" value="#{localemsgs.Nome_Guer}: "/>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'LOCAL'}" value="#{localemsgs.PstServ}: "/>
                                        <p:outputLabel for="opcao" rendered="#{funcionario.chavePesquisa eq 'PIS'}" value="#{localemsgs.PIS}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{funcionario.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-right:0px !important">

                                <p:commandLink id="botaoPesquisaRapida" action="#{funcionario.pesquisarUnico}" oncomplete="PF('dlgPesquisaRapida').hide()"
                                               update=" :main:tabela :msgs cabecalho" styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-search" style="margin-right:8px !important"></i>#{localemsgs.Pesquisar}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                </script>

                <!--Pesquisar funcionário-->
                <h:form id="formPesquisar">
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarFuncion}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel value="#{localemsgs.CodFil}: " for="codfil"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil"
                                                     value="#{funcionario.selecionado.funcion.codFil}"
                                                     converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true"
                                                     filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel value="#{localemsgs.Nome}: " for="nome"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome" value="#{funcionario.selecionado.funcion.nome}" label="#{localemsgs.Nome}"
                                                 maxlength="50" style="width:100%">
                                        <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nome_guer" value="#{localemsgs.Nome_Guer}:"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome_guer" value="#{funcionario.selecionado.funcion.nome_Guer}"
                                                 label="#{localemsgs.Nome_Guer}"
                                                 style="width:100%">

                                        <p:watermark for="nome_guer" value="#{localemsgs.Nome_Guer}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel value="#{localemsgs.Matr}: " for="matr"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="matr" value="#{funcionario.selecionado.funcion.matr}" label="#{localemsgs.Matr}"
                                                 maxlength="50" style="width:100%">
                                        <p:watermark for="matr" value="#{localemsgs.Matr}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left;">
                                    <p:outputLabel for="situacao2" value="#{localemsgs.Situacao}:" />
                                </div>
                                <div style="width: 75%; float: left;">
                                    <p:selectOneRadio id="situacao2"
                                                      value="#{funcionario.selecionado.funcion.situacao}"
                                                      style="width: 100%;">
                                        <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                        <f:selectItem itemLabel="#{localemsgs.Demitido}" itemValue="D" />
                                    </p:selectOneRadio>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="pstserv" value="#{localemsgs.PstServ}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:autoComplete id="pstserv" value="#{funcionario.selecionado.pstserv.local}" styleClass="pstserv"
                                                    label="#{localemsgs.cidade}" completeMethod="#{funcionario.BuscarPstServ}" scrollHeight="200"
                                                    style="width: 100%;">
                                        <p:watermark for="pstserv" value="#{localemsgs.PstServ}"/>
                                    </p:autoComplete>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{funcionario.PesquisaPaginada}" update=" :main:tabela :msgs"
                                               title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisar').hide()">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Exportar-->
                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" width="400"
                          style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                    </f:facet>
                    <h:form class="form-inline">
                        <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                        <p:separator />
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="filial" value="#{funcionario.eFilial}">
                                    <p:ajax update="labelFilial"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{funcionario.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="matr" value="#{funcionario.eMatr}">
                                    <p:ajax update="labelMatr"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelMatr" value="#{localemsgs.Matr}" style="#{funcionario.eMatr eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="nome" value="#{funcionario.eNome}">
                                    <p:ajax update="labelNome"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelNome" value="#{localemsgs.Nome}" style="#{funcionario.eNome eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="email" value="#{funcionario.eEmail}">
                                    <p:ajax update="labelEmail"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelEmail" value="#{localemsgs.Email}" style="#{funcionario.eEmail eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cpf" value="#{funcionario.eCPF}">
                                    <p:ajax update="labelCPF"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCPF" value="#{localemsgs.CPF}" style="#{funcionario.eCPF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="rg" value="#{funcionario.eRG}">
                                    <p:ajax update="labelRG"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelRG" value="#{localemsgs.RG}" style="#{funcionario.eRG ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="orgemis" value="#{funcionario.eOrgEmis}">
                                    <p:ajax update="labelOrgEmis"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelOrgEmis" value="#{localemsgs.RGOrg}" style="#{funcionario.eOrgEmis eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="NomeGuer" value="#{funcionario.eNomeGuer}">
                                    <p:ajax update="eNomeGuer"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eNomeGuer" value="#{localemsgs.Nome_Guer}" style="#{funcionario.eNomeGuer eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="nasc" value="#{funcionario.eDtNasc}">
                                    <p:ajax update="labelNasc"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelNasc" value="#{localemsgs.Dt_Nasc}" style="#{funcionario.eDtNasc eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="sit" value="#{funcionario.eSituacao}">
                                    <p:ajax update="labelSit"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSit" value="#{localemsgs.Situacao}" style="#{funcionario.eSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="eDtSituacao" value="#{funcionario.eDtSituacao}">
                                    <p:ajax update="labelSituacao"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelSituacao" value="#{localemsgs.Dt_Situac}" style="#{funcionario.eDtSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="eDtAdmis" value="#{funcionario.eDtAdmis}">
                                    <p:ajax update="labelDtAdmis"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDtAdmis" value="#{localemsgs.Dt_Admis}" style="#{funcionario.eDtAdmis eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="posto" value="#{funcionario.ePosto}">
                                    <p:ajax update="labelPosto"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelPosto"  value="#{localemsgs.Posto}" style="#{funcionario.ePosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="CodPosto" value="#{funcionario.eCodPosto}">
                                    <p:ajax update="eCodPosto"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eCodPosto"  value="#{localemsgs.Secao}" style="#{funcionario.eCodPosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="CodPessoaWeb" value="#{funcionario.eCodPessoaWeb}">
                                    <p:ajax update="eCodPessoaWeb"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eCodPessoaWeb"  value="#{localemsgs.CodPessoa}" style="#{funcionario.eCodPessoaWeb eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="InterfExt" value="#{funcionario.eInterfExt}">
                                    <p:ajax update="eInterfExt"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eInterfExt" value="#{localemsgs.InterfExt}" style="#{funcionario.eInterfExt eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="hralter" value="#{funcionario.eHrAlter}">
                                    <p:ajax update="labelHrAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{funcionario.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="dtalter" value="#{funcionario.eDtAlter}">
                                    <p:ajax update="labelDtAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{funcionario.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="operador" value="#{funcionario.eOperador}">
                                    <p:ajax update="labelOperador"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{funcionario.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <p:separator />
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:panel style="text-align: center;background-color:#EEE;">
                                <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                                <h:commandLink id="pdf" actionListener="#{funcionario.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_pdf.png" style="height:32px;"/>
                                    <p:dataExporter target="main:tabela" type="pdf" fileName="#{localemsgs.Funcionarios}"
                                                    preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                                </h:commandLink>
                            </p:panel>

                            <p:panel style="text-align: center;background-color:#EEE;">
                                <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                                <h:commandLink id="xlsx" actionListener="#{funcionario.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_xls.png" style="height:32px;"/>
                                    <p:dataExporter target="main:tabela" type="xlsx" fileName="#{localemsgs.Funcionarios}"/>
                                </h:commandLink>
                            </p:panel>
                        </p:panelGrid>
                    </h:form>
                </p:dialog>

            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <script type="text/javascript">
                // <![CDATA[
                function CarregarDadosAproveitamento(inCodigoPessoa) {
                    $JanelaFormClientes.close();
                    $('[id*="txtReaproveitaPessoal"]').val(inCodigoPessoa);
                    rcPessoa();
                }

                function GerarMatriculaAtutomatica() {
                    $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}',
                            '#{localemsgs.DesejaGerarMatrAuto}',
                            '#{localemsgs.Sim}',
                            '#{localemsgs.Nao}',
                            function () {
                                rcGerarMatrAuto();
                            },
                            function () {
                                rcNaoGerarMatrAuto();
                            });
                }
                
                function AlertaEnvioWhatsapp(mensagem){
                    console.log(mensagem);
                    $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}',
                                        mensagem,
                                        '#{localemsgs.Sim}',
                                        '#{localemsgs.Nao}',
                                        function(){
                                            enviarConfiguracaoFuncionEW();
                                        },
                                        function(){
                                            preConfiguracaoFuncionEW();
                                        });
                }

                $(document)
                        .on('mousedown', '#btnAtalhoPessoa', function () {
                            let Altura = $('body').height() - 250;

                            let HTML = '<iframe id="ifrPessoas" src="pessoas.xhtml?selecao=S" style="border:thin solid #CCC !important; margin:0px !important; padding:0px !important; width: 100% !important; height: ' + Altura.toString() + 'px !important;"></iframe>';

                            $JanelaFormClientes = $.alert({
                                icon: 'fa fa-search',
                                type: 'blue',
                                title: '<font color="#000">#{localemsgs.SelecionePessoa}</font>',
                                content: HTML,
                                boxWidth: '96%',
                                closeIcon: true,
                                useBootstrap: false,
                                buttons: {
                                    cancel: {
                                        btnClass: 'btn-red',
                                        text: '<i class="fa fa-ban"></i>&nbsp;#{localemsgs.Cancelar}'
                                    }
                                }
                            });
                        })
                        ;
                // ]]>
            </script>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{funcionario.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela" listener="#{funcionario.MostrarFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.SomenteAtivos}: " /></label>
                                <p:selectBooleanCheckbox value="#{funcionario.somenteAtivos}">
                                    <p:ajax update="msgs main:tabela" listener="#{funcionario.SomenteAtivos}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>
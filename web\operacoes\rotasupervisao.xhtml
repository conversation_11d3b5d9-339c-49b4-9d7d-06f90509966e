<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/rotas.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript"></script>
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript"></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{rota.Persistencia(login.pp)}"/>
                <f:viewAction action="#{trajetosMB.Persistencia(login.pp, login.satellite)}"/>
            </f:metadata>
            <p:growl id="msgs"/>
            <div id="body">
                <ui:include src="/botao_panico.xhtml"/>

                <header>
                    <h:form id="cabecalho">

                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-5" style="align-self: center;">
                                    <img src="../assets/img/icone_rotasdesupervisao.png" height="40" width="40"/>
                                    #{localemsgs.RotasSup}
                                </div>

                                <div class="ui-grid-col-4" style="align-self: center; text-align: center;">
                                    <h:outputText value="#{localemsgs.Data}: "/>
                                    <h:outputText id="dataDia" value="#{rota.dataTela}" converter="conversorDia"/>
                                </div>

                                <div class="ui-grid-col-3" style="align-self: center; text-align: center;">
                                    <p:hotkey bind="left" action="#{rota.RotaAnterior}" update="main:tabela cabecalho"/>
                                    <p:commandLink action="#{rota.RotaAnterior}" update="main:tabela cabecalho" style="padding: 20px 22px 0px 20px">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px !important;"/>
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="yyyy-MM-dd" maxdate="#{rota.ultimoDia}" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{rota.SelecionarData}" update="main:tabela cabecalho" />
                                    </p:calendar>

                                    <p:hotkey bind="right" action="#{rota.RotaPosterior}" update="main:tabela cabecalho"/>
                                    <p:commandLink action="#{rota.RotaPosterior}" update="main:tabela cabecalho">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px !important;"/>
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{rota.filialDesc}
                                    </div>
                                    <div class="ui-grid-col-8">
                                        #{localemsgs.QtdRotas}: #{rota.total}
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="e" update="msgs cadastroRota cadastroRota:cadastrar" actionListener="#{rota.buttonAction}"/>
                    <p:hotkey bind="a" update="cadastroRota msgs cabecalho" action="#{rota.NovoRota}"/>
                    <p:hotkey bind="d" action="#{rota.Excluir}" update="tabela msgs cabecalho"/>
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline">
                                    <p:dataTable id="tabela" value="#{rota.allRotas}" paginator="true" rows="15" lazy="true"
                                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.RotasSup}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="listaRotasSup"
                                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                                 selection="#{rota.rotaSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true" scrollWidth="100%"
                                                 style="font-size: 12px; background: white">
                                        <p:ajax event="rowDblselect" listener="#{rota.onRowSelect}"
                                                update="cadastroRota msgs"/>
                                        <p:column headerText="#{localemsgs.CodFil}" style="width: 53px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.codFil}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Rota}" class="celula-right" style="width: 48px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.hrLargada.compareTo('18:00') ge 0
                                                                   ? '☾   '.concat(listaRotasSup.rota)
                                                                   : listaRotasSup.rota}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Supervisor}" style="width: 200px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.nome}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Data}" style="width: 74px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.data}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.TpVeic}" style="width: 30px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.tpVeic}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Obs}" style="width: 200px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.observacao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Largada}" style="width: 50px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.hrLargada}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Chegada}" style="width: 53px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.hrChegada}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_IntIni}" style="width: 50px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.hrIntIni}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_IntFim}" style="width: 50px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.hrIntFim}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Total}" class="celula-right" style="width: 50px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.hsTotal}">
                                                <f:convertNumber pattern="0.00"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Sequencia}" class="celula-right" style="width: 75px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.sequencia}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" style="width: 90px ;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 99px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 50px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.hr_Alter}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Flag_Excl}" style="width: 50px;
                                                  #{listaRotasSup.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                            <h:outputText value="#{listaRotasSup.flag_Excl}"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}"
                                           update="cadastroRota msgs cabecalho" action="#{rota.NovoRota}">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" update="msgs cadastroRota cadastroRota:cadastrar"
                                           actionListener="#{rota.buttonAction}">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Excluir}" action="#{rota.Excluir}" update="tabela msgs cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirRota}" icon="ui-icon-alert" />
                            </p:commandLink>
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button"
                                                 styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button"
                                                 styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                        </div>
                        <div style=" top: 0px; right: 5px; position: fixed">
                            <p:commandLink title="#{localemsgs.Voltar}"
                                           action="#{login.voltar}">
                                <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                    </p:panel>
                </h:form>
                <!-- MODAL DE ROTAS-->
                <h:form id="cadastroRota">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                              focus="supervisor"
                              style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#cadastroRota\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icone_rotasdesupervisao.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarRota}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="subFil" value="#{localemsgs.Filial}:"  />
                                <p:selectOneMenu id="subFil" value="#{rota.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%"
                                                 disabled="#{rota.flag eq 2 || rota.novaRota.flag_Excl eq '*'}">
                                    <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                                   itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                    <p:ajax event="itemSelect" listener="#{rota.SelecionarFilial}"
                                            update="cadastroRota:rota"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="data" value="#{localemsgs.Data}: " rendered="#{rota.flag eq 1}" />
                                <p:inputMask id="data" value="#{rota.novaRota.dataS}" mask="99/99/9999"
                                             required="true" label="#{localemsgs.Data}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                             style="width: 100%"
                                             maxlength="8" placeholder="00/00/0000" rendered="#{rota.flag eq 1}"
                                             converter="conversorData" disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                    <p:ajax partialSubmit="true" process="@this" event="blur"
                                            listener="#{rota.CalculaHorasTrabalhadas()}" update="cadastroRota:rota msgs"/>
                                </p:inputMask>

                                <p:outputLabel for="data2" value="#{localemsgs.Data}: " rendered="#{rota.flag eq 2}"/>
                                <p:inputText id="data2" value="#{rota.novaRota.data}" label="#{localemsgs.Data}"
                                             style="width: 100%"
                                             disabled="true" rendered="#{rota.flag eq 2}" converter="conversorData"/>

                                <p:outputLabel for="rota" value="#{localemsgs.Rota}:"/>
                                <p:inputText id="rota" value="#{rota.novaRota.rota}"
                                             required="true" label="#{localemsgs.Rota}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Rota}"
                                             style="width: 100%"
                                             maxlength="3" disabled="true">
                                    <p:watermark for="rota" value="#{localemsgs.Rota}"/>
                                </p:inputText>

                                <p:outputLabel for="tpvei" value="#{localemsgs.TpVeic}: "/>
                                <p:inputText id="tpvei" value="#{rota.novaRota.tpVeic}"
                                             style="width: 100%"
                                             maxlength="1" disabled="true" >
                                </p:inputText>

                            </p:panelGrid>

                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-5,ui-grid-col-1,ui-grid-col-2,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="supervisor" value="#{localemsgs.Supervisor}: " />
                                <p:autoComplete id="supervisor" value="#{rota.pessoa}" converter="conversorPessoa"
                                                completeMethod="#{rota.BuscarPessoas}" required="true" forceSelection="true"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Supervisor}"
                                                var="sup" itemLabel="#{sup.nome}" itemValue="#{sup}" scrollHeight="250"
                                                style="width: 100%"
                                                placeholder="#{localemsgs.Supervisor}" size="41" styleClass="supervisor"
                                                disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                    <p:ajax event="itemSelect" listener="#{rota.SelecionarSupervisor}" update="cadastroRota:codPessoa cadastroRota:matrPessoa"/>
                                </p:autoComplete>

                                <p:inputText id="codPessoa" value="#{rota.pessoa.codigo}" disabled="true"
                                             style="width: 100%" placeholder="#{localemsgs.Codigo}">
                                    <f:convertNumber type="number" minFractionDigits="0"/>
                                </p:inputText>

                                <p:outputLabel for="matrPessoa" value="#{localemsgs.Matr}: "/>
                                <p:inputText id="matrPessoa" value="#{rota.pessoa.matr}" disabled="true"
                                             style="width: 100%"
                                             placeholder="#{localemsgs.Matr}">
                                    <f:convertNumber pattern="0"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-8, ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:panel style="background: transparent">
                                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                                        <div style="width: 25%; float: left;">
                                            <p:outputLabel for="hrLargada" value="#{localemsgs.Horario}: "/>
                                        </div>
                                        <div style="width: 25%; float: left; padding-left: 4px;">
                                            <p:inputMask id="hrLargada" value="#{rota.novaRota.hrLargada}" mask="99:99"
                                                         required="true" placeholder="00:00"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Largada}"
                                                         maxlength="4" size="3" disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                                <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                            </p:inputMask>
                                        </div>
                                        <div style="width: 25%; float: left; padding-left: 10px;">
                                            <p:outputLabel for="hrChegada" value="#{localemsgs.a}"/>
                                        </div>
                                        <div style="width: 25%; float: left;">
                                            <p:inputMask id="hrChegada" value="#{rota.novaRota.hrChegada}" mask="99:99"
                                                         required="true"  placeholder="00:00"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Chegada}"
                                                         maxlength="4" size="3" disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                                <p:ajax process="@this" update="msgs totalhr" partialSubmit="true"
                                                        listener="#{rota.CalculaHorasTrabalhadas()}" event="blur"/>
                                            </p:inputMask>
                                        </div>
                                    </div>
                                    <div class="ui-grid-row">
                                        <div style="width: 25%; float: left;">
                                            <p:outputLabel for="hrIntIni" value="#{localemsgs.Intervalo}: "/>
                                        </div>
                                        <div style="width: 25%; float: left; padding-left: 4px;">
                                            <p:inputMask id="hrIntIni" value="#{rota.novaRota.hrIntIni}" mask="99:99"
                                                         required="true" placeholder="00:00"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Intini}"
                                                         maxlength="4" size="3" disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                                <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                            </p:inputMask>
                                        </div>
                                        <div style="width: 25%; float: left; padding-left: 10px;">
                                            <p:outputLabel for="hrIntFim" value="#{localemsgs.a}"/>
                                        </div>
                                        <div style="width: 25%; float: left;">
                                            <p:inputMask id="hrIntFim" value="#{rota.novaRota.hrIntFim}" mask="99:99"
                                                         required="true"  placeholder="00:00"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_IntFim}"
                                                         maxlength="4" size="3" disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                                <p:ajax process="@this" update="msgs totalhr" partialSubmit="true"
                                                        listener="#{rota.CalculaHorasTrabalhadas()}" event="blur"/>
                                            </p:inputMask>
                                        </div>
                                    </div>
                                </p:panel>
                                <p:panel style="background: transparent">
                                    <div class="ui-grid-row">
                                        <div style="width: 50%; float: left">
                                            <p:outputLabel for="totalhr" value="#{localemsgs.Hr_Total}: "/>
                                        </div>
                                        <div style="width: 50%; float: left; padding-left: 7px">
                                            <p:inputText id="totalhr" value="#{rota.novaRota.hsTotal}"
                                                         maxlength="4" size="3"
                                                         disabled="true">
                                                <f:convertNumber maxFractionDigits="2"/>
                                            </p:inputText>
                                        </div>
                                    </div>
                                </p:panel>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="obs" value="#{localemsgs.Obs}: " />
                                <p:inputText id="obs" value="#{rota.novaRota.observacao}" style="width: 100%"
                                             label="#{localemsgs.Obs}" disabled="#{rota.novaRota.flag_Excl eq '*'}" />
                                <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:commandLink rendered="#{rota.flag eq 1}" id="cadastro"
                                               update="main:tabela :msgs  cadastroTrajeto cadastrar"
                                               title="#{localemsgs.Cadastrar}" disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                    <f:actionListener binding="#{rota.Cadastrar()}"/>
                                    <f:actionListener binding="#{trajetosMB.NewTrajeto(rota.novaRota)}"/>
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink rendered="#{rota.flag eq 2}" id="edit" action="#{rota.Editar}"
                                               update=":msgs main:tabela cadastrar"
                                               title="#{localemsgs.Editar}" disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                    <f:viewAction action="#{rota.ListarData}"/>
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </p:panelGrid>


                            <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                         layout="grid" styleClass="ui-panelgrid-blank tabs" id="panelTabelaSupervisao"
                                         style="background-color: transparent;"
                                         rendered="#{rota.flag eq 2}">
                                <p:panel id="status">
                                    <img src="../assets/img/icone_satmob_trajetos_40x40.png" height="30"
                                         width="30" rendered="#{rota.flag eq 2}"/>
                                    <p:spacer width="5"/>
                                    <h:outputText value="#{localemsgs.Trajetos}" style="font-size: 14px; font-weight: bold"/>
                                </p:panel>
                                <p:panelGrid columns="2">
                                    <p:panel class="panelTabela">
                                        <p:dataTable id="trajetos" value="#{rota.trajetos}" sortBy="#{listaTrajetos.hora1}"
                                                     style="font-size: 12px" var="listaTrajetos" rowKey="#{listaTrajetos.parada}" styleClass="tabela"
                                                     resizableColumns="true" scrollable="true" selectionMode="single" scrollHeight="95"
                                                     scrollWidth="100%"
                                                     emptyMessage="#{localemsgs.SemRegistros}" rendered="#{rota.flag eq 2}"
                                                     selection="#{trajetosMB.trajetoSelecionado}">
                                            <p:ajax event="rowDblselect" update="msgs cadastroTrajeto" partialSubmit="true" process="@this"
                                                    listener="#{trajetosMB.buttonAction(rota.novaRota)}"/>
                                            <p:column headerText="#{localemsgs.Parada}" style="width: 47px; font-size: 12px;
                                                      #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                <h:outputText value="#{listaTrajetos.parada}">
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora1}"
                                                      style="width: 50px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                <h:outputText value="#{listaTrajetos.hora1}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.CodCli1}"
                                                      style="width: 70px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                <h:outputText value="#{listaTrajetos.codCli1}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.NRed}" style="width: 150px;
                                                      #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                <h:outputText value="#{listaTrajetos.NRed}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Obs}" style="width: 200px;
                                                      #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                <h:outputText value="#{listaTrajetos.observ}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador}" style="width: 80px;
                                                      #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                <h:outputText value="#{listaTrajetos.operador}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 80px;
                                                      #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                <h:outputText value="#{listaTrajetos.dt_Alter}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Alter}"
                                                      style="width: 70px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                <h:outputText value="#{listaTrajetos.hr_Alter}"/>
                                            </p:column>
                                        </p:dataTable>
                                        <div class="ui-grid ui-grid-responsive">
                                            <div class="ui-grid-row">
                                                <div class="ui-grid-col-6">
                                                    <h:outputText id="qtdParadas" value="#{localemsgs.QtdParadas}: #{rota.trajetos.size()}"
                                                                  style="font-size: 12px"/>
                                                </div>
                                                <div class="ui-grid-col-6">
                                                    <p:outputLabel for="checkboxCliente" value="#{localemsgs.ExibirExcluidos}: "
                                                                   style="font-size: 12px"/>
                                                    <p:selectBooleanCheckbox
                                                        id="checkboxCliente"
                                                        value="#{rota.exclFlag}"
                                                        style="font-size: 12px"
                                                        disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                                        <p:ajax update="msgs trajetos qtdParadas" listener="#{rota.ListaTrajetos()}" />
                                                    </p:selectBooleanCheckbox>
                                                </div>
                                            </div>
                                        </div>
                                    </p:panel>
                                    <p:panel style="width: 30px;">
                                        <p:commandLink title="#{localemsgs.Adicionar}"
                                                       update="cadastroTrajeto" disabled="#{rota.novaRota.flag_Excl eq '*'}"
                                                       oncomplete="PF('dlgCadastrarTrajetos').show();">
                                            <f:actionListener binding="#{trajetosMB.NewTrajeto(rota.novaRota)}"/>
                                            <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30"/>
                                        </p:commandLink>
                                        <p:spacer height="20px"/>
                                        <p:commandLink title="#{localemsgs.Editar}" update="cadastroTrajeto msgs"
                                                       disabled="#{rota.novaRota.flag_Excl eq '*'}"
                                                       actionListener="#{trajetosMB.buttonAction(rota.novaRota)}">
                                            <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                        </p:commandLink>
                                        <p:spacer height="20px"/>
                                        <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{trajetosMB.Excluir}"
                                                       action="#{rota.ListaTrajetos}" update="cadastroTrajeto cadastroRota:trajetos msgs"
                                                       disabled="#{rota.novaRota.flag_Excl eq '*'}">
                                            <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="30"/>
                                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirTrajeto}" icon="ui-icon-alert" />
                                        </p:commandLink>
                                    </p:panel>
                                </p:panelGrid>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--INÍCIO MODAL DE TRAJETOS-->
                <h:form id="cadastroTrajeto" class="form-inline">
                    <p:dialog widgetVar="dlgCadastrarTrajetos" id="dlgCadastroTrajeto" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" focus="horaRota"
                              style="background-image: url('../assets/img/menu_fundo.png');
                              background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_trajetos_40x40.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarTrajeto}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="cadastrarTrajeto" style="background-color: transparent" styleClass="cadastrar">
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-7,ui-grid-col-1,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="filialrota" value="#{localemsgs.Filial}: "/>
                                <p:inputText id="filialrota" value="#{trajetosMB.filialDescRota.descricao}" disabled="true"
                                             style=" width: 100%;"/>

                                <p:outputLabel for="rotaTrajeto" value="#{localemsgs.Rota}: "/>
                                <p:inputText value="#{trajetosMB.rota.rota}" disabled="true" id="rotaTrajeto"
                                             style="width: 100%;">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-6"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="horaRota" value="#{localemsgs.Hora}:" />
                                <p:inputMask id="horaRota" mask="99:99"  value="#{trajetosMB.novoTrajeto.hora1}"
                                             disabled="#{trajetosMB.novoTrajeto.flag_Excl eq '*'}"
                                             required="true" placeholder="00:00" converter="conversorHora"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                             size="4" maxlength="4">
                                    <p:ajax process="@this" update="msgs horaRota" partialSubmit="true"
                                            listener="#{trajetosMB.CalculoHoraTrajeto()}" event="blur"/>
                                </p:inputMask>

                                <p:outputLabel for="cliente2" value="#{localemsgs.Cliente}:"/>
                                <p:autoComplete id="cliente2" value="#{trajetosMB.clientes}" styleClass="cliente2"
                                                style="width: 100%"
                                                completeMethod="#{trajetosMB.ListarClientes}" required="true"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}"
                                                var="cli" itemLabel="#{cli.nome}" itemValue="#{cli}"
                                                converter="conversorCliente" scrollHeight="250"
                                                disabled="#{trajetosMB.novoTrajeto.flag_Excl eq '*'}">
                                    <p:ajax event="itemSelect" listener="#{trajetosMB.SelecionarCliente}"/>
                                    <p:watermark for="cliente2" value="#{localemsgs.Cliente}" />
                                </p:autoComplete>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="obsTrajeto1" value="#{localemsgs.Obs}: "/>
                                <p:inputText id="obsTrajeto1" value="#{trajetosMB.novoTrajeto.observ}"
                                             style="width:100%"
                                             label="#{localemsgs.Obs}" placeholder="#{localemsgs.Obs}" maxlength="20" size="40"
                                             disabled="#{trajetosMB.novoTrajeto.flag_Excl eq '*'}"/>

                                <p:outputLabel value="#{localemsgs.tipoServ}: " />
                                <p:inputText value="#{trajetosMB.novoTrajeto.tipoSrv}" disabled="true"
                                             style="width: 100%"/>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:commandLink id="adicionarParada" title="#{localemsgs.Cadastrar}"  action="#{rota.buttonAction(null)}"
                                               actionListener="#{trajetosMB.Cadastrar()}"
                                               update=":cadastroRota:trajetos :msgs" disabled="#{trajetosMB.novoTrajeto.flag_Excl eq '*'}"
                                               rendered="#{trajetosMB.flag eq 1}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>

                                <p:commandLink id="editarParada" action="#{trajetosMB.Editar()}" disabled="#{trajetosMB.novoTrajeto.flag_Excl eq '*'}"
                                               title="#{localemsgs.Editar}" update=":cadastroRota:trajetos :msgs"
                                               rendered="#{trajetosMB.flag eq 2}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </p:panelGrid>

                            <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                         layout="grid" styleClass="ui-panelgrid-blank tabs" id="panelTabelaSupervisao"
                                         style="background-color: transparent;"
                                         rendered="#{trajetosMB.flag eq 2}">
                                <p:panel id="panelSupervisao">
                                    <img src="../assets/img/icone_satmob_supervisoesrecentes.png" height="30"
                                         width="30" rendered="#{trajetosMB.flag eq 2}"/>
                                    <p:spacer width="5"/>
                                    <h:outputText value="#{localemsgs.Supervisoes}" style="font-size: 14px; font-weight: bold; color: black;"/>

                                    <p:commandLink title="#{localemsgs.Detalhes}" update="msgs"  style="float: right"
                                                   actionListener="#{trajetosMB.SelecaoTmktDetPstPstServClientes}">
                                        <p:graphicImage url="../assets/img/icone_redondo_entrevistas.png" height="30" />
                                    </p:commandLink>
                                </p:panel>
                                <p:dataTable id="supervisoes" value="#{trajetosMB.supervisoesMB.listaSupervisao}"
                                             emptyMessage="#{localemsgs.SemRegistros}"
                                             var="lista" scrollable="true" selectionMode="single"
                                             selection="#{trajetosMB.supervisoesMB.supervisaoSelecionado}" rowKey="#{lista.tmktdetpst.sequencia}"
                                             styleClass="tabela tabelaSupervisoes" resizableColumns="true" scrollHeight="115" scrollWidth="100%"
                                             style="font-size: 12px; background-color: transparent"
                                             rendered="#{trajetosMB.flag eq 2}">
                                    <p:ajax event="rowDblselect" listener="#{trajetosMB.doubleSelect}"
                                            update="formEditar msgs" />
                                    <p:column headerText="#{localemsgs.Secao}" style="width: 100px;">
                                        <h:outputText value="#{lista.pstserv.secao}" title="#{lista.pstserv.secao}"
                                                      style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                               lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Local}" style="width: 200px;">
                                        <h:outputText value="#{lista.pstserv.local}" title="#{PstServClientes.pstserv.local}"
                                                      style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                               lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Historico}" style="width: 200px;">
                                        <h:outputText value="#{lista.tmktdetpst.historico}" title="#{PstServClientes.tmktdetpst.historico}"
                                                      style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                               lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Detalhes}" style="width: 200px">
                                        <h:outputText value="#{lista.tmktdetpst.detalhes}"
                                                      style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                               lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue'}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Situacao}" style="width: 60px">
                                        <h:outputText value="#{lista.tmktdetpst.situacao}"
                                                      style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                               lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue'}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 50px;">
                                        <h:outputText value="#{lista.tmktdetpst.hr_alter}" title="#{PstServClientes.tmktdetpst.hr_alter}"
                                                      style="#{lista.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                               lista.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                    </p:column>
                                </p:dataTable>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--INÍCIO MODAL DE SUPERVISÃO-->
                <p:panel>
                    <h:form id="formEditar" class="ui-fluid">
                        <p:dialog  widgetVar="dlgListarSupervisoes" positionType="absolute" responsive="true"
                                   draggable="false" resizable="false" dynamic="true" closable="true" modal="true"
                                   showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dialogosupervisao"
                                   style="background-image: url('../assets/img/bk.jpg')">
                            <f:facet name="header">
                                <img src="../assets/img/icone_supervisao.png" height="40" width="40"/>
                                <h:outputText value="#{localemsgs.Supervisoes}" style="color:black" />
                            </f:facet>

                            <p:panel id="supervisao" styleClass="panelTelaSupervisao">
                                <div class="ui-grid ui-grid-responsive">
                                    <div class="ui-grid-row cabecalhoSupervisao" style="background-color: white">
                                        <div class="ui-grid-col-5" style="align-self: center;">
                                            #{localemsgs.Filial}: #{trajetosMB.supervisoesMB.filial.descricao}
                                        </div>
                                        <div class="ui-grid-col-4" style="align-self: center;">
                                            #{localemsgs.Operador}: #{trajetosMB.supervisoesMB.supervisaoSelecionado.tmktdetpst.operador}
                                        </div>
                                        <div class="ui-grid-col-3" style="align-self: center;">
                                            <p:commandLink title="#{localemsgs.SupervisaoAnterior}" action="#{trajetosMB.SupervisaoAnterior}"
                                                           process="@this"
                                                           update="formEditar:supervisao msgs">
                                                <p:graphicImage url="../assets/img/botao_anterior.png" height="20" />
                                            </p:commandLink>
                                            #{localemsgs.Data}:
                                            <h:outputText value="#{trajetosMB.supervisoesMB.supervisaoSelecionado.tmktdetpst.data}" converter="conversorData" />
                                            #{trajetosMB.supervisoesMB.supervisaoSelecionado.tmktdetpst.hora}
                                            <p:commandLink title="#{localemsgs.SupervisaoPosterior}" action="#{trajetosMB.ProximaSupervisao}"
                                                           process="@this"
                                                           update="formEditar:supervisao msgs">
                                                <p:graphicImage url="../assets/img/botao_proximo.png" height="20" />
                                            </p:commandLink>
                                        </div>
                                    </div>
                                    <div class="ui-grid-row detalhesSupervisao">
                                        <div class="ui-grid-col-6" >
                                            <div class="ui-grid-row">
                                                <div style="width: 20% !important; float: left;">
                                                    <h:outputText value="#{localemsgs.Posto}: " style="color: black; font-size: 12px"/>
                                                </div>
                                                <div style="width: 80% !important; float: left;">
                                                    <h:outputText value="#{trajetosMB.supervisoesMB.supervisaoSelecionado.pstserv.local}"
                                                                  style="color: black; font-size: 16px;"/>
                                                    <div style="color: black; font-size: 14px;">
                                                        #{trajetosMB.supervisoesMB.cliente.NRed} - #{trajetosMB.supervisoesMB.cliente.nome}
                                                    </div>
                                                    <div style="color: black; font-size: 12px;">
                                                        #{trajetosMB.supervisoesMB.cliente.ende} - #{trajetosMB.supervisoesMB.cliente.bairro}
                                                    </div>
                                                    <div style="color: black; font-size: 12px;">
                                                        #{trajetosMB.supervisoesMB.cliente.cidade}/#{trajetosMB.supervisoesMB.cliente.estado} - #{localemsgs.CEP}: <h:outputText value="#{trajetosMB.supervisoesMB.cliente.CEP}" converter="conversorCEP"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div class="ui-grid-col-6">
                                                    <h:outputText value="#{localemsgs.FotosLocal}: " style="color: black; font-size: 12px"/>
                                                </div>
                                                <div class="ui-grid-col-6">
                                                    <h:outputText value="#{localemsgs.QtdFotos}: " style="color: black; font-size: 12px"/>
                                                    <h:outputText value="#{trajetosMB.supervisoesMB.supervisaoSelecionado.fotos.size()}" style="color: black; font-size: 12px"/>
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div style="width: 10% !important; float: left; text-align: center">
                                                    <p:commandLink action="#{trajetosMB.supervisoesMB.VoltarFotoPosto}"
                                                                   rendered="#{trajetosMB.supervisoesMB.supervisaoSelecionado.fotos.size() gt 0}"
                                                                   update="formEditar:fotoPosto msgs" styleClass="botao">
                                                        <p:graphicImage url="../assets/img/botao_anterior.png" height="20" title="#{localemsgs.FotoAnterior}"/>
                                                    </p:commandLink>
                                                </div>
                                                <div style="width: 80% !important; float: left; text-align: center">
                                                    <p:lightBox style=" text-align: center;" id="fotoPosto">
                                                        <h:outputLink value="#{trajetosMB.supervisoesMB.fotoPosto}"
                                                                      title="#{trajetosMB.supervisoesMB.supervisaoSelecionado.pstserv.local}">
                                                            <h:graphicImage value="#{trajetosMB.supervisoesMB.fotoPosto}" id="panelFotoQuestionario" style="height: 200px;"/>
                                                        </h:outputLink>
                                                    </p:lightBox>
                                                </div>
                                                <div style="width: 10% !important; float: left; text-align: center">
                                                    <p:commandLink action="#{trajetosMB.supervisoesMB.AvancarFotoPosto}"
                                                                   rendered="#{trajetosMB.supervisoesMB.supervisaoSelecionado.fotos.size() gt 0}"
                                                                   update="formEditar:fotoPosto msgs" styleClass="botao">
                                                        <p:graphicImage url="../assets/img/botao_proximo.png" height="20" title="#{localemsgs.ProximaFoto}"/>
                                                    </p:commandLink>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ui-grid-col-6" >
                                            <div class="ui-grid-row">
                                                <div style="width: 20% !important; float:left">
                                                    <h:outputText value="#{localemsgs.Tipo}:" style="color:black;font-size: 12px;"/>
                                                </div>
                                                <div style="width: 80% !important; float:left">
                                                    <h:outputText value="#{trajetosMB.supervisoesMB.supervisaoSelecionado.pstserv.tipoPosto} " style="color:black; font-size: 16px"/>
                                                    <h:outputText value="- #{trajetosMB.supervisoesMB.supervisaoSelecionado.pstserv.tipoPostoDesc}" style="color:black;font-size: 16px"/>
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div style="width: 20% !important; float:left">
                                                    <h:outputText value="#{localemsgs.Situacao}:" style="color:black;font-size: 12px;"/>
                                                </div>
                                                <div style="width: 40% !important; float:left">
                                                    <h:outputText id="situacao1" value="#{trajetosMB.supervisoesMB.supervisaoSelecionado.tmktdetpst.situacao} " style="color:#E6E6E6; text-shadow: 1px 1px black"/>
                                                    <p:commandLink oncomplete="PF('dlgSituacao').show()" >
                                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="20"/>
                                                    </p:commandLink>
                                                    <p:inputText id="situacao2" value="#{trajetosMB.supervisoesMB.situacoes.get(trajetosMB.supervisoesMB.supervisaoSelecionado.tmktdetpst.situacao)}"
                                                                 style="font-size: 12px; width: 100px" disabled="true"/>
                                                </div>
                                                <div style="width: 40% !important; float:left">
                                                    <h:outputText value="#{trajetosMB.supervisoesMB.supervisaoSelecionado.pstserv.secao} " style="color:black; font-size: 11px;"/>
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div style="width: 20% !important; float:left">
                                                    <h:outputText value="#{localemsgs.Historico}:" style="color:black;font-size: 12px;"/>
                                                </div>
                                                <div style="width: 80% !important; float:left">
                                                    <h:outputText value="#{trajetosMB.supervisoesMB.supervisaoSelecionado.tmktdetpst.historico} " style="color:black"/>
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <div style="width: 20% !important; float:left">
                                                    <h:outputText value="#{localemsgs.Mapa}:" style="color:black;font-size: 12px;"/>
                                                </div>
                                                <div style="width: 80% !important; float:left">
                                                    <h:outputText value="#{localemsgs.Distancia} (m): " style="color:black; text-align: right" />
                                                    <h:outputText value="#{trajetosMB.supervisoesMB.distPstSup}" style="color: black; text-align: right">
                                                        <f:convertNumber maxFractionDigits="2" />
                                                    </h:outputText>
                                                </div>
                                            </div>
                                            <div class="ui-grid-row">
                                                <p:gmap center="#{trajetosMB.supervisoesMB.coordenadas}" zoom="15" model="#{trajetosMB.supervisoesMB.pin}"
                                                        type="ROADMAP" style="width:100%;height:210px;"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ui-grid-row detalhesSupervisao">
                                        <div class="ui-grid-col-1">
                                            <p:outputLabel for="detalhes" value="#{localemsgs.Detalhes}: " style="color: black"/>
                                        </div>
                                        <div class="ui-grid-col-11">
                                            <p:inputText style="width: 100%" disabled="true" id="detalhes"
                                                         value="#{trajetosMB.supervisoesMB.supervisaoSelecionado.tmktdetpst.detalhes}"/>
                                        </div>
                                    </div>
                                    <div class="ui-grid-row detalhesSupervisao">
                                        <div class="ui-grid-col-3" style="text-align: center; padding-right: 5px">
                                            <h:outputText rendered="#{not empty trajetosMB.supervisoesMB.funcionarios}"
                                                          style="font-size:14px; color: black; font-weight: normal"
                                                          value="#{localemsgs.Entrevistas}" />
                                            <p:dataTable id="tabelaFuncion" value="#{trajetosMB.supervisoesMB.funcionarios}" emptyMessage="#{localemsgs.SemRegistros}"
                                                         styleClass="tabela" var="lista" rowKey="#{lista.funcion.matr}" resizableColumns="true"
                                                         scrollable="true" scrollHeight="130" rendered="#{not empty trajetosMB.supervisoesMB.funcionarios}"
                                                         selectionMode="single" selection="#{trajetosMB.supervisoesMB.questionarioSelecionado}"
                                                         style="font-size: 12px; width: 100%">
                                                <p:ajax event="rowSelect" listener="#{trajetosMB.supervisoesMB.SelecionarQuestionario}"
                                                        update="formEditar:panelQuestoes formEditar:panelFotoFuncion" />
                                                <p:column headerText="#{localemsgs.Matr}" style="width: 65px">
                                                    <h:outputText value="#{lista.funcion.matr}" title="#{lista.funcion.matr}">
                                                        <f:convertNumber pattern="0" />
                                                    </h:outputText>
                                                </p:column>
                                                <p:column headerText="#{localemsgs.Nome}">
                                                    <h:outputText value="#{lista.funcion.nome}" title="#{lista.funcion.nome}"/>
                                                </p:column>
                                            </p:dataTable>
                                            <p:graphicImage url="../assets/img/funcionarios.png" style="width:100%;"
                                                            rendered="#{empty trajetosMB.supervisoesMB.funcionarios}" />
                                        </div>
                                        <div class="ui-grid-col-5" style="text-align: center">
                                            <p:panel id="panelQuestoes">
                                                <h:outputText style="font-size:14px; font-weight: normal; color:  black"
                                                              value="#{localemsgs.Checklist}"
                                                              rendered="#{trajetosMB.supervisoesMB.questoes.size() ge 1}"/>
                                                <p:panel rendered="#{not empty trajetosMB.supervisoesMB.questoes}">
                                                    <p:inputTextarea style="font-size:12px; font-weight: normal; text-align: left; width: 100%; height: 40px"
                                                                     rows="1" scrollHeight="20" autoResize="false" readonly="true" disabled="true"
                                                                     value="#{trajetosMB.supervisoesMB.detalhesQuestoes}"
                                                                     title="#{trajetosMB.supervisoesMB.detalhesQuestoes}"/>
                                                    <p:dataTable id="tabelaDetalhesQuestionario" value="#{trajetosMB.supervisoesMB.questoes}"
                                                                 styleClass="tabela" var="lista" rowKey="#{lista.psthstqst.codQuestao}"
                                                                 resizableColumns="true" rendered="#{trajetosMB.supervisoesMB.questoes.size() gt 1}"
                                                                 scrollable="true" scrollHeight="100" emptyMessage="#{localemsgs.SemRegistros}"
                                                                 style="font-size: 11px">
                                                        <p:column headerText="#{localemsgs.Qst}" style="width: 210px">
                                                            <h:outputText value="#{lista.tbval.descricao}" title="#{lista.tbval.descricao}"/>
                                                        </p:column>
                                                        <p:column headerText="#{localemsgs.Resp}" style="width: 70px">
                                                            <h:outputText value="#{trajetosMB.supervisoesMB.respostas.get(lista.psthstqst.resposta)}"
                                                                          title="#{trajetosMB.supervisoesMB.respostas.get(lista.psthstqst.resposta)}"/>
                                                        </p:column>
                                                        <p:column style="width: 120px" headerText="#{localemsgs.Detalhes}">
                                                            <h:outputText value="#{lista.psthstqst.detalhes}" title="#{lista.psthstqst.detalhes}"/>
                                                        </p:column>
                                                    </p:dataTable>
                                                </p:panel>
                                                <p:graphicImage url="../assets/img/questionario.png" style="max-height:200px"
                                                                rendered="#{empty trajetosMB.supervisoesMB.questoes}" />
                                            </p:panel>
                                        </div>
                                        <div class="ui-grid-col-4" style="text-align: center; padding-left: 5px">
                                            <p:panel id="panelFotoFuncion">
                                                <div class="ui-grid-row">
                                                    <div class="ui-grid-col-12" style="text-align: center">
                                                        <h:outputText style="font-size:14px; font-weight: normal; color: black;
                                                                      text-align: center" value="#{localemsgs.Galeria}"
                                                                      rendered="#{not empty trajetosMB.supervisoesMB.questionarioSelecionado.endfotos}"/>
                                                    </div>
                                                </div>
                                                <div class="ui-grid-row">
                                                    <div class="ui-grid-col-12" style="text-align: left">
                                                        <h:outputText value="#{localemsgs.Foto} #{trajetosMB.supervisoesMB.posFotoFuncion + 1} #{localemsgs.De}
                                                                      #{trajetosMB.supervisoesMB.questionarioSelecionado.qtdefotos}:"
                                                                      style="font-size:12px; font-weight: normal; color:  black;"
                                                                      rendered="#{not empty trajetosMB.supervisoesMB.questionarioSelecionado.endfotos}" />
                                                    </div>
                                                </div>
                                                <div class="ui-grid-row">
                                                    <div style="width: 20% !important; float: left; text-align: center">
                                                        <p:commandLink action="#{trajetosMB.supervisoesMB.VoltarFotoFuncion}"
                                                                       rendered="#{not empty trajetosMB.supervisoesMB.questionarioSelecionado.endfotos}"
                                                                       update="formEditar:panelFotoFuncion msgs">
                                                            <p:graphicImage url="../assets/img/botao_anterior.png" height="20" title="#{localemsgs.FotoAnterior}"/>
                                                        </p:commandLink>
                                                    </div>
                                                    <div style="width: 60% !important; float: left; text-align: center">
                                                        <p:lightBox styleClass="fotoGrande" >
                                                            <h:outputLink value="#{trajetosMB.supervisoesMB.fotoFuncion}" title="#{trajetosMB.supervisoesMB.questionarioSelecionado.funcion.nome}">
                                                                <h:graphicImage value="#{trajetosMB.supervisoesMB.fotoFuncion}" id="fotoFuncionario" style="height: 150px;"/>
                                                            </h:outputLink>
                                                        </p:lightBox>
                                                    </div>
                                                    <div style="width: 20% !important; float: left; text-align: center">
                                                        <p:commandLink action="#{trajetosMB.supervisoesMB.AvancarFotoFuncion}"
                                                                       rendered="#{not empty trajetosMB.supervisoesMB.questionarioSelecionado.endfotos}"
                                                                       update="formEditar:panelFotoFuncion msgs">
                                                            <p:graphicImage url="../assets/img/botao_proximo.png" height="20" title="#{localemsgs.ProximaFoto}"/>
                                                        </p:commandLink>
                                                    </div>
                                                </div>
                                                <p:graphicImage url="../assets/img/galeria.png" style="height:200px" rendered="#{empty trajetosMB.supervisoesMB.questionarioSelecionado.endfotos}"/>
                                            </p:panel>
                                        </div>
                                    </div>
                                </div>
                            </p:panel>
                        </p:dialog>

                        <p:dialog widgetVar="dlgSituacao"  class="dlgSituacao"
                                  resizable="false" dynamic="true" closable="false"
                                  width="400" showEffect="drop" hideEffect="drop"
                                  style="background-image: url('../assets/img/bk.jpg');height: 270px">
                            <f:facet name="header">
                                <h:outputText value="#{localemsgs.Situacao}" style="color:black"/>
                            </f:facet>
                            <p:panel id="listarSituacoes" style="background-color: transparent;  padding-left: 13px ">
                                <p:dataTable id="niveis" value="#{trajetosMB.supervisoesMB.listaSituacoes}"
                                             var="lista" rowKey="#{lista}" resizableColumns="true" emptyMessage="#{localemsgs.SemRegistros}"
                                             style="font-size: 12px" selectionMode="single" styleClass="tabela"
                                             selection="#{trajetosMB.supervisoesMB.situacao}">
                                    <p:ajax event="rowSelect" listener="#{trajetosMB.supervisoesMB.SelecionarSituacao}" />
                                    <p:column headerText="#{localemsgs.Situacao}">
                                        <h:outputText value="#{lista}" title="#{lista}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Descricao}" >
                                        <h:outputText value="#{trajetosMB.supervisoesMB.situacoes.get(lista)}" title="#{trajetosMB.supervisoesMB.situacoes.get(lista)}"/>
                                    </p:column>
                                </p:dataTable>
                                <div class="form-inline">
                                    <p:commandLink oncomplete="PF('dlgSituacao').hide()"
                                                   action="#{trajetosMB.supervisoesMB.ListarSupervisaoEdicaoPosto(postoservico.selecionado.secao)}"
                                                   actionListener="#{trajetosMB.supervisoesMB.AtualizarSituacao}"
                                                   title="#{localemsgs.Selecionar}" update="formDetalhes:situacao1 formDetalhes:situacao2 rota:tabela" process="@this">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                    <p:commandLink oncomplete="PF('dlgSituacao').hide()"
                                                   title="#{localemsgs.Voltar}">
                                        <p:graphicImage url="../assets/img/icone_voltar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </p:panel>
                        </p:dialog>
                    </h:form>
                </p:panel>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>
                <div class="footer-body" id="footer-body">
                    <div>
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.Corporativo}: " />
                            <p:selectBooleanCheckbox value="#{rota.mostrarFiliais}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{rota.MostrarFiliais}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.SomenteAtivos}: " />
                            <p:selectBooleanCheckbox value="#{rota.excl}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{rota.SomenteAtivos}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.LimparFiltros}: " />
                            <p:selectBooleanCheckbox value="#{rota.limparFiltros}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{rota.LimparFiltros}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>

            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

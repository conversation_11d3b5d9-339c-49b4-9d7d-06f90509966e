/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class FolhaPonto {

    private String CodFil, Matr, Secao, Posto, Nome,
            Cargo, Horario, CTPS_Nro, CTPS_Serie, Cliente;

    public FolhaPonto() {
    }

    public FolhaPonto(
            final String CodFil,
            final String Matr,
            final String Secao,
            final String Posto,
            final String Nome,
            final String Horario,
            final String CTPS_Nro,
            final String CTPS_Serie
    ) {
        this.CodFil = CodFil;
        this.Matr = Matr;
        this.Secao = Secao;
        this.Posto = Posto;
        this.Nome = Nome;
        this.Horario = Horario;
        this.CTPS_Nro = CTPS_Nro;
        this.CTPS_Serie = CTPS_Serie;
        this.Cliente = "";
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getPosto() {
        return Posto;
    }

    public void setPosto(String Posto) {
        this.Posto = Posto;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getHorario() {
        return Horario;
    }

    public void setHorario(String Horario) {
        this.Horario = Horario;
    }

    public String getCTPS_Nro() {
        return CTPS_Nro;
    }

    public void setCTPS_Nro(String CTPS_Nro) {
        this.CTPS_Nro = CTPS_Nro;
    }

    public String getCTPS_Serie() {
        return CTPS_Serie;
    }

    public void setCTPS_Serie(String CTPS_Serie) {
        this.CTPS_Serie = CTPS_Serie;
    }

    public String getCargo() {
        return Cargo;
    }

    public void setCargo(String Cargo) {
        this.Cargo = Cargo;
    }

    public String getCliente() {
        return Cliente;
    }

    public void setCliente(String Cliente) {
        this.Cliente = Cliente;
    }    
}

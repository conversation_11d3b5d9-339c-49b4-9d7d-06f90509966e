/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ClientesFech {

    private BigDecimal CodFil;
    private BigDecimal Sequencia;
    private BigDecimal CodFech;
    private String CodCli;
    private BigDecimal CodPessoa;
    private String Tipo;
    private String Comando;
    private String ComandoRet;
    private String COD_REG_REC_ORG;
    private String COD_REG_REC_REF;
    private String COD_REG_REC_CRT;
    private String Fechadura;
    private String Bottom;
    private BigDecimal SeqRota;
    private int Parada;
    private BigDecimal QueueSeq;
    private String HrProg;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_alter;

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Sequencia
     */
    public BigDecimal getSequencia() {
        return Sequencia;
    }

    /**
     * @param Sequencia the Sequencia to set
     */
    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    /**
     * @return the CodFech
     */
    public BigDecimal getCodFech() {
        return CodFech;
    }

    /**
     * @param CodFech the CodFech to set
     */
    public void setCodFech(String CodFech) {
        try {
            this.CodFech = new BigDecimal(CodFech);
        } catch (Exception e) {
            this.CodFech = new BigDecimal("0");
        }
    }

    /**
     * @return the CodCli
     */
    public String getCodCli() {
        return CodCli;
    }

    /**
     * @param CodCli the CodCli to set
     */
    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    /**
     * @return the CodPessoa
     */
    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    /**
     * @param CodPessoa the CodPessoa to set
     */
    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("0");
        }
    }

    /**
     * @return the Tipo
     */
    public String getTipo() {
        return Tipo;
    }

    /**
     * @param Tipo the Tipo to set
     */
    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    /**
     * @return the Comando
     */
    public String getComando() {
        return Comando;
    }

    /**
     * @param Comando the Comando to set
     */
    public void setComando(String Comando) {
        this.Comando = Comando;
    }

    /**
     * @return the ComandoRet
     */
    public String getComandoRet() {
        return ComandoRet;
    }

    /**
     * @param ComandoRet the Comando to set
     */
    public void setComandoRet(String ComandoRet) {
        this.Comando = ComandoRet;
    }

    /**
     * @return the COD_REG_REC_ORG
     */
    public String getCOD_REG_REC_ORG() {
        return COD_REG_REC_ORG;
    }

    /**
     * @param COD_REG_REC_ORG the COD_REG_REC_ORG to set
     */
    public void setCOD_REG_REC_ORG(String COD_REG_REC_ORG) {
        this.COD_REG_REC_ORG = COD_REG_REC_ORG;
    }

    /**
     * @return the COD_REG_REC_REF
     */
    public String getCOD_REG_REC_REF() {
        return COD_REG_REC_REF;
    }

    /**
     * @param COD_REG_REC_REF the COD_REG_REC_REF to set
     */
    public void setCOD_REG_REC_REF(String COD_REG_REC_REF) {
        this.COD_REG_REC_REF = COD_REG_REC_REF;
    }

    /**
     * @return the COD_REG_REC_CRT
     */
    public String getCOD_REG_REC_CRT() {
        return COD_REG_REC_CRT;
    }

    /**
     * @param COD_REG_REC_CRT the COD_REG_REC_CRT to set
     */
    public void setCOD_REG_REC_CRT(String COD_REG_REC_CRT) {
        this.COD_REG_REC_CRT = COD_REG_REC_CRT;
    }

    /**
     * @return the Fechadura
     */
    public String getFechadura() {
        return Fechadura;
    }

    /**
     * @param Fechadura the Fechadura to set
     */
    public void setFechadura(String Fechadura) {
        this.Fechadura = Fechadura;
    }

    /**
     * @return the Bottom
     */
    public String getBottom() {
        return Bottom;
    }

    /**
     * @param Bottom the Bottom to set
     */
    public void setBottom(String Bottom) {
        this.Bottom = Bottom;
    }

    /**
     * @return the SeqRota
     */
    public BigDecimal getSeqRota() {
        return SeqRota;
    }

    /**
     * @param SeqRota the SeqRota to set
     */
    public void setSeqRota(String SeqRota) {
        try {
            this.SeqRota = new BigDecimal(SeqRota);
        } catch (Exception e) {
            this.SeqRota = new BigDecimal("0");
        }
    }

    /**
     * @return the Parada
     */
    public int getParada() {
        return Parada;
    }

    /**
     * @param Parada the Parada to set
     */
    public void setParada(int Parada) {
        this.Parada = Parada;
    }

    public BigDecimal getQueueSeq() {
        return QueueSeq;
    }

    public void setQueueSeq(String QueueSeq) {
        try {
            this.QueueSeq = new BigDecimal(QueueSeq);
        } catch (Exception e) {
            this.QueueSeq = new BigDecimal("0");
        }
    }

    /**
     * @return the HrProg
     */
    public String getHrProg() {
        return HrProg;
    }

    /**
     * @param HrProg the HrProg to set
     */
    public void setHrProg(String HrProg) {
        this.HrProg = HrProg;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_alter
     */
    public String getHr_alter() {
        return Hr_alter;
    }

    /**
     * @param Hr_alter the Hr_alter to set
     */
    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

}

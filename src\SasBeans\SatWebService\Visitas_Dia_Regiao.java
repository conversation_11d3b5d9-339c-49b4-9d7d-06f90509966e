/*
 */
package SasBeans.SatWebService;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Visitas_Dia_Regiao {

    public String region;
    public String dia;
    public BigDecimal visitas;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public BigDecimal getVisitas() {
        return visitas;
    }

    public void setVisitas(BigDecimal visitas) {
        this.visitas = visitas;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Visitas_Dia_Regiao other = (Visitas_Dia_Regiao) obj;
        if (!Objects.equals(this.region, other.region)) {
            return false;
        }
        if (!Objects.equals(this.dia, other.dia)) {
            return false;
        }
        if (!Objects.equals(this.visitas, other.visitas)) {
            return false;
        }
        return true;
    }

}

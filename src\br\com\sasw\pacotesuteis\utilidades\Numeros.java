package br.com.sasw.pacotesuteis.utilidades;

/**
 * <AUTHOR>
 */
public class Numeros {

    public static boolean isNumeric(String strNum) {
        try {
            double d = Double.parseDouble(strNum);
        } catch (NumberFormatException | NullPointerException nfe) {
            return false;
        }
        return true;
    }

    public static int S2I(String valor) {
        int retorno;
        try {
            retorno = Integer.valueOf(valor.replace(".0", ""));
        } catch (Exception e) {
            retorno = 0;
        }
        return retorno;
    }

    /**
     * Separa numeros da string
     *
     * @param texto texto será realiza da verificação
     * @return numeros sem texto
     * @throws Exception
     */
    public static String separarNumerosTexto(String texto) throws Exception {
        try {
            char caracteres[] = texto.toCharArray();
            texto = "";
            for (char caracter : caracteres) {
                if (Character.isDigit(caracter)) {
                    texto += caracter;
                }
            }
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return texto;
    }

    /**
     * Separa texto dos numeros
     *
     * @param texto texto que deseja separar
     * @return texto sem numeros
     * @throws Exception
     */
    public static String separarTextoNumeros(String texto) throws Exception {
        try {
            char caracteres[] = texto.toCharArray();
            texto = "";
            for (char caracter : caracteres) {
                if (!Character.isDigit(caracter)) {
                    texto += caracter;
                }
            }
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return texto;
    }

}

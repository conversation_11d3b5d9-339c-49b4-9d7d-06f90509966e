/*
 */
package br.com.sasw.managedBeans;

import Arquivo.ArquivoLog;
import Controller.Tickets.KanBanSatMobWeb;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.KanBan;
import SasBeans.KanBanAtas;
import SasBeans.KanBanComent;
import SasBeans.KanBanMov;
import SasBeans.Pessoa;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.lazydatamodels.KanBanLazyList;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.enterprise.context.SessionScoped;
import javax.faces.application.FacesMessage;
import javax.faces.component.html.HtmlOutputText;
import javax.faces.context.FacesContext;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.dashboard.Dashboard;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.panel.Panel;
import org.primefaces.event.DashboardReorderEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DashboardColumn;
import org.primefaces.model.DashboardModel;
import org.primefaces.model.DefaultDashboardColumn;
import org.primefaces.model.DefaultDashboardModel;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "kanban")
@SessionScoped
public class KanbanMB implements Serializable {

    private SasPoolPersistencia pool;
    private Persistencia persistencia;
    private Pessoa login;
    private String log, caminho, usuario, sequencia, senha, email, novaSenha, ultimoAcesso, painelClicado;
    private ArquivoLog logerro;
    private int total, flag, totalPendentes;
    private Map filters;
    private LazyDataModel<KanBan> tickets = null;
    private KanBanSatMobWeb kanbansatmobweb;
    private KanBan ticket;
    private List<KanBanMov> movimentacao;
    private List<KanBanComent> comentarios;
    private List<Pessoa> usuarios;
    private KanBanComent novoComentario;
    private Boolean fazer, desenvolver, fila, teste, implantar, feito, marcarDesmarcar, onchange, removerFiltros,
            logado, pendencia, visual, ticketsUsuario;
    private List<KanBanAtas> atas;

    private Dashboard painel;
    private DashboardModel modelPainel;
    private DashboardColumn colunaFazer, colunaDesenvolver, colunaFilaTeste, colunaTeste, colunaImplantar, colunaFeito;
    private DashboardReorderEvent evento;

    private List<KanBan> tts;

    public KanbanMB() {
        try {
            log = new String();
            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + "KanBan\\" + DataAtual.getDataAtual("SQL") + ".txt";
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("kb", "true");
            pool = new SasPoolPersistencia();
            pool.setTamanhoPool(20);
            pool.setCaminho(this.getClass().getResource("mapconect.txt").getPath().replace("%20", " "));
            persistencia = pool.getConexao("SATELLITE");
            if (null == persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            logerro = new ArquivoLog();
            kanbansatmobweb = new KanBanSatMobWeb();
            filters = new HashMap();
            filters.put(" kanban.sequencia = ? ", Arrays.asList());
            filters.put(" kanban.descricao like ? ", Arrays.asList());
            filters.put(" kanban.historico like ? ", Arrays.asList());
            filters.put(" kanban.codpessoa = ? ", Arrays.asList());
            filters.put(" kanban.fase in (?,?,?,?,?,?) ", Arrays.asList("FAZER", "DESENVOLVER", "FILA P/ TESTE", "TESTE", "IMPLANTAR", ""));
            total = kanbansatmobweb.contagem(filters, persistencia);
            fazer = true;
            desenvolver = true;
            fila = true;
            teste = true;
            implantar = true;
            feito = false;
            marcarDesmarcar = false;
            onchange = true;
            ticketsUsuario = true;
            novoComentario = new KanBanComent();
            senha = new String();
            pendencia = false;
            usuarios = kanbansatmobweb.carregaListaUsuarios(persistencia);
            logado = false;
            visual = false;

            modelPainel = new DefaultDashboardModel();

            colunaFazer = new DefaultDashboardColumn();
            colunaDesenvolver = new DefaultDashboardColumn();
            colunaFilaTeste = new DefaultDashboardColumn();
            colunaTeste = new DefaultDashboardColumn();
            colunaImplantar = new DefaultDashboardColumn();
            colunaFeito = new DefaultDashboardColumn();

            modelPainel.addColumn(colunaFazer);
            modelPainel.addColumn(colunaDesenvolver);
            modelPainel.addColumn(colunaFilaTeste);
            modelPainel.addColumn(colunaTeste);
            modelPainel.addColumn(colunaImplantar);
            modelPainel.addColumn(colunaFeito);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("kanbansatellite.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    /**
     * Prepara o cadastro de uma senha
     */
    public void preCadastroNovaSenha() {
        this.novaSenha = new String();
        this.email = new String();
        PrimeFaces.current().executeScript("PF('dlgCadastrarSenha').show()");
    }

    /**
     * Cadastra uma nova senha
     */
    public void cadatrarNovaSenha() {
        try {
            this.kanbansatmobweb.cadastrarSenha(this.email, this.novaSenha, this.persistencia);
            this.usuarios = this.kanbansatmobweb.carregaListaUsuarios(this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrarSenha').hide()");
            this.senha = new String();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void fechar() {
        PrimeFaces.current().executeScript("PF('dlgCadastrarSenha').hide();");
        PrimeFaces.current().resetInputs("login");
        PrimeFaces.current().ajax().update("login");
        this.senha = new String();
    }

    /**
     * Concatena o valor do botão pressionado ao campo senha
     *
     * @param valor
     */
    public void apertarBotaoSenha(String valor) {
        if (valor.equals("x") && this.senha.length() > 0) {
            this.senha = this.senha.substring(0, this.senha.length() - 1);
        } else if (!valor.equals("x")) {
            this.senha = this.senha + valor;
        }
        if (this.senha.length() == 6) {
            logar();
        }
    }

    public void alternarVisualizacao() {
        this.visual = !this.visual;
    }

    /**
     * Ação de logar no kanban satellite
     */
    public void logar() {
        try {
            this.login = this.kanbansatmobweb.logar(this.senha, this.persistencia);
            if (null != this.login.getNome() && !this.login.getNome().equals("")) {
                this.usuario = FuncoesString.RecortaAteEspaço(this.login.getNome(), 0, 100);
                this.logado = true;
                this.totalPendentes = this.kanbansatmobweb.contagemPendentes(this.login.getCodigo().toBigInteger().toString(), this.persistencia);
                this.ultimoAcesso = this.kanbansatmobweb.ultimoAcesso(this.login.getCodigo().toBigInteger().toString(), this.persistencia);
                prepararQuadro();
                PrimeFaces.current().ajax().update("cabecalho");
                PrimeFaces.current().ajax().update("main");
                PrimeFaces.current().ajax().update("corporativo");
                PrimeFaces.current().ajax().update("login");
                if (this.pendencia) {
                    this.flag = 2;
                    PrimeFaces.current().ajax().update("formCadastrar");
                }
            } else {
                this.logado = false;
                throw new Exception();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SenhaIncorreta"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            PrimeFaces.current().ajax().update("login:digiteSenha");
            PrimeFaces.current().ajax().update("login:panelSenha");
        } finally {
            this.senha = new String();
        }
    }

    public void handleReorder(DashboardReorderEvent event) {
        this.evento = event;
        PrimeFaces.current().executeScript("PF('dlgOk').show();");
    }

    public void cancelarEdicao() {
        getColuna(this.evento.getSenderColumnIndex()).addWidget(this.evento.getWidgetId());
        getColuna(this.evento.getColumnIndex()).removeWidget(this.evento.getWidgetId());
    }

    public void confirmarEdicao() {
        // Selecionar ticket movimentado
        String id = this.evento.getWidgetId().split("quadro_ticket_")[1];
        for (KanBan k : this.tts) {
            if (id.equals(k.getSequencia().toBigInteger().toString())) {
                this.ticket = k;
                break;
            }
        }

        // Literalmente editar o ticket
        this.ticket.setFase(getFaseFromColuna(this.evento.getColumnIndex()));

        // Salvar as alterações
        editarTicket();
    }

    /**
     * event.getSenderColumnIndex() colunaFazer = index 0; colunaDesenvolver =
     * index 1; colunaFilaTeste = index 2; colunaTeste = index 3;
     * colunaImplantar = index 4; colunaFeito = index 5;
     *
     * @param index
     * @return
     */
    public DashboardColumn getColuna(Integer index) {
        switch (index) {
            case 0:
                return this.colunaFazer;
            case 1:
                return this.colunaDesenvolver;
            case 2:
                return this.colunaFilaTeste;
            case 3:
                return this.colunaTeste;
            case 4:
                return this.colunaImplantar;
            case 5:
                return this.colunaFeito;
            default:
                return null;
        }
    }

    public String getFaseFromColuna(Integer index) {
        switch (index) {
            case 0:
                return "Fazer".toUpperCase();
            case 1:
                return "Desenvolver".toUpperCase();
            case 2:
                return "FilaTeste".toUpperCase();
            case 3:
                return "Teste".toUpperCase();
            case 4:
                return "Implantar".toUpperCase();
            case 5:
                return "Feito".toUpperCase();
            default:
                return null;
        }
    }

    public void prepararQuadro() {
        try {
            this.tts = this.kanbansatmobweb.listarTickets(this.login.getCodigo().toBigInteger().toString(), this.persistencia);

            this.modelPainel = new DefaultDashboardModel();

            this.colunaFazer = new DefaultDashboardColumn();
            this.colunaDesenvolver = new DefaultDashboardColumn();
            this.colunaFilaTeste = new DefaultDashboardColumn();
            this.colunaTeste = new DefaultDashboardColumn();
            this.colunaImplantar = new DefaultDashboardColumn();
            this.colunaFeito = new DefaultDashboardColumn();

            this.painel = new Dashboard();
            for (KanBan tt : tts) {
                Panel panel = (Panel) FacesContext.getCurrentInstance().getApplication().createComponent(FacesContext.getCurrentInstance(), "org.primefaces.component.Panel", "org.primefaces.component.PanelRenderer");
                panel.setId("quadro_ticket_" + tt.getSequencia().toBigInteger());
                panel.setHeader(tt.getDescricao() + " - #" + tt.getSequencia().toBigInteger());
                panel.setClosable(false);
                panel.setToggleable(false);
                panel.setStyleClass("postit");

                HtmlOutputText text = new HtmlOutputText();
                text.setValue("Responsável: " + FuncoesString.RecortaAteEspaço(tt.getNomePessoa(), 0, 100));
                panel.getChildren().add(text);

                this.painel.getChildren().add(panel);

                switch (tt.getFase().toUpperCase()) {
                    case "FAZER":
                        this.colunaFazer.addWidget(panel.getId());
                        break;
                    case "DESENVOLVER":
                        this.colunaDesenvolver.addWidget(panel.getId());
                        break;
                    case "FILA P/ TESTE":
                        this.colunaFilaTeste.addWidget(panel.getId());
                        break;
                    case "TESTE":
                        this.colunaTeste.addWidget(panel.getId());
                        break;
                    case "IMPLANTAR":
                        this.colunaImplantar.addWidget(panel.getId());
                        break;
                    case "FEITO":
                        this.colunaFeito.addWidget(panel.getId());
                        break;
                }
            }

            this.modelPainel.addColumn(this.colunaFazer);
            this.modelPainel.addColumn(this.colunaDesenvolver);
            this.modelPainel.addColumn(this.colunaFilaTeste);
            this.modelPainel.addColumn(this.colunaTeste);
            this.modelPainel.addColumn(this.colunaImplantar);
            this.modelPainel.addColumn(this.colunaFeito);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void handleClickPanel() {
        try {
            String id = this.painelClicado.split("main:quadro_ticket_")[1];
            for (KanBan k : this.tts) {
                if (id.equals(k.getSequencia().toBigInteger().toString())) {
                    this.ticket = k;
                    break;
                }
            }
        } catch (Exception e) {

        }
    }

    public void handleDbClickPanel() {
        try {
            handleClickPanel();
            abrirTicket();
        } catch (Exception e) {

        }
    }

    /**
     * Ação de sair do kanban satellite
     */
    public void sair() {
        this.logado = false;
        this.usuario = null;
        PrimeFaces.current().ajax().update("cabecalho");
        PrimeFaces.current().ajax().update("main");
        PrimeFaces.current().ajax().update("corporativo");
        PrimeFaces.current().ajax().update("login");
        this.flag = 0;
        PrimeFaces.current().ajax().update("formCadastrar");
    }

    /**
     * Carrega a tabela de forma paginada, padrão do Primefaces
     *
     * @return
     */
    public LazyDataModel<KanBan> getAllTickets() {
        if (this.tickets == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabelaKanban");
            this.filters.replace(" kanban.fase in (?,?,?,?,?,?) ", Arrays.asList("FAZER", "DESENVOLVER", "FILA P/ TESTE", "TESTE", "IMPLANTAR", ""));
            this.filters.replace(" kanban.codpessoa = ? ", Arrays.asList(this.login.getCodigo().toBigInteger().toString()));
            dt.setFilters(this.filters);
            this.tickets = new KanBanLazyList(this.persistencia);
        }
        try {
            this.total = this.kanbansatmobweb.contagem(this.filters, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.tickets;
    }

    /**
     * Prepara a tela de edição de um ticket carregando o histório de
     * movimentação e os comentários
     */
    public void abrirTicket() {
        this.onchange = true;
        if (null == this.usuario || this.usuario.equals("null")) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            this.sequencia = null;
            this.flag = 0;
        } else {
            if (null == this.ticket) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneTicket"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                try {
                    this.flag = 2;
                    this.movimentacao = this.kanbansatmobweb.listarMovimentacaoTicket(this.ticket.getSequencia(), this.persistencia);
                    this.comentarios = this.kanbansatmobweb.listarComentarios(this.ticket.getSequencia(), this.persistencia);
                    PrimeFaces.current().resetInputs("formCadastrar");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
                } catch (Exception e) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    log = this.getClass().getSimpleName() + "\r\n"
                            + Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "\r\n" + e.getMessage() + "\r\n";
                    this.logerro.Grava(log, caminho);
                }
            }
        }
    }

    /**
     * Prepara a tela de pesquisa
     */
    public void prePesquisa() {
        this.ticket = new KanBan();
        PrimeFaces.current().executeScript("PF('dlgPesquisar').show();");
    }

    /**
     * Prepara a tela de cadastro
     */
    public void preCadastro() {
        this.ticket = new KanBan();
        this.flag = 3;
        this.onchange = true;
        if (null == this.usuario || this.usuario.equals("null")) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.flag = 1;
            PrimeFaces.current().resetInputs("formCadastrar");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        }
    }

    /**
     * Cadastra um novo ticket
     */
    public void cadastrarTicket() {
        try {
            if (this.logado) {
                if (this.usuario.equals("null")) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                } else {
                    this.kanbansatmobweb.cadastrar(this.ticket, this.usuario, this.persistencia);
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    prepararQuadro();
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Edita um ticket
     */
    public void editarTicket() {
        try {
            if (this.logado) {
                if (this.usuario.equals("null")) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                } else {
                    this.kanbansatmobweb.editar(this.ticket, this.usuario, this.persistencia);
                    prepararQuadro();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Método para os botões de fase no rodapé da página
     */
    public void filtrarFase() {
        this.filters.replace(" kanban.fase in (?,?,?,?,?,?) ", Arrays.asList(
                this.fazer ? "FAZER" : "",
                this.desenvolver ? "DESENVOLVER" : "",
                this.fila ? "FILA P/ TESTE" : "",
                this.teste ? "TESTE" : "",
                this.implantar ? "IMPLANTAR" : "",
                this.feito ? "FEITO" : ""));
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabelaKanban");
        dt.setFilters(this.filters);
        getAllTickets();
        dt.setFirst(0);
    }

    public void filtrarTicktesUsuario() {
        this.filters.replace(" kanban.codpessoa = ? ", this.ticketsUsuario ? Arrays.asList(this.login.getCodigo().toBigInteger().toString())
                : Arrays.asList());
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabelaKanban");
        dt.setFilters(this.filters);
        getAllTickets();
        dt.setFirst(0);
    }

    /**
     * Método que efetua a pesquisa por tickets
     */
    public void pesquisar() {
        if (this.logado) {
            this.ticket.setFase(null == this.ticket.getFase() ? "null" : this.ticket.getFase());
            switch (this.ticket.getFase()) {
                case "FAZER":
                    this.fazer = true;
                    this.desenvolver = false;
                    this.fila = false;
                    this.teste = false;
                    this.implantar = false;
                    this.feito = false;
                    break;
                case "DESENVOLVER":
                    this.fazer = false;
                    this.desenvolver = true;
                    this.fila = false;
                    this.teste = false;
                    this.implantar = false;
                    this.feito = false;
                    break;
                case "FILA P/ TESTE":
                    this.fazer = false;
                    this.desenvolver = false;
                    this.fila = true;
                    this.teste = false;
                    this.implantar = false;
                    this.feito = false;
                    break;
                case "TESTE":
                    this.fazer = false;
                    this.desenvolver = false;
                    this.fila = false;
                    this.teste = true;
                    this.implantar = false;
                    this.feito = false;
                    break;
                case "IMPLANTAR":
                    this.fazer = false;
                    this.desenvolver = false;
                    this.fila = false;
                    this.teste = false;
                    this.implantar = true;
                    this.feito = false;
                    break;
                case "FEITO":
                    this.fazer = false;
                    this.desenvolver = false;
                    this.fila = false;
                    this.teste = false;
                    this.implantar = false;
                    this.feito = true;
                    break;
                default:
                    this.fazer = true;
                    this.desenvolver = true;
                    this.fila = true;
                    this.teste = true;
                    this.implantar = true;
                    this.feito = true;
            }

            this.filters.replace(" kanban.fase in (?,?,?,?,?,?) ", Arrays.asList(
                    this.fazer ? "FAZER" : "",
                    this.desenvolver ? "DESENVOLVER" : "",
                    this.fila ? "FILA P/ TESTE" : "",
                    this.teste ? "TESTE" : "",
                    this.implantar ? "IMPLANTAR" : "",
                    this.feito ? "FEITO" : ""));
            this.filters.replace(" kanban.descricao like ? ", this.ticket.getDescricao().equals("") ? Arrays.asList() : Arrays.asList("%" + this.ticket.getDescricao() + "%"));
            this.filters.replace(" kanban.historico like ? ", this.ticket.getHistorico().equals("") ? Arrays.asList() : Arrays.asList("%" + this.ticket.getHistorico() + "%"));
            this.filters.replace(" kanban.codpessoa = ? ", null == this.ticket.getCodPessoa() ? Arrays.asList() : Arrays.asList(this.ticket.getCodPessoa()));

            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabelaKanban");
            dt.setFilters(this.filters);
            getAllTickets();
            dt.setFirst(0);
        }
    }

    /**
     * Busca um ticket pelo número de sequência dele, e o histórico de
     * movimentação/comentários
     */
    public void buscaTicket() {
        this.senha = new String();
        try {
            if (null != this.sequencia && !this.sequencia.equals("")) {
                this.ticket = this.kanbansatmobweb.buscarTicketSequencia(this.sequencia, this.persistencia);
                this.movimentacao = this.kanbansatmobweb.listarMovimentacaoTicket(new BigDecimal(this.sequencia), this.persistencia);
                this.comentarios = this.kanbansatmobweb.listarComentarios(new BigDecimal(this.sequencia), this.persistencia);
                this.sequencia = null;
                if (this.logado) {
                    this.flag = 2;
                } else {
                    this.pendencia = true;
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Botão de marcar/desmarcar todos os filtros de fase
     */
    public void desmarcarTodos() {
        if (this.marcarDesmarcar) {
            this.fazer = false;
            this.desenvolver = false;
            this.fila = false;
            this.teste = false;
            this.implantar = false;
            this.feito = false;
            this.ticketsUsuario = false;
        } else {
            this.fazer = true;
            this.desenvolver = true;
            this.fila = true;
            this.teste = true;
            this.implantar = true;
            this.feito = true;
            this.ticketsUsuario = true;
        }

        //this.filters.replace(" kanban.codpessoa = ? ", Arrays.asList(this.ticketsUsuario ? this.login.getCodigo().toBigInteger().toString() : ""));
        this.filters.replace(" kanban.fase in (?,?,?,?,?,?) ", Arrays.asList(
                this.fazer ? "FAZER" : "",
                this.desenvolver ? "DESENVOLVER" : "",
                this.fila ? "FILA P/ TESTE" : "",
                this.teste ? "TESTE" : "",
                this.implantar ? "IMPLANTAR" : "",
                this.feito ? "FEITO" : ""));

        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabelaKanban");
        dt.setFilters(this.filters);
        getAllTickets();
        dt.setFirst(0);
    }

    /**
     * Remove todos os filtros
     */
    public void removerTodos() {
        this.filters.replace(" kanban.sequencia = ? ", Arrays.asList());
        this.filters.replace(" kanban.descricao like ? ", Arrays.asList());
        this.filters.replace(" kanban.historico like ? ", Arrays.asList());
        this.filters.replace(" kanban.codpessoa = ? ", Arrays.asList());
        this.filters.replace(" kanban.fase in (?,?,?,?,?,?) ", Arrays.asList("FAZER", "DESENVOLVER", "FILA P/ TESTE", "TESTE", "IMPLANTAR", ""));
        this.fazer = true;
        this.desenvolver = true;
        this.fila = true;
        this.teste = true;
        this.implantar = true;
        this.feito = false;
        this.marcarDesmarcar = false;
        this.ticketsUsuario = false;
        this.removerFiltros = false;
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabelaKanban");
        dt.setFilters(this.filters);
        getAllTickets();
        dt.setFirst(0);
    }

    /**
     * Salva em cookie o usuário selecionado
     *
     * @param event
     */
    public void selecionarUsuario(SelectEvent event) {
        this.usuario = (String) event.getObject();
        if (this.usuario.equals("null")) {
            this.usuario = null;
        }
    }

    /**
     * Prepara o cadacastro de um comentário
     */
    public void preCadastroComentario() {
        if (!this.onchange) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SalveAlteracoes"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else if (null == this.usuario || this.usuario.equals("null")) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            this.novoComentario = new KanBanComent();
            PrimeFaces.current().executeScript("PF('dlgAddComentario').show();");
        }
    }

    /**
     * Adiciona um novo comentário
     */
    public void adicionarNovoComentario() {
        try {
            if (this.usuario.equals("null")) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneUsuario"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.novoComentario.setCodPessoa(new BigDecimal(this.ticket.getCodPessoa()));
                this.novoComentario.setDt_Alter(LocalDate.now());
                this.novoComentario.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.novoComentario.setSequencia(this.ticket.getSequencia());
                this.novoComentario.setOperador(this.usuario);
                this.kanbansatmobweb.inserirComentario(this.novoComentario, this.persistencia);
                this.comentarios = this.kanbansatmobweb.listarComentarios(this.ticket.getSequencia(), this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgAddComentario').hide();");
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void ListarAtas() {
        try {
            this.atas = new ArrayList<>();//this.pstservsatmobweb.listarDocumentos(docto, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public KanBan getTicket() {
        return ticket;
    }

    public void setTicket(KanBan ticket) {
        this.ticket = ticket;
    }

    public List<KanBanMov> getMovimentacao() {
        return movimentacao;
    }

    public void setMovimentacao(List<KanBanMov> movimentacao) {
        this.movimentacao = movimentacao;
    }

    public Boolean getFazer() {
        return fazer;
    }

    public void setFazer(Boolean fazer) {
        this.fazer = fazer;
    }

    public Boolean getDesenvolver() {
        return desenvolver;
    }

    public void setDesenvolver(Boolean desenvolver) {
        this.desenvolver = desenvolver;
    }

    public Boolean getFila() {
        return fila;
    }

    public void setFila(Boolean fila) {
        this.fila = fila;
    }

    public Boolean getTeste() {
        return teste;
    }

    public void setTeste(Boolean teste) {
        this.teste = teste;
    }

    public Boolean getImplantar() {
        return implantar;
    }

    public void setImplantar(Boolean implantar) {
        this.implantar = implantar;
    }

    public Boolean getFeito() {
        return feito;
    }

    public void setFeito(Boolean feito) {
        this.feito = feito;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getSequencia() {
        return sequencia;
    }

    public void setSequencia(String sequencia) {
        this.sequencia = sequencia;
    }

    public Boolean getMarcarDesmarcar() {
        return marcarDesmarcar;
    }

    public void setMarcarDesmarcar(Boolean marcarDesmarcar) {
        this.marcarDesmarcar = marcarDesmarcar;
    }

    public Boolean getOnchange() {
        return onchange;
    }

    public void setOnchange(Boolean onchange) {
        this.onchange = onchange;
        try {
            PrimeFaces.current().ajax().update("formCadastrar:botaoFechar");
            PrimeFaces.current().ajax().update("formCadastrar:btnFecharComImagem");
        } catch (Exception e) {
        }
    }

    public List<KanBanComent> getComentarios() {
        return comentarios;
    }

    public void setComentarios(List<KanBanComent> comentarios) {
        this.comentarios = comentarios;
    }

    public KanBanComent getNovoComentario() {
        return novoComentario;
    }

    public void setNovoComentario(KanBanComent novoComentario) {
        this.novoComentario = novoComentario;
    }

    public Boolean getRemoverFiltros() {
        return removerFiltros;
    }

    public void setRemoverFiltros(Boolean removerFiltros) {
        this.removerFiltros = removerFiltros;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public Boolean getLogado() {
        return logado;
    }

    public void setLogado(Boolean logado) {
        this.logado = logado;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNovaSenha() {
        return novaSenha;
    }

    public void setNovaSenha(String novaSenha) {
        this.novaSenha = novaSenha;
    }

    public List<Pessoa> getUsuarios() {
        return usuarios;
    }

    public void setUsuarios(List<Pessoa> usuarios) {
        this.usuarios = usuarios;
    }

    public DashboardModel getModelPainel() {
        return modelPainel;
    }

    public void setModelPainel(DashboardModel modelPainel) {
        this.modelPainel = modelPainel;
    }

    public Dashboard getPainel() {
        return painel;
    }

    public void setPainel(Dashboard painel) {
        this.painel = painel;
    }

    public int getTotalPendentes() {
        return totalPendentes;
    }

    public void setTotalPendentes(int totalPendentes) {
        this.totalPendentes = totalPendentes;
    }

    public String getUltimoAcesso() {
        return ultimoAcesso;
    }

    public void setUltimoAcesso(String ultimoAcesso) {
        this.ultimoAcesso = ultimoAcesso;
    }

    public String getPainelClicado() {
        return painelClicado;
    }

    public void setPainelClicado(String painelClicado) {
        this.painelClicado = painelClicado;
    }

    public List<KanBan> getTts() {
        return tts;
    }

    public void setTts(List<KanBan> tts) {
        this.tts = tts;
    }

    public Boolean getVisual() {
        return visual;
    }

    public void setVisual(Boolean visual) {
        this.visual = visual;
    }

    public List<KanBanAtas> getAtas() {
        return atas;
    }

    public void setAtas(List<KanBanAtas> atas) {
        this.atas = atas;
    }

    public Boolean getTicketsUsuario() {
        return ticketsUsuario;
    }

    public void setTicketsUsuario(Boolean ticketsUsuario) {
        this.ticketsUsuario = ticketsUsuario;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1040 {

    private int sucesso;
    private String evtTabFuncao_Id;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideFuncao_codFuncao;
    private String ideFuncao_iniValid;
    private String dadosFuncao_dscFuncao;
    private String dadosFuncao_codCBO;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtTabFuncao_Id() {
        return null == evtTabFuncao_Id ? "" : evtTabFuncao_Id;
    }

    public void setEvtTabFuncao_Id(String evtTabFuncao_Id) {
        this.evtTabFuncao_Id = evtTabFuncao_Id;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeFuncao_codFuncao() {
        return null == ideFuncao_codFuncao ? "" : ideFuncao_codFuncao;
    }

    public void setIdeFuncao_codFuncao(String ideFuncao_codFuncao) {
        this.ideFuncao_codFuncao = ideFuncao_codFuncao;
    }

    public String getIdeFuncao_iniValid() {
        return null == ideFuncao_iniValid ? "" : ideFuncao_iniValid;
    }

    public void setIdeFuncao_iniValid(String ideFuncao_iniValid) {
        this.ideFuncao_iniValid = ideFuncao_iniValid;
    }

    public String getDadosFuncao_dscFuncao() {
        return null == dadosFuncao_dscFuncao ? "" : dadosFuncao_dscFuncao;
    }

    public void setDadosFuncao_dscFuncao(String dadosFuncao_dscFuncao) {
        this.dadosFuncao_dscFuncao = dadosFuncao_dscFuncao;
    }

    public String getDadosFuncao_codCBO() {
        return null == dadosFuncao_codCBO ? "" : dadosFuncao_codCBO;
    }

    public void setDadosFuncao_codCBO(String dadosFuncao_codCBO) {
        this.dadosFuncao_codCBO = dadosFuncao_codCBO;
    }
}

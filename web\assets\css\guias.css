/*
*/
/* 
    Created on : Jan 26, 2017, 6:38:51 PM
    Author     : Richard
*/

.tabelaGuia .ui-datatable-scrollable-body{
    height: calc(100vh - 66px - 30px - 28px - 66px - 70px) !important;
    background:transparent;
}

.tabelaGuia .ui-datatable-scrollable-body.toggled{
    height: calc(100vh - 66px - 30px - 28px - 70px) !important;
}

.ui-tabs .ui-tabs-panel {
    padding: 0 !important;
}

.ui-datatable .ui-column-filter{
    display: none;
}


.origem .ui-panelgrid .ui-panelgrid-cell,
.origem {
    padding-right: 0px !important;
}

@media all and (max-width: 768px) {
    .overlay{
        top: 10px !important;
    }
}

.cadastrar, .editar{
    width: 90vw;
    min-width: 100%;
}

@media all and (min-width: 768px) {
    .overlay{
        right: 100px !important;
        left: auto !important;
    }
    .cadastrar{
        width: 700px;
    }
    .editar{
        width: 500px;
    }
}

.origem .ui-autocomplete-panel {
    width: 100% !important;
}
.origem .ui-autocomplete-input{
    width: 100% !important;
}

.upload.ui-fileupload {
    width: 100%;
    height: 100%;
    border: 1px solid grey;
    background: white;
    border-radius: 3px;
    text-align: center;
}

.upload .ui-fileupload-content {
    width: 100%;
    height: 70px;
    border: none;
    background: white;
    border-radius: 3px;
    overflow: auto;
}

.upload .ui-fileupload-content .ui-progressbar,
.upload .ui-fileupload-progress {
    width: 300px;
}

.tabelaArquivos .ui-widget-content{
    background: white;
}
.tabelaArquivos td{
    margin-bottom: 0px;
    border: none !important;
    background: transparent !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaArquivos .ui-datatable-selectable{
    border: none !important;
    background: transparent !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaArquivos .ui-datatable-scrollable-header-box{
    background: white;
}
.tabelaArquivos .ui-datatable-data .ui-widget-content{
    border: none !important;
}
.tabelaArquivos .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.tabelaArquivos .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}

.calendario .ui-inputfield{
    width: 190px !important;
}
.calendariopedido .ui-inputfield{
    width: 100%;
}

.ui-icon-calendar {
    background-image:  url('../img/icone_escaladodia_40.png') !important;
    background-position: center center !important;
    width: 40px !important;
    height: 40px !important;
    left: -1px !important;
    margin-left: 0px !important;
    top: -1px !important;
    margin-top: 0px !important;
}

.ui-icon-calendarios{
    background-image: url('../img/icone_escaladodia_40.png') !important;
    background-position: center center !important;
    width: 40px !important;
    height: 40px !important;
}

.botao{
    width: 40px !important;
    height: 40px !important;
    background: transparent !important;
    border: none;
    align-self: center;
    position: inherit;
    padding: 0;
    margin-right: 0px;
    margin-top: 0px;
    box-shadow: none;
}

.botao .ui-button-text{
    display: none;
}

.botao .ui-icon{
    position: relative;
    left: -1px !important;
    top: 12px !important;
    margin-left: 0px;
    margin-top: -25px;
}

.calendario .ui-state-active, .calendario .ui-state-highlight{
    background-color: #022a48 !important;
    color: white !important;
}

.calendario .ui-datepicker-trigger{
    background: transparent !important;
}


.datagrid table {
    border-collapse: collapse;
    text-align: left;
    width: 100%;
}

.datagrid {
    background: #fff; 
    overflow: hidden; 
    width: 335px;
    border-right: 1px solid black;
    border-left: 1px solid black; 
    font-size: 9px;
}

.datagrid table td, .datagrid table th { 
    padding: 3px 3px; 
}

td{
    padding: 1px !important;
}

.datagrid table tbody td { 
    color: #000000; 
    font-size: 9px;
    font-weight: normal;
    padding: 1px;
}

.caixa {
    border-top: 1px solid black; 
    border-bottom: 1px solid black; 
}

.campo {
    font-style: italic;
}

.campoCabecalho{
    font-style: italic;
    font-size: 9px;
}

.cliente{
    font-size: 11px;
}

.cabecalhoNota table {
    border-collapse: collapse;
    text-align: left;
    width: 100%;
}

.cabecalhoNota {
    background: #fff; 
    overflow: hidden; 
    width: 335px;
    border: none; 
    font-size: 11px;
}

.cabecalhoNota table td, .cabecalhoNota table th { 
    padding: 3px 3px; 
}


.cabecalhoNota table tbody td { 
    color: #000000; 
    font-size: 11px;
    font-weight: normal;
}

.negrito{
    font-size: 10px;
    font-weight: bold;
}

.preencheLinha{
    text-align: justify;
}
.preencheLinha:after{
    content: "";
    display: inline-block;
    width: 100%;
}

.caixaAcima{
    border-bottom: 1px solid black; 
}

.ui-dialog-title{
    width: 85%;
}

.guiaimpressa{
    width: 530px !important;
}

@media print {
    @page { 
        size: A4;
    }

    body{
        margin-top: 19mm !important;
        margin-bottom: 11mm !important;
        margin-left: 19mm !important;
        margin-right: 11mm !important;
    } 

    .guiaimpressa{
        width: 100% !important;
    }

    .preencheLinha{
        text-align: justify;
    }
    .preencheLinha:after{
        content: "";
        display: inline-block;
        width: 100%;
    }

    .campoCabecalho{
        font-style: italic;
        font-size: 9px;
    }

    .negrito{
        font-size: 10px;
        font-weight: bold;
    }

    table{
        width: 100% !important;
        border-collapse: collapse;
    }

    .datagrid table {
        border-collapse: collapse;
        text-align: left;
        width: 100%;
    }

    .datagrid {
        background: #fff; 
        overflow: hidden; 
        width: 335px;
        border-right: 1px solid black;
        border-left: 1px solid black; 
        font-size: 9px;
    }

    .datagrid table td, .datagrid table th { 
        padding: 3px 3px; 
    }

    td{
        padding: 1px !important;
    }

    .datagrid table tbody td { 
        color: #000000; 
        font-size: 9px;
        font-weight: normal;
        padding: 1px;
    }

    .caixa {
        border-top: 1px solid black; 
        border-bottom: 1px solid black; 
    }

    .caixaAcima{
        border-bottom: 1px solid black; 
    }

    .campo {
        font-style: italic;
    }

    .cliente{
        font-size: 11px;
    }

    .cabecalhoNota table {
        border-collapse: collapse;
        text-align: left;
        width: 100%;
    }

    .cabecalhoNota {
        background: #fff; 
        overflow: hidden; 
        width: 335px;
        border: none; 
        font-size: 11px;
    }

    .cabecalhoNota table td, .cabecalhoNota table th { 
        padding: 3px 3px; 
    }

    .cabecalhoNota table tbody td { 
        color: #000000; 
        font-size: 11px;
        font-weight: normal;
    }
}

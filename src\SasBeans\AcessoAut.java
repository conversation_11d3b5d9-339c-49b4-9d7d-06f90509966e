/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class AcessoAut {

    public String Sequencia;
    public String CodFil;
    public String CodPessoa;
    public String Data;
    public String DtValida;
    public String DtInicio;
    public String DtFinal;
    public String SegSex;
    public String Sab;
    public String Dom;
    public String Fer;
    public String Solicitante;
    public String Destino;
    public String Finalidade;
    public String Obs;
    public String Guarita01;
    public String Guarita02;
    public String Guarita03;
    public String Guarita04;
    public String Situacao;
    public String Operador;
    public String Dt_Alter;
    public String Hr_Alter;
    public String OperAut;
    public String Dt_Aut;
    public String Hr_Aut;
    
    public String Nome;
    public String PSituacao;
    public String RG;
    public BigDecimal Sequencia2;
    public BigDecimal CodFil2;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getDtValida() {
        return DtValida;
    }

    public void setDtValida(String DtValida) {
        this.DtValida = DtValida;
    }

    public String getDtInicio() {
        return DtInicio;
    }

    public void setDtInicio(String DtInicio) {
        this.DtInicio = DtInicio;
    }

    public String getDtFinal() {
        return DtFinal;
    }

    public void setDtFinal(String DtFinal) {
        this.DtFinal = DtFinal;
    }

    public String getSegSex() {
        return SegSex;
    }

    public void setSegSex(String SegSex) {
        this.SegSex = SegSex;
    }

    public String getSab() {
        return Sab;
    }

    public void setSab(String Sab) {
        this.Sab = Sab;
    }

    public String getDom() {
        return Dom;
    }

    public void setDom(String Dom) {
        this.Dom = Dom;
    }

    public String getFer() {
        return Fer;
    }

    public void setFer(String Fer) {
        this.Fer = Fer;
    }

    public String getSolicitante() {
        return Solicitante;
    }

    public void setSolicitante(String Solicitante) {
        this.Solicitante = Solicitante;
    }

    public String getDestino() {
        return Destino;
    }

    public void setDestino(String Destino) {
        this.Destino = Destino;
    }

    public String getFinalidade() {
        return Finalidade;
    }

    public void setFinalidade(String Finalidade) {
        this.Finalidade = Finalidade;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getGuarita01() {
        return Guarita01;
    }

    public void setGuarita01(String Guarita01) {
        this.Guarita01 = Guarita01;
    }

    public String getGuarita02() {
        return Guarita02;
    }

    public void setGuarita02(String Guarita02) {
        this.Guarita02 = Guarita02;
    }

    public String getGuarita03() {
        return Guarita03;
    }

    public void setGuarita03(String Guarita03) {
        this.Guarita03 = Guarita03;
    }

    public String getGuarita04() {
        return Guarita04;
    }

    public void setGuarita04(String Guarita04) {
        this.Guarita04 = Guarita04;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOperAut() {
        return OperAut;
    }

    public void setOperAut(String OperAut) {
        this.OperAut = OperAut;
    }

    public String getDt_Aut() {
        return Dt_Aut;
    }

    public void setDt_Aut(String Dt_Aut) {
        this.Dt_Aut = Dt_Aut;
    }

    public String getHr_Aut() {
        return Hr_Aut;
    }

    public void setHr_Aut(String Hr_Aut) {
        this.Hr_Aut = Hr_Aut;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getPSituacao() {
        return PSituacao;
    }

    public void setPSituacao(String PSituacao) {
        this.PSituacao = PSituacao;
    }

    public String getRG() {
        return RG;
    }

    public void setRG(String RG) {
        this.RG = RG;
    }

    public BigDecimal getSequencia2() {
        return Sequencia2;
    }

    public void setSequencia2(BigDecimal Sequencia2) {
        this.Sequencia2 = Sequencia2;
    }

    public BigDecimal getCodFil2() {
        return CodFil2;
    }

    public void setCodFil2(BigDecimal CodFil2) {
        this.CodFil2 = CodFil2;
    }
}

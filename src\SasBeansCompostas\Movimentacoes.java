/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.CtrOperV;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.Regional;

/**
 *
 * <AUTHOR>
 */
public class Movimentacoes {

    private Funcion funcion;
    private CtrOperV ctrOperV;
    private PstServ pstServ;
    private Filiais filiais;
    private Regional regional;

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public CtrOperV getCtrOperV() {
        return ctrOperV;
    }

    public void setCtrOperV(CtrOperV ctrOperV) {
        this.ctrOperV = ctrOperV;
    }

    public PstServ getPstServ() {
        return pstServ;
    }

    public void setPstServ(PstServ pstServ) {
        this.pstServ = pstServ;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public Regional getRegional() {
        return regional;
    }

    public void setRegional(Regional regional) {
        this.regional = regional;
    }

}

<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      style="overflow:hidden !important; max-height:100% !important;"
      xmlns:p="http://primefaces.org/ui">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/faviconSPM.png" />
            <title>#{localemsgs.SPM} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />            
            <link type="text/css" href="../assets/css/menu.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/animate.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <style>
                a{
                    text-decoration: none !important;
                }  
            </style>
        </h:head>
        <h:body id="h">
            <header class="main-header">
                <!-- Logo -->
                <a href="javascript:void(0)" class="logo" style="background-color:#F9FAFC;">
                    <!-- mini logo for sidebar mini 50x50 pixels -->
                    <label class="logo-lg" style="display:block; margin: 0px; margin-left: -10px !important;"><img src="../assets/img/logo_SPMMONETA-lg.png" style="width: 168px; padding:0px !important;" /></label>
                    <!-- logo for regular state and mobile devices -->
                    <span class="logo-lg"><img src="../assets/img/logo_SPMMONETA-mini.png" style="width: 30px;" /></span>
                </a>
                <!-- Header Navbar: style can be found in header.less -->
                <nav class="navbar navbar-static-top" style="background-color:#F36E28;">
                    <div style="position:absolute; left:50px; top:4px;">
                        <label style="font-size:12pt; font-weight: bold; color:#111;display: block; line-height:13px; text-transform: uppercase !important;">#{os_vig.filiais.descricao}</label>
                        <label style="font-size:10pt; font-weight: 500; color:#111;display: block; line-height:8px; text-transform: uppercase !important;">#{os_vig.filiais.endereco}</label>
                        <label style="font-size:10pt; font-weight: 500; color:#111;display: block; line-height:8px; text-transform: uppercase !important;">#{os_vig.filiais.bairro}, #{os_vig.filiais.cidade}/#{os_vig.filiais.UF}</label>
                    </div>
                    <!-- Sidebar toggle button-->
                    <a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button" style="background-color:#F36E28;">
                        <span class="sr-only" style="background-color:#F36E28;">Toggle navigation</span>
                    </a>

                    <div class="navbar-custom-menu">
                        <ul class="nav navbar-nav">
                            <!-- User Account: style can be found in dropdown.less -->
                            <li class="dropdown user user-menu" style="background-color:#F36E28;">
                                <div style="width:40px; height:40px; top:3px; border-radius: 50%; background: url('#{login.getLogo(login.empresa.bancoDados)}') no-repeat center center; background-size: cover; position:absolute; z-index:10;"></div>

                                <a href="#" class="dropdown-toggle" data-toggle="dropdown" style="white-space: nowrap; margin-left:30px !important; font-size:10pt;background-color:#F36E28 !important;">
                                    <span class="hidden-xs">#{login.usuario.nome}</span>
                                </a>
                            </li>
                            <!-- Control Sidebar Toggle Button -->
                            <li>
                                <a href="#" data-toggle="control-sidebar"><i class="fa fa-gears"></i></a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </header>
        </h:body>
    </f:view>
</html>
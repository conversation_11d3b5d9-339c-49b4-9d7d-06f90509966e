/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.cadastros;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import SasBeans.CCusto;
import SasBeans.Filiais;
import SasBeans.Municipios;
import SasBeans.Pessoa;
import SasBeans.SasPWFill;
import SasBeans.Veiculos;
import SasBeans.VeiculosMod;
import SasDaos.MunicipiosDao;
import SasDaos.PessoaDao;
import SasDaos.VeiculosDao;
import br.com.sasw.lazydatamodels.VeiculosLazyList;
import br.com.sasw.pacotesuteis.controller.veiculos.VeiculosSPM;
import br.com.sasw.pacotesuteis.sasdaos.CCustoDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.utils.Logger;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.enterprise.context.SessionScoped;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "veiculos")
@SessionScoped
public class VeiculosMB implements Serializable {

    private Persistencia persistencia;
    private final VeiculosSPM veiculosSPM;
    private final BigDecimal codPessoa;
    private final String banco, operador, caminho;
    private String codfil, log, query, anoComeco, anoFim;
    private int flag, total;
    private final ArquivoLog logerro;
    private Filiais filiais;
    private Map filters;
    private LazyDataModel<Veiculos> veiculos;
    private List<Veiculos> veiculosImportados;
    private Veiculos veiculo;
    private Pessoa motorista;
    private List<Pessoa> listaPessoa;
    private List<VeiculosMod> modelos, buscaModelos;
    private List<Municipios> municipios;
    private Municipios municipio;
    private List<CCusto> ccustos;
    private CCusto ccusto;
    private VeiculosMod modelo, novoModelo;
    private SasPWFill filial;
    private boolean cadastroNovaModelo, viagem, bacen, aeroporto, veicTerceiros;
    private final String FORM_VEICULO = "formVeiculo:cadastrar";
    private final VeiculosDao veiculosDao;
    private final MunicipiosDao municipiosDao;
    private final CCustoDao ccustoDao;
    private final PessoaDao pessoaDao;

    public VeiculosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();

        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.MONTH, Calendar.JANUARY);
        anoComeco = c.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        c.set(Calendar.DAY_OF_MONTH, 31);
        c.set(Calendar.MONTH, Calendar.DECEMBER);
        anoFim = c.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        veiculosSPM = new VeiculosSPM();
        veiculosDao = new VeiculosDao();
        municipiosDao = new MunicipiosDao();
        ccustoDao = new CCustoDao();
        pessoaDao = new PessoaDao();
    }

    public void Persistencia(Persistencia pstLocal) {
        try {
            this.persistencia = pstLocal;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.codfil = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("filial");

            this.filters = new HashMap();
            this.filters.put("Veiculos.CodFil = ?", this.codfil);
            this.filters.put("Veiculos.Situacao = ?", "A");

            this.filiais = this.veiculosSPM.buscaInfoFilial(this.codfil, this.persistencia);

            this.modelos = this.veiculosSPM.listarModelos(this.persistencia);

            this.novoModelo = new VeiculosMod();
            this.novoModelo.setCodigo(-1);
            this.novoModelo.setDescricao(getMessageS("CadastrarNovoModelo"));

            this.municipios = this.municipiosDao.obterMunicipios(this.persistencia);
            this.ccustos = this.ccustoDao.listarCCusto(this.persistencia);
            
            this.motorista = new Pessoa();

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<Veiculos> getAllVeiculos() {
        if (this.veiculos == null) {
            this.filters.replace("Veiculos.CodFil = ?", this.codfil);
            this.filters.replace("Veiculos.Situacao = ?", "A");
            this.veiculos = new VeiculosLazyList(this.persistencia, this.codPessoa, this.filters);
        } else {
            ((VeiculosLazyList) this.veiculos).setFilters(this.filters);
        }
        try {
            this.total = this.veiculosSPM.contagem(this.filters, this.codPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
        }
        return this.veiculos;
    }

    public void novoVeiculo() {
        try {
            this.veiculo = new Veiculos();
            this.municipio = new Municipios();
            this.ccusto = new CCusto();
            this.motorista = new Pessoa();
            this.listaPessoa = new ArrayList<>();

            this.viagem = true;
            this.bacen = true;
            this.aeroporto = true;

            this.veiculo.setBlindCab("5");
            this.veiculo.setBlindTeto("5");
            this.veiculo.setBlindAssoa("5");
            this.veiculo.setBlindCofre("3");
            this.veiculo.setBlindVidro("5");

            this.veiculo.setDt_Ipva(this.anoFim);
            this.veiculo.setDt_SegIni(this.anoComeco);
            this.veiculo.setDt_VencSeg(this.anoFim);
            this.veiculo.setDt_VisPF(this.anoComeco);
            this.veiculo.setDt_VenPF(this.anoFim);
            this.veiculo.setDt_VisQualid(this.anoComeco);
            this.veiculo.setDt_VenQualid(this.anoFim);
            this.veiculo.setDt_VisConfor(this.anoComeco);
            this.veiculo.setDt_VenConfor(this.anoFim);
            this.veiculo.setDt_VisFabric(this.anoComeco);
            this.veiculo.setDt_VenFabric(this.anoFim);

            this.filial = this.veiculosSPM.buscaFilial(this.codfil, this.codPessoa, this.persistencia);
            this.veiculo.setCodFil(this.codfil);

            this.flag = 1;

            PrimeFaces.current().resetInputs(FORM_VEICULO);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, caminho);
        }
    }

    public List<Pessoa> buscarPessoas(String query) {
        this.listaPessoa = new ArrayList<>();
        try {
            this.listaPessoa = this.pessoaDao.buscarPessoaEscalaFuncao(query, "M", this.veiculo.getCodFil().toString(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return listaPessoa;
    }

    public void selecionarFilial(SelectEvent event) {
        try {
            this.filial = ((SasPWFill) event.getObject());
            if (this.filial != null) {
                this.veiculo.setCodFil(this.filial.getCodfilAc());
            } else {
                this.veiculo.setCodFil(null);
            }

            this.motorista = new Pessoa();
            this.veiculo.setMatr_Mot(null);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, caminho);
        }
    }

    public void selecionarMotorista(SelectEvent event) {
        try {
            this.motorista = ((Pessoa) event.getObject());
            if (this.motorista != null) {
                this.veiculo.setMatr_Mot(this.motorista.getMatr().toBigInteger().toString());
            } else {
                this.veiculo.setMatr_Mot(null);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, caminho);
        }
    }

    public List<VeiculosMod> buscarModelos(String q) {
        this.query = q;
        this.buscaModelos = new ArrayList<>();
        try {
            this.buscaModelos = this.modelos.stream().filter(veiculoMod
                    -> veiculoMod.getDescricao().toUpperCase().contains(this.query.toUpperCase())).collect(Collectors.toList());

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
        }
//        this.buscaModelos.add(this.modelo);
        this.buscaModelos.add(this.novoModelo);
        return this.buscaModelos;
    }

    public void selecionarMunicipio(SelectEvent event) {
        try {
            //this.municipio = ((Municipios) event.getObject());
            if (this.municipio != null) {
                this.veiculo.setMun_Placa(this.municipio.getNome());
                this.veiculo.setUF_Placa(this.municipio.getUF());
            } else {
                this.veiculo.setMun_Placa(null);
                this.veiculo.setUF_Placa(null);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, caminho);
        }
    }

    public void selecionarCCusto(SelectEvent event) {
        try {
            /*this.ccusto = ((CCusto) event.getObject());
            if (this.ccusto != null) {
                this.veiculo.setCCusto(this.ccusto.getCCusto());
            } else {
                this.veiculo.setCCusto(null);
            }*/
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, caminho);
        }
    }

    public void selecionarModelo(SelectEvent event) {
        try {
            this.modelo = ((VeiculosMod) event.getObject());
            if (this.modelo.getCodigo() == -1) {
                this.cadastroNovaModelo = true;
                this.modelo = new VeiculosMod();
                this.modelo.setDescricao(this.query.toUpperCase());
            } else {
                this.cadastroNovaModelo = false;
                this.veiculo.setModelo(this.modelo.getCodigo());
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, caminho);
        }
    }

    public void cadastrar() {
        this.veiculo.setOperador(RecortaAteEspaço(this.operador, 0, 10));
        this.veiculo.setCodFil(this.filial.getCodfilAc());

        try {

            if (this.veiculosDao.buscarVeiculo(this.veiculo.getNumero(), this.persistencia) != null) {
                throw new Exception("NumeroVeiculoExiste");
            }

            this.veiculo.setPlaca(this.veiculo.getPlaca().toUpperCase());
            this.veiculo.setObs(this.veiculo.getObs().toUpperCase());
            this.veiculo.setCarroceria(this.veiculo.getCarroceria().toUpperCase());
            this.veiculo.setChassis(this.veiculo.getChassis().toUpperCase());
            this.veiculo.setRENAVAN(this.veiculo.getRENAVAN().toUpperCase());
            this.veiculo.setSeguradora(this.veiculo.getSeguradora().toUpperCase());
            this.veiculo.setDt_Situac(getDataAtual("SQL"));
            this.veiculo.setDt_Alter(getDataAtual("SQL"));
            this.veiculo.setHr_Alter(getDataAtual("HORA"));
            this.veiculo.setOperador(RecortaAteEspaço(this.operador, 0, 10));

            this.veiculo.setVeicTerceiros(this.veicTerceiros ? 1 : 0);
            this.veiculo.setViagem(this.viagem ? "S" : "N");
            this.veiculo.setBacen(this.bacen ? "S" : "N");
            this.veiculo.setAeroporto(this.aeroporto ? "S" : "N");

            this.veiculosDao.insereVeiculoSatMobWeb(this.veiculo, this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.veiculo) + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void editarVeiculo(Veiculos v) {
        try {
            this.veiculo = v;

//            this.novoModelo = new VeiculosMod();
//            this.novoModelo.setCodigo(-1);
//            this.novoModelo.setDescricao(getMessageS("CadastrarNovoModelo"));
//
//            this.modelo = this.veiculosSPM.buscarModelo(this.veiculo.getModelo(), this.persistencia);
//
//            this.cadastroNovaModelo = false;
//
//            this.buscaModelos = new ArrayList<>();
//            this.buscaModelos.add(this.modelo);
//            this.buscaModelos.add(this.novoModelo);
            this.filial = this.veiculosSPM.buscaFilial(this.veiculo.getCodFil().toString(), this.codPessoa, this.persistencia);

            this.municipio = null;
            for (Municipios m : this.municipios) {
                if (m.getNome().toUpperCase().equals(this.veiculo.getMun_Placa().toUpperCase())
                        && m.getUF().toUpperCase().equals(this.veiculo.getUF_Placa().toUpperCase())) {
                    this.municipio = m;
                    break;
                }
            }

            if (this.municipio == null) {
                this.municipio = new Municipios();
                this.municipio.setNome(this.veiculo.getMun_Placa());
                this.municipio.setUF(this.veiculo.getUF_Placa());
            }

            this.ccusto = null;
            for (CCusto c : this.ccustos) {
                if (c.getCCusto().toUpperCase().equals(this.veiculo.getCCusto().toUpperCase())) {
                    this.ccusto = c;
                    break;
                }
            }

            if (this.ccusto == null) {
                this.ccusto = new CCusto();
                this.ccusto.setCCusto(this.veiculo.getCCusto());
            }

            this.motorista = this.pessoaDao.buscaMatr(this.veiculo.getMatr_Mot().toString(), persistencia);

            this.viagem = this.veiculo.getViagem().toUpperCase().equals("S");
            this.bacen = this.veiculo.getBacen().toUpperCase().equals("S");
            this.aeroporto = this.veiculo.getAeroporto().toUpperCase().equals("S");

            
            this.listaPessoa = new ArrayList<>();
            
            this.flag = 2;
            
            PrimeFaces.current().ajax().update("formVeiculo");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, caminho);
        }
    }

    public void editar() {
        this.veiculo.setOperador(RecortaAteEspaço(this.operador, 0, 10));
        this.veiculo.setCodFil(this.filial.getCodfilAc());
        if (null != this.ccusto
                && null != this.ccusto.getCCusto()) {
            this.veiculo.setCCusto(this.ccusto.getCCusto());
        }

        try {

//            if (this.cadastroNovaModelo) {
//                this.modelo.setDt_alter(LocalDate.now());
//                this.modelo.setHr_alter(getDataAtual("HORA"));
//                this.modelo.setOperador(RecortaAteEspaço(this.operador, 0, 10));
//
//                this.modelo.setCodigo(this.veiculosSPM.cadastrarModelo(this.modelo, this.persistencia));
//            }
//
//            this.veiculo.setModelo(this.modelo.getCodigo());
            this.veiculo.setPlaca(this.veiculo.getPlaca().toUpperCase());
            this.veiculo.setObs(this.veiculo.getObs().toUpperCase());
            this.veiculo.setCarroceria(this.veiculo.getCarroceria().toUpperCase());
            this.veiculo.setChassis(this.veiculo.getChassis().toUpperCase());
            this.veiculo.setRENAVAN(this.veiculo.getRENAVAN().toUpperCase());
            this.veiculo.setSeguradora(this.veiculo.getSeguradora().toUpperCase());
            this.veiculo.setDt_Situac(getDataAtual("SQL"));
            this.veiculo.setDt_Alter(getDataAtual("SQL"));
            this.veiculo.setHr_Alter(getDataAtual("HORA"));
            this.veiculo.setOperador(RecortaAteEspaço(this.operador, 0, 10));

            this.veiculo.setVeicTerceiros(this.veicTerceiros ? 1 : 0);
            this.veiculo.setViagem(this.viagem ? "S" : "N");
            this.veiculo.setBacen(this.bacen ? "S" : "N");
            this.veiculo.setAeroporto(this.aeroporto ? "S" : "N");

            this.veiculosDao.atualizarVeiculoSatMobWeb(this.veiculo, this.persistencia);
//            this.veiculosSPM.atualizarVeiculo(this.veiculo, this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.veiculo) + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void novaCargaDados() {
        this.veiculosImportados = new ArrayList<>();
    }

    public void realizarUpload(FileUploadEvent event) {
        try {
            new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\veiculos\\" + getDataAtual("SQL")).mkdirs();
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\veiculos\\" + getDataAtual("SQL") + "\\" + event.getFile().getFileName();
            File file = new File(arquivo);
            if (file.exists()) {
                arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\veiculos\\" + getDataAtual("SQL") + "\\" + getDataAtual("HHMMSS") + event.getFile().getFileName();
                file = new File(arquivo);
            }

            FileOutputStream output = new FileOutputStream(file);
            output.write(event.getFile().getContents());
            output.close();

            FileReader fileReader = new FileReader(file);
            BufferedReader bufferedReader = new BufferedReader(fileReader);
            String linha;
            String[] dados;
            this.veiculosImportados = new ArrayList<>();
            while ((linha = bufferedReader.readLine()) != null) {

                while (linha.contains(";;")) {
                    linha = linha.replace(";;", "; ;");
                }

                dados = linha.split(";");

                if (dados.length != 6) {
                    throw new Exception("ArquivoInvalido", new Throwable("ArquivoIncompleto"));
                }

                this.veiculo = new Veiculos();

                this.veiculo.setNumero(Integer.valueOf(dados[1]));
                this.veiculo.setPlaca(dados[2]);
                this.veiculo.setCodFil(dados[3]);
                this.veiculo.setTipo(dados[4]);
                this.veiculo.setDescricaoModelo(dados[5]);

                this.veiculo.setDt_Alter(getDataAtual("SQL"));
                this.veiculo.setHr_Alter(getDataAtual("HORA"));
                this.veiculo.setOperador(RecortaAteEspaço(this.operador, 0, 10));

                this.veiculosImportados.add(this.veiculo);
            }

            fileReader.close();
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ArquivoSuceso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, caminho);
        }
    }

    public void importarVeiculos() {
        try {
            for (Veiculos v : this.veiculosImportados) {
                try {

                    // Verificação 
                    this.modelo = this.veiculosSPM.buscarModeloDescricao(v.getDescricaoModelo(), this.persistencia);

                    if (this.modelo == null) {
                        this.modelo = new VeiculosMod();
                        this.modelo.setDescricao(v.getDescricaoModelo());
                        this.modelo.setDt_alter(LocalDate.now());
                        this.modelo.setHr_alter(getDataAtual("HORA"));
                        this.modelo.setOperador(RecortaAteEspaço(this.operador, 0, 10));

                        this.modelo.setCodigo(this.veiculosSPM.cadastrarModelo(this.modelo, this.persistencia));
                    }

                    v.setModelo(this.modelo.getCodigo());

                    this.veiculosSPM.inserirVeiculo(v, this.persistencia);

                } catch (Exception e) {
                    this.log = this.getClass().getSimpleName() + "\r\n"
                            + Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "\r\n" + e.getMessage()
                            + "\r\n" + Logger.objeto2String(v);
                    this.logerro.Grava(this.log, caminho);
                }
            }
            novaCargaDados();
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ImportacaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            this.veiculosImportados = new ArrayList<>();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), getMessageS(e.getCause().getMessage()));
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, caminho);
        }
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<VeiculosMod> getModelos() {
        return modelos;
    }

    public void setModelos(List<VeiculosMod> modelos) {
        this.modelos = modelos;
    }

    public VeiculosMod getModelo() {
        return modelo;
    }

    public void setModelo(VeiculosMod modelo) {
        this.modelo = modelo;
    }

    public List<VeiculosMod> getBuscaModelos() {
        return buscaModelos;
    }

    public void setBuscaModelos(List<VeiculosMod> buscaModelos) {
        this.buscaModelos = buscaModelos;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public int getFlag() {
        return flag;
    }

    public boolean isCadastroNovaModelo() {
        return cadastroNovaModelo;
    }

    public void setCadastroNovaModelo(boolean cadastroNovaModelo) {
        this.cadastroNovaModelo = cadastroNovaModelo;
    }

    public Veiculos getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(Veiculos veiculo) {
        this.veiculo = veiculo;
    }

    public List<Veiculos> getVeiculosImportados() {
        return veiculosImportados;
    }

    public void setVeiculosImportados(List<Veiculos> veiculosImportados) {
        this.veiculosImportados = veiculosImportados;
    }

    public List<Municipios> getMunicipios() {
        return municipios;
    }

    public void setMunicipios(List<Municipios> municipios) {
        this.municipios = municipios;
    }

    public Municipios getMunicipio() {
        return municipio;
    }

    public void setMunicipio(Municipios municipio) {
        this.municipio = municipio;
    }

    public List<CCusto> getCcustos() {
        return ccustos;
    }

    public void setCcustos(List<CCusto> ccustos) {
        this.ccustos = ccustos;
    }

    public CCusto getCcusto() {
        return ccusto;
    }

    public void setCcusto(CCusto ccusto) {
        this.ccusto = ccusto;
    }

    public boolean isViagem() {
        return viagem;
    }

    public void setViagem(boolean viagem) {
        this.viagem = viagem;
    }

    public boolean isBacen() {
        return bacen;
    }

    public void setBacen(boolean bacen) {
        this.bacen = bacen;
    }

    public boolean isAeroporto() {
        return aeroporto;
    }

    public void setAeroporto(boolean aeroporto) {
        this.aeroporto = aeroporto;
    }

    public boolean isVeicTerceiros() {
        return veicTerceiros;
    }

    public void setVeicTerceiros(boolean veicTerceiros) {
        this.veicTerceiros = veicTerceiros;
    }

    public Pessoa getMotorista() {
        return motorista;
    }

    public void setMotorista(Pessoa motorista) {
        this.motorista = motorista;
    }

    public List<Pessoa> getListaPessoa() {
        return listaPessoa;
    }

    public void setListaPessoa(List<Pessoa> listaPessoa) {
        this.listaPessoa = listaPessoa;
    }
}

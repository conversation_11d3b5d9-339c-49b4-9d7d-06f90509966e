package br.com.sasw.pacotesuteis.sasdaos.compostas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.CxFSaidas;
import SasBeansCompostas.CxFSaidasCxFGuiasCxFGuiasVol;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CxFSaidasCxFGuiasCxFGuiasvolDao {

    /**
     * Busca todas as guias e lacres de uma remessa
     *
     * @param remessa - código da remessa
     * @param CodFil - código da filial
     * @param persistencia - conexão com o banco
     * @return
     * @throws Exception
     */
    public List<CxFSaidasCxFGuiasCxFGuiasVol> getRemessa(String remessa, BigDecimal CodFil, Persistencia persistencia) throws Exception {
        try {
            List<CxFSaidasCxFGuiasCxFGuiasVol> retorno = new ArrayList();
            String sql = "select CxFSaidas.CodFil, CxFSaidas.SeqRota, CxFGuias.Guia, CxFGuias.Serie, CxFGuias.Valor, CxFGuiasVol.Lacre, cxfsaidas.codremessa from cxfsaidas"
                    + " left join cxfguias on cxfguias.seqrotasai = cxfsaidas.seqrota"
                    + "                   and cxfguias.remessa = cxfsaidas.remessa"
                    + " left join cxfguiasvol on cxfguiasvol.guia = cxfguias.guia"
                    + "                      and cxfguiasvol.serie = cxfguias.serie"
                    + "                      and cxfguiasvol.codfil = cxfguias.codfil"
                    + " where"
                    + " cxfsaidas.codremessa = ?"
                    + " and cxfsaidas.codfil = ?"
                    + " and dt_saida is null";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(remessa);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                CxFSaidasCxFGuiasCxFGuiasVol ret = new CxFSaidasCxFGuiasCxFGuiasVol();
                CxFSaidas cxfsaidas = new CxFSaidas();
                CxFGuias cxfguias = new CxFGuias();
                CxFGuiasVol cxfguiasvol = new CxFGuiasVol();
                cxfsaidas.setCodFil(consult.getString("CodFil"));
                cxfsaidas.setSeqRota(consult.getString("SeqRota"));
                cxfsaidas.setCodRemessa(consult.getString("codremessa"));
                cxfguias.setCodFil(consult.getString("CodFil"));
                cxfguias.setGuia(consult.getString("Guia"));
                cxfguias.setSerie(consult.getString("Serie"));
                cxfguias.setValor(consult.getString("Valor"));
                cxfguiasvol.setCodFil(consult.getString("CodFil"));
                cxfguiasvol.setGuia(consult.getString("Guia"));
                cxfguiasvol.setSerie(consult.getString("Serie"));
                cxfguiasvol.setLacre(consult.getString("Lacre"));
                ret.setCxfguias(cxfguias);
                ret.setCxfsaidas(cxfsaidas);
                ret.setCxfguiasvol(cxfguiasvol);
                retorno.add(ret);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CxFSaidasCxFGuiasCxFGuiasvolDao.getRemessa - " + e.getMessage() + "\r\n"
                    + "select CxFSaidas.CodFil, CxFSaidas.SeqRota, CxFGuias.Guia, CxFGuias.Serie, CxFGuias.Valor, CxFGuiasVol.Lacre from cxfsaidas"
                    + " left join cxfguias on cxfguias.seqrotasai = cxfsaidas.seqrota"
                    + "                   and cxfguias.remessa = cxfsaidas.remessa"
                    + " left join cxfguiasvol on cxfguiasvol.guia = cxfguias.guia"
                    + "                      and cxfguiasvol.serie = cxfguias.serie"
                    + "                      and cxfguiasvol.codfil = cxfguias.codfil"
                    + " where"
                    + " cxfsaidas.codremessa = " + remessa
                    + " and cxfsaidas.codfil = " + CodFil
                    + " and dt_saida is null");
        }
    }
}

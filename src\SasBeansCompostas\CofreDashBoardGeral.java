/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CofreDashBoardGeral {

    private String Hora;
    private String Data;
    private String CofresAtivos;
    private String QtdDepositos;
    private String QtdColetas;

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getCofresAtivos() {
        return CofresAtivos;
    }

    public void setCofresAtivos(String CofresAtivos) {
        this.CofresAtivos = CofresAtivos;
    }

    public String getQtdDepositos() {
        return QtdDepositos;
    }

    public void setQtdDepositos(String QtdDepositos) {
        this.QtdDepositos = QtdDepositos;
    }

    public String getQtdColetas() {
        return QtdColetas;
    }

    public void setQtdColetas(String QtdColetas) {
        this.QtdColetas = QtdColetas;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 29 * hash + Objects.hashCode(this.Hora);
        hash = 29 * hash + Objects.hashCode(this.Data);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CofreDashBoardGeral other = (CofreDashBoardGeral) obj;
        if (!Objects.equals(this.Hora, other.Hora)) {
            return false;
        }
        if (!Objects.equals(this.Data, other.Data)) {
            return false;
        }
        return true;
    }
}

package Controller.Graficos;

import Dados.Persistencia;
import SasBeans.GraficoBean;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.GraficoDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class GraficoSatMobWeb {

    // Lista Hora 50% Ultimo Mes
    //Grafico pizza
    public List<GraficoBean> listarGrafico(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.lista(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    //Horas 100% ultimo Mês
    //Grafico pizza
    public List<GraficoBean> listarGraficoHE100(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.listaHe100(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    //Lista Horas Extras
    //Grafico linha
    public List<GraficoBean> listarGraficoHEs(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.listaHEs(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    //Faltas ultimo Mês
    //Grafico pizza
    public List<GraficoBean> listarGraficoFaltasUltimo(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.faltasUltimo(filtros, persistencia);

        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    //Evolução Faltas
    //Grafico linha
    public List<GraficoBean> listarEvolucaoFaltas(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.listaEvolucaoFaltas(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    // Suspenção Ultimo Mes
    //Grafico pizza
    public List<GraficoBean> listarSuspUltimo(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.listaSuspencaoUltimo(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    //Evolução Suspensões
    //Grafico linha
    public List<GraficoBean> listaEvoSuspensoes(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.evolucaoSuspensoes(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    //Atestados médicos ultimo Mês
    //Grafico pizza
    public List<GraficoBean> listaAtMedicosUltimoMes(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.atestadosMedicosUltimo(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    //Evolução Atestados Médicos
    //Grafico linha
    public List<GraficoBean> listaEvoAtMedicos(Map filtros, Persistencia persistencia) throws Exception {
        try {
            GraficoDao gdao = new GraficoDao();
            return gdao.evolucaoAtestadosMedicos(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    public List<GraficoBean> listagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<GraficoBean> retorno;
            GraficoDao graficoDao = new GraficoDao();
            retorno = graficoDao.listaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;

        } catch (Exception e) {
            throw new Exception("Grafico.falhageral<message>" + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Siates;

import Dados.Persistencia;
import SasBeans.Siates;
import SasDaos.SiatesDao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SiatesSatWeb {

    /**
     * Listagem paginada
     *
     * @param primeiro
     * @param linhas
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Siates> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            SiatesDao siatesDao = new SiatesDao();
            return siatesDao.listaPaginada(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("siates.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Conta a quantidade de chamados.
     *
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            SiatesDao siatesDao = new SiatesDao();
            return siatesDao.totalChamados(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("siates.falhageral<message>" + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.faturamento;

import Dados.Persistencia;
import SasBeans.ExtratoFaturamento;
import SasDaos.FatTVGuiasDao;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ExtratoFaturamentoLazyList extends LazyDataModel<ExtratoFaturamento> {

    private static final long serialVersionUID = 1L;
    private List<ExtratoFaturamento> extratos;
    private final FatTVGuiasDao extratosDao;
    private final Persistencia persistencia;
    private Map filters;

    public ExtratoFaturamentoLazyList(Persistencia pst, Map filters) {
        this.persistencia = pst;
        this.extratosDao = new FatTVGuiasDao();
        this.filters = filters;
    }

    @Override
    public List<ExtratoFaturamento> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.extratos = this.extratosDao.extratoFaturamentoListaPaginada(first, pageSize, this.filters, this.persistencia);

            //set the  total of players
            setRowCount(this.extratosDao.totalExtratoFaturamento(this.filters, this.persistencia));
            //set the page size
            setPageSize(pageSize);

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.extratos;
    }

    @Override
    public Object getRowKey(ExtratoFaturamento extrato) {
        try {
            return extrato.getGuia() + ";" + extrato.getData();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public ExtratoFaturamento getRowData(String extrato) {
        try {
            for (ExtratoFaturamento p : this.extratos) {
                if (p.getGuia().equals(extrato.split(";")[0])
                        && p.getData().equals(extrato.split(";")[1])) {
                    return p;
                }
            }
            return null;
        } catch (Exception e) {
            System.out.println("Extrato: " + extrato + "\r\nERRO: " + e.getMessage());
            return null;
        }
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

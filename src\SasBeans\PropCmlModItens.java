/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class PropCmlModItens {

    BigDecimal Codigo;
    BigDecimal Item;
    String Descricao;
    String Detalhe;
    String Operador;
    LocalDate Dt_alter;
    String Hr_Alter;

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(BigDecimal Codigo) {
        this.Codigo = Codigo;
    }

    public BigDecimal getItem() {
        return Item;
    }

    public void setItem(BigDecimal Item) {
        this.Item = Item;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getDetalhe() {
        return Detalhe;
    }

    public void setDetalhe(String Detalhe) {
        this.Detalhe = Detalhe;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(LocalDate Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

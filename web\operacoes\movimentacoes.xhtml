<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet"  media="print"/>
            <link type="text/css" href="../assets/css/movimentacoes.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{mbmovimentacoes.Persistencias(login.pp)}"/>
            </f:metadata>
            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-12">
                                    <img src="../assets/img/icone_filiais.png" height="40" width="40"/>
                                    #{localemsgs.Movimentacao}
                                </div>
                            </div>
                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{mbmovimentacoes.filialDesc}
                                    </div>
                                    <div class="ui-grid-col-8">
                                        #{localemsgs.Movimentacao}: #{mbmovimentacoes.total}
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>




                <h:form id="main">
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela" value="#{mbmovimentacoes.allMovimentacoes}" paginator="true" rows="15" lazy="true"
                                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Movimentacao}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista" rowKey="#{lista.operador}"
                                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                                 selection="#{mbmovimentacoes.ctrSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true" scrollWidth="100%"
                                                 style="font-size: 12px"> 


                                        <p:ajax event="rowDblselect" listener="#{mbmovimentacoes.dblSelect}" update="cadastroMovimentacao"/>

                                        <p:column headerText="#{localemsgs.numero}" style="width: 5px" exportable="#{mbmovimentacoes.eNumero}">
                                            <h:outputText value="#{lista.numero}"> 
                                                <f:convertNumber pattern="0"/>
                                            </h:outputText>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.CodFil}" style="width: 5px" exportable="#{mbmovimentacoes.eCodfil}">
                                            <h:outputText value="#{lista.codFil}"> 
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>


                                        <p:column headerText="#{localemsgs.Data}" style="width: 15px" exportable="#{mbmovimentacoes.eData}">
                                            <h:outputText value="#{lista.data}" converter="conversorData"/>

                                        </p:column>


                                        <p:column headerText="#{localemsgs.PostoServicos}" style="width: 49px" exportable="#{mbmovimentacoes.ePosto}">
                                            <h:outputText value="#{lista.posto}">

                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Funcionarios}" style="width: 49px" exportable="#{mbmovimentacoes.eFunc}">
                                            <h:outputText value="#{lista.funcAus}">
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" style="width: 49px" exportable="#{mbmovimentacoes.eOperador}">
                                            <h:outputText value="#{lista.operador}"/>
                                        </p:column>
                                    </p:dataTable>

                                </p:panel>
                            </div>
                        </div>
                    </div>


                    <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}"
                                           action="#{mbmovimentacoes.novasMovimentacoes}" update="cadastroMovimentacao">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" update="msgs cadastroMovimentacao cadastroMovimentacao" 
                                           action="#{mbmovimentacoes.buttonAction}">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" action="#{mbmovimentacoes.prePesquisar}"
                                           oncomplete="PF('dlgMovimentacao').show()" update="formMovimentacao">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div> 

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Exportar}" action="#{exportarMB.setTitulo(localemsgs.Clientes)}" oncomplete="PF('dlgExportar').show();" >
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style=" top: 0px; right: 5px; position: fixed">
                            <p:commandLink title="#{localemsgs.Voltar}"
                                           action="#{login.voltar}">
                                <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                    </p:panel>
                </h:form>




                <!-- Cadastro Movimentacao -->

                <h:form  id="cadastroMovimentacao" class="form-inline">

                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                              style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">

                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#cadastroMovimentacao\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icone_filiais.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarMovimentacao}" style="color:#022a48" /> 
                        </f:facet>


                        <p:panel id="idCadastro" style=" background-color: transparent" styleClass="cadastrar">
                            
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-8" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="codfil" value="#{localemsgs.CodFil}: "  />
                                <p:selectOneMenu id="codfil" value="#{mbmovimentacoes.novaMovimentacao.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains" disabled="#{clientes.flag eq 2}">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </p:panelGrid>
                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2, ui-grid-col-3, ui-grid-col-4, ui-grid-col-4, ui-grid-col-4" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="data" value="#{localemsgs.Data}: " />
                                <p:inputMask id="data" mask="99/99/9999" value="#{mbmovimentacoes.novaMovimentacao.data}" 
                                             style="width: 100%" maxlength="8" placeholder="00/00/0000" converter="conversorData">
                                    <p:ajax partialSubmit="true" process="@this" event="blur" />
                                </p:inputMask>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-4" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="escolha" value="#{localemsgs.CodMovimentacao}"/>
                                <p:selectOneMenu id="escolha" value="#{mbmovimentacoes.escolha}"  style="width: 100%" >
                                    <f:selectItem itemLabel="Teste" itemValue="T"/>
                                    <f:selectItem itemLabel="Reforço" itemValue="R" />
                                    <p:ajax update="idCadastro"/>
                                </p:selectOneMenu>
                            </p:panelGrid>


                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-2" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="posto" value="#{localemsgs.Posto}:" rendered="#{mbmovimentacoes.escolha == 'R'}" />


                                <p:inputText id="posto" value="#{mbmovimentacoes.novaMovimentacao.posto}" style="width: 100%"
                                             required="true" label="#{localemsgs.Posto}" disabled="#{movimentacoes}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Posto}"
                                             maxlength="50" rendered="#{mbmovimentacoes.escolha eq 'R'}">

                                </p:inputText> 
                            </p:panelGrid>
                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-6" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="funcionario" value="#{localemsgs.Funcion}:" rendered="#{mbmovimentacoes.escolha eq 'R'}" />
                                <p:autoComplete id="funcionario" value="#{mbmovimentacoes.novaMovimentacao.funcion}" styleClass="funcionario"
                                                style="width: 100%" 
                                                completeMethod="#{mbmovimentacoes.listarFuncionarios}"
                                                required="true" scrollHeight="200" 
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Funcion}" 
                                                forceSelection="true" var="fun" itemLabel="#{fun.nome}" itemValue="#{fun}" converter="conversorFuncion" 
                                                rendered="#{mbmovimentacoes.escolha eq 'R'}" >
                                    <p:ajax event="itemSelect" listener="#{mbmovimentacoes.selecionarFunc}" update="cadastroMovimentacao:funcionario cadastroMovimentacao:nomefuncaus"/>
                                    <p:watermark for="funcaus" value="#{localemsgs.Funcion}" />
                                </p:autoComplete>
                                 
                                <p:inputText id="nomefuncaus" value="#{mbmovimentacoes.funcion.nome_Guer}" disabled="true" 
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Funcion}" required="true" style="width: 100%"
                                             rendered="#{mbmovimentacoes.escolha eq 'R'}">
                                    <p:watermark for="nomefuncaus" value="#{localemsgs.Funcion}" />
                                </p:inputText> 
                                <p:inputText id="codFunc" value="#{mbmovimentacoes.funcion.matr}" disabled="true"
                                             style="width: 100%" placeholder="#{localemsgs.Codigo}">
                                    <f:convertNumber type="number" minFractionDigits="0"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-2" 
                                         layout="grid" styleClass="ui-panelgrid-blank">

                                <p:outputLabel for="operador" value="#{localemsgs.Operador}:" rendered="#{mbmovimentacoes.escolha == 'R'}" />
                                <p:inputText id="operador" value="#{mbmovimentacoes.novaMovimentacao.operador}" style="width: 100%"
                                             required="true" label="#{localemsgs.Operador}" disabled="#{movimentacoes}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Operador}"
                                             maxlength="50" rendered="#{mbmovimentacoes.escolha eq 'R'}">
                                </p:inputText> 

                            </p:panelGrid>
                            <p:panelGrid id="horas" columns="4" columnClasses="ui-grid-col-10, ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:panel style="background: transparent ">
                                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                                        <div style="width: 25%; float: left;">
                                            <p:outputLabel for="hora1" value="#{localemsgs.HoraInicio}:" rendered="#{mbmovimentacoes.escolha eq 'R'}" />
                                        </div>
                                        <div style="width: 25%; float: left; padding-left: 4px;">
                                            <p:inputMask id="hora1" value="#{mbmovimentacoes.novaMovimentacao.hora1}" mask="99:99"
                                                         required="true" placeholder="00:00"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.HoraInicio}"
                                                         size="3" maxlength="4" rendered="#{mbmovimentacoes.escolha eq 'R'}">
                                                <p:ajax partialSubmit="true" process="@this" />
                                            </p:inputMask>
                                        </div>




                                        <p:outputLabel for="hora2" value="#{localemsgs.HoraFim}:" rendered="#{mbmovimentacoes.escolha eq 'R'}"/>

                                        <div style="width: 25%; float: left; padding-left: 4px;">
                                            <p:inputMask id="hora2" value="#{mbmovimentacoes.novaMovimentacao.hora2}" mask="99:99"
                                                         required="true" placeholder="00:00"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora2}"
                                                         size="3" maxlength="4" rendered="#{mbmovimentacoes.escolha eq 'R'}">
                                                <p:ajax partialSubmit="true" process="@this" />
                                            </p:inputMask>
                                        </div>
                                    </div>
                                </p:panel>
                            </p:panelGrid>
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="Obs" value="#{localemsgs.Descricao}:" rendered="#{mbmovimentacoes.escolha eq 'R'}" />
                                <p:inputText value="#{mbmovimentacoes.novaMovimentacao.obs}" id="Obs" required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Obs}"
                                             style="width: 100%" rendered="#{mbmovimentacoes.escolha eq 'R'}">

                                </p:inputText>
                            </p:panelGrid>

                            <p:tab id="documentos" title="#{localemsgs.Documentos}" disabled="#{postoservico.flag eq 1}" rendered="#{mbmovimentacoes.escolha eq 'R'}" >
                                <div style="text-align: justify; width: 100%; padding-bottom: 10px" >
                                    <h:outputText value="#{localemsgs.SelecioneArquivo}:"/>
                                </div>
                                <div  style="text-align: center; width: 100%; height: 150px">
                                    <p:fileUpload id="upload" fileUploadListener="#{postoservico.HandleFileUpload}" rendered="#{mbmovimentacoes.escolha eq 'R'}"
                                                  allowTypes="/(\.|\/)(pdf|jpe?g|xls|xlsx)$/" label="#{localemsgs.Pesquisar}" auto="true"
                                                  invalidFileMessage="#{localemsgs.ArquivoInvalido}" 
                                                  dragDropSupport="true" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                                  update="cadastroMovimentacao:documentos" previewWidth="10" skinSimple="true">
                                        <h:outputText value="#{localemsgs.ArrasteAqui}" id="ArrasteAqui"
                                                      style="text-align: justify; color: black; top: 30px; position: relative;"/>
                                    </p:fileUpload>
                                </div>
                                <p:commandButton value="Inserir Anexo" ajax="false" actionListener="#{mbmovimentacoes.upload}" disabled="true" rendered="#{mbmovimentacoes.escolha eq 'R'}" />
                            </p:tab>
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="email" value="#{localemsgs.Email}:" rendered="#{mbmovimentacoes.escolha eq 'R'}" />
                                <p:inputText id="email" value="#{mbmovimentacoes.emailsEnviar.dest_email}" style="width: 100%"
                                             required="false" label="#{localemsgs.Email}" disabled="#{movimentacoes}" rendered="#{mbmovimentacoes.escolha eq 'R'}"
                                             maxlength="50" >

                                </p:inputText> 
                            </p:panelGrid>
                            <div class="container">
                                <div class="col-sm-3">
                                    <table class="footer-time">
                                        <tr>
                                            <td>
                                                <p:clock pattern="HH:mm:ss" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <h:outputText value="#{localeController.mostraData}" />
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>




                            <div class="form-inline">
                                <p:commandLink rendered="#{mbmovimentacoes.flag eq 1}" id="cadastro" action="#{mbmovimentacoes.cadastrar}" update=":main:tabela :msgs :cabecalho:status" 
                                               title="#{localemsgs.Cadastrar}" >
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink rendered="#{mbmovimentacoes.flag eq 2}" id="edit" action="#{mbmovimentacoes.editar}" update=":msgs :main:tabela :cabecalho:status"
                                               title="#{localemsgs.Editar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>





            
            
            <h:form id="formMovimentacao">
                <p:dialog  widgetVar="dlgMovimentacao" positionType="absolute" responsive="true"
                           draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                           showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                           style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_filiais.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.PesquisarMovimentacao}" style="color:black" /> 
                    </f:facet>

                    <p:panel id="pesquisar" style="background: transparent">
                        <div class="ui-grid-row">
                            <div style="width: 25%; float: left">
                                <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"/>
                            </div>
                            <div style="width: 75%; float: left">
                                <p:selectOneMenu id="codfil" value="#{mbmovimentacoes.novaMovimentacao.codFil}" converter="omnifaces.SelectItemsConverter"
                                                 styleClass="filial"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>


                                    <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                </p:selectOneMenu>
                            </div>
                        </div> 



                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 25%; float: left">
                                <p:outputLabel for="nome" value="#{localemsgs.FuncAus}: "/>
                            </div>
                            <div style="width: 75%; float: left">
                                <p:inputText id="nome" value="#{mbmovimentacoes.novaMovimentacao.funcAus}" label="#{localemsgs.FuncAus}"
                                             style="width: 100%" maxlength="60">
                                    <p:watermark for="nome" value="#{localemsgs.FuncAus}"/>
                                </p:inputText>
                            </div>
                        </div>

                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 25%; float: left">
                                <p:outputLabel for="operador" value="#{localemsgs.Operador}: "/>
                            </div>
                            <div style="width: 75%; float: left">
                                <p:inputText id="operador" value="#{mbmovimentacoes.novaMovimentacao.operador}" label="#{localemsgs.Operador}"
                                             style="width: 100%" maxlength="60">
                                    <p:watermark for="operador" value="#{localemsgs.Operador}"/>
                                </p:inputText>
                            </div>
                        </div>

                        <div class="form-inline">
                            <p:commandLink id="pesquisa" action="#{mbmovimentacoes.pesquisaPaginada}" oncomplete="PF('dlgMovimentacao').hide()"
                                           update=" :main:tabela cabecalho"
                                           title="#{localemsgs.Pesquisar}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>            
                    </p:panel>
                </p:dialog>
            </h:form>

                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" width="400"
                          style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_filiais.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:black" /> 
                    </f:facet>
                    <h:form class="form-inline">
                        <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                        <p:separator />
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="filial" value="#{mbmovimentacoes.eCodfil}">
                                    <p:ajax update="labelFilial"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{mbmovimentacoes.eCodfil eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="codigo" value="#{mbmovimentacoes.eNumero}">
                                    <p:ajax update="labelCodigo"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCodigo" value="#{localemsgs.Codigo}" style="#{mbmovimentacoes.eNumero eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="funcAus" value="#{mbmovimentacoes.eFunc}">
                                    <p:ajax update="labelFuncAus"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFuncAus" value="#{localemsgs.FuncAus}" style="#{mbmovimentacoes.eFunc eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="nred" value="#{mbmovimentacoes.eOperador}">
                                    <p:ajax update="labelNome"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                               <p:outputLabel id="labelNome" value="#{localemsgs.Nome}" style="#{mbmovimentacoes.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>

                        <p:separator />
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:panel style="text-align: center">
                                <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                                <h:commandLink id="pdf" actionListener="#{mbmovimentacoes.atualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_pdf.png"/>
                                    <p:dataExporter target="main:tabela" type="pdf" fileName="#{localemsgs.Movimentacoes}" 
                                                    preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                                </h:commandLink>
                            </p:panel>

      
                        </p:panelGrid>
                    </h:form>
                </p:dialog>



            </div>
            
            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
            
            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>
                <div class="footer-body" id="footer-body">
                    <div>
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.LimparFiltros}: " />
                            <p:selectBooleanCheckbox value="#{filiaisMB.limparFiltros}">
                                <p:ajax update="msgs main:tabela" listener="#{filiaisMB.LimparFiltros}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>











/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class Sindicatos {

    private String Codigo;
    private String Descricao;
    private String PgHE100Dom;
    private String PgHE100Fer;
    private String PgHE100FT;
    private String PgHE100Ger;
    private String PgHE70;
    private String PgARVFerias;
    private String PgARV13;
    private BigDecimal HE70FranquiaDia;
    private BigDecimal HELimite;
    private BigDecimal HELimiteOper;
    private String CompensarHE;
    private Boolean HE100Dom;
    private Boolean HE100Fer;
    private Boolean HE100FT;
    private Boolean HE100Ger;
    private Boolean HEInc;
    private String FolgaxDSR;
    private String PgFerTrab;
    private BigDecimal DivCompISem;
    private String IntrajProp;
    private String IntrajDebHE;
    private String VerbaHE1;
    private String VerbaHE2;
    private String VerbaHE3;
    private String VerbaAdNot;
    private String VerbaHsNotRed;
    private String VerbaHsNotPror;
    private String VerbaIntraJ;
    private String VerbaFaltas;
    private String VerbaSuspensao;
    private String VerbaDescDSR;
    private String VerbaDescVT;
    private String VerbaDescVA;
    private String VerbaDescVR;
    private String VerbaBaseVT;
    private String VerbaBaseVA;
    private String VerbaBaseVR;
    private String VerbaFerTrab;
    private LocalDate Dt_Comemor;
    private String VerbaComemor;
    private String VerbaFaltasJust;
    private String VerbaAtMedico;
    private String VerbaAdTSrv1;
    private String VerbaDesc1;
    private String VerbaDiasTrab1;
    private BigDecimal ValorDiaTrab1;
    private String VerbaDiasTrab2;
    private BigDecimal ValorDiaTrab2;
    private String VerbaHsTrab;
    private String IncidDesc1;
    private LocalDate DtAdmissao1;
    private int QtdeMeses1;
    private BigDecimal PercTSrv1;
    private int LimitePer1;
    private String VerbaAdTSrv2;
    private LocalDate DtAdmissao2;
    private int QtdeMeses2;
    private BigDecimal PercTSrv2;
    private String LimitePer2;
    private BigDecimal SalarioTetoTSrv;
    private String VerbaPPR;
    private int MesPPR;
    private int AnoPPR;
    private BigDecimal ValorPPR;
    private LocalDate DtInicioPPR;
    private LocalDate DtFinalPPR;
    private BigDecimal PerdaFaltaColetiva;
    private BigDecimal PerdaAtrasoColetivo;
    private BigDecimal PercDesc1;
    private String MsgExtrato;
    private String MsgExtrato2;
    private LocalDate Dt_CCT;
    private String CNPJ;
    private String CodSindical;
    private String VerbaDescCS;
    private String CompetContr;
    private BigDecimal DiasTrabVR;
    private BigDecimal DiasSindVR;
    private BigDecimal DiasReciclVR;
    private BigDecimal DiasFerVR;
    private String DescFaltaVR;
    private String DescFaltaJusVR;
    private String DescSuspVR;
    private BigDecimal FranqFaltaVA;
    private String PgAcTrabVA;
    private String PgAfastINSSVA;
    private String PgLicMatVA;
    private BigDecimal DiasCarAcTrab;
    private BigDecimal DiasCarAfastINSSVA;
    private String DescFaltaVA;
    private String DescFaltaJusVA;
    private String DescsuspVA;
    private BigDecimal CodForn;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the Codigo
     */
    public String getCodigo() {
        return Codigo;
    }

    /**
     * @param Codigo the Codigo to set
     */
    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    /**
     * @return the Descricao
     */
    public String getDescricao() {
        return Descricao;
    }

    /**
     * @param Descricao the Descricao to set
     */
    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    /**
     * @return the PgHE100Dom
     */
    public String getPgHE100Dom() {
        return PgHE100Dom;
    }

    /**
     * @param PgHE100Dom the PgHE100Dom to set
     */
    public void setPgHE100Dom(String PgHE100Dom) {
        this.PgHE100Dom = PgHE100Dom;
    }

    /**
     * @return the PgHE100Fer
     */
    public String getPgHE100Fer() {
        return PgHE100Fer;
    }

    /**
     * @param PgHE100Fer the PgHE100Fer to set
     */
    public void setPgHE100Fer(String PgHE100Fer) {
        this.PgHE100Fer = PgHE100Fer;
    }

    /**
     * @return the PgHE100FT
     */
    public String getPgHE100FT() {
        return PgHE100FT;
    }

    /**
     * @param PgHE100FT the PgHE100FT to set
     */
    public void setPgHE100FT(String PgHE100FT) {
        this.PgHE100FT = PgHE100FT;
    }

    /**
     * @return the PgHE100Ger
     */
    public String getPgHE100Ger() {
        return PgHE100Ger;
    }

    /**
     * @param PgHE100Ger the PgHE100Ger to set
     */
    public void setPgHE100Ger(String PgHE100Ger) {
        this.PgHE100Ger = PgHE100Ger;
    }

    /**
     * @return the PgHE70
     */
    public String getPgHE70() {
        return PgHE70;
    }

    /**
     * @param PgHE70 the PgHE70 to set
     */
    public void setPgHE70(String PgHE70) {
        this.PgHE70 = PgHE70;
    }

    /**
     * @return the PgARVFerias
     */
    public String getPgARVFerias() {
        return PgARVFerias;
    }

    /**
     * @param PgARVFerias the PgARVFerias to set
     */
    public void setPgARVFerias(String PgARVFerias) {
        this.PgARVFerias = PgARVFerias;
    }

    /**
     * @return the PgARV13
     */
    public String getPgARV13() {
        return PgARV13;
    }

    /**
     * @param PgARV13 the PgARV13 to set
     */
    public void setPgARV13(String PgARV13) {
        this.PgARV13 = PgARV13;
    }

    /**
     * @return the HE70FranquiaDia
     */
    public BigDecimal getHE70FranquiaDia() {
        return HE70FranquiaDia;
    }

    /**
     * @param HE70FranquiaDia the HE70FranquiaDia to set
     */
    public void setHE70FranquiaDia(String HE70FranquiaDia) {
        try {
            this.HE70FranquiaDia = new BigDecimal(HE70FranquiaDia);
        } catch (Exception e) {
            this.HE70FranquiaDia = new BigDecimal("0");
        }
    }

    /**
     * @return the HELimite
     */
    public BigDecimal getHELimite() {
        return HELimite;
    }

    /**
     * @param HELimite the HELimite to set
     */
    public void setHELimite(String HELimite) {
        try {
            this.HELimite = new BigDecimal(HELimite);
        } catch (Exception e) {
            this.HELimite = new BigDecimal("0");
        }
    }

    /**
     * @return the HELimiteOper
     */
    public BigDecimal getHELimiteOper() {
        return HELimiteOper;
    }

    /**
     * @param HELimiteOper the HELimiteOper to set
     */
    public void setHELimiteOper(String HELimiteOper) {
        try {
            this.HELimiteOper = new BigDecimal(HELimiteOper);
        } catch (Exception e) {
            this.HELimiteOper = new BigDecimal("0");
        }
    }

    /**
     * @return the CompensarHE
     */
    public String getCompensarHE() {
        return CompensarHE;
    }

    /**
     * @param CompensarHE the CompensarHE to set
     */
    public void setCompensarHE(String CompensarHE) {
        this.CompensarHE = CompensarHE;
    }

    /**
     * @return the HE100Dom
     */
    public Boolean getHE100Dom() {
        return HE100Dom;
    }

    /**
     * @param HE100Dom the HE100Dom to set
     */
    public void setHE100Dom(Boolean HE100Dom) {
        this.HE100Dom = HE100Dom;
    }

    /**
     * @return the HE100Fer
     */
    public Boolean getHE100Fer() {
        return HE100Fer;
    }

    /**
     * @param HE100Fer the HE100Fer to set
     */
    public void setHE100Fer(Boolean HE100Fer) {
        this.HE100Fer = HE100Fer;
    }

    /**
     * @return the HE100FT
     */
    public Boolean getHE100FT() {
        return HE100FT;
    }

    /**
     * @param HE100FT the HE100FT to set
     */
    public void setHE100FT(Boolean HE100FT) {
        this.HE100FT = HE100FT;
    }

    /**
     * @return the HE100Ger
     */
    public Boolean getHE100Ger() {
        return HE100Ger;
    }

    /**
     * @param HE100Ger the HE100Ger to set
     */
    public void setHE100Ger(Boolean HE100Ger) {
        this.HE100Ger = HE100Ger;
    }

    /**
     * @return the HEInc
     */
    public Boolean getHEInc() {
        return HEInc;
    }

    /**
     * @param HEInc the HEInc to set
     */
    public void setHEInc(Boolean HEInc) {
        this.HEInc = HEInc;
    }

    /**
     * @return the FolgaxDSR
     */
    public String getFolgaxDSR() {
        return FolgaxDSR;
    }

    /**
     * @param FolgaxDSR the FolgaxDSR to set
     */
    public void setFolgaxDSR(String FolgaxDSR) {
        this.FolgaxDSR = FolgaxDSR;
    }

    /**
     * @return the PgFerTrab
     */
    public String getPgFerTrab() {
        return PgFerTrab;
    }

    /**
     * @param PgFerTrab the PgFerTrab to set
     */
    public void setPgFerTrab(String PgFerTrab) {
        this.PgFerTrab = PgFerTrab;
    }

    /**
     * @return the DivCompISem
     */
    public BigDecimal getDivCompISem() {
        return DivCompISem;
    }

    /**
     * @param DivCompISem the DivCompISem to set
     */
    public void setDivCompISem(String DivCompISem) {
        try {
            this.DivCompISem = new BigDecimal(DivCompISem);
        } catch (Exception e) {
            this.DivCompISem = new BigDecimal("0");
        }
    }

    /**
     * @return the IntrajProp
     */
    public String getIntrajProp() {
        return IntrajProp;
    }

    /**
     * @param IntrajProp the IntrajProp to set
     */
    public void setIntrajProp(String IntrajProp) {
        this.IntrajProp = IntrajProp;
    }

    /**
     * @return the IntrajDebHE
     */
    public String getIntrajDebHE() {
        return IntrajDebHE;
    }

    /**
     * @param IntrajDebHE the IntrajDebHE to set
     */
    public void setIntrajDebHE(String IntrajDebHE) {
        this.IntrajDebHE = IntrajDebHE;
    }

    /**
     * @return the VerbaHE1
     */
    public String getVerbaHE1() {
        return VerbaHE1;
    }

    /**
     * @param VerbaHE1 the VerbaHE1 to set
     */
    public void setVerbaHE1(String VerbaHE1) {
        this.VerbaHE1 = VerbaHE1;
    }

    /**
     * @return the VerbaHE2
     */
    public String getVerbaHE2() {
        return VerbaHE2;
    }

    /**
     * @param VerbaHE2 the VerbaHE2 to set
     */
    public void setVerbaHE2(String VerbaHE2) {
        this.VerbaHE2 = VerbaHE2;
    }

    /**
     * @return the VerbaHE3
     */
    public String getVerbaHE3() {
        return VerbaHE3;
    }

    /**
     * @param VerbaHE3 the VerbaHE3 to set
     */
    public void setVerbaHE3(String VerbaHE3) {
        this.VerbaHE3 = VerbaHE3;
    }

    /**
     * @return the VerbaAdNot
     */
    public String getVerbaAdNot() {
        return VerbaAdNot;
    }

    /**
     * @param VerbaAdNot the VerbaAdNot to set
     */
    public void setVerbaAdNot(String VerbaAdNot) {
        this.VerbaAdNot = VerbaAdNot;
    }

    /**
     * @return the VerbaHsNotRed
     */
    public String getVerbaHsNotRed() {
        return VerbaHsNotRed;
    }

    /**
     * @param VerbaHsNotRed the VerbaHsNotRed to set
     */
    public void setVerbaHsNotRed(String VerbaHsNotRed) {
        this.VerbaHsNotRed = VerbaHsNotRed;
    }

    /**
     * @return the VerbaHsNotPror
     */
    public String getVerbaHsNotPror() {
        return VerbaHsNotPror;
    }

    /**
     * @param VerbaHsNotPror the VerbaHsNotPror to set
     */
    public void setVerbaHsNotPror(String VerbaHsNotPror) {
        this.VerbaHsNotPror = VerbaHsNotPror;
    }

    /**
     * @return the VerbaIntraJ
     */
    public String getVerbaIntraJ() {
        return VerbaIntraJ;
    }

    /**
     * @param VerbaIntraJ the VerbaIntraJ to set
     */
    public void setVerbaIntraJ(String VerbaIntraJ) {
        this.VerbaIntraJ = VerbaIntraJ;
    }

    /**
     * @return the VerbaFaltas
     */
    public String getVerbaFaltas() {
        return VerbaFaltas;
    }

    /**
     * @param VerbaFaltas the VerbaFaltas to set
     */
    public void setVerbaFaltas(String VerbaFaltas) {
        this.VerbaFaltas = VerbaFaltas;
    }

    /**
     * @return the VerbaSuspensao
     */
    public String getVerbaSuspensao() {
        return VerbaSuspensao;
    }

    /**
     * @param VerbaSuspensao the VerbaSuspensao to set
     */
    public void setVerbaSuspensao(String VerbaSuspensao) {
        this.VerbaSuspensao = VerbaSuspensao;
    }

    /**
     * @return the VerbaDescDSR
     */
    public String getVerbaDescDSR() {
        return VerbaDescDSR;
    }

    /**
     * @param VerbaDescDSR the VerbaDescDSR to set
     */
    public void setVerbaDescDSR(String VerbaDescDSR) {
        this.VerbaDescDSR = VerbaDescDSR;
    }

    /**
     * @return the VerbaDescVT
     */
    public String getVerbaDescVT() {
        return VerbaDescVT;
    }

    /**
     * @param VerbaDescVT the VerbaDescVT to set
     */
    public void setVerbaDescVT(String VerbaDescVT) {
        this.VerbaDescVT = VerbaDescVT;
    }

    /**
     * @return the VerbaDescVA
     */
    public String getVerbaDescVA() {
        return VerbaDescVA;
    }

    /**
     * @param VerbaDescVA the VerbaDescVA to set
     */
    public void setVerbaDescVA(String VerbaDescVA) {
        this.VerbaDescVA = VerbaDescVA;
    }

    /**
     * @return the VerbaDescVR
     */
    public String getVerbaDescVR() {
        return VerbaDescVR;
    }

    /**
     * @param VerbaDescVR the VerbaDescVR to set
     */
    public void setVerbaDescVR(String VerbaDescVR) {
        this.VerbaDescVR = VerbaDescVR;
    }

    /**
     * @return the VerbaBaseVT
     */
    public String getVerbaBaseVT() {
        return VerbaBaseVT;
    }

    /**
     * @param VerbaBaseVT the VerbaBaseVT to set
     */
    public void setVerbaBaseVT(String VerbaBaseVT) {
        this.VerbaBaseVT = VerbaBaseVT;
    }

    /**
     * @return the VerbaBaseVA
     */
    public String getVerbaBaseVA() {
        return VerbaBaseVA;
    }

    /**
     * @param VerbaBaseVA the VerbaBaseVA to set
     */
    public void setVerbaBaseVA(String VerbaBaseVA) {
        this.VerbaBaseVA = VerbaBaseVA;
    }

    /**
     * @return the VerbaBaseVR
     */
    public String getVerbaBaseVR() {
        return VerbaBaseVR;
    }

    /**
     * @param VerbaBaseVR the VerbaBaseVR to set
     */
    public void setVerbaBaseVR(String VerbaBaseVR) {
        this.VerbaBaseVR = VerbaBaseVR;
    }

    /**
     * @return the VerbaFerTrab
     */
    public String getVerbaFerTrab() {
        return VerbaFerTrab;
    }

    /**
     * @param VerbaFerTrab the VerbaFerTrab to set
     */
    public void setVerbaFerTrab(String VerbaFerTrab) {
        this.VerbaFerTrab = VerbaFerTrab;
    }

    /**
     * @return the Dt_Comemor
     */
    public LocalDate getDt_Comemor() {
        return Dt_Comemor;
    }

    /**
     * @param Dt_Comemor the Dt_Comemor to set
     */
    public void setDt_Comemor(LocalDate Dt_Comemor) {
        this.Dt_Comemor = Dt_Comemor;
    }

    /**
     * @return the VerbaComemor
     */
    public String getVerbaComemor() {
        return VerbaComemor;
    }

    /**
     * @param VerbaComemor the VerbaComemor to set
     */
    public void setVerbaComemor(String VerbaComemor) {
        this.VerbaComemor = VerbaComemor;
    }

    /**
     * @return the VerbaFaltasJust
     */
    public String getVerbaFaltasJust() {
        return VerbaFaltasJust;
    }

    /**
     * @param VerbaFaltasJust the VerbaFaltasJust to set
     */
    public void setVerbaFaltasJust(String VerbaFaltasJust) {
        this.VerbaFaltasJust = VerbaFaltasJust;
    }

    /**
     * @return the VerbaAtMedico
     */
    public String getVerbaAtMedico() {
        return VerbaAtMedico;
    }

    /**
     * @param VerbaAtMedico the VerbaAtMedico to set
     */
    public void setVerbaAtMedico(String VerbaAtMedico) {
        this.VerbaAtMedico = VerbaAtMedico;
    }

    /**
     * @return the VerbaAdTSrv1
     */
    public String getVerbaAdTSrv1() {
        return VerbaAdTSrv1;
    }

    /**
     * @param VerbaAdTSrv1 the VerbaAdTSrv1 to set
     */
    public void setVerbaAdTSrv1(String VerbaAdTSrv1) {
        this.VerbaAdTSrv1 = VerbaAdTSrv1;
    }

    /**
     * @return the VerbaDesc1
     */
    public String getVerbaDesc1() {
        return VerbaDesc1;
    }

    /**
     * @param VerbaDesc1 the VerbaDesc1 to set
     */
    public void setVerbaDesc1(String VerbaDesc1) {
        this.VerbaDesc1 = VerbaDesc1;
    }

    /**
     * @return the VerbaDiasTrab1
     */
    public String getVerbaDiasTrab1() {
        return VerbaDiasTrab1;
    }

    /**
     * @param VerbaDiasTrab1 the VerbaDiasTrab1 to set
     */
    public void setVerbaDiasTrab1(String VerbaDiasTrab1) {
        this.VerbaDiasTrab1 = VerbaDiasTrab1;
    }

    /**
     * @return the ValorDiaTrab1
     */
    public BigDecimal getValorDiaTrab1() {
        return ValorDiaTrab1;
    }

    /**
     * @param ValorDiaTrab1 the ValorDiaTrab1 to set
     */
    public void setValorDiaTrab1(String ValorDiaTrab1) {
        try {
            this.ValorDiaTrab1 = new BigDecimal(ValorDiaTrab1);
        } catch (Exception e) {
            this.ValorDiaTrab1 = new BigDecimal("0");
        }
    }

    /**
     * @return the VerbaDiasTrab2
     */
    public String getVerbaDiasTrab2() {
        return VerbaDiasTrab2;
    }

    /**
     * @param VerbaDiasTrab2 the VerbaDiasTrab2 to set
     */
    public void setVerbaDiasTrab2(String VerbaDiasTrab2) {
        this.VerbaDiasTrab2 = VerbaDiasTrab2;
    }

    /**
     * @return the ValorDiaTrab2
     */
    public BigDecimal getValorDiaTrab2() {
        return ValorDiaTrab2;
    }

    /**
     * @param ValorDiaTrab2 the ValorDiaTrab2 to set
     */
    public void setValorDiaTrab2(String ValorDiaTrab2) {
        try {
            this.ValorDiaTrab2 = new BigDecimal(ValorDiaTrab2);
        } catch (Exception e) {
            this.ValorDiaTrab2 = new BigDecimal("0");
        }
    }

    /**
     * @return the VerbaHsTrab
     */
    public String getVerbaHsTrab() {
        return VerbaHsTrab;
    }

    /**
     * @param VerbaHsTrab the VerbaHsTrab to set
     */
    public void setVerbaHsTrab(String VerbaHsTrab) {
        this.VerbaHsTrab = VerbaHsTrab;
    }

    /**
     * @return the IncidDesc1
     */
    public String getIncidDesc1() {
        return IncidDesc1;
    }

    /**
     * @param IncidDesc1 the IncidDesc1 to set
     */
    public void setIncidDesc1(String IncidDesc1) {
        this.IncidDesc1 = IncidDesc1;
    }

    /**
     * @return the DtAdmissao1
     */
    public LocalDate getDtAdmissao1() {
        return DtAdmissao1;
    }

    /**
     * @param DtAdmissao1 the DtAdmissao1 to set
     */
    public void setDtAdmissao1(LocalDate DtAdmissao1) {
        this.DtAdmissao1 = DtAdmissao1;
    }

    /**
     * @return the QtdeMeses1
     */
    public int getQtdeMeses1() {
        return QtdeMeses1;
    }

    /**
     * @param QtdeMeses1 the QtdeMeses1 to set
     */
    public void setQtdeMeses1(int QtdeMeses1) {
        this.QtdeMeses1 = QtdeMeses1;
    }

    /**
     * @return the PercTSrv1
     */
    public BigDecimal getPercTSrv1() {
        return PercTSrv1;
    }

    /**
     * @param PercTSrv1 the PercTSrv1 to set
     */
    public void setPercTSrv1(String PercTSrv1) {
        try {
            this.PercTSrv1 = new BigDecimal(PercTSrv1);
        } catch (Exception e) {
            this.PercTSrv1 = new BigDecimal("0");
        }
    }

    /**
     * @return the LimitePer1
     */
    public int getLimitePer1() {
        return LimitePer1;
    }

    /**
     * @param LimitePer1 the LimitePer1 to set
     */
    public void setLimitePer1(int LimitePer1) {
        this.LimitePer1 = LimitePer1;
    }

    /**
     * @return the VerbaAdTSrv2
     */
    public String getVerbaAdTSrv2() {
        return VerbaAdTSrv2;
    }

    /**
     * @param VerbaAdTSrv2 the VerbaAdTSrv2 to set
     */
    public void setVerbaAdTSrv2(String VerbaAdTSrv2) {
        this.VerbaAdTSrv2 = VerbaAdTSrv2;
    }

    /**
     * @return the DtAdmissao2
     */
    public LocalDate getDtAdmissao2() {
        return DtAdmissao2;
    }

    /**
     * @param DtAdmissao2 the DtAdmissao2 to set
     */
    public void setDtAdmissao2(LocalDate DtAdmissao2) {
        this.DtAdmissao2 = DtAdmissao2;
    }

    /**
     * @return the QtdeMeses2
     */
    public int getQtdeMeses2() {
        return QtdeMeses2;
    }

    /**
     * @param QtdeMeses2 the QtdeMeses2 to set
     */
    public void setQtdeMeses2(int QtdeMeses2) {
        this.QtdeMeses2 = QtdeMeses2;
    }

    /**
     * @return the PercTSrv2
     */
    public BigDecimal getPercTSrv2() {
        return PercTSrv2;
    }

    /**
     * @param PercTSrv2 the PercTSrv2 to set
     */
    public void setPercTSrv2(String PercTSrv2) {
        try {
            this.PercTSrv2 = new BigDecimal(PercTSrv2);
        } catch (Exception e) {
            this.PercTSrv2 = new BigDecimal("0");
        }
    }

    /**
     * @return the LimitePer2
     */
    public String getLimitePer2() {
        return LimitePer2;
    }

    /**
     * @param LimitePer2 the LimitePer2 to set
     */
    public void setLimitePer2(String LimitePer2) {
        this.LimitePer2 = LimitePer2;
    }

    /**
     * @return the SalarioTetoTSrv
     */
    public BigDecimal getSalarioTetoTSrv() {
        return SalarioTetoTSrv;
    }

    /**
     * @param SalarioTetoTSrv the SalarioTetoTSrv to set
     */
    public void setSalarioTetoTSrv(String SalarioTetoTSrv) {
        try {
            this.SalarioTetoTSrv = new BigDecimal(SalarioTetoTSrv);
        } catch (Exception e) {
            this.SalarioTetoTSrv = new BigDecimal("0");
        }
    }

    /**
     * @return the VerbaPPR
     */
    public String getVerbaPPR() {
        return VerbaPPR;
    }

    /**
     * @param VerbaPPR the VerbaPPR to set
     */
    public void setVerbaPPR(String VerbaPPR) {
        this.VerbaPPR = VerbaPPR;
    }

    /**
     * @return the MesPPR
     */
    public int getMesPPR() {
        return MesPPR;
    }

    /**
     * @param MesPPR the MesPPR to set
     */
    public void setMesPPR(int MesPPR) {
        this.MesPPR = MesPPR;
    }

    /**
     * @return the AnoPPR
     */
    public int getAnoPPR() {
        return AnoPPR;
    }

    /**
     * @param AnoPPR the AnoPPR to set
     */
    public void setAnoPPR(int AnoPPR) {
        this.AnoPPR = AnoPPR;
    }

    /**
     * @return the ValorPPR
     */
    public BigDecimal getValorPPR() {
        return ValorPPR;
    }

    /**
     * @param ValorPPR the ValorPPR to set
     */
    public void setValorPPR(String ValorPPR) {
        try {
            this.ValorPPR = new BigDecimal(ValorPPR);
        } catch (Exception e) {
            this.ValorPPR = new BigDecimal("0");
        }
    }

    /**
     * @return the DtInicioPPR
     */
    public LocalDate getDtInicioPPR() {
        return DtInicioPPR;
    }

    /**
     * @param DtInicioPPR the DtInicioPPR to set
     */
    public void setDtInicioPPR(LocalDate DtInicioPPR) {
        this.DtInicioPPR = DtInicioPPR;
    }

    /**
     * @return the DtFinalPPR
     */
    public LocalDate getDtFinalPPR() {
        return DtFinalPPR;
    }

    /**
     * @param DtFinalPPR the DtFinalPPR to set
     */
    public void setDtFinalPPR(LocalDate DtFinalPPR) {
        this.DtFinalPPR = DtFinalPPR;
    }

    /**
     * @return the PerdaFaltaColetiva
     */
    public BigDecimal getPerdaFaltaColetiva() {
        return PerdaFaltaColetiva;
    }

    /**
     * @param PerdaFaltaColetiva the PerdaFaltaColetiva to set
     */
    public void setPerdaFaltaColetiva(String PerdaFaltaColetiva) {
        try {
            this.PerdaFaltaColetiva = new BigDecimal(PerdaFaltaColetiva);
        } catch (Exception e) {
            this.PerdaFaltaColetiva = new BigDecimal("0");
        }
    }

    /**
     * @return the PerdaAtrasoColetivo
     */
    public BigDecimal getPerdaAtrasoColetivo() {
        return PerdaAtrasoColetivo;
    }

    /**
     * @param PerdaAtrasoColetivo the PerdaAtrasoColetivo to set
     */
    public void setPerdaAtrasoColetivo(String PerdaAtrasoColetivo) {
        try {
            this.PerdaAtrasoColetivo = new BigDecimal(PerdaAtrasoColetivo);
        } catch (Exception e) {
            this.PerdaAtrasoColetivo = new BigDecimal("0");
        }
    }

    /**
     * @return the PercDesc1
     */
    public BigDecimal getPercDesc1() {
        return PercDesc1;
    }

    /**
     * @param PercDesc1 the PercDesc1 to set
     */
    public void setPercDesc1(String PercDesc1) {
        try {
            this.PercDesc1 = new BigDecimal(PercDesc1);
        } catch (Exception e) {
            this.PercDesc1 = new BigDecimal("0");
        }
    }

    /**
     * @return the MsgExtrato
     */
    public String getMsgExtrato() {
        return MsgExtrato;
    }

    /**
     * @param MsgExtrato the MsgExtrato to set
     */
    public void setMsgExtrato(String MsgExtrato) {
        this.MsgExtrato = MsgExtrato;
    }

    /**
     * @return the MsgExtrato2
     */
    public String getMsgExtrato2() {
        return MsgExtrato2;
    }

    /**
     * @param MsgExtrato2 the MsgExtrato2 to set
     */
    public void setMsgExtrato2(String MsgExtrato2) {
        this.MsgExtrato2 = MsgExtrato2;
    }

    /**
     * @return the Dt_CCT
     */
    public LocalDate getDt_CCT() {
        return Dt_CCT;
    }

    /**
     * @param Dt_CCT the Dt_CCT to set
     */
    public void setDt_CCT(LocalDate Dt_CCT) {
        this.Dt_CCT = Dt_CCT;
    }

    /**
     * @return the CNPJ
     */
    public String getCNPJ() {
        return CNPJ;
    }

    /**
     * @param CNPJ the CNPJ to set
     */
    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    /**
     * @return the CodSindical
     */
    public String getCodSindical() {
        return CodSindical;
    }

    /**
     * @param CodSindical the CodSindical to set
     */
    public void setCodSindical(String CodSindical) {
        this.CodSindical = CodSindical;
    }

    /**
     * @return the VerbaDescCS
     */
    public String getVerbaDescCS() {
        return VerbaDescCS;
    }

    /**
     * @param VerbaDescCS the VerbaDescCS to set
     */
    public void setVerbaDescCS(String VerbaDescCS) {
        this.VerbaDescCS = VerbaDescCS;
    }

    /**
     * @return the CompetContr
     */
    public String getCompetContr() {
        return CompetContr;
    }

    /**
     * @param CompetContr the CompetContr to set
     */
    public void setCompetContr(String CompetContr) {
        this.CompetContr = CompetContr;
    }

    /**
     * @return the DiasTrabVR
     */
    public BigDecimal getDiasTrabVR() {
        return DiasTrabVR;
    }

    /**
     * @param DiasTrabVR the DiasTrabVR to set
     */
    public void setDiasTrabVR(String DiasTrabVR) {
        try {
            this.DiasTrabVR = new BigDecimal(DiasTrabVR);
        } catch (Exception e) {
            this.DiasTrabVR = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasSindVR
     */
    public BigDecimal getDiasSindVR() {
        return DiasSindVR;
    }

    /**
     * @param DiasSindVR the DiasSindVR to set
     */
    public void setDiasSindVR(String DiasSindVR) {
        try {
            this.DiasSindVR = new BigDecimal(DiasSindVR);
        } catch (Exception e) {
            this.DiasSindVR = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasReciclVR
     */
    public BigDecimal getDiasReciclVR() {
        return DiasReciclVR;
    }

    /**
     * @param DiasReciclVR the DiasReciclVR to set
     */
    public void setDiasReciclVR(String DiasReciclVR) {
        try {
            this.DiasReciclVR = new BigDecimal(DiasReciclVR);
        } catch (Exception e) {
            this.DiasReciclVR = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasFerVR
     */
    public BigDecimal getDiasFerVR() {
        return DiasFerVR;
    }

    /**
     * @param DiasFerVR the DiasFerVR to set
     */
    public void setDiasFerVR(String DiasFerVR) {
        try {
            this.DiasFerVR = new BigDecimal(DiasFerVR);
        } catch (Exception e) {
            this.DiasFerVR = new BigDecimal("0");
        }
    }

    /**
     * @return the DescFaltaVR
     */
    public String getDescFaltaVR() {
        return DescFaltaVR;
    }

    /**
     * @param DescFaltaVR the DescFaltaVR to set
     */
    public void setDescFaltaVR(String DescFaltaVR) {
        this.DescFaltaVR = DescFaltaVR;
    }

    /**
     * @return the DescFaltaJusVR
     */
    public String getDescFaltaJusVR() {
        return DescFaltaJusVR;
    }

    /**
     * @param DescFaltaJusVR the DescFaltaJusVR to set
     */
    public void setDescFaltaJusVR(String DescFaltaJusVR) {
        this.DescFaltaJusVR = DescFaltaJusVR;
    }

    /**
     * @return the DescSuspVR
     */
    public String getDescSuspVR() {
        return DescSuspVR;
    }

    /**
     * @param DescSuspVR the DescSuspVR to set
     */
    public void setDescSuspVR(String DescSuspVR) {
        this.DescSuspVR = DescSuspVR;
    }

    /**
     * @return the FranqFaltaVA
     */
    public BigDecimal getFranqFaltaVA() {
        return FranqFaltaVA;
    }

    /**
     * @param FranqFaltaVA the FranqFaltaVA to set
     */
    public void setFranqFaltaVA(String FranqFaltaVA) {
        try {
            this.FranqFaltaVA = new BigDecimal(FranqFaltaVA);
        } catch (Exception e) {
            this.FranqFaltaVA = new BigDecimal("0");
        }
    }

    /**
     * @return the PgAcTrabVA
     */
    public String getPgAcTrabVA() {
        return PgAcTrabVA;
    }

    /**
     * @param PgAcTrabVA the PgAcTrabVA to set
     */
    public void setPgAcTrabVA(String PgAcTrabVA) {
        this.PgAcTrabVA = PgAcTrabVA;
    }

    /**
     * @return the PgAfastINSSVA
     */
    public String getPgAfastINSSVA() {
        return PgAfastINSSVA;
    }

    /**
     * @param PgAfastINSSVA the PgAfastINSSVA to set
     */
    public void setPgAfastINSSVA(String PgAfastINSSVA) {
        this.PgAfastINSSVA = PgAfastINSSVA;
    }

    /**
     * @return the PgLicMatVA
     */
    public String getPgLicMatVA() {
        return PgLicMatVA;
    }

    /**
     * @param PgLicMatVA the PgLicMatVA to set
     */
    public void setPgLicMatVA(String PgLicMatVA) {
        this.PgLicMatVA = PgLicMatVA;
    }

    /**
     * @return the DiasCarAcTrab
     */
    public BigDecimal getDiasCarAcTrab() {
        return DiasCarAcTrab;
    }

    /**
     * @param DiasCarAcTrab the DiasCarAcTrab to set
     */
    public void setDiasCarAcTrab(String DiasCarAcTrab) {
        try {
            this.DiasCarAcTrab = new BigDecimal(DiasCarAcTrab);
        } catch (Exception e) {
            this.DiasCarAcTrab = new BigDecimal("0");
        }
    }

    /**
     * @return the DiasCarAfastINSSVA
     */
    public BigDecimal getDiasCarAfastINSSVA() {
        return DiasCarAfastINSSVA;
    }

    /**
     * @param DiasCarAfastINSSVA the DiasCarAfastINSSVA to set
     */
    public void setDiasCarAfastINSSVA(String DiasCarAfastINSSVA) {
        try {
            this.DiasCarAfastINSSVA = new BigDecimal(DiasCarAfastINSSVA);
        } catch (Exception e) {
            this.DiasCarAfastINSSVA = new BigDecimal("0");
        }
    }

    /**
     * @return the DescFaltaVA
     */
    public String getDescFaltaVA() {
        return DescFaltaVA;
    }

    /**
     * @param DescFaltaVA the DescFaltaVA to set
     */
    public void setDescFaltaVA(String DescFaltaVA) {
        this.DescFaltaVA = DescFaltaVA;
    }

    /**
     * @return the DescFaltaJusVA
     */
    public String getDescFaltaJusVA() {
        return DescFaltaJusVA;
    }

    /**
     * @param DescFaltaJusVA the DescFaltaJusVA to set
     */
    public void setDescFaltaJusVA(String DescFaltaJusVA) {
        this.DescFaltaJusVA = DescFaltaJusVA;
    }

    /**
     * @return the DescsuspVA
     */
    public String getDescsuspVA() {
        return DescsuspVA;
    }

    /**
     * @param DescsuspVA the DescsuspVA to set
     */
    public void setDescsuspVA(String DescsuspVA) {
        this.DescsuspVA = DescsuspVA;
    }

    /**
     * @return the CodForn
     */
    public BigDecimal getCodForn() {
        return CodForn;
    }

    /**
     * @param CodForn the CodForn to set
     */
    public void setCodForn(String CodForn) {
        try {
            this.CodForn = new BigDecimal(CodForn);
        } catch (Exception e) {
            this.CodForn = new BigDecimal("0");
        }
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

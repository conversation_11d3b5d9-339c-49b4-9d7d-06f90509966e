/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.relatorio;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Controller.NFiscal.NFiscalSatMobWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.Rt_Perc;
import SasBeans.NFiscal;
import SasBeans.SasPWFill;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import br.com.sasw.utils.Messages;
import java.util.HashMap;
import java.util.Map;
import org.primefaces.event.SelectEvent;

/**
 *
 * <AUTHOR>
 */
@Named(value = "prefatura")
@ViewScoped
public class PreFaturaMB implements Serializable {

    private Persistencia persistencia;
    private List<Rt_Perc> trajetos;
    private List<NFiscal> nfiscal;
    private BigDecimal codpessoa;
    private final String banco;
    private String codFil, dataTela, caminho, log, portal;
    private ArquivoLog logerro;
    private Date ultimoDia;
    private SasPWFill filial;
    private final RotasSatWeb rotassatweb;
    private final NFiscalSatMobWeb nfiscalsatmobweb;
    private Filiais filiais;

    public PreFaturaMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");

        dataTela = getDataAtual("SQL");
        ultimoDiadoMes();
        rotassatweb = new RotasSatWeb();
        nfiscalsatmobweb = new NFiscalSatMobWeb();
        portal = "N";
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
    }

    /**
     * Procura o último dia do mês atual
     */
    public void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            this.ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.codFil = (FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("filial")).toString();

            this.filiais = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);

            preFatura();

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void detalhesRotasData() {
        try {
            this.trajetos = this.rotassatweb.detalhesRotasData(this.dataTela, this.codFil, this.persistencia);
            //this.nfiscal = this.nfiscalsatmobweb.listaPaginada(1, 10, null , this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preFatura() {
        try {
            this.trajetos = this.rotassatweb.preFatura(this.dataTela, this.codFil, this.codpessoa, this.portal, this.persistencia);            
            //this.nfiscal = this.nfiscalsatmobweb.listaPaginada(1, 10, null , this.codpessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarData(SelectEvent data) {
        this.dataTela = (String) data.getObject();
        preFatura();
    }

    public void dataAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            preFatura();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void impressaoAssinatura() {

    }

    public void dataPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            preFatura();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Rt_Perc> getTrajetos() {
        return trajetos;
    }
       
    public void setTrajetos(List<Rt_Perc> trajetos) {
        this.trajetos = trajetos;
    }

        public List<NFiscal> getNFiscal() {
        return null;
    }
       
    public void setNFiscal(List<NFiscal> nfiscal) {
        this.nfiscal = null;
    }

    
    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public String getPortal() {
        return portal;
    }

    public void setPortal(String portal) {
        this.portal = portal;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }
}

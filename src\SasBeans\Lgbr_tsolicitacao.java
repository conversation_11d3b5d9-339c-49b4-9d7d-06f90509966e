/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class Lgbr_tsolicitacao {

    private String des_terminal;
    private String des_usuario;
    private String cod_reg_env_ns;
    private String sn_tip;
    private String cod_tip_solicitacao;
    private String des_usuario_id;
    private String des_senha_usuario;
    private String des_senha_recebe;
    private String fuso_horario;
    private String des_senha_recebe_fecha;

    /**
     * @return the des_terminal
     */
    public String getDes_terminal() {
        return des_terminal;
    }

    /**
     * @param des_terminal the des_terminal to set
     */
    public void setDes_terminal(String des_terminal) {
        this.des_terminal = des_terminal;
    }

    /**
     * @return the des_usuario
     */
    public String getDes_usuario() {
        return des_usuario;
    }

    /**
     * @param des_usuario the des_usuario to set
     */
    public void setDes_usuario(String des_usuario) {
        this.des_usuario = des_usuario;
    }

    /**
     * @return the cod_reg_env_ns
     */
    public String getCod_reg_env_ns() {
        return cod_reg_env_ns;
    }

    /**
     * @param cod_reg_env_ns the cod_reg_env_ns to set
     */
    public void setCod_reg_env_ns(String cod_reg_env_ns) {
        this.cod_reg_env_ns = cod_reg_env_ns;
    }

    /**
     * @return the sn_tip
     */
    public String getSn_tip() {
        return sn_tip;
    }

    /**
     * @param sn_tip the sn_tip to set
     */
    public void setSn_tip(String sn_tip) {
        this.sn_tip = sn_tip;
    }

    /**
     * @return the cod_tip_solicitacao
     */
    public String getCod_tip_solicitacao() {
        return cod_tip_solicitacao;
    }

    /**
     * @param cod_tip_solicitacao the cod_tip_solicitacao to set
     */
    public void setCod_tip_solicitacao(String cod_tip_solicitacao) {
        this.cod_tip_solicitacao = cod_tip_solicitacao;
    }

    /**
     * @return the des_usuario_id
     */
    public String getDes_usuario_id() {
        return des_usuario_id;
    }

    /**
     * @param des_usuario_id the des_usuario_id to set
     */
    public void setDes_usuario_id(String des_usuario_id) {
        this.des_usuario_id = des_usuario_id;
    }

    /**
     * @return the des_senha_usuario
     */
    public String getDes_senha_usuario() {
        return des_senha_usuario;
    }

    /**
     * @param des_senha_usuario the des_senha_usuario to set
     */
    public void setDes_senha_usuario(String des_senha_usuario) {
        this.des_senha_usuario = des_senha_usuario;
    }

    /**
     * @return the des_senha_recebe
     */
    public String getDes_senha_recebe() {
        return des_senha_recebe;
    }

    /**
     * @param des_senha_recebe the des_senha_recebe to set
     */
    public void setDes_senha_recebe(String des_senha_recebe) {
        this.des_senha_recebe = des_senha_recebe;
    }

    /**
     * @return the fuso_horario
     */
    public String getFuso_horario() {
        return fuso_horario;
    }

    /**
     * @param fuso_horario the fuso_horario to set
     */
    public void setFuso_horario(String fuso_horario) {
        this.fuso_horario = fuso_horario;
    }

    /**
     * @return the des_senha_recebe_fecha
     */
    public String getDes_senha_recebe_fecha() {
        return des_senha_recebe_fecha;
    }

    /**
     * @param des_senha_recebe_fecha the des_senha_recebe_fecha to set
     */
    public void setDes_senha_recebe_fecha(String des_senha_recebe_fecha) {
        this.des_senha_recebe_fecha = des_senha_recebe_fecha;
    }

}

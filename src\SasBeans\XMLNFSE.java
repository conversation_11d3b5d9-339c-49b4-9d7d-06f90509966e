package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class XMLNFSE {

    private String CNPJ;
    private int Praca;
    private String Serie;
    private BigDecimal Numero;
    private String Dt_Nota;
    private String Hr_Nota;
    private String XML_Envio;
    private String XML_Retorno;
    private String Dt_Envio;
    private String Hr_Envio;
    private String Dt_Retorno;
    private String Hr_Retorno;
    private String Protocolo;

    private String NFERetorno;

    public String getCodCidade() {
        return CodCidade;
    }

    public void setCodCidade(String CodCidade) {
        this.CodCidade = CodCidade;
    }
    private String CodCidade;

    /**
     *
     * @return CNPJ
     */
    public String getCNPJ() {
        return CNPJ;
    }

    /**
     * Set CNPJ
     *
     * @param CNPJ
     */
    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    /**
     *
     * @return Praca
     */
    public int getPraca() {
        return Praca;
    }

    /**
     * Set Praca
     *
     * @param Praca
     */
    public void setPraca(int Praca) {
        this.Praca = Praca;
    }

    /**
     *
     * @return Serie
     */
    public String getSerie() {
        return Serie;
    }

    /**
     * Set Serie
     *
     * @param Serie
     */
    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    /**
     *
     * @return Numero
     */
    public BigDecimal getNumero() {
        return Numero;
    }

    /**
     * Set Numero
     *
     * @param Numero
     */
    public void setNumero(String Numero) {
        try {
            this.Numero = new BigDecimal(Numero);
        } catch (Exception e) {
            this.Numero = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Dt_Nota()
     */
    public String getDt_Nota() {
        return Dt_Nota;
    }

    /**
     * Set Dt_Nota
     *
     * @param Dt_Nota
     */
    public void setDt_Nota(String Dt_Nota) {
        this.Dt_Nota = Dt_Nota;
    }

    /**
     *
     * @return Hr_Nota
     */
    public String getHr_Nota() {
        return Hr_Nota;
    }

    /**
     *
     * Set Hr_Nota
     *
     * @param Hr_Nota
     */
    public void setHr_Nota(String Hr_Nota) {
        this.Hr_Nota = Hr_Nota;
    }

    /**
     *
     * @return XML_Envio
     */
    public String getXML_Envio() {
        return XML_Envio;
    }

    /**
     * Set XML_Envio
     *
     * @param XML_Envio
     */
    public void setXML_Envio(String XML_Envio) {
        this.XML_Envio = XML_Envio;
    }

    /**
     *
     * @return XML_Retorno
     */
    public String getXML_Retorno() {
        return XML_Retorno;
    }

    /**
     * Set XML_Retorno
     *
     * @param XML_Retorno
     */
    public void setXML_Retorno(String XML_Retorno) {
        this.XML_Retorno = XML_Retorno;
    }

    /**
     *
     * @return Dt_Envio
     */
    public String getDt_Envio() {
        return Dt_Envio;
    }

    /**
     * Set Dt_Envio
     *
     * @param Dt_Envio
     */
    public void setDt_Envio(String Dt_Envio) {
        this.Dt_Envio = Dt_Envio;
    }

    /**
     *
     * @return Hr_Envio
     */
    public String getHr_Envio() {
        return Hr_Envio;
    }

    /**
     * Set Hr_Envio
     *
     * @param Hr_Envio
     */
    public void setHr_Envio(String Hr_Envio) {
        this.Hr_Envio = Hr_Envio;
    }

    /**
     *
     * @return Dt_Retorno
     */
    public String getDt_Retorno() {
        return Dt_Retorno;
    }

    /**
     * Set Dt_Retorno
     *
     * @param Dt_Retorno
     */
    public void setDt_Retorno(String Dt_Retorno) {
        this.Dt_Retorno = Dt_Retorno;
    }

    /**
     *
     * @return Hr_Retorno
     */
    public String getHr_Retorno() {
        return Hr_Retorno;
    }

    /**
     * Set Hr_Retorno
     *
     * @param Hr_Retorno
     */
    public void setHr_Retorno(String Hr_Retorno) {
        this.Hr_Retorno = Hr_Retorno;
    }

    public String getProtocolo() {
        return Protocolo;
    }

    public void setProtocolo(String Protocolo) {
        this.Protocolo = Protocolo;
    }

    public String getNFERetorno() {
        return NFERetorno;
    }

    public void setNFERetorno(String NFERetorno) {
        this.NFERetorno = NFERetorno;
    }
}

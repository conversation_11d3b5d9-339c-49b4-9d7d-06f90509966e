/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.relatorio;

import Arquivo.ArquivoLog;
import Controller.Inspecoes.InspecoesSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.ColumnModel;
import SasBeans.Filiais;
import SasBeans.Inspecoes;
import SasBeans.InspecoesItens;
import SasBeans.LinhaModel;
import SasBeans.Pessoa;
import SasBeans.PstInspecao;
import SasBeans.PstServ;
import SasBeansCompostas.PstInspecaoRelatorio;
import SasDaos.PstInspecaoDao;
import br.com.sasw.lazydatamodels.PstInspecaoLazyList;
import br.com.sasw.pacotesuteis.sasbeans.InspecoesItensLista;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.LogsSatMobEWDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.utils.Mapas.MARCADOR;
import br.com.sasw.utils.Mascaras;
import static br.com.sasw.utils.Mascaras.Data;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import com.itextpdf.text.BadElementException;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import java.awt.Color;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.export.PDFOptions;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.map.DefaultMapModel;
import org.primefaces.model.map.LatLng;
import org.primefaces.model.map.MapModel;
import org.primefaces.model.map.Marker;

/**
 *
 * <AUTHOR>
 */
@Named(value = "inspecoes")
@ViewScoped
public class InspecoesMB implements Serializable {

    private BigDecimal codPessoa;
    private Persistencia persistencia;
    private String codFil, banco, operador, nomeFilial, caminho, log, dataTela, relatorio, novasOpcoesResposta, centroMapa, zoomMapa, listaTrabalhos, markers, centro,
            htmlTiposInspecoes, htmlInspecoesExec, ultimaDataPesquisada, tipoInspecao, itensNovaInspecao;
    private static Date ultimoDia;
    private ArquivoLog logerro;
    private InspecoesSatWeb inspecoesSatWeb;
    private Map filters;
    private Filiais filiais;
    private List<Inspecoes> inspecoes;
    private Inspecoes inspecaoSelecionada;
    private List<InspecoesItens> itensInspecao;
    private InspecoesItens novoItemInspecao;
    private LazyDataModel<PstInspecao> pstInspecoes;
    private PstInspecao pstInspecaoSelecionada;
    private List<PstInspecaoDetalhes> pstInspecaoSelecionadaDetalhes;
    private List<PstServ> postos;
    private MapModel mapa;
    private List<InspecoesItensLista> itensInsert;
    private boolean mostrarFiliais, limparFiltros;
    private List<PstInspecaoRelatorio> listaRelatorio;
    private final PDFOptions pdfOptions;
    private List<Pessoa> pessoas;
    private Pessoa pessoaSelecionada;
    private List<PstInspecao> pstInspecoesBoletimTrabalho;
    private PstInspecaoDao pstInspecaoDao;
    private List<ColumnModel> columnsGride;
    private List<LinhaModel> dadosGride;
    private ResultSet resultSet;

    public InspecoesMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        try {
            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        } catch (Exception ex) {
        }
        logerro = new ArquivoLog();
        inspecoesSatWeb = new InspecoesSatWeb();
        ultimaDataPesquisada = "";
        mostrarFiliais = false;
        limparFiltros = false;
        itensInsert = new ArrayList<>();
        dataTela = DataAtual.getDataAtual("SQL");
        ultimoDiadoMes();
        htmlInspecoesExec = "";
        mapa = new DefaultMapModel();
        pstInspecaoDao = new PstInspecaoDao();

        // Export
//        pdfOptions = new PDFOptions("HELVETICA", "#FFFFFF", "8");
        pdfOptions = new PDFOptions();
        pdfOptions.setFacetBgColor("#c0c0c0");
        pdfOptions.setFacetFontColor("#303030");
        pdfOptions.setFacetFontStyle("BOLD");
        pdfOptions.setCellFontSize("6");
        pdfOptions.setFacetFontSize("6");
        pdfOptions.setColumnWidths(new float[]{0.09f, 0.06f, 0.05f, 0.03f, 0.08f, 0.14f, 0.08f, 0.06f, 0.02f, 0.05f, 0.05f, 0.24f, 0.05f});
    }

    public void PersistenciaWebPonto(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("index.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.filters = new HashMap();
            this.filters.put("PstInspecao.data = ?", this.dataTela);
            this.filters.put("PstInspecao.codfil = ?", this.codFil);
            this.filters.put("PstInspecao.CodInspecao = ?", "");
            this.filters.put("PstInspecao.secao = ?", "");

            this.postos = this.inspecoesSatWeb.getPstSerList(this.codPessoa, null, this.persistencia);
            this.filiais = this.inspecoesSatWeb.buscaInfoFilial(this.codFil, this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    private void listarItensInspecao() throws Exception {
        String HTML = "";

        for (Inspecoes listaItensInspecoe : pstInspecaoDao.listaItensInspecoes(this.persistencia)) {
            HTML += "<div class=\"col-md-12 col-sm-12 col-xs-12 ItemInspecao\" codigo=\"" + listaItensInspecoe.getCodigo() + "\">";
            HTML += listaItensInspecoe.getDescricao();
            HTML += "</div>";
        }

        if (HTML.equals("")) {
            HTML = Messages.getMessageS("NaoHaDados");
        }

        this.htmlTiposInspecoes = HTML;
    }

    public void listarPerguntas() throws Exception {
        String HTML = "";

        for (InspecoesItens listaInspecoesItens : pstInspecaoDao.listarInspecoesItens(this.tipoInspecao, this.persistencia)) {
            HTML += " <div class=\"col-md-12 col-sm-12 col-xs-12 FundoItem\" codigo=\"" + this.tipoInspecao + "\" sequencia=\"" + listaInspecoesItens.getSequencia() + "\" tipo_resp=\"" + listaInspecoesItens.getTipoResp() + "\">\n";
            HTML += "   <div style=\"width: 100%; padding: 8px 12px 8px 12px\">";
            HTML += "     <label class=\"TituloItem\">" + listaInspecoesItens.getPergunta() + "</label>\n";

            switch (listaInspecoesItens.getTipoResp()) {
                case "0":
                    HTML += " <input type=\"radio\" name=\"optCodigo" + listaInspecoesItens.getSequencia() + "\" class=\"txtItem\" value=\"S\" />" + getMessageS("Sim");
                    HTML += " <input type=\"radio\" name=\"optCodigo" + listaInspecoesItens.getSequencia() + "\" class=\"txtItem\" value=\"N\" />" + getMessageS("Nao");

                    break;

                case "1":
                    HTML += " <input type=\"text\" class=\"txtItem\" placeholder=\"" + getMessageS("ToqueAquiResponder") + "\" />\n";
                    break;

                case "2":
                    HTML += " <input type=\"text\" class=\"txtItem\" placeholder=\"" + getMessageS("ToqueAquiResponder") + "\" />\n";
                    break;

                case "3":

                    break;

                case "4":
                    HTML += " <label class=\"txtItem\" ref=\"Assinatura\">" + getMessageS("ToqueAquiAssinar") + "</label>\n";
                    break;

                case "5":
                    HTML += " <input type=\"date\" class=\"txtItem\" placeholder=\"" + getMessageS("ToqueAquiResponder") + "\" />\n";
                    break;

                case "6":

                    break;
            }
            HTML += "   </div>";

            if (!listaInspecoesItens.getFoto().equals("0") || !listaInspecoesItens.getVideo().equals("0")) {
                for (int I = 1; I <= Integer.parseInt(listaInspecoesItens.getFoto()); I++) {
                    HTML += "<div class=\"col-md-12 col-sm-12 col-xs-12 FundoFoto\" tipo=\"foto\">";
                    HTML += "  <table>";
                    HTML += "    <tr>";
                    HTML += "      <td><i class=\"fa fa-camera\" aria-hidden=\"true\"></i><br>" + getMessageS("ToqueTirarFoto") + "</td>";
                    HTML += "    <tr>";
                    HTML += "  </table>";
                    HTML += "</div>";
                }

                /*for (int I = 1; I <= Integer.parseInt(listaInspecoesItens.getVideo()); I++) {
                    HTML += "<div class=\"col-md-12 col-sm-12 col-xs-12 FundoFoto\" tipo=\"video\">";
                    HTML += "  <table>";
                    HTML += "    <tr>";
                    HTML += "      <td><i class=\"fa fa-camera\" aria-hidden=\"true\"></i><br>" + getMessageS("ToqueGravarVideo") + "</td>";
                    HTML += "    <tr>";
                    HTML += "  </table>";
                    HTML += "</div>";
                }*/
            }

            HTML += " </div>";
        }

        this.itensNovaInspecao = HTML;
        //PrimeFaces.current().ajax().update("frmInspecao");
    }

    public void listarExecutados(boolean PrimeiroCarregamento) throws Exception {
        FacesContext fc = FacesContext.getCurrentInstance();
        String HTML = "", UltimaDataLoop = "";
        if (this.ultimaDataPesquisada.equals("")) {
            this.ultimaDataPesquisada = this.dataTela;
        }

        for (PstInspecao listaInspecoesExe : pstInspecaoDao.listaInspecoesExec(this.ultimaDataPesquisada, (String) fc.getExternalContext().getSessionMap().get("CodFil"), this.persistencia)) {
            if (PrimeiroCarregamento || (!PrimeiroCarregamento && !listaInspecoesExe.getData().equals(this.ultimaDataPesquisada))) {
                if (!UltimaDataLoop.equals(listaInspecoesExe.getData())) {
                    HTML += "<div class=\"col-md-12 col-sm-12 col-xs-12 DataInspecao\">" + Data(listaInspecoesExe.getData()) + "</div>\n";
                }

                if (!listaInspecoesExe.getPergunta().equals("")) {
                    HTML += "                <div class=\"col-md-12 col-sm-12 col-xs-12 DescricaoInspecao\">\n"
                            + "                    <label><b>" + listaInspecoesExe.getPergunta() + "</b></label>\n"
                            + "                    <label>" + listaInspecoesExe.getLocal() + "</label>\n"
                            + "                    <label ref=\"Inspetor\">" + Messages.getMessageS("Inspetor") + ": " + listaInspecoesExe.getInspetor() + "</label>\n"
                            + "                </div>";
                }
            }

            UltimaDataLoop = listaInspecoesExe.getData();
        }

        this.ultimaDataPesquisada = UltimaDataLoop;

        if (HTML.equals("")) {
            HTML = Messages.getMessageS("NaoHaDados");
        }

        if (!PrimeiroCarregamento) {
            //PrimeFaces.current().executeScript("EscreverHTML('" + HTML + "')");
        }

        //PrimeFaces.current().ajax().update("frmInspecao");
        this.htmlInspecoesExec += HTML;
    }

    public void carregarPaginaInspecoesMob() throws Exception {
        listarItensInspecao();
        listarExecutados(true);
    }

    /**
     * Lista todas as inspeções
     */
    public void listarInspecoes() {
        try {
            this.inspecoes = this.inspecoesSatWeb.getAllInspecoes(this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgListarInspecoes').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listarInspecaoRelatorio() {
        try {
            listaRelatorio = inspecoesSatWeb.listaInspecaoRelatorio(codFil, dataTela, persistencia);
            PrimeFaces.current().executeScript("PF('dlgExportar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void listarInspecaoRelatorioDinamico() {
        try {

            columnsGride = new ArrayList<>();
            dadosGride = new ArrayList<>();
            columnsGride = pstInspecaoDao.listaInspecaoRelatorioColunadoCabecalho(codFil, dataTela, persistencia);
            dadosGride = pstInspecaoDao.listaInspecaoRelatorioColunadoDados(codFil, dataTela, persistencia);
            // Criar Colunas/Cabeçalhos
           /* columnsGride = new ArrayList<>();
            String name = "";

            while (resultSet.next()) {
                if (name.equals("")) {
                    ResultSetMetaData rsmd = resultSet.getMetaData();
                    int columnCount = rsmd.getColumnCount();

                    for (int i = 1; i <= columnCount; i++) {
                        name = rsmd.getColumnName(i);
                        columnsGride.add(new ColumnModel(name, name));
                    }
                }
            }*/

            PrimeFaces.current().executeScript("PF('dlgExportar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void prepararFiltro() {
        try {
            this.pstInspecaoSelecionada = new PstInspecao();
            this.inspecoes = this.inspecoesSatWeb.getAllInspecoes(this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgPesquisar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void prepararNovaInspecao() {
        this.inspecaoSelecionada = new Inspecoes();
        PrimeFaces.current().resetInputs("formAdicionarInspecao");
        PrimeFaces.current().executeScript("PF('dlgAdicionarInspecao').show();");
    }

    public void cadastrarInspecao() {
        try {
            this.inspecaoSelecionada.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.inspecaoSelecionada.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.inspecaoSelecionada.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.inspecoesSatWeb.inserirInspecao(this.inspecaoSelecionada, this.persistencia);
            this.inspecoes = this.inspecoesSatWeb.getAllInspecoes(this.persistencia);

            this.inspecaoSelecionada = new Inspecoes();
            PrimeFaces.current().executeScript("PF('dlgAdicionarInspecao').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void apagarInspecao() {
        if (null == this.inspecaoSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneInspecao"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.inspecoesSatWeb.removerInspecao(this.inspecaoSelecionada, this.persistencia);
                this.inspecoes = this.inspecoesSatWeb.getAllInspecoes(this.persistencia);

                this.inspecaoSelecionada = new Inspecoes();
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void dblSelectInspecao(SelectEvent event) {
        this.inspecaoSelecionada = (Inspecoes) event.getObject();
        abrirInspecao(null);
    }

    public void abrirInspecao(ActionEvent actionEvent) {
        if (null == this.inspecaoSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneInspecao"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.itensInspecao = this.inspecoesSatWeb.getAllInspecoesItens(this.inspecaoSelecionada.getCodigo(), this.persistencia);

                this.novoItemInspecao = new InspecoesItens();
                PrimeFaces.current().executeScript("PF('dlgInspecaoItens').show()");
//                PrimeFaces.current().ajax().update("panelItensInspecao");
                PrimeFaces.current().executeScript("SubstituirTraducao();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        }
    }

    public void cadastrarItemInspecao() {
        try {
            if (null == this.novoItemInspecao.getPergunta() || this.novoItemInspecao.getPergunta().equals("")) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("Pergunta"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.novoItemInspecao.setCodigo(this.inspecaoSelecionada.getCodigo());
                this.novoItemInspecao.setObrigatorio(this.novoItemInspecao.getObrigatorio().equals("true") ? "1" : "0");
                this.novoItemInspecao.setFoto((this.novoItemInspecao.getFoto() != null && this.novoItemInspecao.getFoto().equals("true")) ? "1" : "0");
                this.novoItemInspecao.setVideo((this.novoItemInspecao.getVideo() != null && this.novoItemInspecao.getVideo().equals("true")) ? "1" : "0");
                this.novoItemInspecao.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.novoItemInspecao.setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.novoItemInspecao.setHr_Alter(DataAtual.getDataAtual("HORA"));

                if (this.novoItemInspecao.getTipoResp().equals("8")) {
                    tratarOpcoesInspecao(this.novasOpcoesResposta);
                } else {
                    itensInsert = new ArrayList<>();
                }

                this.inspecoesSatWeb.inserirInspecaoItem(this.novoItemInspecao, this.itensInsert, this.persistencia);
                this.itensInspecao = this.inspecoesSatWeb.getAllInspecoesItens(this.inspecaoSelecionada.getCodigo(), this.persistencia);

                this.novoItemInspecao = new InspecoesItens();
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
//            PrimeFaces.current().executeScript("PF('dlgAdicionarInspecao').hide();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void removerItemInspecao(InspecoesItens inspecaoItem) {
        try {
            this.inspecoesSatWeb.removerInspecaoItem(inspecaoItem, this.persistencia);
            this.itensInspecao = this.inspecoesSatWeb.getAllInspecoesItens(this.inspecaoSelecionada.getCodigo(), this.persistencia);

            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private void tratarOpcoesInspecao(String Dados) {
        String[] BaseNovasOpcoes = Dados.split("sasw_separator2");
        itensInsert = new ArrayList<>();
        InspecoesItensLista item;
        int Ordem = 1;

        try {
            for (int I = 0; I < BaseNovasOpcoes.length; I++) {
                if (null != BaseNovasOpcoes[I]
                        && !BaseNovasOpcoes[I].equals("")
                        && ((null != BaseNovasOpcoes[I].split("sasw_separator1")[0]
                        && !BaseNovasOpcoes[I].split("sasw_separator1")[0].equals(""))
                        || (null != BaseNovasOpcoes[I].split("sasw_separator1")[1]
                        && !BaseNovasOpcoes[I].split("sasw_separator1")[1].equals("")))) {
                    item = new InspecoesItensLista();

                    item.setCodigo(this.inspecaoSelecionada.getCodigo());
                    item.setSequencia("");
                    try {
                        if (null != BaseNovasOpcoes[I].split("sasw_separator1")[0]
                                && !BaseNovasOpcoes[I].split("sasw_separator1")[0].equals("")) {
                            item.setItem(BaseNovasOpcoes[I].split("sasw_separator1")[0]);
                        } else {
                            item.setItem(BaseNovasOpcoes[I].split("sasw_separator1")[1]);
                        }
                    } catch (Exception e) {
                        item.setItem(BaseNovasOpcoes[I].split("sasw_separator1")[1]);
                    }

                    try {
                        if (null != BaseNovasOpcoes[I].split("sasw_separator1")[1]
                                && !BaseNovasOpcoes[I].split("sasw_separator1")[1].equals("")) {
                            item.setDescricao(BaseNovasOpcoes[I].split("sasw_separator1")[1]);
                        } else {
                            item.setDescricao(BaseNovasOpcoes[I].split("sasw_separator1")[0]);
                        }
                    } catch (Exception e) {
                        item.setDescricao(BaseNovasOpcoes[I].split("sasw_separator1")[0]);
                    }
                    item.setDt_Alter(DataAtual.getDataAtual("SQL"));
                    item.setHr_Alter(DataAtual.getDataAtual("HORA"));
                    item.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                    item.setOrdem(Integer.toString(Ordem));

                    itensInsert.add(item);
                    Ordem++;
                }
            }
        } catch (Exception e) {
        }
    }

    public void editarItemInspecao(InspecoesItens inspecaoItem1, InspecoesItens inspecaoItem2) {
        try {
            inspecaoItem1.setDt_Alter(getDataAtual("SQL"));
            inspecaoItem1.setHr_Alter(getDataAtual("HORA"));
            inspecaoItem1.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));

            if (inspecaoItem2.getTipoResp().equals("8")) {
                tratarOpcoesInspecao(inspecaoItem2.getOpcoesResposta());
            } else {
                itensInsert = new ArrayList<>();
            }
            this.inspecoesSatWeb.editarItemInspecao(inspecaoItem2, this.itensInsert, this.persistencia);
            this.itensInspecao = this.inspecoesSatWeb.getAllInspecoesItens(this.inspecaoSelecionada.getCodigo(), this.persistencia);

            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            PrimeFaces.current().executeScript("SubstituirTraducao();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<PstInspecao> getAllPstInspecoes() {
        if (this.pstInspecoes == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace("PstInspecao.data = ?", this.dataTela);
            this.filters.replace("PstInspecao.codfil = ?", this.codFil);
//            this.filters.replace("posto", this.secao == null ? "" : this.secao);
            dt.setFilters(this.filters);
            this.pstInspecoes = new PstInspecaoLazyList(this.persistencia, this.codPessoa);

        }
        return this.pstInspecoes;
    }

    public void dataAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filters.replace("PstInspecao.data = ?", this.dataTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllPstInspecoes();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            this.filters.replace("PstInspecao.data = ?", this.dataTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllPstInspecoes();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarData(SelectEvent data) {
        this.dataTela = (String) data.getObject();
        this.filters.replace("PstInspecao.data = ?", this.dataTela);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllPstInspecoes();
        dt.setFirst(0);
    }

    /**
     * Tranforma Date em String
     *
     * @param date Data a ser formatada
     * @return String no formato yyyyMMdd
     */
    public String Date2String(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * Procura o último dia do mês atual
     */
    public static void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void atualizarListaPostos() {
        try {
            this.postos = this.inspecoesSatWeb.getPstSerList(this.codPessoa, this.pstInspecaoSelecionada.getCodfil(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void filtrar() {
        this.filters.replace("PstInspecao.codfil = ?", null == this.pstInspecaoSelecionada.getCodfil() ? "" : this.pstInspecaoSelecionada.getCodfil());
        this.filters.replace("PstInspecao.secao = ?", null == this.pstInspecaoSelecionada.getSecao() ? "" : this.pstInspecaoSelecionada.getSecao());
        this.filters.replace("PstInspecao.CodInspecao = ?", null == this.pstInspecaoSelecionada.getCodInspecao() ? "" : this.pstInspecaoSelecionada.getCodInspecao().replace(".0", ""));
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllPstInspecoes();
        dt.setFirst(0);
        this.pstInspecaoSelecionada = null;
    }

    public void limparTodosFiltros() {
        this.mostrarFiliais = false;
        this.limparFiltros = false;
        this.dataTela = DataAtual.getDataAtual("SQL");
        this.filters.replace("PstInspecao.data = ?", this.dataTela);
        this.filters.replace("PstInspecao.secao = ?", "");
        this.filters.replace("PstInspecao.codfil = ?", this.codFil);
        this.filters.replace("PstInspecao.CodInspecao = ?", "");
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllPstInspecoes();
        dt.setFirst(0);
        this.pstInspecaoSelecionada = null;
    }

    public void mostrarTodasFiliais() {
        if (this.mostrarFiliais) {
            this.filters.replace("PstInspecao.codfil = ?", "");
        } else {
            this.filters.replace("PstInspecao.codfil = ?", this.codFil);
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllPstInspecoes();
        dt.setFirst(0);
        this.pstInspecaoSelecionada = null;
    }

    public void rowDblselect(SelectEvent event) {
        this.pstInspecaoSelecionada = (PstInspecao) event.getObject();
        abrirPstInspecao(null);
    }

    public void abrirPstInspecao(ActionEvent actionEvent) {
        if (null == this.pstInspecaoSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneInspecao"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                boolean SemCoordedanas = false;

                if (!this.pstInspecaoSelecionada.getPergunta().equals("RESUMO DO TRABALHO DE CAMPO")) {
                    List<PstInspecao> pp = this.inspecoesSatWeb.getDetalhesPstInspecoes(this.pstInspecaoSelecionada, this.persistencia);
                    this.pstInspecaoSelecionadaDetalhes = new ArrayList<>();

                    String idioma = FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
                    this.relatorio = Messages.replaceTags(this.inspecoesSatWeb.getRelatorio(this.pstInspecaoSelecionada, idioma, this.persistencia));

                    PstInspecaoDetalhes aux;
                    for (PstInspecao p : pp) {
                        aux = new PstInspecaoDetalhes();
                        aux.setPstInspecao(p);
                        aux.getPstInspecao().setResposta(p.getResposta().replace("\\N", "<br>").replace("\n", "<br>"));

                        aux.setFotos(Arrays.asList(p.getCaminhoImagem().split(";")));
                        if (!aux.getFotos().isEmpty()) {
                            if (aux.getFotos().size() == 1 && aux.getFotos().get(0).equals("")) {
                                aux.setFotos(new ArrayList<>());
                            } else {
                                aux.setFoto(aux.getFotos().get(0));
                            }
                        }

                        aux.setVideos(Arrays.asList(p.getCaminhoVideo().split(";")));
                        if (!aux.getVideos().isEmpty()) {
                            if (aux.getVideos().size() == 1 && aux.getVideos().get(0).equals("")) {
                                aux.setVideos(new ArrayList<>());
                            } else {
                                aux.setVideo(aux.getVideos().get(0));
                            }
                        }

                        aux.setAudios(Arrays.asList(p.getCaminhoAudio().split(";")));
                        if (!aux.getAudios().isEmpty()) {
                            if (aux.getAudios().size() == 1 && aux.getAudios().get(0).equals("")) {
                                aux.setAudios(new ArrayList<>());
                            } else {
                                aux.setAudio(aux.getAudios().get(0));
                            }
                        }

                        aux.setAudios(Arrays.asList(p.getCaminhoAudio().split(";")));
                        if (!aux.getAudios().isEmpty()) {
                            if (aux.getAudios().size() == 1 && aux.getAudios().get(0).equals("")) {
                                aux.setAudios(new ArrayList<>());
                            } else {
                                aux.setAudio(aux.getAudios().get(0));
                            }
                        }

                        aux.setPosicaoFoto(0);
                        aux.setPosicaoVideo(0);

                        this.mapa = new DefaultMapModel();
                        try {
                            this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.pstInspecaoSelecionada.getLatitude()).doubleValue(),
                                    new BigDecimal(this.pstInspecaoSelecionada.getLongitude()).doubleValue()),
                                    this.pstInspecaoSelecionada.getResposta(), this.pstInspecaoSelecionada,
                                    "https://mobile.sasw.com.br:9091/satmobile/img/pin_funcionario.png"));
                            this.centroMapa = this.pstInspecaoSelecionada.getLatitude() + "," + this.pstInspecaoSelecionada.getLongitude();
                        } catch (Exception ex) {
                            SemCoordedanas = true;
                        }

                        this.zoomMapa = "17";

                        this.pstInspecaoSelecionadaDetalhes.add(aux);
                    }

                    if (SemCoordedanas) {
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("PostoSemCoordenadas"), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    }

                    PrimeFaces.current().executeScript("PF('dlgPstInspecao').show()");
                } else {
                    abrirBoletimTrabalho(false);
                }

                try {
                    this.mapa = new DefaultMapModel();
                    this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.pstInspecaoSelecionada.getLatitude()).doubleValue(),
                            new BigDecimal(this.pstInspecaoSelecionada.getLongitude()).doubleValue()),
                            this.pstInspecaoSelecionada.getResposta(), this.pstInspecaoSelecionada,
                            "https://mobile.sasw.com.br:9091/satmobile/img/pin_funcionario.png"));
                    this.centroMapa = this.pstInspecaoSelecionada.getLatitude() + "," + this.pstInspecaoSelecionada.getLongitude();
                    this.zoomMapa = "17";
                } catch (Exception eM) {
                    this.mapa = null;
                    this.centroMapa = null;
                    this.zoomMapa = null;
                }
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);

            }
        }
    }

    public void selecionarPessoa(SelectEvent event) {
        abrirBoletimTrabalho(true);
    }

    public void abrirBoletimTrabalho(boolean Reload) {
        try {
            if (!Reload || null == this.pessoaSelecionada) {
                this.pessoaSelecionada = new Pessoa();
            }
            LogsSatMobEWDao obj = new LogsSatMobEWDao();

            // Carregar Lista de Trabalho
            this.pstInspecoesBoletimTrabalho = obj.listaBoletimTrabalho(this.dataTela, this.codFil, this.pessoaSelecionada.getCodigo(), this.persistencia);
            StringBuilder str = new StringBuilder();

            for (PstInspecao pstInspecoesBoletimTrabalho1 : this.pstInspecoesBoletimTrabalho) {
                str.append("ArrayBoletimTrabalho.push({");
                str.append("  Nred: '").append(pstInspecoesBoletimTrabalho1.getLocal()).append("',");
                str.append("  Latitude: '").append(pstInspecoesBoletimTrabalho1.getLatitude()).append("',");
                str.append("  Longitude: '").append(pstInspecoesBoletimTrabalho1.getLongitude()).append("',");
                str.append("  Pergunta: '").append(pstInspecoesBoletimTrabalho1.getPergunta()).append("',");
                str.append("  Resposta: '").append(pstInspecoesBoletimTrabalho1.getResposta()).append("'");
                str.append("});");
            }

            this.listaTrabalhos = str.toString();

            // Carregar Clientes (PINs)
            List<Clientes> lstClientes = obj.listaBoletimTrabalhoClientes(this.dataTela, this.codFil, this.pessoaSelecionada.getCodigo(), this.persistencia);
            this.markers = "";
            this.centro = "";
            int Contador = 0;

            for (Clientes lstClientes1 : lstClientes) {
                if (this.centro.equals("")) {
                    this.centro = "{ lat: " + lstClientes1.getLatitude() + ", lng: " + lstClientes1.getLongitude() + " }";
                }

                this.markers += MARCADOR.replace("@indice", "pin_" + Integer.toString(Contador))
                        .replace("@lat", lstClientes1.getLatitude()).replace("@lng", lstClientes1.getLongitude())
                        .replace("@title", getMessageS("Local") + ": " + lstClientes1.getNRed() + "\\n" + getMessageS("Endereco") + ": " + lstClientes1.getEnde() + "\\n" + getMessageS("Bairro") + ": " + lstClientes1.getBairro() + "\\n" + getMessageS("Cidade") + ": " + lstClientes1.getCidade())
                        .replace("@icon", "https://mobile.sasw.com.br:9091/satmobile/pins/icone_mobile_supervisor.png");

                Contador++;
            }

            // Carregar Pessoas
            this.pessoas = new ArrayList<>();
            this.pessoas = obj.listaBoletimTrabalhoPessoas(this.dataTela, this.codFil, this.persistencia);

            // Abrir Modal
            if (!Reload) {
                PrimeFaces.current().ajax().update("formBoletimTrabalho");
                PrimeFaces.current().executeScript("PF('dlgBoletimTrabalho').show()");
            }
            PrimeFaces.current().executeScript("setTimeout(function(){ CarregarListaTrabalho('" + Integer.toString(lstClientes.size()) + "'); }, 500)");

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void preProcessPDF(Object document) throws IOException, BadElementException, DocumentException {
        try {
            Document pdf = (Document) document;
//            String msgOperador = Messages.getMessageS("operador") + ": " + FuncoesString.RecortaAteEspaço(operador, 0, 10);

            String msgData = Messages.getMessageS("data") + ": " + Mascaras.Data(dataTela);
            String titulo = Messages.getMessageS("Inspecoes") + " (" + msgData + ")";
//            ExternalContext externalContext = FacesContext.getCurrentInstance().getExternalContext();

            pdf.setPageSize(PageSize.A4.rotate());
            pdf.setMargins(0, 0, 24, 24); // esq, dir, cima, baixo
            pdf.open();

//            PdfPTable table = new PdfPTable(1);
//
//            PdfPCell cell = new PdfPCell(new Phrase(msgOperador));
//            table.addCell(cell);
//            cell = new PdfPCell(new Phrase(msgData));
//            table.addCell(cell);
//            pdf.add(table);
            // Logo
//            try {
//                String logoPath = externalContext.getRealPath("") + File.separator + "assets" + File.separator + "logos" + File.separator + getLogo(banco);
//                Image image = Image.getInstance(logoPath);
//                image.setAlignment(Image.ALIGN_CENTER);
//                pdf.add(image);
//            } catch (IOException e) {
//                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//                FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            };
            Paragraph p = new Paragraph(titulo, new Font(Font.HELVETICA, 16, Font.BOLD, Color.BLACK));
            p.setAlignment(Element.ALIGN_CENTER);
            pdf.add(p);
            Paragraph p2 = new Paragraph("\n", new Font(Font.HELVETICA, 12, Font.BOLD, Color.BLACK));
            p2.setAlignment(Element.ALIGN_CENTER);
            pdf.add(p2);
            pdf.addAuthor(operador);
            pdf.addCreationDate();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }

    }

    public List<PstInspecaoRelatorio> getListaRelatorio() {
        return listaRelatorio;
    }

    public PDFOptions getPdfOptions() {
        return pdfOptions;
    }

    public static class PstInspecaoDetalhes {

        private PstInspecao pstInspecao;
        private String foto, video, audio;
        private List<String> fotos, videos, audios;
        private int posicaoFoto, posicaoVideo;

        public void avancarFoto() {
            if (this.posicaoFoto + 1 == this.fotos.size()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosFim"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.foto = this.fotos.get(this.posicaoFoto + 1);
                this.posicaoFoto = this.posicaoFoto + 1;
            }
        }

        public void voltarFoto() {
            if (this.posicaoFoto == 0) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosInicio"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.foto = this.fotos.get(this.posicaoFoto - 1);
                this.posicaoFoto = this.posicaoFoto - 1;
            }
        }

        public void avancarVideo() {
            if (this.posicaoVideo + 1 == this.videos.size()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisVideosFim"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.video = this.videos.get(this.posicaoVideo + 1);
                this.posicaoVideo = this.posicaoVideo + 1;
            }
        }

        public void voltarVideo() {
            if (this.posicaoVideo == 0) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisVideosInicio"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.video = this.videos.get(this.posicaoVideo - 1);
                this.posicaoVideo = this.posicaoVideo - 1;
            }
        }

        public void setPstInspecao(PstInspecao pstInspecao) {
            this.pstInspecao = pstInspecao;
        }

        public PstInspecao getPstInspecao() {
            return pstInspecao;
        }

        public String getFoto() {
            return foto;
        }

        public void setFoto(String foto) {
            this.foto = foto;
        }

        public String getVideo() {
            return video;
        }

        public void setVideo(String video) {
            this.video = video;
        }

        public List<String> getFotos() {
            return fotos;
        }

        public void setFotos(List<String> fotos) {
            this.fotos = fotos;
        }

        public List<String> getVideos() {
            return videos;
        }

        public void setVideos(List<String> videos) {
            this.videos = videos;
        }

        public int getPosicaoFoto() {
            return posicaoFoto;
        }

        public String getAudio() {
            return audio;
        }

        public void setAudio(String audio) {
            this.audio = audio;
        }

        public List<String> getAudios() {
            return audios;
        }

        public void setAudios(List<String> audios) {
            this.audios = audios;
        }

        public void setPosicaoFoto(int posicaoFoto) {
            this.posicaoFoto = posicaoFoto;
        }

        public int getPosicaoVideo() {
            return posicaoVideo;
        }

        public void setPosicaoVideo(int posicaoVideo) {
            this.posicaoVideo = posicaoVideo;
        }
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public List<Inspecoes> getInspecoes() {
        return inspecoes;
    }

    public void setInspecoes(List<Inspecoes> inspecoes) {
        this.inspecoes = inspecoes;
    }

    public Inspecoes getInspecaoSelecionada() {
        return inspecaoSelecionada;
    }

    public void setInspecaoSelecionada(Inspecoes inspecaoSelecionada) {
        this.inspecaoSelecionada = inspecaoSelecionada;
    }

    public List<InspecoesItens> getItensInspecao() {
        return itensInspecao;
    }

    public void setItensInspecao(List<InspecoesItens> itensInspecao) {
        this.itensInspecao = itensInspecao;
    }

    public InspecoesItens getNovoItemInspecao() {
        return novoItemInspecao;
    }

    public void setNovoItemInspecao(InspecoesItens novoItemInspecao) {
        this.novoItemInspecao = novoItemInspecao;
    }

    public PstInspecao getPstInspecaoSelecionada() {
        return pstInspecaoSelecionada;
    }

    public void setPstInspecaoSelecionada(PstInspecao pstInspecaoSelecionada) {
        this.pstInspecaoSelecionada = pstInspecaoSelecionada;
    }

    public List<PstServ> getPostos() {
        return postos;
    }

    public void setPostos(List<PstServ> postos) {
        this.postos = postos;
    }

    public List<PstInspecaoDetalhes> getPstInspecaoSelecionadaDetalhes() {
        return pstInspecaoSelecionadaDetalhes;
    }

    public void setPstInspecaoSelecionadaDetalhes(List<PstInspecaoDetalhes> pstInspecaoSelecionadaDetalhes) {
        this.pstInspecaoSelecionadaDetalhes = pstInspecaoSelecionadaDetalhes;
    }

    public String getRelatorio() {
        return relatorio;
    }

    public void setRelatorio(String relatorio) {
        this.relatorio = relatorio;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public boolean isLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getNovasOpcoesResposta() {
        return novasOpcoesResposta;
    }

    public void setNovasOpcoesResposta(String novasOpcoesResposta) {
        this.novasOpcoesResposta = novasOpcoesResposta;
    }

    public String getCentroMapa() {
        return centroMapa;
    }

    public void setCentroMapa(String centroMapa) {
        this.centroMapa = centroMapa;
    }

    public String getZoomMapa() {
        return zoomMapa;
    }

    public void setZoomMapa(String zoomMapa) {
        this.zoomMapa = zoomMapa;
    }

    public MapModel getMapa() {
        return mapa;
    }

    public void setMapa(MapModel mapa) {
        this.mapa = mapa;
    }

    public String getListaTrabalhos() {
        return listaTrabalhos;
    }

    public void setListaTrabalhos(String listaTrabalhos) {
        this.listaTrabalhos = listaTrabalhos;
    }

    public String getMarkers() {
        return markers;
    }

    public void setMarkers(String markers) {
        this.markers = markers;
    }

    public String getCentro() {
        return centro;
    }

    public void setCentro(String centro) {
        this.centro = centro;
    }

    public List<Pessoa> getPessoas() {
        return pessoas;
    }

    public void setPessoas(List<Pessoa> pessoas) {
        this.pessoas = pessoas;
    }

    public Pessoa getPessoaSelecionada() {
        return pessoaSelecionada;
    }

    public void setPessoaSelecionada(Pessoa pessoaSelecionada) {
        this.pessoaSelecionada = pessoaSelecionada;
    }

    public List<PstInspecao> getPstInspecoesBoletimTrabalho() {
        return pstInspecoesBoletimTrabalho;
    }

    public void setPstInspecoesBoletimTrabalho(List<PstInspecao> pstInspecoesBoletimTrabalho) {
        this.pstInspecoesBoletimTrabalho = pstInspecoesBoletimTrabalho;
    }

    public String getHtmlTiposInspecoes() {
        return htmlTiposInspecoes;
    }

    public void setHtmlTiposInspecoes(String htmlTiposInspecoes) {
        this.htmlTiposInspecoes = htmlTiposInspecoes;
    }

    public String getHtmlInspecoesExec() {
        return htmlInspecoesExec;
    }

    public void setHtmlInspecoesExec(String htmlInspecoesExec) {
        this.htmlInspecoesExec = htmlInspecoesExec;
    }

    public String getTipoInspecao() {
        return tipoInspecao;
    }

    public void setTipoInspecao(String tipoInspecao) {
        this.tipoInspecao = tipoInspecao;
    }

    public String getItensNovaInspecao() {
        return itensNovaInspecao;
    }

    public void setItensNovaInspecao(String itensNovaInspecao) {
        this.itensNovaInspecao = itensNovaInspecao;
    }

    public List<ColumnModel> getColumnsGride() {
        return columnsGride;
    }

    public void setColumnsGride(List<ColumnModel> columnsGride) {
        this.columnsGride = columnsGride;
    }

    public ResultSet getResultSet() {
        return resultSet;
    }

    public void setResultSet(ResultSet resultSet) {
        this.resultSet = resultSet;
    }

    public List<LinhaModel> getDadosGride() {
        return dadosGride;
    }

    public void setDadosGride(List<LinhaModel> dadosGride) {
        this.dadosGride = dadosGride;
    }
}

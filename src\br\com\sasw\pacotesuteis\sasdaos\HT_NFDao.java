/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.HT_NF;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class HT_NFDao {

    public HT_NF buscarHT_NF(String codigo, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT HT_NF.* \n"
                    + "FROM HT_NF \n"
                    + "WHERE HT_NF.Codigo = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString(codFil);
            consulta.select();
            HT_NF ht_NF = null;
            if (consulta.Proximo()) {
                ht_NF = new HT_NF();
                ht_NF.setCodFil(consulta.getString("CodFil"));
                ht_NF.setCodigo(consulta.getString("Codigo"));
                ht_NF.setDescricao(consulta.getString("Descricao"));
                ht_NF.setCFOP(consulta.getString("CFOP"));
                ht_NF.setCFOPDescr(consulta.getString("CFOPDescr"));
                ht_NF.setMensagem(consulta.getString("Mensagem"));
                ht_NF.setObs1(consulta.getString("Obs1"));
                ht_NF.setObs2(consulta.getString("Obs2"));
                ht_NF.setCod_Conta(consulta.getString("Cod_Conta"));
                ht_NF.setOperador(consulta.getString("Operador"));
                ht_NF.setDt_Alter(consulta.getString("Dt_Alter"));
                ht_NF.setHr_Alter(consulta.getString("Hr_Alter"));
            }
            consulta.close();
            return ht_NF;
        } catch (Exception e) {
            throw new Exception("HT_NFDao.buscarHT_NF - " + e.getMessage() + "\r\n"
                    + " SELECT HT_NF.* \n"
                    + "FROM HT_NF \n"
                    + "WHERE HT_NF.Codigo = " + codigo + " AND CodFil = " + codFil);
        }
    }

    public List<HT_NF> listarHT_NF(String query, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<HT_NF> retorno = new ArrayList<>();
            String sql = " SELECT HT_NF.* \n"
                    + "FROM HT_NF \n"
                    + "WHERE HT_NF.Descricao LIKE ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.setString(codFil);
            consulta.select();
            HT_NF ht_NF;
            while (consulta.Proximo()) {
                ht_NF = new HT_NF();
                ht_NF.setCodFil(consulta.getString("CodFil"));
                ht_NF.setCodigo(consulta.getString("Codigo"));
                ht_NF.setDescricao(consulta.getString("Descricao"));
                ht_NF.setCFOP(consulta.getString("CFOP"));
                ht_NF.setCFOPDescr(consulta.getString("CFOPDescr"));
                ht_NF.setMensagem(consulta.getString("Mensagem"));
                ht_NF.setObs1(consulta.getString("Obs1"));
                ht_NF.setObs2(consulta.getString("Obs2"));
                ht_NF.setCod_Conta(consulta.getString("Cod_Conta"));
                ht_NF.setOperador(consulta.getString("Operador"));
                ht_NF.setDt_Alter(consulta.getString("Dt_Alter"));
                ht_NF.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(ht_NF);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("HT_NFDao.listarHT_NF - " + e.getMessage() + "\r\n"
                    + " SELECT HT_NF.* \n"
                    + "FROM HT_NF \n"
                    + "WHERE HT_NF.Descricao LIKE %" + query + "% AND CodFil = " + codFil);
        }
    }
}

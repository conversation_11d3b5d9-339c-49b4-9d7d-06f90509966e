/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class TmktDet {

    private String Sequencia;
    private String Andamento;
    private String Data;
    private String Hora;
    private String TipoCont;
    private String CodPessoa;
    private String CodCont;
    private String CodFil;
    private String Contato;
    private String Fone;
    private String Fone2;
    private String Historico;
    private String Detalhes;
    private String CodPrestAtual;
    private String QtdeFotos;
    private String Latitude;
    private String Longitude;
    private String Precisao;
    private String FiltroWeb;
    private String Operador;
    private String Dt_Alter;
    private String Hr_alter;
    private String Ciente;
    private String Situacao;
    private String Fotos;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getAndamento() {
        return Andamento;
    }

    public void setAndamento(String Andamento) {
        this.Andamento = Andamento;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getTipoCont() {
        return TipoCont;
    }

    public void setTipoCont(String TipoCont) {
        this.TipoCont = TipoCont;
    }

    public String getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getCodCont() {
        return CodCont;
    }

    public void setCodCont(String CodCont) {
        this.CodCont = CodCont;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getContato() {
        return Contato;
    }

    public void setContato(String Contato) {
        this.Contato = Contato;
    }

    public String getFone() {
        return Fone;
    }

    public void setFone(String Fone) {
        this.Fone = Fone;
    }

    public String getFone2() {
        return Fone2;
    }

    public void setFone2(String Fone2) {
        this.Fone2 = Fone2;
    }

    public String getHistorico() {
        return Historico;
    }

    public void setHistorico(String Historico) {
        this.Historico = Historico;
    }

    public String getDetalhes() {
        return Detalhes;
    }

    public void setDetalhes(String Detalhes) {
        this.Detalhes = Detalhes;
    }

    public String getCodPrestAtual() {
        return CodPrestAtual;
    }

    public void setCodPrestAtual(String CodPrestAtual) {
        this.CodPrestAtual = CodPrestAtual;
    }

    public String getQtdeFotos() {
        return QtdeFotos;
    }

    public void setQtdeFotos(String QtdeFotos) {
        this.QtdeFotos = QtdeFotos;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getPrecisao() {
        return Precisao;
    }

    public void setPrecisao(String Precisao) {
        this.Precisao = Precisao;
    }

    public String getFiltroWeb() {
        return FiltroWeb;
    }

    public void setFiltroWeb(String FiltroWeb) {
        this.FiltroWeb = FiltroWeb;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getCiente() {
        return Ciente;
    }

    public void setCiente(String Ciente) {
        this.Ciente = Ciente;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getFotos() {
        return Fotos;
    }

    public void setFotos(String Fotos) {
        this.Fotos = Fotos;
    }
}

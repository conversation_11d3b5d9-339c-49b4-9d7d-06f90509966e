/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class Rt_Modelo {

    private String Sequencia;
    private String Modelo;
    private String Rota;
    private String Data;
    private String CodFil;
    private String TpVeic;
    private String Viagem;
    private String ATM;
    private String BACEN;
    private String Aeroporto;
    private String HRLargada;
    private String HrChegada;
    private String HrIntIni;
    private String HrIniFim;
    private String HsTotal;
    private String Observacao;
    private String OperFech;
    private String DtFim;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Flag_Excl;
    private String Dt_Excl;
    private String Hr_Excl;
    private String QtdeRotas;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getModelo() {
        return Modelo;
    }

    public void setModelo(String Modelo) {
        this.Modelo = Modelo;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getTpVeic() {
        return TpVeic;
    }

    public void setTpVeic(String TpVeic) {
        this.TpVeic = TpVeic;
    }

    public String getViagem() {
        return Viagem;
    }

    public void setViagem(String Viagem) {
        this.Viagem = Viagem;
    }

    public String getATM() {
        return ATM;
    }

    public void setATM(String ATM) {
        this.ATM = ATM;
    }

    public String getBACEN() {
        return BACEN;
    }

    public void setBACEN(String BACEN) {
        this.BACEN = BACEN;
    }

    public String getAeroporto() {
        return Aeroporto;
    }

    public void setAeroporto(String Aeroporto) {
        this.Aeroporto = Aeroporto;
    }

    public String getHRLargada() {
        return HRLargada;
    }

    public void setHRLargada(String HRLargada) {
        this.HRLargada = HRLargada;
    }

    public String getHrChegada() {
        return HrChegada;
    }

    public void setHrChegada(String HrChegada) {
        this.HrChegada = HrChegada;
    }

    public String getHrIntIni() {
        return HrIntIni;
    }

    public void setHrIntIni(String HrIntIni) {
        this.HrIntIni = HrIntIni;
    }

    public String getHrIniFim() {
        return HrIniFim;
    }

    public void setHrIniFim(String HrIniFim) {
        this.HrIniFim = HrIniFim;
    }

    public String getHsTotal() {
        return HsTotal;
    }

    public void setHsTotal(String HsTotal) {
        this.HsTotal = HsTotal;
    }

    public String getObservacao() {
        return Observacao;
    }

    public void setObservacao(String Observacao) {
        this.Observacao = Observacao;
    }

    public String getOperFech() {
        return OperFech;
    }

    public void setOperFech(String OperFech) {
        this.OperFech = OperFech;
    }

    public String getDtFim() {
        return DtFim;
    }

    public void setDtFim(String DtFim) {
        this.DtFim = DtFim;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    public String getDt_Excl() {
        return Dt_Excl;
    }

    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    public String getHr_Excl() {
        return Hr_Excl;
    }

    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    public String getQtdeRotas() {
        return QtdeRotas;
    }

    public void setQtdeRotas(String QtdeRotas) {
        this.QtdeRotas = QtdeRotas;
    }
}

<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <p:dialog
        widgetVar="dlgVolumesGTV"
        header="#{localemsgs.VolumesDaGTV}"
        positionType="absolute"
        responsive="true"
        draggable="false"
        modal="true"
        closable="true"
        resizable="false"
        dynamic="true"
        showEffect="drop"
        hideEffect="drop"
        closeOnEscape="true"
        style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;"
        >
        <h:form id="guiaSerieCadastroForm" style="color: black;">
            <f:facet name="header">
                <img src="../assets/img/icone_tesouraria_entradas.png" height="40" width="40"/>
                <p:spacer width="5px"/>
                <h:outputText value="#{localemsgs.CadastrarNumerario}" style="color:#022a48" />
            </f:facet>

            <p:panel id="cadastrar" style="background-color: transparent;" styleClass="cadastrar">
                <div class="content-fluid">
                    <div class="row flexBottom">
                        <div class="col-xs-6">
                            <p:outputLabel for="guia"
                                           value="#{localemsgs.Guia}: "
                                           indicateRequired="false"/>
                            <p:inputNumber id="guia"
                                           value="#{cxForteEntrada.guiaEdicao.guia}"
                                           disabled="true"
                                           class="primefacesInputFix"
                                           maxValue="999999999"
                                           decimalPlaces="0"
                                           />
                        </div>

                        <div class="col-xs-6">
                            <p:outputLabel for="serie"
                                           value="#{localemsgs.Serie}:"/>
                            <p:inputText id="serie"
                                         value="#{cxForteEntrada.guiaEdicao.serie}"
                                         disabled="true"
                                         class="primefacesInputFix"
                                         />
                        </div>
                    </div>

                    <div class="row flexBottom">
                        <div class="col-xs-6">
                            <p:outputLabel for="tipo"
                                           value="#{localemsgs.Tipo}: "/>
                            <p:selectOneMenu
                                value="#{cxForteEntrada.guiaEdicao.tipo}"
                                id="tipo"
                                converter="omnifaces.SelectItemsConverter"
                                disabled="false"
                                class="primefacesInputFix"
                                styleClass="filial"
                                style="width: 100%;"
                                >
                                <f:selectItem itemValue="#{null}"
                                              itemLabel="#{localemsgs.Selecionar}"
                                              noSelectionOption="true"/>
                                <f:selectItem itemValue="1"
                                              itemLabel="1 - #{localemsgs.Dinheiro}"/>
                                <f:selectItem itemValue="2"
                                              itemLabel="2 - #{localemsgs.Cheque}"/>
                                <f:selectItem itemValue="3"
                                              itemLabel="3 - #{localemsgs.Moeda}"/>
                                <f:selectItem itemValue="4"
                                              itemLabel="4 - #{localemsgs.MetaisPreciosos}"/>
                                <f:selectItem itemValue="5"
                                              itemLabel="5 - #{localemsgs.MoedaExtrangeira}"/>
                                <f:selectItem itemValue="9"
                                              itemLabel="9 - #{localemsgs.Outros}"/>
                            </p:selectOneMenu>
                        </div>

                        <div class="col-xs-6">
                            <p:outputLabel for="lacre"
                                           value="#{localemsgs.Lacre}: "/>
                            <p:inputNumber  id="lacre"
                                            value="#{cxForteEntrada.guiaEdicao.OS}"
                                            class="primefacesInputFix"
                                            maxValue="999999999.99"
                                            decimalPlaces="2"
                                            required="true"
                                            validatorMessage="Not valid Number"/>
                        </div>
                    </div>

                    <div class="row flexBottom">
                        <div class="col-xs-6">
                            <p:outputLabel for="quantidade"
                                           value="#{localemsgs.Qtde}: "/>
                            <p:inputNumber  id="quantidade"
                                            value="#{cxForteEntrada.guiaEdicao.valor}"
                                            class="primefacesInputFix"
                                            maxValue="999999999.99"
                                            decimalPlaces="2"
                                            required="true"
                                            validatorMessage="Not valid Number"/>
                        </div>

                        <div class="col-xs-6">
                            <p:outputLabel for="valor"
                                           value="#{localemsgs.Valor}: "/>
                            <p:inputNumber  id="valor"
                                            value="#{cxForteEntrada.guiaEdicao.valor}"
                                            class="primefacesInputFix"
                                            maxValue="999999999.99"
                                            decimalPlaces="2"
                                            required="true"
                                            validatorMessage="Not valid Number"/>
                        </div>
                    </div>

                    <div class="row flexBottom">
                        <div class="col-xs-12 flexBottom">
                            <div>
                                <p:outputLabel for="observacao"
                                               value="#{localemsgs.Obs}: "/>
                                <!--TODO-->
                                <p:inputText id="observacao"
                                             value="#{cxForteEntrada.guiaEdicao.valorOutr}"
                                             class="primefacesInputFix"
                                             required="true"/>
                            </div>
                        </div>
                    </div>

                    <div class="row flexBottom">
                        <div class="col-xs-12 flexBottom">
                            <div>
                                <p:outputLabel for="volumes"
                                               value="#{localemsgs.Volumes}: "
                                               indicateRequired="false"/>
                                <p:dataTable
                                    id="volumes"
                                    value="#{tesEntrada.cxfGuiasVolLista}"
                                    currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Volumes}"
                                    var="volume"
                                    emptyMessage="#{localemsgs.SemRegistros}"
                                    paginator="#{tesEntrada.cxfGuiasVolLista.size() > 15}"
                                    rows="15"
                                    reflow="true"
                                    rowsPerPageTemplate="5,10,15, 20, 25"
                                    paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                    styleClass="tabela"
                                    scrollable="true"
                                    scrollWidth="100%"
                                    paginatorPosition="top"
                                    class="tabela DataGrid"
                                    style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                    >
                                    <p:column headerText="#{localemsgs.Ordem}">
                                        <h:outputText value="#{volume.ordem}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Qtde}">
                                        <h:outputText value="#{volume.qtde}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Descricao}">
                                        <h:outputText value="#{volume.tipo}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Lacre}">
                                        <h:outputText value="#{volume.lacre}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Valor}">
                                        <h:outputText value="#{volume.valor}" converter="conversormoeda"/>
                                    </p:column>
                                </p:dataTable>
                            </div>
                        </div>
                    </div>

                    <div class="form-inline">
                        <p:commandLink
                            id="cadastro"
                            action="#{cxForteEntrada.gravarVolumesGTV()}"
                            update="msgs main cabecalho"
                            title="#{localemsgs.Editar}">
                            <p:graphicImage
                                url="../assets/img/icone_adicionar.png"
                                width="40" height="40" />
                        </p:commandLink>
                    </div>
                </div>
            </p:panel>
        </h:form>
    </p:dialog>

</ui:composition>
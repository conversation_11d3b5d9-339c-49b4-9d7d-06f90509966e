<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.3/Chart.min.js"></script>
            <ui:fragment rendered="#{solicitacaoSenhaMB.temPermissao}">
                <script
                    src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}">
                </script>
            </ui:fragment>

            <style>
                body, html{
                    background-color: #000;
                }

                body{
                    min-height:100% !important;
                    height:100% !important;
                }

                #TopoDash{
                    height:60px;
                    padding:0px !important;
                    font-family: 'Trebuchet MS', sans-serif;
                }

                #TopoDash .Empresa{
                    font-size:11pt;
                    font-weight: bold;
                    color:#FFF;
                    text-transform: uppercase;
                    display:block;
                    line-height: 15px;
                    margin-top: 6px;
                    margin-left:8px !important;
                }

                #TopoDash .Filial{
                    font-size:7pt;
                    font-weight: bold;
                    color:#FFF;
                    text-transform: uppercase;
                    display:block;
                    line-height: 8px;
                    margin-left:8px !important;
                    color:#AAA !important;
                }

                #TopoDash .Voltar{
                    background-color:orangered;
                    color:#FFF;
                    border-radius:12px;
                    padding:0px 8px 0px 8px;
                    border: 2px solid #ae2f00;
                    font-size:9pt;
                    cursor:pointer;
                    position:absolute;
                    left:6px;
                    bottom:-23px;
                    height:18px !important;
                    font-size:8pt !important;
                    width:80px;
                    text-align:center;
                }

                #TopoDash .ItemDash{
                    padding:0px 1px 0px 1px !important;
                    height:60px;
                }

                #TopoDash .ItemDash [ref="Titulo"]{
                    font-size:8pt !important;
                    font-weight: 500;
                    color:#BBB;
                    line-height:10px !important;
                    text-transform: uppercase;
                    width:100%;
                    text-align:center;
                    margin-top:12px;
                }

                #TopoDash .ItemDash [ref="Valor"]{
                    font-size:14pt !important;
                    font-weight: 600;
                    color:#FFF;
                    width:100%;
                    text-align:center;
                    margin-top:1px;
                }

                #TopoDash .ItemDashDados{
                    background-image: linear-gradient(#000, #222);
                    height:60px;
                }

                .FundoChart{
                    padding: 5px 5px 20px 5px;
                    margin-bottom: 10px;
                    border-bottom: 1px dotted #BBB;
                    height:298px;
                    margin-top:20px;
                    background-image: linear-gradient(rgba(0,0,0,0.1), #202020);
                }

                .FundoChart table{
                    width:100%;
                }

                .DadosTab div {
                    font-size: 8pt;
                }

                .DadosTab div::-webkit-scrollbar-thumb:vertical {
                    background: #111 !important;
                }

                .DadosTab div:hover::-webkit-scrollbar-thumb:vertical {
                    background: #ff6a00 !important;
                    border: 2px solid #c35201;
                }

                ::-webkit-scrollbar-track-piece {
                    background: #222 !important;
                }

                .Titulo {
                    font-size: 12pt !important;
                    color: #EEE !important;
                    font-weight: bold !important;
                    line-height:18px !important;
                }

                .Titulo span{
                    font-size: 9pt !important;
                    color: #666 !important;
                    font-weight: bold !important;
                }

                ::-webkit-scrollbar {
                    width: 7px;
                    height: 8px;
                    font-weight: 500 !important;
                }

                ::-webkit-scrollbar-track-piece {
                    background: #ccc;
                    border-radius: 20px;
                }

                ::-webkit-scrollbar-thumb:horizontal {
                    width: 10%;
                    background: #999;
                    border-radius: 20px;
                    border-radius: 5px;
                }

                ::-webkit-scrollbar-thumb:vertical {
                    height: 5px;
                    background: #999;
                    border-radius: 25px;
                }

                .BoxMedia{
                    padding:0px 8px 0px 0px !important;
                }

                .BoxMedia div{
                    background-color:#222 !important;
                    border-radius:6px;
                    height:68px;
                }

                .BoxMedia [ref="Titulo"]{
                    color:#999;
                    font-size:10pt;
                    font-weight:500 !important;
                    width:100%;
                    text-align:center;
                    margin:8px 0px 0px 0px !important;
                    text-transform: uppercase;
                }

                .BoxMedia [ref="Valor"]{
                    color:#FFF;
                    font-size:18pt;
                    font-weight:500 !important;
                    width:100%;
                    text-align:center;
                    margin:0px !important;
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    [id*="main"]{
                        height:2120px !important;
                    }

                    .BoxMedia div{
                        margin-bottom:8px !important;
                    }

                    .BoxMedia [ref="Titulo"]{
                        margin:15px 0px 0px 0px !important;
                        font-size:8pt !important;
                    }

                    .BoxMedia [ref="Valor"]{
                        font-size:12pt !important;
                    }

                    #LinhaDivisao{
                        position:absolute;
                        top:36px;
                        width:100% !important;
                    }

                    #TopoDash .Voltar{
                        left:auto;
                        right:2px !important;
                        top:2px !important;
                        width:70px;
                    }
                }

            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{dashboard.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{dashboard.carregarGraficos()}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <ui:include src="/botao_panico.xhtml"/>

            <header style="background-color:#000">
                <div id="TopoDash" class="col-md-12">
                    <h:form id="top" style="background-color: #000; height:100%;">
                        <div class="col-md-4 col-sm-4 col-xs-12" style="padding-left:0px !important;">
                            <label class="Empresa">#{dashboard.filiais.descricao}</label>
                            <label class="Filial">#{dashboard.filiais.endereco} - #{dashboard.filiais.bairro} - #{dashboard.filiais.cidade}/#{dashboard.filiais.UF}</label>
                            <label class="Voltar" onclick="window.history.back();"><i class="fa fa-arrow-circle-left"></i>&nbsp;#{localemsgs.Voltar}</label>
                        </div>
                        <div class="col-md-8 col-sm-8 col-xs-12" style="padding:0px !important;">
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.MaximoRotas}</label>
                                    <label ref="Valor">#{dashboard.topoTotalRotas}</label>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.TotalParadas}</label>
                                    <label ref="Valor">#{dashboard.topoTotalParadas}</label>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.TotalGuias}</label>
                                    <label ref="Valor">#{dashboard.topoTotalGuias}</label>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.TotalKm}</label>
                                    <label ref="Valor">#{dashboard.topoTotalKm}</label>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.Ano}</label>
                                    <p:selectOneMenu id="slcAno" style="padding-left: 10px !important; border:none !important;bottom:6px !important; font-size:14pt; color:#FFF !important; font-weight:600 !important; text-align:center !important; font-family: 'Trebuchet MS'" value="#{dashboard.anoTela}">
                                        <f:selectItem itemLabel="2018" itemValue="2018" />
                                        <f:selectItem itemLabel="2019" itemValue="2019" />
                                        <f:selectItem itemLabel="2020" itemValue="2020" />
                                        <f:selectItem itemLabel="2021" itemValue="2021" />
                                        <f:selectItem itemLabel="2022" itemValue="2022" />
                                        <f:selectItem itemLabel="2023" itemValue="2023" />
                                        <p:ajax listener="#{dashboard.carregarGraficos}" update="msgs main top" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.Mes}</label>
                                    <p:selectOneMenu id="slcMes" required="true" style="padding-left: 10px !important;border:none !important;bottom:6px !important; font-size:14pt; color:#FFF !important; font-weight:600 !important; font-family: 'Trebuchet MS'" value="#{dashboard.mesTela}" >
                                        <f:selectItem itemLabel="#{localemsgs.JANUARY.toUpperCase()}" itemValue="01" />
                                        <f:selectItem itemLabel="#{localemsgs.FEBRUARY.toUpperCase()}" itemValue="02" />
                                        <f:selectItem itemLabel="#{localemsgs.MARCH.toUpperCase()}" itemValue="03" />
                                        <f:selectItem itemLabel="#{localemsgs.APRIL.toUpperCase()}" itemValue="04" />
                                        <f:selectItem itemLabel="#{localemsgs.MAY.toUpperCase()}" itemValue="05" />
                                        <f:selectItem itemLabel="#{localemsgs.JUNE.toUpperCase()}" itemValue="06" />
                                        <f:selectItem itemLabel="#{localemsgs.JULY.toUpperCase()}" itemValue="07" />
                                        <f:selectItem itemLabel="#{localemsgs.AUGUST.toUpperCase()}" itemValue="08" />
                                        <f:selectItem itemLabel="#{localemsgs.SEPTEMBER.toUpperCase()}" itemValue="09" />
                                        <f:selectItem itemLabel="#{localemsgs.OCTOBER.toUpperCase()}" itemValue="10" />
                                        <f:selectItem itemLabel="#{localemsgs.NOVEMBER.toUpperCase()}" itemValue="11" />
                                        <f:selectItem itemLabel="#{localemsgs.DECEMBER.toUpperCase()}" itemValue="12" />

                                        <p:ajax listener="#{dashboard.carregarGraficos}" update="msgs main top" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                        </div>
                        <div id="LinhaDivisao" class="col-md-12" style="height: 3px; background-color:#202020; margin-top:2px;"></div>
                    </h:form>
                </div>
            </header>
            <h:form id="main" style="height:950px; padding:0px 10px 0px 15px; width:100% !important;background: linear-gradient(to bottom, #000, #131313) !important;">
                <p:panel id="tabela" style="padding:0px !important; min-height: 100% !important; background-color:transparent; border:none;">
                    <div class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 28px; margin-bottom: 10px; padding:0px 0px 10px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important">
                            <label class="Titulo">#{localemsgs.MediaProdDia}<br />
                                <span>#{localemsgs.ConsiderandoDados}</span></label>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-hand-paper-o" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Paradas}</label>
                                <label ref="Valor">#{dashboard.mediaParadas}</label>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-file-text-o" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Guias}</label>
                                <label ref="Valor">#{dashboard.mediaGuias}</label>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-truck" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Rotas}</label>
                                <label ref="Valor">#{dashboard.mediaRotas}</label>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-usd" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Valor}</label>
                                <label ref="Valor">#{dashboard.mediaValor}</label>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-map-marker" aria-hidden="true"></i>&nbsp;&nbsp;KM</label>
                                <label ref="Valor">#{dashboard.mediaKm}</label>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-calendar" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Dias}</label>
                                <label ref="Valor">#{dashboard.mediaTotalDias}</label>
                            </div>
                        </div>
                    </div>






                    <div class="col-md-4 col-sm-4 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseFaturamentoServico}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chartTipoServ" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseFaturamentoRamoAtividade}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chartRamosTipo" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseFaturamentoTipoCliente}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chartTipoCliente" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>













                    <div class="col-md-6 col-sm-6 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseProd}<br />
                                        <span>#{localemsgs.ParadasGuias} </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chart1" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6 col-xs-12" style="padding:0px 10px 0px 10px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnalseValores}<br />
                                        <span>#{localemsgs.TotalTransportado}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chart2" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseValoresTransp}<br />
                                        <span>#{localemsgs.TotalValoresTransp}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chart3" height="220" style="float: left; width:100%; margin-top:15px;margin-bottom:15px"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6 col-xs-12" style="padding:0px 10px 0px 10px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseTrajetoExec}<br />
                                        <span>#{localemsgs.TotalKmPerc}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chart4" height="220" style="float: left; width:100%; margin-top:15px;margin-bottom:0px"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 10px 0px 10px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseParada}<br />
                                        <span>#{localemsgs.TotalParadasRota}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chart5" height="220" style="float: left; width:100%; margin-top:15px;margin-bottom:15px;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </p:panel>
                <script type="text/javascript">
                    // <![CDATA[
                    var CoresGrafico = new Array("#1E90FF", "#878BB6", "#4ACAB4", "#FF8153", "#FFEA88", "#6A5ACD", "#7CFC00", "#191970", "#ADFF2F", "#0000FF", "#00BFFF", "#6B8E23", "#4682B4", "#778899", "#008B8B", "#5F9EA0", "#808000", "#FF00FF", "#8B4513", "#DA70D6", "#CD853F", "#F4A400", "#F08080", "#8B0000", "#FA8072", "#FF0000", "#FFD700", "#4682B4", "#3F51B5", "#87CEFA", "#4682B4", "#B0C4DE", "#ADD8E6", "#B0E0E6", "#AFEEEE", "#00CED1", "#43CD80", "#2E8B57", "#9AFF9A", "#90EE90", "#7CCD7C", "#548B54", "#00FF7F", "#00EE76", "#00CD66", "#008B45", "#00FF00", "#00EE00", "#00CD00", "#008B00", "#7FFF00", "#76EE00", "#66CD00", "#458B00", "#C0FF3E", "#FF83FA", "#8B4789", "#FFBBFF", "#CD96CD", "#E066FF", "#B452CD", "#BF3EFF", "#9A32CD", "#68228B", "#7D26CD", "#551A8B", "#AB82FF", "#8968CD", "#5D478B", "#8B008B", "#8A2BE2", "#A020F0", "#9370DB", "#BA55D3", "#DDA0DD", "#FFF68F", "#CDC673", "#8B864E", "#FFEC8B", "#CDBE70", "#EEEED1", "#FFFF00", "#CDCD00", "#8B8B00", "#FFD700", "#CDAD00", "#FFC125", "#CD9B1D", "#FFB90F", "#CD950C", "#EEC900", "#EEB422", "#FF8247", "#CD6839", "#8B4726", "#CDAA7D", "#FFA54F", "#EE7942", "#EE9A49", "#FF7F24", "#FFD700", "#CD853F", "#F4A460", "#D2691E", "#FF8C00", "#FFA500", "#FF7F50", "#FF4500", "#CD661D", "#8B4513", "#EEC900", "#EE9A00", "#EE7600", "#EE9572", "#8B7500");

                    $(document).ready(function () {
                        CarregarGraficoEstatisticaPorDia();
                        CarregarGraficoEstatisticaPorRota();
                        CarregarGraficoEstatisticaPorHora();
                        CarregarGraficoEstatisticaTipos();
                    });

                    function CarregarGraficoEstatisticaPorDia() {
                        // Horas, Guias e Paradas
                        var optionsChart = {
                            scaleGridLineColor: "#303030",
                            responsive: false
                        };

                        var lineChartData = {
                            labels: #{dashboard.dadosGrafico1QdeLabels},
                            datasets: [
                                {
                                    label: "#{localemsgs.Paradas}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#F00000",
                                    lineTension: 0.1,
                                    pointBorderColor: "#F00000",
                                    pointBackgroundColor: "#F00000",
                                    pointStrokeColor: "transparent",
                                    data: #{dashboard.dadosGrafico1QdeParadas},
                                    spanGaps: false
                                },
                                {
                                    label: "#{localemsgs.Guias}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#4cff00",
                                    lineTension: 0.1,
                                    pointBorderColor: "#4cff00",
                                    pointBackgroundColor: "#4cff00",
                                    pointStrokeColor: "transparent",
                                    data: #{dashboard.dadosGrafico1QdeGuias},
                                    spanGaps: false
                                },
                                {
                                    label: "#{localemsgs.Rotas}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#0094ff",
                                    lineTension: 0.1,
                                    pointBorderColor: "#0094ff",
                                    pointBackgroundColor: "#0094ff",
                                    pointStrokeColor: "transparent",
                                    data: #{dashboard.dadosGrafico1QdeRotas},
                                    spanGaps: false
                                }
                            ]
                        };

                        Chart.defaults.global.hover.mode = 'nearest';
                        Chart.defaults.global.scaleLineColor = "#AAAAAA";
                        Chart.defaults.global.legend.display = true;

                        var ctxQtdeEventos = $('#chart1')[0].getContext("2d");

                        try {
                            var CharGuiasRotasParadas = new Chart(ctxQtdeEventos, {
                                type: "line",
                                data: lineChartData,
                                options: optionsChart
                            });

                        } catch (e) {
                            alert(e);
                        }

                        // Valores
                        var lineChartData2 = {
                            labels: #{dashboard.dadosGrafico1QdeLabels},
                            datasets: [
                                {
                                    label: "#{localemsgs.Valor}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#ffd800",
                                    lineTension: 0.1,
                                    pointBorderColor: "#ffd800",
                                    pointBackgroundColor: "#ffd800",
                                    pointStrokeColor: "transparent",
                                    data: #{dashboard.dadosGrafico2TotalValores},
                                    spanGaps: false
                                }
                            ]
                        };

                        Chart.defaults.global.legend.display = false;
                        var ctxDiaValor = $('#chart2')[0].getContext("2d");

                        try {
                            var ChartValores = new Chart(ctxDiaValor, {
                                type: "line",
                                data: lineChartData2,
                                options: optionsChart
                            });

                        } catch (e) {
                            alert(e);
                        }
                    }

                    function CarregarGraficoEstatisticaPorRota() {
                        // Por paradas
                        var options = {
                            animateRotate: true,
                            animateScale: false,
                            responsive: false,
                            maintainAspectRatio: true,
                            percentageInnerCutout: 90,
                            segmentStrokeColor: 'transparent'
                        };

                        var pieDataRotas = {
                            labels: #{dashboard.estatisticaRotaLabel},
                            datasets: [
                                {
                                    data: #{dashboard.estatisticaRotaParadas},
                                    backgroundColor: #{dashboard.estatisticaCoresParadas}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxParadasRota = document.getElementById("chart5").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxParadasRota, {
                                type: 'doughnut',
                                data: pieDataRotas,
                                options: options
                            });
                        } catch (e) {
                            alert(e);
                        }

                        // Por Valor
                        var pieDataValores = {
                            labels: #{dashboard.estatisticaRotaLabel},
                            datasets: [
                                {
                                    data: #{dashboard.estatisticaRotaValores},
                                    backgroundColor: #{dashboard.estatisticaCoresParadas}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxValoresRota = document.getElementById("chart3").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxValoresRota, {
                                type: 'pie',
                                data: pieDataValores,
                                options: options
                            });
                        } catch (e) {
                            alert(e);
                        }
                    }

                    function CarregarGraficoEstatisticaTipos() {
                        // Por Tipo serviço
                        var options = {
                            animateRotate: true,
                            animateScale: false,
                            responsive: false,
                            maintainAspectRatio: true,
                            percentageInnerCutout: 90,
                            segmentStrokeColor: 'transparent'
                        };

                        var pieTipoServico = {
                            labels: #{dashboard.estatisticaTipoServicoLabel},
                            datasets: [
                                {
                                    data: #{dashboard.estatisticaTipoServico},
                                    backgroundColor: #{dashboard.estatisticaCores1}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxTiposServico = document.getElementById("chartTipoServ").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxTiposServico, {
                                type: 'pie',
                                data: pieTipoServico,
                                options: options
                            });
                        } catch (e) {
                            alert(e);
                        }

                        // Por Ramos Tipo
                        var pieDataRamosTipo = {
                            labels: #{dashboard.estatisticaRamosTipoLabel},
                            datasets: [
                                {
                                    data: #{dashboard.estatisticaRamosTipo},
                                    backgroundColor: #{dashboard.estatisticaCores2}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxValoresRamosTipo = document.getElementById("chartRamosTipo").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxValoresRamosTipo, {
                                type: 'pie',
                                data: pieDataRamosTipo,
                                options: options
                            });
                        } catch (e) {
                            alert(e);
                        }

                        // Por Tipo Cliente
                        var pieDataTipoCliente = {
                            labels: #{dashboard.estatisticaTipoClienteLabel},
                            datasets: [
                                {
                                    data: #{dashboard.estatisticaTipoCliente},
                                    backgroundColor: #{dashboard.estatisticaCores3}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxValoresTipoCliente = document.getElementById("chartTipoCliente").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxValoresTipoCliente, {
                                type: 'pie',
                                data: pieDataTipoCliente,
                                options: options
                            });
                        } catch (e) {
                            alert(e);
                        }
                    }

                    function CarregarGraficoEstatisticaPorHora() {
                        // Horas, Guias e Paradas
                        var optionsChart = {
                            scaleGridLineColor: "#303030",
                            responsive: false
                        };

                        // Valores
                        var lineChartData3 = {
                            labels: #{dashboard.dadosGrafico1QdeLabels},
                            datasets: [
                                {
                                    label: "#{localemsgs.TrajetoExecutado}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#A020F0",
                                    lineTension: 0.1,
                                    pointBorderColor: "#A020F0",
                                    pointBackgroundColor: "#A020F0",
                                    pointStrokeColor: "transparent",
                                    data: #{dashboard.dadosGrafico1QdeKm},
                                    spanGaps: false
                                }
                            ]
                        };

                        var ctxDiaHora = $('#chart4')[0].getContext("2d");

                        try {
                            var ChartHoras = new Chart(ctxDiaHora, {
                                type: "line",
                                data: lineChartData3,
                                options: optionsChart
                            });

                        } catch (e) {
                            alert(e);
                        }
                    }
                    // ]]>
                </script>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>
        </h:body>
    </f:view>
</html>

package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Controller.Pessoas.PessoasSatMobWeb;
import Dados.Persistencia;
import SasBeans.*;
import br.com.sasw.pacotesuteis.controller.candidatos.CandidatosSatMobWeb;
import br.com.sasw.pacotesuteis.utilidades.BuscarEndereco;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.ValidadorCPF_CNPJ;
import br.com.sasw.utils.LocaleController;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.FlowEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.json.JSONObject;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

import javax.enterprise.context.SessionScoped;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.context.FacesContext;
import javax.inject.Named;
import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@ManagedBean
@Named(value = "candidato")
@SessionScoped
public class CandidatoMB implements Serializable {

    private final ArquivoLog logerro;
    private final CandidatosSatMobWeb candidatosSatMobWeb;
    private final PessoasSatMobWeb pessoasatmobweb;
    private final String dataTela;
    private Persistencia persistenciaLocal, persistenciaCentral;
    private String banco, caminho;
    private String empresa;
    private Pessoa novaPessoa;
    private String situacao;
    private boolean isEdicao;
    private boolean extTV, extSPP, extEscolta;
    private List<Municipios> cidades;
    private List<Pe_Doctos> documentos;
    private StreamedContent download;
    private List<Pe_Cargo> cargosPretendidos;
    private List<Cargos> cargos, cargosSelecionados = new ArrayList<>();

    public CandidatoMB() {
        candidatosSatMobWeb = new CandidatosSatMobWeb();
        pessoasatmobweb = new PessoasSatMobWeb();
        novaPessoa = new Pessoa();
        novaPessoa.setSituacao("C");
        dataTela = DataAtual.getDataAtual("SQL");

        logerro = new ArquivoLog();
    }

    public void Persistencias(Persistencia pstLocal, Persistencia pstCentral) {
        try {
            checaPersistencias(pstLocal, pstCentral);
            preparaCaminhoLog();
            cargos = pessoasatmobweb.listarCargos(pstLocal);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../index.xhtml");
            } catch (IOException ex) {
                displayFatal(ex.getMessage());
            }
        }
    }

    private void checaPersistencias(Persistencia pstLocal, Persistencia pstCentral) throws Exception {
        persistenciaCentral = pstCentral;
        if (null == persistenciaCentral) {
            throw new Exception("ImpossivelConectarSatellite");
        }
        persistenciaLocal = pstLocal;
        if (null == persistenciaLocal) {
            throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
        }
    }

    private void preparaCaminhoLog() {
        final String LOG_BASE_PATH = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\";
        banco = persistenciaLocal.getEmpresa();
        caminho = LOG_BASE_PATH + banco + "\\" + dataTela + ".txt";
    }

    private void displayOverlay(String mensagemDicionario, FacesMessage.Severity severity) {
        FacesMessage mensagem = new FacesMessage(severity, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
        PrimeFaces.current().ajax().update("msgs");
    }

    protected void displayInfo(String mensagemDicionario) {
        displayOverlay(mensagemDicionario, FacesMessage.SEVERITY_INFO);
    }

    protected void displayWarn(String mensagemDicionario) {
        displayOverlay(mensagemDicionario, FacesMessage.SEVERITY_WARN);
    }

    protected void displayError(String mensagemDicionario) {
        displayOverlay(mensagemDicionario, FacesMessage.SEVERITY_ERROR);
    }

    protected void displayFatal(String mensagemDicionario) {
        displayOverlay(mensagemDicionario, FacesMessage.SEVERITY_FATAL);
    }

    protected void logaErro(final Exception e, final String methodName) {
        String log = this.getClass().getSimpleName() + "\r\n"
                + methodName + "\r\n"
                + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    public String handleFlow(FlowEvent event) {
        String id = event.getOldStep();
        String next = event.getNewStep();

        if (id.equals("tabCPFEmail")) {
            try {
                buscarPessoaViaCPFeEMail();
            } catch (Exception e) {
                displayError(e.getMessage());
                return "tabCPFEmail";
            }
        } else if (!isEdicao && id.equals("tabDadosPessoais") && next.equals("tabFormacao")) {
            if (novaPessoa.getCodigo() == null) {
                try {
                    cadastrar();
                } catch (Exception e) {
                    logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
                    displayError("ErroSalvarBD");
                    return "tabDadosPessoais";
                }
            }
        } else if (!isEdicao && (id.equals("tabFormacao") || id.equals("tabDocumentos"))) {
            return "tabCPFEmail";
        }

        situacao = novaPessoa.getSituacao();
        if (situacao != null && situacao.equals("C")) {
            return next;
        } else {
            return "tabCPFEmail";
        }
    }

    private void buscarPessoaViaCPFeEMail() throws Exception {
        isEdicao = false;
        String cpf = Mascaras.removeMascara(novaPessoa.getCPF());
        String email = novaPessoa.getEmail().toLowerCase();
        if (cpf.equals("") || email.equals("")) {
            return;
        }

        Locale locale = LocaleController.getsCurrentLocale();
        if (locale.getLanguage().toUpperCase().equals("PT")) {
            if (!ValidadorCPF_CNPJ.ValidarCPF(cpf)) {
                throw new Exception(Messages.getMessageS("CPFInvalido"));
            }
        }

        buscaPessoa(cpf, email);
        if (isEdicao) {
//            if (!novaPessoa.getSituacao().equals("C")) {
//                throw new Exception(Messages.getMessageS("PessoaNaoEhCandidata"));
//            }
            documentos = candidatosSatMobWeb.listarDoctos(novaPessoa.getCodigo(), persistenciaLocal);
            cargosPretendidos = pessoasatmobweb.listarCargosPretendidos(novaPessoa.getCodigo().toString(), persistenciaLocal);
            convertCargosPretendidosParaSelecionados();
        } else {
            if (buscaCPF(cpf)) {
                throw new Exception(Messages.getMessageS("EmailIncorretoParaCPF"));
            }
        }
        refresh();
    }

    private void convertCargosPretendidosParaSelecionados() {
        cargosSelecionados = new ArrayList<>();
        for (Pe_Cargo peCargos : cargosPretendidos) {
            Cargos cargo = new Cargos();
            cargo.setCargo(String.format("%05d", Integer.parseInt(peCargos.getCod_Cargo())));
            cargo.setCodigo(peCargos.getCod_Cargo());
            cargo.setDescricao(peCargos.getDescricao());
            cargo.setDt_Alter(peCargos.getDt_Alter());
            cargo.setHr_Alter(peCargos.getHr_Alter());
            cargo.setOperador(peCargos.getOperador());
            cargosSelecionados.add(cargo);
        }
    }

    private void buscaPessoa(String cpf, String email) throws Exception {
        novaPessoa = pessoasatmobweb.buscarPessoaCPFAndEmail(cpf, email, persistenciaLocal);
        isEdicao = novaPessoa.getCodigo() != null;
    }

    private boolean buscaCPF(String cpf) throws Exception {
        Pessoa pessoa = pessoasatmobweb.buscarPessoa(cpf, persistenciaLocal);
        return pessoa.getCodigo() != null;
    }

    private void refresh() {
        String semMascara = Mascaras.removeMascara(novaPessoa.getCPF());
        novaPessoa.setCPF(Mascaras.CPF(semMascara));
        semMascara = Mascaras.removeMascara(novaPessoa.getCEP());
        novaPessoa.setCEP(Mascaras.CEP(semMascara));
        extTV = novaPessoa.getExtTV().equals("S");
        extSPP = novaPessoa.getExtSPP().equals("S");
        extEscolta = novaPessoa.getExtEscolta().equals("S");

        PrimeFaces.current().ajax().update("main:wizard");
    }

    public void buscarEndereco() {
        try {
            String enderecoCompleto = new BuscarEndereco().BuscarPeloCEP(novaPessoa.getCEP());
            JSONObject obj = new JSONObject(enderecoCompleto);
            if (Integer.parseInt(obj.get("resultado").toString()) == 1) {
                novaPessoa.setBairro(FuncoesString.RecortaString(obj.get("bairro").toString(), 0, 20));
                novaPessoa.setEndereco(obj.get("tipo_logradouro").toString() + " " + obj.get("logradouro").toString());
                if (obj.get("uf").toString().equals("DF") || obj.get("uf").toString().equals("df")) {
                    novaPessoa.setCidade("BRASILIA");
                    novaPessoa.setUF("DF");
                } else {
                    cidades = candidatosSatMobWeb.listarMunicipios(obj.get("uf").toString(), obj.get("cidade").toString(), persistenciaLocal);
                    novaPessoa.setCidade(cidades.get(0).getNome());
                    novaPessoa.setUF(cidades.get(0).getUF().substring(0, 2));
                }
                novaPessoa.setCEP(Mascaras.CEP(novaPessoa.getCEP()));
                PrimeFaces.current().executeScript("PF('dlgOk').show()");
            } else {
                novaPessoa.setCEP(Mascaras.CEP(novaPessoa.getCEP()));
                throw new Exception(Messages.getMessageS("EnderecoNaoEncontrado"));
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarCidade(SelectEvent event) {
        String[] parts = event.getObject().toString().split(", {2}");
        novaPessoa.setCidade(parts[0]);
        novaPessoa.setUF(parts[1]);
    }

    public List<String> buscarCidade(String query) {
        try {
            List<String> retorno = new ArrayList<>();
            cidades = candidatosSatMobWeb.listarMunicipios(query, persistenciaLocal);
            cidades.forEach(cidade -> retorno.add(cidade.getNome() + ",  " + cidade.getUF()));
            return retorno;
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return null;
    }

    public void handleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                UploadedFile uploadedFile = fileUploadEvent.getFile();
                Pe_Doctos docto = new Pe_Doctos();
                docto.setDescricao(uploadedFile.getFileName());
                docto.setCodigo(novaPessoa.getCodigo().toBigInteger().toString());
                docto.setDt_alter(DataAtual.getDataAtual("SQL"));
                docto.setHr_Alter(DataAtual.getDataAtual("HORA"));
                docto.setOperador(FuncoesString.RecortaAteEspaço(novaPessoa.getNome(), 0, 10));
                docto = candidatosSatMobWeb.inserirDocumento(docto, persistenciaLocal);
                boolean success = new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + persistenciaLocal.getEmpresa()
                        + "\\" + novaPessoa.getCodigo().toBigInteger()).mkdirs();
                if (!success) {
                    throw new Exception("ErroCriarDir");
                }
                String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + persistenciaLocal.getEmpresa()
                        + "\\" + novaPessoa.getCodigo().toBigInteger() + "\\" + docto.getDescricao();
                File file = new File(arquivo);
                FileOutputStream output = new FileOutputStream(file);
                output.write(uploadedFile.getContents());
                output.close();
                documentos = candidatosSatMobWeb.listarDoctos(novaPessoa.getCodigo(), persistenciaLocal);
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void handleFileDownload(Pe_Doctos documento) {
        try {
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + persistenciaLocal.getEmpresa()
                    + "\\" + novaPessoa.getCodigo().toBigInteger() + "\\" + documento.getDescricao();
            InputStream stream = new FileInputStream(arquivo);
            download = new DefaultStreamedContent(stream, "application/" + documento.getDescricao().split("\\.")[documento.getDescricao().split("\\.").length - 1], documento.getDescricao());
        } catch (FileNotFoundException e) {
            displayError("ArquivoNaoEncontrado");
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    /**
     * Excluí o documento no servidor O documento excluído e movido para uma
     * pasta de removidos O nome do documento entao e alterado para o formato
     * "Nome_UsuarioQueRealizouAcao_yyyy-MM-dd HH-mm-ss.extensao"
     *
     * @param documento documento a ser deletado
     */
    public void handleFileDelete(Pe_Doctos documento) {
        try {
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + persistenciaLocal.getEmpresa()
                    + "\\" + novaPessoa.getCodigo().toBigInteger() + "\\" + documento.getDescricao();
            File file = new File(arquivo);

            // cria diretório de removidos
            File removidos = new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + persistenciaLocal.getEmpresa()
                    + "\\" + novaPessoa.getCodigo().toBigInteger() + "\\" + "Removidos");
            boolean success = removidos.mkdirs();
            if (!success) {
                logaErro(new Exception("ErroCriarDir"), Thread.currentThread().getStackTrace()[1].getMethodName());
            }

            if (documento.getDescricao().lastIndexOf(".") > 0) {
                String nome = documento.getDescricao().substring(0, documento.getDescricao().lastIndexOf("."));
                String tipo = documento.getDescricao().split("\\.")[documento.getDescricao().split("\\.").length - 1];

                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
                LocalDateTime now = LocalDateTime.now();

                String newFilename = removidos + "\\" + nome + "_" + FuncoesString.RecortaAteEspaço(novaPessoa.getNome(), 0, 10) + "_" + dtf.format(now) + "." + tipo;
                success = file.renameTo(new File(newFilename));
                // FIXME: aparentemente o arquivo nunca é renomeado corretamente
                // é preciso checar no servidor
                if (!success) {
                    logaErro(new Exception("ErroDeletarArquivo"), Thread.currentThread().getStackTrace()[1].getMethodName());
                }
            }
            candidatosSatMobWeb.deletarDocumento(documento, persistenciaLocal);
            documentos = candidatosSatMobWeb.listarDoctos(novaPessoa.getCodigo(), persistenciaLocal);
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private boolean verificaVirgula(String numero) {
        char[] array = numero.toCharArray();

        for (char c : array) {
            if (c == ',' || c == '.') {
                return true;
            }
        }
        return false;
    }

    private void cadastrar() throws Exception {
        try {
            situacao = novaPessoa.getSituacao();
            boolean candidato = situacao != null && situacao.equals("C");
            BigDecimal codigo = novaPessoa.getCodigo();
            if (codigo != null || !candidato) {
                throw new Exception(Messages.getMessageS("PermissaoNegada"));
            }

            Locale locale = LocaleController.getsCurrentLocale();

            novaPessoa.setCPF(Mascaras.removeMascara(novaPessoa.getCPF()));
            if (locale.getLanguage().toUpperCase().equals("PT")
                    && !novaPessoa.getSituacao().equals("I")
                    && !novaPessoa.getCPF().equals("")) {
                if (!ValidadorCPF_CNPJ.ValidarCPF(novaPessoa.getCPF())) {
                    throw new Exception(Messages.getMessageS("CPFInvalido"));
                }
            }

            novaPessoa.setOperador(FuncoesString.RecortaAteEspaço(novaPessoa.getNome().toUpperCase(), 0, 10));
            novaPessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
            novaPessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            novaPessoa.setDt_Situac(DataAtual.getDataAtual("SQL"));
            novaPessoa.setNome(novaPessoa.getNome().toUpperCase());

            if (!novaPessoa.getSituacao().equals("I")) {
                novaPessoa.setRG(novaPessoa.getRG().toUpperCase());
                novaPessoa.setRGOrgEmis(novaPessoa.getRGOrgEmis().toUpperCase());
                novaPessoa.setEmail(novaPessoa.getEmail().toLowerCase());
            }

            novaPessoa.setEndereco(FuncoesString.RecortaString(novaPessoa.getEndereco().toUpperCase(), 0, 50));
            novaPessoa.setBairro(FuncoesString.RecortaString(novaPessoa.getBairro().toUpperCase(), 0, 20));
            novaPessoa.setUF(FuncoesString.RecortaString(novaPessoa.getUF().toUpperCase(), 0, 2));
            novaPessoa.setFuncao(novaPessoa.getFuncao() == null ? null : novaPessoa.getFuncao().toUpperCase());

            if (novaPessoa.getComplemento() != null && !novaPessoa.getComplemento().equals("")) {
                novaPessoa.setComplemento(novaPessoa.getComplemento().toUpperCase());
            }

            if (novaPessoa.getSexo() != null) {
                novaPessoa.setSexo(novaPessoa.getSexo().toUpperCase());
            }

            novaPessoa.setCEP(Mascaras.removeMascara(novaPessoa.getCEP()));

            if (!novaPessoa.getSituacao().equals("I")) {
                novaPessoa.setFone1(Mascaras.removeMascara(novaPessoa.getFone1()));
                novaPessoa.setFone2(Mascaras.removeMascara(novaPessoa.getFone2()));
                novaPessoa.setDt_nasc(Mascaras.removeMascara(novaPessoa.getDt_nasc()));
                novaPessoa.setDtValCNV(Mascaras.removeMascara(novaPessoa.getDtValCNV()));
            }

            if (!novaPessoa.getSituacao().equals("I")) {
                if (novaPessoa.getAltura() != null || novaPessoa.getPeso() != null) {
                    if (verificaVirgula(novaPessoa.getAltura().toString())) {
                        displayWarn("AlturaSemVirgula");
                    } else if (verificaVirgula(novaPessoa.getPeso().toString())) {
                        displayWarn("PessoaSemVirgula");
                    }
                }
            } else {
                novaPessoa.setAltura("0");
                novaPessoa.setAltura(BigDecimal.ZERO);
                novaPessoa.setPeso("0");
                novaPessoa.setPeso(BigDecimal.ZERO);
            }

            pessoasatmobweb.Inserir(novaPessoa, persistenciaLocal, persistenciaCentral);
            isEdicao = true;
            buscaPessoa(novaPessoa.getCPF(), novaPessoa.getEmail()); // para buscar codigo

            documentos = candidatosSatMobWeb.listarDoctos(novaPessoa.getCodigo(), persistenciaLocal);
            cargosPretendidos = pessoasatmobweb.listarCargosPretendidos(novaPessoa.getCodigo().toString(), persistenciaLocal);
            convertCargosPretendidosParaSelecionados();
            displayInfo("CadastroSucessoCandidato");
        } finally {
            refresh();
        }
    }

    public void editar() {
        try {
            situacao = novaPessoa.getSituacao();
            boolean candidato = situacao != null && situacao.equals("C");
            BigDecimal codigo = novaPessoa.getCodigo();
            if (codigo == null || !candidato) {
                throw new Exception(Messages.getMessageS("PermissaoNegada"));
            }

            novaPessoa.setOperador(FuncoesString.RecortaAteEspaço(novaPessoa.getNome(), 0, 10));
            novaPessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
            novaPessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            novaPessoa.setDt_Situac(DataAtual.getDataAtual("SQL"));
            novaPessoa.setNome(novaPessoa.getNome().toUpperCase());

            if (!novaPessoa.getSituacao().equals("I")) {
                novaPessoa.setCPF(Mascaras.removeMascara(novaPessoa.getCPF()));
                novaPessoa.setRG(novaPessoa.getRG().toUpperCase());
                novaPessoa.setRGOrgEmis(novaPessoa.getRGOrgEmis().toUpperCase());
                novaPessoa.setEmail(novaPessoa.getEmail().toLowerCase());
                novaPessoa.setFone1(Mascaras.removeMascara(novaPessoa.getFone1()));
                novaPessoa.setFone2(Mascaras.removeMascara(novaPessoa.getFone2()));
                novaPessoa.setDt_nasc(Mascaras.removeMascara(novaPessoa.getDt_nasc()));
                novaPessoa.setDtValCNV(Mascaras.removeMascara(novaPessoa.getDtValCNV()));
            }

            if (novaPessoa.getComplemento() != null && !novaPessoa.getComplemento().equals("")) {
                novaPessoa.setComplemento(novaPessoa.getComplemento().toUpperCase());
            }

            novaPessoa.setEndereco(FuncoesString.RecortaString(novaPessoa.getEndereco().toUpperCase(), 0, 50));
            novaPessoa.setBairro(FuncoesString.RecortaString(novaPessoa.getBairro(), 0, 20));
            novaPessoa.setFuncao(novaPessoa.getFuncao().toUpperCase());
            novaPessoa.setUF(FuncoesString.RecortaString(novaPessoa.getUF().toUpperCase(), 0, 2));
            novaPessoa.setSexo(novaPessoa.getSexo().toUpperCase());
            novaPessoa.setObs(novaPessoa.getObs().toUpperCase());

            novaPessoa.setCEP(Mascaras.removeMascara(novaPessoa.getCEP()));

            if (novaPessoa.getAltura() != null || novaPessoa.getPeso() != null) {
                if (verificaVirgula(novaPessoa.getAltura().toString())) {
                    displayWarn("AlturaSemVirgula");
                } else if (verificaVirgula(novaPessoa.getPeso().toString())) {
                    displayWarn("PessoaSemVirgula");
                }
            }

            novaPessoa.setIndicacao(novaPessoa.getIndicacao().toUpperCase());
            novaPessoa.setDt_FormIni(Mascaras.removeMascaraData(novaPessoa.getDt_FormIni()));
            novaPessoa.setDt_FormFim(Mascaras.removeMascaraData(novaPessoa.getDt_FormFim()));
            novaPessoa.setLocalForm(novaPessoa.getLocalForm().toUpperCase());
            novaPessoa.setCertific(novaPessoa.getCertific().toUpperCase());
            novaPessoa.setDt_Recicl(Mascaras.removeMascaraData(novaPessoa.getDt_Recicl()));
            novaPessoa.setDt_VenCurs(Mascaras.removeMascaraData(novaPessoa.getDt_VenCurs()));
            novaPessoa.setReg_PF(novaPessoa.getReg_PF().toUpperCase());
            novaPessoa.setReg_PFUF(novaPessoa.getReg_PFUF().toUpperCase());
            novaPessoa.setReg_PFDt(Mascaras.removeMascaraData(novaPessoa.getReg_PFDt()));
            novaPessoa.setCNH(novaPessoa.getCNH().toUpperCase());
            novaPessoa.setCNHDtVenc(Mascaras.removeMascaraData(novaPessoa.getCNHDtVenc()));
            novaPessoa.setExtTV(extTV ? "S" : "N");
            novaPessoa.setExtSPP(extSPP ? "S" : "N");
            novaPessoa.setExtEscolta(extEscolta ? "S" : "N");

            novaPessoa = (Pessoa) FuncoesString.removeAcentoObjeto(novaPessoa);
            salvarCargos();
            pessoasatmobweb.gravarCandidato(novaPessoa, persistenciaLocal, persistenciaCentral);

            displayInfo("EdicaoSucessoCandidato");
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        } finally {
            refresh();
        }
    }

    private void salvarCargos() {
        try {
            String codigo = novaPessoa.getCodigo().toString();
            String operador = FuncoesString.RecortaAteEspaço(novaPessoa.getNome(), 0, 10);

            pessoasatmobweb.inserirListaCargoPretendido(cargosSelecionados, codigo, operador, persistenciaLocal);
            cargosPretendidos = pessoasatmobweb.listarCargosPretendidos(codigo, persistenciaLocal);
            convertCargosPretendidosParaSelecionados();
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public Pessoa getNovaPessoa() {
        return novaPessoa;
    }

    public List<Pe_Doctos> getDocumentos() {
        return documentos;
    }

    public void setDocumentos(List<Pe_Doctos> documentos) {
        this.documentos = documentos;
    }

    public boolean isExtTV() {
        return extTV;
    }

    public void setExtTV(boolean extTV) {
        this.extTV = extTV;
    }

    public boolean isExtSPP() {
        return extSPP;
    }

    public void setExtSPP(boolean extSPP) {
        this.extSPP = extSPP;
    }

    public boolean isExtEscolta() {
        return extEscolta;
    }

    public void setExtEscolta(boolean extEscolta) {
        this.extEscolta = extEscolta;
    }

    public String getDataTela() {
        return dataTela;
    }

    public List<Cargos> getCargos() {
        return cargos;
    }

    public List<Cargos> getCargosSelecionados() {
        return cargosSelecionados;
    }

    public void setCargosSelecionados(List<Cargos> cargosSelecionados) {
        this.cargosSelecionados = cargosSelecionados;
    }

    public boolean isIsEdicao() {
        return isEdicao;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public StreamedContent getDownload() {
        return download;
    }

    public void setDownload(StreamedContent download) {
        this.download = download;
    }
}

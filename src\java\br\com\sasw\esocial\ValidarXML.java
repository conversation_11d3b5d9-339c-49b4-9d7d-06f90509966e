/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.esocial;

import static br.com.sasw.esocial.UtilXML.subStrIntoDelim;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import javax.xml.XMLConstants;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import org.xml.sax.SAXException;

/**
 *
 * <AUTHOR>
 */
public class ValidarXML {

    public static String obterEventos(String xml) {
        String erros = "";
        try {
            String linha;
            List<String> eventos = new ArrayList<>();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8.name()))));
            while ((linha = bufferedReader.readLine()) != null) {
                if (linha.contains("Id=")) {
                    eventos.add(subStrIntoDelim(linha, "<", " "));
                }
            }

            //InputStream xmlFile = new ByteArrayInputStream(xml.getBytes());
            File xsdFile, xmlFile = new File("C:\\Users\\<USER>\\Downloads\\leiautes-do-esocial-v2-4-pdf-e-xsd(1)\\Esquemas XSD v2.4\\envio.xml");
            for (String evento : eventos) {
                xsdFile = new File("C:\\Users\\<USER>\\Downloads\\leiautes-do-esocial-v2-4-pdf-e-xsd(1)\\Esquemas XSD v2.4\\" + evento + ".xsd");
                erros += (validarXML(xmlFile, xsdFile) ? evento + ", " : "");
            }
        } catch (Exception e) {

        }
        return erros;
    }

    public static boolean validarXML(File xml, File xsd) {
        try {
            Source schemaFile = new StreamSource(xsd);
            Source xmlFile = new StreamSource(xml);
            SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
            Schema schema = schemaFactory.newSchema(schemaFile);
            Validator validator = schema.newValidator();
            validator.validate(xmlFile);
            return true;
        } catch (SAXException | IOException ex) {
            System.err.println(ex.getMessage());
            return false;
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.XMLSiates;

/**
 *
 * <AUTHOR>
 */
public class Tipo1 {

    private ArquivoXml arquivoxml;

    public ArquivoXml getArquivoxml() {
        return arquivoxml;
    }

    public void setArquivoxml(ArquivoXml arquivoxml) {
        this.arquivoxml = arquivoxml;
    }

    public static class ArquivoXml {

        public ArquivoXml(Info_Arquivo info_arquivo, Info_Demandante info_demandante, Info_Fornecedor info_fornecedor,
                Categorizacao categorizacao, Info_Solicitante info_solicitante, Solicitacao solicitacao, Anexos anexos) {
            this.info_arquivo = info_arquivo;
            this.info_demandante = info_demandante;
            this.info_fornecedor = info_fornecedor;
            this.categorizacao = categorizacao;
            this.info_solicitante = info_solicitante;
            this.solicitacao = solicitacao;
            this.anexos = anexos;
        }

        private Info_Arquivo info_arquivo;
        private Info_Demandante info_demandante;
        private Info_Fornecedor info_fornecedor;
        private Categorizacao categorizacao;
        private Info_Solicitante info_solicitante;
        private Solicitacao solicitacao;
        private Anexos anexos;

        public Info_Arquivo getInfo_arquivo() {
            return info_arquivo;
        }

        public void setInfo_arquivo(Info_Arquivo info_arquivo) {
            this.info_arquivo = info_arquivo;
        }

        public Info_Demandante getInfo_demandante() {
            return info_demandante;
        }

        public void setInfo_demandante(Info_Demandante info_demandante) {
            this.info_demandante = info_demandante;
        }

        public Info_Fornecedor getInfo_fornecedor() {
            return info_fornecedor;
        }

        public void setInfo_fornecedor(Info_Fornecedor info_fornecedor) {
            this.info_fornecedor = info_fornecedor;
        }

        public Categorizacao getCategorizacao() {
            return categorizacao;
        }

        public void setCategorizacao(Categorizacao categorizacao) {
            this.categorizacao = categorizacao;
        }

        public Info_Solicitante getInfo_solicitante() {
            return info_solicitante;
        }

        public void setInfo_solicitante(Info_Solicitante info_solicitante) {
            this.info_solicitante = info_solicitante;
        }

        public Solicitacao getSolicitacao() {
            return solicitacao;
        }

        public void setSolicitacao(Solicitacao solicitacao) {
            this.solicitacao = solicitacao;
        }
    }

    public static class Info_Arquivo {

        public Info_Arquivo(String tipoarquivo, String idarquivo, String datahorageracaoarquivo, String comunicacao) {
            this.tipoarquivo = tipoarquivo;
            this.idarquivo = idarquivo;
            this.datahorageracaoarquivo = datahorageracaoarquivo;
            this.comunicacao = comunicacao;
        }

        private String tipoarquivo;
        private String idarquivo;
        private String datahorageracaoarquivo;
        private String comunicacao;

        private String tipoarquivoExt;

        public String getTipoarquivo() {
            return tipoarquivo;
        }

        public void setTipoarquivo(String tipoarquivo) {
            this.tipoarquivo = tipoarquivo;
        }

        public String getIdarquivo() {
            return idarquivo;
        }

        public void setIdarquivo(String idarquivo) {
            this.idarquivo = idarquivo;
        }

        public String getDatahorageracaoarquivo() {
            return datahorageracaoarquivo;
        }

        public void setDatahorageracaoarquivo(String datahorageracaoarquivo) {
            this.datahorageracaoarquivo = datahorageracaoarquivo;
        }

        public String getComunicacao() {
            return comunicacao;
        }

        public void setComunicacao(String comunicacao) {
            this.comunicacao = comunicacao;
        }

        public String getTipoarquivoExt() {
            switch (this.tipoarquivo) {
                case "1":
                    return "Abertura de Chamado";
                case "2":
                    return "Aceite/Recusa";
                case "3":
                    return "Atualização";
                case "4":
                    return "Retorno para Fornecedor";
                case "5":
                    return "Complementação/Reiteração";
            }
            return "";
        }
    }

    public static class Info_Demandante {

        public Info_Demandante(String idgruposuportedemandante, String gruposuportedemandante, Chamado_Caixa chamado_caixa, TipoRequisicao tiporequisicao) {
            this.idgruposuportedemandante = idgruposuportedemandante;
            this.gruposuportedemandante = gruposuportedemandante;
            this.chamado_caixa = chamado_caixa;
            this.tiporequisicao = tiporequisicao;
        }

        private String idgruposuportedemandante;
        private String gruposuportedemandante;
        private Chamado_Caixa chamado_caixa;
        private TipoRequisicao tiporequisicao;

        public String getIdgruposuportedemandante() {
            return idgruposuportedemandante;
        }

        public void setIdgruposuportedemandante(String idgruposuportedemandante) {
            this.idgruposuportedemandante = idgruposuportedemandante;
        }

        public String getGruposuportedemandante() {
            return gruposuportedemandante;
        }

        public void setGruposuportedemandante(String gruposuportedemandante) {
            this.gruposuportedemandante = gruposuportedemandante;
        }

        public Chamado_Caixa getChamado_caixa() {
            return chamado_caixa;
        }

        public void setChamado_caixa(Chamado_Caixa chamado_caixa) {
            this.chamado_caixa = chamado_caixa;
        }

        public TipoRequisicao getTiporequisicao() {
            return tiporequisicao;
        }

        public void setTiporequisicao(TipoRequisicao tiporequisicao) {
            this.tiporequisicao = tiporequisicao;
        }
    }

    public static class Chamado_Caixa {

        public Chamado_Caixa(String no_req, String no_wo, String no_inc, String no_crq, String numero_serie) {
            this.no_req = no_req;
            this.no_wo = no_wo;
            this.no_inc = no_inc;
            this.no_crq = no_crq;
            this.numero_serie = numero_serie;
        }

        private String no_req;
        private String no_wo;
        private String no_inc;
        private String no_crq;
        private String numero_serie;

        public String getNo_req() {
            return no_req;
        }

        public void setNo_req(String no_req) {
            this.no_req = no_req;
        }

        public String getNo_wo() {
            return no_wo;
        }

        public void setNo_wo(String no_wo) {
            this.no_wo = no_wo;
        }

        public String getNo_inc() {
            return no_inc;
        }

        public void setNo_inc(String no_inc) {
            this.no_inc = no_inc;
        }

        public String getNo_crq() {
            return no_crq;
        }

        public void setNo_crq(String no_crq) {
            this.no_crq = no_crq;
        }

        public String getNumero_serie() {
            return numero_serie;
        }

        public void setNumero_serie(String numero_serie) {
            this.numero_serie = numero_serie;
        }
    }

    public static class TipoRequisicao {

        public TipoRequisicao(String idreq, String nomereq) {
            this.idreq = idreq;
            this.nomereq = nomereq;
        }

        private String idreq;
        private String nomereq;

        public String getIdreq() {
            return idreq;
        }

        public void setIdreq(String idreq) {
            this.idreq = idreq;
        }

        public String getNomereq() {
            return nomereq;
        }

        public void setNomereq(String nomereq) {
            this.nomereq = nomereq;
        }
    }

    public static class Info_Fornecedor {

        public Info_Fornecedor(String idfornecedor, String nomefornecedor, String prioridade) {
            this.idfornecedor = idfornecedor;
            this.nomefornecedor = nomefornecedor;
            this.prioridade = prioridade;
        }

        private String idfornecedor;
        private String nomefornecedor;
        private String prioridade;

        public String getIdfornecedor() {
            return idfornecedor;
        }

        public void setIdfornecedor(String idfornecedor) {
            this.idfornecedor = idfornecedor;
        }

        public String getNomefornecedor() {
            return nomefornecedor;
        }

        public void setNomefornecedor(String nomefornecedor) {
            this.nomefornecedor = nomefornecedor;
        }

        public String getPrioridade() {
            return prioridade;
        }

        public void setPrioridade(String prioridade) {
            this.prioridade = prioridade;
        }
    }

    public static class Categorizacao {

        public Categorizacao(String categ_op_n1, String categ_op_n2, String categ_op_n3, String categ_prod_n1, String categ_prod_n2, String categ_prod_n3, String nomeproduto, String modeloproduto, String fabricante) {
            this.categ_op_n1 = categ_op_n1;
            this.categ_op_n2 = categ_op_n2;
            this.categ_op_n3 = categ_op_n3;
            this.categ_prod_n1 = categ_prod_n1;
            this.categ_prod_n2 = categ_prod_n2;
            this.categ_prod_n3 = categ_prod_n3;
            this.nomeproduto = nomeproduto;
            this.modeloproduto = modeloproduto;
            this.fabricante = fabricante;
        }

        private String categ_op_n1;
        private String categ_op_n2;
        private String categ_op_n3;
        private String categ_prod_n1;
        private String categ_prod_n2;
        private String categ_prod_n3;
        private String nomeproduto;
        private String modeloproduto;
        private String fabricante;

        public String getCateg_op_n1() {
            return categ_op_n1;
        }

        public void setCateg_op_n1(String categ_op_n1) {
            this.categ_op_n1 = categ_op_n1;
        }

        public String getCateg_op_n2() {
            return categ_op_n2;
        }

        public void setCateg_op_n2(String categ_op_n2) {
            this.categ_op_n2 = categ_op_n2;
        }

        public String getCateg_op_n3() {
            return categ_op_n3;
        }

        public void setCateg_op_n3(String categ_op_n3) {
            this.categ_op_n3 = categ_op_n3;
        }

        public String getCateg_prod_n1() {
            return categ_prod_n1;
        }

        public void setCateg_prod_n1(String categ_prod_n1) {
            this.categ_prod_n1 = categ_prod_n1;
        }

        public String getCateg_prod_n2() {
            return categ_prod_n2;
        }

        public void setCateg_prod_n2(String categ_prod_n2) {
            this.categ_prod_n2 = categ_prod_n2;
        }

        public String getCateg_prod_n3() {
            return categ_prod_n3;
        }

        public void setCateg_prod_n3(String categ_prod_n3) {
            this.categ_prod_n3 = categ_prod_n3;
        }

        public String getNomeproduto() {
            return nomeproduto;
        }

        public void setNomeproduto(String nomeproduto) {
            this.nomeproduto = nomeproduto;
        }

        public String getModeloproduto() {
            return modeloproduto;
        }

        public void setModeloproduto(String modeloproduto) {
            this.modeloproduto = modeloproduto;
        }

        public String getFabricante() {
            return fabricante;
        }

        public void setFabricante(String fabricante) {
            this.fabricante = fabricante;
        }
    }

    public static class Info_Solicitante {

        public Info_Solicitante(String codigodobanco, String tipounidade, String codigounidade, String siglaunidade, String nomeunidade, String enderecounidade, String cidadeunidade, String ufunidade, String cep, String idsolicitante, String contatonome, String contatotelefone, String contatoemail) {
            this.codigodobanco = codigodobanco;
            this.tipounidade = tipounidade;
            this.codigounidade = codigounidade;
            this.siglaunidade = siglaunidade;
            this.nomeunidade = nomeunidade;
            this.enderecounidade = enderecounidade;
            this.cidadeunidade = cidadeunidade;
            this.ufunidade = ufunidade;
            this.cep = cep;
            this.idsolicitante = idsolicitante;
            this.contatonome = contatonome;
            this.contatotelefone = contatotelefone;
            this.contatoemail = contatoemail;
        }

        private String codigodobanco;
        private String tipounidade;
        private String codigounidade;
        private String siglaunidade;
        private String nomeunidade;
        private String enderecounidade;
        private String cidadeunidade;
        private String ufunidade;
        private String cep;
        private String idsolicitante;
        private String contatonome;
        private String contatotelefone;
        private String contatoemail;

        public String getCodigodobanco() {
            return codigodobanco;
        }

        public void setCodigodobanco(String codigodobanco) {
            this.codigodobanco = codigodobanco;
        }

        public String getTipounidade() {
            return tipounidade;
        }

        public void setTipounidade(String tipounidade) {
            this.tipounidade = tipounidade;
        }

        public String getCodigounidade() {
            return codigounidade;
        }

        public void setCodigounidade(String codigounidade) {
            this.codigounidade = codigounidade;
        }

        public String getSiglaunidade() {
            return siglaunidade;
        }

        public void setSiglaunidade(String siglaunidade) {
            this.siglaunidade = siglaunidade;
        }

        public String getNomeunidade() {
            return nomeunidade;
        }

        public void setNomeunidade(String nomeunidade) {
            this.nomeunidade = nomeunidade;
        }

        public String getEnderecounidade() {
            return enderecounidade;
        }

        public void setEnderecounidade(String enderecounidade) {
            this.enderecounidade = enderecounidade;
        }

        public String getCidadeunidade() {
            return cidadeunidade;
        }

        public void setCidadeunidade(String cidadeunidade) {
            this.cidadeunidade = cidadeunidade;
        }

        public String getUfunidade() {
            return ufunidade;
        }

        public void setUfunidade(String ufunidade) {
            this.ufunidade = ufunidade;
        }

        public String getCep() {
            return cep;
        }

        public void setCep(String cep) {
            this.cep = cep;
        }

        public String getIdsolicitante() {
            return idsolicitante;
        }

        public void setIdsolicitante(String idsolicitante) {
            this.idsolicitante = idsolicitante;
        }

        public String getContatonome() {
            return contatonome;
        }

        public void setContatonome(String contatonome) {
            this.contatonome = contatonome;
        }

        public String getContatotelefone() {
            return contatotelefone;
        }

        public void setContatotelefone(String contatotelefone) {
            this.contatotelefone = contatotelefone;
        }

        public String getContatoemail() {
            return contatoemail;
        }

        public void setContatoemail(String contatoemail) {
            this.contatoemail = contatoemail;
        }
    }

    public static class Solicitacao {

        public Solicitacao(String descricao, Detalhes detalhes) {
            this.descricao = descricao;
            this.detalhes = detalhes;
        }

        private String descricao;
        private Detalhes detalhes;

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }

        public Detalhes getDetalhes() {
            return detalhes;
        }

        public void setDetalhes(Detalhes detalhes) {
            this.detalhes = detalhes;
        }
    }

    public static class Detalhes {

        public Detalhes(String detalhe1, String detalhe2, String detalhe3, String detalhe4, String detalhe5, String detalhe6, String detalhe7, String detalhe8, String detalhe9, String detalhe10, String detalhe11, String detalhe12, String detalhe13, String detalhe14, String detalhe15, String detalhe16, String detalhe17, String detalhe18, String detalhe19, String detalhe20, String detalhe21, String detalhe22, String detalhe23, String detalhe24, String detalhe25, String detalhe26, String detalhe27, String detalhe28, String detalhe29, String detalhe30) {
            this.detalhe1 = detalhe1;
            this.detalhe2 = detalhe2;
            this.detalhe3 = detalhe3;
            this.detalhe4 = detalhe4;
            this.detalhe5 = detalhe5;
            this.detalhe6 = detalhe6;
            this.detalhe7 = detalhe7;
            this.detalhe8 = detalhe8;
            this.detalhe9 = detalhe9;
            this.detalhe10 = detalhe10;
            this.detalhe11 = detalhe11;
            this.detalhe12 = detalhe12;
            this.detalhe13 = detalhe13;
            this.detalhe14 = detalhe14;
            this.detalhe15 = detalhe15;
            this.detalhe16 = detalhe16;
            this.detalhe17 = detalhe17;
            this.detalhe18 = detalhe18;
            this.detalhe19 = detalhe19;
            this.detalhe20 = detalhe20;
            this.detalhe21 = detalhe21;
            this.detalhe22 = detalhe22;
            this.detalhe23 = detalhe23;
            this.detalhe24 = detalhe24;
            this.detalhe25 = detalhe25;
            this.detalhe26 = detalhe26;
            this.detalhe27 = detalhe27;
            this.detalhe28 = detalhe28;
            this.detalhe29 = detalhe29;
            this.detalhe30 = detalhe30;
        }

        private String detalhe1;
        private String detalhe2;
        private String detalhe3;
        private String detalhe4;
        private String detalhe5;
        private String detalhe6;
        private String detalhe7;
        private String detalhe8;
        private String detalhe9;
        private String detalhe10;
        private String detalhe11;
        private String detalhe12;
        private String detalhe13;
        private String detalhe14;
        private String detalhe15;
        private String detalhe16;
        private String detalhe17;
        private String detalhe18;
        private String detalhe19;
        private String detalhe20;
        private String detalhe21;
        private String detalhe22;
        private String detalhe23;
        private String detalhe24;
        private String detalhe25;
        private String detalhe26;
        private String detalhe27;
        private String detalhe28;
        private String detalhe29;
        private String detalhe30;

        public String getDetalhe1() {
            return detalhe1;
        }

        public void setDetalhe1(String detalhe1) {
            this.detalhe1 = detalhe1;
        }

        public String getDetalhe2() {
            return detalhe2;
        }

        public void setDetalhe2(String detalhe2) {
            this.detalhe2 = detalhe2;
        }

        public String getDetalhe3() {
            return detalhe3;
        }

        public void setDetalhe3(String detalhe3) {
            this.detalhe3 = detalhe3;
        }

        public String getDetalhe4() {
            return detalhe4;
        }

        public void setDetalhe4(String detalhe4) {
            this.detalhe4 = detalhe4;
        }

        public String getDetalhe5() {
            return detalhe5;
        }

        public void setDetalhe5(String detalhe5) {
            this.detalhe5 = detalhe5;
        }

        public String getDetalhe6() {
            return detalhe6;
        }

        public void setDetalhe6(String detalhe6) {
            this.detalhe6 = detalhe6;
        }

        public String getDetalhe7() {
            return detalhe7;
        }

        public void setDetalhe7(String detalhe7) {
            this.detalhe7 = detalhe7;
        }

        public String getDetalhe8() {
            return detalhe8;
        }

        public void setDetalhe8(String detalhe8) {
            this.detalhe8 = detalhe8;
        }

        public String getDetalhe9() {
            return detalhe9;
        }

        public void setDetalhe9(String detalhe9) {
            this.detalhe9 = detalhe9;
        }

        public String getDetalhe10() {
            return detalhe10;
        }

        public void setDetalhe10(String detalhe10) {
            this.detalhe10 = detalhe10;
        }

        public String getDetalhe11() {
            return detalhe11;
        }

        public void setDetalhe11(String detalhe11) {
            this.detalhe11 = detalhe11;
        }

        public String getDetalhe12() {
            return detalhe12;
        }

        public void setDetalhe12(String detalhe12) {
            this.detalhe12 = detalhe12;
        }

        public String getDetalhe13() {
            return detalhe13;
        }

        public void setDetalhe13(String detalhe13) {
            this.detalhe13 = detalhe13;
        }

        public String getDetalhe14() {
            return detalhe14;
        }

        public void setDetalhe14(String detalhe14) {
            this.detalhe14 = detalhe14;
        }

        public String getDetalhe15() {
            return detalhe15;
        }

        public void setDetalhe15(String detalhe15) {
            this.detalhe15 = detalhe15;
        }

        public String getDetalhe16() {
            return detalhe16;
        }

        public void setDetalhe16(String detalhe16) {
            this.detalhe16 = detalhe16;
        }

        public String getDetalhe17() {
            return detalhe17;
        }

        public void setDetalhe17(String detalhe17) {
            this.detalhe17 = detalhe17;
        }

        public String getDetalhe18() {
            return detalhe18;
        }

        public void setDetalhe18(String detalhe18) {
            this.detalhe18 = detalhe18;
        }

        public String getDetalhe19() {
            return detalhe19;
        }

        public void setDetalhe19(String detalhe19) {
            this.detalhe19 = detalhe19;
        }

        public String getDetalhe20() {
            return detalhe20;
        }

        public void setDetalhe20(String detalhe20) {
            this.detalhe20 = detalhe20;
        }

        public String getDetalhe21() {
            return detalhe21;
        }

        public void setDetalhe21(String detalhe21) {
            this.detalhe21 = detalhe21;
        }

        public String getDetalhe22() {
            return detalhe22;
        }

        public void setDetalhe22(String detalhe22) {
            this.detalhe22 = detalhe22;
        }

        public String getDetalhe23() {
            return detalhe23;
        }

        public void setDetalhe23(String detalhe23) {
            this.detalhe23 = detalhe23;
        }

        public String getDetalhe24() {
            return detalhe24;
        }

        public void setDetalhe24(String detalhe24) {
            this.detalhe24 = detalhe24;
        }

        public String getDetalhe25() {
            return detalhe25;
        }

        public void setDetalhe25(String detalhe25) {
            this.detalhe25 = detalhe25;
        }

        public String getDetalhe26() {
            return detalhe26;
        }

        public void setDetalhe26(String detalhe26) {
            this.detalhe26 = detalhe26;
        }

        public String getDetalhe27() {
            return detalhe27;
        }

        public void setDetalhe27(String detalhe27) {
            this.detalhe27 = detalhe27;
        }

        public String getDetalhe28() {
            return detalhe28;
        }

        public void setDetalhe28(String detalhe28) {
            this.detalhe28 = detalhe28;
        }

        public String getDetalhe29() {
            return detalhe29;
        }

        public void setDetalhe29(String detalhe29) {
            this.detalhe29 = detalhe29;
        }

        public String getDetalhe30() {
            return detalhe30;
        }

        public void setDetalhe30(String detalhe30) {
            this.detalhe30 = detalhe30;
        }
    }

    public static class Anexos {

        public Anexos(String nome_arquivo1, String anexo) {
            this.nome_arquivo1 = nome_arquivo1;
            this.anexo = anexo;
        }

        private String nome_arquivo1;
        private String anexo;

        public String getNome_arquivo1() {
            return nome_arquivo1;
        }

        public void setNome_arquivo1(String nome_arquivo1) {
            this.nome_arquivo1 = nome_arquivo1;
        }

        public String getAnexo() {
            return anexo;
        }

        public void setAnexo(String anexo) {
            this.anexo = anexo;
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S2306 {

    private int sucesso;
    private String evtTSVAltContr_Id;

    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;

    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;

    private String ideTrabSemVinculo_cpfTrab;
    private String ideTrabSemVinculo_nisTrab;
    private String ideTrabSemVinculo_codCateg;

    private String infoTSVAlteracao_dtAlteracao;
    private String infoTSVAlteracao_natAtividade;

    private String cargoFuncao_codCargo;
    private String cargoFuncao_codFuncao;

    private String remuneracao_vrSalFx;
    private String remuneracao_undSalFixo;
    private String remuneracao_dscSalVar;

    private String infoEstagiario_natEstagio;
    private String infoEstagiario_nivEstagio;
    private String infoEstagiario_areaAtuacao;
    private String infoEstagiario_nrApol;
    private String infoEstagiario_vlrBolsa;
    private String infoEstagiario_dtPrevTerm;

    private String instEnsino_cnpjInstEnsino;
    private String instEnsino_nmRazao;
    private String instEnsino_dscLograd;
    private String instEnsino_nrLograd;
    private String instEnsino_bairro;
    private String instEnsino_cep;
    private String instEnsino_codMunic;
    private String instEnsino_uf;

    private String ageIntegracao_cnpjAgntInteg;
    private String ageIntegracao_nmRazao;
    private String ageIntegracao_dscLograd;
    private String ageIntegracao_nrLograd;
    private String ageIntegracao_bairro;
    private String ageIntegracao_cep;
    private String ageIntegracao_codMunic;
    private String ageIntegracao_uf;

    private String supervisorEstagio_cpfSupervisor;
    private String supervisorEstagio_nmSuperv;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtTSVAltContr_Id() {
        return evtTSVAltContr_Id;
    }

    public void setEvtTSVAltContr_Id(String evtTSVAltContr_Id) {
        this.evtTSVAltContr_Id = evtTSVAltContr_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeTrabSemVinculo_cpfTrab() {
        return ideTrabSemVinculo_cpfTrab;
    }

    public void setIdeTrabSemVinculo_cpfTrab(String ideTrabSemVinculo_cpfTrab) {
        this.ideTrabSemVinculo_cpfTrab = ideTrabSemVinculo_cpfTrab;
    }

    public String getIdeTrabSemVinculo_nisTrab() {
        return ideTrabSemVinculo_nisTrab;
    }

    public void setIdeTrabSemVinculo_nisTrab(String ideTrabSemVinculo_nisTrab) {
        this.ideTrabSemVinculo_nisTrab = ideTrabSemVinculo_nisTrab;
    }

    public String getIdeTrabSemVinculo_codCateg() {
        return ideTrabSemVinculo_codCateg;
    }

    public void setIdeTrabSemVinculo_codCateg(String ideTrabSemVinculo_codCateg) {
        this.ideTrabSemVinculo_codCateg = ideTrabSemVinculo_codCateg;
    }

    public String getInfoTSVAlteracao_dtAlteracao() {
        return infoTSVAlteracao_dtAlteracao;
    }

    public void setInfoTSVAlteracao_dtAlteracao(String infoTSVAlteracao_dtAlteracao) {
        this.infoTSVAlteracao_dtAlteracao = infoTSVAlteracao_dtAlteracao;
    }

    public String getInfoTSVAlteracao_natAtividade() {
        return infoTSVAlteracao_natAtividade;
    }

    public void setInfoTSVAlteracao_natAtividade(String infoTSVAlteracao_natAtividade) {
        this.infoTSVAlteracao_natAtividade = infoTSVAlteracao_natAtividade;
    }

    public String getCargoFuncao_codCargo() {
        return cargoFuncao_codCargo;
    }

    public void setCargoFuncao_codCargo(String cargoFuncao_codCargo) {
        this.cargoFuncao_codCargo = cargoFuncao_codCargo;
    }

    public String getCargoFuncao_codFuncao() {
        return cargoFuncao_codFuncao;
    }

    public void setCargoFuncao_codFuncao(String cargoFuncao_codFuncao) {
        this.cargoFuncao_codFuncao = cargoFuncao_codFuncao;
    }

    public String getRemuneracao_vrSalFx() {
        return remuneracao_vrSalFx;
    }

    public void setRemuneracao_vrSalFx(String remuneracao_vrSalFx) {
        this.remuneracao_vrSalFx = remuneracao_vrSalFx;
    }

    public String getRemuneracao_undSalFixo() {
        return remuneracao_undSalFixo;
    }

    public void setRemuneracao_undSalFixo(String remuneracao_undSalFixo) {
        this.remuneracao_undSalFixo = remuneracao_undSalFixo;
    }

    public String getRemuneracao_dscSalVar() {
        return remuneracao_dscSalVar;
    }

    public void setRemuneracao_dscSalVar(String remuneracao_dscSalVar) {
        this.remuneracao_dscSalVar = remuneracao_dscSalVar;
    }

    public String getInfoEstagiario_natEstagio() {
        return infoEstagiario_natEstagio;
    }

    public void setInfoEstagiario_natEstagio(String infoEstagiario_natEstagio) {
        this.infoEstagiario_natEstagio = infoEstagiario_natEstagio;
    }

    public String getInfoEstagiario_nivEstagio() {
        return infoEstagiario_nivEstagio;
    }

    public void setInfoEstagiario_nivEstagio(String infoEstagiario_nivEstagio) {
        this.infoEstagiario_nivEstagio = infoEstagiario_nivEstagio;
    }

    public String getInfoEstagiario_areaAtuacao() {
        return infoEstagiario_areaAtuacao;
    }

    public void setInfoEstagiario_areaAtuacao(String infoEstagiario_areaAtuacao) {
        this.infoEstagiario_areaAtuacao = infoEstagiario_areaAtuacao;
    }

    public String getInfoEstagiario_nrApol() {
        return infoEstagiario_nrApol;
    }

    public void setInfoEstagiario_nrApol(String infoEstagiario_nrApol) {
        this.infoEstagiario_nrApol = infoEstagiario_nrApol;
    }

    public String getInfoEstagiario_vlrBolsa() {
        return infoEstagiario_vlrBolsa;
    }

    public void setInfoEstagiario_vlrBolsa(String infoEstagiario_vlrBolsa) {
        this.infoEstagiario_vlrBolsa = infoEstagiario_vlrBolsa;
    }

    public String getInfoEstagiario_dtPrevTerm() {
        return infoEstagiario_dtPrevTerm;
    }

    public void setInfoEstagiario_dtPrevTerm(String infoEstagiario_dtPrevTerm) {
        this.infoEstagiario_dtPrevTerm = infoEstagiario_dtPrevTerm;
    }

    public String getInstEnsino_cnpjInstEnsino() {
        return instEnsino_cnpjInstEnsino;
    }

    public void setInstEnsino_cnpjInstEnsino(String instEnsino_cnpjInstEnsino) {
        this.instEnsino_cnpjInstEnsino = instEnsino_cnpjInstEnsino;
    }

    public String getInstEnsino_nmRazao() {
        return instEnsino_nmRazao;
    }

    public void setInstEnsino_nmRazao(String instEnsino_nmRazao) {
        this.instEnsino_nmRazao = instEnsino_nmRazao;
    }

    public String getInstEnsino_dscLograd() {
        return instEnsino_dscLograd;
    }

    public void setInstEnsino_dscLograd(String instEnsino_dscLograd) {
        this.instEnsino_dscLograd = instEnsino_dscLograd;
    }

    public String getInstEnsino_nrLograd() {
        return instEnsino_nrLograd;
    }

    public void setInstEnsino_nrLograd(String instEnsino_nrLograd) {
        this.instEnsino_nrLograd = instEnsino_nrLograd;
    }

    public String getInstEnsino_bairro() {
        return instEnsino_bairro;
    }

    public void setInstEnsino_bairro(String instEnsino_bairro) {
        this.instEnsino_bairro = instEnsino_bairro;
    }

    public String getInstEnsino_cep() {
        return instEnsino_cep;
    }

    public void setInstEnsino_cep(String instEnsino_cep) {
        this.instEnsino_cep = instEnsino_cep;
    }

    public String getInstEnsino_codMunic() {
        return instEnsino_codMunic;
    }

    public void setInstEnsino_codMunic(String instEnsino_codMunic) {
        this.instEnsino_codMunic = instEnsino_codMunic;
    }

    public String getInstEnsino_uf() {
        return instEnsino_uf;
    }

    public void setInstEnsino_uf(String instEnsino_uf) {
        this.instEnsino_uf = instEnsino_uf;
    }

    public String getAgeIntegracao_cnpjAgntInteg() {
        return ageIntegracao_cnpjAgntInteg;
    }

    public void setAgeIntegracao_cnpjAgntInteg(String ageIntegracao_cnpjAgntInteg) {
        this.ageIntegracao_cnpjAgntInteg = ageIntegracao_cnpjAgntInteg;
    }

    public String getAgeIntegracao_nmRazao() {
        return ageIntegracao_nmRazao;
    }

    public void setAgeIntegracao_nmRazao(String ageIntegracao_nmRazao) {
        this.ageIntegracao_nmRazao = ageIntegracao_nmRazao;
    }

    public String getAgeIntegracao_dscLograd() {
        return ageIntegracao_dscLograd;
    }

    public void setAgeIntegracao_dscLograd(String ageIntegracao_dscLograd) {
        this.ageIntegracao_dscLograd = ageIntegracao_dscLograd;
    }

    public String getAgeIntegracao_nrLograd() {
        return ageIntegracao_nrLograd;
    }

    public void setAgeIntegracao_nrLograd(String ageIntegracao_nrLograd) {
        this.ageIntegracao_nrLograd = ageIntegracao_nrLograd;
    }

    public String getAgeIntegracao_bairro() {
        return ageIntegracao_bairro;
    }

    public void setAgeIntegracao_bairro(String ageIntegracao_bairro) {
        this.ageIntegracao_bairro = ageIntegracao_bairro;
    }

    public String getAgeIntegracao_cep() {
        return ageIntegracao_cep;
    }

    public void setAgeIntegracao_cep(String ageIntegracao_cep) {
        this.ageIntegracao_cep = ageIntegracao_cep;
    }

    public String getAgeIntegracao_codMunic() {
        return ageIntegracao_codMunic;
    }

    public void setAgeIntegracao_codMunic(String ageIntegracao_codMunic) {
        this.ageIntegracao_codMunic = ageIntegracao_codMunic;
    }

    public String getAgeIntegracao_uf() {
        return ageIntegracao_uf;
    }

    public void setAgeIntegracao_uf(String ageIntegracao_uf) {
        this.ageIntegracao_uf = ageIntegracao_uf;
    }

    public String getSupervisorEstagio_cpfSupervisor() {
        return supervisorEstagio_cpfSupervisor;
    }

    public void setSupervisorEstagio_cpfSupervisor(String supervisorEstagio_cpfSupervisor) {
        this.supervisorEstagio_cpfSupervisor = supervisorEstagio_cpfSupervisor;
    }

    public String getSupervisorEstagio_nmSuperv() {
        return supervisorEstagio_nmSuperv;
    }

    public void setSupervisorEstagio_nmSuperv(String supervisorEstagio_nmSuperv) {
        this.supervisorEstagio_nmSuperv = supervisorEstagio_nmSuperv;
    }

}

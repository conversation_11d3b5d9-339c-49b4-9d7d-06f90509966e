/*
 */
package Controller.PortalRH;

import Dados.Persistencia;
import SasBeans.AvisoPortal;
import SasBeans.DIRFDet;
import SasBeans.Pessoa;
import SasBeans.PessoaLogin;
import SasDaos.AvisoPortalDao;
import SasDaos.DIRFDetDao;
import SasDaos.LogPortalDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PortalRHSatMobWeb {

    /**
     * Busca a próxima sequência de mensagem
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal getMaxSequencia(Persistencia persistencia) throws Exception {
        try {
            AvisoPortalDao avisoportaldao = new AvisoPortalDao();
            return avisoportaldao.getAvisoPortal(persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha ao buscar próxima sequencia> - " + e.getMessage());
        }
    }

    /**
     * Insere nova mensagem no banco
     *
     * @param sequencia
     * @param codFil
     * @param assunto
     * @param aviso
     * @param operador
     * @param persistencia
     * @throws Exception
     */
    public void insereMensagem(String sequencia, String codFil, String assunto, String aviso, String operador, Persistencia persistencia) throws Exception {
        try {
            AvisoPortalDao avisoportaldao = new AvisoPortalDao();
            avisoportaldao.insereAviso(sequencia, codFil, assunto, aviso, operador,
                    DataAtual.getDataAtual("SQL"), DataAtual.getDataAtual("HORA"), persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha inserir nova mensagem> - " + e.getMessage());
        }
    }

    /**
     * Busca os logs de geração de contracheque por matricula no período
     * consultado
     *
     * @param matricula
     * @param dataInicio
     * @param dataFinal
     * @param persistencia
     * @return
     * @throws Exception
     */
    public ArrayList<String> getLogsContraChequeUsuario(String matricula, String dataInicio, String dataFinal, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            return logportaldao.buscaChequeUsuario(matricula, dataInicio, dataFinal, persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha buscar logs contracheque por usuario> - " + e.getMessage());
        }
    }

    /**
     * Busca os logs de geração de folha de ponto por matricula no período
     * consultado
     *
     * @param matricula
     * @param dataInicio
     * @param dataFinal
     * @param persistencia
     * @return
     * @throws Exception
     */
    public ArrayList<String> getLogsFolhaPontoUsuario(String matricula, String dataInicio, String dataFinal, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            return logportaldao.buscaPontoUsuario(matricula, dataInicio, dataFinal, persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha buscar logs folha de ponto por usuario> - " + e.getMessage());
        }
    }

    /**
     * Busca o log da geração de folha de ponto do usuário
     *
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> getLogsFolhaPonto(String matricula, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            return logportaldao.buscaFolhaPonto(matricula, persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha buscar logs folha de ponto> - " + e.getMessage());
        }
    }

    /**
     * Busca o log da geração de folha de ponto do usuário
     *
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> getLogsContraCheque(String matricula, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            return logportaldao.buscaCCheque(matricula, persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha buscar logs contracheque> - " + e.getMessage());
        }
    }

    /**
     * Insere log da geração de troca de senha
     *
     * @param matricula
     * @param codFil
     * @param persistencia
     * @throws Exception
     */
    public void geraLogTrocaSenha(String matricula, String codFil, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            logportaldao.insereLog(matricula, codFil, "Alteração de senha com sucesso", persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha salvar log troca de senha> - " + e.getMessage());
        }
    }

    /**
     * Método para trocar a senha atual
     *
     * @param senha
     * @param matricula
     * @param operador
     * @param persistencia
     * @throws Exception
     */
    public void trocarSenha(String senha, String matricula, String operador, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            pessoadao.atualizaSenha(senha, matricula, operador, persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha salvar log troca de senha> - " + e.getMessage());
        }
    }

    /**
     * Busca a mensagem do portal rh
     *
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public AvisoPortal buscaMensagem(String codfil, Persistencia persistencia) throws Exception {
        try {
            AvisoPortalDao avisoportaldao = new AvisoPortalDao();
            return avisoportaldao.getAvisoPortal(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<falha ao buscar mensagem>" + e.getMessage());
        }
    }

    /**
     * Busca se há rendimentos para o funcionário
     *
     * @param matricula
     * @param persistencia
     * @return
     * @throws java.lang.Exception
     */
    public String RendimentosAno(String matricula, Persistencia persistencia) throws Exception {
        try {
            String ano = String.valueOf(LocalDate.now().getYear());
            DIRFDetDao dirf = new DIRFDetDao();
            List<DIRFDet> ldirf = dirf.BuscaDirf(String.valueOf(LocalDate.now().getYear() - 1), matricula, persistencia);
            if (ldirf.isEmpty()) {
                return null;
            }
            return ano;
        } catch (Exception ex) {
            throw new Exception("portalrh.falhageral<falha ao buscar rendimentos do ano>" + ex.getMessage());
        }
    }

    public void atualizaAcesso(String codigo, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            pessoadao.ultAcPortal(codigo, persistencia);
        } catch (Exception ex) {
            throw new Exception("portalrh.falhageral<falha ao atualizar data do último acesso>" + ex.getMessage());
        }
    }

    /**
     * Busca informações do funcionário para primeiro acesso
     *
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Pessoa PrimeiroAcesso(String matricula, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            return pessoadao.primeiroAcessoRH(matricula, persistencia);
        } catch (Exception ex) {
            throw new Exception("portalrh.falhageral<falha buscar usuario>" + ex.getMessage());
        }
    }

    /**
     * @param pessoa - objeto pessoa Dados necessário - nome, email, cpf, rg
     * @param persistenciaLocal - persistencia local
     * @param persistenciaSat - persistencia base central
     * @throws Exception
     */
    public void CadastroRH(Pessoa pessoa, Persistencia persistenciaLocal, Persistencia persistenciaSat) throws Exception {
        try {
            // buscar pessoa base satellite
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            PessoaDao pessoadao = new PessoaDao();
            Pessoa p = pessoadao.buscarPessoaCPF(pessoa.getCPF(), persistenciaSat);
            if (null == p.getNome()) {
                p.setCodigo(pessoadao.getCodigo(persistenciaSat));
                p.setNome(pessoa.getNome());
                p.setCPF(pessoa.getCPF());
                p.setRG(pessoa.getRG());
                p.setMatr(pessoa.getMatr());
                p.setCidade(pessoa.getCidade());
                p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                p.setHr_Alter(DataAtual.getDataAtual("HORA"));
                pessoadao.InserirPessoaSatMobWeb(p, persistenciaSat);
            }
            pessoa.setCodPessoaWEB(p.getCodigo());
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            // atualiza pwportal e codpessoaweb
            pessoadao.AtualizaPessoaSatMobRH(pessoa, persistenciaLocal);
            PessoaLogin pl = new PessoaLogin();
            pl.setBancoDados(persistenciaLocal.getEmpresa());
            pl.setCodigo(pessoa.getCodPessoaWEB());
            pl.setCodPessoaBD(pessoa.getCodigo());
            pl.setNivel("4");
            PessoaLoginDao pldao = new PessoaLoginDao();
            pldao.gravaPessoaLogin(pl, persistenciaSat);
        } catch (Exception e) {
            throw new Exception("portalrh.falhageral<message>" + e.getMessage());
        }
    }
}

package SasBeans;

import java.math.BigDecimal;
import java.util.List;

public class GraficoBean {

    private String Descricao;
    private String Dt_Ini;
    private String Dt_Fim;
    private BigDecimal Suspensao;
    private BigDecimal Faltas;
    private BigDecimal CodFil;
    private BigDecimal HE50;
    private BigDecimal HE100;
    private BigDecimal AtMedico;

    List<Filiais> listaFiliais;
    List<Rh_Ctrl> listaCtrl;

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getDt_Ini() {
        return Dt_Ini;
    }

    public void setDt_Ini(String Dt_Ini) {
        this.Dt_Ini = Dt_Ini;
    }

    public String getDt_Fim() {
        return Dt_Fim;
    }

    public void setDt_Fim(String Dt_Fim) {
        this.Dt_Fim = Dt_Fim;
    }

    public BigDecimal getFaltas() {
        return Faltas;
    }

    public void setFaltas(BigDecimal Faltas) {
        this.Faltas = Faltas;
    }

    public BigDecimal getSuspensao() {
        return Suspensao;
    }

    public void setSuspensao(BigDecimal Suspensao) {
        this.Suspensao = Suspensao;
    }

    public BigDecimal getAtMedico() {
        return AtMedico;
    }

    public void setAtMedico(BigDecimal AtMedico) {
        this.AtMedico = AtMedico;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public List<Filiais> getListaFiliais() {
        return listaFiliais;
    }

    public void setListaFiliais(List<Filiais> listaFiliais) {
        this.listaFiliais = listaFiliais;
    }

    public List<Rh_Ctrl> getListaCtrl() {
        return listaCtrl;
    }

    public void setListaCtrl(List<Rh_Ctrl> listaCtrl) {
        this.listaCtrl = listaCtrl;
    }

    public BigDecimal getHE50() {
        return HE50;
    }

    public void setHE50(BigDecimal HE50) {
        this.HE50 = HE50;
    }

    public BigDecimal getHE100() {
        return HE100;
    }

    public void setHE100(BigDecimal HE100) {
        this.HE100 = HE100;
    }

}

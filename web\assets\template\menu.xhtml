<?xml version="1.0" encoding="UTF-8"?>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://xmlns.jcp.org/jsf/core">
    <h:form id="formMenu">
        <ul class="sidebar-menu tree" data-widget="tree">
            <ui:fragment rendered="true">
                <li class="treeview" style="display: #{valores.codServico ne '402'?'':'none !important'}">
                    <a href="#">
                        <i class="fas fa-route"></i>
                        <span>#{localemsgs.Operacoes}</span>
                        <span class="pull-right-container">
                            <i class="fa fa-angle-left pull-right"></i>
                        </span>
                    </a>
                    <ul class="treeview-menu">
                        <ui:fragment rendered="true">
                            <li>
                                <p:link outcome="/containers/containers.xhtml">
                                    <i class="fas fa-map-marked-alt"></i>
                                    <span>#{localemsgs.Containers}</span>
                                </p:link>
                            </li>
                        </ui:fragment>
                        <ui:fragment rendered="false">
                            <li>
                                <p:link outcome="/containers/rotas.xhtml">
                                    <i class="fas fa-map-marked-alt"></i>
                                    <span>#{localemsgs.Rotas}</span>
                                </p:link>
                            </li>
                        </ui:fragment>
                        <ui:fragment rendered="true">
                            <li>
                                <p:link outcome="/containers/pedidos.xhtml">
                                    <i class="fas fa-receipt"></i>
                                    <span>#{localemsgs.Pedidos}</span>
                                </p:link>
                            </li>
                        </ui:fragment>
                    </ul>
                </li>
            </ui:fragment>
            <ui:fragment rendered="#{login.empresa.nivel gt 3}">
                <li>
                    <p:link outcome="/containers/clientes.xhtml">
                        <i class="fas fa-address-card"></i>
                        <span>#{localemsgs.Clientes}</span>
                    </p:link>
                </li>
            </ui:fragment>

            <ui:fragment rendered="#{login.empresa.nivel gt 3}">

                <li class="treeview">
                    <a href="#">
                        <i class="fas fa-cog"></i>
                        <span>#{localemsgs.Configuracoes}</span>
                        <span class="pull-right-container">
                            <i class="fa fa-angle-left pull-right"></i>
                        </span>
                    </a>
                    <ul id="usuarios" class="treeview-menu">
                        <li class="treeview">
                            <a href="#">
                                <i class="fas fa-language"></i>
                                <span>#{localemsgs.Idioma}</span>
                                <span class="pull-right-container">
                                    <i class="fa fa-angle-left pull-right"></i>
                                </span>
                            </a>
                            <ul class="treeview-menu">
                                <li>
                                    <p:commandLink ajax="false"
                                                   action="#{localeController.setNumberAndUpdate(1)}" update="msgs">
                                        <i class="iconeHome flag-icon flag-icon-br"></i>
                                        <span>#{localemsgs.Portugues}</span>
                                    </p:commandLink>
                                </li>                               
                                <li>
                                    <p:commandLink ajax="false"
                                                   action="#{localeController.setNumberAndUpdate(2)}" update="msgs">
                                        <i class="iconeHome flag-icon flag-icon-es"></i>
                                        <span>#{localemsgs.Espanhol}</span>
                                    </p:commandLink>
                                </li>
                                <li>
                                    <p:commandLink ajax="false"
                                                   action="#{localeController.setNumberAndUpdate(3)}" update="msgs">
                                        <i class="iconeHome flag-icon flag-icon-us"></i>
                                        <span>#{localemsgs.Ingles}</span>
                                    </p:commandLink>
                                </li>                                                        
                            </ul>
                        </li>
                        <li>
                            <p:link outcome="/configuracoes/usuarios.xhtml">
                                <i class="far fa-address-card"></i>
                                <span>#{localemsgs.Usuarios}</span>
                            </p:link>
                        </li>
                    </ul>
                </li>
            </ui:fragment>
            <li style="text-align: center;">
                <img src="#{login.getLogo(login.pp.empresa)}" style="max-width: 225px"/>
            </li>
        </ul>
    </h:form>
</ui:composition>
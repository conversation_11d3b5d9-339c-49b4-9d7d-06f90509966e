/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class RHAvisoPrevio {

    private BigDecimal numero;
    private LocalDate data;
    private BigDecimal matr;
    private String iniciativa;
    private String tipo;
    private LocalDate dtIncio;
    private LocalDate dtFim;
    private String mensagem;
    private String obs;
    private BigDecimal codModelo;
    private String operador;
    private String dt_alter;
    private String hr_alter;

    public RHAvisoPrevio() {
        this.numero = new BigDecimal("0");
        this.data = null;
        this.matr = new BigDecimal("0");
        this.iniciativa = "";
        this.tipo = "";
        this.dtIncio = null;
        this.dtFim = null;
        this.mensagem = "";
        this.obs = "";
        this.codModelo = new BigDecimal("0");
        this.operador = "";
        this.dt_alter = "";
        this.hr_alter = "";
    }

    public BigDecimal getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        try {
            this.numero = new BigDecimal(numero);
        } catch (Exception e) {
            this.numero = new BigDecimal("0");
        }
    }

    public LocalDate getData() {
        return data;
    }

    public void setData(LocalDate data) {
        this.data = data;
    }

    public BigDecimal getMatr() {
        return matr;
    }

    public void setMatr(String matr) {
        try {
            this.matr = new BigDecimal(matr);
        } catch (Exception e) {
            this.matr = new BigDecimal("0");
        }
    }

    public String getIniciativa() {
        return iniciativa;
    }

    public void setIniciativa(String iniciativa) {
        this.iniciativa = iniciativa;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public LocalDate getDtIncio() {
        return dtIncio;
    }

    public void setDtIncio(LocalDate dtIncio) {
        this.dtIncio = dtIncio;
    }

    public LocalDate getDtFim() {
        return dtFim;
    }

    public void setDtFim(LocalDate dtFim) {
        this.dtFim = dtFim;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public BigDecimal getCodModelo() {
        return codModelo;
    }

    public void setCodModelo(String codModelo) {
        try {
            this.codModelo = new BigDecimal(codModelo);
        } catch (Exception e) {
            this.codModelo = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDt_alter() {
        return dt_alter;
    }

    public void setDt_alter(String dt_alter) {
        this.dt_alter = dt_alter;
    }

    public String getHr_alter() {
        return hr_alter;
    }

    public void setHr_alter(String hr_alter) {
        this.hr_alter = hr_alter;
    }

}

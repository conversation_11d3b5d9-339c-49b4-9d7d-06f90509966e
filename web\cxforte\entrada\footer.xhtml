<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <footer class="fixed-footer">
        <div id="footer-body" class="footer-body footer-body-2">
            <div class="footer-buttons" style="flex-shrink: 0;">
                <h:form>
                    <p:commandLink
                        title="#{localemsgs.Imprimir}"
                        action="#{cxForteEntrada.imprimirEntradas()}"
                        update="msgs">
                        <p:graphicImage
                            url="../assets/img/icone_redondo_impressao.png"
                            width="40" height="40" />
                    </p:commandLink>

                    <p:commandLink
                        title="#{localemsgs.GuiasMobile}"
                        action="#{cxForteEntrada.verGuiasMobile()}"
                        update="msgs">
                        <p:graphicImage
                            url="../assets/img/icone_satmob_guias_40x40.png"
                            width="40" height="40" />
                    </p:commandLink>

                    <p:commandLink
                        rendered="false"
                        title="#{localemsgs.CapturaGTV}"
                        action="#{cardapio.salvarCadastroCardapio()}"
                        update="msgs">
                        <p:graphicImage
                            url="../assets/img/icone_satmob_funcionarios.png"
                            width="40" height="40" />
                    </p:commandLink>

                    <p:selectBooleanCheckbox
                        rendered="false"
                        itemLabel="#{localemsgs.CapturaAuto}"
                        value="#{cxForteEntrada.capturaAuto}">
                        <p:ajax listener="#{cxForteEntrada.onCapturaAutomatica()}"
                                update="msgs"/>
                    </p:selectBooleanCheckbox>
                </h:form>
            </div>

            <div id="divFooterTimer" style="flex-shrink: 0; min-height:10px !important;max-height:40px !important;">
                <table class="footer-time" style="min-height:10px !important">
                    <tr>
                        <td>
                            <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                        </td>
                    </tr>
                </table>
            </div>

            <div style="flex-shrink: 2; min-height:10px !important;max-height:40px !important;">
                <table class="footer-user" style="min-height:10px !important">
                    <tr>
                        <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                        <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                    </tr>
                </table>
            </div>

            <div style="flex-shrink: 1; height:40px !important;">
                <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                    <tr>
                        <td><img src="../assets/img/logo_satweb.png" /></td>
                        <td>
                            <h:form>
                                <h:commandLink actionListener="#{localeController.increment}"
                                               action="#{localeController.getLocales}" >
                                    <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                </h:commandLink>
                            </h:form>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </footer>

</ui:composition>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.operacoes;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Controller.Login.LoginSatMobWeb;
import Controller.PstServ.PstServSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.EmailsEnviar;
import SasBeans.Rotas;
import SasBeans.Rt_Guias;
import SasBeans.Rt_Hist;
import SasBeans.Rt_Perc;
import SasBeans.SasPWFill;
import SasBeansCompostas.TmktDetPstPstServClientes;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.LocaleController;
import br.com.sasw.utils.Messages;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.map.DefaultMapModel;
import org.primefaces.model.map.LatLng;
import org.primefaces.model.map.Marker;

/**
 *
 * <AUTHOR>
 */
@Named(value = "trajetosMB")
@ViewScoped
public class TrajetosMB implements Serializable {

    private List<Rt_Perc> listaTrajetos;
    private Rt_Perc trajetoSelecionado, novoTrajeto;
    private final RotasSatWeb rotasSatWeb;
    private SupervisoesMB supervisoesMB;

    private BigDecimal codPessoa;
    private String codfil, banco, dataTela, operador, caminho, log, ERExtenso, tipoSrvExtenso;
    private Rotas rota;
    private ArquivoLog logerro;
    private SasPWFill filialDescRota;
    private List<String> sequencias;

    private Clientes clientes, cliOri, cliDst;
    private List<Clientes> listaClientes;
    private List<TmktDetPstPstServClientes> tmktDetPstPstServClientes;
    private TmktDetPstPstServClientes tmktDetPstPstServClienteSelecionado;
    private final PstServSatMobWeb pstservsatmobweb;
    private final ClientesSatMobWeb clientesSatMobWeb;
    private final LoginSatMobWeb loginsatmobweb;
    private Calendar hora1, hora2, hora3, hora4;

    private List<Rt_Hist> historico;
    private Rt_Hist historicoSelecionado, novoHistorico;
    private List<Rt_Guias> guias;
    private Rt_Guias guiaSelecionada;

    private boolean exclFlag;
    private int flag;

    private Persistencia persistencia, satellite;
    private EmailsEnviar email;
    private List<Clientes> clientesManifest;
    private Clientes clienteManifest;

    public TrajetosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        rotasSatWeb = new RotasSatWeb();
        pstservsatmobweb = new PstServSatMobWeb();
        clientesSatMobWeb = new ClientesSatMobWeb();
        filialDescRota = new SasPWFill();
        listaClientes = new ArrayList<>();
        historico = new ArrayList<>();
        novoTrajeto = new Rt_Perc();
        novoTrajeto.setTipoSrv("S");
        hora1 = Calendar.getInstance();
        hora2 = Calendar.getInstance();
        hora3 = Calendar.getInstance();
        hora4 = Calendar.getInstance();
        exclFlag = false;
        log = new String();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
    }

    public void Persistencia(Persistencia pp, Persistencia ss) {
        try {
            this.persistencia = pp;
            this.satellite = ss;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.supervisoesMB = new SupervisoesMB(this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public List<Clientes> ListarClientes(String query) {
        this.listaClientes = new ArrayList<>();
        try {
            List<Clientes> retorno = this.pstservsatmobweb.ListarClientes(this.filialDescRota.getCodfilAc(), query, this.persistencia);
            for (Clientes c : retorno) {
                if (c.getNRed().toUpperCase().contains(query.toUpperCase())
                        || c.getNome().toUpperCase().contains(query.toUpperCase())
                        || c.getCPF().toUpperCase().contains(query.toUpperCase())
                        || c.getCGC().toUpperCase().contains(query.toUpperCase())) {
                    c.setNome(c.getNRed() + " - " + c.getNome());
                    this.listaClientes.add(c);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.listaClientes;
    }

    /**
     * Cliente que foi selecionado para o trajeto
     *
     * @param event
     */
    public void SelecionarCliente(SelectEvent event) {
        try {
            Clientes c = ((Clientes) event.getObject());
            this.novoTrajeto.setCodCli1(c.getCodigo());
            this.clientes = this.clientesSatMobWeb.ListaClientes(this.novoTrajeto.getCodFil().toString(), c.getCodigo(), "", "", "", this.persistencia).get(0);
            this.novoTrajeto.setNRed(this.clientes.getNRed());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Cliente de Origem que foi selecionado para o trajeto
     *
     * @param event
     */
    public void selecionarCliOri(SelectEvent event) {
        try {
            Clientes c = ((Clientes) event.getObject());
            this.novoTrajeto.setCodCli1(c.getCodigo());
            this.cliOri = this.clientesSatMobWeb.ListaClientes(this.novoTrajeto.getCodFil().toString(), c.getCodigo(), "", "", "", this.persistencia).get(0);
            this.novoTrajeto.setNRed(this.clientes.getNRed());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Cliente de Origem que foi selecionado para o trajeto
     *
     * @param event
     */
    public void selecionarCliDst(SelectEvent event) {
        try {
            Clientes c = ((Clientes) event.getObject());
            this.novoTrajeto.setCodCli2(c.getCodigo());
            this.cliDst = this.clientesSatMobWeb.ListaClientes(this.novoTrajeto.getCodFil().toString(), c.getCodigo(), "", "", "", this.persistencia).get(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Listar() {
        try {
            this.listaTrajetos = this.rotasSatWeb.listarTrajetos(this.rota.getSequencia(), this.exclFlag, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Cadastrar() {
        try {
            VerificarHoraTrajeto();

            this.novoTrajeto.setSequencia(this.rota.getSequencia().toPlainString());
            this.novoTrajeto.setER("E");
            this.novoTrajeto.setTipoSrv("S");

            this.novoTrajeto.setOperIncl(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.novoTrajeto.setDt_Incl(LocalDate.now());
            this.novoTrajeto.setHr_Incl(DataAtual.getDataAtual("HORA"));

            this.novoTrajeto.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            this.novoTrajeto.setDt_Alter(LocalDate.now());
            this.novoTrajeto.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novoTrajeto.setFlag_Excl("");

            this.novoTrajeto.setHora1(this.novoTrajeto.getHora1().substring(0, 2) + this.novoTrajeto.getHora1().substring(3, 5));
            this.rotasSatWeb.criarTrajetos(this.novoTrajeto, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);

            NewTrajeto(this.rota);
            PrimeFaces.current().ajax().update("cadastroTrajeto:cadastrarTrajeto");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        Listar();
    }

    public void VerificarHoraTrajeto() throws Exception {
//        Escala escala = this.rotasSatWeb.SelecionaEscala(this.rota.getData(), this.rota.getSequencia(), this.persistencia);
//        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
//        sdf.setLenient(true);
//
//        Calendar hora = Calendar.getInstance();
//        hora.setTime(sdf.parse(this.novoTrajeto.getHora1()));
//        hora.set(this.rota.getData().getYear(), this.rota.getData().getMonthValue(), this.rota.getData().getDayOfMonth(),
//                        hora.get(Calendar.HOUR_OF_DAY), hora.get(Calendar.MINUTE));
//        this.hora1.setTime(sdf.parse(escala.getHora1()));
//        this.hora1.set(this.rota.getData().getYear(), this.rota.getData().getMonthValue(), this.rota.getData().getDayOfMonth(),
//                        this.hora1.get(Calendar.HOUR_OF_DAY), this.hora1.get(Calendar.MINUTE));
//        this.hora2.setTime(sdf.parse(escala.getHora2()));
//        this.hora2.set(this.rota.getData().getYear(), this.rota.getData().getMonthValue(), this.rota.getData().getDayOfMonth(),
//                        this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
//        this.hora3.setTime(sdf.parse(escala.getHora3()));
//        this.hora3.set(this.rota.getData().getYear(), this.rota.getData().getMonthValue(), this.rota.getData().getDayOfMonth(),
//                        this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
//        this.hora4.setTime(sdf.parse(escala.getHora4()));
//        this.hora4.set(this.rota.getData().getYear(), this.rota.getData().getMonthValue(), this.rota.getData().getDayOfMonth(),
//                        this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
//
//        // FUNCIONA SIM (:
//        for(Rt_Perc trajeto : this.listaTrajetos){
//            if(trajeto.getHora1().equals(hora.get(Calendar.HOUR)+""+hora.get(Calendar.MINUTE)) ||
//                    trajeto.getHora1().equals(hora.get(Calendar.HOUR)+"0"+hora.get(Calendar.MINUTE))) throw new Exception("ExisteServico");
//        }
//        
//        // ISSO AQUI QUE NÃO FUNCIONA, E FICA GERANDO EXCEÇÃO.
////        for(Rt_Perc perc : this.listaTrajetos){
////            if(perc.getHora1().equals(this.novoTrajeto.getHora1().substring(0, 2)+this.novoTrajeto.getHora1().substring(3, 5)))
////                throw new Exception("ExisteServico");
////        }
//        
//        if(this.hora4.before(this.hora1)){
//                LocalDate dia4 = this.rota.getData().plusDays(1);
//                this.hora4.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
//                    this.hora4.get(Calendar.HOUR_OF_DAY), this.hora4.get(Calendar.MINUTE));
//        }
//        if(this.hora3.before(this.hora1)){
//                LocalDate dia3 = this.rota.getData().plusDays(1);
//                this.hora3.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
//                    this.hora3.get(Calendar.HOUR_OF_DAY), this.hora3.get(Calendar.MINUTE));
//        }
//        if(this.hora2.before(this.hora1)){
//                LocalDate dia2 = this.rota.getData().plusDays(1);
//                this.hora2.set(dia2.getYear(), dia2.getMonthValue(), dia2.getDayOfMonth(),
//                    this.hora2.get(Calendar.HOUR_OF_DAY), this.hora2.get(Calendar.MINUTE));
//        }
//
//        if (!escala.getHora2().equals(escala.getHora3())) {
//            if((hora.before(this.hora1) || hora.after(this.hora2)) && (hora.before(this.hora3) || hora.after(this.hora4))) {
//                throw new Exception("VerifiqueHorario");
//            }
//        }
    }

    public void CalculoHoraTrajeto() {
        try {
            VerificarHoraTrajeto();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Ação do botão de edição: erro quando nenhum posto de seviço está
     * selecionado. Inicializa as variáveis necessárias para exibir detalhes do
     * posto e abre a tela de edição.
     *
     * @param rota
     */
    public void buttonAction(Rotas rota) {
        if (null == this.trajetoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneTrajeto"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.rota = rota;
//                this.rota.setDataS(this.rota.getData().toString());
                this.novoTrajeto = this.trajetoSelecionado;
                this.novoTrajeto.setCodFil(this.rota.getCodFil().toPlainString());
                this.clientes = this.clientesSatMobWeb.ListaClientes(this.novoTrajeto.getCodFil().toString(), this.novoTrajeto.getCodCli1(), "", "", "", this.persistencia).get(0);
                this.flag = 2;
                this.filialDescRota = this.loginsatmobweb.BuscaFilial(this.novoTrajeto.getCodFil().toPlainString(), this.codPessoa, this.persistencia);
//                this.supervisoesMB.ListarSupervisaoCliente(this.novoTrajeto.getCodFil(),  this.novoTrajeto.getCodCli1(), this.rota.getDataS(), this.persistencia);
                this.sequencias = new ArrayList<>();
                for (TmktDetPstPstServClientes sup : this.supervisoesMB.getListaSupervisao()) {
                    this.sequencias.add(sup.getTmktdetpst().getSequencia().toPlainString());
                }
                Listar();
                PrimeFaces.current().executeScript("PF('dlgCadastrarTrajetos').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void selecionarServico(Rt_Perc trajeto) {
        this.trajetoSelecionado = trajeto;
    }

    public void abrirServicoValores(Rotas rota) {
        if (null == this.trajetoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneTrajeto"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.rota = rota;
//                this.rota.setDataS(this.rota.getData().toString());
                this.novoTrajeto = this.trajetoSelecionado;
                this.novoTrajeto.setCodFil(this.rota.getCodFil().toPlainString());
                this.cliOri = this.clientesSatMobWeb.ListaClientes(this.novoTrajeto.getCodFil().toString(), this.novoTrajeto.getCodCli1(), "", "", "", this.persistencia).get(0);
                this.cliDst = this.clientesSatMobWeb.ListaClientes(this.novoTrajeto.getCodFil().toString(), this.novoTrajeto.getCodCli2(), "", "", "", this.persistencia).get(0);
                this.flag = 2;
                this.filialDescRota = this.loginsatmobweb.BuscaFilial(this.novoTrajeto.getCodFil().toPlainString(), this.codPessoa, this.persistencia);

                this.historico = this.rotasSatWeb.listarHistorico(this.novoTrajeto.getSequencia(), this.novoTrajeto.getParada(), this.persistencia);
                this.guias = this.rotasSatWeb.listarGuias(this.novoTrajeto.getSequencia(), this.novoTrajeto.getParada(), this.persistencia);

                PrimeFaces.current().ajax().update("cadastroTrajeto");
                PrimeFaces.current().executeScript("PF('dlgCadastrarTrajetos').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void prepararManifest() {
        try {
            this.clientesManifest = this.rotasSatWeb.clientesFaturar(this.trajetoSelecionado.getCodFil().toPlainString(),
                    this.trajetoSelecionado.getSequencia().toPlainString(), this.trajetoSelecionado.getParada(), this.persistencia);
            this.clienteManifest = this.clientesManifest.get(0);
            if (this.clientesManifest.size() == 1) {
                gerarManifest();
            } else {
                PrimeFaces.current().ajax().update("formClientesManifest");
                PrimeFaces.current().executeScript("PF('dlgClientesManifest').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void gerarManifest() {
        try {

            this.email = new EmailsEnviar();
            // Preparação do arquivo html
            File file = new File(this.getClass().getResource("guias_entrega.html").getPath().replace("%20", " "));
            FileInputStream fis = new FileInputStream(file);
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            fis.close();
            String html = new String(data, "UTF-8");
            this.email = this.rotasSatWeb.geraManifest(this.trajetoSelecionado.getCodFil().toPlainString(),
                    this.trajetoSelecionado.getSequencia().toPlainString(), this.trajetoSelecionado.getParada(), DataAtual.getDataAtual("SQL"),
                    DataAtual.getDataAtual("HORA"), html, LocaleController.getsCurrentLocale().getLanguage(), this.clienteManifest.getCodigo(),
                    this.persistencia);
            PrimeFaces.current().ajax().update("formEmail");
            PrimeFaces.current().executeScript("PF('dlgEmail').show();");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void enviarManifest() {
        try {
            this.rotasSatWeb.enviarEmailManifest(this.email, this.satellite);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("EmailFilaEnvio"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void NewTrajeto(Rotas rota) {
        try {
            this.rota = rota;
//            this.rota.setDataS(this.rota.getData().toString());
            this.filialDescRota = this.loginsatmobweb.BuscaFilial(this.rota.getCodFil().toPlainString(), this.codPessoa, this.persistencia);
            this.trajetoSelecionado = new Rt_Perc();
            this.novoTrajeto = new Rt_Perc();
            Listar();
            if (this.listaTrajetos.isEmpty()) {
                this.novoTrajeto.setHora1(this.rota.getHrLargada());
            } else {
                this.novoTrajeto.setHora1("00:00");
            }
            this.novoTrajeto.setER("E");
            this.novoTrajeto.setTipoSrv("S");
            this.novoTrajeto.setCodFil(this.filialDescRota.getCodfilAc());
            this.clientes = new Clientes();
            this.flag = 1;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Editar() {
        try {
            VerificarHoraTrajeto();

            this.novoTrajeto.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novoTrajeto.setDt_Alter(LocalDate.now());
            this.novoTrajeto.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novoTrajeto.setHora1(this.novoTrajeto.getHora1().substring(0, 2) + this.novoTrajeto.getHora1().substring(3, 5));

            this.rotasSatWeb.editarTrajeto(this.novoTrajeto, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            PrimeFaces.current().executeScript("PF('dlgCadastrarTrajetos').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        Listar();
    }

    public void Excluir() {
        try {
            if (null == this.trajetoSelecionado) {
                throw new Exception("SelecioneTrajeto");
            }
            if (this.trajetoSelecionado.getFlag_Excl().equals("*")) {
                throw new Exception("trajetoExcluido");
            }
            this.novoTrajeto = this.trajetoSelecionado;
            this.novoTrajeto.setOperExcl(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novoTrajeto.setHr_Excl(DataAtual.getDataAtual("HORA"));
            this.novoTrajeto.setDt_Excl(LocalDate.now());
            this.rotasSatWeb.FlagExcl(this.novoTrajeto, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Seleciona o TmktDetPstPstServClientes com duplo clique
     *
     * @param event
     */
    public void doubleSelect(SelectEvent event) {
        this.supervisoesMB.setSupervisaoSelecionado((TmktDetPstPstServClientes) event.getObject());
        SelecaoTmktDetPstPstServClientes();
    }

    public void SelecaoTmktDetPstPstServClientes() {
        if (this.supervisoesMB.getSupervisaoSelecionado() == null) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneSupervisao"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.clientes = clientesSatMobWeb.ListaClientes(this.novoTrajeto.getCodFil().toString(), this.novoTrajeto.getCodCli1(), "", "", "", this.persistencia).get(0);
                this.supervisoesMB.setCliente(this.clientes);

                AtualizarSupervisao();

                PrimeFaces.current().ajax().update("formEditar");
                PrimeFaces.current().executeScript("PF('dlgListarSupervisoes').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }

    }

    /**
     * Busca a supervisão seguinte com base em uma lista de sequencias
     */
    public void ProximaSupervisao() {
        try {
            TmktDetPstPstServClientes supervisao = new TmktDetPstPstServClientes();
            int so = this.supervisoesMB.getListaSupervisao().indexOf(this.supervisoesMB.getSupervisaoSelecionado());
            this.supervisoesMB.setSupervisaoSelecionado(this.supervisoesMB.getListaSupervisao().get(so + 1));
            AtualizarSupervisao();
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemSupervisaoRecente"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    /**
     * Busca a supervisão anterior com base em uma lista de sequencias
     */
    public void SupervisaoAnterior() {
        try {
            TmktDetPstPstServClientes supervisao = new TmktDetPstPstServClientes();
            int so = this.supervisoesMB.getListaSupervisao().indexOf(this.supervisoesMB.getSupervisaoSelecionado());
            this.supervisoesMB.setSupervisaoSelecionado(this.supervisoesMB.getListaSupervisao().get(so - 1));
            AtualizarSupervisao();
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemSupervisaoAntiga"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    /**
     * Atualiza a managed bean SuperviaoMB para atualizar os valores da nova
     * supervisão a ser exibida
     */
    public void AtualizarSupervisao() {
        try {
            this.supervisoesMB.setFilial(this.loginsatmobweb.BuscaFilial(this.supervisoesMB.getSupervisaoSelecionado().getTmktdetpst().getCodFil().toPlainString(), this.codPessoa, this.persistencia));
            Double cliLat = 0.0, cliLon = 0.0, pstLat = 0.0, pstLon = 0.0;
            try {
                cliLat = Double.parseDouble(this.supervisoesMB.getCliente().getLatitude());
                cliLon = Double.parseDouble(this.supervisoesMB.getCliente().getLongitude());
            } catch (Exception e) {
            }
            try {
                pstLat = Double.valueOf(this.supervisoesMB.getSupervisaoSelecionado().getTmktdetpst().getLatitude());
                pstLon = Double.valueOf(this.supervisoesMB.getSupervisaoSelecionado().getTmktdetpst().getLongitude());
            } catch (Exception e) {
            }
            if (cliLat != 0.0 || cliLon != 0.0) {
                this.supervisoesMB.setPin(new DefaultMapModel());
                this.supervisoesMB.setMarker(new Marker(new LatLng(cliLat, cliLon),
                        this.supervisoesMB.getSupervisaoSelecionado().getPstserv().getLocal()));
                this.supervisoesMB.getMarker().setFlat(true);
                this.supervisoesMB.getMarker().setTitle(this.supervisoesMB.getSupervisaoSelecionado().getPstserv().getLocal());
                this.supervisoesMB.getMarker().setIcon("https://mobile.sasw.com.br:9091/satmobile/img/novo_iconedourado_M.png");
                this.supervisoesMB.getPin().addOverlay(this.supervisoesMB.getMarker());
            }
            if (pstLat != 0.0 || pstLon != 0.0) {
                this.supervisoesMB.setMarker(new Marker(new LatLng(pstLat, pstLon),
                        this.supervisoesMB.getSupervisaoSelecionado().getTmktdetpst().getOperador()));
                this.supervisoesMB.getMarker().setFlat(true);
                this.supervisoesMB.getMarker().setTitle(this.supervisoesMB.getSupervisaoSelecionado().getTmktdetpst().getOperador());
                this.supervisoesMB.getMarker().setIcon("https://mobile.sasw.com.br:9091/satmobile/img/PIN_chefe_equipe_googlemaps.png");
                this.supervisoesMB.getPin().addOverlay(this.supervisoesMB.getMarker());
            }
            try {
                if ((cliLat == 0.0 || cliLon == 0.0) && pstLat != 0.0 && pstLon != 0.0) {
                    cliLat = pstLat;
                    cliLon = pstLon;
                } else if ((pstLat == 0.0 || pstLon == 0.0) && cliLat != 0.0 && cliLon != 0.0) {
                    pstLat = cliLat;
                    pstLon = cliLon;
                } else if (cliLat == 0.0 && cliLon == 0.0 && pstLat == 0.0 && pstLon == 0.0) {
                    throw new Exception();
                }
                this.supervisoesMB.setCoordenadas("" + (cliLat + pstLat) / 2 + ", " + (cliLon + pstLon) / 2);
            } catch (Exception e) {
            }
            this.supervisoesMB.CalculaDistPstSup();
            this.supervisoesMB.setPosFotoFuncion(0);
            this.supervisoesMB.setFotoFuncion(null);
            this.supervisoesMB.setPosFotoPosto(0);
            if (!this.supervisoesMB.getSupervisaoSelecionado().getFotos().isEmpty()) {
                this.supervisoesMB.setFotoPosto(this.supervisoesMB.getSupervisaoSelecionado().getFotos().get(0));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        this.supervisoesMB.ListarQuestionarios();
    }

    public List<Rt_Perc> getListaTrajetos() {
        return listaTrajetos;
    }

    public void setListaTrajetos(List<Rt_Perc> listaTrajetos) {
        this.listaTrajetos = listaTrajetos;
    }

    public Rt_Perc getTrajetoSelecionado() {
        return trajetoSelecionado;
    }

    public void setTrajetoSelecionado(Rt_Perc trajetoSelecionado) {
        this.trajetoSelecionado = trajetoSelecionado;
    }

    public Rt_Perc getNovoTrajeto() {
        return novoTrajeto;
    }

    public void setNovoTrajeto(Rt_Perc novoTrajeto) {
        this.novoTrajeto = novoTrajeto;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Rotas getRota() {
        return rota;
    }

    public void setRota(Rotas rota) {
        this.rota = rota;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    public List<Clientes> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<Clientes> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public List<TmktDetPstPstServClientes> getTmktDetPstPstServClientes() {
        return tmktDetPstPstServClientes;
    }

    public void setTmktDetPstPstServClientes(List<TmktDetPstPstServClientes> tmktDetPstPstServClientes) {
        this.tmktDetPstPstServClientes = tmktDetPstPstServClientes;
    }

    public SupervisoesMB getSupervisoesMB() {
        return supervisoesMB;
    }

    public void setSupervisoesMB(SupervisoesMB supervisoesMB) {
        this.supervisoesMB = supervisoesMB;
    }

    public TmktDetPstPstServClientes getTmktDetPstPstServClienteSelecionado() {
        return tmktDetPstPstServClienteSelecionado;
    }

    public void setTmktDetPstPstServClienteSelecionado(TmktDetPstPstServClientes tmktDetPstPstServClienteSelecionado) {
        this.tmktDetPstPstServClienteSelecionado = tmktDetPstPstServClienteSelecionado;
    }

    public SasPWFill getFilialDescRota() {
        return filialDescRota;
    }

    public void setFilialDescRota(SasPWFill filialDescRota) {
        this.filialDescRota = filialDescRota;
    }

    public boolean isExclFlag() {
        return exclFlag;
    }

    public void setExclFlag(boolean exclFlag) {
        this.exclFlag = exclFlag;
    }

    public Clientes getCliOri() {
        return cliOri;
    }

    public void setCliOri(Clientes cliOri) {
        this.cliOri = cliOri;
    }

    public Clientes getCliDst() {
        return cliDst;
    }

    public void setCliDst(Clientes cliDst) {
        this.cliDst = cliDst;
    }

    public List<Rt_Hist> getHistorico() {
        return historico;
    }

    public void setHistorico(List<Rt_Hist> historico) {
        this.historico = historico;
    }

    public List<Rt_Guias> getGuias() {
        return guias;
    }

    public void setGuias(List<Rt_Guias> guias) {
        this.guias = guias;
    }

    public Rt_Hist getHistoricoSelecionado() {
        return historicoSelecionado;
    }

    public void setHistoricoSelecionado(Rt_Hist historicoSelecionado) {
        this.historicoSelecionado = historicoSelecionado;
    }

    public Rt_Hist getNovoHistorico() {
        return novoHistorico;
    }

    public void setNovoHistorico(Rt_Hist novoHistorico) {
        this.novoHistorico = novoHistorico;
    }

    public Rt_Guias getGuiaSelecionada() {
        return guiaSelecionada;
    }

    public void setGuiaSelecionada(Rt_Guias guiaSelecionada) {
        this.guiaSelecionada = guiaSelecionada;
    }

    public List<String> ER(String query) {
        List<String> ER = new ArrayList<>();
        ER.add("E");
        ER.add("R");
        ER.add("ER");
        ER.add("T");
        return ER;
    }

    public List<String> tipoSrv(String query) {
        List<String> tipoSrv = new ArrayList<>();
        tipoSrv.add("R");
        tipoSrv.add("V");
        tipoSrv.add("E");
        tipoSrv.add("A");
        tipoSrv.add("I");
        tipoSrv.add("P");
        tipoSrv.add("J");
        return tipoSrv;
    }

    public String getERExtenso() {
        switch (this.novoTrajeto.getER()) {
            case "E":
                ERExtenso = Messages.getMessageS("Entrega");
                break;
            case "R":
                ERExtenso = Messages.getMessageS("Recolhimento");
                break;
            case "ER":
                ERExtenso = Messages.getMessageS("Entrega/Recolhimento");
                break;
            case "T":
                ERExtenso = Messages.getMessageS("Transbordo");
                break;
            default:
                ERExtenso = "";
        }
        return ERExtenso;
    }

    public void setERExtenso(String ERExtenso) {
        this.ERExtenso = ERExtenso;
    }

    public String getTipoSrvExtenso() {
        switch (this.novoTrajeto.getTipoSrv()) {
            case "R":
                tipoSrvExtenso = Messages.getMessageS("Rotineiro");
                break;
            case "V":
                tipoSrvExtenso = Messages.getMessageS("Eventual");
                break;
            case "E":
                tipoSrvExtenso = Messages.getMessageS("Especial");
                break;
            case "A":
                tipoSrvExtenso = Messages.getMessageS("AssistTecnica");
                break;
            case "I":
                tipoSrvExtenso = Messages.getMessageS("Intermediaria");
                break;
            case "P":
                tipoSrvExtenso = Messages.getMessageS("Preliminar");
                break;
            case "J":
                tipoSrvExtenso = Messages.getMessageS("ProjetoEspecial");
                break;
            default:
                tipoSrvExtenso = "";
        }
        return tipoSrvExtenso;
    }

    public void setTipoSrvExtenso(String tipoSrvExtenso) {
        this.tipoSrvExtenso = tipoSrvExtenso;
    }

    public EmailsEnviar getEmail() {
        return email;
    }

    public void setEmail(EmailsEnviar email) {
        this.email = email;
    }

    public List<Clientes> getClientesManifest() {
        return clientesManifest;
    }

    public void setClientesManifest(List<Clientes> clientesManifest) {
        this.clientesManifest = clientesManifest;
    }

    public Clientes getClienteManifest() {
        return clienteManifest;
    }

    public void setClienteManifest(Clientes clienteManifest) {
        this.clienteManifest = clienteManifest;
    }
}

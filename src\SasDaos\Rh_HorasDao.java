package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Cargos;
import SasBeans.Clientes;
import SasBeans.CtrOperV;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.RHPonto;
import SasBeans.Rastrear;
import SasBeans.Rh_Horas;
import SasBeansCompostas.CarregaRHHoras;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rh_HorasDao {

    /**
     * Atualiza o posto de serviço de um funcionário
     *
     * @param secao
     * @param dtCompet
     * @param codFil
     * @param matr
     * @param dataAtual
     * @param horaAtual
     * @param operador
     * @param persistencia
     * @throws Exception
     */
    public void atualizarRh_Horas(String secao, String dtCompet, String codFil, String matr, String dataAtual, String horaAtual, String operador,
            Persistencia persistencia) throws Exception {
        try {
            String sql = " Update Rh_Horas Set Secao = ?, dt_alter = ?, hr_alter = ?, operador = ? "
                    + " where Data = ? and CodFil = ? and Matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            consulta.setString(dtCompet);
            consulta.setString(codFil);
            consulta.setString(matr);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rh_HorasDao.atualizarRh_Hora - " + e.getMessage() + "\r\n"
                    + " Update Rh_Horas Set Secao = " + secao + ", dt_alter = " + dataAtual + ", hr_alter = " + horaAtual + ", operador = " + operador
                    + " where Data = " + dtCompet + " and CodFil = " + codFil + " and Matr = " + matr);
        }
    }

    /**
     * Verifica a existencia de alguma entrada
     *
     * @param rh_Horas
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeRh_Horas(Rh_Horas rh_Horas, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = false;
            String sql = " Select * from Rh_Horas "
                    + " where matr = ? and codfil = ? and data = ? ";//and secao = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rh_Horas.getMatr());
            consulta.setBigDecimal(rh_Horas.getCodFil());
            consulta.setString(rh_Horas.getData());
//            consulta.setString(rh_Horas.getSecao());
            consulta.select();
            while (consulta.Proximo()) {
                retorno = true;
                break;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rh_HorasDao.existeRh_Hora - " + e.getMessage() + "\r\n"
                    + " Select * from Rh_Horas "
                    + " where matr = " + rh_Horas.getMatr() + " and codfil = " + rh_Horas.getCodFil() + ""
                    + " and data = " + rh_Horas.getData());//+" and secao = "+rh_Horas.getSecao());
        }
    }

    /**
     * Incluir registro em rh horas
     *
     * @param horas Objeto contendo registros
     * @param persistencia conexão com o banco de dados
     * @throws Exception
     */
    public void atualizar(Rh_Horas horas, Persistencia persistencia) throws Exception {

        String sql = "UPDATE RH_Horas SET HsDiurnas = ?, HsNoturnas = ?, HsExceden = ?,"
                + " Situacao = ?, Secao = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ? "
                + " WHERE Matr = ? AND CodFil = ? AND Data = ?";

        try {

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(horas.getHsDiurnas());
            consulta.setBigDecimal(horas.getHsNoturnas());
            consulta.setBigDecimal(horas.getHsExceden());
            consulta.setString(horas.getSituacao());
            consulta.setString(horas.getSecao());
            consulta.setString(horas.getOperador());
            consulta.setString(horas.getData());
            consulta.setString(horas.getHr_Alter());
            consulta.setBigDecimal(horas.getMatr());
            consulta.setBigDecimal(horas.getCodFil());
            consulta.setString(horas.getData());

            consulta.update();
            consulta.close();
        } catch (Exception ex) {
            throw new Exception("Ocorre um erro: " + ex.getMessage());
        }
    }

    /**
     * Incluir registro em rh horas
     *
     * @param horas Objeto contendo registros
     * @param persistencia conexão com o banco de dados
     * @throws Exception
     */
    public void incluir(Rh_Horas horas, Persistencia persistencia) throws Exception {

        String sql = "INSERT INTO RH_Horas (Matr, CodFil, Data, HsDiurnas,"
                + " HsNoturnas, HsExceden, Situacao, Secao, Operador, Dt_Alter, Hr_Alter) "
                + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

        try {

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(horas.getMatr());
            consulta.setBigDecimal(horas.getCodFil());
            consulta.setString(horas.getData());
            consulta.setBigDecimal(horas.getHsDiurnas());
            consulta.setBigDecimal(horas.getHsNoturnas());
            consulta.setBigDecimal(horas.getHsExceden());
            consulta.setString(horas.getSituacao());
            consulta.setString(horas.getSecao());
            consulta.setString(horas.getOperador());
            consulta.setString(horas.getData());
            consulta.setString(horas.getHr_Alter());

            consulta.insert();
            consulta.close();
        } catch (Exception ex) {
            throw new Exception("Ocorre um erro: " + ex.getMessage());
        }
    }

    /**
     * Verifica se existe a hora
     *
     * @param matr matricula do funcionário
     * @param codfil codigo da filial
     * @param persistencia conexão com a base de dados
     * @return se tem a hora ou não
     * @throws Exception
     */
    public boolean isHoras(String matr, String codfil, String data, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM rh_horas WHERE matr = ? "
                    + "AND codfil = ? "
                    + "AND data = ? ";

            //Realiza o processamento da consulta
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.setString(codfil);
            consulta.setString(data);
            consulta.select();

            while (consulta.Proximo()) {
                if (consulta.getInt("qtd") > 0) {
                    existe = true;
                }
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("erro: " + e.getMessage());
        }
        return existe;
    }

    public List<Rh_Horas> getCabecalhoPonto(String sMatricula, String sDt_Ini, String sDt_Fim, String sCodFil, Persistencia persistencia) throws Exception {

        List<Rh_Horas> lRh_Horas = new ArrayList();

        String sql = "Select Convert(VarChar, data, 103)Data1, "
                + "DATEPART(weekday, data + @@DATEFIRST)Semana, Hora1, Hora2, Hora3, Hora4 "
                + "from Rh_Horas as Rh_Horas "
                + "where Matr =  ? "
                + "and Rh_Horas.data between ? and ? "
                + "and CodFil = ? "
                + "order by data";
        try {
            Consulta cRh_Horas = new Consulta(sql, persistencia);
            cRh_Horas.setString(sMatricula);
            cRh_Horas.setString(sDt_Ini);
            cRh_Horas.setString(sDt_Fim);
            cRh_Horas.setString(sCodFil);
            cRh_Horas.select();

            while (cRh_Horas.Proximo()) {
                Rh_Horas oRh_Horas = new Rh_Horas();

                oRh_Horas.setData(cRh_Horas.getString("Data1"));
                oRh_Horas.setSemana(cRh_Horas.getString("Semana"));
                oRh_Horas.setHora1(cRh_Horas.getString("Hora1"));
                oRh_Horas.setHora2(cRh_Horas.getString("Hora2"));
                oRh_Horas.setHora3(cRh_Horas.getString("Hora3"));
                oRh_Horas.setHora4(cRh_Horas.getString("Hora4"));
                lRh_Horas.add(oRh_Horas);

            }
            cRh_Horas.Close();
            return lRh_Horas;
        } catch (Exception e) {
            throw new Exception("Falha ao retorna dados da função - " + e.getMessage());
        }

    }

    public List<Rh_Horas> getRh_Horas(String sMatricula, String sCodFil, Persistencia persistencia) throws Exception {

        List<Rh_Horas> lRh_Horas = new ArrayList();

        String sql = " Select Matr, CodFil, Semana, Data, DiaSem, Hora1, Hora2, Hora3, Hora4, "
                + " Hora5, Hora6, HsDiurnas, HsNoturnas, HE50, HE100, HE50i, HE100i, "
                + " HsDiaMin, HEDia, HsAbono, HsProjecao, HsExceden, HsEscala, HsIntraJ, "
                + " Situacao, Secao, Operador, Dt_Alter, Hr_Alter "
                + " from Rh_Horas as Rh_Horas "
                + " where Matr =  ? "
                + " and CodFil = ? "
                + " order by data ";
        try {
            Consulta cRh_Horas = new Consulta(sql, persistencia);
            cRh_Horas.setString(sMatricula);
            cRh_Horas.setString(sCodFil);
            cRh_Horas.select();

            while (cRh_Horas.Proximo()) {
                Rh_Horas oRh_Horas = new Rh_Horas();

                oRh_Horas.setMatr(cRh_Horas.getString("Matr"));
                oRh_Horas.setCodFil(cRh_Horas.getString("CodFil"));
                oRh_Horas.setSemana(cRh_Horas.getString("Semana"));
                oRh_Horas.setData(cRh_Horas.getString("Data"));
                oRh_Horas.setDiaSem(cRh_Horas.getInt("DiaSem"));
                oRh_Horas.setHora1(cRh_Horas.getString("Hora1"));
                oRh_Horas.setHora2(cRh_Horas.getString("Hora2"));
                oRh_Horas.setHora3(cRh_Horas.getString("Hora3"));
                oRh_Horas.setHora4(cRh_Horas.getString("Hora4"));
                oRh_Horas.setHora5(cRh_Horas.getString("Hora5"));
                oRh_Horas.setHora6(cRh_Horas.getString("Hora6"));
                oRh_Horas.setHsDiurnas(cRh_Horas.getString("HsDiurnas"));
                oRh_Horas.setHsNoturnas(cRh_Horas.getString("HsNoturnas"));
                oRh_Horas.setHE50(cRh_Horas.getString("HE50"));
                oRh_Horas.setHE100(cRh_Horas.getString("HE100"));
                oRh_Horas.setHE50i(cRh_Horas.getString("HE50i"));
                oRh_Horas.setHE100i(cRh_Horas.getString("HE100i"));
                oRh_Horas.setHsDiaMin(cRh_Horas.getString("HsDiaMin"));
                oRh_Horas.setHEDia(cRh_Horas.getString("HEDia"));
                oRh_Horas.setHsAbono(cRh_Horas.getString("HsAbono"));
                oRh_Horas.setHsProjecao(cRh_Horas.getString("HsProjecao"));
                oRh_Horas.setHsExceden(cRh_Horas.getString("HsExceden"));
                oRh_Horas.setHsEscala(cRh_Horas.getString("HsEscala"));
                oRh_Horas.setHsIntraJ(cRh_Horas.getString("HsIntraJ"));
                oRh_Horas.setSituacao(cRh_Horas.getString("Situacao"));
                oRh_Horas.setSecao(cRh_Horas.getString("Secao"));
                oRh_Horas.setOperador(cRh_Horas.getString("Operador"));
                oRh_Horas.setDt_Alter(cRh_Horas.getString("Dt_Alter"));
                oRh_Horas.setHr_Alter(cRh_Horas.getString("Hr_Alter"));

                lRh_Horas.add(oRh_Horas);
            }
            cRh_Horas.Close();
            return lRh_Horas;
        } catch (Exception e) {
            throw new Exception("Falha ao retorna dados da função - " + e.getMessage());
        }

    }

    public List<CarregaRHHoras> carregaRHHoras(String turno, String secao, String codfil, Persistencia persistencia) throws Exception {
        String data_sql = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");

        try {
            List<CarregaRHHoras> list_CarregaRHHoras = new ArrayList<CarregaRHHoras>();

            String sql = "Select RH_Horas.Matr, Funcion.Nome, Cargos.Descricao CargoDesc, RH_Horas.Data, RH_Horas.HsDiurnas, RH_Horas.HsNoturnas, "
                    + " RH_Horas.Situacao, RHPonto.Hora, Rastrear.Latitude LatFunc, Rastrear.Longitude LongFunc, Clientes.Latitude LatCli, "
                    + " Clientes.Longitude LongCli, Clientes.NRed, PstServ.CodCli, Rastrear.Precisao, CtrOperV.HEDiurna BHEDiu, "
                    + " CtrOperV.HENoturna BHENot, Funcion.Situacao SitFunc, RH_Horas.Hora1, RH_Horas.Hora2, RH_Horas.Hora3, RH_Horas.Hora4    from RH_Horas "
                    + " left join RHPonto on  RHPonto.Matr = RH_Horas.Matr "
                    + " and RHPonto.DtCompet = ? "
                    + " and RHPonto.Batida   = 1 "
                    + " left join Funcion on Funcion.Matr = RH_Horas.Matr "
                    + " left join Cargos  on Cargos.Cargo = Funcion.Cargo "
                    + " left join Rastrear on  Rastrear.Matr   = RH_Horas.Matr "
                    + " and Rastrear.Data   = RH_Horas.Data "
                    + " and Rastrear.Batida = 1 "
                    + " left join PstServ  on  PstServ.Secao   = RH_Horas.Secao  "
                    + " and PstServ.CodFil  = RH_Horas.CodFil "
                    + " left join Clientes on  Clientes.Codigo = PstServ.CodCli  "
                    + " and Clientes.CodFil = PstServ.CodFil  "
                    + " left join CtrOperV on  CtrOperV.FuncSubs   = RH_Horas.Matr "
                    + " and CtrOperV.Data = RH_Horas.Data "
                    + " and CtrOperV.Hora_Extra = 'B' "
                    + " where RH_Horas.Data   = ? "
                    + " and RH_Horas.Secao  = ? "
                    + " and RH_Horas.CodFil = ? ";

            if ("d".equals(turno)) {
                sql += " and (HsNoturnas = 0 or (HsDiurnas > 2*HsNoturnas)) "; //quando diurno
            } else {
                sql += " and (HsNoturnas > 0 or (HsNoturnas + HsDiurnas = 0)) "; //quando noturno
            }
            sql += " and (((RH_Horas.HsDiurnas+RH_Horas.HsNoturnas) <> 0) or ((CtrOperV.HEDiurna +CtrOperV.HENoturna ) <> 0)) ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(data_sql);
            consult.setString(data_sql);
            consult.setString(secao);
            consult.setString(codfil);
            //consult.setString(data_sql);
            consult.select();
            while (consult.Proximo()) {
                Rh_Horas rH_Horas = new Rh_Horas();
                Funcion oFuncion = new Funcion();
                Cargos oCargos = new Cargos();
                RHPonto oRHPonto = new RHPonto();
                Rastrear oRastrear = new Rastrear();
                Clientes oClientes = new Clientes();
                PstServ oPstServ = new PstServ();
                CtrOperV oCtrOperV = new CtrOperV();
                CarregaRHHoras oCarregaRHHoras = new CarregaRHHoras();
                rH_Horas.setMatr(consult.getString("Matr"));
                oFuncion.setNome(consult.getString("Nome"));
                oCargos.setDescricao(consult.getString("CargoDesc"));
                rH_Horas.setData(consult.getString("Data"));
                rH_Horas.setHora1(consult.getString("Hora1"));                
                rH_Horas.setHora2(consult.getString("Hora2"));                
                rH_Horas.setHora3(consult.getString("Hora3"));                
                rH_Horas.setHora4(consult.getString("Hora4"));                                
                rH_Horas.setHsDiurnas(consult.getString("HsDiurnas"));
                rH_Horas.setHsNoturnas(consult.getString("HsNoturnas"));
                rH_Horas.setSituacao(consult.getString("Situacao"));
                oRHPonto.setHora(consult.getString("Hora"));
                oRastrear.setLatitude(consult.getString("LatFunc"));
                oRastrear.setLongitude(consult.getString("LongFunc"));
                oClientes.setLongitude(consult.getString("LatCli"));
                oClientes.setLongitude(consult.getString("LongCli"));
                oClientes.setNRed(consult.getString("NRed"));
                oPstServ.setCodCli(consult.getString("CodCli"));
                oRastrear.setPrecisao(consult.getString("Precisao"));
                oCtrOperV.setHEDiurna(consult.getString("BHEDiu"));
                oCtrOperV.setHENoturna(consult.getString("BHENot"));
                oFuncion.setSituacao(consult.getString("SitFunc"));

                oCarregaRHHoras.setrH_Horas(rH_Horas);
                oCarregaRHHoras.setFuncion(oFuncion);
                oCarregaRHHoras.setCargos(oCargos);
                oCarregaRHHoras.setrHPonto(oRHPonto);
                oCarregaRHHoras.setRastrear(oRastrear);
                oCarregaRHHoras.setClientes(oClientes);
                oCarregaRHHoras.setCtrOperV(oCtrOperV);
                oCarregaRHHoras.setPstServ(oPstServ);

                list_CarregaRHHoras.add(oCarregaRHHoras);
            }
            consult.Close();
            return list_CarregaRHHoras;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar funcionarios para supervisão - " + e.getMessage());
        }
    }

    public List<CarregaRHHoras> carregaRHHoras(String turno, String secao, String codfil, String data_sql, Persistencia persistencia) throws Exception {
        try {
            List<CarregaRHHoras> list_CarregaRHHoras = new ArrayList<CarregaRHHoras>();

            String sql = "Select RH_Horas.Matr, Funcion.Nome, Cargos.Descricao CargoDesc, RH_Horas.Data, RH_Horas.HsDiurnas, RH_Horas.HsNoturnas, "
                    + " RH_Horas.Situacao, RHPonto.Hora, Rastrear.Latitude LatFunc, Rastrear.Longitude LongFunc, Clientes.Latitude LatCli, "
                    + " Clientes.Longitude LongCli, Clientes.NRed, PstServ.CodCli, Rastrear.Precisao, CtrOperV.HEDiurna BHEDiu, "
                    + " CtrOperV.HENoturna BHENot, Funcion.Situacao SitFunc, RH_horas.Hora1, RH_Horas.Hora2, RH_Horas.Hora3, RH_Horas.Hora4 from RH_Horas "
                    + " left join RHPonto on  RHPonto.Matr = RH_Horas.Matr "
                    + " and RHPonto.DtCompet = ? "
                    + " and RHPonto.Batida   = 1 "
                    + " left join Funcion on Funcion.Matr = RH_Horas.Matr "
                    + " left join Cargos  on Cargos.Cargo = Funcion.Cargo "
                    + " left join Rastrear on  Rastrear.Matr   = RH_Horas.Matr "
                    + " and Rastrear.Data   = RH_Horas.Data "
                    + " and Rastrear.Batida = 1 "
                    + " left join PstServ  on  PstServ.Secao   = RH_Horas.Secao  "
                    + " and PstServ.CodFil  = RH_Horas.CodFil "
                    + " left join Clientes on  Clientes.Codigo = PstServ.CodCli  "
                    + " and Clientes.CodFil = PstServ.CodFil  "
                    + " left join CtrOperV on  CtrOperV.FuncSubs   = RH_Horas.Matr "
                    + " and CtrOperV.Data = RH_Horas.Data "
                    + " and CtrOperV.Hora_Extra = 'B' "
                    + " where RH_Horas.Data   = ? "
                    + " and RH_Horas.Secao  = ? "
                    + " and RH_Horas.CodFil = ? ";

            if ("d".equals(turno)) {
                sql += " and (HsNoturnas = 0 or (HsDiurnas > 2*HsNoturnas)) "; //quando diurno
            } else {
                sql += " and (HsNoturnas > 0 or (HsNoturnas + HsDiurnas = 0)) "; //quando noturno
            }
            sql += " and (((RH_Horas.HsDiurnas+RH_Horas.HsNoturnas) <> 0) or ((CtrOperV.HEDiurna +CtrOperV.HENoturna ) <> 0)) ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(data_sql);
            consult.setString(data_sql);
            consult.setString(secao);
            consult.setString(codfil);
            //consult.setString(data_sql);
            consult.select();
            while (consult.Proximo()) {
                Rh_Horas rH_Horas = new Rh_Horas();
                Funcion oFuncion = new Funcion();
                Cargos oCargos = new Cargos();
                RHPonto oRHPonto = new RHPonto();
                Rastrear oRastrear = new Rastrear();
                Clientes oClientes = new Clientes();
                PstServ oPstServ = new PstServ();
                CtrOperV oCtrOperV = new CtrOperV();
                CarregaRHHoras oCarregaRHHoras = new CarregaRHHoras();
                rH_Horas.setMatr(consult.getString("Matr"));
                oFuncion.setNome(consult.getString("Nome"));
                oCargos.setDescricao(consult.getString("CargoDesc"));
                rH_Horas.setData(consult.getString("Data"));
                rH_Horas.setHsDiurnas(consult.getString("HsDiurnas"));
                rH_Horas.setHsNoturnas(consult.getString("HsNoturnas"));
                rH_Horas.setSituacao(consult.getString("Situacao"));
                oRHPonto.setHora(consult.getString("Hora"));
                oRastrear.setLatitude(consult.getString("LatFunc"));
                oRastrear.setLongitude(consult.getString("LongFunc"));
                oClientes.setLongitude(consult.getString("LatCli"));
                oClientes.setLongitude(consult.getString("LongCli"));
                oClientes.setNRed(consult.getString("NRed"));
                oPstServ.setCodCli(consult.getString("CodCli"));
                oRastrear.setPrecisao(consult.getString("Precisao"));
                oCtrOperV.setHEDiurna(consult.getString("BHEDiu"));
                oCtrOperV.setHENoturna(consult.getString("BHENot"));
                oFuncion.setSituacao(consult.getString("SitFunc"));

                oCarregaRHHoras.setrH_Horas(rH_Horas);
                oCarregaRHHoras.setFuncion(oFuncion);
                oCarregaRHHoras.setCargos(oCargos);
                oCarregaRHHoras.setrHPonto(oRHPonto);
                oCarregaRHHoras.setRastrear(oRastrear);
                oCarregaRHHoras.setClientes(oClientes);
                oCarregaRHHoras.setCtrOperV(oCtrOperV);
                oCarregaRHHoras.setPstServ(oPstServ);

                list_CarregaRHHoras.add(oCarregaRHHoras);
            }
            consult.Close();
            return list_CarregaRHHoras;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar funcionarios para supervisão - " + e.getMessage());
        }
    }
}

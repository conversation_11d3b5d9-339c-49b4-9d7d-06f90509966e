/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.guia;

import Arquivo.ArquivoLog;
import Controller.Guias.PedidosPreOrderSatWeb;
import Dados.Persistencia;
import SasBeans.PreOrder;
import SasBeansCompostas.PreOrderManifesto;
import br.com.sasw.lazydatamodels.PedidosPreOrderLazyList;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.LazyDataModel;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 * <AUTHOR>
 */
@Named(value = "pedidos")
@ViewScoped
public class PedidosPreOrderMB implements Serializable {

    private String codFil, data, banco, nomeFilial, codGrupo, novoLacre,
            nomecli, codcli, operador, caminho, log, codBarras, extenso, autenticacao, voltar,
            origemteste, hora1o, hora2o, hora1d, hora2d, agencia, subagencia,
            html, relatorio, tabela, coluna, linha, span, nomeArquivo;
    private ArquivoLog logerro;
    private List<PreOrderManifesto> manifesto;
    private BigDecimal codPessoa;
    private Persistencia persistencia;
    private final PedidosPreOrderSatWeb pedidosweb;
    private Date dataSelecionada;
    private Map filters;
    private int total;
    private LazyDataModel<PreOrder> pedidos = null;
    private PreOrder preOrderSelecionado;
    private boolean limpar;

    public PedidosPreOrderMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codcli = (String) fc.getExternalContext().getSessionMap().get("cliente");
        nomecli = (String) fc.getExternalContext().getSessionMap().get("nomeCliente");
        agencia = (String) fc.getExternalContext().getSessionMap().get("agencia");
        subagencia = (String) fc.getExternalContext().getSessionMap().get("subagencia");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        voltar = (String) fc.getExternalContext().getSessionMap().get("origem");
        codGrupo = (String) fc.getExternalContext().getSessionMap().get("codgrupo");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");

        dataSelecionada = Calendar.getInstance().getTime();
        data = Calendar.getInstance().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog(this.getClass().getSimpleName());

        pedidosweb = new PedidosPreOrderSatWeb();
    }

    public void Persistencias(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.filters = new HashMap();
            this.filters.put(" PreOrder.DtColeta = ? ", this.data);

            this.total = this.pedidosweb.contagem(this.filters, this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<PreOrder> getAllPedidos() {
        if (this.pedidos == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace(" PreOrder.DtColeta = ? ", this.data);
            dt.setFilters(this.filters);
            this.pedidos = new PedidosPreOrderLazyList(this.persistencia);
        }
        return this.pedidos;
    }

    public void selecionarData() {
        try {
            this.data = this.dataSelecionada.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.filters.replace(" PreOrder.DtColeta = ? ", this.data);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            gerarLog("Listando pedidos");
            getAllPedidos();
            dt.setFirst(0);

            gerarLog("Contando quantidade de pedidos.");
            this.total = this.pedidosweb.contagem(this.filters, this.persistencia);

            PrimeFaces.current().ajax().update("msgs", "main", "cabecalho", "panelCals", "totais");
            PrimeFaces.current().executeScript("PF('oCalendarios').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataAnterior() {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(this.dataSelecionada);
            c.add(Calendar.DAY_OF_YEAR, -1);
            this.dataSelecionada = c.getTime();

            this.data = this.dataSelecionada.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.filters.replace(" PreOrder.DtColeta = ? ", this.data);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            gerarLog("Listando pedidos");
            getAllPedidos();
            dt.setFirst(0);

            gerarLog("Contando quantidade de pedidos.");
            this.total = this.pedidosweb.contagem(this.filters, this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataPosterior() {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(this.dataSelecionada);
            c.add(Calendar.DAY_OF_YEAR, 1);
            this.dataSelecionada = c.getTime();
            this.data = this.dataSelecionada.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.filters.replace(" PreOrder.DtColeta = ? ", this.data);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            gerarLog("Listando pedidos");
            getAllPedidos();
            dt.setFirst(0);

            gerarLog("Contando quantidade de pedidos.");
            this.total = this.pedidosweb.contagem(this.filters, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preparaEdicaoLacre() {
        if (null == this.preOrderSelecionado) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePedido"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.novoLacre = "";
            PrimeFaces.current().ajax().update("formEditarLacre");
            PrimeFaces.current().executeScript("PF('dlgEditarLacre').show();");
        }
    }

    public void editarLacre() {
        if (null == this.preOrderSelecionado) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePedido"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.preOrderSelecionado.setOperador(FuncoesString.RecortaAteEspaço("WEB-" + this.operador, 0, 10));
                this.pedidosweb.atualizarLacre(this.preOrderSelecionado, this.novoLacre, this.persistencia);
                PrimeFaces.current().ajax().update("main");
                PrimeFaces.current().executeScript("PF('dlgEditarLacre').hide();");
            } catch (Exception e) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void gerarGuiaDownload() throws Exception {
        InputStream stream = new ByteArrayInputStream(this.html.getBytes());

        ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
        ITextRenderer renderer = new ITextRenderer();
        Tidy tidy = new Tidy();
        tidy.setShowWarnings(false);
        Document doc = tidy.parseDOM(stream, null);
        renderer.setDocument(doc, null);
        renderer.layout();
        renderer.createPDF(osPdf);

        InputStream inputPDF = new ByteArrayInputStream(osPdf.toByteArray());

//        this.arquivoDownload = new DefaultStreamedContent(inputPDF, "pdf", this.nomeArquivo);
        osPdf.close();
        stream.close();
        inputPDF.close();
    }
//
//    public void listarManifestosDisponiveis(ActionEvent acitionEvent) {
//        try {
//            this.preOrderSelecionado = null;
//            this.pedidosRecentes = this.guiasweb.listarManifestosDisponiveis(this.codFil.equals(BigDecimal.ZERO) ? "" : this.codFil.toPlainString(),
//                    this.data1, this.data2, this.persistencia);
//            if (this.pedidosRecentes.isEmpty()) {
//                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemManifestos"), null);
//                FacesContext.getCurrentInstance().addMessage(null, message);
//            } else if (this.pedidosRecentes.size() == 1) {
//                this.preOrderSelecionado = this.pedidosRecentes.get(0);
//                listarManifestoPreOrder();
//            } else {
//                PrimeFaces.current().resetInputs("formManifestosDisponiveis");
//                PrimeFaces.current().executeScript("PF('dlgManifestosDisponiveis').show()");
//            }
//        } catch (Exception e) {
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            log = this.getClass().getSimpleName() + "\r\n"
//                    + Thread.currentThread().getStackTrace()[1].getMethodName()
//                    + "\r\n" + e.getMessage() + "\r\n";
//            this.logerro.Grava(log, caminho);
//        }
//    }
//
//    public void listarManifestoPreOrder() {
//        try {
//            if (null == this.preOrderSelecionado) {
//                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneManifesto"), null);
//                FacesContext.getCurrentInstance().addMessage(null, message);
//            } else {
//                this.manifesto = this.guiasweb.listarManifestosPreOrder(this.preOrderSelecionado.getCodFil(),
//                        this.preOrderSelecionado.getDtColeta(), this.preOrderSelecionado.getHora1D(), this.persistencia);
//                if (this.manifesto.isEmpty()) {
//                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemManifesto"), null);
//                    FacesContext.getCurrentInstance().addMessage(null, message);
//                } else {
//
//                    this.nomeArquivo = "manifesto" + this.preOrderSelecionado.getDtColeta() + ".pdf";
//
//                    Filiais infoFilial = this.guiasweb.buscaInfoFilial(this.preOrderSelecionado.getCodFil(), this.persistencia);
//
//                    this.relatorio = LerArquivo.obterConteudo(PedidosPreOrderMB.class.getResourceAsStream("relatorio/relatorio.html"));
//                    this.tabela = LerArquivo.obterConteudo(PedidosPreOrderMB.class.getResourceAsStream("relatorio/tabela.html"));
//                    this.linha = LerArquivo.obterConteudo(PedidosPreOrderMB.class.getResourceAsStream("relatorio/linha.html"));
//                    this.coluna = LerArquivo.obterConteudo(PedidosPreOrderMB.class.getResourceAsStream("relatorio/coluna.html"));
//                    this.span = LerArquivo.obterConteudo(PedidosPreOrderMB.class.getResourceAsStream("relatorio/span.html"));
//
//                    StringBuilder guiaImpressa = new StringBuilder();
//                    StringBuilder guiaImpressaAuxTextoTabela, guiaImpressaAuxTextoLinha, guiaImpressaAuxTextoColuna;
//
//                    guiaImpressaAuxTextoLinha = new StringBuilder();
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoTabela = new StringBuilder();
//
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "4").replace("@StyleTD", "")
//                            .replace("@WidthTD", "").replace("@ClassTD", "")
//                            .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.preOrderSelecionado.getCodFil())
//                                    + "\" height=\"47px\" width=\"59px\"/>"));
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
//                            .replace("@WidthTD", "").replace("@ClassTD", "")
//                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
//                                    .replace("@TextoSpan", infoFilial.getRazaoSocial())));
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
//                            .replace("@WidthTD", "").replace("@ClassTD", "")
//                            .replace("@TextoTD", infoFilial.getEndereco()));
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
//                            .replace("@TextoTD", infoFilial.getCidade() + "&nbsp;-&nbsp;"
//                                    + CEP(infoFilial.getCEP())));
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
//                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
//                                    + ": &nbsp;" + CNPJ(infoFilial.getCNPJ())
//                                    + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Telefone"))
//                                    + ": &nbsp;" + Fone(infoFilial.getFone())));
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "4").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
//                            .replace("@TextoTD",
//                                    this.span.replace("@StyleSpan", "font-weight: bold; font-size: 18px;").replace("@ClassSpan", "")
//                                            .replace("@TextoSpan", "M A N I F E S T O")));
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoTabela.append(this.tabela
//                            .replace("@StyleTabela", "border-collapse: collapse; text-align: left; background: #fff; overflow: hidden; width: 500px;  font-size: 16px; padding: 3px 3px; color: #000000; font-weight: normal;")
//                            .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
//                            .replace("@TextoTabela", guiaImpressaAuxTextoLinha));
//
////            guiaImpressa.append(guiaImpressaAuxTextoTabela);
//                    BigDecimal valorTotal = BigDecimal.ZERO;
//                    for (PreOrderManifesto preOrderManifesto : this.manifesto) {
//                        guiaImpressaAuxTextoLinha = new StringBuilder();
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", "<hr>")));
//
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", getMessageS("Destino") + ": "));
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", preOrderManifesto.getAgenciaDestino() + "/" + preOrderManifesto.getSubAgenciaDestino()
//                                                + " " + preOrderManifesto.getDestino())));
//
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", getMessageS("Valor") + ": "));
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", Moeda(preOrderManifesto.getValor()))));
//
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", getMessageS("Lacre") + ": "));
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", preOrderManifesto.getLacre())));
//
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", getMessageS("Guia") + ": "));
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", preOrderManifesto.getGuia() + " - " + preOrderManifesto.getSerie())));
//
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", getMessageS("AssinadoPor") + ": "));
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", preOrderManifesto.getCodPessoaAut() + " " + preOrderManifesto.getNome())));
//
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", getMessageS("Rota") + ": "));
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", preOrderManifesto.getRota())));
//
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@IdTabela", "").replace("@ClassTabela", "")
//                                .replace("@StyleTabela", " background: #fff; width: 500px;")
//                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));
//
//                        valorTotal = valorTotal.add(new BigDecimal(preOrderManifesto.getValor()));
//                    }
//
//                    guiaImpressaAuxTextoLinha = new StringBuilder();
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "").replace("@WidthTD", "")
//                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                    .replace("@TextoSpan", "<hr>")));
//
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "").replace("@WidthTD", "")
//                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold")
//                                    .replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Total").toUpperCase())));
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center; font-size: 16px")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "").replace("@WidthTD", "")
//                            .replace("@TextoTD", getMessageS("Quantidade") + ": "));
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "").replace("@WidthTD", "")
//                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                    .replace("@TextoSpan", this.manifesto.size() + "")));
//
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "").replace("@WidthTD", "")
//                            .replace("@TextoTD", getMessageS("Valor") + ": "));
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "").replace("@WidthTD", "")
//                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                    .replace("@TextoSpan", Moeda(valorTotal.toString()))));
//
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoColuna = new StringBuilder();
//                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
//                            .replace("@StyleTD", "").replace("@WidthTD", "")
//                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
//                                    .replace("@TextoSpan", Messages.getValorExtensoS(valorTotal.toString()))));
//
//                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                    guiaImpressaAuxTextoTabela.append(this.tabela.replace("@IdTabela", "").replace("@ClassTabela", "")
//                            .replace("@StyleTabela", "width:500px; background: #fff;")
//                            .replace("@TextoTabela", guiaImpressaAuxTextoLinha));
//
//                    guiaImpressa.append(guiaImpressaAuxTextoTabela);
//
//                    this.html = guiaImpressa.toString();
//                    PrimeFaces.current().executeScript("PF('dlgImprimir').show();");
//                }
//            }
//        } catch (Exception e) {
//            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, message);
//        }
//    }

    public void pesquisar() {
//        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
//        this.filters.replace(" (RPV.Guia = ?) ", this.guiaPesquisa.getGuia().equals("") ? Arrays.asList() : Arrays.asList(this.guiaPesquisa.getGuia()));
//        dt.setFilters(this.filters);
//        getAllGuias();
//        dt.setFirst(0);
//        this.guiaPesquisa = new EGtv();
    }

    public void limparFiltros() {
//        this.filters.replace(" (RPV.Guia = ?) ", Arrays.asList());
//        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
//        dt.setFilters(this.filters);
//        getAllGuias();
//        dt.setFirst(0);
//        this.limpar = false;
    }

    public void inserirPreOrders() {
//        try {
//            this.guiasweb.inserirPedidosImportados(this.listaAgencias, this.codcli,
//                    FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia);
//            this.pedidosRecentes = this.guiasweb.pedidosRecentes(this.codcli, this.codFil.toPlainString(), this.persistencia);
//            PrimeFaces.current().executeScript("PF('dlgConfirmacao').hide();");
//            PrimeFaces.current().executeScript("PF('dlgPreOrders').hide();");
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ImportacaoCompleta"), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//        } catch (Exception e) {
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            log = this.getClass().getSimpleName() + "\r\n"
//                    + Thread.currentThread().getStackTrace()[1].getMethodName()
//                    + "\r\n" + e.getMessage() + "\r\n";
//            this.logerro.Grava(log, caminho);
//        }
    }

    public void verificarExistenciaPreOrder() {
//        try {
//            if (this.guiasweb.verificarExistenciaPreOrder(this.listaAgencias.get(0).getDtColeta(), this.codcli, this.persistencia)) {
//                this.mensagemImportacao = Messages.getMessageS("MensagemConfirmacaoPreOrder")
//                        .replace("%s1", FuncoesString.formatarString(
//                                LocalDate.parse(this.listaAgencias.get(0).getDtColeta(),
//                                        DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("ddMMyyyy")), "##/##/####"));
//
//                PrimeFaces.current().executeScript("PF('dlgConfirmacao').show();");
//            } else {
//                inserirPreOrders();
//            }
//        } catch (Exception e) {
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            log = this.getClass().getSimpleName() + "\r\n"
//                    + Thread.currentThread().getStackTrace()[1].getMethodName()
//                    + "\r\n" + e.getMessage() + "\r\n";
//            this.logerro.Grava(log, caminho);
//        }
    }

    public void realizarUpload(FileUploadEvent event) {
//        try {
//            new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL")).mkdirs();
//            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL") + "\\" + event.getFile().getFileName();
//            File file = new File(arquivo);
//            if (file.exists()) {
//                arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL") + "\\" + getDataAtual("HHMMSS") + event.getFile().getFileName();
//                file = new File(arquivo);
//            }
//
//            FileOutputStream output = new FileOutputStream(file);
//            output.write(event.getFile().getContents());
//            output.close();
//            this.pedidos.add(file);
//
//            FileReader fileReader = new FileReader(file);
//            BufferedReader bufferedReader = new BufferedReader(fileReader);
//            String linha, dtEntrega = "", dtColeta = "";
//            String[] dados;
//            int indice = -1;
//            BBPedidoAgencia pedidoAgencia;
//            BBPedidoMalote pedidoMalote;
//            this.listaAgencias = new ArrayList<>();
//            this.volumesPreOrder = 0;
//            while ((linha = bufferedReader.readLine()) != null) {
//                this.logerro.Grava("leitura arquivo: " + linha, caminho);
//                dados = linha.split("\\|");
//                if (dados[0].equals("1")) {
//                    dtColeta = LocalDate.parse(dados[2], DateTimeFormatter.ofPattern("ddMMyyyy")).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//                    dtEntrega = LocalDate.parse(dados[4], DateTimeFormatter.ofPattern("ddMMyyyy")).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//                } else if (dados[0].equals("2")) {
//                    pedidoAgencia = new BBPedidoAgencia();
//                    pedidoAgencia.setAgencia(dados[1]);
//                    pedidoAgencia.setSubAgencia(dados[2]);
//                    pedidoAgencia.setDtColeta(dtColeta);
//                    pedidoAgencia.setDtEntrega(dtEntrega);
//                    pedidoAgencia.setListaMalotes(new ArrayList<>());
//
//                    indice = this.listaAgencias.indexOf(pedidoAgencia);
//                    if (indice == -1) {
//                        this.listaAgencias.add(pedidoAgencia);
//                        indice = this.listaAgencias.indexOf(pedidoAgencia);
//                    }
//                } else if (dados[0].equals("3")) {
//                    pedidoMalote = new BBPedidoMalote();
//                    pedidoMalote.setLacre(dados[1]);
//                    pedidoMalote.setHorario(dados[2]);
//                    pedidoMalote.setValor(new BigDecimal(dados[4]).divide(new BigDecimal("100")).toString());
//                    pedidoMalote.setObs(dados[17]);
//                    pedidoMalote.setPedidoCliente(dados[17].substring(12, 20));
//
//                    if (indice != -1) {
//                        this.volumesPreOrder++;
//                        this.listaAgencias.get(indice).getListaMalotes().add(pedidoMalote);
//                    }
//                }
//
//            }
//
//            fileReader.close();
//
//            if (this.listaAgencias.isEmpty()) {
//                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ArquivoInvalido"), null);
//                FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            } else {
//                this.mensagemImportacao = Messages.getMessageS("MensagemImportacaoPreOrder")
//                        .replace("%s1", String.valueOf(this.volumesPreOrder))
//                        .replace("%s2", String.valueOf(this.listaAgencias.size()));
//                PrimeFaces.current().executeScript("PF('dlgPreOrders').show();");
//            }
//        } catch (Exception e) {
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            log = this.getClass().getSimpleName() + "\r\n"
//                    + Thread.currentThread().getStackTrace()[1].getMethodName()
//                    + "\r\n" + e.getMessage() + "\r\n";
//            this.logerro.Grava(log, caminho);
//        }
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public BigDecimal getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(BigDecimal codPessoa) {
        this.codPessoa = codPessoa;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public String getData() {
        return data;
    }

    public void setData1(String data) {
        this.data = data;
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            this.dataSelecionada = df.parse(this.data);
        } catch (Exception e) {
        }
    }

    public Date getDataSelecionada() {
        return dataSelecionada;
    }

    public void setDataSelecionada(Date dataSelecionada) {
        this.dataSelecionada = dataSelecionada;
    }

    public String getCodBarras() {
        return codBarras;
    }

    public void setCodBarras(String codBarras) {
        this.codBarras = codBarras;
    }

    public String getExtenso() {
        return extenso;
    }

    public void setExtenso(String extenso) {
        this.extenso = extenso;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public void gerarLog(String texto) {
        this.logerro.GravaMetodos(texto, this.caminho);
    }

    public String getAutenticacao() {
        return autenticacao;
    }

    public void setAutenticacao(String autenticacao) {
        this.autenticacao = autenticacao;
    }

    public String getVoltar() {
        return null == voltar ? "login.xhtml" : voltar;
    }

    public void setVoltar(String voltar) {
        this.voltar = voltar;
    }

    public String getOrigemteste() {
        return origemteste;
    }

    public void setOrigemteste(String origemteste) {
        this.origemteste = origemteste;
    }

    public String getNomecli() {
        return nomecli;
    }

    public void setNomecli(String nomecli) {
        this.nomecli = nomecli;
    }

    public void setData(String data) {
        this.data = data;
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            this.dataSelecionada = df.parse(this.data);
        } catch (Exception e) {
        }
    }

    public String getHora1o() {
        return hora1o;
    }

    public void setHora1o(String hora1o) {
        this.hora1o = hora1o;
    }

    public String getHora2o() {
        return hora2o;
    }

    public void setHora2o(String hora2o) {
        this.hora2o = hora2o;
    }

    public String getHora1d() {
        return hora1d;
    }

    public void setHora1d(String hora1d) {
        this.hora1d = hora1d;
    }

    public String getHora2d() {
        return hora2d;
    }

    public void setHora2d(String hora2d) {
        this.hora2d = hora2d;
    }

    public boolean isLimpar() {
        return limpar;
    }

    public void setLimpar(boolean limpar) {
        this.limpar = limpar;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getSubagencia() {
        return subagencia;
    }

    public void setSubagencia(String subagencia) {
        this.subagencia = subagencia;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public PreOrder getPreOrderSelecionado() {
        return preOrderSelecionado;
    }

    public void setPreOrderSelecionado(PreOrder preOrderSelecionado) {
        this.preOrderSelecionado = preOrderSelecionado;
    }

    public String getNovoLacre() {
        return novoLacre;
    }

    public void setNovoLacre(String novoLacre) {
        this.novoLacre = novoLacre;
    }
}

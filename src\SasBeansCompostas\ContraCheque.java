/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.CCusto;
import SasBeans.Cargos;
import SasBeans.FPLancamentos;
import SasBeans.FPMensal;
import SasBeans.FPMensalOP;
import SasBeans.FPPeriodos;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.Verbas;

/**
 *
 * <AUTHOR>
 */
public class ContraCheque {

    private Funcion funcion;
    private PstServ pstServ;
    private Cargos cargos;
    private Filiais filiais;
    private CCusto cCusto;
    private FPMensalOP fPMensalOP;
    private FPMensal fPMensal;
    private Verbas verbas;
    private FPLancamentos fPLancamentos;
    private FPPeriodos fPeriodos;

    /**
     * @return the funcion
     */
    public Funcion getFuncion() {
        return funcion;
    }

    /**
     * @param funcion the funcion to set
     */
    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    /**
     * @return the pstServ
     */
    public PstServ getPstServ() {
        return pstServ;
    }

    /**
     * @param pstServ the pstServ to set
     */
    public void setPstServ(PstServ pstServ) {
        this.pstServ = pstServ;
    }

    /**
     * @return the cargos
     */
    public Cargos getCargos() {
        return cargos;
    }

    /**
     * @param cargos the cargos to set
     */
    public void setCargos(Cargos cargos) {
        this.cargos = cargos;
    }

    /**
     * @return the filiais
     */
    public Filiais getFiliais() {
        return filiais;
    }

    /**
     * @param filiais the filiais to set
     */
    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    /**
     * @return the cCusto
     */
    public CCusto getcCusto() {
        return cCusto;
    }

    /**
     * @param cCusto the cCusto to set
     */
    public void setcCusto(CCusto cCusto) {
        this.cCusto = cCusto;
    }

    /**
     * @return the fPMensalOP
     */
    public FPMensalOP getfPMensalOP() {
        return fPMensalOP;
    }

    /**
     * @param fPMensalOP the fPMensalOP to set
     */
    public void setfPMensalOP(FPMensalOP fPMensalOP) {
        this.fPMensalOP = fPMensalOP;
    }

    /**
     * @return the fPMensal
     */
    public FPMensal getfPMensal() {
        return fPMensal;
    }

    /**
     * @param fPMensal the fPMensal to set
     */
    public void setfPMensal(FPMensal fPMensal) {
        this.fPMensal = fPMensal;
    }

    /**
     * @return the verbas
     */
    public Verbas getVerbas() {
        return verbas;
    }

    /**
     * @param verbas the verbas to set
     */
    public void setVerbas(Verbas verbas) {
        this.verbas = verbas;
    }

    /**
     * @return the fPLancamentos
     */
    public FPLancamentos getfPLancamentos() {
        return fPLancamentos;
    }

    /**
     * @param fPLancamentos the fPLancamentos to set
     */
    public void setfPLancamentos(FPLancamentos fPLancamentos) {
        this.fPLancamentos = fPLancamentos;
    }

    /**
     * @return the fPeriodos
     */
    public FPPeriodos getfPeriodos() {
        return fPeriodos;
    }

    /**
     * @param fPeriodos the fPeriodos to set
     */
    public void setfPeriodos(FPPeriodos fPeriodos) {
        this.fPeriodos = fPeriodos;
    }
}

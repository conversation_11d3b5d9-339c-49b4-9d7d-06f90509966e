/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

/**
 *
 * <AUTHOR>
 */
public class PedidoRefeicaoItens {

    private String Sequencia;
    private String Ordem;
    private String Secao;
    private String QtdeCafe;
    private String QtdeAlmoco;
    private String QtdeJantar;
    private String QtdeCeia;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    
    private String Local;

    public PedidoRefeicaoItens() {
        this.Sequencia = "";
        this.Ordem = "";
        this.Secao = "";
        this.QtdeCafe = "";
        this.QtdeAlmoco = "";
        this.QtdeJantar = "";
        this.QtdeCeia = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
        
        this.Local = "";
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getOrdem() {
        return Ordem;
    }

    public void setOrdem(String Ordem) {
        this.Ordem = Ordem;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getQtdeCafe() {
        return QtdeCafe;
    }

    public void setQtdeCafe(String QtdeCafe) {
        this.QtdeCafe = QtdeCafe;
    }

    public String getQtdeAlmoco() {
        return QtdeAlmoco;
    }

    public void setQtdeAlmoco(String QtdeAlmoco) {
        this.QtdeAlmoco = QtdeAlmoco;
    }

    public String getQtdeJantar() {
        return QtdeJantar;
    }

    public void setQtdeJantar(String QtdeJantar) {
        this.QtdeJantar = QtdeJantar;
    }

    public String getQtdeCeia() {
        return QtdeCeia;
    }

    public void setQtdeCeia(String QtdeCeia) {
        this.QtdeCeia = QtdeCeia;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getLocal() {
        return Local;
    }

    public void setLocal(String Local) {
        this.Local = Local;
    }
}

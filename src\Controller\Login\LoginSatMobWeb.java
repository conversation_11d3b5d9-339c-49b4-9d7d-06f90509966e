package Controller.Login;

import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Filiais;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.KMPrestador;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaLogin;
import SasBeans.PstServ;
import SasBeans.SasPWFill;
import SasBeans.Saspwac;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasBeansCompostas.UsuarioSatMobWebServicos;
import SasDaos.AcessosDao;
import SasDaos.FiliaisDao;
import SasDaos.LoginDao;
import SasDaos.PessoaCliAutDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.PessoaTrajetoDao;
import SasDaos.PstServDao;
import SasDaos.SasPwFilDao;
import SasDaos.SaspwAcDao;
import br.com.sasw.pacotesuteis.sasdaos.compostas.UsuarioSatMobWebServicosDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.EnvioEmail;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LoginSatMobWeb {

    public UsuarioSatMobWebServicos buscarUsuarios(String email, String pwweb, Persistencia persistencia) throws Exception {
        try {
            UsuarioSatMobWebServicosDao usuarioSatMobWebServicosDao = new UsuarioSatMobWebServicosDao();
            List<UsuarioSatMobWebServicos> usuariosSatMobWebServicos = usuarioSatMobWebServicosDao.buscarUsuarios(email, persistencia);
            UsuarioSatMobWebServicos retorno = null;
            for (UsuarioSatMobWebServicos usuarioSatMobWebServicos : usuariosSatMobWebServicos) {
                if (usuarioSatMobWebServicos.getPessoa().getPWWeb().equals(pwweb)) {
                    retorno = usuarioSatMobWebServicos;
                    break;
                }
            }

            if (usuariosSatMobWebServicos.isEmpty()) {
                throw new Exception("login.usuarioerrado");
            }

            if (retorno == null && !usuariosSatMobWebServicos.isEmpty()) {
                throw new Exception("login.senhaerrada");
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("LoginSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("LoginSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<KMPrestador> obterRelatorioKMPrestador(String codPessoa, String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        try {
            PessoaTrajetoDao pessoaTrajetoDao = new PessoaTrajetoDao();
            return pessoaTrajetoDao.obterRotaPrestador(codPessoa, dataInicio, dataFim, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pessoa> listarPrestadoresServicoDatas(String dataIni, String dataFim, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            return pessoaDao.listarPrestadoresServicoDatas(dataIni, dataFim, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }

    public List<PstServ> listarPostosCliente(PessoaCliAut cliente, Boolean excluido, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstServDao = new PstServDao();
            return pstServDao.listarPostosCliente(cliente, excluido, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Valida o Login do Usuário
     *
     * @param Email - Email de login
     * @param PWWeb - Senha de Acesso
     * @param persistencia - Conexão ao banco de dados
     * @return - Lista de empresas que pode acessar
     * @throws Exception
     */
    public List<PessoaLogin> Login(String Email, String PWWeb, Persistencia persistencia) throws Exception {
        try {

//            gerarLog();
            PessoaDao pessoadao = new PessoaDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            BigDecimal CodPessoa = BigDecimal.ZERO;
            String Situacao = "";

            List<PessoaLogin> retorno = new ArrayList();
            boolean senhacerta = false;
            List<Pessoa> lpessoa = pessoadao.BuscaEmail(Email, persistencia);
            if (lpessoa.size() <= 0) {
                throw new Exception("login.usuarioerrado");
            }
            for (Pessoa p : lpessoa) {
                if (PWWeb.equals(p.getPWWeb())) {
                    senhacerta = true;
                    retorno = pessoalogindao.getPessoaLogin(p.getCodigo(), persistencia);
                }

                if (Situacao.equals("")) {
                    Situacao = p.getSituacao();
                }

                if (CodPessoa == BigDecimal.ZERO) {
                    CodPessoa = p.getCodigo();
                }
            }

            if (Situacao.equals("B")) {
                throw new Exception("login.usuariobloqueado");
            } else if (!senhacerta) {
                guardarAcesso(CodPessoa, false, getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
                throw new Exception("login.senhaerrada");
            } else if (retorno.size() <= 0) {
                throw new Exception("login.usuariosemacessocadastrado");
            } else {
                guardarAcesso(CodPessoa, true, getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
                return retorno;
            }
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }

    }

    public List<PessoaLogin> LoginCod(String CodigoPessoa, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            BigDecimal CodPessoa = BigDecimal.ZERO;

            List<PessoaLogin> retorno = new ArrayList();
            retorno = pessoalogindao.getPessoaLogin(CodigoPessoa, persistencia);

            guardarAcesso(CodPessoa, false, getDataAtual("SQL"), getDataAtual("HORA"), persistencia);

            if (retorno.size() <= 0) {
                throw new Exception("login.usuariosemacessocadastrado");
            } else {
                guardarAcesso(CodPessoa, true, getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }

    public void guardarAcesso(BigDecimal CodPessoa, boolean SenhaCorreta, String Dt_Alter, String Hr_Alter, Persistencia persistencia) throws Exception {

        try {
            PessoaDao pessoadao = new PessoaDao();
            pessoadao.guardarAcesso(CodPessoa, SenhaCorreta, Dt_Alter, Hr_Alter, persistencia);

        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista as filiais autorizadas no cliente
     *
     * @param CodPessoa - Código da pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<SasPWFill> SelecionaEmpresa(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            List<SasPWFill> lsaspwfill = saspwfildao.getSasPWFill(CodPessoa, persistencia);
            return lsaspwfill;
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista as filiais autorizadas no cliente
     *
     * @param CodPessoa - Código da pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String consultarIdioma(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.getIdiomaUsuario(CodPessoa.toPlainString(), persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista as filiais autorizadas no cliente
     *
     * @param CodPessoa - Código da pessoa
     * @param Idioma - Código da pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String atualizarIdioma(BigDecimal CodPessoa, String Idioma, Persistencia persistencia) throws Exception {
        try {
            switch (Idioma) {
                case "pt":
                    Idioma = "porBR";
                    break;
                case "en":
                    Idioma = "engUK";
                    break;
                case "es":
                    Idioma = "espES";
                    break;
            }

            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.setIdiomaUsuario(CodPessoa.toPlainString(), Idioma, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista de permissoes do usuário
     *
     * @param CodPessoa - código de pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Saspwac> ListarPermissoes(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SaspwAcDao saspwacdao = new SaspwAcDao();
            return saspwacdao.getSasPwAc(CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhapermissao<message>" + e.getMessage());
        }
    }

    /**
     * Busca os dados do usuário
     *
     * @param CodPessoa - Código da pessoa
     * @param persistencia - conexão ao banco de dados (parametro SATELLITE)
     * @return - Dados: codigo, nome, email
     * @throws Exception
     */
    public Pessoa BuscaPessoa(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            Pessoa pessoa = pessoadao.getPessoaCodigo(CodPessoa, persistencia);
            return pessoa;
        } catch (Exception e) {
            throw new Exception("login.falhabuscausuario<message>" + e.getMessage());
        }
    }

    /**
     * Busca os dados de uma filial
     *
     * @param CodFil - Código da Filial
     * @param CodPessoa
     * @param persistencia - conexão ao banco de dados
     * @return filial
     * @throws Exception
     */
    public SasPWFill BuscaFilial(String CodFil, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFillLogin(CodFil, CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhabuscausuario<message>" + e.getMessage());
        }
    }

    public void senhaDia(String senhaDia, String email, String mapconect, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            List<PessoaLogin> logins;
            SasPoolPersistencia pool = new SasPoolPersistencia();
            pool.setTamanhoPool(20);
            pool.setCaminho(mapconect);
            Boolean trocarsenha = false;
            List<Pessoa> lpessoa = pessoadao.BuscaEmail(email, persistencia);
            if (lpessoa.size() <= 0) {
                throw new Exception("UsuarioNaoEncontrado");
            }
            String senhaNova = String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10));
            for (Pessoa p : lpessoa) {
                logins = pessoalogindao.getPessoaLogin(p.getCodigo(), persistencia);
                if (logins.size() <= 0) {
                    throw new Exception("UsuarioSemAcessos");
                }
                for (PessoaLogin login : logins) {
                    if (login.getNivel() != "5") {
                        throw new Exception("UsuarioSemAutorizacao");
                    }
                    Persistencia pst = new Persistencia(login.getBancoDados());
                    if (null == pst && !trocarsenha) {
                        throw new Exception("ImpossivelConectarBanco");
                    }
                    List<Integer> retorno = new ArrayList<>();
                    retorno.add(0);
                    retorno.add(0);
                    AcessosDao acesso = new AcessosDao();
                    Calendar c = Calendar.getInstance();
                    int trim;
                    if (c.get(Calendar.MONTH) >= 1 && c.get(Calendar.MONTH) < 3) {
                        trim = 1;
                    } else if (c.get(Calendar.MONTH) >= 4 && c.get(Calendar.MONTH) < 6) {
                        trim = 2;
                    } else if (c.get(Calendar.MONTH) >= 7 && c.get(Calendar.MONTH) < 9) {
                        trim = 3;
                    } else {
                        trim = 4;
                    }
                    retorno = acesso.senhaDia(retorno, "A", trim, c.get(Calendar.YEAR), pst);
                    retorno = acesso.senhaDia(retorno, "B", c.get(Calendar.MONTH) + 1, c.get(Calendar.YEAR), pst);
                    retorno = acesso.senhaDia(retorno, "C", c.get(Calendar.DAY_OF_WEEK), c.get(Calendar.YEAR), pst);
                    retorno = acesso.senhaDia(retorno, "D", c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.YEAR), pst);
                    if (senhaDia.equals(retorno.get(0).toString())) {
                        Pessoa pessoa = new Pessoa();
                        pessoa.setOperador("TROCASENHA");
                        pessoa.setPWWeb(senhaNova);
                        pessoa.setDt_Alter(getDataAtual("SQL"));
                        pessoa.setHr_Alter(getDataAtual("HORA"));
                        pessoa.setCodigo(login.getCodPessoaBD());
                        pessoadao.atualizaSenhaSatMob(pessoa, persistencia);
                        pessoa.setCodigo(login.getCodPessoaBD());
                        pessoadao.atualizaSenhaSatMob(pessoa, pst);
                        trocarsenha = true;
                    }
                }
            }
            if (trocarsenha) {
                EnvioEmail.enviaEmailFormatoHtmlSemAnexo("smtplw.com.br", email, lpessoa.get(0).getNome().toUpperCase(),
                        "<EMAIL>", "SatMOB", "NOVA SENHA", mensagem(senhaNova), "Nova senha: " + senhaNova,
                        "sasw", "xNiadJEj9607", 587);
            }
        } catch (Exception e) {
            throw new Exception("login.falhatrocarsenha<message>" + e.getMessage());
        }
    }

    /**
     * Gera uma senha aleatória para o funcionário
     *
     * @param pessoa
     * @param persistencia
     * @return a nova senha
     * @throws Exception
     */
    public String esqueciSenha(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            String senhaNova = String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10))
                    + String.valueOf((int) (Math.random() * 10));
            pessoa.setOperador("ESQUECIPW");
            pessoa.setPWPortal(senhaNova);
            pessoadao.atualizaSenha(pessoa.getPWPortal(), pessoa.getMatr().toBigInteger().toString(), pessoa.getOperador(), persistencia);
            return senhaNova;
        } catch (Exception e) {
            throw new Exception("login.falhatrocarsenha<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> ListarClientes(PessoaCliAut cliente, Boolean excluidos, Persistencia persistencia) throws Exception {
        try {
            PessoaCliAutDao pessoacliautdao = new PessoaCliAutDao();
            return pessoacliautdao.listarClientes(cliente, excluidos, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhalistarclientes<message>" + e.getMessage());
        }
    }

    public String SituacaoUsuario(String email, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acesso = new AcessosDao();
            return acesso.situacaoUsuario(email, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhalistarclientes<message>" + e.getMessage());
        }
    }

    public String SituacaoUsuario(String codFil, String email, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acesso = new AcessosDao();
            return acesso.situacaoUsuario(codFil, email, persistencia);
        } catch (Exception e) {
            throw new Exception("login.falhalistarclientes<message>" + e.getMessage());
        }
    }

    public Boolean VerificaSenha(BigDecimal codPessoa, String senha, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            Pessoa p = pessoadao.getPessoaCodigo(codPessoa, persistencia);
            if (p.getPWWeb().equals(senha)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new Exception("login.falhalistarclientes<message>" + e.getMessage());
        }
    }

    public String mensagem(String novaSenha) {
        String mensagem = "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:600px\">"
                + "	<tbody>"
                + "		<tr>"
                + "			<td bgcolor=\"#e93d3d\">"
                + "			<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "				<tbody>"
                + "					<tr>"
                + "						<td style=\"width:150px;align:left;\"><img "
                + "src=\"http://www.satmob.com.br/imagens/logosatmobmail.png\" border=\"0\" alt=\"0\" style=\"display:block\"></td>"
                + "						<td style=\"width:150px;align:right;\">&nbsp;</td>"
                + "					</tr>"
                + "				</tbody>"
                + "			</table>"
                + "			</td>"
                + "		</tr>"
                + "		<tr>"
                + "			<td>"
                + "			<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "				<tbody>"
                + "					<tr>"
                + "						"
                + "						<td align=\"center\" >"
                + "						<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
                + "style=\"width:255px\">"
                + "							<tbody>"
                + ""
                + "								"
                + "							</tbody>"
                + "						</table>"
                + "						</td>"
                + "					</tr>"
                + "				</tbody>"
                + "			</table>"
                + "			</td>"
                + "		</tr>"
                + "		<tr>"
                + "	"
                + "		</tr>"
                + "		<tr>"
                + "			<td>"
                + "			<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "				<tbody>"
                + "					<tr>"
                + "						<td align=\"center\" >"
                + "						<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
                + "style=\"width:255px\">"
                + "							<tbody>"
                + "								<tr>"
                + "									<td style=\"font-family:Arial;font-size:18px;font-"
                + "weight:bold;text-align:center;color:#284235;\"></td>"
                + "								</tr>"
                + "								<tr>"
                + "									<td align=\"center\" > "
                + "									<table align=\"center\" border=\"0\" cellpadding=\"0\" "
                + "cellspacing=\"0\" style=\"width:680px\">"
                + "										<tbody>"
                + "											<tr>"
                + "												<td style=\"font-family: "
                + "tahoma; font-size: 15px; color: #284235; padding-top: 10px; padding-bottom: 10px; text-align: center;\">"
                + "												<span style=\"text-align: "
                + "center\"><strong>Sua nova senha é: </strong></span>										"
                + "		<p>"
                + "												  "
                + "										      "
                + novaSenha
                + "</td>"
                + "											</tr>"
                + "											<tr>"
                + "												"
                + "											</tr>"
                + "										</tbody>"
                + "									</table>"
                + "									</td>"
                + "								</tr>"
                + "							</tbody>"
                + "						</table>"
                + "						</td>"
                + "					</tr>"
                + "				</tbody>"
                + "			</table>"
                + "			</td>"
                + "		</tr>"
                + "		<tr>"
                + "			<td bgcolor=\"#e93d3d\" style=\"padding:20px\" align=\"center\" >"
                + "			<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:575px\">"
                + "				<tbody>"
                + "					<tr>"
                + "						<td style=\"color:#164b6b;font-family:tahoma;font-weight:bold;font-"
                + "size:22px;text-align:left;\">&nbsp;</td>"
                + "						<td style=\"color:#fff;font-family:tahoma;font-weight:bold;font-"
                + "size:22px;text-align:right;\">web.satmob.com.br</td>"
                + "						<tr>"
                + "	"
                + ""
                + "				</tbody>"
                + "			</table>"
                + "			</td>"
                + "		</tr>"
                + "		"
                + "		<tr>"
                + "			<td bgcolor=\"#ffffff\" style=\"padding:10px\" align=\"center\" >"
                + "			<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:575px\">"
                + "				<tbody>"
                + "					<tr>"
                + "						<td style=\"backpadding-top:5px;color:#164b6b;font-family:tahoma;font-"
                + "size:10px;text-align:right;\">Powered by Satellite | www.gruposas.com.br</td>"
                + ""
                + "						<tr>"
                + ""
                + "				</tbody>"
                + "			</table>"
                + "			</td>"
                + "		</tr>"
                + "				"
                + "	</tbody>"
                + "</table>";
        return mensagem;
    }

    /**
     * ******************************************** MODULO RH
     *
     * @param matr
     * @param senha
     * @param persistencia
     * @param satellite
     * @return
     * @throws java.lang.Exception *********************************************
     */
    public UsuarioSatMobWeb loginRH(String matr, String senha, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            LoginDao logindao = new LoginDao();
            PessoaLoginDao pldao = new PessoaLoginDao();
            List<PessoaLogin> pl;
            UsuarioSatMobWeb login = logindao.NovoPortalRH(matr, persistencia);
            Boolean senhaerrada = false;
            // Caso exista alguma pessoa com a matricula inserida, faz algumas verificacoes
            if (null != login) {
                /**
                 * Validação de senha correta diferente para Servite/Interfort A
                 * senha do funcionário é o próprio CPF.
                 */
                /*String senhaCorreta = (persistencia.getEmpresa().contains("SERVITE") || persistencia.getEmpresa().contains("INTERFORT")
                        || persistencia.getEmpresa().contains("TRANSPORTER"))
                        ? login.getPessoa().getCPF() : login.getPessoa().getPWPortal();*/
                // Se a senha estiver correta, verifica se a pessoa já existe na base central.
                if (senha.equals(login.getPessoa().getPWPortal())
                        || senha.equals(login.getPessoa().getPWWeb())
                        ) {
                    // Se a pessoa existir na base central, busca pelo login na tabela pessoalogin
                    if (login.getPessoa().getCodPessoaWEB() != null) {
                        pl = pldao.getPessoaLogin(login.getPessoa().getCodPessoaWEB(), satellite);
//                        for (PessoaLogin p : pl) {
//                            // Se houver algum login nivel 4, libera o acesso.
//                            if (p.getNivel().equals("4")
//                                    || p.getNivel().equals("8")
//                                    //                                     || p.getNivel().equals("3")
//                                    || p.getNivel().equals("9")) {
//                                login.getSaspw().setNivelx("4");
//                                return login;
//                            }
//                        }
                        // Caso não exista nenhum login, insere um novo login;
                        if (pl.isEmpty()) {
                            PessoaLogin p = new PessoaLogin();
                            p.setBancoDados(persistencia.getEmpresa());
                            p.setCodigo(login.getPessoa().getCodPessoaWEB());
                            p.setCodPessoaBD(login.getPessoa().getCodigo());
                            p.setNivel("4");
                            pldao.gravaPessoaLogin(p, satellite);
                        } else {
                            return login;
//                            throw new Exception("login.metodoerrado");
                        }
                        // Se a pessoa não existir na base central, adiciona ela na base central e cria um novo login.
                    } else {
                        PessoaDao pessoadao = new PessoaDao();
                        Pessoa p = new Pessoa();
                        p.setCodigo(pessoadao.getCodigo(satellite));
                        p.setNome(login.getPessoa().getNome());
                        p.setCPF(login.getPessoa().getCPF());
                        p.setRG(login.getPessoa().getRG());
                        p.setMatr(login.getPessoa().getMatr());
                        p.setCidade(login.getPessoa().getCidade());
                        p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                        p.setHr_Alter(DataAtual.getDataAtual("HORA"));
                        pessoadao.InserirPessoaSatMobWeb(p, satellite);

                        p.setCodPessoaWEB(p.getCodigo());
                        p.setPWPortal(senha);
                        p.setCodigo(login.getPessoa().getCodigo());
                        pessoadao.AtualizaPessoaSatMobRH(p, persistencia);

                        PessoaLogin pp = new PessoaLogin();
                        pp.setBancoDados(persistencia.getEmpresa());
                        pp.setCodigo(p.getCodPessoaWEB());
                        pp.setCodPessoaBD(login.getPessoa().getCodigo());
                        pp.setNivel("4");
                        pldao.gravaPessoaLogin(pp, satellite);

                        login.getPessoa().setCodPessoaWEB(p.getCodPessoaWEB());
                        return login;
                    }
                } else {
                    senhaerrada = true;
                }
            }
            if (senhaerrada) {
                throw new Exception("login.senhaerrada");
            }
            return null;
        } catch (Exception e) {
            throw new Exception("login.falhageral<message>" + e.getMessage());
        }
    }
}

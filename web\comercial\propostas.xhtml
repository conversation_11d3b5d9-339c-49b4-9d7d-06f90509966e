<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/propostas.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{propostas.Persistencias(login.pp)}"/>
            </f:metadata>
            <p:growl id="msgs"/>
            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div class="ui-grid-col-12">
                                    <img src="../assets/img/icone_satmob_propostas.png" height="40" width="40"/>
                                    #{localemsgs.Propostas}
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{propostas.nomeFilial}
                                    </div>
                                    <div class="ui-grid-col-8">
                                        #{localemsgs.QtdPropostas}: #{propostas.total}
                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="a" action="#{propostas.preCadastro}" oncomplete="PF('dlgCadastrar').show();" update="formCadastrar msgs"/> 
                    <p:hotkey bind="p" action="#{propostas.PreCadastrar}" update="formPesquisar msgs" oncomplete="PF('dlgPesquisar').show()"/> 
                    <p:hotkey bind="e" action="#{propostas.preEdicao}" update="formCadastrar msgs"/> 
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline;">
                                    <p:dataTable id="tabela" value="#{propostas.allPropostas}" paginator="true" rows="15" lazy="true"
                                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Propostas}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista" rowKey="#{lista.numero}" selection="#{propostas.novaProposta}" selectionMode="single"
                                                 resizableColumns="true" styleClass="tabela"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 scrollable="true" scrollWidth="100%"
                                                 style="font-size: 12px; background: white">                                    
                                        <p:column headerText="#{localemsgs.CodFil}" style="width: 49px">
                                            <h:outputText value="#{lista.codFil}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Numero}" class="celula-right" style="width: 60px">
                                            <h:outputText value="#{lista.numero}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Data}" style="width: 80px">
                                            <h:outputText value="#{lista.data}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Situacao}" style="width: 145px">
                                            <h:outputText value="#{lista.nomeSituacao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Cliente}" style="width: 180px">
                                            <h:outputText value="#{lista.nomeContato}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Descricao}" style="width: 290px">
                                            <h:outputText value="#{lista.descricao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Referencia}" style="width: 170px">
                                            <h:outputText value="#{lista.refProp}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Contato}" style="width: 122px">
                                            <h:outputText value="#{lista.contato}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Consultor}" style="width: 170px">
                                            <h:outputText value="#{lista.nomeConsultor}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.DtValidade}" style="width: 120px">
                                            <h:outputText value="#{lista.dtValidade}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Validade}" style="width: 115px">
                                            <h:outputText value="#{lista.validade}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" style="width: 100px">
                                            <h:outputText value="#{lista.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 49px">
                                            <h:outputText value="#{lista.hr_Alter}"/>  
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 84px">
                                            <h:outputText value="#{lista.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panel>
                            </div>
                        </div>
                    </div>
                    <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" action="#{propostas.preCadastro}"
                                           oncomplete="PF('dlgCadastrar').show();"
                                           update="formCadastrar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" action="#{propostas.preEdicao}"
                                           update="formCadastrar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" action="#{propostas.PreCadastrar}"
                                           update="formPesquisar msgs" oncomplete="PF('dlgPesquisar').show()">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style=" top: 0px; right: 5px; position: fixed">
                            <p:commandLink title="#{localemsgs.Voltar}"
                                           action="#{login.voltar}">
                                <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                    </p:panel>
                </h:form>

                <!-- Cadastrar novo -->
                <h:form id="formCadastrar" class="form-inline">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" focus="cliente" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formCadastrar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });

                            });
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_propostas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarProposta}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-8,ui-grid-col-4" id="panelCentral"
                                     layout="grid" styleClass="ui-panelgrid-blank cadastrar">
                            <p:scrollPanel mode="native" id="cadastrar" style="background-color: transparent; height: 485px">
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                </p:confirmDialog>
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="codfil" value="#{localemsgs.CodFil}: "  />
                                    <p:selectOneMenu id="codfil" value="#{propostas.novaProposta.codFil}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                     styleClass="filial" style="width: 100%"
                                                     filter="true" filterMatchMode="contains" disabled="#{propostas.flag eq 2}">
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                    </p:selectOneMenu>                               
                                </p:panelGrid>

                                <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-4,ui-grid-col-5" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="cliente" value="#{localemsgs.Cliente}: "/>
                                    <p:autoComplete id="cliente" required="true" class="cliente"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}"
                                                    maxlength="60" scrollHeight="200" 
                                                    var="cont" itemValue="#{cont}" itemLabel="#{cont.nome}"
                                                    value="#{propostas.contato}" style="width: 100%"
                                                    completeMethod="#{propostas.buscarContatos}" forceSelection="true" >
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{propostas.contatos}"/>
                                        <p:ajax event="itemSelect" listener="#{propostas.selecionarContato}"
                                                update="infos formCadastrar:fantasiaContato formCadastrar:contato formCadastrar:email"/>
                                        <p:watermark for="cliente" value="#{localemsgs.Cliente}" />
                                    </p:autoComplete>

                                    <p:inputText id="fantasiaContato" value="#{propostas.contato.fantasia}" disabled="true" style="width: 100%"/>
                                </p:panelGrid>

                                <p:panelGrid columns="1" columnClasses="ui-grid-col-12" 
                                             layout="grid" styleClass="ui-panelgrid-blank" id="infos">
                                    <p:inputText value="#{propostas.enderecoContato}" disabled="true" style="width: 100%"/>
                                    <p:inputText value="#{propostas.foneContato}" disabled="true" style="width: 100%"/>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="contato" value="#{localemsgs.Contato}: "  />
                                    <p:inputText id="contato" value="#{propostas.contato.contato}" style="width: 100%"/>

                                    <p:outputLabel for="email" value="#{localemsgs.Email}: "  />
                                    <p:inputText id="email"  value="#{propostas.contato.email}" style="width: 100%"/>

                                    <p:outputLabel for="sexo" value="#{localemsgs.Sexo}: "  />
                                    <p:selectOneRadio id="sexo" value="#{propostas.novaProposta.sexo}">
                                        <f:selectItem itemLabel="#{localemsgs.Masculino}" itemValue="M" />
                                        <f:selectItem itemLabel="#{localemsgs.Feminino}" itemValue="F" />
                                        <f:selectItem itemLabel="#{localemsgs.Outro}" itemValue="O" />
                                    </p:selectOneRadio>

                                    <p:outputLabel for="desc" value="#{localemsgs.Descricao}: " />
                                    <p:inputText id="desc"  value="#{propostas.novaProposta.descricao}" style="width: 100%"/>

                                    <p:outputLabel for="ref" value="#{localemsgs.Referencia}: "/>
                                    <p:autoComplete id="ref" class="cliente"
                                                    maxlength="60" scrollHeight="200" 
                                                    var="refs" itemValue="#{refs}" itemLabel="#{refs}"
                                                    value="#{propostas.novaProposta.refProp}" style="width: 100%"
                                                    completeMethod="#{propostas.buscarReferencias}" forceSelection="false" >
                                        <p:watermark for="ref" value="#{localemsgs.Referencia}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:separator />

                                <p:panel style="background: transparent">
                                    <img src="../assets/img/icone_satmob_produtosG.png" height="30" width="30"/>
                                    <p:spacer width="5px"/>
                                    <h:outputText value="#{localemsgs.Produtos}" style="font-size: 14px; font-weight: bold"/>
                                </p:panel>
                                <p:spacer width="5px"/>
                                <p:panel id="tabsProdutos" style="background: transparent;width: 100%; display: grid">
                                    <p:panel id="produtos" style="background: transparent;width: calc(50% - 25px); float: left; height: 100%">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6 sem-padding" 
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background: transparent">
                                            <p:outputLabel for="globalFilter" value="#{localemsgs.Buscar}: " />
                                            <p:inputText id="globalFilter" value="#{propostas.queryProduto}"
                                                         style="width:100%"
                                                         placeholder="#{localemsgs.Produto}">
                                                <p:ajax event="keyup" listener="#{propostas.buscarProdutos}" update="formCadastrar:todosProdutos"/>
                                            </p:inputText>
                                        </p:panelGrid>
                                        <p:dataTable id="todosProdutos" var="todosProdutos" value="#{propostas.allProdutos}" styleClass="produtos"
                                                     paginator="true" rows="7" lazy="true" selectionMode="single" selection="#{propostas.produtoSelecionado}"
                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {NextPageLink} {LastPageLink}"
                                                     paginatorPosition="bottom" rowKey="#{todosProdutos.codigo}" scrollable="true" scrollHeight="200">
                                            <p:ajax event="rowSelect" listener="#{propostas.selecionarProduto}"/>
                                            <p:column style="text-align: center" headerText="#{localemsgs.Descricao}">
                                                <h:outputText value="#{todosProdutos.descricao}" id="nomeProduto" style="#{todosProdutos.indServico eq 'S' ? 'color: red' : 'color: black'}"/>
                                                <p:draggable for="nomeProduto" revert="true" helper="clone" />
                                            </p:column>
                                            <p:column style="text-align: center; width: 50px" headerText="#{localemsgs.Preco}">
                                                <h:outputText value="#{todosProdutos.precoVenda}" converter="conversormoeda" id="precoProduto" style="color: #{todosProdutos.indServico eq 'S' ? red : black}"/>
                                                <p:draggable for="precoProduto" revert="true" helper="clone" />
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>

                                    <p:panel id="addSub" style="background: transparent; text-align: center; width: 40px; float: left; top: 120px; position: relative;">
                                        <p:commandButton  id="btnAdicionar" actionListener="#{propostas.adicionarNovoProduto}"
                                                          update="formCadastrar:produtosProposta msgs formCadastrar:valorAcumulado formCadastrar:resumo"
                                                          icon="ui-icon-circle-plus" title="#{localemsgs.Adicionar}" process="@this" />
                                        <p:commandButton  id="btnRemover" actionListener="#{propostas.retirarNovoProduto}"
                                                          update="formCadastrar:produtosProposta msgs formCadastrar:valorAcumulado formCadastrar:resumo"
                                                          icon="ui-icon-circle-minus" title="#{localemsgs.Adicionar}" process="@this" />
                                    </p:panel>

                                    <p:panel id="produtosSelecionados" style="background: transparent; height:100%;width: calc(50% - 25px); float: left;">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6 sem-padding " 
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background: transparent">
                                            <p:outputLabel for="valorAcumulado" value="#{localemsgs.Valor}: " />
                                            <p:inputText id="valorAcumulado" value="#{propostas.novaProposta.valorProd}" class="text-right"
                                                         style="width:100%" readonly="true" converter="conversormoeda"/>
                                        </p:panelGrid>
                                        <p:dataTable id="produtosProposta" var="produtoProposta" value="#{propostas.produtosProposta}"
                                                     styleClass="produtos produtos" scrollable="true" scrollHeight="233"
                                                     rowKey="#{produtoProposta.id}" emptyMessage="#{localemsgs.ArrasteOuAdicione}"
                                                     selection="#{propostas.produtoPropostaSelecionado}" selectionMode="single">
                                            <f:facet name="header">
                                                <h:outputText value="#{localemsgs.ProdutosSelecionados}"/>
                                            </f:facet>
                                            <p:ajax event="rowSelect" listener="#{propostas.selecionarProdutoProposta}"/>
                                            <p:column style="width:23px">
                                                <p:rowToggler />
                                            </p:column>
                                            <p:column style="text-align: center">
                                                <h:outputText id="nomeProduto" value="#{produtoProposta.produto.descricao}"/>
                                            </p:column>

                                            <p:rowExpansion>
                                                <p:panel rendered="#{produtoProposta.produto.indServico eq 'S'}">

                                                </p:panel>

                                                <!-- Serviço legado -->
                                                <p:panel rendered="#{produtoProposta.produto.indServico}">
                                                    <h:outputText value="#{localemsgs.Itens}" rendered="#{not produtoProposta.propServItens.isEmpty()}"/>
                                                    <h:outputText value="#{localemsgs.SemItens}" rendered="#{produtoProposta.propServItens.isEmpty()}"/>
                                                    <p:separator rendered="#{not produtoProposta.propServItens.isEmpty()}"/>
                                                    <ui:repeat value="#{produtoProposta.propServItens}" var="item">
                                                        <p:panelGrid  columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7">
                                                            <h:outputText value="#{localemsgs.Descricao}: " />
                                                            <h:outputText value="#{item.descricao}" />

                                                            <h:outputText value="#{localemsgs.Preco}: " />
                                                            <h:outputText value="#{item.valor}"/>
                                                        </p:panelGrid>
                                                        <p:panelGrid  columns="2" columnClasses="ui-grid-col-7,ui-grid-col-5">
                                                            <h:outputText value="#{localemsgs.Faturamento}: " />
                                                            <h:outputText value="#{item.tipoCalc}"/>
                                                        </p:panelGrid>
                                                        <p:separator />
                                                    </ui:repeat>
                                                </p:panel>

                                                <p:panel rendered="#{produtoProposta.produto.indServico ne 'S' or produtoProposta.produto.indServico ne '*'}">

                                                </p:panel>


                                            </p:rowExpansion>
                                        </p:dataTable>

                                        <p:droppable for="produtosSelecionados" tolerance="pointer" 
                                                     activeStyleClass="drag"
                                                     datasource="todosProdutos">
                                            <p:ajax listener="#{propostas.onSelecionarProduto}" update="produtosProposta formCadastrar:valorAcumulado formCadastrar:resumo" />
                                        </p:droppable>
                                    </p:panel>
                                </p:panel>

                                <p:separator />

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="prazo" value="#{localemsgs.PrazoEntregaImplantacao}: "  />
                                    <p:inputText id="prazo" value="#{propostas.novaProposta.prazoEntrega}" style="width: 100%"/>

                                    <p:outputLabel for="garantia" value="#{localemsgs.Garantia}: "  />
                                    <p:inputText id="garantia"  value="#{propostas.novaProposta.garantia}" style="width: 100%"/>

                                    <p:outputLabel for="validade" value="#{localemsgs.Validade}: " />
                                    <p:inputText id="validade"  value="#{propostas.novaProposta.validade}" style="width: 100%"/>
                                </p:panelGrid>

                                <p:separator />

                                <p:panel style="background: transparent">
                                    <img src="../assets/img/icone_satmob_produtosP.png" height="30" width="30"/>
                                    <p:spacer width="5px"/>
                                    <h:outputText value="#{localemsgs.CondicoesPagamento}" style="font-size: 14px; font-weight: bold"/>
                                </p:panel>

                                <p:panelGrid id="panelCondicoes" columns="3" columnClasses="ui-grid-col-2 text-right,ui-grid-col-9,ui-grid-col-1" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <h:outputText value="#{localemsgs.Opcao}"/>
                                    <h:outputText value="#{localemsgs.Descricao}"/>
                                    <p:spacer width="1"/>

                                    <p:inputText value="#{propostas.condicao.opcao}" style="width: 100%"/>
                                    <p:inputText value="#{propostas.condicao.descricao}" style="width: 100%"/>
                                    <p:commandLink title="#{localemsgs.Adicionar}" action="#{propostas.adicionarCondicao}" update="panelCondicoes formCadastrar:resumo">
                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" style="width: 100%"/>
                                    </p:commandLink>

                                    <c:forEach items="#{propostas.condicoes}" var="cond">
                                        <h:outputText value="#{cond.opcao}"/>
                                        <h:outputText value="#{cond.descricao}"/>
                                        <p:commandLink title="#{localemsgs.Remover}" action="#{propostas.removerCondicao(cond)}" update="panelCondicoes formCadastrar:resumo">
                                            <p:graphicImage url="../assets/img/icone_redondo_excluir.png" style="width: 100%"/>
                                        </p:commandLink>
                                    </c:forEach>
                                </p:panelGrid>

                                <p:separator />

                            </p:scrollPanel>
                            <p:panel id="resumo" style="background: transparent" class="borda">
                                <f:facet name="header">
                                    <img src="../assets/img/icone_satmob_rendimentos_G.png" height="30" width="30"/>
                                    <p:spacer width="5px"/>
                                    <h:outputText value="#{localemsgs.ResumoProposta}" style="font-size: 14px; font-weight: bold"/>
                                </f:facet>
                                <p:scrollPanel mode="native" style="height: 400px; border: 1px solid #dddddd !important">
                                    <h:outputText value="#{localemsgs.Produtos.toUpperCase()}" rendered="#{not empty propostas.produtosProposta}"
                                                  style="font-weight: bold; font-size: 1.1em"/>
                                    <br/>
                                    <c:forEach var="select" items="#{propostas.produtosProposta}" varStatus="loop">
                                        <h:outputText value="#{loop.index+1}. " style="font-weight: bold"/>
                                        <h:outputText value="#{select.produto.descricao}" style="font-weight: bold"/>
                                        <p:panelGrid columnClasses="ui-grid-col-8, ui-grid-col-4 text-right" style="width: 100%; font-size: 0.9em"
                                                     columns="2" layout="grid">
                                            <h:outputText value="#{localemsgs.Quantidade}:"/>
                                            <h:outputText value="#{select.produto.qtde.toBigInteger()}"/>

                                            <h:outputText value="#{localemsgs.Preco}:" rendered="#{select.produto.indServico ne 'S'}"/>
                                            <h:outputText value="#{select.produto.precoVenda}" converter="conversormoeda" rendered="#{select.produto.indServico ne 'S'}"/>

                                            <h:outputText value="#{localemsgs.Preco}:" rendered="#{select.produto.indServico eq 'S'}"/>
                                            <h:outputText value="#{select.produto.precoCusto}" converter="conversormoeda" rendered="#{select.produto.indServico eq 'S'}"/>

                                            <h:outputText value="#{localemsgs.PrecoAjustado}:" rendered="#{select.produto.indServico eq 'S'}"/>
                                            <h:outputText value="#{select.produto.precoVenda}" converter="conversormoeda" rendered="#{select.produto.indServico eq 'S'}"/>

                                            <h:outputText value="#{localemsgs.CustosAdicionaisDescontos}:" style="font-size: 0.7em" rendered="#{select.produto.indServico eq 'S'}"/>
                                            <h:outputText value="#{select.produto.margem}" rendered="#{select.produto.indServico eq 'S'}"/>
                                        </p:panelGrid>
                                        <p:panelGrid columnClasses="ui-grid-col-8, ui-grid-col-4 text-right" style="width: 100%; font-size: 0.9em;
                                                     border-top: 1px solid #dddddd"
                                                     columns="2" layout="grid">
                                            <h:outputText value="#{localemsgs.Subtotal}:"/>
                                            <h:outputText value="#{select.produto.precoCusto.multiply(select.produto.qtde).multiply(select.produto.margem)}" converter="conversormoeda" rendered="#{select.produto.precoCusto gt 0}"/>
                                            <h:outputText value="#{select.produto.precoVenda.multiply(select.produto.qtde).multiply(select.produto.margem)}" converter="conversormoeda" rendered="#{select.produto.precoCusto.intValue() eq 0}"/>
                                        </p:panelGrid>
                                        <br/>
                                    </c:forEach>
                                    <h:outputText value="#{localemsgs.CondicoesPagamento.toUpperCase()}" rendered="#{not empty propostas.condicoes}"
                                                  style="font-weight: bold; font-size: 1.1em"/>
                                    <br/>
                                    <c:forEach items="#{propostas.condicoes}" var="condRes">
                                        <p:panelGrid columnClasses="ui-grid-col-2 text-right, ui-grid-col-10" style="width: 100%; font-size: 0.9em"
                                                     columns="2" layout="grid">
                                            <h:outputText value="#{condRes.opcao}"/>
                                            <h:outputText value="#{condRes.descricao}"/>
                                        </p:panelGrid>
                                    </c:forEach>
                                </p:scrollPanel>
                                <f:facet name="footer" class="borda">
                                    <p:panelGrid columnClasses="ui-grid-col-6,ui-grid-col-6 text-right" columns="2" layout="grid">
                                        <h:outputText value="#{localemsgs.Total}: "/>
                                        <h:outputText value="#{propostas.novaProposta.valorProd}" converter="conversormoeda"/>
                                    </p:panelGrid>
                                </f:facet>
                            </p:panel>
                        </p:panelGrid>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 15%; float: left">  
                                <p:commandLink rendered="#{propostas.flag eq 1}" id="cadastro" update=" :main:tabela :msgs :cabecalho"
                                               title="#{localemsgs.Cadastrar}" action="#{propostas.cadastrar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink rendered="#{propostas.flag eq 2}" id="edit" update=":msgs :main:tabela :cabecalho"
                                               title="#{localemsgs.Editar}" action="#{propostas.editar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:blockUI block="formCadastrar:panelCentral" trigger="formCadastrar:cadastro"/>
                                <p:blockUI block="formCadastrar:panelCentral" trigger="formCadastrar:edit"/>
                            </div>
                            <div style="width: 15%; float: left">
                                <p:commandLink oncomplete="PF('dlgCadastrar').hide()" id="btnFecharComImagem">
                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}"
                                               icon="ui-icon-alert"/>
                                    <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                                </p:commandLink>  
                            </div>
                            <div style="width: 15%; float: left">
                                <p:commandLink oncomplete="PF('dlgSelecionarModeloProposta').show()" update="formCadastrar:propcmlmod" id="btnGerarProposta">
                                    <p:graphicImage url="../assets/img/icone_satmob_exportar.png" width="40" height="40" />
                                </p:commandLink>  
                            </div>
                        </div>
                    </p:dialog>

                    <p:dialog widgetVar="dlgSelecionarModeloProposta" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                              style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_propostas_P.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.ExportarProposta}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="propcmlmod" style="background: transparent">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" 
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="modelo" value="#{localemsgs.Modelo}: "/>
                                <p:selectOneMenu id="modelo" value="#{propostas.modelo}" 
                                                 styleClass="filial" filter="true" filterMatchMode="contains"
                                                 style="width: 100%"  converter="omnifaces.SelectItemsConverter">
                                    <f:selectItems value="#{propostas.modelos}" var="mods" itemValue="#{mods}"
                                                   itemLabel="#{mods.descricao}"/>
                                    <p:ajax event="itemSelect" listener="#{propostas.selecionarModelo}"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <div class="form-inline">
                                <div style="width: 15%; float: left">
                                    <p:commandLink id="gerar" action="#{propostas.gerarProposta}"
                                                   update="msgs main:tabela" ajax="false" target="_blank"
                                                   title="#{localemsgs.Exportar}">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                                <div style="width: 15%; float: left">
                                    <p:commandLink oncomplete="PF('dlgSelecionarModeloProposta').hide()" id="btnFecharComImagem2">
                                        <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                                    </p:commandLink>  
                                </div>
                            </div>            
                        </p:panel>
                    </p:dialog>             
                </h:form>

                <!--Pesquisar propostas-->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="  background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_propostas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarContato}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil" value="#{propostas.novoContato.codFil}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div> 

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nome" value="#{localemsgs.Nome}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome" value="#{propostas.novoContato.nome}" label="#{localemsgs.Nome}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nred" value="#{localemsgs.NomeFantasia}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nred" value="#{propostas.novoContato.fantasia}" label="#{localemsgs.NomeFantasia}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nred" value="#{localemsgs.NomeFantasia}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="tpcli" value="#{localemsgs.TpCli}: " style="display:flex;justify-content:center;align-items:center;"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="tpcli" value="#{propostas.novoContato.tpCli}"
                                                     styleClass="filial" style="width: 100%"
                                                     filter="true" filterMatchMode="contains">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Agencia}" itemValue="0"/>
                                        <f:selectItem itemLabel="#{localemsgs.PAB}" itemValue="3"/>
                                        <f:selectItem itemLabel="#{localemsgs.TA}" itemValue="4"/>
                                        <f:selectItem itemLabel="#{localemsgs.TAC}" itemValue="5"/>
                                        <f:selectItem itemLabel="#{localemsgs.Cliente}" itemValue="6"/>
                                        <f:selectItem itemLabel="#{localemsgs.Tesouraria}" itemValue="7"/>
                                        <f:selectItem itemLabel="#{localemsgs.OutrasTransp}" itemValue="8"/>
                                        <f:selectItem itemLabel="#{localemsgs.ATM}" itemValue="9"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="situacao" value="#{localemsgs.Situacao}: " />
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="situacao" value="#{propostas.novoContato.situacao}"
                                                     styleClass="filial" style="width: 100%"
                                                     filter="true" filterMatchMode="contains">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItems value="#{propostas.sit}" var="situacoes" itemValue="#{situacoes.codigo}"
                                                       itemLabel="#{situacoes.descricao}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{propostas.PesquisaPaginada}" oncomplete="PF('dlgPesquisar').hide()"
                                               update=" :main:tabela :msgs :cabecalho corporativo"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>            
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>
            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>

                    <p:ajaxStatus class="status" >
                        <f:facet name="start">
                            <p:graphicImage value="../assets/img/ajax-loader.gif" style="height: 20px; width: 20px"/>
                        </f:facet>
                    </p:ajaxStatus>
                </div>
                <div class="footer-body" id="footer-body">
                    <div>
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.Corporativo}: " />
                            <p:selectBooleanCheckbox value="#{propostas.mostrarFiliais}">
                                <p:ajax update="msgs main:tabela cabecalho:status corporativo" listener="#{propostas.mostrarTodasFiliais}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.LimparFiltros}: " />
                            <p:selectBooleanCheckbox value="#{propostas.limparFiltros}">
                                <p:ajax update="msgs main:tabela cabecalho:status corporativo" listener="#{propostas.LimparFiltros}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

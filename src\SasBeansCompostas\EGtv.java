/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

/**
 *
 * <AUTHOR>
 */
public class EGtv {

    private String Data;
    private String Pedido;
    private String Guia;
    private String Serie;
    private String Operacao;
    private String NRedFat;
    private String CodCli1;
    private String LocalParada;
    private String AgenciaParada;
    private String SubAgenciaParada;
    private String HrCheg;
    private String HrSaida;
    private String Destino;
    private String AgenciaDestino;
    private String SubAgenciaDestino;
    private String Entrega;
    private String Valor;
    private String Volumes;
    private String Volume1;
    private String Assinatura;
    private String AssinaturaDestino;
    private String ParadaD;
    private String OS;
    private String Viacxf;
    private String DtEntCxf;
    private String HrEntCxf;
    private String DtSaiCxf;
    private String DtEntTes;
    private String HrEntTes;
    private String DtSaiTes;
    private String HrSaiTes;

    private String PreOrderOBS;

    private String codFil;
    private String sequencia;
    private String parada;
    private String rota;

    private String DtEnt;
    private String Vol;
    private String Lacre;
    private String Numero;
    private String OSRtg;
    private String DtColeta;
    private String AgSB;
    private String Origem;
    private String DtEntrega;
    private String AgSBD;
    private String HrPrg;
    private String HrCheg_E;
    private String HrCheg_S;
    private String sequencia_E;
    private String parada_E;

    private String ClassCelula;
    private String ClassIcon;
    private String Status;

    private String ER;

    private String razaoSocial;
    private String enderecoFilial;
    private String bairroFilial;
    private String cidadeFilial;
    private String ufFilial;
    private String cepFilial;
    private String cnpjFilial;
    private String foneFilial;

    private String nRedFat;
    private String codCliFat;
    private String nomeFat;
    private String endFat;
    private String cidadeFat;
    private String bairroFat;
    private String estadoFat;
    private String cgcFat;
    private String ieFat;
    private String emailFat;

    private String nRedOri;
    private String nomeOri;
    private String registroOri;
    private String endOri;
    private String cidadeOri;
    private String bairroOri;
    private String estadoOri;
    private String emailOri;
    private String veiculoOri;
    private String rotaOri;
    private String coletaOri;
    private String CCustoOS;

    private String nRedDst;
    private String nomeDst;
    private String registroDst;
    private String codCliDst;
    private String endDst;
    private String cidadeDst;
    private String bairroDst;
    private String estadoDst;
    private String emailDst;
    private String veiculoDst;
    private String rotaDst;
    private String coletaDst;

    private String moeda;
    private String Obs;
    private String TipoSrv;

    private String GTVeProtocolo;
    private String GTVeChave;
    private String GTVeLink;
    private String GTVeData;
    private String GTVeHora;
    
    private String Operador;
    
    private String StatusGuia;
    
    private String PedidoCli;

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getPedido() {
        return Pedido;
    }

    public void setPedido(String Pedido) {
        this.Pedido = Pedido;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getOperacao() {
        return Operacao;
    }

    public void setOperacao(String Operacao) {
        this.Operacao = Operacao;
    }

    public String getNRedFat() {
        return NRedFat;
    }

    public void setNRedFat(String NRedFat) {
        this.NRedFat = NRedFat;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getLocalParada() {
        return LocalParada;
    }

    public void setLocalParada(String LocalParada) {
        this.LocalParada = LocalParada;
    }

    public String getHrCheg() {
        return HrCheg;
    }

    public void setHrCheg(String HrCheg) {
        this.HrCheg = HrCheg;
    }

    public String getHrSaida() {
        return HrSaida;
    }

    public void setHrSaida(String HrSaida) {
        this.HrSaida = HrSaida;
    }

    public String getDestino() {
        return Destino;
    }

    public void setDestino(String Destino) {
        this.Destino = Destino;
    }

    public String getEntrega() {
        return Entrega;
    }

    public void setEntrega(String Entrega) {
        this.Entrega = Entrega;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getVolumes() {
        return Volumes;
    }

    public void setVolumes(String Volumes) {
        this.Volumes = Volumes;
    }

    public String getVolume1() {
        return Volume1;
    }

    public void setVolume1(String Volume1) {
        this.Volume1 = Volume1;
    }

    public String getPreOrderOBS() {
        return PreOrderOBS;
    }

    public void setPreOrderOBS(String PreOrderOBS) {
        this.PreOrderOBS = PreOrderOBS;
    }

    public String getAssinatura() {
        return Assinatura;
    }

    public void setAssinatura(String Assinatura) {
        this.Assinatura = Assinatura;
    }

    public String getAssinaturaDestino() {
        return AssinaturaDestino;
    }

    public void setAssinaturaDestino(String AssinaturaDestino) {
        this.AssinaturaDestino = AssinaturaDestino;
    }

    public String getParadaD() {
        return ParadaD;
    }

    public void setParadaD(String ParadaD) {
        this.ParadaD = ParadaD;
    }
    
    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getViacxf() {
        return Viacxf;
    }

    public void setViacxf(String Viacxf) {
        this.Viacxf = Viacxf;
    }

    public String getDtEntCxf() {
        return DtEntCxf;
    }

    public void setDtEntCxf(String DtEntCxf) {
        this.DtEntCxf = DtEntCxf;
    }

    public String getHrEntCxf() {
        return HrEntCxf;
    }

    public void setHrEntCxf(String HrEntCxf) {
        this.HrEntCxf = HrEntCxf;
    }

    public String getDtSaiCxf() {
        return DtSaiCxf;
    }

    public void setDtSaiCxf(String DtSaiCxf) {
        this.DtSaiCxf = DtSaiCxf;
    }

    public String getDtEntTes() {
        return DtEntTes;
    }

    public void setDtEntTes(String DtEntTes) {
        this.DtEntTes = DtEntTes;
    }

    public String getHrEntTes() {
        return HrEntTes;
    }

    public void setHrEntTes(String HrEntTes) {
        this.HrEntTes = HrEntTes;
    }

    public String getDtSaiTes() {
        return DtSaiTes;
    }

    public void setDtSaiTes(String DtSaiTes) {
        this.DtSaiTes = DtSaiTes;
    }

    public String getHrSaiTes() {
        return HrSaiTes;
    }

    public void setHrSaiTes(String HrSaiTes) {
        this.HrSaiTes = HrSaiTes;
    }

    public String getAgenciaParada() {
        return AgenciaParada;
    }

    public void setAgenciaParada(String AgenciaParada) {
        this.AgenciaParada = AgenciaParada;
    }

    public String getSubAgenciaParada() {
        return SubAgenciaParada;
    }

    public void setSubAgenciaParada(String SubAgenciaParada) {
        this.SubAgenciaParada = SubAgenciaParada;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public String getSequencia() {
        return sequencia;
    }

    public void setSequencia(String sequencia) {
        this.sequencia = sequencia;
    }

    public String getParada() {
        return parada;
    }

    public void setParada(String parada) {
        this.parada = parada;
    }

    public String getAgenciaDestino() {
        return AgenciaDestino;
    }

    public void setAgenciaDestino(String AgenciaDestino) {
        this.AgenciaDestino = AgenciaDestino;
    }

    public String getSubAgenciaDestino() {
        return SubAgenciaDestino;
    }

    public void setSubAgenciaDestino(String SubAgenciaDestino) {
        this.SubAgenciaDestino = SubAgenciaDestino;
    }

    public String getDtEnt() {
        return DtEnt;
    }

    public void setDtEnt(String DtEnt) {
        this.DtEnt = DtEnt;
    }

    public String getVol() {
        return Vol;
    }

    public void setVol(String Vol) {
        this.Vol = Vol;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getOSRtg() {
        return OSRtg;
    }

    public void setOSRtg(String OSRtg) {
        this.OSRtg = OSRtg;
    }

    public String getDtColeta() {
        return DtColeta;
    }

    public void setDtColeta(String DtColeta) {
        this.DtColeta = DtColeta;
    }

    public String getAgSB() {
        return AgSB;
    }

    public void setAgSB(String AgSB) {
        this.AgSB = AgSB;
    }

    public String getOrigem() {
        return Origem;
    }

    public void setOrigem(String Origem) {
        this.Origem = Origem;
    }

    public String getDtEntrega() {
        return DtEntrega;
    }

    public void setDtEntrega(String DtEntrega) {
        this.DtEntrega = DtEntrega;
    }

    public String getAgSBD() {
        return AgSBD;
    }

    public void setAgSBD(String AgSBD) {
        this.AgSBD = AgSBD;
    }

    public String getHrPrg() {
        return HrPrg;
    }

    public void setHrPrg(String HrPrg) {
        this.HrPrg = HrPrg;
    }

    public String getHrCheg_E() {
        return HrCheg_E;
    }

    public void setHrCheg_E(String HrCheg_E) {
        this.HrCheg_E = HrCheg_E;
    }

    public String getHrCheg_S() {
        return HrCheg_S;
    }

    public void setHrCheg_S(String HrCheg_S) {
        this.HrCheg_S = HrCheg_S;
    }

    public String getRota() {
        return rota;
    }

    public void setRota(String rota) {
        this.rota = rota;
    }

    public String getClassCelula() {
        return ClassCelula;
    }

    public void setClassCelula(String ClassCelula) {
        this.ClassCelula = ClassCelula;
    }

    public String getClassIcon() {
        return ClassIcon;
    }

    public void setClassIcon(String ClassIcon) {
        this.ClassIcon = ClassIcon;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String Status) {
        this.Status = Status;
    }

    public String getSequencia_E() {
        return sequencia_E;
    }

    public void setSequencia_E(String sequencia_E) {
        this.sequencia_E = sequencia_E;
    }

    public String getParada_E() {
        return parada_E;
    }

    public void setParada_E(String parada_E) {
        this.parada_E = parada_E;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getEnderecoFilial() {
        return enderecoFilial;
    }

    public void setEnderecoFilial(String enderecoFilial) {
        this.enderecoFilial = enderecoFilial;
    }

    public String getBairroFilial() {
        return bairroFilial;
    }

    public void setBairroFilial(String bairroFilial) {
        this.bairroFilial = bairroFilial;
    }

    public String getCidadeFilial() {
        return cidadeFilial;
    }

    public void setCidadeFilial(String cidadeFilial) {
        this.cidadeFilial = cidadeFilial;
    }

    public String getUfFilial() {
        return ufFilial;
    }

    public void setUfFilial(String ufFilial) {
        this.ufFilial = ufFilial;
    }

    public String getCepFilial() {
        return cepFilial;
    }

    public void setCepFilial(String cepFilial) {
        this.cepFilial = cepFilial;
    }

    public String getCnpjFilial() {
        return cnpjFilial;
    }

    public void setCnpjFilial(String cnpjFilial) {
        this.cnpjFilial = cnpjFilial;
    }

    public String getFoneFilial() {
        return foneFilial;
    }

    public void setFoneFilial(String foneFilial) {
        this.foneFilial = foneFilial;
    }

    public String getnRedFat() {
        return nRedFat;
    }

    public void setnRedFat(String nRedFat) {
        this.nRedFat = nRedFat;
    }

    public String getCodCliFat() {
        return codCliFat;
    }

    public void setCodCliFat(String codCliFat) {
        this.codCliFat = codCliFat;
    }

    public String getNomeFat() {
        return nomeFat;
    }

    public void setNomeFat(String nomeFat) {
        this.nomeFat = nomeFat;
    }

    public String getEndFat() {
        return endFat;
    }

    public void setEndFat(String endFat) {
        this.endFat = endFat;
    }

    public String getCidadeFat() {
        return cidadeFat;
    }

    public void setCidadeFat(String cidadeFat) {
        this.cidadeFat = cidadeFat;
    }

    public String getBairroFat() {
        return bairroFat;
    }

    public void setBairroFat(String bairroFat) {
        this.bairroFat = bairroFat;
    }

    public String getEstadoFat() {
        return estadoFat;
    }

    public void setEstadoFat(String estadoFat) {
        this.estadoFat = estadoFat;
    }

    public String getCgcFat() {
        return cgcFat;
    }

    public void setCgcFat(String cgcFat) {
        this.cgcFat = cgcFat;
    }

    public String getIeFat() {
        return ieFat;
    }

    public void setIeFat(String ieFat) {
        this.ieFat = ieFat;
    }

    public String getEmailFat() {
        return emailFat;
    }

    public void setEmailFat(String emailFat) {
        this.emailFat = emailFat;
    }

    public String getnRedOri() {
        return nRedOri;
    }

    public void setnRedOri(String nRedOri) {
        this.nRedOri = nRedOri;
    }

    public String getNomeOri() {
        return nomeOri;
    }

    public void setNomeOri(String nomeOri) {
        this.nomeOri = nomeOri;
    }

    public String getRegistroOri() {
        return registroOri;
    }

    public void setRegistroOri(String registroOri) {
        this.registroOri = registroOri;
    }

    public String getEndOri() {
        return endOri;
    }

    public void setEndOri(String endOri) {
        this.endOri = endOri;
    }

    public String getCidadeOri() {
        return cidadeOri;
    }

    public void setCidadeOri(String cidadeOri) {
        this.cidadeOri = cidadeOri;
    }

    public String getBairroOri() {
        return bairroOri;
    }

    public void setBairroOri(String bairroOri) {
        this.bairroOri = bairroOri;
    }

    public String getEstadoOri() {
        return estadoOri;
    }

    public void setEstadoOri(String estadoOri) {
        this.estadoOri = estadoOri;
    }

    public String getEmailOri() {
        return emailOri;
    }

    public void setEmailOri(String emailOri) {
        this.emailOri = emailOri;
    }

    public String getVeiculoOri() {
        return veiculoOri;
    }

    public void setVeiculoOri(String veiculoOri) {
        this.veiculoOri = veiculoOri;
    }

    public String getRotaOri() {
        return rotaOri;
    }

    public void setRotaOri(String rotaOri) {
        this.rotaOri = rotaOri;
    }

    public String getColetaOri() {
        return coletaOri;
    }

    public void setColetaOri(String coletaOri) {
        this.coletaOri = coletaOri;
    }

    public String getnRedDst() {
        return nRedDst;
    }

    public void setnRedDst(String nRedDst) {
        this.nRedDst = nRedDst;
    }

    public String getNomeDst() {
        return nomeDst;
    }

    public void setNomeDst(String nomeDst) {
        this.nomeDst = nomeDst;
    }

    public String getRegistroDst() {
        return registroDst;
    }

    public void setRegistroDst(String registroDst) {
        this.registroDst = registroDst;
    }

    public String getCodCliDst() {
        return codCliDst;
    }

    public void setCodCliDst(String codCliDst) {
        this.codCliDst = codCliDst;
    }

    public String getEndDst() {
        return endDst;
    }

    public void setEndDst(String endDst) {
        this.endDst = endDst;
    }

    public String getCidadeDst() {
        return cidadeDst;
    }

    public void setCidadeDst(String cidadeDst) {
        this.cidadeDst = cidadeDst;
    }

    public String getBairroDst() {
        return bairroDst;
    }

    public void setBairroDst(String bairroDst) {
        this.bairroDst = bairroDst;
    }

    public String getEstadoDst() {
        return estadoDst;
    }

    public void setEstadoDst(String estadoDst) {
        this.estadoDst = estadoDst;
    }

    public String getEmailDst() {
        return emailDst;
    }

    public void setEmailDst(String emailDst) {
        this.emailDst = emailDst;
    }

    public String getVeiculoDst() {
        return veiculoDst;
    }

    public void setVeiculoDst(String veiculoDst) {
        this.veiculoDst = veiculoDst;
    }

    public String getRotaDst() {
        return rotaDst;
    }

    public void setRotaDst(String rotaDst) {
        this.rotaDst = rotaDst;
    }

    public String getColetaDst() {
        return coletaDst;
    }

    public void setColetaDst(String coletaDst) {
        this.coletaDst = coletaDst;
    }

    public String getER() {
        return ER;
    }

    public void setER(String ER) {
        this.ER = ER;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getTipoSrv() {
        return TipoSrv;
    }

    public void setTipoSrv(String TipoSrv) {
        this.TipoSrv = TipoSrv;
    }

    public String getGTVeProtocolo() {
        return GTVeProtocolo;
    }

    public void setGTVeProtocolo(String GTVeProtocolo) {
        this.GTVeProtocolo = GTVeProtocolo;
    }

    public String getGTVeChave() {
        return GTVeChave;
    }

    public void setGTVeChave(String GTVeChave) {
        this.GTVeChave = GTVeChave;
    }

    public String getGTVeLink() {
        return GTVeLink;
    }

    public void setGTVeLink(String GTVeLink) {
        this.GTVeLink = GTVeLink;
    }

    public String getGTVeData() {
        return GTVeData;
    }

    public void setGTVeData(String GTVeData) {
        this.GTVeData = GTVeData;
    }

    public String getGTVeHora() {
        return GTVeHora;
    }

    public void setGTVeHora(String GTVeHora) {
        this.GTVeHora = GTVeHora;
    }

    public String getStatusGuia() {
        return StatusGuia;
    }

    public void setStatusGuia(String StatusGuia) {
        this.StatusGuia = StatusGuia;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getPedidoCli() {
        return PedidoCli;
    }

    public void setPedidoCli(String PedidoCli) {
        this.PedidoCli = PedidoCli;
    }

    /**
     * @return the CCustoOS
     */
    public String getCCustoOS() {
        return CCustoOS;
    }

    /**
     * @param CCustoOS the CCustoOS to set
     */
    public void setCCustoOS(String CCustoOS) {
        this.CCustoOS = CCustoOS;
    }
    
    
}

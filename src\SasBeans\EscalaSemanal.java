/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class EscalaSemanal {

    private BigDecimal CodFil;
    private Integer Ano;
    private Integer Semana;
    private BigDecimal Matr;
    private Integer GrpEscala;
    private String Funcao;
    private String CHDia;
    private LocalDate DtInicio;
    private String Hr1;
    private String Hr2;
    private String Hr3;
    private String Hr4;
    private String Hr5;
    private String Hr6;
    private String Hr7;

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Ano
     */
    public Integer getAno() {
        return Ano;
    }

    /**
     * @param Ano the Ano to set
     */
    public void setAno(Integer Ano) {
        if (Ano == null) {
            Ano = 0;
        } else {
            this.Ano = Ano;
        }
    }

    /**
     * @return the Semana
     */
    public Integer getSemana() {
        return Semana;
    }

    /**
     * @param Semana the Semana to set
     */
    public void setSemana(Integer Semana) {
        if (Semana == null) {
            Semana = 0;
        } else {
            this.Semana = Semana;
        }
    }

    /**
     * @return the Matr
     */
    public BigDecimal getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal(0);
        }
    }

    /**
     * @return the GrpEscala
     */
    public Integer getGrpEscala() {
        return GrpEscala;
    }

    /**
     * @param GrpEscala the GrpEscala to set
     */
    public void setGrpEscala(Integer GrpEscala) {
        if (GrpEscala == null) {
            GrpEscala = 0;
        } else {
            this.GrpEscala = GrpEscala;
        }
    }

    /**
     * @return the Funcao
     */
    public String getFuncao() {
        return Funcao;
    }

    /**
     * @param Funcao the Funcao to set
     */
    public void setFuncao(String Funcao) {
        this.Funcao = Funcao;
    }

    /**
     * @return the CHDia
     */
    public String getCHDia() {
        return CHDia;
    }

    /**
     * @param CHDia the CHDia to set
     */
    public void setCHDia(String CHDia) {
        this.CHDia = CHDia;
    }

    /**
     * @return the DtInicio
     */
    public LocalDate getDtInicio() {
        return DtInicio;
    }

    /**
     * @param DtInicio the DtInicio to set
     */
    public void setDtInicio(LocalDate DtInicio) {
        this.DtInicio = DtInicio;
    }

    /**
     * @return the Hr1
     */
    public String getHr1() {
        return Hr1;
    }

    /**
     * @param Hr1 the Hr1 to set
     */
    public void setHr1(String Hr1) {
        this.Hr1 = Hr1;
    }

    /**
     * @return the Hr2
     */
    public String getHr2() {
        return Hr2;
    }

    /**
     * @param Hr2 the Hr2 to set
     */
    public void setHr2(String Hr2) {
        this.Hr2 = Hr2;
    }

    /**
     * @return the Hr3
     */
    public String getHr3() {
        return Hr3;
    }

    /**
     * @param Hr3 the Hr3 to set
     */
    public void setHr3(String Hr3) {
        this.Hr3 = Hr3;
    }

    /**
     * @return the Hr4
     */
    public String getHr4() {
        return Hr4;
    }

    /**
     * @param Hr4 the Hr4 to set
     */
    public void setHr4(String Hr4) {
        this.Hr4 = Hr4;
    }

    /**
     * @return the Hr5
     */
    public String getHr5() {
        return Hr5;
    }

    /**
     * @param Hr5 the Hr5 to set
     */
    public void setHr5(String Hr5) {
        this.Hr5 = Hr5;
    }

    /**
     * @return the Hr6
     */
    public String getHr6() {
        return Hr6;
    }

    /**
     * @param Hr6 the Hr6 to set
     */
    public void setHr6(String Hr6) {
        this.Hr6 = Hr6;
    }

    /**
     * @return the Hr7
     */
    public String getHr7() {
        return Hr7;
    }

    /**
     * @param Hr7 the Hr7 to set
     */
    public void setHr7(String Hr7) {
        this.Hr7 = Hr7;
    }

}

package Dados.Pool;

import Dados.LeArqConect;
import Dados.Persistencia;

/**
 * Atualmente essa classe serve somente para o dicionário entre parâmetro e
 * jdbc.
 *
 * <AUTHOR>
 */
public class SasPoolPersistencia {

    private int tamanhoPool = 1;
    private String caminho;
    private LeArqConect learqconect = new LeArqConect();

    /**
     * Devolve uma conexão para a aplicação, diretamente do pool do cliente
     * escolhido
     *
     * @param parametro - Parâmetro da Empresa
     * @return - Uma Persistencia, uma conexão pronta
     */
    public Persistencia getConexao(String parametro) {
        try {
            DadosBancos dados = learqconect.mapeia(parametro, caminho);
            if (dados == null) {
                return null;
            }

            //Persistencia retorno = new Persistencia(dados.getJndi());
            Persistencia retorno = new Persistencia(dados.getUrl(), dados.getUsuario(), dados.getSenha(), "");

            retorno.setEmpresa(dados.getEmpresa());
            return retorno;
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * Devolve uma conexão para a aplicação, diretamente do pool do cliente
     * escolhido
     *
     * @param parametro - Parâmetro da Empresa
     * @return - Uma Persistencia, uma conexão pronta
     * @throws java.lang.Exception
     */
    public Persistencia getConexao(String parametro, String enderecoUrl) throws Exception {
        DadosBancos dados = learqconect.mapeia(parametro, caminho);
        if (dados == null) {
            return null;
        }

        //Persistencia retorno = new Persistencia(dados.getJndi());
        Persistencia retorno = new Persistencia(dados.getUrl(), dados.getUsuario(), dados.getSenha(), enderecoUrl);

        retorno.setEmpresa(dados.getEmpresa());
        return retorno;
    }

    /**
     * Devolve o tamanho do pool atual
     *
     * @return
     * @deprecated Campo não utilizado para nada.
     *
     */
    @Deprecated
    public int getTamanhoPool() {
        return tamanhoPool;
    }

    /**
     * Determina o tamanho do pool a ser criado
     *
     * @param tamanhopool
     * @deprecated Campo não utilizado para nada.
     */
    @Deprecated
    public void setTamanhoPool(int tamanhopool) {
        this.tamanhoPool = tamanhopool;
    }

    public void setCaminho(String caminho) {
        this.caminho = caminho;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.SatWebService;

import SasBeans.NFItens;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class IntegracaoProtheus {

    private String numero;
    private String emissao;
    private String cliente;
    private String loja;
    private String cond_pagto;
    private String mensagem_nota;
    private String banco_cliente;
    private String desconto;
    private String tipo_frete;
    private String valor;
    private String descricao;
    private String agrupador;

    private List<NFItens> produtos;

    private String praca;
    private String compet;

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getEmissao() {
        return emissao;
    }

    public void setEmissao(String emissao) {
        this.emissao = emissao;
    }

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public String getLoja() {
        return loja;
    }

    public void setLoja(String loja) {
        this.loja = loja;
    }

    public String getCond_pagto() {
        return cond_pagto;
    }

    public void setCond_pagto(String cond_pagto) {
        this.cond_pagto = cond_pagto;
    }

    public String getMensagem_nota() {
        return mensagem_nota;
    }

    public void setMensagem_nota(String mensagem_nota) {
        this.mensagem_nota = mensagem_nota;
    }

    public String getBanco_cliente() {
        return banco_cliente;
    }

    public void setBanco_cliente(String banco_cliente) {
        this.banco_cliente = banco_cliente;
    }

    public String getDesconto() {
        return desconto;
    }

    public void setDesconto(String desconto) {
        this.desconto = desconto;
    }

    public String getTipo_frete() {
        return tipo_frete;
    }

    public void setTipo_frete(String tipo_frete) {
        this.tipo_frete = tipo_frete;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getAgrupador() {
        return agrupador;
    }

    public void setAgrupador(String agrupador) {
        this.agrupador = agrupador;
    }

    public List<NFItens> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<NFItens> produtos) {
        this.produtos = produtos;
    }

    public String getPraca() {
        return praca;
    }

    public void setPraca(String praca) {
        this.praca = praca;
    }

    public String getCompet() {
        return compet;
    }

    public void setCompet(String compet) {
        this.compet = compet;
    }
}

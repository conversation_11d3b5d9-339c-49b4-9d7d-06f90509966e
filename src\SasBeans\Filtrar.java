/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class Filtrar {

    private String coluna;
    private String tipo;
    private Integer tamanho;
    private Integer eh_nulo;

    public String getColuna() {
        return coluna;
    }

    public void setColuna(String coluna) {
        this.coluna = coluna;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getTamanho() {
        return tamanho;
    }

    public void setTamanho(Integer tamanho) {
        this.tamanho = tamanho;
    }

    public Integer getEh_nulo() {
        return eh_nulo;
    }

    public void setEh_nulo(Integer eh_nulo) {
        this.eh_nulo = eh_nulo;
    }

}

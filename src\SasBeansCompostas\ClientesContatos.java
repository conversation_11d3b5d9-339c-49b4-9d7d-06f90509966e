/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ClientesContatos {

    private String CodFil;
    private String Descricao;
    private String CodCont;
    private String CodCli;
    private String Endereco;
    private String NRed;
    private String Cidade;
    private String UF;
    private String Distancia;
    private List<Postos> postos;

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 41 * hash + Objects.hashCode(this.CodFil);
        hash = 41 * hash + Objects.hashCode(this.CodCli);
        hash = 41 * hash + Objects.hashCode(this.CodCont);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ClientesContatos other = (ClientesContatos) obj;
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        if (!Objects.equals(this.CodCont, other.CodCont)) {
            return false;
        }
        if (!Objects.equals(this.CodCli, other.CodCli)) {
            return false;
        }
        return true;
    }

    public static class Postos {

        private String Secao;
        private String Local;

        public String getSecao() {
            return Secao;
        }

        public void setSecao(String Secao) {
            this.Secao = Secao;
        }

        public String getLocal() {
            return Local;
        }

        public void setLocal(String Local) {
            this.Local = Local;
        }
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getCodCont() {
        return CodCont;
    }

    public void setCodCont(String CodCont) {
        this.CodCont = CodCont;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getDistancia() {
        return Distancia;
    }

    public void setDistancia(String Distancia) {
        this.Distancia = Distancia;
    }

    public List<Postos> getPostos() {
        return postos;
    }

    public void setPostos(List<Postos> postos) {
        this.postos = postos;
    }
}

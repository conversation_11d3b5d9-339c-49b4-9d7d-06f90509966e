<?xml version="1.0" encoding="UTF-8"?>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiMob}" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <link href="../assets/css/animate.css" rel="stylesheet" type="text/css"/>
            <link type="text/css" href="../assets/css/webPonto.css" rel="stylesheet" />
            <meta name="theme-color" content="#002172" />
            <meta name="msapplication-navbutton-color" content="#002172" />
            <meta name="apple-mobile-web-app-status-bar-style" content="#002172" />
            <style>
                .DataInspecao{
                    width: 100%;
                    height: 42px;
                    background-color: #002172;
                    color: #FFF;
                    text-align: center;
                    font-size: 14pt;
                    font-weight: 500;
                    padding-top: 7px;
                    margin-bottom: 3px;
                    margin-top: 3px;
                }

                .DescricaoInspecao{
                    padding: 0px !important;
                }

                .DescricaoInspecao label{
                    font-size: 10pt !important;
                    color: #101010;
                    font-weight: 500 !important;
                    display: block;
                    margin: 0px !important;
                    padding: 0px !important;
                    height: 20px;
                }

                .DescricaoInspecao label[ref="Inspetor"]{
                    border-bottom: thin solid #DDD;
                }

                .ItemInspecao{
                    padding: 10px;
                    border: thin solid #F0F0F0;
                    border-bottom: 4px solid #DDD;
                    background-color: #FFF;
                    cursor: pointer;
                }

                .ui-panel-content{
                    height: 100% !important;
                    overflow: auto;
                    padding-bottom: 0px !important;
                }

                .FundoItem{
                    background-color: #FFF;
                    border-radius: 4px;
                    box-shadow: 1px 1px 3px #CCC;
                    padding: 0px !important;
                    margin-bottom: 8px !Important;
                }
                
                .FundoItem input[type="radio"]{
                    width: 15px !important;
                    margin-right: 4px;
                }

                .FundoItem input[type="radio"]:nth-child(3){
                    margin-left: 16px;
                }
                
                .TituloItem{
                    font-weight: 500 !important;
                    font-size: 16pt;
                    display: block;
                }

                .txtItem{
                    box-shadow: none;
                    outline: none;
                    border: none;
                    width: 100%;
                    font-weight: 500 !important;
                    font-size: 16pt;
                }
                
                .FundoFoto{
                    background-color: #DDD;
                    height: 250px;
                    text-align: center;
                    cursor: pointer;
                }
                
                .FundoFoto table,
                .FundoFoto table tr td{
                    width: 100%;
                    height: 100%;
                    vertical-align: middle;
                    text-align: center;
                    font-size: 12pt;
                    font-weight: 600 !important;
                    text-shadow: 1px 0 0 #000, -1px 0 0 #000, 0 1px 0 #000, 0 -1px 0 #000, 1px 1px #000, -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000;
                    color: #FFF;
                    cursor: pointer;
                }
                
                .FundoFoto table tr td i{
                    color: #FFF;
                    font-size: 26pt;
                    text-shadow: none;
                    margin-bottom: 8px;
                    cursor: pointer;
                }
            </style>
        </h:head> 
        <h:body id="bodyWebPonto" style="height: 100%; overflow: hidden !important">
            <f:metadata>
                <f:viewAction action="#{login.carregarDadosWebPonto}" />
                <f:viewAction action="#{inspecoes.PersistenciaWebPonto(login.pp)}" />
                <f:viewAction action="#{inspecoes.carregarPaginaInspecoesMob}" />
            </f:metadata>

            <p:growl id="msgs" />
            <div class="LightBox" style="display: none"></div>

            <h:form id="frmCabecalho" style="height: 60px; width: 100%; padding: 0px; margin: 0px; background-color: #FFF;">
                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 60px; width: 100%; padding: 0px 0px 0px 65px; margin: 0px; background-color: #002172; text-align: left;">
                    <img src="../assets/logos/SatMobEw.png" height="42" style="border-radius: 50%; left: 10px; top: 7px; position: absolute" />
                    <i class="fa fa-bars" ref="BarsMenu" style="position: absolute; font-size: 16pt; color: #FFF; right: 27px; top: 1px; cursor: pointer; padding: 14px 8px 14px 8px !important; width: 50px; text-align: center"></i>
                    <label id="lblTituloInspecao" class="DescricaoPonto" style="max-width: calc(100% - 100px) !important; font-size: 15pt !important; height: 40px !important; padding: 10px 5px 0px 0px !important; margin-top: 5px !important; text-transform: uppercase">#{localemsgs.Inspecoes}</label>
                </div>
            </h:form>

            <h:form id="frmInspecao" style="height: calc(100% - 61px) !important; width: 100%; padding: 7px 0px 10px 0px; margin: 0px; background-color: #EAEAEA; overflow-y: auto;">
                <a href="javascript:void(0);" id="btNovaInspecao" class="btn btn-primary BotaoPontoMob" STYLE="position: absolute; width: 60px !important; height: 60px !important; box-shadow: 3px 3px 5px rgba(0,0,0,0.4); padding-top: 17px; font-size: 18pt !important; bottom: 25px; right: 25px; z-index: 9999; border-radius: 50%; background-color: #5472D2 !important; cursor: pointer;"><i class="fa fa-plus"></i></a>
                <a href="javascript:void(0);" id="btSalvarInspecao" class="btn btn-primary BotaoPontoMob" STYLE="position: absolute; width: 60px !important; height: 60px !important; box-shadow: 3px 3px 5px rgba(0,0,0,0.4); padding-top: 17px; font-size: 18pt !important; bottom: 25px; right: 25px; z-index: 9999; border-radius: 50%; background-color: #5472D2 !important; cursor: pointer; display: none;"><i class="fa fa-check"></i></a>

                <div id="divNovaInspecao" style="z-index: 99999999; height: 300px; width: 90%; position: absolute; top:0; right:0; bottom:0; left:0; margin: auto; background-color: #F3F3F3; display: none">
                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px; font-size: 11pt; color: #666; text-shadow: 1px 1px #FFF; background-color: #EEE; text-align: center; border-bottom: thin solid #CCC; box-shadow: 0px 3px 4px #DDD">#{localemsgs.SelecioneInspecao}</div>
                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px !important; height: calc(100% - 73px) !Important; overflow-y: auto !important;">
                        <h:outputText escape="false" value="#{inspecoes.htmlTiposInspecoes}"></h:outputText>
                    </div>
                    <div id="btCancelar" style="width: 100%; text-align: right; text-transform: uppercase; padding: 0px 15px 3px 0px; cursor: pointer;">#{localemsgs.Cancelar}</div>
                </div>

                <div id="divInspecoesExe" class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important;">
                    <p:panel id="panel">
                        <h:outputText escape="false" value="#{inspecoes.htmlInspecoesExec}"></h:outputText>
                    </p:panel>
                </div>    

                <div id="divPerguntasInspecao" class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important; height: calc(100% + 0px); background-color: #EEE; overflow: hidden !important; display:none;">
                    <p:panel id="pnlListaInspecoes" style="background-color: transparent; padding: 0px !important; margin-top: -8px !important;width: calc(100% + 20px) !important;overflow: hidden !important; margin-left: -10px !important; height: 100% !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="height: 50px; background-color: #EEE; box-shadow: 2px 2px 3px #DDD; padding: 0px !important; overflow-x: hidden !important;">
                            <div class="row" style="padding: 0px !important;">
                                <div class="col-md-3 col-sm-3 col-xs-3" style="text-align: right; padding-right: 0px !Important;">
                                    <label style="color: #888 !important; text-shadow: 1px 1px #FFF">#{localemsgs.Posto}:</label>
                                </div>
                                <div class="col-md-9 col-sm-9 col-xs-9" style="padding-left: 6px !important">
                                    <label style="color: #000 !important; text-shadow: 1px 1px #FFF; text-transform: uppercase !important">#{login.webPontoLocal}</label>
                                </div>
                            </div>
                            <div class="row" style="padding: 0px !important;">
                                <div class="col-md-3 col-sm-3 col-xs-3" style="text-align: right; padding-right: 0px !Important;">
                                    <label style="color: #888 !important; text-shadow: 1px 1px #FFF">#{localemsgs.Inspetor}:</label>
                                </div>
                                <div class="col-md-9 col-sm-9 col-xs-9" style="padding-left: 6px !important">
                                    <label style="color: #000 !important; text-shadow: 1px 1px #FFF; text-transform: uppercase !important">#{login.webPontoNomeGuer}</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100% - 50px); overflow: auto !important; padding: 10px 10px 0px 10px !important;">
                            <h:outputText escape="false" value="#{inspecoes.itensNovaInspecao}"></h:outputText>
                        </div>
                    </p:panel>
                </div>
                <h:inputHidden id="txtTipoInspecao" value="#{inspecoes.tipoInspecao}"></h:inputHidden>

                <p:remoteCommand name="rcCarregar" partialSubmit="true" 
                                 process="@this" 
                                 update="msgs frmInspecao:panel" 
                                 actionListener="#{inspecoes.listarExecutados(false)}" />   

                <p:remoteCommand name="rcNovaInspecao" partialSubmit="true" 
                                 process="@this txtTipoInspecao" 
                                 update="msgs pnlListaInspecoes" 
                                 actionListener="#{inspecoes.listarPerguntas}"
                                 />   

                <script type="text/javascript">
                    // <![CDATA[
                    let AguardeMsg = "#{localemsgs.ProcessandoAguarde}";

                    $(document).ready(function () {
                        $('[id*="frmInspecao"]').on('scroll', function () {
                            $('#lblCarregandoInspecoes').remove();

                            if (!$('#lblCarregandoInspecoes').attr('id')) {
                                if ($(this)[0].scrollHeight - $(this)[0].scrollTop <= $(this)[0].clientHeight) {
                                    $('#lblCarregandoInspecoes').remove();
                                    $('[id*="panel"]').append('<label id="lblCarregandoInspecoes" style="width: 100% !important; text-align: center !important; padding: 10px; font-weight: 500 !important; color: #888 !important; margin-top: 10px; font-size: 12pt !important"><i class="fa fa-refresh fa-spin fa-fw"></i> ' + AguardeMsg + '</label>');
                                    rcCarregar();
                                }
                            }
                        });
                    })
                            .on('click', '#divNovaInspecao .ItemInspecao', function() {
                                $('#lblTituloInspecao').text($(this).text());
                                $('[id*="txtTipoInspecao"]').val($(this).attr('codigo'));
                                rcNovaInspecao();
                                $('#btCancelar').click();
                                $('#divInspecoesExe').css('display', 'none');
                                $('#divPerguntasInspecao').fadeIn();
                                $('#btNovaInspecao').css('display','none');
                                $('#btSalvarInspecao').css('display','');
                            })
                            .on('click', '#btNovaInspecao', () => {
                                $('.LightBox').css('display', '');
                                $('#divNovaInspecao').fadeIn();
                            })
                            .on('click', '#btCancelar', () => {
                                $('#divNovaInspecao').fadeOut(500, () => {
                                    $('.LightBox').css('display', 'none');
                                });
                            })
                            .on('click', '#btSalvarInspecao', () => {
                                alert('Em Desenvolvimento!');
                            })
                            ;

                    // ]]>
                </script>
            </h:form>
        </h:body>
    </f:view>
</html>

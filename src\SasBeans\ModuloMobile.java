package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ModuloMobile {

    private String Parametro;
    private BigDecimal Sistema;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public ModuloMobile() {
        this.Parametro = "";
        this.Sistema = new BigDecimal("0");
        this.Operador = "";
        this.Dt_Alter = null;
        this.Hr_Alter = "";
    }

    public String getParametro() {
        return Parametro;
    }

    public void setParametro(String Parametro) {
        this.Parametro = Parametro;
    }

    public BigDecimal getSistema() {
        return Sistema;
    }

    public void setSistema(String Sistema) {
        try {
            this.Sistema = new BigDecimal(Sistema);
        } catch (Exception e) {
            this.Sistema = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
*/
/* 
    Created on : 22/10/2018, 16:45:48
    Author     : PC-sasw
*/

[id*="formCadastrar"] .ui-dialog-title{
    max-height:30px !important;
}

[id*="formCadastroComposicao"] .ui-inputfield{
    width:100% !important;
}

[id*="formCadastrar"] .ui-autocomplete-input,
[id*="formCadastrar"] .ui-inputfield{
    min-width:100% !important;
    width:100% !important;
    max-width:100% !important;
}

@media only screen and (max-width: 700px) and (min-width: 10px) {
    /*
                        #divDadosFilial,
                        #divDadosFilial div,
                        .FilialNome,
                        .FilialEndereco,
                        .FilialBairroCidade{
                            min-width:100% !important;
                            width:100% !important;
                            max-width:100% !important;
                            text-align: center !important;
                        }*/

    .ui-paginator-top {
        white-space: normal !important;
    }

    .tabela .ui-datatable-scrollable-body{
        flex-grow: 1;

    }
}

@media only screen and (max-width: 2000px) and (min-width: 701px) {
    .DataGrid [role="columnheader"] > span {
        top: -3px !important;
        position: relative !important;
    }

    .DataGrid{
        width:100% !important;
        border:none !important
    }

    .DataGrid thead tr th,
    .DataGrid tbody tr td {
        min-width: 120px !important;
        max-width: 120px !important;
    }
}

html, body{
    max-height:100% !important;
    overflow:hidden !important;
}
#divCorporativo{
    bottom:23px !important;
}

#corporativo {
    max-width: 18vw;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

#corporativo label[ref="lblCheck"]{
    font-size:11px !important;
    min-width:75px !important;
    font-weight:500 !important;
    padding: 2px !important;
}

footer .ui-chkbox-box {
    max-width: 12px !important;
    max-height: 12px !important;
}

.ui-dialog .ui-panel-content {
    height: auto !important;
}

#formCadastrar .ui-selectonemenu.ui-state-default {
    background: #fff !important;
}

#formCadastrar .ui-selectonemenu.ui-state-disabled {
    color: #555 !important;
    background: #f7f7f7 !important;
    opacity: 0.7 !important;
}

[id*="formCadastrar"] div[class*="col-md"]{
    padding: 5px !important;
}

#body {
    height: calc(100% - 40px);
    position: relative;
    display: flex;
    flex-direction: column;
}

#main {
    flex-grow: 1;
}

#formPesquisar .ui-radiobutton {
    background: transparent !important;
}

.ui-datatable-scrollable-body,
.FundoPagina > .ui-panel-content {
    height: 100% !important;
}

.FundoPagina {
    border: thin solid #CCC !important;
    border-top:4px solid #3C8DBC !important;
}

[id*="tabGeral"],
[id*="tabDocumentos"]{
    background-color:#FFF !important;
    padding-right: 20px!important;
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}
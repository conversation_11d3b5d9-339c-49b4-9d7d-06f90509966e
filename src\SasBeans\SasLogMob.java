package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class SasLogMob {

    private BigDecimal Sequencia;
    private BigDecimal SeqRota;
    private Integer Parada;
    private String HrCheg;
    private String HrSaida;
    private BigDecimal Guia;
    private String Serie;
    private BigDecimal Valor;
    private String Versao;
    private String Volumes;
    private String IMEI;
    private String Historico;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public SasLogMob() {
        this.Sequencia = new BigDecimal("0");
        this.SeqRota = new BigDecimal("0");
        this.Parada = 0;
        this.HrCheg = "";
        this.HrSaida = "";
        this.Guia = new BigDecimal("0");
        this.Serie = "";
        this.Valor = new BigDecimal("0");
        this.Versao = "";
        this.Volumes = "";
        this.IMEI = "";
        this.Historico = "";
        this.Dt_Alter = LocalDate.now();
        this.Hr_Alter = "";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public BigDecimal getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(String SeqRota) {
        try {
            this.SeqRota = new BigDecimal(SeqRota);
        } catch (Exception e) {
            this.SeqRota = new BigDecimal("0");
        }
    }

    public Integer getParada() {
        return Parada;
    }

    public void setParada(Integer Parada) {
        this.Parada = Parada;
    }

    public String getHrCheg() {
        return HrCheg;
    }

    public void setHrCheg(String HrCheg) {
        this.HrCheg = HrCheg;
    }

    public String getHrSaida() {
        return HrSaida;
    }

    public void setHrSaida(String HrSaida) {
        this.HrSaida = HrSaida;
    }

    public BigDecimal getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        try {
            this.Guia = new BigDecimal(Guia);
        } catch (Exception e) {
            this.Guia = null;
            //this.Guia = new BigDecimal("0");
        }
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public String getVersao() {
        return Versao;
    }

    public void setVersao(String Versao) {
        this.Versao = Versao;
    }

    public String getVolumes() {
        return Volumes;
    }

    public void setVolumes(String Volumes) {
        this.Volumes = Volumes;
    }

    public void setIMEI(String IMEI) {
        this.IMEI = IMEI;
    }

    public String getIMEI() {
        return this.IMEI;
    }

    public String getHistorico() {
        return Historico;
    }

    public void setHistorico(String Historico) {
        this.Historico = Historico;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets" style="overflow:hidden !important; max-height:100% !important;"
      xmlns:cadastros="http://xmlns.jcp.org/jsf/composite/cadastros"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/menu.css" rel="stylesheet" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/cofres.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/charts.js" library="primefaces" type="text/javascript" ></script>
            <script src="https://maps.google.com/maps/api/js?key=AIzaSyBLXQgn09RZmoQ6NfHxGQaR7Y0M5eFTMts" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                .formRotulo{
                    font-size:10pt !important;
                    color:#000 !important;
                    font-weight: bold !important;
                    text-shadow:1px 1px #FFF;
                    width:100% !important;
                    display:block;
                    padding:0px !important;
                    margin:0px 0px 4px 0px !important;
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -2px !important;
                        position: relative !important;
                    }
                }
            </style>
        </h:head>
        <h:body id="h" style="overflow:hidden !important;max-height:100% !important;">
            <f:metadata>
                <f:viewAction action="#{Coaf.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{Coaf.carregarComunicacoes()}"/>
            </f:metadata>
            <p:growl id="msgs" widgetVar="g"/>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <div id="body" style="overflow:hidden !important;max-height:100% !important;">
                <ui:include src="/botao_panico.xhtml"/>

                <cadastros:header
                    id="cabecalho"
                    titulo="#{localemsgs.CoafComunic}"
                    imagem="../assets/img/icone_satmob_rendimentos_G.png"
                    data="#{Coaf.dataTela}"
                    descricao="#{Coaf.filiais.descricao}"
                    endereco="#{Coaf.filiais.endereco}"
                    bairro="#{Coaf.filiais.bairro}"
                    cidade="#{Coaf.filiais.cidade}"
                    UF="#{Coaf.filiais.UF}"
                    />

                <h:form id="main" style="overflow:hidden !important;">
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important;">
                        <div ref="pai" class="col-md-10 col-sm-9 col-xs-12" style="padding:0px 4px 26px 0px !important">
                            <div style="width:100% !important; height: 100% !important; overflow:auto !important">
                                <p:dataTable
                                    id="tabela"
                                    value="#{Coaf.listaCoafComunic}"
                                    rowKey="#{lista.sequencia}"
                                    paginator="false"
                                    paginatorTemplate="false"
                                    lazy="true"
                                    reflow="true"
                                    var="lista"
                                    selection="#{Coaf.coafComunicSelecionado}"
                                    styleClass="tabela"
                                    selectionMode="single"
                                    emptyMessage="#{localemsgs.SemRegistros}"
                                    scrollable="true"
                                    class="tabela DataGrid"
                                    scrollWidth="100%"
                                    style="display: flex; flex-direction: column; font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important; background-color:#FFF !important;margin-top:-10px !important"
                                    >
                                    <p:column headerText="#{localemsgs.Sequencia}" class="text-center">
                                        <h:outputText value="#{lista.sequencia}" converter="conversor0" class="text-center" />
                                    </p:column>
                                    <p:column headerText="#{localemsgs.CodFil}" class="text-center">
                                        <h:outputText value="#{lista.codFil}" class="text-center">
                                            <f:convertNumber pattern="0000"/>
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.DataInicial}"  class="text-center">
                                        <h:outputText value="#{lista.dtInicio}" converter="conversorData" class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.DataFinal}" class="text-center">
                                        <h:outputText value="#{lista.dtFinal}" converter="conversorData" class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Protocolo}" class="text-center">
                                        <h:outputText value="#{lista.protocolo}"  class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Quantidade}" class="text-center">
                                        <h:outputText value="#{lista.qtde}" converter="conversor0" class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                        <h:outputText value="#{lista.operador}"  class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                        <h:outputText value="#{lista.dtAlter}" converter="conversorData" class="text-center"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                        <h:outputText value="#{lista.hrAlter}" converter="conversorHora" class="text-center"/>
                                    </p:column>
                                </p:dataTable>
                            </div>
                        </div>
                        <div ref="pai" class="col-md-2 col-sm-3 col-xs-12" style="padding:0px 12px 26px 4px;">
                            <div style="width:100%; height:175px; border:thin solid #CCC; border-radius:4px; background-color:#EEE">
                                <div class="col-md-12" style="text-align:center !important; padding-top:8px !important;">
                                    <label class="formRotulo" style="text-align:center !important;">#{localemsgs.DataInicial}</label>
                                    <p:calendar id="calendarioDe" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{Coaf.dataInicio}" locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                    </p:calendar>
                                </div>
                                <div class="col-md-12" style="text-align:center !important; padding-top:4px !important;">
                                    <label class="formRotulo" style="text-align:center !important;">#{localemsgs.DataFinal}</label>
                                    <p:calendar id="calendarioAte" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{Coaf.dataFinal}" locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                    </p:calendar>
                                </div>
                                <div class="col-md-12" style="text-align:center !important; padding-left: 10px !important;padding-right: 10px !important;">
                                    <h:commandButton class="btn btn-info btn-large"
                                                     value="#{localemsgs.Pesquisar}"
                                                     __action="#{valores.consultarPedidoBaseOrigem()}"
                                                     style="width:100%; margin-top: 8px"><i class='fa fa-search' style='position:absolute; margin-top: 16px; font-size:12pt !important; left:24px'></i></h:commandButton>
                                </div>
                            </div>
                            <div style="width:100%; height:calc(100% - 183px); border:thin solid #CCC; border-radius:4px; margin-top:8px; padding:2px 10px 4px 10px !important">
                                <h:commandButton class="btn btn-primary btn-large" value="#{localemsgs.Filtrar}" __action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%; margin-top: 8px"><i class='fa fa-filter' style='position:absolute; margin-top: 17px; font-size:12pt !important; left:30px'></i></h:commandButton>
                                <h:commandButton class="btn btn-primary btn-large" value="#{localemsgs.Imprimir}" __action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%; margin-top: 4px"><i class='fa fa-print' style='position:absolute; margin-top: 13px; font-size:12pt !important; left:30px'></i></h:commandButton>
                                <h:commandButton class="btn btn-primary btn-large" value="#{localemsgs.Exportar}" __action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%; margin-top: 4px"><i class='fa fa-download' style='position:absolute; margin-top: 13px; font-size:12pt !important; left:30px'></i></h:commandButton>
                                <h:commandButton class="btn btn-primary btn-large" value="#{localemsgs.Pesquisar}" __action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%; margin-top: 4px"><i class='fa fa-search' style='position:absolute; margin-top: 13px; font-size:12pt !important; left:30px'></i></h:commandButton>
                                <h:commandButton class="btn btn-primary btn-large" value="#{localemsgs.Ordenacao}" __action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%; margin-top: 4px"><i class='fa fa-sort-alpha-asc' style='position:absolute; margin-top: 13px; font-size:12pt !important; left:30px'></i></h:commandButton>

                                <h:commandButton class="btn btn-success btn-large" value="#{localemsgs.Novo}" __action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%; margin-top: 4px"><i class='fa fa-plus' style='position:absolute; margin-top: 13px; font-size:12pt !important; left:30px'></i></h:commandButton>
                                <h:commandButton class="btn btn-warning btn-large" value="#{localemsgs.Editar}" __action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%; margin-top: 4px"><i class='fa fa-edit' style='position:absolute; margin-top: 13px; font-size:12pt !important; left:30px'></i></h:commandButton>
                                <h:commandButton class="btn btn-danger btn-large" value="#{localemsgs.Excluir}" __action="#{valores.consultarPedidoBaseOrigem()}" style="width:100%; margin-top: 4px"><i class='fa fa-trash' style='position:absolute; margin-top: 13px; font-size:12pt !important; left:30px'></i></h:commandButton>
                            </div>
                        </div>
                    </div>
                    <script type="text/javascript">
                        // <![CDATA[
                        if ($(document).width() <= 700)
                            $('.FundoPagina, .FundoPagina [ref="pai"]').css('max-height', ($('html').height() - 384) + 'px').css('height', ($('html').height() - 199) + 'px').css('min-height', ($('html').height() - 199) + 'px');
                        else
                            $('.FundoPagina, .FundoPagina [ref="pai"]').css('max-height', ($('html').height() - 121) + 'px').css('height', ($('html').height() - 121) + 'px').css('min-height', ($('html').height() - 121) + 'px');

                        $(window).resize(function () {
                            if ($(document).width() <= 700)
                                $('.FundoPagina, .FundoPagina [ref="pai"]').css('max-height', ($('html').height() - 384) + 'px').css('height', ($('html').height() - 199) + 'px').css('min-height', ($('html').height() - 199) + 'px');
                            else
                                $('.FundoPagina, .FundoPagina [ref="pai"]').css('max-height', ($('html').height() - 121) + 'px').css('height', ($('html').height() - 121) + 'px').css('height', ($('html').height() - 121) + 'px');
                        });
                        // ]]>
                    </script>
                </h:form>
            </div>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>

        </h:body>
    </f:view>
</html>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class FuncionFerias {
    private String Matr;
    private String Nome;
    private String DtInicioFer;
    private String DtFinalFer;
    private String Data;
    private String Matr_Subs;
    private String Nome_Subs;

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getDtInicioFer() {
        return DtInicioFer;
    }

    public void setDtInicioFer(String DtInicioFer) {
        this.DtInicioFer = DtInicioFer;
    }

    public String getDtFinalFer() {
        return DtFinalFer;
    }

    public void setDtFinalFer(String DtFinalFer) {
        this.DtFinalFer = DtFinalFer;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getMatr_Subs() {
        return Matr_Subs;
    }

    public void setMatr_Subs(String Matr_Subs) {
        this.Matr_Subs = Matr_Subs;
    }

    public String getNome_Subs() {
        return Nome_Subs;
    }

    public void setNome_Subs(String Nome_Subs) {
        this.Nome_Subs = Nome_Subs;
    }

    
}

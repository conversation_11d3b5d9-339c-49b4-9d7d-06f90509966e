package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class ChamadosAnexos {

    private BigDecimal Sequencia;
    private BigDecimal Ordem;
    private String Nome;
    private String Extensao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public BigDecimal getOrdem() {
        return Ordem;
    }

    public void setOrdem(BigDecimal Ordem) {
        this.Ordem = Ordem;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getExtensao() {
        return Extensao;
    }

    public void setExtensao(String Extensao) {
        this.Extensao = Extensao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

/*
 */
package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import SasBeans.DIRFDet;
import SasBeans.DIRFDetPS;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasDaos.DIRFDetDao;
import SasDaos.DIRFDetPSDao;
import SasDaos.DIRFRemDao;
import SasDaos.FiliaisDao;
import SasDaos.FuncionDao;
import SasLibrary.FolhaPagamento;
import br.com.sasw.arquivos.PDF;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

/**
 *
 * <AUTHOR>
 */
@Named(value = "rendimentos")
@ViewScoped
public class RendimentosMB implements Serializable {

    private FiliaisDao filialdao;
    private FolhaPagamento fopag;
    private DIRFDetPSDao ps;
    private DIRFDetDao dirf;
    private DIRFRemDao dirfrem;
    private FuncionDao funcdao;

    private Persistencia persistencia;
    private BigDecimal codPessoa;
    private String codFil, caminho, banco, matricula, log, operador;
    private ArquivoLog logerro;

    public RendimentosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        try {
            matricula = matricula.replace(".0", "");
        } catch (Exception e) {
            matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        }
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\Contracheques\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        filialdao = new FiliaisDao();
        fopag = new FolhaPagamento();
        ps = new DIRFDetPSDao();
        dirf = new DIRFDetDao();
        dirfrem = new DIRFRemDao();
        funcdao = new FuncionDao();
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        log = new String();
    }

    public void imprimirRendimentos() {
        try {
            PDF pdf = new PDF("C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.banco + "\\Rendimentos\\", this.codPessoa.toBigInteger() + ".pdf");
            String escudo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("\\assets\\img\\escudo.png");
            String ano = String.valueOf(LocalDate.now().getYear());
            String ano_base = String.valueOf(LocalDate.now().getYear() - 1);
            List<Funcion> funcion = this.funcdao.BuscaNome(this.matricula, this.persistencia);
            List<DIRFDet> ldirf = this.dirf.BuscaDirf(ano_base, this.matricula, this.persistencia);
            List<Filiais> filial = this.filialdao.BuscaFilial(this.codFil, this.persistencia);
            List<DIRFDetPS> lps = this.ps.getPlanoAnual(this.matricula, ano_base, this.persistencia);
            LocalDate dtenvio = this.dirfrem.DataEnvio(ano_base, this.persistencia);
            String CNPJ = FuncoesString.formatarString(filial.get(0).getCNPJ(), "##.###.###/####-##");
            String RazaoSocial = filial.get(0).getRazaoSocial();
            String CPF = FuncoesString.formatarString(ldirf.get(0).getCPF(), "###.###.###-##");
            String Nome = funcion.get(0).getNome();
            String Rendimentos = "0,00";
            String PrevOfi = "0,00";
            String IRRF = "0,00";
            String Pensao = "0.00";
            String abonoPecuniario = "0.00";
            String Rendimentos13 = "0,00";
            String PrevOfi13 = "0,00";
            String IRRF13 = "0,00";
            BigDecimal acumula13 = new BigDecimal("0");
            for (DIRFDet ldirf2 : ldirf) {
                if ("RTRT".equals(ldirf2.getTipoReg())) { //rendimentos tributaveis
                    BigDecimal acumula = ldirf2.getValorTotal().subtract(ldirf2.getValor13());
                    Rendimentos = FuncoesString.formatarStringMoeda(acumula.toPlainString(), false);
                    acumula13 = acumula13.add(new BigDecimal(ldirf2.getValor13().toPlainString()));
                } else if ("RTPO".equals(ldirf2.getTipoReg())) { //previdencia oficial
                    BigDecimal acumula = ldirf2.getValorTotal().subtract(ldirf2.getValor13());
                    PrevOfi = FuncoesString.formatarStringMoeda(acumula.toPlainString(), false);
                    PrevOfi13 = FuncoesString.formatarStringMoeda(ldirf2.getValor13().toPlainString(), false);
                    acumula13 = acumula13.subtract(new BigDecimal(ldirf2.getValor13().toPlainString()));
                } else if ("RTPA".equals(ldirf2.getTipoReg()) || "ESPA".equals(ldirf2.getTipoReg())) { //pensao alimenticia
                    Pensao = FuncoesString.formatarStringMoeda(ldirf2.getValorTotal().toPlainString(), false);
                    acumula13 = acumula13.subtract(new BigDecimal(ldirf2.getValor13().toPlainString()));
                } else if ("RTIRF".equals(ldirf2.getTipoReg())) { //imposto retido na fonte
                    BigDecimal acumula = ldirf2.getValorTotal().subtract(ldirf2.getValor13());
                    IRRF = FuncoesString.formatarStringMoeda(acumula.toPlainString(), false);
                    IRRF13 = FuncoesString.formatarStringMoeda(ldirf2.getValor13().toPlainString(), false);
                    acumula13 = acumula13.subtract(new BigDecimal(ldirf2.getValor13().toPlainString()));
                } else if ("RIAP".equals(ldirf2.getTipoReg())) { //imposto retido na fonte
                    abonoPecuniario = FuncoesString.formatarStringMoeda(ldirf2.getValorTotal().toPlainString(), false);
                } else {
                    acumula13 = acumula13.subtract(new BigDecimal(ldirf2.getValor13().toPlainString()));
                }
            }
            Rendimentos13 = FuncoesString.formatarStringMoeda(acumula13.toPlainString(), false);
            String RespInf = this.fopag.ResponsavelInformacaoDIRF(this.codFil, this.persistencia);

            pdf.InformeRendimentos(escudo, ano, ano_base, CNPJ, RazaoSocial,
                    CPF, Nome, "Rendimentos do trabalho assalariado",
                    Rendimentos, PrevOfi, "0,00", Pensao, IRRF,
                    "0,00", "0,00", "0,00", "0,00", "0,00", "0,00", abonoPecuniario,
                    Rendimentos13, IRRF13, "0,00",
                    "0",
                    "0,00", "0,00", "0,00", "0,00", "0,00", "0,00",
                    lps,
                    RespInf, DataAtual.voltaData(dtenvio.toString().replaceAll("-", ""), "dd/MM/aaaa"));
            pdf.FechaPdf();
            pdf.service();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

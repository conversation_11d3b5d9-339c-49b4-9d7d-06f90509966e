/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.cofre;

import Arquivo.ArquivoLog;
import Controller.CofreInteligente.DashBoardsSatMobWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.TesCofresMov;
import SasBeansCompostas.CofreDashBoardGeral;
import SasBeansCompostas.CofreDashBoardStatus;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.utils.Mascaras.Data;
import static br.com.sasw.utils.Mascaras.Hora;
import static br.com.sasw.utils.Mascaras.HoraSegundos;
import static br.com.sasw.utils.Mascaras.Moeda;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

/**
 *
 * <AUTHOR>
 */
@Named(value = "dashboardCofreGeral")
@ViewScoped
public class DashBoardGeralMB implements Serializable {

    private Persistencia persistencia;
    private Map filters, filtroMovimentacoes;
    private Filiais filiais;
    private final DashBoardsSatMobWeb dashBoardsSatMobWeb;
    private final String codfil, caminho, banco, operador;
    private final BigDecimal codpessoa;
    private final ArquivoLog logerro;
    private String log, dia, mes, ano, versaoAtual,
            totalCofres, totalDepositos, totalColetas,
            tabelaMovimentacoes, tabelaMovimentacoesCofres, tabelaSaldos, tabelaStatus,
            labelEstatisticaPorHora, dadosDepositosPorHora, dadosColetasPorHora, dadosCofresDesatualizados, dadosCofresOffline, dadosCofreBateria;
    private List<String> diasMes;
    private List<CofreDashBoardGeral> dadosCofreDashboardGeral;
    private List<CofreDashBoardStatus> dadosCofreDashboardStatus;
    private List<TesCofresMov> movimentacoes, movimentacoesCofre;
    private LocalDateTime horaAtual, calendario;
    private boolean exibirTodos;

    public DashBoardGeralMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        try {
            exibirTodos = (boolean) fc.getExternalContext().getSessionMap().get("menu");
        } catch (Exception ex) {
        }
        dashBoardsSatMobWeb = new DashBoardsSatMobWeb();
        logerro = new ArquivoLog();

        dia = getDataAtual("TELA").split("/")[0];
        mes = getDataAtual("TELA").split("/")[1];
        ano = getDataAtual("TELA").split("/")[2];
    }

    public void Persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;

            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            if (this.codfil.equals("")) {
                this.filiais = null;
            } else {
                this.filiais = this.dashBoardsSatMobWeb.buscaInfoFilial(this.codfil, this.persistencia);
            }

            this.filters = new HashMap();
            this.filtroMovimentacoes = new HashMap();
            this.filtroMovimentacoes.put(" TesCofresMov.data = ? ", this.ano + this.mes + this.dia);

            if (!this.exibirTodos) {
                this.filters.put(" pessoacliaut.codigo = ? ", this.codpessoa.toPlainString());
                this.filters.put(" pessoacliaut.Flag_Excl <> ? ", "*");

                this.filtroMovimentacoes.put(" pessoacliaut.codigo = ? ", this.codpessoa.toPlainString());
                this.filtroMovimentacoes.put(" pessoacliaut.Flag_Excl <> ? ", "*");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void carregarGraficos() {
        carregarMovimentacoes();
        carregarMovimentacoesPorCofre();
        carregarEstatisticaPorHora();
        carregarEstatisticaPorCofre();
    }

    public void carregarEstatisticaPorHora() {
        try {
            this.dadosCofreDashboardGeral = this.dashBoardsSatMobWeb.obterEstatisticaPorHora(this.filtroMovimentacoes, this.exibirTodos, this.persistencia);
            this.totalCofres = "0";
            this.totalDepositos = "0";
            this.totalColetas = "0";

            String labels = "", qtdDepositos = "", qtdColetas = "";
            for (CofreDashBoardGeral cofreDashBoardGeral : this.dadosCofreDashboardGeral) {
                // Se o gráfico for na data de hora, não há informações além da hora atual.
                if (cofreDashBoardGeral.getData().equals(getDataAtual("SQL"))
                        && Integer.valueOf(cofreDashBoardGeral.getHora()) > Integer.valueOf(getDataAtual("HORA").split(":")[0])) {
                    break;
                }
                this.totalCofres = new BigDecimal(this.totalCofres)
                        .compareTo(new BigDecimal(cofreDashBoardGeral.getCofresAtivos())) == 1
                        ? this.totalCofres : cofreDashBoardGeral.getCofresAtivos();

                this.totalDepositos = new BigDecimal(this.totalDepositos)
                        .add(new BigDecimal(cofreDashBoardGeral.getQtdDepositos()))
                        .toBigInteger().toString();

                this.totalColetas = new BigDecimal(this.totalColetas)
                        .add(new BigDecimal(cofreDashBoardGeral.getQtdColetas()))
                        .toBigInteger().toString();

                labels += !labels.equals("") ? ",'" + cofreDashBoardGeral.getHora() + ":00'" : "'" + cofreDashBoardGeral.getHora() + ":00'";
                qtdDepositos += !qtdDepositos.equals("") ? ",'" + cofreDashBoardGeral.getQtdDepositos() + "'" : "'" + cofreDashBoardGeral.getQtdDepositos() + "'";
                qtdColetas += !qtdColetas.equals("") ? ",'" + cofreDashBoardGeral.getQtdColetas() + "'" : "'" + cofreDashBoardGeral.getQtdColetas() + "'";

            }

            this.labelEstatisticaPorHora = "[" + labels + "]";
            this.dadosDepositosPorHora = "[" + qtdDepositos + "]";
            this.dadosColetasPorHora = "[" + qtdColetas + "]";

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void carregarEstatisticaPorCofre() {
        try {
            this.dadosCofreDashboardStatus = this.dashBoardsSatMobWeb.obterEstatisticaPorCofre(this.filters, this.exibirTodos, this.persistencia);
            this.horaAtual = LocalDateTime.now();
            long dif;

            StringBuilder tabelaSaldosSB = new StringBuilder("<table id=\"tblSaldosCofres\" grid-style=\"dark\">\n");
            tabelaSaldosSB.append("<thead>\n");
            tabelaSaldosSB.append("<tr><th>").append(getMessageS("Cofre")).append("</th>\n");
            tabelaSaldosSB.append("<th>").append(getMessageS("Local")).append("</th>\n");
            tabelaSaldosSB.append("<th>").append(getMessageS("Saldo")).append("</th>\n");
            tabelaSaldosSB.append("<th>").append(getMessageS("UltimaMovimentacao")).append("</th>\n");
            tabelaSaldosSB.append("<th>").append(getMessageS("Valor")).append("</th>\n");
            tabelaSaldosSB.append("<th>").append(getMessageS("Data")).append("</th>\n");
            tabelaSaldosSB.append("<th>").append(getMessageS("Hora")).append("</th></tr>\n");
            tabelaSaldosSB.append("<tr><th colspan=\"7\"><input type=\"text\" /></th></tr>");
            tabelaSaldosSB.append("</thead>\n");
            tabelaSaldosSB.append("<tbody>\n");

            StringBuilder tabelaStatusSB = new StringBuilder("<table id=\"tblStatusCofres\" grid-style=\"dark\">\n");
            tabelaStatusSB.append("<thead>\n");
            tabelaStatusSB.append("<tr><th></th><th>").append(getMessageS("Cofre")).append("</th>\n");
            tabelaStatusSB.append("<th>").append(getMessageS("Local")).append("</th>\n");
            tabelaStatusSB.append("<th>").append(getMessageS("Bateria")).append("</th>\n");
            tabelaStatusSB.append("<th>").append(getMessageS("Versao")).append("</th>\n");
            tabelaStatusSB.append("<th>").append(getMessageS("Data")).append("</th>\n");
            tabelaStatusSB.append("<th>").append(getMessageS("Hora")).append("</th>\n");
            tabelaStatusSB.append("<th>").append(getMessageS("Status")).append("</th></tr>\n");
            tabelaStatusSB.append("<tr><th colspan=\"8\"><input type=\"text\" /></th></tr>");
            tabelaStatusSB.append("</thead>\n");
            tabelaStatusSB.append("<tbody>\n");

//            this.dadosCofresDesatualizados, dadosCofresOffline, dadosCofreBateria;
            int cofresAtualizados = 0, cofresDesatualizados = 0;
            int cofresOnline = 0, cofresOffline = 0, cofresDesligados = 0;
            int cofresBateria100 = 0, cofresBateria50_100 = 0, cofresBateria0_50 = 0, bateria = 0;

            if (!this.dadosCofreDashboardStatus.isEmpty()) {
                this.versaoAtual = this.dadosCofreDashboardStatus.get(0).getVersaoAtual();
            }

            for (CofreDashBoardStatus cofreDashBoardStatus : this.dadosCofreDashboardStatus) {

                // Tabela Saldo Cofre
                tabelaSaldosSB.append("<tr><td>").append(cofreDashBoardStatus.getCodCofre()).append("</td>");
                tabelaSaldosSB.append("<td>").append(cofreDashBoardStatus.getNRed()).append("</td>");
                tabelaSaldosSB.append("<td>").append(Moeda(cofreDashBoardStatus.getSaldo())).append("</td>");
                tabelaSaldosSB.append("<td>")
                        .append(cofreDashBoardStatus.getTipoDepositoMov().toUpperCase().equals("DINHEIRO")
                                ? getMessageS("Deposito") : (cofreDashBoardStatus.getTipoDepositoMov().toUpperCase().equals("COLETA")
                                ? getMessageS("Coleta") : getMessageS(cofreDashBoardStatus.getTipoDepositoMov())))
                        .append("</td>");
                tabelaSaldosSB.append("<td>").append(Moeda(cofreDashBoardStatus.getValorMov())).append("</td>");
                tabelaSaldosSB.append("<td>").append(Data(cofreDashBoardStatus.getDataMov())).append("</td>");
                tabelaSaldosSB.append("<td>").append(HoraSegundos(cofreDashBoardStatus.getHoraMov()).replace(" ", "&nbsp;")).append("</td></tr>");

                // Tabela Status Cofre
                if (cofreDashBoardStatus.getDataSituacao() != null && !cofreDashBoardStatus.getDataSituacao().equals("")) {
                    this.calendario = LocalDateTime
                            .parse(cofreDashBoardStatus.getDataSituacao() + cofreDashBoardStatus.getHorasituacao(),
                                    DateTimeFormatter.ofPattern("yyyyMMddHH:mm"));

                    dif = this.calendario.until(this.horaAtual, ChronoUnit.MINUTES);

                    if (dif > 60) {
                        tabelaStatusSB.append("<tr><td>").append("<i class=\"icon-wifi_off\" aria-hidden=\"true\"></i>").append("</td>");
                        cofresDesligados++;
                    } else if (dif <= 60 && dif > 15) {
                        tabelaStatusSB.append("<tr><td>").append("<i class=\"icon-wifi_low\" aria-hidden=\"true\"></i>").append("</td>");
                        cofresOffline++;
                    } else {
                        tabelaStatusSB.append("<tr><td>").append("<i class=\"icon-wifi\" aria-hidden=\"true\"></i>").append("</td>");
                        cofresOnline++;
                    }

                    tabelaStatusSB.append("<td>").append(cofreDashBoardStatus.getCodCofre()).append("</td>");
                    tabelaStatusSB.append("<td>").append(cofreDashBoardStatus.getNRed()).append("</td>");
                    tabelaStatusSB.append("<td>").append(cofreDashBoardStatus.getBateria().replace(".0", "")).append("</td>");
                    if (cofreDashBoardStatus.getBateria() != null && !cofreDashBoardStatus.getBateria().equals("")) {
                        bateria = (int) Double.parseDouble(cofreDashBoardStatus.getBateria());
                        if (bateria == 100) {
                            cofresBateria100++;
                        } else if (bateria < 100 && bateria > 50) {
                            cofresBateria50_100++;
                        } else {
                            cofresBateria0_50++;
                        }
                    }
                    tabelaStatusSB.append("<td>").append(cofreDashBoardStatus.getVersao()).append("</td>");
                    if (cofreDashBoardStatus.getVersaoAtual().equals(cofreDashBoardStatus.getVersao())) {
                        cofresAtualizados++;
                    } else if (cofreDashBoardStatus.getVersao() != null && !cofreDashBoardStatus.getVersao().equals("")) {
                        cofresDesatualizados++;
                    }
                    tabelaStatusSB.append("<td>").append(Data(cofreDashBoardStatus.getDataSituacao())).append("</td>");
                    tabelaStatusSB.append("<td>").append(Hora(cofreDashBoardStatus.getHorasituacao())).append("</td>");

                    /**
                     * +--------------------+ | o Porta Superior | 1 Aberto , 0
                     * Fechado | o porta Cofre | 1 Aberto , 0 Fechado | o
                     * Cassete | 1 Aberto , 0 Fechado | o Fechadura | 1 Aberto ,
                     * 0 Fechado | o Validador | 1 Conectado, 0 Desconectado | o
                     * Impressao | 1 Conectado, 0 Desconectado
                     * +--------------------+
                     */
                    tabelaStatusSB.append("<td style=\"text-align: left;\">");
                    tabelaStatusSB.append("<label>")
                            .append(cofreDashBoardStatus.getSituacao().substring(0, 1).equals("1") ? "<i style=\"color: yellow\" class=\"fa fa-exclamation-triangle\"></i>"
                                    : (cofreDashBoardStatus.getSituacao().substring(0, 1).equals("0") ? "<i style=\"color: green\" class=\"fa fa-check-circle\"></i>"
                                    : "<i style=\"color: red\" class=\"fa fa-exclamation-circle\"></i>"))
                            .append(" ")
                            .append(getMessageS("PortaSuperior"))
                            .append("</label></br>");
                    tabelaStatusSB.append("<label>")
                            .append(cofreDashBoardStatus.getSituacao().substring(1, 2).equals("1") ? "<i style=\"color: yellow\" class=\"fa fa-exclamation-triangle\"></i>"
                                    : (cofreDashBoardStatus.getSituacao().substring(1, 2).equals("0") ? "<i style=\"color: green\" class=\"fa fa-check-circle\"></i>"
                                    : "<i style=\"color: red\" class=\"fa fa-exclamation-circle\"></i>"))
                            .append(" ")
                            .append(getMessageS("PortaCofre"))
                            .append("</label></br>");
                    tabelaStatusSB.append("<label>")
                            .append(cofreDashBoardStatus.getSituacao().substring(2, 3).equals("1") ? "<i style=\"color: yellow\" class=\"fa fa-exclamation-triangle\"></i>"
                                    : (cofreDashBoardStatus.getSituacao().substring(2, 3).equals("0") ? "<i style=\"color: green\" class=\"fa fa-check-circle\"></i>"
                                    : "<i style=\"color: red\" class=\"fa fa-exclamation-circle\"></i>"))
                            .append(" ")
                            .append(getMessageS("Cassete"))
                            .append("</label></br>");
                    tabelaStatusSB.append("<label>")
                            .append(cofreDashBoardStatus.getSituacao().substring(4, 5).equals("1") ? "<i style=\"color: yellow\" class=\"fa fa-exclamation-triangle\"></i>"
                                    : (cofreDashBoardStatus.getSituacao().substring(4, 5).equals("0") ? "<i style=\"color: green\" class=\"fa fa-check-circle\"></i>"
                                    : "<i style=\"color: red\" class=\"fa fa-exclamation-circle\"></i>"))
                            .append(" ")
                            .append(getMessageS("Validadora"))
                            .append("</label></br>");
                    tabelaStatusSB.append("</td></tr>");
                }
            }

            tabelaSaldosSB.append("</tbody>\n");
            tabelaSaldosSB.append("</table>\n");

            tabelaStatusSB.append("</tbody>\n");
            tabelaStatusSB.append("</table>\n");

            this.tabelaSaldos = tabelaSaldosSB.toString();
            this.tabelaStatus = tabelaStatusSB.toString();

            this.dadosCofresDesatualizados = "[" + (cofresAtualizados > 0 ? cofresAtualizados + "," : "") + (cofresDesatualizados > 0 ? cofresDesatualizados + "," : "") + "]";
            this.dadosCofresOffline = "[" + (cofresOnline > 0 ? cofresOnline + "," : "0,")
                    + (cofresOffline > 0 ? cofresOffline + "," : "0,")
                    + (cofresDesligados > 0 ? cofresDesligados + "," : "0,") + "]";
            this.dadosCofreBateria = "[" + (cofresBateria100 > 0 ? cofresBateria100 + "," : "0,")
                    + (cofresBateria50_100 > 0 ? cofresBateria50_100 + "," : "0,")
                    + (cofresBateria0_50 > 0 ? cofresBateria0_50 + "," : "0,") + "]";
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void carregarMovimentacoesPorCofre() {
        try {
            this.movimentacoesCofre = this.dashBoardsSatMobWeb.detalhesMovimentacaoPorCofre(this.filters, this.exibirTodos, this.persistencia);

            StringBuilder tabela = new StringBuilder("<table id=\"tblUltimasMovimentacoesCofre\" grid-style=\"dark\">\n");
            tabela.append("<thead>\n");
            tabela.append("<tr><th>").append(getMessageS("Data")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Hora")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Cofre")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Local")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Valor")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Movimentacao")).append("</th></tr>\n");
            tabela.append("<tr><th colspan=\"6\"><input type=\"text\" /></th></tr>");
            tabela.append("</thead>\n");
            tabela.append("<tbody>\n");

            for (TesCofresMov tesCofresMov : this.movimentacoesCofre) {
                if (tesCofresMov.getData() == null) {
                    tabela.append("<tr><td>").append("</td>");
                } else {
                    tabela.append("<tr><td>").append(Data(tesCofresMov.getData().toString())).append("</td>");
                }
                tabela.append("<td>").append(HoraSegundos(tesCofresMov.getHora()).replace(" ", "&nbsp;")).append("</td>");
                tabela.append("<td>").append(tesCofresMov.getCodCofre().toBigInteger()).append("</td>");
                tabela.append("<td>").append(tesCofresMov.getNRedCofre()).append("</td>");
                if (tesCofresMov.getValorDeposito() == null) {
                    tabela.append("<td>").append(Moeda("0.00")).append("</td>");
                } else {
                    tabela.append("<td>").append(Moeda(tesCofresMov.getValorDeposito().toPlainString())).append("</td>");
                }
                tabela.append("<td>")
                        .append(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                ? getMessageS("Deposito") : (tesCofresMov.getTipoDeposito().toUpperCase().equals("COLETA")
                                ? getMessageS("Coleta") : getMessageS(tesCofresMov.getTipoDeposito())))
                        .append("</td></tr>");
            }

            tabela.append("</tbody>\n");
            tabela.append("</table>\n");

            this.tabelaMovimentacoesCofres = tabela.toString();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void carregarMovimentacoes() {
        try {
            this.filtroMovimentacoes.replace(" TesCofresMov.data = ? ", this.ano + this.mes + this.dia);
            this.movimentacoes = this.dashBoardsSatMobWeb.detalhesMovimentacao(this.filtroMovimentacoes, this.exibirTodos, this.persistencia);

            StringBuilder tabela = new StringBuilder("<table id=\"tblUltimasMovimentacoes\" grid-style=\"dark\">\n");
            tabela.append("<thead>\n");
            tabela.append("<tr><th>").append(getMessageS("Hora")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Cofre")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Local")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Valor")).append("</th>\n");
            tabela.append("<th>").append(getMessageS("Movimentacao")).append("</th></tr>\n");
            tabela.append("<tr><th colspan=\"5\"><input type=\"text\" /></th></tr>");
            tabela.append("</thead>\n");
            tabela.append("<tbody>\n");

            for (TesCofresMov tesCofresMov : this.movimentacoes) {
                tabela.append("<tr><td>").append(HoraSegundos(tesCofresMov.getHora()).replace(" ", "&nbsp;")).append("</td>");
                tabela.append("<td>").append(tesCofresMov.getCodCofre().toBigInteger()).append("</td>");
                tabela.append("<td>").append(tesCofresMov.getNRedCofre()).append("</td>");
                tabela.append("<td>").append(Moeda(tesCofresMov.getValorDeposito().toPlainString())).append("</td>");
                tabela.append("<td>")
                        .append(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                ? getMessageS("Deposito") : (tesCofresMov.getTipoDeposito().toUpperCase().equals("COLETA")
                                ? getMessageS("Coleta") : getMessageS(tesCofresMov.getTipoDeposito())))
                        .append("</td></tr>");
            }

            tabela.append("</tbody>\n");
            tabela.append("</table>\n");

            this.tabelaMovimentacoes = tabela.toString();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getTotalCofres() {
        return totalCofres;
    }

    public void setTotalCofres(String totalCofres) {
        this.totalCofres = totalCofres;
    }

    public String getTotalDepositos() {
        return totalDepositos;
    }

    public void setTotalDepositos(String totalDepositos) {
        this.totalDepositos = totalDepositos;
    }

    public String getTotalColetas() {
        return totalColetas;
    }

    public void setTotalColetas(String totalColetas) {
        this.totalColetas = totalColetas;
    }

    public String getLabelEstatisticaPorHora() {
        return labelEstatisticaPorHora;
    }

    public void setLabelEstatisticaPorHora(String labelEstatisticaPorHora) {
        this.labelEstatisticaPorHora = labelEstatisticaPorHora;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getMes() {
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public String getAno() {
        return ano;
    }

    public void setAno(String ano) {
        this.ano = ano;
    }

    public List<String> getDiasMes() {
        diasMes = new ArrayList<>();

        if (mes == null) {
            mes = getDataAtual("TELA").split("/")[1];
        }
        if (ano == null) {
            ano = getDataAtual("TELA").split("/")[2];
        }

        YearMonth yearMonthObject = YearMonth.of(Integer.valueOf(ano), Integer.valueOf(mes));
        for (int i = 1; i <= yearMonthObject.lengthOfMonth(); i++) {
            diasMes.add(i < 10 ? "0" + String.valueOf(i) : String.valueOf(i));
        }
        return diasMes;
    }

    public void setDiasMes(List<String> diasMes) {
        this.diasMes = diasMes;
    }

    public String getDadosDepositosPorHora() {
        return dadosDepositosPorHora;
    }

    public void setDadosDepositosPorHora(String dadosDepositosPorHora) {
        this.dadosDepositosPorHora = dadosDepositosPorHora;
    }

    public String getDadosColetasPorHora() {
        return dadosColetasPorHora;
    }

    public void setDadosColetasPorHora(String dadosColetasPorHora) {
        this.dadosColetasPorHora = dadosColetasPorHora;
    }

    public String getTabelaSaldos() {
        return tabelaSaldos;
    }

    public void setTabelaSaldos(String tabelaSaldos) {
        this.tabelaSaldos = tabelaSaldos;
    }

    public String getTabelaStatus() {
        return tabelaStatus;
    }

    public void setTabelaStatus(String tabelaStatus) {
        this.tabelaStatus = tabelaStatus;
    }

    public String getTabelaMovimentacoes() {
        return tabelaMovimentacoes;
    }

    public void setTabelaMovimentacoes(String tabelaMovimentacoes) {
        this.tabelaMovimentacoes = tabelaMovimentacoes;
    }

    public String getDadosCofresDesatualizados() {
        return dadosCofresDesatualizados;
    }

    public void setDadosCofresDesatualizados(String dadosCofresDesatualizados) {
        this.dadosCofresDesatualizados = dadosCofresDesatualizados;
    }

    public String getDadosCofresOffline() {
        return dadosCofresOffline;
    }

    public void setDadosCofresOffline(String dadosCofresOffline) {
        this.dadosCofresOffline = dadosCofresOffline;
    }

    public String getDadosCofreBateria() {
        return dadosCofreBateria;
    }

    public void setDadosCofreBateria(String dadosCofreBateria) {
        this.dadosCofreBateria = dadosCofreBateria;
    }

    public String getVersaoAtual() {
        return versaoAtual;
    }

    public void setVersaoAtual(String versaoAtual) {
        this.versaoAtual = versaoAtual;
    }

    public String getTabelaMovimentacoesCofres() {
        return tabelaMovimentacoesCofres;
    }

    public void setTabelaMovimentacoesCofres(String tabelaMovimentacoesCofres) {
        this.tabelaMovimentacoesCofres = tabelaMovimentacoesCofres;
    }
}

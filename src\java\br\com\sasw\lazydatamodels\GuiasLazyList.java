/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Guias.GuiasSatWeb;
import Dados.Persistencia;
import SasBeans.GuiasCliente;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class GuiasLazyList extends LazyDataModel<GuiasCliente> {

    private static final long serialVersionUID = 1L;
    private List<GuiasCliente> guias;
    private final GuiasSatWeb guiasweb;
    private Persistencia persistencia;

    public GuiasLazyList(Persistencia pst) {
        this.guiasweb = new GuiasSatWeb();
        this.persistencia = pst;
    }

    @Override
    public List<GuiasCliente> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.guias = this.guiasweb.listagemPaginada(first, pageSize, filters, this.persistencia);

            for (GuiasCliente g : this.guias) {
                if (g.getAssinado().contains("Chefe Equipe")) {
                    g.setAssinado(g.getAssinado().replaceFirst("Chefe Equipe", Messages.getMessageS("ChefeEquipe")));
                }
            }

            // set the total of players
            setRowCount(this.guiasweb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.guias;
    }

    @Override
    public Object getRowKey(GuiasCliente guiaCliente) {
        return guiaCliente.getGuia();
    }

    @Override
    public GuiasCliente getRowData(String guia) {
        for (GuiasCliente guiaCliente : this.guias) {
            if (guia.equals(guiaCliente.getGuia())) {
                return guiaCliente;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Regiao;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RegiaoDao {

    public boolean inserirRegiao(Regiao regiao, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Regiao (CodFil, Regiao, Descricao, Area, Local, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(regiao.getCodFil());
            consulta.setString(regiao.getRegiao());
            consulta.setString(regiao.getDescricao());
            consulta.setString(regiao.getArea());
            consulta.setString(regiao.getLocal());
            consulta.setString(regiao.getOperador());
            consulta.setString(regiao.getDt_Alter());
            consulta.setString(regiao.getHr_Alter());
            int retorno = consulta.insert();
            consulta.Close();
            return retorno > 0;
        } catch (Exception e) {
            throw new Exception("RegiaoDao.listarRegioes - " + e.getMessage() + "\r\n"
                    + " INSERT INTO Regiao (CodFil, Regiao, Descricao, Area, Local, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (" + regiao.getCodFil() + ", " + regiao.getRegiao() + ", " + regiao.getDescricao() + ", " + regiao.getArea() + ", "
                    + regiao.getLocal() + ", " + regiao.getOperador() + ", " + regiao.getDt_Alter() + ", " + regiao.getHr_Alter() + ") ");
        }
    }

    public boolean existeRegiao(String regiao, String codFil, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = false;
            String sql = " SELECT * FROM Regiao WHERE Regiao = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(regiao);
            consulta.setString(codFil);
            consulta.select();
            retorno = consulta.Proximo();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RegiaoDao.listarRegioes - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Regiao WHERE Regiao = " + regiao + " AND CodFil = " + codFil);
        }
    }

    public List<Regiao> listarRegioes(String codfil, Persistencia persistencia) throws Exception {
        try {
            List<Regiao> retorno = new ArrayList<>();
            String sql = " SELECT * FROM Regiao WHERE CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.select();
            Regiao regiao;
            while (consulta.Proximo()) {
                regiao = new Regiao();
                regiao.setCodFil(consulta.getString("CodFil").replace(".0", ""));
                regiao.setRegiao(consulta.getString("Regiao").replace(".0", ""));
                regiao.setDescricao(consulta.getString("Descricao"));
                regiao.setArea(consulta.getString("Area"));
                regiao.setLocal(consulta.getString("Local"));
                regiao.setOperador(consulta.getString("Operador"));
                regiao.setDt_Alter(consulta.getString("Dt_Alter"));
                regiao.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(regiao);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RegiaoDao.listarRegioes - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Regiao WHERE CodFil = " + codfil);
        }
    }
}

package SasBeans;

import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class NFiscal {

    private String Praca;
    private String Serie;
    private String Numero;
    private BigDecimal CodFil;
    private LocalDate Data;
    private String Situacao;
    private LocalDate Dt_Situac;
    private String CliFat;
    private String CCusto;
    private String NRed;
    private BigDecimal CodHist;
    private BigDecimal SeloFiscal;
    private BigDecimal Valor;
    private BigDecimal ValorLiq;
    private String CFOP;
    private String CFOp_Descr;
    private BigDecimal TCobran;
    private String Compet;
    private LocalDate Dt_Venc;
    private BigDecimal AliqIRRF;
    private BigDecimal IRRF;
    private String IRRFRet;
    private BigDecimal AliqICMS;
    private BigDecimal ICMS;
    private String ICMSRet;
    private BigDecimal AliqISS;
    private BigDecimal ISS;
    private String ISSRet;
    private BigDecimal BaseINSSPerc;
    private BigDecimal BaseINSS;
    private BigDecimal AliqINSS;
    private BigDecimal INSS;
    private String INSSRet;
    private BigDecimal AliqPIS;
    private BigDecimal PIS;
    private String PISRet;
    private BigDecimal AliqCOFINS;
    private BigDecimal COFINS;
    private String COFINSRet;
    private BigDecimal AliqCSL;
    private BigDecimal CSL;
    private String CSLRet;
    private String NossoNumero;
    private BigDecimal AliqIRPJ;
    private BigDecimal IRPJ;
    private String IRPJRet;
    private String RetEsp;
    private BigDecimal RetEspValor;
    private Integer AgrupISS;
    private String Boleto;
    private LocalDate Dt_Pagto;
    private BigDecimal Juros;
    private BigDecimal Descontos;
    private BigDecimal ValorPago;
    private Integer ContaFin;
    private String Obs;
    private BigDecimal CodReceber;
    private Integer SitCob;
    private BigDecimal ValorFrete;
    private BigDecimal NumCalculo;
    private BigDecimal NumVenda;
    private Integer NFEmissao;
    private String NFERetorno;
    private String NFEChave;
    private String Carne;
    private BigDecimal OS;
    private String Oper_Incl;
    private LocalDate Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;
    private String Oper_Canc;
    private LocalDate Dt_Canc;
    private String Hr_Canc;
    private String MotivCanc;
    private String Auditada;
    private String Auditor;
    private LocalDate Dt_audit;
    private String Hr_Audit;
    private String link;

    private String NRedPraca;
    private String Dt_Nota;
    private String Hr_Nota;
    private String Dt_Envio;
    private String Hr_Envio;
    private String Dt_Retorno;
    private String Hr_Retorno;

    public String getPraca() {
        return Praca;
    }

    public void setPraca(String Praca) {
        this.Praca = Praca;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getNumeroFormatado() {
        String n = FuncoesString.PreencheEsquerda(Numero, 9, "0");
        StringBuilder sb = new StringBuilder();
        for (int i = 1; i <= 9; i++) {
            sb.append(n.charAt(i - 1));
            if (i % 3 == 0 && i != 9) {
                sb.append('.');
            }
        }
        return sb.toString();
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getNumero() {
        return Numero;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public LocalDate getDt_Situac() {
        return Dt_Situac;
    }

    public void setDt_Situac(LocalDate Dt_Situac) {
        this.Dt_Situac = Dt_Situac;
    }

    public String getCliFat() {
        return CliFat;
    }

    public void setCliFat(String CliFat) {
        this.CliFat = CliFat;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public BigDecimal getCodHist() {
        return CodHist;
    }

    public void setCodHist(String CodHist) {
        try {
            this.CodHist = new BigDecimal(CodHist);
        } catch (Exception e) {
            this.CodHist = new BigDecimal("0");
        }
    }

    public BigDecimal getSeloFiscal() {
        return SeloFiscal;
    }

    public void setSeloFiscal(String SeloFiscal) {
        try {
            this.SeloFiscal = new BigDecimal(SeloFiscal);
        } catch (Exception e) {
            this.SeloFiscal = new BigDecimal("0");
        }
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public BigDecimal getValorLiq() {
        return ValorLiq;
    }

    public void setValorLiq(String ValorLiq) {
        try {
            this.ValorLiq = new BigDecimal(ValorLiq);
        } catch (Exception e) {
            this.ValorLiq = new BigDecimal("0");
        }
    }

    public String getCFOP() {
        return CFOP;
    }

    public void setCFOP(String CFOP) {
        this.CFOP = CFOP;
    }

    public String getCFOp_Descr() {
        return CFOp_Descr;
    }

    public void setCFOp_Descr(String CFOp_Descr) {
        this.CFOp_Descr = CFOp_Descr;
    }

    public BigDecimal getTCobran() {
        return TCobran;
    }

    public void setTCobran(String TCobran) {
        try {
            this.TCobran = new BigDecimal(TCobran);
        } catch (Exception e) {
            this.TCobran = new BigDecimal("0");
        }
    }

    public String getCompet() {
        return Compet;
    }

    public void setCompet(String Compet) {
        this.Compet = Compet;
    }

    public LocalDate getDt_Venc() {
        return Dt_Venc;
    }

    public void setDt_Venc(LocalDate Dt_Venc) {
        this.Dt_Venc = Dt_Venc;
    }

    public BigDecimal getAliqIRRF() {
        return AliqIRRF;
    }

    public void setAliqIRRF(String AliqIRRF) {
        try {
            this.AliqIRRF = new BigDecimal(AliqIRRF);
        } catch (Exception e) {
            this.AliqIRRF = new BigDecimal("0");
        }
    }

    public BigDecimal getIRRF() {
        return IRRF;
    }

    public void setIRRF(String IRRF) {
        try {
            this.IRRF = new BigDecimal(IRRF);
        } catch (Exception e) {
            this.IRRF = new BigDecimal(IRRF);
        }
    }

    public String getIRRFRet() {
        return IRRFRet;
    }

    public void setIRRFRet(String IRRFRet) {
        this.IRRFRet = IRRFRet;
    }

    public BigDecimal getAliqICMS() {
        return AliqICMS;
    }

    public void setAliqICMS(String AliqICMS) {
        try {
            this.AliqICMS = new BigDecimal(AliqICMS);
        } catch (Exception e) {
            this.AliqICMS = new BigDecimal("0");
        }
    }

    public BigDecimal getICMS() {
        return ICMS;
    }

    public void setICMS(String ICMS) {
        try {
            this.ICMS = new BigDecimal(ICMS);
        } catch (Exception e) {
            this.ICMS = new BigDecimal("0");
        }
    }

    public String getICMSRet() {
        return ICMSRet;
    }

    public void setICMSRet(String ICMSRet) {
        this.ICMSRet = ICMSRet;
    }

    public BigDecimal getAliqISS() {
        return AliqISS;
    }

    public void setAliqISS(String AliqISS) {
        try {
            this.AliqISS = new BigDecimal(AliqISS);
        } catch (Exception e) {
            this.AliqISS = new BigDecimal("0");
        }
    }

    public BigDecimal getISS() {
        return ISS;
    }

    public void setISS(String ISS) {
        try {
            this.ISS = new BigDecimal(ISS);
        } catch (Exception e) {
            this.ISS = new BigDecimal("0");
        }
    }

    public String getISSRet() {
        return ISSRet;
    }

    public void setISSRet(String ISSRet) {
        this.ISSRet = ISSRet;
    }

    public BigDecimal getBaseINSSPerc() {
        return BaseINSSPerc;
    }

    public void setBaseINSSPerc(String BaseINSSPerc) {
        try {
            this.BaseINSSPerc = new BigDecimal(BaseINSSPerc);
        } catch (Exception e) {
            this.BaseINSSPerc = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseINSS() {
        return BaseINSS;
    }

    public void setBaseINSS(String BaseINSS) {
        try {
            this.BaseINSS = new BigDecimal(BaseINSS);
        } catch (Exception e) {
            this.BaseINSSPerc = new BigDecimal("0");
        }
    }

    public BigDecimal getAliqINSS() {
        return AliqINSS;
    }

    public void setAliqINSS(String AliqINSS) {
        try {
            this.AliqINSS = new BigDecimal(AliqINSS);
        } catch (Exception e) {
            this.AliqINSS = new BigDecimal("0");
        }
    }

    public BigDecimal getINSS() {
        return INSS;
    }

    public void setINSS(String INSS) {
        try {
            this.INSS = new BigDecimal(INSS);
        } catch (Exception e) {
            this.INSS = new BigDecimal("0");
        }
    }

    public String getINSSRet() {
        return INSSRet;
    }

    public void setINSSRet(String INSSRet) {
        this.INSSRet = INSSRet;
    }

    public BigDecimal getAliqPIS() {
        return AliqPIS;
    }

    public void setAliqPIS(String AliqPIS) {
        try {
            this.AliqPIS = new BigDecimal(AliqPIS);
        } catch (Exception e) {
            this.AliqPIS = new BigDecimal("0");
        }
    }

    public BigDecimal getPIS() {
        return PIS;
    }

    public void setPIS(String PIS) {
        try {
            this.PIS = new BigDecimal(PIS);
        } catch (Exception e) {
            this.PIS = new BigDecimal("0");
        }
    }

    public String getPISRet() {
        return PISRet;
    }

    public void setPISRet(String PISRet) {
        this.PISRet = PISRet;
    }

    public BigDecimal getAliqCOFINS() {
        return AliqCOFINS;
    }

    public void setAliqCOFINS(String AliqCOFINS) {
        try {
            this.AliqCOFINS = new BigDecimal(AliqCOFINS);
        } catch (Exception e) {
            this.AliqCOFINS = new BigDecimal("0");
        }
    }

    public BigDecimal getCOFINS() {
        return COFINS;
    }

    public void setCOFINS(String COFINS) {
        try {
            this.COFINS = new BigDecimal(COFINS);
        } catch (Exception e) {
            this.COFINS = new BigDecimal("0");
        }
    }

    public String getCOFINSRet() {
        return COFINSRet;
    }

    public void setCOFINSRet(String COFINSRet) {
        this.COFINSRet = COFINSRet;
    }

    public BigDecimal getAliqCSL() {
        return AliqCSL;
    }

    public void setAliqCSL(String AliqCSL) {
        try {
            this.AliqCSL = new BigDecimal(AliqCSL);
        } catch (Exception e) {
            this.AliqCSL = new BigDecimal("0");
        }
    }

    public BigDecimal getCSL() {
        return CSL;
    }

    public void setCSL(String CSL) {
        try {
            this.CSL = new BigDecimal(CSL);
        } catch (Exception e) {
            this.CSL = new BigDecimal("0");
        }
    }

    public String getCSLRet() {
        return CSLRet;
    }

    public void setCSLRet(String CSLRet) {
        this.CSLRet = CSLRet;
    }

    public String getNossoNumero() {
        return NossoNumero;
    }

    public void setNossoNumero(String NossoNumero) {
        this.NossoNumero = NossoNumero;
    }

    public BigDecimal getAliqIRPJ() {
        return AliqIRPJ;
    }

    public void setAliqIRPJ(String AliqIRPJ) {
        try {
            this.AliqIRPJ = new BigDecimal(AliqIRPJ);
        } catch (Exception e) {
            this.AliqIRPJ = new BigDecimal("0");
        }
    }

    public BigDecimal getIRPJ() {
        return IRPJ;
    }

    public void setIRPJ(String IRPJ) {
        try {
            this.IRPJ = new BigDecimal(IRPJ);
        } catch (Exception e) {
            this.IRPJ = new BigDecimal("0");
        }
    }

    public String getIRPJRet() {
        return IRPJRet;
    }

    public void setIRPJRet(String IRPJRet) {
        this.IRPJRet = IRPJRet;
    }

    public String getRetEsp() {
        return RetEsp;
    }

    public void setRetEsp(String RetEsp) {
        this.RetEsp = RetEsp;
    }

    public BigDecimal getRetEspValor() {
        return RetEspValor;
    }

    public void setRetEspValor(String RetEspValor) {
        try {
            this.RetEspValor = new BigDecimal(RetEspValor);
        } catch (Exception e) {
            this.RetEspValor = new BigDecimal("0");
        }
    }

    public Integer getAgrupISS() {
        return AgrupISS;
    }

    public void setAgrupISS(Integer AgrupISS) {
        this.AgrupISS = AgrupISS;
    }

    public String getBoleto() {
        return Boleto;
    }

    public void setBoleto(String Boleto) {
        this.Boleto = Boleto;
    }

    public LocalDate getDt_Pagto() {
        return Dt_Pagto;
    }

    public void setDt_Pagto(LocalDate Dt_Pagto) {
        this.Dt_Pagto = Dt_Pagto;
    }

    public BigDecimal getJuros() {
        return Juros;
    }

    public void setJuros(String Juros) {
        try {
            this.Juros = new BigDecimal(Juros);
        } catch (Exception e) {
            this.Juros = new BigDecimal("0");
        }
    }

    public BigDecimal getDescontos() {
        return Descontos;
    }

    public void setDescontos(String Descontos) {
        try {
            this.Descontos = new BigDecimal(Descontos);
        } catch (Exception e) {
            this.Descontos = new BigDecimal("0");
        }
    }

    public BigDecimal getValorPago() {
        return ValorPago;
    }

    public void setValorPago(String ValorPago) {
        try {
            this.ValorPago = new BigDecimal(ValorPago);
        } catch (Exception e) {
            this.ValorPago = new BigDecimal("0");
        }
    }

    public Integer getContaFin() {
        return ContaFin;
    }

    public void setContaFin(Integer ContaFin) {
        this.ContaFin = ContaFin;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public BigDecimal getCodReceber() {
        return CodReceber;
    }

    public void setCodReceber(String CodReceber) {
        try {
            this.CodReceber = new BigDecimal(CodReceber);
        } catch (Exception e) {
            this.CodReceber = new BigDecimal("0");
        }
    }

    public Integer getSitCob() {
        return SitCob;
    }

    public void setSitCob(Integer SitCob) {
        this.SitCob = SitCob;
    }

    public BigDecimal getValorFrete() {
        return ValorFrete;
    }

    public void setValorFrete(String ValorFrete) {
        try {
            this.ValorFrete = new BigDecimal(ValorFrete);
        } catch (Exception e) {
            this.CodReceber = new BigDecimal("0");
        }
    }

    public BigDecimal getNumCalculo() {
        return NumCalculo;
    }

    public void setNumCalculo(String NumCalculo) {
        try {
            this.NumCalculo = new BigDecimal(NumCalculo);
        } catch (Exception e) {
            this.NumCalculo = new BigDecimal("0");
        }
    }

    public BigDecimal getNumVenda() {
        return NumVenda;
    }

    public void setNumVenda(String NumVenda) {
        try {
            this.NumVenda = new BigDecimal(NumVenda);
        } catch (Exception e) {
            this.NumVenda = new BigDecimal("0");
        }
    }

    public Integer getNFEmissao() {
        return NFEmissao;
    }

    public void setNFEmissao(Integer NFEmissao) {
        this.NFEmissao = NFEmissao;
    }

    public String getNFERetorno() {
        return NFERetorno;
    }

    public void setNFERetorno(String NFERetorno) {
        this.NFERetorno = NFERetorno;
    }

    public String getNFEChave() {
        return NFEChave;
    }

    public void setNFEChave(String NFEChave) {
        this.NFEChave = NFEChave;
    }

    public String getCarne() {
        return Carne;
    }

    public void setCarne(String Carne) {
        this.Carne = Carne;
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    public String getOper_Incl() {
        return Oper_Incl;
    }

    public void setOper_Incl(String Oper_Incl) {
        this.Oper_Incl = Oper_Incl;
    }

    public LocalDate getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(LocalDate Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOper_Canc() {
        return Oper_Canc;
    }

    public void setOper_Canc(String Oper_Canc) {
        this.Oper_Canc = Oper_Canc;
    }

    public LocalDate getDt_Canc() {
        return Dt_Canc;
    }

    public void setDt_Canc(LocalDate Dt_Canc) {
        this.Dt_Canc = Dt_Canc;
    }

    public String getHr_Canc() {
        return Hr_Canc;
    }

    public void setHr_Canc(String Hr_Canc) {
        this.Hr_Canc = Hr_Canc;
    }

    public String getMotivCanc() {
        return MotivCanc;
    }

    public void setMotivCanc(String MotivCanc) {
        this.MotivCanc = MotivCanc;
    }

    public String getAuditada() {
        return Auditada;
    }

    public void setAuditada(String Auditada) {
        this.Auditada = Auditada;
    }

    public String getAuditor() {
        return Auditor;
    }

    public void setAuditor(String Auditor) {
        this.Auditor = Auditor;
    }

    public LocalDate getDt_audit() {
        return Dt_audit;
    }

    public void setDt_audit(LocalDate Dt_audit) {
        this.Dt_audit = Dt_audit;
    }

    public String getHr_Audit() {
        return Hr_Audit;
    }

    public void setHr_Audit(String Hr_Audit) {
        this.Hr_Audit = Hr_Audit;
    }

    public String getNRedPraca() {
        return NRedPraca;
    }

    public void setNRedPraca(String NRedPraca) {
        this.NRedPraca = NRedPraca;
    }

    public String getDt_Nota() {
        return Dt_Nota;
    }

    public void setDt_Nota(String Dt_Nota) {
        this.Dt_Nota = Dt_Nota;
    }

    public String getHr_Nota() {
        return Hr_Nota;
    }

    public void setHr_Nota(String Hr_Nota) {
        this.Hr_Nota = Hr_Nota;
    }

    public String getDt_Envio() {
        return Dt_Envio;
    }

    public void setDt_Envio(String Dt_Envio) {
        this.Dt_Envio = Dt_Envio;
    }

    public String getHr_Envio() {
        return Hr_Envio;
    }

    public void setHr_Envio(String Hr_Envio) {
        this.Hr_Envio = Hr_Envio;
    }

    public String getDt_Retorno() {
        return Dt_Retorno;
    }

    public void setDt_Retorno(String Dt_Retorno) {
        this.Dt_Retorno = Dt_Retorno;
    }

    public String getHr_Retorno() {
        return Hr_Retorno;
    }

    public void setHr_Retorno(String Hr_Retorno) {
        this.Hr_Retorno = Hr_Retorno;
    }

    /**
     * @return the link
     */
    public String getLink() {
        return link;
    }

    /**
     * @param link the link to set
     */
    public void setLink(String link) {
        this.link = link;
    }
    
    
}

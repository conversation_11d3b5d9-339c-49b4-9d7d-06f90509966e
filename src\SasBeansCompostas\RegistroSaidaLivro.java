package SasBeansCompostas;

import SasBeans.ContasCTB;
import SasBeans.CtbLctos;
import SasBeans.Filiais;

/**
 *
 * <AUTHOR>
 */
public class RegistroSaidaLivro {

    private ContasCTB contasCtb;
    private CtbLctos ctbLctos;
    private Filiais filiais;

    public ContasCTB getContasCtb() {
        return contasCtb;
    }

    public void setContasCtb(ContasCTB contasCtb) {
        this.contasCtb = contasCtb;
    }

    public CtbLctos getCtbLctos() {
        return ctbLctos;
    }

    public void setCtbLctos(CtbLctos ctbLctos) {
        this.ctbLctos = ctbLctos;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

/**
 *
 * <AUTHOR>
 */
public class LogradouroESocial {

    public static String getTipo(String endereco) {
        if (endereco.toUpperCase().contains("Zigue-Zagu".toUpperCase())) {
            return "ZIG";
        } else if (endereco.toUpperCase().contains("Vila".toUpperCase())) {
            return "VL";
        } else if (endereco.toUpperCase().contains("Viela".toUpperCase())) {
            return "VLA";
        } else if (endereco.toUpperCase().contains("Viaduto".toUpperCase())) {
            return "VD";
        } else if (endereco.toUpperCase().contains("Via de Pedestre".toUpperCase())) {
            return "VPE";
        } else if (endereco.toUpperCase().contains("Via de Acesso".toUpperCase())) {
            return "VAC";
        } else if (endereco.toUpperCase().contains("Via Local".toUpperCase())) {
            return "V L";
        } else if (endereco.toUpperCase().contains("Via Litorânea".toUpperCase())) {
            return "VLT";
        } else if (endereco.toUpperCase().contains("Via Expressa".toUpperCase())) {
            return "V-E";
        } else if (endereco.toUpperCase().contains("Via Elevado".toUpperCase())) {
            return "VEV";
        } else if (endereco.toUpperCase().contains("Via Costeira".toUpperCase())) {
            return "VCO";
        } else if (endereco.toUpperCase().contains("Via Coletora".toUpperCase())) {
            return "V C";
        } else if (endereco.toUpperCase().contains("Via".toUpperCase())) {
            return "V";
        } else if (endereco.toUpperCase().contains("Vereda".toUpperCase())) {
            return "VER";
        } else if (endereco.toUpperCase().contains("Variante".toUpperCase())) {
            return "VRT";
        } else if (endereco.toUpperCase().contains("Vale".toUpperCase())) {
            return "VLE";
        } else if (endereco.toUpperCase().contains("Vala".toUpperCase())) {
            return "VAL";
        } else if (endereco.toUpperCase().contains("Unidade".toUpperCase())) {
            return "UNI";
        } else if (endereco.toUpperCase().contains("Túnel".toUpperCase())) {
            return "TUN";
        } else if (endereco.toUpperCase().contains("Trincheira".toUpperCase())) {
            return "TCH";
        } else if (endereco.toUpperCase().contains("Trevo".toUpperCase())) {
            return "TRV";
        } else if (endereco.toUpperCase().contains("Trecho".toUpperCase())) {
            return "TR";
        } else if (endereco.toUpperCase().contains("Travessa Velha".toUpperCase())) {
            return "TVV";
        } else if (endereco.toUpperCase().contains("Travessa Particular".toUpperCase())) {
            return "TVP";
        } else if (endereco.toUpperCase().contains("Travessa".toUpperCase())) {
            return "TV";
        } else if (endereco.toUpperCase().contains("Terminal".toUpperCase())) {
            return "TER";
        } else if (endereco.toUpperCase().contains("Subida".toUpperCase())) {
            return "SUB";
        } else if (endereco.toUpperCase().contains("Sitio".toUpperCase())) {
            return "SIT";
        } else if (endereco.toUpperCase().contains("Setor".toUpperCase())) {
            return "ST";
        } else if (endereco.toUpperCase().contains("Servidão".toUpperCase())) {
            return "SRV";
        } else if (endereco.toUpperCase().contains("Segunda Avenida".toUpperCase())) {
            return "SEG";
        } else if (endereco.toUpperCase().contains("Ruela".toUpperCase())) {
            return "RLA";
        } else if (endereco.toUpperCase().contains("Rua de Pedestre".toUpperCase())) {
            return "RPE";
        } else if (endereco.toUpperCase().contains("Rua de Ligação".toUpperCase())) {
            return "R L";
        } else if (endereco.toUpperCase().contains("Rua Velha".toUpperCase())) {
            return "R V";
        } else if (endereco.toUpperCase().contains("Rua Particular".toUpperCase())) {
            return "R P";
        } else if (endereco.toUpperCase().contains("Rua Integração".toUpperCase())) {
            return "R I";
        } else if (endereco.toUpperCase().contains("Rua".toUpperCase())) {
            return "R";
        } else if (endereco.toUpperCase().contains("R.".toUpperCase())) {
            return "R";
        } else if (endereco.toUpperCase().contains("Rotula".toUpperCase())) {
            return "ROT";
        } else if (endereco.toUpperCase().contains("Rotatória".toUpperCase())) {
            return "RTT";
        } else if (endereco.toUpperCase().contains("Rodovia".toUpperCase())) {
            return "ROD";
        } else if (endereco.toUpperCase().contains("Rodo Anel".toUpperCase())) {
            return "ROA";
        } else if (endereco.toUpperCase().contains("Retorno".toUpperCase())) {
            return "RTN";
        } else if (endereco.toUpperCase().contains("Retiro".toUpperCase())) {
            return "RER";
        } else if (endereco.toUpperCase().contains("Reta".toUpperCase())) {
            return "RET";
        } else if (endereco.toUpperCase().contains("Residencial".toUpperCase())) {
            return "RES";
        } else if (endereco.toUpperCase().contains("Recreio".toUpperCase())) {
            return "RCR";
        } else if (endereco.toUpperCase().contains("Recanto".toUpperCase())) {
            return "REC";
        } else if (endereco.toUpperCase().contains("Rampa".toUpperCase())) {
            return "RMP";
        } else if (endereco.toUpperCase().contains("Ramal".toUpperCase())) {
            return "RAM";
        } else if (endereco.toUpperCase().contains("Quintas".toUpperCase())) {
            return "QTS";
        } else if (endereco.toUpperCase().contains("Quinta".toUpperCase())) {
            return "QTA";
        } else if (endereco.toUpperCase().contains("Quadra".toUpperCase())) {
            return "Q";
        } else if (endereco.toUpperCase().contains("Q.".toUpperCase())) {
            return "Q";
        } else if (endereco.toUpperCase().contains("QD".toUpperCase())) {
            return "Q";
        } else if (endereco.toUpperCase().contains("Pátio".toUpperCase())) {
            return "PAT";
        } else if (endereco.toUpperCase().contains("Prolongamento".toUpperCase())) {
            return "PRL";
        } else if (endereco.toUpperCase().contains("Projeção".toUpperCase())) {
            return "MRG";
        } else if (endereco.toUpperCase().contains("Praça de Esportes".toUpperCase())) {
            return "PCE";
        } else if (endereco.toUpperCase().contains("Praça".toUpperCase())) {
            return "PC";
        } else if (endereco.toUpperCase().contains("Praia".toUpperCase())) {
            return "PR";
        } else if (endereco.toUpperCase().contains("Porto".toUpperCase())) {
            return "PTO";
        } else if (endereco.toUpperCase().contains("Ponte".toUpperCase())) {
            return "PTE";
        } else if (endereco.toUpperCase().contains("Ponta".toUpperCase())) {
            return "PNT";
        } else if (endereco.toUpperCase().contains("Passeio".toUpperCase())) {
            return "PAS";
        } else if (endereco.toUpperCase().contains("Passarela".toUpperCase())) {
            return "PSA";
        } else if (endereco.toUpperCase().contains("Passagem de Pedestre".toUpperCase())) {
            return "PSP";
        } else if (endereco.toUpperCase().contains("Passagem Subterrânea".toUpperCase())) {
            return "PSS";
        } else if (endereco.toUpperCase().contains("Passagem".toUpperCase())) {
            return "PSG";
        } else if (endereco.toUpperCase().contains("Parque Residencial".toUpperCase())) {
            return "PRR";
        } else if (endereco.toUpperCase().contains("Parque Municipal".toUpperCase())) {
            return "PRM";
        } else if (endereco.toUpperCase().contains("Parque".toUpperCase())) {
            return "PRQ";
        } else if (endereco.toUpperCase().contains("Paralela".toUpperCase())) {
            return "PAR";
        } else if (endereco.toUpperCase().contains("Paradouro".toUpperCase())) {
            return "PDO";
        } else if (endereco.toUpperCase().contains("Parada".toUpperCase())) {
            return "PDA";
        } else if (endereco.toUpperCase().contains("Outeiro".toUpperCase())) {
            return "OUT";
        } else if (endereco.toUpperCase().contains("Núcleo Rural".toUpperCase())) {
            return "NUR";
        } else if (endereco.toUpperCase().contains("Núcleo Habitacional".toUpperCase())) {
            return "HAB";
        } else if (endereco.toUpperCase().contains("Núcleo".toUpperCase())) {
            return "NUC";
        } else if (endereco.toUpperCase().contains("Morro".toUpperCase())) {
            return "MRO";
        } else if (endereco.toUpperCase().contains("Monte".toUpperCase())) {
            return "MTE";
        } else if (endereco.toUpperCase().contains("Modulo".toUpperCase())) {
            return "MOD";
        } else if (endereco.toUpperCase().contains("Mercado".toUpperCase())) {
            return "MER";
        } else if (endereco.toUpperCase().contains("Marina".toUpperCase())) {
            return "MNA";
        } else if (endereco.toUpperCase().contains("Margem".toUpperCase())) {
            return "RPR";
        } else if (endereco.toUpperCase().contains("Loteamento".toUpperCase())) {
            return "LOT";
        } else if (endereco.toUpperCase().contains("Lote".toUpperCase())) {
            return "LT";
        } else if (endereco.toUpperCase().contains("Largo".toUpperCase())) {
            return "LRG";
        } else if (endereco.toUpperCase().contains("Lagoa".toUpperCase())) {
            return "LGA";
        } else if (endereco.toUpperCase().contains("Lago".toUpperCase())) {
            return "LGO";
        } else if (endereco.toUpperCase().contains("Ladeira".toUpperCase())) {
            return "LD";
        } else if (endereco.toUpperCase().contains("Jardinete".toUpperCase())) {
            return "JDE";
        } else if (endereco.toUpperCase().contains("Jardim".toUpperCase())) {
            return "JD";
        } else if (endereco.toUpperCase().contains("Indeterminado".toUpperCase())) {
            return "IND";
        } else if (endereco.toUpperCase().contains("Ilhota".toUpperCase())) {
            return "IOA";
        } else if (endereco.toUpperCase().contains("Ilha".toUpperCase())) {
            return "IA";
        } else if (endereco.toUpperCase().contains("Granja".toUpperCase())) {
            return "GJA";
        } else if (endereco.toUpperCase().contains("Galeria".toUpperCase())) {
            return "GAL";
        } else if (endereco.toUpperCase().contains("Forte".toUpperCase())) {
            return "FTE";
        } else if (endereco.toUpperCase().contains("Fonte".toUpperCase())) {
            return "FNT";
        } else if (endereco.toUpperCase().contains("Ferrovia".toUpperCase())) {
            return "FER";
        } else if (endereco.toUpperCase().contains("Feira".toUpperCase())) {
            return "FRA";
        } else if (endereco.toUpperCase().contains("Fazenda".toUpperCase())) {
            return "FAZ";
        } else if (endereco.toUpperCase().contains("Favela".toUpperCase())) {
            return "FAV";
        } else if (endereco.toUpperCase().contains("Evangélica".toUpperCase())) {
            return "EVA";
        } else if (endereco.toUpperCase().contains("Estância".toUpperCase())) {
            return "ETN";
        } else if (endereco.toUpperCase().contains("Estádio".toUpperCase())) {
            return "ETD";
        } else if (endereco.toUpperCase().contains("Estrada de Servidão".toUpperCase())) {
            return "ESS";
        } else if (endereco.toUpperCase().contains("Estrada de Ligação".toUpperCase())) {
            return "ESL";
        } else if (endereco.toUpperCase().contains("Estrada Vicinal".toUpperCase())) {
            return "ESI";
        } else if (endereco.toUpperCase().contains("Estrada Velha".toUpperCase())) {
            return "ESV";
        } else if (endereco.toUpperCase().contains("Estrada Particular".toUpperCase())) {
            return "ETP";
        } else if (endereco.toUpperCase().contains("Estrada Municipal".toUpperCase())) {
            return "ESM";
        } else if (endereco.toUpperCase().contains("Estrada Intermunicipal".toUpperCase())) {
            return "EIM";
        } else if (endereco.toUpperCase().contains("Estrada Estadual".toUpperCase())) {
            return "ESE";
        } else if (endereco.toUpperCase().contains("Estrada Antiga".toUpperCase())) {
            return "ETA";
        } else if (endereco.toUpperCase().contains("Estrada".toUpperCase())) {
            return "EST";
        } else if (endereco.toUpperCase().contains("Estação".toUpperCase())) {
            return "ETC";
        } else if (endereco.toUpperCase().contains("Estacionamento".toUpperCase())) {
            return "ETT";
        } else if (endereco.toUpperCase().contains("Esplanada".toUpperCase())) {
            return "ESP";
        } else if (endereco.toUpperCase().contains("Escadaria".toUpperCase())) {
            return "ESD";
        } else if (endereco.toUpperCase().contains("Escada".toUpperCase())) {
            return "ESC";
        } else if (endereco.toUpperCase().contains("Entre Quadra".toUpperCase())) {
            return "EQ";
        } else if (endereco.toUpperCase().contains("Entre Bloco".toUpperCase())) {
            return "EB";
        } else if (endereco.toUpperCase().contains("Entrada Particular".toUpperCase())) {
            return "ENT";
        } else if (endereco.toUpperCase().contains("Enseada".toUpperCase())) {
            return "ENS";
        } else if (endereco.toUpperCase().contains("Elevada".toUpperCase())) {
            return "EVD";
        } else if (endereco.toUpperCase().contains("Eixo Industrial".toUpperCase())) {
            return "EX";
        } else if (endereco.toUpperCase().contains("Distrito".toUpperCase())) {
            return "DT";
        } else if (endereco.toUpperCase().contains("Desvio".toUpperCase())) {
            return "DSV";
        } else if (endereco.toUpperCase().contains("Descida".toUpperCase())) {
            return "DSC";
        } else if (endereco.toUpperCase().contains("Córrego".toUpperCase())) {
            return "CRG";
        } else if (endereco.toUpperCase().contains("Corredor".toUpperCase())) {
            return "COR";
        } else if (endereco.toUpperCase().contains("Contorno".toUpperCase())) {
            return "CTN";
        } else if (endereco.toUpperCase().contains("Conjunto Mutirão".toUpperCase())) {
            return "CJM";
        } else if (endereco.toUpperCase().contains("Conjunto".toUpperCase())) {
            return "CJ";
        } else if (endereco.toUpperCase().contains("Condomínio".toUpperCase())) {
            return "CON";
        } else if (endereco.toUpperCase().contains("Comunidade".toUpperCase())) {
            return "COM";
        } else if (endereco.toUpperCase().contains("Complexo Viário".toUpperCase())) {
            return "CMP";
        } else if (endereco.toUpperCase().contains("Colônia".toUpperCase())) {
            return "COL";
        } else if (endereco.toUpperCase().contains("Circular".toUpperCase())) {
            return "CIR";
        } else if (endereco.toUpperCase().contains("Ciclovia".toUpperCase())) {
            return "CIC";
        } else if (endereco.toUpperCase().contains("Chácara".toUpperCase())) {
            return "CH";
        } else if (endereco.toUpperCase().contains("CH.".toUpperCase())) {
            return "CH";
        } else if (endereco.toUpperCase().contains("Chapadão".toUpperCase())) {
            return "CHA";
        } else if (endereco.toUpperCase().contains("Canal".toUpperCase())) {
            return "CAN";
        } else if (endereco.toUpperCase().contains("Campo".toUpperCase())) {
            return "CPO";
        } else if (endereco.toUpperCase().contains("Caminho".toUpperCase())) {
            return "CAM";
        } else if (endereco.toUpperCase().contains("Calçada".toUpperCase())) {
            return "CAL";
        } else if (endereco.toUpperCase().contains("Cais".toUpperCase())) {
            return "C";
        } else if (endereco.toUpperCase().contains("Buraco".toUpperCase())) {
            return "BCO";
        } else if (endereco.toUpperCase().contains("Bulevar".toUpperCase())) {
            return "BLV";
        } else if (endereco.toUpperCase().contains("Boulevard".toUpperCase())) {
            return "BVD";
        } else if (endereco.toUpperCase().contains("Bosque".toUpperCase())) {
            return "BSQ";
        } else if (endereco.toUpperCase().contains("Blocos".toUpperCase())) {
            return "BLS";
        } else if (endereco.toUpperCase().contains("Bloco".toUpperCase())) {
            return "BL";
        } else if (endereco.toUpperCase().contains("Belvedere".toUpperCase())) {
            return "BEL";
        } else if (endereco.toUpperCase().contains("Beco".toUpperCase())) {
            return "BC";
        } else if (endereco.toUpperCase().contains("Balão".toUpperCase())) {
            return "BLO";
        } else if (endereco.toUpperCase().contains("Balneário".toUpperCase())) {
            return "BAL";
        } else if (endereco.toUpperCase().contains("Baixa".toUpperCase())) {
            return "BX";
        } else if (endereco.toUpperCase().contains("Avenida".toUpperCase())) {
            return "AV";
        } else if (endereco.toUpperCase().contains("Avenida Velha".toUpperCase())) {
            return "AVV";
        } else if (endereco.toUpperCase().contains("Avenida Marginal".toUpperCase())) {
            return "AVM";
        } else if (endereco.toUpperCase().contains("Avenida Marginal Esquerda".toUpperCase())) {
            return "AME";
        } else if (endereco.toUpperCase().contains("Avenida Marginal Direita".toUpperCase())) {
            return "AMD";
        } else if (endereco.toUpperCase().contains("Avenida Contorno".toUpperCase())) {
            return "AVC";
        } else if (endereco.toUpperCase().contains("Atalho".toUpperCase())) {
            return "ATL";
        } else if (endereco.toUpperCase().contains("Artéria".toUpperCase())) {
            return "ART";
        } else if (endereco.toUpperCase().contains("Antiga Estrada".toUpperCase())) {
            return "ANT";
        } else if (endereco.toUpperCase().contains("Anel Viário".toUpperCase())) {
            return "AN";
        } else if (endereco.toUpperCase().contains("Alto".toUpperCase())) {
            return "AT";
        } else if (endereco.toUpperCase().contains("Alameda".toUpperCase())) {
            return "AL";
        } else if (endereco.toUpperCase().contains("Aeroporto".toUpperCase())) {
            return "AER";
        } else if (endereco.toUpperCase().contains("Adro".toUpperCase())) {
            return "AD";
        } else if (endereco.toUpperCase().contains("Acesso".toUpperCase())) {
            return "AC";
        } else if (endereco.toUpperCase().contains("Acesso Local".toUpperCase())) {
            return "ACL";
        } else if (endereco.toUpperCase().contains("Acampamento".toUpperCase())) {
            return "ACA";
        } else if (endereco.toUpperCase().contains("Área Verde".toUpperCase())) {
            return "A V";
        } else if (endereco.toUpperCase().contains("Área Especial".toUpperCase())) {
            return "AE";
        } else if (endereco.toUpperCase().contains("Área".toUpperCase())) {
            return "A";
        }
        return "R";
    }
}

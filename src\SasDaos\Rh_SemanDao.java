/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Rh_Seman;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rh_SemanDao {

    String sql;

    public List<Rh_Seman> getRhSeman(String sMatricula, String sCodFil, Persistencia persistencia) throws Exception {

        List<Rh_Seman> lRh_Seman = new ArrayList();

        sql = " select Matr, CodFil, Semana, Hr50, Hr100, Hr3, Hr50i, Hr100i, Hr3i, AdNot,  "
                + " HorasTrab, HsAbnFalta, HsAbnFeriado, HsProjecao, Faltas, FaltasJust, Reciclagem, "
                + " Sindicato, Transito, HEInc, HorasExtr, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, H<PERSON><PERSON>do, "
                + " <PERSON><PERSON>, <PERSON><PERSON>_<PERSON><PERSON>, <PERSON>r_<PERSON>er "
                + " from rh_seman"
                + " where Matr =  ? "
                + " and CodFil = ? ";

        try {
            Consulta cRh_Seman = new Consulta(sql, persistencia);
            cRh_Seman.setString(sMatricula);
            cRh_Seman.setString(sCodFil);
            cRh_Seman.select();

            while (cRh_Seman.Proximo()) {
                Rh_Seman oRh_Seman = new Rh_Seman();

                oRh_Seman.setMatr(cRh_Seman.getString("Matr"));
                oRh_Seman.setCodFil(cRh_Seman.getString("CodFil"));
                oRh_Seman.setSemana(cRh_Seman.getString("Semana"));
                oRh_Seman.setHr50(cRh_Seman.getString("Hr50"));
                oRh_Seman.setHr100(cRh_Seman.getString("Hr100"));
                oRh_Seman.setHr3(cRh_Seman.getString("Hr3"));
                oRh_Seman.setHr50i(cRh_Seman.getString("Hr50i"));
                oRh_Seman.setHr100i(cRh_Seman.getString("Hr100i"));
                oRh_Seman.setHr3i(cRh_Seman.getString("Hr3i"));
                oRh_Seman.setAdNot(cRh_Seman.getString("AdNot"));
                oRh_Seman.setHorasTrab(cRh_Seman.getString("HorasTrab"));
                oRh_Seman.setHsAbnFalta(cRh_Seman.getString("HsAbnFalta"));
                oRh_Seman.setHsAbnFeriado(cRh_Seman.getString("HsAbnFeriado"));
                oRh_Seman.setHsProjecao(cRh_Seman.getString("HsProjecao"));
                oRh_Seman.setFaltas(cRh_Seman.getString("Faltas"));
                oRh_Seman.setFaltasJust(cRh_Seman.getString("FaltasJust"));
                oRh_Seman.setReciclagem(cRh_Seman.getString("Reciclagem"));
                oRh_Seman.setSindicato(cRh_Seman.getString("Sindicato"));
                oRh_Seman.setTransito(cRh_Seman.getString("Transito"));
                oRh_Seman.setHEInc(cRh_Seman.getString("HEInc"));
                oRh_Seman.setHorasExtr(cRh_Seman.getString("HorasExtr"));
                oRh_Seman.setHsAComp(cRh_Seman.getString("HsAComp"));
                oRh_Seman.setDiasFolga(cRh_Seman.getString("DiasFolga"));
                oRh_Seman.setDiasFolga(cRh_Seman.getString("DiasFerTrab"));
                oRh_Seman.setDiasFolga(cRh_Seman.getString("HEFeriado"));
                oRh_Seman.setDiasFolga(cRh_Seman.getString("Operador"));
                oRh_Seman.setDt_Alter(cRh_Seman.getString("Dt_Alter"));
                oRh_Seman.setHr_Alter(cRh_Seman.getString("Hr_Alter"));

                lRh_Seman.add(oRh_Seman);
            }
            cRh_Seman.Close();
            return lRh_Seman;
        } catch (Exception e) {
            throw new Exception("Falha ao retorna dados Semana - " + e.getMessage());
        }

    }
}

/*
 */
package SasBeansCompostas;

import SasBeans.Pessoa;
import SasBeans.PessoaLogin;
import SasBeans.SASGrupos;
import SasBeans.Saspw;

/**
 *
 * <AUTHOR>
 */
public class UsuarioSatMobWeb {

    private Saspw saspw;
    private Pessoa pessoa;
    private PessoaLogin pessoalogin;
    private SASGrupos grupo;

    public UsuarioSatMobWeb() {
        this.saspw = new Saspw();
        this.pessoa = new Pessoa();
        this.pessoalogin = new PessoaLogin();
        this.grupo = new SASGrupos();
    }

    public Saspw getSaspw() {
        return saspw;
    }

    public void setSaspw(Saspw saspw) {
        this.saspw = saspw;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public PessoaLogin getPessoalogin() {
        return pessoalogin;
    }

    public void setPessoalogin(PessoaLogin pessoalogin) {
        this.pessoalogin = pessoalogin;
    }

    public SASGrupos getGrupo() {
        return grupo;
    }

    public void setGrupo(SASGrupos grupo) {
        this.grupo = grupo;
    }
}

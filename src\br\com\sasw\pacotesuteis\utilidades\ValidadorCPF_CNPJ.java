package br.com.sasw.pacotesuteis.utilidades;

/**
 *
 * <AUTHOR>
 */
public class ValidadorCPF_CNPJ {

    /**
     * Valida o CPF como o SATELLITE
     *
     * @param CPF - string contendo apenas os algarismos do cpf, sem . e -
     * @return - true para cpf válido, false para cpf inválido
     */
    public static Boolean ValidarCPF(String CPF) {
        if (null == CPF) {
            return false;
        }
        if (CPF.length() != 11) {
            return false;
        }
        int k, soma, digito;
        String CPF1 = CPF.substring(0, 9);
        for (int j = 1; j <= 2; j++) {
            k = 2;
            soma = 0;
            for (int i = CPF1.length(); i >= 1; i--) {
                soma = soma + (Integer.parseInt(String.valueOf(CPF1.charAt(i - 1)))) * k;
                k++;
            }
            digito = 11 - soma % 11;
            if (digito >= 10) {
                digito = 0;
            }
            CPF1 = "" + CPF1 + digito;
        }
        if (CPF1.equals(CPF)) {
            return true;
        }
        return false;
    }

    /**
     * Valida o CNPJ como o SATELLITE
     *
     * @param CNPJ - string contendo apenas os algarismos do cnpj, sem . e /
     * @return - true para cnpj válido, false para cnpj inválido
     */
    public static Boolean ValidarCNPJ(String CNPJ) {
        try {
            int total, digito1, digito2;
            total = ((Integer.parseInt(String.valueOf(CNPJ.charAt(0))) + Integer.parseInt(String.valueOf(CNPJ.charAt(8)))) * 5)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(1))) + Integer.parseInt(String.valueOf(CNPJ.charAt(9)))) * 4)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(2))) + Integer.parseInt(String.valueOf(CNPJ.charAt(10)))) * 3)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(3))) + Integer.parseInt(String.valueOf(CNPJ.charAt(11)))) * 2)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(4))) * 9) + (Integer.parseInt(String.valueOf(CNPJ.charAt(5)))) * 8)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(6))) * 7) + (Integer.parseInt(String.valueOf(CNPJ.charAt(7)))) * 6);
            digito1 = 11 - (total % 11);
            if (digito1 > 9) {
                digito1 = 0;
            }
            total = ((Integer.parseInt(String.valueOf(CNPJ.charAt(0))) + Integer.parseInt(String.valueOf(CNPJ.charAt(8)))) * 6)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(1))) + Integer.parseInt(String.valueOf(CNPJ.charAt(9)))) * 5)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(2))) + Integer.parseInt(String.valueOf(CNPJ.charAt(10)))) * 4)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(3))) + Integer.parseInt(String.valueOf(CNPJ.charAt(11)))) * 3)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(4))) + (digito1)) * 2)
                    + ((Integer.parseInt(String.valueOf(CNPJ.charAt(5))) * 9) + (Integer.parseInt(String.valueOf(CNPJ.charAt(6)))) * 8)
                    + (Integer.parseInt(String.valueOf(CNPJ.charAt(7))) * 7);
            digito2 = 11 - (total % 11);
            if (digito2 > 9) {
                digito2 = 0;
            }
            return CNPJ.substring(12, 14).equals("" + digito1 + digito2);
        } catch (Exception e) {
            return false;
        }
    }
}

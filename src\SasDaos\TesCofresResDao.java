package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.MobileHW;
import SasBeans.TesCofresRes;
import SasBeansCompostas.TesCofresResClientes;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class TesCofresResDao {

    /**
     * Retorna o movimento do cofre
     *
     * @param CodCofre - código do cofre
     * @param DtInicial - data inicial
     * @param DtFinal - data final
     * @param persistencia - conexão com o banco
     * @return - retorna lista ou nulo para
     * @throws Exception
     */
    public static List<TesCofresRes> MovimentoCofre(String CodCofre, String DtInicial, String DtFinal, Persistencia persistencia) throws Exception {
        List<TesCofresRes> listCofre;
        TesCofresRes tescofreres;
        String vSQL = "Select TesCofresRes.*"
                + " from TesCofresRes "
                + " where TesCofresRes.CodCofre = ?"
                + "   and Data >= ?"
                + "   and Data <= ?"
                + "   order by TesCofresRes.Data Desc ";
        try {
            listCofre = new ArrayList();
            Consulta consult = new Consulta(vSQL, persistencia);
            consult.setString(CodCofre);
            consult.setString(DtInicial);
            consult.setString(DtFinal);
            consult.select();
            while (consult.Proximo()) {
                tescofreres = new TesCofresRes();
                tescofreres.setCodCofre(consult.getString("CodCofre"));
                tescofreres.setData(consult.getDate("Data").toLocalDate());
                tescofreres.setDataStr(consult.getString("DataStr"));
                tescofreres.setDiaSem(consult.getInt("DiaSem"));
                tescofreres.setFeriado(consult.getString("Feriado"));
                tescofreres.setHrRecApos(consult.getString("HrRecApos"));
                tescofreres.setHrRecDepD0(consult.getString("HrRecDepD0"));
                tescofreres.setSaldoFisCred(consult.getString("SaldoFisCred"));
                tescofreres.setSaldoFisCst(consult.getString("SaldoFisCst"));
                tescofreres.setSaldoFisTotal(consult.getString("SaldoFisTotal"));
                tescofreres.setVlrCredCorteD0(consult.getString("VlrCredCorteD0"));
                tescofreres.setVlrCredRecD0(consult.getString("VlrCredRecD0"));
                tescofreres.setVlrD0Apos(consult.getString("VlrD0Apos"));
                tescofreres.setVlrD1Apos(consult.getString("VlrD1Apos"));
                tescofreres.setVlrDep(consult.getString("VlrDep"));
                tescofreres.setVlrDepProxDU(consult.getString("VlrDepProxDU"));
                tescofreres.setVlrRecApos(consult.getString("VlrRecApos"));
                tescofreres.setVlrRecDepD0(consult.getString("VlrRecDepD0"));
                tescofreres.setVlrTotalCred(consult.getString("VlrTotalCred"));
                tescofreres.setVlrTotalRec(consult.getString("VlrTotalRec"));
                listCofre.add(tescofreres);
            }
            consult.Close();
        } catch (Exception e) {
            listCofre = null;
            throw new Exception("Falha ao carregar dados da Tesouraria - " + e.getMessage());
        }
        return listCofre;
    }

    public static List<TesCofresResClientes> MovimentoCofreCliente(String CodCofre, String DtInicial, String DtFinal, Persistencia persistencia) throws Exception {
        String vSql;
        List<TesCofresResClientes> listCofre;
        TesCofresResClientes tescofres;
        TesCofresRes cofre;
        Clientes clientes;
        vSql = "Select TesCofresRes.Data,"
                + " DiaSem, Clientes.CodCofre, Clientes.NRed, SubString(Clientes.Ende,1,40) Endereco, Clientes.Bairro,"
                + " Clientes.Cidade, Clientes.Estado UF, TesCofresRes.VlrTotalCred 'CreditoDia',"
                + " VlrDepProxDU 'CreditoProxDU'"
                + " from TesCofresRes"
                + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre"
                + " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo"
                + " and OS_Vig.CodFil = Clientes.CodFil"
                + " and OS_Vig.Situacao =  'A'"
                + " and OS_Vig.DtFim >= ?"
                + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS"
                + " and OS_VTes.CodFil = OS_Vig.CodFil"
                + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr"
                + " where TesCofresRes.data <= ?"
                + " and TesCofresRes.data >= ?"
                + " and OS_Vig.CliDst     = ?" //9997077
                + " and clientes.codcofre in (42,43,45,46,56,68,73)" //acrescentar cofres
                + " order by TesCofresRes.Data desc,Clientes.NRed";
        try {
            listCofre = new ArrayList();
            Consulta consult = new Consulta(vSql, persistencia);
            consult.setString(DtInicial);
            consult.setString(DtFinal);
            consult.setString(DtInicial);
            consult.setString(CodCofre);
            consult.select();
            while (consult.Proximo()) {
                tescofres = new TesCofresResClientes();
                cofre = new TesCofresRes();
                clientes = new Clientes();
                cofre.setData(consult.getDate("Data").toLocalDate());
                cofre.setDiaSem(consult.getInt("DiaSem"));
                cofre.setVlrTotalCred(consult.getString("CreditoDia"));
                cofre.setVlrDepProxDU(consult.getString("CreditoProxDU"));
                clientes.setCodCofre(consult.getString("CodCofre"));
                clientes.setNRed(consult.getString("NRed"));
                clientes.setEnde(consult.getString("Endereco"));
                clientes.setBairro(consult.getString("Bairro"));
                clientes.setCidade(consult.getString("Cidade"));
                clientes.setEstado(consult.getString("UF"));
                tescofres.setClientes(clientes);
                tescofres.setTescofresres(cofre);
                listCofre.add(tescofres);
            }
            consult.Close();
        } catch (Exception e) {
            listCofre = null;
            throw new Exception("Falha ao carregar dados da Tesouraria - " + e.getMessage());
        }
        return listCofre;
    }

    public static List<TesCofresResClientes> MovimentoCofreCliente(BigDecimal CodCofre, String DtInicial, String DtFinal, Persistencia persistencia) throws Exception {
        String vSql;
        List<TesCofresResClientes> listCofre;
        TesCofresResClientes tescofres;
        TesCofresRes cofre;
        Clientes clientes;
        vSql = "Select TesCofresRes.Data,"
                + " DiaSem, Clientes.CodCofre, Clientes.NRed, SubString(Clientes.Ende,1,40) Endereco, Clientes.Bairro,"
                + " Clientes.Cidade, Clientes.Estado UF, TesCofresRes.VlrTotalCred 'CreditoDia',"
                + " VlrDepProxDU 'CreditoProxDU'"
                + " from TesCofresRes"
                + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre"
                + " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo"
                + " and OS_Vig.CodFil = Clientes.CodFil"
                + " and OS_Vig.Situacao =  'A'"
                + " and OS_Vig.DtFim >= ?"
                + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS"
                + " and OS_VTes.CodFil = OS_Vig.CodFil"
                + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr"
                + " where TesCofresRes.data <= ?"
                + " and TesCofresRes.data >= ?"
                //+" and OS_Vig.CliDst     = ?" //9997077
                + " and clientes.codcofre =?"//in (42,43,45,46)"
                + " order by TesCofresRes.Data desc,Clientes.NRed";
        try {
            listCofre = new ArrayList();
            Consulta consult = new Consulta(vSql, persistencia);
            consult.setString(DtInicial);
            consult.setString(DtFinal);
            consult.setString(DtInicial);
            consult.setBigDecimal(CodCofre);
            consult.select();
            while (consult.Proximo()) {
                tescofres = new TesCofresResClientes();
                cofre = new TesCofresRes();
                clientes = new Clientes();
                cofre.setData(consult.getDate("Data").toLocalDate());
                cofre.setDiaSem(consult.getInt("DiaSem"));
                cofre.setVlrTotalCred(consult.getString("CreditoDia"));
                cofre.setVlrDepProxDU(consult.getString("CreditoProxDU"));
                clientes.setCodCofre(consult.getString("CodCofre"));
                clientes.setNRed(consult.getString("NRed"));
                clientes.setEnde(consult.getString("Endereco"));
                clientes.setBairro(consult.getString("Bairro"));
                clientes.setCidade(consult.getString("Cidade"));
                clientes.setEstado(consult.getString("UF"));
                tescofres.setClientes(clientes);
                tescofres.setTescofresres(cofre);
                listCofre.add(tescofres);
            }
            consult.Close();
        } catch (Exception e) {
            listCofre = null;
            throw new Exception("Falha ao carregar dados da Tesouraria - " + e.getMessage());
        }
        return listCofre;
    }

    /**
     * Seleciona os clientes com cofres
     *
     * @param data Data do dia para pesquisa(Default: dia de hoje) yyyyMMdd
     * @param persistencia Conexão com o banco
     * @return Retorna a lista de clientes com cofres para movimento no dia
     * @throws Exception
     */
    public List<TesCofresResClientes> CreditosAnaliticos(String data, Persistencia persistencia) throws Exception {
        List<TesCofresResClientes> listCofre;
        TesCofresResClientes tesCofresResClientes;
        TesCofresRes tesCofresRes;
        Clientes clientes;

        String sql = "Select Clientes.Codigo CodigoCli, Clientes.CodFil, Clientes.CodCofre, Clientes.NRed, SubString(Clientes.Ende,1,40) Endereco, \n"
                + "Clientes.Bairro,  Clientes.Cidade, Clientes.Estado UF, TesCofresRes.VlrTotalCred CreditoDia, \n"
                + "TesCofresRes.VlrRecAntesCorte + TesCofresRes.VlrRecAposCorte ValorRec, SaldoFisCst SaldoCustodia, SaldoFisTotal SaldoCofre from TesCofresRes \n"
                + "left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre \n"
                + "left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo \n"
                + "and OS_Vig.CodFil = Clientes.CodFil \n"
                + "and OS_Vig.Situacao = 'A'\n"
                + "and OS_Vig.DtFim >= Convert(Date,Getdate())\n"
                + "and SubString(OS_Vig.CliDst,4,1) = '7'\n"
                + "left join OS_VTes on  OS_VTes.OS = OS_Vig.OS \n"
                + "and OS_VTes.CodFil = OS_Vig.CodFil \n"
                + "left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr \n"
                + "inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                + "and PessoaCliAut.CodFil = Clientes.Codfil\n"
                + "where TesCofresRes.data = ? \n"
                + "order by CliDst, TesContas.Descricao";

        try {
            listCofre = new ArrayList<>();
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(data);
            consult.select();

            while (consult.Proximo()) {
                tesCofresResClientes = new TesCofresResClientes();
                tesCofresRes = new TesCofresRes();
                clientes = new Clientes();

                clientes.setCodigo(consult.getString("CodigoCli"));
                clientes.setCodFil(consult.getString("CodFil"));
                clientes.setCodCofre(consult.getString("CodCofre"));
                clientes.setNRed(consult.getString("NRed"));
                clientes.setEnde(consult.getString("Endereco"));
                clientes.setBairro(consult.getString("Bairro"));
                clientes.setCidade(consult.getString("Cidade"));
                clientes.setEstado(consult.getString("UF"));
                tesCofresRes.setVlrTotalCred(consult.getString("CreditoDia"));
                tesCofresRes.setVlrTotalRec(consult.getString("ValorRec"));
                tesCofresRes.setSaldoFisCst(consult.getString("SaldoCustodia"));
                tesCofresRes.setSaldoFisTotal(consult.getString("SaldoCofre"));

                tesCofresResClientes.setClientes(clientes);
                tesCofresResClientes.setTescofresres(tesCofresRes);
                listCofre.add(tesCofresResClientes);
            }
            consult.Close();
        } catch (Exception e) {
            listCofre = null;
            throw new Exception("Falha ao carregar dados da Tesouraria - " + e.getMessage());
        }

        return listCofre;
    }

    public Integer TotalCofresMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select count(*) total "
                    + " from TesCofresRes "
                    + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre "
                    + " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo "
                    + " and OS_Vig.CodFil = Clientes.CodFil "
                    + " and OS_Vig.Situacao = 'A'"
                    + " and OS_Vig.DtFim >= Convert(Date,Getdate())"
                    + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                    + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS "
                    + " and OS_VTes.CodFil = OS_Vig.CodFil "
                    + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr "
                    + " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo"
                    + " and PessoaCliAut.CodFil = Clientes.Codfil"
                    + " where Clientes.codCofre is not null";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar cofres - \r\n" + e.getMessage());
        }
    }

    public Integer TotalCofresMobWeb(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select count(*) total "
                    + " from TesCofresRes "
                    + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre ";
                    /*+ " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo "
                    + " and OS_Vig.CodFil = Clientes.CodFil "
                    + " and OS_Vig.Situacao = 'A'"
                    + " and OS_Vig.DtFim >= Convert(Date,Getdate())"
                    + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                    + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS "
                    + " and OS_VTes.CodFil = OS_Vig.CodFil "
                    + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr ";*/
            if (!exibirTodos) {
                sql += " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo"
                        + " and PessoaCliAut.CodFil = Clientes.Codfil";
            }
            sql += " where Clientes.codCofre is not null";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar cofres - \r\n" + e.getMessage());
        }
    }

    public BigDecimal TotalVlrTotalCred(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select ISNULL(sum( TesCofresRes.VlrTotalCred ),0) valor "
                    + " from TesCofresRes "
                    + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre "
                    + " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo "
                    + " and OS_Vig.CodFil = Clientes.CodFil "
                    + " and OS_Vig.Situacao = 'A'"
                    + " and OS_Vig.DtFim >= Convert(Date,Getdate())"
                    + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                    + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS "
                    + " and OS_VTes.CodFil = OS_Vig.CodFil "
                    + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr "
                    + " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo"
                    + " and PessoaCliAut.CodFil = Clientes.Codfil"
                    + " where Clientes.codCofre is not null";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            BigDecimal retorno = BigDecimal.ZERO;
            while (consult.Proximo()) {
                retorno = consult.getBigDecimal("valor");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresResDAO.TotalVlrTotalCred - " + e.getMessage() + "\r\n"
                    + "");
        }
    }

    public BigDecimal TotalSaldoFisTotal(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select ISNULL(sum( TesCofresRes.SaldoFisTotal ),0) valor "
                    + " from TesCofresRes "
                    + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre "
                    + " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo "
                    + " and OS_Vig.CodFil = Clientes.CodFil "
                    + " and OS_Vig.Situacao = 'A'"
                    + " and OS_Vig.DtFim >= Convert(Date,Getdate())"
                    + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                    + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS "
                    + " and OS_VTes.CodFil = OS_Vig.CodFil "
                    + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr "
                    + " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo"
                    + " and PessoaCliAut.CodFil = Clientes.Codfil"
                    + " where Clientes.codCofre is not null";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            BigDecimal retorno = BigDecimal.ZERO;
            while (consult.Proximo()) {
                retorno = consult.getBigDecimal("valor");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar saldo do cofre - \r\n" + e.getMessage());
        }
    }

    public BigDecimal TotalSaldoFisCst(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select ISNULL(sum( TesCofresRes.SaldoFisCst ),0) valor "
                    + " from TesCofresRes "
                    + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre "
                    + " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo "
                    + " and OS_Vig.CodFil = Clientes.CodFil "
                    + " and OS_Vig.Situacao = 'A'"
                    + " and OS_Vig.DtFim >= Convert(Date,Getdate())"
                    + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                    + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS "
                    + " and OS_VTes.CodFil = OS_Vig.CodFil "
                    + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr "
                    + " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo"
                    + " and PessoaCliAut.CodFil = Clientes.Codfil"
                    + " where Clientes.codCofre is not null";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            BigDecimal retorno = BigDecimal.ZERO;
            while (consult.Proximo()) {
                retorno = consult.getBigDecimal("valor");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar custo do dia - \r\n" + e.getMessage());
        }
    }

    public BigDecimal TotalVlrRecolhido(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select ISNULL( sum( TesCofresRes.VlrRecAntesCorte + TesCofresRes.VlrRecAposCorte ),0) valor "
                    + " from TesCofresRes "
                    + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre "
                    + " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo "
                    + " and OS_Vig.CodFil = Clientes.CodFil "
                    + " and OS_Vig.Situacao = 'A'"
                    + " and OS_Vig.DtFim >= Convert(Date,Getdate())"
                    + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                    + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS "
                    + " and OS_VTes.CodFil = OS_Vig.CodFil "
                    + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr "
                    + " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo"
                    + " and PessoaCliAut.CodFil = Clientes.Codfil"
                    + " where Clientes.codCofre is not null";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            BigDecimal retorno = BigDecimal.ZERO;
            while (consult.Proximo()) {
                retorno = consult.getBigDecimal("valor");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar valor recolhido no dia - \r\n" + e.getMessage());
        }
    }

    public List<TesCofresResClientes> ListaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        List<TesCofresResClientes> retorno = new ArrayList<>();
        try {
            String sql = "SELECT  *  FROM ( SELECT    ROW_NUMBER() OVER ( ORDER BY OS_Vig.CliDst, TesContas.Descricao ) AS RowNum, "
                    + " Clientes.Codigo CodigoCli, Clientes.CodFil, Clientes.CodCofre, Clientes.NRed, SubString(Clientes.Ende,1,40) Endereco, "
                    + " Clientes.Bairro,  Clientes.Cidade, Clientes.Estado UF, Clientes.CEP, TesCofresRes.VlrTotalCred CreditoDia, "
                    + " TesCofresRes.VlrRecAntesCorte + TesCofresRes.VlrRecAposCorte ValorRec, SaldoFisCst SaldoCustodia, SaldoFisTotal SaldoCofre,"
                    + " TesCofresRes.CredD0, TesCofresRes.CredD1 \n"
                    + " from TesCofresRes "
                    + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre "
                    + " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo "
                    + " and OS_Vig.CodFil = Clientes.CodFil "
                    + " and OS_Vig.Situacao = 'A'"
                    + " and OS_Vig.DtFim >= Convert(Date,Getdate())"
                    + " and SubString(OS_Vig.CliDst,4,1) = '7'"
                    + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS "
                    + " and OS_VTes.CodFil = OS_Vig.CodFil "
                    + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr "
                    + " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo"
                    + " and PessoaCliAut.CodFil = Clientes.Codfil"
                    + " where Clientes.codCofre is not null ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + ") AS RowConstrainedResult"
                    + " WHERE RowNum >= ?"
                    + " AND RowNum < ?"
                    + " ORDER BY RowNum";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            TesCofresResClientes tesCofresResClientes;
            TesCofresRes tesCofresRes;
            Clientes clientes;

            while (consulta.Proximo()) {
                tesCofresResClientes = new TesCofresResClientes();
                tesCofresRes = new TesCofresRes();
                clientes = new Clientes();

                clientes.setCodigo(consulta.getString("CodigoCli"));
                if (clientes.getCodigo().contains("6074001")) {
                    clientes.setNRed("TESTE PRESERVE");
                } else {
                    clientes.setNRed(consulta.getString("NRed"));
                }
//                clientes.setNRed(consulta.getString("NRed"));
                clientes.setCodFil(consulta.getString("CodFil"));
                clientes.setCodCofre(consulta.getString("CodCofre"));
                clientes.setEnde(consulta.getString("Endereco"));
                clientes.setBairro(consulta.getString("Bairro"));
                clientes.setCidade(consulta.getString("Cidade"));
                clientes.setEstado(consulta.getString("UF"));
                clientes.setCEP(consulta.getString("CEP"));
                tesCofresRes.setVlrTotalCred(consulta.getString("CreditoDia"));
                tesCofresRes.setVlrTotalRec(consulta.getString("ValorRec"));
                tesCofresRes.setSaldoFisCst(consulta.getString("SaldoCustodia"));
                tesCofresRes.setSaldoFisTotal(consulta.getString("SaldoCofre"));
                tesCofresRes.setCredD0(consulta.getString("CredD0"));
                tesCofresRes.setCredD1(consulta.getString("CredD1"));

                tesCofresResClientes.setClientes(clientes);
                tesCofresResClientes.setTescofresres(tesCofresRes);
                retorno.add(tesCofresResClientes);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list cofres - \r\n" + e.getMessage());
        }
        return retorno;
    }

    public List<TesCofresResClientes> ListaPaginada(int primeiro, int linhas, Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        List<TesCofresResClientes> retorno = new ArrayList<>();
        try {
            //String sql = "SELECT  *  FROM ( SELECT    ROW_NUMBER() OVER ( ORDER BY Clientes.CliDst, TesContas.Descricao ) AS RowNum, \n"
            String sql = "SELECT  *  FROM ( SELECT    ROW_NUMBER() OVER ( ORDER BY Clientes.Nred) AS RowNum, \n"
                    + " Clientes.Codigo CodigoCli, Clientes.CodFil, Clientes.CodCofre, Clientes.NRed, SubString(Clientes.Ende,1,40) Endereco, \n"
                    + " Clientes.Bairro,  Clientes.Cidade, Clientes.Estado UF, Clientes.CEP, ROUND(CAST(ISNULL(TesCofresRes.VlrTotalCred,0) AS decimal (12,2)),2) CreditoDia, \n"
                    + " ISNULL(TesCofresRes.VlrRecAntesCorte + TesCofresRes.VlrRecAposCorte,0) ValorRec, ISNULL(SaldoFisCst,0) SaldoCustodia, ISNULL(SaldoFisTotal,0) SaldoCofre,\n"
                    + " ROUND(CAST(ISNULL(TesCofresRes.CredD0,0) AS decimal (12,2)),2) CredD0, ROUND(CAST(ISNULL(TesCofresRes.CredD1,0) AS decimal (12,2)),2) CredD1, MobileHW.TipoEquip, MobileHW.IMEI, \n"
                    + " case when CataEquip.Status = 1 then 'On-Line' when CataEquip.Status = 2 then 'Off-Line' else '' end status "
                    + " from TesCofresRes \n"
                    + " left join Clientes on Clientes.CodCofre = TesCofresRes.CodCofre \n"
                    /*+ " left join OS_Vig on  OS_Vig.Cliente = Clientes.Codigo \n"
                    + " and OS_Vig.CodFil = Clientes.CodFil \n"
                    + " and OS_Vig.Situacao = 'A'\n"
                    + " and OS_Vig.DtFim >= Convert(Date,Getdate())\n"
                    + " and SubString(OS_Vig.CliDst,4,1) = '7'\n"
                    + " left join OS_VTes on  OS_VTes.OS = OS_Vig.OS \n"
                    + " and OS_VTes.CodFil = OS_Vig.CodFil \n"
                    + " left join TesContas on TesContas.Codigo = OS_VTes.ContaPdr \n"*/
                    + " left join MobileHW on MobileHW.CodEquip = Clientes.CodCofre \n"
                    + "                     and MobileHW.CodFil = Clientes.CodFil \n"
                    + " left join CataEquip  on Substring(CataEquip.Serial, len(Serial)-3,4) = TesCofresRes.CodCofre AND TesCofresRes.Data = CataEquip.Data \n";
            if (!exibirTodos) {
                sql += " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                        + " and PessoaCliAut.CodFil = Clientes.Codfil";
            }
            sql += " where Clientes.codCofre is not null ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }
            sql = sql + ") AS RowConstrainedResult"
                    + " WHERE RowNum >= ?"
                    + " AND RowNum < ?"
                    + " ORDER BY RowNum";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            TesCofresResClientes tesCofresResClientes;
            TesCofresRes tesCofresRes;
            Clientes clientes;
            MobileHW mobileHw;

            while (consulta.Proximo()) {
                tesCofresResClientes = new TesCofresResClientes();
                tesCofresRes = new TesCofresRes();
                clientes = new Clientes();
                mobileHw = new MobileHW();

                clientes.setCodigo(consulta.getString("CodigoCli"));
                if (clientes.getCodigo().contains("6074001")) {
                    clientes.setNRed("TESTE PRESERVE");
                } else {
                    clientes.setNRed(consulta.getString("NRed"));
                }
//                clientes.setNRed(consulta.getString("NRed"));
                clientes.setCodFil(consulta.getString("CodFil"));
                clientes.setCodCofre(consulta.getString("CodCofre"));
                clientes.setEnde(consulta.getString("Endereco"));
                clientes.setBairro(consulta.getString("Bairro"));
                clientes.setCidade(consulta.getString("Cidade"));
                clientes.setEstado(consulta.getString("UF"));
                clientes.setCEP(consulta.getString("CEP"));
                tesCofresRes.setVlrTotalCred(consulta.getString("CreditoDia"));
                tesCofresRes.setVlrTotalRec(consulta.getString("ValorRec"));
                tesCofresRes.setSaldoFisCst(consulta.getString("SaldoCustodia"));
                tesCofresRes.setSaldoFisTotal(consulta.getString("SaldoCofre"));
                tesCofresRes.setCredD0(consulta.getString("CredD0"));
                tesCofresRes.setCredD1(consulta.getString("CredD1"));
                mobileHw.setTipoEquip(consulta.getString("TipoEquip"));
                mobileHw.setIMEI(consulta.getString("IMEI"));
                mobileHw.setStatus(consulta.getString("status"));
                tesCofresResClientes.setClientes(clientes);
                tesCofresResClientes.setMobileHW(mobileHw);
                tesCofresResClientes.setTescofresres(tesCofresRes);
                retorno.add(tesCofresResClientes);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list cofres - \r\n" + e.getMessage());
        }
        return retorno;
    }

    /**
     * Lista todas as movimentações do cofre em um período
     *
     * @param codigo Código do cliente
     * @param codfil Código de filial do cliente
     * @param dataInicio Data inicial do período
     * @param dataFinal Data final do período
     * @param persistencia Conexão com o banco de dados
     * @return Retorna lista com as movimentações do cofre para o período
     * @throws Exception
     */
    public List<TesCofresResClientes> MovimentoCofreClienteSatMob(String codigo, String codfil, String dataInicio, String dataFinal, String vCodPessoa, Persistencia persistencia) throws Exception {
        List<TesCofresResClientes> retorno = new ArrayList<>();
        TesCofresResClientes tesCofresResClientes;
        TesCofresRes tesCofresRes;
        Clientes clientes;
        String vSQLAdic = ""; 
        
        if (!vCodPessoa.equals("0")){
           vSQLAdic = " and PessoaCliAut.Codigo = "+vCodPessoa;                   
           codfil = "1";
        }
        String sql = "Select ";
        
        if (dataInicio.equals("") || dataFinal.equals("")) {
            sql = sql + " top 20 ";
        }
        sql = sql + "TesCofresRes.CodCofre, TesCofresRes.Data, TesCofresRes.DiaSem, TesCofresRes.Feriado, TesCofresRes.DataStr, TesCofresRes.HrRecAntesCorte,   TesCofresRes.HrRecAposCorte, "
                  + "isnull(TesCofresRes.vlrRecAntesCorte,0) vlrRecAntesCorte, isnull(TesCofresRes.vLrRecAPosCorte,0) vLrRecAPosCorte, "
                  + "isnull(TesCofresRes.CredD0Rec,0) CredD0Rec, isnull(TesCofresRes.CredD0RecAposCorte,0)CredD0RecAposCorte, \n" 
                  + "isnull(TesCofresRes.CredD1Rec,0) CredD1Rec, isnull(TesCofresRes.CredD1,0) CredD1,  isnull(TesCofresRes.CredDiaAnt,0)CredDiaAnt, \n"
                  + "isnull(TesCofresRes.CredDiaD0Rec,0) CredDiaD0Rec, isnull(TesCofresRes.SaldoFisCred,0) SaldoFisCred, isnull(TesCofresRes.SaldoFisTotal,0) SaldoFisTotal,  \n" 
                  + "isnull(TesCofresRes.SaldoFisCst,0) SaldoFisCst,  isnull(TesCofresRes.VlrTotalCred,0) VlrTotalCred, isnull(TesCofresRes.VlrTotalRec,0) VlrTotalRec, "
                  + " isnull(TesCofresRes.CredD0,0) CredD0, isnull(TesCofresRes.CredProxDU,0) CredProxDU, isnull(TesCofresRes.CredCorte,0) CredCorte, "
                  + " Case    when DiaSem = 1 then 'Domingo  '   "
                  + " when DiaSem = 2 then 'Segunda  '    "
                  + " when DiaSem = 3 then 'Terça    '   "
                  + " when DiaSem = 4 then 'Quarta   '   "
                  + " when DiaSem = 5 then 'Quinta   '   "
                  + " when DiaSem = 6 then 'Sexta    '   "
                  + " when DiaSem = 7 then 'Sabado   ' "
                  + " else '' end DiaSemana, "
                  + " Clientes.Nred "
                  + " from TesCofresRes  "
                  + " Left Join Clientes on  Clientes.CodCofre = TesCofresRes.CodCofre "
//                + "                    and Clientes.CodFil = "+codfil 
                  + " where TesCofresRes.CodCofre in (Select Clientes.CodCofre "
                  + " From PessoaCliAut"
                  + " Left Join Clientes  on PessoaCliAut.CodCli = Clientes.Codigo "
                  + " and PessoaCliAut.CodFil = Clientes.CodFil"
                  + " Where Clientes.CodFil >= ? "+vSQLAdic+" ) ";
// Tratamento para mostrar toda a Lista dos cofres Carlos 31/08/2023        
/*
                + " Where Clientes.Codigo = ? "
                + " and Clientes.CodFil = ? ) ";
*/
        if (!dataInicio.equals("") || !dataFinal.equals("")) {
           sql = sql + " and Data between ? and ? ";
        }
           sql = sql + " order by TesCofresRes.Data Desc";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            //consulta.setString(codigo);
            
            consulta.setString(codfil);
            if (!dataInicio.equals("") || !dataFinal.equals("")) {
                consulta.setString(dataInicio);
                consulta.setString(dataFinal);
            }
            consulta.select();

            while (consulta.Proximo()) {
                tesCofresResClientes = new TesCofresResClientes();
                tesCofresRes = new TesCofresRes();
                clientes = new Clientes();
                tesCofresRes.setCodCofre(consulta.getString("CodCofre"));
                tesCofresRes.setData(consulta.getLocalDate("Data"));
                tesCofresRes.setDiaSem(consulta.getInt("DiaSem"));
                tesCofresRes.setFeriado(consulta.getString("Feriado"));
                //tesCofresRes.setVlrDep(consulta.getString("VlrDep"));
                tesCofresRes.setVlrDep(consulta.getString("VlrTotalCred"));
                //tesCofresRes.setVlrRecDepD0(consulta.getString("VlrRecDepD0"));
                tesCofresRes.setVlrRecDepD0(consulta.getString("VlrRecAntesCorte"));
                //tesCofresRes.setHrRecDepD0(consulta.getString("HrRecDepD0"));
                String vHrAntesCorte = consulta.getString("HrRecAntesCorte");
                if (null == vHrAntesCorte) {
                   vHrAntesCorte = "";
                }else{
                   vHrAntesCorte = consulta.getString("HrRecAntesCorte");
                }
                
                String vHrAposCorte = consulta.getString("HrRecAposCorte");
                if (null == vHrAposCorte) {
                   vHrAposCorte = "";
                }else{
                   vHrAposCorte = consulta.getString("HrRecAposCorte");
                }
                tesCofresRes.setHrRecApos(vHrAposCorte);
                
                //tesCofresRes.setVlrCredRecD0(consulta.getString("VlrCredRecD0"));
                tesCofresRes.setVlrCredRecD0(consulta.getString("CredD0Rec"));
                //tesCofresRes.setVlrCredCorteD0(consulta.getString("VlrCredorteD0"));
                tesCofresRes.setVlrCredCorteD0(consulta.getString("CredD0"));
                tesCofresRes.setVlrTotalCred(consulta.getString("VlrTotalCred"));
                //tesCofresRes.setVlrRecApos(consulta.getString("VlrRecApos"));
                tesCofresRes.setVlrRecApos(consulta.getString("VlrRecAposCorte"));                
                //String []tok = consulta.getString("HrRecApos").split(",");
                String[] tok = consulta.getString("HrRecAposCorte").split(",");
                tesCofresRes.setHrRecApos(tok[0]);
                //tesCofresRes.setVlrD0Apos(consulta.getString("VlrD0Apos"));
                tesCofresRes.setVlrD0Apos(consulta.getString("CredD0Rec"));
                //tesCofresRes.setVlrD1Apos(consulta.getString("VlrD1Apos"));
                tesCofresRes.setVlrD1Apos(consulta.getString("CredD1Rec"));
                tesCofresRes.setVlrTotalRec(consulta.getString("VlrTotalRec"));
                //tesCofresRes.setVlrDepProxDU(consulta.getString("VlrDepProxDU"));
                tesCofresRes.setVlrDepProxDU(consulta.getString("CredProxDU"));
                tesCofresRes.setSaldoFisTotal(consulta.getString("SaldoFisTotal"));
                tesCofresRes.setSaldoFisCred(consulta.getString("SaldoFisCred"));
                tesCofresRes.setSaldoFisCst(consulta.getString("SaldoFisCst"));
                tesCofresRes.setDataStr(consulta.getString("DataStr"));
                tesCofresRes.setDiaSemT(consulta.getString("DiaSemana"));
                tesCofresRes.setNRed(consulta.getString("NRed"));
                clientes.setCodigo(codigo);
                clientes.setCodFil(codfil);

                tesCofresResClientes.setClientes(clientes);
                tesCofresResClientes.setTescofresres(tesCofresRes);
                retorno.add(tesCofresResClientes);
            }
            consulta.Close();
        } catch (Exception e) {
            retorno = null;
            throw new Exception("Falha ao carregar dados da Tesouraria - " + e.getMessage());
        }

        return retorno;
    }
}

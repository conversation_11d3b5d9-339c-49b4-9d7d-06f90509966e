<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      style="overflow:hidden !important; max-height:100% !important;"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
            <link rel="icon" href="../assets/img/favicon.png" />
            <link rel="stylesheet" type="text/css" href="vendor/bootstrap/css/bootstrap.min.css" />
            <link rel="stylesheet" type="text/css" href="fonts/font-awesome-4.7.0/css/font-awesome.min.css" />
            <link rel="stylesheet" type="text/css" href="fonts/iconic/css/material-design-iconic-font.min.css" />
            <link rel="stylesheet" type="text/css" href="vendor/animate/animate.css" />
            <link rel="stylesheet" type="text/css" href="vendor/css-hamburgers/hamburgers.min.css"/>
            <link rel="stylesheet" type="text/css" href="vendor/animsition/css/animsition.min.css"/>
            <link rel="stylesheet" type="text/css" href="vendor/select2/select2.min.css" />
            <link rel="stylesheet" type="text/css" href="vendor/daterangepicker/daterangepicker.css" />
            <link rel="stylesheet" type="text/css" href="css/util.css" />
            <link rel="stylesheet" type="text/css" href="css/main.css" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />

            <script src="vendor/jquery/jquery-3.2.1.min.js"></script>
            <script src="vendor/animsition/js/animsition.min.js"></script>
            <script src="vendor/bootstrap/js/popper.js"></script>
            <script src="vendor/bootstrap/js/bootstrap.min.js"></script>
            <script src="vendor/select2/select2.min.js"></script>
            <script src="vendor/daterangepicker/moment.min.js"></script>
            <script src="vendor/daterangepicker/daterangepicker.js"></script>
            <script src="vendor/countdowntime/countdowntime.js"></script>
            <script src="js/main.js"></script>
            
            <style>
                .ui-hidden-container{
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    margin: auto;
                    height: 100px !important;
                }
            </style>
        </h:head>
        <h:body id="h">
             <f:metadata>
                <f:viewParam name="empresa" value="#{login.cli}"/>
            </f:metadata>
            
            <div class="limiter">
                <div class="container-login100" style="background-image: url('images/bg-01.jpg');">
                    <div class="wrap-login100">
                        <div class="center">
                            <div style="width: 100%; text-align: center;">
                                <img src="https://mobile.sasw.com.br:9091/satmobile/logos/logo_#{login.cli eq null or login.cli eq ''? 'SATMOB':login.cli}.png" alt="logocliente" style="max-height: 80px; display: #{login.cli eq null or login.cli eq ''? 'none':''}" />
                                <img src="images/logo.png" style="height: 100px" />
                            </div>
                            <p:growl id="msgs" widgetVar="msgs" />
                            <h:form class="form-inline" id="login" style="overflow:hidden !important;">
                                <div class="wrap-input100 validate-input">
                                    <h:outputText value="#{localemsgs.Chave}" style="font-size: 12pt !important; color: #FFF !important;"/>
                                    <p:inputText id="email" value="#{login.email}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Chave}"
                                                 style="width: 100%; height: 40px;font-size: 10pt !important"/>
                                </div>
                                
                                <div class="wrap-input100 validate-input">
                                    <h:outputText value="#{localemsgs.Senha}" style="font-size: 12pt !important; color: #FFF !important;"/>
                                    <p:password id="senha" value="#{login.senhaDia}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                                 style="width: 100%; height: 40px;font-size: 10pt !important"/>
                                </div>
                                
                                <div class="container-login100-form-btn">
                                    <p:commandButton class="login100-form-btn" value="#{localemsgs.Entrar}" id="btnLogar" 
                                                     action="#{login.LogarGTVe}" style="width: 100%; border-radius: 50px; color: #000 !important"
                                                     update="msgs"/>
                                </div>

                                <div class="col-md-12 text-center" style="margin-top: 30px">
                                    <center><img src="images/logosatmob.png" width="150" /></center>
                                </div>
                            </h:form>
                        </div>
                    </div>
                </div>
            </div>
            <ui:insert name="loading">
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>

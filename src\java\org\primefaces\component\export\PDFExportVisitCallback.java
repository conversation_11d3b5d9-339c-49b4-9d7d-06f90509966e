/*
 */
package org.primefaces.component.export;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Paragraph;
import javax.faces.component.UIComponent;
import javax.faces.component.visit.VisitCallback;
import javax.faces.component.visit.VisitContext;
import javax.faces.component.visit.VisitResult;
import javax.faces.view.facelets.FaceletException;
import org.primefaces.component.datatable.DataTable;

public class PDFExportVisitCallback implements VisitCallback {

    private CustomPDFExporter exporter;
    private Document document;
    private boolean pageOnly;
    private boolean selectionOnly;
    private String encoding;

    public PDFExportVisitCallback(CustomPDFExporter exporter, Document document, boolean pageOnly, boolean selectionOnly, String encoding) {
        this.exporter = exporter;
        this.document = document;
        this.pageOnly = pageOnly;
        this.selectionOnly = selectionOnly;
        this.encoding = encoding;
    }

    public VisitResult visit(VisitContext context, UIComponent target) {
        DataTable dt = (DataTable) target;
        try {
            document.add(exporter.exportPDFTable(context.getFacesContext(), dt, pageOnly, selectionOnly, encoding));

            Paragraph preface = new Paragraph();
            exporter.addEmptyLine(preface, 3);
            document.add(preface);

        } catch (DocumentException e) {
            throw new FaceletException(e.getMessage());
        }

        return VisitResult.ACCEPT;
    }

}

package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.BatidaPonto;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BatidasPontoDao {

    private final String SQLPontos;

    public BatidasPontoDao() {
        SQLPontos = "SELECT \n"
                    + " PstServ.Local,\n"
                    + " Rh_Horas_XPE.Matr,\n"
                    + " Rh_Horas_XPE.CodFil,\n"
                    + " Rh_Horas_XPE.Data,\n"
                    + " Rh_Horas_XPE.DiaSem,\n"
                    + " Rh_Horas_XPE.Hora1,\n"
                    + " Rh_Horas_XPE.Hora2,\n"
                    + " Rh_Horas_XPE.Hora3,\n"
                    + " Rh_Horas_XPE.Hora4,\n"
                    + " Rh_Horas_XPE.Situacao\n"
                    + " FROM Rh_Horas_XPE\n"
                    + " LEFT JOIN PstServ\n"
                    + "   ON Rh_Horas_XPE.Secao = PstServ.Secao\n"
                    + "  AND Rh_Horas_XPE.CodFil = PstServ.CodFil\n"
                    + " LEFT JOIN Funcion\n"
                    + "   ON Rh_Horas_XPE.Matr = Funcion.Matr\n"
                    + " WHERE Rh_Horas_XPE.Data BETWEEN ? AND ?\n"
                    + " AND   Rh_Horas_XPE.Matr = ?\n"
                    + " ORDER BY Funcion.Nome, Rh_Horas_XPE.Matr, Rh_Horas_XPE.Data";
        
        /*SQLPontos = "SELECT RhPonto.Matr, RhPonto.DtCompet Data,\n"
                + "DatePart(DW,Convert(Date,RhPonto.DtCompet)) DiaSemana,\n"
                + "(SELECT TOP 1 Hora from RhPonto z  WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 1) Batida01,\n"
                + "CASE WHEN (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr) = 2\n"
                + "THEN '' ELSE\n"
                + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 2) end Batida02,\n"
                + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 3) Batida03,\n"
                + "Case when (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND RhPonto.Matr = z.Matr) = 2 then\n"
                + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 2) else\n"
                + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 4) end Batida04,\n"
                + "(SELECT TOP 1 z.Local from RhPonto x (Nolock)\n"
                + "LEFT JOIN RhPontoDet y (Nolock) ON x.DtCompet = y.DtCompet\n"
                + "AND x.Matr = y.Matr\n"
                + "AND x.Batida = y.Batida\n"
                + "LEFT JOIN PstServ  z (NoLock) ON y.Secao = z.Secao\n"
                + "AND Funcion.CodFil = z.CodFil\n"
                + "WHERE x.DtCompet = RhPonto.DtCompet AND x.Matr = RhPonto.Matr AND x.Batida = 1) PostoRegistro,\n"
                + "CASE WHEN (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr) not IN (2,4) THEN 'IRREGULAR' ELSE '' end OBS\n"
                + "FROM RhPonto (Nolock)\n"
                + "LEFT JOIN Funcion ON Funcion.Matr = RhPonto.Matr\n"
                + "WHERE RhPonto.DtCompet between ? AND ?\n"
                + "AND RHPonto.MAtr = ?\n"
                + "GROUP BY RhPonto.Matr, RhPonto.DtCompet, Funcion.CodFil\n"
                + "ORDER BY RhPonto.DtCompet";*/
    }

    public BatidasPontoDao(Persistencia persistencia) {
        if (persistencia.getEmpresa().toUpperCase().indexOf("CONFEDERAL") == -1
                && persistencia.getEmpresa().toUpperCase().indexOf("MAXIMA") == -1) {
            SQLPontos = "SELECT \n"
                    + " PstServ.Local,\n"
                    + " Rh_Horas_XPE.Matr,\n"
                    + " Rh_Horas_XPE.CodFil,\n"
                    + " Rh_Horas_XPE.Data,\n"
                    + " Rh_Horas_XPE.DiaSem,\n"
                    + " REPLACE(Rh_Horas_XPE.Hora1, ',',':') Hora1,\n"
                    + " REPLACE(Rh_Horas_XPE.Hora2, ',',':') Hora2,\n"
                    + " REPLACE(Rh_Horas_XPE.Hora3, ',',':') Hora3,\n"
                    + " REPLACE(Rh_Horas_XPE.Hora4, ',',':') Hora4,\n"
                    + " Rh_Horas_XPE.Situacao\n"
                    + " FROM Rh_Horas_XPE\n"
                    + " LEFT JOIN PstServ\n"
                    + "   ON Rh_Horas_XPE.Secao = PstServ.Secao\n"
                    + "  AND Rh_Horas_XPE.CodFil = PstServ.CodFil\n"
                    + " LEFT JOIN Funcion\n"
                    + "   ON Rh_Horas_XPE.Matr = Funcion.Matr\n"
                    + " WHERE Rh_Horas_XPE.Data BETWEEN ? AND ?\n"
                    + " AND   Rh_Horas_XPE.Matr = ?\n"
                    + " ORDER BY Funcion.Nome, Rh_Horas_XPE.Matr, Rh_Horas_XPE.Data";
        } else {
            SQLPontos = "SELECT \n"
                    + " PstServ.Local,\n"
                    + " Rh_Horas_XPE.Matr,\n"
                    + " Rh_Horas_XPE.CodFil,\n"
                    + " Rh_Horas_XPE.Data,\n"
                    + " Rh_Horas_XPE.DiaSem,\n"
                    + " REPLACE(Rh_Horas_XPE.Hora1, ',',':') Hora1,\n"
                    + " REPLACE(Rh_Horas_XPE.Hora2, ',',':') Hora2,\n"
                    + " REPLACE(Rh_Horas_XPE.Hora3, ',',':') Hora3,\n"
                    + " REPLACE(CASE WHEN Rh_Horas_XPE_Dia_Anterior.Hora4 IS NULL AND Rh_Horas_XPE_Dia.Hora4 IS NULL THEN Rh_Horas_XPE.Hora4 ELSE CASE WHEN Rh_Horas_XPE_Dia_Seguinte.Hora4 IS NULL OR Rh_Horas_XPE.Hora4 IS NULL OR Rh_Horas_XPE.Hora4 = '' THEN CASE WHEN (Rh_Horas_XPE_Dia.Hora4 IS NULL OR Rh_Horas_XPE_Dia.Hora4 = '') THEN Rh_Horas_XPE_Dia_Anterior.Hora4 ELSE '' END ELSE Rh_Horas_XPE.Hora4 END END, ',',':') Hora4,\n"
                    + " Rh_Horas_XPE.Situacao\n"
                    + " FROM Rh_Horas_XPE\n"
                    + " LEFT JOIN PstServ\n"
                    + "   ON Rh_Horas_XPE.Secao = PstServ.Secao\n"
                    + "  AND Rh_Horas_XPE.CodFil = PstServ.CodFil\n"
                    + "  LEFT JOIN Rh_Horas_XPE Rh_Horas_XPE_Dia\n"
                    + "   ON Rh_Horas_XPE.Matr = Rh_Horas_XPE_Dia.Matr\n"
                    + "  AND Rh_Horas_XPE.Data = Rh_Horas_XPE_Dia.Data\n"
                    + "  AND convert(datetime, Rh_Horas_XPE_Dia.Hora4, 8) < convert(datetime, Rh_Horas_XPE_Dia.Hora1, 8)\n"
                    + " LEFT JOIN Rh_Horas_XPE Rh_Horas_XPE_Dia_Anterior\n"
                    + "   ON Rh_Horas_XPE.Matr = Rh_Horas_XPE_Dia_Anterior.Matr\n"
                    + "  AND DATEADD(DD,-1,Rh_Horas_XPE.Data) = Rh_Horas_XPE_Dia_Anterior.Data\n"
                    + "  AND convert(datetime, Rh_Horas_XPE_Dia_Anterior.Hora4, 8) < convert(datetime, Rh_Horas_XPE_Dia_Anterior.Hora1, 8)\n"
                    + " LEFT JOIN Rh_Horas_XPE Rh_Horas_XPE_Dia_Seguinte\n"
                    + "   ON Rh_Horas_XPE.Matr = Rh_Horas_XPE_Dia_Seguinte.Matr\n"
                    + "  AND DATEADD(DD,1,Rh_Horas_XPE.Data) = Rh_Horas_XPE_Dia_Seguinte.Data\n"
                    + "  AND convert(datetime, Rh_Horas_XPE_Dia_Seguinte.Hora4, 8) < convert(datetime, Rh_Horas_XPE_Dia_Seguinte.Hora1, 8)"
                    + " LEFT JOIN Funcion\n"
                    + "   ON Rh_Horas_XPE.Matr = Funcion.Matr\n"
                    + " WHERE Rh_Horas_XPE.Data BETWEEN ? AND ?\n"
                    + " AND   Rh_Horas_XPE.Matr = ?\n"
                    + " ORDER BY Funcion.Nome, Rh_Horas_XPE.Matr, Rh_Horas_XPE.Data";
        }

        /*
        if (persistencia.getEmpresa().toUpperCase().indexOf("CONFEDERAL") == -1) {
            SQLPontos = "SELECT RhPonto.Matr, RhPonto.DtCompet Data,\n"
                    + "DatePart(DW,Convert(Date,RhPonto.DtCompet)) DiaSemana,\n"
                    + "(SELECT TOP 1 Hora from RhPonto z  WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 1) Batida01,\n"
                    + "CASE WHEN (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr) = 2\n"
                    + "THEN '' ELSE\n"
                    + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 2) end Batida02,\n"
                    + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 3) Batida03,\n"
                    + "Case when (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND RhPonto.Matr = z.Matr) = 2 then\n"
                    + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida IN(2, 4)) else\n"
                    + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr AND z.Batida = 4) end Batida04,\n"
                    + "(SELECT TOP 1 z.Local from RhPonto x (Nolock)\n"
                    + "LEFT JOIN RhPontoDet y (Nolock) ON x.DtCompet = y.DtCompet\n"
                    + "AND x.Matr = y.Matr\n"
                    + "AND x.Batida = y.Batida\n"
                    + "LEFT JOIN PstServ  z (NoLock) ON y.Secao = z.Secao\n"
                    + "AND Funcion.CodFil = z.CodFil\n"
                    + "WHERE x.DtCompet = RhPonto.DtCompet AND x.Matr = RhPonto.Matr AND x.Batida = 1) PostoRegistro,\n"
                    + "CASE WHEN (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr) not IN (2,4) THEN 'IRREGULAR' ELSE '' end OBS\n"
                    + "FROM RhPonto (Nolock)\n"
                    + "LEFT JOIN Funcion ON Funcion.Matr = RhPonto.Matr\n"
                    + "WHERE RhPonto.DtCompet between ? AND ?\n"
                    + "AND RHPonto.MAtr = ?\n"
                    + "GROUP BY RhPonto.Matr, RhPonto.DtCompet, Funcion.CodFil\n"
                    + "ORDER BY RhPonto.DtCompet";
        }
        else{
            SQLPontos = "SELECT RhPonto.Matr, RhPonto.DtBatida Data,\n"
                    + "DatePart(DW,Convert(Date,RhPonto.DtCompet)) DiaSemana,\n"
                    + "(SELECT TOP 1 Hora from RhPonto z  WHERE z.DtBatida = RhPonto.DtBatida AND z.Matr = RhPonto.Matr AND z.Batida = 1) Batida01,\n"
                    + "CASE WHEN (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr) = 2\n"
                    + "THEN '' ELSE\n"
                    + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtBatida = RhPonto.DtBatida AND z.Matr = RhPonto.Matr AND z.Batida = 2) end Batida02,\n"
                    + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtBatida = RhPonto.DtBatida AND z.Matr = RhPonto.Matr AND z.Batida = 3) Batida03,\n"
                    + "Case when (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND RhPonto.Matr = z.Matr) = 2 then\n"
                    + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtBatida = RhPonto.DtBatida AND z.Matr = RhPonto.Matr AND z.Batida IN(2, 4)) else\n"
                    + "(SELECT TOP 1 Hora from RhPonto z (Nolock) WHERE z.DtBatida = RhPonto.DtBatida AND z.Matr = RhPonto.Matr AND z.Batida = 4) end Batida04,\n"
                    + "(SELECT TOP 1 z.Local from RhPonto x (Nolock)\n"
                    + "LEFT JOIN RhPontoDet y (Nolock) ON x.DtCompet = y.DtCompet\n"
                    + "AND x.Matr = y.Matr\n"
                    + "AND x.Batida = y.Batida\n"
                    + "LEFT JOIN PstServ  z (NoLock) ON y.Secao = z.Secao\n"
                    + "AND Funcion.CodFil = z.CodFil\n"
                    + "WHERE x.DtCompet = RhPonto.DtCompet AND x.Matr = RhPonto.Matr AND x.Batida = 1) PostoRegistro,\n"
                    + "CASE WHEN (Select Count(*) from RhPonto z (Nolock) WHERE z.DtCompet = RhPonto.DtCompet AND z.Matr = RhPonto.Matr) not IN (2,4) THEN 'IRREGULAR' ELSE '' end OBS\n"
                    + "FROM RhPonto (Nolock)\n"
                    + "LEFT JOIN Funcion ON Funcion.Matr = RhPonto.Matr\n"
                    + "WHERE RhPonto.DtCompet between ? AND ?\n"
                    + "AND RHPonto.MAtr = ?\n"
                    + "GROUP BY RhPonto.Matr, RhPonto.DtCompet, RhPonto.DtBatida, Funcion.CodFil\n"
                    + "ORDER BY RhPonto.DtBatida";
        }*/
    }

    public List<BatidaPonto> getAll(
            String Matricula,
            String sDt_Ini,
            String sDt_Fim,
            Persistencia persistencia
    ) throws Exception {
        try {
            List<BatidaPonto> pontos = new ArrayList();
            Consulta consulta = new Consulta(SQLPontos, persistencia);
            consulta.setString(sDt_Ini);
            consulta.setString(sDt_Fim);
            consulta.setBigDecimal(Matricula.replace(" ", ""));
            consulta.select();

            while (consulta.Proximo()) {
                BatidaPonto ponto = new BatidaPonto();

                ponto.setMatr(consulta.getString("Matr"));
                ponto.setData(consulta.getString("Data"));
                //ponto.setDiaSemana(consulta.getString("DiaSemana"));
                ponto.setDiaSemana(consulta.getString("DiaSem"));
                /*
                ponto.setBatida01(consulta.getString("Batida01"));
                ponto.setBatida02(consulta.getString("Batida02"));
                ponto.setBatida03(consulta.getString("Batida03"));
                ponto.setBatida04(consulta.getString("Batida04"));
                 */
                ponto.setBatida01(consulta.getString("Hora1"));
                ponto.setBatida02(consulta.getString("Hora2"));
                ponto.setBatida03(consulta.getString("Hora3"));
                ponto.setBatida04(consulta.getString("Hora4"));
                //ponto.setPostoRegistro(consulta.getString("Local"));
                ponto.setPostoRegistro("");
                ponto.setOBS(consulta.getString("Situacao"));

                pontos.add(ponto);
            }

            consulta.Close();
            return pontos;
        } catch (Exception e) {
            throw new Exception("Falha ao retornar dados da função - " + e.getMessage());
        }
    }

}
